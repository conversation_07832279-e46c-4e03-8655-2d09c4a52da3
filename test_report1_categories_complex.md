# 测试报告：report1Categories动态变量修改（复杂版本）

## 修改概述

已成功将MyBatis的`downloadReport1`查询从固定的`category1`和`category2`变量修改为使用动态的`report1Categories`数组，采用复杂的foreach循环方式实现完全动态化。

## 修改内容

### 1. TrackingTestReport1Bean类修改
- 添加了`category1`, `category2`, `category3`, `category4`, `category5`字段
- 添加了对应的getter和setter方法
- 支持最多5个动态category字段

### 2. downloadReport1 SQL查询修改（复杂版本）
- 使用`<foreach collection="report1Categories" item="category" index="index" separator=",">mm.category${index+1}</foreach>`动态生成SELECT字段
- 使用`<foreach collection="report1Categories" item="category" index="index" separator=",">t.${report1Categories[${index}]} as category${index+1}</foreach>`动态生成子查询字段
- 使用`<foreach collection="report1Categories" item="category" index="index" separator=" and ">t.${report1Categories[${index}]} is not null</foreach>`动态生成WHERE条件
- 使用`<foreach collection="report1Categories" item="category" index="index" separator=", ">t.${report1Categories[${index}]}</foreach>`动态生成GROUP BY子句
- 使用`<foreach collection="report1Categories" item="category" index="index" separator=" and ">sales.category${index+1} = order_intake.category${index+1}</foreach>`动态生成JOIN条件
- 修改了所有子查询（sales, order_intake, crd, backorder, backlog, soh, fcst, ud, output）使用完全动态的foreach循环

## 核心技术实现

### SELECT字段动态生成
```xml
select <foreach collection="report1Categories" item="category" index="index" separator=",">mm.category${index+1}</foreach>,
       sum(mm.sales_qty) sales_qty,
       ...
```

### 子查询字段动态生成
```xml
(select <foreach collection="report1Categories" item="category" index="index" separator=",">
        t.${category} as category${index+1}
        </foreach>,
        sum(t.order_quantity) sales_qty,
        ...
```

### WHERE条件动态生成
```xml
where <foreach collection="report1Categories" item="category" index="index" separator=" and ">t.${category} is not null</foreach>
      and t.${dateColumn} = #{saleMonth,jdbcType=VARCHAR}
      ...
```

### GROUP BY动态生成
```xml
group by <foreach collection="report1Categories" item="category" index="index" separator=", ">t.${category}</foreach>
```

### JOIN条件动态生成
```xml
on <foreach collection="report1Categories" item="category" index="index" separator=" and ">sales.category${index+1} = order_intake.category${index+1}</foreach>
```

### COALESCE动态生成
```xml
select <foreach collection="report1Categories" item="category" index="index" separator=",">
       coalesce(sales.category${index+1}, order_intake.category${index+1}, crd.category${index+1}, backorder.category${index+1}, backlog.category${index+1}, soh.category${index+1}, fcst.category${index+1}, ud.category${index+1}, output.category${index+1}, 'Others') category${index+1}
       </foreach>,
```

## 使用方式

### 单个category
```java
Map<String, Object> parameterMap = new HashMap<>();
List<String> report1Categories = Arrays.asList("BU");
parameterMap.put("report1Categories", report1Categories);
// 结果将包含category1字段
```

### 两个category（原有逻辑）
```java
Map<String, Object> parameterMap = new HashMap<>();
List<String> report1Categories = Arrays.asList("BU", "PRODUCT_LINE");
parameterMap.put("report1Categories", report1Categories);
// 结果将包含category1和category2字段
```

### 三个category
```java
Map<String, Object> parameterMap = new HashMap<>();
List<String> report1Categories = Arrays.asList("BU", "PRODUCT_LINE", "LOCAL_PRODUCT_FAMILY");
parameterMap.put("report1Categories", report1Categories);
// 结果将包含category1, category2和category3字段
```

### 最多五个category
```java
Map<String, Object> parameterMap = new HashMap<>();
List<String> report1Categories = Arrays.asList("BU", "PRODUCT_LINE", "LOCAL_PRODUCT_FAMILY", "MATERIAL_OWNER_NAME", "MRP_CONTROLLER");
parameterMap.put("report1Categories", report1Categories);
// 结果将包含category1到category5字段
```

## 优势

1. **完全动态化**：支持任意数量的category字段（最多5个）
2. **高度灵活**：可以根据业务需求动态选择不同的字段组合
3. **代码复用**：一个查询可以处理多种不同的category组合
4. **性能优化**：只查询需要的字段，避免不必要的数据传输
5. **向后兼容**：完全兼容原有的两个category的使用方式

## 语法修复说明

在MyBatis的foreach循环中，不能使用嵌套的`${}`表达式，如`${report1Categories[${index}]}`会导致OGNL解析错误。
正确的语法是直接使用foreach的item变量：`${category}`，其中`category`是foreach循环中的item变量。

**错误语法：**
```xml
<foreach collection="report1Categories" item="category" index="index">
    t.${report1Categories[${index}]} as category${index+1}
</foreach>
```

**正确语法：**
```xml
<foreach collection="report1Categories" item="category" index="index">
    t.${category} as category${index+1}
</foreach>
```

## 注意事项

1. 确保传入的`report1Categories`数组不为空
2. 数组中的字段名必须存在于相应的数据库视图中
3. 目前Bean类支持最多5个category字段，如需更多可以继续扩展
4. 建议在使用前验证字段名的有效性
5. 在foreach循环中直接使用item变量，避免嵌套的`${}`表达式

## Service层修改

### TrackingTestServiceImpl.java中的downloadReport1方法修改

原来的代码使用单一的`tracking.getCategory()`方法，现在修改为动态处理多个category字段：

```java
// 动态添加category字段
if (report1Categories != null && !report1Categories.isEmpty()) {
    for (int i = 0; i < report1Categories.size(); i++) {
        String categoryName = report1Categories.getString(i);
        String categoryValue = null;

        // 根据索引获取对应的category值
        switch (i) {
            case 0 -> categoryValue = tracking.getCategory1();
            case 1 -> categoryValue = tracking.getCategory2();
            case 2 -> categoryValue = tracking.getCategory3();
            case 3 -> categoryValue = tracking.getCategory4();
            case 4 -> categoryValue = tracking.getCategory5();
        }

        if (categoryValue != null) {
            map.put(categoryName, categoryValue);
        }
    }
}
```

这样修改后，Service层可以正确处理SQL查询返回的动态category字段，并将它们映射到正确的输出格式中。

## 完整修改总结

1. **TrackingTestReport1Bean.java**: 添加了category1-category5字段及其getter/setter方法
2. **TrackingTestDao.xml的downloadReport1查询**: 完全重写为使用foreach循环的动态查询
3. **TrackingTestServiceImpl.java的downloadReport1方法**: 修改为动态处理多个category字段
4. **语法修复**: 修复了MyBatis OGNL解析错误，使用正确的foreach语法

## 测试建议

1. 测试1-5个不同数量的category组合
2. 验证生成的SQL语法正确性
3. 测试数据返回的完整性和正确性
4. 性能测试，确保动态生成不影响查询效率
5. 边界条件测试（空数组、无效字段名等）
6. 测试Service层的数据映射是否正确
