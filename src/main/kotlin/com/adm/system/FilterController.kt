package com.adm.system

import com.adm.system.service.FilterService
import com.starter.context.bean.Response
import com.starter.context.bean.SchneiderRequestMapping
import com.starter.context.servlet.ControllerHelper
import jakarta.annotation.Resource
import jakarta.servlet.http.HttpServletRequest
import org.springframework.context.annotation.Scope
import org.springframework.web.bind.annotation.CrossOrigin
import org.springframework.web.bind.annotation.RestController

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = ["/system/filter"])
class FilterController : ControllerHelper() {

    @Resource
    lateinit var filterService: FilterService

    @SchneiderRequestMapping("/query_cascader_opts")
    fun queryCascaderOpts(request: HttpServletRequest): Response {
        super.pageLoad(request)
        super.setGlobalCache(true)
        return filterService.queryCascaderOpts(parameterMap)
    }

    @SchneiderRequestMapping("/query_columns_by_tablename")
    fun queryColumnsByTablename(request: HttpServletRequest): Response {
        super.pageLoad(request)
        super.setGlobalCache(true)
        return filterService.queryColumnsByTablename(parameterMap)
    }

    @SchneiderRequestMapping("/query_suggestion_by_keywords")
    fun querySuggestionByKeywords(request: HttpServletRequest): Response {
        super.pageLoad(request)
        super.setGlobalCache(true)
        return filterService.querySuggestionByKeywords(parameterMap)
    }
}
