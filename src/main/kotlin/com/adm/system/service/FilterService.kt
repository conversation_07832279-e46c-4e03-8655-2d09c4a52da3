package com.adm.system.service

import com.adm.system.dao.IFilterDao
import com.alibaba.fastjson.JSON
import com.starter.context.bean.Configuration
import com.starter.context.bean.Response
import com.starter.context.bean.filter.FilterElement
import com.starter.context.configuration.SCPATableConfiguration
import com.starter.context.servlet.UserContextHolder
import com.starter.utils.EncryptionUtil
import com.starter.utils.Utils
import jakarta.annotation.Resource
import org.codehaus.plexus.util.StringUtils
import org.springframework.cache.annotation.Cacheable
import org.springframework.context.annotation.Scope
import org.springframework.stereotype.Service
import java.util.concurrent.TimeUnit
import java.util.concurrent.locks.ReentrantLock

@Service
@Scope("prototype")
class FilterService {

    @Resource
    lateinit var filterDao: IFilterDao

    @Resource
    lateinit var response: Response

    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    fun queryCascaderOpts(parameterMap: Map<String, Any>): Response {
        val table = parameterMap["table"]?.toString()
        if (table == null || StringUtils.isBlank(table) || Utils.hasInjectionAttack(table)) {
            return response
        }
        return response.setBody(Utils.parseCascader(filterDao.queryCascaderOpts(table)))
    }

    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    fun queryColumnsByTablename(parameterMap: Map<String, Any>): Response {
        var tables = parameterMap["tables"] as List<String>
        if (tables.isEmpty()) {
            tables = filterDao.queryDependTablesByUrls(parameterMap);
        }
        return response.setBody(SCPATableConfiguration.getMatchedTblColumns(tables))
    }

    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    fun querySuggestionByKeywords(parameterMap: Map<String, Any>): Response {
        var tables = parameterMap["tables"] as List<String>
        val field = parameterMap["field"] as String
        val type = parameterMap["type"] as String
        val keywords = parameterMap["keywords"] as String
        if (tables.isEmpty()) {
            tables = filterDao.queryDependTablesByUrls(parameterMap);
        }
        if (tables.isEmpty()) {
            tables = filterDao.queryAvailableTable(tables, field);
        }

        val result = ArrayList<String>()
        if (tables.isEmpty()) {
            result.add("No Data")
            return response.setBody(result)
        }
        var data = ArrayList<String>()
        for (table in tables) {
            try {
                data = filterDao.querySuggestionByKeywords(table, field, type, keywords) as ArrayList<String>
                break
            } catch (ignore: Exception) {

            }
        }

        if (StringUtils.isBlank(keywords) == false && data.indexOf(keywords) == -1) {
            result.add(keywords)
        }
        result.addAll(data)
        if (result.isEmpty()) {
            result.add("No Data")
        }
        return response.setBody(result)
    }

    companion object {
        val locker = ReentrantLock()
    }

    fun doSpeedup(element: FilterElement, splitedQueryList: List<List<List<String>>>): String? {
        if (splitedQueryList.size == 1 && splitedQueryList[0].size < 256) {
            return null
        }

        // 包含$的字段不使用表加速
        // 如果SPEEDUP表不包含所有字段, 也不启用加速
        for (field in element.getFields()) {
            if (field.indexOf("$") != -1) {
                return null
            }
            if (SCPATableConfiguration.isColumnInTable(SCPATableConfiguration.FILTER_SPEEDUP_TABLE, field) == false) {
                return null
            }
        }

        val id = EncryptionUtil.md5Short6(JSON.toJSONString(element))
        try {
            locker.lock()
            if (filterDao.queryFilterSpeedupExistingID(id) == 0) {
                for (slist in splitedQueryList) {
                    val mlist = ArrayList<Map<String, Any>>()
                    for (row in slist) {
                        val map = HashMap<String, Any>()
                        for (i in 0 until element.getFields().size) {
                            map[element.getFields()[i]] = row[i]
                        }
                        mlist.add(map)
                    }
                    filterDao.saveFilterSpeedup(id, element.fields, mlist, UserContextHolder.getUserID());
                }
                filterDao.deleteExpiredRecord()

                var maxTry = 10
                while (filterDao.queryPopulatingCnt() != 0) {
                    if (maxTry-- < 0) {
                        break
                    }
                    TimeUnit.MILLISECONDS.sleep(500)
                    println(">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> Waiting populating...")
                }
            }
        } finally {
            locker.unlock()
        }
        return id;
    }
}
