<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.adm.system.dao.IFilterDao">
    <select id="queryCascaderOpts" resultType="java.util.Map">
        SELECT * FROM ${table} T ORDER BY T.CATEGORY, DECODE(T.NAME, 'Others' , 'zzz', T.NAME)
    </select>

    <select id="queryDependTablesByUrls" resultType="java.lang.String">
        WITH URLS AS (SELECT T.URL
                      FROM SY_MENU T
                      WHERE T.PARENT_ID IN (SELECT T0.MENU_CODE
                                            FROM SY_MENU T0
                                            WHERE T0.URL = #{url, jdbcType=VARCHAR}))
        SELECT DISTINCT T.TABLE_NAME
        FROM SY_METHOD_TABLE_MAPPING T
                 INNER JOIN URLS ON T.URL = URLS.URL
        WHERE T.TABLE_NAME NOT LIKE 'SY_%' AND T.TABLE_NAME NOT IN ('USER_TAB_COLS') AND T.TABLE_NAME NOT LIKE '%_FILTER_V'
    </select>

    <select id="queryAvailableTable" resultType="java.lang.String">
        SELECT DISTINCT T.TABLE_NAME FROM USER_TAB_COLS T
         WHERE T.TABLE_NAME IN
             <foreach collection="tables" item="table" open="(" close=")" separator=",">
                 #{table,jdbcType=VARCHAR}
             </foreach>
           AND T.COLUMN_NAME = #{field,jdbcType=VARCHAR} AND T.HIGH_VALUE IS NOT NULL AND T.LOW_VALUE IS NOT NULL
         ORDER BY CASE WHEN T.TABLE_NAME LIKE '%_V' THEN 1 WHEN T.TABLE_NAME LIKE '%_HIST' THEN 2 ELSE 3 END
    </select>

    <select id="querySuggestionByKeywords" resultType="java.lang.String">
        SELECT /*+ parallel */ DISTINCT
        <choose>
            <when test="type=='DATE'">
                TO_CHAR(${field}, 'YYYY/MM/DD') FROM ${table}
                WHERE ${field} IS NOT NULL
                <if test="keywords != null and keywords != ''.toString()">
                    AND TO_CHAR(${field}, 'YYYY/MM/DD') LIKE '%' || #{keywords, jdbcType=VARCHAR} || '%'
                </if>
            </when>
            <otherwise>
                ${field} FROM ${table}
                WHERE ${field} IS NOT NULL
                <if test="keywords != null and keywords != ''.toString()">
                    AND UPPER(${field}) LIKE '%' || UPPER(#{keywords, jdbcType=VARCHAR}) || '%'
                </if>
                ORDER BY LENGTH(${field})
            </otherwise>
        </choose>
        FETCH NEXT 50 ROWS ONLY
    </select>

    <insert id="saveFilterSpeedup">
        BEGIN
            INSERT INTO SY_FILTER_SPEEDUP (ID$,
            <foreach collection="fields" index="index" item="field" separator=",">
                ${field}
            </foreach>, CREATE_DATE$, CREATE_BY$)
            <foreach collection="data" item="row" separator="union all">
                SELECT #{id,jdbcType=VARCHAR},
                <foreach collection="fields" item="field" separator=",">
                    #{row.${field}, jdbcType=VARCHAR}
                </foreach>, SYSDATE, #{createBy, jdbcType=VARCHAR}
                FROM DUAL
            </foreach>;
            COMMIT;
        END;
    </insert>

    <delete id="deleteExpiredRecord">
        BEGIN
            DELETE FROM SY_FILTER_SPEEDUP T WHERE T.CREATE_DATE$ &lt; SYSDATE - 1/24;
            COMMIT;
        END;
    </delete>

    <select id="queryFilterSpeedupExistingID" resultType="java.lang.Integer">
        select count(1) from SY_FILTER_SPEEDUP T WHERE T.ID$ = #{id, jdbcType=VARCHAR}
    </select>

    <select id="queryPopulatingCnt" resultType="java.lang.Integer" useCache="false">
        SELECT COUNT(1) FROM V$INMEMORY_AREA T WHERE T.POPULATE_STATUS = 'POPULATING'
    </select>
</mapper>
