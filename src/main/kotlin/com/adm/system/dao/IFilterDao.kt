package com.adm.system.dao

import org.apache.ibatis.annotations.Mapper

@Mapper
interface IFilterDao {
    fun queryCascaderOpts(table: String): List<Map<String, String>>

    fun queryDependTablesByUrls(parameterMap: Map<String, Any>): List<String>

    fun queryAvailableTable(tables: List<String>, field: String): List<String>

    fun querySuggestionByKeywords(table: String, field: String, type: String, keywords: String): List<String>

    fun saveFilterSpeedup(id: String, fields: List<String>, data: List<Map<String, Any>>, createBy: String)

    fun deleteExpiredRecord()

    fun queryFilterSpeedupExistingID(id: String): Int

    fun queryPopulatingCnt(): Int
}
