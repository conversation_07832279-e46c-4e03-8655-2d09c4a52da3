package com.adm.demo

import com.adm.demo.service.KotlinDemoService
import com.starter.context.bean.Response
import com.starter.context.bean.SchneiderRequestMapping
import com.starter.context.servlet.ControllerHelper
import jakarta.annotation.Resource
import org.springframework.context.annotation.Scope
import org.springframework.web.bind.annotation.CrossOrigin
import org.springframework.web.bind.annotation.RestController

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = ["/adm/demo"])
class KotlinDemoController : ControllerHelper() {

    @Resource
    lateinit var kotlinDemoService: KotlinDemoService


    @SchneiderRequestMapping("/query_time")
    fun queryTime(): Response {
        super.setGlobalCache(true)
        return kotlinDemoService.queryTime()
    }
}
