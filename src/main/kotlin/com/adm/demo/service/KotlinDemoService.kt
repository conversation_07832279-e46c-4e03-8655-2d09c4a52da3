package com.adm.demo.service

import com.adm.demo.dao.KotlinDemoDao
import com.starter.context.bean.Response
import jakarta.annotation.Resource
import org.springframework.context.annotation.Scope
import org.springframework.stereotype.Service

@Service
@Scope("prototype")
class KotlinDemoService {

    @Resource
    lateinit var kotlinDemoDao: KotlinDemoDao

    @Resource
    lateinit var response: Response

    fun queryTime(): Response {
        return response.setBody(kotlinDemoDao.queryDate())
    }
}
