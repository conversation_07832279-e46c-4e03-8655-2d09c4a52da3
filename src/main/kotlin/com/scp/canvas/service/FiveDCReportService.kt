package com.scp.canvas.service

import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.scp.canvas.bean.FiveDCReport1Bean
import com.scp.canvas.bean.FiveDCTreemapBean
import com.scp.canvas.dao.IFiveDCReportDao
import com.starter.context.bean.CacheRemove
import com.starter.context.bean.Configuration
import com.starter.context.bean.Response
import com.starter.context.bean.SimplePage
import com.starter.context.configuration.SCPATableConfiguration
import com.starter.context.servlet.ServiceHelper
import com.starter.login.bean.Session
import com.starter.utils.Utils
import com.starter.utils.excel.ExcelTemplate
import jakarta.annotation.Resource
import jakarta.servlet.http.HttpServletResponse
import org.apache.commons.lang3.StringUtils
import org.springframework.cache.annotation.Cacheable
import org.springframework.context.annotation.Scope
import org.springframework.stereotype.Service
import java.math.BigDecimal

@Service
@Scope("prototype")
class FiveDCReportService : ServiceHelper() {

    companion object {
        const val PARENT_CODE = "menu540"
    }

    @Resource
    lateinit var fiveDCReportDao: IFiveDCReportDao

    @Resource
    lateinit var excelTemplate: ExcelTemplate

    @Resource
    lateinit var response: Response

    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    fun initPage(parameterMap: MutableMap<String, Any>, session: Session): Response {
        val resultMap: MutableMap<String, Any> = HashMap()
        resultMap["filterOpts"] = Utils.parseCascader(fiveDCReportDao.queryFilters())
        resultMap["report1ColumnOpts"] = Utils.parseCascader(fiveDCReportDao.queryReport1ColumnOpts(), false)
        resultMap["isAdmin"] = fiveDCReportDao.queryPageAdmin(session.userid, PARENT_CODE) > 0
        return response.setBody(resultMap)
    }

    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    fun queryReport1(parameterMap: MutableMap<String, Any>): Response {
        this.generateFilter(parameterMap)
        this.generateReport1Filter(parameterMap)
        if ("Table" == parameterMap["report1ReportType"]) {
            return response.setBody(queryReport1Table(parameterMap))
        } else if ("Tree Table" == parameterMap["report1ReportType"]) {
            return response.setBody(queryReport1TreeTable(parameterMap))
        } else if ("Pie Chart" == parameterMap["report1ReportType"]) {
            return response.setBody(queryReport1PieChart(parameterMap))
        } else if ("Treemap Chart" == parameterMap["report1ReportType"]) {
            return response.setBody(queryReport1TreemapChart(parameterMap))
        }
        return response
    }

    fun downloadReport1(parameterMap: MutableMap<String, Any>, response: HttpServletResponse) {
        this.generateFilter(parameterMap)
        this.generateReport1Filter(parameterMap)
        this.prepareReport1TableAndTreeTable(parameterMap)
        val page = SimplePage<LinkedHashMap<String, Any>>(parameterMap)
        page.start = 0
        page.length = SimplePage.PAGE_MAX

        val fileName = "query_result_" + Utils.randomStr(4) + ".xlsx"
        excelTemplate.create(response, fileName, "com.scp.canvas.dao.IFiveDCReportDao.queryReport1Table", parameterMap)
    }

    fun queryReport1Table(parameterMap: MutableMap<String, Any>): SimplePage<LinkedHashMap<String, Any>> {
        this.prepareReport1TableAndTreeTable(parameterMap)
        val page = SimplePage<LinkedHashMap<String, Any>>(parameterMap)
        page.total = fiveDCReportDao.queryReport1TableCount(parameterMap)
        if (page.total > 0) {
            page.data = fiveDCReportDao.queryReport1Table(parameterMap)
        }
        return page
    }

    fun queryReport1TreeTable(parameterMap: MutableMap<String, Any>): HashMap<String, Any> {
        this.prepareReport1TableAndTreeTable(parameterMap)
        val report1PivotBy = parameterMap["report1PivotBy"] as JSONArray
        parameterMap["category"] = report1PivotBy[0]
        val resultMap = HashMap<String, Any>()
        val columns = ArrayList<String>()
        resultMap["columns"] = columns
        val resultList = fiveDCReportDao.queryReport1TreeTable(parameterMap)
        if (resultList.size > 0) {
            val data = resultList[0]
            for (key in data.keys) {
                if (report1PivotBy.contains(key) == false && "category" != key) {
                    columns.add(key)
                }
            }
        }
        for (map in resultList) {
            map["parent"] = ArrayList<String>()
            map["id"] = Utils.randomStr(12)
            map["hasChildren"] = report1PivotBy.size > 1 && "Total" != map["category"]
        }
        resultMap["data"] = resultList
        return resultMap
    }

    fun queryReport1PieChart(parameterMap: MutableMap<String, Any>): List<HashMap<String, Any?>> {
        val valuesAll = (parameterMap["report1Table"] as JSONObject).getJSONArray("selectedVirtualColumns")
        val selectedId = parameterMap["report1ChartValueId"] as String
        val values = valuesAll.filter { e: Any -> selectedId == (e as JSONObject).getString("id") }
        (parameterMap["report1Table"] as JSONObject)["selectedVirtualColumns"] = values

        this.prepareReport1TableAndTreeTable(parameterMap)

        this.prepareReport1TableAndTreeTable(parameterMap)

        val report1PivotBy = parameterMap["report1PivotBy"] as JSONArray
        parameterMap["category"] = report1PivotBy[0]
        val resultList = ArrayList<HashMap<String, Any?>>()
        val data = fiveDCReportDao.queryReport1PieChart(parameterMap)

        for (value in data) {
            val map = HashMap<String, Any?>()
            for (key in value.keys) {
                if ("category" == key) {
                    map["name"] = value[key]
                } else {
                    map["value"] = value[key]
                }
            }
            resultList.add(map)
        }

        resultList.sortWith(compareByDescending { Utils.parseBigDecimal(it["value"]) })
        return resultList
    }

    fun queryReport1TreemapChart(parameterMap: MutableMap<String, Any>): List<FiveDCTreemapBean> {
        val valuesAll = (parameterMap["report1Table"] as JSONObject).getJSONArray("selectedVirtualColumns")
        val selectedId = parameterMap["report1ChartValueId"] as String
        val values = valuesAll.filter { e: Any -> selectedId == (e as JSONObject).getString("id") }
        (parameterMap["report1Table"] as JSONObject)["selectedVirtualColumns"] = values

        this.prepareReport1TableAndTreeTable(parameterMap)

        val dataList = fiveDCReportDao.queryReport1TreemapChart(parameterMap)
        val resultList = ArrayList<FiveDCTreemapBean>()
        for (data in dataList) {
            this.convertReport1TreemapData(resultList, data)
        }
        return resultList
    }

    fun queryReport1TreeTableSub(parameterMap: MutableMap<String, Any>): Response {
        this.prepareReport1TableAndTreeTable(parameterMap)
        val report1PivotBy = parameterMap["report1PivotBy"] as JSONArray
        val parent = parameterMap["parent"] as JSONArray
        val expandValue = parameterMap["expandValue"] as Any
        val expandColumn = report1PivotBy[parent.size] // 展开的Column
        parameterMap["expandValue"] = expandValue
        parameterMap["expandColumn"] = expandColumn
        parameterMap["category"] = report1PivotBy[parent.size + 1] // 展开的Column的下一级

        val resultList = fiveDCReportDao.queryReport1TreeTableSub(parameterMap)
        for (map in resultList) {
            map["id"] = Utils.randomStr(12)
            map["hasChildren"] = report1PivotBy.size > parent.size + 2
            map["parent"] = parent.plus(expandValue)
        }
        return response.setBody(resultList)
    }

    fun queryReport1TableDetails(parameterMap: MutableMap<String, Any>): Response {
        val page = SimplePage<LinkedHashMap<String, Any>>(parameterMap)
        this.generateFilter(parameterMap)
        this.generateReport1Filter(parameterMap)

        parameterMap["tableName"] = fiveDCReportDao.queryReport1TableDetailsTableName(parameterMap)
        if (parameterMap["tableName"] == null) {
            return response.setBody(page)
        }
        parameterMap["hasDateColumn"] = SCPATableConfiguration.isColumnInTable(parameterMap["tableName"] as String, "DATE$")

        page.total = fiveDCReportDao.queryReport1TableDetailsCount(parameterMap)
        if (page.total > 0) {
            page.data = fiveDCReportDao.queryReport1TableDetails(parameterMap)
        }
        return response.setBody(page)
    }

    fun downloadReport1TableDetails(parameterMap: MutableMap<String, Any>, response: HttpServletResponse) {
        this.generateFilter(parameterMap)
        this.generateReport1Filter(parameterMap)
        val page = SimplePage<LinkedHashMap<String, Any>>(parameterMap)
        parameterMap["tableName"] = fiveDCReportDao.queryReport1TableDetailsTableName(parameterMap)
        parameterMap["hasDateColumn"] = SCPATableConfiguration.isColumnInTable(parameterMap["tableName"] as String, "DATE$")
        page.start = 0
        page.length = SimplePage.PAGE_MAX

        val fileName = "query_result_" + Utils.randomStr(4) + ".xlsx"
        excelTemplate.create(response, fileName, "com.scp.canvas.dao.IFiveDCReportDao.queryReport1TableDetails", parameterMap)
    }

    fun downloadReport1TreeTable(parameterMap: MutableMap<String, Any>, response: HttpServletResponse) {
        this.prepareReport1TableAndTreeTable(parameterMap)

        val report1PivotBy = parameterMap["report1PivotBy"] as JSONArray
        parameterMap["category"] = report1PivotBy[0]
        val resultMap = HashMap<String, Any>()
        val columns = ArrayList<String>()
        resultMap["columns"] = columns
        val fileName = "query_result_" + Utils.randomStr(4) + ".xlsx"
        excelTemplate.create(response, fileName, "com.scp.canvas.dao.IFiveDCReportDao.queryReport1TreeTable", parameterMap)
    }

    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = ["this.queryReport1"])
    fun saveReport1Comments(parameterMap: MutableMap<String, Any>): Response {
        val commentsUpdate = parameterMap["update"] as JSONObject
        val list = ArrayList<Map<String, String?>>()
        for (key in commentsUpdate.keys) {
            val map = HashMap<String, String?>()
            map["key"] = key
            map["comments"] = commentsUpdate.getString(key)
            list.add(map)
        }
        parameterMap["list"] = list
        fiveDCReportDao.saveReport1Comments(parameterMap)
        return response
    }

    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    fun queryReport2(parameterMap: MutableMap<String, Any>): Response {
        this.generateFilter(parameterMap)
        this.generateReport1Filter(parameterMap)

        val valuesAll = (parameterMap["report1Table"] as JSONObject).getJSONArray("selectedVirtualColumns")
        val selectedId = parameterMap["report2SelectedValues"] as JSONArray
        val valuesOrg = valuesAll.filter { e: Any -> selectedId.contains((e as JSONObject).getString("id")) }
        val values = ArrayList<Any>()
        for (id in selectedId) {
            for (v: Any in valuesOrg) {
                if ((v as JSONObject).getString("id") == id) {
                    values.add(v)
                }
            }
        }
        (parameterMap["report1Table"] as JSONObject)["selectedVirtualColumns"] = values

        if (values.isEmpty()) {
            return response
        }
        this.prepareReport1TableAndTreeTable(parameterMap)

        val dataList = fiveDCReportDao.queryReport2(parameterMap)
        val dataMap = HashMap<String, Any>()
        val xAxis = dataList.map { e -> e["AXIS"] }
        val yAxis = LinkedHashMap<String, Any>()
        dataMap["xAxis"] = xAxis
        if (dataList.isNotEmpty()) {
            for (key in dataList[0].keys) {
                if (key != "AXIS") {
                    val list = ArrayList<Any?>()
                    for (data in dataList) {
                        list.add(data[key])
                    }
                    yAxis[key] = list
                }
            }
        }

        dataMap["yAxis"] = yAxis
        return response.setBody(dataMap)
    }

    private fun prepareReport1TableAndTreeTable(parameterMap: MutableMap<String, Any>) {
        val report1SelectedValues = this.parseReport1SelectedVirtualColumns(parameterMap)
        val columns = ArrayList<String>()
        for (bean in report1SelectedValues) {
            for (token in bean.tokens) {
                if (token.getString("type") == "field") {
                    val values = token.getJSONArray("value")
                    for (value in values) {
                        columns.add((value as JSONArray).getString(1))
                    }
                }
            }

            for (token in bean.conditions) {
                if (token.getString("type") == "field") {
                    val values = token.getJSONArray("value")
                    for (value in values) {
                        columns.add((value as JSONArray).getString(1))
                    }
                }
            }
        }
        parameterMap["displayColumn"] = columns.distinct()

        val allMatchedList = fiveDCReportDao.queryReport1AllMatchedList(parameterMap)
        val tempTableMap = HashMap<String, ArrayList<Map<String, Any>>>()
        val hasDateColumnMap = HashMap<String, Boolean>()
        parameterMap["hasDateColumnMap"] = hasDateColumnMap
        val tableList = allMatchedList.map { e ->
            val list = tempTableMap.computeIfAbsent(e["TABLE_NAME"] as String) { _: String? -> ArrayList() }
            list.add(e)
            hasDateColumnMap[e["TABLE_NAME"] as String] = SCPATableConfiguration.isColumnInTable(e["TABLE_NAME"], "DATE$")

            return@map e["TABLE_NAME"]
        }.distinct()

        val unionColumn = ArrayList<String>()
        for (bean in report1SelectedValues) {
            unionColumn.add(bean.getSQL(parameterMap))
        }
        parameterMap["unionColumn"] = unionColumn
        parameterMap["tempTableMap"] = tempTableMap
        parameterMap["tableList"] = tableList
    }

    private fun parseReport1SelectedVirtualColumns(parameterMap: MutableMap<String, Any>): List<FiveDCReport1Bean> {
        val values = (parameterMap["report1Table"] as JSONObject).getJSONArray("selectedVirtualColumns")
        val resultList = ArrayList<FiveDCReport1Bean>()
        val existAlias = ArrayList<String>()
        for (element in values) {
            resultList.add(FiveDCReport1Bean(element as JSONObject, existAlias))
        }
        return resultList
    }

    private fun generateReport1Filter(parameterMap: MutableMap<String, Any>) {
        var report1PivotBy = parameterMap["report1PivotBy"] as JSONArray?

        if (report1PivotBy.isNullOrEmpty()) {
            report1PivotBy = JSONArray()
            report1PivotBy.add("ENTITY")
        }
        parameterMap["report1PivotBy"] = report1PivotBy
        val report1PivotByOrdered = ArrayList<Any>(report1PivotBy)
        parameterMap["report1PivotByOrdered"] = report1PivotByOrdered.sortedBy { it.toString() }

        val selectValues = (parameterMap["report1Table"] as JSONObject).getJSONObject("selectedCell").getJSONArray("values")
        parameterMap["selectValues"] = selectValues
    }

    private fun generateFilter(parameterMap: MutableMap<String, Any>) {
        this.generateCascaderFilterSQL(parameterMap, null, "MATERIAL_MASTER_V", "T", "_filters")
    }

    private fun convertReport1TreemapData(list: MutableList<FiveDCTreemapBean>, data: LinkedHashMap<String, Any?>) {
        val categories: MutableList<String> = ArrayList()
        var valueKey = ""
        for (key in data.keys) {
            if (key.startsWith("category")) {
                if (data[key] != null && StringUtils.isNotBlank(data[key] as String)) {
                    categories.add(data[key] as String)
                } else {
                    break
                }
            } else {
                valueKey = key
            }
        }
        // 不计算0值对象
        if (Utils.parseBigDecimal(data[valueKey], BigDecimal.ZERO) == BigDecimal.ZERO) {
            return
        }
        // 这边逻辑比较复杂, 所以用最笨的方法来描述了, 以免后期不好维护
        // 先把这一行数据转成treemap的数据
        // 第一个节点
        var child = ArrayList<FiveDCTreemapBean>()
        val root = FiveDCTreemapBean()
        root.name = categories[0]
        root.children = child

        // 中间节点
        for (i in 1 until categories.size - 1) {
            val treemap = FiveDCTreemapBean()
            treemap.name = categories[i]
            child.add(treemap)
            child = ArrayList()
            treemap.children = child
        }

        // 最后一个节点
        val lastNode = FiveDCTreemapBean()
        lastNode.name = categories[categories.size - 1]
        lastNode.value = Utils.parseBigDecimal(data[valueKey], null)
        child.add(lastNode)

        // 将这行treemap与原始数据相加
        // 先找到list中是否有这个数据节点
        val beanOpt = list.stream().filter { b: FiveDCTreemapBean ->
            b.name == categories[0]
        }.findFirst()
        if (beanOpt.isPresent) {
            val bean = beanOpt.get()
            bean.add(root) // 两个节点合并
        } else { //找不到的时候最省事, 直接放入list就可以了
            list.add(root)
        }
    }
}

