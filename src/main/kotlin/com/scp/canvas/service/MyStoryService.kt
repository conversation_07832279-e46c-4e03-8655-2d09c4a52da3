package com.scp.canvas.service

import com.adm.system.service.ISystemService
import com.alibaba.fastjson.JSON
import com.scp.canvas.dao.IMyStoryDao
import com.scp.toolbox.bean.TreeNode
import com.starter.context.bean.CacheRemove
import com.starter.context.bean.Configuration
import com.starter.context.bean.Response
import com.starter.context.bean.SimplePage
import com.starter.context.mail.MailBean
import com.starter.context.mail.MailFeignClient
import com.starter.login.bean.Session
import com.starter.utils.Utils
import jakarta.annotation.Resource
import org.apache.commons.lang3.StringUtils
import org.springframework.cache.annotation.Cacheable
import org.springframework.context.annotation.Scope
import org.springframework.stereotype.Service

@Service
@Scope("prototype")
class MyStoryService {

    companion object {
        const val PARENT_CODE = "menu510"
    }

    @Resource
    lateinit var myStoryDao: IMyStoryDao

    @Resource
    lateinit var response: Response

    @Resource
    lateinit var systemService: ISystemService

    @Resource
    lateinit var mailFeignClient: MailFeignClient

    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    fun initVariantPage(parameterMap: MutableMap<String, Any>, userid: String): Response {
        val resultMap: MutableMap<String, Any> = HashMap()
        resultMap["shareTo"] = myStoryDao.querySharedTo()
        resultMap["existsGroup"] = myStoryDao.queryExistsGroup()
        resultMap["isAdmin"] = myStoryDao.queryPageAdmin(userid, PARENT_CODE) > 0
        return response.setBody(resultMap)
    }

    fun queryPageList(parameterMap: MutableMap<String, Any>): Response {
        return response.setBody(myStoryDao.queryPageList(parameterMap))
    }

    fun queryComments(parameterMap: MutableMap<String, Any>, userid: String): Response {
        val commentsList: List<MutableMap<String, Any>> = myStoryDao.queryComments(parameterMap)
        if (commentsList.isEmpty() == false) {
            val replyList: List<MutableMap<String, Any>> = myStoryDao.queryReplies(commentsList)
            val replyMap: MutableMap<String?, MutableList<Map<String, Any>>> = java.util.HashMap()
            for (reply in replyList) {
                reply["IS_ME"] = StringUtils.equalsIgnoreCase(userid, reply["USER_ID"] as String?)
                val list = replyMap.computeIfAbsent(
                    reply["COMMENT_ID"] as String?
                ) { k: String? -> ArrayList() }
                list.add(reply)
            }
            for (comment in commentsList) {
                val commentID = comment["COMMENT_ID"] as String?
                comment["IS_ME"] = StringUtils.equalsIgnoreCase(userid, comment["USER_ID"] as String?)
                val replies: List<Map<String, Any>> = replyMap.getOrDefault(commentID, ArrayList())
                comment["REPLIES"] = replies
                comment["REPLIES_CNT"] = replies.size
            }
        }
        return response.setBody(commentsList)
    }

    fun deleteComment(parameterMap: MutableMap<String, Any>): Response {
        myStoryDao.deleteComment(parameterMap)

        parameterMap["operation"] = "DELETE COMMENT"
        myStoryDao.saveLogs(parameterMap)
        return response
    }

    fun sendComment(parameterMap: MutableMap<String, Any>, session: Session): Response {
        parameterMap["commentId"] = Utils.randomStr(12)
        myStoryDao.sendComment(parameterMap)

        parameterMap["operation"] = "CREATE COMMENT"
        myStoryDao.saveLogs(parameterMap)
        return response
    }

    fun queryCommentById(parameterMap: MutableMap<String, Any>): Response {
        return response.setBody(myStoryDao.queryCommentById(parameterMap))
    }

    fun queryStoryNameById(parameterMap: MutableMap<String, Any>): Response {
        return response.setBody(myStoryDao.queryStoryNameById(parameterMap))
    }

    fun modifyComment(parameterMap: MutableMap<String, Any>): Response {
        myStoryDao.modifyComment(parameterMap)

        parameterMap["operation"] = "UPDATE COMMENT"
        myStoryDao.saveLogs(parameterMap)
        return response
    }

    fun queryChangeLogs(parameterMap: MutableMap<String, Any>): Response {
        val page = SimplePage<Map<String, Any>>(parameterMap)

        val total: Int = myStoryDao.queryChangeLogsCount(parameterMap)
        page.total = total
        if (total > 0) {
            page.setData(myStoryDao.queryChangeLogs(parameterMap))
        }
        return response.setBody(page)
    }

    fun sendReply(parameterMap: MutableMap<String, Any>): Response {
        if (StringUtils.isNotBlank(parameterMap["content"] as String?)) {
            (parameterMap as HashMap<String, Any>)["replyId"] = Utils.randomStr(12)
            myStoryDao.sendReply(parameterMap)

            parameterMap["operation"] = "CREATE REPLY"
            myStoryDao.saveLogs(parameterMap)
        }
        return response
    }

    fun deleteReply(parameterMap: MutableMap<String, Any>): Response {
        myStoryDao.deleteReply(parameterMap)

        parameterMap["operation"] = "DELETE REPLY"
        myStoryDao.saveLogs(parameterMap)
        return response
    }

    fun queryVariantList(parameterMap: MutableMap<String, Any>, session: Session): Response {
        parameterMap["userid"] = session.userid

        val resultList = ArrayList<TreeNode>()
        val myVariant = TreeNode()
        myVariant.label = "我创建的"
        myVariant.children = Utils.parseTreeNodes(myStoryDao.queryMyVariantList(parameterMap))
        resultList.add(myVariant)

        val sharedVariant = TreeNode()
        sharedVariant.label = "分享给我的"
        sharedVariant.children = Utils.parseTreeNodes(myStoryDao.querySharedVariantList(parameterMap))
        if (sharedVariant.children.size > 0) {
            resultList.add(sharedVariant)
        }

        if (myStoryDao.queryPageAdmin(session.userid, PARENT_CODE) > 0) {
            val allVariant = TreeNode()
            allVariant.label = "管理员"
            allVariant.children = Utils.parseTreeNodes(myStoryDao.queryAllVariantList(parameterMap))
            if (allVariant.children.size > 0) {
                resultList.add(allVariant)
            }
        }

        return response.setBody(resultList)
    }

    fun saveVariant(parameterMap: MutableMap<String, Any>): Response {
        parameterMap["variantID"] = Utils.randomStr(16)
        val shareTo = parameterMap["shareTo"] as List<*>
        parameterMap["authType"] = if (shareTo.contains("[ALL USER]")) "PUBLIC" else "PRIVATE"
        if (shareTo.isEmpty() == false) {
            myStoryDao.saveSharedTo(parameterMap)
        }
        myStoryDao.saveVariant(parameterMap)
        myStoryDao.saveBuiltInPages(parameterMap)
        return response
    }

    fun queryVariantByKey(parameterMap: MutableMap<String, Any>): Response {
        return response.setBody(JSON.parse(myStoryDao.queryVariantByKey(parameterMap)))
    }

    fun queryVariantConfigByKey(parameterMap: MutableMap<String, Any>): Response {
        val resultMap = myStoryDao.queryVariantConfigByKey(parameterMap)
        resultMap["shareTo"] = myStoryDao.querySharedToByKey(parameterMap)
        return response.setBody(resultMap)
    }

    fun deleteVariant(parameterMap: MutableMap<String, Any>): Response {
        val commentsCnt =  myStoryDao.queryCommentCntByVariantId(parameterMap)
        if(commentsCnt == 0){
            myStoryDao.deleteVariant(parameterMap)
        } else {
            response.setBody(-1)
        }
        return response
    }

    fun modifyVariant(parameterMap: MutableMap<String, Any>): Response {
        val shareTo = parameterMap["shareTo"] as List<*>
        parameterMap["authType"] = if (shareTo.contains("[ALL USER]")) "PUBLIC" else "PRIVATE"
        if (shareTo.isEmpty() == false) {
            myStoryDao.modifySharedTo(parameterMap)
        }
        myStoryDao.modifyVariant(parameterMap)
        parameterMap["variantID"] = parameterMap["key"] as String
        myStoryDao.saveBuiltInPages(parameterMap)
        return response
    }

    fun shareCondition(userid: String, username: String, email: String, parameterMap: MutableMap<String, Any>): Response {
        val users = parameterMap["users"] as List<*>?
        if (users == null || users.isEmpty()) {
            return response.setBody("Please select at least one user to share!")
        }
        val name = parameterMap["name"] as String?
        val cid = parameterMap["cid"] as String?

        // send notice mail
        val body = StringBuilder()
        val remarks = parameterMap["remarks"] as String?
        val mailBean = MailBean()
        mailBean.subject = "【Variant Sharing】$username shared a variant [$name] with you"
        val to: MutableList<String?> = java.util.ArrayList()
        for (user in users) {
            to.add("$<EMAIL>")
        }
        mailBean.to = StringUtils.join(to, ",")
        mailBean.cc = email
        body.append("<div style='font-size:10pt;font-family:DengXian;'>")
        body.append("<p>")
        body.append(username).append(" shared a variant [").append(name).append("] with you")
        body.append("</p>")
        if (StringUtils.isNotBlank(remarks)) {
            body.append("<br/>")
            body.append(remarks)
            body.append("<br/>")
        }
        body.append("Click <a href='https://scp-dss.cn.schneider-electric.com/#")
        body.append(parameterMap["url"])
        body.append("?cid=")
        body.append(cid)
        body.append("'><b><i>")
        body.append("2024 Performance Dashboard")
        body.append("</i></b></a> for more information")
        val style =
            "<style>p{font-size: 10pt;font-family:DengXian;padding:0;margin:0} span{font-size: 10pt;font-family:DengXian;} div{font-size: 10pt;font-family:DengXian;}</style>"
        var signatrue: String = systemService.getMailSignature(userid)
        if (StringUtils.isBlank(signatrue)) {
            signatrue = ""
        }
        signatrue = "<br><br><br>$signatrue"
        mailBean.body = style + body + signatrue
        mailFeignClient.sendAsync(mailBean)
        return response
    }

    fun queryConditionById(parameterMap: MutableMap<String, Any>): Response {
        val conditions: Map<String, Any>? = myStoryDao.queryConditionById(parameterMap)
        if (conditions.isNullOrEmpty()) {
            return response.setBody("Invalid Parameter: " + parameterMap["name"])
        }
        return response.setBody(JSON.parse(conditions["CONDITIONS"] as String?))
    }

    fun queryDefaultCondition(parameterMap: MutableMap<String, Any>): Response {
        val conditions: Map<String, Any>? = myStoryDao.queryDefaultCondition(parameterMap)
        if (conditions.isNullOrEmpty()) {
            return response
        }
        return response.setBody(conditions)
    }

    fun updateDefaultVariant(parameterMap: MutableMap<String, Any>): Response {
        val deleted = myStoryDao.deleteDefaultVariant(parameterMap)
        // 先尝试删除, 如果删除成功, 则证明用户是取消默认页, 也无需执行merge操作
        // 否则用户为添加默认页
        if (deleted == 0) {
            myStoryDao.updateDefaultVariant(parameterMap)
        }
        return response
    }

    fun queryManualInputById(parameterMap: MutableMap<String, Any>): Response {
        return response.setBody(myStoryDao.queryManualInputById(parameterMap))
    }
}

