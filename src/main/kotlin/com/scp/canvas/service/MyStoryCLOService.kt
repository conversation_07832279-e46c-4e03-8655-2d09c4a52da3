package com.scp.canvas.service

import com.alibaba.fastjson.JSON
import com.alibaba.fastjson.JSONArray
import com.alibaba.fastjson.JSONObject
import com.scp.canvas.dao.IMyStoryCLODao
import com.starter.context.bean.Configuration
import com.starter.context.bean.Response
import com.starter.context.servlet.ServiceHelper
import com.starter.utils.Utils
import jakarta.annotation.Resource
import org.springframework.cache.annotation.Cacheable
import org.springframework.context.annotation.Scope
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.math.RoundingMode
import java.util.stream.Collectors

@Service
@Scope("prototype")
class MyStoryCLOService : ServiceHelper() {

    @Resource
    lateinit var myStoryCLODao: IMyStoryCLODao

    @Resource
    lateinit var response: Response

    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    fun initPage(): Response {
        val resultMap = HashMap<String, Any>()
        resultMap["filterOpts"] = this.parseCascader(myStoryCLODao.queryFilterOpts())
        resultMap["pivotOpts"] = myStoryCLODao.queryPivotOpts()
        return response.setBody(resultMap)
    }

    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    fun queryReport1(parameterMap: MutableMap<String, Any>): Response {
        this.generateFilter(parameterMap)

        val report1Categories = parameterMap["report1Categories"] as JSONArray
        if (report1Categories.isEmpty()) {
            report1Categories.add("T0_ENTITY")
        }
        parameterMap["category"] = report1Categories[0]
        val resultList = myStoryCLODao.queryReport1(parameterMap)
        for (map in resultList) {
            map["parent"] = ArrayList<String>()
            map["id"] = Utils.randomStr(12)
            map["hasChildren"] = report1Categories.size > 1 && "Total" != map["category"]
        }

        return response.setBody(resultList)
    }

    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    fun queryReport1Sub(parameterMap: MutableMap<String, Any>): Response {
        this.generateFilter(parameterMap)

        val report1Categories = parameterMap["report1Categories"] as JSONArray
        val parent = parameterMap["parent"] as JSONArray
        val expandValue = parameterMap["expandValue"] as Any
        val expandColumn = report1Categories[parent.size] // 展开的Column
        parameterMap["expandValue"] = expandValue
        parameterMap["expandColumn"] = expandColumn
        parameterMap["category"] = report1Categories[parent.size + 1] // 展开的Column的下一级
        val resultList = myStoryCLODao.queryReport1Sub(parameterMap)
        for (map in resultList) {
            map["id"] = Utils.randomStr(12)
            map["hasChildren"] = report1Categories.size > parent.size + 2
            map["parent"] = parent.plus(expandValue)
        }
        return response.setBody(resultList)
    }

    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    fun queryReport2(parameterMap: MutableMap<String, Any>): Response {
        this.generateFilter(parameterMap)
        val dataList = myStoryCLODao.queryReport2(parameterMap)
        val resultList = ArrayList<MutableMap<String, Any>>()
        val mergethreshold = parameterMap["report2MergeThreshold"] as Int
        var total = 0.0
        var stack = 0.0
        var stackValue = 0.0
        for (data in dataList) {
            total += Utils.parseDouble(data["value"])
        }
        for (data in dataList) {
            if (stack > total * (1 - mergethreshold / 100.0)) {
                stackValue += Utils.parseDouble(data["value"])
            } else {
                resultList.add(data)
            }
            stack += Utils.parseDouble(data["value"])
        }
        if (stackValue > 0) {
            val stackMap = HashMap<String, Any>()
            stackMap["name"] = "[Others]"
            stackMap["value"] = stackValue
            resultList.add(stackMap);
        }
        return response.setBody(resultList)
    }

    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    fun queryReport3(parameterMap: MutableMap<String, Any>): Response {
        this.generateFilter(parameterMap)
        val selectObj = JSON.parseObject(parameterMap["report3SelectedValues"] as String?)
        if (selectObj != null) {
            if ("Total".equals(selectObj.getString("category"), ignoreCase = true)) {
                parameterMap["report3Values"] = java.util.ArrayList<Any>()
            } else {
                val values: MutableList<String> = java.util.ArrayList(
                    selectObj.getJSONArray("parent").toJavaList(
                        String::class.java
                    )
                )
                values.add(selectObj.getString("category"))
                parameterMap["report3Values"] = values
            }
        }

        val data: List<Map<String, Any>> = myStoryCLODao.queryReport3(parameterMap)

        // 做数据转置
        val legend = data.stream().map { e: Map<String, Any> ->
            e["T0_CLO_LT_GROUP"] as String?
        }.distinct().sorted().toList()
        val xAxis = data.stream().map { e: Map<String, Any> ->
            e["XAIXS"] as String?
        }.distinct().sorted().toList()

        val tempSeries: MutableMap<String?, MutableMap<String?, MutableMap<String, Any?>>> = HashMap()
        val series: MutableList<Map<String, Any?>> = java.util.ArrayList()
        val resultMap: MutableMap<String, Any> = HashMap()

        for (map in data) {
            val lt = map["T0_CLO_LT_GROUP"] as String?
            val x = map["XAIXS"] as String?
            val temp1 = tempSeries.computeIfAbsent(lt) { k: String? -> HashMap() }
            val temp2 = temp1.computeIfAbsent(x) { k: String? -> HashMap() }
            temp2["MEET_VAL"] = map["MEET_VAL"]
            temp2["FAIL_VAL"] = map["FAIL_VAL"]
        }

        val meetTotalMap: MutableMap<String?, BigDecimal> = HashMap()
        val failTotalMap: MutableMap<String?, BigDecimal> = HashMap()
        for (l in legend) {
            val s: MutableMap<String, Any?> = LinkedHashMap()
            val d: Map<String?, MutableMap<String, Any?>> = tempSeries[l]!!

            val meetVal: MutableList<BigDecimal?> = java.util.ArrayList()
            val failVal: MutableList<BigDecimal?> = java.util.ArrayList()

            for (x in xAxis) {
                val v: Map<String, Any?>? = d[x]
                if (v != null) {
                    val meet = Utils.parseBigDecimal(v["MEET_VAL"])
                    val fail = Utils.parseBigDecimal(v["FAIL_VAL"])
                    meetVal.add(meet)
                    failVal.add(fail)
                    var tv = meetTotalMap.computeIfAbsent(x) { k: String? -> BigDecimal.ZERO }
                    meetTotalMap[x] = tv.add(meet)

                    tv = failTotalMap.computeIfAbsent(x) { k: String? -> BigDecimal.ZERO }
                    failTotalMap[x] = tv.add(fail)
                } else {
                    meetVal.add(null)
                    failVal.add(null)
                }
            }
            s["name"] = l
            s["meetVal"] = meetVal
            s["failVal"] = failVal
            series.add(s)
        }

        val percent: MutableList<BigDecimal> = java.util.ArrayList()
        var minPercent = BigDecimal.valueOf(200)
        for (x in xAxis) {
            val meet = meetTotalMap.getOrDefault(x, BigDecimal.ZERO)
            val fail = failTotalMap.getOrDefault(x, BigDecimal.ZERO)
            val total = meet.add(fail.abs())
            var per = BigDecimal.ZERO
            if (total.compareTo(BigDecimal.ZERO) != 0) {
                per = meet.divide(total, 3, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(1, RoundingMode.HALF_UP)
            }
            minPercent = per.min(minPercent)
            percent.add(per)
        }

        resultMap["minPercent"] = minPercent.subtract(BigDecimal.TEN).min(BigDecimal.valueOf(100))
        resultMap["percent"] = percent
        resultMap["series"] = series
        resultMap["xAxis"] = xAxis
        return response.setBody(resultMap)
    }

    private fun generateFilter(parameterMap: MutableMap<String, Any>) {
        // 把Filter的Key加上T0_前缀
        val scpFilter = parameterMap["\$scpFilter"] as MutableMap<*, *>?
        if (scpFilter != null) {
            val filter = scpFilter["filter"] as List<*>
            val cascader = scpFilter["cascader"] as JSONArray

            for (element in cascader) {
                (element as JSONArray)[0] = "T0_" + element[0]
            }

            for (element in filter) {
                (element as JSONObject)["fields"] = (element["fields"] as JSONArray).stream().map { obj: Any -> "T0_" + obj as String }.collect(Collectors.toList())
            }
        }

        // 生成筛选条件
        this.generateCascaderFilterSQL(parameterMap, null, "CLO_STRUCTURE_HIST", "T", "_filters")
        // 生成CLO专有的条件
        this.generateCascaderFilterSQL("filterList", parameterMap, null, "CLO_STRUCTURE_HIST", "T", "_filters2")
    }
}
