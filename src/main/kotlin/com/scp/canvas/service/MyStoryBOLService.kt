package com.scp.canvas.service

import com.alibaba.fastjson.JSONArray
import com.scp.canvas.dao.IMyStoryBOLDao
import com.starter.context.bean.Configuration
import com.starter.context.bean.Response
import com.starter.context.servlet.ServiceHelper
import com.starter.utils.Utils
import jakarta.annotation.Resource
import org.codehaus.plexus.util.StringUtils
import org.springframework.cache.annotation.Cacheable
import org.springframework.context.annotation.Scope
import org.springframework.stereotype.Service
import java.math.BigDecimal

@Service
@Scope("prototype")
class MyStoryBOLService : ServiceHelper() {

    @Resource
    lateinit var myStoryBOLDao: IMyStoryBOLDao

    @Resource
    lateinit var response: Response

    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    fun initPage(): Response {
        val resultMap = HashMap<String, Any>()
        resultMap["pivotOpts"] = myStoryBOLDao.queryPivotOpts()
        resultMap["stackOpts"] = myStoryBOLDao.queryStackOpts()
        resultMap["filterOpts"] = this.parseCascader(myStoryBOLDao.queryFilterOpts())
        return response.setBody(resultMap)
    }

    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    fun queryReport1(parameterMap: MutableMap<String, Any>): Response {
        this.generateFilter(parameterMap)
        this.generateCascaderFilterSQL(parameterMap, null, "DEMAND_BACKLOG_V", "T", "_filter2")

        val report1Categories = parameterMap["report1Categories"] as JSONArray
        if (report1Categories.isEmpty()) {
            report1Categories.add("ENTITY")
        }
        parameterMap["category"] = report1Categories[0]
        val resultList = myStoryBOLDao.queryReport1(parameterMap)
        for (map in resultList) {
            map["parent"] = ArrayList<String>()
            map["id"] = Utils.randomStr(12)
            map["hasChildren"] = report1Categories.size > 1 && "Total" != map["category"]
        }
        return response.setBody(resultList)
    }

    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    fun queryReport1Sub(parameterMap: MutableMap<String, Any>): Response {
        this.generateFilter(parameterMap)

        val report1Categories = parameterMap["report1Categories"] as JSONArray
        val parent = parameterMap["parent"] as JSONArray
        val expandValue = parameterMap["expandValue"] as Any
        val expandColumn = report1Categories[parent.size] // 展开的Column
        parameterMap["expandValue"] = expandValue
        parameterMap["expandColumn"] = expandColumn
        parameterMap["category"] = report1Categories[parent.size + 1] // 展开的Column的下一级
        val resultList = myStoryBOLDao.queryReport1Sub(parameterMap)
        for (map in resultList) {
            map["id"] = Utils.randomStr(12)
            map["hasChildren"] = report1Categories.size > parent.size + 2
            map["parent"] = parent.plus(expandValue)
        }
        return response.setBody(resultList)
    }

    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    fun queryReport2(parameterMap: MutableMap<String, Any>): Response {
        this.generateFilter(parameterMap)
        val report2StackBy = parameterMap["report2StackBy"] as String
        if (StringUtils.isBlank(report2StackBy)) {
            parameterMap["report2StackBy"] = "'DELAY LINES'"
        }

        val dataList = myStoryBOLDao.queryReport2(parameterMap)
        val sumMap = LinkedHashMap<String, Int>()
        val plantList = ArrayList<String>()
        val yAxis = ArrayList<String>()
        val total = ArrayList<Int>()

        // 先排序, 找出来料号顺序, 放入yAxis
        for (data in dataList) {
            val category = data["CATEGORY"] as String
            var sum = sumMap.computeIfAbsent(category) { k: String -> 0 }
            sum += Utils.parseInt(data["CNT"])
            sumMap[category] = sum
        }
        var list: List<Map.Entry<String, Int>> = ArrayList<Map.Entry<String, Int>>(sumMap.entries)
        // 根据值对ArrayList进行排序
        list = list.sortedBy {
            it.value
        }
        for ((key, value) in list) {
            yAxis.add(key)
            total.add(value)
        }

        // 再找可用的plantCode做堆叠准备
        for (data in dataList) {
            val stackBy = data["STACK_BY"] as String
            // save legend
            if (!plantList.contains(stackBy)) {
                plantList.add(stackBy)
            }
        }
        val sortedPlantList = plantList.sortedBy {
            it.toString()
        }

        // 最后找每个material在不同plant下的数量
        val series = LinkedHashMap<String, ArrayList<Int>>()
        val dataMap = HashMap<String, Int>()
        for (data in dataList) {
            dataMap[data["CATEGORY"].toString() + "@" + data["STACK_BY"].toString()] = Utils.parseInt(data["CNT"])
        }
        for (plant in sortedPlantList) {
            val temp = series.computeIfAbsent(plant) { key -> ArrayList() }
            for (material in yAxis) {
                temp.add(dataMap.getOrDefault("$material@$plant", 0))
            }
        }

        // sort legend
        val resultMap = HashMap<String, Any>()
        resultMap["yAxis"] = yAxis
        resultMap["total"] = total
        resultMap["series"] = series
        return response.setBody(resultMap)
    }

    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    fun queryReport3(parameterMap: MutableMap<String, Any>): Response {
        this.generateFilter(parameterMap)
        val resultMap: MutableMap<String, Any> = HashMap()

        val xAxis: MutableList<String?> = ArrayList()
        val yAxis1: MutableList<BigDecimal> = ArrayList()
        val yAxis2: MutableList<BigDecimal> = ArrayList()
        val yAxis3: MutableList<BigDecimal> = ArrayList()
        val yAxis4: MutableList<BigDecimal> = ArrayList()
        val yAxis5: MutableList<BigDecimal> = ArrayList()
        val yAxis6: MutableList<BigDecimal> = ArrayList()
        val yAxis7: MutableList<BigDecimal> = ArrayList()
        val yAxis8: MutableList<BigDecimal> = ArrayList()
        val dataList: List<Map<String, Any>> = myStoryBOLDao.queryReport3(parameterMap)

        for (map in dataList) {
            xAxis.add(map["xAxis"] as String?)
            yAxis1.add(Utils.parseBigDecimal(map["yAxis1"]))
            yAxis2.add(Utils.parseBigDecimal(map["yAxis2"]))
            yAxis3.add(Utils.parseBigDecimal(map["yAxis3"]))
            yAxis4.add(Utils.parseBigDecimal(map["yAxis4"]))
            yAxis5.add(Utils.parseBigDecimal(map["yAxis5"]))
            yAxis6.add(Utils.parseBigDecimal(map["yAxis6"]))
            yAxis7.add(Utils.parseBigDecimal(map["yAxis7"]))
            yAxis8.add(Utils.parseBigDecimal(map["yAxis8"]))
        }

        resultMap["xAxis"] = xAxis
        resultMap["yAxis1"] = yAxis1
        resultMap["yAxis2"] = yAxis2
        resultMap["yAxis3"] = yAxis3
        resultMap["yAxis4"] = yAxis4
        resultMap["yAxis5"] = yAxis5
        resultMap["yAxis6"] = yAxis6
        resultMap["yAxis7"] = yAxis7
        resultMap["yAxis8"] = yAxis8
        return response.setBody(resultMap)
    }

    private fun generateFilter(parameterMap: MutableMap<String, Any>) {
        // 生成筛选条件
        this.generateCascaderFilterSQL(parameterMap, null, "DEMAND_BACK_ORDER_V", "T", "_filters")
        this.generateCascaderFilterSQL("filterList", parameterMap, null, "DEMAND_BACK_ORDER_V", "T", "_filters2")

        val orderType = parameterMap["orderType"] as String?
        var qtyColumn = "OPEN_SO_W_O_GI"
        if ("OPEN_SO_W_O_GI" == orderType) {
            qtyColumn = "OPEN_SO_W_O_GI"
        } else if ("OPEN_SO_W_O_DEL" == orderType) {
            qtyColumn = "OPEN_SO_W_O_DEL"
        }
        parameterMap["qtyColumn"] = qtyColumn
    }
}
