package com.scp.canvas.service

import com.scp.canvas.dao.IMyStoryCatalogDao
import com.starter.context.bean.Response
import com.starter.context.bean.SimplePage
import com.starter.login.bean.Session
import com.starter.utils.Utils
import jakarta.annotation.Resource
import org.apache.commons.lang3.StringUtils
import org.springframework.context.annotation.Scope
import org.springframework.stereotype.Service

@Service
@Scope("prototype")
class MyStoryCatalogService {

    @Resource
    lateinit var myStoryCatalogDao: IMyStoryCatalogDao

    @Resource
    lateinit var response: Response

    fun initCatalogPage(parameterMap: MutableMap<String, Any>): Response {
        val pageList: List<MutableMap<String, Any>> = myStoryCatalogDao.queryPagesById(parameterMap)
        val resultMap = HashMap<String, Any>()
        resultMap["pageList"] = pageList
        return response.setBody(resultMap)
    }

    fun savePageOrder(parameterMap: MutableMap<String, Any>): Response {
        val pageList = parameterMap["pageList"] as List<*>
        if (pageList.isNotEmpty()) {
            myStoryCatalogDao.savePageOrder(parameterMap)
        }
        return response
    }

    fun deletePage(parameterMap: MutableMap<String, Any>): Response {
        myStoryCatalogDao.deletePage(parameterMap)
        return response
    }

    fun savePage(parameterMap: MutableMap<String, Any>): Response {
        parameterMap["pageId"] = Utils.randomStr(12)
        myStoryCatalogDao.savePage(parameterMap)
        return response
    }

    fun modifyPage(parameterMap: MutableMap<String, Any>): Response {
        myStoryCatalogDao.modifyPage(parameterMap)
        return response
    }

    fun queryPageById(parameterMap: MutableMap<String, Any>): Response {
        return response.setBody(myStoryCatalogDao.queryPageById(parameterMap))
    }
}

