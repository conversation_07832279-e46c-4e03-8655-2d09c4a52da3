package com.scp.canvas.service

import com.alibaba.fastjson.JSONArray
import com.scp.canvas.dao.IMyStoryInventoryDao
import com.starter.context.bean.Configuration
import com.starter.context.bean.Response
import com.starter.context.configuration.SCPATableConfiguration
import com.starter.context.servlet.ServiceHelper
import com.starter.utils.Utils
import jakarta.annotation.Resource
import org.codehaus.plexus.util.StringUtils
import org.springframework.cache.annotation.Cacheable
import org.springframework.context.annotation.Scope
import org.springframework.stereotype.Service

@Service
@Scope("prototype")
class MyStoryInventoryService : ServiceHelper() {

    @Resource
    lateinit var myStoryInventoryDao: IMyStoryInventoryDao

    @Resource
    lateinit var response: Response


    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    fun initPage(): Response {
        val resultMap = HashMap<String, Any>()
        resultMap["filterOpts"] = this.parseCascader(myStoryInventoryDao.queryFilterOpts())
        resultMap["pivotOpts"] = myStoryInventoryDao.queryPivotOpts()
        resultMap["stackOpts"] = myStoryInventoryDao.queryStackOpts()
        return response.setBody(resultMap)
    }

    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    fun queryReport1(parameterMap: MutableMap<String, Any>): Response {
        this.generateFilter(parameterMap)

        val report1Categories = parameterMap["report1Categories"] as JSONArray
        if (report1Categories.isEmpty()) {
            report1Categories.add("ENTITY")
        }

        parameterMap["category"] = report1Categories[0]
        val resultList = myStoryInventoryDao.queryReport1(parameterMap)
        for (map in resultList) {
            map["parent"] = ArrayList<String>()
            map["id"] = Utils.randomStr(12)
            map["hasChildren"] = report1Categories.size > 1 && "Total" != map["category"]
        }
        return response.setBody(resultList)
    }

    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    fun queryReport1Sub(parameterMap: MutableMap<String, Any>): Response {
        this.generateFilter(parameterMap)

        val report1Categories = parameterMap["report1Categories"] as JSONArray
        val parent = parameterMap["parent"] as JSONArray
        val expandValue = parameterMap["expandValue"] as Any
        val expandColumn = report1Categories[parent.size] // 展开的Column
        parameterMap["expandValue"] = expandValue
        parameterMap["expandColumn"] = expandColumn
        parameterMap["category"] = report1Categories[parent.size + 1] // 展开的Column的下一级

        val resultList = myStoryInventoryDao.queryReport1Sub(parameterMap)
        for (map in resultList) {
            map["id"] = Utils.randomStr(12)
            map["hasChildren"] = report1Categories.size > parent.size + 2
            map["parent"] = parent.plus(expandValue)
        }
        return response.setBody(resultList)
    }

    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    fun queryReport2(parameterMap: MutableMap<String, Any>): Response {
        this.generateFilter(parameterMap)
        val report2StackBy = parameterMap["report2StackBy"] as String
        if (StringUtils.isBlank(report2StackBy)) {
            parameterMap["report2StackBy"] = "GROSS_INV"
        }

        val dataList = myStoryInventoryDao.queryReport2(parameterMap)
        val sumMap = LinkedHashMap<String, Int>()
        val plantList = ArrayList<String>()
        val yAxis = ArrayList<String>()
        val total = ArrayList<Int>()

        // 先排序, 找出来料号顺序, 放入yAxis
        for (data in dataList) {
            val category = data["CATEGORY"] as String
            var sum = sumMap.computeIfAbsent(category) { k: String -> 0 }
            sum += Utils.parseInt(data["VAL"])
            sumMap[category] = sum
        }
        var list: List<Map.Entry<String, Int>> = ArrayList<Map.Entry<String, Int>>(sumMap.entries)
        // 根据值对ArrayList进行排序
        list = list.sortedBy {
            it.value
        }
        for ((key, value) in list) {
            yAxis.add(key)
            total.add(value)
        }

        // 再找可用的plantCode做堆叠准备
        for (data in dataList) {
            val stackBy = data["STACK_BY"] as String
            // save legend
            if (!plantList.contains(stackBy)) {
                plantList.add(stackBy)
            }
        }
        val sortedPlantList = plantList.sortedBy {
            it.toString()
        }

        // 最后找每个material在不同plant下的数量
        val series = LinkedHashMap<String, ArrayList<Int>>()
        val dataMap = HashMap<String, Int>()
        for (data in dataList) {
            dataMap[data["CATEGORY"].toString() + "@" + data["STACK_BY"].toString()] = Utils.parseInt(data["VAL"])
        }
        for (plant in sortedPlantList) {
            val temp = series.computeIfAbsent(plant) { key -> ArrayList() }
            for (material in yAxis) {
                temp.add(dataMap.getOrDefault("$material@$plant", 0))
            }
        }

        // sort legend
        val resultMap = HashMap<String, Any>()
        resultMap["yAxis"] = yAxis
        resultMap["total"] = total
        resultMap["series"] = series
        return response.setBody(resultMap)
    }

    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    fun queryReport3(parameterMap: MutableMap<String, Any>): Response {
        this.generateFilter(parameterMap)
        val resultMap = HashMap<String, Any>()

        val report3Category = parameterMap["report3Category"] as String
        val report3ValueType = parameterMap["report3ValueType"] as String

        val dataList: List<MutableMap<String, Any>>
        if (StringUtils.contains(report3Category, "UHS") ||
            "THEO_PROVISION" == report3Category || "EXCESS" == report3Category || "MISSING" == report3Category ||
            "SPECIAL" == report3Category || "GROSS_PERCENT" == report3Category || "NET_PERCENT" == report3Category
        ) {
            val valueColumn = when (report3ValueType) {
                "Value" -> when (report3Category) {
                    "GROSS_UHS" -> "SUM(NVL(T.GROSS_UNHEALTHY_VALUE, 0))"
                    "GROSS_PERCENT" -> "CASE WHEN SUM(T.STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT) = 0 THEN 0 ELSE SUM(T.GROSS_UNHEALTHY_VALUE) / SUM(T.STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT) * 100.0 END"
                    "NET_PERCENT" -> "CASE WHEN SUM(T.STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT) = 0 THEN 0 ELSE SUM(T.NET_UNHEALTHY_VALUE) / SUM(T.STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT) * 100.0 END"
                    "THEO_PROVISION" -> "SUM(NVL(T.HS_THEO_PROV_VALUE, 0))"
                    "EXCESS" -> "SUM(NVL(T.HS_EXCESS_VALUE, 0))"
                    "MISSING" -> "SUM(NVL(T.HS_MISSING_VALUE, 0))"
                    "SPECIAL" -> "SUM(CASE WHEN T.HS_RCA LIKE 'E98%' THEN (NVL(T.HS_EXCESS_VALUE, 0) + NVL(T.HS_THEO_PROV_VALUE, 0)) ELSE 0 END)"
                    else -> "0"
                }

                "Qty" -> when (report3Category) {
                    "GROSS_UHS" -> "0"
                    "GROSS_PERCENT" -> "CASE WHEN SUM(T.STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT) = 0 THEN 0 ELSE SUM(T.GROSS_UNHEALTHY_VALUE) / SUM(T.STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT) * 100.0 END"
                    "NET_PERCENT" -> "CASE WHEN SUM(T.STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT) = 0 THEN 0 ELSE SUM(T.NET_UNHEALTHY_VALUE) / SUM(T.STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT) * 100.0 END"
                    "THEO_PROVISION" -> "SUM(NVL(T.HS_THEO_PROV_QTY, 0))"
                    "EXCESS" -> "SUM(NVL(T.HS_EXCESS, 0))"
                    "MISSING" -> "SUM(NVL(T.HS_MISSING, 0))"
                    "SPECIAL" -> "SUM(CASE WHEN T.HS_RCA LIKE 'E98%' THEN (NVL(T.HS_EXCESS, 0) + NVL(T.HS_THEO_PROV_QTY, 0)) ELSE 0 END)"
                    else -> "0"
                }

                else -> "0"
            }

            val report1Categories = parameterMap["report1Categories"] as JSONArray
            val report1SelectedValues = parameterMap["report1SelectedValues"] as JSONArray
            val newReport1Categories = ArrayList<String>()
            for (index in report1SelectedValues.indices) {
                val category = report1Categories[index] as String
                if (SCPATableConfiguration.isColumnInTable("UHS_RCA_HIST", category as String) == false) {
                    newReport1Categories.add("#NULL")
                } else {
                    newReport1Categories.add(category)
                }
            }

            parameterMap["report1Categories"] = newReport1Categories
            parameterMap["valueColumn"] = valueColumn
            dataList = myStoryInventoryDao.queryReport3UHS(parameterMap)
        } else {
            val sohValue =
                "NVL(T.UU_STOCK_VALUE, 0) + NVL(T.STOCK_IN_QI_VALUE, 0) + NVL(T.RESTRICTED_STOCK_VALUE, 0) + NVL(T.BLOCKED_STOCK_VALUE, 0) + NVL(T.RETURNS_STOCK_VALUE, 0)"
            val gitValue = "NVL(T.GIT_VALUE, 0)"
            val wipValue = "NVL(T.WIP_VALUE, 0)"

            val sohQty = "NVL(T.UU_STOCK, 0) + NVL(T.STOCK_IN_QI, 0) + NVL(T.RESTRICTED_STOCK, 0) + NVL(T.BLOCKED_STOCK, 0) + NVL(T.RETURNS_STOCK, 0)"
            val gitQty = "0"
            val wipQty = "0"

            val valueColumn = when (report3ValueType) {
                "Value" -> when (report3Category) {
                    "GROSS_INV" -> "$sohValue + $gitValue + $wipValue"
                    "FG" -> "$sohValue + $gitValue"
                    "SEMI_FG" -> "$sohValue + $gitValue"
                    "RM" -> "$sohValue + $gitValue"
                    "BLOCK_RM" -> "NVL(T.BLOCKED_STOCK_VALUE, 0)"
                    "BLOCK_FG" -> "NVL(T.BLOCKED_STOCK_VALUE, 0)"
                    "TOP20_ENTITY" -> "$sohValue + $gitValue"
                    "TOP20_PLANNER" -> "$sohValue + $gitValue"
                    "GIT" -> gitValue
                    "WIP" -> wipValue
                    "UD_GT_30_CD" -> "NVL(T.UD_GT_30_CD_VALUE, 0)"
                    "UD_LT_7_CD" -> "NVL(T.UD_LT_7_CD_VALUE, 0)"
                    "UD_LT_30_CD" -> "NVL(T.UD_LT_30_CD_VALUE, 0)"
                    "VENDOR_CONSIGN" -> sohValue
                    else -> sohValue
                }

                "Qty" -> when (report3Category) {
                    "GROSS_INV" -> "$sohQty + $gitQty + $wipQty"
                    "FG" -> "$sohQty + $gitQty"
                    "SEMI_FG" -> "$sohQty + $gitQty"
                    "RM" -> "$sohQty + $gitQty"
                    "BLOCK_RM" -> "NVL(T.BLOCKED_STOCK, 0)"
                    "BLOCK_FG" -> "NVL(T.BLOCKED_STOCK, 0)"
                    "TOP20_ENTITY" -> "$sohQty + $gitQty"
                    "TOP20_PLANNER" -> "$sohQty + $gitQty"
                    "GIT" -> gitQty
                    "WIP" -> wipQty
                    "UD_GT_30_CD" -> "NVL(T.UD_GT_30_CD, 0)"
                    "UD_LT_7_CD" -> "NVL(T.UD_LT_7_CD, 0)"
                    "UD_LT_30_CD" -> "NVL(T.UD_LT_30_CD, 0)"
                    "VENDOR_CONSIGN" -> sohQty
                    else -> sohQty
                }

                else -> "0"
            }

            val filter = when (report3Category) {
                "FG" -> "T.MATERIAL_CATEGORY IN ('Finish Goods')"
                "SEMI_FG" -> "T.MATERIAL_CATEGORY IN ('Semi-FG')"
                "RM" -> "T.MATERIAL_CATEGORY IN ('Raw Material', 'Plant Trading')"
                "BLOCK_RM" -> "T.MATERIAL_CATEGORY IN ('Raw Material', 'Plant Trading')"
                "BLOCK_FG" -> "T.MATERIAL_CATEGORY IN ('Finish Goods')"
                "TOP20_ENTITY" -> "T.INV_TOP20_ENTITY_LABEL IS NOT NULL"
                "TOP20_PLANNER" -> "T.INV_TOP20_PLANNER_LABEL IS NOT NULL"
                "VENDOR_CONSIGN" -> "T.SPECIAL_ST = 'K'"
                else -> ""
            }

            parameterMap["valueFilter"] = filter
            parameterMap["valueColumn"] = valueColumn

            dataList = myStoryInventoryDao.queryReport3Inventory(parameterMap)
        }
        resultMap["xAxis"] = dataList.map { e -> e["XAXIS"] }
        resultMap["yAxis"] = dataList.map { e -> e["YAXIS"] }
        return response.setBody(resultMap)
    }

    private fun generateFilter(parameterMap: MutableMap<String, Any>) {
        if ("Value" == parameterMap["report1ValueType"]) {
            parameterMap["suffix"] = "_VALUE";
        } else {
            parameterMap["suffix"] = "_QTY";
        }

        val availableColumns = ArrayList<String>()
        availableColumns.add("GROSS_INV")
        availableColumns.add("FG")
        availableColumns.add("SEMI_FG")
        availableColumns.add("RM")
        availableColumns.add("GIT")
        availableColumns.add("WIP")
        availableColumns.add("GROSS_UHS")
        availableColumns.add("THEO_PROVISION")
        availableColumns.add("EXCESS")
        availableColumns.add("MISSING")
        availableColumns.add("SPECIAL")
        availableColumns.add("TOP20_ENTITY")
        availableColumns.add("TOP20_PLANNER")
        availableColumns.add("BLOCK_FG")
        availableColumns.add("BLOCK_RM")
        availableColumns.add("AGING_GT_2Y")
        availableColumns.add("AGING_LT_2Y")
        availableColumns.add("AGING_LT_1Y")
        availableColumns.add("UD_LT_7_CD")
        availableColumns.add("UD_LT_30_CD")
        availableColumns.add("UD_GT_30_CD")
        availableColumns.add("UNRESTRICT_M0_SO")
        availableColumns.add("UNRESTRICT_M1_SO")
        availableColumns.add("UNRESTRICT_GT_M1_SO")
        availableColumns.add("NONBLOCK_M0_SO")
        availableColumns.add("NONBLOCK_M1_SO")
        availableColumns.add("NONBLOCK_GT_M1_SO")
        availableColumns.add("VENDOR_CONSIGN")

        parameterMap["availableColumns"] = availableColumns
        // 生成筛选条件
        this.generateCascaderFilterSQL(parameterMap, null, "MY_STORY_INVENTORY_V", "T", "_filters")
        // 生成INV专有的条件
        this.generateCascaderFilterSQL("filterList", parameterMap, null, "MY_STORY_INVENTORY_V", "T", "_filters2")
        // 生成INV HIST专有的条件
        this.generateCascaderFilterSQL(parameterMap, null, "INVENTORY_STRUCTURE_HIST", "T", "_filters3")
        this.generateCascaderFilterSQL("filterList", parameterMap, null, "INVENTORY_STRUCTURE_HIST", "T", "_filters4")
        // 生成INV HIST专有的条件
        this.generateCascaderFilterSQL(parameterMap, null, "UHS_RCA_HIST", "T", "_filters5")
        this.generateCascaderFilterSQL("filterList", parameterMap, null, "UHS_RCA_HIST", "T", "_filters6")
    }
}
