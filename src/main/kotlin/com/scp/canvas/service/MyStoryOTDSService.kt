package com.scp.canvas.service

import com.alibaba.fastjson.JSONArray
import com.scp.canvas.dao.IMyStoryOTDSDao
import com.starter.context.bean.Configuration
import com.starter.context.bean.Response
import com.starter.context.servlet.ServiceHelper
import com.starter.utils.Utils
import jakarta.annotation.Resource
import org.codehaus.plexus.util.StringUtils
import org.springframework.cache.annotation.Cacheable
import org.springframework.context.annotation.Scope
import org.springframework.stereotype.Service

@Service
@Scope("prototype")
class MyStoryOTDSService : ServiceHelper() {

    @Resource
    lateinit var myStoryOTDSDao: IMyStoryOTDSDao

    @Resource
    lateinit var response: Response

    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    fun initPage(): Response {
        val resultMap = HashMap<String, Any>()
        resultMap["filterOpts"] = this.parseCascader(myStoryOTDSDao.queryFilterOpts())
        resultMap["pivotOpts"] = myStoryOTDSDao.queryPivotOpts()
        resultMap["stackOpts"] = myStoryOTDSDao.queryStackOpts()
        resultMap["weekOpts"] = myStoryOTDSDao.queryWeekOpts()
        resultMap["currentWeek"] = myStoryOTDSDao.queryCurrentWeek()
        return response.setBody(resultMap)
    }

    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    fun queryReport1(parameterMap: MutableMap<String, Any>): Response {
        this.generateFilter(parameterMap)
        val resultMap = HashMap<String, Any>()

        val report1Categories = parameterMap["report1Categories"] as JSONArray
        if (report1Categories.isEmpty()) {
            report1Categories.add("ENTITY")
        }
        parameterMap["category"] = report1Categories[0]
        val resultList = myStoryOTDSDao.queryReport1(parameterMap)
        for (map in resultList) {
            map["parent"] = ArrayList<String>()
            map["id"] = Utils.randomStr(12)
            map["hasChildren"] = report1Categories.size > 1 && "Total" != map["category"]
        }
        resultMap["data"] = resultList

        val headerMap = myStoryOTDSDao.queryReport1Header(parameterMap)
        resultMap["ytdTitle"] = headerMap["YTD_HEADER"] as Any
        resultMap["mtdTitle"] = headerMap["MTD_HEADER"] as Any
        resultMap["wkTitle"] = headerMap["WK_HEADER"] as Any
        resultMap["wk1Title"] = headerMap["WK1_HEADER"] as Any
        return response.setBody(resultMap)
    }

    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    fun queryReport1Sub(parameterMap: MutableMap<String, Any>): Response {
        this.generateFilter(parameterMap)

        val report1Categories = parameterMap["report1Categories"] as JSONArray
        val parent = parameterMap["parent"] as JSONArray
        val expandValue = parameterMap["expandValue"] as Any
        val expandColumn = report1Categories[parent.size] // 展开的Column
        parameterMap["expandValue"] = expandValue
        parameterMap["expandColumn"] = expandColumn
        parameterMap["category"] = report1Categories[parent.size + 1] // 展开的Column的下一级
        val resultList = myStoryOTDSDao.queryReport1Sub(parameterMap)
        for (map in resultList) {
            map["id"] = Utils.randomStr(12)
            map["hasChildren"] = report1Categories.size > parent.size + 2
            map["parent"] = parent.plus(expandValue)
        }
        return response.setBody(resultList)
    }

    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    fun queryReport2(parameterMap: MutableMap<String, Any>): Response {
        this.generateFilter(parameterMap)
        val report2StackBy = parameterMap["report2StackBy"] as String
        if (StringUtils.isBlank(report2StackBy)) {
            parameterMap["report2StackBy"] = "'DELAY LINES'"
        }

        val dataList = myStoryOTDSDao.queryReport2(parameterMap)
        val sumMap = LinkedHashMap<String, Int>()
        val plantList = ArrayList<String>()
        val yAxis = ArrayList<String>()
        val total = ArrayList<Int>()

        // 先排序, 找出来料号顺序, 放入yAxis
        for (data in dataList) {
            val category = data["CATEGORY"] as String
            var sum = sumMap.computeIfAbsent(category) { k: String -> 0 }
            sum += Utils.parseInt(data["CNT"])
            sumMap[category] = sum
        }
        var list: List<Map.Entry<String, Int>> = ArrayList<Map.Entry<String, Int>>(sumMap.entries)
        // 根据值对ArrayList进行排序
        list = list.sortedBy {
            it.value
        }
        for ((key, value) in list) {
            yAxis.add(key)
            total.add(value)
        }

        // 再找可用的plantCode做堆叠准备
        for (data in dataList) {
            val stackBy = data["STACK_BY"] as String
            // save legend
            if (!plantList.contains(stackBy)) {
                plantList.add(stackBy)
            }
        }
        val sortedPlantList = plantList.sortedBy {
            it.toString()
        }

        // 最后找每个material在不同plant下的数量
        val series = LinkedHashMap<String, ArrayList<Int>>()
        val dataMap = HashMap<String, Int>()
        for (data in dataList) {
            dataMap[data["CATEGORY"].toString() + "@" + data["STACK_BY"].toString()] = Utils.parseInt(data["CNT"])
        }
        for (plant in sortedPlantList) {
            val temp = series.computeIfAbsent(plant) { key -> ArrayList() }
            for (material in yAxis) {
                temp.add(dataMap.getOrDefault("$material@$plant", 0))
            }
        }

        // sort legend
        val resultMap = HashMap<String, Any>()
        resultMap["yAxis"] = yAxis
        resultMap["total"] = total
        resultMap["series"] = series
        return response.setBody(resultMap)
    }

    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    fun queryReport3(parameterMap: MutableMap<String, Any>): Response {
        this.generateFilter(parameterMap)
        if ("[TOTAL]" == parameterMap["report3Category"]) {
            parameterMap["report3Category"] = "'[TOTAL]'"
        }

        val resultMap = HashMap<String, Any>()

        val dataList = myStoryOTDSDao.queryReport3(parameterMap)
        val xAxis = dataList.map { e -> e["WEEK"] }.distinct()
        val legend = dataList.map { e -> e["CATEGORY"] }.distinct()
        val yAxis = LinkedHashMap<String, ArrayList<Any?>>()
        val tempMap = LinkedHashMap<String, Any>()
        for (data in dataList) {
            val key = "" + data["CATEGORY"] + "#" + data["WEEK"]
            tempMap[key] = data["ONTIME_RATE"] as Any
        }

        for (l in legend) {
            for (x in xAxis) {
                val key = "$l#$x"
                val list = yAxis.computeIfAbsent(l.toString()) { key -> ArrayList() }
                list.add(tempMap[key])
            }
        }

        resultMap["legend"] = dataList.map { e -> e["CATEGORY"] }.distinct()
        resultMap["xAxis"] = xAxis
        resultMap["yAxis"] = yAxis
        return response.setBody(resultMap)
    }

    private fun generateFilter(parameterMap: MutableMap<String, Any>) {
        // 生成筛选条件
        this.generateCascaderFilterSQL(parameterMap, null, "OTDS_SOURCE_WEEKLY_V", "T", "_filters")
        // 生成OTDS专有的条件
        this.generateCascaderFilterSQL("filterList", parameterMap, null, "OTDS_SOURCE_WEEKLY_V", "T", "_filters2")
    }
}
