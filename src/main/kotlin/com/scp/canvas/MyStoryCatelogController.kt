package com.scp.canvas

import com.scp.canvas.service.MyStoryService
import com.scp.canvas.service.MyStoryCatalogService
import com.starter.context.bean.Response
import com.starter.context.bean.SchneiderRequestMapping
import com.starter.context.servlet.ControllerHelper
import jakarta.annotation.Resource
import jakarta.servlet.http.HttpServletRequest
import org.springframework.context.annotation.Scope
import org.springframework.web.bind.annotation.CrossOrigin
import org.springframework.web.bind.annotation.RestController

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = ["/canvas/my_story_catalog"], parent = MyStoryService.PARENT_CODE)
class MyStoryCatelogController : ControllerHelper() {

    @Resource
    lateinit var myStoryCatalogService: MyStoryCatalogService

    @SchneiderRequestMapping("/init_catalog_page")
    fun initCatalogPage(request: HttpServletRequest): Response {
        super.pageLoad(request)
        super.setGlobalCache(true)
        return myStoryCatalogService.initCatalogPage(parameterMap)
    }

    @SchneiderRequestMapping("/save_page_order")
    fun savePageOrder(request: HttpServletRequest): Response {
        super.pageLoad(request)
        return myStoryCatalogService.savePageOrder(parameterMap)
    }

    @SchneiderRequestMapping("/delete_page")
    fun deletePage(request: HttpServletRequest): Response {
        super.pageLoad(request)
        return myStoryCatalogService.deletePage(parameterMap)
    }

    @SchneiderRequestMapping("/save_page")
    fun savePage(request: HttpServletRequest): Response {
        super.pageLoad(request)
        return myStoryCatalogService.savePage(parameterMap)
    }

    @SchneiderRequestMapping("/modify_page")
    fun modifyPage(request: HttpServletRequest): Response {
        super.pageLoad(request)
        return myStoryCatalogService.modifyPage(parameterMap)
    }

    @SchneiderRequestMapping("/query_page_by_id")
    fun queryPageById(request: HttpServletRequest): Response {
        super.pageLoad(request)
        return myStoryCatalogService.queryPageById(parameterMap)
    }
}

