package com.scp.canvas.dao

import org.apache.ibatis.annotations.Mapper

@Mapper
interface IMyStoryNORDao {

    fun queryFilterOpts(): List<Map<String, String>>

    fun queryFilterPoOpts(): List<Map<String, String>>

    fun queryPivotOpts(): List<String>

    fun queryPoOpts(): List<String>

    fun queryWeekOpts(): List<String>

    fun queryCurrentWeek(): String

    fun queryReport1(parameterMap: MutableMap<String, Any>): List<MutableMap<String, Any>>

    fun queryReport1Header(parameterMap: MutableMap<String, Any>): MutableMap<String, Any>

    fun queryReport1Sub(parameterMap: MutableMap<String, Any>): List<MutableMap<String, Any>>

    fun queryReport2(parameterMap: MutableMap<String, Any>): List<MutableMap<String, Any>>

    fun queryReport2Header(parameterMap: MutableMap<String, Any>): MutableMap<String, Any>

    fun queryReport2Sub(parameterMap: MutableMap<String, Any>): List<MutableMap<String, Any>>


    fun queryReport3(parameterMap: MutableMap<String, Any>): List<MutableMap<String, Any>>

    fun queryReport4(parameterMap: MutableMap<String, Any>): List<MutableMap<String, Any>>

    fun queryReport3StackOpts(): List<String>
}
