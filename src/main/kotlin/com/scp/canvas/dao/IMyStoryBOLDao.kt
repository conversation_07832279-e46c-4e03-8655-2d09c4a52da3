package com.scp.canvas.dao

import org.apache.ibatis.annotations.Mapper

@Mapper
interface IMyStoryBOLDao {

    fun queryPivotOpts(): List<String>

    fun queryStackOpts(): List<String>

    fun queryFilterOpts(): List<MutableMap<String, String>>

    fun queryReport1(parameterMap: MutableMap<String, Any>): List<MutableMap<String, Any>>

    fun queryReport1Sub(parameterMap: MutableMap<String, Any>): List<MutableMap<String, Any>>

    fun queryReport2(parameterMap: MutableMap<String, Any>): List<MutableMap<String, Any>>

    fun queryReport3(parameterMap: MutableMap<String, Any>): List<MutableMap<String, Any>>
}
