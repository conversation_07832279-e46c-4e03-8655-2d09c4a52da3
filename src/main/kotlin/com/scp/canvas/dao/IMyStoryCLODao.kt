package com.scp.canvas.dao

import org.apache.ibatis.annotations.Mapper

@Mapper
interface IMyStoryCLODao {

    fun queryFilterOpts(): List<Map<String, String>>

    fun queryPivotOpts(): List<String>

    fun queryReport1(parameterMap: MutableMap<String, Any>): List<MutableMap<String, Any>>

    fun queryReport1Sub(parameterMap: MutableMap<String, Any>): List<MutableMap<String, Any>>

    fun queryReport2(parameterMap: MutableMap<String, Any>): List<MutableMap<String, Any>>

    fun queryReport3(parameterMap: MutableMap<String, Any>): List<MutableMap<String, Any>>
}
