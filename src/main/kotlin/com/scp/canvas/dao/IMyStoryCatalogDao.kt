package com.scp.canvas.dao

import org.apache.ibatis.annotations.Mapper

@Mapper
interface IMyStoryCatalogDao {

    fun queryPagesById(parameterMap: MutableMap<String, Any>): List<MutableMap<String, Any>>

    fun savePageOrder(parameterMap: MutableMap<String, Any>)

    fun deletePage(parameterMap: MutableMap<String, Any>)

    fun savePage(parameterMap: MutableMap<String, Any>)

    fun modifyPage(parameterMap: MutableMap<String, Any>)

    fun queryPageById(parameterMap: MutableMap<String, Any>): MutableMap<String, Any>
}
