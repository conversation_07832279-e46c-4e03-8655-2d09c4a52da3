<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.canvas.dao.IMyStoryOTDSDao">
    <sql id="otds_filter">
        <if test="_filters != null and _filters != ''.toString()">
            and ${_filters}
        </if>
        <if test="_filters2 != null and _filters2 != ''.toString()">
            and ${_filters2}
        </if>
    </sql>

    <select id="queryFilterOpts" resultType="java.util.Map">
        select * from SCPA.OTDS_FILTER_V t
        WHERE T.CATEGORY NOT IN (
                'SHIP_TO_SHORT_NAME',
                'SHIP_TO_FULL_NAME',
                'SOLD_TO_FULL_NAME',
                'SOLD_TO_SHORT_NAME',
                'SOLD_TO_PARENT_CODE',
                'SOLD_TO_PARENT_NAME',
                'SHIP_TO_PARENT_CODE',
                'SHIP_TO_PARENT_NAME',
                'VIP_PROJECT_NAME',
                'VIP_EU')
        order by t.category,decode(t.name,'Others','zzz',t.name)
    </select>

    <select id="queryStackOpts" resultType="java.lang.String">
        SELECT T.CATEGORY
        FROM SCPA.OTDS_FILTER_V T
        GROUP BY T.CATEGORY
        HAVING COUNT(1) &lt; 100
        order by t.category
    </select>

    <select id="queryPivotOpts" resultType="java.lang.String">
        SELECT T.COLUMN_NAME
        FROM USER_TAB_COLS T
        WHERE T.TABLE_NAME = 'OTDS_SOURCE_WEEKLY_V'
          AND T.DATA_TYPE = 'VARCHAR2'
          AND T.COLUMN_NAME NOT LIKE '%$%'
          AND T.COLUMN_NAME NOT IN('SALES_ORDER_NUMBER', 'SALES_ORDER_ITEM')
          AND T.COLUMN_NAME NOT LIKE '%BUSINESS_UNIT%'
        ORDER BY T.COLUMN_NAME
    </select>

    <select id="queryWeekOpts" resultType="java.lang.String">
        SELECT DISTINCT T.YEAR || T.WEEK_NO
        FROM SY_CALENDAR T
        WHERE T.NAME = 'National Holidays'
        AND T.DATE$ BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'YYYY'), -24) AND TRUNC(SYSDATE + 7, 'DD')
        ORDER BY T.YEAR || T.WEEK_NO
    </select>

    <select id="queryCurrentWeek" resultType="java.lang.String">
        SELECT T.WEEK FROM SCPA.OTDS_SOURCE_WEEKLY_V T
        GROUP BY T.WEEK
        HAVING COUNT(1) > 10000
        ORDER BY T.WEEK DESC
        FETCH NEXT 1 ROWS ONLY
    </select>

    <select id="queryReport1Header" resultType="java.util.Map">
        SELECT TO_CHAR(T.DATE$, 'YYYY') || 'YTD' AS YTD_HEADER,
               TO_CHAR(T.DATE$, 'Mon-YY', 'NLS_DATE_LANGUAGE=AMERICAN') AS MTD_HEADER,
               SUBSTR(T.YEAR, 3) || 'W' || T.WEEK_NO AS WK_HEADER,
               (SELECT SUBSTR(T.YEAR, 3) || 'W' || T.WEEK_NO
                 FROM SY_CALENDAR T
                WHERE T.YEAR || T.WEEK_NO &lt; #{report1Week,jdbcType=VARCHAR}
                  AND T.NAME = 'National Holidays'
                ORDER BY T.YEAR || T.WEEK_NO DESC
                    FETCH NEXT 1 ROWS ONLY)                             AS WK1_HEADER
        FROM SY_CALENDAR T
        WHERE T.YEAR || T.WEEK_NO = #{report1Week,jdbcType=VARCHAR}
          AND T.NAME = 'National Holidays'
        ORDER BY DATE$
            FETCH NEXT 1 ROWS ONLY
    </select>

    <select id="queryReport1" resultType="java.util.Map">
        WITH OTDS_SOURCE AS (
            SELECT /*+ materialize */ T.${category}, SO04_CRD, ONTIME, WEEK, SALES_ORDER_NUMBER, SALES_ORDER_ITEM, VIP_SO_INDICATOR
            FROM ${SCPA.OTDS_SOURCE_WEEKLY_V} T
            WHERE T.SO04_CRD > TO_DATE(SUBSTR(#{report1Week,jdbcType=VARCHAR}, 0, 4) || '/01/01', 'YYYY/MM/DD') - 30
            <include refid="otds_filter"/>
        ),
        CURRENT_WEEK_SOURCE AS (
            SELECT *
            FROM OTDS_SOURCE T
            WHERE T.WEEK = #{report1Week,jdbcType=VARCHAR}
        ),
        LAST_WEEK_SOURCE AS (
            SELECT *
            FROM OTDS_SOURCE T
            WHERE T.WEEK IN (
                SELECT TT.WEEK FROM SCPA.OTDS_SOURCE_WEEKLY_V TT
                WHERE TT.WEEK &lt; #{report1Week,jdbcType=VARCHAR}
                GROUP BY TT.WEEK
                HAVING COUNT(1) > 10000
                ORDER BY TT.WEEK DESC
                FETCH NEXT 1 ROWS ONLY
            )
        ),
        MTD_SOURCE AS (
            SELECT *
            FROM OTDS_SOURCE T
            WHERE TRUNC(T.SO04_CRD, 'MM') = (
                SELECT TRUNC(T.DATE$, 'MM')
                FROM SY_CALENDAR T
                WHERE T.YEAR || T.WEEK_NO = #{report1Week,jdbcType=VARCHAR}
                  AND T.NAME = 'National Holidays'
                ORDER BY TRUNC(T.DATE$, 'MM')
                    FETCH NEXT 1 ROWS ONLY
            )
        ),
        YTD_SOURCE AS (
            SELECT *
            FROM OTDS_SOURCE T
            WHERE TRUNC(T.SO04_CRD, 'YYYY') = (
                SELECT TRUNC(T.DATE$, 'YYYY')
                FROM SY_CALENDAR T
                WHERE T.YEAR || T.WEEK_NO = #{report1Week,jdbcType=VARCHAR}
                  AND T.NAME = 'National Holidays'
                ORDER BY DATE$
                    FETCH NEXT 1 ROWS ONLY
            )
        ),
        LAST_WEEK_OTDS AS (
            SELECT T.${category},
                   SUM(CASE WHEN T.ONTIME = '+' THEN 1 ELSE 0 END) ONTIME_LINE,
                   SUM(CASE WHEN T.ONTIME = '#' THEN 1 ELSE 0 END) DELAY_LINE
              FROM LAST_WEEK_SOURCE T
             GROUP BY T.${category}
        ),
        MTD_OTDS AS (
            SELECT T.${category},
                   SUM(CASE WHEN T.ONTIME = '+' THEN 1 ELSE 0 END) ONTIME_LINE,
                   SUM(CASE WHEN T.ONTIME = '#' THEN 1 ELSE 0 END) DELAY_LINE
              FROM MTD_SOURCE T
             GROUP BY T.${category}
        ),
        YTD_OTDS AS (
            SELECT T.${category},
                   SUM(CASE WHEN T.ONTIME = '+' THEN 1 ELSE 0 END) ONTIME_LINE,
                   SUM(CASE WHEN T.ONTIME = '#' THEN 1 ELSE 0 END) DELAY_LINE
              FROM YTD_SOURCE T
             GROUP BY T.${category}
        ),
        CURRENT_WEEK_OTDS AS (
            SELECT T.${category},
                   SUM(CASE WHEN T.ONTIME = '+' AND T.VIP_SO_INDICATOR = 'Y' THEN 1 ELSE 0 END)  AS VIP_ONTIME_LINE,
                   SUM(CASE WHEN T.ONTIME = '#' AND T.VIP_SO_INDICATOR = 'Y' THEN 1 ELSE 0 END)  AS VIP_DELAY_LINE,
                   SUM(CASE WHEN T.ONTIME = '+' THEN 1 ELSE 0 END)                               AS ONTIME_LINE,
                   SUM(CASE WHEN T.ONTIME = '#' THEN 1 ELSE 0 END)                               AS DELAY_LINE,
                   SUM(CASE WHEN T.ONTIME = '#' AND T2.RCA_RESULT IS NOT NULL THEN 1 ELSE 0 END) AS RCA_LINE
            FROM CURRENT_WEEK_SOURCE T
                   LEFT JOIN SCPA.OTDS_RCA_FEEDBACK T2 ON T.SALES_ORDER_NUMBER = T2.SALES_ORDER_NUMBER AND T.SALES_ORDER_ITEM = T2.SALES_ORDER_ITEM AND T2.RCA_TYPE = 'W'
           GROUP BY T.${category}
        ),
        BASE AS (
            SELECT T.${category},
                   T.VIP_ONTIME_LINE,
                   T.VIP_DELAY_LINE,
                   T.ONTIME_LINE,
                   T.DELAY_LINE,
                   T.RCA_LINE,
                   T2.ONTIME_LINE AS LW_ONTIME_LINE,
                   T2.DELAY_LINE  AS LW_DELAY_LINE,
                   T3.ONTIME_LINE AS MTD_ONTIME_LINE,
                   T3.DELAY_LINE  AS MTD_DELAY_LINE,
                   T4.ONTIME_LINE AS YTD_ONTIME_LINE,
                   T4.DELAY_LINE  AS YTD_DELAY_LINE
             FROM CURRENT_WEEK_OTDS T
             LEFT JOIN LAST_WEEK_OTDS T2 ON T.${category} = T2.${category}
             LEFT JOIN MTD_OTDS T3 ON T.${category} = T3.${category}
             LEFT JOIN YTD_OTDS T4 ON T.${category} = T4.${category}
        ),
        RESULT AS (
            SELECT T.${category} AS "category",
                 CASE WHEN T.MTD_ONTIME_LINE + T.MTD_DELAY_LINE = 0 THEN 0 ELSE ROUND(T.MTD_ONTIME_LINE / (T.MTD_ONTIME_LINE + T.MTD_DELAY_LINE) * 100, 1) END AS "mtdOtds",
                 CASE WHEN T.YTD_ONTIME_LINE + T.YTD_DELAY_LINE = 0 THEN 0 ELSE ROUND(T.YTD_ONTIME_LINE / (T.YTD_ONTIME_LINE + T.YTD_DELAY_LINE) * 100, 1) END AS "ytdOtds",
                 CASE WHEN T.LW_ONTIME_LINE + T.LW_DELAY_LINE = 0 THEN 0 ELSE ROUND(T.LW_ONTIME_LINE / (T.LW_ONTIME_LINE + T.LW_DELAY_LINE) * 100, 1) END      AS "lwOtds",
                 CASE WHEN T.ONTIME_LINE + T.DELAY_LINE = 0 THEN 0 ELSE ROUND(T.ONTIME_LINE / (T.ONTIME_LINE + T.DELAY_LINE) * 100, 1) END                     AS "otds",
                 CASE WHEN T.VIP_ONTIME_LINE + T.VIP_DELAY_LINE = 0 THEN 0 ELSE ROUND(T.VIP_ONTIME_LINE / (T.VIP_ONTIME_LINE + T.VIP_DELAY_LINE) * 100, 1) END AS "vip",
                 T.ONTIME_LINE                                                                                                                                 AS "ontimeLine",
                 T.DELAY_LINE                                                                                                                                  AS "delayLine",
                 CASE WHEN T.DELAY_LINE = 0 THEN 0 ELSE ROUND(T.RCA_LINE / T.DELAY_LINE * 100, 1) END                                                          AS "rca"
            FROM BASE T
            UNION ALL
            SELECT 'Total',
                 CASE WHEN SUM(T.MTD_ONTIME_LINE + T.MTD_DELAY_LINE) = 0 THEN 0 ELSE ROUND(SUM(T.MTD_ONTIME_LINE) / SUM(T.MTD_ONTIME_LINE + T.MTD_DELAY_LINE) * 100, 1) END,
                 CASE WHEN SUM(T.YTD_ONTIME_LINE + T.YTD_DELAY_LINE) = 0 THEN 0 ELSE ROUND(SUM(T.YTD_ONTIME_LINE) / SUM(T.YTD_ONTIME_LINE + T.YTD_DELAY_LINE) * 100, 1) END,
                 CASE WHEN SUM(T.LW_ONTIME_LINE + T.LW_DELAY_LINE) = 0 THEN 0 ELSE ROUND(SUM(T.LW_ONTIME_LINE) / SUM(T.LW_ONTIME_LINE + T.LW_DELAY_LINE) * 100, 1) END,
                 CASE WHEN SUM(T.ONTIME_LINE + T.DELAY_LINE) = 0 THEN 0 ELSE ROUND(SUM(T.ONTIME_LINE) / SUM(T.ONTIME_LINE + T.DELAY_LINE) * 100, 1) END,
                 CASE WHEN SUM(T.VIP_ONTIME_LINE + T.VIP_DELAY_LINE) = 0 THEN 0 ELSE ROUND(SUM(T.VIP_ONTIME_LINE) / SUM(T.VIP_ONTIME_LINE + T.VIP_DELAY_LINE) * 100, 1) END,
                 SUM(T.ONTIME_LINE),
                 SUM(T.DELAY_LINE),
                 CASE WHEN SUM(T.DELAY_LINE) = 0 THEN 0 ELSE ROUND(SUM(T.RCA_LINE) / SUM(T.DELAY_LINE) * 100, 1) END
            FROM BASE T
        )
        SELECT T."category",
               NVL(T."mtdOtds", 0)      "mtdOtds",
               NVL(T."ytdOtds", 0)      "ytdOtds",
               NVL(T."lwOtds", 0)       "lwOtds",
               NVL(T."otds", 0)         "otds",
               NVL(T."vip", 0)          "vip",
               NVL(T."ontimeLine", 0)   "ontimeLine",
               NVL(T."delayLine", 0)    "delayLine",
               NVL(T."rca", 0)          "rca"
          FROM RESULT T
        ORDER BY DECODE(T."category", 'Others', -1, 'Total', -2, NVL(T."delayLine", 0)) DESC
        OFFSET 0 ROWS FETCH NEXT 128 ROWS ONLY
    </select>

    <select id="queryReport1Sub" resultType="java.util.Map">
        WITH OTDS_SOURCE AS (
            SELECT /*+ materialize */ T.${category}, SO04_CRD, ONTIME, WEEK, SALES_ORDER_NUMBER, SALES_ORDER_ITEM, VIP_SO_INDICATOR
            FROM ${SCPA.OTDS_SOURCE_WEEKLY_V} T
            WHERE T.SO04_CRD > TO_DATE(SUBSTR(#{report1Week,jdbcType=VARCHAR}, 0, 4) || '/01/01', 'YYYY/MM/DD') - 30
            AND T."${expandColumn}" = #{expandValue, jdbcType=VARCHAR}
            <foreach collection="parent" item="item" index="index">
                AND T."${report1Categories[index]}" = #{item, jdbcType=VARCHAR}
            </foreach>
            <include refid="otds_filter"/>
        ),
        CURRENT_WEEK_SOURCE AS (
            SELECT *
            FROM OTDS_SOURCE T
            WHERE T.WEEK = #{report1Week,jdbcType=VARCHAR}
        ),
        LAST_WEEK_SOURCE AS (
            SELECT *
            FROM OTDS_SOURCE T
            WHERE T.WEEK IN (
                SELECT TT.WEEK FROM SCPA.OTDS_SOURCE_WEEKLY_V TT
                WHERE TT.WEEK &lt; #{report1Week,jdbcType=VARCHAR}
                GROUP BY TT.WEEK
                HAVING COUNT(1) > 10000
                ORDER BY TT.WEEK DESC
                FETCH NEXT 1 ROWS ONLY
            )
        ),
        MTD_SOURCE AS (
            SELECT *
            FROM OTDS_SOURCE T
            WHERE TRUNC(T.SO04_CRD, 'MM') = (
                SELECT TRUNC(T.DATE$, 'MM')
                FROM SY_CALENDAR T
                WHERE T.YEAR || T.WEEK_NO = #{report1Week,jdbcType=VARCHAR}
                  AND T.NAME = 'National Holidays'
                ORDER BY TRUNC(T.DATE$, 'MM')
                    FETCH NEXT 1 ROWS ONLY
            )
        ),
        YTD_SOURCE AS (
            SELECT *
            FROM OTDS_SOURCE T
            WHERE TRUNC(T.SO04_CRD, 'YYYY') = (
                SELECT TRUNC(T.DATE$, 'YYYY')
                FROM SY_CALENDAR T
                WHERE T.YEAR || T.WEEK_NO = #{report1Week,jdbcType=VARCHAR}
                  AND T.NAME = 'National Holidays'
                ORDER BY DATE$
                    FETCH NEXT 1 ROWS ONLY
            )
        ),
        LAST_WEEK_OTDS AS (
            SELECT T.${category},
                   SUM(CASE WHEN T.ONTIME = '+' THEN 1 ELSE 0 END) ONTIME_LINE,
                   SUM(CASE WHEN T.ONTIME = '#' THEN 1 ELSE 0 END) DELAY_LINE
              FROM LAST_WEEK_SOURCE T
             GROUP BY T.${category}
        ),
        MTD_OTDS AS (
            SELECT T.${category},
                   SUM(CASE WHEN T.ONTIME = '+' THEN 1 ELSE 0 END) ONTIME_LINE,
                   SUM(CASE WHEN T.ONTIME = '#' THEN 1 ELSE 0 END) DELAY_LINE
              FROM MTD_SOURCE T
             GROUP BY T.${category}
        ),
        YTD_OTDS AS (
            SELECT T.${category},
                   SUM(CASE WHEN T.ONTIME = '+' THEN 1 ELSE 0 END) ONTIME_LINE,
                   SUM(CASE WHEN T.ONTIME = '#' THEN 1 ELSE 0 END) DELAY_LINE
              FROM YTD_SOURCE T
             GROUP BY T.${category}
        ),
        CURRENT_WEEK_OTDS AS (
            SELECT T.${category},
                   SUM(CASE WHEN T.ONTIME = '+' AND T.VIP_SO_INDICATOR = 'Y' THEN 1 ELSE 0 END)  AS VIP_ONTIME_LINE,
                   SUM(CASE WHEN T.ONTIME = '#' AND T.VIP_SO_INDICATOR = 'Y' THEN 1 ELSE 0 END)  AS VIP_DELAY_LINE,
                   SUM(CASE WHEN T.ONTIME = '+' THEN 1 ELSE 0 END)                               AS ONTIME_LINE,
                   SUM(CASE WHEN T.ONTIME = '#' THEN 1 ELSE 0 END)                               AS DELAY_LINE,
                   SUM(CASE WHEN T.ONTIME = '#' AND T2.RCA_RESULT IS NOT NULL THEN 1 ELSE 0 END) AS RCA_LINE
            FROM CURRENT_WEEK_SOURCE T
                   LEFT JOIN SCPA.OTDS_RCA_FEEDBACK T2 ON T.SALES_ORDER_NUMBER = T2.SALES_ORDER_NUMBER AND T.SALES_ORDER_ITEM = T2.SALES_ORDER_ITEM AND T2.RCA_TYPE = 'W'
           GROUP BY T.${category}
        ),
        BASE AS (
            SELECT T.${category},
                   T.VIP_ONTIME_LINE,
                   T.VIP_DELAY_LINE,
                   T.ONTIME_LINE,
                   T.DELAY_LINE,
                   T.RCA_LINE,
                   T2.ONTIME_LINE AS LW_ONTIME_LINE,
                   T2.DELAY_LINE  AS LW_DELAY_LINE,
                   T3.ONTIME_LINE AS MTD_ONTIME_LINE,
                   T3.DELAY_LINE  AS MTD_DELAY_LINE,
                   T4.ONTIME_LINE AS YTD_ONTIME_LINE,
                   T4.DELAY_LINE  AS YTD_DELAY_LINE
             FROM CURRENT_WEEK_OTDS T
             LEFT JOIN LAST_WEEK_OTDS T2 ON T.${category} = T2.${category}
             LEFT JOIN MTD_OTDS T3 ON T.${category} = T3.${category}
             LEFT JOIN YTD_OTDS T4 ON T.${category} = T4.${category}
        ),
        RESULT AS (
            SELECT T.${category} AS "category",
                 CASE WHEN T.MTD_ONTIME_LINE + T.MTD_DELAY_LINE = 0 THEN 0 ELSE ROUND(T.MTD_ONTIME_LINE / (T.MTD_ONTIME_LINE + T.MTD_DELAY_LINE) * 100, 1) END AS "mtdOtds",
                 CASE WHEN T.YTD_ONTIME_LINE + T.YTD_DELAY_LINE = 0 THEN 0 ELSE ROUND(T.YTD_ONTIME_LINE / (T.YTD_ONTIME_LINE + T.YTD_DELAY_LINE) * 100, 1) END AS "ytdOtds",
                 CASE WHEN T.LW_ONTIME_LINE + T.LW_DELAY_LINE = 0 THEN 0 ELSE ROUND(T.LW_ONTIME_LINE / (T.LW_ONTIME_LINE + T.LW_DELAY_LINE) * 100, 1) END      AS "lwOtds",
                 CASE WHEN T.ONTIME_LINE + T.DELAY_LINE = 0 THEN 0 ELSE ROUND(T.ONTIME_LINE / (T.ONTIME_LINE + T.DELAY_LINE) * 100, 1) END                     AS "otds",
                 CASE WHEN T.VIP_ONTIME_LINE + T.VIP_DELAY_LINE = 0 THEN 0 ELSE ROUND(T.VIP_ONTIME_LINE / (T.VIP_ONTIME_LINE + T.VIP_DELAY_LINE) * 100, 1) END AS "vip",
                 T.ONTIME_LINE                                                                                                                                 AS "ontimeLine",
                 T.DELAY_LINE                                                                                                                                  AS "delayLine",
                 CASE WHEN T.DELAY_LINE = 0 THEN 0 ELSE ROUND(T.RCA_LINE / T.DELAY_LINE * 100, 1) END                                                          AS "rca"
            FROM BASE T
        )
        SELECT T."category",
               NVL(T."mtdOtds", 0)      "mtdOtds",
               NVL(T."ytdOtds", 0)      "ytdOtds",
               NVL(T."lwOtds", 0)       "lwOtds",
               NVL(T."otds", 0)         "otds",
               NVL(T."vip", 0)          "vip",
               NVL(T."ontimeLine", 0)   "ontimeLine",
               NVL(T."delayLine", 0)    "delayLine",
               NVL(T."rca", 0)          "rca"
          FROM RESULT T
        ORDER BY DECODE(T."category", 'Others', -1, 'Total', -2, NVL(T."delayLine", 0)) DESC
        OFFSET 0 ROWS FETCH NEXT 128 ROWS ONLY
    </select>

    <select id="queryReport2" resultType="java.util.Map">
        WITH BASE AS (SELECT /*+ materialize */ T.${report2Category} AS CATEGORY, ${report2StackBy} AS STACK_BY
                      FROM ${SCPA.OTDS_SOURCE_WEEKLY_V} T
                      WHERE ONTIME = '#'
                        AND T.WEEK = #{report1Week,jdbcType=VARCHAR}
                        <foreach collection="report1SelectedValues" item="item" index="index" separator="and" open="and">
                            T."${report1Categories[index]}" = #{item, jdbcType=VARCHAR}
                        </foreach>
                        <include refid="otds_filter"/>)
        SELECT CATEGORY, STACK_BY, COUNT(1) AS CNT
          FROM BASE
         GROUP BY CATEGORY, STACK_BY
         ORDER BY COUNT(1) DESC
         OFFSET ${report2TopStart} ROWS FETCH NEXT ${report2TopEnd} - ${report2TopStart} ROWS ONLY
    </select>

    <select id="queryReport3" resultType="java.util.Map">
        WITH TEMP AS (SELECT ${report3Category} AS                           CATEGORY,
                             T.WEEK,
                             SUM(CASE WHEN T.ONTIME = '+' THEN 1 ELSE 0 END) ONTIME_LINE,
                             SUM(CASE WHEN T.ONTIME = '#' THEN 1 ELSE 0 END) DELAY_LINE
                      FROM OTDS_SOURCE_WEEKLY_V T
                      WHERE T.SO04_CRD BETWEEN TO_DATE(#{report3DateRange[0],jdbcType=VARCHAR}, 'YYYY/MM') AND TO_DATE(#{report3DateRange[1],jdbcType=VARCHAR}, 'YYYY/MM')
                      <foreach collection="report1SelectedValues" item="item" index="index" separator="and" open="and">
                        T."${report1Categories[index]}" = #{item, jdbcType=VARCHAR}
                      </foreach>
                      <include refid="otds_filter"/>
                      GROUP BY ${report3Category}, T.WEEK)
        SELECT T.CATEGORY,
               T.WEEK,
               <choose>
                   <when test="report3ValueType == 'FAILED_LINES'">
                       T.DELAY_LINE AS ONTIME_RATE
                   </when>
                   <otherwise>
                       ROUND(CASE WHEN T.DELAY_LINE + T.ONTIME_LINE = 0 THEN 0 ELSE T.ONTIME_LINE / (T.DELAY_LINE + T.ONTIME_LINE) END * 100, 1) AS ONTIME_RATE
                   </otherwise>
               </choose>
        FROM TEMP T
        ORDER BY WEEK, CATEGORY
    </select>
</mapper>
