<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.canvas.dao.IMyStoryBOLDao">
    <sql id="bol_filter">
        <if test="_filters != null and _filters != ''.toString()">
            and ${_filters}
        </if>
        <if test="_filters2 != null and _filters2 != ''.toString()">
            and ${_filters2}
        </if>
    </sql>

    <select id="queryPivotOpts" resultType="java.lang.String">
        SELECT T.COLUMN_NAME
        FROM USER_TAB_COLS T
        WHERE T.TABLE_NAME = 'DEMAND_BACK_ORDER_V'
          AND T.DATA_TYPE = 'VARCHAR2'
          AND T.COLUMN_NAME NOT LIKE '%$%'
          AND T.COLUMN_NAME NOT IN('SALES_ORDER_NUMBER', 'SALES_ORDER_ITEM')
          AND T.COLUMN_NAME IN (SELECT T0.COLUMN_NAME FROM USER_TAB_COLS T0 WHERE T0.TABLE_NAME = 'DEMAND_BACKLOG_V' AND T0.DATA_TYPE='VARCHAR2')
        ORDER BY T.COLUMN_NAME
    </select>

    <select id="queryStackOpts" resultType="java.lang.String">
        SELECT T.CATEGORY
        FROM SCPA.BOL_FILTER_V T
        GROUP BY T.CATEGORY
        HAVING COUNT(1) &lt; 100
        order by t.category
    </select>

    <select id="queryFilterOpts" resultType="java.util.Map">
        SELECT *
        FROM BOL_FILTER_V T
        WHERE T.CATEGORY IN (SELECT T0.COLUMN_NAME FROM USER_TAB_COLS T0 WHERE T0.TABLE_NAME = 'DEMAND_BACKLOG_V' AND T0.DATA_TYPE = 'VARCHAR2')
        ORDER BY T.CATEGORY, DECODE(T.NAME, 'Others', 'zzz', T.NAME)
    </select>

    <select id="queryReport1" resultType="java.util.Map">
        WITH BACKORDER AS (SELECT T.${category}                                                                                     AS CATEGORY,
                                  COUNT(1)                                                                                          AS BOL,
                                  COUNT(CASE WHEN T.VIP_SO_INDICATOR = 'Y' THEN T.OPEN_SO_W_O_DEL * T.AVG_SELLING_PRICE_RMB END)    AS BOL_VIP,
                                  SUM(T.OPEN_SO_W_O_DEL * T.AVG_SELLING_PRICE_RMB)                                                  AS BOV,
                                  SUM(CASE WHEN T.VIP_SO_INDICATOR = 'Y' THEN T.OPEN_SO_W_O_DEL * T.AVG_SELLING_PRICE_RMB END)      AS BOV_VIP
                           FROM ${SCPA.DEMAND_BACK_ORDER_V} T
                           WHERE T.${category} IS NOT NULL
                           <include refid="bol_filter"/>
                           GROUP BY T.${category}),
             BACKLOG AS (SELECT T.${category}                                    AS CATEGORY,
                                COUNT(1)                                         AS BACKLOG_LINE,
                                SUM(T.OPEN_SO_W_O_DEL * T.AVG_SELLING_PRICE_RMB) AS BACKLOG_VALUE
                         FROM ${SCPA.DEMAND_BACKLOG_V} T
                         WHERE T.${category} IS NOT NULL
                         <include refid="bol_filter"/>
                         GROUP BY T.${category}),
             BASE AS (
                 SELECT NVL(T.CATEGORY, T2.CATEGORY) AS CATEGORY,
                       T.BOL,
                       T.BOL_VIP,
                       T.BOV,
                       T.BOV_VIP,
                       T2.BACKLOG_LINE,
                       T2.BACKLOG_VALUE
                FROM BACKORDER T
                         FULL JOIN BACKLOG T2 ON T.CATEGORY = T2.CATEGORY
              )
            SELECT * FROM (
                SELECT T.CATEGORY AS "category",
                       T.BOL AS "bol",
                       T.BOL_VIP AS "bolVip",
                       T.BOV AS "bov",
                       T.BOV_VIP AS "bovVip",
                       T.BACKLOG_LINE AS "backlogLine",
                       T.BACKLOG_VALUE AS "backlogValue",
                       CASE WHEN T.BACKLOG_LINE != 0 THEN ROUND(T.BOL / T.BACKLOG_LINE * 100, 1) END AS "bolPercent",
                       CASE WHEN T.BACKLOG_VALUE != 0 THEN ROUND(T.BOV / T.BACKLOG_VALUE * 100, 1) END AS "bovPercent"
                FROM BASE T
                UNION ALL
                SELECT 'Total',
                       SUM(T.BOL),
                       SUM(T.BOL_VIP),
                       SUM(T.BOV),
                       SUM(T.BOV_VIP),
                       SUM(T.BACKLOG_LINE),
                       SUM(T.BACKLOG_VALUE),
                       CASE WHEN SUM(T.BACKLOG_LINE) != 0 THEN ROUND(SUM(T.BOL) / SUM(T.BACKLOG_LINE) * 100, 1) END,
                       CASE WHEN SUM(T.BACKLOG_VALUE) != 0 THEN ROUND(SUM(T.BOV) / SUM(T.BACKLOG_VALUE) * 100, 1) END
                FROM BASE T
            ) TT
            ORDER BY DECODE(TT."category",
                            'Others', 'ZZZZ',
                            'Total', 'ZZZZZ',
                            '0-3D', 'A', '3-7D', 'B', '7-14D', 'C', '14-30D', 'D', '1-2M', 'E', '2-3M', 'F', '3-6M', 'G', '>6M', 'H',
                     TT."category")
            OFFSET 0 ROWS FETCH NEXT 128 ROWS ONLY
    </select>

    <select id="queryReport1Sub" resultType="java.util.Map">
        WITH BACKORDER AS (SELECT T.${category}                                                                                     AS CATEGORY,
                                  COUNT(1)                                                                                          AS BOL,
                                  COUNT(CASE WHEN T.VIP_SO_INDICATOR = 'Y' THEN T.OPEN_SO_W_O_DEL * T.AVG_SELLING_PRICE_RMB END)    AS BOL_VIP,
                                  SUM(T.OPEN_SO_W_O_DEL * T.AVG_SELLING_PRICE_RMB)                                                  AS BOV,
                                  SUM(CASE WHEN T.VIP_SO_INDICATOR = 'Y' THEN T.OPEN_SO_W_O_DEL * T.AVG_SELLING_PRICE_RMB END)      AS BOV_VIP
                           FROM ${SCPA.DEMAND_BACK_ORDER_V} T
                           <where>
                             AND T."${expandColumn}" = #{expandValue, jdbcType=VARCHAR}
                             <foreach collection="parent" item="item" index="index">
                                  AND T."${report1Categories[index]}" = #{item, jdbcType=VARCHAR}
                             </foreach>
                             <include refid="bol_filter"/>
                           </where>
                           GROUP BY T.${category}),
             BACKLOG AS (SELECT T.${category}                                    AS CATEGORY,
                                COUNT(1)                                         AS BACKLOG_LINE,
                                SUM(T.OPEN_SO_W_O_DEL * T.AVG_SELLING_PRICE_RMB) AS BACKLOG_VALUE
                         FROM ${SCPA.DEMAND_BACKLOG_V} T
                         <where>
                             AND T."${expandColumn}" = #{expandValue, jdbcType=VARCHAR}
                             <foreach collection="parent" item="item" index="index">
                                  AND T."${report1Categories[index]}" = #{item, jdbcType=VARCHAR}
                             </foreach>
                             <include refid="bol_filter"/>
                         </where>
                         GROUP BY T.${category}),
             BASE AS (
                 SELECT NVL(T.CATEGORY, T2.CATEGORY) AS CATEGORY,
                       T.BOL,
                       T.BOL_VIP,
                       T.BOV,
                       T.BOV_VIP,
                       T2.BACKLOG_LINE,
                       T2.BACKLOG_VALUE
                FROM BACKORDER T
                         FULL JOIN BACKLOG T2 ON T.CATEGORY = T2.CATEGORY
              )
            SELECT T.CATEGORY AS "category",
                   T.BOL AS "bol",
                   T.BOL_VIP AS "bolVip",
                   T.BOV AS "bov",
                   T.BOV_VIP AS "bovVip",
                   T.BACKLOG_LINE AS "backlogLine",
                   T.BACKLOG_VALUE AS "backlogValue",
                   CASE WHEN T.BACKLOG_LINE != 0 THEN ROUND(T.BOL / T.BACKLOG_LINE * 100, 1) END AS "bolPercent",
                   CASE WHEN T.BACKLOG_VALUE != 0 THEN ROUND(T.BOV / T.BACKLOG_VALUE * 100, 1) END AS "bovPercent"
            FROM BASE T
            ORDER BY DECODE(T.CATEGORY,
                            'Others', 'ZZZZ',
                            'Total', 'ZZZZZ',
                            '0-3D', 'A', '3-7D', 'B', '7-14D', 'C', '14-30D', 'D', '1-2M', 'E', '2-3M', 'F', '3-6M', 'G', '>6M', 'H',
                     T.CATEGORY)
            OFFSET 0 ROWS FETCH NEXT 128 ROWS ONLY
    </select>

    <select id="queryReport2" resultType="java.util.Map">
        WITH BASE AS (SELECT /*+ materialize */ T.${report2Category} AS CATEGORY, ${report2StackBy} AS STACK_BY
                      FROM ${SCPA.DEMAND_BACK_ORDER_V} T
                      <where>
                          <include refid="bol_filter"/>
                          <foreach collection="report1SelectedValues" item="item" index="index" separator="and" open="and">
                              T."${report1Categories[index]}" = #{item, jdbcType=VARCHAR}
                          </foreach>
                      </where>),
        TOP20 AS (SELECT CATEGORY
                    FROM BASE
                    GROUP BY CATEGORY
                    ORDER BY COUNT(1) DESC
                    OFFSET ${report2TopStart} ROWS FETCH NEXT ${report2TopEnd} - ${report2TopStart} ROWS ONLY)

        SELECT T.CATEGORY, T.STACK_BY, COUNT(1) CNT
        FROM BASE T
        INNER JOIN TOP20 ON T.CATEGORY = TOP20.CATEGORY
        GROUP BY T.CATEGORY, T.STACK_BY
    </select>

    <select id="queryReport3" resultType="java.util.Map">
        SELECT NN."xAxis",
            NN."yAxis1",
            NN."yAxis2",
            NN."yAxis3",
            NN."yAxis4",
            NN."yAxis5",
            NN."yAxis6",
            NN."yAxis7",
            NN."yAxis8"
        FROM (
        SELECT TO_CHAR(T.DATE$, 'YYYY/MM/DD')       "xAxis",
            SUM(DECODE(T.DELAY_DEPTH, '0-3D', 1))   "yAxis1",
            SUM(DECODE(T.DELAY_DEPTH, '3-7D', 1))   "yAxis2",
            SUM(DECODE(T.DELAY_DEPTH, '7-14D', 1))  "yAxis3",
            SUM(DECODE(T.DELAY_DEPTH, '14-30D', 1)) "yAxis4",
            SUM(DECODE(T.DELAY_DEPTH, '1-2M', 1))   "yAxis5",
            SUM(DECODE(T.DELAY_DEPTH, '2-3M', 1))   "yAxis6",
            SUM(DECODE(T.DELAY_DEPTH, '3-6M', 1))   "yAxis7",
            SUM(DECODE(T.DELAY_DEPTH, '>6M', 1))    "yAxis8"
        FROM ${SCPA.DEMAND_BACK_ORDER_HIST} T
        WHERE T.DATE$ BETWEEN TRUNC(ADD_MONTHS(SYSDATE, -1), 'MM') AND SYSDATE
            <include refid="bol_filter"/>
            <foreach collection="report1SelectedValues" item="item" index="index" separator="and" open="and">
                T."${report1Categories[index]}" = #{item, jdbcType=VARCHAR}
            </foreach>
            GROUP BY TO_CHAR(T.DATE$, 'YYYY/MM/DD')
        ) NN
        ORDER BY NN."xAxis"
    </select>
</mapper>
