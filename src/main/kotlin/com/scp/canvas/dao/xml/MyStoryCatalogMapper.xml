<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.canvas.dao.IMyStoryCatalogDao">
    <select id="queryPagesById" resultType="java.util.Map">
        SELECT T.PAGE_ID,
               T.PAGE_TYPE,
               DECODE(T.PAGE_TYPE, 'BUILT_IN', T.PAGE_ID, T.PAGE_TITLE) AS PAGE_TITLE,
               DECODE(T.CREATE_BY$, UPPER(#{session.userid,jdbcType=VARCHAR}), 'Y', 'N') AS IS_OWNER,
               NVL(T2.USER_NAME, T.CREATE_BY$) AUTHOR_NAME,
               TO_CHAR(T.CREATE_DATE$, 'YYYY/MM/DD HH24:MI:SS') CREATE_DATE
        FROM MY_STORY_PAGES T LEFT JOIN SY_USER_MASTER_DATA T2 ON T.CREATE_BY$ = T2.SESA_CODE
        WHERE T.VARIANT_ID = #{bindTo, jdbcType=VARCHAR}
        ORDER BY NVL(T.PAGE_ORDER, 999), T.PAGE_TYPE, NVL(T.PAGE_TITLE, T.PAGE_ID)
    </select>

    <update id="savePageOrder">
        BEGIN
            <foreach collection="pageList" item="item" index="index">
                UPDATE MY_STORY_PAGES T SET T.PAGE_ORDER = #{index,jdbcType=NUMERIC} WHERE T.VARIANT_ID = #{bindTo, jdbcType=VARCHAR} AND PAGE_ID = #{item,jdbcType=VARCHAR};
            </foreach>
        END;
    </update>

    <delete id="deletePage">
        DELETE FROM MY_STORY_PAGES T WHERE T.PAGE_ID = #{pageId,jdbcType=VARCHAR}
    </delete>

    <insert id="savePage">
        DECLARE
            PARAMS_COLB CLOB := #{content, jdbcType=CLOB};
        BEGIN
			INSERT INTO MY_STORY_PAGES (PAGE_ID, VARIANT_ID, PAGE_TITLE, PAGE_TYPE, CONTENT, CREATE_DATE$, CREATE_BY$)
			VALUES
			(#{pageId, jdbcType=VARCHAR}, #{bindTo, jdbcType=VARCHAR}, #{title, jdbcType=VARCHAR}, 'MANUAL', PARAMS_COLB,
			SYSDATE, #{session.userid, jdbcType=VARCHAR});
		END;
    </insert>

    <insert id="modifyPage">
        DECLARE
            PARAMS_COLB CLOB := #{content, jdbcType=CLOB};
        BEGIN
            UPDATE MY_STORY_PAGES
               SET PAGE_TITLE = #{title,jdbcType=VARCHAR},
                   CONTENT = PARAMS_COLB,
                   UPDATE_DATE$ = SYSDATE,
                   UPDATE_BY$ = #{session.userid, jdbcType=VARCHAR}
             WHERE PAGE_ID = #{id,jdbcType=VARCHAR};
		END;
    </insert>

    <select id="queryPageById" resultType="java.util.Map">
        SELECT T.PAGE_ID, T.PAGE_TITLE, T.CONTENT
          FROM MY_STORY_PAGES T WHERE T.PAGE_ID = #{pageId, jdbcType=VARCHAR}
    </select>
</mapper>

