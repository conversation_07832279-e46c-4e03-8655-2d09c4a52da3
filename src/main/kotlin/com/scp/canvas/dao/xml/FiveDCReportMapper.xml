<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.canvas.dao.IFiveDCReportDao">
    <sql id="filter">
        <if test="_filters != null and _filters != ''.toString()">
            and ${_filters}
        </if>
    </sql>

    <select id="queryReport1ColumnOpts" resultType="java.util.Map">
        SELECT T.DISPLAY_COLUMN AS NAME, GROUP_NAME AS CATEGORY
        FROM SCPA.FIVE_DC_COLUMN_CONFIG T
        ORDER BY DECODE(T.GROUP_NAME, 'Master Data', 10,
                                      'Inventory', 20,
                                      'Open PO', 30,
                                      'Open SO', 40,
                                      'FCST - His.Ver. for Cur. Month', 51,
                                      'FCST - Cur.Ver. for Fut. Month', 52,
                                      'Actual Demand', 60),
                 T.DISPLAY_COLUMN
    </select>

    <select id="queryPageAdmin" resultType="java.lang.Integer">
        SELECT COUNT(1) CNT
          FROM SY_MENU_AUTH T
         WHERE UPPER(T.AUTH_DETAILS) = 'ADMIN' AND T.USER_ID = #{userid, jdbcType=VARCHAR} AND T.MENU_CODE = #{parentCode, jdbcType=VARCHAR}
    </select>

    <select id="queryFilters" resultType="java.util.Map">
        SELECT NAME, CATEGORY
        FROM SCPA.MATERIAL_MASTER_FILTER_V T
        ORDER BY T.CATEGORY, DECODE(T.NAME, 'Others', 'zzz', T.NAME)
    </select>

    <select id="queryReport1AllMatchedList" resultType="java.util.Map">
        SELECT T.TABLE_NAME,
               T.DISPLAY_COLUMN,
               <choose>
                   <when test="valueType == 'Moving Average Price'.toString()">
                        T.MOVING_AVG_VALUE_SCRIPT
                   </when>
                   <when test="valueType == 'Quantity'.toString()">
                        T.QTY_SCRIPT
                   </when>
                   <otherwise>
                        T.NET_NET_VALUE_SCRIPT
                   </otherwise>
               </choose> AS CODE
        FROM FIVE_DC_COLUMN_CONFIG T WHERE T.DISPLAY_COLUMN IN
        <foreach collection="displayColumn" item="column" open="(" close=")" separator=",">
            #{column, jdbcType=VARCHAR}
        </foreach>
    </select>

    <sql id="queryReport1TableSql">
        WITH MMV AS (
            SELECT T.MATERIAL AS JOIN_KEY1, T.PLANT_CODE AS JOIN_KEY2,
            <foreach collection="report1PivotBy" separator="," item="item">
                T.${item}
            </foreach> FROM ${SCPA.MATERIAL_MASTER_V} T
            <where>
                <include refid="filter"/>
            </where>
         )
        <foreach collection="tableList" separator="," item="table" open="," index="index">
            TEMP${index} AS (
                SELECT
                <choose>
                    <when test="table == 'CLO_STRUCTURE_V'.toString() or table == 'CLO_STRUCTURE_HIST'.toString()">
                        T.T0_MATERIAL AS JOIN_KEY1, T.T0_PLANT_CODE AS JOIN_KEY2,
                    </when>
                    <otherwise>
                        T.MATERIAL AS JOIN_KEY1, T.PLANT_CODE AS JOIN_KEY2,
                    </otherwise>
                </choose>
                <foreach collection="tempTableMap[table]" item="item2" separator=",">
                    ${item2.CODE} AS "${item2.DISPLAY_COLUMN}"
                </foreach>
                  FROM ${table} T
                  <if test="hasDateColumnMap[table]">
                      WHERE T.DATE$ = TO_DATE(#{report1Date, jdbcType=VARCHAR}, 'YYYY/MM/DD')
                  </if>
                  <choose>
                    <when test="table == 'CLO_STRUCTURE_V'.toString() or table == 'CLO_STRUCTURE_HIST'.toString()">
                        GROUP BY T.T0_MATERIAL, T.T0_PLANT_CODE
                    </when>
                    <otherwise>
                        GROUP BY T.MATERIAL, T.PLANT_CODE
                    </otherwise>
                </choose>

            )
        </foreach>
        SELECT
            <foreach collection="report1PivotBy" separator="," item="item">
                MMV.${item}
            </foreach>,
            CM.COMMENTS,
            <foreach collection="unionColumn" item="column" separator=",">
                ${column}
            </foreach>
          FROM MMV
          LEFT JOIN SCPA.FIVE_DC_COMMENTS CM ON
          <foreach collection="report1PivotByOrdered" separator=" || '#' || " item="item">
                MMV.${item}
          </foreach> = CM.KEY
          <foreach collection="tableList" index="index">
            LEFT JOIN TEMP${index} ON MMV.JOIN_KEY1 = TEMP${index}.JOIN_KEY1 AND MMV.JOIN_KEY2 = TEMP${index}.JOIN_KEY2
          </foreach>
        GROUP BY CM.COMMENTS,
        <foreach collection="report1PivotBy" separator="," item="item">
            MMV.${item}
        </foreach>
    </sql>

    <select id="queryReport1TableCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1TableSql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1Table" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1TableSql"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport1TableDetailsTableName" resultType="java.lang.String">
        SELECT NVL(T.VIEW_DETAIL_TABLE_NAME, T.TABLE_NAME) FROM FIVE_DC_COLUMN_CONFIG T
         WHERE T.DISPLAY_COLUMN = #{report1Table.selectedCell.displayColumn, jdbcType=VARCHAR}
    </select>

    <sql id="queryReport1TableDetailsSql">
        WITH MMV AS (
            SELECT T.MATERIAL, T.PLANT_CODE
            FROM ${SCPA.MATERIAL_MASTER_V} T
            <where>
                <include refid="filter"/>
                <foreach collection="selectValues" separator=" AND " open="AND" item="item" index="index">
                    <choose>
                        <when test="item == null">
                            T."${report1PivotBy[index]}" IS NULL
                        </when>
                        <otherwise>
                            T."${report1PivotBy[index]}" = #{item, jdbcType=VARCHAR}
                        </otherwise>
                    </choose>
                </foreach>
            </where>
         )
        SELECT T.* FROM ${tableName} T INNER JOIN MMV T2 ON
        <choose>
            <when test="tableName == 'CLO_STRUCTURE_V'.toString() or tableName == 'CLO_STRUCTURE_HIST'.toString()">
                T.T0_MATERIAL = T2.MATERIAL AND T.T0_PLANT_CODE = T2.PLANT_CODE
            </when>
            <otherwise>
                T.MATERIAL = T2.MATERIAL AND T.PLANT_CODE = T2.PLANT_CODE
            </otherwise>
        </choose>
        <if test="hasDateColumn">
          WHERE T.DATE$ = TO_DATE(#{report1Date, jdbcType=VARCHAR}, 'YYYY/MM/DD')
        </if>
    </sql>

    <select id="queryReport1TableDetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1TableDetailsSql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1TableDetails" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1TableDetailsSql"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport1TreeTable" resultType="java.util.LinkedHashMap">
        WITH MMV AS (
            SELECT T.MATERIAL AS JOIN_KEY1, T.PLANT_CODE AS JOIN_KEY2,
            <foreach collection="report1PivotBy" separator="," item="item">
                T.${item}
            </foreach> FROM ${SCPA.MATERIAL_MASTER_V} T
            <where>
                <include refid="filter"/>
            </where>
         )
        <foreach collection="tableList" separator="," item="table" open="," index="index">
            TEMP${index} AS (
                SELECT
                <choose>
                    <when test="table == 'CLO_STRUCTURE_V'.toString() or table == 'CLO_STRUCTURE_HIST'.toString()">
                        T.T0_MATERIAL AS JOIN_KEY1, T.T0_PLANT_CODE AS JOIN_KEY2,
                    </when>
                    <otherwise>
                        T.MATERIAL AS JOIN_KEY1, T.PLANT_CODE AS JOIN_KEY2,
                    </otherwise>
                </choose>
                <foreach collection="tempTableMap[table]" item="item2" separator=",">
                    ${item2.CODE} AS "${item2.DISPLAY_COLUMN}"
                </foreach>
                  FROM ${table} T
                  <if test="hasDateColumnMap[table]">
                      WHERE T.DATE$ = TO_DATE(#{report1Date, jdbcType=VARCHAR}, 'YYYY/MM/DD')
                  </if>
                  <choose>
                    <when test="table == 'CLO_STRUCTURE_V'.toString() or table == 'CLO_STRUCTURE_HIST'.toString()">
                        GROUP BY T.T0_MATERIAL, T.T0_PLANT_CODE
                    </when>
                    <otherwise>
                        GROUP BY T.MATERIAL, T.PLANT_CODE
                    </otherwise>
                </choose>

            )
        </foreach>
        SELECT
            NVL(MMV.${category}, 'Others') AS "category",
            <foreach collection="unionColumn" item="column" separator=",">
                ${column}
            </foreach>
          FROM MMV
          <foreach collection="tableList" index="index">
            LEFT JOIN TEMP${index} ON MMV.JOIN_KEY1 = TEMP${index}.JOIN_KEY1 AND MMV.JOIN_KEY2 = TEMP${index}.JOIN_KEY2
          </foreach>
        GROUP BY MMV.${category}
        ORDER BY DECODE(MMV.${category}, 'Others', 'zzzzzzzz', MMV.${category})
        OFFSET 0 ROWS FETCH NEXT 128 ROWS ONLY
    </select>

    <select id="queryReport1TreeTableSub" resultType="java.util.LinkedHashMap">
        WITH MMV AS (
            SELECT T.MATERIAL AS JOIN_KEY1, T.PLANT_CODE AS JOIN_KEY2,
            <foreach collection="report1PivotBy" separator="," item="item">
                T.${item}
            </foreach> FROM ${SCPA.MATERIAL_MASTER_V} T
            <where>
                AND T."${expandColumn}" = #{expandValue, jdbcType=VARCHAR}
                <foreach collection="parent" item="item" index="index">
                    AND T."${report1PivotBy[index]}" = #{item, jdbcType=VARCHAR}
                </foreach>
                <include refid="filter"/>
            </where>
         )
        <foreach collection="tableList" separator="," item="table" open="," index="index">
            TEMP${index} AS (
                SELECT
                <choose>
                    <when test="table == 'CLO_STRUCTURE_V'.toString() or table == 'CLO_STRUCTURE_HIST'.toString()">
                        T.T0_MATERIAL AS JOIN_KEY1, T.T0_PLANT_CODE AS JOIN_KEY2,
                    </when>
                    <otherwise>
                        T.MATERIAL AS JOIN_KEY1, T.PLANT_CODE AS JOIN_KEY2,
                    </otherwise>
                </choose>
                <foreach collection="tempTableMap[table]" item="item2" separator=",">
                    ${item2.CODE} AS "${item2.DISPLAY_COLUMN}"
                </foreach>
                  FROM ${table} T
                  <if test="hasDateColumnMap[table]">
                      WHERE T.DATE$ = TO_DATE(#{report1Date, jdbcType=VARCHAR}, 'YYYY/MM/DD')
                  </if>
                  <choose>
                    <when test="table == 'CLO_STRUCTURE_V'.toString() or table == 'CLO_STRUCTURE_HIST'.toString()">
                        GROUP BY T.T0_MATERIAL, T.T0_PLANT_CODE
                    </when>
                    <otherwise>
                        GROUP BY T.MATERIAL, T.PLANT_CODE
                    </otherwise>
                </choose>

            )
        </foreach>
        SELECT
            NVL(MMV.${category}, 'Others') AS "category",
            <foreach collection="unionColumn" item="column" separator=",">
                ${column}
            </foreach>
          FROM MMV
          <foreach collection="tableList" index="index">
            LEFT JOIN TEMP${index} ON MMV.JOIN_KEY1 = TEMP${index}.JOIN_KEY1 AND MMV.JOIN_KEY2 = TEMP${index}.JOIN_KEY2
          </foreach>
        WHERE MMV.${category} IS NOT NULL
        GROUP BY MMV.${category}
        ORDER BY DECODE(NVL(MMV.${category}, 'Others'), 'Others', 'zzzzzzzz', NVL(MMV.${category}, 'Others'))
        OFFSET 0 ROWS FETCH NEXT 128 ROWS ONLY
    </select>

    <select id="queryReport1PieChart" resultType="java.util.HashMap">
        WITH MMV AS (
            SELECT T.MATERIAL AS JOIN_KEY1, T.PLANT_CODE AS JOIN_KEY2,
            <foreach collection="report1PivotBy" separator="," item="item">
                T.${item}
            </foreach> FROM ${SCPA.MATERIAL_MASTER_V} T
            <where>
                <include refid="filter"/>
            </where>
         )
        <foreach collection="tableList" separator="," item="table" open="," index="index">
            TEMP${index} AS (
                SELECT
                <choose>
                    <when test="table == 'CLO_STRUCTURE_V'.toString() or table == 'CLO_STRUCTURE_HIST'.toString()">
                        T.T0_MATERIAL AS JOIN_KEY1, T.T0_PLANT_CODE AS JOIN_KEY2,
                    </when>
                    <otherwise>
                        T.MATERIAL AS JOIN_KEY1, T.PLANT_CODE AS JOIN_KEY2,
                    </otherwise>
                </choose>
                <foreach collection="tempTableMap[table]" item="item2" separator=",">
                    ${item2.CODE} AS "${item2.DISPLAY_COLUMN}"
                </foreach>
                  FROM ${table} T
                  <if test="hasDateColumnMap[table]">
                      WHERE T.DATE$ = TO_DATE(#{report1Date, jdbcType=VARCHAR}, 'YYYY/MM/DD')
                  </if>
                  <choose>
                    <when test="table == 'CLO_STRUCTURE_V'.toString() or table == 'CLO_STRUCTURE_HIST'.toString()">
                        GROUP BY T.T0_MATERIAL, T.T0_PLANT_CODE
                    </when>
                    <otherwise>
                        GROUP BY T.MATERIAL, T.PLANT_CODE
                    </otherwise>
                </choose>

            )
        </foreach>
        SELECT
            NVL(MMV.${category}, 'Others') AS "category",
            ${unionColumn[0]}
          FROM MMV
          <foreach collection="tableList" index="index">
            LEFT JOIN TEMP${index} ON MMV.JOIN_KEY1 = TEMP${index}.JOIN_KEY1 AND MMV.JOIN_KEY2 = TEMP${index}.JOIN_KEY2
          </foreach>
        GROUP BY NVL(MMV.${category}, 'Others')
    </select>

    <select id="queryReport1TreemapChart" resultType="java.util.LinkedHashMap">
        WITH MMV AS (
            SELECT T.MATERIAL AS JOIN_KEY1, T.PLANT_CODE AS JOIN_KEY2,
            <foreach collection="report1PivotBy" separator="," item="item">
                T.${item}
            </foreach> FROM ${SCPA.MATERIAL_MASTER_V} T
            <where>
                <include refid="filter"/>
            </where>
         )
        <foreach collection="tableList" separator="," item="table" open="," index="index">
            TEMP${index} AS (
                SELECT
                <choose>
                    <when test="table == 'CLO_STRUCTURE_V'.toString() or table == 'CLO_STRUCTURE_HIST'.toString()">
                        T.T0_MATERIAL AS JOIN_KEY1, T.T0_PLANT_CODE AS JOIN_KEY2,
                    </when>
                    <otherwise>
                        T.MATERIAL AS JOIN_KEY1, T.PLANT_CODE AS JOIN_KEY2,
                    </otherwise>
                </choose>
                <foreach collection="tempTableMap[table]" item="item2" separator=",">
                    ${item2.CODE} AS "${item2.DISPLAY_COLUMN}"
                </foreach>
                  FROM ${table} T
                  <if test="hasDateColumnMap[table]">
                      WHERE T.DATE$ = TO_DATE(#{report1Date, jdbcType=VARCHAR}, 'YYYY/MM/DD')
                  </if>
                  <choose>
                    <when test="table == 'CLO_STRUCTURE_V'.toString() or table == 'CLO_STRUCTURE_HIST'.toString()">
                        GROUP BY T.T0_MATERIAL, T.T0_PLANT_CODE
                    </when>
                    <otherwise>
                        GROUP BY T.MATERIAL, T.PLANT_CODE
                    </otherwise>
                </choose>

            )
        </foreach>
        SELECT
            <foreach collection="report1PivotBy" separator="," item="item" index="index">
                MMV.${item} AS "category${index + 1}"
            </foreach>,
            ${unionColumn[0]}
          FROM MMV
          <foreach collection="tableList" index="index">
            LEFT JOIN TEMP${index} ON MMV.JOIN_KEY1 = TEMP${index}.JOIN_KEY1 AND MMV.JOIN_KEY2 = TEMP${index}.JOIN_KEY2
          </foreach>
        GROUP BY
        <foreach collection="report1PivotBy" separator="," item="item">
            MMV.${item}
        </foreach>
    </select>

    <update id="saveReport1Comments">
        MERGE INTO SCPA.FIVE_DC_COMMENTS T
        USING
        (
        <foreach collection="list" item="item" separator="union all">
            SELECT #{item.key, jdbcType=VARCHAR} KEY,
                   #{item.comments, jdbcType=VARCHAR} COMMENTS
            from dual
        </foreach>
        ) S ON (T.KEY = S.KEY)
        WHEN MATCHED THEN
        UPDATE SET
            T.COMMENTS = S.COMMENTS,
            T.UPDATE_DATE$ = SYSDATE,
            T.UPDATE_BY$ = #{session.userid, jdbcType=VARCHAR}
        WHEN NOT MATCHED THEN
        INSERT (KEY, COMMENTS, CREATE_BY$, CREATE_DATE$)
        VALUES (S.KEY, S.COMMENTS, #{session.userid, jdbcType=VARCHAR}, SYSDATE)
    </update>

    <select id="queryReport2" resultType="java.util.LinkedHashMap">
        WITH MMV AS (
            SELECT T3.DATE$ AS JOIN_KEY0, T.MATERIAL AS JOIN_KEY1, T.PLANT_CODE AS JOIN_KEY2
            FROM ${SCPA.MATERIAL_MASTER_V} T
            CROSS JOIN SY_CALENDAR T3
            <where>
                T3.DATE$ BETWEEN TO_DATE(#{report2DateRange[0], jdbcType=VARCHAR}, 'YYYY/MM/DD') AND TO_DATE(#{report2DateRange[1], jdbcType=VARCHAR} || ' 23:59:59', 'YYYY/MM/DD HH24:MI:SS')
                AND T3.NAME = 'National Holidays'
                AND T3.WORKING_DAY = 1
                <include refid="filter"/>
                <foreach collection="selectValues" separator=" AND " open="AND" item="item" index="index">
                    <choose>
                        <when test="item == null">
                            T."${report1PivotBy[index]}" IS NULL
                        </when>
                        <otherwise>
                            T."${report1PivotBy[index]}" = #{item, jdbcType=VARCHAR}
                        </otherwise>
                    </choose>
                </foreach>
                <foreach collection="report1TreeTableSelectedValues" separator=" AND " open="AND" item="item" index="index">
                    <choose>
                        <when test="item == null">
                            T."${report1PivotBy[index]}" IS NULL
                        </when>
                        <otherwise>
                            T."${report1PivotBy[index]}" = #{item, jdbcType=VARCHAR}
                        </otherwise>
                    </choose>
                </foreach>
            </where>
         )
        <foreach collection="tableList" separator="," item="table" open="," index="index">
            TEMP${index} AS (
                SELECT /*+ parallel(t 4) */
                <if test="hasDateColumnMap[table]">
                    T.DATE$ AS JOIN_KEY0,
                </if>
                <choose>
                    <when test="table == 'CLO_STRUCTURE_V'.toString() or table == 'CLO_STRUCTURE_HIST'.toString()">
                        T.T0_MATERIAL AS JOIN_KEY1, T.T0_PLANT_CODE AS JOIN_KEY2,
                    </when>
                    <otherwise>
                        T.MATERIAL AS JOIN_KEY1, T.PLANT_CODE AS JOIN_KEY2,
                    </otherwise>
                </choose>
                <foreach collection="tempTableMap[table]" item="item2" separator=",">
                    ${item2.CODE} AS "${item2.DISPLAY_COLUMN}"
                </foreach>
                  FROM ${table} T
                  <if test="hasDateColumnMap[table]">
                      WHERE T.DATE$ BETWEEN TO_DATE(#{report2DateRange[0], jdbcType=VARCHAR}, 'YYYY/MM/DD') AND TO_DATE(#{report2DateRange[1], jdbcType=VARCHAR} || ' 23:59:59', 'YYYY/MM/DD HH24:MI:SS')
                  </if>
                  <choose>
                    <when test="table == 'CLO_STRUCTURE_V'.toString() or table == 'CLO_STRUCTURE_HIST'.toString()">
                        GROUP BY T.T0_MATERIAL, T.T0_PLANT_CODE
                    </when>
                    <otherwise>
                        GROUP BY
                        <if test="hasDateColumnMap[table]">
                        T.DATE$,
                        </if>
                        T.MATERIAL, T.PLANT_CODE
                    </otherwise>
                </choose>

            )
        </foreach>
        SELECT /*+ parallel */
            MMV.JOIN_KEY0                          AS AXIS,
            <foreach collection="unionColumn" item="column" separator=",">
                ${column}
            </foreach>
          FROM MMV
          <foreach collection="tableList" index="index" item="item">
            LEFT JOIN TEMP${index} ON MMV.JOIN_KEY1 = TEMP${index}.JOIN_KEY1 AND MMV.JOIN_KEY2 = TEMP${index}.JOIN_KEY2
            <if test="hasDateColumnMap[item]">
                AND MMV.JOIN_KEY0 = TEMP${index}.JOIN_KEY0
            </if>
          </foreach>
        GROUP BY MMV.JOIN_KEY0
        ORDER BY MMV.JOIN_KEY0
    </select>
</mapper>

