<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.canvas.dao.IMyStoryMPTDao">
    <sql id="mpt_filter">
        <if test="_filters != null and _filters != ''.toString()">
            and ${_filters}
        </if>
        <if test="_filters2 != null and _filters2 != ''.toString()">
            and ${_filters2}
        </if>
        and T.ACTION != '2. Exclusion'
    </sql>

    <select id="queryFilterOpts" resultType="java.util.Map">
        select * from SCPA.MPT_PENDING_FILTER_V t
        order by t.category,decode(t.name,'Others','zzz',t.name)
    </select>

    <select id="queryPivotOpts" resultType="java.lang.String">
        SELECT T.COLUMN_NAME
        FROM USER_TAB_COLS T
        WHERE T.TABLE_NAME = 'MPT_PENDING_V'
          AND T.DATA_TYPE = 'VARCHAR2'
          AND T.COLUMN_NAME NOT LIKE '%$%'
          AND T.COLUMN_NAME NOT IN('SALES_ORDER_NUMBER', 'SALES_ORDER_ITEM')
        ORDER BY T.COLUMN_NAME
    </select>

    <select id="queryReport1" resultType="java.util.Map">
        WITH BASE AS (
                    SELECT NVL(T.${category}, 'Others')                          AS ${category},
                           SUM(DECODE(ACTION, '4. Escalation', 1, 0))            AS ESCALATION_LINE,
                           SUM(DECODE(CLASSIFICATION, 'To Be Eliminated', 1, 0)) AS TO_BE_ELIMINATED_LINE,
                           SUM(DECODE(ACTION, '2. Exclusion', 0, 1))             AS OPEN_ERROR_LINE
                    FROM ${SCPA.MPT_PENDING_V} T
                        <where>
                            <include refid="mpt_filter"/>
                        </where>
                    GROUP BY NVL(T.${category}, 'Others')
                ),
        RESULT AS (
            SELECT T.${category}           AS "category",
                   ROUND(DECODE(T.TO_BE_ELIMINATED_LINE, 0, 0, T.ESCALATION_LINE / T.TO_BE_ELIMINATED_LINE) * 100, 1) AS "escalationRatio",
                   T.ESCALATION_LINE       AS "escalationLine",
                   T.TO_BE_ELIMINATED_LINE AS "toBeEliminatedLine",
                   T.OPEN_ERROR_LINE       AS "openErrorLine"
            FROM BASE T
            UNION ALL
            SELECT 'Total',
                   ROUND(DECODE(SUM(T.TO_BE_ELIMINATED_LINE), 0, 0, SUM(T.ESCALATION_LINE) / SUM(T.TO_BE_ELIMINATED_LINE)) * 100 ,1),
                   SUM(T.ESCALATION_LINE),
                   SUM(T.TO_BE_ELIMINATED_LINE),
                   SUM(T.OPEN_ERROR_LINE)
            FROM BASE T
        )
        SELECT T."category",
               NVL(T."escalationRatio", 0)    AS "escalation",
               NVL(T."escalationLine", 0)     AS "escalationLine",
               NVL(T."toBeEliminatedLine", 0) AS "toBeEliminatedLine",
               NVL(T."openErrorLine", 0)      AS "openErrorLine"
          FROM RESULT T
        ORDER BY DECODE(T."category", 'Others', -1, 'Total', -2, '>365CD', 0, '181-365CD', 1, '91-180CD', 2, '61-90CD', 3, '31-60CD', 4, '15-30CD', 5,
        '8-14CD', 6, '4-7CD', 7, '2-3CD', 8, '1CD', 9, '0CD', 10, NVL(T."escalationRatio", 0)) DESC
        OFFSET 0 ROWS FETCH NEXT 128 ROWS ONLY
    </select>

    <select id="queryReport1Sub" resultType="java.util.Map">
        WITH BASE AS (
            SELECT T.${category}                                                            AS "category",
                   CASE WHEN SUM(DECODE(CLASSIFICATION, 'To Be Eliminated', 1, 0)) = 0 THEN 0
                   ELSE ROUND(SUM(DECODE(ACTION, '4. Escalation', 1, 0)) /
                        SUM(DECODE(CLASSIFICATION, 'To Be Eliminated', 1, 0)) * 100, 1) END AS ESCALATION_RATIO,
                   SUM(DECODE(ACTION, '4. Escalation', 1, 0))                               AS ESCALATION_LINE,
                   SUM(DECODE(CLASSIFICATION, 'To Be Eliminated', 1, 0))                    AS TO_BE_ELIMINATED_LINE,
                   SUM(DECODE(ACTION, '2. Exclusion', 0, 1))                                AS OPEN_ERROR_LINE
            FROM ${SCPA.MPT_PENDING_V} T
            <where>
                T."${expandColumn}" = #{expandValue, jdbcType=VARCHAR}
                <foreach collection="parent" item="item" index="index">
                    AND T."${report1Categories[index]}" = #{item, jdbcType=VARCHAR}
                </foreach>
                <include refid="mpt_filter"/>
            </where>
            GROUP BY T.${category}
        )
        SELECT T."category",
               NVL(T."ESCALATION_RATIO", 0)      AS "escalation",
               NVL(T."ESCALATION_LINE", 0)       AS "escalationLine",
               NVL(T."TO_BE_ELIMINATED_LINE", 0) AS "toBeEliminatedLine",
               NVL(T."OPEN_ERROR_LINE", 0)       AS "openErrorLine"
          FROM BASE T
        ORDER BY DECODE(T."category", 'Others', -1, 'Total', -2, '>365CD', 0, '181-365CD', 1, '91-180CD', 2, '61-90CD', 3, '31-60CD', 4, '15-30CD', 5,
            '8-14CD', 6, '4-7CD', 7, '2-3CD', 8, '1CD', 9, '0CD', 10, NVL(T."ESCALATION_RATIO", 0)) DESC
        OFFSET 0 ROWS FETCH NEXT 128 ROWS ONLY
    </select>

    <select id="queryReport2" resultType="java.util.Map">
        WITH BASE AS (SELECT T.${report2Category} AS CATEGORY,
                             ${report2StackBy}    AS STACK_BY
                      FROM ${SCPA.MPT_PENDING_V} T
                      <where>
                          ACTION = '4. Escalation'
                          <foreach collection="report1SelectedValues" item="item" index="index" separator="and" open="and">
                              T."${report1Categories[index]}" = #{item, jdbcType=VARCHAR}
                          </foreach>
                          <include refid="mpt_filter"/>
                      </where>),
        TOP_BASE AS (SELECT
                         CATEGORY
                     FROM BASE
                     GROUP BY CATEGORY
                     ORDER BY COUNT(1) DESC
                     OFFSET ${report2TopStart} ROWS FETCH NEXT ${report2TopEnd} - ${report2TopStart} ROWS ONLY)
        SELECT T.CATEGORY, T.STACK_BY, COUNT(1) CNT
        FROM BASE T
        INNER JOIN TOP_BASE ON T.CATEGORY = TOP_BASE.CATEGORY
        GROUP BY T.CATEGORY, T.STACK_BY
    </select>

    <select id="queryReport3" resultType="java.util.Map">
        WITH TEMP AS (SELECT ${report3Category}                                           AS CATEGORY,
                             TRUNC(T.CREATE_DATE$, 'DD')                                  AS CALENDAR_DATE,
                             NVL(SUM(DECODE(ACTION, '4. Escalation', 1, 0)),0)            AS ESCALATION_LINE,
                             NVL(SUM(DECODE(CLASSIFICATION, 'To Be Eliminated', 1, 0)),0) AS TO_BE_ELIMINATED_LINE
                      FROM ${SCPA.MPT_PENDING_HIST} T
                      INNER JOIN (SELECT DATE$
                                  FROM SCPA.SY_CALENDAR
                                  WHERE NAME = 'MPT Working Calendar'
                                    AND WORKING_DAY = 1) SC
                            ON TRUNC(T.CREATE_DATE$, 'DD') = SC.DATE$
                      WHERE TRUNC(T.CREATE_DATE$, 'DD') BETWEEN TO_DATE(#{report3DateRange[0],jdbcType=VARCHAR}, 'YYYY/MM/DD') AND TO_DATE(#{report3DateRange[1],jdbcType=VARCHAR}, 'YYYY/MM/DD')
                      <foreach collection="report1SelectedValues" item="item" index="index" separator="and" open="and">
                          T."${report1Categories[index]}" = #{item, jdbcType=VARCHAR}
                      </foreach>
                      <include refid="mpt_filter"/>
                      GROUP BY ${report3Category}, TRUNC(T.CREATE_DATE$, 'DD'))
        SELECT T.CATEGORY,
               T.CALENDAR_DATE,
               <choose>
                   <when test="report3ValueType == 'ESCALATION_RATIO'">
                       ROUND(DECODE(T.TO_BE_ELIMINATED_LINE, 0, 0, T.ESCALATION_LINE / T.TO_BE_ELIMINATED_LINE) * 100, 1) AS ESCALATION_RATIO
                   </when>
                   <otherwise>
                       T.${report3ValueType}       AS ESCALATION_RATIO
                   </otherwise>
               </choose>
        FROM TEMP T
        ORDER BY CALENDAR_DATE, CATEGORY
    </select>
</mapper>
