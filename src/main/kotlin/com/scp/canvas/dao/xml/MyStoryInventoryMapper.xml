<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.canvas.dao.IMyStoryInventoryDao">
    <sql id="inv_filter">
        <if test="_filters != null and _filters != ''.toString()">
            and ${_filters}
        </if>
        <if test="_filters2 != null and _filters2 != ''.toString()">
            and ${_filters2}
        </if>
    </sql>

    <select id="queryFilterOpts" resultType="java.util.Map">
        SELECT * FROM SCPA.INVENTORY_STRUCTURE_FILTER_V T WHERE T.CATEGORY IN (
            SELECT T.COLUMN_NAME FROM USER_TAB_COLS T WHERE T.TABLE_NAME = 'MY_STORY_INVENTORY_V'
        )
        ORDER BY T.CATEGORY, DECODE(T.NAME, 'Others', 'zzz' ,T.NAME)
    </select>

    <select id="queryStackOpts" resultType="java.lang.String">
        SELECT T.CATEGORY
         FROM SCPA.INVENTORY_STRUCTURE_FILTER_V T WHERE T.CATEGORY IN (
            SELECT T.COLUMN_NAME FROM USER_TAB_COLS T WHERE T.TABLE_NAME = 'MY_STORY_INVENTORY_V'
        )
        GROUP BY T.CATEGORY
        HAVING COUNT(1) &lt; 100
    </select>

    <select id="queryPivotOpts" resultType="java.lang.String">
        SELECT T.COLUMN_NAME
        FROM USER_TAB_COLS T
        WHERE T.TABLE_NAME = 'MY_STORY_INVENTORY_V'
          AND T.DATA_TYPE = 'VARCHAR2'
          AND T.COLUMN_NAME NOT LIKE '%$%'
        ORDER BY T.COLUMN_NAME
    </select>

    <select id="queryReport1" resultType="java.util.Map">
        WITH BASE AS (
            SELECT NVL(T.${category}, 'Others') AS "category",
               <foreach collection="availableColumns" item="item" separator=",">
                   NVL(SUM(T.${item}${suffix}), 0)     AS "${item}"
               </foreach>,
               SUM(GROSS_UHS_VALUE) AS GROSS_UHS_VALUE,
               SUM(NET_UHS_VALUE) AS NET_UHS_VALUE,
               SUM(STOCK_VALUE) AS STOCK_VALUE
          FROM ${SCPA.MY_STORY_INVENTORY_V} T
          <where>
              <include refid="inv_filter"/>
          </where>
          GROUP BY T.${category}
        ), BASE2 AS (
            SELECT T."category",
                   <foreach collection="availableColumns" item="item" separator=",">
                       T."${item}"
                   </foreach>,
                   CASE WHEN T.STOCK_VALUE = 0 THEN 0 ELSE T.GROSS_UHS_VALUE / T.STOCK_VALUE * 100.0 END AS GROSS_PERCENT,
                   CASE WHEN T.STOCK_VALUE = 0 THEN 0 ELSE T.NET_UHS_VALUE / T.STOCK_VALUE * 100.0 END AS NET_PERCENT
              FROM BASE T
            UNION ALL
            SELECT 'Total',
                   <foreach collection="availableColumns" item="item" separator=",">
                       SUM(T."${item}")
                   </foreach>,
                   CASE WHEN SUM(T.STOCK_VALUE) = 0 THEN 0 ELSE SUM(T.GROSS_UHS_VALUE) / SUM(T.STOCK_VALUE) * 100.0 END AS GROSS_PERCENT,
                   CASE WHEN SUM(T.STOCK_VALUE) = 0 THEN 0 ELSE SUM(T.NET_UHS_VALUE) / SUM(T.STOCK_VALUE) * 100.0 END AS NET_PERCENT
              FROM BASE T
        )
        SELECT * FROM BASE2 T
         ORDER BY DECODE(T."category", 'Others', 'ZZZZ', 'Total', 'ZZZZZ', T."category")
        OFFSET 0 ROWS FETCH NEXT 128 ROWS ONLY
    </select>

    <select id="queryReport1Sub" resultType="java.util.Map">
        WITH BASE AS (
                SELECT NVL(T.${category}, 'Others') AS "category",
                   <foreach collection="availableColumns" item="item" separator=",">
                       NVL(SUM(T.${item}${suffix}), 0)     AS "${item}"
                   </foreach>,
                   SUM(GROSS_UHS_VALUE) AS GROSS_UHS_VALUE,
                   SUM(NET_UHS_VALUE) AS NET_UHS_VALUE,
                   SUM(STOCK_VALUE) AS STOCK_VALUE
              FROM ${SCPA.MY_STORY_INVENTORY_V} T
              <where>
                  <include refid="inv_filter"/>
                  AND T."${expandColumn}" = #{expandValue, jdbcType=VARCHAR}
                  <foreach collection="parent" item="item" index="index">
                     AND T."${report1Categories[index]}" = #{item, jdbcType=VARCHAR}
                  </foreach>
              </where>
              GROUP BY T.${category}
            ), BASE2 AS (
                SELECT T."category",
                       <foreach collection="availableColumns" item="item" separator=",">
                           T."${item}"
                       </foreach>,
                       CASE WHEN T.STOCK_VALUE = 0 THEN 0 ELSE T.GROSS_UHS_VALUE / T.STOCK_VALUE * 100.0 END AS GROSS_PERCENT,
                       CASE WHEN T.STOCK_VALUE = 0 THEN 0 ELSE T.NET_UHS_VALUE / T.STOCK_VALUE * 100.0 END AS NET_PERCENT
                  FROM BASE T
            )
            SELECT * FROM BASE2 T
             ORDER BY DECODE(T."category", 'Others', 'ZZZZ', 'Total', 'ZZZZZ', T."category")
            OFFSET 0 ROWS FETCH NEXT 128 ROWS ONLY
    </select>

    <select id="queryReport2" resultType="java.util.Map">
        WITH BASE AS (SELECT T.${report2Category} AS CATEGORY, ${report2StackBy} AS STACK_BY, ${report2SortBy}${suffix} AS VAL0
                      FROM ${SCPA.MY_STORY_INVENTORY_V} T
                     WHERE T.${report2Category} IS NOT NULL
                     <foreach collection="report1SelectedValues" item="item" index="index" separator="and" open="and">
                        T."${report1Categories[index]}" = #{item, jdbcType=VARCHAR}
                     </foreach>
                     <include refid="inv_filter"/>
        )
        SELECT CATEGORY, STACK_BY, SUM(VAL0) AS VAL
          FROM BASE
         GROUP BY CATEGORY, STACK_BY
         ORDER BY NVL(VAL, -1) DESC
         OFFSET ${report2TopStart} ROWS FETCH NEXT ${report2TopEnd} - ${report2TopStart} ROWS ONLY
    </select>

    <select id="queryReport3Inventory" resultType="java.util.Map">
        SELECT /*+ parallel(6) */
               T.DATE$               XAXIS,
               SUM(${valueColumn})   YAXIS
          FROM ${SCPA.INVENTORY_STRUCTURE_HIST} T
         WHERE T.DATE$ BETWEEN TO_DATE(#{report3DateRange[0],jdbcType=VARCHAR}, 'YYYY/MM/DD') AND TO_DATE(#{report3DateRange[1],jdbcType=VARCHAR}, 'YYYY/MM/DD')
         <if test="valueFilter.indexOf('SPECIAL_ST') == -1">
             AND (T.SPECIAL_ST IN ('W', '#', 'E', 'O', 'P', 'Q', 'V', 'M', 'Y', 'Others') OR T.SPECIAL_ST IS NULL)
         </if>
         <if test="valueFilter != null and valueFilter != ''.toString()">
            and ${valueFilter}
         </if>
         <foreach collection="report1SelectedValues" item="item" index="index" separator="and" open="and">
            T."${report1Categories[index]}" = #{item, jdbcType=VARCHAR}
         </foreach>
         <if test="_filters3 != null and _filters3 != ''.toString()">
            and ${_filters3}
         </if>
         <if test="_filters4 != null and _filters4 != ''.toString()">
            and ${_filters4}
         </if>
         GROUP BY T.DATE$
         ORDER BY XAXIS
    </select>

    <select id="queryReport3UHS" resultType="java.util.Map">
        WITH DATE_BASE AS (
            SELECT T.YEAR || T.WEEK_NO AS WEEK, MAX(T.DATE$) MAX_DATE FROM SY_CALENDAR T WHERE T.NAME = 'National Holidays'
            GROUP BY T.YEAR || T.WEEK_NO
        )
        SELECT /*+ parallel(6) */
               T.WEEK           XAXIS,
               ${valueColumn}   YAXIS
          FROM ${SCPA.UHS_RCA_HIST} T INNER JOIN DATE_BASE T2 ON T.WEEK = T2.WEEK
         WHERE T2.MAX_DATE BETWEEN TO_DATE(#{report3DateRange[0],jdbcType=VARCHAR}, 'YYYY/MM/DD') AND TO_DATE(#{report3DateRange[1],jdbcType=VARCHAR}, 'YYYY/MM/DD')
         <foreach collection="report1SelectedValues" item="item" index="index" separator="and" open="and">
            <choose>
                <when test="report1Categories[index] != '#NULL'.toString()">
                    T."${report1Categories[index]}" = #{item, jdbcType=VARCHAR}
                </when>
                <otherwise>
                    0 = 1
                </otherwise>
            </choose>
         </foreach>
         <if test="_filters5 != null and _filters5 != ''.toString()">
            and ${_filters5}
         </if>
         <if test="_filters6 != null and _filters6 != ''.toString()">
            and ${_filters6}
         </if>
         GROUP BY T.WEEK
         ORDER BY XAXIS
    </select>
</mapper>
