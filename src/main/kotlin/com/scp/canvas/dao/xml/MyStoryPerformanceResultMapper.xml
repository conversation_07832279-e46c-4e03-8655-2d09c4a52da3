<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.canvas.dao.IMyStoryPerformanceResultDao">
    <sql id="performance_result_filter">
        <if test="_filters != null and _filters != ''.toString()">
            and ${_filters}
        </if>
    </sql>

    <select id="queryReport1OTDS" resultType="com.scp.canvas.bean.MyStoryPerformanceResultBean">
        WITH OTDS_5DC AS (SELECT /*+ materialize */ TO_CHAR(T.SO04_CRD, 'YYYYMM') AS SO04_CRD_MONTH, T.ONTIME, T.${display} AS DISPLAY, SALES_ORDER_NUMBER, SALES_ORDER_ITEM
                          FROM ${SCPA.OTDS_SOURCE_WEEKLY_V} T
                          WHERE (TO_CHAR(T.SO04_CRD, 'YYYY') = #{year,jdbcType=VARCHAR} OR TO_CHAR(T.SO04_CRD, 'YYYYMM') = #{lastMonth,jdbcType=VARCHAR})
                            AND T.ENTITY NOT IN ('SSAM')
                          <if test="_otdsWeeklyFilters != null and _otdsWeeklyFilters != ''.toString()">
                                and ${_otdsWeeklyFilters}
                          </if>),
             OTDS_NON5DC AS (SELECT /*+ materialize parallel */ T.STANDARD_ONTIME, SUBSTR(T.STANDARD_FIRST_GI, 0, 6) AS STANDARD_FIRST_GI_MONTH, T.${display} AS DISPLAY
                             FROM ${SCPA.OTDS_SOURCE_STANDARD_V} T
                             WHERE T.PLANT_CODE IN ('AX01', 'AX02', 'B002', 'BG02', 'HKL', 'J001', 'K001', 'LH01', 'N002', 'R001', 'SP01', 'SP02', 'XA01', 'XM01')
                               AND T.ENTITY NOT IN ('SSAM')
                               AND (T.STANDARD_FIRST_GI LIKE #{lastMonth,jdbcType=VARCHAR} || '%' OR T.STANDARD_FIRST_GI LIKE #{year,jdbcType=VARCHAR} || '%')
                               AND T.DISTR_CH = 'OG'
                               AND T.STANDARD_OTD_EXCLUSION IS NULL
                               AND NOT EXISTS(SELECT 1 FROM OTDS_5DC T2 WHERE T.SALES_ORDER_NUMBER = T2.SALES_ORDER_NUMBER AND T.SALES_ORDER_ITEM = T2.SALES_ORDER_ITEM)
                               <if test="_otdsStandardFilters != null and _otdsStandardFilters != ''.toString()">
                                    and ${_otdsStandardFilters}
                               </if>),
             TEMP AS (SELECT SO04_CRD_MONTH, ONTIME, DISPLAY
                      FROM OTDS_5DC
                      UNION ALL
                      SELECT STANDARD_FIRST_GI_MONTH, STANDARD_ONTIME, DISPLAY
                      FROM OTDS_NON5DC),
             MTD AS (SELECT DISPLAY,
                            SUM(CASE WHEN T.ONTIME = '+' THEN 1 END) AS ONTIME,
                            COUNT(1)                                 AS TOTAL
                     FROM TEMP T
                     WHERE SO04_CRD_MONTH = #{month,jdbcType=VARCHAR}
                     GROUP BY DISPLAY
             ),
             YTD AS (SELECT DISPLAY,
                            SUM(CASE WHEN T.ONTIME = '+' THEN 1 END) AS ONTIME,
                            COUNT(1)                                 AS TOTAL
                     FROM TEMP T
                     WHERE SO04_CRD_MONTH BETWEEN #{year,jdbcType=VARCHAR} || '01' AND #{month,jdbcType=VARCHAR}
                     GROUP BY DISPLAY
             ),
             LMTD AS (SELECT DISPLAY,
                             SUM(CASE WHEN T.ONTIME = '+' THEN 1 END) AS ONTIME,
                             COUNT(1)                                 AS TOTAL
                      FROM TEMP T
                      WHERE SO04_CRD_MONTH = #{lastMonth,jdbcType=VARCHAR}
                      GROUP BY DISPLAY
             ),
             DETAILS_BASE AS (SELECT MTD.DISPLAY,
                                     ROUND(CASE WHEN MTD.TOTAL = 0 THEN 0 ELSE MTD.ONTIME / MTD.TOTAL * 100.0 END, 1)    AS MTD_OTDS,
                                     ROUND(CASE WHEN YTD.TOTAL = 0 THEN 0 ELSE YTD.ONTIME / YTD.TOTAL * 100.0 END, 1)    AS YTD_OTDS,
                                     ROUND(CASE WHEN LMTD.TOTAL = 0 THEN 0 ELSE LMTD.ONTIME / LMTD.TOTAL * 100.0 END, 1) AS LMTD_OTDS
                              FROM MTD
                                       LEFT JOIN YTD ON MTD.DISPLAY = YTD.DISPLAY
                                       LEFT JOIN LMTD ON MTD.DISPLAY = LMTD.DISPLAY),
             DETAILS AS (SELECT DB.DISPLAY                                                                                                  AS VALUE1,
                                DB.MTD_OTDS                                                                                                 AS VALUE2,
                                DB.YTD_OTDS                                                                                                 AS VALUE3,
                                CASE WHEN MTD_OTDS &gt; LMTD_OTDS THEN 'up' WHEN MTD_OTDS &lt; LMTD_OTDS THEN 'down' ELSE 'right' END       AS ARROW,
                                CASE WHEN MTD_OTDS &gt; LMTD_OTDS THEN 'green' WHEN MTD_OTDS &lt; LMTD_OTDS THEN 'red' ELSE 'yellow' END    AS ARROW_COLOR
                         FROM DETAILS_BASE DB),
             TOTAL_BASE AS (SELECT ROUND(CASE WHEN SUM(MTD.TOTAL) = 0 THEN 0 ELSE SUM(MTD.ONTIME) / SUM(MTD.TOTAL) * 100.0 END, 1)    AS MTD_OTDS,
                                   ROUND(CASE WHEN SUM(YTD.TOTAL) = 0 THEN 0 ELSE SUM(YTD.ONTIME) / SUM(YTD.TOTAL) * 100.0 END, 1)    AS YTD_OTDS,
                                   ROUND(CASE WHEN SUM(LMTD.TOTAL) = 0 THEN 0 ELSE SUM(LMTD.ONTIME) / SUM(LMTD.TOTAL) * 100.0 END, 1) AS LMTD_OTDS
                            FROM MTD
                                     LEFT JOIN YTD ON MTD.DISPLAY = YTD.DISPLAY
                                     LEFT JOIN LMTD ON MTD.DISPLAY = LMTD.DISPLAY),
             TOTAL AS (SELECT '>=97.5%'                                                                                                   AS VALUE1,
                              DB.MTD_OTDS                                                                                               AS VALUE2,
                              DB.YTD_OTDS                                                                                               AS VALUE3,
                              CASE WHEN MTD_OTDS &gt; LMTD_OTDS THEN 'up' WHEN MTD_OTDS &lt; LMTD_OTDS THEN 'down' ELSE 'right' END     AS ARROW,
                              CASE WHEN MTD_OTDS &gt; LMTD_OTDS THEN 'green' WHEN MTD_OTDS &lt; LMTD_OTDS THEN 'red' ELSE 'yellow' END  AS ARROW_COLOR
                       FROM TOTAL_BASE DB)
        SELECT T.VALUE1, VALUE2, VALUE3, ARROW, ARROW_COLOR, 0 AS ORD
        FROM TOTAL T
        WHERE T.VALUE1 IS NOT NULL
        UNION ALL
        SELECT T.VALUE1, VALUE2, VALUE3, ARROW, ARROW_COLOR, NULL
        FROM DETAILS T
        WHERE T.VALUE1 IS NOT NULL
        ORDER BY ORD, VALUE2
    </select>

    <select id="queryReport1OTDSFailLines" resultType="com.scp.canvas.bean.MyStoryPerformanceResultBean">
        WITH OTDS_5DC AS (SELECT /*+ materialize */ TO_CHAR(T.SO04_CRD, 'YYYYMM') AS SO04_CRD_MONTH, T.ONTIME, T.ENTITY AS DISPLAY, SALES_ORDER_NUMBER, SALES_ORDER_ITEM
                      FROM ${SCPA.OTDS_SOURCE_WEEKLY_V} T
                      WHERE (TO_CHAR(T.SO04_CRD, 'YYYY') = #{year,jdbcType=VARCHAR} OR TO_CHAR(T.SO04_CRD, 'YYYYMM') = #{lastMonth,jdbcType=VARCHAR})
                        AND T.ENTITY NOT IN ('SSAM')
                          <if test="_otdsWeeklyFilters != null and _otdsWeeklyFilters != ''.toString()">
                                AND ${_otdsWeeklyFilters}
                          </if>),
         OTDS_NON5DC AS (SELECT /*+ materialize parallel */ T.STANDARD_ONTIME, SUBSTR(T.STANDARD_FIRST_GI, 0, 6) AS STANDARD_FIRST_GI_MONTH, T.ENTITY AS DISPLAY
                         FROM ${SCPA.OTDS_SOURCE_STANDARD_V} T
                         WHERE T.PLANT_CODE IN ('AX01', 'AX02', 'B002', 'BG02', 'HKL', 'J001', 'K001', 'LH01', 'N002', 'R001', 'SP01', 'SP02', 'XA01', 'XM01')
                           AND T.ENTITY NOT IN ('SSAM')
                           AND (T.STANDARD_FIRST_GI LIKE #{lastMonth,jdbcType=VARCHAR} || '%' OR T.STANDARD_FIRST_GI LIKE #{year,jdbcType=VARCHAR} || '%')
                           AND T.DISTR_CH = 'OG'
                           AND T.STANDARD_OTD_EXCLUSION IS NULL
                           AND NOT EXISTS(SELECT 1 FROM OTDS_5DC T2 WHERE T.SALES_ORDER_NUMBER = T2.SALES_ORDER_NUMBER AND T.SALES_ORDER_ITEM = T2.SALES_ORDER_ITEM)
                           <if test="_otdsStandardFilters != null and _otdsStandardFilters != ''.toString()">
                                AND ${_otdsStandardFilters}
                           </if>),
         TEMP AS (SELECT SO04_CRD_MONTH, ONTIME, DISPLAY
                  FROM OTDS_5DC
                  UNION ALL
                  SELECT STANDARD_FIRST_GI_MONTH, STANDARD_ONTIME, DISPLAY
                  FROM OTDS_NON5DC),
         MTD AS (SELECT DISPLAY, SUM(CASE WHEN T.ONTIME = '#' THEN 1 END) AS FAIL
                 FROM TEMP T
                 WHERE SO04_CRD_MONTH = #{month,jdbcType=VARCHAR}
                 GROUP BY DISPLAY),
         YTD AS (SELECT DISPLAY, SUM(CASE WHEN T.ONTIME = '#' THEN 1 END) AS FAIL
                 FROM TEMP T
                 WHERE SO04_CRD_MONTH BETWEEN #{year,jdbcType=VARCHAR} || '01' AND #{month,jdbcType=VARCHAR}
                 GROUP BY DISPLAY),
         LMTD AS (SELECT DISPLAY, SUM(CASE WHEN T.ONTIME = '#' THEN 1 END) AS FAIL
                  FROM TEMP T
                  WHERE SO04_CRD_MONTH = #{lastMonth,jdbcType=VARCHAR}
                  GROUP BY DISPLAY),
         MTD_TOTAL AS (SELECT SUM(FAIL) FAIL_TOTAL FROM MTD),
         YTD_TOTAL AS (SELECT SUM(FAIL) FAIL_TOTAL FROM YTD),
         LMTD_TOTAL AS (SELECT SUM(FAIL) FAIL_TOTAL FROM LMTD),
         DETAILS_BASE AS (SELECT MTD.DISPLAY,
                                 ROUND(CASE WHEN MTD_TOTAL.FAIL_TOTAL = 0 THEN 0 ELSE MTD.FAIL / MTD_TOTAL.FAIL_TOTAL * 100.0 END, 1)    AS MTD_FAIL_PERCENT,
                                 ROUND(CASE WHEN YTD_TOTAL.FAIL_TOTAL = 0 THEN 0 ELSE YTD.FAIL / YTD_TOTAL.FAIL_TOTAL * 100.0 END, 1)    AS YTD_FAIL_PERCENT,
                                 ROUND(CASE WHEN LMTD_TOTAL.FAIL_TOTAL = 0 THEN 0 ELSE LMTD.FAIL / LMTD_TOTAL.FAIL_TOTAL * 100.0 END, 1) AS LMTD_FAIL_PERCENT
                          FROM MTD
                                   LEFT JOIN YTD ON MTD.DISPLAY = YTD.DISPLAY
                                   LEFT JOIN LMTD ON MTD.DISPLAY = LMTD.DISPLAY
                                   CROSS JOIN MTD_TOTAL
                                   CROSS JOIN YTD_TOTAL
                                   CROSS JOIN LMTD_TOTAL),
         DETAILS AS (SELECT DB.DISPLAY                                                                                                                              AS VALUE1,
                            NVL(DB.MTD_FAIL_PERCENT, 0)                                                                                                             AS VALUE2,
                            NVL(DB.YTD_FAIL_PERCENT, 0)                                                                                                             AS VALUE3,
                            CASE WHEN MTD_FAIL_PERCENT > LMTD_FAIL_PERCENT THEN 'up' WHEN MTD_FAIL_PERCENT &lt; LMTD_FAIL_PERCENT THEN 'down' ELSE 'right' END      AS ARROW,
                            CASE WHEN MTD_FAIL_PERCENT > LMTD_FAIL_PERCENT THEN 'red' WHEN MTD_FAIL_PERCENT &lt; LMTD_FAIL_PERCENT THEN 'green' ELSE 'yellow' END   AS ARROW_COLOR
                     FROM DETAILS_BASE DB),
         TOTAL_BASE AS (SELECT SUM(MTD.FAIL)  AS MTD_FAIL,
                               SUM(YTD.FAIL)  AS YTD_FAIL,
                               SUM(LMTD.FAIL) AS LMTD_FAIL
                        FROM MTD
                             LEFT JOIN YTD ON MTD.DISPLAY = YTD.DISPLAY
                             LEFT JOIN LMTD ON MTD.DISPLAY = LMTD.DISPLAY
                             CROSS JOIN MTD_TOTAL
                             CROSS JOIN YTD_TOTAL
                             CROSS JOIN LMTD_TOTAL),
         TOTAL AS (SELECT ' '                                                                                                   AS VALUE1,
                          DB.MTD_FAIL                                                                                           AS VALUE2,
                          DB.YTD_FAIL                                                                                           AS VALUE3,
                          CASE WHEN MTD_FAIL > LMTD_FAIL THEN 'up' WHEN MTD_FAIL &lt; LMTD_FAIL THEN 'down' ELSE 'right' END    AS ARROW,
                          CASE WHEN MTD_FAIL > LMTD_FAIL THEN 'red' WHEN MTD_FAIL &lt; LMTD_FAIL THEN 'green' ELSE 'yellow' END AS ARROW_COLOR
                   FROM TOTAL_BASE DB)
        SELECT T.VALUE1, VALUE2, VALUE3, ARROW, ARROW_COLOR, 0 AS ORD
        FROM TOTAL T
        WHERE T.VALUE1 IS NOT NULL
        UNION ALL
        SELECT T.VALUE1, VALUE2, VALUE3, ARROW, ARROW_COLOR, NULL
        FROM DETAILS T
        WHERE T.VALUE1 IS NOT NULL
        ORDER BY ORD, VALUE2 DESC
    </select>

    <select id="queryReport1OTC" resultType="com.scp.canvas.bean.MyStoryPerformanceResultBean">
        WITH OTC_TEMP AS (SELECT /*+ materialize */ TO_CHAR(TO_DATE(FIRST_CONFIRMED_GI, 'YYYY/MM/DD'), 'YYYYMM') AS FIRST_CONFIRMED_GI, T.OTC, T.${display} AS DISPLAY
        FROM ${SCPA.OPM_OTC_DATA_V} T
        WHERE (TO_CHAR(TO_DATE(FIRST_CONFIRMED_GI, 'YYYY/MM/DD'), 'YYYY') = #{year,jdbcType=VARCHAR} OR TO_CHAR(TO_DATE(FIRST_CONFIRMED_GI, 'YYYY/MM/DD'), 'YYYYMM') = #{lastMonth,jdbcType=VARCHAR})
          AND T.ENTITY NOT IN ('LHBJ', 'Uniflair', 'SSAM', 'SEEE', 'AVXE')
        <include refid="performance_result_filter"/>),
        MTD AS (SELECT DISPLAY,
        SUM(CASE WHEN T.OTC = 100 THEN 1 END) AS ONTIME,
        COUNT(1)                                 AS TOTAL
        FROM OTC_TEMP T
        WHERE FIRST_CONFIRMED_GI = #{month,jdbcType=VARCHAR}
        GROUP BY DISPLAY
        ),
        YTD AS (SELECT DISPLAY,
        SUM(CASE WHEN T.OTC = 100 THEN 1 END) AS ONTIME,
        COUNT(1)                                 AS TOTAL
        FROM OTC_TEMP T
        WHERE FIRST_CONFIRMED_GI BETWEEN #{year,jdbcType=VARCHAR} || '01' AND #{month,jdbcType=VARCHAR}
        GROUP BY DISPLAY
        ),
        LMTD AS (SELECT DISPLAY,
        SUM(CASE WHEN T.OTC = 100 THEN 1 END) AS ONTIME,
        COUNT(1)                                 AS TOTAL
        FROM OTC_TEMP T
        WHERE FIRST_CONFIRMED_GI = #{lastMonth,jdbcType=VARCHAR}
        GROUP BY DISPLAY
        ),
        DETAILS_BASE AS (SELECT MTD.DISPLAY,
        ROUND(CASE WHEN MTD.TOTAL = 0 THEN 0 ELSE MTD.ONTIME / MTD.TOTAL * 100.0 END, 1)    AS MTD_OTDS,
        ROUND(CASE WHEN YTD.TOTAL = 0 THEN 0 ELSE YTD.ONTIME / YTD.TOTAL * 100.0 END, 1)    AS YTD_OTDS,
        ROUND(CASE WHEN LMTD.TOTAL = 0 THEN 0 ELSE LMTD.ONTIME / LMTD.TOTAL * 100.0 END, 1) AS LMTD_OTDS
        FROM MTD
        LEFT JOIN YTD ON MTD.DISPLAY = YTD.DISPLAY
        LEFT JOIN LMTD ON MTD.DISPLAY = LMTD.DISPLAY),
        DETAILS AS (SELECT DB.DISPLAY                                                                                                  AS VALUE1,
        DB.MTD_OTDS                                                                                                 AS VALUE2,
        DB.YTD_OTDS                                                                                                 AS VALUE3,
        CASE WHEN MTD_OTDS &gt; LMTD_OTDS THEN 'up' WHEN MTD_OTDS &lt; LMTD_OTDS THEN 'down' ELSE 'right' END       AS ARROW,
        CASE WHEN MTD_OTDS &gt; LMTD_OTDS THEN 'green' WHEN MTD_OTDS &lt; LMTD_OTDS THEN 'red' ELSE 'yellow' END    AS ARROW_COLOR
        FROM DETAILS_BASE DB),
        TOTAL_BASE AS (SELECT ROUND(CASE WHEN SUM(MTD.TOTAL) = 0 THEN 0 ELSE SUM(MTD.ONTIME) / SUM(MTD.TOTAL) * 100.0 END, 1)    AS MTD_OTDS,
        ROUND(CASE WHEN SUM(YTD.TOTAL) = 0 THEN 0 ELSE SUM(YTD.ONTIME) / SUM(YTD.TOTAL) * 100.0 END, 1)    AS YTD_OTDS,
        ROUND(CASE WHEN SUM(LMTD.TOTAL) = 0 THEN 0 ELSE SUM(LMTD.ONTIME) / SUM(LMTD.TOTAL) * 100.0 END, 1) AS LMTD_OTDS
        FROM MTD
        LEFT JOIN YTD ON MTD.DISPLAY = YTD.DISPLAY
        LEFT JOIN LMTD ON MTD.DISPLAY = LMTD.DISPLAY),
        TOTAL AS (SELECT '>=97.7%'                                                                                                   AS VALUE1,
        DB.MTD_OTDS                                                                                               AS VALUE2,
        DB.YTD_OTDS                                                                                               AS VALUE3,
        CASE WHEN MTD_OTDS &gt; LMTD_OTDS THEN 'up' WHEN MTD_OTDS &lt; LMTD_OTDS THEN 'down' ELSE 'right' END     AS ARROW,
        CASE WHEN MTD_OTDS &gt; LMTD_OTDS THEN 'green' WHEN MTD_OTDS &lt; LMTD_OTDS THEN 'red' ELSE 'yellow' END  AS ARROW_COLOR
        FROM TOTAL_BASE DB)
        SELECT T.VALUE1, VALUE2, VALUE3, ARROW, ARROW_COLOR, 0 AS ORD
        FROM TOTAL T
        WHERE T.VALUE1 IS NOT NULL
        UNION ALL
        SELECT T.VALUE1, VALUE2, VALUE3, ARROW, ARROW_COLOR, NULL
        FROM DETAILS T
        WHERE T.VALUE1 IS NOT NULL
        ORDER BY ORD, VALUE2
    </select>

    <select id="queryReport1ByManualInput" resultType="com.scp.canvas.bean.MyStoryPerformanceResultBean">
        SELECT MTD AS VALUE2,
               YTD AS VALUE3,
               CASE
                   WHEN MTD &gt; MTD_LAST_MONTH THEN 'up'
                   WHEN MTD &lt; MTD_LAST_MONTH THEN 'down'
                   ELSE 'right' END AS ARROW,
               CASE
                   WHEN MTD &gt; MTD_LAST_MONTH THEN 'green'
                   WHEN MTD &lt; MTD_LAST_MONTH THEN 'red'
                   ELSE 'yellow' END AS ARROW_COLOR
        FROM MR3_MY_STORY_PERFORMANCE T
        WHERE T.MONTH = #{month,jdbcType=VARCHAR} AND T.CATEGORY = #{category, jdbcType=VARCHAR}
    </select>

    <select id="queryReport1OTDM" resultType="com.scp.canvas.bean.MyStoryPerformanceResultBean">
        WITH BASE AS (SELECT OTDM_DELAY_WITHIN_5WD, CALENDAR_NEXT_5WD_MONTH, T.${display} AS DISPLAY
                      FROM ${SCPA.OTDM_V} T
                      WHERE T.SHIP_TO_REGION NOT IN ('China', 'CHINA')
                        AND T.SHIP_TO_REGION IS NOT NULL
                        AND T.PLANT_CODE NOT IN ('J001', 'XA01', 'AX01', 'AX02', 'LH01', 'K001', 'K003', 'B002', 'R001')
                        AND (
                                CALENDAR_NEXT_5WD_YEAR = #{year,jdbcType=VARCHAR}
                                OR
                                CALENDAR_NEXT_5WD_MONTH = #{lastMonth,jdbcType=VARCHAR}
                        )
                        <include refid="performance_result_filter"/>
             ),
             YTD AS (SELECT count(decode(t.OTDM_DELAY_WITHIN_5WD, 'ON_TIME', 1, NULL)) AS ONTIME,
                            count(CASE WHEN t.OTDM_DELAY_WITHIN_5WD IN ('ON_TIME', 'DELAY') THEN 1 END) AS TOTAL,
                            T.DISPLAY
                     FROM BASE t
                     WHERE t.CALENDAR_NEXT_5WD_MONTH BETWEEN #{year,jdbcType=VARCHAR} || '01' AND #{month,jdbcType=VARCHAR}
                     GROUP BY T.DISPLAY
             ),
             MTD AS (SELECT count(decode(t.OTDM_DELAY_WITHIN_5WD, 'ON_TIME', 1, NULL)) AS ONTIME,
                            count(CASE WHEN t.OTDM_DELAY_WITHIN_5WD IN ('ON_TIME', 'DELAY') THEN 1 END) AS TOTAL,
                            T.DISPLAY
                     FROM BASE t
                     WHERE t.CALENDAR_NEXT_5WD_MONTH = #{month,jdbcType=VARCHAR}
                     GROUP BY T.DISPLAY
             ),
             LMTD AS (SELECT count(decode(t.OTDM_DELAY_WITHIN_5WD, 'ON_TIME', 1, NULL)) AS ONTIME,
                             count(CASE WHEN t.OTDM_DELAY_WITHIN_5WD IN ('ON_TIME', 'DELAY') THEN 1 END) AS TOTAL,
                             T.DISPLAY
                      FROM BASE t
                      WHERE t.CALENDAR_NEXT_5WD_MONTH = #{lastMonth,jdbcType=VARCHAR}
                      GROUP BY T.DISPLAY
             ),
             DETAILS_BASE AS (SELECT MTD.DISPLAY,
                             ROUND(CASE WHEN MTD.TOTAL = 0 THEN 0 ELSE MTD.ONTIME / MTD.TOTAL * 100.0 END, 1)    AS MTD_OTDM,
                             ROUND(CASE WHEN YTD.TOTAL = 0 THEN 0 ELSE YTD.ONTIME / YTD.TOTAL * 100.0 END, 1)    AS YTD_OTDM,
                             ROUND(CASE WHEN LMTD.TOTAL = 0 THEN 0 ELSE LMTD.ONTIME / LMTD.TOTAL * 100.0 END, 1) AS LMTD_OTDM
                      FROM MTD
                               LEFT JOIN YTD ON MTD.DISPLAY = YTD.DISPLAY
                               LEFT JOIN LMTD ON MTD.DISPLAY = LMTD.DISPLAY),
             DETAILS AS (SELECT DB.DISPLAY                                                                                                  AS VALUE1,
                                DB.MTD_OTDM                                                                                                 AS VALUE2,
                                DB.YTD_OTDM                                                                                                 AS VALUE3,
                                CASE WHEN MTD_OTDM &gt; LMTD_OTDM THEN 'up' WHEN MTD_OTDM &lt; LMTD_OTDM THEN 'down' ELSE 'right' END       AS ARROW,
                                CASE WHEN MTD_OTDM &gt; LMTD_OTDM THEN 'green' WHEN MTD_OTDM &lt; LMTD_OTDM THEN 'red' ELSE 'yellow' END    AS ARROW_COLOR
                         FROM DETAILS_BASE DB),
             TOTAL_BASE AS (SELECT ROUND(CASE WHEN SUM(MTD.TOTAL) = 0 THEN 0 ELSE SUM(MTD.ONTIME) / SUM(MTD.TOTAL) * 100.0 END, 1)    AS MTD_OTDM,
                                   ROUND(CASE WHEN SUM(YTD.TOTAL) = 0 THEN 0 ELSE SUM(YTD.ONTIME) / SUM(YTD.TOTAL) * 100.0 END, 1)    AS YTD_OTDM,
                                   ROUND(CASE WHEN SUM(LMTD.TOTAL) = 0 THEN 0 ELSE SUM(LMTD.ONTIME) / SUM(LMTD.TOTAL) * 100.0 END, 1) AS LMTD_OTDM
                            FROM MTD
                                     LEFT JOIN YTD ON MTD.DISPLAY = YTD.DISPLAY
                                     LEFT JOIN LMTD ON MTD.DISPLAY = LMTD.DISPLAY),
             TOTAL AS (SELECT '>=90%'                                                                                                   AS VALUE1,
                              DB.MTD_OTDM                                                                                               AS VALUE2,
                              DB.YTD_OTDM                                                                                               AS VALUE3,
                              CASE WHEN MTD_OTDM &gt; LMTD_OTDM THEN 'up' WHEN MTD_OTDM &lt; LMTD_OTDM THEN 'down' ELSE 'right' END     AS ARROW,
                              CASE WHEN MTD_OTDM &gt; LMTD_OTDM THEN 'green' WHEN MTD_OTDM &lt; LMTD_OTDM THEN 'red' ELSE 'yellow' END  AS ARROW_COLOR
                       FROM TOTAL_BASE DB)
        SELECT T.VALUE1, VALUE2, VALUE3, ARROW, ARROW_COLOR, 0 AS ORD
        FROM TOTAL T
        WHERE T.VALUE1 IS NOT NULL
        UNION ALL
        SELECT T.VALUE1, VALUE2, VALUE3, ARROW, ARROW_COLOR, NULL
        FROM DETAILS T
        WHERE T.VALUE1 IS NOT NULL
        ORDER BY ORD, VALUE2
    </select>

    <select id="queryReport1OTDMFailLines" resultType="com.scp.canvas.bean.MyStoryPerformanceResultBean">
        WITH BASE AS (SELECT OTDM_DELAY_WITHIN_5WD, CALENDAR_NEXT_5WD_MONTH, T.ENTITY AS DISPLAY
                      FROM ${SCPA.OTDM_V} T
                      WHERE T.SHIP_TO_REGION NOT IN ('China', 'CHINA')
                        AND T.SHIP_TO_REGION IS NOT NULL
                        AND T.PLANT_CODE NOT IN ('J001', 'XA01', 'AX01', 'AX02', 'LH01', 'K001', 'K003', 'B002', 'R001')
                        AND (
                                CALENDAR_NEXT_5WD_YEAR = #{year,jdbcType=VARCHAR}
                                OR
                                CALENDAR_NEXT_5WD_MONTH = #{lastMonth,jdbcType=VARCHAR}
                        )
                        <include refid="performance_result_filter"/>
             ),
             YTD AS (SELECT count(decode(t.OTDM_DELAY_WITHIN_5WD, 'DELAY', 1, NULL)) AS FAIL, T.DISPLAY
                     FROM BASE t
                     WHERE t.CALENDAR_NEXT_5WD_MONTH BETWEEN #{year,jdbcType=VARCHAR} || '01' AND #{month,jdbcType=VARCHAR}
                     GROUP BY T.DISPLAY),
             MTD AS (SELECT count(decode(t.OTDM_DELAY_WITHIN_5WD, 'DELAY', 1, NULL)) AS FAIL, T.DISPLAY
                     FROM BASE t
                     WHERE t.CALENDAR_NEXT_5WD_MONTH = #{month,jdbcType=VARCHAR}
                     GROUP BY T.DISPLAY),
             LMTD AS (SELECT count(decode(t.OTDM_DELAY_WITHIN_5WD, 'DELAY', 1, NULL)) AS FAIL, T.DISPLAY
                      FROM BASE t
                      WHERE t.CALENDAR_NEXT_5WD_MONTH = #{lastMonth,jdbcType=VARCHAR}
                      GROUP BY T.DISPLAY),
             MTD_TOTAL AS (SELECT SUM(FAIL) FAIL_TOTAL FROM MTD),
             YTD_TOTAL AS (SELECT SUM(FAIL) FAIL_TOTAL FROM YTD),
             LMTD_TOTAL AS (SELECT SUM(FAIL) FAIL_TOTAL FROM LMTD),
             DETAILS_BASE AS (SELECT MTD.DISPLAY,
                                     ROUND(CASE WHEN MTD_TOTAL.FAIL_TOTAL = 0 THEN 0 ELSE MTD.FAIL / MTD_TOTAL.FAIL_TOTAL * 100.0 END, 1)    AS MTD_FAIL_PERCENT,
                                     ROUND(CASE WHEN YTD_TOTAL.FAIL_TOTAL = 0 THEN 0 ELSE YTD.FAIL / YTD_TOTAL.FAIL_TOTAL * 100.0 END, 1)    AS YTD_FAIL_PERCENT,
                                     ROUND(CASE WHEN LMTD_TOTAL.FAIL_TOTAL = 0 THEN 0 ELSE LMTD.FAIL / LMTD_TOTAL.FAIL_TOTAL * 100.0 END, 1) AS LMTD_FAIL_PERCENT
                              FROM MTD
                                   LEFT JOIN YTD ON MTD.DISPLAY = YTD.DISPLAY
                                   LEFT JOIN LMTD ON MTD.DISPLAY = LMTD.DISPLAY
                                   CROSS JOIN MTD_TOTAL
                                   CROSS JOIN YTD_TOTAL
                                   CROSS JOIN LMTD_TOTAL),
             DETAILS AS (SELECT DB.DISPLAY                                                                                                                         AS VALUE1,
                                NVL(DB.MTD_FAIL_PERCENT, 0)                                                                                                        AS VALUE2,
                                NVL(DB.YTD_FAIL_PERCENT, 0)                                                                                                        AS VALUE3,
                                CASE WHEN MTD_FAIL_PERCENT > LMTD_FAIL_PERCENT THEN 'up' WHEN MTD_FAIL_PERCENT &lt; LMTD_FAIL_PERCENT THEN 'down' ELSE 'right' END    AS ARROW,
                                CASE WHEN MTD_FAIL_PERCENT > LMTD_FAIL_PERCENT THEN 'red' WHEN MTD_FAIL_PERCENT &lt; LMTD_FAIL_PERCENT THEN 'green' ELSE 'yellow' END AS ARROW_COLOR
                         FROM DETAILS_BASE DB),
             TOTAL_BASE AS (SELECT SUM(MTD.FAIL)  AS MTD_FAIL,
                                   SUM(YTD.FAIL)  AS YTD_FAIL,
                                   SUM(LMTD.FAIL) AS LMTD_FAIL
                            FROM MTD
                                     LEFT JOIN YTD ON MTD.DISPLAY = YTD.DISPLAY
                                     LEFT JOIN LMTD ON MTD.DISPLAY = LMTD.DISPLAY
                                     CROSS JOIN MTD_TOTAL
                                     CROSS JOIN YTD_TOTAL
                                     CROSS JOIN LMTD_TOTAL),
             TOTAL AS (SELECT ' '                                                                                                   AS VALUE1,
                              DB.MTD_FAIL                                                                                           AS VALUE2,
                              DB.YTD_FAIL                                                                                           AS VALUE3,
                              CASE WHEN MTD_FAIL > LMTD_FAIL THEN 'up' WHEN MTD_FAIL &lt; LMTD_FAIL THEN 'down' ELSE 'right' END    AS ARROW,
                              CASE WHEN MTD_FAIL > LMTD_FAIL THEN 'red' WHEN MTD_FAIL &lt; LMTD_FAIL THEN 'green' ELSE 'yellow' END AS ARROW_COLOR
                       FROM TOTAL_BASE DB)
        SELECT T.VALUE1, VALUE2, VALUE3, ARROW, ARROW_COLOR, 0 AS ORD
        FROM TOTAL T
        WHERE T.VALUE1 IS NOT NULL
        UNION ALL
        SELECT T.VALUE1, VALUE2, VALUE3, ARROW, ARROW_COLOR, NULL
        FROM DETAILS T
        WHERE T.VALUE1 IS NOT NULL
        ORDER BY ORD, VALUE2 DESC
    </select>

    <select id="queryReport1PONORO" resultType="com.scp.canvas.bean.MyStoryPerformanceResultBean">
        WITH TEMP AS (SELECT NORO_FLAG, CALENDAR_MONTH, T.${display} AS DISPLAY
                      FROM ${SCPA.COMPLETED_PO_NOR_V} T
                      WHERE T.MRP_CONTROLLER NOT IN ('VIZ', 'VLZ', 'VHZ', 'VDZ')
                        AND T.PLANT_CODE IN ('I001', 'I003', 'M001', 'N001', 'O001')
                        AND T.ENTITY NOT IN ('SFCL','SITX','LHBJ','Uniflair','SSAM','SSPA','SEEE','SBG','SWEEC','Trading_DCHK','Trading_SPC','SST','AVXE','SSBEA','AVXP','SBMV')
                        AND T.NORO_FLAG IN ('EROC=2~3', 'EROC=4~7', 'EROC=8~14', 'EROC>14', 'ON TARGET')
                        AND (T.CALENDAR_MONTH = #{lastMonth,jdbcType=VARCHAR} OR T.CALENDAR_YEAR = #{year,jdbcType=VARCHAR})
                        <include refid="performance_result_filter"/>
             ),
             MTD AS (SELECT COUNT(DECODE(T.NORO_FLAG, 'Others', NULL, 1)) AS TOTAL,
                            COUNT(DECODE(T.NORO_FLAG, 'EROC=2~3', 1, NULL)) +
                            COUNT(DECODE(T.NORO_FLAG, 'EROC=4~7', 1, NULL)) +
                            COUNT(DECODE(T.NORO_FLAG, 'EROC=8~14', 1, NULL)) +
                            COUNT(DECODE(T.NORO_FLAG, 'EROC>14', 1, NULL)) AS FAIL,
                            DISPLAY
                     FROM TEMP T
                     WHERE t.CALENDAR_MONTH = #{month,jdbcType=VARCHAR}
                     GROUP BY DISPLAY
             ),
             YTD AS (SELECT COUNT(DECODE(T.NORO_FLAG, 'Others', NULL, 1)) AS TOTAL,
                            COUNT(DECODE(T.NORO_FLAG, 'EROC=2~3', 1, NULL)) +
                            COUNT(DECODE(T.NORO_FLAG, 'EROC=4~7', 1, NULL)) +
                            COUNT(DECODE(T.NORO_FLAG, 'EROC=8~14', 1, NULL)) +
                            COUNT(DECODE(T.NORO_FLAG, 'EROC>14', 1, NULL)) AS FAIL,
                            DISPLAY
                     FROM TEMP T
                     WHERE t.CALENDAR_MONTH BETWEEN #{year,jdbcType=VARCHAR} || '01' AND #{month,jdbcType=VARCHAR}
                     GROUP BY DISPLAY
             ),
             LMTD AS (SELECT COUNT(DECODE(T.NORO_FLAG, 'Others', NULL, 1)) AS TOTAL,
                             COUNT(DECODE(T.NORO_FLAG, 'EROC=2~3', 1, NULL)) +
                             COUNT(DECODE(T.NORO_FLAG, 'EROC=4~7', 1, NULL)) +
                             COUNT(DECODE(T.NORO_FLAG, 'EROC=8~14', 1, NULL)) +
                             COUNT(DECODE(T.NORO_FLAG, 'EROC>14', 1, NULL)) AS FAIL,
                             DISPLAY
                      FROM TEMP T
                      WHERE t.CALENDAR_MONTH = #{lastMonth,jdbcType=VARCHAR}
                      GROUP BY DISPLAY
             ),
             DETAILS_BASE AS (SELECT MTD.DISPLAY,
                             ROUND(CASE WHEN MTD.TOTAL = 0 THEN 0 ELSE MTD.FAIL / MTD.TOTAL * 100.0 END, 1)    AS MTD_PONORO,
                             ROUND(CASE WHEN YTD.TOTAL = 0 THEN 0 ELSE YTD.FAIL / YTD.TOTAL * 100.0 END, 1)    AS YTD_PONORO,
                             ROUND(CASE WHEN LMTD.TOTAL = 0 THEN 0 ELSE LMTD.FAIL / LMTD.TOTAL * 100.0 END, 1) AS LMTD_PONORO
                      FROM MTD
                               LEFT JOIN YTD ON MTD.DISPLAY = YTD.DISPLAY
                               LEFT JOIN LMTD ON MTD.DISPLAY = LMTD.DISPLAY),
             DETAILS AS (SELECT DB.DISPLAY                                                                                                          AS VALUE1,
                                DB.MTD_PONORO                                                                                                       AS VALUE2,
                                DB.YTD_PONORO                                                                                                       AS VALUE3,
                                CASE WHEN MTD_PONORO &gt; LMTD_PONORO THEN 'up' WHEN MTD_PONORO &lt; LMTD_PONORO THEN 'down' ELSE 'right' END       AS ARROW,
                                CASE WHEN MTD_PONORO &gt; LMTD_PONORO THEN 'red' WHEN MTD_PONORO &lt; LMTD_PONORO THEN 'green' ELSE 'yellow' END    AS ARROW_COLOR
                         FROM DETAILS_BASE DB),
             TOTAL_BASE AS (SELECT ROUND(CASE WHEN SUM(MTD.TOTAL) = 0 THEN 0 ELSE SUM(MTD.FAIL) / SUM(MTD.TOTAL) * 100.0 END, 1)    AS MTD_PONORO,
                                   ROUND(CASE WHEN SUM(YTD.TOTAL) = 0 THEN 0 ELSE SUM(YTD.FAIL) / SUM(YTD.TOTAL) * 100.0 END, 1)    AS YTD_PONORO,
                                   ROUND(CASE WHEN SUM(LMTD.TOTAL) = 0 THEN 0 ELSE SUM(LMTD.FAIL) / SUM(LMTD.TOTAL) * 100.0 END, 1) AS LMTD_PONORO
                            FROM MTD
                                     LEFT JOIN YTD ON MTD.DISPLAY = YTD.DISPLAY
                                     LEFT JOIN LMTD ON MTD.DISPLAY = LMTD.DISPLAY),
             TOTAL AS (SELECT '&lt;5%'                                                                                                          AS VALUE1,
                              DB.MTD_PONORO                                                                                                     AS VALUE2,
                              DB.YTD_PONORO                                                                                                     AS VALUE3,
                              CASE WHEN MTD_PONORO &gt; LMTD_PONORO THEN 'up' WHEN MTD_PONORO &lt; LMTD_PONORO THEN 'down' ELSE 'right' END     AS ARROW,
                              CASE WHEN MTD_PONORO &gt; LMTD_PONORO THEN 'red' WHEN MTD_PONORO &lt; LMTD_PONORO THEN 'green' ELSE 'yellow' END  AS ARROW_COLOR
                       FROM TOTAL_BASE DB)
        SELECT T.VALUE1, VALUE2, VALUE3, ARROW, ARROW_COLOR, 0 AS ORD
        FROM TOTAL T
        WHERE T.VALUE1 IS NOT NULL
        UNION ALL
        SELECT T.VALUE1, VALUE2, VALUE3, ARROW, ARROW_COLOR, NULL
        FROM DETAILS T
        WHERE T.VALUE1 IS NOT NULL
        ORDER BY ORD, VALUE2
    </select>

    <select id="queryReport1SONOR" resultType="com.scp.canvas.bean.MyStoryPerformanceResultBean">
        WITH TEMP AS (SELECT RESCH_COUNTER, GI_MONTH, T.${display} AS DISPLAY
        FROM ${SCPA.NOR_V} T
        WHERE
        (T.GI_MONTH = #{lastMonth,jdbcType=VARCHAR} OR SUBSTR(T.GI_MONTH, 1, 4) = #{year,jdbcType=VARCHAR})
        AND T.ENTITY NOT IN ('SITX','LHBJ','Uniflair','SSAM','SSPA','SEEE','SBG','SWEEC','SST','AVXE','SSBEA','AVXP','SBMV')
        <include refid="performance_result_filter"/>
        ),
        MTD AS (SELECT NVL(AVG(CASE WHEN T.RESCH_COUNTER > 0 THEN T.RESCH_COUNTER END), 0) AS RC_AVERAGE,
        DISPLAY
        FROM TEMP T
        WHERE t.GI_MONTH = #{month,jdbcType=VARCHAR}
        GROUP BY DISPLAY
        ),
        YTD AS (SELECT NVL(AVG(CASE WHEN T.RESCH_COUNTER > 0 THEN T.RESCH_COUNTER END), 0) AS RC_AVERAGE,
        DISPLAY
        FROM TEMP T
        WHERE t.GI_MONTH BETWEEN #{year,jdbcType=VARCHAR} || '01' AND #{month,jdbcType=VARCHAR}
        GROUP BY DISPLAY
        ),
        LMTD AS (SELECT NVL(AVG(CASE WHEN T.RESCH_COUNTER > 0 THEN T.RESCH_COUNTER END), 0) AS RC_AVERAGE,
        DISPLAY
        FROM TEMP T
        WHERE t.GI_MONTH = #{lastMonth,jdbcType=VARCHAR}
        GROUP BY DISPLAY
        ),
        TMTD AS (SELECT NVL(AVG(CASE WHEN T.RESCH_COUNTER > 0 THEN T.RESCH_COUNTER END), 0) AS RC_AVERAGE,'TOTAL' AS TOTAL
        FROM TEMP T
        WHERE t.GI_MONTH = #{month,jdbcType=VARCHAR}
        ),
        TYTD AS (SELECT NVL(AVG(CASE WHEN T.RESCH_COUNTER > 0 THEN T.RESCH_COUNTER END), 0) AS RC_AVERAGE,'TOTAL' AS TOTAL
        FROM TEMP T
        WHERE t.GI_MONTH BETWEEN #{year,jdbcType=VARCHAR} || '01' AND #{month,jdbcType=VARCHAR}
        ),
        TLMTD AS (SELECT NVL(AVG(CASE WHEN T.RESCH_COUNTER > 0 THEN T.RESCH_COUNTER END), 0) AS RC_AVERAGE,'TOTAL' AS TOTAL
        FROM TEMP T
        WHERE t.GI_MONTH = #{lastMonth,jdbcType=VARCHAR}
        ),
        DETAILS_BASE AS (SELECT MTD.DISPLAY,
                                MTD.RC_AVERAGE AS MTD_SONOR,
                                YTD.RC_AVERAGE AS YTD_SONOR,
                                LMTD.RC_AVERAGE AS LMTD_SONOR
                         FROM MTD
                        LEFT JOIN YTD ON MTD.DISPLAY = YTD.DISPLAY
                        LEFT JOIN LMTD ON MTD.DISPLAY = LMTD.DISPLAY),
        DETAILS AS (SELECT DB.DISPLAY            AS VALUE1,
                        ROUND(DB.MTD_SONOR,2)         AS VALUE2,
                        ROUND(DB.YTD_SONOR,2)         AS VALUE3,
                        CASE
                        WHEN MTD_SONOR &gt;LMTD_SONOR THEN 'up'
                        WHEN MTD_SONOR &lt; LMTD_SONOR THEN 'down'
                        ELSE 'right' END  AS ARROW,
                        CASE
                        WHEN MTD_SONOR &gt; LMTD_SONOR THEN 'red'
                        WHEN MTD_SONOR &lt; LMTD_SONOR THEN 'green'
                        ELSE 'yellow' END AS ARROW_COLOR
                        FROM DETAILS_BASE DB),
        TOTAL_BASE AS (SELECT TMTD.RC_AVERAGE AS MTD_SONOR,
                              TYTD.RC_AVERAGE AS YTD_SONOR,
                              TLMTD.RC_AVERAGE AS LMTD_SONOR
                       FROM TMTD
                        LEFT JOIN TYTD ON TMTD.TOTAL = TYTD.TOTAL
                        LEFT JOIN TLMTD ON TMTD.TOTAL = TLMTD.TOTAL),
        TOTAL AS (SELECT '&lt;2'                  AS VALUE1,
        ROUND(DB.MTD_SONOR,2)         AS VALUE2,
        ROUND(DB.YTD_SONOR,2)         AS VALUE3,
        CASE
        WHEN MTD_SONOR &gt; LMTD_SONOR THEN 'up'
        WHEN MTD_SONOR &lt; LMTD_SONOR THEN 'down'
        ELSE 'right' END  AS ARROW,
        CASE
        WHEN MTD_SONOR &gt; LMTD_SONOR THEN 'red'
        WHEN MTD_SONOR &lt; LMTD_SONOR THEN 'green'
        ELSE 'yellow' END AS ARROW_COLOR
        FROM TOTAL_BASE DB)
        SELECT T.VALUE1, VALUE2, VALUE3, ARROW, ARROW_COLOR, 0 AS ORD
        FROM TOTAL T
        WHERE T.VALUE1 IS NOT NULL
        UNION ALL
        SELECT T.VALUE1, VALUE2, VALUE3, ARROW, ARROW_COLOR, NULL
        FROM DETAILS T
        WHERE T.VALUE1 IS NOT NULL
        ORDER BY ORD, VALUE2
    </select>

    <select id="queryReport1MyCP" resultType="com.scp.canvas.bean.MyStoryPerformanceResultBean">
        WITH TEMP AS (SELECT T.CALENDAR_MONTH,
                             DECODE(MYCP_ONTIME_STATUS, 'Ontime', 1, 0) AS ONTIME,
                             DECODE(MYCP_ONTIME_STATUS, 'Fail', 1, 0)   AS FAIL,
                             T.${display} AS DISPLAY
                      FROM ${SCPA.MYCP_COMMITMENT_V} T
                      WHERE (T.CALENDAR_MONTH = #{lastMonth,jdbcType=VARCHAR} OR T.CALENDAR_YEAR = #{year,jdbcType=VARCHAR})
                        AND T.ENTITY NOT IN ('SITX','LHBJ','Uniflair','SSAM','SEEE','SBG','Trading_DCHK','Trading_SPC','SST','AVXE','SSBEA','AVXP','SBMV')
                         <include refid="performance_result_filter"/>),
             MTD AS (SELECT SUM(ONTIME) + SUM(FAIL) AS TOTAL,
                            SUM(ONTIME)             AS ONTIME,
                            DISPLAY
                     FROM TEMP T
                     WHERE T.CALENDAR_MONTH = #{month,jdbcType=VARCHAR}
                     GROUP BY DISPLAY),
             YTD AS (SELECT SUM(ONTIME) + SUM(FAIL) AS TOTAL,
                            SUM(ONTIME)             AS ONTIME,
                            DISPLAY
                     FROM TEMP T
                     WHERE T.CALENDAR_MONTH BETWEEN #{year,jdbcType=VARCHAR} || '01' AND #{month,jdbcType=VARCHAR}
                     GROUP BY DISPLAY),
             LMTD AS (SELECT SUM(ONTIME) + SUM(FAIL) AS TOTAL,
                             SUM(ONTIME)             AS ONTIME,
                             DISPLAY
                      FROM TEMP T
                      WHERE T.CALENDAR_MONTH = #{lastMonth,jdbcType=VARCHAR}
                      GROUP BY DISPLAY),
             DETAILS_BASE AS (SELECT MTD.DISPLAY,
                             ROUND(CASE WHEN MTD.TOTAL = 0 THEN 0 ELSE MTD.ONTIME / MTD.TOTAL * 100.0 END, 1)    AS MTD_MYCP,
                             ROUND(CASE WHEN YTD.TOTAL = 0 THEN 0 ELSE YTD.ONTIME / YTD.TOTAL * 100.0 END, 1)    AS YTD_MYCP,
                             ROUND(CASE WHEN LMTD.TOTAL = 0 THEN 0 ELSE LMTD.ONTIME / LMTD.TOTAL * 100.0 END, 1) AS LMTD_MYCP
                      FROM MTD
                               LEFT JOIN YTD ON MTD.DISPLAY = YTD.DISPLAY
                               LEFT JOIN LMTD ON MTD.DISPLAY = LMTD.DISPLAY),
             DETAILS AS (SELECT DB.DISPLAY                                                                                                  AS VALUE1,
                                DB.MTD_MYCP                                                                                                 AS VALUE2,
                                DB.YTD_MYCP                                                                                                 AS VALUE3,
                                CASE WHEN MTD_MYCP &gt; LMTD_MYCP THEN 'up' WHEN MTD_MYCP &lt; LMTD_MYCP THEN 'down' ELSE 'right' END       AS ARROW,
                                CASE WHEN MTD_MYCP &gt; LMTD_MYCP THEN 'green' WHEN MTD_MYCP &lt; LMTD_MYCP THEN 'red' ELSE 'yellow' END    AS ARROW_COLOR
                         FROM DETAILS_BASE DB),
             TOTAL_BASE AS (SELECT ROUND(CASE WHEN SUM(MTD.TOTAL) = 0 THEN 0 ELSE SUM(MTD.ONTIME) / SUM(MTD.TOTAL) * 100.0 END, 1)    AS MTD_MYCP,
                                   ROUND(CASE WHEN SUM(YTD.TOTAL) = 0 THEN 0 ELSE SUM(YTD.ONTIME) / SUM(YTD.TOTAL) * 100.0 END, 1)    AS YTD_MYCP,
                                   ROUND(CASE WHEN SUM(LMTD.TOTAL) = 0 THEN 0 ELSE SUM(LMTD.ONTIME) / SUM(LMTD.TOTAL) * 100.0 END, 1) AS LMTD_MYCP
                            FROM MTD
                                     LEFT JOIN YTD ON MTD.DISPLAY = YTD.DISPLAY
                                     LEFT JOIN LMTD ON MTD.DISPLAY = LMTD.DISPLAY),
             TOTAL AS (SELECT '>=95%'                                                                                                   AS VALUE1,
                              DB.MTD_MYCP                                                                                               AS VALUE2,
                              DB.YTD_MYCP                                                                                               AS VALUE3,
                              CASE WHEN MTD_MYCP &gt; LMTD_MYCP THEN 'up' WHEN MTD_MYCP &lt; LMTD_MYCP THEN 'down' ELSE 'right' END     AS ARROW,
                              CASE WHEN MTD_MYCP &gt; LMTD_MYCP THEN 'green' WHEN MTD_MYCP &lt; LMTD_MYCP THEN 'red' ELSE 'yellow' END  AS ARROW_COLOR
                       FROM TOTAL_BASE DB)
        SELECT T.VALUE1, VALUE2, VALUE3, ARROW, ARROW_COLOR, 0 AS ORD
        FROM TOTAL T
        WHERE T.VALUE1 IS NOT NULL
        UNION ALL
        SELECT T.VALUE1, VALUE2, VALUE3, ARROW, ARROW_COLOR, NULL
        FROM DETAILS T
        WHERE T.VALUE1 IS NOT NULL
        ORDER BY ORD, VALUE2
    </select>

    <select id="queryReport1CLO" resultType="com.scp.canvas.bean.MyStoryPerformanceResultBean">
        WITH MAT_BASE AS (
                SELECT T.MATERIAL, T.PLANT_CODE FROM SCPA.MATERIAL_MASTER_V T
                <where>
                    <include refid="performance_result_filter"/>
                </where>
             ),
             CLO_BASE AS (SELECT T.T0_MATERIAL,
                                 T.T0_PLANT_CODE,
                                 T.T0_${display},
                                 T.T0_MEET_OR_FAIL,
                                 T.T0_CLO_LT_GROUP
                          FROM ${SCPA.CLO_STRUCTURE_HIST} T
                              INNER JOIN MAT_BASE T2 ON T.T0_MATERIAL = T2.MATERIAL AND T.T0_PLANT_CODE = T2.PLANT_CODE
                              INNER JOIN (SELECT TT.YEAR || TT.MONTH AS TEXT, MIN(TT.DATE$) DATE$
                                             FROM SY_CALENDAR TT
                                             WHERE TT.NAME = 'National Holidays'
                                             GROUP BY TT.YEAR, TT.MONTH) T3
                                            ON T.DATE$ = T3.DATE$
                          WHERE T.T0_MEET_OR_FAIL IN ('Meet', 'Fail')
                          AND T.DATE$ BETWEEN TRUNC(SYSDATE, 'MM') AND TRUNC(SYSDATE, 'DD')
                          AND T.CLO_VERSION = '2025'),
             DETAILS_BASE AS (SELECT T.T0_${display},
                                     NVL(SUM(CASE WHEN T.T0_MEET_OR_FAIL = 'Meet' THEN T2.NET_NET_VALUE_RMB END), 0) AS MEET_VAL,
                                     NVL(SUM(CASE WHEN T.T0_MEET_OR_FAIL = 'Fail' THEN T2.NET_NET_VALUE_RMB END), 0) AS FAIL_VAL
                              FROM CLO_BASE T
                                       LEFT JOIN SCPA.DEMAND_ORDER_INTAKE_V T2 ON T.T0_MATERIAL = T2.MATERIAL AND T.T0_PLANT_CODE = T2.PLANT_CODE
                              WHERE T2.CALENDAR_DATE BETWEEN TRUNC(ADD_MONTHS(SYSDATE, -6), 'MM') AND LAST_DAY(TRUNC(ADD_MONTHS(SYSDATE, -1), 'MM'))
                              GROUP BY T.T0_${display}),
             DETAILS AS (SELECT T.T0_${display}                                                                                              AS VALUE1,
                                CASE WHEN T.MEET_VAL + T.FAIL_VAL = 0 THEN 0 ELSE ROUND(T.MEET_VAL / (T.MEET_VAL + T.FAIL_VAL) * 100, 1) END AS VALUE2
                         FROM DETAILS_BASE T),
             TOTAL AS (SELECT '>=95%'                                                                                                                   AS VALUE1,
                              CASE WHEN SUM(T.MEET_VAL + T.FAIL_VAL) = 0 THEN 0 ELSE ROUND(SUM(T.MEET_VAL) / SUM(T.MEET_VAL + T.FAIL_VAL) * 100, 1) END AS VALUE2
                       FROM DETAILS_BASE T)

        SELECT T.VALUE1, VALUE2, 0 AS ORD
        FROM TOTAL T
        WHERE T.VALUE1 IS NOT NULL
        UNION ALL
        SELECT T.VALUE1, VALUE2, NULL
        FROM DETAILS T
        WHERE T.VALUE1 IS NOT NULL
        ORDER BY ORD, VALUE2
    </select>
</mapper>
