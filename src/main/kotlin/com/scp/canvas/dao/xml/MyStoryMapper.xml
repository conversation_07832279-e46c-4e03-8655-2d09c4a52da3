<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.canvas.dao.IMyStoryDao">
    <select id="queryPageList" resultType="java.util.Map">
        SELECT T.PAGE_ID,
               T.PAGE_TYPE,
               NVL(T.PAGE_TITLE, T.PAGE_ID) AS PAGE_TITLE,
               NVL(T2.USER_NAME, T.CREATE_BY$) AS CREATE_BY
        FROM MY_STORY_PAGES T LEFT JOIN SY_USER_MASTER_DATA T2 ON T.CREATE_BY$ = T2.SESA_CODE
        WHERE T.VARIANT_ID = #{variantId, jdbcType=VARCHAR}
        ORDER BY NVL(T.PAGE_ORDER, 999), T.PAGE_TYPE, NVL(T.PAGE_TITLE, T.PAGE_ID)
    </select>

    <select id="queryComments" resultType="java.util.Map">
        SELECT T.COMMENT_ID, T.USER_ID, T.BIND_TO,
               TO_CHAR(T.CREATE_DATE$, 'YYYY/MM/DD HH24:MI:SS') CREATE_DATE,
               T.CONTENT, T2.USER_NAME, T2.IS_MAINTAINER
		FROM MY_STORY_COMMENTS T
		LEFT JOIN SY_USER_MASTER_DATA T2 ON T.USER_ID = T2.SESA_CODE
		WHERE T.BIND_TO = #{bindTo, jdbcType=VARCHAR} AND T.PARENT_COM = #{parentComponent,jdbcType=VARCHAR}
		ORDER BY T.CREATE_DATE$ DESC
    </select>

    <select id="queryReplies" resultType="java.util.Map">
		SELECT T.REPLY_ID, T.USER_ID, TO_CHAR(T.CREATE_DATE$, 'YYYY/MM/DD HH24:MI:SS') CREATE_DATE, T.CONTENT, T2.USER_NAME, T2.IS_MAINTAINER, T.COMMENT_ID
		FROM MY_STORY_COMMENTS_REPLY T
		LEFT JOIN SY_USER_MASTER_DATA T2 ON T.USER_ID = T2.SESA_CODE
		WHERE T.COMMENT_ID IN
		<foreach collection="comments" item="item" open="(" close=")" separator=",">
			#{item.COMMENT_ID, jdbcType=VARCHAR}
		</foreach>
		ORDER BY T.CREATE_DATE$ DESC
	</select>

    <delete id="deleteComment">
        BEGIN
			DELETE FROM MY_STORY_COMMENTS WHERE COMMENT_ID = #{commentId, jdbcType=VARCHAR};
			DELETE FROM MY_STORY_COMMENTS_REPLY WHERE COMMENT_ID = #{commentId, jdbcType=VARCHAR};
		END;
    </delete>

    <delete id="deleteReply">
        DELETE FROM MY_STORY_COMMENTS_REPLY WHERE REPLY_ID = #{replyId, jdbcType=VARCHAR}
    </delete>

    <insert id="sendComment">
        DECLARE
            PARAMS_COLB CLOB := #{content, jdbcType=CLOB};
        BEGIN
			INSERT INTO MY_STORY_COMMENTS (COMMENT_ID, USER_ID, BIND_TO, CREATE_DATE$, CONTENT, PARENT_COM)
			VALUES
			(#{commentId, jdbcType=VARCHAR}, #{session.userid, jdbcType=VARCHAR}, #{bindTo, jdbcType=VARCHAR},
			sysdate, PARAMS_COLB, #{parentComponent, jdbcType=VARCHAR});
		END;
    </insert>

    <select id="queryCommentById" resultType="java.lang.String">
        SELECT T.CONTENT FROM MY_STORY_COMMENTS T WHERE T.COMMENT_ID = #{commentId, jdbcType=VARCHAR}
    </select>

    <update id="modifyComment">
        DECLARE
            PARAMS_COLB CLOB := #{content, jdbcType=CLOB};
        BEGIN
			UPDATE MY_STORY_COMMENTS T SET T.CONTENT = PARAMS_COLB WHERE T.COMMENT_ID = #{commentId, jdbcType=VARCHAR};
		END;
    </update>

    <insert id="sendReply">
        DECLARE
            PARAMS_COLB CLOB := #{content, jdbcType=CLOB};
		BEGIN
			INSERT INTO MY_STORY_COMMENTS_REPLY (REPLY_ID, USER_ID, COMMENT_ID, CREATE_DATE$, CONTENT) VALUES
			(#{replyId, jdbcType=VARCHAR}, #{session.userid, jdbcType=VARCHAR}, #{commentId, jdbcType=VARCHAR}, sysdate, PARAMS_COLB);
		END;
    </insert>

    <insert id="saveLogs">
        DECLARE
            PARAMS_COLB CLOB := #{content, jdbcType=CLOB};
		BEGIN
            INSERT INTO MY_STORY_COMMENTS_LOGS (BIND_TO, PARENT_COM, USER_ID, OPERATION, LOG_TIME, CONTENT) VALUES
			(#{bindTo, jdbcType=VARCHAR}, #{parentComponent, jdbcType=VARCHAR}, #{session.userid, jdbcType=VARCHAR}, #{operation,jdbcType=VARCHAR}, sysdate, PARAMS_COLB);
		END;
    </insert>

    <sql id="queryChangeLogsSQL">
        SELECT CASE WHEN T2.USER_NAME IS NOT NULL THEN T2.USER_NAME || ' [' || T2.SESA_CODE || ']' ELSE T.USER_ID END AS USER_NAME,
               TO_CHAR(LOG_TIME, 'YYYY/MM/DD HH24:MI:SS') AS LOG_TIME,
               OPERATION
          FROM MY_STORY_COMMENTS_LOGS T
               LEFT JOIN SY_USER_MASTER_DATA T2 ON T.USER_ID = T2.SESA_CODE
         WHERE T.BIND_TO = #{bindTo, jdbcType=VARCHAR}
               AND T.PARENT_COM = #{parentComponent, jdbcType=VARCHAR}
        ORDER BY LOG_TIME DESC
    </sql>

     <select id="queryChangeLogsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryChangeLogsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryChangeLogs" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryChangeLogsSQL"/>
        <include refid="global.select_footer"/>
    </select>

	<select id="queryPageAdmin" resultType="java.lang.Integer">
        SELECT COUNT(1) CNT
          FROM SY_MENU_AUTH T
         WHERE UPPER(T.AUTH_DETAILS) = 'ADMIN'
           AND T.USER_ID = #{userid, jdbcType=VARCHAR}
           AND T.MENU_CODE = #{parentCode, jdbcType=VARCHAR}
    </select>

	<select id="queryMyVariantList" resultType="com.scp.toolbox.bean.TreeData">
        SELECT T.VARIANT_ID AS KEY,
               T.VARIANT_NAME AS LABEL,
               CASE WHEN T2.USER_ID IS NOT NULL THEN 'default' END AS SUB_LABEL,
               T.GROUP_NAME AS GROUPS,
               CASE WHEN T2.USER_ID IS NOT NULL THEN 'IS_DEFAULT' END AS DATA_VALUE
         FROM SCPA.MY_STORY_VARIANT T LEFT JOIN MY_STORY_VARIANT_DEFAULT T2 ON T.VARIANT_ID = T2.VARIANT_ID AND T2.USER_ID = #{userid, jdbcType=VARCHAR}
         WHERE T.CREATE_BY$ = #{userid, jdbcType=VARCHAR}
        ORDER BY T.GROUP_NAME, T.VARIANT_NAME
    </select>

    <select id="querySharedVariantList" resultType="com.scp.toolbox.bean.TreeData">
        SELECT T.VARIANT_ID AS KEY,
               T.VARIANT_NAME AS LABEL,
               NVL(T3.USER_NAME, T.CREATE_BY$) ||
               CASE WHEN T2.USER_ID IS NOT NULL THEN ', default' END AS SUB_LABEL,
               T.GROUP_NAME AS GROUPS,
               CASE WHEN T2.USER_ID IS NOT NULL THEN 'IS_DEFAULT' END AS DATA_VALUE
         FROM SCPA.MY_STORY_VARIANT T LEFT JOIN MY_STORY_VARIANT_DEFAULT T2 ON T.VARIANT_ID = T2.VARIANT_ID AND T2.USER_ID = #{userid, jdbcType=VARCHAR}
                                      LEFT JOIN SY_USER_MASTER_DATA T3 ON T.CREATE_BY$ = T3.SESA_CODE
         WHERE (T.AUTH_TYPE = 'PUBLIC'
                OR EXISTS(SELECT 1 FROM SCPA.MY_STORY_VARIANT_AUTH T2 WHERE T.VARIANT_ID = T2.VARIANT_ID AND T2.USER_ID = #{userid, jdbcType=VARCHAR})
                )
                AND T.CREATE_BY$ != #{userid, jdbcType=VARCHAR}
        ORDER BY T.GROUP_NAME, T.VARIANT_NAME
    </select>

    <select id="queryAllVariantList" resultType="com.scp.toolbox.bean.TreeData">
        SELECT T.VARIANT_ID AS KEY,
               T.VARIANT_NAME AS LABEL,
               NVL(T3.USER_NAME, T.CREATE_BY$) ||
               CASE WHEN T2.USER_ID IS NOT NULL THEN ', default' END AS SUB_LABEL,
               T.GROUP_NAME AS GROUPS,
               CASE WHEN T2.USER_ID IS NOT NULL THEN 'IS_DEFAULT' END AS DATA_VALUE
         FROM SCPA.MY_STORY_VARIANT T LEFT JOIN MY_STORY_VARIANT_DEFAULT T2 ON T.VARIANT_ID = T2.VARIANT_ID AND T2.USER_ID = #{userid, jdbcType=VARCHAR}
                                      LEFT JOIN SY_USER_MASTER_DATA T3 ON T.CREATE_BY$ = T3.SESA_CODE
        ORDER BY T.GROUP_NAME, T.VARIANT_NAME
    </select>

    <select id="querySharedTo" resultType="java.util.Map">
        SELECT '[ALL USER]' AS "NAME", '[ALL USER]' AS "VALUE" FROM DUAL
        UNION ALL
        SELECT T.USER_NAME AS "NAME", T.SESA_CODE AS "VALUE" FROM SY_USER_MASTER_DATA T
    </select>

    <select id="queryExistsGroup" resultType="java.lang.String">
        SELECT DISTINCT GROUP_NAME FROM SCPA.MY_STORY_VARIANT
    </select>

    <insert id="saveSharedTo">
        INSERT INTO SCPA.MY_STORY_VARIANT_AUTH (VARIANT_ID, USER_ID)
        <foreach collection="shareTo" separator=" union all " item="item">
            SELECT #{variantID, jdbcType=VARCHAR}, #{item, jdbcType=VARCHAR} from dual
        </foreach>
    </insert>

    <insert id="saveVariant">
        DECLARE
        	CLOB_CONTENT CLOB := #{conditions, jdbcType=CLOB};
		BEGIN
            INSERT INTO MY_STORY_VARIANT
            (VARIANT_ID, VARIANT_NAME, GROUP_NAME, CONDITIONS, AUTH_TYPE, CREATE_BY$, CREATE_DATE$)
            VALUES
            (#{variantID,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{groups,jdbcType=VARCHAR}, CLOB_CONTENT, #{authType,jdbcType=VARCHAR},
             UPPER(#{session.userid,jdbcType=VARCHAR}), SYSDATE);
        END;
	</insert>

    <insert id="saveBuiltInPages">
        BEGIN
            DELETE FROM MY_STORY_PAGES T WHERE T.PAGE_TYPE = 'BUILT_IN' AND T.VARIANT_ID = #{variantID,jdbcType=VARCHAR}
            <if test="componentList.size() > 0">
                AND T.PAGE_ID NOT IN
                <foreach collection="componentList" item="item" open="(" close=")" separator=",">
                    #{item, jdbcType=VARCHAR}
                </foreach>
            </if>;

            <if test="componentList.size() > 0">
                MERGE INTO MY_STORY_PAGES T USING
                (
                    <foreach collection="componentList" item="item" separator="union all">
                        SELECT #{variantID,jdbcType=VARCHAR} VARIANT_ID, #{item, jdbcType=VARCHAR} PAGE_ID, 'BUILT_IN' PAGE_TYPE FROM DUAL
                    </foreach>
                ) S
                ON (T.VARIANT_ID = s.VARIANT_ID AND T.PAGE_ID = S.PAGE_ID AND T.PAGE_TYPE = S.PAGE_TYPE)
                WHEN NOT MATCHED THEN
                INSERT (PAGE_ID, VARIANT_ID, PAGE_TYPE, CREATE_BY$, CREATE_DATE$)
                VALUES (S.PAGE_ID, S.VARIANT_ID, S.PAGE_TYPE, #{session.userid, jdbcType=VARCHAR}, SYSDATE);
            </if>
        END;
    </insert>

    <select id="queryVariantByKey" resultType="java.lang.String">
        SELECT T.CONDITIONS FROM MY_STORY_VARIANT T WHERE T.VARIANT_ID = #{key, jdbcType=VARCHAR}
    </select>

    <delete id="deleteVariant">
        BEGIN
            DELETE FROM MY_STORY_VARIANT T WHERE T.VARIANT_ID = #{key, jdbcType=VARCHAR};
            DELETE FROM MY_STORY_VARIANT_AUTH T WHERE T.VARIANT_ID = #{key, jdbcType=VARCHAR};
            DELETE FROM MY_STORY_VARIANT_DEFAULT T WHERE T.VARIANT_ID = #{key, jdbcType=VARCHAR};
            DELETE FROM MY_STORY_COMMENTS_LOGS T WHERE T.BIND_TO = #{key, jdbcType=VARCHAR};
            DELETE FROM MY_STORY_PAGES T WHERE T.VARIANT_ID = #{key, jdbcType=VARCHAR};

            DELETE FROM MY_STORY_COMMENTS_REPLY WHERE COMMENT_ID IN
            (SELECT COMMENT_ID FROM MY_STORY_COMMENTS WHERE BIND_TO = #{key, jdbcType=VARCHAR});
			DELETE FROM MY_STORY_COMMENTS WHERE BIND_TO = #{key, jdbcType=VARCHAR};
        END;
    </delete>

    <select id="queryVariantConfigByKey" resultType="java.util.Map">
        SELECT VARIANT_NAME, GROUP_NAME,
               CASE WHEN T2.USER_ID IS NOT NULL THEN 'IS_DEFAULT' END AS IS_DEFAULT,
               CASE WHEN #{session.userid,jdbcType=VARCHAR} = T.CREATE_BY$ THEN 'Y' END IS_MY_STORY
          FROM MY_STORY_VARIANT T LEFT JOIN MY_STORY_VARIANT_DEFAULT T2 ON T.VARIANT_ID = T2.VARIANT_ID AND T2.USER_ID = #{session.userid,jdbcType=VARCHAR}
         WHERE T.VARIANT_ID = #{key, jdbcType=VARCHAR}
    </select>

    <select id="querySharedToByKey" resultType="java.lang.String">
        SELECT T.USER_ID FROM MY_STORY_VARIANT_AUTH T WHERE T.VARIANT_ID = #{key, jdbcType=VARCHAR}
    </select>

    <insert id="modifySharedTo">
        BEGIN
            DELETE FROM SCPA.MY_STORY_VARIANT_AUTH T WHERE T.VARIANT_ID = #{key, jdbcType=VARCHAR};
            INSERT INTO SCPA.MY_STORY_VARIANT_AUTH (VARIANT_ID, USER_ID)
                    <foreach collection="shareTo" separator=" union all " item="item">
                        SELECT #{key, jdbcType=VARCHAR}, #{item, jdbcType=VARCHAR} from dual
                    </foreach>;
        END;
    </insert>

    <update id="modifyVariant">
        DECLARE
        	CLOB_CONTENT CLOB := #{conditions, jdbcType=CLOB};
		BEGIN
		    UPDATE MY_STORY_VARIANT
		       SET VARIANT_NAME = #{name,jdbcType=VARCHAR},
		           GROUP_NAME = #{groups,jdbcType=VARCHAR},
		           CONDITIONS = CLOB_CONTENT,
		           AUTH_TYPE = #{authType,jdbcType=VARCHAR},
		           UPDATE_BY$ = UPPER(#{session.userid,jdbcType=VARCHAR}),
		           UPDATE_DATE$ = SYSDATE
		     WHERE VARIANT_ID = #{key,jdbcType=VARCHAR};
        END;
    </update>

    <select id="queryConditionById" resultType="java.util.Map">
        SELECT CONDITIONS FROM MY_STORY_VARIANT T WHERE T.VARIANT_ID = #{cid,jdbcType=VARCHAR}
    </select>

    <select id="queryDefaultCondition" resultType="java.util.Map">
        SELECT T.VARIANT_ID, T.VARIANT_NAME FROM MY_STORY_VARIANT T INNER JOIN MY_STORY_VARIANT_DEFAULT T2 ON T.VARIANT_ID = T2.VARIANT_ID AND T2.USER_ID = #{session.userid,jdbcType=VARCHAR}
    </select>

    <delete id="deleteDefaultVariant">
        DELETE FROM MY_STORY_VARIANT_DEFAULT T WHERE T.USER_ID = #{session.userid, jdbcType=VARCHAR} AND T.VARIANT_ID = #{key, jdbcType=VARCHAR}
    </delete>

    <update id="updateDefaultVariant">
        MERGE INTO MY_STORY_VARIANT_DEFAULT T
        USING (SELECT #{key, jdbcType=VARCHAR} VARIANT_ID, #{session.userid, jdbcType=VARCHAR} USER_ID FROM DUAL) S
			    ON (T.USER_ID = S.USER_ID)
			WHEN MATCHED THEN
				UPDATE SET T.VARIANT_ID = S.VARIANT_ID,
				           T.UPDATE_BY$ = #{session.userid, jdbcType=VARCHAR},
				           T.UPDATE_DATE$ = SYSDATE
			WHEN NOT MATCHED THEN
				INSERT (USER_ID, VARIANT_ID, CREATE_BY$, CREATE_DATE$)
				VALUES (S.USER_ID, S.VARIANT_ID, #{session.userid, jdbcType=VARCHAR}, SYSDATE)
    </update>

    <select id="queryStoryNameById" resultType="java.lang.String">
        SELECT T.VARIANT_NAME FROM MY_STORY_VARIANT T WHERE T.VARIANT_ID = #{cid,jdbcType=VARCHAR}
    </select>

    <select id="queryManualInputById" resultType="java.util.Map">
        SELECT T.PAGE_TITLE, T.CONTENT  FROM MY_STORY_PAGES T WHERE T.PAGE_ID = #{pageId,jdbcType=VARCHAR}
    </select>

    <select id="queryCommentCntByVariantId" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM MY_STORY_COMMENTS T WHERE T.BIND_TO = #{key,jdbcType=VARCHAR}
    </select>
</mapper>

