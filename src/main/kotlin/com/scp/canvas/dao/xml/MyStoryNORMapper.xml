<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.canvas.dao.IMyStoryNORDao">
    <sql id="nor_filter">
        <if test="_filters != null and _filters != ''.toString()">
            and ${_filters}
        </if>
        <if test="_filters2 != null and _filters2 != ''.toString()">
            and ${_filters2}
        </if>
    </sql>

    <select id="queryFilterOpts" resultType="java.util.Map">
        select * from SCPA.NOR_FILTER_V t
        order by t.category,decode(t.name,'Others','zzz',t.name)
    </select>

    <select id="queryFilterPoOpts" resultType="java.util.Map">
        select * from SCPA.PO_NOR_FILTER_V t
        order by t.category,decode(t.name,'Others','zzz',t.name)
    </select>

    <select id="queryPivotOpts" resultType="java.lang.String">
        SELECT T.COLUMN_NAME
        FROM USER_TAB_COLS T
        WHERE T.TABLE_NAME = 'NOR_V'
          AND T.DATA_TYPE = 'VARCHAR2'
          AND T.COLUMN_NAME NOT LIKE '%$%'
          AND T.COLUMN_NAME NOT LIKE '%BU%'
          AND T.COLUMN_NAME NOT IN('SALES_ORDER_NUMBER', 'SALES_ORDER_ITEM')
        ORDER BY T.COLUMN_NAME
    </select>

    <select id="queryPoOpts" resultType="java.lang.String">
        SELECT T.COLUMN_NAME
        FROM USER_TAB_COLS T
        WHERE T.TABLE_NAME = 'COMPLETED_PO_NOR_V'
          AND T.DATA_TYPE = 'VARCHAR2'
        ORDER BY T.COLUMN_NAME
    </select>

    <select id="queryWeekOpts" resultType="java.lang.String">
        SELECT DISTINCT T.YEAR || T.WEEK_NO
        FROM SY_CALENDAR T
        WHERE T.NAME = 'National Holidays'
        AND T.DATE$ BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'YYYY'), -24) AND TRUNC(SYSDATE + 7, 'DD')
        ORDER BY T.YEAR || T.WEEK_NO
    </select>

    <select id="queryCurrentWeek" resultType="java.lang.String">
        SELECT YEAR || WEEK_NO
        FROM SCPA.SY_CALENDAR T
        WHERE DATE$ = TRUNC(SYSDATE, 'DD')
          AND NAME = 'National Holidays'
    </select>

    <select id="queryReport1Header" resultType="java.util.Map">
        SELECT TO_CHAR(T.DATE$, 'YYYY') || 'YTD' AS YTD_HEADER,
               TO_CHAR(T.DATE$, 'Mon-YY', 'NLS_DATE_LANGUAGE=AMERICAN') AS MTD_HEADER,
               SUBSTR(T.YEAR, 3) || 'W' || T.WEEK_NO AS WK_HEADER,
               (SELECT SUBSTR(T.YEAR, 3) || 'W' || T.WEEK_NO
                 FROM SY_CALENDAR T
                WHERE T.YEAR || T.WEEK_NO &lt; #{report1Week,jdbcType=VARCHAR}
                  AND T.NAME = 'National Holidays'
                ORDER BY T.YEAR || T.WEEK_NO DESC
                    FETCH NEXT 1 ROWS ONLY)                             AS WK1_HEADER
        FROM SY_CALENDAR T
        WHERE T.YEAR || T.WEEK_NO = #{report1Week,jdbcType=VARCHAR}
          AND T.NAME = 'National Holidays'
        ORDER BY DATE$
            FETCH NEXT 1 ROWS ONLY
    </select>

    <select id="queryReport2Header" resultType="java.util.Map">
        SELECT TO_CHAR(T.DATE$, 'YYYY') || 'YTD' AS YTD_HEADER,
               TO_CHAR(T.DATE$, 'Mon-YY', 'NLS_DATE_LANGUAGE=AMERICAN') AS MTD_HEADER,
               SUBSTR(T.YEAR, 3) || 'W' || T.WEEK_NO AS WK_HEADER,
               (SELECT SUBSTR(T.YEAR, 3) || 'W' || T.WEEK_NO
                FROM SY_CALENDAR T
                WHERE T.YEAR || T.WEEK_NO &lt; #{report2Week,jdbcType=VARCHAR}
                  AND T.NAME = 'National Holidays'
                ORDER BY T.YEAR || T.WEEK_NO DESC
                    FETCH NEXT 1 ROWS ONLY)                             AS WK1_HEADER
        FROM SY_CALENDAR T
        WHERE T.YEAR || T.WEEK_NO = #{report2Week,jdbcType=VARCHAR}
          AND T.NAME = 'National Holidays'
        ORDER BY DATE$
            FETCH NEXT 1 ROWS ONLY
    </select>

    <select id="queryReport1" resultType="java.util.Map">
        WITH NOR_SOURCE AS (
        SELECT  NVL(T.${category},'Others') as ${category},
        SALES_ORDER_NUMBER,
        SALES_ORDER_ITEM,
        GI_DATE,
        RESCH_COUNTER,
        POST_PONE_COUNTER,
        PRE_PONE_COUNTER,
        LOCAL_BU,
        CREATED_DATE,
        CRD_DATE,
        VIP_BU
        FROM ${SCPA.NOR_V} T
        WHERE T.GI_DATE > TO_DATE(SUBSTR(#{report1Week,jdbcType=VARCHAR}, 0, 4) || '/01/01', 'YYYY/MM/DD') - 30
        <foreach collection="report2SelectedValues" item="item" index="index" separator="and" open="and">
            T."${report2Categories[index]}" = #{item, jdbcType=VARCHAR}
        </foreach>
        <include refid="nor_filter"/>
        ),
        MTD_SOURCE AS (
        SELECT *
        FROM NOR_SOURCE T
        WHERE TRUNC(T.GI_DATE, 'MM') = (SELECT TRUNC(T.DATE$, 'MM')
        FROM SY_CALENDAR T
        WHERE (T.YEAR || T.WEEK_NO) = #{report1Week,jdbcType=VARCHAR}
        AND T.NAME = 'National Holidays'
        ORDER BY TRUNC(T.DATE$, 'MM')
        FETCH FIRST 1 ROWS ONLY)),

        YTD_SOURCE AS (SELECT *
            FROM NOR_SOURCE T
            WHERE TRUNC(T.GI_DATE, 'YYYY') = (SELECT TRUNC(T.DATE$, 'YYYY')
            FROM SY_CALENDAR T
            WHERE (T.YEAR || T.WEEK_NO) = #{report1Week,jdbcType=VARCHAR}
            AND T.NAME = 'National Holidays'
            ORDER BY DATE$
            FETCH FIRST 1 ROWS ONLY)),
        MTD_SONOR AS (SELECT T.${category},
            SUM(CASE WHEN RESCH_COUNTER > 0 THEN 1 ELSE 0 END) AS MTDSONOR,
            COUNT(SALES_ORDER_NUMBER)                         AS MTD_SO_TOTAL
            FROM MTD_SOURCE T
            GROUP BY T.${category}),
        YTD_SONOR AS (SELECT T.${category},
            SUM(CASE WHEN RESCH_COUNTER > 0 THEN 1 ELSE 0 END)                        AS YTDSONOR,
            SUM(CASE WHEN RESCH_COUNTER >= 2 AND 5 > RESCH_COUNTER THEN 1 ELSE 0 END) AS RC2,
            SUM(CASE WHEN RESCH_COUNTER >= 5 AND 7 > RESCH_COUNTER THEN 1 ELSE 0 END) AS RC5,
            SUM(CASE WHEN RESCH_COUNTER >= 7 THEN 1 ELSE 0 END)                       AS RC7,
            COUNT(SALES_ORDER_NUMBER)                                                 AS YTD_SO_TOTAL
            FROM YTD_SOURCE T
            GROUP BY T.${category}),
        YTD_SONORO AS (SELECT T.${category},
            SUM(CASE WHEN POST_PONE_COUNTER > 0 THEN 1 ELSE 0 END) AS YTDSONORO,
            COUNT(SALES_ORDER_NUMBER)                              AS YTD_SO_TOTAL_O
            FROM YTD_SOURCE T
            GROUP BY T.${category}),
        BASE AS (
        SELECT T.${category},
        T.YTDSONORO,
        T.YTD_SO_TOTAL_O,
        T2.YTDSONOR,
        T3.MTDSONOR,
        T3.MTD_SO_TOTAL,
        T2.RC2,
        T2.RC5,
        T2.RC7,
        T2.YTD_SO_TOTAL
        FROM YTD_SONORO T
        LEFT JOIN YTD_SONOR T2 ON T2.${category} = T.${category}
        LEFT JOIN MTD_SONOR T3 ON T3.${category} = T.${category}
        ),
        RESULT AS (
        SELECT T.${category} AS "category",
        CASE
        WHEN T.YTD_SO_TOTAL = 0 THEN 0
            ELSE ROUND(T.YTDSONOR/ T.YTD_SO_TOTAL * 100, 1) END       AS "ytdSonor",
        CASE
        WHEN T.MTD_SO_TOTAL = 0 THEN 0
            ELSE ROUND(T.MTDSONOR/ T.MTD_SO_TOTAL * 100, 1) END       AS "mtdSonor",
        CASE
        WHEN T.YTD_SO_TOTAL_O = 0 THEN 0
            ELSE ROUND(T.YTDSONORO / T.YTD_SO_TOTAL_O * 100, 1) END AS "ytdSonoro",
        CASE
        WHEN T.YTD_SO_TOTAL = 0 THEN 0
            ELSE ROUND(T.RC2 / T.YTD_SO_TOTAL * 100, 1) END         AS "ytdsonor2",
        CASE
        WHEN T.YTD_SO_TOTAL = 0 THEN 0
            ELSE ROUND(T.RC5 / T.YTD_SO_TOTAL * 100, 1) END         AS "ytdsonor5",
        CASE
        WHEN T.YTD_SO_TOTAL = 0 THEN 0
            ELSE ROUND(T.RC7 / T.YTD_SO_TOTAL * 100, 1) END         AS "ytdsonor7"
        FROM BASE T
        UNION ALL
        SELECT 'Total',
        CASE
        WHEN SUM(T.YTD_SO_TOTAL) = 0 THEN 0
            ELSE ROUND(SUM(T.YTDSONOR)/ SUM(T.YTD_SO_TOTAL) * 100, 1)  END      AS "ytdSonor",
        CASE
        WHEN SUM(T.MTD_SO_TOTAL) = 0 THEN 0
             ELSE ROUND(SUM(T.MTDSONOR)/ SUM(T.MTD_SO_TOTAL) * 100, 1) END      AS "mtdSonor",
        CASE
        WHEN SUM(T.YTD_SO_TOTAL_O) = 0 THEN 0
            ELSE ROUND(SUM(T.YTDSONORO) / SUM(T.YTD_SO_TOTAL_O) * 100, 1) END AS "ytdSonoro",
        CASE
        WHEN SUM(T.YTD_SO_TOTAL) = 0 THEN 0
            ELSE ROUND(SUM(T.RC2) / SUM(T.YTD_SO_TOTAL) * 100, 1) END         AS "ytdsonor2",
        CASE
        WHEN SUM(T.YTD_SO_TOTAL) = 0 THEN 0
            ELSE ROUND(SUM(T.RC5) / SUM(T.YTD_SO_TOTAL) * 100, 1) END         AS "ytdsonor5",
        CASE
        WHEN SUM(T.YTD_SO_TOTAL) = 0 THEN 0
            ELSE ROUND(SUM(T.RC7) / SUM(T.YTD_SO_TOTAL) * 100, 1) END         AS "ytdsonor7"
        FROM BASE T
        )
        SELECT T."category",
        NVL(T."ytdSonor", 0)  AS "ytdSonor",
        NVL(T."mtdSonor", 0)  AS "mtdSonor",
        NVL(T."ytdSonoro", 0) AS "ytdSonoro",
        NVL(T."ytdsonor2", 0) AS "ytdSonor2",
        NVL(T."ytdsonor5", 0) AS "ytdSonor5",
        NVL(T."ytdsonor7", 0) AS "ytdSonor7"
        FROM RESULT T
        ORDER BY DECODE(T."category", 'Others', -1, 'Total', -2) DESC
        OFFSET 0 ROWS FETCH NEXT 128 ROWS ONLY
    </select>
    <select id="queryReport2" resultType="java.util.Map">
        WITH PO_NORO_SOURCE AS (
        SELECT  NVL(T.${category},'Others') as ${category},
        PO_COMPLETION_DATE,
        PO_ITEM_CREATED_DATE,
        CALENDAR_YEAR,
        CALENDAR_MONTH,
        CALENDAR_QUARTER,
        CALENDAR_WEEK,
        POSTPONE_SO_COUNT,
        MATERIAL,
        NOR_COUNT,
        MAX_SO_BOOKED_LINE,
        RESCHEDULE_COUNT,
        RESCHEDULE_IN_COUNT,
        RESCHEDULE_OUT_COUNT,
        PAST_DUE_COUNT,
        RETURN_COUNT,
        PURCH_ORDER_NUMBER,
        PURCH_ORDER_ITEM,
        CLO_SCOPE,
        AMU_ONEMM,
        AMF_ONEMM,
        REORDER_POINT,
        SAFETY_STOCK,
        ACTIVENESS,
        CALCULATED_ABC,
        ORDER_QUANTITY,
        NET_ORDER_VALUE,
        NORO_FLAG AS NORO
        FROM ${SCPA.COMPLETED_PO_NOR_V} T
        WHERE T.PO_COMPLETION_DATE > TO_DATE(SUBSTR(#{report2Week,jdbcType=VARCHAR}, 0, 4) || '/01/01', 'YYYY/MM/DD') - 30
        <foreach collection="report1SelectedValues" item="item" index="index" separator="and" open="and">
            T."${report1Categories[index]}" = #{item, jdbcType=VARCHAR}
        </foreach>
        <include refid="nor_filter"/>
        ),
        PO_MTD_SOURCE AS (
        SELECT *
        FROM PO_NORO_SOURCE T
        WHERE TRUNC(T.PO_COMPLETION_DATE, 'MM') = (SELECT TRUNC(T.DATE$, 'MM')
        FROM SY_CALENDAR T
        WHERE (T.YEAR || T.WEEK_NO) = #{report2Week,jdbcType=VARCHAR}
        AND T.NAME = 'National Holidays'
        ORDER BY TRUNC(T.DATE$, 'MM')
        FETCH FIRST 1 ROWS ONLY)),

        PO_YTD_SOURCE AS (SELECT *
        FROM PO_NORO_SOURCE T
        WHERE TRUNC(T.PO_COMPLETION_DATE, 'YYYY') = (SELECT TRUNC(T.DATE$, 'YYYY')
        FROM SY_CALENDAR T
        WHERE (T.YEAR || T.WEEK_NO) = #{report2Week,jdbcType=VARCHAR}
        AND T.NAME = 'National Holidays'
        ORDER BY DATE$
        FETCH FIRST 1 ROWS ONLY)),

        PO_MTD_NORO AS (
                SELECT T.${category},
                    count(decode(t.NORO, 'Others', 1, NULL))    AS "M_Others",
                    count(decode(t.NORO, 'ON TARGET', 1, NULL)) AS "M_ON TARGET",
                    count(decode(t.NORO, 'EROC=2~3', 1, NULL))  AS "M_EROC=2~3",
                    count(decode(t.NORO, 'EROC=4~7', 1, NULL))  AS "M_EROC=4~7",
                    count(decode(t.NORO, 'EROC=8~14', 1, NULL)) AS "M_EROC=8~14",
                    count(decode(t.NORO, 'EROC>14', 1, NULL))   AS "M_EROC>14"
            FROM PO_MTD_SOURCE T
            GROUP BY T.${category}),

        PO_YTD_NORO AS (
                SELECT T.${category},
                        count(decode(t.NORO, 'Others', 1, NULL))    AS "Y_Others",
                        count(decode(t.NORO, 'ON TARGET', 1, NULL)) AS "Y_ON TARGET",
                        count(decode(t.NORO, 'EROC=2~3', 1, NULL))  AS "Y_EROC=2~3",
                        count(decode(t.NORO, 'EROC=4~7', 1, NULL))  AS "Y_EROC=4~7",
                        count(decode(t.NORO, 'EROC=8~14', 1, NULL)) AS "Y_EROC=8~14",
                        count(decode(t.NORO, 'EROC>14', 1, NULL))   AS "Y_EROC>14"
                FROM PO_YTD_SOURCE T
                GROUP BY T.${category}),

        BASE AS (
        SELECT T.${category},
        T."Y_Others",
        T."Y_ON TARGET",
        T."Y_EROC=2~3",
        T."Y_EROC=4~7",
        T."Y_EROC=8~14",
        T."Y_EROC>14",
        T2."M_Others",
        T2."M_ON TARGET",
        T2."M_EROC=2~3",
        T2."M_EROC=4~7",
        T2."M_EROC=8~14",
        T2."M_EROC>14"
        FROM PO_YTD_NORO T
        LEFT JOIN PO_MTD_NORO T2 ON T2.${category} = T.${category}
        ),
        RESULT AS (
        SELECT T.${category} AS "category",
        CASE
        WHEN (T."Y_ON TARGET"+T."Y_EROC=2~3"+T."Y_EROC=4~7"+T."Y_EROC=8~14"+T."Y_EROC>14") = 0 THEN 0
        ELSE ROUND((T."Y_EROC=2~3"+T."Y_EROC=4~7"+T."Y_EROC=8~14"+T."Y_EROC>14") / (T."Y_ON TARGET"+T."Y_EROC=2~3"+T."Y_EROC=4~7"+T."Y_EROC=8~14"+T."Y_EROC>14") * 100, 1) END AS "ytdponoro",
        CASE
        WHEN (T."M_ON TARGET"+T."M_EROC=2~3"+T."M_EROC=4~7"+T."M_EROC=8~14"+T."M_EROC>14") = 0 THEN 0
        ELSE ROUND((T."M_EROC=2~3"+T."M_EROC=4~7"+T."M_EROC=8~14"+T."M_EROC>14") / (T."M_ON TARGET"+T."M_EROC=2~3"+T."M_EROC=4~7"+T."M_EROC=8~14"+T."M_EROC>14") * 100, 1) END AS "mtdponoro"
        FROM BASE T
        UNION ALL
        SELECT 'Total',
        CASE
        WHEN SUM(T."Y_ON TARGET"+T."Y_EROC=2~3"+T."Y_EROC=4~7"+T."Y_EROC=8~14"+T."Y_EROC>14") = 0 THEN 0
        ELSE ROUND((SUM(T."Y_EROC=2~3")+SUM(T."Y_EROC=4~7")+SUM(T."Y_EROC=8~14")+SUM(T."Y_EROC>14")) / (SUM(T."Y_ON TARGET")+SUM(T."Y_EROC=2~3")+SUM(T."Y_EROC=4~7")+SUM(T."Y_EROC=8~14")+SUM(T."Y_EROC>14")) * 100, 1) END AS "ytdponoro",
        CASE
        WHEN SUM(T."M_ON TARGET"+T."M_EROC=2~3"+T."M_EROC=4~7"+T."M_EROC=8~14"+T."M_EROC>14") = 0 THEN 0
        ELSE ROUND((SUM(T."M_EROC=2~3")+SUM(T."M_EROC=4~7")+SUM(T."M_EROC=8~14")+SUM(T."M_EROC>14")) / (SUM(T."M_ON TARGET")+SUM(T."M_EROC=2~3")+SUM(T."M_EROC=4~7")+SUM(T."M_EROC=8~14")+SUM(T."M_EROC>14")) * 100, 1)END AS "mtdponoro"
        FROM BASE T
        )
        SELECT T."category",
        NVL(T."ytdponoro", 0)  AS "ytdPonoro",
        NVL(T."mtdponoro", 0)  AS "mtdPonoro"
        FROM RESULT T
        ORDER BY DECODE(T."category", 'Others', -1, 'Total', -2) DESC
        OFFSET 0 ROWS FETCH NEXT 128 ROWS ONLY
    </select>

    <select id="queryReport2Sub" resultType="java.util.Map">
        WITH PO_NORO_SOURCE AS (
            SELECT /*+ materialize */ T.${category},
        PO_COMPLETION_DATE,
        PO_ITEM_CREATED_DATE,
        CALENDAR_YEAR,
        CALENDAR_MONTH,
        CALENDAR_QUARTER,
        CALENDAR_WEEK,
        POSTPONE_SO_COUNT,
        MATERIAL,
        NOR_COUNT,
        MAX_SO_BOOKED_LINE,
        RESCHEDULE_COUNT,
        RESCHEDULE_IN_COUNT,
        RESCHEDULE_OUT_COUNT,
        PAST_DUE_COUNT,
        RETURN_COUNT,
        PURCH_ORDER_NUMBER,
        PURCH_ORDER_ITEM,
        CLO_SCOPE,
        AMU_ONEMM,
        AMF_ONEMM,
        REORDER_POINT,
        SAFETY_STOCK,
        ACTIVENESS,
        CALCULATED_ABC,
        ORDER_QUANTITY,
        NET_ORDER_VALUE,
        NORO_FLAG AS NORO
        FROM ${SCPA.COMPLETED_PO_NOR_V} T
            WHERE T.PO_COMPLETION_DATE > TO_DATE(SUBSTR(#{report2Week,jdbcType=VARCHAR}, 0, 4) || '/01/01', 'YYYY/MM/DD') - 30
            AND T."${expandColumn}" = #{expandValue, jdbcType=VARCHAR}
            <foreach collection="parent" item="item" index="index">
                AND T."${report1Categories[index]}" = #{item, jdbcType=VARCHAR}
            </foreach>
            <include refid="nor_filter"/>
        ),

        PO_MTD_SOURCE AS (
        SELECT *
        FROM PO_NORO_SOURCE T
        WHERE TRUNC(T.PO_COMPLETION_DATE, 'MM') = (SELECT TRUNC(T.DATE$, 'MM')
        FROM SY_CALENDAR T
        WHERE (T.YEAR || T.WEEK_NO) = #{report2Week,jdbcType=VARCHAR}
        AND T.NAME = 'National Holidays'
        ORDER BY TRUNC(T.DATE$, 'MM')
        FETCH FIRST 1 ROWS ONLY)),

        PO_YTD_SOURCE AS (SELECT *
        FROM PO_NORO_SOURCE T
        WHERE TRUNC(T.PO_COMPLETION_DATE, 'YYYY') = (SELECT TRUNC(T.DATE$, 'YYYY')
        FROM SY_CALENDAR T
        WHERE (T.YEAR || T.WEEK_NO) = #{report2Week,jdbcType=VARCHAR}
        AND T.NAME = 'National Holidays'
        ORDER BY DATE$
        FETCH FIRST 1 ROWS ONLY)),

        PO_MTD_NORO AS (
                SELECT T.${category},
                       count(decode(t.NORO, 'Others', 1, NULL))    AS "M_Others",
                       count(decode(t.NORO, 'ON TARGET', 1, NULL)) AS "M_ON TARGET",
                       count(decode(t.NORO, 'EROC=2~3', 1, NULL))  AS "M_EROC=2~3",
                       count(decode(t.NORO, 'EROC=4~7', 1, NULL))  AS "M_EROC=4~7",
                       count(decode(t.NORO, 'EROC=8~14', 1, NULL)) AS "M_EROC=8~14",
                       count(decode(t.NORO, 'EROC>14', 1, NULL))   AS "M_EROC>14"
        FROM PO_MTD_SOURCE T
        GROUP BY T.${category}),

        PO_YTD_NORO AS (
                SELECT T.${category},
                       count(decode(t.NORO, 'Others', 1, NULL))    AS "Y_Others",
                       count(decode(t.NORO, 'ON TARGET', 1, NULL)) AS "Y_ON TARGET",
                       count(decode(t.NORO, 'EROC=2~3', 1, NULL))  AS "Y_EROC=2~3",
                       count(decode(t.NORO, 'EROC=4~7', 1, NULL))  AS "Y_EROC=4~7",
                       count(decode(t.NORO, 'EROC=8~14', 1, NULL)) AS "Y_EROC=8~14",
                       count(decode(t.NORO, 'EROC>14', 1, NULL))   AS "Y_EROC>14"
                FROM PO_YTD_SOURCE T
                GROUP BY T.${category}),

        BASE AS (
        SELECT T.${category},
        T."Y_Others",
        T."Y_ON TARGET",
        T."Y_EROC=2~3",
        T."Y_EROC=4~7",
        T."Y_EROC=8~14",
        T."Y_EROC>14",
        T2."M_Others",
        T2."M_ON TARGET",
        T2."M_EROC=2~3",
        T2."M_EROC=4~7",
        T2."M_EROC=8~14",
        T2."M_EROC>14"
        FROM PO_YTD_NORO T
        LEFT JOIN PO_MTD_NORO T2 ON T2.${category} = T.${category}
        ),

        RESULT AS (
        SELECT T.${category} AS "category",
        CASE
        WHEN (T."Y_ON TARGET"+T."Y_EROC=2~3"+T."Y_EROC=4~7"+T."Y_EROC=8~14"+T."Y_EROC>14") = 0 THEN 0
        ELSE ROUND((T."Y_EROC=2~3"+T."Y_EROC=4~7"+T."Y_EROC=8~14"+T."Y_EROC>14") / (T."Y_ON TARGET"+T."Y_EROC=2~3"+T."Y_EROC=4~7"+T."Y_EROC=8~14"+T."Y_EROC>14") * 100, 1) END AS "ytdponoro",
        CASE
        WHEN (T."M_ON TARGET"+T."M_EROC=2~3"+T."M_EROC=4~7"+T."M_EROC=8~14"+T."M_EROC>14") = 0 THEN 0
        ELSE ROUND((T."M_EROC=2~3"+T."M_EROC=4~7"+T."M_EROC=8~14"+T."M_EROC>14") / (T."M_ON TARGET"+T."M_EROC=2~3"+T."M_EROC=4~7"+T."M_EROC=8~14"+T."M_EROC>14") * 100, 1) END AS "mtdponoro"
        FROM BASE T
        )
        SELECT T."category",
        NVL(T."ytdponoro", 0)  AS "ytdPonoro",
        NVL(T."mtdponoro", 0)  AS "mtdPonoro"
        FROM RESULT T
        ORDER BY DECODE(T."category", 'Others', -1, 'Total', -2) DESC
    </select>

    <select id="queryReport1Sub" resultType="java.util.Map">
        WITH NOR_SOURCE AS (
        SELECT /*+ materialize */ T.${category},
        SALES_ORDER_NUMBER,
        SALES_ORDER_ITEM,
        GI_DATE,
        RESCH_COUNTER,
        POST_PONE_COUNTER,
        PRE_PONE_COUNTER,
        LOCAL_BU,
        CREATED_DATE,
        CRD_DATE,
        VIP_BU
        FROM ${SCPA.NOR_V} T
        WHERE T.GI_DATE > TO_DATE(SUBSTR(#{report1Week,jdbcType=VARCHAR}, 0, 4) || '/01/01', 'YYYY/MM/DD') - 30
        AND T."${expandColumn}" = #{expandValue, jdbcType=VARCHAR}
        <foreach collection="parent" item="item" index="index">
            AND T."${report1Categories[index]}" = #{item, jdbcType=VARCHAR}
        </foreach>
        <include refid="nor_filter"/>
        ),

        MTD_SOURCE AS (
        SELECT *
        FROM NOR_SOURCE T
        WHERE TRUNC(T.GI_DATE, 'MM') = (SELECT TRUNC(T.DATE$, 'MM')
        FROM SY_CALENDAR T
        WHERE (T.YEAR || T.WEEK_NO) = #{report1Week,jdbcType=VARCHAR}
        AND T.NAME = 'National Holidays'
        ORDER BY TRUNC(T.DATE$, 'MM')
        FETCH FIRST 1 ROWS ONLY)
        ),
        YTD_SOURCE AS (SELECT *
        FROM NOR_SOURCE T
        WHERE TRUNC(T.GI_DATE, 'YYYY') = (SELECT TRUNC(T.DATE$, 'YYYY')
        FROM SY_CALENDAR T
        WHERE (T.YEAR || T.WEEK_NO) = #{report1Week,jdbcType=VARCHAR}
        AND T.NAME = 'National Holidays'
        ORDER BY DATE$
        FETCH FIRST 1 ROWS ONLY)
        ),

        MTD_SONOR AS (
        SELECT T.${category},
        SUM(CASE WHEN RESCH_COUNTER > 0 THEN 1 ELSE 0 END) AS MTDSONOR,
        COUNT(SALES_ORDER_NUMBER)                          AS MTD_SO_TOTAL
        FROM MTD_SOURCE T
        GROUP BY T.${category}),

        YTD_SONOR AS (SELECT T.${category},
        SUM(CASE WHEN RESCH_COUNTER > 0 THEN 1 ELSE 0 END)                        AS YTDSONOR,
        SUM(CASE WHEN RESCH_COUNTER >= 2 AND 5 > RESCH_COUNTER THEN 1 ELSE 0 END) AS RC2,
        SUM(CASE WHEN RESCH_COUNTER >= 5 AND 7 > RESCH_COUNTER THEN 1 ELSE 0 END) AS RC5,
        SUM(CASE WHEN RESCH_COUNTER >= 7 THEN 1 ELSE 0 END)                       AS RC7,
        COUNT(SALES_ORDER_NUMBER)                                                 AS YTD_SO_TOTAL
        FROM YTD_SOURCE T
        GROUP BY T.${category}),

        YTD_SONORO AS (SELECT T.${category},
        SUM(CASE WHEN POST_PONE_COUNTER > 0 THEN 1 ELSE 0 END) AS YTDSONORO,
        COUNT(SALES_ORDER_NUMBER)                              AS YTD_SO_TOTAL_O
        FROM YTD_SOURCE T
        GROUP BY T.${category}),

        BASE AS (
        SELECT T.${category},
        T.YTDSONORO,
        T.YTD_SO_TOTAL_O,
        T2.YTDSONOR,
        T3.MTDSONOR,
        T3.MTD_SO_TOTAL,
        T2.RC2,
        T2.RC5,
        T2.RC7,
        T2.YTD_SO_TOTAL
        FROM YTD_SONORO T
        LEFT JOIN YTD_SONOR T2 ON T2.${category} = T.${category}
        LEFT JOIN MTD_SONOR T3 ON T3.${category} = T.${category}
        ),
        RESULT AS (
        SELECT T.${category} AS "category",
        CASE
        WHEN T.YTD_SO_TOTAL = 0 THEN 0
            ELSE  ROUND(T.YTDSONOR/T.YTD_SO_TOTAL* 100, 1) END      AS "ytdSonor",
        CASE
        WHEN T.MTD_SO_TOTAL = 0 THEN 0
            ELSE  ROUND(T.MTDSONOR/T.MTD_SO_TOTAL* 100, 1) END      AS "mtdSonor",
        CASE
        WHEN T.YTD_SO_TOTAL_O = 0 THEN 0
        ELSE ROUND(T.YTDSONORO / T.YTD_SO_TOTAL_O * 100, 1) END AS "ytdSonoro",
        CASE
        WHEN T.YTD_SO_TOTAL = 0 THEN 0
        ELSE ROUND(T.RC2 / T.YTD_SO_TOTAL * 100, 1) END         AS "ytdsonor2",
        CASE
        WHEN T.YTD_SO_TOTAL = 0 THEN 0
        ELSE ROUND(T.RC5 / T.YTD_SO_TOTAL * 100, 1) END         AS "ytdsonor5",
        CASE
        WHEN T.YTD_SO_TOTAL = 0 THEN 0
        ELSE ROUND(T.RC7 / T.YTD_SO_TOTAL * 100, 1) END         AS "ytdsonor7"
        FROM BASE T
        )
        SELECT T."category",
        NVL(T."ytdSonor", 0)  AS "ytdSonor",
        NVL(T."mtdSonor", 0)  AS "mtdSonor",
        NVL(T."ytdSonoro", 0) AS "ytdSonoro",
        NVL(T."ytdsonor2", 0) AS "ytdSonor2",
        NVL(T."ytdsonor5", 0) AS "ytdSonor5",
        NVL(T."ytdsonor7", 0) AS "ytdSonor7"
        FROM RESULT T
        ORDER BY DECODE(T."category", 'Others', -1, 'Total', -2) DESC
    </select>

    <select id="queryReport3" resultType="java.util.Map">

        WITH BASE AS (SELECT /*+ materialize */ T.${report3Category} AS CATEGORY, ${report3StackBy} AS STACK_BY
        FROM ${SCPA.OPEN_PO_NOR_V} T
        WHERE
         T.DELIVERY_DATE_WEEK = #{report1Week,jdbcType=VARCHAR}
            AND T.${report3Category} !='Others'
        <foreach collection="report1SelectedValues" item="item" index="index" separator="and" open="and">
            T."${report1Categories[index]}" = #{item, jdbcType=VARCHAR}
        </foreach>
        <foreach collection="report2SelectedValues" item="item" index="index" separator="and" open="and">
            T."${report2Categories[index]}" = #{item, jdbcType=VARCHAR}
        </foreach>
        <include refid="nor_filter"/>)
        SELECT CATEGORY, STACK_BY, COUNT(1) AS CNT
        FROM BASE
        GROUP BY CATEGORY, STACK_BY
        ORDER BY CATEGORY DESC


    </select>

    <select id="queryReport3StackOpts" resultType="java.lang.String">
        SELECT CATEGORY
        FROM SCPA.OPEN_PO_NOR_FILTER_V
        GROUP BY CATEGORY
        HAVING COUNT(1) &lt; 100
        ORDER BY CATEGORY
    </select>

    <select id="queryReport4" resultType="java.util.Map">
        WITH TEMP AS (SELECT ${report4Category} AS CATEGORY,
        <choose>
        <when test="report4DateType == 'Week'.toString()">
                T.CALENDAR_WEEK  AS CALENDAR_DATETYPE,
        </when>
            <when test="report4DateType == 'Month'.toString()">
                T.CALENDAR_MONTH AS CALENDAR_DATETYPE,
            </when>
        </choose>
        count(decode(t.NORO_FLAG, 'Others', 1, NULL))    AS "Others",
        count(decode(t.NORO_FLAG, 'ON TARGET', 1, NULL)) AS "ON TARGET",
        count(decode(t.NORO_FLAG, 'EROC=2~3', 1, NULL))  AS "EROC=2~3",
        count(decode(t.NORO_FLAG, 'EROC=4~7', 1, NULL))  AS "EROC=4~7",
        count(decode(t.NORO_FLAG, 'EROC=8~14', 1, NULL)) AS "EROC=8~14",
        count(decode(t.NORO_FLAG, 'EROC>14', 1, NULL))   AS "EROC>14"
        FROM ${SCPA.COMPLETED_PO_NOR_V} T
        WHERE T.CALENDAR_MONTH BETWEEN #{report4DateRange[0],jdbcType=VARCHAR} AND #{report4DateRange[1],jdbcType=VARCHAR}
        <foreach collection="report1SelectedValues" item="item" index="index" separator="and" open="and">
            T."${report1Categories[index]}" = #{item, jdbcType=VARCHAR}
        </foreach>
        <foreach collection="report2SelectedValues" item="item" index="index" separator="and" open="and">
            T."${report2Categories[index]}" = #{item, jdbcType=VARCHAR}
        </foreach>
        <include refid="nor_filter"/>
        GROUP BY ${report4Category},
        <choose>
            <when test="report4DateType == 'Week'.toString()">
                T.CALENDAR_WEEK
            </when>
            <when test="report4DateType == 'Month'.toString()">
                T.CALENDAR_MONTH
            </when>
        </choose>)
        SELECT T.CATEGORY,
        CALENDAR_DATETYPE,
        CASE WHEN (T."ON TARGET"+T."EROC=2~3"+T."EROC=4~7"+T."EROC=8~14"+T."EROC>14") = 0 THEN 0 ELSE
        ROUND((T."EROC=2~3"+T."EROC=4~7"+T."EROC=8~14"+T."EROC>14") / (T."ON TARGET"+T."EROC=2~3"+T."EROC=4~7"+T."EROC=8~14"+T."EROC>14") * 100, 1) END AS PO_NORO
        FROM TEMP T
        ORDER BY
        CALENDAR_DATETYPE,CATEGORY
    </select>
</mapper>
