<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.canvas.dao.IMyStoryCLODao">
    <sql id="clo_filter">
        AND T.CLO_VERSION = '2024'
        <if test="_filters != null and _filters != ''.toString()">
            and ${_filters}
        </if>
        <if test="_filters2 != null and _filters2 != ''.toString()">
            and ${_filters2}
        </if>
    </sql>

    <select id="queryFilterOpts" resultType="java.util.Map">
        SELECT * FROM SCPA.CLO_STRUCTURE_FILTER_V T
        ORDER BY T.CATEGORY,DECODE(T.NAME,'Others', 'zzz', T.NAME)
    </select>

    <select id="queryPivotOpts" resultType="java.lang.String">
        SELECT T.COLUMN_NAME
        FROM USER_TAB_COLS T
        WHERE T.TABLE_NAME = 'CLO_STRUCTURE_HIST'
          AND T.DATA_TYPE = 'VARCHAR2'
          AND T.COLUMN_NAME NOT LIKE '%$%'
          AND T.COLUMN_NAME != 'CLO_VERSION'
        ORDER BY T.COLUMN_NAME
    </select>

    <select id="queryReport1" resultType="java.util.Map">
        WITH CLO_BASE AS (
            SELECT /*+ materialize */
                   T.T0_MATERIAL, T.T0_PLANT_CODE,
                   T.T0_MEET_OR_FAIL,
                   ${category} AS "category"
            FROM ${SCPA.CLO_STRUCTURE_HIST} T
            WHERE T.DATE$ = TO_DATE(#{report1CLODate,jdbcType=VARCHAR}, 'YYYY/MM/DD')
            AND T.${category} IS NOT NULL
            AND T.T0_MEET_OR_FAIL IN ('Meet', 'Fail')
            <include refid="clo_filter"/>
            ),
         OTDS_BASE AS (
            SELECT /*+ parallel(6) */
                   T.MATERIAL,
                   T.PLANT_CODE,
                   SUM(DECODE(ONTIME, '+', 1, 0)) AS ONTIME,
                   SUM(DECODE(ONTIME, '#', 1, 0)) AS DELAY
              FROM SD4_SO_OTDS_V T INNER JOIN CLO_BASE T2 ON T.MATERIAL = T2.T0_MATERIAL AND T.PLANT_CODE = T2.T0_PLANT_CODE
             WHERE T.CREATED_DATE BETWEEN TO_DATE(#{report1SOTimeRange[0], jdbcType=VARCHAR}, 'YYYYMM') AND LAST_DAY(TO_DATE(#{report1SOTimeRange[1], jdbcType=VARCHAR}, 'YYYYMM'))
             GROUP BY T.MATERIAL, T.PLANT_CODE
         ),
         OTDS_BASE2 AS (
            SELECT /*+ parallel(6) */
                   T."category",
                   SUM(T2.ONTIME) ONTIME,
                   SUM(T2.DELAY) DELAY
            FROM CLO_BASE T
                    LEFT JOIN OTDS_BASE T2 ON T.T0_MATERIAL = T2.MATERIAL AND T.T0_PLANT_CODE = T2.PLANT_CODE
            GROUP BY T."category"
         ),
         ORDER_BASE AS (
            SELECT MAX("category") "category",
                   T0_MATERIAL,
                   T0_PLANT_CODE,
                   MAX(T0_MEET_OR_FAIL) T0_MEET_OR_FAIL
              FROM CLO_BASE T
             GROUP BY T.T0_MATERIAL, T0_PLANT_CODE
         ),
         START_POINT_LT AS (SELECT T.T0_MATERIAL, T.T0_PLANT_CODE, T.T0_CLO_LT_WD FROM CLO_STRUCTURE_HIST T WHERE T.DATE$ = TO_DATE(#{report1ReferenceDate,jdbcType=VARCHAR}, 'YYYY/MM/DD') AND T.CLO_VERSION = '2024'),
         COMPARE_POINT_LT AS (SELECT T.T0_MATERIAL, T.T0_PLANT_CODE, T.T0_CLO_LT_WD FROM CLO_STRUCTURE_HIST T WHERE T.DATE$ = TO_DATE(#{report1TargetDate,jdbcType=VARCHAR}, 'YYYY/MM/DD') AND T.CLO_VERSION = '2024'),
         COMPARE_RESULT AS (SELECT T.T0_MATERIAL,
                                   T.T0_PLANT_CODE,
                                   CASE WHEN T.T0_CLO_LT_WD > T2.T0_CLO_LT_WD THEN 'WORSE' ELSE 'BETTER' END AS CHANGE_TYPE
                            FROM START_POINT_LT T
                                     INNER JOIN COMPARE_POINT_LT T2 ON T.T0_MATERIAL = T2.T0_MATERIAL AND T.T0_PLANT_CODE = T2.T0_PLANT_CODE AND T.T0_CLO_LT_WD != T2.T0_CLO_LT_WD),
         ORDER_BASE2 AS (
            SELECT /*+ parallel(6) */
                   T2."category",
                   COUNT(DISTINCT T.MATERIAL) AS MATERIAL_COUNT,
                   SUM(CASE WHEN T2.T0_MEET_OR_FAIL = 'Meet' THEN T.NET_NET_VALUE_RMB END)   AS MEET_VAL,
                   SUM(CASE WHEN T2.T0_MEET_OR_FAIL = 'Fail' THEN T.NET_NET_VALUE_RMB END)   AS FAIL_VAL,
                   COUNT(DISTINCT CASE WHEN T3.CHANGE_TYPE = 'BETTER' THEN T.MATERIAL END)   AS BETTER_VAL,
                   COUNT(DISTINCT CASE WHEN T3.CHANGE_TYPE = 'WORSE' THEN T.MATERIAL END)   AS WORSE_VAL,
                   SUM(T.NET_NET_VALUE_RMB)   AS SO_VALUE,
                   COUNT(T.SALES_ORDER_ITEM)  AS SO_LINES
            FROM SCPA.DEMAND_ORDER_INTAKE_V T INNER JOIN ORDER_BASE T2 ON T.MATERIAL = T2.T0_MATERIAL AND T.PLANT_CODE = T2.T0_PLANT_CODE
                                              LEFT JOIN COMPARE_RESULT T3 ON T.MATERIAL = T3.T0_MATERIAL AND T.PLANT_CODE = T3.T0_PLANT_CODE
            WHERE T.CALENDAR_DATE BETWEEN TO_DATE(#{report1SOTimeRange[0], jdbcType=VARCHAR}, 'YYYYMM') AND LAST_DAY(TO_DATE(#{report1SOTimeRange[1], jdbcType=VARCHAR}, 'YYYYMM'))
            GROUP BY T2."category"
         ),
         BASE AS (
            SELECT NVL(T."category", T2."category") "category",
                   NVL(T.ONTIME, 0)                 ONTIME_LINES,
                   NVL(T.DELAY, 0)                  DELAY_LINES,
                   NVL(T2.SO_LINES, 0)              SO_LINES,
                   NVL(T2.SO_VALUE, 0)              SO_VALUE,
                   NVL(T2.FAIL_VAL, 0)              FAIL_VAL,
                   NVL(T2.MEET_VAL, 0)              MEET_VAL,
                   NVL(T2.BETTER_VAL, 0)            BETTER_VAL,
                   NVL(T2.WORSE_VAL, 0)             WORSE_VAL,
                   NVL(T2.MATERIAL_COUNT, 0)        MATERIAL_COUNT
              FROM OTDS_BASE2 T FULL JOIN ORDER_BASE2 T2 ON T."category" = T2."category"
         )
         SELECT * FROM (
            SELECT T."category",
                   T.MEET_VAL       AS "meetLines",
                   T.FAIL_VAL       AS "failLines",
                   T.BETTER_VAL     AS "better",
                   T.WORSE_VAL      AS "worse",
                   T.ONTIME_LINES   AS "ontimeLines",
                   T.DELAY_LINES    AS "delayLines",
                   T.MATERIAL_COUNT AS "noOfMaterial",
                   T.SO_LINES       AS "noOfLine",
                   T.SO_VALUE       AS "noOfValue"
            FROM BASE T
            UNION ALL
            SELECT 'Total',
                    SUM(T.MEET_VAL),
                    SUM(T.FAIL_VAL),
                    SUM(T.BETTER_VAL),
                    SUM(T.WORSE_VAL),
                    SUM(T.ONTIME_LINES),
                    SUM(T.DELAY_LINES),
                    SUM(T.MATERIAL_COUNT),
                    SUM(T.SO_LINES),
                    SUM(T.SO_VALUE)
            FROM BASE T
        ) T
        ORDER BY DECODE("category", 'Total', 'ZZZZZZZ',
                'MTS >2W', '1', 'MTO >1M', '2', 'MTS &lt;=2W', '3', 'MTO &lt;=1M', '4', 'MTS D+1', '5', 'MTO &lt;=1W', '6',
                "category")
        OFFSET 0 ROWS FETCH NEXT 128 ROWS ONLY
    </select>

    <select id="queryReport1Sub" resultType="java.util.Map">
        WITH CLO_BASE AS (
            SELECT /*+ materialize */
                   T.T0_MATERIAL, T.T0_PLANT_CODE,
                   T.T0_MEET_OR_FAIL,
                   NVL(T.${category}, 'Others') "category"
            FROM ${SCPA.CLO_STRUCTURE_HIST} T
            WHERE T.DATE$ = TO_DATE(#{report1CLODate,jdbcType=VARCHAR}, 'YYYY/MM/DD')
             AND T.${category} IS NOT NULL
             AND T."${expandColumn}" = #{expandValue, jdbcType=VARCHAR}
             AND T.T0_MEET_OR_FAIL IN ('Meet', 'Fail')
            <foreach collection="parent" item="item" index="index">
                AND T."${report1Categories[index]}" = #{item, jdbcType=VARCHAR}
            </foreach>
            <include refid="clo_filter"/>
            ),
         OTDS_BASE AS (
            SELECT /*+ parallel(6) */
                   T.MATERIAL,
                   T.PLANT_CODE,
                   SUM(DECODE(ONTIME, '+', 1, 0)) AS ONTIME,
                   SUM(DECODE(ONTIME, '#', 1, 0)) AS DELAY
              FROM SD4_SO_OTDS_V T INNER JOIN CLO_BASE T2 ON T.MATERIAL = T2.T0_MATERIAL AND T.PLANT_CODE = T2.T0_PLANT_CODE
             WHERE T.CREATED_DATE BETWEEN TO_DATE(#{report1SOTimeRange[0], jdbcType=VARCHAR}, 'YYYYMM') AND LAST_DAY(TO_DATE(#{report1SOTimeRange[1], jdbcType=VARCHAR}, 'YYYYMM'))
             GROUP BY T.MATERIAL, T.PLANT_CODE
         ),
         OTDS_BASE2 AS (
            SELECT /*+ parallel(6) */
                   T."category",
                   SUM(T2.ONTIME) ONTIME,
                   SUM(T2.DELAY) DELAY
            FROM CLO_BASE T
                    LEFT JOIN OTDS_BASE T2 ON T.T0_MATERIAL = T2.MATERIAL AND T.T0_PLANT_CODE = T2.PLANT_CODE
            GROUP BY T."category"
         ),
         ORDER_BASE AS (
            SELECT MAX("category") "category",
                   T0_MATERIAL,
                   T0_PLANT_CODE,
                   MAX(T0_MEET_OR_FAIL) T0_MEET_OR_FAIL
              FROM CLO_BASE T
             GROUP BY T.T0_MATERIAL, T0_PLANT_CODE
         ),
         START_POINT_LT AS (SELECT T.T0_MATERIAL, T.T0_PLANT_CODE, T.T0_CLO_LT_WD FROM CLO_STRUCTURE_HIST T WHERE T.DATE$ = TO_DATE(#{report1ReferenceDate,jdbcType=VARCHAR}, 'YYYY/MM/DD') AND T.CLO_VERSION = '2024'),
         COMPARE_POINT_LT AS (SELECT T.T0_MATERIAL, T.T0_PLANT_CODE, T.T0_CLO_LT_WD FROM CLO_STRUCTURE_HIST T WHERE T.DATE$ = TO_DATE(#{report1TargetDate,jdbcType=VARCHAR}, 'YYYY/MM/DD') AND T.CLO_VERSION = '2024'),
         COMPARE_RESULT AS (SELECT T.T0_MATERIAL,
                                   T.T0_PLANT_CODE,
                                   CASE WHEN T.T0_CLO_LT_WD > T2.T0_CLO_LT_WD THEN 'WORSE' ELSE 'BETTER' END AS CHANGE_TYPE
                            FROM START_POINT_LT T
                                     INNER JOIN COMPARE_POINT_LT T2 ON T.T0_MATERIAL = T2.T0_MATERIAL AND T.T0_PLANT_CODE = T2.T0_PLANT_CODE AND T.T0_CLO_LT_WD != T2.T0_CLO_LT_WD),
         ORDER_BASE2 AS (
            SELECT /*+ parallel(6) */
                   T2."category",
                   COUNT(DISTINCT T.MATERIAL) AS MATERIAL_COUNT,
                   SUM(CASE WHEN T2.T0_MEET_OR_FAIL = 'Meet' THEN T.NET_NET_VALUE_RMB END)   AS MEET_VAL,
                   SUM(CASE WHEN T2.T0_MEET_OR_FAIL = 'Fail' THEN T.NET_NET_VALUE_RMB END)   AS FAIL_VAL,
                   COUNT(DISTINCT CASE WHEN T3.CHANGE_TYPE = 'BETTER' THEN T.MATERIAL END)   AS BETTER_VAL,
                   COUNT(DISTINCT CASE WHEN T3.CHANGE_TYPE = 'WORSE' THEN T.MATERIAL END)   AS WORSE_VAL,
                   SUM(T.NET_NET_VALUE_RMB)   AS SO_VALUE,
                   COUNT(T.SALES_ORDER_ITEM)  AS SO_LINES
            FROM SCPA.DEMAND_ORDER_INTAKE_V T INNER JOIN ORDER_BASE T2 ON T.MATERIAL = T2.T0_MATERIAL AND T.PLANT_CODE = T2.T0_PLANT_CODE
                                              LEFT JOIN COMPARE_RESULT T3 ON T.MATERIAL = T3.T0_MATERIAL AND T.PLANT_CODE = T3.T0_PLANT_CODE
            WHERE T.CALENDAR_DATE BETWEEN TO_DATE(#{report1SOTimeRange[0], jdbcType=VARCHAR}, 'YYYYMM') AND LAST_DAY(TO_DATE(#{report1SOTimeRange[1], jdbcType=VARCHAR}, 'YYYYMM'))
            GROUP BY T2."category"
         ),
         BASE AS (
            SELECT NVL(T."category", T2."category") "category",
                   NVL(T.ONTIME, 0)                 ONTIME_LINES,
                   NVL(T.DELAY, 0)                  DELAY_LINES,
                   NVL(T2.SO_LINES, 0)              SO_LINES,
                   NVL(T2.SO_VALUE, 0)              SO_VALUE,
                   NVL(T2.FAIL_VAL, 0)              FAIL_VAL,
                   NVL(T2.MEET_VAL, 0)              MEET_VAL,
                   NVL(T2.BETTER_VAL, 0)            BETTER_VAL,
                   NVL(T2.WORSE_VAL, 0)             WORSE_VAL,
                   NVL(T2.MATERIAL_COUNT, 0)        MATERIAL_COUNT
              FROM OTDS_BASE2 T FULL JOIN ORDER_BASE2 T2 ON T."category" = T2."category"
         )
        SELECT * FROM (
            SELECT T."category",
                   T.MEET_VAL       AS "meetLines",
                   T.FAIL_VAL       AS "failLines",
                   T.BETTER_VAL     AS "better",
                   T.WORSE_VAL      AS "worse",
                   T.ONTIME_LINES   AS "ontimeLines",
                   T.DELAY_LINES    AS "delayLines",
                   T.MATERIAL_COUNT AS "noOfMaterial",
                   T.SO_LINES       AS "noOfLine",
                   T.SO_VALUE       AS "noOfValue"
            FROM BASE T
            UNION ALL
            SELECT 'Total',
                    SUM(T.MEET_VAL),
                    SUM(T.FAIL_VAL),
                    SUM(T.BETTER_VAL),
                    SUM(T.WORSE_VAL),
                    SUM(T.ONTIME_LINES),
                    SUM(T.DELAY_LINES),
                    SUM(T.MATERIAL_COUNT),
                    SUM(T.SO_LINES),
                    SUM(T.SO_VALUE)
            FROM BASE T
        ) T
        ORDER BY DECODE("category", 'Total', 'ZZZZZZZ',
                'MTS >2W', '1', 'MTO >1M', '2', 'MTS &lt;=2W', '3', 'MTO &lt;=1M', '4', 'MTS D+1', '5', 'MTO &lt;=1W', '6',
                "category")
        OFFSET 0 ROWS FETCH NEXT 128 ROWS ONLY
    </select>

    <select id="queryReport2" resultType="java.util.Map">
        WITH CLO_BASE AS (
            SELECT /*+ materialize */
                   T.T0_MATERIAL, T.T0_PLANT_CODE,
                   T.T0_MEET_OR_FAIL,
                   ${report2Category} AS "category"
            FROM ${SCPA.CLO_STRUCTURE_HIST} T
            WHERE T.DATE$ = TO_DATE(#{report1CLODate,jdbcType=VARCHAR}, 'YYYY/MM/DD')
            <foreach collection="report1SelectedValues" item="item" index="index" separator="and" open="and">
                T."${report1Categories[index]}" = #{item, jdbcType=VARCHAR}
            </foreach>
            AND T.${report2Category} IS NOT NULL
            <include refid="clo_filter"/>
         ),
         OTDS_BASE AS (
            SELECT /*+ parallel(6) */
                   T.MATERIAL,
                   T.PLANT_CODE,
                   SUM(DECODE(ONTIME, '+', 1, 0)) AS ONTIME,
                   SUM(DECODE(ONTIME, '#', 1, 0)) AS DELAY
              FROM SD4_SO_OTDS_V T INNER JOIN CLO_BASE T2 ON T.MATERIAL = T2.T0_MATERIAL AND T.PLANT_CODE = T2.T0_PLANT_CODE
             WHERE T.CREATED_DATE BETWEEN TO_DATE(#{report1SOTimeRange[0], jdbcType=VARCHAR}, 'YYYYMM') AND LAST_DAY(TO_DATE(#{report1SOTimeRange[1], jdbcType=VARCHAR}, 'YYYYMM'))
             GROUP BY T.MATERIAL, T.PLANT_CODE
         ),
         OTDS_BASE2 AS (
            SELECT /*+ parallel(6) */
                   T."category",
                   SUM(T2.ONTIME) ONTIME,
                   SUM(T2.DELAY) DELAY
            FROM CLO_BASE T
                    LEFT JOIN OTDS_BASE T2 ON T.T0_MATERIAL = T2.MATERIAL AND T.T0_PLANT_CODE = T2.PLANT_CODE
            GROUP BY T."category"
         ),
         ORDER_BASE AS (
            SELECT MAX("category") "category",
                   T0_MATERIAL,
                   T0_PLANT_CODE,
                   MAX(T0_MEET_OR_FAIL) T0_MEET_OR_FAIL
              FROM CLO_BASE T
             GROUP BY T.T0_MATERIAL, T0_PLANT_CODE
         ),
         ORDER_BASE2 AS (
            SELECT /*+ parallel(6) */
                   T2."category",
                   COUNT(DISTINCT T.MATERIAL) AS MATERIAL_COUNT,
                   SUM(CASE WHEN T2.T0_MEET_OR_FAIL = 'Meet' THEN T.NET_NET_VALUE_RMB END)   AS MEET_VAL,
                   SUM(CASE WHEN T2.T0_MEET_OR_FAIL = 'Fail' THEN T.NET_NET_VALUE_RMB END)   AS FAIL_VAL,
                   SUM(T.NET_NET_VALUE_RMB)   AS SO_VALUE,
                   COUNT(T.SALES_ORDER_ITEM)  AS SO_LINES
            FROM SCPA.DEMAND_ORDER_INTAKE_V T INNER JOIN ORDER_BASE T2 ON T.MATERIAL = T2.T0_MATERIAL AND T.PLANT_CODE = T2.T0_PLANT_CODE
            WHERE T.CALENDAR_DATE BETWEEN TO_DATE(#{report1SOTimeRange[0], jdbcType=VARCHAR}, 'YYYYMM') AND LAST_DAY(TO_DATE(#{report1SOTimeRange[1], jdbcType=VARCHAR}, 'YYYYMM'))
            GROUP BY T2."category"
         ),
         BASE AS (
            SELECT NVL(T."category", T2."category") "name",
                   NVL(T.ONTIME, 0)                 ONTIME_LINES,
                   NVL(T.DELAY, 0)                  DELAY_LINES,
                   NVL(T2.SO_LINES, 0)              SO_LINES,
                   NVL(T2.SO_VALUE, 0)              SO_VALUE,
                   NVL(T2.FAIL_VAL, 0)              FAIL_VAL,
                   NVL(T2.MEET_VAL, 0)              MEET_VAL,
                   NVL(T2.MATERIAL_COUNT, 0)        MATERIAL_COUNT
              FROM OTDS_BASE2 T FULL JOIN ORDER_BASE2 T2 ON T."category" = T2."category"
         )
         SELECT T."name",
         <choose>
             <when test="report1SelectedLabel == 'NO. OF MATERIAL'.toString()">
                 SUM(T.MATERIAL_COUNT) AS "value"
             </when>
             <when test="report1SelectedLabel == 'NO. OF LINE'.toString()">
                 SUM(T.SO_LINES) AS "value"
             </when>
             <when test="report1SelectedLabel == 'NO. OF VALUE'.toString()">
                 SUM(T.SO_VALUE) AS "value"
             </when>
             <when test="report1SelectedLabel == 'ONTIME LINE'.toString()">
                 SUM(T.ONTIME_LINES) AS "value"
             </when>
             <otherwise>
                SUM(T.MEET_VAL) AS "value"
             </otherwise>
         </choose>
          FROM BASE T GROUP BY T."name" ORDER BY "value" desc
         OFFSET 0 ROWS FETCH NEXT 1024 ROWS ONLY
    </select>

    <select id="queryReport3" resultType="java.util.Map">
        WITH CLO_BASE AS ( /* 找到符合条件的数据 */
           SELECT  T.T0_MATERIAL,
                   T.T0_PLANT_CODE,
                   T.T0_MEET_OR_FAIL,
                   T.T0_CLO_LT_GROUP,
                   T2.TEXT AS XAIXS
            FROM ${SCPA.CLO_STRUCTURE_HIST} T
            <choose>
                <when test="report3DateType == 'Day'.toString()">
                    INNER JOIN (SELECT TT.TEXT, TT.DATE$ FROM SY_CALENDAR TT WHERE TT.NAME = 'National Holidays') T2 ON T.DATE$ = T2.DATE$
                </when>
                <when test="report3DateType == 'Week'.toString()">
                    INNER JOIN (SELECT TT.YEAR || TT.WEEK_NO AS TEXT, MIN(TT.DATE$) DATE$
                                  FROM SY_CALENDAR TT
                                 WHERE TT.NAME = 'National Holidays'
                                 GROUP BY TT.YEAR, TT.WEEK_NO) T2 ON T.DATE$ = T2.DATE$
                </when>
                <when test="report3DateType == 'Month'.toString()">
                    INNER JOIN (SELECT TT.YEAR || TT.MONTH AS TEXT, MIN(TT.DATE$) DATE$
                                  FROM SY_CALENDAR TT
                                 WHERE TT.NAME = 'National Holidays'
                                 GROUP BY TT.YEAR, TT.MONTH) T2 ON T.DATE$ = T2.DATE$
                </when>
            </choose>
            WHERE T.T0_MEET_OR_FAIL IN ('Meet', 'Fail')
              AND T.DATE$ BETWEEN TO_DATE(#{report3DateRange[0], jdbcType=VARCHAR}, 'YYYY/MM/DD') AND TO_DATE(#{report3DateRange[1], jdbcType=VARCHAR}, 'YYYY/MM/DD')
              <include refid="clo_filter"/>
              <if test="report3Values != null">
                  <foreach collection="report3Values" item="item" index="index" separator=" AND " open=" AND ">
                    "${report1Categories[index]}" = #{item, jdbcType=VARCHAR}
                  </foreach>
              </if>)
             /* 找到这些天中创建的order, 并按天求和 */
            SELECT /*+ parallel(12) */
                T.T0_CLO_LT_GROUP,
                T.XAIXS,
                <choose>
                  <when test="report3ValueType =='Net Net Price'.toString()">
                      SUM(CASE WHEN T.T0_MEET_OR_FAIL = 'Meet' THEN T2.NET_NET_VALUE_RMB END)        AS MEET_VAL,
                      SUM(CASE WHEN T.T0_MEET_OR_FAIL = 'Fail' THEN T2.NET_NET_VALUE_RMB END) * -1   AS FAIL_VAL
                  </when>
                  <when test="report3ValueType =='Quantity'.toString()">
                      SUM(CASE WHEN T.T0_MEET_OR_FAIL = 'Meet' THEN T2.ORDER_QUANTITY END)        AS MEET_VAL,
                      SUM(CASE WHEN T.T0_MEET_OR_FAIL = 'Fail' THEN T2.ORDER_QUANTITY END) * -1   AS FAIL_VAL
                  </when>
                  <when test="report3ValueType =='SO Line'.toString()">
                      COUNT(CASE WHEN T.T0_MEET_OR_FAIL = 'Meet' THEN T2.SALES_ORDER_ITEM END)        AS MEET_VAL,
                      COUNT(CASE WHEN T.T0_MEET_OR_FAIL = 'Fail' THEN T2.SALES_ORDER_ITEM END) * -1   AS FAIL_VAL
                  </when>
                  <otherwise>
                      COUNT(DISTINCT CASE WHEN T.T0_MEET_OR_FAIL = 'Meet' THEN T.T0_MATERIAL END)        AS MEET_VAL,
                      COUNT(DISTINCT CASE WHEN T.T0_MEET_OR_FAIL = 'Fail' THEN T.T0_MATERIAL END) * -1   AS FAIL_VAL
                  </otherwise>
                </choose>
            FROM CLO_BASE T
                 LEFT JOIN SCPA.DEMAND_ORDER_INTAKE_V T2 ON T.T0_MATERIAL = T2.MATERIAL AND T.T0_PLANT_CODE = T2.PLANT_CODE
            WHERE T2.CALENDAR_DATE BETWEEN TO_DATE(#{report1SOTimeRange[0], jdbcType=VARCHAR}, 'YYYYMM') AND LAST_DAY(TO_DATE(#{report1SOTimeRange[1], jdbcType=VARCHAR}, 'YYYYMM'))
            GROUP BY T.T0_CLO_LT_GROUP, T.XAIXS
    </select>
</mapper>
