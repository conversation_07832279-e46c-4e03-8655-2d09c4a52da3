<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.canvas.dao.IMyStoryMyCPDao">
    <sql id="mycp_filter">
        <if test="_filters != null and _filters != ''.toString()">
            and ${_filters}
        </if>
        <if test="_filters2 != null and _filters2 != ''.toString()">
            and ${_filters2}
        </if>
    </sql>

    <select id="queryFilterOpts" resultType="java.util.Map">
        SELECT DISTINCT T.CATEGORY, T2.NAME
        FROM SCPA.MYCP_COMMITMENT_FILTER_V T
                 INNER JOIN SCPA.MYCP_RESPONSE_FILTER_V T1 ON T.CATEGORY = T1.CATEGORY
                 LEFT JOIN (SELECT CATEGORY, NAME
                            FROM SCPA.MYCP_COMMITMENT_FILTER_V
                            UNION
                            SELECT CATEGORY, NAME
                            FROM SCPA.MYCP_RESPONSE_FILTER_V) T2 ON T.CATEGORY = T2.CATEGORY
        ORDER BY T.CATEGORY,DECODE(T2.NAME,'Others','zzz',T2.NAME)
    </select>

    <select id="queryPivotOpts" resultType="java.lang.String">
        SELECT T.COLUMN_NAME
        FROM USER_TAB_COLS T
        WHERE T.TABLE_NAME IN( 'MYCP_COMMITMENT_V', 'MYCP_RESPONSE_V')
          AND T.DATA_TYPE = 'VARCHAR2'
          AND T.COLUMN_NAME NOT LIKE '%$%'
          AND T.COLUMN_NAME NOT LIKE '%BU%'
          AND T.COLUMN_NAME NOT IN ('SALES_ORDER_NUMBER', 'SALES_ORDER_ITEM')
        GROUP BY T.COLUMN_NAME HAVING COUNT(1) = 2
        ORDER BY T.COLUMN_NAME
    </select>

    <select id="queryWeekOpts" resultType="java.lang.String">
        SELECT DISTINCT T.YEAR || T.WEEK_NO
        FROM SY_CALENDAR T
        WHERE T.NAME = 'National Holidays'
        AND T.DATE$ BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'YYYY'), -24) AND TRUNC(SYSDATE + 7, 'DD')
        ORDER BY T.YEAR || T.WEEK_NO
    </select>

    <select id="queryCurrentWeek" resultType="java.lang.String">
        SELECT YEAR || WEEK_NO
        FROM SCPA.SY_CALENDAR T
        WHERE DATE$ = TRUNC(SYSDATE, 'DD')
          AND NAME = 'National Holidays'
    </select>

    <select id="queryReport1Header" resultType="java.util.Map">
        SELECT TO_CHAR(T.DATE$, 'YYYY') || 'YTD' AS YTD_HEADER,
               TO_CHAR(T.DATE$, 'Mon-YY', 'NLS_DATE_LANGUAGE=AMERICAN') AS MTD_HEADER,
               SUBSTR(T.YEAR, 3) || 'W' || T.WEEK_NO AS WK_HEADER,
               (SELECT SUBSTR(T.YEAR, 3) || 'W' || T.WEEK_NO
                 FROM SY_CALENDAR T
                WHERE T.YEAR || T.WEEK_NO &lt; #{report1Week,jdbcType=VARCHAR}
                  AND T.NAME = 'National Holidays'
                ORDER BY T.YEAR || T.WEEK_NO DESC
                    FETCH NEXT 1 ROWS ONLY)                             AS WK1_HEADER
        FROM SY_CALENDAR T
        WHERE T.YEAR || T.WEEK_NO = #{report1Week,jdbcType=VARCHAR}
          AND T.NAME = 'National Holidays'
        ORDER BY DATE$
            FETCH NEXT 1 ROWS ONLY
    </select>

    <select id="queryReport1" resultType="java.util.Map">
        WITH RESPONSE_SOURCE AS (
            SELECT /*+ materialize */ NVL(T.${category},'Others') as ${category}, APPLICATION_DATE, MYCP_ONTIME_STATUS, CALENDAR_WEEK, SALES_ORDER_NUMBER, SALES_ORDER_ITEM, MYCP_VIP, IMPORTANCE, BEGIN_PROCESS_DATE, APPLICATION_STATUS
            FROM ${SCPA.MYCP_RESPONSE_V} T
            WHERE T.APPLICATION_DATE > TO_DATE(SUBSTR(#{report1Week,jdbcType=VARCHAR}, 0, 4) || '/01/01', 'YYYY/MM/DD') - 30
            <include refid="mycp_filter"/>
        ),
        RESPONSE_LAST_WEEK AS (
            SELECT T.${category},
                   SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Ontime' AND T.MYCP_VIP = 'Y' THEN 1 ELSE 0 END)  AS VIP_ONTIME_LINE,
                   SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Fail' AND T.MYCP_VIP = 'Y' THEN 1 ELSE 0 END)    AS VIP_DELAY_LINE,
                   SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Ontime' THEN 1 ELSE 0 END) AS ONTIME_LINE,
                   SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Fail' THEN 1 ELSE 0 END)   AS DELAY_LINE
            FROM (SELECT * FROM RESPONSE_SOURCE T
                  WHERE T.CALENDAR_WEEK IN (
                      SELECT TT.CALENDAR_WEEK FROM ${SCPA.MYCP_RESPONSE_V} TT
                      WHERE TT.CALENDAR_WEEK &lt; #{report1Week,jdbcType=VARCHAR}
                      GROUP BY TT.CALENDAR_WEEK
                      ORDER BY TT.CALENDAR_WEEK DESC
                      FETCH NEXT 1 ROWS ONLY))T
            GROUP BY T.${category}
        ),
        RESPONSE_COMPLETION AS (
            SELECT T.${category},
                   SUM(CASE WHEN T.MYCP_VIP = 'Y' THEN 1 ELSE 0 END)   VIP_OPEN_LINE,
                   SUM(CASE WHEN T.MYCP_VIP = 'Y' THEN 0 ELSE 1 END)   NON_VIP_OPEN_LINE
            FROM ${SCPA.MYCP_RESPONSE_V} T
            <where>
                APPLICATION_STATUS = '采购处理中'
                <include refid="mycp_filter"/>
            </where>
            GROUP BY T.${category}
        ),
        RESPONSE_MTD AS (
            SELECT T.${category},
                   SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Ontime' THEN 1 ELSE 0 END) ONTIME_LINE,
                   SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Fail' THEN 1 ELSE 0 END)   DELAY_LINE
            FROM (SELECT *
                  FROM RESPONSE_SOURCE T
                  WHERE TRUNC(T.APPLICATION_DATE, 'MM') = (
                      SELECT TRUNC(T.DATE$, 'MM')
                      FROM SY_CALENDAR T
                      WHERE T.YEAR || T.WEEK_NO = #{report1Week,jdbcType=VARCHAR}
                          AND T.NAME = 'National Holidays'
                      ORDER BY TRUNC(T.DATE$, 'MM')
                      FETCH NEXT 1 ROWS ONLY
                 )) T
            GROUP BY T.${category}
        ),
        RESPONSE_YTD AS (
            SELECT T.${category},
                   SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Ontime' THEN 1 ELSE 0 END) AS ONTIME_LINE,
                   SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Fail' THEN 1 ELSE 0 END)   AS DELAY_LINE
            FROM (SELECT *
                  FROM RESPONSE_SOURCE T
                  WHERE TRUNC(T.APPLICATION_DATE, 'YYYY') = (
                      SELECT TRUNC(T.DATE$, 'YYYY')
                      FROM SY_CALENDAR T
                      WHERE T.YEAR || T.WEEK_NO = #{report1Week,jdbcType=VARCHAR}
                      AND T.NAME = 'National Holidays'
                      ORDER BY DATE$
                      FETCH NEXT 1 ROWS ONLY
            )) T
            GROUP BY T.${category}
        ),
        COMMIT_SOURCE AS (
            SELECT /*+ materialize */ NVL(T.${category},'Others') as ${category}, PURCHASE_EXPECTED_DATE, MYCP_ONTIME_STATUS, CALENDAR_WEEK, SALES_ORDER_NUMBER, SALES_ORDER_ITEM, MYCP_VIP, IMPORTANCE, BEGIN_PROCESS_DATE, APPLICATION_STATUS
            FROM ${SCPA.MYCP_COMMITMENT_V} T
            WHERE T.PURCHASE_EXPECTED_DATE > TO_DATE(SUBSTR(#{report1Week,jdbcType=VARCHAR}, 0, 4) || '/01/01', 'YYYY/MM/DD') - 30
            <include refid="mycp_filter"/>
        ),
        COMMIT_LAST_WEEK AS (
            SELECT T.${category},
                   SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Ontime'  AND T.MYCP_VIP = 'Y' THEN 1 ELSE 0 END) VIP_ONTIME_LINE,
                   SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Fail'  AND T.MYCP_VIP = 'Y' THEN 1 ELSE 0 END)   VIP_DELAY_LINE,
                   SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Ontime' THEN 1 ELSE 0 END) ONTIME_LINE,
                   SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Fail' THEN 1 ELSE 0 END)   DELAY_LINE
            FROM (SELECT * FROM COMMIT_SOURCE T
                  WHERE T.CALENDAR_WEEK IN (
                  SELECT TT.CALENDAR_WEEK FROM ${SCPA.MYCP_COMMITMENT_V} TT
                  WHERE TT.CALENDAR_WEEK &lt; #{report1Week,jdbcType=VARCHAR}
                  GROUP BY TT.CALENDAR_WEEK
                  ORDER BY TT.CALENDAR_WEEK DESC
                  FETCH NEXT 1 ROWS ONLY))T
            GROUP BY T.${category}
        ),
        COMMIT_MTD AS (
            SELECT T.${category},
                   SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Ontime' THEN 1 ELSE 0 END) ONTIME_LINE,
                   SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Fail' THEN 1 ELSE 0 END)   DELAY_LINE
              FROM (SELECT *
                    FROM COMMIT_SOURCE T
                    WHERE TRUNC(T.PURCHASE_EXPECTED_DATE, 'MM') = (
                    SELECT TRUNC(T.DATE$, 'MM')
                    FROM SY_CALENDAR T
                    WHERE T.YEAR || T.WEEK_NO = #{report1Week,jdbcType=VARCHAR}
                    AND T.NAME = 'National Holidays'
                    ORDER BY TRUNC(T.DATE$, 'MM')
                    FETCH NEXT 1 ROWS ONLY
              )) T
             GROUP BY T.${category}
        ),
        COMMIT_YTD AS (
            SELECT T.${category},
                   SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Ontime' THEN 1 ELSE 0 END) ONTIME_LINE,
                   SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Fail' THEN 1 ELSE 0 END)   DELAY_LINE
              FROM (SELECT *
                    FROM COMMIT_SOURCE T
                    WHERE TRUNC(T.PURCHASE_EXPECTED_DATE, 'YYYY') = (
                    SELECT TRUNC(T.DATE$, 'YYYY')
                    FROM SY_CALENDAR T
                    WHERE T.YEAR || T.WEEK_NO = #{report1Week,jdbcType=VARCHAR}
                    AND T.NAME = 'National Holidays'
                    ORDER BY DATE$
                    FETCH NEXT 1 ROWS ONLY
              )) T
             GROUP BY T.${category}
        ),
        BASE AS (
            SELECT /*+ parallel */ T.${category},
                   NVL(T2.VIP_ONTIME_LINE, 0)      AS COMMIT_LW_VIP_ONTIME_LINE,
                   NVL(T2.VIP_DELAY_LINE, 0)       AS COMMIT_LW_VIP_DELAY_LINE,
                   NVL(T2.ONTIME_LINE, 0)          AS COMMIT_LW_ONTIME_LINE,
                   NVL(T2.DELAY_LINE, 0)           AS COMMIT_LW_DELAY_LINE,
                   NVL(T3.ONTIME_LINE, 0)          AS COMMIT_MTD_ONTIME_LINE,
                   NVL(T3.DELAY_LINE, 0)           AS COMMIT_MTD_DELAY_LINE,
                   NVL(T4.ONTIME_LINE, 0)          AS COMMIT_YTD_ONTIME_LINE,
                   NVL(T4.DELAY_LINE, 0)           AS COMMIT_YTD_DELAY_LINE,
                   NVL(T7.VIP_ONTIME_LINE, 0)      AS RESPONSE_LW_VIP_ONTIME_LINE,
                   NVL(T7.VIP_DELAY_LINE, 0)       AS RESPONSE_LW_VIP_DELAY_LINE,
                   NVL(T7.ONTIME_LINE, 0)          AS RESPONSE_LW_ONTIME_LINE,
                   NVL(T7.DELAY_LINE, 0)           AS RESPONSE_LW_DELAY_LINE,
                   NVL(T8.ONTIME_LINE, 0)          AS RESPONSE_MTD_ONTIME_LINE,
                   NVL(T8.DELAY_LINE, 0)           AS RESPONSE_MTD_DELAY_LINE,
                   NVL(T9.ONTIME_LINE, 0)          AS RESPONSE_YTD_ONTIME_LINE,
                   NVL(T9.DELAY_LINE, 0)           AS RESPONSE_YTD_DELAY_LINE,
                   NVL(T10.NON_VIP_OPEN_LINE, 0)   AS RESPONSE_COMPLETION_NON_VIP_OPEN_LINE,
                   NVL(T10.VIP_OPEN_LINE, 0)       AS RESPONSE_COMPLETION_VIP_OPEN_LINE
             FROM (SELECT DISTINCT ${category} FROM COMMIT_SOURCE
                        UNION
                   SELECT DISTINCT ${category} FROM RESPONSE_SOURCE
                  ) t
             LEFT JOIN COMMIT_LAST_WEEK T2 ON T.${category} = T2.${category}
             LEFT JOIN COMMIT_MTD T3 ON T.${category} = T3.${category}
             LEFT JOIN COMMIT_YTD T4 ON T.${category} = T4.${category}
             LEFT JOIN RESPONSE_LAST_WEEK T7 ON T.${category} = T7.${category}
             LEFT JOIN RESPONSE_MTD T8 ON T.${category} = T8.${category}
             LEFT JOIN RESPONSE_YTD T9 ON T.${category} = T9.${category}
             LEFT JOIN RESPONSE_COMPLETION T10 ON T.${category} = T10.${category}
        ),
        RESULT AS (
            SELECT T.${category} AS "category",
                 CASE WHEN T.COMMIT_MTD_ONTIME_LINE + T.COMMIT_MTD_DELAY_LINE = 0 THEN 0 ELSE ROUND(T.COMMIT_MTD_ONTIME_LINE / (T.COMMIT_MTD_ONTIME_LINE + T.COMMIT_MTD_DELAY_LINE) * 100, 1) END                               AS "commitMtd",
                 CASE WHEN T.COMMIT_YTD_ONTIME_LINE + T.COMMIT_YTD_DELAY_LINE = 0 THEN 0 ELSE ROUND(T.COMMIT_YTD_ONTIME_LINE / (T.COMMIT_YTD_ONTIME_LINE + T.COMMIT_YTD_DELAY_LINE) * 100, 1) END                               AS "commitYtd",
                 CASE WHEN T.COMMIT_LW_VIP_ONTIME_LINE + T.COMMIT_LW_VIP_DELAY_LINE = 0 THEN 0 ELSE ROUND(T.COMMIT_LW_VIP_ONTIME_LINE / (T.COMMIT_LW_VIP_ONTIME_LINE + T.COMMIT_LW_VIP_DELAY_LINE) * 100, 1) END                AS "commitLwVip",
                 CASE WHEN T.COMMIT_LW_ONTIME_LINE + T.COMMIT_LW_DELAY_LINE = 0 THEN 0 ELSE ROUND(T.COMMIT_LW_ONTIME_LINE / (T.COMMIT_LW_ONTIME_LINE + T.COMMIT_LW_DELAY_LINE) * 100, 1) END                                    AS "commitLw",
                 T.COMMIT_LW_VIP_ONTIME_LINE                                                                                                                                                                                    AS "commitLwVipOntimeLine",
                 T.COMMIT_LW_VIP_DELAY_LINE                                                                                                                                                                                     AS "commitLwVipDelayLine",
                 T.COMMIT_LW_ONTIME_LINE                                                                                                                                                                                        AS "commitLwOntimeLine",
                 T.COMMIT_LW_DELAY_LINE                                                                                                                                                                                         AS "commitLwDelayLine",
                 CASE WHEN T.RESPONSE_MTD_ONTIME_LINE + T.RESPONSE_MTD_DELAY_LINE = 0 THEN 0 ELSE ROUND(T.RESPONSE_MTD_ONTIME_LINE / (T.RESPONSE_MTD_ONTIME_LINE + T.RESPONSE_MTD_DELAY_LINE) * 100, 1) END                     AS "responseMtd",
                 CASE WHEN T.RESPONSE_YTD_ONTIME_LINE + T.RESPONSE_YTD_DELAY_LINE = 0 THEN 0 ELSE ROUND(T.RESPONSE_YTD_ONTIME_LINE / (T.RESPONSE_YTD_ONTIME_LINE + T.RESPONSE_YTD_DELAY_LINE) * 100, 1) END                     AS "responseYtd",
                 CASE WHEN T.RESPONSE_LW_ONTIME_LINE + T.RESPONSE_LW_DELAY_LINE = 0 THEN 0 ELSE ROUND(T.RESPONSE_LW_ONTIME_LINE / (T.RESPONSE_LW_ONTIME_LINE + T.RESPONSE_LW_DELAY_LINE) * 100, 1) END                          AS "responseLw",
                 CASE WHEN T.RESPONSE_LW_VIP_ONTIME_LINE + T.RESPONSE_LW_VIP_DELAY_LINE = 0 THEN 0 ELSE ROUND(T.RESPONSE_LW_VIP_ONTIME_LINE / (T.RESPONSE_LW_VIP_ONTIME_LINE + T.RESPONSE_LW_VIP_DELAY_LINE) * 100, 1) END      AS "responseLwVip",
                 T.RESPONSE_LW_VIP_ONTIME_LINE                                                                                                                                                                                  AS "responseLwVipOntimeLine",
                 T.RESPONSE_LW_VIP_DELAY_LINE                                                                                                                                                                                   AS "responseLwVipDelayLine",
                 T.RESPONSE_LW_ONTIME_LINE                                                                                                                                                                                      AS "responseLwOntimeLine",
                 T.RESPONSE_LW_DELAY_LINE                                                                                                                                                                                       AS "responseLwDelayLine",
                 T.RESPONSE_COMPLETION_NON_VIP_OPEN_LINE                                                                                                                                                                        AS "responseNonVipIncompleteLine",
                 T.RESPONSE_COMPLETION_VIP_OPEN_LINE                                                                                                                                                                            AS "responseVipIncompleteLine"
            FROM BASE T
            UNION ALL
            SELECT 'Total',
                 CASE WHEN SUM(T.COMMIT_MTD_ONTIME_LINE + T.COMMIT_MTD_DELAY_LINE) = 0 THEN 0 ELSE ROUND(SUM(T.COMMIT_MTD_ONTIME_LINE) / SUM(T.COMMIT_MTD_ONTIME_LINE + T.COMMIT_MTD_DELAY_LINE) * 100, 1) END,
                 CASE WHEN SUM(T.COMMIT_YTD_ONTIME_LINE + T.COMMIT_YTD_DELAY_LINE) = 0 THEN 0 ELSE ROUND(SUM(T.COMMIT_YTD_ONTIME_LINE) / SUM(T.COMMIT_YTD_ONTIME_LINE + T.COMMIT_YTD_DELAY_LINE) * 100, 1) END,
                 CASE WHEN SUM(T.COMMIT_LW_VIP_ONTIME_LINE + T.COMMIT_LW_VIP_DELAY_LINE) = 0 THEN 0 ELSE ROUND(SUM(T.COMMIT_LW_VIP_ONTIME_LINE) / SUM(T.COMMIT_LW_VIP_ONTIME_LINE + T.COMMIT_LW_VIP_DELAY_LINE) * 100, 1) END,
                 CASE WHEN SUM(T.COMMIT_LW_ONTIME_LINE + T.COMMIT_LW_DELAY_LINE) = 0 THEN 0 ELSE ROUND(SUM(T.COMMIT_LW_ONTIME_LINE) / SUM(T.COMMIT_LW_ONTIME_LINE + T.COMMIT_LW_DELAY_LINE) * 100, 1) END,
                 SUM(T.COMMIT_LW_VIP_ONTIME_LINE),
                 SUM(T.COMMIT_LW_VIP_DELAY_LINE),
                 SUM(T.COMMIT_LW_ONTIME_LINE),
                 SUM(T.COMMIT_LW_DELAY_LINE),
                 CASE WHEN SUM(T.RESPONSE_MTD_ONTIME_LINE + T.RESPONSE_MTD_DELAY_LINE) = 0 THEN 0 ELSE ROUND(SUM(T.RESPONSE_MTD_ONTIME_LINE) / SUM(T.RESPONSE_MTD_ONTIME_LINE + T.RESPONSE_MTD_DELAY_LINE) * 100, 1) END,
                 CASE WHEN SUM(T.RESPONSE_YTD_ONTIME_LINE + T.RESPONSE_YTD_DELAY_LINE) = 0 THEN 0 ELSE ROUND(SUM(T.RESPONSE_YTD_ONTIME_LINE) / SUM(T.RESPONSE_YTD_ONTIME_LINE + T.RESPONSE_YTD_DELAY_LINE) * 100, 1) END,
                 CASE WHEN SUM(T.RESPONSE_LW_ONTIME_LINE + T.RESPONSE_LW_DELAY_LINE) = 0 THEN 0 ELSE ROUND(SUM(T.RESPONSE_LW_ONTIME_LINE) / SUM(T.RESPONSE_LW_ONTIME_LINE + T.RESPONSE_LW_DELAY_LINE) * 100, 1) END,
                 CASE WHEN SUM(T.RESPONSE_LW_VIP_ONTIME_LINE + T.RESPONSE_LW_VIP_DELAY_LINE) = 0 THEN 0 ELSE ROUND(SUM(T.RESPONSE_LW_VIP_ONTIME_LINE) / SUM(T.RESPONSE_LW_VIP_ONTIME_LINE + T.RESPONSE_LW_VIP_DELAY_LINE) * 100, 1) END,
                 SUM(RESPONSE_LW_VIP_ONTIME_LINE),
                 SUM(RESPONSE_LW_VIP_DELAY_LINE),
                 SUM(RESPONSE_LW_ONTIME_LINE),
                 SUM(RESPONSE_LW_DELAY_LINE),
                 SUM(RESPONSE_COMPLETION_NON_VIP_OPEN_LINE),
                 SUM(RESPONSE_COMPLETION_VIP_OPEN_LINE)
            FROM BASE T
        )
        SELECT /*+ parallel */ T."category",
               NVL(T."commitMtd", 0)                        AS "commitMtd",
               NVL(T."commitYtd", 0)                        AS "commitYtd",
               NVL(T."commitLw", 0)                         AS "commitLw",
               NVL(T."commitLwVip", 0)                      AS "commitLwVip",
               NVL(T."commitLwOntimeLine", 0)               AS "commitLwOntimeLine",
               NVL(T."commitLwDelayLine", 0)                AS "commitLwDelayLine",
               NVL(T."commitLwVipOntimeLine", 0)            AS "commitLwVipOntimeLine",
               NVL(T."commitLwVipDelayLine", 0)             AS "commitLwVipDelayLine",
               NVL(T."responseMtd", 0)                      AS "responseMtd",
               NVL(T."responseYtd", 0)                      AS "responseYtd",
               NVL(T."responseLw", 0)                       AS "responseLw",
               NVL(T."responseLwVip", 0)                    AS "responseLwVip",
               NVL(T."responseLwOntimeLine", 0)             AS "responseLwOntimeLine",
               NVL(T."responseLwDelayLine", 0)              AS "responseLwDelayLine",
               NVL(T."responseLwVipOntimeLine", 0)          AS "responseLwVipOntimeLine",
               NVL(T."responseLwVipDelayLine", 0)           AS "responseLwVipDelayLine",
               NVL(T."responseNonVipIncompleteLine", 0)     AS "responseNonVipIncompleteLine",
               NVL(T."responseVipIncompleteLine", 0)        AS "responseVipIncompleteLine"
          FROM RESULT T
        ORDER BY DECODE(T."category", 'Others', -1, 'Total', -2, NVL(T."commitLwDelayLine", 0)) DESC
        OFFSET 0 ROWS FETCH NEXT 128 ROWS ONLY
    </select>

    <select id="queryReport1Sub" resultType="java.util.Map">
        WITH RESPONSE_SOURCE AS (
            SELECT /*+ materialize */ T.${category}, APPLICATION_DATE, MYCP_ONTIME_STATUS, CALENDAR_WEEK, SALES_ORDER_NUMBER, SALES_ORDER_ITEM, MYCP_VIP
            FROM ${SCPA.MYCP_RESPONSE_V} T
            WHERE T.APPLICATION_DATE > TO_DATE(SUBSTR(#{report1Week,jdbcType=VARCHAR}, 0, 4) || '/01/01', 'YYYY/MM/DD') - 30
                AND T."${expandColumn}" = #{expandValue, jdbcType=VARCHAR}
                <foreach collection="parent" item="item" index="index">
                    AND T."${report1Categories[index]}" = #{item, jdbcType=VARCHAR}
                </foreach>
            <include refid="mycp_filter"/>
        ),
        RESPONSE_COMPLETION AS (
            SELECT T.${category},
                SUM(CASE WHEN T.MYCP_VIP = 'Y' THEN 1 ELSE 0 END)   VIP_OPEN_LINE,
                SUM(CASE WHEN T.MYCP_VIP = 'Y' THEN 0 ELSE 1 END)   NON_VIP_OPEN_LINE
            FROM ${SCPA.MYCP_RESPONSE_V} T
            <where>
                APPLICATION_STATUS = '采购处理中' AND
                T."${expandColumn}" = #{expandValue, jdbcType=VARCHAR}
                <foreach collection="parent" item="item" index="index">
                    AND T."${report1Categories[index]}" = #{item, jdbcType=VARCHAR}
                </foreach>
                <include refid="mycp_filter"/>
            </where>
            GROUP BY T.${category}
        ),
        RESPONSE_LAST_WEEK_SOURCE AS (
            SELECT *
            FROM RESPONSE_SOURCE T
            WHERE T.CALENDAR_WEEK IN (
                SELECT TT.CALENDAR_WEEK FROM ${SCPA.MYCP_RESPONSE_V} TT
                WHERE TT.CALENDAR_WEEK &lt; #{report1Week,jdbcType=VARCHAR}
                GROUP BY TT.CALENDAR_WEEK
                HAVING COUNT(1) > 10000
                ORDER BY TT.CALENDAR_WEEK DESC
                FETCH NEXT 1 ROWS ONLY
                )
        ),
        RESPONSE_MTD_SOURCE AS (
            SELECT *
            FROM RESPONSE_SOURCE T
            WHERE TRUNC(T.APPLICATION_DATE, 'MM') = (
                SELECT TRUNC(T.DATE$, 'MM')
                FROM SY_CALENDAR T
                WHERE T.YEAR || T.WEEK_NO = #{report1Week,jdbcType=VARCHAR}
                    AND T.NAME = 'National Holidays'
                ORDER BY TRUNC(T.DATE$, 'MM')
                FETCH NEXT 1 ROWS ONLY
            )
        ),
        RESPONSE_YTD_SOURCE AS (
            SELECT *
            FROM RESPONSE_SOURCE T
            WHERE TRUNC(T.APPLICATION_DATE, 'YYYY') = (
                SELECT TRUNC(T.DATE$, 'YYYY')
                FROM SY_CALENDAR T
                WHERE T.YEAR || T.WEEK_NO = #{report1Week,jdbcType=VARCHAR}
                    AND T.NAME = 'National Holidays'
                ORDER BY DATE$
                FETCH NEXT 1 ROWS ONLY
            )
        ),
        RESPONSE_LAST_WEEK AS (
            SELECT T.${category},
                SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Ontime' THEN 1 ELSE 0 END) ONTIME_LINE,
                SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Fail' THEN 1 ELSE 0 END)   DELAY_LINE,
                SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Ontime' AND T.MYCP_VIP = 'Y' THEN 1 ELSE 0 END) VIP_ONTIME_LINE,
                SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Fail' AND T.MYCP_VIP = 'Y' THEN 1 ELSE 0 END)   VIP_DELAY_LINE
            FROM RESPONSE_LAST_WEEK_SOURCE T
            GROUP BY T.${category}
        ),
        RESPONSE_MTD AS (
            SELECT T.${category},
                SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Ontime' THEN 1 ELSE 0 END) ONTIME_LINE,
                SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Fail' THEN 1 ELSE 0 END) DELAY_LINE
            FROM RESPONSE_MTD_SOURCE T
            GROUP BY T.${category}
        ),
        RESPONSE_YTD AS (
            SELECT T.${category},
                    SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Ontime' THEN 1 ELSE 0 END) ONTIME_LINE,
                    SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Fail' THEN 1 ELSE 0 END) DELAY_LINE
            FROM RESPONSE_YTD_SOURCE T
            GROUP BY T.${category}
        ),
        COMMIT_SOURCE AS (
            SELECT /*+ materialize */ T.${category}, PURCHASE_EXPECTED_DATE, MYCP_ONTIME_STATUS, CALENDAR_WEEK, SALES_ORDER_NUMBER, SALES_ORDER_ITEM, MYCP_VIP
            FROM ${SCPA.MYCP_COMMITMENT_V} T
            WHERE T.PURCHASE_EXPECTED_DATE > TO_DATE(SUBSTR(#{report1Week,jdbcType=VARCHAR}, 0, 4) || '/01/01', 'YYYY/MM/DD') - 30
                AND T."${expandColumn}" = #{expandValue, jdbcType=VARCHAR}
                <foreach collection="parent" item="item" index="index">
                    AND T."${report1Categories[index]}" = #{item, jdbcType=VARCHAR}
                </foreach>
                <include refid="mycp_filter"/>
        ),
        COMMIT_LAST_WEEK_SOURCE AS (
            SELECT *
            FROM COMMIT_SOURCE T
            WHERE T.CALENDAR_WEEK IN (
                SELECT TT.CALENDAR_WEEK FROM ${SCPA.MYCP_COMMITMENT_V} TT
                WHERE TT.CALENDAR_WEEK &lt; #{report1Week,jdbcType=VARCHAR}
                GROUP BY TT.CALENDAR_WEEK
                HAVING COUNT(1) > 10000
                ORDER BY TT.CALENDAR_WEEK DESC
                FETCH NEXT 1 ROWS ONLY
            )
        ),
        COMMIT_MTD_SOURCE AS (
            SELECT *
            FROM COMMIT_SOURCE T
            WHERE TRUNC(T.PURCHASE_EXPECTED_DATE, 'MM') = (
                SELECT TRUNC(T.DATE$, 'MM')
                FROM SY_CALENDAR T
                WHERE T.YEAR || T.WEEK_NO = #{report1Week,jdbcType=VARCHAR}
                  AND T.NAME = 'National Holidays'
                ORDER BY TRUNC(T.DATE$, 'MM')
                    FETCH NEXT 1 ROWS ONLY
            )
        ),
        COMMIT_YTD_SOURCE AS (
            SELECT *
            FROM COMMIT_SOURCE T
            WHERE TRUNC(T.PURCHASE_EXPECTED_DATE, 'YYYY') = (
                SELECT TRUNC(T.DATE$, 'YYYY')
                FROM SY_CALENDAR T
                WHERE T.YEAR || T.WEEK_NO = #{report1Week,jdbcType=VARCHAR}
                  AND T.NAME = 'National Holidays'
                ORDER BY DATE$
                    FETCH NEXT 1 ROWS ONLY
            )
        ),
        COMMIT_LAST_WEEK AS (
            SELECT T.${category},
                   SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Ontime' THEN 1 ELSE 0 END) ONTIME_LINE,
                   SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Fail' THEN 1 ELSE 0 END) DELAY_LINE,
                   SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Ontime'  AND T.MYCP_VIP = 'Y' THEN 1 ELSE 0 END) VIP_ONTIME_LINE,
                   SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Fail'  AND T.MYCP_VIP = 'Y' THEN 1 ELSE 0 END) VIP_DELAY_LINE
              FROM COMMIT_LAST_WEEK_SOURCE T
             GROUP BY T.${category}
        ),
        COMMIT_MTD AS (
            SELECT T.${category},
                   SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Ontime' THEN 1 ELSE 0 END) ONTIME_LINE,
                   SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Fail' THEN 1 ELSE 0 END) DELAY_LINE
              FROM COMMIT_MTD_SOURCE T
             GROUP BY T.${category}
        ),
        COMMIT_YTD AS (
            SELECT T.${category},
                   SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Ontime' THEN 1 ELSE 0 END) ONTIME_LINE,
                   SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Fail' THEN 1 ELSE 0 END) DELAY_LINE
              FROM COMMIT_YTD_SOURCE T
             GROUP BY T.${category}
        ),
        BASE AS (
            SELECT T.${category},
                   NVL(T2.VIP_ONTIME_LINE, 0)       AS COMMIT_LW_VIP_ONTIME_LINE,
                   NVL(T2.VIP_DELAY_LINE, 0)        AS COMMIT_LW_VIP_DELAY_LINE,
                   NVL(T2.ONTIME_LINE, 0)           AS COMMIT_LW_ONTIME_LINE,
                   NVL(T2.DELAY_LINE, 0)            AS COMMIT_LW_DELAY_LINE,
                   NVL(T3.ONTIME_LINE, 0)           AS COMMIT_MTD_ONTIME_LINE,
                   NVL(T3.DELAY_LINE, 0)            AS COMMIT_MTD_DELAY_LINE,
                   NVL(T4.ONTIME_LINE, 0)           AS COMMIT_YTD_ONTIME_LINE,
                   NVL(T4.DELAY_LINE, 0)            AS COMMIT_YTD_DELAY_LINE,
                   NVL(T6.VIP_ONTIME_LINE, 0)       AS RESPONSE_LW_VIP_ONTIME_LINE,
                   NVL(T6.VIP_DELAY_LINE, 0)        AS RESPONSE_LW_VIP_DELAY_LINE,
                   NVL(T6.ONTIME_LINE, 0)           AS RESPONSE_LW_ONTIME_LINE,
                   NVL(T6.DELAY_LINE, 0)            AS RESPONSE_LW_DELAY_LINE,
                   NVL(T7.ONTIME_LINE, 0)           AS RESPONSE_MTD_ONTIME_LINE,
                   NVL(T7.DELAY_LINE, 0)            AS RESPONSE_MTD_DELAY_LINE,
                   NVL(T8.ONTIME_LINE, 0)           AS RESPONSE_YTD_ONTIME_LINE,
                   NVL(T8.DELAY_LINE, 0)            AS RESPONSE_YTD_DELAY_LINE,
                   NVL(T9.VIP_OPEN_LINE, 0)         AS RESPONSE_COMPLETION_VIP_OPEN_LINE,
                   NVL(T9.NON_VIP_OPEN_LINE, 0)     AS RESPONSE_COMPLETION_NON_VIP_OPEN_LINE
            FROM (SELECT ${category} FROM RESPONSE_SOURCE UNION SELECT ${category} FROM COMMIT_SOURCE) T
            LEFT JOIN COMMIT_LAST_WEEK T2 ON T.${category} = T2.${category}
            LEFT JOIN COMMIT_MTD T3 ON T.${category} = T3.${category}
            LEFT JOIN COMMIT_YTD T4 ON T.${category} = T4.${category}
            LEFT JOIN RESPONSE_LAST_WEEK T6 ON T.${category} = T6.${category}
            LEFT JOIN RESPONSE_MTD T7 ON T.${category} = T7.${category}
            LEFT JOIN RESPONSE_YTD T8 ON T.${category} = T8.${category}
            LEFT JOIN RESPONSE_COMPLETION T9 ON T.${category} = T9.${category}
        ),
        RESULT AS (
            SELECT T.${category} AS "category",
                 CASE WHEN T.COMMIT_MTD_ONTIME_LINE + T.COMMIT_MTD_DELAY_LINE = 0 THEN 0 ELSE ROUND(T.COMMIT_MTD_ONTIME_LINE / (T.COMMIT_MTD_ONTIME_LINE + T.COMMIT_MTD_DELAY_LINE) * 100, 1) END                                   AS "commitMtd",
                 CASE WHEN T.COMMIT_YTD_ONTIME_LINE + T.COMMIT_YTD_DELAY_LINE = 0 THEN 0 ELSE ROUND(T.COMMIT_YTD_ONTIME_LINE / (T.COMMIT_YTD_ONTIME_LINE + T.COMMIT_YTD_DELAY_LINE) * 100, 1) END                                   AS "commitYtd",
                 CASE WHEN T.COMMIT_LW_ONTIME_LINE + T.COMMIT_LW_DELAY_LINE = 0 THEN 0 ELSE ROUND(T.COMMIT_LW_ONTIME_LINE / (T.COMMIT_LW_ONTIME_LINE + T.COMMIT_LW_DELAY_LINE) * 100, 1) END                                        AS "commitLw",
                 CASE WHEN T.COMMIT_LW_VIP_ONTIME_LINE + T.COMMIT_LW_VIP_DELAY_LINE = 0 THEN 0 ELSE ROUND(T.COMMIT_LW_VIP_ONTIME_LINE / (T.COMMIT_LW_VIP_ONTIME_LINE + T.COMMIT_LW_VIP_DELAY_LINE) * 100, 1) END                    AS "commitLwVip",
                 T.COMMIT_LW_ONTIME_LINE                                                                                                                                                                                            AS "commitLwOntimeLine",
                 T.COMMIT_LW_DELAY_LINE                                                                                                                                                                                             AS "commitLwDelayLine",
                 T.COMMIT_LW_VIP_ONTIME_LINE                                                                                                                                                                                        AS "commitLwVipOntimeLine",
                 T.COMMIT_LW_VIP_DELAY_LINE                                                                                                                                                                                         AS "commitLwVipDelayLine",
                 CASE WHEN T.RESPONSE_MTD_ONTIME_LINE + T.RESPONSE_MTD_DELAY_LINE = 0 THEN 0 ELSE ROUND(T.RESPONSE_MTD_ONTIME_LINE / (T.RESPONSE_MTD_ONTIME_LINE + T.RESPONSE_MTD_DELAY_LINE) * 100, 1) END                         AS "responseMtd",
                 CASE WHEN T.RESPONSE_YTD_ONTIME_LINE + T.RESPONSE_YTD_DELAY_LINE = 0 THEN 0 ELSE ROUND(T.RESPONSE_YTD_ONTIME_LINE / (T.RESPONSE_YTD_ONTIME_LINE + T.RESPONSE_YTD_DELAY_LINE) * 100, 1) END                         AS "responseYtd",
                 CASE WHEN T.RESPONSE_LW_ONTIME_LINE + T.RESPONSE_LW_DELAY_LINE = 0 THEN 0 ELSE ROUND(T.RESPONSE_LW_ONTIME_LINE / (T.RESPONSE_LW_ONTIME_LINE + T.RESPONSE_LW_DELAY_LINE) * 100, 1) END                              AS "responseLw",
                 CASE WHEN T.RESPONSE_LW_VIP_ONTIME_LINE + T.RESPONSE_LW_VIP_DELAY_LINE = 0 THEN 0 ELSE ROUND(T.RESPONSE_LW_VIP_ONTIME_LINE / (T.RESPONSE_LW_VIP_ONTIME_LINE + T.RESPONSE_LW_VIP_DELAY_LINE) * 100, 1) END          AS "responseLwVip",
                 T.RESPONSE_LW_ONTIME_LINE                                                                                                                                                                                          AS "responseLwOntimeLine",
                 T.RESPONSE_LW_DELAY_LINE                                                                                                                                                                                           AS "responseLwDelayLine",
                 T.RESPONSE_LW_VIP_ONTIME_LINE                                                                                                                                                                                      AS "responseLwVipOntimeLine",
                 T.RESPONSE_LW_VIP_DELAY_LINE                                                                                                                                                                                       AS "responseLwVipDelayLine",
                 T.RESPONSE_COMPLETION_NON_VIP_OPEN_LINE                                                                                                                                                                            AS "responseNonVipIncompleteLine",
                 T.RESPONSE_COMPLETION_VIP_OPEN_LINE                                                                                                                                                                                AS "responseVipIncompleteLine"
            FROM BASE T
        )
        SELECT T."category",
               NVL(T."commitMtd", 0)                    AS "commitMtd",
               NVL(T."commitYtd", 0)                    AS "commitYtd",
               NVL(T."commitLw", 0)                     AS "commitLw",
               NVL(T."commitLwVip", 0)                  AS "commitLwVip",
               NVL(T."commitLwOntimeLine", 0)           AS "commitLwOntimeLine",
               NVL(T."commitLwDelayLine", 0)            AS "commitLwDelayLine",
               NVL(T."commitLwVipOntimeLine", 0)        AS "commitLwVipOntimeLine",
               NVL(T."commitLwVipDelayLine", 0)         AS "commitLwVipDelayLine",
               NVL(T."responseMtd", 0)                  AS "responseMtd",
               NVL(T."responseYtd", 0)                  AS "responseYtd",
               NVL(T."responseLw", 0)                   AS "responseLw",
               NVL(T."responseLwVip", 0)                AS "responseLwVip",
               NVL(T."responseLwOntimeLine", 0)         AS "responseLwOntimeLine",
               NVL(T."responseLwDelayLine", 0)          AS "responseLwDelayLine",
               NVL(T."responseLwVipOntimeLine", 0)      AS "responseLwVipOntimeLine",
               NVL(T."responseLwVipDelayLine", 0)       AS "responseLwVipDelayLine",
               NVL(T."responseNonVipIncompleteLine", 0) AS "responseNonVipIncompleteLine",
               NVL(T."responseVipIncompleteLine", 0)    AS "responseVipIncompleteLine"
          FROM RESULT T
        ORDER BY DECODE(T."category", 'Others', -1, 'Total', -2, NVL(T."commitLwDelayLine", 0)) DESC
        OFFSET 0 ROWS FETCH NEXT 128 ROWS ONLY
    </select>

    <select id="queryReport2" resultType="java.util.Map">
        WITH BASE AS (SELECT /*+ materialize */ T.${report2Category} AS CATEGORY, ${report2StackBy} AS STACK_BY
                        <choose>
                            <when test="report1SelectedType == 'MyCP Commitment'.toString()">
                                FROM ${SCPA.MYCP_COMMITMENT_V} T
                                WHERE MYCP_ONTIME_STATUS = 'Fail'
                                <choose>
                                    <when test="report2DateRange.size() == 0">
                                        AND T.CALENDAR_WEEK = #{report1Week,jdbcType=VARCHAR}
                                    </when>
                                    <otherwise>
                                        AND TRUNC(T.PURCHASE_EXPECTED_DATE, 'DD') BETWEEN TO_DATE(#{report2DateRange[0],jdbcType=VARCHAR}, 'YYYY/MM/DD') AND TO_DATE(#{report2DateRange[1],jdbcType=VARCHAR}, 'YYYY/MM/DD')
                                    </otherwise>
                                </choose>
                            </when>
                            <otherwise>
                                FROM ${SCPA.MYCP_RESPONSE_V} T
                                WHERE MYCP_ONTIME_STATUS = 'Fail'
                                <choose>
                                    <when test="report2DateRange.size() == 0">
                                        AND T.CALENDAR_WEEK = #{report1Week,jdbcType=VARCHAR}
                                    </when>
                                    <otherwise>
                                        AND TRUNC(T.APPLICATION_DATE, 'DD') BETWEEN TO_DATE(#{report2DateRange[0],jdbcType=VARCHAR}, 'YYYY/MM/DD') AND TO_DATE(#{report2DateRange[1],jdbcType=VARCHAR}, 'YYYY/MM/DD')
                                    </otherwise>
                                </choose>
                            </otherwise>
                        </choose>
                        <foreach collection="report1SelectedValues" item="item" index="index" separator="and" open="and">
                            T."${report1Categories[index]}" = #{item, jdbcType=VARCHAR}
                        </foreach>
                        <include refid="mycp_filter"/>),
        TOP20 AS (SELECT CATEGORY
                    FROM BASE
                    GROUP BY CATEGORY
                    ORDER BY COUNT(1) DESC
                    OFFSET ${report2TopStart} ROWS FETCH NEXT ${report2TopEnd} - ${report2TopStart} ROWS ONLY)
        SELECT T.CATEGORY, T.STACK_BY, COUNT(1) CNT
        FROM BASE T
        INNER JOIN TOP20 ON T.CATEGORY = TOP20.CATEGORY
        GROUP BY T.CATEGORY, T.STACK_BY
    </select>

    <select id="queryReport2StackOpts" resultType="java.lang.String">
        SELECT CATEGORY
        FROM SCPA.MYCP_COMMITMENT_FILTER_V
        GROUP BY CATEGORY
        HAVING COUNT(1) &lt; 100
        ORDER BY CATEGORY
    </select>

    <select id="queryReport3" resultType="java.util.Map">
        WITH TEMP AS (SELECT ${report3Category} AS                           CATEGORY,
                             T.CALENDAR_WEEK,
                             SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Ontime' THEN 1 ELSE 0 END) ONTIME_LINE,
                             SUM(CASE WHEN T.MYCP_ONTIME_STATUS = 'Fail' THEN 1 ELSE 0 END) DELAY_LINE
                      <choose>
                          <when test="report1SelectedType == 'MyCP Commitment'.toString()">
                              FROM ${SCPA.MYCP_COMMITMENT_V} T
                              WHERE TRUNC(T.PURCHASE_EXPECTED_DATE, 'DD') BETWEEN TO_DATE(#{report3DateRange[0],jdbcType=VARCHAR}, 'YYYY/MM/DD') AND TO_DATE(#{report3DateRange[1],jdbcType=VARCHAR}, 'YYYY/MM/DD')
                          </when>
                          <otherwise>
                              FROM ${SCPA.MYCP_RESPONSE_V} T
                              WHERE TRUNC(T.APPLICATION_DATE, 'DD') BETWEEN TO_DATE(#{report3DateRange[0],jdbcType=VARCHAR}, 'YYYY/MM/DD') AND TO_DATE(#{report3DateRange[1],jdbcType=VARCHAR}, 'YYYY/MM/DD')
                          </otherwise>
                      </choose>
                      <foreach collection="report1SelectedValues" item="item" index="index" separator="and" open="and">
                          T."${report1Categories[index]}" = #{item, jdbcType=VARCHAR}
                      </foreach>
                      <include refid="mycp_filter"/>
                      GROUP BY ${report3Category}, T.CALENDAR_WEEK)
        SELECT T.CATEGORY,
               T.CALENDAR_WEEK,
               T.ONTIME_LINE,
               T.DELAY_LINE,
               <choose>
                   <when test="report3ValueType == 'FAILED_LINES'">
                       T.DELAY_LINE AS ONTIME_RATE
                   </when>
                   <otherwise>
                       ROUND(CASE WHEN T.DELAY_LINE + T.ONTIME_LINE = 0 THEN 0 ELSE T.ONTIME_LINE / (T.DELAY_LINE + T.ONTIME_LINE) END * 100, 1) AS ONTIME_RATE
                   </otherwise>
               </choose>
        FROM TEMP T
        ORDER BY CALENDAR_WEEK, CATEGORY
    </select>
</mapper>
