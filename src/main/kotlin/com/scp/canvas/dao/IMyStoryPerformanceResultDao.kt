package com.scp.canvas.dao

import com.scp.canvas.bean.MyStoryPerformanceResultBean
import org.apache.ibatis.annotations.Mapper

@Mapper
interface IMyStoryPerformanceResultDao {
    fun queryReport1OTDS(parameterMap: MutableMap<String, Any>): ArrayList<MyStoryPerformanceResultBean>

    fun queryReport1OTDSFailLines(parameterMap: MutableMap<String, Any>): ArrayList<MyStoryPerformanceResultBean>

    fun queryReport1ByManualInput(parameterMap: MutableMap<String, Any>): MyStoryPerformanceResultBean

    fun queryReport1OTDM(parameterMap: MutableMap<String, Any>): ArrayList<MyStoryPerformanceResultBean>

    fun queryReport1OTDMFailLines(parameterMap: MutableMap<String, Any>): ArrayList<MyStoryPerformanceResultBean>

    fun queryReport1CLO(parameterMap: MutableMap<String, Any>): ArrayList<MyStoryPerformanceResultBean>

    fun queryReport1PONORO(parameterMap: MutableMap<String, Any>): ArrayList<MyStoryPerformanceResultBean>

    fun queryReport1SONOR(parameterMap: MutableMap<String, Any>): ArrayList<MyStoryPerformanceResultBean>

    fun queryReport1OTC(parameterMap: MutableMap<String, Any>): ArrayList<MyStoryPerformanceResultBean>

    fun queryReport1MyCP(parameterMap: MutableMap<String, Any>): ArrayList<MyStoryPerformanceResultBean>
}
