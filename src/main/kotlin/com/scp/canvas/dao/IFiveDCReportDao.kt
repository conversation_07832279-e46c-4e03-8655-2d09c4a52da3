package com.scp.canvas.dao

import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param

@Mapper
interface IFiveDCReportDao {
    fun queryFilters(): List<Map<String, String>>

    fun queryPageAdmin(@Param("userid") userid: String?, @Param("parentCode") parentCode: String?): Int

    fun queryReport1ColumnOpts(): List<Map<String, String>>

    fun queryReport1AllMatchedList(parameterMap: MutableMap<String, Any>): List<Map<String, String>>

    fun queryReport1Table(parameterMap: MutableMap<String, Any>): MutableList<LinkedHashMap<String, Any>>

    fun queryReport1TableCount(parameterMap: MutableMap<String, Any>): Int

    fun queryReport1TreeTable(parameterMap: MutableMap<String, Any>): MutableList<LinkedHashMap<String, Any>>

    fun queryReport1TreemapChart(parameterMap: MutableMap<String, Any>): MutableList<LinkedHashMap<String, Any?>>

    fun queryReport1TreeTableSub(parameterMap: MutableMap<String, Any>): MutableList<LinkedHashMap<String, Any>>

    fun queryReport1TableDetailsTableName(parameterMap: MutableMap<String, Any>): String

    fun queryReport1TableDetailsCount(parameterMap: MutableMap<String, Any>): Int

    fun queryReport1TableDetails(parameterMap: MutableMap<String, Any>): MutableList<LinkedHashMap<String, Any>>

    fun queryReport1PieChart(parameterMap: MutableMap<String, Any>): MutableList<HashMap<String, Any>>

    fun saveReport1Comments(parameterMap: MutableMap<String, Any>)

    fun queryReport2(parameterMap: MutableMap<String, Any>): MutableList<LinkedHashMap<String, Any>>
}

