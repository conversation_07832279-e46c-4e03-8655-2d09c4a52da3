package com.scp.canvas.dao

import com.scp.toolbox.bean.TreeData
import org.apache.ibatis.annotations.Mapper

@Mapper
interface IMyStoryDao {

    fun queryPageList(parameterMap: MutableMap<String, Any>): List<String>

    fun queryComments(parameterMap: MutableMap<String, Any>): List<MutableMap<String, Any>>

    fun queryReplies(comments: List<MutableMap<String, Any>>): List<MutableMap<String, Any>>

    fun deleteComment(parameterMap: MutableMap<String, Any>)

    fun deleteReply(parameterMap: MutableMap<String, Any>)

    fun sendComment(parameterMap: MutableMap<String, Any>)

    fun queryCommentById(parameterMap: MutableMap<String, Any>): String

    fun queryStoryNameById(parameterMap: MutableMap<String, Any>): String

    fun modifyComment(parameterMap: MutableMap<String, Any>)

    fun sendReply(parameterMap: MutableMap<String, Any>)

    fun saveLogs(parameterMap: MutableMap<String, Any>)

    fun queryChangeLogsCount(parameterMap: MutableMap<String, Any>): Int

    fun queryChangeLogs(parameterMap: MutableMap<String, Any>): List<MutableMap<String, Any>>

    fun queryPageAdmin(userid: String, parentCode: String): Int

    fun queryMyVariantList(parameterMap: MutableMap<String, Any>): List<TreeData?>

    fun querySharedVariantList(parameterMap: MutableMap<String, Any>): List<TreeData?>

    fun queryAllVariantList(parameterMap: MutableMap<String, Any>): List<TreeData?>

    fun querySharedTo(): List<Map<String, Any>>

    fun queryExistsGroup(): List<String>

    fun saveSharedTo(parameterMap: Map<String, Any>)

    fun saveVariant(parameterMap: Map<String, Any>)

    fun saveBuiltInPages(parameterMap: Map<String, Any>)

    fun queryVariantByKey(parameterMap: Map<String, Any>): String

    fun deleteVariant(parameterMap: Map<String, Any>)

    fun queryVariantConfigByKey(parameterMap: Map<String, Any>): MutableMap<String, Any>

    fun querySharedToByKey(parameterMap: Map<String, Any>): List<String>

    fun modifySharedTo(parameterMap: Map<String, Any>)

    fun modifyVariant(parameterMap: Map<String, Any>)

    fun queryConditionById(parameterMap: Map<String, Any>): Map<String, Any>?

    fun queryDefaultCondition(parameterMap: Map<String, Any>): Map<String, Any>?

    fun updateDefaultVariant(parameterMap: Map<String, Any>)

    fun deleteDefaultVariant(parameterMap: Map<String, Any>): Int

    fun queryManualInputById(parameterMap: Map<String, Any>): Map<String, Any>?

    fun queryCommentCntByVariantId(parameterMap: Map<String, Any>): Int
}

