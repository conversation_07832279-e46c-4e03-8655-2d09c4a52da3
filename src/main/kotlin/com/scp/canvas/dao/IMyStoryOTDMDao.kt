package com.scp.canvas.dao

import org.apache.ibatis.annotations.Mapper

@Mapper
interface IMyStoryOTDMDao {

    fun queryFilterOpts(): List<Map<String, String>>

    fun queryPivotOpts(): List<String>

    fun queryStackOpts(): List<String>

    fun queryWeekOpts(): List<String>

    fun queryCurrentWeek(): String

    fun queryReport1(parameterMap: MutableMap<String, Any>): List<MutableMap<String, Any>>

    fun queryReport1Header(parameterMap: MutableMap<String, Any>): MutableMap<String, Any>

    fun queryReport1Sub(parameterMap: MutableMap<String, Any>): List<MutableMap<String, Any>>

    fun queryReport2(parameterMap: MutableMap<String, Any>): List<MutableMap<String, Any>>

    fun queryReport3(parameterMap: MutableMap<String, Any>): List<MutableMap<String, Any>>
}
