package com.scp.canvas.bean

import com.fasterxml.jackson.annotation.JsonInclude
import java.math.BigDecimal

@JsonInclude(JsonInclude.Include.NON_NULL)
class FiveDCTreemapBean {

    var name: String? = null
    var value: BigDecimal? = null
    var children = ArrayList<FiveDCTreemapBean>()

    fun hasChildren(): <PERSON><PERSON><PERSON> {
        return this.children.isNotEmpty()
    }

    // 合并两个节点
    fun add(addElement: FiveDCTreemapBean) {
        var addElementPointer = addElement
        var mainElement: FiveDCTreemapBean = this

        // 相加子节点
        while (addElementPointer.hasChildren()) {
            val mainChildren = mainElement.children
            val child = addElementPointer.children[0] // 加数节点只有一个子节点

            val beanOpt = mainChildren.stream().filter { b: FiveDCTreemapBean -> b.name == child.name }.findFirst()
            if (beanOpt.isPresent) {
                val bean = beanOpt.get()
                // 向下移动一层
                addElementPointer = child
                mainElement = bean
            } else {
                mainChildren.add(child) // 如果找不到子节点, 那直接把需要相加的节点附在这个子节点下面
                break // 然后直接跳出循环, 相加结束
            }
        }
    }
}
