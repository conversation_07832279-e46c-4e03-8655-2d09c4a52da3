package com.scp.canvas.bean

import com.fasterxml.jackson.annotation.JsonInclude
import com.starter.utils.Utils
import java.math.BigDecimal

@JsonInclude(JsonInclude.Include.NON_NULL)
class MyStoryPerformanceResultBean {
    var id: String? = Utils.randomStr(6)
    var rowType: String? = "detail"
    var value1: String? = null
    var value2: BigDecimal? = null
    var value3: BigDecimal? = null
    var arrow: String? = null
    var arrowColor: String? = null

    var children: MutableList<MyStoryPerformanceResultBean> = ArrayList()
}
