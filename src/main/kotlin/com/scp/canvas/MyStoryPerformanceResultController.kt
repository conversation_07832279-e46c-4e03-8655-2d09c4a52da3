package com.scp.canvas

import com.scp.canvas.service.MyStoryPerformanceResultService
import com.scp.canvas.service.MyStoryService
import com.starter.context.bean.Response
import com.starter.context.bean.SchneiderRequestMapping
import com.starter.context.servlet.ControllerHelper
import jakarta.annotation.Resource
import jakarta.servlet.http.HttpServletRequest
import org.springframework.context.annotation.Scope
import org.springframework.web.bind.annotation.CrossOrigin
import org.springframework.web.bind.annotation.RestController

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = ["/canvas/my_story_performance_result"], parent = MyStoryService.PARENT_CODE)
class MyStoryPerformanceResultController : ControllerHelper() {

    @Resource
    lateinit var myStoryPerformanceResultService: MyStoryPerformanceResultService

    @SchneiderRequestMapping("/query_report1")
    fun queryReport1(request: HttpServletRequest): Response {
        super.pageLoad(request)
        super.setGlobalCache(true)
        return myStoryPerformanceResultService.queryReport1(parameterMap)
    }
}
