package com.scp.canvas

import com.scp.canvas.service.MyStoryNORService
import com.scp.canvas.service.MyStoryService
import com.starter.context.bean.Response
import com.starter.context.bean.SchneiderRequestMapping
import com.starter.context.servlet.ControllerHelper
import jakarta.annotation.Resource
import jakarta.servlet.http.HttpServletRequest
import org.springframework.context.annotation.Scope
import org.springframework.web.bind.annotation.CrossOrigin
import org.springframework.web.bind.annotation.RestController

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = ["/canvas/my_story_nor"], parent = MyStoryService.PARENT_CODE)
class MyStoryNORController : ControllerHelper() {

    @Resource
    lateinit var myStoryNORService: MyStoryNORService

    @SchneiderRequestMapping("/init_page")
    fun initPage(request: HttpServletRequest): Response {
        super.pageLoad(request)
        super.setGlobalCache(true)
        return myStoryNORService.initPage()
    }

    @SchneiderRequestMapping("/query_report1")
    fun queryReport1(request: HttpServletRequest): Response {
        super.pageLoad(request)
        super.setGlobalCache(true)
        return myStoryNORService.queryReport1(parameterMap)
    }

    @SchneiderRequestMapping("/query_report1_sub")
    fun queryReport1Sub(request: HttpServletRequest): Response {
        super.pageLoad(request)
        super.setGlobalCache(true)
        return myStoryNORService.queryReport1Sub(parameterMap)
    }

    @SchneiderRequestMapping("/query_report2")
    fun queryReport2(request: HttpServletRequest): Response {
        super.pageLoad(request)
        super.setGlobalCache(true)
        return myStoryNORService.queryReport2(parameterMap)
    }

    @SchneiderRequestMapping("/query_report2_sub")
    fun queryReport2Sub(request: HttpServletRequest): Response {
        super.pageLoad(request)
        super.setGlobalCache(true)
        return myStoryNORService.queryReport2Sub(parameterMap)
    }

    @SchneiderRequestMapping("/query_report3")
    fun queryReport3(request: HttpServletRequest): Response {
        super.pageLoad(request)
        super.setGlobalCache(true)
        return myStoryNORService.queryReport3(parameterMap)
    }

    @SchneiderRequestMapping("/query_report4")
    fun queryReport4(request: HttpServletRequest): Response {
        super.pageLoad(request)
        super.setGlobalCache(true)
        return myStoryNORService.queryReport4(parameterMap)
    }
}
