package com.scp.canvas

import com.scp.canvas.service.MyStoryBOLService
import com.scp.canvas.service.MyStoryService
import com.starter.context.bean.Response
import com.starter.context.bean.SchneiderRequestMapping
import com.starter.context.servlet.ControllerHelper
import jakarta.annotation.Resource
import jakarta.servlet.http.HttpServletRequest
import org.springframework.context.annotation.Scope
import org.springframework.web.bind.annotation.CrossOrigin
import org.springframework.web.bind.annotation.RestController

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = ["/canvas/my_story_bol"], parent = MyStoryService.PARENT_CODE)
class MyStoryBOLController : ControllerHelper() {

    @Resource
    lateinit var myStoryBOLService: MyStoryBOLService

    @SchneiderRequestMapping("/init_page")
    fun initPage(request: HttpServletRequest): Response {
        super.pageLoad(request)
        super.setGlobalCache(true)
        return myStoryBOLService.initPage()
    }

    @SchneiderRequestMapping("/query_report1")
    fun queryReport1(request: HttpServletRequest): Response {
        super.pageLoad(request)
        super.setGlobalCache(true)
        return myStoryBOLService.queryReport1(parameterMap)
    }

    @SchneiderRequestMapping("/query_report1_sub")
    fun queryReport1Sub(request: HttpServletRequest): Response {
        super.pageLoad(request)
        super.setGlobalCache(true)
        return myStoryBOLService.queryReport1Sub(parameterMap)
    }

    @SchneiderRequestMapping("/query_report2")
    fun queryReport2(request: HttpServletRequest): Response {
        super.pageLoad(request)
        super.setGlobalCache(true)
        return myStoryBOLService.queryReport2(parameterMap)
    }

    @SchneiderRequestMapping("/query_report3")
    fun queryReport3(request: HttpServletRequest): Response {
        super.pageLoad(request)
        super.setGlobalCache(true)
        return myStoryBOLService.queryReport3(parameterMap)
    }
}
