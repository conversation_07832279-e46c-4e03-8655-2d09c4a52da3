package com.scp.canvas

import com.scp.canvas.service.FiveDCReportService
import com.starter.context.bean.Response
import com.starter.context.bean.SchneiderRequestMapping
import com.starter.context.servlet.ControllerHelper
import jakarta.annotation.Resource
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.springframework.context.annotation.Scope
import org.springframework.web.bind.annotation.CrossOrigin
import org.springframework.web.bind.annotation.RestController

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = ["/canvas/five_dc_report"], parent = FiveDCReportService.PARENT_CODE)
class FiveDCReportController : ControllerHelper() {

    @Resource
    lateinit var fiveDCReportService: FiveDCReportService

    @SchneiderRequestMapping("/init_page")
    fun initPage(request: HttpServletRequest): Response {
        super.pageLoad(request)
        super.setGlobalCache(true)
        return fiveDCReportService.initPage(parameterMap, session)
    }

    @SchneiderRequestMapping("/query_report1")
    fun queryReport1(request: HttpServletRequest): Response {
        super.pageLoad(request)
        super.setGlobalCache(true)
        return fiveDCReportService.queryReport1(parameterMap)
    }

    @SchneiderRequestMapping("/download_report1")
    fun downloadReport1(request: HttpServletRequest?, response: HttpServletResponse) {
        super.pageLoad(request)
        fiveDCReportService.downloadReport1(parameterMap, response)
    }

    @SchneiderRequestMapping("/query_report1_tree_table_sub")
    fun queryReport1TreeTableSub(request: HttpServletRequest): Response {
        super.pageLoad(request)
        super.setGlobalCache(true)
        return fiveDCReportService.queryReport1TreeTableSub(parameterMap)
    }

    @SchneiderRequestMapping(value = ["/query_report1_table_details"])
    fun queryReport1TableDetails(request: HttpServletRequest): Response {
        this.pageLoad(request)
        this.setGlobalCache(true)
        return fiveDCReportService.queryReport1TableDetails(parameterMap)
    }

    @SchneiderRequestMapping("/download_report1_table_details")
    fun downloadReport1TableDetails(request: HttpServletRequest?, response: HttpServletResponse) {
        super.pageLoad(request)
        fiveDCReportService.downloadReport1TableDetails(parameterMap, response)
    }

    @SchneiderRequestMapping("/download_report1_tree_table")
    fun downloadReport1TreeTable(request: HttpServletRequest?, response: HttpServletResponse) {
        super.pageLoad(request)
        fiveDCReportService.downloadReport1TreeTable(parameterMap, response)
    }

    @SchneiderRequestMapping(value = ["/save_report1_comments"])
    fun saveReport1Comments(request: HttpServletRequest): Response {
        this.pageLoad(request)
        this.setGlobalCache(true)
        return fiveDCReportService.saveReport1Comments(parameterMap)
    }

    @SchneiderRequestMapping("/query_report2")
    fun queryReport2(request: HttpServletRequest): Response {
        super.pageLoad(request)
        super.setGlobalCache(true)
        return fiveDCReportService.queryReport2(parameterMap)
    }
}

