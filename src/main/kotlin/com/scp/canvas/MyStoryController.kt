package com.scp.canvas

import com.scp.canvas.service.MyStoryService
import com.starter.context.bean.Response
import com.starter.context.bean.SchneiderRequestMapping
import com.starter.context.servlet.ControllerHelper
import jakarta.annotation.Resource
import jakarta.servlet.http.HttpServletRequest
import org.springframework.context.annotation.Scope
import org.springframework.web.bind.annotation.CrossOrigin
import org.springframework.web.bind.annotation.RestController

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = ["/canvas/my_story"], parent = MyStoryService.PARENT_CODE)
class MyStoryController : ControllerHelper() {

    @Resource
    lateinit var myStoryService: MyStoryService

    @SchneiderRequestMapping("/init_variant_page")
    fun initVariantPage(request: HttpServletRequest): Response {
        super.pageLoad(request)
        super.setGlobalCache(true)
        return myStoryService.initVariantPage(parameterMap, session.userid)
    }

    @SchneiderRequestMapping(value = ["/query_page_list"])
    fun queryPageList(request: HttpServletRequest?): Response {
        super.pageLoad(request)
        return myStoryService.queryPageList(parameterMap)
    }

    @SchneiderRequestMapping("/query_comments")
    fun queryComments(request: HttpServletRequest): Response {
        super.pageLoad(request)
        super.setGlobalCache(true)
        return myStoryService.queryComments(parameterMap, session.userid)
    }

    @SchneiderRequestMapping("/query_change_logs")
    fun queryChangeLogs(request: HttpServletRequest): Response {
        super.pageLoad(request)
        return myStoryService.queryChangeLogs(parameterMap)
    }

    @SchneiderRequestMapping("/query_comment_by_id")
    fun queryCommentById(request: HttpServletRequest): Response {
        super.pageLoad(request)
        return myStoryService.queryCommentById(parameterMap)
    }

    @SchneiderRequestMapping("/query_story_name_by_id")
    fun queryStoryNameById(request: HttpServletRequest): Response {
        super.pageLoad(request)
        return myStoryService.queryStoryNameById(parameterMap)
    }

    @SchneiderRequestMapping("/modify_comment")
    fun modifyComment(request: HttpServletRequest): Response {
        super.pageLoad(request)
        return myStoryService.modifyComment(parameterMap)
    }

    @SchneiderRequestMapping("/delete_comment")
    fun deleteComment(request: HttpServletRequest): Response {
        super.pageLoad(request)
        super.setGlobalCache(true)
        return myStoryService.deleteComment(parameterMap)
    }

    @SchneiderRequestMapping("/delete_reply")
    fun deleteReply(request: HttpServletRequest): Response {
        super.pageLoad(request)
        super.setGlobalCache(true)
        return myStoryService.deleteReply(parameterMap)
    }

    @SchneiderRequestMapping("/send_comment")
    fun sendComment(request: HttpServletRequest): Response {
        super.pageLoad(request)
        super.setGlobalCache(true)
        return myStoryService.sendComment(parameterMap, session)
    }

    @SchneiderRequestMapping("/send_reply")
    fun sendReply(request: HttpServletRequest): Response {
        super.pageLoad(request)
        super.setGlobalCache(true)
        return myStoryService.sendReply(parameterMap)
    }

    @SchneiderRequestMapping("/query_variant_list")
    fun queryVariantList(request: HttpServletRequest?): Response {
        pageLoad(request)
        setGlobalCache(true)
        return myStoryService.queryVariantList(parameterMap, session)
    }

    @SchneiderRequestMapping("/save_variant")
    fun saveVariant(request: HttpServletRequest): Response? {
        pageLoad(request)
        return myStoryService.saveVariant(parameterMap)
    }

    @SchneiderRequestMapping("/query_variant_by_key")
    fun queryVariantByKey(request: HttpServletRequest): Response? {
        pageLoad(request)
        return myStoryService.queryVariantByKey(parameterMap)
    }

    @SchneiderRequestMapping("/query_variant_config_by_key")
    fun queryVariantConfigByKey(request: HttpServletRequest): Response? {
        pageLoad(request)
        return myStoryService.queryVariantConfigByKey(parameterMap)
    }

    @SchneiderRequestMapping("/delete_variant")
    fun deleteVariant(request: HttpServletRequest): Response? {
        pageLoad(request)
        return myStoryService.deleteVariant(parameterMap)
    }

    @SchneiderRequestMapping("/modify_variant")
    fun modifyVariant(request: HttpServletRequest): Response? {
        pageLoad(request)
        return myStoryService.modifyVariant(parameterMap)
    }

    @SchneiderRequestMapping(value = ["/share_condition"])
    fun shareCondition(request: HttpServletRequest?): Response {
        super.pageLoad(request)
        return myStoryService.shareCondition(session.userid, session.username, session.email, parameterMap)
    }

    @SchneiderRequestMapping(value = ["/query_condition_by_id"])
    fun queryConditionById(request: HttpServletRequest?): Response {
        super.pageLoad(request)
        return myStoryService.queryConditionById(parameterMap)
    }

    @SchneiderRequestMapping(value = ["/query_default_condition"])
    fun queryDefaultCondition(request: HttpServletRequest?): Response {
        super.pageLoad(request)
        return myStoryService.queryDefaultCondition(parameterMap)
    }

    @SchneiderRequestMapping(value = ["/update_default_variant"])
    fun updateDefaultVariant(request: HttpServletRequest?): Response {
        super.pageLoad(request)
        return myStoryService.updateDefaultVariant(parameterMap)
    }

    @SchneiderRequestMapping(value = ["/query_manual_input_by_id"])
    fun queryManualInputById(request: HttpServletRequest?): Response {
        super.pageLoad(request)
        return myStoryService.queryManualInputById(parameterMap)
    }
}

