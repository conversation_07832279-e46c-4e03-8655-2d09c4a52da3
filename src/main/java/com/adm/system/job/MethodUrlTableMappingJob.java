package com.adm.system.job;

import com.adm.system.dao.ISystemDao;
import com.starter.context.configuration.IJobClient;
import com.starter.context.configuration.database.MyBatisSQLInterceptor;
import com.starter.utils.Utils;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 用来将存在内存中的URL和TABLE MAPPING关系存入数据库
 */

@Component
public class MethodUrlTableMappingJob implements IJobClient {

    private static final Logger logger = LoggerFactory.getLogger(MethodUrlTableMappingJob.class);

    @Value("${spring.monitor}")
    private boolean monitor;

    @Resource
    private ISystemDao systemDao;

    private static volatile boolean LOCK = false;

    @Override
    public boolean triggered(Calendar fireTime) {
        if (monitor) {
            int minute = fireTime.get(Calendar.MINUTE);
            return minute != 0 && minute % 19 == 0; // 每19分钟触发一次
        } else {
            return false;
        }
    }

    @Override
    public void run() {
        if (LOCK == true) {
            return;
        }
        LOCK = true;
        synchronized (this) {
            try {
                if (MyBatisSQLInterceptor.URL_TABLE_MAPPING.isEmpty() == false) {
                    // 当主线程向URL_TABLE_MAPPING插入数据时, MethodUrlTableMappingJob线程不可以对MAP对象执行遍历操作
                    // 为了避免这个情况, 系统将使用两个MAP对象来完成该操作, 一个对象用来接收数据, 一个对象用来保存数据, 两者交替切换
                    Map<String, Map<String, Long>> pointerOld = MyBatisSQLInterceptor.URL_TABLE_MAPPING;
                    // 切换指针
                    if (MyBatisSQLInterceptor.URL_TABLE_MAPPING == MyBatisSQLInterceptor.URL_TABLE_MAPPING1) {
                        MyBatisSQLInterceptor.URL_TABLE_MAPPING = MyBatisSQLInterceptor.URL_TABLE_MAPPING2;
                    } else {
                        MyBatisSQLInterceptor.URL_TABLE_MAPPING = MyBatisSQLInterceptor.URL_TABLE_MAPPING1;
                    }


                    List<Map<String, Object>> data = new ArrayList<>();
                    for (String url : pointerOld.keySet()) {
                        Map<String, Long> mapping = pointerOld.get(url);

                        for (String tablename : mapping.keySet()) {
                            Map<String, Object> map = new HashMap<>();
                            map.put("URL", url);
                            map.put("TABLE_NAME", tablename);
                            map.put("LAST_ACTIVE_TIME", mapping.get(tablename));
                            data.add(map);
                        }
                    }

                    systemDao.saveUrlTableMapping(data, System.currentTimeMillis() / 1000);
                    pointerOld.clear();
                }
            } catch (Exception e) {
                logger.error(Utils.getExceptionMessage(e));
            }
        }
        LOCK = false;
    }
}
