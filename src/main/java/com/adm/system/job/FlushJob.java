package com.adm.system.job;


import com.adm.system.FlushController;
import com.scp.inventory.service.IInventoryRebalanceService;
import com.starter.context.bean.Configuration;
import com.starter.context.configuration.IJobClient;
import jakarta.annotation.Resource;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.Calendar;
import java.util.Set;

@Component
public class FlushJob implements IJobClient {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private IInventoryRebalanceService inventoryRebalanceService;

    @Value("${file.upload.path}")
    private String appUploadPath;

    @Override
    public boolean triggered(Calendar fireTime) {
        return false; // 默认不自动触发
    }

    /**
     * 重置系统变量, 由数据端触发
     */
    @Override
    public void run() {
        try {
            // 重新计算rebalance
            inventoryRebalanceService.calcRebalance();

            // 最后清空数据缓存
            Set<String> keys = redisTemplate.keys(Configuration.CACHED_KEY + "*");
            if (keys != null) {
                redisTemplate.delete(keys);
            }

            // 删除上传的临时文件夹
            FileUtils.deleteDirectory(new File(appUploadPath + "application/temp/"));
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            FlushController.finishFlush();
        }
    }
}
