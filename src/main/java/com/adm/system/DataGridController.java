package com.adm.system;

import com.adm.system.service.IDataGridService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.util.WebUtils;

import java.io.File;

@RestController
@CrossOrigin
@SchneiderRequestMapping("/datagrid")
@Scope("prototype")
public class DataGridController extends ControllerHelper {
    @Resource
    private IDataGridService dataGridService;

    @Resource
    private Response response;

    @SchneiderRequestMapping(value = "/query_column_def")
    public Response queryColumnDef(HttpServletRequest request) {
        super.pageLoad(request);
        return dataGridService.queryColumnDef(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_data")
    public Response queryData(HttpServletRequest request) {
        super.pageLoad(request);
        return dataGridService.queryData(parameterMap);
    }

    @SchneiderRequestMapping(value = "/save_changes")
    public Response saveChanges(HttpServletRequest request) {
        super.pageLoad(request);
        return dataGridService.saveChanges(parameterMap);
    }

    @SchneiderRequestMapping("/upload_data")
    public Response uploadData(HttpServletRequest request) {
        super.pageLoad(request);
        MultipartHttpServletRequest multipartRequest = WebUtils.getNativeRequest(request, MultipartHttpServletRequest.class);
        if (multipartRequest != null) {
            MultipartFile file = multipartRequest.getFile("file");
            File tempFile = null;
            try {
                // 将文件保存在本地
                tempFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), ".tmp");
                file.transferTo(tempFile);
                // 超过8MB的文件, 就会启动
                if (tempFile.length() > 1024 * 1024 * 8) {
                    return dataGridService.uploadDataWithTempTable(session.getUserid(), (String) parameterMap.get("uploadModule"), (String) parameterMap.get("bindTo"), (String) parameterMap.get("cacheKeys"), tempFile);
                } else {
                    return dataGridService.uploadData(session.getUserid(), (String) parameterMap.get("uploadModule"), (String) parameterMap.get("bindTo"), (String) parameterMap.get("cacheKeys"), tempFile);
                }
            } catch (Exception e) {
                return response.setError(e);
            } finally {
                if (tempFile != null) {
                    if (tempFile.delete() == false) {
                        System.err.println(tempFile.getAbsolutePath() + " delete failed");
                    }
                }
            }
        } else {
            return response.setError(new Exception("upload failed, multipartRequest is null !"));
        }
    }


    @SchneiderRequestMapping("/download_template")
    public void downloadTemplate(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        dataGridService.downloadTemplate((String) parameterMap.get("bindTo"), response);
    }

    @SchneiderRequestMapping("/download_data")
    public void downloadData(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        dataGridService.downloadData(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_avalible_columns_by_url")
    public Response queryAvalibleColumnsByUrl(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        return dataGridService.queryAvalibleColumnsByUrl(parameterMap);
    }
}
