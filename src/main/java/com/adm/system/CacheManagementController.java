package com.adm.system;

import com.adm.system.service.ICacheManagementService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/system/cache_management", parent = "menu924")
public class CacheManagementController extends ControllerHelper {

    @Resource
    private ICacheManagementService cacheManagementService;

    @SchneiderRequestMapping(value = "/query_key_list")
    public Response queryKeyList() {
        return cacheManagementService.queryKeyList();
    }

    @SchneiderRequestMapping(value = "/query_content_by_key")
    public Response queryContentByKey(HttpServletRequest request) {
        super.pageLoad(request);
        return cacheManagementService.queryContentByKey(parameterMap);
    }

    @SchneiderRequestMapping(value = "/delete_cache_by_key")
    public Response deleteCacheByKey(HttpServletRequest request) {
        super.pageLoad(request);
        return cacheManagementService.deleteCacheByKey(parameterMap);
    }
}
