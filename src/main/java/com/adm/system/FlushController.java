package com.adm.system;

import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.configuration.IJobClient;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

@RestController
@CrossOrigin
@Scope("prototype")
public class FlushController extends ControllerHelper {

    @Resource(name = "flushJob")
    private IJobClient flushJob;

    @Resource
    private Response response;

    private static boolean RUNNING = false;

    /**
     * 数据同步完或数据重新补跑的时候, 会调用此方法, 此方法重置很多变量
     *
     * @param request request
     * @return result
     */
    @SchneiderRequestMapping(value = "/flush")
    public Response queryAllAvailableComponents(HttpServletRequest request) {
        super.pageLoad(request);
        Map<String, String> resultMap = new HashMap<>();

        if ("1cb73ca13fc541b6b9cffa365f713aaf".equals(parameterMap.get("t"))) {
            if (RUNNING) {
                resultMap.put("result", "Job is running");
            } else {
                RUNNING = true;
                new Thread(flushJob).start();
                resultMap.put("result", "Request submitted");
            }
        } else {
            resultMap.put("result", "Request ignore");
        }
        return response.setBody(resultMap);
    }

    public static void finishFlush() {
        RUNNING = false;
    }
}
