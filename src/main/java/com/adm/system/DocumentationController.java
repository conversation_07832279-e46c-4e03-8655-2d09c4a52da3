package com.adm.system;

import com.adm.system.service.IDocumentationService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/system/documentation", parent = "menu932")
public class DocumentationController extends ControllerHelper {

    @Resource
    private IDocumentationService documentationService;

    @SchneiderRequestMapping(value = "/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        return documentationService.initPage(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_doc_list")
    public Response queryDocList(HttpServletRequest request) {
        super.pageLoad(request);
        return documentationService.queryDocList(parameterMap);
    }

    @SchneiderRequestMapping(value = "/save_new_doc")
    public Response saveNewDoc(HttpServletRequest request) {
        super.pageLoad(request);
        return documentationService.saveNewDoc(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_doc")
    public Response queryDoc(HttpServletRequest request) {
        super.pageLoad(request);
        return documentationService.queryDoc(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_doc_with_function")
    public Response queryDocWithFunction(HttpServletRequest request) {
        super.pageLoad(request);
        return documentationService.queryDocWithFunction(parameterMap);
    }

    @SchneiderRequestMapping(value = "/modify_doc")
    public Response modifyDoc(HttpServletRequest request) {
        super.pageLoad(request);
        return documentationService.modifyDoc(parameterMap);
    }

    @SchneiderRequestMapping(value = "/delete_doc")
    public Response deleteDoc(HttpServletRequest request) {
        super.pageLoad(request);
        return documentationService.deleteDoc(parameterMap);
    }
}
