package com.adm.system;

import com.adm.system.service.IMicrosoftGraphService;
import com.alibaba.fastjson.JSON;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;

@RestController
@CrossOrigin
@SchneiderRequestMapping("/microsoftgraph")
@Scope("prototype")
public class MicrosoftGraphController extends ControllerHelper {

    private static final Logger logger = LoggerFactory.getLogger(MicrosoftGraphController.class);

    @Resource
    private IMicrosoftGraphService microsoftGraphService;

    @SchneiderRequestMapping(value = "/mailreply")
    public Response mailReply(HttpServletRequest request) {
        super.pageLoad(request);
        return microsoftGraphService.mailReply(parameterMap);
    }

    // token Ghv1RPdkyE+phf7OrwPG6h0kdwekqPJdsBT5QE7r+FA=
    @SchneiderRequestMapping(value = "/teams/dss_user_community")
    public Response teamsDssUserCommunity(HttpServletRequest request) {
        super.pageLoad(request);
        logger.error("hmac = " + request.getHeader("hmac"));
        logger.error("HMAC = " + request.getHeader("HMAC"));
        logger.error(JSON.toJSONString(parameterMap));
        return new Response();
    }
}
