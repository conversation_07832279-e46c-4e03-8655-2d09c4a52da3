package com.adm.system;

import com.adm.system.service.IPivotTableService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/pivot_table")
public class PivotTableController extends ControllerHelper {

    @Resource
    private IPivotTableService pivotTableService;

    @SchneiderRequestMapping("/query_data")
    public Response queryData(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return pivotTableService.queryData(parameterMap);
    }

    @SchneiderRequestMapping("/download_data")
    public void downloadData(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        pivotTableService.downloadData(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_details")
    public Response queryDetails(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return pivotTableService.queryDetails(parameterMap);
    }

    @SchneiderRequestMapping("/download_details")
    public void downloadDetails(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        pivotTableService.downloadDetails(parameterMap, response);
    }
}
