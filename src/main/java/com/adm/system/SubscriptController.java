package com.adm.system;

import com.adm.system.service.ISubscriptService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;

@RestController
@CrossOrigin
@SchneiderRequestMapping("/subscript")
@Scope("prototype")
public class SubscriptController extends ControllerHelper {
    @Resource
    private ISubscriptService subscriptService;

    @SchneiderRequestMapping(value = "/query_report_subscript")
    public Response queryReportSubscript(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return subscriptService.queryReportSubscript(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_project_diagram")
    public Response queryProjectDiagram(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return subscriptService.queryProjectDiagram(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_table_diagram")
    public Response queryTableDiagram(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return subscriptService.queryTableDiagram(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_subscript_doc")
    public Response querySubscriptDoc(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return subscriptService.querySubscriptDoc(parameterMap);
    }

    @SchneiderRequestMapping(value = "/modify_subscript_doc")
    public Response modifySubscriptDoc(HttpServletRequest request) {
        super.pageLoad(request);
        return subscriptService.modifySubscriptDoc(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_related_methods")
    public Response queryRelatedMethods(HttpServletRequest request) {
        super.pageLoad(request);
        return subscriptService.queryRelatedMethods(parameterMap);
    }

    @SchneiderRequestMapping(value = "/modify_report_rel")
    public Response modifyReportRel(HttpServletRequest request) {
        super.pageLoad(request);
        return subscriptService.modifyReportRel(parameterMap);
    }
}
