package com.adm.system;

import com.adm.system.service.ISQLConsoleService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@SchneiderRequestMapping("/toolbox/sql_console")
@Scope("prototype")
public class SQLConsoleController extends ControllerHelper {

    @Resource
    private ISQLConsoleService SQLConsoleService;

    @SchneiderRequestMapping(value = "/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        this.pageLoad(request);
        this.setGlobalCache(true);
        return SQLConsoleService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_report1_headers")
    public Response queryReport1Headers(HttpServletRequest request) {
        this.pageLoad(request);
        this.setGlobalCache(true);
        return SQLConsoleService.queryReport1Headers(parameterMap);
    }

    @SchneiderRequestMapping(value = "/download_report1")
    public void downloadReport1(HttpServletRequest request, HttpServletResponse response) {
        this.pageLoad(request);
        SQLConsoleService.downloadReport1(parameterMap, response);
    }

    @SchneiderRequestMapping(value = "/query_table_list")
    public Response queryTableList(HttpServletRequest request) {
        this.pageLoad(request);
        return SQLConsoleService.queryTableList(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_table_cols")
    public Response queryTableCols(HttpServletRequest request) {
        this.pageLoad(request);
        this.setGlobalCache(true);
        return SQLConsoleService.queryTableCols(parameterMap);
    }
}
