package com.adm.system.service;

import com.starter.context.bean.Response;
import java.util.Map;

public interface IBroadcastService {

    Response queryBroadcastCascader(Map<String, Object> parameterMap);

    Response queryBroadcastConfig(Map<String, Object> parameterMap);

    Response queryBroadcastOptions(Map<String, Object> parameterMap);

    Response saveBroadcastConfig(Map<String, Object> parameterMap);

    Response deleteBroadcast(Map<String, Object> parameterMap);
}
