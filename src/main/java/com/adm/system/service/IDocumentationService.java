package com.adm.system.service;

import com.starter.context.bean.Response;

import java.util.Map;

public interface IDocumentationService {

    Response initPage(Map<String, Object> parameterMap);

    Response queryDocList(Map<String, Object> parameterMap);

    Response saveNewDoc(Map<String, Object> parameterMap);

    Response queryDoc(Map<String, Object> parameterMap);

    Response modifyDoc(Map<String, Object> parameterMap);

    Response deleteDoc(Map<String, Object> parameterMap);

    Response queryDocWithFunction(Map<String, Object> parameterMap);
}
