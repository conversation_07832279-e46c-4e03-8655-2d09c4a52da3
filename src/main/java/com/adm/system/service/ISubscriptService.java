package com.adm.system.service;

import com.starter.context.bean.Response;

import java.util.Map;

public interface ISubscriptService {

    Response queryReportSubscript(Map<String, Object> parameterMap);

    Response queryProjectDiagram(Map<String, Object> parameterMap);

    Response queryTableDiagram(Map<String, Object> parameterMap);

    Response querySubscriptDoc(Map<String, Object> parameterMap);

    Response modifySubscriptDoc(Map<String, Object> parameterMap);

    Response queryRelatedMethods(Map<String, Object> parameterMap);

    Response modifyReportRel(Map<String, Object> parameterMap);
}
