package com.adm.system.service;

import com.starter.context.bean.Response;
import jakarta.servlet.http.HttpServletResponse;

import java.io.File;
import java.util.Map;

public interface IDataGridService {
    Response queryColumnDef(Map<String, Object> parameterMap);

    Response queryData(Map<String, Object> parameterMap);

    Response saveChanges(Map<String, Object> parameterMap);

    Response uploadData(String userid, String uploadModule, String bindTo, String cacheKeys, File file);

    Response uploadDataWithTempTable(String userid, String uploadModule, String bindTo, String cacheKeys, File file);

    void downloadTemplate(String bindTo, HttpServletResponse response);

    void downloadData(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryAvalibleColumnsByUrl(Map<String, Object> parameterMap);
}
