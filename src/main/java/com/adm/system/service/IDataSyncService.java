package com.adm.system.service;

import com.starter.context.bean.Response;
import com.starter.login.bean.Session;

import java.util.Map;

public interface IDataSyncService {

    Response formatJson(Map<String, Object> parameterMap);

    Response queryReport1(Map<String, Object> parameterMap);

    Response queryReport1Change(Map<String, Object> parameterMap);

    Response queryReport1TemplateOpts();

    Response saveReport1Template(Map<String, Object> parameterMap);

    Response queryReport1Template(Map<String, Object> parameterMap);

    Response deleteReport1Template(Map<String, Object> parameterMap);

    Response queryReport2Version(Map<String, Object> parameterMap);

    Response queryReport2(Map<String, Object> parameterMap);

    Response queryReport2Details(Map<String, Object> parameterMap);

    Response modifyReport2Details(Map<String, Object> parameterMap);

    Response deleteReport2Details(Map<String, Object> parameterMap);

    Response createReport2Details(Map<String, Object> parameterMap);

    Response queryReport3(Map<String, Object> parameterMap);

    Response report3RefreshMV(Session session, Map<String, Object> parameterMap);

    Response queryReport4(Map<String, Object> parameterMap);

    Response queryReport5(Map<String, Object> parameterMap);
}
