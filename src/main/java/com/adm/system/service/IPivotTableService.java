package com.adm.system.service;

import com.starter.context.bean.Response;
import jakarta.servlet.http.HttpServletResponse;

import java.util.Map;

public interface IPivotTableService {
    Response queryData(Map<String, Object> parameterMap);

    void downloadData(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryDetails(Map<String, Object> parameterMap);

    void downloadDetails(Map<String, Object> parameterMap, HttpServletResponse response);
}
