package com.adm.system.service;

import com.starter.context.bean.Response;
import com.starter.login.bean.Session;
import com.adm.system.bean.MailboxSenderBean;

import java.util.Map;

public interface IMailboxService {
    Response sendMail(Session session, MailboxSenderBean mailboxSenderBean);

    Response deleteMail(Map<String, Object> parameterMap);

    Response queryMailByID(Map<String, Object> parameterMap);

    Response queryMailList(Map<String, Object> parameterMap);

    Response markAllMailAsRead(Map<String, Object> parameterMap);

    Response queryMailtoByKeywords(Map<String, Object> parameterMap);

}
