package com.adm.system.service;

import com.starter.context.bean.Response;
import jakarta.servlet.http.HttpServletResponse;

import java.util.Map;

public interface IMaterialRiskService {

    Response querySuggestionByKeywords(Map<String, Object> parameterMap);

    Response saveRule(Map<String, Object> parameterMap);

    Response searchRules(Map<String, Object> parameterMap);

    Response modifyRuleStatus(Map<String, Object> parameterMap);

    Response queryRuleById(Map<String, Object> parameterMap);

    Response deleteRule(Map<String, Object> parameterMap);

    Response queryMaterialListByRule(Map<String, Object> parameterMap);

    void downloadMaterialListByRule(Map<String, Object> parameterMap, HttpServletResponse response);

    void recreateMaterialRiskLevelView();

    Response queryAllAvailableComponentsScheduled(Map<String, Object> parameterMap);
}
