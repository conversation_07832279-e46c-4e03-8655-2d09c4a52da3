package com.adm.system.service.impl;

import com.alibaba.fastjson.JSON;
import com.starter.context.bean.Response;
import com.adm.system.dao.IMicrosoftGraphDao;
import com.adm.system.service.IMicrosoftGraphService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Service
@Scope("prototype")
@Transactional
public class MicrosoftGraphServiceImpl implements IMicrosoftGraphService {

    private static final Logger logger = LoggerFactory.getLogger(MicrosoftGraphServiceImpl.class);

    @Resource
    private IMicrosoftGraphDao microsoftGraphDao;

    @Resource
    private Response response;

    @Override
    public Response mailReply(Map<String, Object> parameterMap) {
        Map<String, String> resultMap = new HashMap<>();
        resultMap.put("code", (String) parameterMap.get("code"));
        resultMap.put("state", (String) parameterMap.get("state"));
        logger.error(JSON.toJSONString(resultMap));
        return response;
    }
}
