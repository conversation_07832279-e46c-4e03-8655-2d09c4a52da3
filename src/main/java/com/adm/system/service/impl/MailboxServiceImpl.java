package com.adm.system.service.impl;

import com.adm.system.bean.MailboxSenderBean;
import com.adm.system.dao.IMailboxDao;
import com.adm.system.service.IMailboxService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.starter.context.bean.Response;
import com.starter.context.bean.Status;
import com.starter.context.mail.MailBean;
import com.starter.context.mail.MailFeignClient;
import com.starter.login.bean.Session;
import com.starter.utils.Utils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Scope("prototype")
@Transactional
public class MailboxServiceImpl implements IMailboxService {

    @Resource
    private IMailboxDao mailboxDao;

    @Resource
    private Response response;

    @Resource
    private MailFeignClient mailFeignClient;

    @Override
    public Response sendMail(Session session, MailboxSenderBean mailboxSenderBean) {
        mailboxSenderBean.setFrom(session.getUserid());

        List<String> recipients = mailboxSenderBean.getTo();
        if (recipients.isEmpty()) {
            return response.set(Status.FORBIDDEN, "No recipients found");
        }

        // 无论是发送还是草稿, 都先保存邮件至数据库
        if (StringUtils.isBlank(mailboxSenderBean.getMail_id())) {
            mailboxSenderBean.setMail_id(Utils.randomStr(16));
            mailboxDao.saveMailBody(mailboxSenderBean);
        } else {
            mailboxDao.updateMailBody(mailboxSenderBean);
        }

        if (mailboxSenderBean.getType() == 0) { // draft
            // 草稿箱的实现方式是, 将一封邮件的收件人设置为当前用户, 这样这个用户就可以在自己的草稿箱内看到一封邮件
            // 如果这个草稿已经保存过, 那么更新这个草稿的阅读时间, 这样用户就可以在界面上看到这个草稿最后修改时间
            if (mailboxDao.queryMailDraftCount(mailboxSenderBean.getMail_id()) > 0) {
                mailboxDao.updateMailDraft(mailboxSenderBean.getMail_id(), session.getEmail());
            } else {
                mailboxDao.saveMailDraft(mailboxSenderBean.getMail_id(), session.getEmail());
            }
        } else {
            // 发送站内信, 本质上就是给自己发送一封邮件, 保存在已发送文件夹
            // 所有收件人保存一封邮件在收件箱
            mailboxDao.saveMailRecipients(mailboxSenderBean.getMail_id(), recipients, session.getEmail());

            if (mailboxSenderBean.isMail()) {
                MailBean mailBean = new MailBean();
                mailBean.setSubject(mailboxSenderBean.getSubject());
                mailBean.setBody(mailboxSenderBean.getBody());
                mailBean.setTo(StringUtils.join(recipients, ","));
                mailFeignClient.sendAsync(mailBean);
            }
        }

        return response.setBody(recipients.size());
    }

    @Override
    public Response deleteMail(Map<String, Object> parameterMap) {
        mailboxDao.deleteMail(parameterMap);
        return response;
    }

    @Override
    public Response queryMailByID(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = mailboxDao.queryMailByID(parameterMap);
        mailboxDao.updateMailReadTime(parameterMap);
        if (resultMap != null) {
            resultMap.put("body", Utils.clob2String(resultMap.get("body")));
            JSONArray recipientArray = JSON.parseArray(Utils.clob2String(resultMap.get("to")));
            if (recipientArray != null && recipientArray.size() > 0) {
                resultMap.put("to", recipientArray.toJavaList(String.class));
            }
        }
        return response.setBody(resultMap);
    }

    @Override
    public Response queryMailtoByKeywords(Map<String, Object> parameterMap) {
        return response.setBody(mailboxDao.queryMailtoByKeywords(parameterMap));
    }

    @Override
    public Response queryMailList(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> mailList = mailboxDao.queryMailList(parameterMap);
        List<Map<String, Object>> folderList = mailboxDao.queryMailCount(parameterMap);


        for (Map<String, Object> map : mailList) {
            map.put("loading", false);
            map.put("body", Utils.delHTMLTag(Utils.clob2String(map.get("body"))));
        }
        resultMap.put("list", mailList);
        int total = 0;
        int inbox = 0;
        int draft = 0;
        if (folderList != null && folderList.isEmpty() == false) {
            for (Map<String, Object> map : folderList) {
                if (StringUtils.equalsIgnoreCase((String) parameterMap.get("folder"), (String) map.get("save_folder"))) {
                    total = Utils.parseInt(map.get("total"));
                }
                if ("inbox".equals(StringUtils.lowerCase((String) map.get("save_folder")))) {
                    inbox = Utils.parseInt(map.get("unread"));
                } else if ("draft".equals(StringUtils.lowerCase((String) map.get("save_folder")))) {
                    draft = Utils.parseInt(map.get("total"));
                }
            }
        }

        resultMap.put("total", total);
        resultMap.put("inbox", inbox);
        resultMap.put("draft", draft);
        return response.setBody(resultMap);
    }

    @Override
    public Response markAllMailAsRead(Map<String, Object> parameterMap) {
        mailboxDao.markAllMailAsRead(parameterMap);
        return response;
    }
}
