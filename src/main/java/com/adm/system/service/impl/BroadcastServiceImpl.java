package com.adm.system.service.impl;

import com.adm.system.bean.*;
import com.adm.system.dao.IBroadcastDao;
import com.adm.system.service.IBroadcastService;
import com.starter.context.bean.*;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.Utils;
import jakarta.annotation.Resource;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Scope("prototype")
@Transactional
public class BroadcastServiceImpl extends ServiceHelper implements IBroadcastService {

    @Resource
    private IBroadcastDao broadcastDao;

    @Resource
    private Response response;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryBroadcastCascader(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cascader", Utils.parseCascader(broadcastDao.queryBroadcastCascader(parameterMap)));
        return response.setBody(resultMap);
    }

    @Override
    public Response queryBroadcastConfig(Map<String, Object> parameterMap) {
        this.generateCascaderFilterSQL(parameterMap);
        // popupConfig 获取弹窗的数据权限信息
        List<Map<String, Object>> popupConfig = broadcastDao.queryBroadcastConfig(parameterMap);

        Map<String, BroadcastConfigBean> configMap = new LinkedHashMap<>();

        // 创建SimpleDateFormat对象，设置日期格式
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        // 由于join权限表会扩行，此处for循环用于整合权限
        for (Map<String, Object> row : popupConfig) {
            String urlKey = (String) row.get("URL");
            String remindSubjectKey = (String) row.get("REMIND_SUBJECT");
            String key = urlKey + "-" + remindSubjectKey;

            BroadcastConfigBean config = configMap.getOrDefault(key, new BroadcastConfigBean());
            config.setUrl(urlKey);
            config.setRemindSubject(remindSubjectKey);
            config.setFrequency((String) row.get("FREQUENCY"));
            config.setRemindCount((Integer) ((BigDecimal) row.get("REMIND_COUNT")).intValueExact());
            config.setPageName((String) row.get("PAGE_NAME"));
            config.setDescription((String) row.get("DESCRIPTION"));
            config.setValidFrom(dateFormat.format((Date) row.get("VALID_FROM")));
            config.setValidTo(dateFormat.format((Date) row.get("VALID_TO")));

            String authType = (String) row.get("AUTH_TYPE");
            String authDetail = (String) row.get("AUTH_DETAIL");

            if (config.getAuthInfo() == null) {
                config.setAuthInfo(new HashMap<>());
            }
            // 创建或更新 authInfo 的键值对
            if (authType != null) {
                config.getAuthInfo().computeIfAbsent(authType, k -> new ArrayList<>()).add(authDetail);
            }
            configMap.put(key, config);
        }
        List<BroadcastConfigBean> resultMap = new ArrayList<>(configMap.values());

        Map<String, Object> result = new HashMap<>();
        result.put("data", resultMap);
        result.put("total", broadcastDao.queryBroadcastConfigCount(parameterMap));

        return response.setBody(result);
    }

    @Override
    public Response queryBroadcastOptions(Map<String, Object> parameterMap) {
        List<Map<String, String>>  broadcastOptions = broadcastDao.queryBroadcastOptions(parameterMap);
        Map<String, List<String>> broadcastMap = new LinkedHashMap<>();
        for (Map<String,String> row : broadcastOptions) {
            String key = row.get("COLUMN_NAME");
            String value = row.get("COLUMN_VALUE");
            broadcastMap.computeIfAbsent(key, k -> new ArrayList<>()).add(value);
        }
        return response.setBody(broadcastMap);
    }

    @Override
    public Response saveBroadcastConfig(Map<String, Object> parameterMap) {
        broadcastDao.saveBroadcastConfig(parameterMap);
        return response;
    }

    @Override
    public Response deleteBroadcast(Map<String, Object> parameterMap) {
        broadcastDao.deleteBroadcast(parameterMap);
        return response;
    }
}
