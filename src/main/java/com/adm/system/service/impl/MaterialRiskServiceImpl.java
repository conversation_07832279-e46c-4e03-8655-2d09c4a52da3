package com.adm.system.service.impl;

import com.adm.system.dao.IMaterialRiskDao;
import com.adm.system.service.IMaterialRiskService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.bean.filter.FilterElement;
import com.starter.context.bean.filter.FilterGenerator;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

@Service
@Scope("prototype")
@Transactional
public class MaterialRiskServiceImpl implements IMaterialRiskService {

    @Resource
    private IMaterialRiskDao materialRiskDao;

    @Resource
    private Response response;

    @Resource
    private ExcelTemplate excelTemplate;

    @Override
    public Response querySuggestionByKeywords(Map<String, Object> parameterMap) {
        String keywords = (String) parameterMap.get("keywords");
        String field = (String) parameterMap.get("field");
        String type = (String) parameterMap.get("type");
        List<String> data = materialRiskDao.querySuggestionByKeywords(field, type, keywords);

        List<String> result = new ArrayList<>();
        if (StringUtils.isBlank(keywords) == false && data.contains(keywords) == false) {
            result.add(keywords);
        }
        result.addAll(data);
        if (result.isEmpty()) {
            result.add("No Data");
        }
        return response.setBody(result);
    }

    @Override
    public Response saveRule(Map<String, Object> parameterMap) {
        if (StringUtils.isEmpty((String) parameterMap.get("id"))) {
            parameterMap.put("id", Utils.randomStr(12));
        }
        return materialRiskDao.saveRule(parameterMap);
    }

    @Override
    public Response searchRules(Map<String, Object> parameterMap) {
        List<Map<String, Object>> data = materialRiskDao.searchRules();
        for (Map<String, Object> map : data) {
            map.put("FILTER_CHAIN", this.displayFilterChain((String) map.get("FILTER_CHAIN")));
            map.put("STATUS", "Y".equalsIgnoreCase((String) map.get("STATUS")));
        }
        return response.setBody(data);
    }

    @Override
    public Response modifyRuleStatus(Map<String, Object> parameterMap) {
        JSONArray jsonArray = (JSONArray) parameterMap.get("rule");
        for (int index = 0; index < jsonArray.size(); index++) {
            JSONObject jsonObject = (JSONObject) jsonArray.get(index);
            jsonObject.put("ORDER_NUM", index);
            jsonObject.put("STATUS", jsonObject.getBooleanValue("STATUS") ? "Y" : "N");
        }
        materialRiskDao.modifyRuleStatus(parameterMap);
        return response;
    }

    @Override
    public Response queryRuleById(Map<String, Object> parameterMap) {
        String rule = materialRiskDao.queryRuleById(parameterMap);
        return response.setBody(JSONArray.parseArray(rule));
    }

    @Override
    public Response deleteRule(Map<String, Object> parameterMap) {
        materialRiskDao.deleteRule(parameterMap);
        return response;
    }

    @Override
    public Response queryMaterialListByRule(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        parameterMap.put("levelSQL", this.getRuleSQL());
        page.setTotal(materialRiskDao.queryMaterialListByRuleCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(materialRiskDao.queryMaterialListByRule(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadMaterialListByRule(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        parameterMap.put("levelSQL", this.getRuleSQL());
        String fileName = "material_risk_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.adm.system.dao.IMaterialRiskDao.queryMaterialListByRule", parameterMap);
    }

    @Override
    public void recreateMaterialRiskLevelView() {
        Map<String, Object> parameterMap = new HashMap<>();
        try {
            parameterMap.put("levelSQL", StringUtils.replace(this.getRuleSQL(), "'", "''"));
            materialRiskDao.recreateMaterialRiskLevelView(parameterMap);
        } catch (Exception e) {
            materialRiskDao.createEmptyView();
        }
    }


    private static final AtomicBoolean RECREATE_MATERIAL_RISK_LEVEL_VIEW_SCHEDULED_RUNNING = new AtomicBoolean(false);

    @Override
    public Response queryAllAvailableComponentsScheduled(Map<String, Object> parameterMap) {
        Map<String, String> resultMap = new HashMap<>();

        try {
            if ("091e8da88be3484e94d6c09451e73540".equals(parameterMap.get("t"))) {
                if (RECREATE_MATERIAL_RISK_LEVEL_VIEW_SCHEDULED_RUNNING.get()) {
                    resultMap.put("result", "Job is running");
                } else {
                    RECREATE_MATERIAL_RISK_LEVEL_VIEW_SCHEDULED_RUNNING.set(true);
                    recreateMaterialRiskLevelView();
                    resultMap.put("result", "MView Refreshed");
                }
            } else {
                resultMap.put("result", "Invalid Token");
            }
        } catch (Exception e) {
            resultMap.put("result", "[error] " + e.getMessage());
        } finally {
            RECREATE_MATERIAL_RISK_LEVEL_VIEW_SCHEDULED_RUNNING.set(false);
        }
        return response.setBody(resultMap);
    }

    private String getRuleSQL() {
        List<Map<String, String>> rules = materialRiskDao.queryRules();

        if (rules.isEmpty()) {
            return "NULL";
        }
        List<String> result = new ArrayList<>();
        result.add("CASE");
        for (Map<String, String> map : rules) {
            result.add(this.getSqlByFilterChain(map.get("FILTER_CHAIN"), map.get("RISK_LEVEL")));
        }
        result.add("END");
        return StringUtils.join(result, " ");
    }

    private Map<String, String> displayFilterChain(String filterChain) {
        JSONArray jsonArray = JSONArray.parseArray(filterChain);
        Map<String, String> resultMap = new HashMap<>();
        int size = jsonArray.size();
        if (size == 0) {
            resultMap.put("rule1", "No Rule");
        }
        for (int i = 0; i < 2 && i < size; i++) {
            JSONObject element = jsonArray.getJSONObject(i);
            resultMap.put("rule" + (i + 1), this.displayRule(element));
        }
        String suffix = size <= 2 ? "" : "+" + (size - 2);
        resultMap.put("suffix", suffix);
        return resultMap;
    }

    private String displayRule(JSONObject element) {
        StringBuilder display = new StringBuilder(StringUtils.join(element.getJSONArray("fields"), ", "));
        display.append(" ");
        display.append(element.getString("operator"));
        display.append(" ");
        String value = element.getString("value");
        if (StringUtils.isBlank(value) || "[]".equals(value)) {
            value = element.getString("text").replace("\n", ", ");
        }
        if (value.length() > 36) {
            value = value.substring(0, 36);
        }
        value = StringUtils.remove(value, "，");
        value = StringUtils.remove(value, "\t");
        value = StringUtils.remove(value, "\r");
        value = StringUtils.remove(value, "\n");
        display.append(value);
        String finalDisplay = display.toString();
        if (display.toString().length() > 128) {
            finalDisplay = display.substring(0, 128);
        }
        return finalDisplay;
    }

    private String getSqlByFilterChain(String filterChain, String riskLevel) {
        List<FilterElement> filterElementList = new ArrayList<>();
        JSONArray array = JSONArray.parseArray(filterChain);
        FilterGenerator.rebuildFilterChain(array);
        for (Object obj : array) {
            filterElementList.add(new FilterElement((JSONObject) obj));
        }

        StringBuilder conditions = new StringBuilder();
        for (int i = 0; i < filterElementList.size(); i++) {
            FilterElement element = filterElementList.get(i);
            // 如果当前element不是or, 但是下一个element是or, 则插入左括号
            if (i != 0) {
                conditions.append(" ");
                conditions.append(element.getJoiner());
                conditions.append(" ");
            }
            if (element.isCurrentOr() == false && element.isNextOr() == true) {
                conditions.append(" (");
            }

            conditions.append(element.getRawSQL());

            // 如果当前element是or, 但是下一个element不是or, 则插入右括号
            if (element.isCurrentOr() == true && element.isNextOr() == false) {
                conditions.append(")");
            }
        }

        String resultSql = conditions.toString();
        if (Utils.hasInjectionAttack(resultSql)) {
            return "WHEN 1 = 0 THEN NULL";
        }
        return "WHEN " + conditions + " THEN '" + riskLevel + "'";
    }
}
