package com.adm.system.service.impl;

import com.starter.context.bean.Response;
import com.starter.utils.Utils;
import com.adm.system.dao.IDocumentationDao;
import com.adm.system.service.IDocumentationService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;

import java.io.BufferedReader;
import java.io.Reader;
import java.math.BigDecimal;
import java.sql.Clob;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Scope("prototype")
@Transactional
public class DocumentationServiceImpl implements IDocumentationService {

    @Resource
    private IDocumentationDao documentationDao;

    @Resource
    private Response response;

    @Override
    public Response initPage(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("existsGroup", documentationDao.queryExistsGroup());
        return response.setBody(resultMap);
    }

    @Override
    public Response queryDocList(Map<String, Object> parameterMap) {
        return response.setBody(Utils.parseTreeNodes(documentationDao.queryDocList()));
    }

    @Override
    public Response saveNewDoc(Map<String, Object> parameterMap) {
        parameterMap.put("docid", Utils.randomStr(12));
        documentationDao.saveNewDoc(parameterMap);
        return response;
    }

    @Override
    public Response queryDoc(Map<String, Object> parameterMap) {
        return response.setBody(documentationDao.queryDoc(parameterMap));
    }

    @Override
    public Response modifyDoc(Map<String, Object> parameterMap) {
        documentationDao.modifyDoc(parameterMap);
        return response;
    }

    @Override
    public Response deleteDoc(Map<String, Object> parameterMap) {
        documentationDao.deleteDoc(parameterMap);
        return response;
    }

    @Override
    public Response queryDocWithFunction(Map<String, Object> parameterMap) {
        Map<String, String> docoment = documentationDao.queryDoc(parameterMap);
        if (docoment == null) {
            docoment = new HashMap<>();
        }
        String content = docoment.getOrDefault("CONTENT", "### NO AVAILABLE DOCUMENTATION");
        // 替换自定义方法
        // Support $_INSERT_TABLE_SCHEMA(MATERIAL_MASTER_V)
        String[] insertTableSchema = this.getFuncInfoFromContent(content, "INSERT_TABLE_SCHEMA");
        if (insertTableSchema != null) {
            String orgStr = insertTableSchema[0];
            String tablename = insertTableSchema[1];
            String database = insertTableSchema[2];

            String tableSchemaInMakeDownFormat = this.getTableSchemaInMakeDownFormat(tablename, database);
            if (StringUtils.isNotBlank(tableSchemaInMakeDownFormat)) {
                content = StringUtils.replace(content, orgStr, tableSchemaInMakeDownFormat);
                docoment.put("CONTENT", content);
            }
        }
        return response.setBody(docoment);
    }

    private String getTableSchemaInMakeDownFormat(String tablename, String database) {
        StringBuilder result = new StringBuilder();
        try {
            result.append("| COLUMN NAME | DATA TYPE | NULLABLE | EXAMPLE |\r\n");
            result.append("|--|--|--|--|--|--|\r\n");
            List<Map<String, Object>> columns = documentationDao.queryColumnsByTableName(tablename, database);
            Map<String, Object> sample = documentationDao.queryOneRowForSample(tablename, database);
            if (sample == null) {
                sample = new HashMap<>();
            }
            for (Map<String, Object> map : columns) {
                result.append("|");
                result.append(Utils.removeMakeDownSpliter(map.get("COLUMN_NAME")));
                result.append("|");
                result.append(Utils.removeMakeDownSpliter(map.get("DATA_TYPE")));
                if (map.get("DATA_LENGTH") != null) {
                    result.append("(");
                    result.append(map.get("DATA_LENGTH"));
                    result.append(")");
                }
                result.append("|");
                result.append(Utils.removeMakeDownSpliter(map.get("NULLABLE")));
                result.append("|");
                result.append(Utils.removeMakeDownSpliter(sample.get((String) map.get("COLUMN_NAME"))));
                result.append("|\r\n");
            }

        } catch (Exception ignore) {
            return "";
        }
        return result.toString();
    }

    private String[] getFuncInfoFromContent(String content, String functionName) {
        if (StringUtils.isBlank(content)) {
            return null;
        }
        if (content.contains("$_" + functionName + "(")) {
            String patternString = "\\$_" + functionName + "\\(([\\w@]*)\\)";
            Pattern pattern = Pattern.compile(patternString);
            Matcher matcher = pattern.matcher(content);
            if (matcher.find()) {
                String orgStr = matcher.group(0);
                String tablename = matcher.group(1);
                String database = "";
                if (tablename.indexOf("@") == 1) {
                    database = StringUtils.upperCase(tablename.substring(tablename.indexOf("@")));
                    if ("@SCPDB01".equals(database) == false) {
                        database = "";
                    }
                }

                return new String[]{orgStr, tablename, database};
            } else {
                return null;
            }
        }
        return null;
    }
}
