package com.adm.system.service.impl;

import com.adm.system.bean.CalendarBean;
import com.adm.system.bean.ChangeLogBean;
import com.adm.system.bean.ChangeLogDetailsBean;
import com.adm.system.bean.SessionBean;
import com.adm.system.dao.ISystemDao;
import com.adm.system.service.ISystemService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.starter.context.bean.*;
import com.starter.health.jvm.JVMHealth;
import com.starter.login.bean.Session;
import com.starter.utils.Utils;
import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Scope("prototype")
@Transactional
public class SystemServiceImpl implements ISystemService {

    @Resource
    private ISystemDao systemDao;

    @Resource
    private Response response;

    @Value("${file.upload.path}")
    private String appUploadPath;

    @Value("${spring.monitor}")
    private boolean monitor;

    private final String allowedOriginPattern = monitor ? "https://scp-dss.cn.schneider-electric.com" : "*";

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public Response queryCalendar(Map<String, Object> parameterMap) {
        List<List<List<CalendarBean>>> year = new ArrayList<>();
        List<List<CalendarBean>> month = new ArrayList<>();
        List<CalendarBean> week = new ArrayList<>();

        List<CalendarBean> list = systemDao.queryCalendar(parameterMap);

        year.add(month);
        month.add(week);

        for (int i = 0; i < list.size(); i++) {
            CalendarBean calendarBean = list.get(i);
            if (calendarBean.isFirstDayOfWeek() && i != 0) {
                week = new ArrayList<>();
                month.add(week);
            }

            if (calendarBean.isFirstDayOfMonth() && i != 0) {
                while (week.size() > 0 && week.size() < 7) {
                    week.add(new CalendarBean());
                }

                month = new ArrayList<>();
                week = new ArrayList<>();
                month.add(week);
                year.add(month);
            }

            week.add(calendarBean);
        }

        while (week.size() > 0 && week.size() < 7) {
            week.add(new CalendarBean());
        }


        return response.setBody(year);
    }

    @Override
    public Response updateCalendar(Map<String, Object> parameterMap) {
        systemDao.updateCalendar(parameterMap);
        return response;
    }

    @Override
    public Response saveCalendarTemplate(Map<String, Object> parameterMap) {
        try {
            systemDao.saveCalendarTemplate(parameterMap);
            return response;
        } catch (Exception e) {
            return response.set(Status.FORBIDDEN, "Calendar duplicated");
        }
    }

    @Override
    public Response queryCalendarTemplate(Map<String, Object> parameterMap) {
        return response.setBody(systemDao.queryCalendarTemplate(parameterMap));
    }

    @Override
    public Response deleteCalendarTemplate(Map<String, Object> parameterMap) {
        systemDao.deleteCalendarTemplate(parameterMap);
        return response;
    }

    @Override
    public Response queryUsersLite(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);

        int total = systemDao.queryUsersLiteCount(parameterMap);
        page.setTotal(total);
        if (total > 0) {
            page.setData(systemDao.queryUsersLite(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public Response queryMenuLite(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);

        int total = systemDao.queryMenuLiteCount(parameterMap);
        page.setTotal(total);
        if (total > 0) {
            page.setData(systemDao.queryMenuLite(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public Response queryAuthDetails(Map<String, Object> parameterMap) {
        return response.setBody(systemDao.queryAuthDetails(parameterMap));
    }

    @Override
    public Response saveAuthDetails(Map<String, Object> parameterMap) {
        try {
            String accessible = String.valueOf(parameterMap.get("accessible"));
            String authDetails = (String) parameterMap.get("authDetails");
            String classification = (String) parameterMap.get("classification");

            // 如果访问当前菜单是public,并且用户有权限访问此菜单, 并且没有更详细的权限说明, 那么从表中删除此条记录
            if (StringUtils.equalsIgnoreCase(classification, "public") && StringUtils.isEmpty(authDetails) && accessible.equalsIgnoreCase("true")) {
                systemDao.deleteAuthDetails(parameterMap);
            } else {
                // 否则插入一条记录
                parameterMap.put("accessible", accessible);
                systemDao.insertAuthDetails(parameterMap);
            }
            this.deleteExpiredPrivileges();
        } catch (Exception e) {
            response.setError(e);
        }
        return response;
    }

    @Override
    public Response queryLoginSessions(Map<String, Object> parameterMap) {
        SimplePage<SessionBean> page = new SimplePage<>(parameterMap);

        Set<String> keys = redisTemplate.keys(Configuration.CACHED_SESSION_KEY + "*");

        if (keys != null) {
            List<SessionBean> resultList = new ArrayList<>();

            List<Map<String, String>> locationInfo = systemDao.queryIPLocationInfo();
            Map<String, String> locationInfoMap = new HashMap<>();
            for (Map<String, String> map : locationInfo) {
                locationInfoMap.put(map.get("IP_PREFIX"), map.get("LOCATION"));
            }

            for (String key : keys) {
                Session session = (Session) redisTemplate.opsForValue().get(key);
                if (session == null) {
                    continue;
                }

                Long expireTime = redisTemplate.getExpire(key);
                if (expireTime == null) {
                    continue;
                }

                String ip = session.getLoginIP();
                String location = "";
                if (StringUtils.isNotBlank(ip)) {
                    String prefix = StringUtils.substring(ip, 0, StringUtils.lastIndexOf(ip, "."));
                    location = locationInfoMap.get(prefix);
                    if (location == null) {
                        if (ip.startsWith("10.2")) {
                            location = "[Connected by VPN]";
                        }
                    }
                }

                SessionBean bean = new SessionBean();
                bean.setKey(key);
                bean.setUserid(session.getUserid());
                bean.setUsername(session.getUsername());
                bean.setLoginIP(ip);
                bean.setExpiringTimeLong(expireTime);
                bean.setLocation(location);

                resultList.add(bean);
            }
            resultList.sort((e1, e2) -> (int) (e2.getExpiringTimeLong() - e1.getExpiringTimeLong()));
            page.setTotal(resultList.size());
            page.setData(resultList);
        }

        return response.setBody(page);
    }

    @Override
    public Response clearLoginSessions(String key) {
        if ("all".equals(key)) {
            Set<String> keys = redisTemplate.keys(Configuration.CACHED_SESSION_KEY + "*");
            if (keys != null) {
                redisTemplate.delete(keys);
            }
        } else if (StringUtils.isNotBlank(key)) {
            redisTemplate.delete(key);
        }

        return response;
    }

    @Override
    public Response queryBaseMonth() {
        return response.setBody(systemDao.queryBaseMonth());
    }

    @Override
    public Response saveBaseMonth(Map<String, Object> parameterMap) {
        systemDao.saveBaseMonth(parameterMap);
        return response;
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryChangeLog(Map<String, Object> parameterMap) {
        List<ChangeLogBean> resultList = systemDao.queryChangeLog(parameterMap);
        for (ChangeLogBean cb : resultList) {
            cb.setLogs(this.convertLogs(systemDao.queryChangeLogDetails(cb.getSubmit())));
        }
        return response.setBody(resultList);
    }

    @Override
    @Cacheable(value = Configuration.APPLICATION_NAME + ":5m", condition = "#cachable")
    public Response queryNotification(Map<String, Object> parameterMap, Session session, boolean cachable) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("changeLog", systemDao.queryChangeLogNotification());
        resultMap.put("mail", systemDao.queryMailNotification(session.getEmail()));
        resultMap.put("maintainer", session.getMaintainer());
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryLastStartTime() {
        return response.setBody(JVMHealth.START_DATE);
    }

    @Override
    public Response initChangelogPage(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        List<String> releaseTime = new ArrayList<>();
        releaseTime.add(JVMHealth.START_DATE);
        releaseTime.add(new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date()));
        resultMap.put("releaseTime", releaseTime);

        String version = systemDao.queryCurrentVersion();
        version = StringUtils.isBlank(version) ? "0.4.0" : version;
        String[] svs = StringUtils.split(version, ".");
        int sv = Utils.parseInt(svs[svs.length - 1]) + 1;
        svs[svs.length - 1] = String.valueOf(sv);
        resultMap.put("version", StringUtils.join(svs, "."));

        List<String> categories = systemDao.queryMainMenu();
        categories.add("Others");
        resultMap.put("categories", categories);
        resultMap.put("raisedBy", systemDao.queryRaisedBy(parameterMap));

        return response.setBody(resultMap);
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = {"this.queryChangeLog", "this.queryNotification"})
    public Response saveChangelog(Map<String, Object> parameterMap) {
        parameterMap.put("id", Utils.randomStr(8));
        JSONArray commits = (JSONArray) parameterMap.get("commits");
        parameterMap.put("commit_ids", StringUtils.join(commits, ","));
        parameterMap.put("summary", parameterMap.get("releasedName") + " released this on " + parameterMap.get("releasedTime"));
        systemDao.saveChangelog(parameterMap);
        systemDao.saveChangelogDetails(parameterMap);

        return response;
    }

    public List<ChangeLogDetailsBean> convertLogs(List<Map<String, Object>> list) {
        Map<String, List<String>> tempMap = new HashMap<>();

        for (Map<String, Object> map : list) {
            String category = String.valueOf(map.get("CATEGORY"));
            String content = (String) map.get("CONTENT");
            String raiseBy = (String) map.get("RAISE_BY");
            List<String> l = tempMap.computeIfAbsent(category, k -> new ArrayList<>());
            if (StringUtils.isNotBlank(raiseBy)) {
                content += " (by @" + raiseBy + ")";
            }
            l.add(content);
        }

        List<ChangeLogDetailsBean> resultList = new ArrayList<>();
        for (String key : tempMap.keySet()) {
            ChangeLogDetailsBean bean = new ChangeLogDetailsBean();
            bean.setCategory(key);
            bean.setContents(tempMap.get(key));
            resultList.add(bean);
        }
        return resultList;
    }


    /**
     * 查询所有可用组件
     *
     * @param userid userid
     * @return Response
     */
    @Override
    public Response queryAllAvailableComponents(String userid) {
        int adminCount = systemDao.queryMyFavorityAdminCnt(userid);
        List<Map<String, Object>> list = systemDao.queryAllAvailableComponents(userid, adminCount > 0);
        List<List<Map<String, Object>>> resultList = new ArrayList<>();

        String groupName = null;
        List<Map<String, Object>> temp = new ArrayList<>();
        for (Map<String, Object> map : list) {
            if (StringUtils.equals(groupName, (String) map.get("GROUP_NAME")) == false) {
                groupName = (String) map.get("GROUP_NAME");
                temp = new ArrayList<>();
                resultList.add(temp);
            }
            map.put("CHECK", map.get("USER_ID") != null);
            temp.add(map);
        }

        return response.setBody(resultList);
    }

    @Override
    public Response saveMyFavourites(Map<String, Object> parameterMap) {
        systemDao.deleteMyFavourites(parameterMap);
        if (parameterMap.get("selectedObj") instanceof JSONArray && ((JSONArray) parameterMap.get("selectedObj")).isEmpty() == false) {
            systemDao.saveMyFavourites(parameterMap);
        }
        return response;
    }

    @Override
    public Response queryComponentById(Map<String, Object> parameterMap) {
        String params = systemDao.queryComponentById(parameterMap);
        if (StringUtils.isNotBlank(params)) {
            response.setBody(JSONObject.parseObject(params));
        }
        return response;
    }


    @Override
    public Response uploadMailSignature(String userid, MultipartFile file) {
        String html;
        String image;
        String imageHeader = "";
        if (file.getSize() > 200 * 1024) {
            return response.set(Status.FORBIDDEN, "上传文件不得超过200kb");
        }
        try {
            File tempFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), ".tmp");
            file.transferTo(tempFile);
            String fileName = file.getOriginalFilename();
            if (StringUtils.endsWithIgnoreCase(fileName, "htm")) {
                html = this.convertSignature2String(tempFile);
                html = Utils.delHTMLRemarks(html);

                if (systemDao.queryMailSignatureCount(userid, "HTML") > 0) {
                    systemDao.updateMailSignature(userid, html, "HTML");
                } else {
                    systemDao.insertMailSignature(userid, html, "HTML");
                }
            } else if (StringUtils.endsWithIgnoreCase(fileName, ".jpg") || StringUtils.endsWithIgnoreCase(fileName, ".png") || StringUtils.endsWithIgnoreCase(fileName, ".jpeg") || StringUtils.endsWithIgnoreCase(fileName, ".gif")) {

                image = Utils.convertImage2String(tempFile);
                if (StringUtils.endsWithIgnoreCase(fileName, ".jpg") || StringUtils.endsWithIgnoreCase(fileName, ".jpeg")) {
                    imageHeader = "data:image/jpeg;";
                } else if (StringUtils.endsWithIgnoreCase(fileName, ".png")) {
                    imageHeader = "data:image/png;";
                } else if (StringUtils.endsWithIgnoreCase(fileName, ".gif")) {
                    imageHeader = "data:image/gif;";
                }

                image = imageHeader + "base64," + image;

                if (systemDao.queryMailSignatureCount(userid, "IMAGE") > 0) {
                    systemDao.updateMailSignature(userid, image, "IMAGE");
                } else {
                    systemDao.insertMailSignature(userid, image, "IMAGE");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return response;
    }

    @Override
    public Response queryMailSignature(String userid) {
        return response.setBody(this.getMailSignature(userid));
    }

    @Override
    public String getMailSignature(String userid) {
        List<Map<String, Object>> signatures = systemDao.queryMailSignature(userid);
        if (signatures != null && signatures.isEmpty() == false) {
            String html = null;
            String image = null;

            for (Map<String, Object> signature : signatures) {
                String type = (String) signature.get("SIG_TYPE");
                if ("IMAGE".equals(type)) {
                    image = Utils.clob2String(signature.get("SIG_CONTENT"));
                } else if ("HTML".equals(type)) {
                    html = Utils.clob2String(signature.get("SIG_CONTENT"));
                }
            }

            if (html == null) {
                return "";
            } else if (image != null) {
                String regEx = "src=\"[\\s\\S]*?\"";

                Pattern pattern = Pattern.compile(regEx, Pattern.CASE_INSENSITIVE);
                Matcher matcher = pattern.matcher(html);
                html = matcher.replaceAll("src=\"" + image + "\"");
            }
            String randClass = "signature_" + Utils.randomStr(6);
            return "<div class=\"" + randClass + "\">" + "<style> " + "." + randClass + "{" + "line-height: 1 !important;" + "font-size: 12px;" + "} " + "." + randClass + " p{" + "margin: auto !important;" + "} " + "</style>" + html + "</div>";
        } else {
            return "";
        }
    }

    @Override
    public Response refreshMv(Map<String, Object> parameterMap) {
        String mvObj = (String) parameterMap.get("mv");
        String[] mvs = StringUtils.split(mvObj, ",");

        for (String mv : mvs) {
            mv = StringUtils.trim(mv);
            // 检查物化视图是否存在
            int count = systemDao.queryMVCount(mv);
            if (count == 1) {
                // 检查物化视图是否有索引, 物化视图一般都没有主键, 用这个方法刷新带主键的MV会导致主键丢失
                // 如果有索引, 需要保存索引生成语句
                List<Map<String, Object>> mvIndexs = systemDao.queryMVIndexs(mv);
                try {
                    // drop 索引
                    for (Map<String, Object> map : mvIndexs) {
                        String indexName = (String) map.get("INDEX_NAME");
                        systemDao.dropMVIndex(indexName);
                    }
                    // 刷新物化视图
                    systemDao.refreshMV(mv);
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    // 无论成功与否都要尝试把drop的索引加回去
                    for (Map<String, Object> map : mvIndexs) {
                        String indexName = (String) map.get("INDEX_NAME");
                        // 检查索引是否还存在
                        int indexCnt = systemDao.queryMVIndexCount(indexName);
                        // 索引不存在, 尝试把索引加回去
                        if (indexCnt == 0) {
                            systemDao.createMVIndex((String) map.get("DDL"));
                        }
                    }
                }
            }
        }

        return response;
    }

    @Override
    public Response queryCpuWorkload(Map<String, Object> parameterMap) {
        return response.setBody(systemDao.queryCpuWorkload(parameterMap));
    }

    @Override
    public Response querySurveyMenu(Map<String, Object> parameterMap) {
        int count = systemDao.querySurveyResultCount(parameterMap);
        if (count == 0) {
            String prop = systemDao.querySystemProperties("MANPOWER_SURVEY_202205");

            if ("Y".equals(prop)) {
                Map<String, Object> resultMap = new HashMap<>();
                resultMap.put("total", systemDao.querySurveyMenuCount(parameterMap));
                resultMap.put("data", systemDao.querySurveyMenu(parameterMap));
                return response.setBody(resultMap);
            } else {
                return response;
            }
        } else {
            return response;
        }
    }

    @Override
    public Response saveClickData(Map<String, Object> parameterMap) {
        systemDao.saveClickData(parameterMap);
        return response;
    }

    @Override
    @SuppressWarnings("unchecked")
    public Response queryClickData(Map<String, Object> parameterMap, String userid) {
        List<List<String>> users = (List<List<String>>) parameterMap.get("users");
        List<String> us = new ArrayList<>();
        if (users != null) {
            for (List<String> list : users) {
                us.add(list.get(1));
            }
        }
        parameterMap.put("users", us);
        return response.setBody(systemDao.queryClickData((parameterMap)));
    }

    public Response queryUserOpts(Session session) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("userOpts", Utils.parseCascader(systemDao.queryUserOpts()));
        List<List<String>> userField = new ArrayList<>();
        List<String> temp = new ArrayList<>();
        temp.add(session.getEntity());
        temp.add(session.getUserid());
        userField.add(temp);
        resultMap.put("userField", userField);
        return response.setBody(resultMap);
    }

    public Response queryClickShowAuth(Map<String, Object> parameterMap) {
        if (systemDao.queryClickShowAuth(parameterMap).isEmpty()) {
            return response.setBody(false);
        } else {
            return response.setBody(true);
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public Response submitSurvey(Map<String, Object> parameterMap) {
        Map<String, Object> form = (Map<String, Object>) parameterMap.get("form");
        String timeUnit = (String) form.remove("timeUnit");
        List<Map<String, Object>> data = new ArrayList<>();
        for (String key : form.keySet()) {
            if (key.endsWith("_COMMENTS") == true) {
                continue;
            }
            Map<String, Object> temp = new HashMap<>();
            temp.put("menuCode", key);
            temp.put("saving", form.get(key));
            temp.put("timeUnit", timeUnit);
            temp.put("comments", form.get(key + "_COMMENTS"));
            data.add(temp);
        }
        parameterMap.put("data", data);
        systemDao.submitSurvey(parameterMap);
        return response;
    }

    @Override
    public Response convertImageToBase64(MultipartFile file) throws Exception {
        String fileName = file.getOriginalFilename();
        fileName = fileName == null ? "image.jpg" : fileName;
        String suffix = fileName.substring(fileName.lastIndexOf("."));
        File tempFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), suffix);
        file.transferTo(tempFile);

        String image = "";
        String imageHeader = "";
        if (StringUtils.endsWithIgnoreCase(fileName, ".jpg") || StringUtils.endsWithIgnoreCase(fileName, ".png") || StringUtils.endsWithIgnoreCase(fileName, ".jpeg") || StringUtils.endsWithIgnoreCase(fileName, ".gif")) {

            File newTempFile = Utils.compressImage(tempFile, 1920, 1);
            image = Utils.convertImage2String(newTempFile);
            tempFile.delete();
            newTempFile.delete();

            if (StringUtils.endsWithIgnoreCase(fileName, ".jpg") || StringUtils.endsWithIgnoreCase(fileName, ".jpeg")) {
                imageHeader = "data:image/jpeg;";
            } else if (StringUtils.endsWithIgnoreCase(fileName, ".png")) {
                imageHeader = "data:image/png;";
            } else if (StringUtils.endsWithIgnoreCase(fileName, ".gif")) {
                imageHeader = "data:image/gif;";
            }

            image = imageHeader + "base64," + image;
        }
        return response.setBody(image);
    }

    // TODO 1. 保存文件到本地
    // TODO 2. 返回文件路径到前台并显示
    // TODO 2. 路径示例 https://scp-dss.cn.schneider-electric.com:8081/get_image_from_local?p=/2024/06/03/1097431098410.png
    // TODO 2. 路径示例 http://localhost:8081/get_image_from_local?p=/2024/06/03/1097431098410.png
    @Override
    public Response saveImageToLocal(MultipartFile file, String saveType) {
        String path = appUploadPath + "application/";
        if ("temp".equals(saveType)) {
            path += "temp/";
        } else {
            path += new SimpleDateFormat("yyyyMMdd").format(new Date()) + "/";
        }
        File dir = new File(path);
        if (!dir.exists()) {
            dir.mkdirs(); // 创建所有必需的父目录
        }
        String savePath = path + System.currentTimeMillis() + "_" + Utils.randomStr(4) + ".png";
        String absolutePath = new File(savePath).getAbsolutePath();
        try {
            file.transferTo(new File(absolutePath));
        } catch (Exception e) {
            e.printStackTrace();
        }
        String ap = URLEncoder.encode(absolutePath, StandardCharsets.UTF_8);
        if (monitor) {
            return response.setBody("https://scp-dss.cn.schneider-electric.com:8443/system/get_image_from_local?p=" + ap);
        } else {
            return response.setBody("http://localhost:8081/system/get_image_from_local?p=" + ap);
        }
    }

    @Override
    public void getImageFromLocal(String path, HttpServletResponse httpResponse) {
        httpResponse.setHeader("Access-Control-Allow-Origin", allowedOriginPattern);
        httpResponse.setHeader("Access-Control-Allow-Credentials", "true");
        httpResponse.setHeader("Access-Control-Allow-Methods", "GET");
        httpResponse.setHeader("Access-Control-Max-Age", "3600");
        httpResponse.setHeader("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept");
        File imageFile = new File(path);
        try {
            FileInputStream fis = new FileInputStream(imageFile);
            ServletOutputStream out = httpResponse.getOutputStream();
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
            httpResponse.setContentType("image/png");
            out.flush();
            out.close();
            fis.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private String convertSignature2String(File file) throws Exception {
        StringBuilder result = new StringBuilder();

        BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(file), "GB2312"));
        String line;
        while ((line = br.readLine()) != null) {
            result.append(line).append(" ");
        }
        br.close();
        if (result.length() > 0) {
            Document doc = Jsoup.parseBodyFragment(result.toString());
            return doc.body().html();
        }

        return result.toString();
    }

    /**
     * 最多每天执行一次清理异常权限的脚本
     */
    private void deleteExpiredPrivileges() {
        String key = Configuration.APPLICATION_NAME + ":1d::SystemServiceImpl.c.s.s.s.i:deleted-expired-privileges";
        Object valueObj = redisTemplate.opsForValue().get(key);
        if (valueObj == null) {
            systemDao.deleteExpiredPrivileges(key);
            redisTemplate.opsForValue().getAndSet(key, 1);
            redisTemplate.expire(key, 1, TimeUnit.DAYS);
        }
    }
}
