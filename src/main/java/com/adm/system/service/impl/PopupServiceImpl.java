package com.adm.system.service.impl;

import com.adm.system.dao.IPopupDao;
import com.adm.system.service.IPopupService;
import com.alibaba.fastjson.JSONObject;
import com.starter.context.bean.CacheRemove;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.adm.system.bean.PopupAuth;
import com.adm.system.bean.PopupDate;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

@Service
@Scope("prototype")
@Transactional
public class PopupServiceImpl implements IPopupService {

    @Resource
    private IPopupDao popupDao;

    @Resource
    private Response response;


    @Override
    public Response queryPopupConfig(Map<String, Object> parameterMap, String[] ipAddrs) {
        if (Arrays.asList(ipAddrs).contains("************")) {
            return response;
        }
        // resultMap 存放范围给前端要呈现的列表
        List<PopupAuth> resultMap = new ArrayList<>();
        // popupConfig 获取弹窗的数据权限信息
        List<Map<String, Object>> popupConfig = popupDao.queryPopupConfig(parameterMap);
        // 获取路由的访问历史
        List<PopupDate> visitHistory = popupDao.queryUrlVisitHistory(parameterMap);
        // 获取dss calendar的信息
        PopupDate currentDate = popupDao.queryCurrentDate(parameterMap);
        // 获取user master data用于后续访问弹窗权限判定
        Map<String, String> userInfo = popupDao.queryUserInfo(parameterMap);

        Map<String, PopupAuth> configMap = new LinkedHashMap<>();

        // 由于join权限表会扩行，此处for循环用于整合权限
        generateAuthConfig(popupConfig, configMap);

        // 获取sy calendar
        Date today = currentDate.getDate();
        int currentWeek = currentDate.getWeek();
        int currentMonth = currentDate.getMonth();
        int currentYear = currentDate.getYear();

        // 生成dialog清单
        for (PopupAuth remind : configMap.values()) {
            // 判断是否指定人看
            Map<String, List<String>> authInfo = remind.getAuthInfo();
            if (!authInfo.isEmpty()) {
                int count = 0;
                for (String authType : authInfo.keySet()) {
                    switch (authType) {
                        case "ENTITY_NAME":
                            if (userInfo != null && authInfo.get(authType).contains(userInfo.get("ENTITY_NAME"))) {
                                count += 1;
                            };
                        case "SESA_CODE":
                            if (userInfo != null && authInfo.get(authType).contains(userInfo.get("SESA_CODE"))) {
                                count += 1;
                            };
                        case "JOB_CODE":
                            if (userInfo != null && authInfo.get(authType).contains(userInfo.get("JOB_CODE"))) {
                                count += 1;
                            };
                    }
                }
                if (count == 0) {
                    continue;
                }
            }

            // 判断推送频次和次数
            Date validFrom = remind.getValidFrom();
            Date validTo = remind.getValidTo();
            LocalDateTime validFromDate = validFrom.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            LocalDateTime validToDate = validTo.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
            int remind_count = remind.getRemindCount();
            String frequency = remind.getFrequency();
            if ("DAILY".equalsIgnoreCase(frequency)) {
                long count = visitHistory.stream().map(date ->
                                date.getDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime())
                        .filter(localDate -> localDate.isAfter(validFromDate) && localDate.isBefore(validToDate))
                        .filter(localDate -> localDate.toLocalDate().equals(today.toInstant().atZone(ZoneId.systemDefault()).toLocalDate())).count();
                // 由于请求url时会先保存访问记录再计算popup url的访问次数，所以remind_count应 + 1 || count <= remind_count
                if (count <= remind_count) {
                    resultMap.add(remind);
                }
            } else if ("WEEKLY".equalsIgnoreCase(frequency)) {
                long count = visitHistory.stream()
                        .filter(dateInfo -> {
                            LocalDateTime localDate = dateInfo.getDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                            return localDate.isAfter(validFromDate) && localDate.isBefore(validToDate)
                                    && dateInfo.getWeek() == currentWeek && dateInfo.getYear() == currentYear;
                        })
                        .count();
                if (count <= remind_count) {
                    resultMap.add(remind);
                }
            } else if ("MONTHLY".equalsIgnoreCase(frequency)) {
                long count = visitHistory.stream()
                        .filter(dateInfo -> {
                            LocalDateTime localDate = dateInfo.getDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                            return localDate.isAfter(validFromDate) && localDate.isBefore(validToDate)
                                    && dateInfo.getMonth() == currentMonth && dateInfo.getYear() == currentYear;
                        })
                        .count();
                if (count <= remind_count) {
                    resultMap.add(remind);
                }
            } else if ("YEARLY".equalsIgnoreCase(frequency)) {
                long count = visitHistory.stream()
                        .filter(dateInfo -> {
                            LocalDateTime localDate = dateInfo.getDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                            return localDate.isAfter(validFromDate) && localDate.isBefore(validToDate)
                                    && dateInfo.getYear() == currentYear;
                        })
                        .filter(dateInfo -> dateInfo.getYear() == currentYear)
                        .count();
                if (count <= remind_count) {
                    resultMap.add(remind);
                }
            }
        }

        return response.setBody(resultMap);
    }

    private static void generateAuthConfig(List<Map<String, Object>> popupConfig, Map<String, PopupAuth> configMap) {
        for (Map<String, Object> row : popupConfig) {
            String urlKey = (String) row.get("URL");
            String remindSubjectKey = (String) row.get("REMIND_SUBJECT");
            String key = urlKey + "-" + remindSubjectKey;

            PopupAuth config = configMap.getOrDefault(key, new PopupAuth());
            config.setUrl(urlKey);
            config.setRemindSubject(remindSubjectKey);
            config.setFrequency((String) row.get("FREQUENCY"));
            config.setRemindCount((Integer) ((BigDecimal) row.get("REMIND_COUNT")).intValueExact());
            config.setDescription((String) row.get("DESCRIPTION"));
            config.setValidFrom((Date) row.get("VALID_FROM"));
            config.setValidTo((Date) row.get("VALID_TO"));

            String authType = (String) row.get("AUTH_TYPE");
            String authDetail = (String) row.get("AUTH_DETAIL");

            if (config.getAuthInfo() == null) {
                config.setAuthInfo(new HashMap<>());
            }
            // 创建或更新 authInfo 的键值对
            if (authType != null) {
                config.getAuthInfo().computeIfAbsent(authType, k -> new ArrayList<>()).add(authDetail);
            }
            configMap.put(key, config);
        }
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response saveNeverDisplay(Map<String, Object> parameterMap) {
        popupDao.saveNeverDisplay(parameterMap);
        return response;
    }
}
