package com.adm.system.service.impl;

import com.adm.system.bean.DatagridDropdown;
import com.adm.system.dao.IDataGridDao;
import com.adm.system.service.IDataGridService;
import com.alibaba.fastjson.JSONArray;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.bean.Status;
import com.starter.login.bean.Session;
import com.starter.utils.DateCalUtil;
import com.starter.utils.GZipUtil;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import com.starter.utils.excel.SheetInfoWithData;
import com.starter.utils.excel.SimpleSheetContentsHandler;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.executor.BatchExecutorException;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.poi.xssf.model.StylesTable;
import org.springframework.context.annotation.Scope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.*;

@Service
@Scope("prototype")
@Transactional
public class DataGridServiceImpl implements IDataGridService {

    @Resource
    private IDataGridDao dataGridDao;

    @Resource
    private Response response;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private SqlSessionFactory sqlSessionFactory;

    @Resource
    private ExcelTemplate excelTemplate;

    private final List<String> SYSTEM_FILEDS = new ArrayList<>() {
        {
            add("CREATE_BY$");
            add("CREATE_DATE$");
            add("UPDATE_BY$");
            add("UPDATE_DATE$");
        }
    };

    @Override
    public Response queryColumnDef(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("COLUMN_DEF", dataGridDao.queryColumnDef(parameterMap));

        List<Map<String, String>> dropdownList = dataGridDao.queryColumnDropdown(parameterMap);

        Map<String, DatagridDropdown> dropdownMap = new HashMap<>();
        for (Map<String, String> map : dropdownList) {
            dropdownMap.put(map.get("FIELD_NAME"), new DatagridDropdown(map.get("CELL_TYPE"), map.get("CONTENT")));
        }

        resultMap.put("COLUMN_DROPDOWN", dropdownMap);
        return response.setBody(resultMap);
    }

    @Override
    @SuppressWarnings("unchecked")
    public Response queryData(Map<String, Object> parameterMap) {
        if (Utils.hasInjectionAttack(parameterMap.get("bindTo")) == true) {
            return response.setError(new Exception("Invalid bindTo!"));
        }
        Map<String, Object> defineMap = new HashMap<>();
        Map<String, Object> formatMap = new HashMap<>();
        List<String> colOrgHeaders = (List<String>) parameterMap.get("_colOrgHeaders");
        List<String> colOrgTypes = (List<String>) parameterMap.get("_colOrgTypes");
        List<String> colOrgFormats = (List<String>) parameterMap.get("_colOrgFormats");

        int i = 0;
        for (String header : colOrgHeaders) {
            String type = colOrgTypes.get(i);
            String format = colOrgFormats.get(i++);

            if (Utils.hasInjectionAttack(header) == true || Utils.hasInjectionAttack(type) == true) {
                return response.setError(new Exception("Invalid cols!"));
            }
            defineMap.put(header, type);
            formatMap.put(header, this.convertDateFormat(format));
        }

        parameterMap.put("define", defineMap);
        parameterMap.put("format", formatMap);

        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        String where = (String) parameterMap.get("_where");
        String conditions = page.getConditions() == null ? "" : page.getConditions().getSql();
        if (StringUtils.isNotBlank(where)) {
            if (StringUtils.isNotBlank(conditions)) {
                conditions += " AND (" + where + ")";
            } else {
                conditions = where;
            }
        }
        parameterMap.put("_final_conditions", conditions);
        int total = dataGridDao.queryDataCount(parameterMap);
        page.setTotal(total);
        if (total > 0) {
            page.setData(dataGridDao.queryDataPagging(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    @SuppressWarnings("unchecked")
    public Response saveChanges(Map<String, Object> parameterMap) {
        if (Utils.hasInjectionAttack(parameterMap.get("bindTo")) == true) {
            return response.setError(new Exception("Invalid bindTo!"));
        }

        Session loginSession = (Session) parameterMap.get("session");
        SqlSession session = sqlSessionFactory.openSession(ExecutorType.BATCH, false);
        IDataGridDao gridDao = session.getMapper(IDataGridDao.class);

        Map<String, Object> defineMap = new HashMap<>();
        Map<String, Object> formatMap = new HashMap<>();
        List<String> colOrgHeaders = (List<String>) parameterMap.get("_colOrgHeaders");
        List<String> colOrgTypes = (List<String>) parameterMap.get("_colOrgTypes");
        List<String> colOrgFormats = (List<String>) parameterMap.get("_colOrgFormats");
        boolean hasSystemFields = new HashSet<>(colOrgHeaders).containsAll(SYSTEM_FILEDS);

        int i = 0;
        for (String header : colOrgHeaders) {
            String type = colOrgTypes.get(i);
            String format = colOrgFormats.get(i++);

            if (Utils.hasInjectionAttack(header) == true || Utils.hasInjectionAttack(type) == true) {
                return response.setError(new Exception("Invalid cols!"));
            }

            defineMap.put(header, type);
            formatMap.put(header, this.convertDateFormat(format));
        }

        // 移除所有系统字段, 后面会补上
        if (hasSystemFields) {
            colOrgHeaders.removeAll(SYSTEM_FILEDS);
        }

        try {
            // Save
            if (parameterMap.containsKey("createdRows")) {
                List<Map<String, Object>> createdRows = Utils.clearList(parameterMap.get("createdRows"));
                if (createdRows.size() > 0) {
                    parameterMap.put("define", defineMap);
                    parameterMap.put("format", formatMap);
                    parameterMap.put("hasSystemFields", hasSystemFields);
                    parameterMap.put("userid", loginSession.getUserid());
                    gridDao.saveChanges(parameterMap);
                }
            }

            // Update
            if (parameterMap.containsKey("updatedRows")) {
                Map<String, Object> updateMap = (Map<String, Object>) parameterMap.get("updatedRows");
                Map<String, Object> param = new HashMap<>();
                param.put("bindTo", parameterMap.get("bindTo"));
                for (String key : updateMap.keySet()) {
                    List<Map<String, Object>> cols = new ArrayList<>();
                    param.put("rowid", key);
                    param.put("cols", cols);

                    Map<String, Object> colMap = (Map<String, Object>) updateMap.get(key);
                    for (String k : colMap.keySet()) {
                        if (Utils.hasInjectionAttack(k) == true) {
                            return response.setError(new Exception("Invalid update parameters!"));
                        }

                        if (SYSTEM_FILEDS.contains(k)) {
                            continue;
                        }

                        Map<String, Object> col = new HashMap<>();
                        col.put("key", k);
                        col.put("value", colMap.get(k));
                        cols.add(col);
                    }
                    param.put("define", defineMap);
                    param.put("format", formatMap);
                    param.put("hasSystemFields", hasSystemFields);
                    param.put("userid", loginSession.getUserid());
                    gridDao.updateChanges(param);
                }
            }

            // Delete
            if (parameterMap.containsKey("removedRows")) {
                List<String> removedRows = (List<String>) parameterMap.get("removedRows");
                if (removedRows.size() > 0) {
                    gridDao.deleteChanges(parameterMap);
                }
            }
            session.commit();
            return response.setBody(1);
        } catch (Exception e) {
            if (e.getCause() instanceof BatchExecutorException) {
                BatchExecutorException batchExecutorException = (BatchExecutorException) e.getCause();
                response.set(Status.FORBIDDEN, batchExecutorException.getCause().getMessage());
            } else {
                e.printStackTrace();
                response.setError(e);
            }
            session.rollback();
        } finally {
            session.close();

            String cacheKeys = (String) parameterMap.get("cacheKeys");
            if (StringUtils.isNotBlank(cacheKeys)) {
                cacheKeys = Configuration.APPLICATION_NAME + ":" + cacheKeys + ":*";
                Set<String> keys = redisTemplate.keys(cacheKeys);
                if (keys != null) {
                    redisTemplate.delete(keys);
                }
            }
        }

        return response.setBody(-1);
    }

    @Override
    public void downloadTemplate(String bindTo, HttpServletResponse response) {
        String fileName = StringUtils.lowerCase(bindTo) + "_template_" + Utils.randomStr(4) + ".xlsx";
        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        resultList.add(map);
        List<Map<String, Object>> columnDef = dataGridDao.queryColumnDef(new HashMap<>() {{
            put("bindTo", bindTo);
        }});
        for (Map<String, Object> column : columnDef) {
            String columnName = (String) column.get("COLUMN_NAME");
            if (SYSTEM_FILEDS.contains(columnName) == false) {
                map.put(columnName, null);
            }
        }

        // 根据SY_TABLE_DROPDOWN, 给模板定义输入规范
        Map<String, Object> params = new HashMap<>();
        params.put("bindTo", bindTo);
        List<Map<String, String>> columnValidation = dataGridDao.queryColumnDropdown(params);

        SheetInfoWithData sheetInfo = new SheetInfoWithData();
        sheetInfo.setSheetName("Sheet1");
        sheetInfo.setDataList(resultList);
        if (columnValidation.isEmpty() == false) {
            Map<String, List<String>> validationMap = new HashMap<>();
            for (Map<String, String> cv : columnValidation) {
                try {
                    validationMap.put(cv.get("FIELD_NAME"), JSONArray.parseArray(cv.get("CONTENT"), String.class));
                } catch (Exception ignore) {

                }
            }
            sheetInfo.setValidationMap(validationMap);
        }
        excelTemplate.create(response, fileName, sheetInfo);
    }

    @Override
    @SuppressWarnings("unchecked")
    public void downloadData(Map<String, Object> parameterMap, HttpServletResponse response) {
        String fileName = StringUtils.lowerCase((String) parameterMap.get("bindTo")) + "_data_" + Utils.randomStr(4) + ".xlsx";

        if (Utils.hasInjectionAttack(parameterMap.get("bindTo")) == true) {
            return;
        }

        Map<String, Object> defineMap = new HashMap<>();
        Map<String, Object> formatMap = new HashMap<>();
        List<String> colOrgHeaders = (List<String>) parameterMap.get("_colOrgHeaders");
        List<String> colOrgTypes = (List<String>) parameterMap.get("_colOrgTypes");
        List<String> colOrgFormats = (List<String>) parameterMap.get("_colOrgFormats");
        boolean systemFields = Boolean.parseBoolean(String.valueOf(parameterMap.get("systemFields")));

        List<String> newColOrgHeaders = new ArrayList<>(); // 删除System field
        List<String> newColOrgTypes = new ArrayList<>();

        int i = 0;
        for (String header : colOrgHeaders) {
            if (systemFields == false) {
                if (header.endsWith("$")) {
                    i++;
                    continue;
                }
            }

            String type = colOrgTypes.get(i);
            String format = colOrgFormats.get(i++);

            newColOrgHeaders.add(header);
            newColOrgTypes.add(type);

            if (Utils.hasInjectionAttack(header) == true || Utils.hasInjectionAttack(type) == true) {
                return;
            }
            defineMap.put(header, type);
            formatMap.put(header, this.convertDateFormat(format));
        }

        parameterMap.put("define", defineMap);
        parameterMap.put("format", formatMap);
        parameterMap.put("newColOrgHeaders", newColOrgHeaders);
        parameterMap.put("newColOrgTypes", newColOrgTypes);

        new SimplePage<>(parameterMap);
        excelTemplate.create(response, fileName, "com.adm.system.dao.IDataGridDao.downloadAllData", parameterMap);
    }

    @Override
    public Response queryAvalibleColumnsByUrl(Map<String, Object> parameterMap) {
        return response.setBody(GZipUtil.uncompress(dataGridDao.queryAvalibleColumnsByUrl(parameterMap)));
    }

    @Override
    public Response uploadData(String uploadBy, String uploadModule, String bindTo, String cacheKeys, File sourceFile) {
        // 获取表主键和唯一索引,用来生成hint和merger语句
        List<Map<String, Object>> columnConstraint = dataGridDao.queryColumnConstraint(bindTo, SYSTEM_FILEDS);

        List<String> defColumnList = new ArrayList<>(); // 数据库中定义的所有列名
        List<String> defPkColumnList = new ArrayList<>(); // 数据库中定义的pk列或唯一索引列
        List<String> defNormalColumnList = new ArrayList<>(); // 数据库中定义的非pk列和唯一索引列
        String constraintName = null;
        Map<String, String> columnType = new HashMap<>();

        for (Map<String, Object> map : columnConstraint) {
            String type = (String) map.get("CONSTRAINT_TYPE");
            String name = (String) map.get("COLUMN_NAME");
            columnType.put(name, (String) map.get("DATA_TYPE"));
            if ("P".equals(type) || "U".equals(type)) {
                defPkColumnList.add(name);
                if (constraintName == null) {
                    constraintName = (String) map.get("CONSTRAINT_NAME");
                }
            } else {
                defNormalColumnList.add(name);
            }
            defColumnList.add(name);
        }

        boolean hasSystemFields = defColumnList.containsAll(SYSTEM_FILEDS);

        // 在主键或唯一索引缺失的情况下,不能使用merger进行数据合并
        if (defPkColumnList.isEmpty() && "MERGE".equals(uploadModule)) {
            return response.set(Status.FORBIDDEN, bindTo + " does not have a primary key and cannot upload data in [MERGE] mode.<br><br> Please use [INSERT-IGNORE] or [REPLACE-ALL] instead.");
        }

        if (hasSystemFields == false && "REPLACE-MY-DATA".equals(uploadModule)) {
            return response.set(Status.FORBIDDEN, bindTo + " does not have 'create_by' field and cannot upload data in [REPLACE-MY-DATA] mode.<br><br> Please use [INSERT-IGNORE] or [REPLACE-ALL] instead.");
        }

        // 根据upload modual生成hint
        final String hint = (("INSERT-IGNORE".equals(uploadModule) || "REPLACE-ALL".equals(uploadModule)) && defPkColumnList.isEmpty() == false) ? "/*+  APPEND IGNORE_ROW_ON_DUPKEY_INDEX (" + bindTo + "," + constraintName + ") */" : "/*+ APPEND NOLOGGING */";

        // 获取文件中的表头信息, 判断是否和数据库定义的列不同, 表头中可能会有空单元格
        List<String> headers = new ArrayList<>(); // 上传文件的表头信息

        excelTemplate.read(sourceFile, 1, new SimpleSheetContentsHandler() {
            @Override
            public void handleRow(int rowNum) {

                if (rowNum == 0) {
                    for (String s : row) {
                        headers.add(StringUtils.upperCase(StringUtils.trim(s)));
                    }
                }
            }
        }, new StylesTable());

        // 开始判断
        List<String> invalidHeaders = new ArrayList<>(); // 文件中未包含在数据库定义中的列
        List<String> pkHeaders = new ArrayList<>(); // 是主键的header
        List<String> normalHeaders = new ArrayList<>(); // 正常的header
        List<String> trimHeaders = new ArrayList<>(); // 去除空格之后的header
        for (String h : headers) {
            if (StringUtils.isEmpty(h) || StringUtils.endsWith(h, "$")) {
                continue;
            }
            trimHeaders.add(h);
            if (defPkColumnList.contains(h)) {
                defPkColumnList.remove(h);
                pkHeaders.add(h);
            } else if (defNormalColumnList.contains(h)) {
                defNormalColumnList.remove(h);
                normalHeaders.add(h);
            } else {
                invalidHeaders.add(h);
            }
        }

        // 抛出异常
        if (invalidHeaders.isEmpty() == false) {
            return response.set(Status.FORBIDDEN, bindTo + " 请检查上传文件的格式, 发现非法列 [" + StringUtils.join(invalidHeaders, ",") + ']');
        }

        // defPkColumnList经过移除, 如果不为空, 说明header中并未包含所有的主键, 异常
        if (defPkColumnList.isEmpty() == false) {
            return response.set(Status.FORBIDDEN, "The uploaded file does not contain primary key [" + StringUtils.join(defPkColumnList, ",") + ']');
        }

        // 如果上传模式是替换所有, 那么需要删除原有数据, 这里使用delete而不是truncate, 是为了可以回滚
        if (uploadModule.equals("REPLACE-ALL")) {
            dataGridDao.deleteUploadData(bindTo);
        } else if (uploadModule.equals("REPLACE-MY-DATA")) {
            dataGridDao.deleteCurrentUserUploadData(bindTo, uploadBy);
        }

        List<Map<String, Object>> dataList = new ArrayList<>();

        excelTemplate.read(sourceFile, 1, new SimpleSheetContentsHandler() {
            @Override
            public void handleRow(int rowNum) {

                if (rowNum == 0) {
                    return;
                }

                // 从excel中取数据的时候要使用原始的表头
                Map<String, Object> map = new HashMap<>();
                boolean isBlackLine = true;
                for (int i = 0; i < headers.size(); i++) {
                    String h = headers.get(i);
                    String v = row.get(i);
                    if (StringUtils.isBlank(h)) {
                        continue;
                    }
                    if (StringUtils.isNotBlank(v)) {
                        isBlackLine = false;
                        map.put(h, v);
                    } else {
                        map.put(h, null);
                    }

                }
                if (isBlackLine == false) {
                    dataList.add(map);
                }

                // 插入数据的时候要使用去空格之后的表头,否则会出现错行
                if (dataList.size() >= 200) {
                    replaceExcleDate(trimHeaders, columnType, dataList);
                    if ("MERGE".equals(uploadModule)) {
                        dataGridDao.mergeUploadData(bindTo, trimHeaders, pkHeaders, normalHeaders, columnType, dataList, hasSystemFields, uploadBy);
                    } else {
                        dataGridDao.insertUploadData(bindTo, hint, trimHeaders, columnType, dataList, hasSystemFields, uploadBy);
                    }
                    dataList.clear();
                }
            }
        }, new StylesTable());

        if (dataList.size() > 0) {
            replaceExcleDate(trimHeaders, columnType, dataList);
            if ("MERGE".equals(uploadModule)) {
                dataGridDao.mergeUploadData(bindTo, trimHeaders, pkHeaders, normalHeaders, columnType, dataList, hasSystemFields, uploadBy);
            } else {
                dataGridDao.insertUploadData(bindTo, hint, trimHeaders, columnType, dataList, hasSystemFields, uploadBy);
            }
            dataList.clear();
        }

        if (StringUtils.isNotBlank(cacheKeys)) {
            cacheKeys = Configuration.APPLICATION_NAME + ":" + cacheKeys + ":*";
            Set<String> keys = redisTemplate.keys(cacheKeys);
            if (keys != null) {
                redisTemplate.delete(keys);
            }
        }

        return response;
    }

    @Override
    public Response uploadDataWithTempTable(String uploadBy, String uploadModule, String bindTo, String cacheKeys, File sourceFile) {
        // 获取表主键和唯一索引,用来生成hint和merger语句
        List<Map<String, Object>> columnConstraint = dataGridDao.queryColumnConstraint(bindTo, SYSTEM_FILEDS);

        List<String> defColumnList = new ArrayList<>(); // 数据库中定义的所有列名
        List<String> defPkColumnList = new ArrayList<>(); // 数据库中定义的pk列或唯一索引列
        List<String> defNormalColumnList = new ArrayList<>(); // 数据库中定义的非pk列和唯一索引列
        String constraintName = null;
        Map<String, String> columnType = new HashMap<>();

        for (Map<String, Object> map : columnConstraint) {
            String type = (String) map.get("CONSTRAINT_TYPE");
            String name = (String) map.get("COLUMN_NAME");
            columnType.put(name, (String) map.get("DATA_TYPE"));
            if ("P".equals(type) || "U".equals(type)) {
                defPkColumnList.add(name);
                if (constraintName == null) {
                    constraintName = (String) map.get("CONSTRAINT_NAME");
                }
            } else {
                defNormalColumnList.add(name);
            }
            defColumnList.add(name);
        }

        boolean hasSystemFields = defColumnList.containsAll(SYSTEM_FILEDS);

        // 在主键或唯一索引缺失的情况下,不能使用merger进行数据合并
        if (defPkColumnList.isEmpty() && "MERGE".equals(uploadModule)) {
            return response.set(Status.FORBIDDEN, bindTo + " does not have a primary key and cannot upload data in [MERGE] mode.<br><br> Please use [INSERT-IGNORE] or [REPLACE-ALL] instead.");
        }

        if (hasSystemFields == false && "REPLACE-MY-DATA".equals(uploadModule)) {
            return response.set(Status.FORBIDDEN, bindTo + " does not have 'create_by' field and cannot upload data in [REPLACE-MY-DATA] mode.<br><br> Please use [INSERT-IGNORE] or [REPLACE-ALL] instead.");
        }

        // 根据upload modual生成hint
        final String hint = (("INSERT-IGNORE".equals(uploadModule) || "REPLACE-ALL".equals(uploadModule)) && defPkColumnList.isEmpty() == false) ? "/*+  APPEND IGNORE_ROW_ON_DUPKEY_INDEX (" + bindTo + "," + constraintName + ") */" : "/*+ APPEND NOLOGGING */";

        // 获取文件中的表头信息, 判断是否和数据库定义的列不同, 表头中可能会有空单元格
        List<String> headers = new ArrayList<>(); // 上传文件的表头信息

        excelTemplate.read(sourceFile, 1, new SimpleSheetContentsHandler() {
            @Override
            public void handleRow(int rowNum) {

                if (rowNum == 0) {
                    for (String s : row) {
                        headers.add(StringUtils.upperCase(StringUtils.trim(s)));
                    }
                }
            }
        }, new StylesTable());

        // 开始判断
        List<String> invalidHeaders = new ArrayList<>(); // 文件中未包含在数据库定义中的列
        List<String> pkHeaders = new ArrayList<>(); // 是主键的header
        List<String> normalHeaders = new ArrayList<>(); // 正常的header
        List<String> trimHeaders = new ArrayList<>(); // 去除空格之后的header
        for (String h : headers) {
            if (StringUtils.isEmpty(h) || StringUtils.endsWith(h, "$")) {
                continue;
            }
            trimHeaders.add(h);
            if (defPkColumnList.contains(h)) {
                defPkColumnList.remove(h);
                pkHeaders.add(h);
            } else if (defNormalColumnList.contains(h)) {
                defNormalColumnList.remove(h);
                normalHeaders.add(h);
            } else {
                invalidHeaders.add(h);
            }
        }

        // 抛出异常
        if (invalidHeaders.isEmpty() == false) {
            return response.set(Status.FORBIDDEN, bindTo + " 请检查上传文件的格式, 发现非法列 [" + StringUtils.join(invalidHeaders, ",") + ']');
        }

        // defPkColumnList经过移除, 如果不为空, 说明header中并未包含所有的主键, 异常
        if (defPkColumnList.isEmpty() == false) {
            return response.set(Status.FORBIDDEN, "The uploaded file does not contain primary key [" + StringUtils.join(defPkColumnList, ",") + ']');
        }

        // 如果上传模式是替换所有, 那么需要删除原有数据, 这里使用delete而不是truncate, 是为了可以回滚
        if (uploadModule.equals("REPLACE-ALL")) {
            dataGridDao.deleteUploadData(bindTo);
        } else if (uploadModule.equals("REPLACE-MY-DATA")) {
            dataGridDao.deleteCurrentUserUploadData(bindTo, uploadBy);
        }

        List<Map<String, Object>> dataList = new ArrayList<>();

        // 创建一个临时表
        String tempTableName = bindTo + "_DATAGRID_TEMP_" + Utils.randomStr(4);
        dataGridDao.createEmptyTempTable(tempTableName, bindTo);

        try {
            excelTemplate.read(sourceFile, 1, new SimpleSheetContentsHandler() {
                @Override
                public void handleRow(int rowNum) {

                    if (rowNum == 0) {
                        return;
                    }

                    // 从excel中取数据的时候要使用原始的表头
                    Map<String, Object> map = new HashMap<>();
                    boolean isBlackLine = true;
                    for (int i = 0; i < headers.size(); i++) {
                        String h = headers.get(i);
                        String v = row.get(i);
                        if (StringUtils.isBlank(h)) {
                            continue;
                        }
                        if (StringUtils.isNotBlank(v)) {
                            isBlackLine = false;
                            map.put(h, v);
                        } else {
                            map.put(h, null);
                        }

                    }
                    if (isBlackLine == false) {
                        dataList.add(map);
                    }

                    // 插入数据的时候要使用去空格之后的表头,否则会出现错行
                    if (dataList.size() >= 100) {
                        replaceExcleDate(trimHeaders, columnType, dataList);
                        dataGridDao.insertTempTable(tempTableName, trimHeaders, columnType, dataList);
                        dataList.clear();
                    }
                }
            }, new StylesTable());

            if (dataList.size() > 0) {
                replaceExcleDate(trimHeaders, columnType, dataList);
                dataGridDao.insertTempTable(tempTableName, trimHeaders, columnType, dataList);
                dataList.clear();
            }

            // 将temp表中的数据merge或者是replace到正式表中
            dataGridDao.createEmptyTempTableIndex(tempTableName, pkHeaders);
            if ("MERGE".equals(uploadModule)) {
                dataGridDao.mergeTempIntoTable(tempTableName, bindTo, trimHeaders, pkHeaders, normalHeaders, columnType, dataList, hasSystemFields, uploadBy);
            } else {
                dataGridDao.mergeTempIntoTable2(tempTableName, bindTo, trimHeaders, pkHeaders, normalHeaders, columnType, dataList, hasSystemFields, uploadBy);
            }

            if (StringUtils.isNotBlank(cacheKeys)) {
                cacheKeys = Configuration.APPLICATION_NAME + ":" + cacheKeys + ":*";
                Set<String> keys = redisTemplate.keys(cacheKeys);
                if (keys != null) {
                    redisTemplate.delete(keys);
                }
            }
        } finally {
            dataGridDao.dropTempTable(tempTableName);
        }
        return response;
    }

    private static void replaceExcleDate(List<String> headers, Map<String, String> columnType, List<Map<String, Object>> dataList) {
        List<String> dataHeaders = new ArrayList<>();
        for (String header : headers) {
            if ("DATE".equals(columnType.get(header))) {
                dataHeaders.add(header);
            }
        }

        if (dataHeaders.isEmpty() == false) {
            for (String header : dataHeaders) {
                for (Map<String, Object> map : dataList) {
                    map.put(header, DateCalUtil.excelNumber2Day(map.get(header)));
                }
            }
        }
    }

    /**
     * 将前台传过来的数据格式转换为Oracle可识别的格式
     *
     * @param format mement.js的日期格式
     * @return Oracle可识别格式
     */
    private String convertDateFormat(String format) {
        format = StringUtils.replace(format, "HH", "HH24");
        format = StringUtils.replace(format, "mm", "mi");
        return format;
    }
}
