package com.adm.system.service.impl;

import com.adm.system.bean.DataSyncJobInfo;
import com.adm.system.bean.DataSyncLink;
import com.adm.system.bean.DataSyncNode;
import com.adm.system.dao.IDataSyncDao;
import com.adm.system.dao.ISystemDao;
import com.adm.system.service.IDataSyncService;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.starter.context.bean.Response;
import com.starter.login.bean.Session;
import com.starter.utils.Utils;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.InetAddress;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Scope("prototype")
@Transactional
public class DataSyncServiceImpl implements IDataSyncService {

    @Resource
    private IDataSyncDao dataSyncDao;

    @Resource
    private Response response;

    @Resource
    private ISystemDao systemDao;

    boolean dropIndex = false;

    @Override
    public Response formatJson(Map<String, Object> parameterMap) {
        Object json = parameterMap.get("json");
        try {
            return response.setBody(this.formatJSON(json));
        } catch (Exception e) {
            return response.setBody(json);
        }
    }

    @Override
    public Response queryReport1(Map<String, Object> parameterMap) {
        List<Map<String, Object>> dataList = dataSyncDao.queryReport1(parameterMap);
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (Map<String, Object> mv : dataList) {
            Map<String, Object> temp = new HashMap<>();
            temp.put("label", "[" + mv.get("STATUS") + "] " + mv.get("NAME"));
            temp.put("key", mv.get("NAME"));
            resultList.add(temp);
        }
        return response.setBody(resultList);
    }

    @Override
    public Response queryReport1TemplateOpts() {
        return response.setBody(dataSyncDao.queryReport1TemplateOpts());
    }

    @Override
    public Response saveReport1Template(Map<String, Object> parameterMap) {
        parameterMap.put("id", Utils.randomStr(8));
        dataSyncDao.saveReport1Template(parameterMap);
        return response;
    }

    @Override
    public Response deleteReport1Template(Map<String, Object> parameterMap) {
        dataSyncDao.deleteReport1Template(parameterMap);
        return response;
    }

    @Override
    public Response queryReport1Template(Map<String, Object> parameterMap) {
        return response.setBody(dataSyncDao.queryReport1Template(parameterMap));
    }

    @Override
    @SuppressWarnings("unchecked")
    public Response queryReport1Change(Map<String, Object> parameterMap) {
        List<String> selected = (List<String>) parameterMap.get("selected");
        LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();
        Map<String, Object> pipe = new HashMap<>();
        resultMap.put("id", "com.schneider.sap.admin.execute." + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()));
        resultMap.put("pipe", pipe);
        if (selected == null || selected.isEmpty()) {
            return response.setBody(JSON.toJSONString(resultMap, true));
        }

        List<Map<String, Object>> dataList = dataSyncDao.queryReport1Change(selected);


        for (Map<String, Object> data : dataList) {
            pipe.put((String) data.get("NAME"), JSON.parseObject((String) data.get("CONFIG")));
        }

        return response.setBody(JSON.toJSONString(resultMap, true));
    }

    @Override
    public Response queryReport2Version(Map<String, Object> parameterMap) {
        List<String> report2VersionList = dataSyncDao.queryReport2Version();
        List<String> report2VersionOpts = new ArrayList<>();

        for (int i = 0; i < report2VersionList.size(); i++) {
            String start = report2VersionList.get(i);
            String startDay = start.split(" ")[0];
            String end;
            int j = i + 1;
            if (j >= report2VersionList.size()) {
                end = startDay + " 23:59:59";
            } else {
                end = report2VersionList.get(j);
            }
            String endDay = end.split(" ")[0];

            if (StringUtils.equals(startDay, endDay) == false || end.endsWith(" 00:00:00") == true) {
                end = startDay + " 23:59:59";
            }

            if (start.endsWith(" 00:00:00") == false) {
                start = start.substring(0, 14) + "00:00";
            }
            if (end.endsWith(" 23:59:59") == false) {
                end = end.substring(0, 14) + "00:00";
            }

            report2VersionOpts.add(start + " - " + end);
        }

        Collections.reverse(report2VersionOpts);
        return response.setBody(report2VersionOpts);
    }

    @Override
    public Response queryReport2(Map<String, Object> parameterMap) {
        String version = (String) parameterMap.get("version");
        parameterMap.put("dateRange", new ArrayList<>(Arrays.asList(version.split(" - "))));
        List<Map<String, Object>> dataList = dataSyncDao.queryReport2(parameterMap);
        Map<String, List<DataSyncJobInfo>> dataMap = new HashMap<>();

        List<String> message = new ArrayList<>();
        Map<String, Object> resultMap = new HashMap<>();

        // 依赖校验
        Map<String, Integer> availableTask = new HashMap<>();
        for (Map<String, Object> map : dataList) {
            if ("Y".equals(map.get("ENABLE"))) {
                availableTask.put((String) map.get("NAME"), 0);
            }
        }

        for (Map<String, Object> map : dataList) {

            // 依赖校验
            JSONObject config = JSON.parseObject((String) map.get("CONFIG"));
            if (config.containsKey("after") && "Y".equals(map.get("ENABLE"))) {
                JSONArray afterArray = config.getJSONArray("after");
                for (Object o : afterArray) {
                    String after = String.valueOf(o);
                    if (availableTask.containsKey(after) == false) {
                        message.add(map.get("NAME") + " 缺失依赖 " + after);
                    }
                }
            }

            String type = (String) map.get("TYPE");
            if ("N".equals(map.get("ENABLE"))) {
                type = "disabled";
            }
            String typeName = this.getTypeName(type);
            List<DataSyncJobInfo> list = dataMap.computeIfAbsent(typeName, key -> new ArrayList<>());
            map.put("TYPE_NAME", typeName);
            list.add(new DataSyncJobInfo(map));
        }

        LinkedHashMap<String, List<DataSyncJobInfo>> sortedDataMap = new LinkedHashMap<>();
        for (String key : keys) {
            sortedDataMap.put(key, dataMap.getOrDefault(key, new ArrayList<>()));
        }

        LinkedHashMap<String, List<DataSyncJobInfo>> newSortedDataMap = new LinkedHashMap<>();
        for (String key : sortedDataMap.keySet()) {
            List<DataSyncJobInfo> value = sortedDataMap.get(key);

            int success = 0;
            int active = 0;
            for (DataSyncJobInfo info : value) {
                if ("success".equals(info.getStatus())) {
                    success++;
                }
                if ("disable".equals(info.getStatus()) == false) {
                    active++;
                }
            }
            newSortedDataMap.put(key + " (" + success + " / " + active + ")", value);
        }

        resultMap.put("data", newSortedDataMap);
        resultMap.put("message", message);
        return response.setBody(resultMap);
    }

    String[] keys = new String[]{
            "Merge To Table", // 0
            "Before / After Merge To Table", // 1
            "Refresh Materialized View", // 2
            "Schedule", // 3
            "System Operation", // 4
            "Disabled" // 5
    };

    private String getTypeName(String typeCode) {
        typeCode = StringUtils.lowerCase(typeCode);

        if ("disabled".equals(typeCode)) {
            return keys[5];
        } else if ("sap.refresh_mv".equals(typeCode)) {
            return keys[2];
        } else if ("sap.merge_to_table".equals(typeCode) || "sap.merge_to_table_by_info".equals(typeCode) || "sap.iterate_files_in_directory".equals(typeCode)) {
            return keys[0];
        } else if ("sap.schedule".equals(typeCode)) {
            return keys[3];
        } else if (StringUtils.startsWith(typeCode, "date.") || StringUtils.startsWith(typeCode, "scp.")) {
            return keys[4];
        } else {
            return keys[1];
        }
    }

    @Override
    public Response queryReport2Details(Map<String, Object> parameterMap) {
        Map<String, Object> result = dataSyncDao.queryReport2Details(parameterMap);
        result.put("CONFIG", this.formatJSON(result.get("CONFIG")));
        result.put("ENABLE", "Y".equals(result.get("ENABLE")));
        return response.setBody(result);
    }

    @Override
    public Response modifyReport2Details(Map<String, Object> parameterMap) {
        parameterMap = this.prepareForReport2Update(parameterMap);

        if (parameterMap == null) {
            return response.setBody("Invalid job config, 'func' cannot be null");
        }

        dataSyncDao.modifyReport2Details(parameterMap);
        dataSyncDao.updateErrorLogStatus(parameterMap);
        return response;
    }

    @Override
    public Response deleteReport2Details(Map<String, Object> parameterMap) {
        dataSyncDao.deleteReport2Details(parameterMap);
        return response;
    }

    @Override
    public Response createReport2Details(Map<String, Object> parameterMap) {
        parameterMap = this.prepareForReport2Update(parameterMap);

        if (parameterMap == null) {
            return response.setBody("Invalid job config, 'func' cannot be null");
        }

        dataSyncDao.createReport2Details(parameterMap);
        return response;
    }

    private Map<String, Object> prepareForReport2Update(Map<String, Object> parameterMap) {
        String config = (String) parameterMap.get("CONFIG");
        JSONObject configObj = JSONObject.parseObject(config);
        String type = configObj.getString("func");

        if (StringUtils.isBlank(type)) {
            return null;
        }

        String name = StringUtils.lowerCase((String) parameterMap.get("NAME"));
        switch (type) {
            case "sap.refresh_mv":
            case "sap.dump_to_hist":
            case "sap.merge_to_table":
            case "sap.gen_aging_using_list":
            case "sap.interpret_table_info":
            case "sap.clean_table_by_date_safe":
            case "sap.merge_to_table_by_info":
            case "sap.get_table_latest_update_date":
            case "neo4j.sync_table_to_node":
            case "neo4j.sync_table_to_link":
            case "neo4j.execute_cypher":
                JSONObject kwargs = configObj.getJSONObject("kwargs");
                if (kwargs == null) {
                    kwargs = new JSONObject();
                }
                kwargs.put("key", name);
                configObj.put("kwargs", kwargs);
                break;
        }

        parameterMap.put("NAME", name);
        parameterMap.put("TYPE", type);
        parameterMap.put("CONFIG", configObj.toJSONString());
        parameterMap.put("ENABLE", Utils.parseBoolean(parameterMap.get("ENABLE")) ? "Y" : "N");
        return parameterMap;
    }

    @Override
    public Response queryReport3(Map<String, Object> parameterMap) {
        List<String> dataList = dataSyncDao.queryReport3(parameterMap);
        List<Map<String, Object>> resultList = new ArrayList<>();
        for (String mv : dataList) {
            Map<String, Object> temp = new HashMap<>();
            temp.put("label", mv);
            temp.put("key", mv);
            resultList.add(temp);
        }
        return response.setBody(resultList);
    }

    private static String REFRESH_LOCK = null;

    @Override
    public Response report3RefreshMV(Session session, Map<String, Object> parameterMap) {
        List<String> logs = new ArrayList<>();
        String mv = StringUtils.trim((String) parameterMap.get("mv"));
        SimpleDateFormat format = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        if (REFRESH_LOCK != null) {
            return response.setBody(REFRESH_LOCK);
        }

        String source = "SCPA." + mv + "_" + Utils.randomStr(8);
        String key = StringUtils.lowerCase(mv);
        String note = "";
        double seconds = 0;
        try {
            String host = System.getProperty("user.name");
            String ip = "";
            try {
                host = InetAddress.getLocalHost().getHostName();
                ip = InetAddress.getLocalHost().getHostAddress();
            } catch (Exception ignored) {

            }

            try {
                systemDao.saveRefreshLog(source, host, ip, key);
            } catch (Exception e) {
                logs.add(format.format(new Date()) + " - " + e.getMessage());
                e.printStackTrace();
            }


            REFRESH_LOCK = session.getUsername() + "正在刷新视图" + mv + ", 请等待...";

            long start = System.currentTimeMillis();

            // 检查物化视图是否存在
            int count = systemDao.queryMVCount(mv);
            if (count == 1) {
                // 检查物化视图是否有索引, 物化视图一般都没有主键, 用这个方法刷新带主键的MV会导致主键丢失
                // 如果有索引, 需要保存索引生成语句
                List<Map<String, Object>> mvIndexs = new ArrayList<>();

                if (dropIndex == true) {
                    mvIndexs = systemDao.queryMVIndexs(mv);
                }

                try {
                    // drop 索引
                    if (dropIndex == true) {
                        for (Map<String, Object> map : mvIndexs) {
                            String indexName = (String) map.get("INDEX_NAME");
                            logs.add(format.format(new Date()) + " - drop index " + indexName);
                            systemDao.dropMVIndex(indexName);
                        }
                    }
                    // 刷新物化视图
                    systemDao.refreshMV(mv);
                    logs.add(format.format(new Date()) + " - refreshed MV " + mv);
                } catch (Exception e) {
                    logs.add(format.format(new Date()) + " - " + e.getMessage());
                    e.printStackTrace();
                } finally {
                    if (dropIndex == true) {
                        for (Map<String, Object> map : mvIndexs) {
                            String indexName = (String) map.get("INDEX_NAME");
                            // 检查索引是否还存在
                            int indexCnt = systemDao.queryMVIndexCount(indexName);
                            // 索引不存在, 尝试把索引加回去
                            if (indexCnt == 0) {
                                logs.add(format.format(new Date()) + " - re-create index " + indexName);
                                systemDao.createMVIndex((String) map.get("DDL"));
                            }
                        }
                    }
                }
            }

            seconds = BigDecimal.valueOf((System.currentTimeMillis() - start) / 1000.0).setScale(1, RoundingMode.HALF_UP).doubleValue();
            logs.add(format.format(new Date()) + " - timecost " + seconds + "s");
        } catch (Exception e) {
            logs.add(format.format(new Date()) + " - " + Utils.getExceptionMessage(e));
            note = Utils.getExceptionMessage(e);
            if (note.length() > 500) {
                note = note.substring(0, 256); // 考虑到汉字占位符的问题
            }
        } finally {
            REFRESH_LOCK = null;

            try {
                systemDao.updateRefreshLog(source, note, seconds);
            } catch (Exception e) {
                logs.add(format.format(new Date()) + " - " + e.getMessage());
                e.printStackTrace();
            }
        }
        return response.setBody(logs);
    }

    @Override
    public Response queryReport4(Map<String, Object> parameterMap) {
        List<Map<String, Object>> dataList = dataSyncDao.queryReport4(parameterMap);
        Map<String, Object> resultMap = new HashMap<>();

        List<DataSyncLink> links = new ArrayList<>();
        List<DataSyncNode> data = new ArrayList<>();
        Map<String, Object> tips = new HashMap<>();

        resultMap.put("links", links);
        resultMap.put("data", data);
        resultMap.put("tips", tips);

        for (Map<String, Object> map : dataList) {
            String name = (String) map.get("NAME");
            if ("update_date".equalsIgnoreCase(name)) {
                continue;
            }
            data.add(new DataSyncNode(map));
            map.put("TYPE_NAME", this.getTypeName((String) map.get("TYPE")));
            tips.put(name, new DataSyncJobInfo(map));

            JSONObject config = JSON.parseObject((String) map.get("CONFIG"));

            if (config.containsKey("after") && config.getJSONArray("after").isEmpty() == false) {
                JSONArray afterList = config.getJSONArray("after");

                for (Object jobObj : afterList.toArray()) {
                    links.add(new DataSyncLink((String) jobObj, name));
                }
            }
        }

        return response.setBody(resultMap);
    }

    @Override
    public Response queryReport5(Map<String, Object> parameterMap) {
        List<Map<String, Object>> data = dataSyncDao.queryReport5(parameterMap);
        for (int i = 1; i <= data.size(); i++) {
            Map<String, Object> map = data.get(i - 1);
            map.put("KEY", i + ". " + map.get("KEY"));
        }
        return response.setBody(data);
    }

    private String formatJSON(Object jsonObj) {
        JSONObject obj = JSONObject.parseObject((String) jsonObj);
        return JSON.toJSONString(obj, true);
    }
}
