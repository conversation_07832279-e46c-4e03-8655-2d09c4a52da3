package com.adm.system.service.impl;

import com.starter.context.bean.Response;
import com.starter.context.mail.MailBean;
import com.starter.context.mail.MailFeignClient;
import com.starter.utils.Utils;
import com.adm.system.dao.ISearchDao;
import com.adm.system.service.ISearchService;
import com.adm.system.service.ISystemService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@Scope("prototype")
@Transactional
public class SearchServiceImpl implements ISearchService {

    @Resource
    private ISearchDao searchDao;

    @Resource
    private ISystemService systemService;

    @Resource
    private Response response;

    @Resource
    private MailFeignClient mailFeignClient;

    @Override
    public Response queryPageConditionByID(Map<String, Object> parameterMap) {
        return response.setBody(searchDao.queryPageConditionByID(parameterMap));
    }

    @Override
    public Response queryPageConditionByDefault(Map<String, Object> parameterMap) {
        return response.setBody(searchDao.queryPageConditionByDefault(parameterMap));
    }

    @Override
    public Response toggleDefaultConditions(Map<String, Object> parameterMap) {
        searchDao.toggleDefaultConditions(parameterMap);
        return this.queryPageConditions(parameterMap);
    }

    @Override
    public Response queryPageConditions(Map<String, Object> parameterMap) {
        List<Map<String, Object>> resultList = searchDao.queryPageConditions(parameterMap);
        for (Map<String, Object> map : resultList) {
            map.put("SHARED_REMARKS", Utils.delHTMLTag((String) map.get("SHARED_REMARKS")));
        }
        return response.setBody(resultList);
    }

    @Override
    public Response savePageConditions(Map<String, Object> parameterMap) {
        int count = searchDao.queryExistsConditions(parameterMap);
        if (count == 0) {
            searchDao.savePageConditions(parameterMap);
        } else {
            searchDao.updatePageConditions(parameterMap);
        }
        return this.queryPageConditions(parameterMap);
    }

    @Override
    public Response deletePageConditions(Map<String, Object> parameterMap) {
        searchDao.deletePageConditions(parameterMap);
        return response;
    }

    @Override
    public Response queryAllUsers() {
        return response.setBody(searchDao.queryAllUsers());
    }

    @Override
    @SuppressWarnings("unchecked")
    public Response shareCondition(String userid, String username, String email, Map<String, Object> parameterMap) {
        List<String> users = (List<String>) parameterMap.get("users");
        if (users == null || users.size() == 0) {
            return response.setBody("Please select at least one user to share!");
        }
        parameterMap.put("userid", userid);
        Map<String, Object> conditions = searchDao.queryShareCondition(parameterMap);
        if (conditions == null || conditions.isEmpty()) {
            return response.setBody("Invalid Parameter: " + parameterMap.get("name"));
        }

        String name = (String) parameterMap.get("name");
        int index = StringUtils.lastIndexOf(name, "#");
        if (index != -1 && index == name.length() - 7) {
            name = name.substring(0, index);
        }
        name = name + "#" + Utils.randomStr(6);

        String cid = "";
        List<String> cids = new ArrayList<>();
        for (int i = 0; i < users.size(); i++) {
            cid = Utils.randomStr(12);
            cids.add(cid);
        }

        parameterMap.put("cids", cids);
        parameterMap.put("name", name);
        parameterMap.put("withDate", conditions.get("WITH_DATE"));
        parameterMap.put("condi_str", conditions.get("CONDITIONS"));
        searchDao.shareCondition(parameterMap);

        // send notice mail
        StringBuilder body = new StringBuilder();
        String remarks = (String) parameterMap.get("remarks");

        MailBean mailBean = new MailBean();
        mailBean.setSubject("【Variant Sharing】" + username + " shared a variant [" + name + "] with you");
        List<String> to = new ArrayList<>();
        for (String user : users) {
            to.add(user + "@se.com");
        }
        mailBean.setTo(StringUtils.join(to, ","));
        mailBean.setCc(email);

        String menuName = searchDao.queryMenuName(parameterMap);
        if (StringUtils.isBlank(menuName)) {
            menuName = (String) parameterMap.get("url");
        }

        body.append("<div style='font-size:10pt;font-family:DengXian;'>");
        body.append("<p>");
        body.append(username).append(" shared a variant [").append(name).append("] with you");
        body.append("</p>");
        if (StringUtils.isNotBlank(remarks)) {
            body.append("<br/>");
            body.append(remarks);
            body.append("<br/>");
        }

        body.append("Click <a href='https://scp-dss.cn.schneider-electric.com/#");
        body.append(parameterMap.get("url"));
        body.append("?cid=");
        body.append(cid);
        body.append("'><b><i>");
        body.append(menuName);
        body.append("</i></b></a> for more information</div>");
        String style = "<style>p{font-size: 10pt;font-family:DengXian;padding:0;margin:0} span{font-size: 10pt;font-family:DengXian;} div{font-size: 10pt;font-family:DengXian;}</style>";

        String signatrue = systemService.getMailSignature(userid);
        if (StringUtils.isBlank(signatrue)) {
            signatrue = "";
        }
        signatrue = "<br><br><br>" + signatrue;
        mailBean.setBody(style + body + signatrue);
        mailFeignClient.sendAsync(mailBean);

        return response;
    }
}
