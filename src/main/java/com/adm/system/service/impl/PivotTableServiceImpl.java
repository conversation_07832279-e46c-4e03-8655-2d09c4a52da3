package com.adm.system.service.impl;

import com.adm.system.dao.IPivotDao;
import com.adm.system.service.IPivotTableService;
import com.alibaba.fastjson.JSONArray;
import com.scp.master.dao.IMasterDataSummaryDao;
import com.scp.master.service.IMasterDataSummaryService;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@Scope("prototype")
@Transactional
public class PivotTableServiceImpl implements IPivotTableService {

    @Resource
    private Response response;

    @Resource
    private IPivotDao pivotDao;

    @Resource
    private ExcelTemplate excelTemplate;

    @Override
    public Response queryData(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        page.setTotal(pivotDao.queryDataCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(pivotDao.queryData(parameterMap));
        }

        return response.setBody(page);
    }

    @Override
    public void downloadData(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        String fileName = "pivot_table_data_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.adm.system.dao.IPivotDao.queryData", parameterMap);
    }

    @Override
    public Response queryDetails(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateCascaderFilter(parameterMap);

        page.setTotal(pivotDao.queryDetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(pivotDao.queryDetails(parameterMap));
        }

        return response.setBody(page);
    }

    @Override
    public void downloadDetails(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        String fileName = "pivot_table_details_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.adm.system.dao.IPivotDao.queryDetails", parameterMap);
    }

    private void generateValueColumn(Map<String, Object> parameterMap) {
        String calcType = (String) parameterMap.get("calcType");

        switch (calcType) {
            case "Sum" -> parameterMap.put("calcType", "SUM");
            case "Min" -> parameterMap.put("calcType", "MIN");
            case "Max" -> parameterMap.put("calcType", "MAX");
            case "Avg" -> parameterMap.put("calcType", "AVG");
            case "Std" -> parameterMap.put("calcType", "STDDEV");
        }
    }

    /**
     * 生成cascader filter
     *
     * @param parameterMap 参数map
     */
    private void generateCascaderFilter(Map<String, Object> parameterMap) {
        String bindTo = (String) parameterMap.get("bindTo");
        if (Utils.hasInjectionAttack(bindTo)) {
            throw new RuntimeException("[" + bindTo + "] is a illegal table!");
        }
        String dateRangeType = (String) parameterMap.get("dateRangeType");
        if (Utils.hasInjectionAttack(dateRangeType)) {
            throw new RuntimeException("[" + dateRangeType + "] is a illegal column name!");
        }
        String specialType = (String) parameterMap.get("specialType");
        if (Utils.hasInjectionAttack(specialType)) {
            throw new RuntimeException("[" + specialType + "] is a illegal column name!");
        }

        // 生成筛选条件
        JSONArray filterArray = (JSONArray) parameterMap.get("filterList");

        if (filterArray != null) {
            Map<String, List<String>> filterMap = new HashMap<>();
            Map<String, List<String>> valueMap = new HashMap<>();

            for (Object subObj : filterArray) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = subArray.getString(0);

                String value = subArray.getString(1);
                String key = Utils.randomStr(8);

                if (Utils.hasInjectionAttack(columnName)) {
                    continue;
                }

                List<String> fl = filterMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                fl.add("#{" + key + ",jdbcType=VARCHAR}");
                List<String> fv = valueMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                fv.add(value);
                parameterMap.put(key, value);
            }

            List<String> filterList = new ArrayList<>();

            for (String key : filterMap.keySet()) {
                List<String> fl = filterMap.get(key);
                List<String> fv = valueMap.get(key);
                if (fv.contains("Others")) {
                    filterList.add("(t." + key + " in (" + StringUtils.join(fl, ",") + ") or t." + key + " is null)");
                } else {
                    filterList.add("t." + key + " in (" + StringUtils.join(fl, ",") + ")");
                }
            }

            parameterMap.put("filters", StringUtils.join(filterList, " and "));
        }

        // load special parameter
        String specialContent = (String) parameterMap.get("specialContent");
        String specialColumn = (String) parameterMap.get("specialType");
        if (Utils.hasInjectionAttack(specialColumn) == false) {
            if (StringUtils.isNotBlank(specialContent)) {
                List<List<String>> value = Utils.splitValue(specialContent, 0); // 不分割
                if (value.isEmpty() == false) {
                    parameterMap.put("specialList", value.get(0));
                    parameterMap.put("specialColumn", specialColumn);
                }
            }
        }

        // categroy
        JSONArray categroyArray = (JSONArray) parameterMap.get("categroy");
        if (categroyArray == null || categroyArray.isEmpty()) {
            parameterMap.put("categroy", parameterMap.get("defaultPivotColumns"));
        }
    }
}
