package com.adm.system.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.utils.Utils;
import com.adm.system.dao.IAccessLogsDao;
import com.adm.system.service.IAccessLogsService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Scope("prototype")
@Transactional
public class AccessLogsServiceImpl implements IAccessLogsService {

    @Resource
    private Response response;
    @Resource
    private IAccessLogsDao accessLogsDao;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cascader", Utils.parseCascader(accessLogsDao.queryCascader(), false));
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1m")
    public Response queryReport1(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        int total = accessLogsDao.queryReport1Count(parameterMap);
        page.setTotal(total);
        if (total > 0) {
            page.setData(accessLogsDao.queryReport1(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1m")
    public Response queryReport2(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        List<Map<String, Object>> dataList = accessLogsDao.queryReport2(parameterMap);
        Map<String, Object> resultMap = new HashMap<>();

        // 根据结束日期向开始日期找, 最多找10天
        JSONArray dateRange = (JSONArray) parameterMap.get("dateRange");
        String startDate = dateRange.getString(0);
        String endDate = dateRange.getString(1);
        int maxLoop = 14400;

        List<String> allXList = new ArrayList<>();

        Calendar end = Calendar.getInstance();
        Calendar start = Calendar.getInstance();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm");

        try {
            end.setTime(simpleDateFormat.parse(endDate + " 23:59").getTime() > new Date().getTime() ? new Date() : simpleDateFormat.parse(endDate + " 23:59"));
            start.setTime(simpleDateFormat.parse(startDate + " 00:00"));
        } catch (Exception ignore) {

        }

        while (maxLoop-- > 0 && end.getTimeInMillis() > start.getTimeInMillis()) {
            allXList.add(simpleDateFormat.format(end.getTime()));
            end.add(Calendar.MINUTE, -1);
        }

        allXList.sort(Comparator.naturalOrder());

        List<String> xAxis = new ArrayList<>();
        List<BigDecimal> yAxis1 = new ArrayList<>();
        List<BigDecimal> yAxis2 = new ArrayList<>();
        List<BigDecimal> yAxis3 = new ArrayList<>();

        Map<String, Map<String, Object>> dataMap = new HashMap<>();
        for (Map<String, Object> map : dataList) {
            dataMap.put((String) map.get("KEY"), map);
        }

        for (String x : allXList) {
            Map<String, Object> map = dataMap.get(x);
            if (map == null) {
                xAxis.add(x.split(" ")[1]);
                yAxis1.add(null);
                yAxis2.add(null);
                yAxis3.add(null);
            } else {
                xAxis.add((String) map.get("XAXIS"));
                yAxis1.add(Utils.parseBigDecimal(map.get("YAXIS1")));
                yAxis2.add(Utils.parseBigDecimal(map.get("YAXIS2")));
                yAxis3.add(Utils.parseBigDecimal(map.get("YAXIS3")));
            }
        }

        resultMap.put("xAxis", xAxis);
        resultMap.put("yAxis1", yAxis1);
        resultMap.put("yAxis2", yAxis2);
        resultMap.put("yAxis3", yAxis3);
        return response.setBody(resultMap);
    }

    private void generateFilter(Map<String, Object> parameterMap) {
        // 生成筛选条件
        JSONArray categoryArray = (JSONArray) parameterMap.get("filterList");
        if (categoryArray != null) {
            Map<String, List<String>> filterMap = new HashMap<>();

            for (Object subObj : categoryArray) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = subArray.getString(0);

                String value = subArray.getString(1);
                String key = Utils.randomStr(8);

                if (Utils.hasInjectionAttack(columnName)) {
                    continue;
                }

                List<String> fl = filterMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                fl.add("#{" + key + ",jdbcType=VARCHAR}");
                parameterMap.put(key, value);
            }

            List<String> filterList = new ArrayList<>();

            for (String key : filterMap.keySet()) {
                List<String> fl = filterMap.get(key);
                filterList.add("t." + key + " in (" + StringUtils.join(fl, ",") + ")");
            }

            parameterMap.put("filters", StringUtils.join(filterList, " and "));
        }
    }
}
