package com.adm.system.service.impl;

import com.adm.system.bean.TreeBean;
import com.adm.system.dao.ISubscriptDao;
import com.adm.system.service.ISubscriptService;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.cache.annotation.Cacheable;

import java.util.*;

@Service
@Scope("prototype")
@Transactional
public class SubscriptServiceImpl implements ISubscriptService {

    @Resource
    private ISubscriptDao subscriptDao;

    @Resource
    private Response response;

    @Override
    public Response queryReportSubscript(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = subscriptDao.queryReportSubscript(parameterMap);
        if (resultMap == null) {
            return response;
        }
        List<String> references = subscriptDao.queryReportReference(parameterMap);
        if (references.isEmpty() == false) {
            resultMap.put("DATA_SYNC", subscriptDao.queryReportSyncDate(references));
        } else {
            resultMap.put("DATA_SYNC", "--");
        }
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryProjectDiagram(Map<String, Object> parameterMap) {
        List<String> references = subscriptDao.queryReportReference(parameterMap);
        if (references.isEmpty()) {
            return response;
        }
        String report_id = (String) parameterMap.get("id");

        TreeBean result = new TreeBean();
        result.setName(report_id);
        result.setValue("[REPORT]" + report_id);

        // 如果报表配置了依赖表, 那么根据oracle中的依赖关系, 生成树状图
        List<Map<String, Object>> tableComments = subscriptDao.queryAllTableComments();
        Map<String, Object> tableCommentsMap = new HashMap<>();
        for (Map<String, Object> map : tableComments) {
            tableCommentsMap.put((String) map.get("TABLE_NAME"), map.get("COMMENTS"));
        }

        // 先把手动配置的信息加载到树的二级菜单
        for (String name : references) {
            result.addChildByName(name, tableCommentsMap);
        }

        // 根据配置信息查询依赖
        parameterMap.put("tables", references);
        List<Map<String, Object>> referenceLevel = subscriptDao.queryReferences(parameterMap);
        Map<String, List<String>> referenceMap = new HashMap<>();

        for (Map<String, Object> map : referenceLevel) {
            referenceMap.put((String) map.get("NAME"), Arrays.asList(StringUtils.split((String) map.get("REFERENCED_NAME"), ",")));
        }
        result.renderReference(result.getChildren(), referenceMap, tableCommentsMap);

        return response.setBody(result);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryTableDiagram(Map<String, Object> parameterMap) {
        List<Map<String, Object>> tableComments = subscriptDao.queryAllTableComments();
        Map<String, Object> tableCommentsMap = new HashMap<>();
        for (Map<String, Object> map : tableComments) {
            tableCommentsMap.put((String) map.get("TABLE_NAME"), map.get("COMMENTS"));
        }

        TreeBean result = new TreeBean();
        String tablename = (String) parameterMap.get("tablename");
        result.setName(tablename);
        result.setValue((String) tableCommentsMap.get(tablename));

        List<Map<String, Object>> referenceLevel = subscriptDao.queryTableReferences(parameterMap);
        List<Map<String, Object>> referenceReport = subscriptDao.queryReportsByTablename(parameterMap);
        Map<String, List<String>> referenceMap = new HashMap<>();

        for (Map<String, Object> map : referenceLevel) {
            List<String> refs = new ArrayList<>(Arrays.asList(StringUtils.split((String) map.get("NAME"), ",")));
            referenceMap.put((String) map.get("REFERENCED_NAME"), refs);
        }

        for (Map<String, Object> map : referenceReport) {
            String tableName = (String) map.get("TABLE_NAME");
            List<String> list = referenceMap.computeIfAbsent(tableName, key -> new ArrayList<>());
            list.add((String) map.get("REPORT_ID"));
        }

        result.renderReference(new ArrayList<>() {{
            add(result);
        }}, referenceMap, tableCommentsMap);

        return response.setBody(result);
    }

    @Override
    public Response querySubscriptDoc(Map<String, Object> parameterMap) {
        Map<String, String> resultMap = new HashMap<>();
        response.setBody(resultMap);
        String id = (String) parameterMap.get("id");

        int cnt = subscriptDao.queryReportSubscriptCount(parameterMap);

        if (cnt == 0 || StringUtils.isBlank(id) || StringUtils.equalsIgnoreCase(id, "EMPTY")) {
            resultMap.put("message", "该报表尚未生成ID, 暂时无法维护文档, 请联系管理员!");
            return response;
        }

        String title = subscriptDao.queryReportSubscriptTitle(parameterMap);
        if (StringUtils.isNotBlank(title)) {
            title = id + " - " + title;
        } else {
            title = id;
        }

        String groups = subscriptDao.queryMenuNameByURL(parameterMap);
        if (StringUtils.isBlank(groups)) {
            resultMap.put("message", "未找到报表归属的菜单, 暂时无法维护文档, 请联系管理员!");
            return response;
        }

        resultMap.put("subject", title);
        resultMap.put("groups", groups);
        resultMap.put("content", subscriptDao.queryDocContentByID(parameterMap));
        resultMap.put("template", subscriptDao.querySubscriptDocTempate());
        return response;
    }

    @Override
    public Response modifySubscriptDoc(Map<String, Object> parameterMap) {
        Map<String, String> resultMap = new HashMap<>();
        response.setBody(resultMap);
        String id = (String) parameterMap.get("id");

        if (StringUtils.isBlank(id) || StringUtils.equalsIgnoreCase(id, "EMPTY")) {
            resultMap.put("message", "该报表尚未生成ID, 暂时无法维护文档, 请联系管理员!");
            return response;
        }

        String title = subscriptDao.queryReportSubscriptTitle(parameterMap);
        if (StringUtils.isNotBlank(title)) {
            title = id + " - " + title;
        } else {
            title = id;
        }

        String groups = subscriptDao.queryMenuNameByURL(parameterMap);
        if (StringUtils.isBlank(groups)) {
            resultMap.put("message", "未找到报表归属的菜单, 暂时无法维护文档, 请联系管理员!");
            return response;
        }

        parameterMap.put("subject", title);
        parameterMap.put("groups", groups);

        subscriptDao.modifySubscriptDoc(parameterMap);
        return response;
    }

    @Override
    public Response queryRelatedMethods(Map<String, Object> parameterMap) {
        Map<String, Object> result = new HashMap<>();
        result.put("avalibleMethods", subscriptDao.queryAvalibleMethods(parameterMap));
        result.put("selectedMethods", subscriptDao.querySelectedMethods(parameterMap));
        return response.setBody(result);
    }

    @Override
    public Response modifyReportRel(Map<String, Object> parameterMap) {
        subscriptDao.modifyReportRel(parameterMap);
        return response;
    }
}
