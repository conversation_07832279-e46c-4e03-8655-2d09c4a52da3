package com.adm.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.scp.toolbox.bean.TreeData;
import com.starter.context.bean.Response;
import com.starter.utils.Utils;
import com.adm.system.dao.ICacheManagementDao;
import com.adm.system.service.ICacheManagementService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.*;

@Service
@Scope("prototype")
@Transactional
public class CacheManagementServiceImpl implements ICacheManagementService {

    @Resource
    private ICacheManagementDao cacheManagementDao;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private Response response;

    @Override
    public Response queryKeyList() {
        List<TreeData> data = new ArrayList<>();
        Set<String> sets = redisTemplate.keys("*");
        if (sets != null) {
            for (String key : sets) {
                String group = key.replace("::", ":");
                if (group.contains(":") == false) {
                    continue;
                }
                String newGroup = StringUtils.remove(group, group.substring(group.lastIndexOf(":")));
                String label = StringUtils.remove(StringUtils.removeStart(group, newGroup), ":");
                if (StringUtils.isEmpty(label)) {
                    label = group;
                }
                TreeData treeData = new TreeData();
                treeData.setGroups(newGroup);
                treeData.setKey(key);
                treeData.setLabel(label);
                data.add(treeData);
            }
        }
        return response.setBody(Utils.parseTreeNodes(data, ":"));
    }

    @Override
    public Response queryContentByKey(Map<String, Object> parameterMap) {
        String key = (String) parameterMap.get("key");
        Map<String, Object> resultMap = new HashMap<>();
        try {
            // Object valueObj = redisTemplate.opsForValue().get(key);
            Map<String, String> valueObj = new HashMap<>();
            valueObj.put("message", "Cannot deserialize object generated by JdkSerializationRedisSerializer");
            resultMap.put("text", JSONObject.toJSONString(valueObj, true));
        } catch (Exception e) {
            resultMap.put("text", "[object:object]");
        }

        Long ttl = redisTemplate.getExpire(key);
        resultMap.put("size", redisTemplate.opsForValue().size(key));
        resultMap.put("ttl", ttl == null ? 0 : ttl);
        return response.setBody(resultMap);
    }

    @Override
    public Response deleteCacheByKey(Map<String, Object> parameterMap) {
        String key = (String) parameterMap.get("key");
        Set<String> keys = redisTemplate.keys(key + "*");
        if (keys != null) {
            redisTemplate.delete(keys);
        }
        return response;
    }
}
