package com.adm.system.service;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.Map;

import com.starter.context.bean.Response;
import com.starter.login.bean.Session;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

public interface ISystemService {
    Response queryCalendar(Map<String, Object> parameterMap);

    Response updateCalendar(Map<String, Object> parameterMap);

    Response saveCalendarTemplate(Map<String, Object> parameterMap);

    Response queryCalendarTemplate(Map<String, Object> parameterMap);

    Response deleteCalendarTemplate(Map<String, Object> parameterMap);

    Response queryUsersLite(Map<String, Object> parameterMap);

    Response queryMenuLite(Map<String, Object> parameterMap);

    Response queryAuthDetails(Map<String, Object> parameterMap);

    Response saveAuthDetails(Map<String, Object> parameterMap);

    Response queryLoginSessions(Map<String, Object> parameterMap);

    Response clearLoginSessions(String key);

    Response queryBaseMonth();

    Response saveBaseMonth(Map<String, Object> parameterMap);

    Response queryAllAvailableComponents(String userid);

    Response saveMyFavourites(Map<String, Object> parameterMap);

    Response queryComponentById(Map<String, Object> parameterMap);

    Response queryChangeLog(Map<String, Object> parameterMap);

    Response queryNotification(Map<String, Object> parameterMap, Session session, boolean cachable);

    Response queryLastStartTime();

    Response initChangelogPage(Map<String, Object> parameterMap);

    Response saveChangelog(Map<String, Object> parameterMap);

    Response uploadMailSignature(String userid, MultipartFile files);

    Response queryMailSignature(String userid);

    String getMailSignature(String userid);

    Response refreshMv(Map<String, Object> parameterMap);

    Response queryCpuWorkload(Map<String, Object> parameterMap);

    Response querySurveyMenu(Map<String, Object> parameterMap);

    Response submitSurvey(Map<String, Object> parameterMap);

    Response saveClickData(Map<String, Object> parameterMap);

    Response queryClickData(Map<String, Object> parameterMap, String userid);

    Response queryUserOpts(Session session);

    Response queryClickShowAuth(Map<String, Object> parameterMap);

    Response convertImageToBase64(MultipartFile file) throws Exception;

    Response saveImageToLocal(MultipartFile file, String saveType);

    void getImageFromLocal(String path, HttpServletResponse response) throws IOException;
}
