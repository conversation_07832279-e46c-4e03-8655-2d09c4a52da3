package com.adm.system.service;

import com.starter.context.bean.Response;

import java.util.Map;

public interface ISearchService {

    Response queryPageConditionByID(Map<String, Object> parameterMap);

    Response queryPageConditionByDefault(Map<String, Object> parameterMap);

    Response queryPageConditions(Map<String, Object> parameterMap);

    Response savePageConditions(Map<String, Object> parameterMap);

    Response deletePageConditions(Map<String, Object> parameterMap);

    Response queryAllUsers();

    Response shareCondition(String userid, String username, String email, Map<String, Object> parameterMap);

    Response toggleDefaultConditions(Map<String, Object> parameterMap);
}
