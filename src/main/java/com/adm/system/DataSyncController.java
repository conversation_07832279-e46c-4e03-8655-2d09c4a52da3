package com.adm.system;

import com.adm.system.service.IDataSyncService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/system/data_sync", parent = "menu923")
public class DataSyncController extends ControllerHelper {

    @Resource
    private IDataSyncService dataSyncService;

    @SchneiderRequestMapping(value = "/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        return dataSyncService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_report1_change")
    public Response queryReport1Change(HttpServletRequest request) {
        super.pageLoad(request);
        return dataSyncService.queryReport1Change(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_report1_template_opts")
    public Response queryReport1TemplateOpts(HttpServletRequest request) {
        super.pageLoad(request);
        return dataSyncService.queryReport1TemplateOpts();
    }

    @SchneiderRequestMapping(value = "/save_report1_template")
    public Response saveReport1Template(HttpServletRequest request) {
        super.pageLoad(request);
        return dataSyncService.saveReport1Template(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_report1_template")
    public Response queryReport1Template(HttpServletRequest request) {
        super.pageLoad(request);
        return dataSyncService.queryReport1Template(parameterMap);
    }

    @SchneiderRequestMapping(value = "/delete_report1_template")
    public Response deleteReport1Template(HttpServletRequest request) {
        super.pageLoad(request);
        return dataSyncService.deleteReport1Template(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_report2_version")
    public Response queryReport2Version(HttpServletRequest request) {
        super.pageLoad(request);
        return dataSyncService.queryReport2Version(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        super.pageLoad(request);
        return dataSyncService.queryReport2(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_report2_details")
    public Response queryReport2Details(HttpServletRequest request) {
        super.pageLoad(request);
        return dataSyncService.queryReport2Details(parameterMap);
    }

    @SchneiderRequestMapping(value = "/format_json")
    public Response formatJson(HttpServletRequest request) {
        super.pageLoad(request);
        return dataSyncService.formatJson(parameterMap);
    }

    @SchneiderRequestMapping(value = "/create_report2_details")
    public Response createReport2Details(HttpServletRequest request) {
        super.pageLoad(request);
        return dataSyncService.createReport2Details(parameterMap);
    }

    @SchneiderRequestMapping(value = "/modify_report2_details")
    public Response modifyReport2Details(HttpServletRequest request) {
        super.pageLoad(request);
        return dataSyncService.modifyReport2Details(parameterMap);
    }

    @SchneiderRequestMapping(value = "/delete_report2_details")
    public Response deleteReport2Details(HttpServletRequest request) {
        super.pageLoad(request);
        return dataSyncService.deleteReport2Details(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_report3")
    public Response queryReport3(HttpServletRequest request) {
        super.pageLoad(request);
        return dataSyncService.queryReport3(parameterMap);
    }

    @SchneiderRequestMapping(value = "/report3_refresh_mv")
    public Response report3RefreshMV(HttpServletRequest request) {
        super.pageLoad(request);
        return dataSyncService.report3RefreshMV(session, parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_report4")
    public Response queryReport4(HttpServletRequest request) {
        super.pageLoad(request);
        return dataSyncService.queryReport4(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_report5")
    public Response queryReport5(HttpServletRequest request) {
        super.pageLoad(request);
        return dataSyncService.queryReport5(parameterMap);
    }
}
