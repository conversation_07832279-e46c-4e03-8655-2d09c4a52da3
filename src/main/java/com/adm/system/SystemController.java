package com.adm.system;

import com.adm.system.service.IDocumentationService;
import com.adm.system.service.ISystemService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.util.WebUtils;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;

@RestController
@CrossOrigin
@SchneiderRequestMapping("/system")
@Scope("prototype")
public class SystemController extends ControllerHelper {

    @Resource
    private ISystemService systemService;

    @Resource
    private IDocumentationService documentationService;

    @Resource
    private Response response;

    private static final String PRIVILEGES = "menu912";
    private static final String CALENDAR = "menu913";

    private static final String SYSTEM_INFO = "menu922";

    @SchneiderRequestMapping(value = "/update_calendar", parent = CALENDAR)
    public Response updateCalendar(HttpServletRequest request) {
        super.pageLoad(request);
        return systemService.updateCalendar(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_calendar", parent = CALENDAR)
    public Response queryCalendar(HttpServletRequest request) {
        super.pageLoad(request);
        return systemService.queryCalendar(parameterMap);
    }

    @SchneiderRequestMapping(value = "/save_calendar_template", parent = CALENDAR)
    public Response saveCalendarTemplate(HttpServletRequest request) {
        super.pageLoad(request);
        return systemService.saveCalendarTemplate(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_calendar_template", parent = CALENDAR)
    public Response queryCalendarTemplate(HttpServletRequest request) {
        super.pageLoad(request);
        return systemService.queryCalendarTemplate(parameterMap);
    }

    @SchneiderRequestMapping(value = "/delete_calendar_template", parent = CALENDAR)
    public Response deleteCalendarTemplate(HttpServletRequest request) {
        super.pageLoad(request);
        return systemService.deleteCalendarTemplate(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_users_lite", parent = PRIVILEGES)
    public Response queryUsersLite(HttpServletRequest request) {
        super.pageLoad(request);
        return systemService.queryUsersLite(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_menu_lite", parent = PRIVILEGES)
    public Response queryMenuLite(HttpServletRequest request) {
        super.pageLoad(request);
        return systemService.queryMenuLite(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_auth_details", parent = PRIVILEGES)
    public Response queryAuthDetails(HttpServletRequest request) {
        super.pageLoad(request);
        return systemService.queryAuthDetails(parameterMap);
    }

    @SchneiderRequestMapping(value = "/save_auth_details", parent = PRIVILEGES)
    public Response saveAuthDetails(HttpServletRequest request) {
        super.pageLoad(request);
        return systemService.saveAuthDetails(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_login_sessions")
    public Response queryLoginSessions(HttpServletRequest request) {
        super.pageLoad(request);
        return systemService.queryLoginSessions(parameterMap);
    }

    @SchneiderRequestMapping(value = "/clear_login_sessions")
    public Response clearLoginSessions(HttpServletRequest request) {
        super.pageLoad(request);
        return systemService.clearLoginSessions((String) parameterMap.get("key"));
    }

    @SchneiderRequestMapping(value = "/query_base_month")
    public Response queryBaseMonth(HttpServletRequest request) {
        super.pageLoad(request);
        return systemService.queryBaseMonth();
    }

    @SchneiderRequestMapping(value = "/save_base_month")
    public Response saveBaseMonth(HttpServletRequest request) {
        super.pageLoad(request);
        return systemService.saveBaseMonth(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_all_available_components")
    public Response queryAllAvailableComponents(HttpServletRequest request) {
        super.pageLoad(request);
        return systemService.queryAllAvailableComponents(session.getUserid());
    }

    @SchneiderRequestMapping(value = "/query_component_by_id")
    public Response queryComponentById(HttpServletRequest request) {
        super.pageLoad(request);
        return systemService.queryComponentById(parameterMap);
    }

    @SchneiderRequestMapping(value = "/save_my_favourites")
    public Response saveMyFavourites(HttpServletRequest request) {
        super.pageLoad(request);
        return systemService.saveMyFavourites(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_change_log")
    public Response queryChangeLog(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return systemService.queryChangeLog(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_notification")
    public Response queryNotification(HttpServletRequest request) {
        super.pageLoad(request);
        return systemService.queryNotification(parameterMap, session, "Y".equals(parameterMap.get("cachable")));
    }

    @SchneiderRequestMapping(value = "/query_last_start_time")
    public Response queryLastStartTime() {
        super.setGlobalCache(true);
        return systemService.queryLastStartTime();
    }

    @SchneiderRequestMapping(value = "/init_changelog_page")
    public Response initChangelogPage(HttpServletRequest request) {
        super.pageLoad(request);
        return systemService.initChangelogPage(parameterMap);
    }

    @SchneiderRequestMapping(value = "/save_changelog")
    public Response saveChangelog(HttpServletRequest request) {
        super.pageLoad(request);
        return systemService.saveChangelog(parameterMap);
    }

    @SchneiderRequestMapping(value = "/upload_mail_signature")
    public Response uploadMailSignature(HttpServletRequest request) {
        super.pageLoad(request);
        MultipartHttpServletRequest multipartRequest = WebUtils.getNativeRequest(request, MultipartHttpServletRequest.class);
        if (multipartRequest != null) {
            try {
                return systemService.uploadMailSignature(session.getUserid(), multipartRequest.getFile("file"));
            } catch (Exception e) {
                return response.setError(e);
            }
        } else {
            return response.setError(new Exception("upload failed, multipartRequest is null !"));
        }
    }

    @SchneiderRequestMapping(value = "/query_mail_signature")
    public Response queryMailSignature(HttpServletRequest request) {
        super.pageLoad(request);
        return systemService.queryMailSignature(session.getUserid());
    }

    @SchneiderRequestMapping(value = "/refresh_mv")
    public Response refreshMv(HttpServletRequest request) {
        super.pageLoad(request);
        return systemService.refreshMv(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_cpu_workload", parent = SYSTEM_INFO)
    public Response queryCpuWorkload(HttpServletRequest request) {
        super.pageLoad(request);
        return systemService.queryCpuWorkload(parameterMap);
    }

    @SchneiderRequestMapping("/query_survey_menu")
    public Response querySurveyMenu(HttpServletRequest request) {
        super.pageLoad(request);
        return systemService.querySurveyMenu(parameterMap);
    }

    @SchneiderRequestMapping("/submit_survey")
    public Response submitSurvey(HttpServletRequest request) {
        super.pageLoad(request);
        return systemService.submitSurvey(parameterMap);
    }

    @SchneiderRequestMapping("/query_public_documentation")
    public Response queryPublicDocumentation(HttpServletRequest request) {
        super.pageLoad(request);
        return documentationService.queryDocWithFunction(parameterMap);
    }

    @SchneiderRequestMapping(value = "/save_click_data")
    public Response saveClickData(HttpServletRequest request) {
        super.pageLoad(request);
        return systemService.saveClickData(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_click_data")
    public Response queryClickData(HttpServletRequest request) {
        super.pageLoad(request);
        return systemService.queryClickData(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping(value = "/query_user_opts")
    public Response queryUserOpts(HttpServletRequest request) {
        super.pageLoad(request);
        return systemService.queryUserOpts(session);
    }

    @SchneiderRequestMapping(value = "/query_click_show_auth")
    public Response queryClickShowAuth(HttpServletRequest request) {
        super.pageLoad(request);
        return systemService.queryClickShowAuth(parameterMap);
    }

    @SchneiderRequestMapping("/convert_image_to_base64")
    public Response convertImageToBase64(HttpServletRequest request) {
        super.pageLoad(request);
        MultipartHttpServletRequest multipartRequest = WebUtils.getNativeRequest(request, MultipartHttpServletRequest.class);
        if (multipartRequest != null) {
            MultipartFile file = multipartRequest.getFile("file");
            try {
                return systemService.convertImageToBase64(file);
            } catch (Exception e) {
                return response.setError(e);
            }
        } else {
            return response.setError(new Exception("upload failed, multipartRequest is null !"));
        }
    }

    @SchneiderRequestMapping("/save_image_to_local")
    public Response saveImageToLocal(HttpServletRequest request) {
        super.pageLoad(request);
        MultipartHttpServletRequest multipartRequest = WebUtils.getNativeRequest(request, MultipartHttpServletRequest.class);
        if (multipartRequest != null) {
            MultipartFile file = multipartRequest.getFile("file");
            String saveType = (String) parameterMap.getOrDefault("saveType", "archive");
            try {
                return systemService.saveImageToLocal(file, saveType);
            } catch (Exception e) {
                return response.setError(e);
            }
        } else {
            return response.setError(new Exception("upload failed, multipartRequest is null !"));
        }
    }

    @SchneiderRequestMapping("/get_image_from_local")
    public void getImageFromLocal(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        String path = (String) parameterMap.get("p");
        if (StringUtils.isBlank(path) == false) {
            try {
                systemService.getImageFromLocal(path, response);
            } catch (Exception e) {
                System.err.println(e.getMessage());
            }
        }
    }
}

