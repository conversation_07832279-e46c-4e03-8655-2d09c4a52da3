package com.adm.system;

import com.adm.system.bean.MailboxSenderBean;
import com.adm.system.service.IMailboxService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.bean.Status;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import java.util.stream.Collectors;

@RestController
@CrossOrigin
@SchneiderRequestMapping(value = "/mailbox", parent = "sub-menu05")
@Scope("prototype")
public class MailboxController extends ControllerHelper {

    @Resource
    private IMailboxService mailboxService;

    @Resource
    private Response response;

    @SchneiderRequestMapping(value = "/send_mail", produces = {"application/json;charset=UTF-8"})
    public Response sendMail(HttpServletRequest request, @RequestBody @Validated MailboxSenderBean mailboxSenderBean, BindingResult result) {
        super.pageLoad(request);
        if (result.hasErrors()) {
            return response.set(Status.FORBIDDEN, result.getAllErrors().stream().map(ObjectError::getDefaultMessage).collect(Collectors.joining(",")));
        } else {
            return mailboxService.sendMail(session, mailboxSenderBean);
        }
    }

    @SchneiderRequestMapping(value = "/delete_mail")
    public Response deleteMail(HttpServletRequest request) {
        super.pageLoad(request);
        return mailboxService.deleteMail(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_mail_by_id")
    public Response queryMailByID(HttpServletRequest request) {
        super.pageLoad(request);
        return mailboxService.queryMailByID(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_mail_list")
    public Response queryMailList(HttpServletRequest request) {
        super.pageLoad(request);
        return mailboxService.queryMailList(parameterMap);
    }

    @SchneiderRequestMapping(value = "/mark_all_mail_as_read")
    public Response markAllMailAsRead(HttpServletRequest request) {
        super.pageLoad(request);
        return mailboxService.markAllMailAsRead(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_mailto_by_keywords")
    public Response queryMailtoByKeywords(HttpServletRequest request) {
        super.pageLoad(request);
        return mailboxService.queryMailtoByKeywords(parameterMap);
    }
}
