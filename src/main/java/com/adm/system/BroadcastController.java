package com.adm.system;

import com.adm.system.service.IBroadcastService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin
@SchneiderRequestMapping("/system/broadcast")
@Scope("prototype")
public class BroadcastController extends ControllerHelper {

    @Resource
    private IBroadcastService broadcastService;

    @SchneiderRequestMapping("/query_broadcast_cascader")
    public Response initPage(HttpServletRequest request) {
        super.setGlobalCache(true);
        super.pageLoad(request);
        return broadcastService.queryBroadcastCascader(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_broadcast_config")
    public Response queryBroadcastConfig(HttpServletRequest request) {
        super.pageLoad(request);
        return broadcastService.queryBroadcastConfig(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_broadcast_options")
    public Response queryBroadcastOptions(HttpServletRequest request) {
        super.pageLoad(request);
        return broadcastService.queryBroadcastOptions(parameterMap);
    }

    @SchneiderRequestMapping(value = "/save_broadcast_config")
    public Response saveBroadcastConfig(HttpServletRequest request) {
        super.pageLoad(request);
        return broadcastService.saveBroadcastConfig(parameterMap);
    }

    @SchneiderRequestMapping(value = "/delete_broadcast")
    public Response deleteBroadcast(HttpServletRequest request) {
        super.pageLoad(request);
        return broadcastService.deleteBroadcast(parameterMap);
    }
}

