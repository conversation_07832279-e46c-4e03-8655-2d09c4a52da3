package com.adm.system.feign;

import com.adm.system.bean.TeamsMessage;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * code e.g.
 * TeamsMessage message = new TeamsMessage();
 * TeamsMessageAttachmentContentBody body0 = new TeamsMessageAttachmentContentBody();
 * TeamsMessageAttachmentContentBody body1 = new TeamsMessageAttachmentContentBody();
 * TeamsMessageAttachmentContentBody body2 = new TeamsMessageAttachmentContentBody();
 * body0.setText("now is " + new Date());
 * body1.setText("this message come from dss automatic notification");
 * body2.setType(TeamsMessageAttachmentContentBody.TYPE_IMAGE);
 * body2.setUrl("https://c.s-microsoft.com/en-us/CMSImages/DesktopContent-04_UPDATED.png?version=43c80870-99dd-7fb1-48c0-59aced085ab6");
 * message.addContentBody(body0);
 * message.addContentBody(body1);
 * message.addContentBody(body2);
 * <p>
 * teamsFeignClient.sendMessageToDeveloperChannel(message);
 */
@FeignClient(name = "teams", url = TeamsFeignClient.WEBHOOK_URL)
public interface TeamsFeignClient {
    String WEBHOOK_URL = "https://schneiderelectric.webhook.office.com";

    @PostMapping(value = "/webhookb2/7cef0e8f-dcfd-44c6-bcd3-755603b19400@6e51e1ad-c54b-4b39-b598-0ffe9ae68fef/IncomingWebhook/df8edbf9bf2f497389f04c164d5aa963/d4a235c2-0b74-4805-ba48-3f455fa785ec")
    String sendMessageToDeveloperChannel(TeamsMessage message);

    @PostMapping(value = "/webhookb2/7cef0e8f-dcfd-44c6-bcd3-755603b19400@6e51e1ad-c54b-4b39-b598-0ffe9ae68fef/IncomingWebhook/5312805fd9e44fe7b840c8e518aa36c9/d4a235c2-0b74-4805-ba48-3f455fa785ec")
    String sendMessageToGeneralChannel(TeamsMessage message);
}
