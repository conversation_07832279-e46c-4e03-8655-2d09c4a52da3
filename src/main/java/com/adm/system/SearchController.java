package com.adm.system;

import com.adm.system.service.ISearchService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;

@RestController
@CrossOrigin
@SchneiderRequestMapping("/search")
@Scope("prototype")
public class SearchController extends ControllerHelper {
    @Resource
    private ISearchService searchService;

    @SchneiderRequestMapping(value = "/query_page_condition_by_id")
    public Response queryPageConditionByID(HttpServletRequest request) {
        super.pageLoad(request);
        return searchService.queryPageConditionByID(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_page_condition_by_default")
    public Response queryPageConditionByDefault(HttpServletRequest request) {
        super.pageLoad(request);
        return searchService.queryPageConditionByDefault(parameterMap);
    }

    @SchneiderRequestMapping(value = "/toggle_default_conditions")
    public Response toggleDefaultConditions(HttpServletRequest request) {
        super.pageLoad(request);
        return searchService.toggleDefaultConditions(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_page_conditions")
    public Response queryPageConditions(HttpServletRequest request) {
        super.pageLoad(request);
        return searchService.queryPageConditions(parameterMap);
    }

    @SchneiderRequestMapping(value = "/save_page_conditions")
    public Response savePageConditions(HttpServletRequest request) {
        super.pageLoad(request);
        return searchService.savePageConditions(parameterMap);
    }

    @SchneiderRequestMapping(value = "/delete_page_conditions")
    public Response deletePageConditions(HttpServletRequest request) {
        super.pageLoad(request);
        return searchService.deletePageConditions(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_all_users")
    public Response queryAllUsers(HttpServletRequest request) {
        return searchService.queryAllUsers();
    }

    @SchneiderRequestMapping(value = "/share_condition")
    public Response shareCondition(HttpServletRequest request) {
        super.pageLoad(request);
        return searchService.shareCondition(session.getUserid(), session.getUsername(), session.getEmail(), parameterMap);
    }
}
