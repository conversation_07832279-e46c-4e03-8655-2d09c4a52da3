package com.adm.system.dao;

import com.scp.toolbox.bean.TreeData;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface ISQLConsoleDao {

    int queryReport1Count(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport1(Map<String, Object> parameterMap);

    List<TreeData> queryTableList();

    List<String> queryTables();
    List<String> queryTableCols();
}
