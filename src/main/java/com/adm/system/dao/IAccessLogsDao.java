package com.adm.system.dao;

import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface IAccessLogsDao {

    List<Map<String, String>> queryCascader();

    int queryReport1Count(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport1(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport2(Map<String, Object> parameterMap);
}
