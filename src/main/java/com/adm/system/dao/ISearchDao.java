package com.adm.system.dao;

import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface ISearchDao {

    void toggleDefaultConditions(Map<String, Object> parameterMap);

    Map<String, Object> queryPageConditionByID(Map<String, Object> parameterMap);

    Map<String, Object> queryPageConditionByDefault(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryPageConditions(Map<String, Object> parameterMap);

    void savePageConditions(Map<String, Object> parameterMap);

    void deletePageConditions(Map<String, Object> parameterMap);

    int queryExistsConditions(Map<String, Object> parameterMap);

    void updatePageConditions(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryAllUsers();

    Map<String, Object> queryShareCondition(Map<String, Object> parameterMap);

    void shareCondition(Map<String, Object> parameterMap);

    String queryMenuName(Map<String, Object> parameterMap);
}
