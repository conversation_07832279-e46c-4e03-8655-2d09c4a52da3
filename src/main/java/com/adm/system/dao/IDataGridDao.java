package com.adm.system.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface IDataGridDao {
    List<Map<String, Object>> queryColumnDef(Map<String, Object> parameterMap);

    int queryDataCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryDataPagging(Map<String, Object> parameterMap);

    void updateChanges(Map<String, Object> parameterMap);

    void deleteChanges(Map<String, Object> parameterMap);

    void saveChanges(Map<String, Object> parameterMap);

    void deleteUploadData(String bindTo);

    List<Map<String, Object>> queryColumnConstraint(@Param("bindTo") String bindTo, @Param("systemFileds") List<String> systemFileds);

    void insertUploadData(@Param("bindTo") String bindTo, @Param("hint") String hint, @Param("headers") List<String> headers, @Param("columnType") Map<String, String> columnType, @Param("dataList") List<Map<String, Object>> dataList, @Param("hasSystemFields") boolean hasSystemFields, @Param("userid") String userid);

    void mergeUploadData(@Param("bindTo") String bindTo, @Param("headers") List<String> headers, @Param("pkHeaders") List<String> pkHeaders, @Param("normalHeaders") List<String> normalHeaders, @Param("columnType") Map<String, String> columnType, @Param("dataList") List<Map<String, Object>> dataList, @Param("hasSystemFields") boolean hasSystemFields, @Param("userid") String userid);

    void deleteCurrentUserUploadData(@Param("bindTo") String bindTo, @Param("userid") String userid);

    List<Map<String, String>> queryColumnDropdown(Map<String, Object> parameterMap);

    String queryAvalibleColumnsByUrl(Map<String, Object> parameterMap);

    void updateDownloadHeader(String url, String columns);

    void createEmptyTempTable(String tempTableName, String bindTo);

    void createEmptyTempTableIndex(String tempTableName, List<String> pkHeaders);

    void insertTempTable(String tempTableName, List<String> headers, Map<String, String> columnType, List<Map<String, Object>> dataList);

    void mergeTempIntoTable(@Param("sourceTable") String sourceTable, @Param("targetTable") String targetTable, @Param("headers") List<String> headers, @Param("pkHeaders") List<String> pkHeaders, @Param("normalHeaders") List<String> normalHeaders, @Param("columnType") Map<String, String> columnType, @Param("dataList") List<Map<String, Object>> dataList, @Param("hasSystemFields") boolean hasSystemFields, @Param("userid") String userid);

    void mergeTempIntoTable2(@Param("sourceTable") String sourceTable, @Param("targetTable") String targetTable, @Param("headers") List<String> headers, @Param("pkHeaders") List<String> pkHeaders, @Param("normalHeaders") List<String> normalHeaders, @Param("columnType") Map<String, String> columnType, @Param("dataList") List<Map<String, Object>> dataList, @Param("hasSystemFields") boolean hasSystemFields, @Param("userid") String userid);

    void dropTempTable(String tempTableName);
}
