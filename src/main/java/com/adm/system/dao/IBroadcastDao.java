package com.adm.system.dao;

import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface IBroadcastDao {

    List<Map<String, String>> queryBroadcastCascader(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryBroadcastConfig(Map<String, Object> parameterMap);

    List<Map<String, String>> queryBroadcastOptions(Map<String, Object> parameterMap);

    void saveBroadcastConfig(Map<String, Object> parameterMap);

    void deleteBroadcast(Map<String, Object> parameterMap);

    int queryBroadcastConfigCount(Map<String, Object> parameterMap);
}
