<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.adm.system.dao.ISQLConsoleDao">
	<sql id="queryReport1SQL">
		${sql}
	</sql>

	<select id="queryReport1Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1SQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryTableList" resultType="com.scp.toolbox.bean.TreeData">
        SELECT *
        FROM (
                 SELECT T.TABLESPACE_NAME || '.' || T.TABLE_NAME AS KEY,
                        T.TABLE_NAME                             AS LABEL,
                        T.TABLESPACE_NAME                        AS GROUPS
                 FROM ALL_TABLES T
                 WHERE T.TABLESPACE_NAME = 'SCPA' AND T.TABLE_NAME != 'DATA_MART_DB_CONFIG'

                 UNION
                 SELECT T.OWNER || '.' || T.MVIEW_NAME AS KEY, T.MVIEW_NAME AS LABEL, T.OWNER AS GROUPS
                 FROM ALL_MVIEWS T
                 WHERE T.OWNER = 'SCPA'

                 UNION
                 SELECT T.OWNER || '.' || T.VIEW_NAME AS KEY, T.VIEW_NAME AS LABEL, T.OWNER AS GROUPS
                 FROM ALL_VIEWS T
                 WHERE T.OWNER = 'SCPA'
        ) TT
        ORDER BY TT.GROUPS, TT.LABEL
    </select>

    <select id="queryTables" resultType="java.lang.String">
        SELECT DISTINCT T.TABLE_NAME FROM ALL_TABLES T WHERE T.OWNER = 'SCPA'
    </select>

    <select id="queryTableCols" resultType="java.lang.String">
        SELECT DISTINCT UPPER(T.COLUMN_NAME) FROM ALL_TAB_COLS T WHERE REGEXP_LIKE(t.COLUMN_NAME, '^(\w+)$') AND T.OWNER = 'SCPA'
    </select>
</mapper>
