<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.adm.system.dao.ISubscriptDao">
	<select id="queryReportSubscript" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT REPORT_ID,
			   TITLE,
			   NVL(T3.CONTENT, 'NO DATA') DESCRIPTION,
			   nvl(t2.USER_NAME, OWNER) OWNER,
		       T2.SESA_CODE OWNER_ID,
			   TO_CHAR(CREATE_TIME, 'YYYY/MM/DD') AS CREATE_TIME
		  FROM SCPA.SY_REPORT_DESCRIPTION t
		       LEFT JOIN SY_USER_MASTER_DATA T2 ON T.OWNER = T2.SESA_CODE
		  	   LEFT JOIN SY_DOCUMENTATION T3 ON T.REPORT_ID = T3.DOC_ID
		 WHERE REPORT_ID = #{id, jdbcType=VARCHAR}
	</select>

	<select id="queryReportReference" parameterType="java.util.Map" resultType="java.lang.String">
		SELECT T2.TABLE_NAME
		  FROM SCPA.SY_REPORT_METHOD_MAPPING T INNER JOIN SCPA.SY_METHOD_TABLE_MAPPING T2 ON T.METHOD_URL = T2.URL
		 WHERE REPORT_ID = #{id, jdbcType=VARCHAR}
	</select>

	<select id="queryReferences" parameterType="java.util.Map" resultType="java.util.Map">
		select mm.name, listagg(mm.REFERENCED_NAME,',') within group (order by mm.REFERENCED_NAME) REFERENCED_NAME
		from (
			select distinct name, REFERENCED_NAME
			from (
					 select name, REFERENCED_NAME
					 from dba_dependencies
					 where name != REFERENCED_NAME
		               and REFERENCED_NAME not like '%$%'
					   and OWNER = 'SCPA') t
			START WITH NAME in
					 <foreach collection="tables" open="(" close=")" separator="," item="item">
						 #{item, jdbcType=VARCHAR}
					 </foreach>
			CONNECT BY NOCYCLE PRIOR REFERENCED_NAME = NAME ) mm
		group by name
	</select>

	<select id="queryAllTableComments" resultType="java.util.Map">
		select t.TABLE_NAME, t2.COMMENTS
		  from USER_TABLES t
			   inner join USER_TAB_COMMENTS t2 on t.TABLE_NAME = t2.TABLE_NAME
		 where t.TABLESPACE_NAME = 'SCPA'

        union all

		select REPORT_ID, '[REPORT] ' || TITLE from SCPA.SY_REPORT_DESCRIPTION
	</select>

	<select id="queryTableReferences" parameterType="java.util.Map" resultType="java.util.Map">
		select mm.REFERENCED_NAME, listagg(mm.name,',') within group (order by mm.name) NAME
		  from (
					select distinct name, REFERENCED_NAME
					  from (
							   select name, REFERENCED_NAME
								 from dba_dependencies
								where name != REFERENCED_NAME
								  and REFERENCED_NAME not like '%$%'
								  and OWNER = 'SCPA') t
					 START WITH REFERENCED_NAME = #{tablename, jdbcType=VARCHAR}
				   CONNECT BY NOCYCLE PRIOR NAME = REFERENCED_NAME) mm
		 group by REFERENCED_NAME
	</select>

	<select id="queryReportsByTablename" parameterType="java.util.Map">
		SELECT T.TABLE_NAME, T2.REPORT_ID
		FROM SY_METHOD_TABLE_MAPPING T
				 INNER JOIN SY_REPORT_METHOD_MAPPING T2
							ON T.URL = T2.METHOD_URL
		WHERE T.TABLE_NAME = #{tablename, jdbcType=VARCHAR}
	</select>

	<select id="queryReportSyncDate" resultType="java.lang.String">
		select to_char(max(LAST_DDL_TIME),'yyyy/mm/dd HH24:mi:ss')
		  from USER_OBJECTS
		 where object_type='MATERIALIZED VIEW'
			   and object_name in
		<foreach collection="list" item="item" separator="," open="(" close=")">
			#{item,jdbcType=VARCHAR}
		</foreach>
	</select>

	<select id="queryReportSubscriptTitle" parameterType="java.util.Map" resultType="java.lang.String">
		SELECT TITLE FROM SCPA.SY_REPORT_DESCRIPTION WHERE REPORT_ID = #{id, jdbcType=VARCHAR}
	</select>

	<select id="queryDocContentByID" resultType="java.lang.String">
		SELECT T.CONTENT FROM SY_DOCUMENTATION T WHERE T.DOC_ID = #{id, jdbcType=VARCHAR}
	</select>

	<select id="queryMenuNameByURL" resultType="java.lang.String">
		WITH MENU AS (
			SELECT MENU_CODE, NAME, PARENT_ID, URL
			FROM SY_MENU T
			WHERE T.MENU_TYPE IN ('menu', 'sub-menu')

			UNION ALL

			SELECT '-1', 'DSS Subscript Doc', '', ''
			FROM DUAL
		)

		SELECT LISTAGG(MM.NAME, '.') within group (order by CONNECT_LEVEL DESC)
		FROM (SELECT T.NAME, LEVEL AS CONNECT_LEVEL
			  FROM MENU T
			  START WITH T.URL = #{url, jdbcType=VARCHAR}
			  CONNECT BY PRIOR T.PARENT_ID = T.MENU_CODE
			 ) MM
	</select>

    <update id="modifySubscriptDoc">
		DECLARE
        	CLOB_CONTENT CLOB := #{content, jdbcType=CLOB};
		BEGIN
			INSERT INTO SY_DOCUMENTATION_HIST
			(DOC_ID, SUBJECT, GROUPS, CONTENT, CREATE_BY$, CREATE_DATE$, UPDATE_BY$, UPDATE_DATE$, DATE$, OPERATOR$)
			SELECT DOC_ID, SUBJECT, GROUPS, CONTENT, CREATE_BY$, CREATE_DATE$, UPDATE_BY$, UPDATE_DATE$, SYSDATE, #{session.userid, jdbcType=VARCHAR}
			  FROM SY_DOCUMENTATION
			 WHERE DOC_ID = #{id, jdbcType=VARCHAR};


			 MERGE INTO SY_DOCUMENTATION T
			 USING (SELECT #{id, jdbcType=VARCHAR} DOC_ID FROM DUAL) S
			    ON (T.DOC_ID = S.DOC_ID)
			WHEN MATCHED THEN
				UPDATE SET T.SUBJECT = #{subject, jdbcType=VARCHAR},
				           T.GROUPS = #{groups, jdbcType=VARCHAR},
				           T.CONTENT = CLOB_CONTENT,
				           T.UPDATE_BY$ = #{session.userid, jdbcType=VARCHAR},
				           T.UPDATE_DATE$ = SYSDATE
			WHEN NOT MATCHED THEN
				INSERT (DOC_ID, SUBJECT, GROUPS, CONTENT, CREATE_BY$, CREATE_DATE$)
				VALUES (S.DOC_ID, #{subject, jdbcType=VARCHAR}, #{groups, jdbcType=VARCHAR}, CLOB_CONTENT, #{session.userid, jdbcType=VARCHAR}, SYSDATE);
		END;
	</update>

	<select id="querySubscriptDocTempate" resultType="java.lang.String">
		SELECT T.CONTENT FROM SY_DOCUMENTATION T WHERE T.DOC_ID = 'SUBSCRIPT_TEMPLATE'
	</select>

    <select id="queryReportSubscriptCount" resultType="java.lang.Integer">
		SELECT COUNT(1) FROM SCPA.SY_REPORT_DESCRIPTION WHERE REPORT_ID = #{id, jdbcType=VARCHAR}
	</select>

	<select id="queryAvalibleMethods" resultType="java.lang.String">
		SELECT C.URL FROM SY_MENU P INNER JOIN SY_MENU C ON C.PARENT_ID = P.MENU_CODE
		WHERE P.URL = #{url, jdbcType=VARCHAR} ORDER BY C.URL
	</select>

	<select id="querySelectedMethods" resultType="java.lang.String">
		SELECT METHOD_URL FROM SY_REPORT_METHOD_MAPPING T WHERE T.REPORT_ID = #{id, jdbcType=VARCHAR}
	</select>

	<update id="modifyReportRel">
		BEGIN
			DELETE FROM SY_REPORT_METHOD_MAPPING T WHERE T.REPORT_ID = #{id, jdbcType=VARCHAR};

			INSERT INTO SY_REPORT_METHOD_MAPPING (REPORT_ID, METHOD_URL, CREATE_DATE$, CREATE_BY$)
			<foreach collection="urls" separator=" union all" item="url" index="index">
				SELECT #{id, jdbcType=VARCHAR}, #{url, jdbcType=VARCHAR}, SYSDATE, #{session.userid, jdbcType=VARCHAR} FROM DUAL
			</foreach>;
		END;
	</update>
</mapper>
