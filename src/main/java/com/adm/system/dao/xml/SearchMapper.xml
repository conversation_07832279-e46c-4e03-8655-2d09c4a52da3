<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.adm.system.dao.ISearchDao">
	<select id="queryPageConditionByID" resultType="java.util.Map">
		SELECT CONDITIONS, WITH_DATE FROM SY_PAGE_CONDITIONS T WHERE T.COND_ID = #{cid, jdbcType=VARCHAR} AND T.PAGE_URL = #{url,jdbcType=VARCHAR}
	</select>

	<select id="queryPageConditionByDefault" resultType="java.util.Map">
		SELECT T.CONDITIONS, T.WITH_DATE
		  FROM SY_PAGE_CONDITIONS T
		 WHERE T.USER_ID = UPPER(#{session.userid,jdbcType=VARCHAR})
		   AND T.PAGE_URL = #{url,jdbcType=VARCHAR}
		   AND T.IS_DEFAULT = '1'
		OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY
	</select>

	<select id="queryPageConditions" resultType="java.util.Map">
		SELECT T.NAME, T.CONDITIONS, T2.USER_NAME AS SHARED_BY, T.SHARED_REMARKS, T.COND_ID, T.IS_DEFAULT, T.WITH_DATE
		  FROM SY_PAGE_CONDITIONS T LEFT JOIN SY_USER_MASTER_DATA T2 ON T.SHARED_BY = T2.SESA_CODE
		 WHERE T.USER_ID = UPPER(#{session.userid,jdbcType=VARCHAR}) AND T.PAGE_URL = #{url,jdbcType=VARCHAR}
		 ORDER BY NVL(T.UPDATE_DATE$, T.CREATE_DATE$) DESC
	</select>

	<update id="toggleDefaultConditions">
		<choose>
			<when test="isDefault == '0'.toString()">
				BEGIN
					UPDATE SY_PAGE_CONDITIONS
					   SET IS_DEFAULT = '0'
					 WHERE USER_ID = UPPER(#{session.userid,jdbcType=VARCHAR})
						   AND PAGE_URL = #{url,jdbcType=VARCHAR};

					UPDATE SY_PAGE_CONDITIONS
					   SET IS_DEFAULT = '1',
						   UPDATE_DATE$ = SYSDATE,
						   UPDATE_BY$ = UPPER(#{session.userid,jdbcType=VARCHAR})
					 WHERE COND_ID = #{cid,jdbcType=VARCHAR};
				END;
			</when>
			<otherwise>
				UPDATE SY_PAGE_CONDITIONS
				   SET IS_DEFAULT = '0',
					   UPDATE_DATE$ = SYSDATE,
					   UPDATE_BY$ = UPPER(#{session.userid,jdbcType=VARCHAR})
				 WHERE COND_ID = #{cid,jdbcType=VARCHAR}
			</otherwise>
		</choose>
	</update>

	<delete id="deletePageConditions">
		DELETE FROM SY_PAGE_CONDITIONS WHERE COND_ID = #{cid,jdbcType=VARCHAR}
	</delete>

	<select id="queryExistsConditions" resultType="java.lang.Integer">
		SELECT COUNT(1) FROM SY_PAGE_CONDITIONS T
		 WHERE T.USER_ID = UPPER(#{session.userid,jdbcType=VARCHAR})
		   AND T.PAGE_URL = #{url,jdbcType=VARCHAR}
		   AND T.NAME = #{name,jdbcType=VARCHAR}
	</select>

	<insert id="savePageConditions">
		INSERT INTO SY_PAGE_CONDITIONS
		(COND_ID, USER_ID, PAGE_URL, NAME, CONDITIONS, CREATE_BY$, CREATE_DATE$, WITH_DATE)
		VALUES
		(LOWER(SUBSTR(SYS_GUID(), 0, 12)), UPPER(#{session.userid,jdbcType=VARCHAR}),#{url,jdbcType=VARCHAR},
		 #{name,jdbcType=VARCHAR}, #{conditions,jdbcType=CLOB}, UPPER(#{session.userid,jdbcType=VARCHAR}), SYSDATE, #{withDate,jdbcType=VARCHAR})
	</insert>

	<update id="updatePageConditions">
		UPDATE SY_PAGE_CONDITIONS
		   SET CONDITIONS = #{conditions,jdbcType=CLOB},
		       WITH_DATE = #{withDate,jdbcType=VARCHAR},
		       UPDATE_DATE$ = SYSDATE,
		       UPDATE_BY$ = UPPER(#{session.userid,jdbcType=VARCHAR})
         WHERE USER_ID = upper(#{session.userid,jdbcType=VARCHAR})
			   AND PAGE_URL = #{url,jdbcType=VARCHAR}
			   AND NAME = #{name,jdbcType=VARCHAR}
	</update>

	<select id="queryAllUsers" resultType="java.util.Map">
		SELECT USER_NAME || ' &lt;' || EMAIL || '>' AS LABEL, SESA_CODE AS VAL FROM SY_USER_MASTER_DATA
		 WHERE SESA_CODE != 'Admin'
		 ORDER BY USER_NAME
	</select>

	<select id="queryShareCondition" resultType="java.util.Map">
		SELECT T.CONDITIONS, T.WITH_DATE FROM SY_PAGE_CONDITIONS T WHERE T.COND_ID = #{cid, jdbcType=VARCHAR}
	</select>

	<select id="queryMenuName" resultType="java.lang.String">
		SELECT NAME
		  FROM SY_MENU T
		 WHERE T.URL = #{url, jdbcType=VARCHAR}
		OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY
	</select>

    <insert id="shareCondition">
		DECLARE
            PARAMS_COLB CLOB := #{remarks, jdbcType=CLOB};
        BEGIN
			INSERT INTO SY_PAGE_CONDITIONS
			(COND_ID, USER_ID, PAGE_URL, NAME, CONDITIONS, CREATE_BY$, CREATE_DATE$, SHARED_BY, SHARED_DATE, SHARED_REMARKS, WITH_DATE)
			<foreach collection="users" separator=" union all" item="item" index="index">
				SELECT #{cids[${index}], jdbcType=VARCHAR}, #{item, jdbcType=VARCHAR}, #{url, jdbcType=VARCHAR}, #{name, jdbcType=VARCHAR},
					   #{condi_str, jdbcType=CLOB}, #{userid, jdbcType=VARCHAR}, SYSDATE,
					   #{userid, jdbcType=VARCHAR}, SYSDATE, PARAMS_COLB, #{withDate,jdbcType=VARCHAR}
				  FROM DUAL
			</foreach>;
		END;
	</insert>
</mapper>
