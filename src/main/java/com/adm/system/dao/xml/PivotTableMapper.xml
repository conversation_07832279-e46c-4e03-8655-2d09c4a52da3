<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.adm.system.dao.IPivotDao">
    <sql id="queryDataSql">
        select <foreach collection="categroy" separator="," item="item">
                    NVL(${item}, 'Others') "${item}"
               </foreach>,
               COUNT(1) as CNT,
               <foreach collection="valueColumns" separator="," item="item">
                   ${calcType}(${item}) "${item}"
               </foreach>
        from ${bindTo} t
        <where>
            <if test="dateRangeType != ''.toString()">
                (
                    ${dateRangeType} between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'YYYY/MM/DD') and to_date(#{dateRange[1], jdbcType=VARCHAR}, 'YYYY/MM/DD')
                    or
                    ${dateRangeType} is null
                )
            </if>
            <if test="filters != null and filters != ''.toString()">
                and ${filters}
            </if>
            <if test="specialColumn != null and specialColumn != ''.toString()">
                and
                <foreach collection="specialList" item="item" separator=" or " open="(" close=")">
                    (${specialColumn} like #{item,jdbcType=VARCHAR} || '%')
                </foreach>
            </if>
        </where>
        group by
        <foreach collection="categroy" separator="," item="item">
            nvl(${item},'Others')
        </foreach>
        order by
        <foreach collection="categroy" separator="," item="item">
            decode (${item},'Others','zzz',${item})
        </foreach>
    </sql>

    <select id="queryDataCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryDataSql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryData" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryDataSql"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryDetailsSql">
        select  t.*
        from ${bindTo} t
        where 1 = 1
        <if test="dateRangeType != null and dateRangeType != ''.toString()">
            AND (
                ${dateRangeType} between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'YYYY/MM/DD') and to_date(#{dateRange[1], jdbcType=VARCHAR}, 'YYYY/MM/DD')
                or
                ${dateRangeType} is null
            )
        </if>
        <if test="filters != null and filters != ''.toString()">
            AND ${filters}
        </if>
        <if test="specialColumn != null and specialColumn != ''.toString()">
            AND
            <foreach collection="specialList" item="item" separator=" or " open="(" close=")">
                (${specialColumn} LIKE #{item,jdbcType=VARCHAR} || '%')
            </foreach>
        </if>
        <if test="selectedValue != null and selectedValue.isEmpty() == false">
            <foreach collection="categroy" separator=" and " item="item" index="index" open=" and ">
                <choose>
                    <when test="selectedValue[index] == 'Others'.toString()">
                        (${item} = 'Others' or ${item} is null)
                    </when>
                    <when test="selectedValue[index] != null and selectedValue[index] != ''.toString()">
                        ${item} = #{selectedValue[${index}], jdbcType=VARCHAR}
                    </when>
                </choose>
            </foreach>
        </if>
    </sql>


    <select id="queryDetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryDetailsSql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryDetails" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryDetailsSql"/>
        <include refid="global.select_footer"/>
    </select>
</mapper>
