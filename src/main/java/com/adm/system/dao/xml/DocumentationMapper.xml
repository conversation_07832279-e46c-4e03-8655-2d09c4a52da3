<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.adm.system.dao.IDocumentationDao">

	<select id="queryExistsGroup" resultType="java.lang.String">
		SELECT DISTINCT T.GROUPS
		FROM SY_DOCUMENTATION T
		ORDER BY T.GROUPS
	</select>

	<select id="queryDocList" resultType="com.scp.toolbox.bean.TreeData">
		SELECT T.DOC_ID AS KEY,
			   T.SUBJECT AS LABEL,
			   T.GROUPS
		FROM SY_DOCUMENTATION T
		ORDER BY T.GROUPS, T.SUBJECT
	</select>

	<insert id="saveNewDoc">
		INSERT INTO SY_DOCUMENTATION (DOC_ID, SUBJECT, GROUPS, CONTENT, CREATE_BY$, CREATE_DATE$)
		VALUES
		(#{docid, jdbcType=VARCHAR}, #{subject, jdbcType=VARCHAR}, #{groups, jdbcType=VARCHAR}, #{content, jdbcType=VARCHAR},
		#{session.userid, jdbcType=VARCHAR}, sysdate)
	</insert>

	<select id="queryDoc" resultType="java.util.HashMap">
		SELECT T.DOC_ID,
		       T.CONTENT,
		       T.SUBJECT,
		       T.GROUPS,
		       UMD.USER_NAME AS CREATE_BY,
		       TO_CHAR(T.CREATE_DATE$, 'YYYY/MM/DD HH24:MI:SS') CREATE_DATE,
		       UMD2.USER_NAME AS UPDATE_BY,
		       TO_CHAR(T.UPDATE_DATE$, 'YYYY/MM/DD HH24:MI:SS') UPDATE_DATE
		FROM SY_DOCUMENTATION T
			 LEFT JOIN SY_USER_MASTER_DATA UMD ON T.CREATE_BY$ = UMD.SESA_CODE
			 LEFT JOIN SY_USER_MASTER_DATA UMD2 ON T.UPDATE_BY$ = UMD2.SESA_CODE
		WHERE T.DOC_ID = #{docid, jdbcType=VARCHAR}
	</select>

	<update id="modifyDoc">
		BEGIN
			INSERT INTO SY_DOCUMENTATION_HIST
			(DOC_ID, SUBJECT, GROUPS, CONTENT, CREATE_BY$, CREATE_DATE$, UPDATE_BY$, UPDATE_DATE$, DATE$, OPERATOR$)
			SELECT DOC_ID, SUBJECT, GROUPS, CONTENT, CREATE_BY$, CREATE_DATE$, UPDATE_BY$, UPDATE_DATE$, SYSDATE, #{session.userid, jdbcType=VARCHAR}
			  FROM SY_DOCUMENTATION
			 WHERE DOC_ID = #{DOC_ID, jdbcType=VARCHAR};

			UPDATE SY_DOCUMENTATION
			   SET CONTENT = #{CONTENT, jdbcType=VARCHAR},
				   SUBJECT = #{SUBJECT, jdbcType=VARCHAR},
				   GROUPS = #{GROUPS, jdbcType=VARCHAR},
				   UPDATE_BY$ = #{session.userid, jdbcType=VARCHAR},
				   UPDATE_DATE$ = SYSDATE
			 WHERE DOC_ID = #{DOC_ID, jdbcType=VARCHAR};
		END;
	</update>

	<delete id="deleteDoc">
		BEGIN
			INSERT INTO SY_DOCUMENTATION_HIST
			(DOC_ID, SUBJECT, GROUPS, CONTENT, CREATE_BY$, CREATE_DATE$, UPDATE_BY$, UPDATE_DATE$, DATE$, OPERATOR$)
			SELECT DOC_ID, SUBJECT, GROUPS, CONTENT, CREATE_BY$, CREATE_DATE$, UPDATE_BY$, UPDATE_DATE$, SYSDATE, #{session.userid, jdbcType=VARCHAR}
			  FROM SY_DOCUMENTATION
			 WHERE DOC_ID = #{docid, jdbcType=VARCHAR};

			DELETE FROM SY_DOCUMENTATION WHERE DOC_ID = #{docid, jdbcType=VARCHAR};
		END;
	</delete>

	<select id="queryColumnsByTableName" resultType="java.util.HashMap">
		SELECT T.COLUMN_NAME,
		       T.DATA_TYPE,
		       T.DATA_LENGTH,
		       T.NULLABLE
		 FROM USER_TAB_COLS${database} T
		WHERE T.TABLE_NAME = #{tablename, jdbcType=VARCHAR}
		ORDER BY T.SEGMENT_COLUMN_ID
	</select>

	<select id="queryOneRowForSample" resultType="java.util.HashMap">
		SELECT * FROM ${tablename}${database} FETCH NEXT 1 ROWS ONLY
	</select>
</mapper>
