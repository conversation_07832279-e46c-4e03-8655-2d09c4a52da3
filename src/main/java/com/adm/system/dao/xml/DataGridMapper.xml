<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.adm.system.dao.IDataGridDao">
	<select id="queryColumnDef" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT t."COLUMN_NAME",
			   t.DATA_TYPE,
			   t.DATA_LENGTH,
			   t.DATA_PRECISION,
			   t.DATA_SCALE,
			   mm.CONSTRAINT_TYPE
		  FROM USER_TAB_COLS t
				   left join
			   (
				   select distinct t01.TABLE_NAME, t01.COLUMN_NAME, 'P' as CONSTRAINT_TYPE
					 from user_constraints t0
							  inner join user_cons_columns t01
										 on t0.CONSTRAINT_NAME = t01.CONSTRAINT_NAME and t0.TABLE_NAME = t01.TABLE_NAME
					where t0.CONSTRAINT_TYPE in ('U', 'P')
			   ) mm on t.COLUMN_NAME = mm.COLUMN_NAME and t.TABLE_NAME = mm.TABLE_NAME
		 WHERE t.TABLE_NAME = UPPER (#{bindTo,jdbcType=VARCHAR})
		 ORDER BY t.COLUMN_ID
	</select>

	<select id="queryColumnDropdown" resultType="java.util.Map">
		select FIELD_NAME, CELL_TYPE, CONTENT from SY_TABLE_DROPDOWN where TABLE_NAME = UPPER (#{bindTo,jdbcType=VARCHAR})
	</select>

	<select id="queryColumnConstraint" parameterType="java.lang.String" resultType="java.util.Map">
		SELECT t."COLUMN_NAME", t3.CONSTRAINT_TYPE, t2.CONSTRAINT_NAME, t.DATA_TYPE
		  FROM USER_TAB_COLS t
				   left join user_cons_columns t2 on t.COLUMN_NAME = t2.COLUMN_NAME and t.TABLE_NAME = t2.TABLE_NAME
				   left join user_constraints t3 on t2.CONSTRAINT_NAME = t3.CONSTRAINT_NAME and t2.TABLE_NAME = t3.TABLE_NAME
		 WHERE (t.column_name not like '%$' or t.column_name in
			 	<foreach collection="systemFileds" item="item" separator="," open="(" close=")">#{item,jdbcType=VARCHAR}</foreach>)
		   and t.TABLE_NAME = #{bindTo,jdbcType=VARCHAR}
	</select>

	<select id="queryDataCount" parameterType="java.util.Map" resultType="java.lang.Integer">
		<choose>
			<when test="not _page.pagging">
				select 1 from dual
			</when>
			<otherwise>
				SELECT count(1) from ${bindTo} t
				<if test="_final_conditions != null and _final_conditions != ''.toString()">
					where ${_final_conditions}
				</if>
			</otherwise>
		</choose>
	</select>

	<select id="queryDataPagging" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT * FROM (
			SELECT ROWIDTOCHAR(t.rowid) row_id,
			  <foreach collection="_colOrgHeaders" item="item" separator=",">
				<choose>
					<when test="define[item] == 'NUMBER'.toString()">t."${item}"</when>
					<when test="define[item] == ''.toString()">t."${item}"</when>
					<otherwise>to_char(t."${item}",'${format[item]}') "${item}"</otherwise>
				</choose>
			  </foreach>
			  FROM ${bindTo} t
			 <if test="_final_conditions != null and _final_conditions != ''.toString()">
				where ${_final_conditions}
			 </if>
			 <if test="_page.sort != null and _page.sort != ''.toString()">
				order by ${_page.sort}
			 </if>
		  ) page
		  <choose>
			<when test="_page.pagging">
				offset #{_page.start,jdbcType=INTEGER} rows fetch next #{_page.length,jdbcType=INTEGER} rows only
			</when>
			<otherwise>
				offset 0 rows fetch next #{_page.maxRows,jdbcType=INTEGER} rows only
			</otherwise>
		  </choose>
	</select>

	<insert id="saveChanges" parameterType="java.util.Map">
		INSERT INTO ${bindTo} (
			<foreach collection="_colOrgHeaders" item="item"  separator="," open="" close="">"${item}"</foreach>
			<if test="hasSystemFields">, CREATE_BY$, CREATE_DATE$</if>
		)
		<foreach collection="createdRows" item="rowMap" separator="union all">
			SELECT
			<foreach collection="_colOrgHeaders" item="header" separator=",">
				<choose>
					<when test="define[header] == 'NUMBER'.toString()">#{rowMap.${header},jdbcType=DOUBLE}</when>
					<when test="define[header] == ''.toString()">#{rowMap.${header},jdbcType=VARCHAR}</when>
					<otherwise>to_date(#{rowMap.${header},jdbcType=VARCHAR},'${format[header]}')</otherwise>
				</choose>
			</foreach>
			<if test="hasSystemFields">,#{userid,jdbcType=VARCHAR},sysdate</if>
			 from dual
		</foreach>
	</insert>

	<update id="updateChanges" parameterType="java.util.Map">
		UPDATE ${bindTo}
		SET
		<foreach collection="cols" item="col" separator=",">
			<choose>
				<when test="define[col.key] == 'NUMBER'.toString()">"${col.key}" = #{col.value,jdbcType=DOUBLE}</when>
				<when test="define[col.key] == ''.toString()">"${col.key}" = #{col.value,jdbcType=VARCHAR}</when>
				<otherwise>"${col.key}" = to_date(#{col.value,jdbcType=VARCHAR},'${format[col.key]}')</otherwise>
			</choose>
		</foreach>
		<if test="cols.size() > 0 and hasSystemFields">
			,
		</if>
		<if test="hasSystemFields"> UPDATE_BY$ = #{userid,jdbcType=VARCHAR}, UPDATE_DATE$ = sysdate</if>
		WHERE rowid = #{rowid,jdbcType=VARCHAR}
		<if test="_where != null and _where != ''.toString()">
			and ${_where}
		</if>
	</update>

	<update id="deleteChanges" parameterType="java.util.Map">
		DELETE FROM ${bindTo} t WHERE t.rowid in
		<foreach collection="removedRows" item="item"  separator="," open="(" close=")">
			#{item,jdbcType=VARCHAR}
		</foreach>
	</update>

	<delete id="deleteUploadData" parameterType="java.lang.String">
		delete from ${_parameter}
	</delete>

	<delete id="deleteCurrentUserUploadData">
		delete from ${bindTo} where create_by$ = #{userid, jdbcType=VARCHAR}
	</delete>

	<insert id="insertUploadData" parameterType="java.util.Map">
		BEGIN
			INSERT ${hint} INTO ${bindTo} (
				<foreach collection="headers" item="item" separator=",">"${item}"</foreach>
				<if test="hasSystemFields">, CREATE_BY$, CREATE_DATE$</if>
			)
			<foreach collection="dataList" item="rowMap" separator="union all">
				SELECT
				<foreach collection="headers" item="header" separator=",">
					<choose>
						<when test="columnType[header] == 'NUMBER'.toString()">#{rowMap.${header},jdbcType=VARCHAR}</when>
						<when test="columnType[header] == 'DATE'.toString()">
							<choose>
								<when test="rowMap[header] == 'null'.toString()">
									NULL
								</when>
								<otherwise>
									TO_DATE(#{rowMap.${header},jdbcType=VARCHAR},'yyyy/mm/dd')
								</otherwise>
							</choose>
						</when>
						<otherwise>#{rowMap.${header},jdbcType=VARCHAR}</otherwise>
					</choose>
				</foreach>
				<if test="hasSystemFields">,#{userid,jdbcType=VARCHAR},sysdate</if>
				from dual
			</foreach>;
			COMMIT;
		END;
	</insert>

	<insert id="mergeUploadData" parameterType="java.util.Map">
		merge into ${bindTo} t
		using
			(
				SELECT
					<foreach collection="pkHeaders" item="pkHeader" separator=", ">
						"${pkHeader}"
					</foreach>
					<foreach collection="normalHeaders" item="normalHeader" separator=" , " open=",">
						MAX("${normalHeader}") AS "${normalHeader}"
					</foreach>
				  FROM (
					<foreach collection="dataList" item="rowMap" separator="union all">
						SELECT
						<foreach collection="headers" item="header" separator=",">
							<choose>
								<when test="columnType[header] == 'NUMBER'.toString()">#{rowMap.${header},jdbcType=VARCHAR}</when>
								<when test="columnType[header] == 'DATE'.toString()">to_date(#{rowMap.${header},jdbcType=VARCHAR},'yyyy/mm/dd')</when>
								<otherwise>#{rowMap.${header},jdbcType=VARCHAR}</otherwise>
							</choose> as "${header}"
						</foreach>
						from dual
					</foreach>
				) MM
				GROUP BY
				<foreach collection="pkHeaders" item="pkHeader" separator=" , ">
					"${pkHeader}"
				</foreach>
			) s on
				<foreach collection="pkHeaders" item="pkHeader" separator=" and " open="(" close=")">
					t."${pkHeader}" = s."${pkHeader}"
				</foreach>
		when matched then
		update set
		<foreach collection="normalHeaders" item="normalHeader" separator=" , ">
			t."${normalHeader}" = s."${normalHeader}"
		</foreach>
		<if test="hasSystemFields and normalHeaders.isEmpty() == false">
			,
		</if>
		<if test="hasSystemFields"> UPDATE_BY$ = #{userid,jdbcType=VARCHAR}, UPDATE_DATE$ = sysdate</if>
		when not matched then
		insert (
			<foreach collection="headers" item="header" separator=",">"${header}"</foreach>
			<if test="hasSystemFields">, CREATE_BY$, CREATE_DATE$</if>
		)
		values (
			<foreach collection="headers" item="header" separator=",">s."${header}"</foreach>
			<if test="hasSystemFields">,#{userid,jdbcType=VARCHAR},sysdate</if>
		)
	</insert>

	<select id="downloadAllData" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
		SELECT
		<foreach collection="newColOrgHeaders" item="item" separator=",">
			<choose>
				<when test="define[item] == 'NUMBER'.toString()">t."${item}"</when>
				<when test="define[item] == ''.toString()">t."${item}"</when>
				<otherwise>to_char(t."${item}",'${format[item]}') "${item}"</otherwise>
			</choose>
		</foreach>
		FROM ${bindTo} t
		<if test="_page.conditions != null and _page.conditions.sql != ''.toString()">
			where ${_page.conditions.sql}
		</if>
		<if test="_page.sort != null and _page.sort != ''.toString()">
			order by ${_page.sort}
		</if>
		offset 0 rows fetch next 1048576 rows only
	</select>

	<select id="queryAvalibleColumnsByUrl" parameterType="java.util.Map" resultType="java.lang.String">
		SELECT AVALIBLE_COLUMNS FROM SY_TABLE_DOWNLOAD_COLUMN T WHERE URL = #{url, jdbcType=VARCHAR}
	</select>

	<update id="updateDownloadHeader">
		MERGE INTO SY_TABLE_DOWNLOAD_COLUMN T
			 USING (SELECT #{url, jdbcType=VARCHAR} URL, #{columns, jdbcType=VARCHAR} AVALIBLE_COLUMNS FROM DUAL) S
			    ON (T.URL = S.URL)
			WHEN MATCHED THEN
				UPDATE SET T.AVALIBLE_COLUMNS = S.AVALIBLE_COLUMNS
			WHEN NOT MATCHED THEN
				INSERT (URL, AVALIBLE_COLUMNS) VALUES (S.URL, S.AVALIBLE_COLUMNS)
	</update>

	<update id="createEmptyTempTable">
		CREATE TABLE ${tempTableName} NOLOGGING AS SELECT * FROM ${bindTo} WHERE 0 = 1
	</update>

	<update id="createEmptyTempTableIndex">
		CREATE INDEX ${tempTableName}_INDEX ON ${tempTableName} (
		<foreach collection="pkHeaders" item="pkHeader" separator=", ">
			"${pkHeader}"
		</foreach>
		)
	</update>


	<insert id="insertTempTable">
		BEGIN
			INSERT /*+ APPEND NOLOGGING */ INTO ${tempTableName} (
				<foreach collection="headers" item="item" separator=",">"${item}"</foreach>
			)
			<foreach collection="dataList" item="rowMap" separator="union all">
				SELECT
				<foreach collection="headers" item="header" separator=",">
					<choose>
						<when test="columnType[header] == 'NUMBER'.toString()">#{rowMap.${header},jdbcType=VARCHAR}</when>
						<when test="columnType[header] == 'DATE'.toString()">
							<choose>
								<when test="rowMap[header] == 'null'.toString()">
									NULL
								</when>
								<otherwise>
									TO_DATE(#{rowMap.${header},jdbcType=VARCHAR},'yyyy/mm/dd')
								</otherwise>
							</choose>
						</when>
						<otherwise>#{rowMap.${header},jdbcType=VARCHAR}</otherwise>
					</choose>
				</foreach>
				from dual
			</foreach>;
			COMMIT;
		END;
	</insert>

	<insert id="mergeTempIntoTable" parameterType="java.util.Map">
		merge into ${targetTable} t
		using
			(
				SELECT /*+ parallel(MM 6) */
					<foreach collection="pkHeaders" item="pkHeader" separator=", ">
						"${pkHeader}"
					</foreach>
					<foreach collection="normalHeaders" item="normalHeader" separator=" , " open=",">
						MAX("${normalHeader}") AS "${normalHeader}"
					</foreach>
				  FROM ${sourceTable} MM
				GROUP BY
				<foreach collection="pkHeaders" item="pkHeader" separator=" , ">
					"${pkHeader}"
				</foreach>
			) s on
				<foreach collection="pkHeaders" item="pkHeader" separator=" and " open="(" close=")">
					t."${pkHeader}" = s."${pkHeader}"
				</foreach>
		when matched then
		update set
		<foreach collection="normalHeaders" item="normalHeader" separator=" , ">
			t."${normalHeader}" = s."${normalHeader}"
		</foreach>
		<if test="hasSystemFields and normalHeaders.isEmpty() == false">
			,
		</if>
		<if test="hasSystemFields"> UPDATE_BY$ = #{userid,jdbcType=VARCHAR}, UPDATE_DATE$ = sysdate</if>
		when not matched then
		insert (
			<foreach collection="headers" item="header" separator=",">"${header}"</foreach>
			<if test="hasSystemFields">, CREATE_BY$, CREATE_DATE$</if>
		)
		values (
			<foreach collection="headers" item="header" separator=",">s."${header}"</foreach>
			<if test="hasSystemFields">,#{userid,jdbcType=VARCHAR},sysdate</if>
		)
	</insert>

	<insert id="mergeTempIntoTable2" parameterType="java.util.Map">
		merge into ${targetTable} t
		using
			(
				SELECT /*+ parallel(MM 6) */
					<foreach collection="pkHeaders" item="pkHeader" separator=", ">
						"${pkHeader}"
					</foreach>
					<foreach collection="normalHeaders" item="normalHeader" separator=" , " open=",">
						MAX("${normalHeader}") AS "${normalHeader}"
					</foreach>
				  FROM ${sourceTable} MM
				GROUP BY
				<foreach collection="pkHeaders" item="pkHeader" separator=" , ">
					"${pkHeader}"
				</foreach>
			) s on
				<foreach collection="pkHeaders" item="pkHeader" separator=" and " open="(" close=")">
					t."${pkHeader}" = s."${pkHeader}"
				</foreach>
		when not matched then
		insert (
			<foreach collection="headers" item="header" separator=",">"${header}"</foreach>
			<if test="hasSystemFields">, CREATE_BY$, CREATE_DATE$</if>
		)
		values (
			<foreach collection="headers" item="header" separator=",">s."${header}"</foreach>
			<if test="hasSystemFields">,#{userid,jdbcType=VARCHAR},sysdate</if>
		)
	</insert>

	<update id="dropTempTable">
		DROP TABLE ${tempTableName} PURGE
	</update>
</mapper>
