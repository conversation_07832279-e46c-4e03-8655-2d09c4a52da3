<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="mv">
	<sql id="inventory_structure_hist_v" >
        INVENTORY_STRUCTURE_HIST_V AS (
            SELECT /*+ parallel */ t.*,
                NVL(T2.MATERIAL_OWNER_SESA, 'Others') AS MATERIAL_OWNER_SESA,
                NVL(T2.MATERIAL_OWNER_NAME, 'Others') AS MATERIAL_OWNER_NAME,
                T2.INVENTORY_MONITOR_TYPE
            FROM ${SCPA.INVENTORY_STRUCTURE_HIST} T
                     LEFT JOIN ${SCPA.MATERIAL_MASTER_V} T2 ON T.MATERIAL = T2.MATERIAL AND T.PLANT_CODE = T2.PLANT_CODE
         )
	</sql>

    <sql id="so_backlog_hist_v" >
        SO_BACKLOG_HIST_V AS (
            SELECT /*+ parallel */
                    t.*,
                    NVL(T2.MATERIAL_OWNER_SESA, 'Others') AS MATERIAL_OWNER_SESA,
                    NVL(T2.MATERIAL_OWNER_NAME, 'Others') AS MATERIAL_OWNER_NAME
                FROM ${SCPA.OPEN_SO_STRUCTURE_HIST} T
                         LEFT JOIN MATERIAL_MASTER_V T2 ON T.MATERIAL = T2.MATERIAL AND T.PLANT_CODE = T2.PLANT_CODE
        )
    </sql>
</mapper>
