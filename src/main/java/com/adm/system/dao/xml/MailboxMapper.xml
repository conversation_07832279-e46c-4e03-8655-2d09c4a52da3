<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.adm.system.dao.IMailboxDao">
    <insert id="saveMailBody" parameterType="com.adm.system.bean.MailboxSenderBean">
		insert into sy_mailbox
			(mail_id, recipients, subject, body, mail, send_by, send_time)
		values
			(#{mail_id, jdbcType=VARCHAR}, #{recipients, jdbcType=VARCHAR}, #{subject, jdbcType=VARCHAR},
			 #{body, jdbcType=VARCHAR}, #{mail, jdbcType=VARCHAR}, #{from, jdbcType=VARCHAR},
			 sysdate)
	</insert>

	<update id="updateMailBody" parameterType="com.adm.system.bean.MailboxSenderBean">
		 update sy_mailbox t
			set t.recipients = #{recipients, jdbcType=VARCHAR},
				t.subject = #{subject, jdbcType=VARCHAR},
				t.body = #{body, jdbcType=CLOB},
				t.mail = #{mail, jdbcType=VARCHAR},
				t.send_time = sysdate
		  where t.mail_id = #{mail_id, jdbcType=VARCHAR}
	</update>

	<!--
	1. 删除所有与这条发送记录有关的信息
	2. 插入一条发送记录
	3. 按收件人插入收件信息
	-->
	<insert id="saveMailRecipients">
		begin

		delete sy_mail_recipient where mail_id = #{id, jdbcType=VARCHAR};

		insert into sy_mail_recipient (mail_id,recipient,save_folder,read_time)
		values
		(#{id, jdbcType=VARCHAR},#{send_by, jdbcType=VARCHAR},'sent',sysdate);

		insert into sy_mail_recipient (mail_id,recipient,save_folder)
		<foreach collection="recipients" separator=" union all" item="item">
			select #{id, jdbcType=VARCHAR},#{item, jdbcType=VARCHAR},'inbox' from dual
		</foreach>;
		end;
	</insert>

	<select id="queryMailDraftCount" resultType="java.lang.Integer">
		select count(1) from sy_mail_recipient where mail_id = #{id, jdbcType=VARCHAR}
	</select>

	<insert id="saveMailDraft">
		insert into sy_mail_recipient
			(mail_id,recipient,save_folder)
		values
			(#{id, jdbcType=VARCHAR}, #{from, jdbcType=VARCHAR}, 'draft')
	</insert>

	<update id="updateMailDraft">
		update sy_mail_recipient t
		   set t.read_time = sysdate
		 where mail_id = #{id, jdbcType=VARCHAR}
	</update>

	<update id="deleteMail" parameterType="java.util.Map">
		delete sy_mail_recipient
		 where mail_id in
		       <foreach collection="checkedList" open="(" close=")" item="item" separator=",">#{item, jdbcType=VARCHAR}</foreach>
		   and recipient = #{session.email,jdbcType=VARCHAR}
           and save_folder = #{folder,jdbcType=VARCHAR}
	</update>

	<select id="queryMailByID" parameterType="java.util.Map" resultType="java.util.HashMap">
		select t.mail_id as "mail_id",
		       t.recipients as "to",
			   t.subject as "subject",
			   t.body as "body",
			   t.mail as "mail",
			   nvl(t3.user_name, t.send_by) as "send_by",
			   case when trunc(sysdate,'dd') = trunc(t.send_time,'dd') then to_char(t.send_time, '"Today at" HH:miam', 'NLS_DATE_LANGUAGE=AMERICAN')
					when trunc(sysdate - 1,'dd') = trunc(t.send_time,'dd') then to_char(t.send_time, '"Yesterday at" HH:miam', 'NLS_DATE_LANGUAGE=AMERICAN')
					else to_char(t.send_time, 'Mon DD "at" HH:miam', 'NLS_DATE_LANGUAGE=AMERICAN') end as "send_time",
			   t2.recipient as "recipient",
			   to_char(t2.read_time, 'yyyy/mm/dd HH24:mi:ss') as "read_time",
			   t2.save_folder as "save_folder"
		  from sy_mailbox t
		 inner join sy_mail_recipient t2
			on t.mail_id = t2.mail_id
		   and t2.recipient = #{session.email,jdbcType=VARCHAR}
		   and t2.mail_id = #{mailid, jdbcType=VARCHAR}
		  left join SY_USER_MASTER_DATA t3
			on t.send_by = t3.sesa_code
         where t2.save_folder = #{folder,jdbcType=VARCHAR}
	</select>

	<update id="updateMailReadTime" parameterType="java.util.Map">
		update sy_mail_recipient
           set read_time = sysdate
         where read_time is null
               and recipient = #{session.email,jdbcType=VARCHAR}
               and mail_id = #{mailid, jdbcType=VARCHAR}
	</update>

	<update id="markAllMailAsRead" parameterType="java.util.Map">
		update sy_mail_recipient
		   set read_time = sysdate
		 where read_time is null
		   and save_folder = 'inbox'
		   and recipient = #{session.email,jdbcType=VARCHAR}
	</update>

	<select id="queryMailList" parameterType="java.util.Map" resultType="java.util.HashMap">
		select t.mail_id as "mail_id",
			   t.subject as "subject",
			   case when length(t.body) > 1024 then to_char(substr(t.body,1,1024)) || '...' else to_char(t.body) end as "body",
			   t.mail as "mail",
		       t2.save_folder as "save_folder",
			   nvl(t3.user_name, t.send_by) as "send_by",
			   case when trunc(sysdate,'dd') = trunc(t.send_time,'dd') then to_char(t.send_time, '"Today at" HH:miam', 'NLS_DATE_LANGUAGE=AMERICAN')
				    when trunc(sysdate - 1,'dd') = trunc(t.send_time,'dd') then to_char(t.send_time, '"Yesterday at" HH:miam', 'NLS_DATE_LANGUAGE=AMERICAN')
				    else to_char(t.send_time, 'Mon DD "at" HH:miam', 'NLS_DATE_LANGUAGE=AMERICAN') end as "send_time",
			   to_char(t2.read_time, 'yyyy/mm/dd HH24:mi:ss') as "read_time"
		  from sy_mailbox t
		 inner join sy_mail_recipient t2
			on t.mail_id = t2.mail_id
		   and t2.recipient = #{session.email,jdbcType=VARCHAR}
		  left join SY_USER_MASTER_DATA t3 on t.send_by = t3.sesa_code
			where
			<choose>
				<when test="folder != null and folder != ''.toString()">
					t2.save_folder = #{folder, jdbcType=VARCHAR}
				</when>
				<otherwise>
					t2.save_folder = 'inbox'
				</otherwise>
			</choose>
			<if test="input != null and input != ''.toString()">and (t.subject like '%' || #{input,jdbcType=VARCHAR} || '%' or t.body like '%' || #{input,jdbcType=VARCHAR} || '%')</if>
			<if test="unread">and t2.read_time is null</if>
			order by t.send_time desc
			offset (#{currentPage,jdbcType=INTEGER} - 1) * #{pageSize,jdbcType=INTEGER} rows fetch next #{pageSize,jdbcType=INTEGER} rows only
	</select>

	<select id="queryMailCount" parameterType="java.util.Map" resultType="java.util.HashMap">
		select t2.save_folder as "save_folder",
			   sum(case when read_time is null then 1 else 0 end) as "unread",
			   count(1)                                           as "total"
		  from sy_mailbox t inner join sy_mail_recipient t2
			on t.mail_id = t2.mail_id
			   and t2.recipient = #{session.email,jdbcType=VARCHAR}
		       <if test="input != null and input != ''.toString()">and (t.subject like '%' || #{input,jdbcType=VARCHAR} || '%' or t.body like '%' || #{input,jdbcType=VARCHAR} || '%')</if>
		  	   <if test="unread"> where t2.read_time is null</if>
		 group by t2.save_folder
	</select>

	<select id="queryMailtoByKeywords" parameterType="java.util.Map" resultType="java.util.HashMap">
		select user_name || ' [' || email || ']' as "label",
		       email as "value"
		  from SY_USER_MASTER_DATA
		 where lower(user_name) like '%' || lower(#{keywords, jdbcType=VARCHAR}) || '%'
		       or lower(email) like '%' || lower(#{keywords, jdbcType=VARCHAR}) || '%'
		       or lower(sesa_code) like '%' || lower(#{keywords, jdbcType=VARCHAR}) || '%'
		offset 0 rows fetch next 5 rows only
	</select>
</mapper>
