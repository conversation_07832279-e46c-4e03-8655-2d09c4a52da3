<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.adm.system.dao.IDataSyncDao">
	<select id="queryReport1TemplateOpts" resultType="java.util.Map">
		SELECT T.ID, T.NAME FROM SY_DATA_SYNC_SAVED_TASKS T ORDER BY CREATE_DATE$ DESC
	</select>

	<select id="queryReport1" resultType="java.util.Map">
		SELECT * FROM (
			SELECT NVL(MAX(TT.STATUS), 'not start') STATUS, NAME FROM SY_DATA_SYNC_CONFIG T
			LEFT JOIN (
						  SELECT T2.KEY,
								 CASE
									WHEN T2.NOTE IS NOT NULL THEN 'failed'
									WHEN T2.START_DATE IS NOT NULL AND T2.END_DATE IS NULL THEN 'running'
									WHEN T2.END_DATE IS NOT NULL AND T2.NOTE IS NULL THEN 'success' END STATUS
							FROM SY_TAB_UPDATE_LOG_V T2 INNER JOIN (
							   SELECT KEY, MAX(DATE$) DATE$
								 FROM SY_TAB_UPDATE_LOG_V T3
								WHERE T3.DATE$ > TRUNC(SYSDATE, 'dd')
								GROUP BY KEY
							) KK ON T2.KEY = KK.KEY AND T2.DATE$ = KK.DATE$
						   WHERE T2.DATE$ > TRUNC(SYSDATE, 'dd')

						   UNION ALL

						   SELECT 'update_date', 'success' FROM DUAL
					  ) TT ON T.NAME = TT.KEY
			GROUP BY NAME
		) MM
		ORDER BY DECODE(MM.NAME, 'update_date', -99999, DECODE(NVL(MM.STATUS, 'not start'), 'failed', 0, 'running', 1, 'not start', 2, 'success', 9)), NAME
	</select>

	<insert id="saveReport1Template">
		INSERT INTO SY_DATA_SYNC_SAVED_TASKS
		(ID, NAME, TASKS, CREATE_DATE$, CREATE_BY$)
		VALUES
		(#{id, jdbcType=VARCHAR}, #{name, jdbcType=VARCHAR}, #{tasks, jdbcType=VARCHAR}, SYSDATE, #{session.userid, jdbcType=VARCHAR})
	</insert>

	<select id="queryReport1Template" resultType="java.lang.String">
		SELECT T.TASKS FROM SY_DATA_SYNC_SAVED_TASKS T WHERE T.ID = #{id, jdbcType=VARCHAR}
	</select>

	<delete id="deleteReport1Template">
		DELETE FROM SY_DATA_SYNC_SAVED_TASKS T WHERE T.ID = #{id, jdbcType=VARCHAR}
	</delete>

	<select id="queryReport2Version" resultType="java.lang.String">
		SELECT CASE WHEN DATE$ - TRUNC(DATE$, 'DD') &lt; (3 / 864)
			THEN TO_CHAR(TRUNC(DATE$, 'DD'), 'YYYY/MM/DD HH24:MI:SS')
			ELSE TO_CHAR(DATE$ - 3/864, 'YYYY/MM/DD HH24:MI:SS') END AS "DATE"
		FROM SY_TAB_UPDATE_LOG_V T
		WHERE T.DATE$ > SYSDATE - 7
		  AND T.KEY = 'virtual_starter'
		ORDER BY DATE$
	</select>

	<select id="queryReport2" resultType="java.util.Map">
		WITH ERROR AS (
			SELECT T.NAME, T.ERR_LOG, T.DATE$
			FROM SY_DATA_SYNC_ERR_LOGS T
					 INNER JOIN (
							SELECT MAX(T0.DATE$) DATE$, T0.NAME
							FROM SY_DATA_SYNC_ERR_LOGS T0
							WHERE T0.DATE$ BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'YYYY/MM/DD HH24:MI:SS') AND TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'YYYY/MM/DD HH24:MI:SS')
							GROUP BY T0.NAME
					 ) LASTEST ON T.DATE$ = LASTEST.DATE$ AND T.NAME = LASTEST.NAME
			WHERE T.DATE$ BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'YYYY/MM/DD HH24:MI:SS') AND TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'YYYY/MM/DD HH24:MI:SS')
			  AND T.FIXED = 'N'
		)
		SELECT MM.* FROM (
		SELECT ROWIDTOCHAR(T.ROWID) ROW_ID, T.NAME, T.ENABLE, T.TYPE,
			   CASE WHEN T.ENABLE = 'N' THEN 'disable'
			   	    WHEN TE.ERR_LOG IS NOT NULL THEN 'error'
			   	    ELSE NVL(TT.STATUS, 'not_start') END STATUS,
			   CASE WHEN TE.ERR_LOG IS NOT NULL THEN TO_CHAR(TE.DATE$, 'YYYY/MM/DD HH24:MI:SS')
			        ELSE TT.START_DATE END START_DATE,
			   CASE WHEN TE.ERR_LOG IS NOT NULL THEN TO_CHAR(TE.DATE$, 'YYYY/MM/DD HH24:MI:SS')
			        ELSE TT.END_DATE END END_DATE,
			   CASE WHEN TE.ERR_LOG IS NOT NULL THEN '-'
			        ELSE TT.SECONDS END SECONDS,
			   NVL(TE.ERR_LOG, TT.NOTE) NOTE,
			   T.CONFIG
		  FROM SY_DATA_SYNC_CONFIG T
		  LEFT JOIN (
		  	  SELECT KK.KEY,
		  	         KK.START_DATE,
		  	         KK.END_DATE,
		  	         KK.SECONDS,
		  	         KK.NOTE,
		  	         CASE
							WHEN KK.NOTE IS NOT NULL THEN 'failed'
							WHEN KK.START_DATE IS NOT NULL AND KK.END_DATE IS NULL THEN 'running'
							WHEN KK.END_DATE IS NOT NULL AND KK.NOTE IS NULL THEN 'success' END STATUS
		  	    FROM (
				  SELECT T2.KEY,
						 TO_CHAR(MIN(T2.START_DATE), 'YYYY/MM/DD HH24:MI:SS') START_DATE,
						 TO_CHAR(MAX(T2.END_DATE), 'YYYY/MM/DD HH24:MI:SS') END_DATE,
						 TO_CHAR(ROUND(SUM(T2.SECONDS), 0)) SECONDS,
						 LISTAGG(SUBSTR(T2.NOTE, 0, 256), '; ') NOTE
					FROM SY_TAB_UPDATE_LOG_V T2 INNER JOIN (
					   SELECT KEY, MAX(DATE$) DATE$
						 FROM SY_TAB_UPDATE_LOG_V T3
						WHERE T3.DATE$ BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'YYYY/MM/DD HH24:MI:SS') AND TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'YYYY/MM/DD HH24:MI:SS')
						GROUP BY KEY
					) KK ON T2.KEY = KK.KEY AND T2.DATE$ = KK.DATE$
				   WHERE T2.DATE$ BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'YYYY/MM/DD HH24:MI:SS') AND TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'YYYY/MM/DD HH24:MI:SS')
				   GROUP BY T2.KEY
			   ) KK

			   UNION ALL

			   SELECT 'update_date',
			   		  TO_CHAR(MIN(T.START_DATE), 'YYYY/MM/DD HH24:MI:SS'),
			   		  TO_CHAR(MIN(T.START_DATE), 'YYYY/MM/DD HH24:MI:SS'),
			   		  '0', NULL, 'success'
			   	 FROM SY_TAB_UPDATE_LOG_V T
			   	WHERE T.DATE$ BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'YYYY/MM/DD HH24:MI:SS') AND TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'YYYY/MM/DD HH24:MI:SS')
		  ) TT ON T.NAME = TT.KEY
		  LEFT JOIN ERROR TE ON T.NAME = TE.NAME
		 ORDER BY DECODE(T.ENABLE, 'Y', 0, 'N', 1)) MM
		 ORDER BY DECODE(STATUS, 'error', 0, 'failed', 1, 'running', 2, 'success', 3, 'not_start', 4, 'disable', 5), MM.NAME
	</select>

    <select id="queryReport2Details" resultType="java.util.Map">
		SELECT ROWIDTOCHAR(ROWID) ROW_ID, NAME, ENABLE, CONFIG FROM SY_DATA_SYNC_CONFIG WHERE ROWID = #{key, jdbcType=VARCHAR}
	</select>

	<insert id="modifyReport2Details">
		UPDATE SY_DATA_SYNC_CONFIG
		   SET NAME = #{NAME, jdbcType=VARCHAR},
		       CONFIG = #{CONFIG, jdbcType=VARCHAR},
		       TYPE = #{TYPE, jdbcType=VARCHAR},
		       ENABLE = #{ENABLE, jdbcType=VARCHAR},
		       UPDATE_BY$ = #{session.userid,jdbcType=VARCHAR},
		       UPDATE_DATE$ = SYSDATE
		 WHERE ROWID = #{ROW_ID, jdbcType=VARCHAR}
	</insert>

	<delete id="deleteReport2Details">
		DELETE FROM SY_DATA_SYNC_CONFIG
		 WHERE ROWID = #{ROW_ID, jdbcType=VARCHAR}
	</delete>

	<insert id="createReport2Details">
		INSERT INTO SY_DATA_SYNC_CONFIG
		(NAME, CONFIG, ENABLE, CREATE_BY$, CREATE_DATE$, TYPE)
		VALUES
		(#{NAME, jdbcType=VARCHAR}, #{CONFIG, jdbcType=VARCHAR}, #{ENABLE, jdbcType=VARCHAR},
		#{session.userid,jdbcType=VARCHAR}, SYSDATE, #{TYPE, jdbcType=VARCHAR})
	</insert>

	<select id="queryReport3" resultType="java.lang.String">
		SELECT MVIEW_NAME FROM ALL_MVIEWS T WHERE T.OWNER = 'SCPA' ORDER BY MVIEW_NAME
	</select>

	<select id="queryReport1Change" resultType="java.util.Map">
		SELECT NAME, CONFIG FROM SY_DATA_SYNC_CONFIG T WHERE T.NAME IN
		<foreach collection="selected" open="(" close=")" separator="," item="item">
			#{item,jdbcType=VARCHAR}
		</foreach>
	</select>

    <select id="queryReport4" resultType="java.util.Map">
		SELECT T.NAME,T.CONFIG, T.TYPE, NVL(TT.STATUS, 'not_start') AS STATUS,
			   TT.START_DATE, TT.END_DATE, TT.SECONDS, TT.NOTE
		  FROM SY_DATA_SYNC_CONFIG T
		  LEFT JOIN (
		  	  SELECT MM.KEY, START_DATE, END_DATE, SECONDS, NOTE,
		  	  		CASE
						WHEN MM.NOTE IS NOT NULL THEN 'failed'
						WHEN MM.START_DATE IS NOT NULL AND MM.END_DATE IS NULL THEN 'running'
						WHEN MM.END_DATE IS NOT NULL AND MM.NOTE IS NULL THEN 'success' END STATUS
		  	    FROM (
				  SELECT T2.KEY,
						 TO_CHAR(MIN(T2.START_DATE), 'YYYY/MM/DD HH24:MI:SS') START_DATE,
						 TO_CHAR(MAX(T2.END_DATE), 'YYYY/MM/DD HH24:MI:SS') END_DATE,
						 TO_CHAR(ROUND(SUM(T2.SECONDS), 0)) SECONDS,
						 LISTAGG(SUBSTR(T2.NOTE, 0, 256), '; ') NOTE
					FROM SY_TAB_UPDATE_LOG_V T2 INNER JOIN (
					   SELECT KEY, MAX(DATE$) DATE$
						 FROM SY_TAB_UPDATE_LOG_V T3
						WHERE T3.DATE$ > TRUNC(SYSDATE, 'dd')
						GROUP BY KEY
					) KK ON T2.KEY = KK.KEY AND T2.DATE$ = KK.DATE$
				   WHERE T2.DATE$ > TRUNC(SYSDATE, 'dd')
			   GROUP BY T2.KEY ) MM

			   UNION ALL

			   SELECT 'update_date',
			   		  TO_CHAR(MIN(T.START_DATE), 'YYYY/MM/DD HH24:MI:SS'),
			   		  TO_CHAR(MIN(T.START_DATE), 'YYYY/MM/DD HH24:MI:SS'),
			   		  '0', NULL, 'success'  FROM SY_TAB_UPDATE_LOG_V T WHERE T.DATE$ > TRUNC(SYSDATE, 'dd')
		  ) TT ON T.NAME = TT.KEY
		 WHERE T.ENABLE = 'Y'
	</select>

	<select id="queryReport5" resultType="java.util.Map">
		SELECT T.KEY || CASE WHEN T.END_DATE IS NULL THEN '[running]' WHEN T.NOTE IS NOT NULL THEN ' [failed]' ELSE '' END KEY,
               TO_CHAR(T.START_DATE,'YYYY/MM/DD HH24:MI:SS') START_DATE,
               ROUND((NVL(T.END_DATE, LEAST(TO_DATE(#{date, jdbcType=VARCHAR} || ' 23:59:59', 'YYYY/MM/DD HH24:MI:SS'), SYSDATE)) - T.START_DATE) * 1440, 1) AS DURATION
		  FROM SY_TAB_UPDATE_LOG_V T
		 WHERE T.DATE$ between TO_DATE(#{date, jdbcType=VARCHAR}, 'YYYY/MM/DD') AND TO_DATE(#{date, jdbcType=VARCHAR} || ' 23:59:59', 'YYYY/MM/DD HH24:MI:SS')
		 	   <if test="key != null and key != ''.toString()">
				   AND T.KEY LIKE '%' || #{key, jdbcType=VARCHAR} || '%'
			   </if>
		 ORDER BY START_DATE, END_DATE
	</select>

	<update id="updateErrorLogStatus" parameterType="java.util.Map">
		UPDATE SY_DATA_SYNC_ERR_LOGS
		   SET FIXED = 'Y',
		       FIXED_TIME = SYSDATE
		 WHERE NAME = #{NAME, jdbcType=VARCHAR}
		   AND DATE$ > TRUNC(SYSDATE, 'dd')
		   AND FIXED = 'N'
	</update>
</mapper>
