<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.adm.system.dao.IBroadcastDao">
	<select id="queryBroadcastCascader" resultType="java.util.Map">
		SELECT NAME,
		       CATEGORY
		FROM ${SCPA.BROADCAST_FILTER_V} T
		ORDER BY CATEGORY,NAME
	</select>

	<sql id="broadcastFilter">
		<if test="_filters != null and _filters != ''.toString()">
			AND ${_filters}
		</if>
	</sql>

	<select id="queryBroadcastConfig" parameterType="java.util.Map">
		SELECT * FROM (SELECT T.*,
		       T1.AUTH_TYPE,
		       T1.AUTH_DETAIL
		FROM (SELECT T.URL,
		             T.PAGE_NAME,
			         T.REMIND_SUBJECT,
			         T.FREQUENCY,
			         T.REMIND_COUNT,
			         T.DESCRIPTION,
			         T.VALID_FROM,
			         T.VALID_TO,
			         T.CREATED_DATE,
			         T.CREATED_BY_NAME,
			         T.CREATED_BY_SESA
			  FROM SCPA.SY_DIALOG_CONFIG_V T
			  ORDER BY T.CREATED_DATE DESC
			  OFFSET #{page.start,jdbcType=INTEGER} ROWS FETCH NEXT #{page.length,jdbcType=INTEGER} ROWS ONLY
		) T
		LEFT JOIN SY_DIALOG_AUTH T1 ON T.URL = T1.URL AND T.REMIND_SUBJECT = T1.REMIND_SUBJECT) T
		<where>
			<include refid="broadcastFilter"/>
		</where>
	</select>

	<select id="queryBroadcastConfigCount" parameterType="java.util.Map" resultType="java.lang.Integer">
		SELECT COUNT(1) FROM (
			   SELECT DISTINCT URL, REMIND_SUBJECT
			   FROM (SELECT T.*,
			                T1.AUTH_TYPE,
							T1.AUTH_DETAIL
					 FROM SY_DIALOG_CONFIG T
					 LEFT JOIN SY_DIALOG_AUTH T1 ON T.URL = T1.URL AND T.REMIND_SUBJECT = T1.REMIND_SUBJECT) T
				<where>
					<include refid="broadcastFilter"/>
				</where>
		)
	</select>

	<select id="queryBroadcastOptions">
		SELECT COLUMN_NAME, COLUMN_VALUE
		FROM (SELECT DISTINCT 'SESA_CODE' AS COLUMN_NAME, SESA_CODE AS COLUMN_VALUE
		      FROM SY_USER_MASTER_DATA
		      WHERE SESA_CODE IS NOT NULL
		      UNION
		      SELECT DISTINCT 'ENTITY_NAME', ENTITY_NAME
		      FROM SY_USER_MASTER_DATA
		      WHERE ENTITY_NAME IS NOT NULL
		      UNION
		      SELECT DISTINCT 'JOB_CODE', JOB_CODE
		      FROM SY_USER_MASTER_DATA
		      WHERE JOB_CODE IS NOT NULL
		      UNION
		      SELECT DISTINCT 'URL', URL
		      FROM SY_MENU
		      WHERE UPPER(MENU_TYPE) = 'MENU'
			    AND URL IS NOT NULL
			  UNION
			  SELECT 'URL', '*' AS URL
			  FROM DUAL)
		ORDER BY COLUMN_NAME, DECODE(COLUMN_VALUE, '*', '/aaa', COLUMN_VALUE)
	</select>

	<update id="saveBroadcastConfig">
		BEGIN
			<foreach collection="url" item="urlItem">
				MERGE INTO SY_DIALOG_CONFIG T
					USING (SELECT   #{urlItem, jdbcType=VARCHAR}       AS URL,
									#{remindSubject, jdbcType=VARCHAR} AS REMIND_SUBJECT,
									TO_DATE(#{validFrom, jdbcType=VARCHAR}, 'yyyy/mm/dd hh24:mi:ss')     AS VALID_FROM,
									TO_DATE(#{validTo, jdbcType=VARCHAR}, 'yyyy/mm/dd hh24:mi:ss')       AS VALID_TO,
									#{frequency, jdbcType=VARCHAR}     AS FREQUENCY,
									TO_NUMBER(#{remindCount, jdbcType=VARCHAR})   AS REMIND_COUNT,
									#{description, jdbcType=VARCHAR}   AS DESCRIPTION
							FROM DUAL) T1
					ON (T.URL = T1.URL AND T.REMIND_SUBJECT = T1.REMIND_SUBJECT)
				WHEN MATCHED THEN
				UPDATE
				SET T.VALID_FROM   = T1.VALID_FROM,
					T.VALID_TO     = T1.VALID_TO,
					T.FREQUENCY    = T1.FREQUENCY,
					T.REMIND_COUNT = T1.REMIND_COUNT,
					T.DESCRIPTION  = T1.DESCRIPTION,
					T.UPDATE_DATE$ = SYSDATE,
					T.UPDATE_BY$   = #{session.userid, jdbcType=VARCHAR}
				WHEN NOT MATCHED THEN
					INSERT (T.URL, T.REMIND_SUBJECT, T.VALID_FROM, T.VALID_TO, T.FREQUENCY, T.REMIND_COUNT, T.DESCRIPTION, CREATE_DATE$, CREATE_BY$)
					VALUES (T1.URL, T1.REMIND_SUBJECT, T1.VALID_FROM, T1.VALID_TO, T1.FREQUENCY, T1.REMIND_COUNT,
						T1.DESCRIPTION, SYSDATE, #{session.userid, jdbcType=VARCHAR});

				DELETE
				FROM SY_DIALOG_AUTH T
				WHERE TRIM(URL) = TRIM(#{urlItem, jdbcType=VARCHAR}) AND TRIM(REMIND_SUBJECT) = TRIM(#{remindSubject, jdbcType=VARCHAR});

				<if test="specificTarget != null and specificTarget.size() > 0">
					INSERT INTO SY_DIALOG_AUTH (URL, REMIND_SUBJECT, AUTH_TYPE, AUTH_DETAIL)
					<foreach collection="specificTarget" item="item" separator=" union ">
						SELECT #{urlItem, jdbcType=VARCHAR}, #{remindSubject, jdbcType=VARCHAR},
						<foreach collection="item" item="val" separator=",">
							#{val, jdbcType=VARCHAR}
						</foreach> FROM DUAL
					</foreach>;
				</if>
			</foreach>
		END;
	</update>

	<delete id="deleteBroadcast" parameterType="java.util.Map">
		BEGIN
			DELETE FROM SY_DIALOG_CONFIG
			WHERE URL = #{url,jdbcType=VARCHAR}
			  AND TRIM(REMIND_SUBJECT) = TRIM(#{remindSubject,jdbcType=VARCHAR});

			DELETE FROM SY_DIALOG_AUTH
			WHERE URL = #{url,jdbcType=VARCHAR}
			  AND TRIM(REMIND_SUBJECT) = TRIM(#{remindSubject,jdbcType=VARCHAR});
		END;
	</delete>
</mapper>
