<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.adm.system.dao.IMaterialRiskDao">

    <select id="querySuggestionByKeywords" resultType="java.lang.String">
        SELECT /*+ parallel */ DISTINCT
        <choose>
            <when test="type=='DATE'">
                TO_CHAR(${field}, 'YYYY/MM/DD') FROM SCPA.MATERIAL_MASTER_V
                WHERE ${field} IS NOT NULL
                <if test="keywords != null and keywords != ''.toString()">
                    AND TO_CHAR(${field}, 'YYYY/MM/DD') LIKE '%' || #{keywords, jdbcType=VARCHAR} || '%'
                </if>
            </when>
            <otherwise>
                ${field} FROM SCPA.MATERIAL_MASTER_V
                WHERE ${field} IS NOT NULL
                <if test="keywords != null and keywords != ''.toString()">
                    AND UPPER(${field}) LIKE '%' || UPPER(#{keywords, jdbcType=VARCHAR}) || '%'
                </if>
            </otherwise>
        </choose>
        ORDER BY ${field}
        FETCH NEXT 50 ROWS ONLY
    </select>

    <select id="searchRules" resultType="java.util.HashMap">
        SELECT ID, FILTER_CHAIN, RISK_LEVEL, STATUS, ORDER_NUM FROM MATERIAL_RISK_SETTINGS ORDER BY ORDER_NUM
    </select>

    <select id="saveRule">
        DECLARE
        	CLOB_CONTENT CLOB := #{chain, jdbcType=CLOB};
        BEGIN
            MERGE INTO MATERIAL_RISK_SETTINGS T
            USING (
                SELECT #{id, jdbcType=VARCHAR} ID FROM DUAL
            ) S ON (T.ID = S.ID)
            WHEN MATCHED THEN
                UPDATE SET T.FILTER_CHAIN = CLOB_CONTENT,
                           T.RISK_LEVEL = #{level, jdbcType=VARCHAR},
                           T.UPDATE_DATE$ = SYSDATE,
                           T.UPDATE_BY$ = #{session.userid, jdbcType=VARCHAR}
            WHEN NOT MATCHED THEN
            INSERT (ID, FILTER_CHAIN, RISK_LEVEL, STATUS, ORDER_NUM, CREATE_BY$, CREATE_DATE$)
            VALUES
            (#{id, jdbcType=VARCHAR}, CLOB_CONTENT, #{level, jdbcType=VARCHAR}, 'Y',
            (SELECT MAX(ORDER_NUM) + 1 FROM MATERIAL_RISK_SETTINGS),
            #{session.userid, jdbcType=VARCHAR}, SYSDATE);
        END;
    </select>

    <update id="modifyRuleStatus">
        MERGE INTO MATERIAL_RISK_SETTINGS T
			USING (
			<foreach collection="rule" item="item" separator="union all">
				SELECT #{item.ID, jdbcType=VARCHAR} ID,
					   #{item.ORDER_NUM, jdbcType=VARCHAR} ORDER_NUM,
					   #{item.STATUS, jdbcType=VARCHAR} STATUS
				FROM DUAL
			</foreach>) S ON (T.ID = S.ID)
			WHEN MATCHED THEN
				UPDATE SET T.ORDER_NUM = S.ORDER_NUM, T.STATUS = S.STATUS
    </update>

    <select id="queryRuleById" resultType="java.lang.String">
        SELECT FILTER_CHAIN FROM MATERIAL_RISK_SETTINGS T WHERE T.ID = #{id, jdbcType=VARCHAR}
    </select>

    <delete id="deleteRule">
        DELETE FROM MATERIAL_RISK_SETTINGS T WHERE T.ID = #{id, jdbcType=VARCHAR}
    </delete>

    <select id="queryRules" resultType="java.util.HashMap">
        SELECT RISK_LEVEL, FILTER_CHAIN FROM MATERIAL_RISK_SETTINGS WHERE STATUS = 'Y' ORDER BY ORDER_NUM
    </select>

    <sql id="queryMaterialListByRuleSql">
        SELECT * FROM (
            SELECT MATERIAL,
                   PLANT_CODE,
                   ${levelSQL} AS RISK_LEVEL,
                   AVAILABILITY_CHECK,
                   EXISTING_IN_BOM,
                   LT_RANGE,
                   PLANNING_ALIVE_STATUS,
                   RES_COV_STK_LA_RANGE,
                   STARS_CODE,
                   VENDOR_NAME,
                   XBOARD,
                   COV_END_ORDER,
                   COV_RANGE_END_ORDER,
                   NUM_OF_WHERE_USE,
                   NUM_OF_WHERE_USE_RANGE
            FROM SCPA.MATERIAL_MASTER_V
        ) TT WHERE TT.RISK_LEVEL IS NOT NULL
    </sql>

    <select id="queryMaterialListByRuleCount" parameterType="java.util.Map" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
        <include refid="queryMaterialListByRuleSql"/>
        <include refid="global.count_footer"/>
	</select>

	<select id="queryMaterialListByRule" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryMaterialListByRuleSql"/>
        <include refid="global.select_footer"/>
    </select>

    <update id="recreateMaterialRiskLevelView">
        BEGIN
            execute immediate '
                DROP MATERIALIZED VIEW MATERIAL_RISK_LEVEL_V
            ';

            execute immediate '
            CREATE MATERIALIZED VIEW MATERIAL_RISK_LEVEL_V
            AS
            SELECT MATERIAL,
			       PLANT_CODE,
				   CAST(MAX(RISK_LEVEL) AS VARCHAR2(48)) AS RISK_LEVEL
			FROM (
                SELECT MATERIAL,
                       PLANT_CODE,
                       ${levelSQL} AS RISK_LEVEL
                FROM SCPA.MATERIAL_MASTER_V
            ) TT WHERE TT.RISK_LEVEL IS NOT NULL
            GROUP BY MATERIAL, PLANT_CODE
            ';
        END;
    </update>

    <update id="createEmptyView">
        BEGIN
            execute immediate '
                CREATE MATERIALIZED VIEW MATERIAL_RISK_LEVEL_V
                AS
                    SELECT MATERIAL,
                           PLANT_CODE,
                           CAST(NULL AS VARCHAR2(48)) AS RISK_LEVEL
                    FROM SCPA.MATERIAL_MASTER_V T WHERE 0 = 1
            ';
        END;
    </update>
</mapper>
