<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.adm.system.dao.ISystemDao">
	<select id="queryCalendar" parameterType="java.util.Map" resultType="com.adm.system.bean.CalendarBean">
		select to_char(extract(month from mm.day)) M,
			   to_char(extract(day from mm.day)) D,
			   to_char(INITCAP(substr(to_char(mm.day, 'day', 'NLS_DATE_LANGUAGE=AMERICAN'), 0, 3))) as W,
			   to_char(mm.working_day) WD,
			   to_char((trunc(mm.DATE$,'dd') - to_date('19700101', 'yyyymmdd')) * 86400 - 28800) start_time,
			   mm.week_no WK,
		       text
		from (select to_date(text, 'yyyy/mm/dd') day, working_day,DATE$,text, week_no from sy_calendar
		where
		<choose>
			<when test="name != null and name != ''.toString()">NAME = #{name,jdbcType=VARCHAR}</when>
			<otherwise>NAME = 'National Holidays'</otherwise>
		</choose>
		and text like #{year,jdbcType=VARCHAR} || '%') mm
		order by mm.day
	</select>

	<update id="updateCalendar" parameterType="java.util.Map">
		update sy_calendar set working_day = #{workingDay,jdbcType=VARCHAR} where NAME = #{name,jdbcType=VARCHAR} and text = #{text,jdbcType=VARCHAR}
	</update>

	<insert id="saveCalendarTemplate" parameterType="java.util.Map">
		insert into sy_calendar
		(text, working_day, name, is_default, date$, DATE_IN_SECS)
		select text, working_day, #{templateName,jdbcType=VARCHAR}, 0, date$, DATE_IN_SECS
		from sy_calendar
		where name = #{name,jdbcType=VARCHAR} and text like #{year,jdbcType=VARCHAR} || '%'
	</insert>

	<delete id="deleteCalendarTemplate" parameterType="java.util.Map">
		delete from sy_calendar
		where name = #{name,jdbcType=VARCHAR}
		      and text like #{year,jdbcType=VARCHAR} || '%'
		      and name != 'National Holidays'
	</delete>

	<select id="queryCalendarTemplate" parameterType="java.util.Map" resultType="java.lang.String">
		select distinct name from sy_calendar
	</select>

	<select id="queryUsersLiteCount" parameterType="java.util.Map" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		select sesa_code,user_name,entity_name from SY_USER_MASTER_DATA
		<if test="keywords != null and keywords !=''.toString()">
			where lower(sesa_code) like '%' || lower(#{keywords,jdbcType=VARCHAR}) || '%'
			or lower(user_name) like '%' || lower(#{keywords,jdbcType=VARCHAR}) || '%'
			or lower(entity_name) like '%' || lower(#{keywords,jdbcType=VARCHAR}) || '%'
		</if>
		<include refid="global.count_footer"/>
	</select>

	<select id="queryUsersLite" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="global.select_header"/>
		select sesa_code,user_name,entity_name from SY_USER_MASTER_DATA
		<if test="keywords != null and keywords !=''.toString()">
		where lower(sesa_code) like '%' || lower(#{keywords,jdbcType=VARCHAR}) || '%'
		      or lower(user_name) like '%' || lower(#{keywords,jdbcType=VARCHAR}) || '%'
		      or lower(entity_name) like '%' || lower(#{keywords,jdbcType=VARCHAR}) || '%'
		</if>
		<include refid="global.select_footer"/>
	</select>

	<sql id="queryMenuLiteSql">
		select * from (
			select  t.name,
					t.url,
					nvl(t2.name,t.parent_id) parent_name,
					t.classification,
					t.menu_type,
					t.menu_code
			from SY_MENU t left join SY_MENU t2 on t.parent_id = t2.menu_code
			<if test="keywords != null and keywords != ''.toString()">
				where (lower(t.name) like ('%' || lower(#{keywords,jdbcType=VARCHAR}) || '%')
				or lower(t.menu_type) like ('%' || lower(#{keywords,jdbcType=VARCHAR}) || '%')
				or lower(t.menu_code) like ('%' || lower(#{keywords,jdbcType=VARCHAR}) || '%')
				or lower(t2.name) like ('%' || lower(#{keywords,jdbcType=VARCHAR}) || '%'))
			</if>

			union all

			select t.label,
				   '-',
				   t.group_name,
				   t.classification,
				   'component',
				   t.id || ''
			from sy_components t
			<if test="keywords != null and keywords != ''.toString()">
				where (lower(t.label) like ('%' || lower(#{keywords,jdbcType=VARCHAR}) || '%')
				or lower(t.group_name) like ('%' || lower(#{keywords,jdbcType=VARCHAR}) || '%')
				or 'component' like ('%' || lower(#{keywords,jdbcType=VARCHAR}) || '%'))
			</if>) mm
		order by decode(mm.menu_type, 'menu', 0, 'component', 1, 'sub-menu', 2, 3), nvl(mm.name,mm.parent_name)
	</sql>

	<select id="queryMenuLiteCount" parameterType="java.util.Map" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="queryMenuLiteSql"/>
		<include refid="global.count_footer"/>
	</select>

	<select id="queryMenuLite" parameterType="java.util.Map" resultType="java.util.Map">
		<include refid="global.select_header"/>
		<include refid="queryMenuLiteSql"/>
		<include refid="global.select_footer"/>
	</select>

	<select id="queryAuthDetails" parameterType="java.util.Map" resultType="java.util.Map">
		select accessible, auth_details from SY_MENU_AUTH where lower(user_id) = lower(#{userCode, jdbcType=VARCHAR}) and menu_code = #{menuCode, jdbcType=VARCHAR}
	</select>

	<update id="deleteAuthDetails" parameterType="java.util.Map">
		delete from SY_MENU_AUTH where user_id = #{userCode,jdbcType=VARCHAR} and menu_code = #{menuCode,jdbcType=VARCHAR}
	</update>

	<update id="insertAuthDetails" parameterType="java.util.Map">
		merge into SY_MENU_AUTH t
		using (select #{userCode,jdbcType=VARCHAR} user_id,#{menuCode,jdbcType=VARCHAR} menu_code,#{authDetails,jdbcType=VARCHAR} auth_details,#{accessible,jdbcType=VARCHAR} accessible from dual) s
		on (t.user_id = s.user_id and t.menu_code = s.menu_code)
		when matched then
			update set t.auth_details = s.auth_details,t.accessible = s.accessible, t.update_by$ = #{session.userid,jdbcType=VARCHAR}, t.update_date$ = sysdate
		when not matched then
			insert (user_id,menu_code,auth_details,accessible,t.create_by$, t.create_date$)
			values (s.user_id,s.menu_code,s.auth_details,s.accessible, #{session.userid,jdbcType=VARCHAR}, sysdate)
	</update>

	<delete id="deleteExpiredPrivileges" parameterType="java.lang.String">
		BEGIN
			delete from SY_MENU_AUTH s
			where (s.user_id, s.menu_code) in
				  (select t.user_id, t.menu_code
				   from SY_MENU_AUTH t
							inner join SY_MENU t2
									   on t.menu_code = t2.menu_code
				   where ((t2.classification = 'public' and t.accessible = 'true') or
						  (t2.classification = 'private' and t.accessible = 'false'))
					 and t.auth_details is null
					 and t2.menu_type in ('menu', 'sub-menu'));

			DELETE FROM SY_MENU_AUTH T WHERE T.USER_ID NOT IN (
				SELECT SESA_CODE FROM SY_USER_MASTER_DATA
			);
		END;
	</delete>

	<select id="queryBaseMonth" resultType="java.lang.String">
		select property from sy_properties where name like 'ADU_ADF_MONTH%'
	</select>

	<insert id="saveBaseMonth">
		begin
			delete from sy_properties where name like 'ADU_ADF_MONTH%';
			insert into sy_properties (name, property)
			<foreach collection="list" separator=" union all" item="item" index="index">
				select 'ADU_ADF_MONTH${index}',#{item, jdbcType=VARCHAR} from dual
			</foreach>;
		end;
	</insert>

	<select id="queryMyFavorityAdminCnt" resultType="java.lang.Integer">
		SELECT SUM(CNT) CNT FROM (
			select count(1) CNT from SY_MENU_AUTH t
			 where t.user_id = upper(#{userid,jdbcType=VARCHAR})
			   and t.menu_code = 'sub-menu04'
			   and t.accessible = 'true'
			   and lower(t.auth_details) = 'admin'
        )
	</select>

	<select id="queryAllAvailableComponents" resultType="java.util.Map">
		<choose>
			<when test="isAdmin == true">
				select id, group_name, icon, url, label, t2.user_id
				from SY_COMPONENTS t left join SY_COMPONENTS_AUTH t2 on t.id = t2.component_id and upper(t2.user_id) = upper(#{userid,jdbcType=VARCHAR})
				order by t.group_name, t.label
			</when>
			<otherwise>
				select id, group_name, icon, url, label, t2.user_id
				from SY_COMPONENTS t left join SY_COMPONENTS_AUTH t2 on t.id = t2.component_id and upper(t2.user_id) = upper(#{userid,jdbcType=VARCHAR})
									 left join SY_MENU_AUTH t3 on t.id = t3.menu_code and upper(t3.user_id) = upper(#{userid,jdbcType=VARCHAR})
				where (t.classification = 'public' and (t3.accessible is null or t3.accessible = 'true'))
					  or (t.classification = 'private' and t3.accessible = 'true')
				order by t.group_name, t.label
			</otherwise>
		</choose>
	</select>

    <select id="queryComponentById" resultType="java.lang.String">
		select t.params
		  from SY_COMPONENTS t left join SY_MENU_AUTH t2 on t.id = t2.menu_code and upper(t2.user_id) = upper(#{session.userid,jdbcType=VARCHAR})
         where t.id = #{id,jdbcType=VARCHAR}
               and (
                   (t.classification = 'public' and (t2.accessible is null or t2.accessible = 'true'))
			 	or (t.classification = 'private' and t2.accessible = 'true')
               )
	</select>

    <delete id="deleteMyFavourites" parameterType="java.util.Map">
		delete from sy_components_auth where user_id = upper(#{session.userid,jdbcType=VARCHAR})
	</delete>

	<insert id="saveMyFavourites" parameterType="java.util.Map">
		insert into sy_components_auth (component_id,user_id)
		<foreach collection="selectedObj" separator=" union all " item="item">
			select #{item.ID,jdbcType=INTEGER}, upper(#{session.userid,jdbcType=VARCHAR}) from dual
		</foreach>
	</insert>

	<select id="queryExistsLastMonth" resultType="java.lang.Integer">
		select count(1) from SY_PROPERTIES t where t.PROPERTY = to_char(add_months(trunc(sysdate),-1),'yyyymm')
	</select>

	<select id="queryCurrentBaseMonth" resultType="java.lang.String">
		select property from SY_PROPERTIES where name like 'ADU_ADF_MONTH%' order by property
	</select>

	<select id="queryChangeLog" resultType="com.adm.system.bean.ChangeLogBean">
		select version,
			   submit_id as submit,
			   summary
		  from SY_CHANGE_LOG t
		order by date$ desc
		offset #{offset, jdbcType=INTEGER} rows fetch next #{next, jdbcType=INTEGER} rows only
	</select>

    <select id="queryChangeLogDetails" resultType="java.util.Map">
		select t.category, t.content, t2.user_name raise_by
		from SY_CHANGE_LOG_DETAILS t left join SY_USER_MASTER_DATA t2 on t.raise_by = t2.sesa_code
		where parent_id = #{parentID, jdbcType=VARCHAR}
	</select>

	<select id="queryChangeLogNotification" resultType="java.lang.Integer">
		select count(1)
		from SY_CHANGE_LOG t
		where t.DATE$ &gt; (
			select t0.DATE$ - 1
			from SY_CALENDAR t0
			where t0.NAME = 'National Holidays'
			  and t0.WORKING_DAY = '1'
			  and t0.DATE$ &lt; sysdate
			order by t0.DATE$ desc
				fetch next 1 rows only
		)
	</select>

    <select id="queryMainMenu" resultType="java.lang.String">
		select t.name
		  from sy_menu t
		 where t.PARENT_ID = '-1'
		   and t.MENU_TYPE = 'menu'
		 order by t.ORDER_NO
	</select>

	<select id="queryCurrentVersion" resultType="java.lang.String">
		select version from sy_change_log order by date$ desc fetch next 1 rows only
	</select>

    <select id="queryIPLocationInfo" resultType="java.util.Map">
		select ip_prefix, location from sy_ip_location
	</select>

	<select id="queryRaisedBy" resultType="java.util.Map">
		select t.USER_NAME, t.SESA_CODE
		  from SY_USER_MASTER_DATA t
		 order by USER_NAME
	</select>

    <insert id="saveChangelog">
		insert into SY_CHANGE_LOG (submit_id, summary, date$, version, release_by, release_time, commit_ids)
		values
		(#{id, jdbcType=VARCHAR}, #{summary, jdbcType=VARCHAR}, sysdate, #{version, jdbcType=VARCHAR},
		 #{releasedBy, jdbcType=VARCHAR}, #{releasedTime, jdbcType=VARCHAR}, #{commit_ids, jdbcType=VARCHAR})
	</insert>

	<insert id="saveChangelogDetails">
		insert into SY_CHANGE_LOG_DETAILS (parent_id, category, content, raise_by)
		<foreach collection="logDetails" item="item" separator=" union all ">
			<foreach collection="item.details" item="item2" separator=" union all ">
				select #{id, jdbcType=VARCHAR}, #{item.category, jdbcType=VARCHAR}, #{item2.content, jdbcType=VARCHAR}, #{item2.raisedBy, jdbcType=VARCHAR} from dual
			</foreach>
		</foreach>
	</insert>

	<select id="queryMailNotification" resultType="java.lang.Integer">
		select count(1) from sy_mail_recipient t where t.READ_TIME is null and t.SAVE_FOLDER = 'inbox' and t.RECIPIENT = #{mail, jdbcType=VARCHAR}
	</select>

    <select id="queryMailSignatureCount" resultType="java.lang.Integer">
		select count(1) from SY_USER_MAIL_SIGNATURE where SESA_CODE = #{userid, jdbcType=VARCHAR}
	</select>

	<update id="updateMailSignature">
		update SY_USER_MAIL_SIGNATURE
		   set SIG_CONTENT = #{content, jdbcType=CLOB},
		       UPDATE_BY$ = #{userid, jdbcType=VARCHAR},
			   UPDATE_DATE$ = SYSDATE
		 where SESA_CODE = #{userid, jdbcType=VARCHAR}
		   and SIG_TYPE = #{type, jdbcType=VARCHAR}
	</update>

	<insert id="insertMailSignature">
		insert into SY_USER_MAIL_SIGNATURE
		(SESA_CODE, SIG_CONTENT, SIG_TYPE, CREATE_BY$, CREATE_DATE$)
		VALUES
		(#{userid, jdbcType=VARCHAR}, #{content, jdbcType=CLOB}, #{type, jdbcType=CLOB}, #{userid, jdbcType=VARCHAR}, SYSDATE)
	</insert>

	<select id="queryMailSignature" resultType="java.util.Map">
		SELECT SIG_CONTENT, SIG_TYPE FROM SY_USER_MAIL_SIGNATURE WHERE SESA_CODE = #{userid, jdbcType=VARCHAR}
	</select>

	<select id="queryMVCount" resultType="java.lang.Integer">
		select count(1) from user_mviews t where t.MVIEW_NAME = #{mv, jdbcType=VARCHAR} and t.OWNER = 'SCPA'
	</select>

	<select id="queryMVIndexs" resultType="java.util.Map">
		SELECT INDEX_NAME, DBMS_METADATA.GET_DDL('INDEX', INDEX_NAME) AS DDL
		FROM USER_INDEXES IDX WHERE TABLE_NAME = #{mv, jdbcType=VARCHAR}
	</select>

	<update id="dropMVIndex">
		drop index "SCPA."."${indexName}"
	</update>

	<update id="refreshMV">
		begin
            DBMS_MVIEW.REFRESH(list =>'${mv}', Method =>'C', refresh_after_errors => false, atomic_refresh => false, out_of_place => true);
        end;
	</update>

	<select id="queryMVIndexCount" resultType="java.lang.Integer">
		select count(1) from USER_INDEXES t where t.INDEX_NAME = #{indexName, jdbcType=VARCHAR} and TABLE_OWNER = 'SCPA'
	</select>

	<update id="createMVIndex">
		${createDDL}
	</update>

	<select id="queryCpuWorkload" resultType="java.util.Map">
		SELECT to_char(T.TIME, 'yyyy/MM/dd hh24:mi') as time,
			   T.CPU,
			   T.MEMORY,
			   T.ORA_MEMORY,
			   T.JAVA_MEMORY,
			   T.TRAFFIC_IN,
			   T.TRAFFIC_OUT
		from SY_CPU_MONITOR T
		<where>
			<if test="serverID != null and serverID != ''">
				and t.server_id = #{serverID, jdbcType=VARCHAR}
			</if>
			<if test="dateRange != null">
				and t.time between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') and to_date(#{dateRange[1], jdbcType=VARCHAR} || ' 23:59', 'yyyy/mm/dd hh24:mi')
			</if>
		</where>
		order by to_char(t.time, 'yyyy/mm/dd hh24:mi')
	</select>

	<select id="querySurveyMenu" resultType="java.util.Map">
		SELECT T2.MENU_CODE, T2.NAME, COUNT(1) AS "COUNT"
		  FROM SY_VISIT_LOGS T
			   INNER JOIN SY_MENU T2 ON T.URL = T2.URL
		 WHERE T.VISIT_TIME > SYSDATE - 180
		   AND T2.NAME IS NOT NULL
		   AND T.USERID = #{session.userid,jdbcType=VARCHAR}
		   AND T2.MENU_CODE NOT LIKE 'menu9%'
  		   AND T2.MENU_CODE NOT IN ('menu120')
		 GROUP BY T2.MENU_CODE, T2.NAME
		 HAVING COUNT(1) > 15
		 ORDER BY COUNT(1) DESC
	</select>

	<select id="querySurveyMenuCount" resultType="java.lang.Integer">
		SELECT COUNT(1) AS "COUNT"
		  FROM SY_VISIT_LOGS T
			   INNER JOIN SY_MENU T2 ON T.URL = T2.URL
		 WHERE T.VISIT_TIME > SYSDATE - 180
		   AND T2.NAME IS NOT NULL
		   AND T.USERID = #{session.userid,jdbcType=VARCHAR}
	</select>

	<select id="querySystemProperties" resultType="java.lang.String">
		SELECT PROPERTY FROM SY_PROPERTIES T WHERE T.NAME = #{key, jdbcType=VARCHAR}
	</select>

	<select id="querySurveyResultCount" resultType="java.lang.Integer">
		SELECT COUNT(1) FROM SY_MANPOWER_SURVEY WHERE USER_ID = #{session.userid,jdbcType=VARCHAR} AND SURVEY_BATCH_ID = '202205'
	</select>

	<insert id="submitSurvey">
		INSERT INTO SY_MANPOWER_SURVEY
		(SURVEY_BATCH_ID, USER_ID, MENU_CODE, SAVING, CREATE_DATE$, TIME_UNIT, COMMENTS)
		<foreach collection="data" separator=" union all" item="item" index="index">
			SELECT '202205', #{session.userid,jdbcType=VARCHAR}, #{item.menuCode, jdbcType=VARCHAR},
			       #{item.saving, jdbcType=DOUBLE}, SYSDATE, #{item.timeUnit, jdbcType=VARCHAR},
			       #{item.comments, jdbcType=VARCHAR} FROM DUAL
		</foreach>
	</insert>

    <insert id="saveRefreshLog">
		insert into SCPL.SY_TAB_UPDATE_LOG
		(DATE$, SOURCE, OPERATION, START_DATE, HOSTNAME, IP, KEY, TOTAL, SUCCESS, ERROR)
 		values
		(sysdate, #{source, jdbcType=VARCHAR}, 'refresh_mv', sysdate, #{host, jdbcType=VARCHAR}, #{ip, jdbcType=VARCHAR}, #{key, jdbcType=VARCHAR}, 1, 1, 0)
	</insert>

	<update id="updateRefreshLog">
		UPDATE SCPL.SY_TAB_UPDATE_LOG
		   SET SECONDS = #{seconds, jdbcType=DOUBLE},
		       NOTE = #{note, jdbcType=VARCHAR},
		       END_DATE = sysdate
		 WHERE SOURCE = #{source, jdbcType=VARCHAR}
		   AND DATE$ > sysdate - 1
	</update>

	<insert id="saveClickData">
		INSERT INTO SY_CLICKS
		(URL, USERID, EVENT, CLICK_X, CLICK_Y, CREATE_DATE$)
		<foreach collection="clicks" item="item" separator=" union all " index="index">
			SELECT #{url, jdbcType=VARCHAR}, #{user, jdbcType=VARCHAR}, #{item.type, jdbcType=VARCHAR}, #{item.clickX}, #{item.clickY}, sysdate FROM DUAL
		</foreach>
	</insert>

	<select id="queryClickData" resultType="java.util.Map">
		SELECT EVENT as "type", CLICK_X as "clickX", CLICK_Y as "clickY"
		FROM SY_CLICKS
		WHERE URL = #{url, jdbcType=VARCHAR}
		  <if test="users.size() > 0">
			  AND USERID in
				<foreach collection="users" item="user" separator="," open="(" close=")">
					#{user,jdbcType=VARCHAR}
				</foreach>
		  </if>
		  AND CREATE_DATE$ BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') AND TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
	</select>

	<select id="queryUserOpts" resultType="java.util.Map">
		SELECT USER_NAME || ' [' || SESA_CODE || ']' AS NAME,
		       SESA_CODE AS "VALUE",
		       NVL(ENTITY_NAME, 'Others') AS CATEGORY
		  FROM SY_USER_MASTER_DATA
		 ORDER BY DECODE(NVL(ENTITY_NAME, 'Others'), 'Others', 'ZZZZZ', NVL(ENTITY_NAME, 'Others')), NAME
	</select>

	<select id="queryClickShowAuth" resultType="java.util.Map">
		SELECT SESA_CODE FROM SY_USER_MASTER_DATA T
		WHERE T.IS_MAINTAINER = 'Y'
		AND T.SESA_CODE = #{userid, jdbcType=VARCHAR}
	</select>

	<update id="saveUrlTableMapping">
		BEGIN
			DELETE FROM SY_METHOD_TABLE_MAPPING T WHERE T.LAST_ACTIVE_TIME &lt; #{currentTime, jdbcType=NUMERIC} - 86400 * 90;

			MERGE INTO SY_METHOD_TABLE_MAPPING T
			USING (
			<foreach collection="data" item="item" separator="union all">
				SELECT #{item.URL, jdbcType=VARCHAR} URL,
					   #{item.TABLE_NAME, jdbcType=VARCHAR} TABLE_NAME,
					   #{item.LAST_ACTIVE_TIME, jdbcType=VARCHAR} LAST_ACTIVE_TIME
				FROM DUAL
			</foreach>) S ON (T.URL = S.URL AND T.TABLE_NAME = S.TABLE_NAME)
			WHEN MATCHED THEN
				UPDATE SET T.LAST_ACTIVE_TIME = S.LAST_ACTIVE_TIME
			WHEN NOT MATCHED THEN
				INSERT (URL, TABLE_NAME, LAST_ACTIVE_TIME)
				VALUES (S.URL, S.TABLE_NAME, S.LAST_ACTIVE_TIME);
		END;
	</update>
</mapper>
