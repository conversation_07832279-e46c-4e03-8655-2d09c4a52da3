<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="global">
	<sql id="select_header" >
		<choose>
			<when test="_page.outputColumns != null">
				SELECT
					<foreach collection="_page.outputColumns" item="item" separator=",">
						"${item}"
					</foreach>
				FROM (
			</when>
			<otherwise>
				SELECT * FROM (
			</otherwise>
		</choose>
	</sql>
	<sql id="select_footer">
		) PAGE WHERE 1 = 1
		<if test="_page.conditions != null and _page.conditions.sql != ''.toString()">
			AND ${_page.conditions.sql}
		</if>
		<if test="_page.sort != null and _page.sort != ''.toString()">
			ORDER BY ${_page.sort} NULLS LAST
		</if>
		<choose>
			<when test="_page.pagging">
				OFFSET #{_page.start,jdbcType=INTEGER} ROWS FETCH NEXT #{_page.length,jdbcType=INTEGER} ROWS ONLY
			</when>
			<otherwise>
				OFFSET 0 ROWS FETCH NEXT #{_page.maxRows,jdbcType=INTEGER} ROWS ONLY
			</otherwise>
		</choose>
	</sql>
	<sql id="count_header">
		<if test="not _page.pagging">
			SELECT 1 FROM DUAL UNION SELECT 1 FROM (
		</if>
		<if test="_page.pagging">
			SELECT COUNT(1) FROM (
		</if>
	</sql>
	<sql id="count_footer">
		<if test="_page.pagging">
			) PAGE WHERE 1 = 1
			<if test="_page.conditions != null and _page.conditions.sql != ''.toString()">
				AND ${_page.conditions.sql}
			</if>
		</if>
		<if test="not _page.pagging">
			) PAGE WHERE 0 = 1
		</if>
	</sql>
</mapper>
