<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.adm.system.dao.IAccessLogsDao">

    <select id="queryCascader" resultType="java.util.Map">
        WITH SOURCE AS (
            SELECT T.URL AS NAME, 'ACCESS_URL' AS CATEGORY FROM SY_MENU T WHERE T.MENU_TYPE = 'method'

            UNION ALL

            SELECT 'query', 'OPR_TYPE' FROM DUAL

            UNION ALL

            SELECT 'upload', 'OPR_TYPE' FROM DUAL

            UNION ALL

            SELECT 'download', 'OPR_TYPE' FROM DUAL

            UNION ALL

            SELECT 'delete', 'OPR_TYPE' FROM DUAL

            UNION ALL

            SELECT 'upsert', 'OPR_TYPE' FROM DUAL

            UNION ALL

            SELECT T.USER_NAME || ' [' || T.EMAIL || ']', 'USER_NAME' FROM SY_USER_MASTER_DATA T

            UNION ALL

            SELECT '200', 'RESPONSE_CODE' FROM DUAL

            UNION ALL

            SELECT '500', 'RESPONSE_CODE' FROM DUAL

            UNION ALL

            SELECT T.NAME, 'MENU_NAME' FROM SY_MENU T WHERE T.MENU_TYPE = 'menu' AND T.PARENT_ID != '-1'

            UNION ALL

            SELECT '[sub-menu]', 'MENU_NAME' FROM DUAL
        )

        SELECT * FROM SOURCE T ORDER BY T.CATEGORY,DECODE(T.NAME,'Others','zzz',T.NAME)
    </select>

    <sql id="queryReport1SQL">
        SELECT * FROM (
            SELECT NVL(T4.NAME, '[sub-menu]') AS MENU_NAME,
                   ACCESS_URL,
                   OPR_TYPE,
                   REQUEST_LENGTH,
                   RESPONSE_LENGTH,
                   TO_CHAR(ACCESS_TIME, 'YYYY/MM/DD HH24:MI:SS') ACCESS_TIME,
                   TIMECOST_IN_MS,
                   RESPONSE_CODE,
                   T2.USER_NAME || ' [' || T2.EMAIL || ']' USER_NAME,
                   REQUEST_BODY,
                   RESPONSE_TXT,
                   ERROR_MESSAGE
              FROM SY_METHOD_ACCESS_LOGS T LEFT JOIN SY_USER_MASTER_DATA T2 ON T.USER_ID = T2.SESA_CODE
                                           LEFT JOIN SY_MENU T3 ON T.ACCESS_URL = T3.URL
                                           LEFT JOIN SY_MENU T4 ON T3.PARENT_ID = T4.MENU_CODE
             WHERE T.ACCESS_TIME BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'YYYY/MM/DD') AND TO_DATE(#{dateRange[1], jdbcType=VARCHAR} || ' 23:59:59', 'YYYY/MM/DD HH24:MI:SS')
         ) T
         <if test="filters != null and filters != ''.toString()">
            WHERE ${filters}
         </if>
         ORDER BY ACCESS_TIME DESC
    </sql>

    <select id="queryReport1Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1SQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport2" resultType="java.util.Map">
        WITH SOURCE AS (
            SELECT * FROM (
                SELECT NVL(T4.NAME, '[sub-menu]') AS MENU_NAME,
                       ACCESS_URL,
                       OPR_TYPE,
                       REQUEST_LENGTH,
                       RESPONSE_LENGTH,
                       ACCESS_TIME,
                       RESPONSE_CODE,
                       T2.USER_NAME || ' [' || T2.EMAIL || ']' USER_NAME
                  FROM SY_METHOD_ACCESS_LOGS T LEFT JOIN SY_USER_MASTER_DATA T2 ON T.USER_ID = T2.SESA_CODE
                                               LEFT JOIN SY_MENU T3 ON T.ACCESS_URL = T3.URL
                                               LEFT JOIN SY_MENU T4 ON T3.PARENT_ID = T4.MENU_CODE
                 WHERE T.ACCESS_TIME BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'YYYY/MM/DD') AND TO_DATE(#{dateRange[1], jdbcType=VARCHAR} || ' 23:59:59', 'YYYY/MM/DD HH24:MI:SS')
             ) T
             <if test="filters != null and filters != ''.toString()">
                WHERE ${filters}
             </if>
         )
        SELECT TO_CHAR(T.ACCESS_TIME, 'YYYY/MM/DD HH24:MI') KEY,
               TO_CHAR(T.ACCESS_TIME, 'HH24:MI')            XAXIS,
               ROUND(SUM(T.REQUEST_LENGTH) / 1024, 0)       YAXIS1,
               ROUND(SUM(T.RESPONSE_LENGTH) / 1024, 0)      YAXIS2,
               COUNT(1)                                     YAXIS3
        FROM SOURCE T
        GROUP BY TO_CHAR(T.ACCESS_TIME, 'HH24:MI'), TO_CHAR(T.ACCESS_TIME, 'YYYY/MM/DD HH24:MI')
        ORDER BY TO_CHAR(T.ACCESS_TIME, 'YYYY/MM/DD HH24:MI')
        OFFSET 0 ROWS FETCH NEXT 14400 ROWS ONLY
    </select>
</mapper>
