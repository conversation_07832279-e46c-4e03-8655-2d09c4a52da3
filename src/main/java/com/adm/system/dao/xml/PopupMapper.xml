<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.adm.system.dao.IPopupDao">
	<select id="queryPopupConfig" parameterType="java.util.Map">
		SELECT T.URL,
		       T.REMIND_SUBJECT,
		       T.FREQUENCY,
		       T.REMIND_COUNT,
		       T.DESCRIPTION,
		       T.VALID_FROM,
		       T.VALID_TO,
		       T1.AUTH_TYPE,
		       T1.AUTH_DETAIL
		FROM SY_DIALOG_CONFIG T LEFT JOIN SY_DIALOG_AUTH T1 ON T.URL = T1.URL AND T.REMIND_SUBJECT = T1.REMIND_SUBJECT
								LEFT JOIN BROADCAST_EXCLUDE_LIST T2 ON T.URL = T2.URL AND T.REMIND_SUBJECT = T2.REMIND_SUBJECT AND T2.SESA_CODE = #{username, jdbcType=VARCHAR}
		WHERE SYSDATE BETWEEN VALID_FROM AND VALID_TO
		  AND T.URL IN (#{url, jdbcType=VARCHAR}, '*')
		  AND T2.URL IS NULL
	</select>

	<select id="queryUrlVisitHistory" parameterType="java.util.Map" resultType="com.adm.system.bean.PopupDate">
		SELECT VISIT_TIME AS date$,TO_NUMBER(SC.WEEK_NO) AS week, SC.month, SC.year
		FROM SY_VISIT_LOGS T
			     LEFT JOIN SY_CALENDAR SC
			               ON TRUNC(T.VISIT_TIME, 'DD') = SC.DATE$ AND SC.NAME = 'National Holidays'
		WHERE RESPONSE_CODE = '200'
		  AND URL = #{url, jdbcType=VARCHAR}
		  AND USERID = #{username, jdbcType=VARCHAR}
	</select>

	<select id="queryCurrentDate" resultType="com.adm.system.bean.PopupDate">
		SELECT SYSDATE AS date$,TO_NUMBER(WEEK_NO) AS week, month, year FROM SY_CALENDAR
		WHERE TRUNC(SYSDATE, 'DD') = DATE$ AND NAME = 'National Holidays'
	</select>

	<select id="queryUserInfo" parameterType="java.util.Map">
		SELECT SESA_CODE, USER_NAME, ENTITY_NAME, JOB_CODE FROM SY_USER_MASTER_DATA WHERE SESA_CODE = #{username, jdbcType=VARCHAR}
	</select>

	<insert id="saveNeverDisplay" parameterType="java.util.Map">
		INSERT INTO SCPA.BROADCAST_EXCLUDE_LIST (URL, REMIND_SUBJECT, SESA_CODE, CREATE_DATE$)
		VALUES (#{excludeContent.url, jdbcType=VARCHAR}, #{excludeContent.remindSubject, jdbcType=VARCHAR}, #{username, jdbcType=VARCHAR}, SYSDATE)
	</insert>
</mapper>
