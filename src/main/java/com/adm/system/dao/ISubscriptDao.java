package com.adm.system.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface ISubscriptDao {

    Map<String, Object> queryReportSubscript(Map<String, Object> parameterMap);

    List<String> queryReportReference(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReferences(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryAllTableComments();

    List<Map<String, Object>> queryTableReferences(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReportsByTablename(Map<String, Object> parameterMap);

    String queryReportSyncDate(@Param("list") List<String> refs);

    String queryReportSubscriptTitle(Map<String, Object> parameterMap);

    String queryDocContentByID(Map<String, Object> parameterMap);

    String queryMenuNameByURL(Map<String, Object> parameterMap);

    void modifySubscriptDoc(Map<String, Object> parameterMap);

    String querySubscriptDocTempate();

    int queryReportSubscriptCount(Map<String, Object> parameterMap);

    List<String> queryAvalibleMethods(Map<String, Object> parameterMap);

    void modifyReportRel(Map<String, Object> parameterMap);

    List<String> querySelectedMethods(Map<String, Object> parameterMap);
}
