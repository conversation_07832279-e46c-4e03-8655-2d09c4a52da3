package com.adm.system.dao;

import com.starter.context.bean.Response;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface IMaterialRiskDao {

    List<String> querySuggestionByKeywords(String field, String type, String keywords);

    Response saveRule(Map<String, Object> parameterMap);

    List<Map<String, Object>> searchRules();

    void modifyRuleStatus(Map<String, Object> parameterMap);

    String queryRuleById(Map<String, Object> parameterMap);

    void deleteRule(Map<String, Object> parameterMap);

    List<Map<String, String>> queryRules();

    List<Map<String, Object>> queryMaterialListByRule(Map<String, Object> parameterMap);

    int queryMaterialListByRuleCount(Map<String, Object> parameterMap);

    void recreateMaterialRiskLevelView(Map<String, Object> parameterMap);

    void createEmptyView();
}
