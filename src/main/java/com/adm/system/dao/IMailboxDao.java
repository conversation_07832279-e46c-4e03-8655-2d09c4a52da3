package com.adm.system.dao;

import com.adm.system.bean.MailboxSenderBean;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface IMailboxDao {

    void saveMailBody(MailboxSenderBean mailboxSenderBean);

    void updateMailBody(MailboxSenderBean mailboxSenderBean);

    void saveMailRecipients(@Param("id") String id, @Param("recipients") List<String> recipients, @Param("send_by") String send_by);

    void deleteMail(Map<String, Object> parameterMap);

    Map<String, Object> queryMailByID(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryMailList(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryMailCount(Map<String, Object> parameterMap);

    void updateMailReadTime(Map<String, Object> parameterMap);

    void markAllMailAsRead(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryMailtoByKeywords(Map<String, Object> parameterMap);

    void saveMailDraft(@Param("id") String id, @Param("from") String from);

    void updateMailDraft(@Param("id") String id, @Param("from") String from);

    int queryMailDraftCount(@Param("id") String id);
}
