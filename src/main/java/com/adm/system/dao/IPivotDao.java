package com.adm.system.dao;

import org.apache.ibatis.annotations.Mapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IPivotDao {

    int queryDataCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryData(Map<String, Object> parameterMap);

    int queryDetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryDetails(Map<String, Object> parameterMap);
}
