package com.adm.system.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface IDataSyncDao {

    List<Map<String, Object>> queryReport1(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport1Change(@Param("selected") List<String> selected);

    void saveReport1Template(Map<String, Object> parameterMap);

    void deleteReport1Template(Map<String, Object> parameterMap);

    String queryReport1Template(Map<String, Object> parameterMap);

    List<String> queryReport2Version();

    List<Map<String, Object>> queryReport2(Map<String, Object> parameterMap);

    Map<String, Object> queryReport2Details(Map<String, Object> parameterMap);

    void modifyReport2Details(Map<String, Object> parameterMap);

    void deleteReport2Details(Map<String, Object> parameterMap);

    void createReport2Details(Map<String, Object> parameterMap);

    List<String> queryReport3(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport4(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport5(Map<String, Object> parameterMap);

    void updateErrorLogStatus(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport1TemplateOpts();
}
