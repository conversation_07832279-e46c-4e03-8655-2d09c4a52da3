package com.adm.system.dao;

import com.scp.toolbox.bean.TreeData;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface IDocumentationDao {

    List<String> queryExistsGroup();

    List<TreeData> queryDocList();

    void saveNewDoc(Map<String, Object> parameterMap);

    Map<String, String> queryDoc(Map<String, Object> parameterMap);

    void modifyDoc(Map<String, Object> parameterMap);

    void deleteDoc(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryColumnsByTableName(String tablename, String database);

    Map<String, Object> queryOneRowForSample(String tablename, String database);
}
