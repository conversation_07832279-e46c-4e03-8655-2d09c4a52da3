package com.adm.system.dao;

import com.adm.system.bean.PopupDate;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface IPopupDao {

    List<Map<String, Object>> queryPopupConfig(Map<String, Object> parameterMap);

    List<PopupDate> queryUrlVisitHistory(Map<String, Object> parameterMap);

    PopupDate queryCurrentDate(Map<String, Object> parameterMap);

    Map<String, String> queryUserInfo(Map<String, Object> parameterMap);

    void saveNeverDisplay(Map<String, Object> parameterMap);
}
