package com.adm.system.dao;

import com.adm.system.bean.CalendarBean;
import com.adm.system.bean.ChangeLogBean;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface ISystemDao {

    List<CalendarBean> queryCalendar(Map<String, Object> parameterMap);

    void updateCalendar(Map<String, Object> parameterMap);

    void saveCalendarTemplate(Map<String, Object> parameterMap);

    List<String> queryCalendarTemplate(Map<String, Object> parameterMap);

    void deleteCalendarTemplate(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryUsersLite(Map<String, Object> parameterMap);

    int queryUsersLiteCount(Map<String, Object> parameterMap);

    int queryMenuLiteCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryMenuLite(Map<String, Object> parameterMap);

    Map<String, Object> queryAuthDetails(Map<String, Object> parameterMap);

    void deleteAuthDetails(Map<String, Object> parameterMap);

    void insertAuthDetails(Map<String, Object> parameterMap);

    void deleteExpiredPrivileges(String key);

    List<String> queryBaseMonth();

    void saveBaseMonth(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryAllAvailableComponents(String userid, boolean isAdmin);

    void deleteMyFavourites(Map<String, Object> parameterMap);

    void saveMyFavourites(Map<String, Object> parameterMap);

    String queryComponentById(Map<String, Object> parameterMap);

    List<String> queryCurrentBaseMonth();

    int queryExistsLastMonth();

    List<ChangeLogBean> queryChangeLog(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryChangeLogDetails(@Param("parentID") String submit);

    int queryChangeLogNotification();

    List<String> queryMainMenu();

    String queryCurrentVersion();

    List<Map<String, String>> queryIPLocationInfo();

    List<Map<String, String>> queryRaisedBy(Map<String, Object> parameterMap);

    void saveChangelog(Map<String, Object> parameterMap);

    void saveChangelogDetails(Map<String, Object> parameterMap);

    int queryMailNotification(@Param("mail") String mail);

    int queryMailSignatureCount(@Param("userid") String userid, @Param("type") String type);

    void updateMailSignature(@Param("userid") String userid, @Param("content") String content, @Param("type") String type);

    void insertMailSignature(@Param("userid") String userid, @Param("content") String content, @Param("type") String type);

    List<Map<String, Object>> queryMailSignature(@Param("userid") String userid);

    int queryMVCount(@Param("mv") String mv);

    List<Map<String, Object>> queryMVIndexs(@Param("mv") String mv);

    void dropMVIndex(@Param("indexName") String index_name);

    void refreshMV(@Param("mv") String mv);

    int queryMVIndexCount(@Param("indexName") String indexName);

    void createMVIndex(@Param("createDDL") String createDDL);

    List<Map<String, Object>> queryCpuWorkload(Map<String, Object> parameterMap);

    List<Map<String, Object>> querySurveyMenu(Map<String, Object> parameterMap);

    String querySystemProperties(@Param("key") String key);

    int querySurveyMenuCount(Map<String, Object> parameterMap);

    int querySurveyResultCount(Map<String, Object> parameterMap);

    void submitSurvey(Map<String, Object> parameterMap);

    void saveRefreshLog(String source, String host, String ip, String key);

    void updateRefreshLog(String source, String note, double seconds);

    void saveClickData(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryClickData(Map<String, Object> parameterMap);

    List<Map<String, String>> queryUserOpts();

    List<Map<String, Object>> queryClickShowAuth(Map<String, Object> parameterMap);

    void saveUrlTableMapping(List<Map<String, Object>> data, Long currentTime);

    int queryMyFavorityAdminCnt(String userid);
}
