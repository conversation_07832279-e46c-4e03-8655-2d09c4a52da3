package com.adm.system;

import com.adm.system.service.IPopupService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin
@SchneiderRequestMapping("/popup")
@Scope("prototype")
public class PopupController extends ControllerHelper {
    @Resource
    private IPopupService popupService;

    @SchneiderRequestMapping("/query_popup_config")
    public Response queryPopupConfig(HttpServletRequest request) {
        super.pageLoad(request);
        return popupService.queryPopupConfig(parameterMap, this.getIpAddrs(request));
    }

    @SchneiderRequestMapping("/save_never_display")
    public Response saveNeverDisplay(HttpServletRequest request) {
        super.pageLoad(request);
        return popupService.saveNeverDisplay(parameterMap);
    }
}
