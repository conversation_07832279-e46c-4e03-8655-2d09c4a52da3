package com.adm.system.bean;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Date;

public class PopupDate {
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private Date date$;
    private int week;
    private int month;
    private int year;

    public Date getDate() {
        return date$;
    }

    public int getWeek() {
        return week;
    }

    public int getMonth() {
        return month;
    }

    public int getYear() {
        return year;
    }
}
