package com.adm.system.bean;
import java.util.List;
import java.util.Map;

public class BroadcastConfigBean {
    private String url;
    private String pageName;
    private String remindSubject;
    private String frequency;
    private Integer remindCount;
    private String description;
    private String validFrom;
    private String validTo;
    private Map<String, List<String>> authInfo;

    public String getValidFrom() {
        return validFrom;
    }

    public void setValidFrom(String validFrom) {
        this.validFrom = validFrom;
    }

    public String getValidTo() {
        return validTo;
    }

    public void setValidTo(String validTo) {
        this.validTo = validTo;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getRemindSubject() {
        return remindSubject;
    }

    public void setRemindSubject(String remindSubject) {
        this.remindSubject = remindSubject;
    }

    public Integer getRemindCount() {
        return remindCount;
    }

    public void setRemindCount(Integer remindCount) {
        this.remindCount = remindCount;
    }

    public String getFrequency() {
        return frequency;
    }

    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }


    public Map<String, List<String>> getAuthInfo() {
        return authInfo;
    }

    public void setAuthInfo(Map<String, List<String>> authInfo) {
        this.authInfo = authInfo;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getPageName() {
        return pageName;
    }

    public void setPageName(String pageName) {
        this.pageName = pageName;
    }
}
