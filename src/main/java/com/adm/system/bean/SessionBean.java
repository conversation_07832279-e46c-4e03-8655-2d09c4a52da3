package com.adm.system.bean;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.Date;

public class SessionBean {
    private String key;
    private String userid;
    private String username;
    private String loginIP;
    private String location;
    private long expiringTimeLong;
    private long lastActiveTimeLong;

    private static final int SESSION_TIME_IN_SECONDS = 28800; // 8H

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getLoginIP() {
        return loginIP;
    }

    public void setLoginIP(String loginIP) {
        this.loginIP = loginIP;
    }

    public String getExpiringTime() {
        return new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date(System.currentTimeMillis() + expiringTimeLong * 1000));
    }

    public String getLastActiveTime() {
        return new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date(System.currentTimeMillis() + (expiringTimeLong - SESSION_TIME_IN_SECONDS) * 1000));
    }

    public long getExpiringTimeLong() {
        return expiringTimeLong;
    }

    public String getIdleTime() {
        BigDecimal cd = new BigDecimal(SESSION_TIME_IN_SECONDS - expiringTimeLong);
        String unit = "s";
        BigDecimal sixty = new BigDecimal(60);
        if (cd.compareTo(sixty) > 0) {
            cd = cd.divide(sixty, 1, RoundingMode.HALF_UP);
            unit = "m";
        }
        if (cd.compareTo(sixty) > 0) {
            cd = cd.divide(sixty, 1, RoundingMode.HALF_UP);
            unit = "h";
        }
        if (cd.compareTo(BigDecimal.ZERO) == 0) {
            return "0";
        }
        return cd.toPlainString() + unit;
    }

    public void setExpiringTimeLong(long expiringTimeLong) {
        this.expiringTimeLong = expiringTimeLong;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }
}
