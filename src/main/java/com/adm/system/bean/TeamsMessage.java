package com.adm.system.bean;

import java.util.ArrayList;
import java.util.List;

public class TeamsMessage {
    private String type = "message";
    private List<TeamsMessageAttachment> attachments = new ArrayList<>();

    TeamsMessageAttachment attachment;
    TeamsMessageAttachmentContent content;

    public TeamsMessage() {
        attachment = new TeamsMessageAttachment();
        content = new TeamsMessageAttachmentContent();

        attachment.setContent(content);
        attachments.add(attachment);
    }

    public void addContentBody(TeamsMessageAttachmentContentBody body) {
        content.addBody(body);
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<TeamsMessageAttachment> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<TeamsMessageAttachment> attachments) {
        this.attachments = attachments;
    }
}
