package com.adm.system.bean;

import com.starter.utils.Utils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 将每个单独条件进行解析
 * {
 * "column": "FISCAL_YEAR",
 * "conditions": [{
 * "args": [],
 * "name": "empty"
 * },
 * {
 * "args": ["1"],
 * "name": "eq"
 * }],
 * {
 * "args": [[]],
 * "name": "by_value"
 * },
 * "operation": "conjunction"
 * }
 */
public class ConditionBean {
    private String column;
    private String operation;
    private List<SubConditionBean> conditions = new ArrayList<>();
    private Map<String, Object> parameter = new HashMap<>();
    Map<String, String> colDefMap = new HashMap<>();
    Map<String, String> colFormatMap = new HashMap<>();

    public String getColumn() {
        return column;
    }

    public void setColumn(String column) {
        this.column = column;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public List<SubConditionBean> getConditions() {
        return conditions;
    }

    public void setConditions(List<SubConditionBean> conditions) {
        this.conditions = conditions;
    }

    public Map<String, Object> getParameter() {
        return parameter;
    }

    public void setParameter(Map<String, Object> parameter) {
        this.parameter = parameter;
    }

    public Map<String, String> getColDefMap() {
        return colDefMap;
    }

    public void setColDefMap(Map<String, String> colDefMap) {
        this.colDefMap = colDefMap;
    }

    public String toString() {
        if (Utils.hasInjectionAttack(column)) {
            return " 0 = 1 ";
        }
        for (SubConditionBean subConditionBean : conditions) {
            subConditionBean.setColumn(column);
            subConditionBean.setParameter(parameter);
            subConditionBean.setColDefMap(colDefMap);
            subConditionBean.setColFormatMap(colFormatMap);
        }

        String join = " OR ";
        if ("conjunction".equalsIgnoreCase(operation)) {
            join = " AND ";
        }

        return "(" + StringUtils.join(conditions, join) + ")";
    }

    public void setFormatMap(Map<String, String> colFormatMap) {
        this.colFormatMap = colFormatMap;
    }
}
