package com.adm.system.bean;

import com.alibaba.fastjson.JSON;

import jakarta.validation.constraints.NotNull;
import java.util.List;

public class MailboxSenderBean {
    private String mail_id; // 邮件id,自动生成
    @NotNull(message = "Recipient cannot be null")
    private List<String> to; // 收件人地址
    private String scope; // 邮件所属的范畴,供显示角标使用
    private String subject; // 邮件标题
    private String body; // html的邮件体
    private String from; // 发件人
    private int type; // 发件类型, 0: 草稿, 1: 发送
    private boolean mail = false; // 是否同步发送邮件到收件人邮箱

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getMail_id() {
        return mail_id;
    }

    public void setMail_id(String mail_id) {
        this.mail_id = mail_id;
    }

    public List<String> getTo() {
        return to;
    }

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    public void setTo(List<String> to) {
        this.to = to;
    }

    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public boolean isMail() {
        return mail;
    }

    public void setMail(Object mail) {
        if (mail instanceof Boolean) {
            this.mail = (Boolean) mail;
        } else if (mail instanceof Integer) {
            this.mail = ((Integer) mail == 1);
        } else {
            this.mail = mail.toString().equalsIgnoreCase("true");
        }

    }

    public String getRecipients() {
        return JSON.toJSONString(this.to);
    }
}
