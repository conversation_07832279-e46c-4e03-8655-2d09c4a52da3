package com.adm.system.bean;

public class RecipientType {
    public static final String MATERIAL = "material";
    public static final String MRPCN = "mrpcn";
    public static final String ENTITY = "entity";
    public static final String PLANT_CODE = "plant_code";
    public static final String CLUSTER = "cluster";
    public static final String GSC_CHINA = "gsc_china";
    public static final String VENDOR_CODE = "vendor_code";
    public static final String PRODUCT_LINE = "product_line";
    public static final String BU = "bu";
    public static final String LOCAL_PRODUCT_LINE = "local_product_line";
    public static final String LOCAL_PRODUCT_FAMILY = "local_product_family";
    public static final String LOCAL_PRODUCT_SUBFAMILY = "local_product_subfamily";
    public static final String PRODUCTION_LINE = "production_line";
    public static final String FACTORY_PRODUCT_FAMILY = "factory_product_family";
    public static final String FACTORY_PRODUCT_SUBFAMILY = "factory_product_subfamily";
    public static final String DEFAULT = "mail_address";


    public static String getRecipientNameByType(String type) {
        return switch (type) {
            case RecipientType.BU -> "BU";
            case RecipientType.CLUSTER -> "Cluster";
            case RecipientType.ENTITY -> "Entity";
            case RecipientType.FACTORY_PRODUCT_FAMILY -> "Factory Product Family";
            case RecipientType.FACTORY_PRODUCT_SUBFAMILY -> "Factory Product SubFamily";
            case RecipientType.GSC_CHINA -> "GSC China";
            case RecipientType.LOCAL_PRODUCT_FAMILY -> "Local Product Family";
            case RecipientType.LOCAL_PRODUCT_LINE -> "Local Product Line";
            case RecipientType.LOCAL_PRODUCT_SUBFAMILY -> "Local Product SubFamily";
            case RecipientType.MATERIAL -> "Material";
            case RecipientType.MRPCN -> "MRPCN";
            case RecipientType.PLANT_CODE -> "Plant Code";
            case RecipientType.PRODUCT_LINE -> "Product Line";
            case RecipientType.PRODUCTION_LINE -> "Production Line";
            case RecipientType.VENDOR_CODE -> "Vendor Code";
            default -> type;
        };
    }
}
