package com.adm.system.bean;

import java.util.List;

public class SelectGroupOptions {

    private String label;
    private String value;
    private String ds;
    private int order;
    private String sortColumn;
    private String sortOrder;
    private String uploadEnable;
    private String uploadModule;
    private List<SelectGroupOptions> options;

    public int getOrder() {
        return order;
    }

    public void setOrder(int order) {
        this.order = order;
    }

    public String getDs() {
        return ds;
    }

    public void setDs(String ds) {
        this.ds = ds;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getSortColumn() {
        return sortColumn;
    }

    public void setSortColumn(String sortColumn) {
        this.sortColumn = sortColumn;
    }

    public String getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(String sortOrder) {
        this.sortOrder = sortOrder;
    }

    public List<SelectGroupOptions> getOptions() {
        return options;
    }

    public void setOptions(List<SelectGroupOptions> options) {
        this.options = options;
    }

    public String getUploadEnable() {
        return uploadEnable;
    }

    public void setUploadEnable(String uploadEnable) {
        this.uploadEnable = uploadEnable;
    }

    public String getUploadModule() {
        return uploadModule;
    }

    public void setUploadModule(String uploadModule) {
        this.uploadModule = uploadModule;
    }
}
