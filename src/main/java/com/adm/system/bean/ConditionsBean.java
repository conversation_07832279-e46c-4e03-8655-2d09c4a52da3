package com.adm.system.bean;

import com.alibaba.fastjson.JSONArray;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 接收前台传过来的条件,并转换为单个条件
 * [{
 * "column": "STORAGE_LOCATION",
 * "conditions": [{
 * "args": ["3pl"],
 * "name": "eq"
 * },
 * {
 * "args": [""],
 * "name": "neq"
 * }],
 * "operation": "conjunction"
 * },
 * {
 * "column": "FISCAL_YEAR",
 * "conditions": [{
 * "args": [],
 * "name": "empty"
 * },
 * {
 * "args": ["1"],
 * "name": "eq"
 * }],
 * {
 * "args": [[]],
 * "name": "by_value"
 * },
 * "operation": "conjunction"
 * }]
 */
public class ConditionsBean {
    private List<ConditionBean> conditionList = new ArrayList<>();
    private final Map<String, Object> parameter = new HashMap<>();

    public ConditionsBean(Map<String, Object> parameterMap) {
        if (parameterMap.containsKey("_conditions")) {
            Object conditionsObj = parameterMap.remove("_conditions");

            JSONArray conditionArray = (JSONArray) conditionsObj;
            JSONArray colOrgHeaders = (JSONArray) parameterMap.get("_colOrgHeaders");
            JSONArray colOrgTypes = (JSONArray) parameterMap.get("_colOrgTypes");
            JSONArray colOrgFormats = (JSONArray) parameterMap.get("_colOrgFormats");


            Map<String, String> colDefMap = new HashMap<>();
            Map<String, String> colFormatMap = new HashMap<>();

            if (colOrgHeaders != null && colOrgHeaders.size() > 0) {
                for (int i = 0; i < colOrgHeaders.size(); i++) {
                    colDefMap.put(StringUtils.upperCase(colOrgHeaders.getString(i)), colOrgTypes.getString(i));
                    colFormatMap.put(StringUtils.upperCase(colOrgHeaders.getString(i)), colOrgFormats.getString(i));
                }
            }

            if (conditionArray != null && conditionArray.size() > 0) {
                conditionList = conditionArray.toJavaList(ConditionBean.class);
                for (ConditionBean conditionBean : conditionList) {
                    conditionBean.setParameter(parameter);
                    conditionBean.setColDefMap(colDefMap);
                    conditionBean.setFormatMap(colFormatMap);
                }
            }
        }
    }

    public String getSql() {
        if (conditionList.isEmpty()) {
            return "";
        }
        return "(" + StringUtils.join(conditionList, " AND ") + ")";
    }

    public Map<String, Object> getParameter() {
        return parameter;
    }
}
