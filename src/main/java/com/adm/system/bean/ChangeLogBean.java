package com.adm.system.bean;

import java.util.ArrayList;
import java.util.List;

public class ChangeLogBean {
    private String version;
    private String submit;
    private String summary;
    private List<ChangeLogDetailsBean> logs = new ArrayList<>();

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getSubmit() {
        return submit;
    }

    public void setSubmit(String submit) {
        this.submit = submit;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public List<ChangeLogDetailsBean> getLogs() {
        return logs;
    }

    public void setLogs(List<ChangeLogDetailsBean> logs) {
        this.logs = logs;
    }
}
