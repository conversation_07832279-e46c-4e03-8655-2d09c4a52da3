package com.adm.system.bean;

import com.alibaba.fastjson.JSON;

import java.util.List;

public class DatagridDropdown {
    private String type;
    private List<String> source;

    public DatagridDropdown(String type, String content) {
        this.type = type;
        this.source = JSON.parseArray(content).toJavaList(String.class);
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<String> getSource() {
        return source;
    }

    public void setSource(List<String> source) {
        this.source = source;
    }
}
