package com.adm.system.bean;

import java.util.HashMap;
import java.util.Map;

public class DataSyncNode {
    private String name;
    private Map<String, String> itemStyle = new HashMap<>();

    public DataSyncNode(Map<String, Object> data) {
        String color = "#1f77b4";
        String borderColor = "#1f77b4";
        this.name = (String) data.get("NAME");
        String status = (String) data.get("STATUS");

        switch (status) {
            case "not_start" -> {
                color = "#b8b8b8";
                borderColor = "#b8b8b8";
            }
            case "failed" -> {
                color = "#F83A05";
                borderColor = "#F83A05";
            }
            case "running" -> {
                color = "#3DCD58";
                borderColor = "#3DCD58";
            }
            case "success" -> {
                color = "#00BCF2";
                borderColor = "#00BCF2";
            }
        }
        itemStyle.put("color", color);
        itemStyle.put("borderColor", borderColor);
        itemStyle.put("borderColorOrg", borderColor);
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Map<String, String> getItemStyle() {
        return itemStyle;
    }

    public void setItemStyle(Map<String, String> itemStyle) {
        this.itemStyle = itemStyle;
    }
}
