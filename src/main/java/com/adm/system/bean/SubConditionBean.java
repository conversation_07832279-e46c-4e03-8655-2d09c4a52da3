package com.adm.system.bean;

import com.alibaba.fastjson.JSON;
import com.starter.utils.Utils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

public class SubConditionBean {
    private String[] args;
    private String name;
    private String column;
    Map<String, String> colDefMap = new HashMap<>();
    Map<String, String> colFormatMap = new HashMap<>();
    private Map<String, Object> parameter = new HashMap<>();

    public Map<String, String> getColDefMap() {
        return colDefMap;
    }

    public void setColDefMap(Map<String, String> colDefMap) {
        this.colDefMap = colDefMap;
    }

    public String[] getArgs() {
        return args;
    }

    public String getColumn() {
        return column;
    }

    public void setColumn(String column) {
        this.column = column;
    }

    public void setArgs(String[] args) {
        this.args = args;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Map<String, Object> getParameter() {
        return parameter;
    }

    public void setParameter(Map<String, Object> parameter) {
        this.parameter = parameter;
    }

    private String generateKey() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    public String parseColType(String key, String type, String format) {
        if ("date".equalsIgnoreCase(type)) {
            return "to_date(#{" + key + ", jdbcType=VARCHAR},'" + format + "')";
        } else if ("numeric".equalsIgnoreCase(type) || "number".equalsIgnoreCase(type)) {
            return "to_number(#{" + key + ", jdbcType=DOUBLE})";
        } else {
            return "#{" + key + ", jdbcType=VARCHAR}";
        }
    }

    public String parseColName(String column, String type) {
        if ("text".equalsIgnoreCase(type) || type == null || "".equals(type) || "VARCHAR".equals(type)) {
            return "lower(\"" + column + "\")";
        } else {
            return "\"" + column + "\"";
        }
    }

    public String toString() {
        boolean hasArgs = true;
        if ("empty".equalsIgnoreCase(name) == true || "not_empty".equalsIgnoreCase(name) == true || "date_tomorrow".equalsIgnoreCase(name) == true || "date_today".equalsIgnoreCase(name) == true || "date_yesterday".equalsIgnoreCase(name) == true) {
            hasArgs = false;
        }
        if (hasArgs && (args.length == 0 || "[]".equals(args[0]))) {
            return " 1 = 1";
        }
        String value = hasArgs ? args[0] : "";
        if (Utils.hasInjectionAttack(value)) {
            return " 0 = 1 ";
        }
        String key = this.generateKey();
        String colType = this.colDefMap.get(StringUtils.upperCase(column));
        String colFormat = this.colFormatMap.get(StringUtils.upperCase(column));
        parameter.put(key, value);
        switch (name.toLowerCase()) {
            case "eq":
                return this.parseColName(column, colType) + " = " + this.parseColType("_page.conditions.parameter." + key, colType, colFormat);
            case "neq":
                return this.parseColName(column, colType) + " != " + this.parseColType("_page.conditions.parameter." + key, colType, colFormat);
            case "empty":
                return "\"" + column + "\" IS NULL";
            case "not_empty":
                return "\"" + column + "\" IS NOT NULL";
            case "by_value":
                if (value.length() > 0) {
                    // 将多个值按照空和非空拆开来, 空的用is null来处理, 非空的用in处理
                    List<String> valueList = new ArrayList<>();

                    boolean hasNull = false;
                    if (StringUtils.startsWith(value, "[")) {
                        List<String> list = JSON.parseArray(value).toJavaList(String.class);
                        for (String s : list) {
                            String s0 = this.removeQuotation(s);
                            if (StringUtils.isBlank(s0)) {
                                hasNull = true;
                                continue;
                            }
                            valueList.add(s0);
                        }
                    } else {
                        for (String s : value.split(",")) {
                            String s0 = this.removeQuotation(s);
                            if (StringUtils.isBlank(s0)) {
                                hasNull = true;
                                continue;
                            }
                            valueList.add(s0);
                        }
                    }


                    StringBuilder valueSql = new StringBuilder();
                    if (valueList.isEmpty() == false) {
                        valueSql.append("\"").append(column).append("\" IN (");

                        // 0~N-1
                        for (int i = 0; i < valueList.size() - 1; i++) {
                            String k = this.generateKey();
                            parameter.put(k, valueList.get(i));
                            valueSql.append(this.parseColType("_page.conditions.parameter." + k, colType, colFormat)).append(",");
                        }

                        // N
                        String k = this.generateKey();
                        parameter.put(k, valueList.get(valueList.size() - 1));
                        valueSql.append(this.parseColType("_page.conditions.parameter." + k, colType, colFormat));
                        valueSql.append(")");
                    }

                    String nullSql = hasNull ? "\"" + column + "\" is null" : null;

                    if (StringUtils.isNotBlank(nullSql) && StringUtils.isNotBlank(valueSql.toString())) {
                        return valueSql.toString() + " OR " + nullSql;
                    } else {
                        return StringUtils.isNotBlank(nullSql) ? nullSql : valueSql.toString();
                    }
                } else {
                    return " 1 = 0 ";
                }

            case "contains":
                return this.parseColName(column, colType) + " LIKE '%' || " + this.parseColType("_page.conditions.parameter." + key, colType, colFormat) + " || '%'";
            case "not_contains":
                return this.parseColName(column, colType) + " NOT LIKE '%' || " + this.parseColType("_page.conditions.parameter." + key, colType, colFormat) + " || '%'";
            case "begins_with":
                return this.parseColName(column, colType) + " LIKE " + this.parseColType("_page.conditions.parameter." + key, colType, colFormat) + " || '%'";
            case "ends_with":
                return this.parseColName(column, colType) + " LIKE '%' || " + this.parseColType("_page.conditions.parameter." + key, colType, colFormat);
            case "gt":
                if (Utils.isStrictNumeric(value) == false) {
                    return " 0 = 1 ";
                }
                return "\"" + column + "\" > to_number(#{_page.conditions.parameter." + key + ",jdbcType=DOUBLE})";
            case "gte":
                if (Utils.isStrictNumeric(value) == false) {
                    return " 0 = 1 ";
                }
                return "\"" + column + "\" >= to_number(#{_page.conditions.parameter." + key + ",jdbcType=DOUBLE})";
            case "lt":
                if (Utils.isStrictNumeric(value) == false) {
                    return " 0 = 1 ";
                }
                return "\"" + column + "\" < to_number(#{_page.conditions.parameter." + key + ",jdbcType=DOUBLE})";
            case "lte":
                if (Utils.isStrictNumeric(value) == false) {
                    return " 0 = 1 ";
                }
                return "\"" + column + "\" <= to_number(#{_page.conditions.parameter." + key + ",jdbcType=DOUBLE})";
            case "between":
                if (args.length != 2) {
                    return " 0 = 1 ";
                }

                String min = String.valueOf(args[0]);
                String max = String.valueOf(args[1]);

                if (Utils.hasInjectionAttack(min)) {
                    return " 0 = 1 ";
                }

                if (Utils.hasInjectionAttack(max)) {
                    return " 0 = 1 ";
                }

                parameter.put("min_" + key, min);
                parameter.put("max_" + key, max);

                if ("NUMBER".equalsIgnoreCase(colType)) {
                    return "\"" + column + "\" BETWEEN to_number(#{_page.conditions.parameter.min_" + key + ",jdbcType=DOUBLE}) AND " + "to_number(#{_page.conditions.parameter.max_" + key + ",jdbcType=DOUBLE})";
                } else {
                    return "\"" + column + "\" BETWEEN to_date(#{_page.conditions.parameter.min_" + key + ",jdbcType=VARCHAR},'" + colFormat + "') AND " + "to_date(#{_page.conditions.parameter.max_" + key + ",jdbcType=VARCHAR},'" + colFormat + "')";
                }

            case "not_between":
                if (args.length != 2) {
                    return " 0 = 1 ";
                }

                min = String.valueOf(args[0]);
                max = String.valueOf(args[1]);

                if (Utils.hasInjectionAttack(min) || Utils.isStrictNumeric(min) == false) {
                    return " 0 = 1 ";
                }

                if (Utils.hasInjectionAttack(max) || Utils.isStrictNumeric(max) == false) {
                    return " 0 = 1 ";
                }

                parameter.put("min_" + key, min);
                parameter.put("max_" + key, max);

                return "\"" + column + "\" NOT BETWEEN to_number(#{_page.conditions.parameter.min_" + key + ",jdbcType=DOUBLE}) AND " + "to_number(#{_page.conditions.parameter.max_" + key + ",jdbcType=DOUBLE})";
            case "date_before":
                return "\"" + column + "\" <= to_date(#{_page.conditions.parameter." + key + ",jdbcType=VARCHAR},'" + colFormat + "')";
            case "date_after":
                return "\"" + column + "\" >= to_date(#{_page.conditions.parameter." + key + ",jdbcType=VARCHAR},'" + colFormat + "')";
            case "date_tomorrow":
                return "\"" + column + "\" = trunc(sysdate + 1)";
            case "date_today":
                return "\"" + column + "\" = trunc(sysdate)";
            case "date_yesterday":
                return "\"" + column + "\" = trunc(sysdate - 1)";

        }
        return " 1 = 1 ";
    }

    private String removeQuotation(String s) {
        return StringUtils.removeEnd(StringUtils.removeStart(s, "\""), "\"");
    }

    public void setColFormatMap(Map<String, String> colFormatMap) {
        this.colFormatMap = colFormatMap;
    }
}
