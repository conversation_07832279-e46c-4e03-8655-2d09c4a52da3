package com.adm.system.bean;

import com.starter.utils.Utils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;

public class DataSyncJobInfo {
    private String row_id;
    private String name;
    private String enable;
    private String type;
    private String status;
    private String start_date;
    private String end_date;
    private String seconds;
    private String note;
    private String title;

    public DataSyncJobInfo() {

    }

    public DataSyncJobInfo(Map<String, Object> data) {
        this.row_id = (String) data.get("ROW_ID");
        this.name = (String) data.get("NAME");
        this.enable = (String) data.get("ENABLE");
        this.type = (String) data.get("TYPE_NAME");
        this.status = (String) data.get("STATUS");
        this.start_date = (String) data.get("START_DATE");
        this.end_date = (String) data.get("END_DATE");
        this.seconds = (String) data.get("SECONDS");
        this.note = (String) data.get("NOTE");

        this.title = "Not Start";
        if (StringUtils.isNotBlank(this.start_date)) {
            title = "Start: " + this.start_date;
        }
        if (StringUtils.isNotBlank(this.end_date)) {
            title += "\nEnd: " + this.end_date;
        }
        if (StringUtils.isNotBlank(this.seconds)) {
            if (Utils.isStrictNumeric(this.seconds)) {
                int s = new BigDecimal(this.seconds).intValue();
                if (s == 0) {
                    title += "\nTimecost: 0";
                } else if (s > 3600) {
                    String s0 = BigDecimal.valueOf(s / 3600.0).setScale(1, RoundingMode.HALF_UP).toPlainString();
                    if (s0.endsWith(".0")) {
                        s0 = StringUtils.removeEnd(s0, ".0");
                    }
                    title += "\nTimecost: " + Utils.thousandBitSeparator(s0) + "h";
                } else if (s > 60) {
                    String s0 = BigDecimal.valueOf(s / 60.0).setScale(1, RoundingMode.HALF_UP).toPlainString();
                    if (s0.endsWith(".0")) {
                        s0 = StringUtils.removeEnd(s0, ".0");
                    }
                    title += "\nTimecost: " + Utils.thousandBitSeparator(s0) + "min";
                } else {
                    title += "\nTimecost: " + Utils.thousandBitSeparator(String.valueOf(s)) + "s";
                }
            } else {
                title += "\nTimecost: " + Utils.thousandBitSeparator(this.seconds) + "s";
            }
        }
        if (StringUtils.isNotBlank(this.note)) {
            title += "\nNote: " + this.note;
        }
    }

    public String getStart_date() {
        return start_date;
    }

    public void setStart_date(String start_date) {
        this.start_date = start_date;
    }

    public String getEnd_date() {
        return end_date;
    }

    public void setEnd_date(String end_date) {
        this.end_date = end_date;
    }

    public String getSeconds() {
        return seconds;
    }

    public void setSeconds(String seconds) {
        this.seconds = seconds;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getStatus() {
        return status;
    }

    public String getRow_id() {
        return row_id;
    }

    public void setRow_id(String row_id) {
        this.row_id = row_id;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEnable() {
        return enable;
    }

    public void setEnable(String enable) {
        this.enable = enable;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
