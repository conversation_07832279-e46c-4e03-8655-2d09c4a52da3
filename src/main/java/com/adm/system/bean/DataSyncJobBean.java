package com.adm.system.bean;

import java.util.ArrayList;
import java.util.List;

public class DataSyncJobBean {
    private String name;
    private String group;
    private List<DataSyncJobBean> children = new ArrayList<>();

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public List<DataSyncJobBean> getChildren() {
        return children;
    }

    public void setChildren(List<DataSyncJobBean> children) {
        this.children = children;
    }


    public void addChild(DataSyncJobBean bean) {
        children.add(bean);
    }
}
