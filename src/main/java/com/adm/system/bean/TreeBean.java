package com.adm.system.bean;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TreeBean {
    private String name;
    private String value = "";

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<TreeBean> children = new ArrayList<>();

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void addChildByName(String name, Map<String, Object> tableCommentsMap) {
        TreeBean b = new TreeBean();
        b.setName(name);
        String value = (String) tableCommentsMap.get(name);
        b.setValue(value);

        this.children.add(b);
    }

    public List<TreeBean> getChildren() {
        return children;
    }

    public void setChildren(List<TreeBean> children) {
        this.children = children;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    // 递归为树级结构赋值
    Map<String, Object> existsNode = new HashMap<>();

    public void renderReference(List<TreeBean> children, Map<String, List<String>> referenceMap, Map<String, Object> tableCommentsMap) {
        for (TreeBean tree : children) {
            List<String> list = referenceMap.get(tree.getName());
            if (list != null && list.isEmpty() == false) {
                for (String name : list) {
                    if (existsNode.containsKey(name) == false) {
                        existsNode.put(name, null);
                        tree.addChildByName(name, tableCommentsMap);
                    }
                }
            }
            this.renderReference(tree.getChildren(), referenceMap, tableCommentsMap);
        }
    }
}
