package com.adm.system.bean;

import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.TimeZone;

public class GitlabCommits {
    private String short_id;
    private String title;
    private String author_name;
    private String authored_date;

    public String getShort_id() {
        return short_id;
    }

    public void setShort_id(String short_id) {
        this.short_id = short_id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getAuthor_name() {
        return author_name;
    }

    public void setAuthor_name(String author_name) {
        this.author_name = author_name;
    }

    public String getAuthored_date() {
        return authored_date;
    }

    public void setAuthored_date(String authored_date) {
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'.000Z'");
            format.setTimeZone(TimeZone.getTimeZone("Etc/GMT+0"));

            this.authored_date = new SimpleDateFormat("MM/dd HH:mm").format(format.parse(authored_date));
        } catch (Exception e) {
            this.authored_date = StringUtils.removeEnd(authored_date, ".000Z").replace("-", "/").replace("T", " ").substring(5, 16);
        }
    }
}
