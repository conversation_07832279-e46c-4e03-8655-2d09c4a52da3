package com.adm.system.bean;

import java.util.ArrayList;
import java.util.List;

public class CascaderBean {
    private String value;
    private String label;
    private List<CascaderBean> children = null;

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public List<CascaderBean> getChildren() {
        return children;
    }

    public void setChildren(List<CascaderBean> children) {
        this.children = children;
    }

    public void addChild(CascaderBean subBean) {
        if (children == null) {
            children = new ArrayList<>();
        }
        children.add(subBean);
    }
}
