package com.adm.system.bean;

import java.util.ArrayList;
import java.util.List;

public class TeamsMessageAttachmentContent {
    private String type = "AdaptiveCard";
    private String version = "1.4";
    private List<TeamsMessageAttachmentContentBody> body = new ArrayList<>();

    public void addBody(TeamsMessageAttachmentContentBody b) {
        body.add(b);
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public List<TeamsMessageAttachmentContentBody> getBody() {
        return body;
    }

    public void setBody(List<TeamsMessageAttachmentContentBody> body) {
        this.body = body;
    }
}
