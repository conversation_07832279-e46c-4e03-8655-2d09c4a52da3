package com.adm.system;

import com.adm.system.service.IMaterialRiskService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin
@SchneiderRequestMapping(value = "/system/material_risk", parent = "sub-menu33")
@Scope("prototype")
public class MaterialRiskController extends ControllerHelper {

    @Resource
    private IMaterialRiskService materialRiskService;

    @SchneiderRequestMapping(value = "/query_suggestion_by_keywords")
    public Response querySuggestionByKeywords(HttpServletRequest request) {
        super.pageLoad(request);
        return materialRiskService.querySuggestionByKeywords(parameterMap);
    }

    @SchneiderRequestMapping(value = "/search_rules")
    public Response searchRules(HttpServletRequest request) {
        super.pageLoad(request);
        return materialRiskService.searchRules(parameterMap);
    }

    @SchneiderRequestMapping(value = "/save_rule")
    public Response saveRule(HttpServletRequest request) {
        super.pageLoad(request);
        return materialRiskService.saveRule(parameterMap);
    }

    @SchneiderRequestMapping(value = "/delete_rule")
    public Response deleteRule(HttpServletRequest request) {
        super.pageLoad(request);
        return materialRiskService.deleteRule(parameterMap);
    }

    @SchneiderRequestMapping(value = "/modify_rule_status")
    public Response modifyRuleStatus(HttpServletRequest request) {
        super.pageLoad(request);
        return materialRiskService.modifyRuleStatus(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_rule_by_id")
    public Response queryRuleById(HttpServletRequest request) {
        super.pageLoad(request);
        return materialRiskService.queryRuleById(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_material_list_by_rule")
    public Response queryMaterialListByRule(HttpServletRequest request) {
        super.pageLoad(request);
        return materialRiskService.queryMaterialListByRule(parameterMap);
    }

    @SchneiderRequestMapping(value = "/download_material_list_by_rule")
    public void downloadMaterialListByRule(HttpServletRequest request, HttpServletResponse response) {
        this.pageLoad(request);
        materialRiskService.downloadMaterialListByRule(parameterMap, response);
    }

    @SchneiderRequestMapping(value = "/recreate_material_risk_level_view")
    public void recreateMaterialRiskLevelView() {
        materialRiskService.recreateMaterialRiskLevelView();
    }

    @SchneiderRequestMapping(value = "/recreate_material_risk_level_view_scheduled")
    public Response queryAllAvailableComponentsScheduled(HttpServletRequest request) {
        super.pageLoad(request);
        return materialRiskService.queryAllAvailableComponentsScheduled(parameterMap);
    }
}
