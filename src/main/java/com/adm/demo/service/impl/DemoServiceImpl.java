package com.adm.demo.service.impl;

import com.adm.demo.dao.IDemoDao;
import com.adm.demo.service.IDemoService;
import com.starter.context.bean.Response;
import com.starter.utils.Utils;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

@Service
@Scope("prototype")
@Transactional
public class DemoServiceImpl implements IDemoService {

    @Resource
    private IDemoDao demoDao;

    @Resource
    private Response response;

    @Override
    public Response initPage(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cascader", Utils.parseCascader(demoDao.queryCascader()));
        return response.setBody(resultMap);
    }
}
