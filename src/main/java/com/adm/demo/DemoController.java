package com.adm.demo;

import com.adm.demo.service.IDemoService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;

@RestController
@Scope("prototype")
@SchneiderRequestMapping(value = "/system/demo")
public class DemoController extends ControllerHelper {

    @Resource
    private IDemoService demoService;

    @SchneiderRequestMapping("/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        return demoService.initPage(parameterMap);
    }
}
