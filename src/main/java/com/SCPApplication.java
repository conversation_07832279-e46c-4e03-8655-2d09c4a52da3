package com;

import org.apache.catalina.connector.Connector;
import org.apache.coyote.http11.Http11NioProtocol;
import org.apache.ibatis.annotations.Mapper;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.servlet.server.ServletWebServerFactory;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;

@SpringBootApplication
@EnableFeignClients
@MapperScan(basePackages = "com", annotationClass = Mapper.class)
public class SCPApplication {

    @Value("${http.port}")
    private Integer port;

    @Value("${server.compression.mime-types}")
    private String mimeTypes;

    @Value("${server.compression.min-response-size}")
    private int minResponseSize;

    @Value("${server.compression.enabled}")
    private boolean enable;

    @Bean
    public ServletWebServerFactory servletContainer() {
        TomcatServletWebServerFactory tomcat = new TomcatServletWebServerFactory();
        tomcat.addAdditionalTomcatConnectors(createStandardConnector()); // 添加http 8081
        return tomcat;
    }

    private Connector createStandardConnector() {
        Connector connector = new Connector("org.apache.coyote.http11.Http11NioProtocol");
        Http11NioProtocol protocol = (Http11NioProtocol) connector.getProtocolHandler();
        protocol.setCompressibleMimeType(mimeTypes);
        if (enable) {
            protocol.setCompression("on");
        } else {
            protocol.setCompression("off");
        }
        protocol.setCompressionMinSize(minResponseSize);
        connector.setPort(port);
        return connector;
    }

    public static void main(String[] args) {
        SpringApplication.run(SCPApplication.class, args);
    }
}
