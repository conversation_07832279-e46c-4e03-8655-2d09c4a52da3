package com.starter.login.bean;

public class PingSSORequest {
    private String grant_type = "authorization_code";
    private String response_type = "code";
    private String client_id = "GSC China SCP Decision Support System_D2754";
    private String client_secret = "MuUAgQ7tBu9QxavJG1c3BAaEFXpPE0PuaWXKpFCpYJJUr9gUwqOwmRWJnLSMQ7N6";
    private String redirect_uri = "https://scp-dss.cn.schneider-electric.com:8443/pingsso/callback";
    private String code;
    private String code_verifier;

    public String getGrant_type() {
        return grant_type;
    }

    public void setGrant_type(String grant_type) {
        this.grant_type = grant_type;
    }

    public String getResponse_type() {
        return response_type;
    }

    public void setResponse_type(String response_type) {
        this.response_type = response_type;
    }

    public String getClient_id() {
        return client_id;
    }

    public void setClient_id(String client_id) {
        this.client_id = client_id;
    }

    public String getClient_secret() {
        return client_secret;
    }

    public void setClient_secret(String client_secret) {
        this.client_secret = client_secret;
    }

    public String getRedirect_uri() {
        return redirect_uri;
    }

    public void setRedirect_uri(String redirect_uri) {
        this.redirect_uri = redirect_uri;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getCode_verifier() {
        return code_verifier;
    }

    public void setCode_verifier(String code_verifier) {
        this.code_verifier = code_verifier;
    }
}
