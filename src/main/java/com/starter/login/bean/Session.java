package com.starter.login.bean;

import java.io.Serializable;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonInclude;
import org.apache.commons.lang3.StringUtils;

public class Session implements Serializable {
    private String userid;
    private String username;
    private String email;
    private String entity;

    private String maintainer;
    private String supervisor;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String loginIP;

    private String loginIP2;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Map<String, Map<String, Object>> authMenu;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Map<String, Object> authMethod;

    public String getEntity() {
        return entity;
    }

    public void setEntity(String entity) {
        this.entity = entity;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getLoginIP() {
        return loginIP;
    }

    public void setLoginIP(String loginIP) {
        this.loginIP = loginIP;
    }

    public String getLoginIP2() {
        return loginIP2;
    }

    public void setLoginIP2(String loginIP2) {
        this.loginIP2 = loginIP2;
    }

    public Map<String, Map<String, Object>> getAuthMenu() {
        return authMenu;
    }

    public void setAuthMenu(Map<String, Map<String, Object>> authMenu) {
        this.authMenu = authMenu;
    }

    public Map<String, Object> getAuthMethod() {
        return authMethod;
    }

    public void setAuthMethod(Map<String, Object> authMethod) {
        this.authMethod = authMethod;
    }

    public String getMaintainer() {
        return maintainer;
    }

    public void setMaintainer(String maintainer) {
        if (StringUtils.isNotBlank(maintainer)) {
            this.maintainer = maintainer;
        } else {
            this.maintainer = "N";
        }
    }

    public String getSupervisor() {
        return supervisor;
    }

    public void setSupervisor(String supervisor) {
        if (StringUtils.isNotBlank(supervisor)) {
            this.supervisor = supervisor;
        } else {
            this.supervisor = "N";
        }
    }
}
