package com.starter.login.bean;

import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class Menu {
    @JsonIgnore
    private String menuCode;
    private String name;
    private String ico;
    private String url;
    private String parentId;
    private String parentName;
    private String defaultUrl;
    private List<Menu> children = new ArrayList<>();

    public Menu() {

    }

    public Menu(Map<String, Object> beanMap) {
        this.setMenuCode((String) beanMap.get("MENU_CODE"));
        this.setUrl((String) beanMap.get("URL"));
        this.setName((String) beanMap.get("NAME"));
        this.setIco((String) beanMap.get("ICO_CLS"));
        this.setParentId((String) beanMap.get("PARENT_ID"));
        this.setParentName((String) beanMap.get("PARENT_NAME"));
    }

    public String getMenuCode() {
        return menuCode;
    }

    public void setMenuCode(String menuCode) {
        this.menuCode = menuCode;
    }

    public void addChildren(Menu menu) {
        children.add(menu);
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIco() {
        return ico;
    }

    public void setIco(String ico) {
        this.ico = ico;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public List<Menu> getChildren() {
        return children;
    }

    public void setChildren(List<Menu> children) {
        this.children = children;
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public String getDefaultUrl() {
        if (StringUtils.isNotBlank(defaultUrl)) {
            return defaultUrl;
        } else {
            if (children.isEmpty() == false) {
                return children.get(0).getUrl();
            } else {
                return null;
            }
        }
    }

    public void setDefaultUrl(String defaultUrl) {
        this.defaultUrl = defaultUrl;
    }
}
