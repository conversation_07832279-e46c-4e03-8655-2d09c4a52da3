package com.starter.login.dao;

import java.util.List;
import java.util.Map;

import com.starter.login.bean.Session;
import com.starter.context.bean.AuthAnnotation;
import com.starter.login.bean.Menu;
import org.apache.ibatis.annotations.Mapper;

import org.apache.ibatis.annotations.Param;

@Mapper
public interface ILoginDao {
    Session login(@Param("username") String username, @Param("password") String password);

    Session loginWithoutPWD(@Param("username") String username);

    List<Map<String, Object>> queryAuthedMenu(String userid);

    String queryUserPassword(Map<String, Object> parameterMap);

    void updateUserPassword(Map<String, Object> parameterMap);

    void saveMethodAuth(List<AuthAnnotation> authAnnotationList);

    List<Map<String, Object>> queryMenuList(String userid);

    void saveLoginLogs(@Param("username") String username, @Param("ip") String ip);

    void updateUserPasswordMD5(@Param("username") String username, @Param("password") String password);

    void saveVisitLogs(@Param("url") String url, @Param("ip") String ip, @Param("username") String username, @Param("responseCode") String responseCode);

    List<Map<String, Object>> querySystemNotification();

    List<Menu> queryScpHotMenus(String userid);

    void sendToHomepage(Map<String, Object> parameterMap);

    String queryUserHomepage(String userid);

    List<Map<String, String>> queryAvailableEntity();

    Map<String, String> queryUserInfo(String userid);

    int queryExistAccount(Map<String, Object> parameterMap);

    void createNewAccount(Map<String, Object> parameterMap);

    List<Map<String, String>> queryAllMenuAndUrl();

    List<Map<String, String>> querySCPATabCols();
}
