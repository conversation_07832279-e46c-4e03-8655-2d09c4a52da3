package com.starter.login.service;

import java.util.Map;

import com.starter.login.bean.Session;
import com.starter.context.bean.Response;

public interface ILoginService {

    Response login(Map<String, Object> parameterMap, String... ips);

    Response logout(String token);

    Response checkRouteAuth(String token, String route, String requestIp);

    Response changePassword(Map<String, Object> parameterMap);

    Session queryLoginSession(String username, String password, String... ips);

    Response querySystemNotification();

    Response pingSSOLogin(Map<String, Object> parameterMap, String... ips);

    Response queryMenuList(String userid);

    Response sendToHomepage(Map<String, Object> parameterMap);

    Response queryAvailableEntity();

    Response queryUserInfo(String userid);

    Response createNewAccount(Map<String, Object> parameterMap);

    Response remoteLogin(Map<String, Object> parameterMap, String... ips);

    String getAccessToken(Map<String, Object> parameterMap, String... ips) throws IllegalAccessException;
}
