package com.starter.login.service.impl;

import com.auth0.jwt.JWT;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.Status;
import com.starter.context.mail.MailBean;
import com.starter.context.mail.MailFeignClient;
import com.starter.context.servlet.AuthInterceptor;
import com.starter.login.bean.*;
import com.starter.login.dao.ILoginDao;
import com.starter.login.feign.PingSSOFeignClient;
import com.starter.login.service.ILoginService;
import com.starter.utils.EncryptionUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
@Scope("prototype")
@Transactional
public class LoginServiceImpl implements ILoginService {

    @Resource
    private ILoginDao loginDao;

    @Resource
    private Response response;

    @Resource
    private PingSSOFeignClient pingSSOFeignClient;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private MailFeignClient mailFeignClient;

    private @Value("${session.timeout-in-minute}") long sessionTimeout;

    private final String GET_TOKEN_PWD = "2580921a-9125-4327-a165-6d02df90dd32";

    private final static Map<String, List<String>> MENU_CACHE_MAP = new HashMap<>();

    @Override
    public Response pingSSOLogin(Map<String, Object> parameterMap, String... ips) {
        PingSSORequest pingSSORequest = new PingSSORequest();
        pingSSORequest.setCode((String) parameterMap.get("code"));
        pingSSORequest.setCode_verifier((String) parameterMap.get("code_verifier"));
        Map<String, Object> resultMap = new HashMap<>();

        try {
            // logger.error("request=" + JSON.toJSONString(pingSSORequest));
            PingSSOResponse pingSSOResponse = pingSSOFeignClient.execute(pingSSORequest);
            // logger.error("response=" + JSON.toJSONString(pingSSORequest));

            //base64解析
            DecodedJWT jwt = JWT.decode(pingSSOResponse.getId_token());

            // logger.error("jwt=" + jwt.getSubject());

            //登录成功
            String userid = jwt.getSubject();
            Session session = this.queryLoginSessionWithoutPWD(userid, ips);//从数据库拿
            if (session == null) {
                resultMap.put("status", 403);
                resultMap.put("userid", userid);
            } else {
                resultMap.put("status", 200);
                resultMap.put("userid", userid);
                String token = UUID.randomUUID().toString();
                resultMap.put("token", token);
                resultMap.put("name", session.getUsername());

                String cacheKey = Configuration.CACHED_SESSION_KEY + token;
                redisTemplate.opsForValue().getAndSet(cacheKey, session);
                redisTemplate.expire(cacheKey, sessionTimeout, TimeUnit.MINUTES);
            }
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put("status", 500);
            resultMap.put("message", e.getMessage());
        }
        return response.setBody(resultMap);
    }

    @Override
    public Response remoteLogin(Map<String, Object> parameterMap, String... ips) {
        String username = (String) parameterMap.get("username");
        Map<String, Object> resultMap = new HashMap<>();
        Session session = this.queryLoginSessionWithoutPWD(username, ips);

        if (session == null) {
            resultMap.put("status", 403);
        } else {
            try {
                String url = (String) parameterMap.get("url");
                if (url.contains("?") == true) {
                    url += "&_watermark=" + username;
                } else {
                    url += "?_watermark=" + username;
                }
                resultMap.put("url", url);
            } catch (Exception e) {
                resultMap.put("status", 403);
                return response.setBody(resultMap);
            }

            resultMap.put("status", 200);
            String token = "rlogin-" + UUID.randomUUID();
            resultMap.put("token", token);
            resultMap.put("name", session.getUsername());

            String cacheKey = Configuration.CACHED_SESSION_KEY + token;
            redisTemplate.opsForValue().getAndSet(cacheKey, session);
            // 远程登录的时间很短, 可能就是为了查看某一个页面, 所以超时时间设置为5分钟
            redisTemplate.expire(cacheKey, 5, TimeUnit.MINUTES);
        }

        return response.setBody(resultMap);
    }

    @Override
    public String getAccessToken(Map<String, Object> parameterMap, String... ips) throws IllegalAccessException {
        String username = (String) parameterMap.get("username");
        String pwd = (String) parameterMap.get("pwd");

        if (GET_TOKEN_PWD.equals(pwd)) {
            Session session = this.queryLoginSessionWithoutPWD(username, ips);
            if (session == null) {
                throw new IllegalAccessException(username + " is a invalid account.");
            }
            String token = "slogin-" + UUID.randomUUID();
            String cacheKey = Configuration.CACHED_SESSION_KEY + token;
            redisTemplate.opsForValue().getAndSet(cacheKey, session);
            // 这种登录方式只允许5分钟的登录时间
            redisTemplate.expire(cacheKey, 5, TimeUnit.MINUTES);
            return token;
        } else {
            throw new IllegalAccessException("invalid pwd.");
        }
    }

    @Override
    public Response login(Map<String, Object> parameterMap, String... ips) {
        String username = (String) parameterMap.get("username");
        String password = (String) parameterMap.get("password");
        String passwordDecrypt = EncryptionUtil.decryptAES(password);

        Map<String, Object> resultMap = new HashMap<>();

        // 如果能解密成功, 说明用户输入的是MD5密码, 系统禁止使用MD5密码登录
        if (passwordDecrypt != null) {
            resultMap.put("status", 403);
            return response.setBody(resultMap);
        }

        String passwordMD5 = EncryptionUtil.encryptAES(password);
        Session session = this.queryLoginSession(username, passwordMD5, ips);

        if (session == null) {
            session = this.queryLoginSession(username, password, ips);

            if (session != null) {
                loginDao.updateUserPasswordMD5(username, passwordMD5);
            }
        }

        if (session == null) {
            resultMap.put("status", 403);
        } else {
            resultMap.put("status", 200);
            String token = UUID.randomUUID().toString();
            resultMap.put("token", token);
            resultMap.put("name", session.getUsername());

            String cacheKey = Configuration.CACHED_SESSION_KEY + token;
            redisTemplate.opsForValue().getAndSet(cacheKey, session);
            redisTemplate.expire(cacheKey, sessionTimeout, TimeUnit.MINUTES);
        }

        return response.setBody(resultMap);
    }

    @Override
    public Session queryLoginSession(String username, String password, String... ips) {
        Session session = loginDao.login(username, password);
        if (session == null) {
            return null;
        }
        session.setLoginIP(ips[0]);
        loginDao.saveLoginLogs(username, ips[0]);
        if (ips.length > 1) {
            session.setLoginIP2(ips[1]);
        }

        List<Map<String, Object>> authDetails = loginDao.queryAuthedMenu(session.getUserid());
        Map<String, Map<String, Object>> authMenu = new HashMap<>();
        Map<String, Object> authMethod = new HashMap<>();
        for (Map<String, Object> m : authDetails) {
            if (m == null || StringUtils.isEmpty((String) m.get("URL"))) {
                continue;
            }

            Map<String, Object> details = new HashMap<>();
            details.put("AUTH_DETAILS", m.get("AUTH_DETAILS"));
            details.put("NAME", m.get("NAME"));
            if (StringUtils.indexOfIgnoreCase((String) m.get("MENU_TYPE"), "menu") != -1) {
                authMenu.put((String) m.get("URL"), details);
            } else if (StringUtils.equalsIgnoreCase((String) m.get("MENU_TYPE"), "method")) {
                authMethod.put((String) m.get("URL"), details);
            }
        }
        session.setAuthMenu(authMenu);
        session.setAuthMethod(authMethod);
        return session;
    }

    private Session queryLoginSessionWithoutPWD(String username, String... ips) {
        Session session = loginDao.loginWithoutPWD(username);
        if (session == null) {
            return null;
        }
        session.setLoginIP(ips[0]);
        loginDao.saveLoginLogs(username, ips[0]);
        if (ips.length > 1) {
            session.setLoginIP2(ips[1]);
        }

        List<Map<String, Object>> authDetails = loginDao.queryAuthedMenu(session.getUserid());
        Map<String, Map<String, Object>> authMenu = new HashMap<>();
        Map<String, Object> authMethod = new HashMap<>();
        for (Map<String, Object> m : authDetails) {
            if (m == null || StringUtils.isEmpty((String) m.get("URL"))) {
                continue;
            }

            Map<String, Object> details = new HashMap<>();
            details.put("AUTH_DETAILS", m.get("AUTH_DETAILS"));
            details.put("NAME", m.get("NAME"));
            if (StringUtils.indexOfIgnoreCase((String) m.get("MENU_TYPE"), "menu") != -1) {
                authMenu.put((String) m.get("URL"), details);
            } else if (StringUtils.equalsIgnoreCase((String) m.get("MENU_TYPE"), "method")) {
                authMethod.put((String) m.get("URL"), details);
            }
        }
        session.setAuthMenu(authMenu);
        session.setAuthMethod(authMethod);
        return session;
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":5m")
    public Response querySystemNotification() {
        List<Map<String, Object>> list = loginDao.querySystemNotification();
        List<Notification> resultList = new ArrayList<>();

        int i = 0;
        for (Map<String, Object> map : list) {
            resultList.add(new Notification(i++, (String) map.get("MESSAGE"), (String) map.get("TYPE")));
        }

        return response.setBody(resultList);
    }

    @Override
    public Response logout(String token) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("status", 200);
        String cacheKey = Configuration.CACHED_SESSION_KEY + token;
        redisTemplate.delete(cacheKey);
        return response.setBody(resultMap);
    }


    /**
     * 检查用户是否有某个路由的权限
     * 1. session为null, 返回Status.TIME_OUT, 前台将跳转至/login
     * 2. session中未包含指定路由, 返回Status.FORBIDDEN, 前台将提示无权限信息
     * 3. session中包含指定路由, 返回Status.SUCCESS, 前台将正常跳转
     *
     * @param token token
     * @return Response
     */
    @Override
    public Response checkRouteAuth(String token, String route, String requestIp) {
        Map<String, Object> resultMap = new HashMap<>();

        // white list
        if (AuthInterceptor.SECURE_MENU_WHITE_LIST.contains(route) == true) {
            resultMap.put("status", Status.SUCCESS.getStatus());
            return response.setBody(resultMap);
        }

        // normal
        String cacheKey = Configuration.CACHED_SESSION_KEY + token;
        Object obj = redisTemplate.opsForValue().get(cacheKey);
        if (obj == null) {
            resultMap.put("status", Status.TIME_OUT.getStatus());
        } else {
            Session session = (Session) obj;

            if (StringUtils.equals(session.getLoginIP(), requestIp) == false) {
                if ("127.0.0.1".equals(requestIp) == false && "0:0:0:0:0:0:0:1".equals(requestIp) == false) {
                    resultMap.put("status", Status.TIME_OUT.getStatus());
                    return response.setBody(resultMap);
                }
            }

            if (route.contains("?")) {
                route = route.split("\\?")[0];
            }

            Map<String, Object> menu = session.getAuthMenu().get(route);

            if (MENU_CACHE_MAP.isEmpty()) {
                synchronized (this) {
                    List<Map<String, String>> menuData = loginDao.queryAllMenuAndUrl();
                    for (Map<String, String> map : menuData) {
                        List<String> list = MENU_CACHE_MAP.computeIfAbsent(map.get("URL"), key -> new ArrayList<>());
                        list.add(map.get("PARENT_NAME"));
                        list.add(map.get("MENU_NAME"));
                    }
                }
            }

            if (menu != null) {
                resultMap.put("status", Status.SUCCESS.getStatus());
                resultMap.put("menu", MENU_CACHE_MAP.get(route));
            } else {
                resultMap.put("status", Status.FORBIDDEN.getStatus());
            }
            // 保存访问日志
            if (StringUtils.endsWith(route, "/") == false) {
                loginDao.saveVisitLogs(route, session.getLoginIP(), session.getUserid(), String.valueOf(resultMap.get("status")));
            }
        }
        return response.setBody(resultMap);
    }

    /**
     * 根据每个人的权限获取可以看到的菜单列表
     *
     * @param userid userid
     * @return Response
     */
    @Override
    public Response queryMenuList(String userid) {
        List<Map<String, Object>> result = loginDao.queryMenuList(userid);

        Map<String, Menu> resultMap = new LinkedHashMap<>();

        for (Map<String, Object> map : result) {
            resultMap.put(String.valueOf(map.get("MENU_CODE")), new Menu(map));
        }

        List<Menu> resultList = new ArrayList<>();

        Iterator<Map.Entry<String, Menu>> iterator = resultMap.entrySet().iterator();

        Map.Entry<String, Menu> entry;
        while (iterator.hasNext()) {
            entry = iterator.next();
            Menu menuBean = entry.getValue();

            String parent_id = menuBean.getParentId();

            if ("-1".equals(parent_id)) {
                resultList.add(menuBean);
            } else {
                if (resultMap.get(menuBean.getParentId()) != null) {
                    resultMap.get(menuBean.getParentId()).addChildren(menuBean);
                }
            }
        }

        resultList.removeIf(menu -> menu.getChildren().isEmpty());

        Menu scpHotMenu = new Menu();
        scpHotMenu.setName("Hot");
        scpHotMenu.setChildren(loginDao.queryScpHotMenus(userid));

        for (Menu menu : resultList) {
            if (StringUtils.equals(menu.getMenuCode(), "SCP") == true) {
                menu.getChildren().add(0, scpHotMenu);
                break;
            }
        }

        Map<String, Object> menuMap = new HashMap<>();
        menuMap.put("menuList", resultList);
        String homeURL = loginDao.queryUserHomepage(userid);
        if (StringUtils.isBlank(homeURL)) {
            homeURL = "/demand/tracking";
        }
        menuMap.put("homeURL", homeURL);
        response.setBody(menuMap);
        return response;
    }

    @Override
    public Response sendToHomepage(Map<String, Object> parameterMap) {
        loginDao.sendToHomepage(parameterMap);
        return response;
    }

    @Override
    public Response queryAvailableEntity() {
        List<Map<String, String>> data = loginDao.queryAvailableEntity();
        LinkedHashMap<String, List<String>> dataMap = new LinkedHashMap<>();
        for (Map<String, String> map : data) {
            List<String> list = dataMap.computeIfAbsent(map.get("GROUP"), k -> new ArrayList<>());
            list.add(map.get("ENTITY_NAME"));
        }

        List<Map<String, Object>> resultList = new ArrayList<>();
        for (String key : dataMap.keySet()) {
            Map<String, Object> map = new HashMap<>();
            map.put("label", key);
            map.put("options", dataMap.get(key));
            resultList.add(map);
        }
        return response.setBody(resultList);
    }

    @Override
    public Response queryUserInfo(String userid) {
        Map<String, String> data = loginDao.queryUserInfo(userid);
        return response.setBody(data);
    }

    @Override
    public Response createNewAccount(Map<String, Object> parameterMap) {
        int count = loginDao.queryExistAccount(parameterMap);
        if (count > 0) {
            return response.setBody(StringUtils.upperCase(parameterMap.get("sesaNo") + "") + " already exists. Please login by PINGSSO. If it's' a newly registered account, please wait patiently for approval");
        }
        loginDao.createNewAccount(parameterMap);

        // send notice mail
        StringBuilder body = new StringBuilder();

        MailBean mailBean = new MailBean();
        mailBean.setSubject("【DSS Account Request】" + parameterMap.get("name") + "[" + parameterMap.get("sesaNo") + "]" + " raised a account registration");
        List<String> to = new ArrayList<>();
        to.add("<EMAIL>");
        mailBean.setTo(StringUtils.join(to, ","));

        List<String> cc = new ArrayList<>();
        cc.add("<EMAIL>");
        cc.add("<EMAIL>");
        cc.add("<EMAIL>");
        mailBean.setCc(StringUtils.join(cc, ","));

        body.append("<div style='font-size:10pt;font-family:DengXian;'>");
        body.append("<p>");
        body.append(parameterMap.get("name")).append("[").append(parameterMap.get("sesaNo")).append("]").append(" raised a account registration");
        body.append("</p>");
        body.append("<br/>");
        body.append("SESA No: ").append(parameterMap.get("sesaNo"));
        body.append("<br/>");
        body.append("Name: ").append(parameterMap.get("name"));
        body.append("<br/>");
        body.append("Email: ").append(parameterMap.get("email"));
        body.append("<br/>");
        body.append("Job Code: ").append(parameterMap.get("jobCode"));
        body.append("<br/>");
        body.append("Job Title: ").append(parameterMap.get("jobTitle"));
        body.append("<br/>");
        body.append("Line Manager: ").append(parameterMap.get("lineManagerSesaNo"));
        body.append("<br/>");
        body.append("Entity: ").append(parameterMap.get("entity"));
        body.append("<br/>");
        body.append("Backup: ").append(parameterMap.get("backupSesaNo"));
        body.append("<br/>");
        body.append("Registration Reason: ").append(parameterMap.get("reason"));
        body.append("<br/>");
        body.append("<br/>");


        body.append("Click <a href='https://scp-dss.cn.schneider-electric.com/#/login_and_redirect?redirect=/components/table_viewer?id=COMP_32&label=USER_MASTER_DATA'>");
        body.append("<b><i>");
        body.append("USER_MASTER_DATA");
        body.append("</i></b></a> to approval.");
        body.append("<br/>");
        body.append("<br/>");
        body.append("And");
        body.append("<br/>");
        body.append("<br/>");
        body.append("Click <a href='mailto:").append(parameterMap.get("email"));
        body.append("?subject=DSS Account Created&cc=<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>&body=Your DSS account has been created. Please login by PINGSSO");
        body.append("'><b><i>");
        body.append("Reply");
        body.append("</i></b></a> to notify user after registration completed.");

        String style = "<style>p{font-size: 10pt;font-family:DengXian;padding:0;margin:0} span{font-size: 10pt;font-family:DengXian;} div{font-size: 10pt;font-family:DengXian;}</style>";

        mailBean.setBody(style + body);
        mailFeignClient.sendAsync(mailBean);

        return response;
    }

    @Override
    public Response changePassword(Map<String, Object> parameterMap) {
        String oldPassword = loginDao.queryUserPassword(parameterMap);
        if (oldPassword == null || (oldPassword.equals(parameterMap.get("oldPassword").toString()) == false && Objects.equals(EncryptionUtil.encryptAES(parameterMap.get("oldPassword").toString()), oldPassword) == false)) {
            return response.setBody(-1);
        } else {
            loginDao.updateUserPassword(parameterMap);
            return response.setBody(0);
        }
    }
}
