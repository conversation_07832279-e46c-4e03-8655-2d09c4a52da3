package com.starter.login;

import com.starter.login.service.ILoginService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;

@RestController
@CrossOrigin
@Scope("prototype")
public class LoginController extends ControllerHelper {

    @Resource
    private ILoginService loginService;

    @Resource
    private Response res;

    /*
     * 账号密码登录, 目前仅供开发人员使用
     */
    @SchneiderRequestMapping("/login")
    public Response login(HttpServletRequest request) {
        super.pageLoad(request);
        try {
            return loginService.login(parameterMap, this.getIpAddrs(request));
        } catch (Exception e) {
            e.printStackTrace();
            return res.setError(e);
        }
    }

    /*
     * 获取系统登录token, 用于访问后台API使用
     */
    @SchneiderRequestMapping("/get_access_token")
    public String getAccessToken(HttpServletRequest request) throws IllegalAccessException {
        super.pageLoad(request);
        return loginService.getAccessToken(parameterMap, this.getIpAddrs(request));
    }

    /*
     * 仅需用户名即可登录系统, 主要用于系统截图使用, 不安全, 目前不对外开放, 仅自用
     */
    @SchneiderRequestMapping("/remote_login")
    public Response remoteLogin(HttpServletRequest request) {
        super.pageLoad(request);
        try {
            return loginService.remoteLogin(parameterMap, this.getIpAddrs(request));
        } catch (Exception e) {
            e.printStackTrace();
            return res.setError(e);
        }
    }

    @SchneiderRequestMapping("/pingsso/callback")
    public void pingSSOCallback(HttpServletRequest request, HttpServletResponse response) throws IOException {
        super.pageLoad(request);
        // String location = request.getScheme() + "://" + request.getServerName() + ":8080/#/pingsso_callback?code=" + parameterMap.get("code");
        // String location = request.getScheme() + "://" + request.getServerName() + "/#/pingsso_callback?code=" + parameterMap.get("code");
        String location = "https://scp-dss.cn.schneider-electric.com/#/pingsso_callback?code=" + parameterMap.get("code");
        response.sendRedirect(location);
    }

    @SchneiderRequestMapping("/pingsso/login")
    public Response pingSSOLogin(HttpServletRequest request) {
        super.pageLoad(request);
        return loginService.pingSSOLogin(parameterMap, this.getIpAddrs(request));
    }

    @SchneiderRequestMapping("/logout")
    public Response logout(HttpServletRequest request) {
        super.pageLoad(request);
        try {
            return loginService.logout(request.getHeader("token"));
        } catch (Exception e) {
            return res.setError(e);
        }
    }

    @SchneiderRequestMapping("/change_password")
    public Response changePassword(HttpServletRequest request) {
        super.pageLoad(request);
        try {
            return loginService.changePassword(parameterMap);
        } catch (Exception e) {
            return res.setError(e);
        }
    }

    @SchneiderRequestMapping("/check_route_auth")
    public Response checkRouteAuth(HttpServletRequest request) {
        try {
            return loginService.checkRouteAuth(request.getHeader("token"), request.getHeader("route"), super.getIpAddr(request));
        } catch (Exception e) {
            return res.setError(e);
        }
    }

    @SchneiderRequestMapping("/query_menu_list")
    public Response queryMenuList(HttpServletRequest request) {
        super.pageLoad(request);
        return loginService.queryMenuList(session.getUserid());
    }

    @SchneiderRequestMapping("/send_to_homepage")
    public Response sendToHomepage(HttpServletRequest request) {
        super.pageLoad(request);
        return loginService.sendToHomepage(parameterMap);
    }

    @SchneiderRequestMapping("/query_system_notification")
    public Response querySystemNotification(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return loginService.querySystemNotification();
    }

    @SchneiderRequestMapping("/query_available_entity")
    public Response queryAvailableEntity(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return loginService.queryAvailableEntity();
    }

    @SchneiderRequestMapping("/query_user_info")
    public Response queryUserInfo(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return loginService.queryUserInfo((String) parameterMap.get("sesaCode"));
    }

    @SchneiderRequestMapping("/create_new_account")
    public Response createNewAccount(HttpServletRequest request) {
        super.pageLoad(request);
        return loginService.createNewAccount(parameterMap);
    }
}
