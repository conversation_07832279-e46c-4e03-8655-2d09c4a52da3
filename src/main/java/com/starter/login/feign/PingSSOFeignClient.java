package com.starter.login.feign;

import com.starter.login.bean.PingSSORequest;
import com.starter.login.bean.PingSSOResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(name = "PingSSOFeignClient", url = PingSSOFeignClient.PingSSO_FEIGN_CLIENT_URL)
public interface PingSSOFeignClient {
    String PingSSO_FEIGN_CLIENT_URL = "https://ping-sso.schneider-electric.com";

    @PostMapping(value = "/as/token.oauth2", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    PingSSOResponse execute(PingSSORequest request);
}
