package com.starter.utils;

import com.adm.system.bean.CascaderBean;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.dialect.oracle.parser.OracleStatementParser;
import com.alibaba.druid.sql.dialect.oracle.visitor.OracleSchemaStatVisitor;
import com.alibaba.druid.sql.parser.SQLStatementParser;
import com.alibaba.druid.stat.TableStat;
import com.scp.toolbox.bean.TreeData;
import com.scp.toolbox.bean.TreeNode;
import jakarta.servlet.http.HttpServletRequest;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.springframework.core.NestedRuntimeException;
import org.springframework.jdbc.BadSqlGrammarException;

import javax.imageio.ImageIO;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.sql.Blob;
import java.sql.Clob;
import java.sql.Types;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class Utils {
    public static String reversalURLParameter(String param) {
        if (StringUtils.isEmpty(param)) {
            return "[invalid parameter format]";
        }

        String regex = "^[a-zA-Z_]+\\w*=\\{[a-zA-Z_]+\\w*\\}(&[a-zA-Z_]+\\w*=\\{[a-zA-Z_]+\\w*\\})*$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(param);

        if (matcher.matches()) {
            String[] params = StringUtils.split(param, "&");
            StringBuffer result = new StringBuffer("?");
            String[] paramsSub;
            for (String p : params) {
                p = p.replace("{", "").replace("}", "");
                paramsSub = StringUtils.split(p, "=");
                result.append(paramsSub[1] + "={" + paramsSub[0] + "}&");
            }

            if (result.length() > 2) {
                result.deleteCharAt(result.length() - 1);
            }
            return result.toString();
        } else {
            return "[invalid parameter format]";
        }
    }

    public static void sortList(List<Map<String, Object>> orgList, String sortKey, String type) {
        if (type.equalsIgnoreCase("asc")) {
            orgList.sort((Map<String, Object> m1, Map<String, Object> m2) -> m1.get(sortKey).toString().compareTo(m2.get(sortKey).toString()));
        } else {
            orgList.sort((Map<String, Object> m1, Map<String, Object> m2) -> m2.get(sortKey).toString().compareTo(m1.get(sortKey).toString()));
        }
    }

    public static List<String> splitStr2List(Object strObj) {
        if ("null".equals(String.valueOf(strObj))) {
            return null;
        }
        String str = String.valueOf(strObj);
        if (str.length() == 0) {
            return new ArrayList<>();
        }
        return Arrays.asList(str.split(","));
    }

    @SuppressWarnings("unchecked")
    public static List<String> object2StrList(Object obj) {
        if (obj instanceof String) {
            return splitStr2List(obj);
        } else if (obj instanceof String[]) {
            return Arrays.asList((String[]) obj);
        } else if (obj instanceof List) {
            return (List<String>) obj;
        } else {
            return new ArrayList<>();
        }
    }

    public static String delHTMLTag(String htmlStr) {
        if (StringUtils.isBlank(htmlStr)) {
            return "";
        }
        String regEx_script = "<script[^>]*?>[\\s\\S]*?<\\/script>"; // 定义script的正则表达式
        String regEx_style = "<style[^>]*?>[\\s\\S]*?<\\/style>"; // 定义style的正则表达式
        String regEx_html = "<[^>]+>"; // 定义HTML标签的正则表达式

        Pattern p_script = Pattern.compile(regEx_script, Pattern.CASE_INSENSITIVE);
        Matcher m_script = p_script.matcher(htmlStr);
        htmlStr = m_script.replaceAll(" "); // 过滤script标签

        Pattern p_style = Pattern.compile(regEx_style, Pattern.CASE_INSENSITIVE);
        Matcher m_style = p_style.matcher(htmlStr);
        htmlStr = m_style.replaceAll(" "); // 过滤style标签

        Pattern p_html = Pattern.compile(regEx_html, Pattern.CASE_INSENSITIVE);
        Matcher m_html = p_html.matcher(htmlStr);
        htmlStr = m_html.replaceAll(" "); // 过滤html标签
        while (htmlStr.contains("  ")) {
            htmlStr = htmlStr.replace("  ", " ");
        }

        return htmlStr.trim(); // 返回文本字符串
    }


    /**
     * 删除HTML中的注释
     *
     * @param htmlStr 原始HTML
     * @return 删掉注释之后的HTML
     */
    public static String delHTMLRemarks(String htmlStr) {
        if (StringUtils.isBlank(htmlStr)) {
            return "";
        }
        String regEx = "<!--[\\s\\S]*?-->"; // 定义注释的正则表达式

        Pattern p = Pattern.compile(regEx, Pattern.CASE_INSENSITIVE);
        Matcher m = p.matcher(htmlStr);
        htmlStr = m.replaceAll(" ");

        return htmlStr.trim(); // 返回文本字符串
    }

    public static boolean containsStr(Object string, Object contains) {
        String str = (String) string;
        String con = (String) contains;
        if (StringUtils.isEmpty(str) || StringUtils.isEmpty(con)) {
            return false;
        }
        String[] strs = str.split(",");
        for (String s : strs) {
            if (con.equalsIgnoreCase(s)) {
                return true;
            }
        }
        return false;
    }

    public static boolean containsStr(Object[] objs, Object contains) {
        if (objs == null || contains == null) {
            return false;
        }
        for (Object o : objs) {
            if (StringUtils.equalsIgnoreCase((String) o, (String) contains)) {
                return true;
            }
        }
        return false;
    }

    public static long parseLong(Object obj) {
        if (obj == null) {
            return 0L;
        }

        if (obj instanceof Long) {
            return (long) obj;
        }

        String strObj = String.valueOf(obj);
        if (!StringUtils.isNumeric(strObj)) {
            return 0L;
        }

        try {
            return Long.parseLong(strObj);
        } catch (Exception ex) {
            return 0L;
        }
    }

    public static double parseDouble(Object obj, double defaultValue) {
        if (obj == null) {
            return defaultValue;
        }
        String strObj = String.valueOf(obj);

        try {
            return Double.parseDouble(strObj.trim());
        } catch (Exception ex) {
            return defaultValue;
        }
    }

    public static double parseDouble(Object obj) {
        return parseDouble(obj, 0);
    }

    public static int parseInt(Object obj) {
        return parseInt(obj, 0);
    }

    public static String parseDate(Object date) {
        return parseDate(date, "yyyy/MM/dd");
    }

    public static String parseDate(Object date, String format) {
        if (date == null) {
            return null;
        }
        if (date instanceof Date) {
            return new SimpleDateFormat(format).format((Date) date);
        } else {
            return String.valueOf(date);
        }
    }

    public static boolean parseBoolean(Object obj) {
        return parseBoolean(obj, false);
    }

    public static boolean parseBoolean(Object obj, boolean defaultValue) {
        if (obj == null) {
            return defaultValue;
        }
        try {
            if (obj instanceof Boolean) {
                return (Boolean) obj;
            }
            return "true".equalsIgnoreCase(obj.toString());
        } catch (Exception e) {
            return defaultValue;
        }
    }

    public static Integer parseInt(Object obj, Integer defaultValue) {
        if (obj == null) {
            return defaultValue;
        }
        if (obj instanceof Integer) {
            return (Integer) obj;
        }
        String strObj = String.valueOf(obj);

        try {
            return (int) Double.parseDouble(strObj);
        } catch (Exception ex) {
            return defaultValue;
        }
    }

    public static BigDecimal parseBigDecimal(Object obj, int scale) {
        if (obj == null) {
            return BigDecimal.ZERO;
        }

        if (obj instanceof BigDecimal) {
            return ((BigDecimal) obj).setScale(scale, RoundingMode.HALF_UP);
        }

        String strObj = String.valueOf(obj);

        try {
            return new BigDecimal(strObj.trim()).setScale(scale, RoundingMode.HALF_UP);
        } catch (Exception ex) {
            return BigDecimal.ZERO;
        }
    }

    public static BigDecimal parseBigDecimal(Object obj) {
        return parseBigDecimal(obj, BigDecimal.ZERO);
    }

    public static BigDecimal parseBigDecimal(Object obj, BigDecimal defaultVal) {
        if (obj == null) {
            return defaultVal;
        }

        if (obj instanceof BigDecimal) {
            return (BigDecimal) obj;
        }

        String strObj = String.valueOf(obj);

        try {
            return new BigDecimal(strObj);
        } catch (Exception ex) {
            return defaultVal;
        }
    }

    /**
     * 除小数点和起始负号外所有非数字字符均为非法
     *
     * @param str
     * @return
     */
    public static boolean isStrictNumeric(String str) {
        if (StringUtils.isEmpty(str)) {
            return false;
        }

        str = StringUtils.removeStart(str, "-");
        str = StringUtils.replaceOnce(str, ".", "");

        if (str.length() == 0) {
            return false;
        }

        for (int i = 0; i < str.length(); i++) {
            if (!Character.isDigit(str.charAt(i))) {
                return false;
            }
        }
        return true;
    }

    public static String renameFile(File orgFile, String newName) {
        try {
            String orgPath = orgFile.getCanonicalPath();
            String suffix = "";
            String floderPath = "";
            String newPath = "";
            int floderIndex = orgPath.lastIndexOf("/");
            int suffixIndex = orgPath.lastIndexOf(".");

            // 如果路径中不包含反斜线,则路径有问题,不做处理
            if (floderIndex == -1) {
                return null;
            }

            if (suffixIndex != -1) {
                suffix = orgPath.substring(orgPath.lastIndexOf("."));
            }

            // 获取文件夹名称
            floderPath = orgPath.substring(0, orgPath.lastIndexOf("/"));

            newPath = floderPath + "/" + newName + suffix;

            File newFile = new File(newPath);

            if (newFile.exists()) {
                newFile.delete();
            }

            FileUtils.moveFile(orgFile, newFile);

            if (newFile.exists()) {
                return newFile.getCanonicalPath();
            } else if (orgFile.exists()) {
                return orgFile.getCanonicalPath();
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * list1减去list2,返回结果,不对list1和list2进行修改
     *
     * @param <T>
     * @param list1
     * @param list2
     * @return
     */
    public static <T> List<T> subtractList(List<T> list1, List<T> list2) {
        List<T> resultList = new ArrayList<>(list1);
        resultList.removeAll(list2);
        return resultList;
    }

    /**
     * <pre>
     * 将Properties格式的文本解析为map
     * </pre>
     *
     * @param property
     * @return
     * @throws IOException
     */
    public static Map<String, String> getPropertyMap(Object property) {
        Map<String, String> resultMap = new HashMap<>();
        String propertyStr = String.valueOf(property);
        if (property == null || StringUtils.isEmpty(propertyStr)) {
            return resultMap;
        }
        try {
            Properties properties = new Properties();
            properties.load(new ByteArrayInputStream(propertyStr.getBytes()));

            for (Entry<Object, Object> entry : properties.entrySet()) {
                resultMap.put(String.valueOf(entry.getKey()), String.valueOf(entry.getValue()));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return resultMap;
    }

    public static Object[] appendArray(Object[] array, Object obj) {
        Object[] result = new Object[array.length + 1];
        for (int i = 0; i < array.length; i++) {
            result[i] = array[i];
        }
        result[array.length] = obj;
        return result;
    }

    /**
     * <pre>
     * 获取poi读取的excel的单元格内容,返回Object类型
     * </pre>
     *
     * @param cell
     * @return
     */
    public static String getCellValue(Cell cell) {
        String result = null;
        if (cell != null) {
            switch (cell.getCellType()) {
                case BLANK:
                    break;
                case STRING:
                    result = cell.getStringCellValue();
                    break;
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        Date date = cell.getDateCellValue();
                        result = String.valueOf(date.getTime() / 1000);
                    } else {
                        result = new BigDecimal(String.valueOf(cell.getNumericCellValue())).toPlainString();
                    }
                    break;
                case FORMULA:
                    switch (cell.getCachedFormulaResultType()) {
                        case BLANK:
                            break;
                        case STRING:
                            result = cell.getStringCellValue();
                            break;
                        case NUMERIC:
                            if (DateUtil.isCellDateFormatted(cell)) {
                                Date date = cell.getDateCellValue();
                                result = String.valueOf(date.getTime() / 1000);
                            } else {
                                result = new BigDecimal(String.valueOf(cell.getNumericCellValue())).toPlainString();
                            }
                            break;
                        case BOOLEAN:
                            result = String.valueOf(cell.getBooleanCellValue());
                            break;
                        case ERROR:
                            result = String.valueOf(cell.getErrorCellValue());
                            break;
                        default:
                    }

                    break;
                case BOOLEAN:
                    result = String.valueOf(cell.getBooleanCellValue());
                    break;
                case ERROR:
                    result = String.valueOf(cell.getErrorCellValue());
                    break;
                default:
            }
        }
        return result == null ? null : result.trim();
    }

    /**
     * <pre>
     * 从数据中获取map中取值,如果没有值,则返回newInstance
     * </pre>
     *
     * @param <T>
     * @param map
     * @return
     */
    public static <T> T getValue(Map<String, T> map, Object keyObj, T newInstance) {
        String key = String.valueOf(keyObj);
        return map.computeIfAbsent(key, k -> newInstance);
    }

    /**
     * <pre>
     * 根据maxSize将list切割为多个list
     * </pre>
     *
     * @param list
     * @param maxSize
     * @return
     */
    public static <T> List<List<T>> subList(List<T> list, int maxSize) {
        List<List<T>> result = new ArrayList<>();

        int current_size = list.size();
        int start = 0;
        int end = 0;
        List<T> subList;
        do {
            end = Math.min(current_size, maxSize);
            subList = list.subList(start, start + end);
            result.add(new ArrayList<>(subList));
            start += end;
            current_size -= maxSize;
        } while (current_size > 0);

        return result;
    }

    public static String clob2String(Object clobObj) {
        try {
            if (clobObj == null) {
                return null;
            }
            if (clobObj instanceof Clob) {
                Clob clob = (Clob) clobObj;
                String ret = "";
                Reader read = clob.getCharacterStream();
                BufferedReader br = new BufferedReader(read);
                String s = br.readLine();
                StringBuffer sb = new StringBuffer();
                while (s != null) {
                    sb.append(s);
                    s = br.readLine();
                }
                ret = sb.toString();
                br.close();
                read.close();
                return ret;
            } else {
                return String.valueOf(clobObj);
            }
        } catch (Exception e) {
            return "";
        }
    }

    public static void parseParameterArray(Map<String, Object> parameterMap, String key, String newKey) {
        Object paramObj = parameterMap.get(key);
        if (paramObj instanceof String) {
            String param = (String) paramObj;
            if (StringUtils.isNotBlank(param) == true) {
                param = param.replace("，", ",");
                param = param.replace("\t", ",");
                param = param.replace("\r", ",");
                param = param.replace("\n", ",");

                String[] ps = param.split(",");
                List<String> paramList = new ArrayList<>();

                for (String p : ps) {
                    if (StringUtils.isNotBlank(p) == true) {
                        paramList.add(p.trim());
                    }
                }

                parameterMap.put(newKey, paramList);
            }
        } else if (paramObj instanceof String[]) {
            parameterMap.put(newKey, Arrays.asList((String[]) paramObj));
        }
    }

    public static String randomStr(int length) {
        StringBuilder result = new StringBuilder();
        while (result.length() < length) {
            result.append(UUID.randomUUID().toString().replace("-", ""));
        }
        return result.substring(0, length);
    }

    public static void parseParameterArray(Map<String, Object> parameterMap, String key) {
        parseParameterArray(parameterMap, key, key + "_list");
    }

    public static boolean hasInjectionAttack(Object sqlObj) {
        if (sqlObj == null) {
            return false;
        }
        String sql = String.valueOf(sqlObj);
        String[] injectionWords = {";", "delete ", "update ", "select ", "insert ", "alter ", "truncate ", "drop ", "union ", "kill ", "lock "};
        if (StringUtils.isBlank(sql) == true) {
            return false;
        }

        for (String w : injectionWords) {
            if (StringUtils.indexOfIgnoreCase(sql, w) != -1) {
                return true;
            }
        }
        return false;
    }

    public static String getExcelHeaderByIndex(int i) {
        StringBuilder sb = new StringBuilder();
        int radix = 26;
        boolean f = true;
        while (f) {
            sb.insert(0, ((char) (i % radix + 65)));
            i = i / radix;
            if (i == 0) {
                f = false;
            }
        }
        return sb.toString();
    }

    public static String thousandBitSeparator(Object numberStr) {
        return thousandBitSeparator(numberStr, null);
    }

    public static String thousandBitSeparator(Object numberStr, Integer round) {
        if (numberStr == null) {
            return null;
        }
        if (numberStr instanceof BigDecimal) {
            return thousandBitSeparator(((BigDecimal) numberStr).toPlainString(), round);
        }
        return thousandBitSeparator(String.valueOf(numberStr), round);
    }

    public static String thousandBitSeparator(String numberStr, Integer round) {
        try {
            String[] n = numberStr.split("\\.");

            if (n.length == 1 || n.length == 2) {
                String str = n[0];
                String prefix = "";
                if (str.startsWith("-")) {
                    prefix = "-";
                    str = str.substring(1);
                }
                int l = str.length() + str.length() / 3 - ((str.length() % 3 == 0) ? 1 : 0);
                char[] chars = new char[l];

                int j = 1;
                int c = chars.length - 1;
                for (int i = str.length() - 1; i > -1; i--) {
                    chars[c--] = str.charAt(i);
                    if (j % 3 == 0 && i != 0) {
                        chars[c--] = ',';
                    }
                    j++;
                }

                if (n.length == 2) {
                    if (round != null) {
                        return prefix + new String(chars) + "." + StringUtils.substring(n[1], 0, round);
                    } else {
                        return prefix + new String(chars) + "." + n[1];
                    }
                } else {
                    return prefix + new String(chars);
                }
            } else {
                return numberStr;
            }
        } catch (Exception e) {
            return numberStr;
        }
    }

    public static String getStackTrace(Throwable throwable) {
        StringWriter sw = new StringWriter();
        try (PrintWriter pw = new PrintWriter(sw)) {
            throwable.printStackTrace(pw);
            return sw.toString();
        }
    }

    /**
     * 获取简单的错误提示
     */
    public static String getExceptionMessage(Exception e) {
        if (e instanceof BadSqlGrammarException) {
            return ((BadSqlGrammarException) e).getSQLException().getMessage();
        } else if (e instanceof NestedRuntimeException) {
            return e.getCause().getMessage();
        } else if (e instanceof NullPointerException) {
            return "空指针异常 - NullPointerException";
        } else {
            return e.getMessage();
        }
    }

    public static List<CascaderBean> parseCascader(List<Map<String, String>> dataList) {
        return parseCascader(dataList, true);
    }

    /**
     * 将查询出来的结果集转换为Cascader可以识别的格式
     *
     * @param dataList 源结果集, 需要包含 CATEGORY和 NAME
     * @return 可以识别数据
     */
    public static List<CascaderBean> parseCascader(List<Map<String, String>> dataList, boolean containOthers) {
        List<CascaderBean> resultList = new ArrayList<>();

        Map<String, List<Map<String, String>>> tempMap = new LinkedHashMap<>();

        for (Map<String, String> map : dataList) {
            List<Map<String, String>> list = tempMap.computeIfAbsent(map.get("CATEGORY"), key -> new ArrayList<>());
            list.add(map);
        }

        for (String key : tempMap.keySet()) {
            CascaderBean bean = new CascaderBean();
            resultList.add(bean);
            bean.setLabel(key);
            bean.setValue(key);
            boolean hasOthers = false;
            for (Map<String, String> map : tempMap.get(key)) {
                CascaderBean subBean = new CascaderBean();
                String name = map.get("NAME");
                hasOthers = hasOthers ? hasOthers : "Others".equals(name);
                subBean.setLabel(name);
                subBean.setValue(map.getOrDefault("VALUE", name));
                bean.addChild(subBean);
            }
            if (hasOthers == false && containOthers) {
                CascaderBean subBean = new CascaderBean();
                subBean.setLabel("Others");
                subBean.setValue("Others");
                bean.addChild(subBean);
            }

        }

        return resultList;
    }

    /**
     * <pre>
     * 将 202101格式的文本转为JAN-21格式
     * </pre>
     *
     * @param fullMonth 202101格式的文本
     * @return JAN-21文本
     */
    public static String convertMonth(String fullMonth) {
        if (StringUtils.length(fullMonth) != 6) {
            return fullMonth;
        }
        String year = fullMonth.substring(2, 4);
        String month = fullMonth.substring(4, 6);
        return switch (month) {
            case "01" -> "JAN-" + year;
            case "02" -> "FEB-" + year;
            case "03" -> "MAR-" + year;
            case "04" -> "APR-" + year;
            case "05" -> "MAY-" + year;
            case "06" -> "JUN-" + year;
            case "07" -> "JUL-" + year;
            case "08" -> "AUG-" + year;
            case "09" -> "SEP-" + year;
            case "10" -> "OCT-" + year;
            case "11" -> "NOV-" + year;
            case "12" -> "DEC-" + year;
            default -> fullMonth;
        };
    }

    public static String convertDayName(String day) {
        return switch (day) {
            case "1", "01" -> "Mon";
            case "2", "02" -> "Tue";
            case "3", "03" -> "Wed";
            case "4", "04" -> "Thu";
            case "5", "05" -> "Fri";
            case "6", "06" -> "Sat";
            case "7", "07" -> "Sun";
            default -> day;
        };
    }

    /**
     * @param monthName JAN-21
     * @return 202101
     */
    public static String convertMonthFromName2Code(String monthName) {
        if (StringUtils.length(monthName) != 6 || (!monthName.contains("-"))) {
            return monthName;
        }
        String[] monthNames = monthName.split("-");
        String year = monthNames[1];
        String month = monthNames[0];
        return switch (month) {
            case "JAN" -> "20" + year + "01";
            case "FEB" -> "20" + year + "02";
            case "MAR" -> "20" + year + "03";
            case "APR" -> "20" + year + "04";
            case "MAY" -> "20" + year + "05";
            case "JUN" -> "20" + year + "06";
            case "JUL" -> "20" + year + "07";
            case "AUG" -> "20" + year + "08";
            case "SEP" -> "20" + year + "09";
            case "OCT" -> "20" + year + "10";
            case "NOV" -> "20" + year + "11";
            case "DEC" -> "20" + year + "12";
            default -> monthName;
        };
    }

    /**
     * <pre>
     * 字符串202101格式的月份+1
     * </pre>
     *
     * @param fullMonth 202101格式的日期
     * @return 返回202101 +1Month的结果
     */
    public static String addMonth(String fullMonth) {
        if (StringUtils.length(fullMonth) != 6) {
            return fullMonth;
        }
        int year = Utils.parseInt(fullMonth.substring(0, 4));
        int month = Utils.parseInt(fullMonth.substring(4, 6));

        if (month == 12) {
            month = 1;
            year++;
        } else {
            month++;
        }
        return year + "" + (month > 9 ? month : "0" + month);
    }

    public static String addMonth(String fullMonth, int add) {
        if (StringUtils.length(fullMonth) != 6) {
            return fullMonth;
        }
        int year = Utils.parseInt(fullMonth.substring(0, 4));
        int month = Utils.parseInt(fullMonth.substring(4, 6));

        if (add > 0) {
            for (int i = 0; i < add; i++) {
                if (month == 12) {
                    month = 1;
                    year++;
                } else {
                    month++;
                }
            }
        } else {
            add = Math.abs(add);
            for (int i = 0; i < add; i++) {
                if (month == 1) {
                    month = 12;
                    year--;
                } else {
                    month--;
                }
            }
        }

        return year + "" + (month > 9 ? month : "0" + month);
    }

    /**
     * 加减月, 支持负数
     *
     * @param orgWeek 202010
     * @param n       1,2,3
     * @return 202012
     */
    public static String addWeek(String orgWeek, int n) {
        int year = Integer.parseInt(orgWeek.substring(0, 4));
        int week = Integer.parseInt(orgWeek.substring(4, 6));

        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.WEEK_OF_YEAR, week);
        calendar.set(Calendar.DAY_OF_WEEK, 3); // 计算的时候设置到每周第三天
        calendar.add(Calendar.WEEK_OF_YEAR, n);
        return new SimpleDateFormat("yyyyww").format(calendar.getTime());
    }


    /**
     * <pre>
     * 字符串格式的月份 -1
     * </pre>
     *
     * @param fullMonth 202101格式的日期
     * @return 返回202101 - 1Month的结果
     */
    public static String minusMonth(String fullMonth) {
        if (StringUtils.length(fullMonth) != 6) {
            return fullMonth;
        }
        int year = Utils.parseInt(fullMonth.substring(0, 4));
        int month = Utils.parseInt(fullMonth.substring(4, 6));

        if (month == 0) {
            month = 12;
            year--;
        } else {
            month--;
        }
        return year + "" + (month > 9 ? month : "0" + month);
    }


    public static List<List<String>> splitValue(String value) {
        return splitValue(value, 800);
    }


    /**
     * 将前台传过来的值分割成list, 因为oracle的in有数量限制
     *
     * @param value   前台传过来的文本
     * @param maxSize 单个list最大容量, 如果maxSize <= 0 则不分割
     * @return List<List < String>>
     */
    public static List<List<String>> splitValue(String value, int maxSize) {
        if (StringUtils.isBlank(value)) {
            return new ArrayList<>();
        }
        value = value.trim();
        // value = value.replace(" ", ","); 暂时移除空格
        value = value.replace("\r", ",");
        value = value.replace("\n", ",");
        value = value.replace("，", ",");
        while (value.contains(",,")) {
            value = value.replace(",,", ",");
        }
        List<String> valueList = new ArrayList<>(Arrays.asList(value.split(",")));
        if (valueList.isEmpty() == false) {
            if (maxSize > 0) {
                return subList(valueList, maxSize);
            } else {
                return new ArrayList<>() {{
                    add(valueList);
                }};
            }
        }
        return new ArrayList<>();
    }

    public static List<List<String>> splitValueByEnter(String value, int maxSize) {
        if (StringUtils.isBlank(value)) {
            return new ArrayList<>();
        }
        value = value.trim();
        value = value.replace("\r", "\n");
        while (value.contains("\n\n")) {
            value = value.replace("\n\n", "\n");
        }
        List<String> valueList = Arrays.asList(value.split("\n"));
        if (valueList.isEmpty() == false) {
            if (maxSize > 0) {
                return subList(valueList, maxSize);
            } else {
                return new ArrayList<>() {{
                    add(valueList);
                }};
            }
        }
        return new ArrayList<>();
    }

    public static String file2Base64(File file) throws IOException {
        InputStream in = new FileInputStream(file);
        byte[] data = new byte[in.available()];
        in.read(data);
        in.close();
        return Base64.getEncoder().encodeToString(data);
    }

    public static String clearStr(String org) {
        if (org == null) {
            return null;
        }
        org = org.replace(" ", " ");
        org = org.replace("\r", "");
        org = org.replace("\n", "");
        org = org.replace("\t", "");
        org = org.trim();
        return org;
    }

    /**
     * 目前只支持对List<Map<String, Object>>结构的数据进行清理
     * 清除单元格的空格, 清除空行
     *
     * @param rows
     * @return
     */
    @SuppressWarnings("unchecked")
    public static List<Map<String, Object>> clearList(Object rows) {
        List<Map<String, Object>> resultList = new ArrayList<>();
        if (rows instanceof List) {
            for (Object obj : (List<Object>) rows) {
                if (obj instanceof Map) {
                    Map<String, Object> map = (Map<String, Object>) obj;
                    boolean allBlank = true;
                    for (String key : map.keySet()) {
                        Object value = map.get(key);
                        if (value instanceof String) {
                            String valueCleared = Utils.clearStr((String) value);
                            if (StringUtils.isNotBlank(valueCleared)) {
                                allBlank = false;
                            }
                            map.put(key, valueCleared);
                        }
                    }
                    if (allBlank == false) {
                        resultList.add(map);
                    }
                }
            }
        }
        return resultList;
    }

    public static List<TreeNode> parseTreeNodes(List<TreeData> dataList) {
        return parseTreeNodes(dataList, "\\.");
    }

    /**
     * 渲染树形菜单
     *
     * @param dataList 扁平的树结构数据
     * @return 结构化的数据
     */
    public static List<TreeNode> parseTreeNodes(List<TreeData> dataList, String spliterRegex) {
        List<TreeNode> resultList = new ArrayList<>();
        String spliterStr = StringUtils.remove(spliterRegex, "\\");

        LinkedHashMap<String, List<TreeData>> tempMap = new LinkedHashMap<>();

        for (TreeData data : dataList) {
            if (StringUtils.isBlank(data.getGroups())) {
                TreeNode treeNode = new TreeNode();
                treeNode.setLabel(data.getLabel());
                treeNode.setSubLabel(data.getSubLabel());
                treeNode.setKey(data.getKey());
                treeNode.setDataValue(data.getDataValue());
                resultList.add(treeNode);
            } else {
                String key = data.getGroups();
                String[] keys = key.split(spliterRegex);

                String k0 = keys[0]; // 一级目录
                String k1 = (keys.length == 1) ? "" : StringUtils.remove(key, k0 + spliterStr); // 二级目录

                List<TreeData> list = tempMap.computeIfAbsent(data.getGroups().split(spliterRegex)[0], k -> new ArrayList<>());
                data.setGroups(k1);
                list.add(data);
            }
        }

        for (String key : tempMap.keySet()) {
            List<TreeData> list = tempMap.get(key);
            TreeNode treeNode = new TreeNode();
            treeNode.setKey(null); // 目录不设置key, 叶子节点才有key属性
            treeNode.setLabel(key);
            treeNode.setChildren(parseTreeNodes(list, spliterRegex));
            treeNode.setChildren(treeNode.getChildren().stream().sorted(Comparator.comparing(TreeNode::getLabel)).collect(Collectors.toList()));
            resultList.add(treeNode);
        }
        return resultList;
    }

    static final List<Integer> numberTypes = Arrays.asList(Types.TINYINT, Types.SMALLINT, Types.INTEGER, Types.BIGINT, Types.FLOAT, Types.REAL, Types.DOUBLE, Types.NUMERIC, Types.DECIMAL);

    public static boolean isNumberColumn(int columnType) {
        return numberTypes.contains(columnType) == true;
    }

    public static String prettyMailBody(String html) {
        html = StringUtils.replaceIgnoreCase(html, "<table>", "<table style=\"border-collapse: collapse;border: 1px solid #ccc\">");
        html = StringUtils.replaceIgnoreCase(html, "<th>", "<th style=\"font-size: 9.5pt;font-family:Calibri,DengXian;background-color: rgb(204, 204, 204);border: 1px solid #fff;padding:3pt;\">");
        html = StringUtils.replaceIgnoreCase(html, "<td>", "<td style=\"font-size: 9.5pt;font-family:Calibri,DengXian;border: 1px solid #ccc;padding:3pt;\">");
        html = StringUtils.replaceIgnoreCase(html, "<p>", "<p style=\"font-size: 9.5pt;font-family:Calibri,DengXian;padding:0;margin:0\">");
        html = StringUtils.replaceIgnoreCase(html, "<span>", "<span style=\"font-size: 9.5pt;font-family:Calibri,DengXian;\">");
        html = StringUtils.replaceIgnoreCase(html, "<div>", "<div style=\"font-size: 9.5pt;font-family:Calibri,DengXian;\">");
        html = StringUtils.replaceIgnoreCase(html, "<pre>", "<pre style=\"font-size: 9.5pt;font-family:Calibri,DengXian;\">");
        return html;
    }

    public static File compressImage(File orgImage, int maxWidth, double quality) throws Exception {
        String orgFilePath = orgImage.getAbsolutePath();
        String suffix = orgFilePath.substring(orgFilePath.lastIndexOf("."));
        String newFilePath = StringUtils.removeEnd(orgFilePath, suffix) + "_" + Utils.randomStr(4) + suffix;
        int imageWidth = ImageIO.read(orgImage).getWidth();
        double scale = 1;
        if (imageWidth > maxWidth) {
            scale = maxWidth * 1.0 / imageWidth;
        }
        Thumbnails.of(orgImage).scale(scale) //图片大小（长宽）压缩比例 从0-1，1表示原图
                .outputQuality(quality) //图片质量压缩比例 从0-1，越接近1质量越好
                .toOutputStream(new FileOutputStream(newFilePath));
        return new File(newFilePath);
    }

    public static String convertImage2String(File file) throws Exception {
        InputStream in = new FileInputStream(file);
        byte[] data = new byte[in.available()];
        in.read(data);
        in.close();
        return Base64.getEncoder().encodeToString(data);
    }

    public static String getStrByByteLength(String str, int maxLength, Charset charset) {
        byte[] bytes = str.getBytes(charset);
        if (maxLength > bytes.length) {
            return str;
        } else {
            // 防止最后一位乱码, 直接移除最后一位字符
            String result = new String(Arrays.copyOfRange(bytes, 0, maxLength));
            return result.substring(0, result.length() - 1);
        }
    }

    public static String getStrByByteLength(String str, int maxLength) {
        return getStrByByteLength(str, maxLength, StandardCharsets.UTF_8);
    }

    private static final SimpleDateFormat defaultDateFormat = new SimpleDateFormat("yyyy/MM/dd");

    public static String removeMakeDownSpliter(Object v) {
        String result;
        if (v == null) {
            result = "";
        } else if (v instanceof String) {
            result = String.valueOf(v);
        } else if (v instanceof Date) {
            try {
                result = defaultDateFormat.format((Date) v);
            } catch (Exception e) {
                result = String.valueOf(v);
            }
        } else if (v instanceof BigDecimal) {
            result = String.valueOf(((BigDecimal) v).doubleValue());
            result = StringUtils.removeEnd(result, ".0");
        } else if (v instanceof Clob) {
            try {
                Clob clob = (Clob) v;
                String ret = "";
                Reader read = clob.getCharacterStream();
                BufferedReader br = new BufferedReader(read);
                String s = br.readLine();
                StringBuilder sb = new StringBuilder();
                while (s != null) {
                    sb.append(s);
                    s = br.readLine();
                }
                ret = sb.toString();
                br.close();
                read.close();
                result = ret;
            } catch (Exception e) {
                result = "Clob";
            }

        } else if (v instanceof Blob) {
            try {
                result = new String(((Blob) v).getBytes(1, (int) ((Blob) v).length()), StandardCharsets.UTF_8);
            } catch (Exception e) {
                result = "Blob";
            }
        } else if (v instanceof byte[]) {
            try {
                result = new String((byte[]) v, StandardCharsets.UTF_8);
            } catch (Exception e) {
                result = "byte[]";
            }
        } else {
            result = String.valueOf(v);
        }
        result = StringUtils.substring(result, 0, 50);
        result = StringUtils.replace(result, "|", " ");
        result = StringUtils.replace(result, "\r", " ");
        result = StringUtils.replace(result, "\n", " ");
        return result;
    }

    public static List<String> getTableNamesBySQL(String sql) {
        List<String> result = new ArrayList<>();
        try {
            SQLStatementParser parser = new OracleStatementParser(sql);
            SQLStatement statement = parser.parseStatement();
            OracleSchemaStatVisitor visitor = new OracleSchemaStatVisitor();
            statement.accept(visitor);
            Map<TableStat.Name, TableStat> tables = visitor.getTables();
            for (TableStat.Name t : tables.keySet()) {
                String name = t.getName().toUpperCase();
                if (name.contains(".") == true) {
                    result.add(StringUtils.split(name, ".")[1]);
                } else {
                    result.add(name);
                }
            }
        } catch (Exception ignore) {
        }
        return result;
    }

    public static String getRealIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip) || isAllEqualIpAddr(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip) || isAllEqualIpAddr(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip) || isAllEqualIpAddr(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    public static String getIpAddr(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    // 当四段IP中有三段相等时, 大概率是虚拟IP, 尝试通过其他方式获取
    public static boolean isAllEqualIpAddr(String ip) {
        // 施耐德内网IP以10开头, 所以10开头的网址都不再判断
        if (StringUtils.startsWith(ip, "10.")) {
            return false;
        }
        List<String> list = Arrays.asList(StringUtils.split(ip, "."));
        return list.stream().distinct().count() <= 2;
    }

    public static String convertUnicodeToChinese(String unicodeString) {
        StringBuilder result = new StringBuilder();
        int index = 0;

        while (index < unicodeString.length()) {
            if (index + 6 <= unicodeString.length() && unicodeString.startsWith("\\u", index)) {
                // 提取 Unicode 编码
                String unicode = unicodeString.substring(index + 2, index + 6);
                // 转换为字符
                char character = (char) Integer.parseInt(unicode, 16);
                result.append(character);
                // 跳过已处理的 Unicode 部分
                index += 6;
            } else {
                // 普通字符直接追加
                result.append(unicodeString.charAt(index));
                index++;
            }
        }

        return result.toString();
    }
}
