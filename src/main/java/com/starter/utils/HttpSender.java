package com.starter.utils;

import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Map;

import javax.net.ssl.HttpsURLConnection;

@Component
public class HttpSender {

	private static final int READ_TIMEOUT = 60000;
	private static final int CONNECT_TIMEOUT = 60000;

	public enum RequestMethod {
		POST, GET
	}

	/**
	 * <pre>
	 *  1. default UTF-8 charset
	 *  2. no addition request header
	 *  3. request method : get
	 *  4. no parameter
	 * </pre>
	 *
	 * @param url
	 * @return
	 */
	public String sendRequest(String url) {
		return this.sendRequest(url, null, RequestMethod.GET, null, "UTF-8");
	}

	/**
	 * <pre>
	 *  1. default UTF-8 charset
	 *  2. no request header
	 *  3. request method : get
	 * </pre>
	 *
	 * @param url
	 * @param param
	 * @return
	 */
	public String sendRequest(String url, String param) {
		return this.sendRequest(url, param, RequestMethod.GET, null, "UTF-8");
	}

	/**
	 * <pre>
	 *  1. default UTF-8 charset
	 *  2. no request header
	 * </pre>
	 *
	 * @param url
	 * @param param
	 * @param method
	 * @return
	 */
	public String sendRequest(String url, String param, RequestMethod method) {
		return this.sendRequest(url, param, method, null, "UTF-8");
	}

	/**
	 * <pre>
	 *  1. default UTF-8 charset
	 * </pre>
	 *
	 * @param url
	 * @param param
	 * @param method
	 * @return
	 */
	public String sendRequest(String url, String param, RequestMethod method, Map<String, String> requestHeader) {
		return this.sendRequest(url, param, method, requestHeader, "UTF-8");
	}

	/**
	 * @param url
	 * @param param
	 * @param method
	 * @param charsetName
	 * @return
	 */
	public String sendRequest(String url, String param, RequestMethod method, Map<String, String> requestHeader, String charsetName) {
		if (url == null || url.length() == 0) {
			throw new NullPointerException("url is empty");
		}
		boolean isHttps = url.toLowerCase().startsWith("https");

		if (isHttps) {
			return this.sendRequestHttps(url, method, param, requestHeader, charsetName);
		} else {
			return this.sendRequestHttp(url, method, param, requestHeader, charsetName);
		}
	}

	private String sendRequestHttp(String url, RequestMethod method, String param, Map<String, String> requestHeader, String charsetName) {
		PrintWriter out = null;
		BufferedReader in = null;
		String result = "";

		try {
			URL requestUrl = new URL(url);
			HttpURLConnection conn = (HttpURLConnection) requestUrl.openConnection();

			conn.setRequestMethod(method.toString());
			conn.setDoOutput(true);
			conn.setDoInput(true);
			conn.setUseCaches(false);
			conn.setInstanceFollowRedirects(true);
			conn.setReadTimeout(READ_TIMEOUT);
			conn.setConnectTimeout(CONNECT_TIMEOUT);
			conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");

			if (requestHeader != null && (!requestHeader.isEmpty())) {
				for (Map.Entry<String, String> entry : requestHeader.entrySet()) {
					conn.setRequestProperty(entry.getKey(), entry.getValue());
				}
			}

			if (param != null && param.length() > 0) {
				out = new PrintWriter(conn.getOutputStream());
				out.print(param);
				out.flush();
			}

			in = new BufferedReader(new InputStreamReader(conn.getInputStream(), charsetName));
			String line;
			while ((line = in.readLine()) != null) {
				result += line;
			}
		} catch (Exception e) {
			result = null;
			e.printStackTrace();
		} finally {
			try {
				if (out != null) {
					out.close();
				}
				if (in != null) {
					in.close();
				}
			} catch (IOException ex) {
				ex.printStackTrace();
			}
		}

		return result;
	}

	private String sendRequestHttps(String url, RequestMethod method, String param, Map<String, String> requestHeader, String charsetName) {
		PrintWriter out = null;
		BufferedReader in = null;
		StringBuilder result = new StringBuilder();

		try {
			URL requestUrl = new URL(url);
			HttpsURLConnection conn = (HttpsURLConnection) requestUrl.openConnection();

			conn.setRequestMethod(method.toString());
			conn.setDoOutput(true);
			conn.setDoInput(true);
			conn.setUseCaches(false);
			conn.setInstanceFollowRedirects(true);
			conn.setReadTimeout(READ_TIMEOUT);
			conn.setConnectTimeout(CONNECT_TIMEOUT);
			conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");

			if (requestHeader != null && (!requestHeader.isEmpty())) {
				for (Map.Entry<String, String> entry : requestHeader.entrySet()) {
					conn.setRequestProperty(entry.getKey(), entry.getValue());
				}
			}

			if (param != null && param.length() > 0) {
				out = new PrintWriter(conn.getOutputStream());
				out.print(param);
				out.flush();
			}

			in = new BufferedReader(new InputStreamReader(conn.getInputStream(), charsetName));
			String line;
			while ((line = in.readLine()) != null) {
				result.append(line);
			}
		} catch (Exception e) {
			result = new StringBuilder();
			e.printStackTrace();
		} finally {
			try {
				if (out != null) {
					out.close();
				}
				if (in != null) {
					in.close();
				}
			} catch (IOException ex) {
				ex.printStackTrace();
			}
		}

		return result.toString();
	}
}
