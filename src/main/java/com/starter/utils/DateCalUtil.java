package com.starter.utils;

import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;

public class DateCalUtil {
    private static final String DEFAULT_DATETIME_FORMAT = "yyyy/MM/dd HH:mm:ss";
    private static final String DEFAULT_DATE_FORMAT = "yyyy/MM/dd";
    private static final String DEFAULT_TIMEZONE = "GMT+8";

    /**
     * <pre>
     * 	根据daterange计算unix_start_time和unix_end_time
     * dateObj格式
     * 1. 2018/09/05
     * 2. 2018/09/05 12:48
     * 3. 2018/09/05 12:48 - 2018/09/05 23:59
     * </pre>
     *
     * @param dateObj
     * @return
     */
    public static Map<String, Long> calUnixTime(Object dateObj) {
        if (dateObj == null) {
            return null;
        }

        Map<String, Long> result = new HashMap<String, Long>();

        String dateStr = String.valueOf(dateObj).trim();

        if (dateStr.contains("-")) {
            String[] dates = dateStr.split("-");
            String startDateStr = parseDateStr(dates[0]);
            String endDateStr = parseDateStr(dates[1]);
            result.put("unix_start_time", parseDateTime(startDateStr));
            result.put("unix_end_time", parseDateTime(endDateStr));
        } else {
            String startDateStr = parseDateStr(dateStr);
            result.put("unix_start_time", parseDateTime(startDateStr));
        }

        return result;
    }

    public static String unixTime2DayTime(Object unixTimeObj) {
        return fromUnixTime(unixTimeObj, DEFAULT_DATETIME_FORMAT, DEFAULT_TIMEZONE);
    }

    public static String unixTime2Day(Object unixTimeObj) {
        return fromUnixTime(unixTimeObj, DEFAULT_DATE_FORMAT, DEFAULT_TIMEZONE);
    }

    public static String fromUnixTime(Object unixTimeObj, String format, String timezone) {
        if (unixTimeObj == null) {
            return null;
        }

        try {
            long unixTime = Long.parseLong(String.valueOf(unixTimeObj));
            Date date = new Date();
            date.setTime(unixTime * 1000);
            SimpleDateFormat dateFormat = new SimpleDateFormat(format);
            dateFormat.setTimeZone(TimeZone.getTimeZone(timezone));
            return dateFormat.format(date);
        } catch (Exception e) {
            return e.getMessage();
        }
    }

    /**
     * Excel的日期如果是数字, 此数字是距离1900年1月1日的天数,小数代表时间
     * 如果excelNumberObj不是一个数字, 则直接返回原值
     *
     * @param excelNumberObj Excel 中的日期数字
     * @return
     */
    public static String excelNumber2Date(Object excelNumberObj, String format, String timezone) {
        try {
            String excelNumberStr = excelNumberObj == null ? "" : String.valueOf(excelNumberObj);
            if (Utils.isStrictNumeric(excelNumberStr) == false) {
                return excelNumberStr;
            }

            double excelNumber = Double.parseDouble(excelNumberStr);
            long unixTime = (long) ((excelNumber - 25569) * 86400 - 28800);
            return fromUnixTime(unixTime, format, timezone);
        } catch (Exception e) {
            return e.getMessage();
        }
    }

    public static String getDateStrFromExcel(Object excelData) {
        return getDateStrFromExcel(excelData, DEFAULT_DATE_FORMAT, DEFAULT_TIMEZONE);
    }

    /**
     * 从Excel中获取日期, 支持Excel的日期格式
     *
     * @param excelData
     * @param format
     * @param timezone
     * @return
     */
    public static String getDateStrFromExcel(Object excelData, String format, String timezone) {
        if (Utils.isStrictNumeric(String.valueOf(excelData))) {
            return excelNumber2Date(excelData, format, timezone);
        } else {
            return String.valueOf(excelData);
        }

    }

    public static String excelNumber2Date(Object unixTimeObj, String format) {
        return excelNumber2Date(unixTimeObj, format, DEFAULT_TIMEZONE);
    }

    public static String excelNumber2Day(Object unixTimeObj) {
        return excelNumber2Date(unixTimeObj, DEFAULT_DATE_FORMAT, DEFAULT_TIMEZONE);
    }

    public static String excelNumber2DayTime(Object unixTimeObj) {
        return excelNumber2Date(unixTimeObj, DEFAULT_DATETIME_FORMAT, DEFAULT_TIMEZONE);
    }

    /**
     * 将带时间的日期转化为unix时间戳
     *
     * @param dateStr
     * @return
     */
    public static long parseDateTime(String dateStr) {
        return parseDate(dateStr, DEFAULT_DATETIME_FORMAT, DEFAULT_TIMEZONE);
    }

    /**
     * 将不带时间的日期转化为时间戳
     *
     * @param dateStr
     * @return
     */
    public static long parseDate(String dateStr) {
        return parseDate(dateStr, DEFAULT_DATE_FORMAT, DEFAULT_TIMEZONE);
    }

    public static long parseDate(String dateStr, String dateFormat, String timezone) {
        try {
            TimeZone timeZone = TimeZone.getTimeZone(timezone);
            SimpleDateFormat format = new SimpleDateFormat(dateFormat);
            format.setTimeZone(timeZone);

            Date date = format.parse(dateStr);
            return date.getTime() / 1000;
        } catch (Exception e) {
            return -1L;
        }
    }

    /**
     * 计算两个月份的差
     *
     * @param startMonth 201901
     * @param endMonth   202001
     * @return 12
     */
    public static int calcMonthGap(int startMonth, int endMonth) {
        int smallDate = Math.min(startMonth, endMonth);
        int bigDate = Math.max(startMonth, endMonth);

        if (bigDate - smallDate < 12) {
            if (startMonth > endMonth) {
                return smallDate - bigDate;
            } else {
                return bigDate - smallDate;
            }
        }

        int gapMonth = bigDate - smallDate;
        int gapYear = (int) Math.ceil(gapMonth / 120.0);

        int result = gapMonth - 88 * gapYear;// gapYear * 12 - (100 * gapYear - gapMonth) 的简写
        if (startMonth > endMonth) {
            result *= -1;
        }

        return result;
    }

    /**
     * 加减月, 支持负数
     *
     * @param orgMonth 202010
     * @param n        1,2,3
     * @return 202012
     */
    public static String addMonth(String orgMonth, int n) {
        int year = Integer.parseInt(orgMonth.substring(0, 4));
        int month = Integer.parseInt(orgMonth.substring(4, 6));
        month += n;

        if (month > 0 && month < 13) {
            return year + (month < 10 ? "0" + month : month + "");
        } else {
            int y = month / 12;
            year += y;
            month -= y * 12;
            if (month < 1) {
                year--;
                month += 12;
            }
            return year + (month < 10 ? "0" + month : month + "");
        }
    }

    private static String parseDateStr(String dateStr) {
        dateStr = dateStr.trim();
        switch (StringUtils.countMatches(dateStr, ":")) {
            case 0:
                dateStr += " 00:00:00";
                break;
            case 1:
                dateStr += ":00";
                break;
            default:
                break;
        }
        return dateStr;
    }
}
