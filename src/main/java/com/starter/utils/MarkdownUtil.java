package com.starter.utils;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class MarkdownUtil {
    public static String generateMarkdownTable(List<LinkedHashMap<String, Object>> dataList) {
        if (dataList == null || dataList.isEmpty() || dataList.get(0) == null) {
            return "\n\n**No Data**";
        }

        // 获取所有键的集合
        Set<String> keys = dataList.get(0).keySet();
        StringBuilder markdown = new StringBuilder();

        // 构建表头
        markdown.append("\n");
        markdown.append("\n");
        markdown.append("| |");
        for (String key : keys) {
            markdown.append(" ").append(key).append(" |");
        }
        markdown.append("\n");

        // 构建分隔线
        markdown.append("| ---- |");
        markdown.append(" ---- |".repeat(keys.size()));
        markdown.append("\n");

        // 构建表格行
        for (int i = 0; i < dataList.size(); i++) {
            Map<String, Object> data = dataList.get(i);
            markdown.append("| ").append(i + 1).append(" |");
            for (String key : keys) {
                Object obj = data.get(key);
                if (obj == null) {
                    markdown.append(" ").append(" ").append(" |");
                } else {
                    if (obj instanceof BigDecimal) {
                        markdown.append(" <div style='text-align:right'>").append(Utils.thousandBitSeparator(obj)).append("</div> |");
                    } else {
                        markdown.append(" ").append(obj).append(" |");
                    }
                }

            }
            markdown.append("\n");
        }
        markdown.append("\n");

        return markdown.toString();
    }

    // 将MARKDOWN文本变成备注样式
    public static String addQuotePrefix(String markdownText) {
        if (markdownText == null || markdownText.isEmpty()) {
            return markdownText;
        }

        // 按行分割文本
        String[] lines = markdownText.split("\n");
        StringBuilder result = new StringBuilder();

        for (String line : lines) {
            // 在每行开头添加 '>' 符号
            result.append("> ").append(line).append("\n");
        }

        // 删除最后一个多余的换行符
        return result.isEmpty() == false ? result.substring(0, result.length() - 1) : result.toString();
    }

}
