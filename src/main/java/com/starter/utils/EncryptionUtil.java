/**
 *
 */
package com.starter.utils;

import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.security.MessageDigest;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

/**
 * 加密
 *
 */
public class EncryptionUtil {
    private static char hexDigits[] = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

    private static Cipher cipherEncrypt = null;
    private static Cipher cipherDecrypt = null;

    static {
        try {
            byte[] keyBytes = "9682400a516ac415".getBytes(StandardCharsets.UTF_8);
            Key key = new SecretKeySpec(keyBytes, "AES");
            cipherEncrypt = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipherEncrypt.init(Cipher.ENCRYPT_MODE, key);

            cipherDecrypt = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipherDecrypt.init(Cipher.DECRYPT_MODE, key);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * md5加密
     *
     * @param password
     * @return
     */
    public static String md5(String password) {
        if (password == null) {
            return null;
        }

        try {
            byte[] temp = password.getBytes();
            MessageDigest digest = MessageDigest.getInstance("MD5");
            digest.update(temp);

            byte[] md = digest.digest();
            int length = md.length;
            char buffer[] = new char[length * 2];
            int k = 0;
            for (int i = 0; i < length; i++) {
                byte byte0 = md[i];
                buffer[k++] = hexDigits[byte0 >>> 4 & 0xf];
                buffer[k++] = hexDigits[byte0 & 0xf];
            }
            return new String(buffer);
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String md5Short6(String password) {
        String md5Long = md5(password);
        if (StringUtils.length(md5Long) == 32) {
            return StringUtils.substring(md5Long, 12, 18);
        } else {
            return md5Long;
        }
    }

    public static String md5Short8(String password) {
        String md5Long = md5(password);
        if (StringUtils.length(md5Long) == 32) {
            return StringUtils.substring(md5Long, 10, 18);
        } else {
            return md5Long;
        }
    }

    public static String md5Short16(String password) {
        String md5Long = md5(password);
        if (StringUtils.length(md5Long) == 32) {
            return StringUtils.substring(md5Long, 6, 22);
        } else {
            return md5Long;
        }
    }

    public static String encryptAES(String message) {
        try {
            return Base64.encodeBase64String(cipherEncrypt.doFinal(message.getBytes()));
        } catch (Exception ignore) {
        }
        return null;
    }

    public static String decryptAES(String message) {
        try {
            return new String(cipherDecrypt.doFinal(Base64.decodeBase64(message)));
        } catch (Exception ignore) {
        }
        return null;
    }
}
