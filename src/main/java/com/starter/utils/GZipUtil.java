package com.starter.utils;

import org.apache.commons.lang3.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

public class GZipUtil {

    private static final String PREFIX = "gzip@";

    /**
     * 将字符串进行gzip压缩, 只压缩1024长度以上的字符串
     */
    public static String compress(String data) {
        if (StringUtils.isEmpty(data) || data.length() <= 1024) {
            return data;
        }

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        GZIPOutputStream gzip;
        try {
            gzip = new GZIPOutputStream(out);
            gzip.write(data.getBytes(StandardCharsets.UTF_8));
            gzip.finish();
            gzip.close();
        } catch (IOException e) {
            return data;
        }
        return PREFIX + Base64.getEncoder().encodeToString(out.toByteArray());
    }

    public static String uncompress(String data) {
        if (StringUtils.isEmpty(data) || StringUtils.startsWith(data, PREFIX) == false) {
            return data;
        }
        data = StringUtils.removeStart(data, PREFIX);
        byte[] decode = Base64.getDecoder().decode(data);
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ByteArrayInputStream in = new ByteArrayInputStream(decode);
        GZIPInputStream gzipStream = null;
        try {
            gzipStream = new GZIPInputStream(in);
            byte[] buffer = new byte[256];
            int n;
            while ((n = gzipStream.read(buffer)) >= 0) {
                out.write(buffer, 0, n);
            }
        } catch (IOException e) {
            return data;
        } finally {
            try {
                out.close();
                if (gzipStream != null) {
                    gzipStream.close();
                }
            } catch (IOException e) {
                System.err.println(e.getMessage());
            }
        }
        return out.toString(StandardCharsets.UTF_8);
    }
}
