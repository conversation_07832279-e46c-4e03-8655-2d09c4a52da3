package com.starter.utils;

import com.adm.system.dao.ISystemDao;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.starter.context.SpringContext;
import com.starter.login.bean.Session;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AuthUtils {
    /*
     * 添加内置查询条件
     *             1. 如果AUTH_DETAILS不为空, 则以AUTH_DETAIL设置为主
     *                menu1A0允许的AUTH_DETAILS有两种情况, 即字符串ADMIN和JSON格式的配置字符串, 如{"ENTITY": ["WPF", "SWD"]}
     *                如果AUTH_DETAILS = ADMIN, 可以查看所有内容
     *                如果AUTH_DETAILS 为JSON格式的配置字符串, 只可以查看配置范围内的信息
     *                如果AUTH_DETAILS 没有按照格式配置, 则查询不到任何结果
     *
     *             2. 如果AUTH_DETAILS为空, 则根据USER_MASTER_DATA中的ENTITY进行自动判断
     *                如果ENTITY in (Supply Chain Planning)时, 等同于ADMIN, 可以查看所有信息
     *                如果ENIITY not in (Supply Chain Planning), 系统会根据USER_MASTER_DATA的ENTITY查询, 如果ENTITY不标准, 则查不出任何结果
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> generateConditions(Session session, String menuCode, String filterKey) {
        JSONArray personalArray = new JSONArray();
        Map<String, Object> parameterMap = new HashMap<>();
        if (session != null) {
            ISystemDao systemDao = SpringContext.getBean(ISystemDao.class);
            Map<String, Object> params = new HashMap<>();
            params.put("userCode", session.getUserid());
            params.put("menuCode", menuCode);
            Map<String, Object> authMap = systemDao.queryAuthDetails(params);
            String authDetails = MapUtils.isEmpty(authMap) ? "" : StringUtils.trim((String) authMap.get("AUTH_DETAILS"));

            // 手工配置在AUTH_DETAILS里面的权限优先级最高
            if (StringUtils.isNotBlank(authDetails)) {
                if (StringUtils.equalsIgnoreCase(authDetails, "ADMIN") == false) {
                    try {
                        JSONObject obj = JSON.parseObject(authDetails);
                        for (String key : obj.keySet()) {
                            List<String> valueObj = (List<String>) obj.get(key);
                            for (String value : valueObj) {
                                personalArray.add(new JSONArray() {{
                                    add(key);
                                    add(value);
                                }});
                            }
                        }

                    } catch (Exception ignore) {
                        // 出错的时候随便赋值一个变量, 保证查不出来信息即可
                        JSONArray noneArray = new JSONArray() {{
                            add("ENTITY");
                            add("non-ENTITY");
                        }};
                        personalArray.add(noneArray);
                    }
                }
            } else if (StringUtils.equalsIgnoreCase(session.getSupervisor(), "Y") == true) {
                // 如果没有配置AUTH_DETAILS, 再判断这个账号是否是Supervisor, 如果是, 也不需要做限制
            } else {
                // 以上两者都不符合, 只能看到自己的ENTITY
                JSONArray entityArray = new JSONArray() {{
                    add("ENTITY");
                    add(session.getEntity());
                }};
                personalArray.add(entityArray);
            }
        }

        if (personalArray.isEmpty() == false) {
            Map<String, List<String>> personalFilterMap = new HashMap<>();
            Map<String, List<String>> personalValueMap = new HashMap<>();

            for (Object subObj : personalArray) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = subArray.getString(0);

                String value = subArray.getString(1);
                String key = Utils.randomStr(8);

                if (Utils.hasInjectionAttack(columnName)) {
                    continue;
                }

                List<String> fl = personalFilterMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                List<String> fv = personalValueMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                fl.add("#{" + key + ",jdbcType=VARCHAR}");
                fv.add(value);
                parameterMap.put(key, value);
            }

            List<String> personalFilterList = new ArrayList<>();

            for (String key : personalFilterMap.keySet()) {
                List<String> fl = personalFilterMap.get(key);
                personalFilterList.add("t." + key + " in (" + StringUtils.join(fl, ",") + ")");
            }

            parameterMap.put("personalFilters", StringUtils.join(personalFilterList, " and "));
        }
        return parameterMap;
    }
}
