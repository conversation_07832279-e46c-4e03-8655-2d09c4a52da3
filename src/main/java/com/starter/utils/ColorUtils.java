package com.starter.utils;

import java.util.ArrayList;
import java.util.List;

public class ColorUtils {
    public static String rgbToHex(int r, int g, int b) {
        return String.format("#%02X%02X%02X", r, g, b);
    }

    public static int[] hexToRgb(String hexStr) {
        if (hexStr != null && hexStr.length() == 7) {
            int[] rgb = new int[3];
            rgb[0] = Integer.valueOf(hexStr.substring(1, 3), 16);
            rgb[1] = Integer.valueOf(hexStr.substring(3, 5), 16);
            rgb[2] = Integer.valueOf(hexStr.substring(5, 7), 16);
            return rgb;
        }
        return null;
    }

    // 计算渐变过渡色
    public static List<String> gradient(String startColor, String endColor, int step) {
        List<String> gradientColorList = new ArrayList<>();
        // 将 hex 转换为rgb
        int[] sColor = hexToRgb(startColor);
        int[] eColor = hexToRgb(endColor);
        step = step - 1;
        if (step == 0) {
            gradientColorList.add(endColor);
            return gradientColorList;
        }

        // 计算R\G\B每一步的差值
        int rStep = (eColor[0] - sColor[0]) / step;
        int gStep = (eColor[1] - sColor[1]) / step;
        int bStep = (eColor[2] - sColor[2]) / step;


        for (var i = 0; i < step; i++) {
            // 计算每一步的hex值
            gradientColorList.add(rgbToHex(rStep * i + sColor[0], gStep * i + sColor[1], bStep * i + sColor[2]));
        }
        gradientColorList.add(endColor);
        return gradientColorList;
    }

    public static List<String> gradientYellow2Red(int step) {
        return gradient("#D7B600", "#A91500", step);
    }

    public static List<String> gradientYellow2Green(int step) {
        return gradient("#D7B600", "#097700", step);
    }
}
