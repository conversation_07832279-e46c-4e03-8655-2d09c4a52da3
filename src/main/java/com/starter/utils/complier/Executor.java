package com.starter.utils.complier;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 主执行函数
 *
 * <AUTHOR>
 *
 */
public class Executor {

	private Map<String, List<Token>> storage = new HashMap<>();

	public String exec(Map<String, Object> map, String expression) {
		List<Token> tokens = storage.get(expression);
		if (tokens == null) {
			try {
				tokens = new Complier().complier(expression);
			} catch (Exception e) {
				tokens = new ArrayList<>();
			}
			storage.put(expression, tokens);
		}

		if (tokens.isEmpty()) {
			return "NaN";
		}

		return new Interpreter().interprete(map, tokens);
	}
}
