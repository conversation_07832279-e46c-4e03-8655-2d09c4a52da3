package com.starter.utils.complier;

import java.util.HashMap;
import java.util.Map;

public enum VariableType {
	VARIABLE("VAL"),
	STRING("CONSTANT"), NUMBER("CONSTANT"),
	QUOTATION("<PERSON><PERSON><PERSON><PERSON>"), BRACKET_LEFT("S<PERSON><PERSON><PERSON>"), BRACKET_RIGHT("SY<PERSON><PERSON>"),
	OPERATOR_SUBTRACT("OPERATOR"), OPERATOR_ADD("OPERATOR"), OPERATOR_MULT("OPERATOR"), OPERATOR_DIVIDE("OPERATOR"),
	COMPARER_EQUAL("COMPARER"), COMPARER_GREATER("COMPARER"), COMPARER_LESS("COMPARER"),
	BITWISE_OR("BITWISE"),BITWISE_AND("BITWISE");

	private String type;

	VariableType(String type) {
		this.type = type;
	}

	public String getType() {
		return type;
	}

	private static Map<String, VariableType> TYPE_MAP = new HashMap<>();
	static {
		TYPE_MAP.put("+", VariableType.OPERATOR_ADD);
		TYPE_MAP.put("-", VariableType.OPERATOR_SUBTRACT);
		TYPE_MAP.put("*", VariableType.OPERATOR_MULT);
		TYPE_MAP.put("/", VariableType.OPERATOR_DIVIDE);
		TYPE_MAP.put("\"", VariableType.QUOTATION);
		TYPE_MAP.put("(", VariableType.BRACKET_LEFT);
		TYPE_MAP.put(")", VariableType.BRACKET_RIGHT);
		TYPE_MAP.put("=", VariableType.COMPARER_EQUAL);
		TYPE_MAP.put(">", VariableType.COMPARER_GREATER);
		TYPE_MAP.put("<", VariableType.COMPARER_LESS);
		TYPE_MAP.put("|", VariableType.BITWISE_OR);
		TYPE_MAP.put("&", VariableType.BITWISE_AND);
	}

	public static VariableType getVariableType(String val) {
		if (isDigit(val)) {
			return VariableType.NUMBER;
		}

		if (isVariable(val)) {
			return VariableType.VARIABLE;
		}

		return TYPE_MAP.get(val);
	}

	private static boolean isVariable(String val) {
		return LexerUtil.isLetter(val.charAt(0));
	}

	private static boolean isDigit(String str) {
		str = str.replace(".", "");

		for (int i = 0; i < str.length(); i++) {
			if (!Character.isDigit(str.charAt(i))) {
				return false;
			}
		}
		return true;
	}
}
