package com.starter.utils.complier;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <pre>
 * 语法检查器
 * 检查语法是否正确
 * 第一个类型不能是OPERATOR
 * OPERATOR不能与OPERATOR相邻
 * VAL不能与 VAL相邻
 * </pre>
 *
 * <AUTHOR>
 *
 */
public class Checker {

	private String codeExp = "";
	private String message = "";

	private static List<Pattern> patterns = new ArrayList<>();

	static {
		String baseExp0 = "^(OPERATOR|COMPARER|BITWISE)"; // 表达式不能以运算符,比较符或位运算符开始
		String baseExp1 = "(OPERATOR OPERATOR|VAL VAL|CONSTANT CONSTANT|VAL CONSTANT|CONSTANT VAL)";// 运算符,变量,常量不能直接在一起
		String baseExp2 = "(OPERATOR COMPARER|COMPARER OPERATOR|BITWISE COMPARER|COMPARER BITWISE|BITWISE OPERATOR|OPERATOR BITWISE)"; // 各个运算符不能在一起
		String baseExp3 = "(BITWISE BITWISE BITWISE|COMPARER COMPARER COMPARER|OPERATOR OPERATOR)"; // 相同运算符不能在一起超过一定数量
		String baseExp4 = "(OPERATOR|COMPARER|BITWISE)$";// 表达式不能以运算符或者比较符结束

		List<String> regExps = new ArrayList<>();
		regExps.add(baseExp0);
		regExps.add(baseExp1);
		regExps.add(baseExp2);
		regExps.add(baseExp3);
		regExps.add(baseExp4);

		for (String reg : regExps) {
			patterns.add(Pattern.compile(reg));
		}
	}

	public boolean check(List<Token> tokens) {
		List<String> codeList = new ArrayList<>();

		for (Token t : tokens) {
			codeList.add(t.getType().getType());
		}

		codeExp = String.join(" ", codeList);

		Matcher matcher;
		for (Pattern p : patterns) {
			matcher = p.matcher(codeExp);

			if (matcher.find()) {
				message = "Syntactic error[" + codeExp + "]";
				return false;
			}
		}

		int m1 = 0; // 判断小括号是否匹配
		for (Token t : tokens) {
			if ("(".equals(t.getValue())) {
				m1++;
			}

			if (")".equals(t.getValue())) {
				m1--;
			}
			if (m1 < 0) {
				break;
			}
		}

		if (m1 == 0) {
			return true;
		} else {
			message = "Syntactic error[bracket does not match]";
			return false;
		}
	}

	public String getMessage() {
		return this.message;
	}
}
