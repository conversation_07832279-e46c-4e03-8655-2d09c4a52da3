package com.starter.utils.complier;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * <pre>
 * 解析器
 * 解析分词器分解出来的词,定义词性
 * </pre>
 *
 * <AUTHOR>
 *
 */
public class Parser {

	private String peak = "";
	private Iterator<String> tokenIterator = null;
	private List<Token> tokens = new ArrayList<>();
	private Boolean isEnd = false;

	public void readNext() {
		this.isEnd = !tokenIterator.hasNext();
		if (this.isEnd == false) {
			peak = tokenIterator.next();
		}
	}

	public void parse(List<String> tokens) throws Exception {
		tokenIterator = tokens.iterator();

		while (this.isEnd == false) {
			this.scan();
		}
	}

	public void scan() throws Exception {
		readNext();
		if (this.isEnd == true || peak.trim().equals("")) {
			return;
		}
		if (VariableType.getVariableType(peak) == VariableType.QUOTATION) {
			readNext();
			if (VariableType.getVariableType(peak) == VariableType.QUOTATION) {
				tokens.add(new Token("", VariableType.STRING));
			} else {
				StringBuffer sb = new StringBuffer();
				do {
					sb.append(peak);
					readNext();
				} while (this.isEnd == false && VariableType.getVariableType(peak) != VariableType.QUOTATION);

				tokens.add(new Token(sb.toString(), VariableType.STRING));
			}
		} else {
			VariableType type = VariableType.getVariableType(peak);
			if (type == null) {
				throw new Exception("unkown symbol " + peak);
			}
			tokens.add(new Token(peak, type));
		}
	}

	public List<Token> getTokens() {
		return tokens;
	}
}
