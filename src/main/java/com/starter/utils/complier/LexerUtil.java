package com.starter.utils.complier;

/**
 * <pre>
 * 分词器工具类
 * </pre>
 *
 * <AUTHOR>
 *
 */
public class LexerUtil {
	/**
	 * letter包括 a-Z _ . []
	 *
	 * @param ch
	 * @return
	 */
	public static boolean isLetter(int ch) {
		return ch == '_' || ch == '.' || ch == '[' || ch == ']' || (ch >= 'a' && ch <= 'z') || (ch >= 'A' && ch <= 'Z');
	}

	public static boolean isDigit(int ch) {
		return ch >= '0' && ch <= '9';
	}

	public static boolean isFunction(int ch) {
		return ch == '$';
	}

	public static boolean isDot(int ch) {
		return ch == 0x2E;
	}

	public static boolean isLetterOrDigit(int ch) {
		return isLetter(ch) || isDigit(ch);
	}

	public static boolean isNotLetterOrDigit(int ch) {
		return !isLetterOrDigit(ch);
	}
}
