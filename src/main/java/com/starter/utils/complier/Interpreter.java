package com.starter.utils.complier;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

/**
 * <pre>
 * 解释器
 * 根据编译后的内容和json对象,得出表达式的值
 * </pre>
 *
 * <AUTHOR>
 *
 */
public class Interpreter {

	/**
	 * <pre>
	 * 1. 计算表达式中所有的乘法
	 * 2. 计算表达式中所有的加法
	 * 3. 计算三元表达式
	 * </pre>
	 *
	 * @param jsonObject
	 * @param tokens
	 * @return
	 */
	public String interprete(Map<String, Object> map, List<Token> tokens) {
		if (tokens == null || tokens.isEmpty()) {
			return "NaN";
		}

		try {
			List<Token> tempTokens = new ArrayList<>();
			for (Token t : tokens) {
				tempTokens.add(new Token(t));
			}

			for (Token t : tempTokens) {
				switch (t.getType()) {
					case VARIABLE -> {
						if (map == null) {
							return "NaN";
						}
						Object value = map.get(t.getValue());
						if (value == null) {
							return "NaN";
						}
						if (isStrictNumeric(value.toString())) {
							t.setType(VariableType.NUMBER);
						} else {
							t.setType(VariableType.STRING);
						}
						t.setInter_value(value);
					}
					case NUMBER, STRING -> t.setInter_value(t.getValue());
					default -> {
					}
				}
			}

			// 找出表达式中最小的括号
			int[] bracket_index = this.getBracketIndex(tempTokens);
			while (bracket_index != null) {
				List<Token> subList = this.splitToken(tempTokens, bracket_index[0], bracket_index[1]);
				List<Token> compiledList = this.handleExpression(subList);
				this.megerList(tempTokens, compiledList, bracket_index[0], bracket_index[1]);
				bracket_index = this.getBracketIndex(tempTokens);
			}
			this.handleExpression(tempTokens);

			if (tempTokens.isEmpty()) {
				return "";
			} else {
				return tempTokens.get(0).getInter_value().toString();
			}
		} catch (Exception e) {
			return "NaN";
		}
	}

	/**
	 * 将两个list合并,根据起始和结束位置
	 *
	 * @param tempTokens
	 * @param compiledList
	 * @param start
	 * @param end
	 */
	private void megerList(List<Token> tempTokens, List<Token> compiledList, int start, int end) {
		int j = 0;
		Token t = null;
		for (int i = start; i <= end; i++) {
			if (j >= compiledList.size()) {
				t = null;
			} else {
				t = compiledList.get(j++);
			}
			tempTokens.set(i, t);
		}
		this.clearTokenList(tempTokens);
	}

	// 计算表达式
	private List<Token> handleExpression(List<Token> tokens) throws Exception {
		int length = 0;
		while (length != tokens.size() && tokens.size() > 1) {
			length = tokens.size();
			tokens = this.handleMultiply(tokens);
			tokens = this.handleConcat(tokens);
			tokens = this.handleCompare(tokens);
			tokens = this.handleBitwise(tokens);
		}
		return tokens;
	}

	/**
	 * 截取list,不包含两侧边界
	 *
	 * @param tokens
	 * @param start
	 * @param end
	 * @return
	 */
	private List<Token> splitToken(List<Token> tokens, int start, int end) {
		List<Token> resultList = new ArrayList<>();
		for (int i = start + 1; i < end; i++) {
			resultList.add(tokens.get(i));
		}
		return resultList;
	}

	/**
	 * 获取list中括号的index
	 *
	 * @param tokens
	 * @return
	 */
	private int[] getBracketIndex(List<Token> tokens) {
		int left = -1;
		int right = -1;
		for (int i = 0; i < tokens.size(); i++) {
			if (tokens.get(i).getType() == VariableType.BRACKET_LEFT) {
				left = i;
			}

			if (right == -1 && tokens.get(i).getType() == VariableType.BRACKET_RIGHT) {
				right = i;
				break;
			}
		}
		if (left == -1 || right == -1) {
			return null;
		}
		return new int[] { left, right };
	}

	private List<Token> handleMultiply(List<Token> tokens) {
		// 如果表达式中还有变量没计算,则不计算乘法/除法
		for (int i = 0; i < tokens.size(); i++) {
			if (tokens.get(i).getType() == VariableType.VARIABLE) {
				return tokens;
			}
		}
		BigDecimal tempDecimal = BigDecimal.ZERO;
		for (int i = 0; i < tokens.size(); i++) {
			if (tokens.get(i).getType() == VariableType.OPERATOR_MULT) {
				if (tokens.get(i - 1).getType() == VariableType.NUMBER || tokens.get(i - 1).getType() == VariableType.STRING) {
					if (tokens.get(i + 1).getType() == VariableType.NUMBER || tokens.get(i + 1).getType() == VariableType.STRING) {
						tempDecimal = new BigDecimal(tokens.get(i - 1).getInter_value().toString()).multiply(new BigDecimal(tokens.get(i + 1).getInter_value().toString()));
						tokens.set(i - 1, null);
						tokens.set(i, null);

						i++;
						tokens.get(i).setInter_value(tempDecimal.toPlainString());
					}
				}
			} else if (tokens.get(i).getType() == VariableType.OPERATOR_DIVIDE) {
				if (tokens.get(i - 1).getType() == VariableType.NUMBER || tokens.get(i - 1).getType() == VariableType.STRING) {
					if (tokens.get(i + 1).getType() == VariableType.NUMBER || tokens.get(i + 1).getType() == VariableType.STRING) {
						BigDecimal divideBy = new BigDecimal(tokens.get(i + 1).getInter_value().toString());
						if (divideBy.compareTo(BigDecimal.ZERO) == 0) {
							tempDecimal = BigDecimal.ZERO;
						} else {
							tempDecimal = new BigDecimal(tokens.get(i - 1).getInter_value().toString()).divide(divideBy, 8, RoundingMode.HALF_DOWN);
						}
						tokens.set(i - 1, null);
						tokens.set(i, null);

						i++;
						tokens.get(i).setInter_value(tempDecimal.toPlainString());
					}
				}
			}
		}

		this.clearTokenList(tokens);
		return tokens;
	}

	// 计算位运算
	private List<Token> handleBitwise(List<Token> tokens) throws Exception {
		// 如果表达式中还有变量没计算,则不计算位运算符
		for (int i = 0; i < tokens.size(); i++) {
			if (tokens.get(i).getType() == VariableType.VARIABLE) {
				return tokens;
			}
		}

		Boolean tempBool;
		for (int i = 0; i < tokens.size(); i++) {
			if (tokens.get(i).getType() == VariableType.BITWISE_AND) {
				if (tokens.get(i + 1).getType() == VariableType.BITWISE_AND) {
					tempBool = Boolean.parseBoolean(tokens.get(i - 1).getInter_value().toString()) && Boolean.parseBoolean(tokens.get(i + 2).getInter_value().toString());
					tokens.set(i - 1, null);
					tokens.set(i, null);
					tokens.set(i + 1, null);
					i += 2;
					tokens.get(i).setInter_value(tempBool.toString());
					tokens.get(i).setType(VariableType.STRING);
				} else {
					throw new Exception("Syntactic error:" + tokens.get(i).getType() + tokens.get(i + 1).getType());
				}
			} else if (tokens.get(i).getType() == VariableType.BITWISE_OR) {
				if (tokens.get(i + 1).getType() == VariableType.BITWISE_OR) {
					tempBool = Boolean.parseBoolean(tokens.get(i - 1).getInter_value().toString()) || Boolean.parseBoolean(tokens.get(i + 2).getInter_value().toString());
					tokens.set(i - 1, null);
					tokens.set(i, null);
					tokens.set(i + 1, null);
					i += 2;
					tokens.get(i).setInter_value(tempBool.toString());
					tokens.get(i).setType(VariableType.STRING);
				} else {
					throw new Exception("Syntactic error:" + tokens.get(i).getType() + tokens.get(i + 1).getType());
				}
			}
		}

		this.clearTokenList(tokens);
		return tokens;
	}

	private List<Token> handleConcat(List<Token> tokens) {
		// 如果表达式中还有变量没计算,则不计算加减法
		for (int i = 0; i < tokens.size(); i++) {
			if (tokens.get(i).getType() == VariableType.VARIABLE) {
				return tokens;
			}
		}
		String temp;
		for (int i = 0; i < tokens.size(); i++) {
			if (tokens.get(i).getType() == VariableType.OPERATOR_ADD) {
				if (tokens.get(i - 1).getType() == VariableType.NUMBER && tokens.get(i + 1).getType() == VariableType.NUMBER) {
					temp = new BigDecimal(tokens.get(i - 1).getInter_value().toString()).add(new BigDecimal(tokens.get(i + 1).getInter_value().toString())).toPlainString();
					tokens.set(i - 1, null);
					tokens.set(i, null);

					i++;
					tokens.get(i).setInter_value(temp);
					tokens.get(i).setType(VariableType.NUMBER);

				} else if (tokens.get(i - 1).getType() == VariableType.NUMBER || tokens.get(i - 1).getType() == VariableType.STRING) {
					if (tokens.get(i + 1).getType() == VariableType.NUMBER || tokens.get(i + 1).getType() == VariableType.STRING) {
						temp = tokens.get(i - 1).getInter_value() + tokens.get(i + 1).getInter_value().toString();
						tokens.set(i - 1, null);
						tokens.set(i, null);

						i++;
						tokens.get(i).setInter_value(temp);
						tokens.get(i).setType(VariableType.STRING);
					}
				}
			} else if (tokens.get(i).getType() == VariableType.OPERATOR_SUBTRACT) {
				if (tokens.get(i - 1).getType() == VariableType.NUMBER && tokens.get(i + 1).getType() == VariableType.NUMBER) {
					temp = new BigDecimal(tokens.get(i - 1).getInter_value().toString()).subtract(new BigDecimal(tokens.get(i + 1).getInter_value().toString())).toPlainString();
					tokens.set(i - 1, null);
					tokens.set(i, null);

					i++;
					tokens.get(i).setInter_value(temp);
					tokens.get(i).setType(VariableType.NUMBER);

				}
			}
		}

		this.clearTokenList(tokens);
		return tokens;
	}

	private List<Token> handleCompare(List<Token> tokens) {
		// 如果表达式中还有变量或者还有符号,则不计算比较符
		for (int i = 0; i < tokens.size(); i++) {
			if (tokens.get(i).getType() == VariableType.VARIABLE || tokens.get(i).getType().getType().equals("OPERATOR")) {
				return tokens;
			}
		}
		String temp;
		for (int i = 0; i < tokens.size(); i++) {
			if (tokens.get(i).getType() == VariableType.COMPARER_EQUAL) {
				int compare = new BigDecimal(tokens.get(i - 1).getInter_value().toString()).compareTo(new BigDecimal(tokens.get(i + 1).getInter_value().toString()));
				temp = String.valueOf(compare == 0);
				tokens.set(i - 1, null);
				tokens.set(i, null);

				i++;
				tokens.get(i).setInter_value(temp);
				tokens.get(i).setType(VariableType.STRING);
			} else if (tokens.get(i).getType() == VariableType.COMPARER_GREATER) {
				if (tokens.get(i + 1).getType() == VariableType.COMPARER_EQUAL) {
					// 大于等于
					BigDecimal v1 = new BigDecimal(tokens.get(i - 1).getInter_value().toString());
					BigDecimal v2 = new BigDecimal(tokens.get(i + 2).getInter_value().toString());
					int compare = v1.compareTo(v2);
					temp = String.valueOf(compare >= 0);
					tokens.set(i - 1, null);
					tokens.set(i, null);
					tokens.set(i + 1, null);
					i += 2;
					tokens.get(i).setInter_value(temp);
					tokens.get(i).setType(VariableType.STRING);

				} else {
					// 大于
					BigDecimal v1 = new BigDecimal(tokens.get(i - 1).getInter_value().toString());
					BigDecimal v2 = new BigDecimal(tokens.get(i + 1).getInter_value().toString());
					int compare = v1.compareTo(v2);
					temp = String.valueOf(compare > 0);
					tokens.set(i - 1, null);
					tokens.set(i, null);
					i++;
					tokens.get(i).setInter_value(temp);
					tokens.get(i).setType(VariableType.STRING);
				}
			} else if (tokens.get(i).getType() == VariableType.COMPARER_LESS) {
				if (tokens.get(i + 1).getType() == VariableType.COMPARER_EQUAL) {
					// 小于等于
					BigDecimal v1 = new BigDecimal(tokens.get(i - 1).getInter_value().toString());
					BigDecimal v2 = new BigDecimal(tokens.get(i + 2).getInter_value().toString());
					int compare = v1.compareTo(v2);
					temp = String.valueOf(compare <= 0);
					tokens.set(i - 1, null);
					tokens.set(i, null);
					tokens.set(i + 1, null);
					i += 2;
					tokens.get(i).setInter_value(temp);
					tokens.get(i).setType(VariableType.STRING);

				} else if (tokens.get(i + 1).getType() == VariableType.COMPARER_GREATER) {
					// 不等于
					BigDecimal v1 = new BigDecimal(tokens.get(i - 1).getInter_value().toString());
					BigDecimal v2 = new BigDecimal(tokens.get(i + 2).getInter_value().toString());
					int compare = v1.compareTo(v2);
					temp = String.valueOf(compare != 0);
					tokens.set(i - 1, null);
					tokens.set(i, null);
					tokens.set(i + 1, null);
					i += 2;
					tokens.get(i).setInter_value(temp);
					tokens.get(i).setType(VariableType.STRING);
				} else {
					// 小于
					BigDecimal v1 = new BigDecimal(tokens.get(i - 1).getInter_value().toString());
					BigDecimal v2 = new BigDecimal(tokens.get(i + 1).getInter_value().toString());
					int compare = v1.compareTo(v2);
					temp = String.valueOf(compare < 0);
					tokens.set(i - 1, null);
					tokens.set(i, null);
					i++;
					tokens.get(i).setInter_value(temp);
					tokens.get(i).setType(VariableType.STRING);
				}
			}
		}

		this.clearTokenList(tokens);
		return tokens;
	}

	/**
	 * 清除List中的null元素
	 *
	 * @param tokens
	 */
	private void clearTokenList(List<Token> tokens) {
		Iterator<Token> it = tokens.iterator();
		while (it.hasNext()) {
			if (it.next() == null) {
				it.remove();
			}
		}
	}

	/**
	 * 除小数点和起始负号外所有非数字字符均为非法
	 *
	 * @param str
	 * @return
	 */
	public boolean isStrictNumeric(String str) {
		if (str == null || "".equals(str.trim())) {
			return false;
		}

		str = StringUtils.removeStart(str, "-");
		str = StringUtils.replaceOnce(str, ".", "");

		for (int i = 0; i < str.length(); i++) {
			if (!Character.isDigit(str.charAt(i))) {
				return false;
			}
		}
		return true;
	}
}
