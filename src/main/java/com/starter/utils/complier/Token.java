package com.starter.utils.complier;

public class Token {
	private Object inter_value;
	private String value;
	private VariableType type;

	public Token() {

	}

	public Token(Token t) {
		this.value = t.getValue();
		this.type = t.getType();
	}

	public Token(String value, VariableType type) {
		this.value = value;
		this.type = type;
	}

	public Object getInter_value() {
		return inter_value == null ? "" : inter_value;
	}

	public void setInter_value(Object inter_value) {
		this.inter_value = inter_value;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public VariableType getType() {
		return type;
	}

	public void setType(VariableType type) {
		this.type = type;
	}

	public String toString() {
		return inter_value == null ? value : inter_value.toString();
	}
}
