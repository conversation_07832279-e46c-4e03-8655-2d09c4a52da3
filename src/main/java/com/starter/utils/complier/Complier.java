package com.starter.utils.complier;

import java.util.List;

public class Complier {

	public List<Token> complier(String code) throws Exception {
		// 分词
		Lexer lexer = new Lexer();
		lexer.compiler(code);

		// 属性解析
		Parser parser = new Parser();
		parser.parse(lexer.getTokens());

		// 语法检查
		Checker checker = new Checker();

		if (!checker.check(parser.getTokens())) {
			throw new Exception(checker.getMessage());
		}

		return parser.getTokens();
	}
}
