package com.starter.utils.complier;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.LinkedList;
import java.util.List;

/**
 * <pre>
 * 分词器
 * 用于把表达式解析为单独字符
 * </pre>
 *
 * <AUTHOR>
 *
 */
public class Lexer {
	// 下一个读入字符
	char peek = ' ';
	// token序列
	private List<String> tokens = new LinkedList<String>();
	// 读取文件变量
	InputStream stream = null;
	// 保存当前是否读取到了文件的结尾
	private Boolean isEnd = false;

	public void compiler(String code) throws Exception {
		code = code.replace("'", "\"");
		stream = new ByteArrayInputStream(code.getBytes());

		readNext();
		while (this.isEnd == false) {
			this.scan();
		}
	}

	public void readNext() throws IOException {
		peek = (char) stream.read();
		if ((int) peek == 0xffff) {
			this.isEnd = true;
		}
	}

	public void scan() throws IOException {
		String result = "";
		if (LexerUtil.isDigit(peek)) {
			result = this.parseDigit();
		} else if (LexerUtil.isLetter(peek)) {
			result = this.parseLetter();
		} else if (LexerUtil.isFunction(peek)) {
			result = this.parseFunction();
		} else {
			result = this.parseChars();
		}
		if (result.length() > 0) {
			tokens.add(result);
		}
	}

	private String parseChars() throws IOException {
		StringBuffer sb = new StringBuffer();
		sb.append(peek);
		readNext();
		return sb.toString();
	}

	private String parseFunction() throws IOException {
		StringBuffer sb = new StringBuffer();
		sb.append(peek);
		readNext();
		while (LexerUtil.isLetter(peek)) {
			sb.append(peek);
			readNext();
		}

		return sb.toString();
	}

	private String parseLetter() throws IOException {
		StringBuffer sb = new StringBuffer();
		while (LexerUtil.isLetterOrDigit(peek) || LexerUtil.isDot(peek)) {
			sb.append(peek);
			readNext();
		}

		return sb.toString();
	}

	private String parseDigit() throws IOException {
		StringBuffer sb = new StringBuffer();
		sb.append(peek);
		readNext();
		boolean hasDot = false;
		while (LexerUtil.isDigit(peek) || LexerUtil.isDot(peek)) {
			if (LexerUtil.isDot(peek) && hasDot == false) {
				hasDot = true;
			} else {
				sb.append(peek);
				readNext();
			}
		}

		return sb.toString();
	}

	public List<String> getTokens() {
		return tokens;
	}
}
