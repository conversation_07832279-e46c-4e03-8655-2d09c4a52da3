package com.starter.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class MathUtil {

	/**
	 * |o1 - o2| / o2 * 100
	 *
	 * @param o1
	 * @param o2
	 * @param fix
	 * @return
	 */
	public static double indexDivideAsPersent(Object o1, Object o2, int fix) {
		if (o1 == null || o2 == null) {
			return 0;
		}
		try {
			double d1 = Double.valueOf(o1.toString());
			double d2 = Double.valueOf(o2.toString());

			if (d1 == 0 || d2 == 0) {
				return 0;
			} else {
				BigDecimal bd = new BigDecimal(Math.abs(d1 - d2) / d2 * 100);
				return bd.setScale(fix, RoundingMode.HALF_DOWN).doubleValue();
			}
		} catch (Exception e) {
			return 0;
		}

	}

	/**
	 * o1 / o2
	 *
	 * @param o1
	 * @param o2
	 * @param fix
	 * @return
	 */
	public static double divide(Object o1, Object o2, int fix) {
		if (o1 == null || o2 == null) {
			return 0;
		}
		try {
			double d1 = Double.valueOf(o1.toString());
			double d2 = Double.valueOf(o2.toString());

			if (d1 == 0) {
				return 0;
			} else {
				BigDecimal bd = new BigDecimal(d1 / d2);
				return bd.setScale(fix, RoundingMode.HALF_DOWN).doubleValue();
			}
		} catch (Exception e) {
			return 0;
		}
	}

	/**
	 * o1 - o2
	 *
	 * @param o1
	 * @param o2
	 * @param fix
	 * @return
	 */
	public static double minus(Object o1, Object o2, int fix) {
		if (o1 == null || o2 == null) {
			return 0;
		}
		try {
			double d1 = Double.valueOf(o1.toString());
			double d2 = Double.valueOf(o2.toString());

			if (d1 == 0) {
				return 0;
			} else {
				BigDecimal bd = new BigDecimal(d1 - d2);
				return bd.setScale(fix, RoundingMode.HALF_DOWN).doubleValue();
			}
		} catch (Exception e) {
			return 0;
		}
	}

	/**
	 * o1 / o2 * 100
	 *
	 * @param o1
	 * @param o2
	 * @param fix
	 * @return
	 */
	public static double divideAsPersent(Object o1, Object o2, int fix) {
		if (o1 == null || o2 == null) {
			return 0;
		}

		try {
			double d1 = Double.valueOf(o1.toString());
			double d2 = Double.valueOf(o2.toString());

			if (d1 == 0) {
				return 0;
			} else {
				BigDecimal bd = new BigDecimal(d1 / d2 * 100);
				return bd.setScale(fix, RoundingMode.HALF_DOWN).doubleValue();
			}
		} catch (Exception e) {
			return 0;
		}
	}
}
