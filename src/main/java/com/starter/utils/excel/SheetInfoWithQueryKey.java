package com.starter.utils.excel;

import com.starter.context.SpringContext;
import org.mybatis.spring.SqlSessionTemplate;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

public class SheetInfoWithQueryKey extends ISheetInfo {
    private String sheetName;
    private String queryKey;
    private Map<String, Object> parameterMap = new HashMap<>();
    private Map<String, List<String>> validationMap = new HashMap<>();

    private final List<String> headers = new ArrayList<>();

    public String getSheetName() {
        return sheetName;
    }

    public void setSheetName(String sheetName) {
        this.sheetName = sheetName;
    }

    public String getQueryKey() {
        return queryKey;
    }

    public void setQueryKey(String queryKey) {
        this.queryKey = queryKey;
    }

    public Map<String, Object> getParameterMap() {
        return parameterMap;
    }

    public void setParameterMap(Map<String, Object> parameterMap) {
        this.parameterMap = parameterMap;
    }

    public Map<String, List<String>> getValidationMap() {
        if (validationMap == null) {
            return new HashMap<>();
        }
        return validationMap;
    }

    public void setValidationMap(Map<String, List<String>> validationMap) {
        this.validationMap = validationMap;
    }

    public List<String> getHeaders() {
        return headers;
    }

    @SuppressWarnings("unchecked")
    public void readHeaderAndProcessLine(LineContentHandler handler) {
        SqlSessionTemplate sqlSessionTemplate = SpringContext.getBean("sqlSessionTemplate", SqlSessionTemplate.class);
        AtomicInteger index = new AtomicInteger();
        sqlSessionTemplate.select(this.getQueryKey(), this.getParameterMap(), resultContext -> {
            Map<String, Object> data = (Map<String, Object>) resultContext.getResultObject();
            if (data == null) {
                return;
            }

            if (headers.isEmpty() == true) {
                headers.addAll(data.keySet());
                handler.init(this);
                index.getAndIncrement();
            }

            handler.processLine(headers, data, index.getAndIncrement());
        });
        handler.finish(this);
    }
}
