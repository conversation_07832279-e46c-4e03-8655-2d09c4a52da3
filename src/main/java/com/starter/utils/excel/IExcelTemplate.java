package com.starter.utils.excel;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

import jakarta.servlet.http.HttpServletResponse;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.eventusermodel.XSSFSheetXMLHandler.SheetContentsHandler;
import org.apache.poi.xssf.model.StylesTable;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;

import static com.starter.utils.excel.ExcelTemplate.setCellValue;

public abstract class IExcelTemplate {
    public File createAsFile(String filePath, String queryKey, Map<String, Object> parameterMap) {
        SheetInfoWithQueryKey sheetInfo = new SheetInfoWithQueryKey();
        sheetInfo.setSheetName("Sheet1");
        sheetInfo.setQueryKey(queryKey);
        sheetInfo.setParameterMap(parameterMap);
        return this.createAsFile(filePath, sheetInfo);
    }

    public File createAsFile(String filePath, List<LinkedHashMap<String, Object>> dataList) {
        SheetInfoWithData sheetInfo = new SheetInfoWithData();
        sheetInfo.setSheetName("Sheet1");
        sheetInfo.setDataList(dataList);
        return this.createAsFile(filePath, sheetInfo);
    }

    public File createAsFileSheet(String filePath, List<SheetInfoWithData> sheetInfo,String exceltype) throws IOException {
        File dir = new File(appUploadPath + "application/"+ new SimpleDateFormat("yyyyMMdd").format(new Date()) + "/");
        if (!dir.exists()) {
            dir.mkdirs(); // 创建所有必需的父目录
        }
        if (exceltype.equals("xls")) {
            try (
                    Workbook workbook = new HSSFWorkbook();
                    FileOutputStream outputStream = new FileOutputStream(filePath)) {
                for (SheetInfoWithData sheetsInfo : sheetInfo) {
                    String sheetName = sheetsInfo.getSheetName();
                    List<LinkedHashMap<String, Object>> dataList = sheetsInfo.getDataList();
                    writeDataToSheet(workbook, sheetName, dataList);
                }
                workbook.write(outputStream);
                return new File(filePath);
            }
        } else {
            try (
                    Workbook workbook = new XSSFWorkbook();
                    FileOutputStream outputStream = new FileOutputStream(filePath)) {
                for (SheetInfoWithData sheetsInfo : sheetInfo) {
                    String sheetName = sheetsInfo.getSheetName();
                    List<LinkedHashMap<String, Object>> dataList = sheetsInfo.getDataList();
                    writeDataToSheet(workbook, sheetName, dataList);
                }
                workbook.write(outputStream);
                return new File(filePath);
            }
        }
    }

    public void createfile(HttpServletResponse response,String filePath, String fileName) throws IOException {
        this.createEmailFile(response,filePath,fileName);
    }

    public void create(HttpServletResponse response, String queryKey, Map<String, Object> parameterMap) {
        this.create(response, System.currentTimeMillis() + ".xlsx", queryKey, parameterMap);
    }

    public void create(HttpServletResponse response, String fileName, String queryKey, Map<String, Object> parameterMap) {
        SheetInfoWithQueryKey sheetInfo = new SheetInfoWithQueryKey();
        sheetInfo.setSheetName("Sheet1");
        sheetInfo.setQueryKey(queryKey);
        sheetInfo.setParameterMap(parameterMap);
        this.create(response, fileName, sheetInfo);
    }

    public void createCSV(HttpServletResponse response, String fileName, String queryKey, Map<String, Object> parameterMap) {
        SheetInfoWithQueryKey sheetInfo = new SheetInfoWithQueryKey();
        sheetInfo.setQueryKey(queryKey);
        sheetInfo.setParameterMap(parameterMap);
        this.createCSV(response, fileName, sheetInfo);
    }

    protected abstract void createCSV(HttpServletResponse response, String fileName, ISheetInfo sheetInfo);

    public void create(HttpServletResponse response, String fileName, String queryKey, Map<String, Object> parameterMap, Map<String, List<String>> validationMap) {
        SheetInfoWithQueryKey sheetInfo = new SheetInfoWithQueryKey();
        sheetInfo.setSheetName("Sheet1");
        sheetInfo.setQueryKey(queryKey);
        sheetInfo.setParameterMap(parameterMap);
        sheetInfo.setValidationMap(validationMap);
        this.create(response, fileName, sheetInfo);
    }

    public void create(HttpServletResponse response, List<LinkedHashMap<String, Object>> dataList) {
        this.create(response, System.currentTimeMillis() + ".xlsx", dataList);
    }

    public void create(HttpServletResponse response, String fileName, List<LinkedHashMap<String, Object>> dataList) {
        SheetInfoWithData sheetInfo = new SheetInfoWithData();
        sheetInfo.setSheetName("Sheet1");
        sheetInfo.setDataList(dataList);
        this.create(response, fileName, sheetInfo);
    }

    private void writeDataToSheet(Workbook workbook, String sheetName, List<LinkedHashMap<String, Object>> dataList) {
        Sheet sheet = workbook.createSheet(sheetName);

        // 检查dataList是否为空或只有列头
        if (dataList != null) {
            int rowCount = dataList.size();
            // 从dataList中获取列头
            Map<String, Object> headerMap = rowCount > 0 ? dataList.get(0) : null;
            Row headerRow = sheet.createRow(0);

            // 将列头名称存储在一个列表中，以便按顺序引用
            List<String> headerList = new ArrayList<>(headerMap.keySet());

            // 为每个列头创建一个单元格，并按顺序设置列头名称
            for (int colIndex = 0; colIndex < headerList.size(); colIndex++) {
                String headerName = headerList.get(colIndex);
                Cell headerCell = headerRow.createCell(colIndex);
                headerCell.setCellValue(headerName);
            }

            // 写入数据行
            for (int rowIndex = 1; rowIndex < rowCount+1; rowIndex++) {
                Map<String, Object> row = dataList.get(rowIndex-1);
                Row sheetRow = sheet.createRow(rowIndex);

                // 遍历每一行的每个单元格
                for (int colIndex = 0; colIndex < headerList.size(); colIndex++) {
                    String headerName = headerList.get(colIndex);
                    Cell cell = sheetRow.createCell(colIndex);
                    Object value = row.get(headerName);
                    if (value != null && !value.equals("")) {
                        setCellValue(cell, value);
                    }
                }
            }
        }
    }


    public abstract void create(HttpServletResponse response, String fileName, ISheetInfo... sheetInfos);

    public abstract void createEmailFile(HttpServletResponse response, String filePath, String fileName);

    public abstract File createAsFile(String filePath, ISheetInfo... sheetInfos);

    public abstract void read(File file, int sheetIndex, SheetContentsHandler handler, StylesTable style);

    public void read(File file, int sheetIndex, SheetContentsHandler handler) {
        this.read(file, sheetIndex, handler, null);
    }
    @Value("${file.upload.path}")
    private String appUploadPath;

    public void read(String path, int sheetIndex, SheetContentsHandler handler) {
        this.read(new File(path), sheetIndex, handler);
    }
}
