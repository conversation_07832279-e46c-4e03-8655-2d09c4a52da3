package com.starter.utils.excel;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

public class SheetInfoWithData extends ISheetInfo {
    private String sheetName;
    private List<LinkedHashMap<String, Object>> dataList;

    private final List<String> headers = new ArrayList<>();

    private Map<String, List<String>> validationMap = new HashMap<>();

    public String getSheetName() {
        return sheetName;
    }

    @Override
    public void readHeaderAndProcessLine(LineContentHandler handler) {
        AtomicInteger index = new AtomicInteger();
        this.getDataList().forEach(data -> {
            if (data == null) {
                return;
            }

            if (headers.isEmpty() == true) {
                headers.addAll(data.keySet());
                handler.init(this);
                index.getAndIncrement();
            }

            boolean emptyRow = true;
            for (String header : headers) {
                Object v = data.get(header);
                if (v != null) {
                    emptyRow = false;
                    break;
                }
            }

            if (emptyRow == false) {
                handler.processLine(headers, data, index.getAndIncrement());
            }
        });
        handler.finish(this);
    }

    @Override
    public List<String> getHeaders() {
        return headers;
    }

    public void setSheetName(String sheetName) {
        this.sheetName = sheetName;
    }

    public List<LinkedHashMap<String, Object>> getDataList() {
        return dataList;
    }

    public void setDataList(List<LinkedHashMap<String, Object>> dataList) {
        this.dataList = dataList;
    }

    public Map<String, List<String>> getValidationMap() {
        if (validationMap == null) {
            return new HashMap<>();
        }
        return validationMap;
    }

    public void setValidationMap(Map<String, List<String>> validationMap) {
        this.validationMap = validationMap;
    }
}
