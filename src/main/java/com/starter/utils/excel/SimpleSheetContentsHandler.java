package com.starter.utils.excel;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

import com.starter.utils.Utils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.eventusermodel.XSSFSheetXMLHandler.SheetContentsHandler;
import org.apache.poi.xssf.usermodel.XSSFComment;

public class SimpleSheetContentsHandler implements SheetContentsHandler {
    protected List<String> row = new LinkedList<>();
    private List<String> rowRef = new LinkedList<>();
    private List<String> rowRefTemp = new LinkedList<>();
    private boolean read = false; // 是否已经读了第一行
    protected int maxLength = 255;

    protected SimpleSheetContentsHandler() {
        init();
    }

    @Override
    public void startRow(int rowNum) {
        rowRefTemp = new LinkedList<>(rowRef);
        if (read == false) {
            row = new LinkedList<>();
        } else {
            row = new LinkedList<>(Arrays.asList(new String[rowRef.size()]));
        }
    }

    @Override
    public void endRow(int rowNum) {
        if (read == false) {
            read = true;
        }

        boolean allEmptyRow = true;
        for (String s : row) {
            if (StringUtils.isEmpty(s) == false) {
                allEmptyRow = false;
                break;
            }
        }
        if (allEmptyRow == false) {
            this.handleRow(rowNum);
        }
    }

    public void handleRow(int rowNum) {

    }

    @Override
    public void cell(String cellReference, String formattedValue, XSSFComment comment) {
        String columnRef = this.getExcelColumnReferce(cellReference);
        if (read == false) {
            rowRef.add(columnRef);
            row.add(this.getValue(formattedValue));
        } else {
            int index = rowRefTemp.indexOf(columnRef);
            if (index >= 0) {
                row.set(index, this.getValue(formattedValue));
            }
        }
    }

    @Override
    public void headerFooter(String text, boolean isHeader, String tagName) {

    }

    public void init() {

    }

    private String getValue(String org) {
        if (org == null) {
            return null;
        }
        org = Utils.clearStr(org);
        if (org.length() > maxLength) {
            return org.substring(0, maxLength);
        }
        return org;
    }

    private String getExcelColumnReferce(String cellReference) {
        return cellReference.replaceAll("[^a-z^A-Z]", "");
    }
}
