package com.starter.utils.excel;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Iterator;

import javax.xml.parsers.ParserConfigurationException;

import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.openxml4j.opc.PackageAccess;
import org.apache.poi.util.XMLHelper;
import org.apache.poi.xssf.eventusermodel.ReadOnlySharedStringsTable;
import org.apache.poi.xssf.eventusermodel.XSSFReader;
import org.apache.poi.xssf.eventusermodel.XSSFSheetXMLHandler;
import org.apache.poi.xssf.eventusermodel.XSSFSheetXMLHandler.SheetContentsHandler;
import org.apache.poi.xssf.model.StylesTable;
import org.xml.sax.InputSource;
import org.xml.sax.SAXException;
import org.xml.sax.XMLReader;

public class ExcelEventParser {

	private File file;
	private int sheetIndex;
	private SheetContentsHandler handler;
	private StylesTable settingStyle = null;

	public ExcelEventParser(File file, int sheetIndex) {
		this.file = file;
		this.sheetIndex = sheetIndex;
	}

	public ExcelEventParser setHandler(SheetContentsHandler handler) {
		this.handler = handler;
		return this;
	}

	public ExcelEventParser setSettingStyle(StylesTable settingStyle) {
		this.settingStyle = settingStyle;
		return this;
	}

	public void parse() {
		OPCPackage pkg = null;
		InputStream sheetInputStream = null;

		try {
			pkg = OPCPackage.open(file, PackageAccess.READ);
			XSSFReader xssfReader = new XSSFReader(pkg);

			if (settingStyle == null) {
				settingStyle = xssfReader.getStylesTable();
			}

			ReadOnlySharedStringsTable strings = new ReadOnlySharedStringsTable(pkg);
			Iterator<InputStream> iterator = xssfReader.getSheetsData();

			for (int i = 0; i < sheetIndex; i++) {
				sheetInputStream = iterator.next();
			}

			processSheet(settingStyle, strings, sheetInputStream);
		} catch (Exception e) {
			throw new RuntimeException(e.getMessage(), e);
		} finally {
			if (sheetInputStream != null) {
				try {
					sheetInputStream.close();
				} catch (IOException e) {
					throw new RuntimeException(e.getMessage(), e);
				}
			}
			if (pkg != null) {
				try {
					pkg.close();
				} catch (IOException e) {
					throw new RuntimeException(e.getMessage(), e);
				}
			}
		}
	}

	private void processSheet(StylesTable styles, ReadOnlySharedStringsTable strings, InputStream sheetInputStream) throws SAXException, ParserConfigurationException, IOException {
		XMLReader sheetParser = XMLHelper.newXMLReader();

		if (handler != null) {
			sheetParser.setContentHandler(new XSSFSheetXMLHandler(styles, strings, handler, false));
		} else {
			sheetParser.setContentHandler(new XSSFSheetXMLHandler(styles, strings, new SimpleSheetContentsHandler(), false));
		}

		sheetParser.parse(new InputSource(sheetInputStream));
	}
}
