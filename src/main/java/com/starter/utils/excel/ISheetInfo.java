package com.starter.utils.excel;

import java.util.List;
import java.util.Map;

public abstract class ISheetInfo {
    abstract Map<String, List<String>> getValidationMap();

    abstract String getSheetName();

    abstract void readHeaderAndProcessLine(LineContentHandler handler);

    abstract List<String> getHeaders();

    boolean hasValidation() {
        return this.getValidationMap().isEmpty() == false;
    }
}
