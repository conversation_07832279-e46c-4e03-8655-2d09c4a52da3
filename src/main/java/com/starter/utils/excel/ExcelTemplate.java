package com.starter.utils.excel;

import com.adm.system.dao.IDataGridDao;
import com.alibaba.fastjson.JSON;
import com.starter.context.configuration.MqttConfiguration;
import com.starter.context.servlet.UserContextHolder;
import com.starter.utils.GZipUtil;
import com.starter.utils.Utils;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.SpreadsheetVersion;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.eventusermodel.XSSFSheetXMLHandler.SheetContentsHandler;
import org.apache.poi.xssf.model.StylesTable;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.io.*;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

@Component
@Scope("prototype")
public class ExcelTemplate extends IExcelTemplate {

    private final static String SHEET_PASSWORD = "c79f4ca8-043e-2165-10d7-7fb17ef8e62b"; // sheet 密码

    private final static SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy/MM/dd");

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private int totalRows = 0;

    @Resource
    private IDataGridDao dataGridDao;

    /**
     * 根据sheetInfo生成CSV并写入response
     *
     * @param response  需要写入的response
     * @param fileName  文件名
     * @param sheetInfo sheetInfo
     */
    @Override
    public void createCSV(HttpServletResponse response, String fileName, ISheetInfo sheetInfo) {
        initExcelResponseHeader(response, fileName);

        // 在本地生成临时CSV文件
        File file = null;
        try {
            file = this.createCSV(sheetInfo);
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (file != null) {
            // 写入response
            try (FileInputStream in = new FileInputStream(file)) {
                byte[] buf = new byte[4096];
                int read;
                while (((read = in.read(buf)) != -1)) {
                    response.getOutputStream().write(buf, 0, read);
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                if (file.delete() == false) {
                    file.deleteOnExit();
                    log.error(file.getAbsolutePath() + " delete failed!");
                }
            }
        } else {
            log.error("下载CSV失败, 无法生成本地临时文件!");
        }
    }

    /**
     * 根据sheetInfo生成一个CSV文件对象
     *
     * @param sheetInfo sheetInfo
     * @return 文件对象
     */
    private File createCSV(ISheetInfo sheetInfo) {
        File temp = null;
        try {
            temp = File.createTempFile("csvtemp_", ".csv");
            final FileWriter fileWriter = new FileWriter(temp);

            try (fileWriter; BufferedWriter bufferedWriter = new BufferedWriter(fileWriter)) {
                sheetInfo.readHeaderAndProcessLine(new LineContentHandler() {

                    @Override
                    public void init(ISheetInfo sheetInfo) {
                        try {
                            bufferedWriter.write(StringUtils.join(sheetInfo.getHeaders(), ","));
                            bufferedWriter.newLine();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }

                    @Override
                    public void processLine(List<String> headers, Map<String, Object> data, int index) {
                        List<String> rowData = new ArrayList<>();
                        for (String header : headers) {
                            Object v = data.get(header);
                            if (v == null) {
                                rowData.add("");
                            } else {
                                rowData.add(StringUtils.replace(v.toString(), ",", "&#44;"));
                            }
                        }

                        try {
                            bufferedWriter.write(StringUtils.join(rowData, ","));
                            bufferedWriter.newLine();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                });
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        return temp;
    }

    /**
     * 根据sheetInfo生成excel并写入response
     *
     * @param response   http response
     * @param fileName   要保存的文件名
     * @param sheetInfos sheet配置
     */
    public void create(HttpServletResponse response, String fileName, ISheetInfo... sheetInfos) {
        // 初始化响应头
        initExcelResponseHeader(response, fileName);
        // 生成excel
        SXSSFWorkbook book = this.getWorkbooks(sheetInfos);
        // 写入response
        writeBookToResponse(book, response);
    }

    public void createEmailFile(HttpServletResponse response, String filePath, String fileName)  {
        // 初始化响应头
        initExcelResponseHeader(response, fileName);

        try {
            // 读取Excel文件
            InputStream inputStream = new FileInputStream(filePath);
            // 写入response
            writeExcelToResponse(inputStream, response);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private void initExcelResponseHeaderfile(HttpServletResponse response, String fileName) {
        // 设置响应头
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
    }

    private void writeExcelToResponse(InputStream inputStream, HttpServletResponse response) throws IOException {
        // 获取响应的输出流
        OutputStream outputStream = response.getOutputStream();

        // 将文件内容写入输出流
        byte[] buffer = new byte[4096];
        int bytesRead;
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            outputStream.write(buffer, 0, bytesRead);
        }

        // 刷新并关闭流
        outputStream.flush();
        outputStream.close();
        inputStream.close();
    }


    /**
     * 根据sheetInfos 生成excel文件
     *
     * @param filePath   excel文件路径
     * @param sheetInfos sheetInfos
     * @return 文件对象
     */
    @Override
    public File createAsFile(String filePath, ISheetInfo... sheetInfos) {
        // 声明变量
        SXSSFWorkbook book = this.getWorkbooks(sheetInfos);
        File file = null;

        try {
            if (filePath == null) {
                file = File.createTempFile(Utils.randomStr(8), Utils.randomStr(4));
            } else {
                file = new File(filePath);
            }
            File parentFile = file.getParentFile();
            if (parentFile.exists() == false) {
                parentFile.mkdirs();
            }
            if (file.exists() == false) {
                file.createNewFile();
            }
            // 释放资源
            BufferedOutputStream os = new BufferedOutputStream(new FileOutputStream(file));
            book.write(os);
            os.flush();
            os.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            book.dispose(); // 删除本地磁盘的临时文件
        }
        return file;
    }

    /**
     * 根据sheetInfos生成excel对象
     *
     * @param sheetInfos sheetInfos
     * @return excel对象
     */
    private SXSSFWorkbook getWorkbooks(ISheetInfo... sheetInfos) {
        try {
            // 根据配置先从模板里面复制出几个unlocked的sheet
            InputStream is = ExcelTemplate.class.getClassLoader().getResourceAsStream("files/poi_export_template.xlsx");
            assert is != null;
            XSSFWorkbook org = new XSSFWorkbook(is);
            for (ISheetInfo sheetInfo : sheetInfos) {
                org.cloneSheet(0, sheetInfo.getSheetName());
            }
            org.removeSheetAt(0); // 复制结束后, 将最开始的sheet删掉

            // 然后再用这个sheet作为模板, 导出数据
            SXSSFWorkbook book = new SXSSFWorkbook(org, 128);
            AtomicInteger total = new AtomicInteger();
            String downloadTopic = UserContextHolder.getDownloadTopic();

            // 遍历, 生成多个sheet
            for (int i = 0; i < sheetInfos.length; i++) {
                ISheetInfo sheetInfo = sheetInfos[i];
                SXSSFSheet sheet = ExcelTemplate.createProtectdSheet(book, sheetInfo);
                final int sheetIndex = i;

                sheetInfo.readHeaderAndProcessLine(new LineContentHandler() {
                    @Override
                    public void init(ISheetInfo sheetInfo) {
                        Row rowHeader = sheet.createRow(0);
                        for (int i = 0; i < sheetInfo.getHeaders().size(); i++) {
                            Cell cellHeader = rowHeader.createCell(i);
                            cellHeader.setCellValue(sheetInfo.getHeaders().get(i));
                        }
                        // 只有当网页开启指定列下载, 并且当用户全量下载时, 将表头与下载链接绑定, 方便下次可以让用户指定表头
                        // 如果有多个sheet, 则只识别第一个sheet
                        if (UserContextHolder.getSpecifyColumnHolder() == Boolean.FALSE && sheetIndex == 0) {
                            String headerJson = GZipUtil.compress(JSON.toJSONString(sheetInfo.getHeaders()));
                            dataGridDao.updateDownloadHeader(UserContextHolder.getMethodUrlHolder(), headerJson);
                        }
                    }

                    @Override
                    public void processLine(List<String> headers, Map<String, Object> data, int index) {
                        // 写入数据
                        total.getAndIncrement();
                        Row rowBody = sheet.createRow(index);

                        for (int j = 0; j < headers.size(); j++) {
                            ExcelTemplate.setCellValue(rowBody.createCell(j), data.get(headers.get(j)));
                        }

                        // 当下载请求带有download topic的时候, 随机行数回传一次进度
                        int current = total.get();
                        if (StringUtils.isNotBlank(downloadTopic) == true && (current % 2136 == 0 || Math.random() < 0.001)) {
                            MqttConfiguration.publishMessage(downloadTopic, "Fetching data, " + Utils.thousandBitSeparator(String.valueOf(current)) + " rows...");
                        }
                    }

                    @Override
                    public void finish(ISheetInfo sheetInfo) {
                        // 创建excel校验
                        ExcelTemplate.createColumnValidation(book, sheet, sheetInfo);
                        if (StringUtils.isNotBlank(downloadTopic) == true) {
                            totalRows = total.get();
                            MqttConfiguration.publishMessage(downloadTopic, "Fetched " + Utils.thousandBitSeparator(total.get()) + " rows, generating execl file...");
                        }
                    }
                });
            }
            return book;
        } catch (Exception e) {
            SXSSFWorkbook book = new SXSSFWorkbook(128);
            ExcelTemplate.createErrorSheet(book, Utils.getStackTrace(e));
            e.printStackTrace();
            return book;
        }
    }

    public int getTotalRows() {
        return totalRows;
    }

    public void setTotalRows(int totalRows) {
        this.totalRows = totalRows;
    }

    // region private functions

    /**
     * 初始化响应头
     *
     * @param response 需要操作的response
     * @param fileName 将文件名写入response
     */
    public static void initExcelResponseHeader(HttpServletResponse response, String fileName) {
        try {
            // 初始化response
            response.resetBuffer();
            response.setHeader("Content-disposition", "attachment;filename=" + fileName);
            response.setContentType("APPLICATION/OCTET-STREAM;charset=UTF-8");// 设置类型
            response.setHeader("Cache-Control", "no-cache");// 设置头
            response.addHeader("filename", fileName);// 设置头
            response.addHeader("Access-Control-Expose-Headers", "filename");// 设置头
            response.setDateHeader("Expires", 0);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 将excel对象写入response
     *
     * @param book     需要写入的excel
     * @param response response
     */
    public static void writeBookToResponse(SXSSFWorkbook book, HttpServletResponse response) {
        if (book == null) {
            return;
        }
        // 写入response
        try {
            book.write(response.getOutputStream());
            response.getOutputStream().flush();
            response.getOutputStream().close();
        } catch (Exception e) {
            ExcelTemplate.outputErrorSheet(e, book, response);
        } finally {
            book.dispose();
        }
    }

    private static void outputErrorSheet(Exception e, SXSSFWorkbook book, HttpServletResponse response) {
        ExcelTemplate.createErrorSheet(book, Utils.getStackTrace(e));

        e.printStackTrace();

        try {
            book.write(response.getOutputStream());
            response.getOutputStream().flush();
            response.getOutputStream().close();
        } catch (Exception ignore) {
        }
    }

    /**
     * 为cell赋值, 系统可以自动识别格式, 并且填充单元格
     *
     * @param cellBody 需要被填充的cell
     * @param v        Object格式的值
     */
    public static void setCellValue(Cell cellBody, Object v) {
        if (v == null) {
            cellBody.setCellValue("");
        } else if (v instanceof String) {
            cellBody.setCellValue(StringUtils.substring((String) v, 0, 32765));
        } else if (v instanceof Integer) {
            cellBody.setCellValue((Integer) v);
        } else if (v instanceof Double) {
            cellBody.setCellValue((Double) v);
        } else if (v instanceof Float) {
            cellBody.setCellValue((Float) v);
        } else if (v instanceof Date) {
            try {
                cellBody.setCellValue(DATE_FORMAT.format((Date) v));
            } catch (Exception e) {
                cellBody.setCellValue((Date) v);
            }
        } else if (v instanceof Calendar) {
            cellBody.setCellValue((Calendar) v);
        } else if (v instanceof Boolean) {
            cellBody.setCellValue((Boolean) v);
        } else if (v instanceof Long) {
            cellBody.setCellValue((Long) v);
        } else if (v instanceof BigDecimal) {
            cellBody.setCellValue(((BigDecimal) v).doubleValue());
        } else {
            cellBody.setCellValue(StringUtils.substring(String.valueOf(v), 0, 32765));
        }
    }

    /**
     * 创建一个加密sheet
     *
     * @param book      需要在哪个workbook中创建
     * @param sheetInfo 创建的sheetInfo
     * @return SXSSFSheet 对象
     */
    private static SXSSFSheet createProtectdSheet(SXSSFWorkbook book, ISheetInfo sheetInfo) {
        SXSSFSheet sheet = book.getSheet(sheetInfo.getSheetName());
        if (sheetInfo.hasValidation()) {
            sheet.protectSheet(SHEET_PASSWORD);
            sheet.lockFormatColumns(false);
            sheet.lockDeleteRows(false);
            sheet.lockInsertRows(false);
        }
        return sheet;
    }

    private static void createErrorSheet(SXSSFWorkbook book, String error) {
        SXSSFSheet sheet = book.createSheet("ERROR");
        Row rowHeader = sheet.createRow(0);
        Cell cellHeader = rowHeader.createCell(0);
        cellHeader.setCellValue(error);
    }

    /**
     * 创建一个隐藏sheet, 并且生成公式, 为一个特定列生成下拉菜单
     *
     * @param book      需要操作的workbook
     * @param sheet     需要操作的sheet
     * @param sheetInfo sheetInfo
     */
    private static void createColumnValidation(SXSSFWorkbook book, SXSSFSheet sheet, ISheetInfo sheetInfo) {
        List<String> headers = sheetInfo.getHeaders();
        for (String key : sheetInfo.getValidationMap().keySet()) {
            List<String> dropdownItems = sheetInfo.getValidationMap().get(key);
            int headerIndex = headers.indexOf(key);
            if (dropdownItems != null && headerIndex != -1) {
                // 创建一个隐藏sheet
                String hiddenSheetName = "HIDDEN_" + key;
                SXSSFSheet hiddenSheet = book.createSheet(hiddenSheetName);
                hiddenSheet.protectSheet(SHEET_PASSWORD);
                for (int i = 0; i < dropdownItems.size(); i++) {
                    hiddenSheet.createRow(i).createCell(0).setCellValue(dropdownItems.get(i));
                }
                book.setSheetHidden(book.getSheetIndex(hiddenSheet), true);

                // 定义公式
                Name validationName = book.createName();
                validationName.setNameName(hiddenSheetName);
                validationName.setRefersToFormula(hiddenSheetName + "!$A$1:$A$" + dropdownItems.size());

                DataValidationHelper dataValidationHelper = sheet.getDataValidationHelper();
                DataValidationConstraint dataValidationConstraint = dataValidationHelper.createFormulaListConstraint(hiddenSheetName);

                CellRangeAddressList addressList = new CellRangeAddressList(1, SpreadsheetVersion.EXCEL2007.getLastRowIndex(), headerIndex, headerIndex);
                DataValidation dataValidation = dataValidationHelper.createValidation(dataValidationConstraint, addressList);
                dataValidation.setShowPromptBox(true);
                dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
                dataValidation.setShowErrorBox(true);
                sheet.addValidationData(dataValidation);
            }
        }

    }
    // endregion

    /**
     * 逐行读取Excel
     *
     * @param file       需要读取的文件
     * @param sheetIndex 需要读取的sheet表
     * @param handler    处理方法
     * @param style      style table, 一般使用new StylesTable()
     */
    public void read(File file, int sheetIndex, SheetContentsHandler handler, StylesTable style) {
        new ExcelEventParser(file, sheetIndex).setHandler(handler).setSettingStyle(style).parse();
    }
}
