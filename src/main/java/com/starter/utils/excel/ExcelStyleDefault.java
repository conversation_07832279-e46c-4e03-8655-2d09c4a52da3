package com.starter.utils.excel;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;

public class ExcelStyleDefault extends ExcelStyle {
	public ExcelStyleDefault(Workbook wb) {
		// Border Type binding.
		defaultBorderType = BorderStyle.THIN;
		// NONE = 0;
		// THIN = 1;
		// MEDIUM = 2;
		// DASHED = 3;
		// DOTTED = 4;
		// THICK = 5;
		// DOUBLE = 6;
		defaultBorder = BorderStyle.THIN;

		DataFormat format = wb.createDataFormat();

		// CELL_NORMAL
		normalStyle = wb.createCellStyle();
		Font normalFont = wb.createFont();
		normalFont.setFontName("Consolas");
		normalFont.setFontHeightInPoints((short) 9);
		normalStyle.setFont(normalFont);

		// CELL_TITLE
		titleStyle = wb.createCellStyle();
		Font titleFont = wb.createFont();
		titleFont.setFontName("Consolas");
		titleFont.setColor(IndexedColors.AQUA.getIndex());
		titleFont.setBold(true);
		titleFont.setFontHeightInPoints((short) 9);
		titleStyle.setFont(titleFont);

		// CELL_HEAD
		headStyle = wb.createCellStyle();
		Font headFont = wb.createFont();
		headFont.setFontName("Consolas");
		headFont.setBold(true);
		headFont.setFontHeightInPoints((short) 9);
		headStyle.setFont(headFont);
		headStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
		headStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		headStyle.setAlignment(HorizontalAlignment.CENTER);
		headStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		headStyle.setBorderTop(defaultBorderType);
		headStyle.setBorderBottom(defaultBorderType);
		headStyle.setBorderLeft(defaultBorderType);
		headStyle.setBorderRight(defaultBorderType);

		// CELL_HEAD_UNAVAILABLE
		headUnavailableStyle = wb.createCellStyle();
		Font headUnavailableFont = wb.createFont();
		headUnavailableFont.setFontName("Consolas");
		headUnavailableFont.setFontHeightInPoints((short) 9);
		headUnavailableStyle.setFont(headUnavailableFont);
		headUnavailableStyle.setAlignment(HorizontalAlignment.CENTER);
		headUnavailableStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		headUnavailableStyle.setBorderTop(defaultBorderType);
		headUnavailableStyle.setBorderBottom(defaultBorderType);
		headUnavailableStyle.setBorderLeft(defaultBorderType);
		headUnavailableStyle.setBorderRight(defaultBorderType);

		// CELL_HEAD_REQUIRED
		headRequiredStyle = wb.createCellStyle();
		Font headRequiredFont = wb.createFont();
		headRequiredFont.setFontName("Consolas");
		headRequiredFont.setBold(true);
		headRequiredFont.setFontHeightInPoints((short) 9);
		headRequiredStyle.setFont(headFont);
		headRequiredStyle.setFillForegroundColor(IndexedColors.LIGHT_ORANGE.getIndex());
		headRequiredStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		headRequiredStyle.setAlignment(HorizontalAlignment.CENTER);
		headRequiredStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		headRequiredStyle.setBorderTop(defaultBorderType);
		headRequiredStyle.setBorderBottom(defaultBorderType);
		headRequiredStyle.setBorderLeft(defaultBorderType);
		headRequiredStyle.setBorderRight(defaultBorderType);

		// CELL_HEAD_WARNING
		headWarningStyle = wb.createCellStyle();
		Font headWarningFont = wb.createFont();
		headWarningFont.setFontName("Consolas");
		headWarningFont.setColor(IndexedColors.YELLOW.getIndex());
		headWarningFont.setBold(true);
		headWarningFont.setFontHeightInPoints((short) 9);
		headWarningStyle.setFont(headWarningFont);

		// CELL_HEAD_FORECAST
		headForecastStyle = wb.createCellStyle();
		Font headForecastFont = wb.createFont();
		headForecastFont.setFontName("Consolas");
		headForecastFont.setBold(true);
		headForecastFont.setFontHeightInPoints((short) 9);
		headForecastStyle.setFont(headForecastFont);
		headForecastStyle.setFillForegroundColor(IndexedColors.LIME.getIndex());
		headForecastStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		headForecastStyle.setAlignment(HorizontalAlignment.CENTER);
		headForecastStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		headForecastStyle.setBorderTop(defaultBorderType);
		headForecastStyle.setBorderBottom(defaultBorderType);
		headForecastStyle.setBorderLeft(defaultBorderType);
		headForecastStyle.setBorderRight(defaultBorderType);

		// CELL_HEAD_ACTUAL
		headActualStyle = wb.createCellStyle();
		Font headActualFont = wb.createFont();
		headActualFont.setFontName("Consolas");
		headActualFont.setBold(true);
		headActualFont.setFontHeightInPoints((short) 9);
		headActualStyle.setFont(headActualFont);
		headActualStyle.setFillForegroundColor(IndexedColors.TAN.getIndex());
		headActualStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
		headActualStyle.setAlignment(HorizontalAlignment.CENTER);
		headActualStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		headActualStyle.setBorderTop(defaultBorderType);
		headActualStyle.setBorderBottom(defaultBorderType);
		headActualStyle.setBorderLeft(defaultBorderType);
		headActualStyle.setBorderRight(defaultBorderType);

		// CELL_BODY_CONST_STYLE
		bodyConstStyle = wb.createCellStyle();
		Font headConstFont = wb.createFont();
		headConstFont.setFontName("Consolas");
		headConstFont.setFontHeightInPoints((short) 9);
		bodyConstStyle.setFont(headConstFont);
		bodyConstStyle.setAlignment(HorizontalAlignment.LEFT);
		bodyConstStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		bodyConstStyle.setBorderTop(defaultBorderType);
		bodyConstStyle.setBorderBottom(defaultBorderType);
		bodyConstStyle.setBorderLeft(defaultBorderType);
		bodyConstStyle.setBorderRight(defaultBorderType);

		// CELL_BODY_DATE_STYLE
		bodyDateStyle = wb.createCellStyle();
		Font bodyDateFont = wb.createFont();
		bodyDateFont.setFontName("Consolas");
		bodyDateFont.setFontHeightInPoints((short) 9);
		bodyDateStyle.setDataFormat(format.getFormat("yyyy/MM/dd"));
		bodyDateStyle.setFont(bodyDateFont);
		bodyDateStyle.setAlignment(HorizontalAlignment.LEFT);
		bodyDateStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		bodyDateStyle.setBorderTop(defaultBorderType);
		bodyDateStyle.setBorderBottom(defaultBorderType);
		bodyDateStyle.setBorderLeft(defaultBorderType);
		bodyDateStyle.setBorderRight(defaultBorderType);

		// CELL_BODY_DATE_TIME_STYLE
		bodyDateTimeStyle = wb.createCellStyle();
		Font bodyDateTimeFont = wb.createFont();
		bodyDateTimeFont.setFontName("Consolas");
		bodyDateTimeFont.setFontHeightInPoints((short) 9);
		bodyDateTimeStyle.setDataFormat(format.getFormat("yyyy/MM/dd HH:mm:ss"));
		bodyDateTimeStyle.setFont(bodyDateTimeFont);
		bodyDateTimeStyle.setAlignment(HorizontalAlignment.LEFT);
		bodyDateTimeStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		bodyDateTimeStyle.setBorderTop(defaultBorderType);
		bodyDateTimeStyle.setBorderBottom(defaultBorderType);
		bodyDateTimeStyle.setBorderLeft(defaultBorderType);
		bodyDateTimeStyle.setBorderRight(defaultBorderType);

		// CELL_BODY_INTEGER_STYLE
		bodyIntegerStyle = wb.createCellStyle();
		Font bodyIntegerFont = wb.createFont();
		bodyIntegerFont.setFontName("Consolas");
		bodyIntegerFont.setFontHeightInPoints((short) 9);
		bodyIntegerStyle.setDataFormat(format.getFormat("0"));
		bodyIntegerStyle.setFont(bodyIntegerFont);
		bodyIntegerStyle.setAlignment(HorizontalAlignment.LEFT);
		bodyIntegerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		bodyIntegerStyle.setBorderTop(defaultBorderType);
		bodyIntegerStyle.setBorderBottom(defaultBorderType);
		bodyIntegerStyle.setBorderLeft(defaultBorderType);
		bodyIntegerStyle.setBorderRight(defaultBorderType);

		// CELL_BODY_DOUBLE_STYLE
		bodyDoubleStyle = wb.createCellStyle();
		Font bodyDoubleFont = wb.createFont();
		bodyDoubleFont.setFontName("Consolas");
		bodyDoubleFont.setFontHeightInPoints((short) 9);
		bodyDoubleStyle.setDataFormat(format.getFormat("0.00"));
		bodyDoubleStyle.setFont(bodyDoubleFont);
		bodyDoubleStyle.setAlignment(HorizontalAlignment.LEFT);
		bodyDoubleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		bodyDoubleStyle.setBorderTop(defaultBorderType);
		bodyDoubleStyle.setBorderBottom(defaultBorderType);
		bodyDoubleStyle.setBorderLeft(defaultBorderType);
		bodyDoubleStyle.setBorderRight(defaultBorderType);

		// CELL_BODY_PERCENT_STYLE
		bodyPercentStyle = wb.createCellStyle();
		Font bodyPercentFont = wb.createFont();
		bodyPercentFont.setFontName("Consolas");
		bodyPercentFont.setFontHeightInPoints((short) 9);
		bodyPercentStyle.setDataFormat(format.getFormat("0.00%"));
		bodyPercentStyle.setFont(bodyPercentFont);
		bodyPercentStyle.setAlignment(HorizontalAlignment.LEFT);
		bodyPercentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		bodyPercentStyle.setBorderTop(defaultBorderType);
		bodyPercentStyle.setBorderBottom(defaultBorderType);
		bodyPercentStyle.setBorderLeft(defaultBorderType);
		bodyPercentStyle.setBorderRight(defaultBorderType);

		// CELL_BODY_STRING_STYLE
		bodyStringStyle = wb.createCellStyle();
		Font bodyStringFont = wb.createFont();
		bodyStringFont.setFontName("Consolas");
		bodyStringFont.setFontHeightInPoints((short) 9);
		bodyStringStyle.setFont(bodyStringFont);
		bodyStringStyle.setAlignment(HorizontalAlignment.LEFT);
		bodyStringStyle.setVerticalAlignment(VerticalAlignment.CENTER);
		bodyStringStyle.setBorderTop(defaultBorderType);
		bodyStringStyle.setBorderBottom(defaultBorderType);
		bodyStringStyle.setBorderLeft(defaultBorderType);
		bodyStringStyle.setBorderRight(defaultBorderType);
	}
}
