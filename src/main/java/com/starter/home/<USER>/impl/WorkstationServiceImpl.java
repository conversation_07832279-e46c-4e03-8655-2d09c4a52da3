package com.starter.home.service.impl;

import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.util.JdbcConstants;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.starter.context.bean.CacheRemove;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.configuration.database.DatabaseContextHolder;
import com.starter.context.configuration.database.DatabaseType;
import com.starter.context.configuration.database.DynamicDataSource;
import com.starter.context.configuration.database.TargetDataSource;
import com.starter.home.WorkstationController;
import com.starter.home.dao.IWorkstationDao;
import com.starter.home.service.IWorkstationService;
import com.starter.login.bean.Session;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import com.starter.home.bean.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.util.*;
import java.util.stream.Collectors;

@Service("workstationService")
@Scope("prototype")
@Transactional
public class WorkstationServiceImpl implements IWorkstationService {

    @Resource
    private IWorkstationDao workstationDao;

    @Resource
    private Response response;

    @Resource
    private DynamicDataSource dynamicDataSource;

    @Resource
    private ExcelTemplate excelTemplate;

    static final String GLOBAL_FILTER = "#GLOBAL_FILTER#";

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage(String userid) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("shareTo", workstationDao.queryShareTo());
        resultMap.put("cascader", Utils.parseCascader(workstationDao.queryCascader()));
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryWorkstationsLayout(Session session, Map<String, Object> parameterMap) {
        List<WorkstationConfig> configList = workstationDao.queryActiveWorkstations(parameterMap);

        List<WidgetConfig> activeWidgets = new ArrayList<>();
        for (WorkstationConfig config : configList) {
            activeWidgets.add(new WidgetConfig(config));
        }

        List<WorkstationConfig> availableList = workstationDao.queryAvailableWorkstations(parameterMap);

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("activeWorkstations", this.parseWorkstationGroup(configList)); // 显示在前台的组件列表
        resultMap.put("availableWorkstations", this.parseWorkstationGroup(availableList)); // 显示在后台的组件列表
        resultMap.put("activeWidgets", activeWidgets); // 显示在前台的组件信息
        resultMap.put("parameter", workstationDao.querySavedParameters(session.getUserid()));

        return response.setBody(resultMap);
    }

    private List<WorkstationGroup> parseWorkstationGroup(List<WorkstationConfig> configList) {
        List<WorkstationWidget> widgetList = new ArrayList<>();
        for (WorkstationConfig config : configList) {
            widgetList.add(new WorkstationWidget(config));
        }

        // 先按照Group转成Map
        Map<String, List<WorkstationWidget>> widgetMap = new LinkedHashMap<>();
        for (WorkstationWidget widget : widgetList) {
            List<WorkstationWidget> list = widgetMap.computeIfAbsent(widget.getGroup(), key -> new ArrayList<>());
            list.add(widget);
        }

        // 再将Map转为Group List
        List<WorkstationGroup> resultList = new ArrayList<>();
        List<String> keys = new ArrayList<>(widgetMap.keySet());
        for (String key : keys) {
            WorkstationGroup group = new WorkstationGroup();
            group.setName(key);
            group.setChildren(widgetMap.get(group.getName()));
            resultList.add(group);
        }
        return resultList;
    }

    @Override
    @Cacheable(value = Configuration.APPLICATION_NAME + ":1d", condition = "#cachable")
    @TargetDataSource(DatabaseType.SCP02_READONLY)
    public Response queryWorkstationValue(Map<String, Object> parameterMap, boolean cachable) {
        return response.setBody(this.calcWidget(parameterMap));
    }

    /**
     * 先使用JDBC方式查询出结果集的列,然后再使用MyBatis查询结果
     */
    @Override
    @TargetDataSource(DatabaseType.SCP02_READONLY)
    public Response executeQuery(Map<String, Object> parameterMap) {
        String scripts = (String) parameterMap.get("scripts");
        scripts = StringUtils.replace(scripts, GLOBAL_FILTER, this.generateCascaderFilter(parameterMap));

        Map<String, Object> resultMap = new HashMap<>();

        List<LinkedHashMap<String, Object>> resultList;
        try {
            parameterMap.put("sql", scripts);
            resultList = workstationDao.queryWidget(parameterMap);
        } catch (Exception e) {
            resultMap.put("result", "error");
            if (e instanceof BadSqlGrammarException) {
                resultMap.put("data", ((BadSqlGrammarException) e).getSQLException().getMessage());
            } else {
                resultMap.put("data", e.getMessage());
            }
            return response.setBody(resultMap);
        }

        if (resultList == null) {
            resultList = new ArrayList<>();
        }

        if (resultList.isEmpty()) {
            scripts = (String) parameterMap.get("scripts");
            scripts = StringUtils.replace(scripts, GLOBAL_FILTER, " 1 = 1 ");
            String sql = "select * from (" + scripts + ") tt where 1 = 0";

            try (Connection con = dynamicDataSource.getConnection()) {
                ResultSet rs = con.prepareStatement(sql).executeQuery();
                List<String> headers = new ArrayList<>();

                ResultSetMetaData rsmd = rs.getMetaData();

                for (int i = 1; i <= rsmd.getColumnCount(); i++) {
                    headers.add(rsmd.getColumnLabel(i));
                }

                rs.close();

                LinkedHashMap<String, Object> map = new LinkedHashMap<>();
                for (String header : headers) {
                    map.put(header, null);
                }
                resultList.add(map);
            } catch (Exception e) {
                resultMap.put("result", "error");
                resultMap.put("data", e.getMessage());
                return response.setBody(resultMap);
            }
        }

        resultMap.put("result", "success");
        resultMap.put("data", resultList);
        return response.setBody(resultMap);
    }

    @Override
    public Response formatSQL(Map<String, Object> parameterMap) {
        String sql = (String) parameterMap.get("scripts");
        String randomStr = Utils.randomStr(8);
        String randomSQL = "'" + randomStr + "' = '" + randomStr + "'";
        sql = StringUtils.replace(sql, GLOBAL_FILTER, randomSQL);
        sql = SQLUtils.format(sql, JdbcConstants.ORACLE);
        sql = StringUtils.replace(sql, randomSQL, GLOBAL_FILTER);
        return response.setBody(sql);
    }

    /**
     * DatabaseContextHolder.setDatabaseType(DatabaseType.SCP02_READONLY); 并不会影响事务切面, 因为事务切面在此之前已经执行完毕
     */
    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this", scope = CacheRemove.SCOPE_USER)
    public Response saveWidget(Map<String, Object> parameterMap) {
        if (this.widgetInfoCheck(parameterMap, response) == false) {
            return response;
        }

        parameterMap.put("id", Utils.randomStr(8));
        this.saveShareTo(parameterMap);
        workstationDao.saveWidget(parameterMap);
        return response;
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = {"this.queryWorkstationsLayout", "this.queryWorkstationValue"}, scope = CacheRemove.SCOPE_ALL_USER)
    public Response modifyWidget(Map<String, Object> parameterMap) {
        if (this.widgetInfoCheck(parameterMap, response) == false) {
            return response;
        }

        this.saveShareTo(parameterMap);
        parameterMap.put("isAdmin", workstationDao.queryAdminCnt(WorkstationController.PARENT_CODE, ((Session) parameterMap.get("session")).getUserid()) > 0);
        workstationDao.modifyWidget(parameterMap);
        return response;
    }

    private void saveShareTo(Map<String, Object> parameterMap) {
        JSONArray shareTo = (JSONArray) parameterMap.get("shareTo");
        if (shareTo.contains("ALL")) {
            parameterMap.put("classification", "public");
        } else {
            parameterMap.put("classification", "private");
            if (shareTo.isEmpty() == false) {
                workstationDao.saveShareTo(parameterMap);
            }
        }
    }

    @Override
    public Response queryWidgetById(Map<String, Object> parameterMap, String userid) {
        WidgetInfo widgetInfo = workstationDao.queryWidgetById(parameterMap);
        if (widgetInfo == null) {
            return response.setBody(new WidgetInfo());
        }
        if ("public".equals(widgetInfo.getClassification())) {
            widgetInfo.addShareTo("ALL");
        } else {
            widgetInfo.setShareTo(workstationDao.queryCurrentShareTo(parameterMap));
        }
        widgetInfo.setAuthor(workstationDao.queryAdminCnt(WorkstationController.PARENT_CODE, userid) > 0);
        return response.setBody(widgetInfo);
    }

    /**
     * 删除widget, 需要清除所有用户下的queryWorkstationsLayout缓存
     */
    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this.queryWorkstationsLayout", scope = CacheRemove.SCOPE_ALL_USER)
    public Response deleteWidget(Map<String, Object> parameterMap) {
        parameterMap.put("isAdmin", workstationDao.queryAdminCnt(WorkstationController.PARENT_CODE, ((Session) parameterMap.get("session")).getUserid()) > 0);
        workstationDao.deleteWidget(parameterMap);
        return response;
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = {"this.queryWorkstationsLayout"}, scope = CacheRemove.SCOPE_ALL_USER)
    public Response saveParameters(Map<String, Object> parameterMap) {
        parameterMap.put("filterStr", JSON.toJSONString(parameterMap.get("filterList")));
        workstationDao.saveParameters(parameterMap);

        List<String> list = new ArrayList<>();
        JSONObject checkedWidgets = (JSONObject) parameterMap.get("checkedWidgets");
        for (String key : checkedWidgets.keySet()) {
            if (checkedWidgets.getBoolean(key)) {
                list.add(key);
            }
        }
        parameterMap.put("list", list);
        if (list.isEmpty() == false) {
            workstationDao.saveActiveWidgets(parameterMap);
        } else {
            workstationDao.deleteUserActiveWidgets(parameterMap);
        }
        return response;
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this.queryWorkstationsLayout", scope = CacheRemove.SCOPE_USER)
    public Response saveCustomSettings(Map<String, Object> parameterMap) {
        workstationDao.saveCustomSettings(parameterMap);
        return response;
    }

    @Override
    public Response queryDetails(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        WorkstationConfig config = workstationDao.queryWidgetConfigByID(parameterMap);
        if (config == null || StringUtils.isBlank(config.getDetailsScripts())) {
            page.setTotal(0);
            return response.setBody(page);
        }

        String scripts = config.getDetailsScripts();
        scripts = StringUtils.replace(scripts, GLOBAL_FILTER, this.generateCascaderFilter(parameterMap));
        parameterMap.put("scripts", scripts);

        page.setTotal(workstationDao.queryDetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(workstationDao.queryDetails(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadDetails(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        WorkstationConfig config = workstationDao.queryWidgetConfigByID(parameterMap);
        if (config == null || StringUtils.isBlank(config.getDetailsScripts())) {
            parameterMap.put("scripts", "select [empty] from dual");
        } else {
            String scripts = config.getDetailsScripts();
            scripts = StringUtils.replace(scripts, GLOBAL_FILTER, this.generateCascaderFilter(parameterMap));
            parameterMap.put("scripts", scripts);
        }

        String fileName = "data_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.starter.home.dao.IWorkstationDao.queryDetails", parameterMap);
    }

    @Override
    @TargetDataSource(DatabaseType.SCP02_READONLY)
    public Response queryDetailsHeader(Map<String, Object> parameterMap) {
        DatabaseContextHolder.setDatabaseType(DatabaseType.SCP02_READONLY);
        WorkstationConfig config = workstationDao.queryWidgetConfigByID(parameterMap);
        Map<String, Object> resultMap = new HashMap<>();
        List<String> headers = new ArrayList<>();
        if (config == null || StringUtils.isBlank(config.getDetailsScripts())) {
            resultMap.put("result", "empty");
            headers.add("[Empty]");
            resultMap.put("data", headers);
            return response.setBody(resultMap);
        }

        String scripts = config.getDetailsScripts();
        scripts = StringUtils.replace(scripts, GLOBAL_FILTER, " 1 = 1 ");

        String sql = "select * from (" + scripts + ") tt where 1 = 0";
        try (Connection con = dynamicDataSource.getConnection()) {
            ResultSet rs = con.prepareStatement(sql).executeQuery();


            ResultSetMetaData rsmd = rs.getMetaData();

            for (int i = 1; i <= rsmd.getColumnCount(); i++) {
                headers.add(rsmd.getColumnLabel(i));
            }

            rs.close();

            resultMap.put("result", "success");
            resultMap.put("data", headers);
        } catch (Exception e) {
            resultMap.put("result", "error");
            resultMap.put("data", e.getMessage());
            e.printStackTrace();
        } finally {
            DatabaseContextHolder.setDefalutDatabaseType();
        }
        return response.setBody(resultMap);
    }

    private WorkstationWidget calcWidget(Map<String, Object> parameterMap) {
        WorkstationConfig config = workstationDao.queryWidgetConfigByID(parameterMap);
        WorkstationWidget widget = new WorkstationWidget();
        widget.setId(config.getId());
        widget.setTitle(config.getTitle());
        widget.setGroup(config.getGroupName());
        widget.setValue("");

        try {
            String script = config.getScripts();
            script = StringUtils.replace(script, GLOBAL_FILTER, this.generateCascaderFilter(parameterMap));

            parameterMap.put("sql", script);
            List<LinkedHashMap<String, Object>> resultList = workstationDao.queryWidget(parameterMap);
            switch (config.getReportTypeFormatted()) {
                case "bar":
                case "line":
                case "pie":
                    List<String> xAxis = new ArrayList<>();
                    List<BigDecimal> yAxis = new ArrayList<>();
                    for (Map<String, Object> map : resultList) {
                        xAxis.add((String) map.get("XAXIS"));
                        yAxis.add(Utils.parseBigDecimal(map.get("YAXIS"), null));
                    }
                    Map<String, Object> valueMap = new HashMap<>();
                    valueMap.put("xAxis", xAxis);
                    valueMap.put("yAxis", yAxis);
                    valueMap.put("type", config.getReportTypeFormatted());
                    widget.setValue(valueMap);
                    break;
                case "text":
                    if (resultList != null && resultList.isEmpty() == false) {
                        Map<String, Object> rs = resultList.get(0);
                        if (rs == null) {
                            widget.setValue("");
                            return widget;
                        }
                        List<Object> values = new ArrayList<>();
                        List<Object> colors = new ArrayList<>();
                        List<String> keys = rs.keySet().stream().sorted(String::compareTo).collect(Collectors.toList());

                        for (String key : keys) {
                            if ("icon_color".equalsIgnoreCase(key)) {
                                widget.setIconColor(String.valueOf(rs.get(key)));
                            } else if ("icon".equalsIgnoreCase(key)) {
                                widget.setIcon(String.valueOf(rs.get(key)));
                            } else if (StringUtils.startsWithIgnoreCase(key, "value")) {
                                values.add(rs.get(key));
                            } else if (StringUtils.startsWithIgnoreCase(key, "color")) {
                                colors.add(rs.get(key));
                            }
                        }

                        if (values.size() == 1) {
                            widget.setValue(values.get(0));
                            if (colors.size() > 0) {
                                widget.setColor(colors.get(0));
                            }
                        } else {
                            widget.setValue(values);
                            widget.setColor(colors);
                        }
                    }
                    break;
            }
        } catch (Exception e) {
            widget.setValue("#ERROR");
        }
        return widget;
    }

    private boolean widgetInfoCheck(Map<String, Object> parameterMap, Response res) {
        DatabaseContextHolder.setDatabaseType(DatabaseType.SCP02_READONLY);
        String scripts = (String) parameterMap.get("scripts");

        scripts = StringUtils.replace(scripts, GLOBAL_FILTER, " 1 = 1 ");
        String detailsScripts = (String) parameterMap.get("detailsScripts");
        detailsScripts = StringUtils.replace(detailsScripts, GLOBAL_FILTER, this.generateCascaderFilter(parameterMap));

        String sql = "select * from (" + scripts + ") tt where 1 = 0";
        String message = null;
        try (Connection con = dynamicDataSource.getConnection()) {
            ResultSet rs = con.prepareStatement(sql).executeQuery();
            List<String> headers = new ArrayList<>();

            ResultSetMetaData rsmd = rs.getMetaData();

            for (int i = 1; i <= rsmd.getColumnCount(); i++) {
                headers.add(rsmd.getColumnLabel(i));
            }

            rs.close();

            String type = (String) parameterMap.get("reportType");
            if ("Text/Number".equalsIgnoreCase(type)) {
                boolean findRequired = false;
                for (String h : headers) {
                    if (StringUtils.startsWithIgnoreCase(h, "value")) {
                        findRequired = true;
                        break;
                    }
                }
                if (findRequired == false) {
                    message = "The " + type + " widget must contain fields that begin with value";
                }
            } else {
                boolean findRequiredX = false;
                boolean findRequiredY = false;
                for (String h : headers) {
                    if (StringUtils.startsWithIgnoreCase(h, "xAxis")) {
                        findRequiredX = true;
                    } else if (StringUtils.startsWithIgnoreCase(h, "yAxis")) {
                        findRequiredY = true;
                    }
                }

                if (findRequiredX == false || findRequiredY == false) {
                    message = "The " + type + " widget must contain fields xAxis and yAxis";
                }
            }

            if (StringUtils.isNotBlank(detailsScripts)) {
                try {
                    String detailsSql = "select * from (" + detailsScripts + ") tt where 1 = 0";
                    ResultSet rs1 = con.prepareStatement(detailsSql).executeQuery();
                    rs1.close();
                } catch (Exception e0) {
                    throw new Exception("[Details] " + e0.getMessage());
                }
            }

            if (message != null) {
                Map<String, String> result = new HashMap<>();
                result.put("message", message);
                res.setBody(result);
            } else {
                return true;
            }
        } catch (Exception e) {
            Map<String, String> result = new HashMap<>();
            String exMsg = e.getMessage();
            if (exMsg.startsWith("[Details] ") == false) {
                exMsg = "[Summary] " + exMsg;
            }
            result.put("message", exMsg);
            res.setBody(result);
        } finally {
            DatabaseContextHolder.setDefalutDatabaseType();
        }
        return false;
    }

    /**
     * 生成cascader filter
     *
     * @param parameterMap 参数map
     */
    private String generateCascaderFilter(Map<String, Object> parameterMap) {
        // 生成筛选条件
        List<String> sql = new ArrayList<>();
        JSONArray categoryArray = (JSONArray) parameterMap.get("filterList");
        if (categoryArray != null && categoryArray.isEmpty() == false) {
            Map<String, List<String>> filterMap = new HashMap<>();

            for (Object subObj : categoryArray) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = subArray.getString(0);

                String value = subArray.getString(1);
                String key = Utils.randomStr(8);

                if (Utils.hasInjectionAttack(columnName)) {
                    continue;
                }

                List<String> fl = filterMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                fl.add("#{" + key + ",jdbcType=VARCHAR}");
                parameterMap.put(key, value);
            }

            List<String> filterList = new ArrayList<>();

            for (String key : filterMap.keySet()) {
                List<String> fl = filterMap.get(key);
                filterList.add("t." + key + " in (" + StringUtils.join(fl, ",") + ")");
            }

            sql.add(" (" + StringUtils.join(filterList, " and ") + ") ");
        }

        // load material & vendor code
        String material = (String) parameterMap.get("material");
        if (StringUtils.isNotBlank(material)) {
            List<List<String>> materialList = Utils.splitValue(material);
            List<String> materialSQL = new ArrayList<>();
            for (List<String> subList : materialList) {
                List<String> fl = new ArrayList<>();
                for (String m : subList) {
                    String key = Utils.randomStr(8);
                    fl.add("#{" + key + ",jdbcType=VARCHAR}");
                    parameterMap.put(key, m);
                }
                materialSQL.add("t.MATERIAL in (" + StringUtils.join(fl, ",") + ")");
            }
            sql.add("(" + StringUtils.join(materialSQL, " or ") + ") ");
        }

        String vendor = (String) parameterMap.get("vendorCode");
        if (StringUtils.isNotBlank(vendor)) {
            List<List<String>> vendorList = Utils.splitValue(vendor);
            List<String> vendorSQL = new ArrayList<>();
            for (List<String> subList : vendorList) {
                List<String> fl = new ArrayList<>();
                for (String m : subList) {
                    String key = Utils.randomStr(8);
                    fl.add("#{" + key + ",jdbcType=VARCHAR}");
                    parameterMap.put(key, m);
                }
                vendorSQL.add("t.VENDOR_CODE in (" + StringUtils.join(fl, ",") + ")");
            }
            sql.add("(" + StringUtils.join(vendorSQL, " or ") + ") ");
        }

        return sql.isEmpty() ? " 1 = 1" : StringUtils.join(sql, " and ");
    }
}
