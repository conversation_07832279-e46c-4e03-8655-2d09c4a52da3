package com.starter.home.dao;

import com.starter.home.bean.WidgetInfo;
import com.starter.home.bean.WorkstationConfig;
import com.starter.home.bean.WorkstationParameter;
import org.apache.ibatis.annotations.Mapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IWorkstationDao {

    List<WorkstationConfig> queryActiveWorkstations(Map<String, Object> parameterMap);

    List<WorkstationConfig> queryDefaultActiveWorkstations(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryWidget(Map<String, Object> parameterMap);

    WorkstationConfig queryWidgetConfigByID(Map<String, Object> parameterMap);

    void saveWidget(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryShareTo();

    List<Map<String, String>> queryCascader();

    List<WorkstationConfig> queryAvailableWorkstations(Map<String, Object> parameterMap);

    void saveShareTo(Map<String, Object> parameterMap);

    void saveParameters(Map<String, Object> parameterMap);

    void saveActiveWidgets(Map<String, Object> list);

    WorkstationParameter querySavedParameters(String userid);

    void saveCustomSettings(Map<String, Object> parameterMap);

    void modifyWidget(Map<String, Object> parameterMap);

    WidgetInfo queryWidgetById(Map<String, Object> parameterMap);

    List<String> queryCurrentShareTo(Map<String, Object> parameterMap);

    void deleteWidget(Map<String, Object> parameterMap);

    int queryDetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryDetails(Map<String, Object> parameterMap);

    void deleteUserActiveWidgets(Map<String, Object> parameterMap);

    int queryAdminCnt(String parentCode, String userid);
}
