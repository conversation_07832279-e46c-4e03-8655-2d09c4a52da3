package com.starter.home.bean;

import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * 用户保存的查询条件
 */
public class WorkstationParameter {
    private int sizeRatio;
    private String material;
    private String groupType;
    private String vendorCode;
    @JsonIgnore
    private String filters;
    private JSONArray filterList;

    public JSONArray getFilterList() {
        return filterList;
    }

    public void setFilterList(JSONArray filterList) {
        this.filterList = filterList;
    }

    public String getGroupType() {
        return groupType;
    }

    public void setGroupType(String groupType) {
        this.groupType = groupType;
    }

    public int getSizeRatio() {
        return sizeRatio;
    }

    public void setSizeRatio(int sizeRatio) {
        this.sizeRatio = sizeRatio;
    }

    public String getMaterial() {
        return material;
    }

    public void setMaterial(String material) {
        this.material = material;
    }

    public String getVendorCode() {
        return vendorCode;
    }

    public void setVendorCode(String vendorCode) {
        this.vendorCode = vendorCode;
    }

    public String getFilters() {
        return filters;
    }

    public void setFilters(String filters) {
        this.filters = filters;
        if (this.filters != null) {
            this.filterList = JSONArray.parseArray(this.filters);
        } else {
            this.filterList = new JSONArray();
        }
    }
}
