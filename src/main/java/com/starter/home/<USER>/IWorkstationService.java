package com.starter.home.service;

import com.starter.login.bean.Session;
import com.starter.context.bean.Response;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;

public interface IWorkstationService {

    Response initPage(String userid);

    Response queryWorkstationsLayout(Session session, Map<String, Object> parameterMap);

    Response queryWorkstationValue(Map<String, Object> parameterMap, boolean cachable);

    Response executeQuery(Map<String, Object> parameterMap);

    Response formatSQL(Map<String, Object> parameterMap);

    Response saveWidget(Map<String, Object> parameterMap);

    Response saveParameters(Map<String, Object> parameterMap);

    Response saveCustomSettings(Map<String, Object> parameterMap);

    Response modifyWidget(Map<String, Object> parameterMap);

    Response queryWidgetById(Map<String, Object> parameterMap, String userid);

    Response deleteWidget(Map<String, Object> parameterMap);

    Response queryDetails(Map<String, Object> parameterMap);

    Response queryDetailsHeader(Map<String, Object> parameterMap);

    void downloadDetails(Map<String, Object> parameterMap, HttpServletResponse response);
}
