package com.starter.home.bean;

import java.util.ArrayList;
import java.util.List;

/**
 * 用户显示widget分组
 */
public class WorkstationGroup {
    private String name;
    private boolean open = true;
    private List<WorkstationWidget> children = new ArrayList<>();

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean isOpen() {
        return open;
    }

    public void setOpen(boolean open) {
        this.open = open;
    }

    public List<WorkstationWidget> getChildren() {
        return children;
    }

    public void setChildren(List<WorkstationWidget> children) {
        this.children = children;
    }
}
