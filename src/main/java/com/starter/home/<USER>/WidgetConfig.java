package com.starter.home.bean;

/**
 * 用户保存的个性化配置, 比如字体大小等
 */
public class WidgetConfig {
    private String id;
    private String cachable;
    private String groupName;
    private int fontSize;
    private int orderNo;

    public WidgetConfig() {

    }

    public WidgetConfig(WorkstationConfig config) {
        this.id = config.getId();
        this.cachable = config.getCachable();
        this.groupName = config.getGroupName();
        this.fontSize = config.getFontSize();
        this.orderNo = config.getOrderNo();
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public int getFontSize() {
        return fontSize;
    }

    public void setFontSize(int fontSize) {
        this.fontSize = fontSize;
    }

    public int getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(int orderNo) {
        this.orderNo = orderNo;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCachable() {
        return cachable;
    }

    public void setCachable(String cachable) {
        this.cachable = cachable;
    }
}
