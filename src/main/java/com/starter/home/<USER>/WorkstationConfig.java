package com.starter.home.bean;

/**
 * 记录widget的配置信息, 这部分信息会由service拆成 WorkstationWidget和 WorkstationGroup
 */
public class WorkstationConfig {

    private String id;
    private String title;
    private String groupName;
    private String scripts;
    private String detailsScripts;
    private String classification;
    private String reportType;
    private String cachable;
    private String linkTo;
    private int detailsLength;
    private String author;
    private int orderNo;
    private int fontSize;

    public int getDetailsLength() {
        return detailsLength;
    }

    public void setDetailsLength(int detailsLength) {
        this.detailsLength = detailsLength;
    }

    public String getDetailsScripts() {
        return detailsScripts;
    }

    public void setDetailsScripts(String detailsScripts) {
        this.detailsScripts = detailsScripts;
    }

    public int getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(int orderNo) {
        this.orderNo = orderNo;
    }

    public int getFontSize() {
        return fontSize;
    }

    public void setFontSize(int fontSize) {
        this.fontSize = fontSize;
    }

    public String getCachable() {
        return cachable;
    }

    public void setCachable(String cachable) {
        this.cachable = cachable;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getLinkTo() {
        return linkTo;
    }

    public void setLinkTo(String linkTo) {
        this.linkTo = linkTo;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getScripts() {
        return scripts;
    }

    public void setScripts(String scripts) {
        this.scripts = scripts;
    }

    public String getClassification() {
        return classification;
    }

    public void setClassification(String classification) {
        this.classification = classification;
    }

    public String getReportType() {
        return reportType;
    }

    public String getReportTypeFormatted() {
        if ("text/number".equals(reportType.toLowerCase())) {
            return "text";
        }
        return reportType.toLowerCase();
    }

    public void setReportType(String reportType) {
        this.reportType = reportType;
    }
}
