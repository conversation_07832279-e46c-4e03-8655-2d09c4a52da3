package com.starter.home.bean;

import java.util.ArrayList;
import java.util.List;

/**
 * widget详细信息, 用于查询和修改widget
 */
public class WidgetInfo {

    private String id;
    private String title;
    private String groupName;
    private String scripts;
    private String detailsScripts;
    private String classification;
    private String reportType;
    private String cachable;
    private String linkTo;
    private String createBy;
    private Boolean isAuthor = false;
    private List<String> shareTo = new ArrayList<>();

    public String getDetailsScripts() {
        return detailsScripts;
    }

    public void setDetailsScripts(String detailsScripts) {
        this.detailsScripts = detailsScripts;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public List<String> getShareTo() {
        return shareTo;
    }

    public void setShareTo(List<String> shareTo) {
        this.shareTo = shareTo;
    }

    public void addShareTo(String to) {
        this.shareTo.add(to);
    }

    public String getLinkTo() {
        return linkTo;
    }

    public void setLinkTo(String linkTo) {
        this.linkTo = linkTo;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getScripts() {
        return scripts;
    }

    public void setScripts(String scripts) {
        this.scripts = scripts;
    }

    public String getClassification() {
        return classification;
    }

    public void setClassification(String classification) {
        this.classification = classification;
    }

    public String getReportType() {
        return reportType;
    }

    public void setReportType(String reportType) {
        this.reportType = reportType;
    }

    public String getCachable() {
        return cachable;
    }

    public void setCachable(String cachable) {
        this.cachable = cachable;
    }

    public Boolean getAuthor() {
        return isAuthor;
    }

    public void setAuthor(Boolean author) {
        isAuthor = author;
    }
}
