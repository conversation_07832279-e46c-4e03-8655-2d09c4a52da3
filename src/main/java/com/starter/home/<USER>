package com.starter.home;

import com.starter.home.service.IWorkstationService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/home/<USER>", parent = WorkstationController.PARENT_CODE)
public class WorkstationController extends ControllerHelper {

    public static final String PARENT_CODE = "sub-menu13";

    @Resource
    private IWorkstationService workstationService;

    @SchneiderRequestMapping("/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        return workstationService.initPage(session.getUserid());
    }

    @SchneiderRequestMapping("/query_workstations_layout")
    public Response queryWorkstationsLayout(HttpServletRequest request) {
        super.pageLoad(request);
        parameterMap.put("parentCode", PARENT_CODE);
        return workstationService.queryWorkstationsLayout(session, parameterMap);
    }

    @SchneiderRequestMapping("/query_workstation_value")
    public Response queryWorkstationValue(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return workstationService.queryWorkstationValue(parameterMap, "Y".equals(parameterMap.get("cachable")));
    }

    @SchneiderRequestMapping("/execute_query")
    public Response executeQuery(HttpServletRequest request) {
        super.pageLoad(request);
        return workstationService.executeQuery(parameterMap);
    }

    @SchneiderRequestMapping("/format_sql")
    public Response formatSQL(HttpServletRequest request) {
        super.pageLoad(request);
        return workstationService.formatSQL(parameterMap);
    }

    @SchneiderRequestMapping("/save_widget")
    public Response saveWidget(HttpServletRequest request) {
        super.pageLoad(request);
        return workstationService.saveWidget(parameterMap);
    }

    @SchneiderRequestMapping("/save_parameters")
    public Response saveParameters(HttpServletRequest request) {
        super.pageLoad(request);
        return workstationService.saveParameters(parameterMap);
    }

    @SchneiderRequestMapping("/save_custom_settings")
    public Response saveCustomSettings(HttpServletRequest request) {
        super.pageLoad(request);
        return workstationService.saveCustomSettings(parameterMap);
    }

    @SchneiderRequestMapping("/modify_widget")
    public Response modifyWidget(HttpServletRequest request) {
        super.pageLoad(request);
        return workstationService.modifyWidget(parameterMap);
    }

    @SchneiderRequestMapping("/query_widget_by_id")
    public Response queryWidgetById(HttpServletRequest request) {
        super.pageLoad(request);
        return workstationService.queryWidgetById(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping("/delete_widget")
    public Response deleteWidget(HttpServletRequest request) {
        super.pageLoad(request);
        return workstationService.deleteWidget(parameterMap);
    }

    @SchneiderRequestMapping("/query_details_header")
    public Response queryDetailsHeader(HttpServletRequest request) {
        super.pageLoad(request);
        return workstationService.queryDetailsHeader(parameterMap);
    }

    @SchneiderRequestMapping("/query_details")
    public Response queryDetails(HttpServletRequest request) {
        super.pageLoad(request);
        return workstationService.queryDetails(parameterMap);
    }

    @SchneiderRequestMapping("/download_details")
    public void downloadDetails(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        workstationService.downloadDetails(parameterMap, response);
    }
}
