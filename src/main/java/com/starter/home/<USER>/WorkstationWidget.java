package com.starter.home.bean;

import com.fasterxml.jackson.annotation.JsonIgnore;

/**
 * 用于显示widget
 */
public class WorkstationWidget {
    @JsonIgnore
    private String group;
    private String id;
    private String title;
    private String linkTo;
    private int detailsLength;
    private String author;
    private Object value;
    private Object color;
    private String icon;
    private String iconColor;

    public WorkstationWidget() {

    }

    public WorkstationWidget(WorkstationConfig config) {
        this.id = config.getId();
        this.group = config.getGroupName();
        this.title = config.getTitle();
        this.linkTo = config.getLinkTo();
        this.author = config.getAuthor();
        this.detailsLength = config.getDetailsLength();
    }

    public int getDetailsLength() {
        return detailsLength;
    }

    public void setDetailsLength(int detailsLength) {
        this.detailsLength = detailsLength;
    }

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getLinkTo() {
        return linkTo;
    }

    public void setLinkTo(String linkTo) {
        this.linkTo = linkTo;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Object getColor() {
        return color;
    }

    public void setColor(Object color) {
        this.color = color;
    }

    public String getIconColor() {
        return iconColor;
    }

    public void setIconColor(String iconColor) {
        this.iconColor = iconColor;
    }
}
