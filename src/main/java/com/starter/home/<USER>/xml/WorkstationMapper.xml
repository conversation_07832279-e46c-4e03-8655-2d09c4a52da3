<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.starter.home.dao.IWorkstationDao">
    <select id="queryActiveWorkstations" resultType="com.starter.home.bean.WorkstationConfig" parameterType="java.util.HashMap">
        select t.id,
               t.title,
               nvl(j.custom_group, t.group_name) group_name,
               t.link_to,
               nvl(length(t.details_scripts), 0) details_length,
               t.cachable,
               nvl(j.order_no, 100)              order_no,
               nvl(j.font_size, 100)             font_size
          from sy_workstation t
                   inner join sy_workstation_active j on t.id = j.widget_id
         where j.user_id = upper(#{session.userid,jdbcType=VARCHAR})
           and (
                t.id in (
                    select t0.id from sy_workstation t0 where t0.classification = 'public'
                     union all
                    select t1.widget_id from sy_workstation_share_to t1 where t1.shared_user = upper(#{session.userid,jdbcType=VARCHAR})
                     union all
                    select t2.id from sy_workstation t2 where t2.create_by$ = upper(#{session.userid,jdbcType=VARCHAR})
                ) or exists(select 1 from sy_menu_auth t3
                            where t3.user_id = upper(#{session.userid,jdbcType=VARCHAR})
                              and t3.menu_code = #{parentCode,jdbcType=VARCHAR}
                              and t3.accessible = 'true'
                              and lower(t3.auth_details) = 'admin')
               )
         order by nvl(j.custom_group, t.group_name), nvl(j.order_no, 100), t.title
    </select>

    <select id="queryAvailableWorkstations" resultType="com.starter.home.bean.WorkstationConfig">
        select t.id,
               t.title,
               decode(l.group_type, 'By Author', j.user_name, t.group_name) group_name,
               k.url as link_to,
               j.user_name as author,
               t.cachable
          from sy_workstation t left join SY_MENU k on t.link_to = k.menu_code
                                    left join SY_USER_MASTER_DATA j on t.create_by$ = j.sesa_code
                                    left join sy_workstation_params l on l.user_id = upper(#{session.userid,jdbcType=VARCHAR})
         where t.id in (
                    select t0.id from sy_workstation t0 where t0.classification = 'public'
                     union all
                    select t1.widget_id from sy_workstation_share_to t1 where t1.shared_user = upper(#{session.userid,jdbcType=VARCHAR})
                     union all
                    select t2.id from sy_workstation t2 where t2.create_by$ = upper(#{session.userid,jdbcType=VARCHAR})
                ) or exists(select 1 from sy_menu_auth t3
                            where t3.user_id = upper(#{session.userid,jdbcType=VARCHAR})
                              and t3.menu_code = #{parentCode,jdbcType=VARCHAR}
                              and t3.accessible = 'true'
                              and lower(t3.auth_details) = 'admin')
        order by decode(l.group_type, 'By Author', j.user_name, t.group_name)
    </select>

    <select id="queryWidget" resultType="java.util.LinkedHashMap">
        select * from (
         ${sql}
        ) tt offset 0 rows fetch next 120 rows only
    </select>

    <select id="queryWidgetConfigByID" resultType="com.starter.home.bean.WorkstationConfig">
        select * from scpa.sy_workstation where id = #{id, jdbcType=VARCHAR}
    </select>

    <insert id="saveShareTo">
        begin
            delete from sy_workstation_share_to where widget_id = #{id, jdbcType=VARCHAR};

            insert into sy_workstation_share_to(widget_id, shared_user)
            <foreach collection="shareTo" item="item" open="" separator="union all" close="">
                select #{id, jdbcType=VARCHAR},upper(#{item,jdbcType=VARCHAR}) from dual
            </foreach>;
        end;
    </insert>

    <insert id="saveWidget">
        insert into sy_workstation (id, title, group_name, scripts, details_scripts, classification, report_type, link_to, cachable, create_by$, create_date$)
        values
        (#{id, jdbcType=VARCHAR}, #{title, jdbcType=VARCHAR}, #{groupName, jdbcType=VARCHAR}, #{scripts, jdbcType=VARCHAR},
         #{detailsScripts, jdbcType=VARCHAR}, #{classification, jdbcType=VARCHAR}, #{reportType, jdbcType=VARCHAR},
         #{linkTo, jdbcType=VARCHAR}, #{cachable, jdbcType=VARCHAR}, upper(#{session.userid,jdbcType=VARCHAR}), sysdate)
    </insert>

    <update id="modifyWidget">
        update sy_workstation
           set title = #{title, jdbcType=VARCHAR},
               group_name = #{groupName, jdbcType=VARCHAR},
               scripts = #{scripts, jdbcType=VARCHAR},
               details_scripts = #{detailsScripts, jdbcType=VARCHAR},
               classification = #{classification, jdbcType=VARCHAR},
               report_type = #{reportType, jdbcType=VARCHAR},
               link_to = #{linkTo, jdbcType=VARCHAR},
               cachable = #{cachable, jdbcType=VARCHAR},
               update_by$ = upper(#{session.userid,jdbcType=VARCHAR}),
               update_date$ = sysdate
         where id = #{id, jdbcType=VARCHAR}
            <if test="isAdmin == false">
               and create_by$ = upper(#{session.userid,jdbcType=VARCHAR})
            </if>
    </update>

    <delete id="deleteWidget">
        delete from sy_workstation where id = #{id, jdbcType=VARCHAR}
        <if test="isAdmin == false">
            and create_by$ = upper(#{session.userid,jdbcType=VARCHAR})
        </if>
    </delete>

    <delete id="deleteUserActiveWidgets">
        delete from sy_workstation_active t where t.user_id = upper(#{session.userid,jdbcType=VARCHAR})
    </delete>

    <insert id="saveParameters">
        begin
            delete from sy_workstation_params where user_id = upper(#{session.userid,jdbcType=VARCHAR});
            insert into sy_workstation_params (user_id, size_ratio, material, vendor_code, filters, group_type)
            values
            (upper(#{session.userid,jdbcType=VARCHAR}), #{sizeRatio, jdbcType=INTEGER}, #{material, jdbcType=VARCHAR},
             #{vendorCode, jdbcType=VARCHAR}, #{filterStr, jdbcType=VARCHAR}, #{groupType, jdbcType=VARCHAR});
        end;
    </insert>

    <update id="saveCustomSettings">
        update sy_workstation_active t
           set t.custom_group = #{groupName, jdbcType=VARCHAR},
               t.font_size = #{fontSize, jdbcType=INTEGER},
               t.order_no = #{orderNo, jdbcType=INTEGER}
         where t.widget_id = #{id, jdbcType=VARCHAR}
    </update>

    <insert id="saveActiveWidgets">
        begin
            delete from sy_workstation_active t where (t.widget_id, t.user_id) not in (
                <foreach collection="list" item="item" separator="union all">
                    SELECT #{item} widget_id, upper(#{session.userid,jdbcType=VARCHAR}) user_id from dual
                </foreach>
            ) and t.user_id = upper(#{session.userid,jdbcType=VARCHAR});

            insert into sy_workstation_active (widget_id, user_id)
            select t.widget_id,t.user_id from (
                <foreach collection="list" item="item" separator="union all">
                    SELECT #{item} widget_id, upper(#{session.userid,jdbcType=VARCHAR}) user_id from dual
                </foreach>
            ) t
            where (t.widget_id, t.user_id) not in (select t0.widget_id,t0.user_id from sy_workstation_active t0);
        end;
    </insert>

    <select id="queryShareTo" resultType="java.util.Map">
        select 'All Users' as name, 'ALL' as "VALUE" from dual
        union all
        select '[' || t.sesa_code || '] ' || t.user_name,t.sesa_code  from SY_USER_MASTER_DATA t
    </select>

    <select id="queryCascader" resultType="java.util.Map">
        select *
          from scpa.MATERIAL_MASTER_FILTER_V
         where category in (
                            'LOCAL_PRODUCT_LINE',
                            'LOCAL_PRODUCT_FAMILY',
                            'LOCAL_PRODUCT_SUBFAMILY',
                            'PRODUCT_LINE',
                            'PLANT_CODE',
                            'CLUSTER_NAME',
                            'ENTITY',
                            'BU',
                            'MRP_CONTROLLER',
                            'LOCAL_BU',
                            'MATERIAL_OWNER_SESA'
             )
         order by category, decode(name, 'Others', 'zzz', name)
    </select>

    <select id="querySavedParameters" resultType="com.starter.home.bean.WorkstationParameter">
        select size_ratio, material, vendor_code, filters,
               nvl(group_type, 'By Category') group_type
        from scpa.sy_workstation_params t where user_id = #{_parameter, jdbcType=VARCHAR}
    </select>

    <select id="queryWidgetById" resultType="com.starter.home.bean.WidgetInfo">
        select id, title, group_name, scripts, details_scripts, classification,
               report_type, link_to, cachable, create_by$ as create_by
        from sy_workstation where id = #{id, jdbcType=VARCHAR}
    </select>

    <select id="queryCurrentShareTo" resultType="java.lang.String">
        select t.shared_user from sy_workstation_share_to t where t.widget_id = #{id, jdbcType=VARCHAR}
    </select>

    <sql id="queryDetailsSQL">
        ${scripts}
    </sql>

    <select id="queryDetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryDetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryDetails" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryDetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryAdminCnt" resultType="java.lang.Integer">
        select count(1) from SY_MENU_AUTH t
         where t.user_id = upper(#{userid,jdbcType=VARCHAR})
           and t.menu_code = #{parentCode,jdbcType=VARCHAR}
           and t.accessible = 'true'
           and lower(t.auth_details) = 'admin'
    </select>
</mapper>
