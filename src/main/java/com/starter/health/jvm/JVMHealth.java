package com.starter.health.jvm;

import java.io.File;
import java.lang.management.ClassLoadingMXBean;
import java.lang.management.CompilationMXBean;
import java.lang.management.GarbageCollectorMXBean;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;

import com.sun.management.OperatingSystemMXBean;

import java.lang.management.RuntimeMXBean;
import java.lang.management.ThreadMXBean;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.starter.context.bean.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

@Component
public class JVMHealth {

    public static final String START_DATE = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date());
    private static final String INSTANCE_ID = UUID.randomUUID().toString();
    private @Value("${spring.application.name}")
    String applicationName;
    private @Resource
    Response response;

    public Response health() {
        Map<String, Object> resultMap = new LinkedHashMap<>();
        resultMap.put("application name", applicationName);
        resultMap.put("instance id", INSTANCE_ID);
        resultMap.put("start time", START_DATE);
        resultMap.put("app path", new File("").getAbsolutePath());
        resultMap.putAll(getSystem());
        resultMap.putAll(getMemoryInfo());
        resultMap.putAll(getClassLoading());
        resultMap.putAll(getCompilation());
        resultMap.putAll(getThread());
        resultMap.putAll(getGarbageCollector());
        resultMap.putAll(getJvmInfo());
        return response.setBody(resultMap);
    }

    /**
     * Java 虚拟机的内存系统
     */
    private Map<String, Object> getMemoryInfo() {
        Map<String, Object> resultMap = new LinkedHashMap<>();
        MemoryMXBean mem = ManagementFactory.getMemoryMXBean();
        MemoryUsage memory = mem.getHeapMemoryUsage();
        resultMap.put("jvm memory used", this.formatSize(memory.getUsed()) + " (" + new DecimalFormat("#.00").format(memory.getUsed() * 100.0 / memory.getMax()) + "%)");
        resultMap.put("jvm memory max", this.formatSize(memory.getMax()));
        resultMap.put("jvm memory init", this.formatSize(memory.getInit()));
        resultMap.put("jvm memory committed", this.formatSize(memory.getCommitted()));
        return resultMap;
    }

    /**
     * Java 虚拟机在其上运行的操作系统
     */
    private Map<String, Object> getSystem() {
        Map<String, Object> resultMap = new LinkedHashMap<>();
        OperatingSystemMXBean op = (OperatingSystemMXBean) ManagementFactory.getOperatingSystemMXBean();
        resultMap.put("os name", op.getName());
        resultMap.put("os version", op.getVersion());
        resultMap.put("os architecture", op.getArch());
        resultMap.put("os processors", op.getAvailableProcessors());
        resultMap.put("os processors (%)", BigDecimal.valueOf(op.getCpuLoad() * 100).setScale(2, RoundingMode.HALF_UP).doubleValue());
        resultMap.put("os total memory", this.formatSize(op.getTotalMemorySize()));
        resultMap.put("os used memory", this.formatSize(op.getTotalMemorySize() - op.getFreeMemorySize()));
        resultMap.put("os memory usage (%)", BigDecimal.valueOf((op.getTotalMemorySize() - op.getFreeMemorySize()) * 1.0 / op.getTotalMemorySize() * 100).setScale(2, RoundingMode.HALF_UP).doubleValue());
        return resultMap;
    }

    /**
     * Java 虚拟机的运行时系统
     */
    private Map<String, Object> getJvmInfo() {
        Map<String, Object> resultMap = new LinkedHashMap<>();
        RuntimeMXBean mxbean = ManagementFactory.getRuntimeMXBean();
        resultMap.put("jvm properties", mxbean.getSystemProperties());
        resultMap.put("jvm arguments", mxbean.getInputArguments());
        return resultMap;
    }

    /**
     * Java 虚拟机的类加载系统
     */
    private Map<String, Object> getClassLoading() {
        Map<String, Object> resultMap = new LinkedHashMap<>();
        ClassLoadingMXBean cl = ManagementFactory.getClassLoadingMXBean();
        resultMap.put("total loaded class count", cl.getTotalLoadedClassCount());
        resultMap.put("loaded class count", cl.getLoadedClassCount());
        resultMap.put("unloaded class count", cl.getUnloadedClassCount());
        return resultMap;
    }

    /**
     * Java 虚拟机的编译系统
     */
    private Map<String, Object> getCompilation() {
        Map<String, Object> resultMap = new LinkedHashMap<>();
        CompilationMXBean com = ManagementFactory.getCompilationMXBean();
        resultMap.put("total compilation time", com.getTotalCompilationTime());
        resultMap.put("compilation name", com.getName());
        return resultMap;
    }

    /**
     * Java 虚拟机的线程系统
     */
    private Map<String, Object> getThread() {
        Map<String, Object> resultMap = new LinkedHashMap<>();
        ThreadMXBean thread = ManagementFactory.getThreadMXBean();
        resultMap.put("thread count", thread.getThreadCount());
        resultMap.put("current thread user time", thread.getCurrentThreadUserTime());
        return resultMap;
    }

    /**
     * Java 虚拟机中的垃圾回收器。
     */
    private Map<String, Object> getGarbageCollector() {
        Map<String, Object> resultMap = new LinkedHashMap<>();
        List<GarbageCollectorMXBean> gc = ManagementFactory.getGarbageCollectorMXBeans();
        for (GarbageCollectorMXBean garbageCollectorMXBean : gc) {
            resultMap.put("gc name", garbageCollectorMXBean.getName());
            resultMap.put("gc collection count", garbageCollectorMXBean.getCollectionCount());
            resultMap.put("gc collection time", garbageCollectorMXBean.getCollectionTime());
        }
        return resultMap;
    }

    private String formatSize(long size) {
        try {
            DecimalFormat df = new DecimalFormat("#.00");
            double mbSize = size / 1024.0 / 1024.0;
            return df.format(mbSize) + "MB";
        } catch (Exception e) {
            return "--";
        }
    }
}
