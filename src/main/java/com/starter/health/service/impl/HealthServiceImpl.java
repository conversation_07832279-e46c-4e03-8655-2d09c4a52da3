package com.starter.health.service.impl;

import com.starter.health.dao.IHealthDao;
import com.starter.health.service.IHealthService;
import com.starter.utils.Utils;
import com.sun.management.OperatingSystemMXBean;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.lang.management.ManagementFactory;
import java.math.BigDecimal;
import java.math.RoundingMode;

@Service
@Scope("prototype")
@Transactional
public class HealthServiceImpl implements IHealthService {

    @Resource
    private IHealthDao healthDao;

    @Value("${spring.serverid}")
    private String serverid;

    private static BigDecimal[] TRAFFIC_COUNTER = new BigDecimal[]{BigDecimal.valueOf(-1), BigDecimal.valueOf(-1)};
    private static long TRAFFIC_TIMER = -1L;

    static {
        String os = System.getProperty("os.name");
        try {
            if (StringUtils.containsIgnoreCase(os, "Windows")) {
                TRAFFIC_COUNTER = new HealthServiceImpl().getTrafficWindows();
            } else if (StringUtils.containsIgnoreCase(os, "Linux")) {
                TRAFFIC_COUNTER = new HealthServiceImpl().getTrafficLinux();
            }
            System.out.println("==> TRAFFIC_COUNTER: [" + StringUtils.join(TRAFFIC_COUNTER, ",") + ']');
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void saveCPUWorkload() {
        try {
            String os = System.getProperty("os.name");
            BigDecimal[] workload = new BigDecimal[]{BigDecimal.ZERO, BigDecimal.ZERO};

            BigDecimal received = null;
            BigDecimal sent = null;
            if (StringUtils.containsIgnoreCase(os, "Windows")) {
                workload = this.getCpuWorkloadWindows();

                if (TRAFFIC_TIMER != -1L) {
                    double timeGap = (System.currentTimeMillis() - TRAFFIC_TIMER) / 1000.0;
                    BigDecimal[] currentStat = this.getTrafficWindows();
                    received = currentStat[0].subtract(TRAFFIC_COUNTER[0]).divide(BigDecimal.valueOf(timeGap), 2, RoundingMode.HALF_UP).divide(BigDecimal.valueOf(1024), 1, RoundingMode.HALF_UP);
                    sent = currentStat[1].subtract(TRAFFIC_COUNTER[1]).divide(BigDecimal.valueOf(timeGap), 2, RoundingMode.HALF_UP).divide(BigDecimal.valueOf(1024), 1, RoundingMode.HALF_UP);
                }
                TRAFFIC_TIMER = System.currentTimeMillis();
                TRAFFIC_COUNTER = this.getTrafficWindows();
            } else if (StringUtils.containsIgnoreCase(os, "Linux")) {
                workload = this.getCpuWorkloadLinux();

                if (TRAFFIC_TIMER != -1L) {
                    double timeGap = (System.currentTimeMillis() - TRAFFIC_TIMER) / 1000.0;
                    BigDecimal[] currentStat = this.getTrafficLinux();
                    received = currentStat[0].subtract(TRAFFIC_COUNTER[0]).divide(BigDecimal.valueOf(timeGap), 2, RoundingMode.HALF_UP).divide(BigDecimal.valueOf(1024), 1, RoundingMode.HALF_UP);
                    sent = currentStat[1].subtract(TRAFFIC_COUNTER[1]).divide(BigDecimal.valueOf(timeGap), 2, RoundingMode.HALF_UP).divide(BigDecimal.valueOf(1024), 1, RoundingMode.HALF_UP);
                }
                TRAFFIC_TIMER = System.currentTimeMillis();
                TRAFFIC_COUNTER = this.getTrafficLinux();
            }

            healthDao.saveCPUWorkload(workload[0], workload[1], received, sent, serverid);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public BigDecimal[] getCpuWorkloadLinux() throws IOException {
        OperatingSystemMXBean op = (OperatingSystemMXBean) ManagementFactory.getOperatingSystemMXBean();
        BigDecimal cpu = BigDecimal.valueOf(op.getCpuLoad() * 100).setScale(2, RoundingMode.HALF_UP);
        BigDecimal memory = BigDecimal.ZERO;
        Process pro = null;
        BufferedReader in = null;
        try {
            Runtime r = Runtime.getRuntime();
            String command = "cat /proc/meminfo";
            pro = r.exec(command);
            in = new BufferedReader(new InputStreamReader(pro.getInputStream()));
            String line;

            BigDecimal total = BigDecimal.ZERO;
            BigDecimal available = BigDecimal.ZERO;
            while ((line = in.readLine()) != null) {
                String[] memInfo = line.split("\\s+");
                if (memInfo[0].startsWith("MemAvailable")) {
                    available = new BigDecimal(StringUtils.removeIgnoreCase(memInfo[1], "kb"));
                } else if (memInfo[0].startsWith("MemTotal")) {
                    total = new BigDecimal(StringUtils.removeIgnoreCase(memInfo[1], "kb"));
                }
            }

            if (BigDecimal.ZERO.compareTo(total) != 0) {
                BigDecimal usage = total.subtract(available);
                memory = usage.multiply(BigDecimal.valueOf(100)).divide(total, 2, RoundingMode.HALF_UP);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (in != null) {
                in.close();
            }
            if (pro != null) {
                pro.destroy();
            }
        }

        return new BigDecimal[]{cpu, memory};
    }

    public BigDecimal[] getCpuWorkloadWindows() {
        OperatingSystemMXBean op = (OperatingSystemMXBean) ManagementFactory.getOperatingSystemMXBean();
        BigDecimal cpu = BigDecimal.valueOf(op.getCpuLoad() * 100).setScale(2, RoundingMode.HALF_UP);
        BigDecimal memory = BigDecimal.valueOf((op.getTotalMemorySize() - op.getFreeMemorySize()) * 1.0 / op.getTotalMemorySize() * 100).setScale(2, RoundingMode.HALF_UP);
        return new BigDecimal[]{cpu, memory};
    }

    public BigDecimal[] getTrafficWindows() throws IOException {
        BigDecimal received = BigDecimal.ZERO;
        BigDecimal sent = BigDecimal.ZERO;
        Process pro = null;
        BufferedReader in = null;
        try {
            Runtime r = Runtime.getRuntime();
            String command = "netstat -e";
            pro = r.exec(command);
            in = new BufferedReader(new InputStreamReader(pro.getInputStream(), "GBK"));
            String line;
            while ((line = in.readLine()) != null) {
                String[] trafficInfo = line.split("\\s+");
                if (trafficInfo.length == 3 && StringUtils.isNotBlank(trafficInfo[0]) == true) {
                    received = received.add(Utils.parseBigDecimal(trafficInfo[1], 0));
                    sent = sent.add(Utils.parseBigDecimal(trafficInfo[2], 0));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (in != null) {
                in.close();
            }
            if (pro != null) {
                pro.destroy();
            }
        }
        return new BigDecimal[]{received, sent};
    }

    public BigDecimal[] getTrafficLinux() throws IOException {
        BigDecimal received = BigDecimal.ZERO;
        BigDecimal sent = BigDecimal.ZERO;
        Process pro = null;
        BufferedReader in = null;
        try {
            Runtime r = Runtime.getRuntime();
            String command = "/usr/scp/sbin/ifconfig";
            pro = r.exec(command);
            in = new BufferedReader(new InputStreamReader(pro.getInputStream()));
            String line;

            while ((line = in.readLine()) != null) {
                if (line.trim().startsWith("RX packets")) {
                    String[] trafficInfo = line.trim().split("\\s+");
                    received = received.add(Utils.parseBigDecimal(trafficInfo[4], 0));
                } else if (line.trim().startsWith("TX packets")) {
                    String[] trafficInfo = line.trim().split("\\s+");
                    sent = sent.add(Utils.parseBigDecimal(trafficInfo[4], 0));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (in != null) {
                in.close();
            }
            if (pro != null) {
                pro.destroy();
            }
        }
        return new BigDecimal[]{received, sent};
    }

    @Override
    public void deleteHistoryData() {
        healthDao.deleteHistoryData();
    }
}
