<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.starter.health.dao.IHealthDao">
	<insert id="saveCPUWorkload">
		insert into SY_CPU_MONITOR (TIME, CPU, MEMORY, SERVER_ID, TRAFFIC_IN, TRAFFIC_OUT)
		VALUES
		(sysdate, #{cpu, jdbcType=DOUBLE}, #{memory, jdbcType=DOUBLE}, #{serverid, jdbcType=VARCHAR},
		#{received, jdbcType=DOUBLE}, #{sent, jdbcType=DOUBLE})
	</insert>

    <delete id="deleteHistoryData">
		delete sy_cpu_monitor where time &lt; sysdate - 90
	</delete>
</mapper>
