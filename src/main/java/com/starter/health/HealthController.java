package com.starter.health;

import com.starter.health.jvm.JVMHealth;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import org.springframework.context.annotation.Scope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

import java.util.Set;

@RestController
@Scope("prototype")
public class HealthController {

    @Resource
    private JVMHealth jvmHealth;

    @Resource
    private Response response;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @SchneiderRequestMapping(value = "/jvm", produces = {"application/json;charset=UTF-8"}, classification = SchneiderRequestMapping.PRIVATE)
    public Response health() {
        return jvmHealth.health();
    }

    @SchneiderRequestMapping(value = "/restart", classification = SchneiderRequestMapping.PRIVATE)
    public Response restartSystem() {
        // 停掉服务, docker会自动启动应用
        System.exit(0);
        return null;
    }

    @SchneiderRequestMapping(value = "/flush_cache", classification = SchneiderRequestMapping.PRIVATE)
    public Response flushCache() {
        Set<String> keys = redisTemplate.keys(Configuration.CACHED_KEY + "*");
        if (keys != null) {
            redisTemplate.delete(keys);
        }
        return response;
    }
}
