package com.starter.health.runtime;

import java.io.File;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

public class RuntimeThread implements Runnable {

    private long time = 5;
    private String command = null;
    private String dir = null;

    @Override
    public void run() {
        if (command == null || dir == null) {
            return;
        }
        try {
            TimeUnit.SECONDS.sleep(time);
            Runtime.getRuntime().exec(command, null, new File(dir));
        } catch (InterruptedException | IOException e) {
            e.printStackTrace();
        }
    }

    public long getTime() {
        return time;
    }

    public void setTime(long time) {
        this.time = time;
    }

    public String getCommand() {
        return command;
    }

    public void setCommand(String command) {
        this.command = command;
    }

    public String getDir() {
        return dir;
    }

    public void setDir(String dir) {
        this.dir = dir;
    }
}
