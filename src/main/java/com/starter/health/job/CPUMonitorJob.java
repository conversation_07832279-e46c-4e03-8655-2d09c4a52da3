package com.starter.health.job;

import com.starter.context.configuration.IJobClient;
import com.starter.health.service.IHealthService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Calendar;

@Component
public class CPUMonitorJob implements IJobClient {

    @Value("${spring.monitor}")
    private boolean monitor;

    @Resource
    private IHealthService healthService;


    @Override
    public boolean triggered(Calendar fireTime) {
        if (monitor) {
            int minute = fireTime.get(Calendar.MINUTE);
            return minute % 2 == 0; // 每2分钟触发一次
        } else {
            return false;
        }
    }

    @Override
    public void run() {
        healthService.saveCPUWorkload();
    }
}
