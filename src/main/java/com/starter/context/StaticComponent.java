package com.starter.context;

import org.springframework.stereotype.Component;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.InputStream;

@Component
public class StaticComponent {

    private static BufferedImage imageE = null;
    private static BufferedImage imageK = null;

    static {
        try {
            InputStream inputStreamE = StaticComponent.class.getClassLoader().getResourceAsStream("files/color-e.jpg");
            InputStream inputStreamK = StaticComponent.class.getClassLoader().getResourceAsStream("files/color-k.jpg");
            if (inputStreamE == null) {
                imageE = null;
            } else {
                imageE = ImageIO.read(inputStreamE);
            }
            if (inputStreamK == null) {
                imageK = null;
            } else {
                imageK = ImageIO.read(inputStreamK);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 获取均匀分布色带
     *
     * @return
     */
    public static BufferedImage getColorImageE() {
        return imageE;
    }

    /**
     * 获取K值分布色带
     *
     * @return
     */
    public static BufferedImage getColorImageK() {
        return imageK;
    }
}
