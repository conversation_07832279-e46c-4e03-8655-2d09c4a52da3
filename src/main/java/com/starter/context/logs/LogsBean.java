package com.starter.context.logs;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;

public class LogsBean {
	@JSONField(ordinal = 1)
	private long timestamp = System.currentTimeMillis();
	@JSONField(ordinal = 2)
	private String application;
	@J<PERSON><PERSON>ield(ordinal = 3, name = "class")
	private String clazz;
	@JSO<PERSON>ield(ordinal = 4)
	private String level;
	@JSONField(ordinal = 5)
	private String message;

	public LogsBean() {

	}

	public LogsBean(String level, Object message) {
		this.level = level;
		this.message = String.valueOf(message);
	}

	public long getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(long timestamp) {
		this.timestamp = timestamp;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public String getLevel() {
		return level;
	}

	public void setLevel(String level) {
		this.level = level;
	}

	public String getClazz() {
		return clazz;
	}

	public void setClazz(String clazz) {
		this.clazz = clazz;
	}

	public String getApplication() {
		return application;
	}

	public void setApplication(String application) {
		this.application = application;
	}

	public String toString() {
		return JSON.toJSONString(this);
	}
}
