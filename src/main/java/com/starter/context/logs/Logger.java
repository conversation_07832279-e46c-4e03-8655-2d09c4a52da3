package com.starter.context.logs;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import jakarta.annotation.Resource;

import org.apache.commons.lang3.StringUtils;

import com.starter.context.SpringContext;

@SuppressWarnings("rawtypes")
public class Logger {
	private String name;
	private static String application;
	private static ConcurrentMap<String, Logger> loggers = new ConcurrentHashMap<String, Logger>();

	@Resource
	private LogFeignClient logFeignClient;

	protected Logger(String name) {
		this.name = StringUtils.substring(name, StringUtils.lastIndexOf(name, ".") + 1);
	}

	public void error(Object error) {
		if (error instanceof Exception) {
			Exception ex = (Exception) error;
			StackTraceElement[] stackTraceElement = ex.getStackTrace();
			int size = Math.min(1, stackTraceElement.length);
			StringBuffer eb = new StringBuffer();
			eb.append(ex.getClass());
			eb.append(":");
			eb.append(ex.getMessage());
			eb.append(" at ");
			eb.append(StringUtils.join(stackTraceElement, " ", 0, size));
			this.log(new LogsBean("error", error));
		} else {
			this.log(new LogsBean("error", error));
		}
	}

	public void info(Object info) {
		this.log(new LogsBean("info", info));
	}

	public void debug(Object debug) {
		this.log(new LogsBean("debug", debug));
	}

	public void warn(Object warn) {
		this.log(new LogsBean("warn", warn));
	}

	private void log(LogsBean bean) {
		try {
			bean.setClazz(name);
			if (application == null) {
				application = SpringContext.getApplicationContext().getEnvironment().getProperty("spring.application.name");
			}
			bean.setApplication(application);
			logFeignClient.saveLog(bean);
		} catch (Exception e) {
			System.err.println("save logs error:" + e.getMessage());
		}
	}

	public static Logger getLogger(Class clazz) {
		return getLogger(clazz.getName());
	}

	public static Logger getLogger(String name) {
		Logger instance = loggers.get(name);
		if (instance != null) {
			return instance;
		} else {
			Logger newInstance = new Logger(name);
			Logger oldInstance = loggers.putIfAbsent(name, newInstance);
			return oldInstance == null ? newInstance : oldInstance;
		}
	}
}
