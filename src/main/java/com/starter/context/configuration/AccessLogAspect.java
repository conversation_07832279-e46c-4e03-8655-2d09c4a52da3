package com.starter.context.configuration;

import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.bean.Status;
import com.starter.context.servlet.UserContextHolder;
import org.apache.catalina.connector.ResponseFacade;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.*;

@Component
@Aspect
public class AccessLogAspect {
    @Resource
    private JdbcTemplate jdbcTemplate;

    @Pointcut(value = "(execution(* *.*(..)) && @annotation(com.starter.context.bean.SchneiderRequestMapping))")
    private void pointcut() {
    }

    @Around(value = "pointcut()")
    public Object accessLogAround(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        String url = "/";
        String res = null;
        String req = null;
        Object resObj;
        String oprType = "query";
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Object[] args = joinPoint.getArgs();
            url = this.getUrlByRequestArgs(args);
            if (url == null) {
                url = this.getUrlBySignature(signature);
            }

            String methodURL = "";
            if (url != null) {
                String[] us = url.split("/");
                methodURL = us[us.length - 1];
            }

            if (methodURL.contains("split_") || methodURL.contains("save_") || methodURL.contains("generate_") || methodURL.contains("refresh_")
                    || methodURL.contains("clear_") || methodURL.contains("send_") || methodURL.contains("sync_") || methodURL.contains("calc")
                    || methodURL.contains("modify_") || methodURL.contains("flush_") || methodURL.contains("update_") || methodURL.contains("start_")
                    || methodURL.contains("sent_") || methodURL.contains("kill_") || methodURL.contains("execute_") || methodURL.contains("close_")
                    || methodURL.contains("copy_") || methodURL.contains("mark_") || methodURL.contains("create_") || methodURL.contains("flush")
                    || methodURL.contains("approve_") || methodURL.contains("share_") || methodURL.contains("run_") || methodURL.contains("merge_")
                    || methodURL.contains("compose_")) {
                oprType = "upsert";
            } else if (methodURL.startsWith("upload_")) {
                oprType = "upload";
            } else if (methodURL.startsWith("download_")) {
                oprType = "download";
            } else if (methodURL.equals("delete_")) {
                oprType = "delete";
            }
            req = this.getRequestBodyByArgs(args);
            resObj = joinPoint.proceed(args);
            if (resObj != null) {
                res = String.valueOf(resObj);
            }
            long resSize;
            if (oprType.equals("download")) {
                resSize = this.getDownloadSizeByArgs(args);
            } else {
                resSize = StringUtils.length(res);
            }

            String error = null;
            if (resObj instanceof Response) {
                if (((Response) resObj).getHeader().getStatus() == Status.INTERNAL_SERVER_ERROR.getStatus()) {
                    error = ((Response) resObj).getHeader().getMessage();
                }
            }

            if (oprType.equals("upsert") || oprType.equals("delete")) {
                this.saveAccessLog(startTime, url, req, res, error, UserContextHolder.getUserID(), oprType, StringUtils.length(req), resSize);
            } else {
                this.saveAccessLog(startTime, url, null, null, error, UserContextHolder.getUserID(), oprType, StringUtils.length(req), resSize);
            }
        } catch (Throwable throwable) {
            this.saveAccessLog(startTime, url, req, res, throwable.getLocalizedMessage(), UserContextHolder.getUserID(), oprType, StringUtils.length(req), StringUtils.length(res));
            throw throwable;
        }
        return resObj;
    }

    public long getDownloadSizeByArgs(Object[] args) {
        for (Object arg : args) {
            if (arg instanceof HttpServletResponse) {
                return ((ResponseFacade) arg).getContentWritten();
            }
        }
        return -1;
    }

    public String getUrlByRequestArgs(Object[] args) {
        for (Object arg : args) {
            if (arg instanceof HttpServletRequest) {
                return ((HttpServletRequest) arg).getRequestURI();
            }
        }
        return null;
    }

    public String getUrlBySignature(MethodSignature signature) {
        SchneiderRequestMapping classRequestMapping = AnnotationUtils.findAnnotation(signature.getMethod().getDeclaringClass(), SchneiderRequestMapping.class);
        SchneiderRequestMapping methodRequestMapping = AnnotationUtils.findAnnotation(signature.getMethod(), SchneiderRequestMapping.class);

        String[] classUrl = classRequestMapping == null ? new String[]{"/"} : classRequestMapping.value();
        String[] methodUrl = methodRequestMapping == null ? new String[]{"/"} : methodRequestMapping.value();

        String cUrl = classUrl.length > 0 ? classUrl[0] : "/";
        String mUrl = methodUrl.length > 0 ? methodUrl[0] : "/";
        String url = cUrl + "/" + mUrl;
        while (url.contains("//")) {
            url = url.replace("//", "/");
        }
        return url;
    }

    public String getRequestBodyByArgs(Object[] args) {
        for (Object arg : args) {
            if (arg instanceof HttpServletRequest) {
                HttpServletRequest request = ((HttpServletRequest) arg);
                if ("application/json;charset=UTF-8".equalsIgnoreCase(request.getHeader("Content-Type"))) {
                    try (BufferedReader streamReader = new BufferedReader(new InputStreamReader(request.getInputStream(), StandardCharsets.UTF_8))) {
                        // @RequestBody 会读取request,导致这里无法继续读取request, 所以, 在使用@RequestBody的方法上,是无法从parameterMap中获取到值的
                        if (request.getInputStream().isFinished()) {
                            return null;
                        }
                        StringBuilder responseStrBuilder = new StringBuilder();
                        String inputStr;
                        while ((inputStr = streamReader.readLine()) != null) {
                            responseStrBuilder.append(inputStr);
                        }

                        String responseStr = responseStrBuilder.toString();

                        request.setAttribute("_parameter_str", responseStr);
                        return responseStrBuilder.toString();
                    } catch (Exception e) {
                        return null;
                    }
                }
            }
        }
        return null;
    }

    static List<Map<String, Object>> logBuffer = new ArrayList<>();

    public void saveAccessLog(long startTime, String accessUrl, String req, String res, String error, String user, String oprType, long reqSize, long resSize) {
        Map<String, Object> param = new HashMap<>();
        param.put("accessTime", new java.sql.Date(startTime));
        param.put("accessUrl", accessUrl);
        param.put("req", req);
        param.put("res", res);
        param.put("error", StringUtils.substring(error, 0, 255));
        param.put("user", user);
        param.put("oprType", oprType);
        param.put("reqSize", reqSize);
        param.put("resSize", resSize);
        param.put("resCode", error == null ? "200" : "500");
        param.put("timecost", System.currentTimeMillis() - startTime);
        logBuffer.add(param);

        // 如果日志在内存中超过了一定数量
        if (logBuffer.size() > 19) {
            synchronized (this) {
                try {
                    jdbcTemplate.batchUpdate("INSERT INTO SY_METHOD_ACCESS_LOGS " + "(ACCESS_URL, REQUEST_BODY, RESPONSE_TXT, USER_ID, OPR_TYPE, " + "REQUEST_LENGTH, RESPONSE_LENGTH, ACCESS_TIME, TIMECOST_IN_MS, RESPONSE_CODE, ERROR_MESSAGE) " + "VALUES (?,?,?,?,?,?,?,?,?,?,?)", new BatchPreparedStatementSetter() {
                        @Override
                        public void setValues(PreparedStatement ps, int i) throws SQLException {
                            Map<String, Object> p = logBuffer.get(i);
                            ps.setString(1, (String) p.get("accessUrl"));
                            ps.setString(2, (String) p.get("req"));
                            ps.setString(3, (String) p.get("res"));
                            ps.setString(4, (String) p.get("user"));
                            ps.setString(5, (String) p.get("oprType"));
                            ps.setLong(6, (Long) p.get("reqSize"));
                            ps.setLong(7, (Long) p.get("resSize"));
                            ps.setDate(8, (java.sql.Date) p.get("accessTime"));
                            ps.setLong(9, (Long) p.get("timecost"));
                            ps.setString(10, (String) p.get("resCode"));
                            ps.setString(11, (String) p.get("error"));
                        }

                        @Override
                        public int getBatchSize() {
                            return logBuffer.size();
                        }
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    logBuffer.clear();
                }
            }
        }
    }
}
