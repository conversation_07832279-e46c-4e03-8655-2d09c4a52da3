package com.starter.context.configuration;

import java.util.Calendar;

public interface IJobClient extends Runnable {

	/**
	 * <pre>
	 * 判断是否触发任务
	 *
	 * 1. 系统每分钟会自动检查是否有任务需要执行
	 * 2. fireTime为系统检查时间(GMT+8,中国时间),是一个calendar对象
	 * 3. 用户可以通过实现此方法,来实现定期任务执行
	 * 4. 返回值为true,则执行run方法,false不执行
	 *
	 *  e.g, 每天12点执行任务
	 *
	 *  int hour = fireTime.get(Calendar.HOUR_OF_DAY);
	 *  int minute = fireTime.get(Calendar.MINUTE);
	 *
	 *  if (hour == 12 && minute == 0) {
	 *  	return true;
	 *  } else {
	 *  	return false;
	 *  }
	 * </pre>
	 *
	 * @param fireTime GMT+8时区calendar对象,当前任务调用时间
	 * @return
	 */
	public boolean triggered(Calendar fireTime);

	/**
	 * 执行任务
	 */
	@Override
	public void run();
}
