package com.starter.context.configuration;

import com.alibaba.fastjson.JSON;
import com.starter.utils.EncryptionUtil;
import com.starter.context.servlet.UserContextHolder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachingConfigurer;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.jedis.JedisClientConfiguration;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import redis.clients.jedis.JedisPoolConfig;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

@Configuration
//@EnableCaching
public class RedisConfiguration implements CachingConfigurer {

    @Value("${spring.data.redis.enable}")
    private boolean redisEnable;

    @Bean
    public JedisConnectionFactory redisConnectionFactory(RedisProperties properties) {
        RedisStandaloneConfiguration configuration = new RedisStandaloneConfiguration();
        configuration.setHostName(properties.getHost());
        configuration.setPort(properties.getPort());
        configuration.setDatabase(properties.getDatabase());
        configuration.setPassword(properties.getPassword());

        JedisClientConfiguration.JedisClientConfigurationBuilder jpcb = JedisClientConfiguration.builder();
        jpcb.connectTimeout(Duration.ofSeconds(6));
        jpcb.readTimeout(Duration.ofSeconds(6));
        JedisClientConfiguration clientConfiguration = jpcb.usePooling().build();
        return new JedisConnectionFactory(configuration, clientConfiguration);
    }

    @Bean
    public JedisPoolConfig jedisPoolConfig(RedisProperties properties) {
        JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
        jedisPoolConfig.setMaxTotal(properties.getJedis().getPool().getMaxActive());
        jedisPoolConfig.setMinIdle(properties.getJedis().getPool().getMinIdle());
        jedisPoolConfig.setMaxIdle(properties.getJedis().getPool().getMaxIdle());
        jedisPoolConfig.setMaxWait(Duration.ofMillis(properties.getJedis().getPool().getMaxWait().toMillis()));
        return jedisPoolConfig;
    }

    @Bean
    public RedisTemplate<String, ?> redisTemplate(JedisConnectionFactory connectionFactory) {
        RedisTemplate<String, ?> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(connectionFactory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        // redisTemplate.setValueSerializer(new GenericJackson2JsonRedisSerializer());
        // redisTemplate.setHashValueSerializer(new GenericJackson2JsonRedisSerializer());
        return redisTemplate;
    }

    @Bean
    public CacheManager cacheManager(JedisConnectionFactory connectionFactory) {
        RedisCacheConfiguration defaultConfig = RedisCacheConfiguration.defaultCacheConfig().entryTtl(Duration.ofSeconds(3600)).serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer())).serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer())).disableCachingNullValues();

        Map<String, RedisCacheConfiguration> extraConfig = new HashMap<>();
        if (redisEnable == true) {
            extraConfig.put(com.starter.context.bean.Configuration.APPLICATION_NAME + ":1m", RedisCacheConfiguration.defaultCacheConfig().entryTtl(Duration.ofSeconds(55)).serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer())).serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer())).disableCachingNullValues());
            extraConfig.put(com.starter.context.bean.Configuration.APPLICATION_NAME + ":5m", RedisCacheConfiguration.defaultCacheConfig().entryTtl(Duration.ofSeconds(300)).serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer())).serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer())).disableCachingNullValues());
            extraConfig.put(com.starter.context.bean.Configuration.APPLICATION_NAME + ":15m", RedisCacheConfiguration.defaultCacheConfig().entryTtl(Duration.ofSeconds(900)).serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer())).serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer())).disableCachingNullValues());
            extraConfig.put(com.starter.context.bean.Configuration.APPLICATION_NAME + ":30m", RedisCacheConfiguration.defaultCacheConfig().entryTtl(Duration.ofSeconds(1800)).serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer())).serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer())).disableCachingNullValues());
            extraConfig.put(com.starter.context.bean.Configuration.APPLICATION_NAME + ":1h", RedisCacheConfiguration.defaultCacheConfig().entryTtl(Duration.ofSeconds(3600)).serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer())).serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer())).disableCachingNullValues());
            extraConfig.put(com.starter.context.bean.Configuration.APPLICATION_NAME + ":1d", RedisCacheConfiguration.defaultCacheConfig().entryTtl(Duration.ofSeconds(21600)).serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer())).serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer())).disableCachingNullValues());
        } else {
            defaultConfig = RedisCacheConfiguration.defaultCacheConfig().entryTtl(Duration.ofSeconds(1)).serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer())).serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer())).disableCachingNullValues();
        }

        return new RedisCacheManager(RedisCacheWriter.nonLockingRedisCacheWriter(connectionFactory), defaultConfig, extraConfig);
    }

    /**
     * 如果globalCache = true
     * Key = className:method:params
     * <p>
     * 如果globalCache = false
     * Key = className:{SESA}:method:params
     */
    @Bean
    @SuppressWarnings("unchecked")
    public KeyGenerator keyGenerator() {

        return (target, method, params) -> {
            StringBuilder r = new StringBuilder();
            StringBuilder s = new StringBuilder();
            String className = target.getClass().getName();

            String[] cns = className.split("\\.");
            List<String> t = new ArrayList<>();
            String cname = null;
            for (int i = 0; i < cns.length; i++) {
                String c = cns[i];
                if (i == cns.length - 1) {
                    cname = c;
                } else {
                    t.add(c.substring(0, 1));
                }
            }
            r.append(cname).append(".").append(StringUtils.join(t, "."));
            r.append(":");

            for (Object obj : params) {
                try {
                    if (redisEnable == true) {
                        if (obj instanceof Map) { // 如果参数为Map, 则遍历MAP
                            Map<Object, Object> pm = (Map<Object, Object>) obj;
                            List<Object> keys = new ArrayList<>(pm.keySet()); // 为了防止JSON数据存入无序MAP导致缓存失效, 这里会按照Map中的Key进行排列, 然后再进行缓存计算
                            keys = keys.stream().sorted(Comparator.comparing(String::valueOf)).collect(Collectors.toList());

                            Map<Object, Object> parameterMap = new LinkedHashMap<>();

                            for (Object key : keys) {
                                // token不作为缓存键, session也不做为缓存键, 因为会用多级菜单来区分用户, SCPA也不会作为缓存键, 三个下划线开头的也不作为缓存键
                                String keyStr = String.valueOf(key);
                                if (StringUtils.equals(keyStr, "token") || StringUtils.equals(keyStr, "session") || StringUtils.equals(String.valueOf(key), "SCPA")
                                        || StringUtils.startsWith(keyStr, "___")) {
                                    continue;
                                }
                                parameterMap.put(key, pm.get(key));
                            }
                            s.append(JSON.toJSONString(parameterMap));
                        } else {
                            s.append(JSON.toJSONString(obj));
                        }
                    }
                } catch (Exception ignored) {

                }
            }
            if (UserContextHolder.isGlobalCache() == false) {
                r.append(UserContextHolder.getUserID());
                r.append(":");
            }
            r.append(method.getName());
            r.append(":");
            r.append(EncryptionUtil.md5Short6(s.toString()));
            return r.toString();
        };
    }
}
