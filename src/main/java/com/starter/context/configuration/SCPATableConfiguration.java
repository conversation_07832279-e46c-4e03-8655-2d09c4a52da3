package com.starter.context.configuration;

import com.starter.context.bean.TblColumn;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

public class SCPATableConfiguration {
    private static final Map<String, List<TblColumn>> TABLE_MAPPING_COLUMN = new HashMap<>();

    private static final Map<String, List<String>> COLUMN_MAPPING_TABLE = new HashMap<>();

    private static final List<String> ALL_TABLES = new ArrayList<>();

    public static final String FILTER_SPEEDUP_TABLE = "SY_FILTER_SPEEDUP";

    public static void setTabCols(List<Map<String, String>> source) {
        for (Map<String, String> map : source) {
            String table = StringUtils.upperCase(map.get("TABLE_NAME"));
            List<TblColumn> list = TABLE_MAPPING_COLUMN.computeIfAbsent(table, k -> new ArrayList<>());
            list.add(new TblColumn(map));
            ALL_TABLES.add(table);

            String column = map.get("COLUMN_NAME");
            List<String> list2 = COLUMN_MAPPING_TABLE.computeIfAbsent(column, k -> new ArrayList<>());
            list2.add(table);
        }
    }

    public static List<String> getMatchedTableByColumnName(List<String> columns) {
        Set<String> set = null;
        for (String column : columns) {
            if (set == null) {
                set = new HashSet<>(COLUMN_MAPPING_TABLE.getOrDefault(column, new ArrayList<>()));
            } else {
                set.addAll(COLUMN_MAPPING_TABLE.getOrDefault(column, new ArrayList<>()));
            }
        }
        return set == null ? new ArrayList<>() : new ArrayList<>(set);
    }


    public static List<TblColumn> getMatchedTblColumns(List<String> tableNames) {
        List<String> index = new ArrayList<>();
        List<TblColumn> result = new ArrayList<>();

        for (String table : tableNames) {
            List<TblColumn> data = TABLE_MAPPING_COLUMN.get(table);
            if (data != null) {
                for (TblColumn column : data) {
                    if (index.contains(column.getName()) == false) {
                        index.add(column.getName());
                        result.add(column);
                    }
                }
            }
        }
        result.sort(Comparator.comparing(TblColumn::getName));
        return result;
    }

    public static List<TblColumn> getTblColumnsByTableName(String tableNames) {
        return TABLE_MAPPING_COLUMN.getOrDefault(tableNames, new ArrayList<>());
    }

    public static boolean isColumnInTable(String table, String column) {
        List<TblColumn> data = TABLE_MAPPING_COLUMN.get(table);
        if (data == null) {
            return false;
        }
        for (TblColumn tbl : data) {
            if (tbl.getName().equals(column)) {
                return true;
            }
        }
        return false;
    }

    public static List<String> getAllTables() {
        return ALL_TABLES;
    }
}
