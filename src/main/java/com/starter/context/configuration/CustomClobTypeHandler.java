package com.starter.context.configuration;

import org.apache.ibatis.type.ClobTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

/**
 * 将Oracle中的CLOB字段转为String
 *
 * 此方法只是将JDBC Type为CLOB和JAVA Type为Object的handler指向ClobTypeHandler
 *
 * <AUTHOR>
 *
 */
@MappedJdbcTypes(JdbcType.CLOB)
@MappedTypes(Object.class)
public class CustomClobTypeHandler extends ClobTypeHandler {

}
