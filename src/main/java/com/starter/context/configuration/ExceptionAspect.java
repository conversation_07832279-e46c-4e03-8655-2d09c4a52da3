package com.starter.context.configuration;

import com.starter.context.bean.Response;
import com.starter.context.bean.SCPRuntimeException;
import com.starter.context.bean.Status;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.lang.reflect.Method;
import java.util.Set;

@Component
@Aspect
@Order(0)
public class ExceptionAspect {

    @Resource
    private KeyGenerator keyGenerator;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Pointcut(value = "(execution(com.starter.context.bean.Response com.scp.*.service.impl..*.*(..)))")
    private void pointCut() {
    }

    @Around(value = "pointCut()")
    private Response process(ProceedingJoinPoint joinPoint) {
        try {
            Object[] args = joinPoint.getArgs();
            return (Response) joinPoint.proceed(args);
        } catch (RuntimeException e) {
            e.printStackTrace();
            // this.removeExceptionCache(joinPoint);
            if (e.getCause() instanceof SCPRuntimeException) {
                return new Response().set(Status.FORBIDDEN, e.getLocalizedMessage());
            }
            return new Response().setError(e);
        } catch (Exception e) {
            e.printStackTrace();
            // this.removeExceptionCache(joinPoint);
            return new Response().setError(e);
        } catch (Throwable throwable) {
            throwable.printStackTrace();
            // this.removeExceptionCache(joinPoint);
            return new Response().set(Status.INTERNAL_SERVER_ERROR, throwable.getLocalizedMessage());
        }
    }

    private void removeExceptionCache(ProceedingJoinPoint joinPoint) {
        try {
            Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
            String key = String.valueOf(keyGenerator.generate(joinPoint.getTarget(), method, joinPoint.getArgs()));
            key = key.substring(0, StringUtils.lastIndexOf(key, ":"));
            Set<String> stringSet = redisTemplate.keys(key);
            if (stringSet != null) {
                redisTemplate.delete(stringSet);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
