package com.starter.context.configuration;

import org.apache.poi.util.DefaultTempFileCreationStrategy;
import org.apache.poi.util.TempFile;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.File;

@Component
public class POITempDirComponent {
    /**
     * 设置使用SXSSFWorkbook对象导出excel报表时，TempFile使用的临时目录，代替{java.io.tmpdir}
     */
    @PostConstruct
    public void setExcelSXSSFWorkbookTmpPath() {
        String excelSXSSFWorkbookTmpPath = "/usr/springboot/tmp/profiles";
        File dir = new File(excelSXSSFWorkbookTmpPath);
        if (!dir.exists()) {
            if (dir.mkdirs()) {
                System.out.println("make directory ok." + excelSXSSFWorkbookTmpPath);
            }
        }
        TempFile.setTempFileCreationStrategy(new DefaultTempFileCreationStrategy(dir));
    }
}
