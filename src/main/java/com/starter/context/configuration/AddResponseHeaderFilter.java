package com.starter.context.configuration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

@Component
public class AddResponseHeaderFilter extends OncePerRequestFilter {

    /**
     * monitor本意是开启本机负载监控, 但是monitor一般只在PROD环境上启用, 所以monitor也被用来识别当前环境是否为PROD
     * 如果以后需要将两者区分开, 可以将这个配置分离出去
     * <p>
     * monitor = true, 代表着正式环境, 正式环境需要启动XSS等一系列安全验证
     */
    @Value("${spring.monitor}")
    private boolean monitor;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws IOException, ServletException {
        if (monitor) {
            response.addHeader("X-XSS-Protection", "1; mode=block");
            response.addHeader("Referrer-Policy", "same-origin");
            response.addHeader("Strict-Transport-Security", "max-age=31536000; includeSubDomains");
            response.addHeader("Content-Security-Policy", "img-src 'self' data:;style-src 'self' 'unsafe-inline'; script-src 'self' 'unsafe-inline' 'unsafe-eval';connect-src https://*.schneider-electric.com:*;font-src 'self' data:");
            response.addHeader("Cache-Control", "no-store, max-age=0");
            response.addHeader("X-Frame-Options", "DENY");
        }
        filterChain.doFilter(request, response);
    }
}
