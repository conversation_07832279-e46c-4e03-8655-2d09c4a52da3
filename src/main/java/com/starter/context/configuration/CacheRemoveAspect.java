package com.starter.context.configuration;

import com.starter.context.bean.CacheRemove;
import com.starter.context.servlet.UserContextHolder;
import org.apache.commons.lang3.RegExUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Component
@Aspect
public class CacheRemoveAspect {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Pointcut(value = "(execution(* *.*(..)) && @annotation(com.starter.context.bean.CacheRemove))")
    private void pointcut() {
    }

    @AfterReturning(value = "pointcut()")
    private void process(JoinPoint joinPoint) {
        //获取被代理的类
        Object target = joinPoint.getTarget();
        //获取切入方法的数据
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        //获取切入方法
        Method method = signature.getMethod();
        //获得注解
        CacheRemove cacheRemove = method.getAnnotation(CacheRemove.class);

        if (cacheRemove != null) {
            if (cacheRemove.key().length > 0) {
                for (String key : cacheRemove.key()) {
                    if (StringUtils.isNotBlank(key) && (key.equals("this") || key.startsWith("this."))) {
                        String className = target.getClass().getName();
                        String[] cns = className.split("\\.");
                        List<String> t = new ArrayList<>();
                        String cname = null;
                        for (int i = 0; i < cns.length; i++) {
                            String c = cns[i];
                            if (i == cns.length - 1) {
                                cname = c;
                            } else {
                                t.add(c.substring(0, 1));
                            }
                        }
                        String thisName = cname + "." + StringUtils.join(t, ".");
                        String key2 = StringUtils.replace(key, ".", ":");
                        if (cacheRemove.scope().equals(CacheRemove.SCOPE_USER)) {
                            thisName += ":" + UserContextHolder.getUserID();
                        } else if (cacheRemove.scope().equals(CacheRemove.SCOPE_ALL_USER)) {
                            thisName += ":*";
                        }
                        String newKey = RegExUtils.replaceFirst(key2, "this", thisName);
                        cleanRedisCache(cacheRemove.value() + "::" + newKey + ":*");
                    } else {
                        cleanRedisCache(cacheRemove.value() + "::" + key + ":*");
                    }
                }
            }
        }
    }

    private void cleanRedisCache(String key) {
        if (key != null) {
            Set<String> stringSet = redisTemplate.keys(key);
            if (stringSet != null) {
                redisTemplate.delete(stringSet);//删除缓存
            }
        }
    }

}
