package com.starter.context.configuration;

import java.util.Calendar;
import java.util.Map;
import java.util.TimeZone;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Scope;
import org.springframework.scheduling.quartz.QuartzJobBean;
import org.springframework.stereotype.Component;

import com.starter.context.SpringContext;

@Component
@Scope("prototype")
public class JobDispatcher extends QuartzJobBean {

	private void executeJob(Calendar fireTime) {
		ApplicationContext context = SpringContext.getApplicationContext();
		Map<String, IJobClient> jobMap = context.getBeansOfType(IJobClient.class);

		ExecutorService executorService = Executors.newFixedThreadPool(3);
		for (IJobClient jobClient : jobMap.values()) {
			if (jobClient.triggered(fireTime) == true) {
				executorService.submit(jobClient);
			}
		}
		executorService.shutdown();
	}

	@Override
	protected void executeInternal(JobExecutionContext context) throws JobExecutionException {
		Calendar fireTime = Calendar.getInstance();
		fireTime.setTime(context.getFireTime());
		fireTime.setTimeZone(TimeZone.getTimeZone("GMT+8"));
		this.executeJob(fireTime);
	}
}
