package com.starter.context.configuration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

/**
 * <pre>
 * 1. 配置跨域拦截和请求方法拦截
 * </pre>
 *
 * <AUTHOR>
 *
 */
@Configuration
public class CorsCustomerConfiguration {

	/**
     * monitor本意是开启本机负载监控, 但是monitor一般只在PROD环境上启用, 所以monitor也被用来识别当前环境是否为PROD
     * 如果以后需要将两者区分开, 可以将这个配置分离出去
     * <p>
     * monitor = true, 代表着正式环境, 正式环境需要启动XSS等一系列安全验证
     */
    @Value("${spring.monitor}")
    private boolean monitor;

    private final String allowedOriginPattern = monitor ? "https://scp-dss.cn.schneider-electric.com" : "*";

	private CorsConfiguration corsConfig() {
		CorsConfiguration corsConfiguration = new CorsConfiguration();
		corsConfiguration.addAllowedOriginPattern(allowedOriginPattern);
		corsConfiguration.addAllowedHeader("*");
		corsConfiguration.addAllowedMethod(HttpMethod.GET);
		corsConfiguration.addAllowedMethod(HttpMethod.POST);
		corsConfiguration.setAllowCredentials(true);
		corsConfiguration.setMaxAge(86400L);
		return corsConfiguration;
	}

	@Bean
	public CorsFilter corsFilter() {
		UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
		source.registerCorsConfiguration("/**", this.corsConfig());
		return new CorsFilter(source);
	}
}
