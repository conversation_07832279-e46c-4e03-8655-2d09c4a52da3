package com.starter.context.configuration;

import feign.codec.Encoder;
import feign.form.FormEncoder;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import jakarta.annotation.Resource;

@Configuration
public class FeignClientFormPostConfig {

    @Resource
    private ObjectFactory<HttpMessageConverters> httpMessageConverters;

    @Bean
    public Encoder feignFormEncoder() {
        return new FormEncoder(new SpringEncoder(httpMessageConverters));
    }
}
