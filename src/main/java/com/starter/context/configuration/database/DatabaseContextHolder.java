package com.starter.context.configuration.database;

/**
 * <pre>
 * 1. 将该类设置为protected类型,为了防止其他包调用此方法来切换数据源,导致数据源错乱的情况发生
 * </pre>
 *
 * <AUTHOR>
 */
public class DatabaseContextHolder {
    private static final ThreadLocal<DatabaseType> contextHolder = new ThreadLocal<>();
    private static final DatabaseType defaultDatabaseType = DatabaseType.SCP02;

    public static void setDatabaseType(DatabaseType type) {
        contextHolder.set(type);
    }

    public static void setDefalutDatabaseType() {
        contextHolder.set(defaultDatabaseType);
    }

    public static DatabaseType getDatabaseType() {
        DatabaseType type = contextHolder.get();
        if (type == null) {
            return defaultDatabaseType;
        }
        return type;
    }
}
