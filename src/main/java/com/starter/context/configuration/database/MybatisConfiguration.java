package com.starter.context.configuration.database;

import com.alibaba.druid.pool.DruidDataSource;
import com.starter.context.configuration.CustomClobTypeHandler;
import com.starter.context.configuration.database.properties.BaseDruidProperties;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.util.Assert;

import jakarta.annotation.Resource;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

@Configuration
public class MybatisConfiguration implements EnvironmentAware {

    private Environment env;

    /**
     * monitor本意是开启本机负载监控, 但是monitor一般只在PROD环境上启用, 所以monitor也被用来识别当前环境是否为PROD
     * 如果以后需要将两者区分开, 可以将这个配置分离出去
     * <p>
     * monitor = true, 代表着正式环境, 正式环境需要启动XSS等一系列安全验证
     */
    @Value("${spring.monitor}")
    private boolean monitor;

    @Bean
    @Primary
    public DynamicDataSource dynamicDataSource(BaseDruidProperties baseDruidProperties, Map<String, BaseDruidProperties> druidPropertiesMap) {

        DruidDataSource defaultDataSource = null;
        Map<Object, Object> targetDataSources = new HashMap<>();

        for (BaseDruidProperties druidProperties : druidPropertiesMap.values()) {

            if (druidProperties.getClass().getName().equals(BaseDruidProperties.class.getName())) {
                continue;
            }

            druidProperties.init(baseDruidProperties);

            DruidDataSource druidDataSource = new DruidDataSource();
            druidDataSource.setUrl(druidProperties.getUrl());
            druidDataSource.setUsername(druidProperties.getUsername());
            druidDataSource.setPassword(druidProperties.getPassword());
            druidDataSource.setDriverClassName(druidProperties.getDriverClassName());
            druidDataSource.setInitialSize(druidProperties.getInitialSize());
            druidDataSource.setMinIdle(druidProperties.getMinIdle());
            druidDataSource.setMaxActive(druidProperties.getMaxActive());
            druidDataSource.setMaxWait(druidProperties.getMaxWait());
            druidDataSource.setTimeBetweenEvictionRunsMillis(druidProperties.getTimeBetweenEvictionRunsMillis());
            druidDataSource.setMinEvictableIdleTimeMillis(druidProperties.getMinEvictableIdleTimeMillis());
            druidDataSource.setValidationQuery(druidProperties.getValidationQuery());
            druidDataSource.setTestWhileIdle(druidProperties.isTestWhileIdle());
            druidDataSource.setTestOnBorrow(druidProperties.isTestOnBorrow());
            druidDataSource.setTestOnReturn(druidProperties.isTestOnReturn());
            druidDataSource.setRemoveAbandoned(druidProperties.isRemoveAbandoned());
            druidDataSource.setRemoveAbandonedTimeout(druidProperties.getRemoveAbandonedTimeout());
            druidDataSource.setLogAbandoned(druidProperties.isLogAbandoned());

            Properties properties = new Properties();
            properties.setProperty("defaultRowPrefetch", String.valueOf(druidProperties.getDefaultRowPrefetch()));
            // 只有测试系统开启压缩, 因为正式系统没有网络瓶颈
            if (monitor == false) {
                properties.setProperty("oracle.net.networkCompression", "on");
                properties.setProperty("oracle.net.networkCompressionThreshold", "2048");
            }
            druidDataSource.setConnectProperties(properties);

            if (StringUtils.isNotBlank(druidProperties.getFilters())) {
                try {
                    druidDataSource.setFilters(druidProperties.getFilters());
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }

            targetDataSources.put(druidProperties.getDataBaseType(), druidDataSource);

            if (defaultDataSource == null && druidProperties.isDefaultTargetDataSource() == true) {
                defaultDataSource = druidDataSource;
            }
        }

        Assert.notNull(defaultDataSource, "DefaultDataSource must not be null");
        Assert.notEmpty(targetDataSources, "TargetDataSources must not be empty");

        DynamicDataSource dynamicDataSource = new DynamicDataSource();
        dynamicDataSource.setTargetDataSources(targetDataSources);// 该方法是AbstractRoutingDataSource的方法
        dynamicDataSource.setDefaultTargetDataSource(defaultDataSource);// 默认的datasource设置为druidDataSource

        return dynamicDataSource;
    }

    @Bean
    public SqlSessionFactory sqlSessionFactory(DynamicDataSource dynamicDataSource) throws Exception {
        SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        configuration.setCallSettersOnNulls(true);
        configuration.setMapUnderscoreToCamelCase(true);
        sqlSessionFactoryBean.setConfiguration(configuration);
        sqlSessionFactoryBean.setDataSource(dynamicDataSource);// 指定数据源(这个必须有，否则报错)
        sqlSessionFactoryBean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources(env.getProperty("mybatis.mapperLocations")));
        sqlSessionFactoryBean.setTypeAliasesSuperType(Mapper.class);
        sqlSessionFactoryBean.setTypeAliasesPackage(env.getProperty("mybatis.typeAliasesPackage"));
        sqlSessionFactoryBean.setTypeHandlers(new CustomClobTypeHandler());
        sqlSessionFactoryBean.setPlugins(new MyBatisSQLInterceptor());
        return sqlSessionFactoryBean.getObject();
    }

    @Override
    public void setEnvironment(Environment environment) {
        this.env = environment;
    }
}
