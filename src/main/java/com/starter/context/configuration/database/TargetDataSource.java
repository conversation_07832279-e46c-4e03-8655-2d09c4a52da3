package com.starter.context.configuration.database;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <pre>
 * 1. 不支持将此注解加在类上,因为同一个类中可能会用到其他数据源的方法,直接注解在类上,会导致意料之外的bug
 * </pre>
 *
 * <AUTHOR>
 *
 */
@Target({ ElementType.METHOD, ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface TargetDataSource {
	DatabaseType value();
}
