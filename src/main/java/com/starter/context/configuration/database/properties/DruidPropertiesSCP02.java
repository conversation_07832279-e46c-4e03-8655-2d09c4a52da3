package com.starter.context.configuration.database.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import com.starter.context.configuration.database.DatabaseType;

/**
 * 第二数据源, SCP项目主数据源
 *
 * <AUTHOR>
 * <p>
 *
 */
@Component
@ConfigurationProperties(prefix = "spring.datasource.scp02")
public class DruidPropertiesSCP02 extends BaseDruidProperties {
    private String url;
    private String username;
    private String password;

    @Override
    public boolean isDefaultTargetDataSource() {
        return true;
    }

    @Override
    public DatabaseType getDataBaseType() {
        return DatabaseType.SCP02;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
}
