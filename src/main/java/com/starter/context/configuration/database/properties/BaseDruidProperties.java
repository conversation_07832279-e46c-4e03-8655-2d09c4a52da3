package com.starter.context.configuration.database.properties;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import com.starter.context.configuration.database.DatabaseType;

@Component
@ConfigurationProperties(prefix = "spring.datasource.base")
public class BaseDruidProperties {
    private Integer initialSize = null;
    private Integer maxActive = null;
    private Integer minIdle = null;
    private Integer maxWait = null;
    private Boolean testOnBorrow = null;
    private Boolean testOnReturn = null;
    private Boolean testWhileIdle = null;
    private Long timeBetweenEvictionRunsMillis = null;
    private Long minEvictableIdleTimeMillis = null;
    private Boolean removeAbandoned = null;
    private Integer removeAbandonedTimeout = null;
    private Boolean logAbandoned = null;
    private Boolean remarksReporting = null;
    private String filters = null;
    private Integer defaultRowPrefetch = null;

    private String driverClassName = null;
    private String validationQuery = null;

    /**
     * Need override
     *
     * @return
     */
    public DatabaseType getDataBaseType() {
        return null;
    }

    /**
     * Need override
     *
     * @return
     */
    public String getUrl() {
        return null;
    }

    /**
     * Need override
     *
     * @return
     */
    public String getUsername() {
        return null;
    }

    /**
     * Need override
     *
     * @return
     */
    public String getPassword() {
        return null;
    }

    public void init(BaseDruidProperties baseDruidProperties) {
        this.initialSize = (this.initialSize == null) ? baseDruidProperties.getInitialSize() : this.initialSize;
        this.maxActive = (this.maxActive == null) ? baseDruidProperties.getMaxActive() : this.maxActive;
        this.minIdle = (this.minIdle == null) ? baseDruidProperties.getMinIdle() : this.minIdle;
        this.maxWait = (this.maxWait == null) ? baseDruidProperties.getMaxWait() : this.maxWait;
        this.testOnBorrow = (this.testOnBorrow == null) ? baseDruidProperties.isTestOnBorrow() : this.testOnBorrow;
        this.testOnReturn = (this.testOnReturn == null) ? baseDruidProperties.isTestOnReturn() : this.testOnReturn;
        this.testWhileIdle = (this.testWhileIdle == null) ? baseDruidProperties.isTestWhileIdle() : this.testWhileIdle;
        this.timeBetweenEvictionRunsMillis = (this.timeBetweenEvictionRunsMillis == null) ? baseDruidProperties.getTimeBetweenEvictionRunsMillis() : this.timeBetweenEvictionRunsMillis;
        this.minEvictableIdleTimeMillis = (this.minEvictableIdleTimeMillis == null) ? baseDruidProperties.getMinEvictableIdleTimeMillis() : this.minEvictableIdleTimeMillis;
        this.removeAbandoned = (this.removeAbandoned == null) ? baseDruidProperties.isRemoveAbandoned() : this.removeAbandoned;
        this.removeAbandonedTimeout = (this.removeAbandonedTimeout == null) ? baseDruidProperties.getRemoveAbandonedTimeout() : this.removeAbandonedTimeout;
        this.logAbandoned = (this.logAbandoned == null) ? baseDruidProperties.isLogAbandoned() : this.logAbandoned;
        this.remarksReporting = (this.remarksReporting == null) ? baseDruidProperties.isRemarksReporting() : this.remarksReporting;
        this.filters = (this.filters == null) ? baseDruidProperties.getFilters() : this.filters;
        this.driverClassName = (this.driverClassName == null) ? baseDruidProperties.getDriverClassName() : this.driverClassName;
        this.validationQuery = (this.validationQuery == null) ? baseDruidProperties.getValidationQuery() : this.validationQuery;
        this.defaultRowPrefetch = (this.defaultRowPrefetch == null) ? baseDruidProperties.getDefaultRowPrefetch() : this.defaultRowPrefetch;
    }

    public boolean isDefaultTargetDataSource() {
        return false;
    }

    public int getInitialSize() {
        return initialSize;
    }

    public void setInitialSize(int initialSize) {
        this.initialSize = initialSize;
    }

    public int getMaxActive() {
        return maxActive;
    }

    public void setMaxActive(int maxActive) {
        this.maxActive = maxActive;
    }

    public int getMinIdle() {
        return minIdle;
    }

    public void setMinIdle(int minIdle) {
        this.minIdle = minIdle;
    }

    public int getMaxWait() {
        return maxWait;
    }

    public void setMaxWait(int maxWait) {
        this.maxWait = maxWait;
    }

    public boolean isTestOnBorrow() {
        return testOnBorrow;
    }

    public void setTestOnBorrow(boolean testOnBorrow) {
        this.testOnBorrow = testOnBorrow;
    }

    public boolean isTestOnReturn() {
        return testOnReturn;
    }

    public void setTestOnReturn(boolean testOnReturn) {
        this.testOnReturn = testOnReturn;
    }

    public boolean isTestWhileIdle() {
        return testWhileIdle;
    }

    public void setTestWhileIdle(boolean testWhileIdle) {
        this.testWhileIdle = testWhileIdle;
    }

    public long getTimeBetweenEvictionRunsMillis() {
        return timeBetweenEvictionRunsMillis;
    }

    public void setTimeBetweenEvictionRunsMillis(long timeBetweenEvictionRunsMillis) {
        this.timeBetweenEvictionRunsMillis = timeBetweenEvictionRunsMillis;
    }

    public long getMinEvictableIdleTimeMillis() {
        return minEvictableIdleTimeMillis;
    }

    public void setMinEvictableIdleTimeMillis(long minEvictableIdleTimeMillis) {
        this.minEvictableIdleTimeMillis = minEvictableIdleTimeMillis;
    }

    public boolean isRemoveAbandoned() {
        return removeAbandoned;
    }

    public void setRemoveAbandoned(boolean removeAbandoned) {
        this.removeAbandoned = removeAbandoned;
    }

    public int getRemoveAbandonedTimeout() {
        return removeAbandonedTimeout;
    }

    public void setRemoveAbandonedTimeout(int removeAbandonedTimeout) {
        this.removeAbandonedTimeout = removeAbandonedTimeout;
    }

    public boolean isLogAbandoned() {
        return logAbandoned;
    }

    public void setLogAbandoned(boolean logAbandoned) {
        this.logAbandoned = logAbandoned;
    }

    public boolean isRemarksReporting() {
        return remarksReporting;
    }

    public void setRemarksReporting(boolean remarksReporting) {
        this.remarksReporting = remarksReporting;
    }

    public String getFilters() {
        return filters;
    }

    public void setFilters(String filters) {
        this.filters = filters;
    }

    public void setDriverClassName(String driverClassName) {
        this.driverClassName = driverClassName;
    }

    public void setValidationQuery(String validationQuery) {
        this.validationQuery = validationQuery;
    }

    public String getDriverClassName() {
        return driverClassName;
    }

    public String getValidationQuery() {
        return validationQuery;
    }

    public Integer getDefaultRowPrefetch() {
        return defaultRowPrefetch;
    }

    public void setDefaultRowPrefetch(Integer defaultRowPrefetch) {
        this.defaultRowPrefetch = defaultRowPrefetch;
    }
}
