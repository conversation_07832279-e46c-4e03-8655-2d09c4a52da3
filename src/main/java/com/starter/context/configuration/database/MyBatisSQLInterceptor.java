package com.starter.context.configuration.database;

import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.sql.parser.SQLParserFeature;
import com.alibaba.druid.util.JdbcConstants;
import com.starter.context.configuration.MqttConfiguration;
import com.starter.context.servlet.UserContextHolder;
import com.starter.utils.Utils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.type.JdbcType;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.sql.Statement;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Intercepts({@Signature(type = StatementHandler.class, method = "query", args = {Statement.class, ResultHandler.class})})
public class MyBatisSQLInterceptor implements Interceptor {
    public static Map<String, Map<String, Long>> URL_TABLE_MAPPING1 = new HashMap<>(); // 指针1
    public static Map<String, Map<String, Long>> URL_TABLE_MAPPING2 = new HashMap<>(); // 指针2

    public static Map<String, Map<String, Long>> URL_TABLE_MAPPING = URL_TABLE_MAPPING1; // 当前激活的指针

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
        String sql = statementHandler.getBoundSql().getSql();

        // 拿到URL, 然后清空线程变量, 因为线程会重复使用, 不清空会影响下次使用
        String key = UserContextHolder.getMethodUrlHolder();
        if (key != null) {
            // 如果debug, 则发送debug消息
            if ("Y".equalsIgnoreCase(UserContextHolder.getDebugHolder())) {
                String token = StringUtils.lowerCase(UserContextHolder.getUserID());

                List<ParameterMapping> parameterMappings = statementHandler.getBoundSql().getParameterMappings();
                Object parameterObject = statementHandler.getBoundSql().getParameterObject();
                Map<String, Object> additionalParameters = statementHandler.getBoundSql().getAdditionalParameters();

                for (ParameterMapping p : parameterMappings) {
                    if (p.getJdbcType() == JdbcType.NUMERIC || p.getJdbcType() == JdbcType.INTEGER || p.getJdbcType() == JdbcType.DOUBLE || p.getJdbcType() == JdbcType.DECIMAL) {
                        sql = StringUtils.replaceOnce(sql, "?", this.removeQuota(this.getParameter(parameterObject, additionalParameters, p.getProperty())));
                    } else if (p.getJdbcType() == JdbcType.CLOB) {
                        sql = StringUtils.replaceOnce(sql, "?", "'<CLOB>'");
                    } else {
                        sql = StringUtils.replaceOnce(sql, "?", "'" + this.removeQuota(this.getParameter(parameterObject, additionalParameters, p.getProperty())) + "'");
                    }
                }

                String message = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date()) + " - " + key + "\n===============>\n";
                message += SQLUtils.format(sql, JdbcConstants.ORACLE) + "\r\n<===============\n\n";

                if (message.length() > 65535) {
                    message = StringUtils.substring(message, 0, 65535) + " ...";
                }

                MqttConfiguration.publishMessage("scp/dss/ui/debug/" + token, message);
            }

            Map<String, Long> tableMap;
            synchronized (this) {
                tableMap = URL_TABLE_MAPPING.computeIfAbsent(key, k -> new HashMap<>());
            }
            for (String table : Utils.getTableNamesBySQL(sql)) {
                // 数据访问会触发VISIT_LOGS保存机制, 所以应该将SY_VISIT_LOGS从结果集中去掉
                if (table.equalsIgnoreCase("SY_VISIT_LOGS") == false) {
                    tableMap.put(table, System.currentTimeMillis() / 1000);
                }
            }
        }

        return invocation.proceed();
    }

    private String getParameter(Object parameterObject, Map<String, Object> additionalParameters, String property) {
        try {
            if (additionalParameters != null && additionalParameters.isEmpty() == false) {
                if (property.contains(".")) {
                    String[] ps = property.split("\\.");
                    if (additionalParameters.containsKey(ps[0])) {
                        return String.valueOf(this.getParameter(additionalParameters.get(ps[0]), ps[1]));
                    }
                } else if (property.contains("[")) {
                    String[] ps = property.split("\\[");
                    if (additionalParameters.containsKey(ps[0])) {
                        return String.valueOf(this.getParameter(additionalParameters.get(ps[0]), ps[1].replace("]", "")));
                    }
                } else {
                    if (additionalParameters.containsKey(property)) {
                        return String.valueOf(additionalParameters.get(property));
                    }
                }
            }

            return this.getParameter(parameterObject, property);
        } catch (Exception ignore) {
            return "<UNSUPPORTED PARAMS>";
        }
    }

    private String getParameter(Object parameterObject, String property) {
        try {
            if (parameterObject instanceof Map<?, ?> parameter) {
                if (property.contains(".")) {
                    String[] ps = property.split("\\.");
                    return String.valueOf(this.getParameter(parameter.get(ps[0]), ps[1]));
                } else if (property.contains("[")) {
                    String[] ps = property.split("\\[");
                    return String.valueOf(this.getParameter(parameter.get(ps[0]), ps[1].replace("]", "")));
                } else {
                    return String.valueOf(parameter.get(property));
                }
            } else if (parameterObject instanceof List<?> list) {
                return String.valueOf(list.get(Integer.parseInt(property)));
            } else if (parameterObject instanceof String || parameterObject instanceof Number) {
                return String.valueOf(parameterObject);
            } else if (parameterObject != null) {
                Field[] fields = parameterObject.getClass().getDeclaredFields();

                for (Field field : fields) {
                    if (field.getName().equals(property)) {
                        PropertyDescriptor pd = new PropertyDescriptor(field.getName(), parameterObject.getClass());
                        Method getMethod = pd.getReadMethod();
                        return String.valueOf(getMethod.invoke(parameterObject));
                    }
                }
                return "<UNSUPPORTED PARAMS>";
            } else {
                return "NULL";
            }
        } catch (Exception ignore) {
            return "<UNSUPPORTED PARAMS>";
        }
    }

    private String removeQuota(Object obj) {
        if (obj == null) {
            return "";
        } else {
            return String.valueOf(obj).replace("'", "");
        }
    }

    @Override
    public Object plugin(Object target) {
        if (target instanceof StatementHandler) {
            return Plugin.wrap(target, this);
        } else {
            return target;
        }
    }
}
