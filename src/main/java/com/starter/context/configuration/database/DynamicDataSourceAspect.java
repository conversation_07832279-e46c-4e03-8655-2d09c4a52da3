package com.starter.context.configuration.database;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 *
 * <pre>
 * 1. 保证该AOP在@Transactional之前执行
 * 2. 标签@Transactional和@Cacheable的Order都是Integer.MAX_VALUE,所以随意设置一个值,只要比Integer.MAX_VALUE小,就可以先执行
 * 3. 此拦截器是否在@Cacheable之后执行其实无所谓
 * 3.1 如果不走缓存,此拦截器可以在@Transactional之前执行,可以正常使用
 * 3.2 如果走缓存,反正不会进行数据库查询,同时也不会影响其他线程的数据库查询,所以可以正常使用
 * 3.3 如果将Order也设置为Integer.MAX_VALUE,那么Spring会按照@Cacheable -> @Transactional -> @TargetDataSource顺序执行,导致报错
 * </pre>
 *
 * <AUTHOR>
 *
 */
@Aspect
@Order(0)
@Component
public class DynamicDataSourceAspect {
	private static final Logger logger = LoggerFactory.getLogger(DynamicDataSourceAspect.class);

	@Before("@annotation(targetDataSource)")
	public void switchDataSource(JoinPoint point, TargetDataSource targetDataSource) throws Throwable {
		logger.debug("Switch DataSource : {} > {}", targetDataSource.value(), point.getSignature());
		DatabaseContextHolder.setDatabaseType(targetDataSource.value());
	}

	@After("@annotation(targetDataSource)")
	public void restoreDataSource(JoinPoint point, TargetDataSource targetDataSource) {
		logger.debug("Revert DataSource : {} > {}", targetDataSource.value(), point.getSignature());
		DatabaseContextHolder.setDefalutDatabaseType();
	}
}
