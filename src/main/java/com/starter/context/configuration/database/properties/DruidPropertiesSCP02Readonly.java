package com.starter.context.configuration.database.properties;

import com.starter.context.configuration.database.DatabaseType;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 第二数据源, SCP项目主数据源
 *
 * <AUTHOR>
 * <p>
 *
 */
@Component
@ConfigurationProperties(prefix = "spring.datasource.scp02-readonly")
public class DruidPropertiesSCP02Readonly extends BaseDruidProperties {
    private String url;
    private String username;
    private String password;

    @Override
    public boolean isDefaultTargetDataSource() {
        return true;
    }

    @Override
    public DatabaseType getDataBaseType() {
        return DatabaseType.SCP02_READONLY;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
}
