package com.starter.context.configuration;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.quartz.CronTriggerFactoryBean;
import org.springframework.scheduling.quartz.JobDetailFactoryBean;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;

@Configuration
public class QuartzConfiguration {

	@Bean
	public JobDetailFactoryBean jobDetailFactoryBean(JobDispatcher jobDispatcher) {
		JobDetailFactoryBean jobDetail = new JobDetailFactoryBean();

		jobDetail.setName("job_dispatcher_scheduler");
		jobDetail.setGroup("job_dispatcher_scheduler_group");
		jobDetail.setJobClass(jobDispatcher.getClass());
		return jobDetail;
	}

	@Bean
	public CronTriggerFactoryBean cronTriggerFactoryBean(@Qualifier(value = "jobDetailFactoryBean") JobDetailFactoryBean jobDetailFactoryBean) {
		CronTriggerFactoryBean cronTriggerFactoryBean = new CronTriggerFactoryBean();

		cronTriggerFactoryBean.setName("job_dispatcher_trigger");
		cronTriggerFactoryBean.setJobDetail(jobDetailFactoryBean.getObject());
		cronTriggerFactoryBean.setCronExpression("0 * * * * ?");
		return cronTriggerFactoryBean;
	}

	@Bean
	public SchedulerFactoryBean scheduler(@Qualifier(value = "cronTriggerFactoryBean") CronTriggerFactoryBean cronTriggerFactoryBean) {
		SchedulerFactoryBean schedulerFactoryBean = new SchedulerFactoryBean();

		schedulerFactoryBean.setTriggers(cronTriggerFactoryBean.getObject());
		return schedulerFactoryBean;
	}
}
