package com.starter.context.configuration;

import jakarta.annotation.Resource;

import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.starter.context.servlet.AuthInterceptor;

@Configuration
public class AuthConfiguration implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(authInterceptor()).addPathPatterns("/**")
                .excludePathPatterns("/login")
                .excludePathPatterns("/remote_login")
                .excludePathPatterns("/get_access_token")
                .excludePathPatterns("/logout")
                .excludePathPatterns("/check_route_auth")
                .excludePathPatterns("/create_new_account")
                .excludePathPatterns("/query_available_entity")
                .excludePathPatterns("/query_user_info")
                .excludePathPatterns("/popup/**");
    }

    @Bean
    public AuthInterceptor authInterceptor() {
        return new AuthInterceptor();
    }
}
