package com.starter.context.bean;

public class AuthAnnotation {
    private String url;
    private String classification;
    private String menuCode;
    private String parent;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public void setUrl(String prefix, String url) {
        if (prefix.startsWith("/") == false) {
            prefix = "/" + prefix;
        }
        this.url = (prefix + url).replace("//", "/");
    }

    public String getClassification() {
        return classification;
    }

    public void setClassification(String classification) {
        this.classification = classification;
    }

    public String getMenuCode() {
        return menuCode;
    }

    public void setMenuCode(String menuCode) {
        this.menuCode = menuCode;
    }

    public String getParent() {
        return parent;
    }

    public void setParent(String parent) {
        this.parent = parent;
    }
}
