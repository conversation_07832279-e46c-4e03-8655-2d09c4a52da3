package com.starter.context.bean;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface CacheRemove {

    /**
     * 使用USER缓存的数据, 使用USER或ALL_USER来清除
     * 使用GLOBAL缓存的数据, 使用GLOBAL来清除
     *
     * 1. @CacheRemove(key = "this")
     * (1) scope = CacheRemove.SCOPE_USER
     * 将会清除 this.{SCOPE_USER} 下所有的数据
     *
     * (2) scope = CacheRemove.SCOPE_ALL_USER
     * 将会清除 this.* 下所有的数据, 与GLOBAL相同
     *
     * (3) scope = CacheRemove.GLOBAL
     * 将会清除 this.* 下所有的数据
     *
     *
     * 2. @CacheRemove(key = "this.queryWorkstationsLayout")
     * (1) scope = CacheRemove.SCOPE_USER
     * 将会清除 this.{SCOPE_USER}.queryWorkstationsLayout 下所有的数据
     *
     * (2) scope = CacheRemove.SCOPE_ALL_USER
     * 将会清除 this.*.queryWorkstationsLayout 下所有的数据
     *
     * (3) scope = CacheRemove.GLOBAL
     * 将会清除 this.queryWorkstationsLayout 下所有的数据
     *
     */
    String SCOPE_USER = "USER"; // 当前用户
    String SCOPE_ALL_USER = "ALL_USER"; // 所有用户
    String SCOPE_GLOBAL = "GLOBAL"; // Global

    // 缓存簇
    String value() default "";

    // 簇下具体的key
    // 如果key是this,则清除当前类下所有key
    String[] key() default {};

    // 如果scope是user, 那么仅清除当前用户的缓存, 否则清除所有缓存
    // 此选项仅在globalCache = false时生效, 当globalCache = true时, 无论scope是何值, 都会清除所有
    // 需要配合this使用, 如果未指定this, 此项失效
    String scope() default SCOPE_GLOBAL;
}
