package com.starter.context.bean;

import org.apache.commons.lang3.StringUtils;

public class Message {

    public static final String TYPE_ERROR = "error";
    public static final String TYPE_SUCCESS = "success";
    public static final String TYPE_WARNING = "warning";

    private String message;
    private boolean showClose = true;
    private String type;
    private int duration = 5000;
    private boolean dangerouslyUseHTMLString = false;

    public boolean isDangerouslyUseHTMLString() {
        return dangerouslyUseHTMLString;
    }

    public void setDangerouslyUseHTMLString(boolean dangerouslyUseHTMLString) {
        this.dangerouslyUseHTMLString = dangerouslyUseHTMLString;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public boolean isShowClose() {
        return showClose;
    }

    public void setShowClose(boolean showClose) {
        this.showClose = showClose;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public void set(int change, int unchange, String warningMsg) {
        // 生成提示信息
        StringBuilder msg = new StringBuilder();
        msg.append(change);
        if (change > 1) {
            msg.append(" rows");
        } else {
            msg.append(" row");
        }
        msg.append(" updated");
        if (unchange > 0) {
            this.setType(Message.TYPE_WARNING);
            this.setDangerouslyUseHTMLString(true);
            msg.append("<br>");
            msg.append(unchange);
            if (unchange > 1) {
                msg.append(" rows");
            } else {
                msg.append(" row");
            }
            msg.append(" ignored");

            if (StringUtils.isNotBlank(warningMsg)) {
                msg.append("<br>");
                msg.append(warningMsg);
            }
        } else {
            this.setType(Message.TYPE_SUCCESS);
        }
        this.setMessage(msg.toString());
    }
}
