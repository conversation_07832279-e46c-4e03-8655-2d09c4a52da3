package com.starter.context.bean;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;

public class EditableParameter {
	private boolean edit = false;
	private String id;
	private String key;
	private String value;
	private Map<String, Object> editValues = new HashMap<>();

	public EditableParameter(Map<String, Object> parameterMap) {

		id = (String) parameterMap.remove("editor[id]");
		key = (String) parameterMap.remove("editor[key]");
		value = (String) parameterMap.remove("editor[value]");

		if (key != null) {
			edit = true;

			// 防止SQL侵入
			if (key.contains(" ") || key.contains(";") || key.contains("(")) {
				key = null;
			}
		}

		if (edit == true) {
			Iterator<Entry<String, Object>> iterator = parameterMap.entrySet().iterator();

			Entry<String, Object> entry;
			while (iterator.hasNext()) {
				entry = iterator.next();
				if (entry.getKey().startsWith("editor[values]")) {
					String key = entry.getKey();
					key = key.replace("editor[values][", "");
					key = key.replace("]", "");
					editValues.put(key, entry.getValue());
					iterator.remove();
				}
			}
		}
	}

	public boolean editing() {
		return edit;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getKey() {
		return key;
	}

	public void setKey(String key) {
		this.key = key;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public Map<String, Object> getEditValues() {
		return editValues;
	}

	public void setEditValues(Map<String, Object> editValues) {
		this.editValues = editValues;
	}
}
