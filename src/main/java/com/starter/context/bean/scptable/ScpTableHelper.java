package com.starter.context.bean.scptable;

import com.starter.utils.Utils;
import com.starter.context.bean.Message;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
@SuppressWarnings("unchecked")
public class ScpTableHelper {
    private String warningMessage = null;
    private List<String> excludeColumn = new ArrayList<>(); // 从前台传过来的字段中排除掉某些列
    private List<String> includeColumn = new ArrayList<>(); // 从前台传过来的列, 只有在include里面才能被识别, 否则不被识别, 为空则不生效
    private ScpTableUpdateHandler scpTableUpdateHandler = null;
    private ScpTableDeleteHandler scpTableDeleteHandler = null;
    private ScpTableInsertHandler scpTableInsertHandler = null;


    public Message execCRUD(Map<String, Object> parameterMap) {
        Message message = new Message();
        message.setShowClose(true);

        try {
            int changeCnt = 0;
            int unChangeCnt = 0;

            // update
            if (scpTableUpdateHandler != null) {
                Map<String, Object> updateMap = (Map<String, Object>) parameterMap.get("updatedRows");
                for (String key : updateMap.keySet()) {
                    List<ScpTableCell> cols = new ArrayList<>();
                    Map<String, Object> colMap = (Map<String, Object>) updateMap.get(key);

                    for (String k : colMap.keySet()) {
                        if (Utils.hasInjectionAttack(k) == true) {
                            message.setMessage("Invalid update parameters!");
                            message.setType(Message.TYPE_ERROR);
                            return message;
                        }

                        if (includeColumn.isEmpty() == false) {
                            if (includeColumn.contains(k) == false) {
                                continue;
                            }
                        }

                        if (excludeColumn.contains(k) == false) {
                            ScpTableCell col = new ScpTableCell();
                            col.setKey(k);
                            col.setValue(colMap.get(k));
                            cols.add(col);
                        }
                    }

                    if (cols.isEmpty() == false) {
                        int result = scpTableUpdateHandler.update(key, cols);
                        if (result == 0) {
                            unChangeCnt++;
                        } else {
                            changeCnt++;
                        }
                    }
                }
            }

            // delete
            if (scpTableDeleteHandler != null) {
                List<String> removeList = (List<String>) parameterMap.get("removedRows");
                if (removeList.isEmpty() == false) {
                    int result = scpTableDeleteHandler.delete(removeList);

                    unChangeCnt += removeList.size() - result;
                    changeCnt += result;
                }
            }

            // create
            if (scpTableInsertHandler != null) {
                List<Map<String, Object>> creates = Utils.clearList(parameterMap.get("createdRows"));

                List<String> headersForInsert = new ArrayList<>();

                if (creates.isEmpty() == false) {
                    for (Map<String, Object> map : creates) {
                        for (String exclude : excludeColumn) {
                            map.remove(exclude);
                        }

                        for (String key : map.keySet()) {
                            if (map.get(key) != null) {
                                if (headersForInsert.contains(key) == false) {
                                    headersForInsert.add(key);
                                }
                            }
                        }
                    }

                    int result = scpTableInsertHandler.create(headersForInsert, creates);

                    changeCnt += result;
                }
            }

            message.set(changeCnt, unChangeCnt, warningMessage);
        } catch (Exception e) {
            e.printStackTrace();
            message.setType(Message.TYPE_ERROR);
            message.setMessage(Utils.getExceptionMessage(e));
        }
        return message;
    }


    public ScpTableUpdateHandler getScpTableUpdateHandler() {
        return scpTableUpdateHandler;
    }

    public void setScpTableUpdateHandler(ScpTableUpdateHandler scpTableUpdateHandler) {
        this.scpTableUpdateHandler = scpTableUpdateHandler;
    }

    public List<String> getExcludeColumn() {
        return excludeColumn;
    }

    public void setExcludeColumn(List<String> excludeColumn) {
        this.excludeColumn = excludeColumn;
    }

    public List<String> getIncludeColumn() {
        return includeColumn;
    }

    public void setIncludeColumn(List<String> includeColumn) {
        this.includeColumn = includeColumn;
    }

    public ScpTableDeleteHandler getScpTableDeleteHandler() {
        return scpTableDeleteHandler;
    }

    public void setScpTableDeleteHandler(ScpTableDeleteHandler scpTableDeleteHandler) {
        this.scpTableDeleteHandler = scpTableDeleteHandler;
    }

    public ScpTableInsertHandler getScpTableInsertHandler() {
        return scpTableInsertHandler;
    }

    public void setScpTableInsertHandler(ScpTableInsertHandler scpTableInsertHandler) {
        this.scpTableInsertHandler = scpTableInsertHandler;
    }

    public String getWarningMessage() {
        return warningMessage;
    }

    public void setWarningMessage(String warningMessage) {
        this.warningMessage = warningMessage;
    }
}
