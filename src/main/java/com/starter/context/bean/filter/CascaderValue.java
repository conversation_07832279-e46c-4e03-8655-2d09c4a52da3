package com.starter.context.bean.filter;

import com.alibaba.fastjson.JSONArray;

public class CascaderValue {

    public CascaderValue() {

    }

    public CascaderValue(Object jsonObject) {
        JSONArray list = (JSONArray) jsonObject;
        this.key = list.getString(0);
        this.value = list.getString(1);
    }

    private String key;

    private String value;


    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
