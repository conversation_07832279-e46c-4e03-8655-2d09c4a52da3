package com.starter.context.bean.filter;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.starter.context.configuration.SCPATableConfiguration;

import java.util.*;

public class FilterGenerator {
    public void generator(Map<String, Object> parameterMap, String key) {
        JSONObject scpFilter = (JSONObject) parameterMap.get(key);
        SCPATables scpaTables = new SCPATables();
        if (scpFilter != null && scpFilter.getJSONArray("filter") != null && scpFilter.getJSONArray("filter").isEmpty() == false) {
            List<FilterElement> filterElementList = new ArrayList<>();
            JSONArray array = scpFilter.getJSONArray("filter");
            FilterGenerator.rebuildFilterChain(array);
            for (Object obj : array) {
                filterElementList.add(new FilterElement((JSONObject) obj));
            }
            // 取出所有涉及到的Field
            List<String> fieldList = this.getAllFieldsFromElementList(filterElementList);
            // 取出所有符合条件的Table
            List<String> tables = SCPATableConfiguration.getMatchedTableByColumnName(fieldList);

            scpaTables.setParameterMap(parameterMap);
            scpaTables.setAllMatchedTables(tables);
            scpaTables.setFilterElementList(filterElementList);
        }
        parameterMap.put("SCPA", scpaTables);
    }

    public static void rebuildFilterChain(JSONArray array) {
        for (int i = 0; i < array.size(); i++) {
            JSONObject element = array.getJSONObject(i);
            element.put("currentOr", "OR".equalsIgnoreCase(element.getString("joiner")));
            element.put("nextOr", false);
            if (i < array.size() - 1) {
                JSONObject nextElemnt = array.getJSONObject(i + 1);
                element.put("nextOr", "OR".equalsIgnoreCase(nextElemnt.getString("joiner")));
            }
        }
    }

    private List<String> getAllFieldsFromElementList(List<FilterElement> elementList) {
        Set<String> set = new HashSet<>();
        for (FilterElement element : elementList) {
            set.addAll(element.getFields());
        }
        return new ArrayList<>(set);
    }
}
