package com.starter.context.bean.filter;

import com.adm.system.service.FilterService;
import com.alibaba.fastjson.JSONObject;
import com.starter.context.SpringContext;
import com.starter.context.configuration.SCPATableConfiguration;
import com.starter.utils.Utils;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;

public class FilterElement {
    private String joiner;
    private String operator;
    private String text;
    private boolean nextOr;
    private boolean currentOr;

    private List<String> value;
    private List<String> fields;
    private List<String> types;

    public FilterElement() {

    }

    public FilterElement(JSONObject data) {
        this.joiner = data.getString("joiner");
        this.operator = data.getString("operator");
        this.text = data.getString("text");
        this.nextOr = data.getBoolean("nextOr");
        this.currentOr = data.getBoolean("currentOr");

        this.value = data.getJSONArray("value").toJavaList(String.class);
        this.fields = data.getJSONArray("fields").toJavaList(String.class);
        this.types = data.getJSONArray("types").toJavaList(String.class);
    }

    public String getSQL(Map<String, Object> parameterMap, String table) {
        return this.getSQLByElement(parameterMap, this, table);
    }

    public String getSQLByElement(Map<String, Object> parameterMap, FilterElement element, String table) {
        StringBuilder sql = new StringBuilder();
        // 先判断是table是否包含Field, 将不包含的条件替换为0=1
        for (String field : element.getFields()) {
            if (SCPATableConfiguration.isColumnInTable(table, field) == false) {
                sql.append("0 = 1");
                return sql.toString();
            }
        }
        // 再判断是否为IN, 如果是IN或者NOT IN, 需要将过长的查询条件分割开来, 其他运算符不需要
        if ("IN".equals(element.getOperator()) || "NOT IN".equals(element.getOperator())) {
            // 再将Value拆分成List<List>格式
            List<String> lines = Arrays.stream(element.getText().split("\n")).filter(Objects::nonNull).toList();
            List<List<String>> queryList = new ArrayList<>();
            for (String line : lines) {
                List<String> values = new ArrayList<>(List.of(line.split("\t")));
                // 根据Field的长度进行增补, 保证SQL不报错
                while (values.size() < element.getFields().size()) {
                    values.add(null);
                }
                while (values.size() > element.getFields().size()) {
                    values.remove(values.size() - 1);
                }

                queryList.add(values);
            }
            if (queryList.isEmpty()) {
                return "0 = 1";
            }

            // 将List<List>拆成800一组, 防止IN过长报错
            List<List<List<String>>> splitedQueryList = Utils.subList(queryList, 800);
            String speedupID = SpringContext.getBean(FilterService.class).doSpeedup(element, splitedQueryList);
            // 这里分情况讨论, 有时候用户会上传几千个料号或者订单号, 用IN依旧会非常非常的慢
            // 这时候我们会倾向于将所有数据插入到Speedup表中, 然后使用JOIN来完成查询操作
            if (speedupID != null) {
                // 走临时表加速
                StringBuilder temp = new StringBuilder();
                for (String field : element.getFields()) {
                    temp.append(" AND T$.").append(field);
                    temp.append(" = ");
                    temp.append("TSFS$.").append(field);
                }
                if ("IN".equals(element.getOperator())) {
                    sql.append(" EXISTS (SELECT 1 FROM SCPA." + SCPATableConfiguration.FILTER_SPEEDUP_TABLE + " TSFS$ WHERE TSFS$.ID$ = '");
                } else {
                    sql.append(" NOT EXISTS (SELECT 1 FROM SCPA." + SCPATableConfiguration.FILTER_SPEEDUP_TABLE + " TSFS$ WHERE TSFS$.ID$ = '");
                }
                sql.append(speedupID).append("' ").append(temp).append(") ");
            } else {
                // 走正常的IN拼接
                // 再对每组生成条件
                List<String> subSql = new ArrayList<>();
                for (List<List<String>> slist : splitedQueryList) {
                    StringBuilder tempSql = new StringBuilder();
                    if (element.getFields().size() == 1) {
                        tempSql.append("\"").append(element.getFields().get(0)).append("\"");
                    } else {
                        tempSql.append("(\"").append(StringUtils.join(element.getFields(), "\",\"")).append("\")");
                    }
                    tempSql.append(" ");
                    tempSql.append(element.getOperator());
                    tempSql.append(" ");
                    tempSql.append("(");
                    for (List<String> sslist : slist) {
                        if (sslist.size() > 1) {
                            tempSql.append("(");
                            for (int i = 0; i < sslist.size(); i++) {
                                tempSql.append(this.getPlaceholder(parameterMap, sslist.get(i), element.getTypes().get(i)));
                                if (i != sslist.size() - 1) {
                                    tempSql.append(",");
                                }
                            }
                            tempSql.append(")");
                        } else {
                            tempSql.append(this.getPlaceholder(parameterMap, sslist.get(0), element.getTypes().get(0)));
                        }
                        tempSql.append(",");
                    }
                    if (tempSql.charAt(tempSql.length() - 1) == ',') {
                        tempSql.deleteCharAt(tempSql.length() - 1);
                    }
                    tempSql.append(")");
                    subSql.add(tempSql.toString());
                }

                // 多个IN使用OR连接
                sql.append("(");
                sql.append(StringUtils.join(subSql, " OR "));
                sql.append(")");
            }
        } else {
            // 如果是IS NULL或者IS NOT NULL, 则无需附加值, 直接返回结果
            if (element.getOperator().contains(" NULL")) {
                // 非IN预算符, 不可能出现两个Field, 所以直接使用第一个Field
                sql.append("\"").append(element.getFields().get(0)).append("\"");
                sql.append(" ");
                sql.append(element.getOperator());
                return sql.toString();
            } else {
                // 用户可能会选择多个值, 所以要进行符号转换
                String opr = element.getOperator();
                opr = switch (opr) {
                    case "=" -> "IN";
                    case "!=" -> "NOT IN";
                    default -> opr;
                };

                if ("IN".equals(opr) || "NOT IN".equals(opr)) {
                    sql.append("\"").append(element.getFields().get(0)).append("\"");
                    sql.append(" ");
                    sql.append(opr);
                    sql.append(" ");
                    List<String> tempList = new ArrayList<>();
                    for (String val : element.getValue()) {
                        tempList.add(this.getPlaceholder(parameterMap, val, element.getTypes().get(0)));
                    }
                    sql.append("(").append(StringUtils.join(tempList, ",")).append(")");
                } else {
                    sql.append("(");
                    List<String> tempList = new ArrayList<>();
                    for (String val : element.getValue()) {
                        StringBuilder temp = new StringBuilder();
                        temp.append("\"").append(element.getFields().get(0)).append("\"");
                        temp.append(" ");
                        if (StringUtils.equalsIgnoreCase(opr, "START WITH")) {
                            temp.append("LIKE");
                        } else {
                            temp.append(opr);
                        }
                        temp.append(" ");

                        if (StringUtils.containsIgnoreCase(opr, "LIKE")) {
                            temp.append(" '%' || ");
                        }
                        temp.append(this.getPlaceholder(parameterMap, val, element.getTypes().get(0)));
                        if (StringUtils.containsIgnoreCase(opr, "LIKE") || StringUtils.equalsIgnoreCase(opr, "START WITH")) {
                            temp.append(" || '%'");
                        }
                        tempList.add(temp.toString());
                    }
                    sql.append(StringUtils.join(tempList, " OR "));
                    sql.append(")");
                }
            }
        }
        return sql.toString();
    }


    public String getRawSQL() {
        return this.getRawSQLByElement(this);
    }

    public String getRawSQLByElement(FilterElement element) {
        StringBuilder sql = new StringBuilder();
        // 先判断是table是否包含Field, 将不包含的条件替换为0=1

        // 判断是否为IN, 如果是IN或者NOT IN, 需要将过长的查询条件分割开来, 其他运算符不需要
        if ("IN".equals(element.getOperator()) || "NOT IN".equals(element.getOperator())) {
            // 再将Value拆分成List<List>格式
            List<String> lines = Arrays.stream(element.getText().split("\n")).filter(Objects::nonNull).toList();
            List<List<String>> queryList = new ArrayList<>();
            for (String line : lines) {
                List<String> values = new ArrayList<>(List.of(line.split("\t")));
                // 根据Field的长度进行增补, 保证SQL不报错
                while (values.size() < element.getFields().size()) {
                    values.add(null);
                }
                while (values.size() > element.getFields().size()) {
                    values.remove(values.size() - 1);
                }

                queryList.add(values);
            }
            if (queryList.isEmpty()) {
                return "0 = 1";
            }

            // 将List<List>拆成800一组, 防止IN过长报错
            List<List<List<String>>> splitedQueryList = Utils.subList(queryList, 800);
            // 这里分情况讨论, 有时候用户会上传几千个料号或者订单号, 用IN依旧会非常非常的慢
            // 这时候我们会倾向于将所有数据插入到Speedup表中, 然后使用JOIN来完成查询操作

            // 走正常的IN拼接
            // 再对每组生成条件
            List<String> subSql = new ArrayList<>();
            for (List<List<String>> slist : splitedQueryList) {
                StringBuilder tempSql = new StringBuilder();
                if (element.getFields().size() == 1) {
                    tempSql.append("\"").append(element.getFields().get(0)).append("\"");
                } else {
                    tempSql.append("(\"").append(StringUtils.join(element.getFields(), "\",\"")).append("\")");
                }
                tempSql.append(" ");
                tempSql.append(element.getOperator());
                tempSql.append(" ");
                tempSql.append("(");
                for (List<String> sslist : slist) {
                    if (sslist.size() > 1) {
                        tempSql.append("(");
                        for (int i = 0; i < sslist.size(); i++) {
                            tempSql.append(this.getRawValue(sslist.get(i)));
                            if (i != sslist.size() - 1) {
                                tempSql.append(",");
                            }
                        }
                        tempSql.append(")");
                    } else {
                        tempSql.append(this.getRawValue(sslist.get(0)));
                    }
                    tempSql.append(",");
                }
                if (tempSql.charAt(tempSql.length() - 1) == ',') {
                    tempSql.deleteCharAt(tempSql.length() - 1);
                }
                tempSql.append(")");
                subSql.add(tempSql.toString());
            }

            // 多个IN使用OR连接
            sql.append("(");
            sql.append(StringUtils.join(subSql, " OR "));
            sql.append(")");
        } else {
            // 如果是IS NULL或者IS NOT NULL, 则无需附加值, 直接返回结果
            if (element.getOperator().contains(" NULL")) {
                // 非IN预算符, 不可能出现两个Field, 所以直接使用第一个Field
                sql.append("\"").append(element.getFields().get(0)).append("\"");
                sql.append(" ");
                sql.append(element.getOperator());
                return sql.toString();
            } else {
                // 用户可能会选择多个值, 所以要进行符号转换
                String opr = element.getOperator();
                opr = switch (opr) {
                    case "=" -> "IN";
                    case "!=" -> "NOT IN";
                    default -> opr;
                };

                if ("IN".equals(opr) || "NOT IN".equals(opr)) {
                    sql.append("\"").append(element.getFields().get(0)).append("\"");
                    sql.append(" ");
                    sql.append(opr);
                    sql.append(" ");
                    List<String> tempList = new ArrayList<>();
                    for (String val : element.getValue()) {
                        tempList.add(this.getRawValue(val));
                    }
                    sql.append("(").append(StringUtils.join(tempList, ",")).append(")");
                } else {
                    sql.append("(");
                    List<String> tempList = new ArrayList<>();
                    for (String val : element.getValue()) {
                        StringBuilder temp = new StringBuilder();
                        temp.append("\"").append(element.getFields().get(0)).append("\"");
                        temp.append(" ");
                        if (StringUtils.equalsIgnoreCase(opr, "START WITH")) {
                            temp.append("LIKE");
                        } else {
                            temp.append(opr);
                        }
                        temp.append(" ");

                        if (StringUtils.containsIgnoreCase(opr, "LIKE")) {
                            temp.append(" '%' || ");
                        }
                        temp.append(this.getRawValue(val));
                        if (StringUtils.containsIgnoreCase(opr, "LIKE") || StringUtils.equalsIgnoreCase(opr, "START WITH")) {
                            temp.append(" || '%'");
                        }
                        tempList.add(temp.toString());
                    }
                    sql.append(StringUtils.join(tempList, " OR "));
                    sql.append(")");
                }
            }
        }
        return sql.toString();
    }

    private String getRawValue(String val) {
        if (val == null) {
            return null;
        } else {
            return "'" + StringUtils.remove(val, "'") + "'";
        }
    }

    Map<String, String> placeHolderCache = new HashMap<>();

    private String getPlaceholder(Map<String, Object> parameterMap, String value, String type) {
        if (placeHolderCache.get(value) != null) {
            return placeHolderCache.get(value);
        } else {
            String result;
            String randomKey = "___scp_filter_" + Utils.randomStr(8);
            if ("VARCHAR2".equals(type)) {
                parameterMap.put(randomKey, value);
                result = "#{" + randomKey + ", jdbcType=VARCHAR}";
            } else if ("DATE".equals(type)) {
                parameterMap.put(randomKey, this.getValidDate(value));
                result = "TO_DATE(#{" + randomKey + ", jdbcType=VARCHAR},'YYYY/MM/DD')";
            } else {
                parameterMap.put(randomKey, this.getValidNumber(value));
                result = "#{" + randomKey + ", jdbcType=NUMERIC}";
            }
            placeHolderCache.put(value, result);
            return result;
        }
    }

    SimpleDateFormat format = new SimpleDateFormat("yyyy/MM/dd");

    private String getValidNumber(String number) {
        if (Utils.isStrictNumeric(number)) {
            return number;
        } else {
            return null;
        }
    }

    private String getValidDate(String date) {
        try {
            format.parse(date);
            return date;
        } catch (Exception ignore) {
            return "1970/01/01";
        }
    }

    public String getJoiner() {
        if (Utils.hasInjectionAttack(joiner)) {
            return "<invalid-sql>";
        }
        return joiner;
    }

    public void setJoiner(String joiner) {
        this.joiner = joiner;
    }

    public String getOperator() {
        if (Utils.hasInjectionAttack(operator)) {
            return "<invalid-sql>";
        }
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public List<String> getFields() {
        for (String field : fields) {
            if (Utils.hasInjectionAttack(field)) {
                System.err.println("has injection attack");
                return new ArrayList<>();
            }
        }
        return fields;
    }

    public void setFields(List<String> fields) {
        this.fields = fields;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public List<String> getValue() {
        return value;
    }

    public void setValue(List<String> value) {
        this.value = value;
    }

    public List<String> getTypes() {
        for (String type : types) {
            if (Utils.hasInjectionAttack(type)) {
                System.err.println("has injection attack");
                return new ArrayList<>();
            }
        }
        return types;
    }

    public void setTypes(List<String> types) {
        this.types = types;
    }

    public boolean isNextOr() {
        return nextOr;
    }

    public void setNextOr(boolean nextOr) {
        this.nextOr = nextOr;
    }

    public boolean isCurrentOr() {
        return currentOr;
    }

    public void setCurrentOr(boolean currentOr) {
        this.currentOr = currentOr;
    }
}
