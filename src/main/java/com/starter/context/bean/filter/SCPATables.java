package com.starter.context.bean.filter;

import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.util.*;

/**
 * 继承AbstractMap是想让Mybatis调用get方法来获取值, 而不是用getXXX()的方式取值
 * 这样就可以通过重写get方法简化流程
 * <p>
 * 而且条件也仅在get方法调用时生成, 减少不必要的性能损耗
 */
public class SCPATables extends AbstractMap<String, String> {
    private List<String> allMatchedTables = new ArrayList<>();

    private List<FilterElement> filterElementList = new ArrayList<>();

    private Map<String, Object> parameterMap = new HashMap<>();

    @Override
    public String get(Object tablenameObj) {
        String tablename = String.valueOf(tablenameObj);
        String result = "SCPA." + tablename;
        if (allMatchedTables.contains(tablename)) {
            StringBuffer conditions = new StringBuffer();
            for (int i = 0; i < filterElementList.size(); i++) {
                FilterElement element = filterElementList.get(i);
                // 如果当前element不是or, 但是下一个element是or, 则插入左括号
                if (i != 0) {
                    conditions.append(" ");
                    conditions.append(element.getJoiner());
                    conditions.append(" ");
                }
                if (element.isCurrentOr() == false && element.isNextOr() == true) {
                    conditions.append(" (");
                }

                conditions.append(element.getSQL(parameterMap, tablename));

                // 如果当前element是or, 但是下一个element不是or, 则插入右括号
                if (element.isCurrentOr() == true && element.isNextOr() == false) {
                    conditions.append(")");
                }
            }
            result = "(SELECT * FROM SCPA." + tablename + " T$ WHERE " + conditions + ")";
        }
        return result;
    }

    @NotNull
    @Override
    public Set<Entry<String, String>> entrySet() {
        return new HashSet<>();
    }

    public void setAllMatchedTables(List<String> allMatchedTables) {
        this.allMatchedTables = allMatchedTables;
    }

    public void setFilterElementList(List<FilterElement> filterElementList) {
        this.filterElementList = filterElementList;
    }

    public void setParameterMap(Map<String, Object> parameterMap) {
        this.parameterMap = parameterMap;
    }
}
