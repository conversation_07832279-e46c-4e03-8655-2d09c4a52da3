package com.starter.context.bean;

import org.apache.commons.lang3.StringUtils;

public class MessageBean {
	public static final String INVALID_INPUT_MESSAGE = "Invalid input: {}";
	public static final String UNAUTHORIZED_MESSAGE = "Operation not permitted";

	public static String getMessage(String message, String... replacements) {
		if (StringUtils.isEmpty(message)) {
			return message;
		}

		for (String r : replacements) {
			message = StringUtils.replaceOnce(message, "{}", r);
		}
		return message;
	}
}
