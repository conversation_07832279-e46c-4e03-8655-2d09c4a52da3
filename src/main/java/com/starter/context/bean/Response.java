package com.starter.context.bean;

import com.alibaba.fastjson.JSON;
import com.starter.utils.Utils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.io.Serializable;

@Component
@Scope("prototype")
public class Response implements Serializable {
    private ResponseHeader header = new ResponseHeader();
    private Object body;

    public Response setError(Exception e) {
        // 如果错误是ForbiddenException, 则将错误类别改为FORBIDDEN
        if (e instanceof ForbiddenException) {
            this.set(Status.FORBIDDEN, Utils.getExceptionMessage(e));
        } else {
            this.set(Status.INTERNAL_SERVER_ERROR, Utils.getExceptionMessage(e));
        }
        e.printStackTrace();
        return this;
    }

    /**
     * <pre>
     * default status = 200
     * </pre>
     *
     * @param message
     */
    public Response setMessage(String message) {
        this.header.setMessage(message);
        return this;
    }

    public Response setStatus(Status status) {
        this.header.setStatus(status);
        return this;
    }

    public ResponseHeader getHeader() {
        return this.header;
    }

    public Object getBody() {
        return body;
    }

    public Response setBody(Object body) {
        this.body = body;
        return this;
    }

    public Response set(Status status, String message) {
        this.header.setStatus(status);
        this.header.setMessage(message);

        // 转义错误日志, 将不容易看懂的系统日志转换成容易阅读的提示文字
        // 如果status!=200, 说明是错误日志, 需要进行转换
        // 如果转换前的文字与转换后的文字相同, 说明错误并未收录, 按照原错误级别推送
        // 如果转换前的文字与转换后的文字不同, 说明错误被收录, 将错误类型转换为FORBIDDEN
        // 所有FORBIDDEN的错误, 都会以message的形式显示出来
        if (status.getStatus() != 200) {
            String newMessage = ExceptionMessageConverter.convert(message);
            if (StringUtils.equals(message, newMessage) == false) {
                this.header.setStatus(Status.FORBIDDEN);
                this.header.setMessage(newMessage);
            }
        }
        return this;
    }

    public String toString() {
        return JSON.toJSONString(this);
    }
}
