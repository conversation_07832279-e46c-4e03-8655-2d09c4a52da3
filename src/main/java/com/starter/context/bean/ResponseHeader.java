package com.starter.context.bean;

public class ResponseHeader {
	private long timestamp = System.currentTimeMillis();
	private Status status = Status.SUCCESS;
	private String message = "success";

	public long getTimestamp() {
		return timestamp;
	}

	public void setTimestamp(long timestamp) {
		this.timestamp = timestamp;
	}

	public int getStatus() {
		return status.getStatus();
	}

	public void setStatus(int status) {
		switch (status) {
		case 200:
			this.status = Status.SUCCESS;
			break;
		case 401:
			this.status = Status.UNAUTHORIZED;
			break;
		case 403:
			this.status = Status.FORBIDDEN;
			break;
		case 500:
			this.status = Status.INTERNAL_SERVER_ERROR;
			break;
		default:
			break;
		}

	}

	public void setStatus(Status status) {
		this.status = status;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}
}
