package com.starter.context.bean;

import org.apache.commons.lang3.StringUtils;

public class ExceptionMessageConverter {

    public static String convert(String message) {
        if (StringUtils.isBlank(message)) {
            return "ExceptionMessageConverter - 未知错误";
        }
        if (message.startsWith("ORA-") && message.length() > 10) {
            String errorCode = message.substring(0, 9);
            String errorMessage = "<br/>- " + message.substring(10);
            switch (errorCode) {
                case "ORA-00001":
                    return "数据包含重复行, 请检查源数据." + errorMessage;
                case "ORA-01795":
                    return "查询条件过多, 请减少查询条件." + errorMessage;
            }
        }
        return message;
    }
}
