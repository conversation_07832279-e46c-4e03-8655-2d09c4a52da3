package com.starter.context.bean;

import java.util.Map;

public class TblColumn {

    public TblColumn(Map<String, String> data) {
        this.setName(data.get("COLUMN_NAME"));
        this.setType(data.get("DATA_TYPE"));
    }

    public TblColumn() {
    }

    private String name;

    private String type;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        switch (type) {
            case "NUMBER":
            case "FLOAT":
                this.type = "NUMBER";
                break;
            case "DATE":
                this.type = "DATE";
                break;
            default:
                this.type = "VARCHAR2";
                break;
        }
    }
}
