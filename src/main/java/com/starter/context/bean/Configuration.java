package com.starter.context.bean;

import java.util.UUID;

public class Configuration {
    public static final String DATE_FORMAT = "yyyy/MM/dd";

    public static final String APPLICATION_NAME = "scp-service-prod";

    public static final String CACHED_KEY = APPLICATION_NAME + ":";

    public static final String CACHED_SESSION_KEY = "login:" + APPLICATION_NAME + ":";

    public static final String JSON_HTML_ENCODE_ESCAPE_STR = UUID.randomUUID().toString() + "@";

    public static final boolean OPEN_SERVER_SIDE_XSS_FILTER = false; // 全局xss过滤器, 一般都是关上的, 打开可能会有查询bug

    public static String MAIL_STYLE = "";

    static {
        MAIL_STYLE += "<style>";
        MAIL_STYLE += "p{font-size: 9.5pt;font-family:<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>;padding:0;margin:0} ";
        MAIL_STYLE += "span{font-size: 9.5pt;font-family:<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>;} ";
        MAIL_STYLE += "div{font-size: 9.5pt;font-family:<PERSON><PERSON><PERSON>,<PERSON>g<PERSON>ian;} ";
        MAIL_STYLE += "</style>";
    }
}
