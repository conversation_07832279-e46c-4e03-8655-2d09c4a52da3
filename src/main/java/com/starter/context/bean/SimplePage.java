package com.starter.context.bean;

import com.adm.system.bean.ConditionsBean;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.starter.context.servlet.UserContextHolder;
import com.starter.utils.Utils;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class SimplePage<T> implements Serializable {
    @JsonIgnore
    private static final long serialVersionUID = -6283758828350113183L;
    private int total = 0;// 页总数
    @JsonIgnore
    private int currentPage = 1; // 开始页码
    @JsonIgnore
    private int length = 10; // 每页条数
    @JsonIgnore
    private int start = 0; // 每页条数
    @JsonIgnore
    private int end = 0; // 每页条数
    private List<T> data = new ArrayList<>();
    @JsonIgnore
    private int maxRows;
    @JsonIgnore
    private String sort = "";
    @JsonIgnore
    private String sortColumn = "";
    @JsonIgnore
    private boolean pagging;
    @JsonIgnore
    private ConditionsBean conditions;
    @JsonIgnore
    private List<String> outputColumns = new ArrayList<>();

    public static int PAGE_MAX = 1048575;

    public SimplePage() {
    }

    public SimplePage(Map<String, Object> parameterMap) {
        String where = (String) parameterMap.get("_where");
        if (Utils.hasInjectionAttack(where)) {
            parameterMap.remove("_where");
        }
        currentPage = parameterMap.containsKey("_currentPage") == false ? currentPage : Integer.parseInt(String.valueOf(parameterMap.remove("_currentPage")));
        length = parameterMap.containsKey("_pageSize") == false ? length : Integer.parseInt(String.valueOf(parameterMap.remove("_pageSize")));
        start = (currentPage - 1) * length;
        end = start + length;

        // 获取用户自定义输出字段
        try {
            @SuppressWarnings("unchecked") List<String> columns = (List<String>) parameterMap.remove("_outputColumns");
            // 如果columns == null, 说明网页禁用指定列参数, 则UserContextHolder.setSpecifyColumnHolder(null)
            if (columns != null) {
                for (String column : columns) {
                    if (Utils.hasInjectionAttack(column) == false) {
                        outputColumns.add(column);
                    }
                }
                UserContextHolder.setSpecifyColumnHolder(outputColumns.isEmpty() == false);
            } else {
                UserContextHolder.setSpecifyColumnHolder(null);
            }
        } catch (Exception e) {
            outputColumns = new ArrayList<>();
        }

        List<String> sortList = new ArrayList<>();
        String sortColumn = (String) parameterMap.remove("_sortColumn");
        String sortOrder = (String) parameterMap.remove("_sortOrder");
        String[] so = StringUtils.isBlank(sortOrder) ? new String[]{} : sortOrder.split(",");
        if (StringUtils.isNotBlank(sortColumn)) {
            String[] sc = sortColumn.split(",");

            for (int i = 0; i < sc.length; i++) {
                sortList.add("\"" + StringUtils.trim(StringUtils.upperCase(sc[i])) + "\" " + this.getSortOrder(so, i));
            }

            sort = StringUtils.join(sortList, ",");
        }

        if (Utils.hasInjectionAttack(sortColumn) || Utils.hasInjectionAttack(sortOrder)) {
            sort = "";
        }
        this.sortColumn = sortColumn;
        maxRows = Utils.parseInt(parameterMap.remove("_maxRows"));
        pagging = Utils.parseBoolean(parameterMap.remove("_pagging"), true);
        conditions = new ConditionsBean(parameterMap);

        parameterMap.put("_page", this);
    }

    private String getSortOrder(String[] so, int index) {
        if (so.length > index) {
            String sortOrder = so[index];
            if ("DESC".equalsIgnoreCase(sortOrder) == false && "ASC".equalsIgnoreCase(sortOrder) == false) {
                return "";
            } else {
                return sortOrder;
            }
        } else {
            return "";
        }
    }

    public int getTotal() {
        if (this.total == 0) {
            return data.size();
        }
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getEnd() {
        return end;
    }

    public void setEnd(int end) {
        this.end = end;
    }

    public List<T> getData() {
        return data;
    }

    public void setData(List<T> data) {
        this.data = data;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public int getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(int currentPage) {
        this.currentPage = currentPage;
    }

    public int getLength() {
        return length;
    }

    public void setLength(int length) {
        this.length = length;
    }

    public int getStart() {
        return start;
    }

    public void setStart(int start) {
        this.start = start;
    }

    public List<String> getOutputColumns() {
        if (this.outputColumns.isEmpty() == true) {
            return null;
        }
        return this.outputColumns;
    }

    public int getMaxRows() {
        return maxRows;
    }

    public void setMaxRows(int maxRows) {
        this.maxRows = maxRows;
    }

    public boolean isPagging() {
        return pagging;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public void setPagging(boolean pagging) {
        this.pagging = pagging;
    }

    public ConditionsBean getConditions() {
        return conditions;
    }

    public void setConditions(ConditionsBean conditions) {
        this.conditions = conditions;
    }

    public String getSortColumn() {
        return sortColumn;
    }
}
