package com.starter.context.kettle;

import com.starter.context.bean.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(name = "scp-kettle-prod", url = "${kettle.url}")
public interface KettleFeignClient {
    @PostMapping(value = "/kettle/trans/call")
    Response call(String id);

    @PostMapping(value = "/kettle/disk_mapping")
    Response queryDiskMapping();
}
