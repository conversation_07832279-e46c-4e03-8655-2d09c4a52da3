package com.starter.context.servlet;

import com.adm.system.bean.CascaderBean;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.starter.context.bean.filter.CascaderLooper;
import com.starter.context.bean.filter.CascaderValue;
import com.starter.context.configuration.SCPATableConfiguration;
import com.starter.utils.Utils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

public class ServiceHelper {
    public List<CascaderBean> parseCascader(List<Map<String, String>> dataList) {
        return parseCascader(dataList, true);
    }

    /**
     * 将查询出来的结果集转换为Cascader可以识别的格式
     *
     * @param dataList 源结果集, 需要包含 CATEGORY和 NAME
     * @return 可以识别数据
     */
    public List<CascaderBean> parseCascader(List<Map<String, String>> dataList, boolean containOthers) {
        List<CascaderBean> resultList = new ArrayList<>();

        Map<String, List<Map<String, String>>> tempMap = new LinkedHashMap<>();

        for (Map<String, String> map : dataList) {
            List<Map<String, String>> list = tempMap.computeIfAbsent(map.get("CATEGORY"), key -> new ArrayList<>());
            list.add(map);
        }

        for (String key : tempMap.keySet()) {
            CascaderBean bean = new CascaderBean();
            resultList.add(bean);
            bean.setLabel(key);
            bean.setValue(key);
            boolean hasOthers = false;
            for (Map<String, String> map : tempMap.get(key)) {
                CascaderBean subBean = new CascaderBean();
                String name = map.get("NAME");
                hasOthers = "Others".equals(name);
                subBean.setLabel(name);
                subBean.setValue(map.getOrDefault("VALUE", name));
                bean.addChild(subBean);
            }
            if (hasOthers == false && containOthers) {
                CascaderBean subBean = new CascaderBean();
                subBean.setLabel("Others");
                subBean.setValue("Others");
                bean.addChild(subBean);
            }

        }

        return resultList;
    }

    public void generateCascaderFilterSQL(Map<String, Object> parameterMap) {
        this.generateCascaderFilterSQL(parameterMap, null, null, "T", "_filters");
    }

    public void generateCascaderFilterSQL(Map<String, Object> parameterMap, CascaderLooper looper) {
        this.generateCascaderFilterSQL(parameterMap, looper, null, "T", "_filters");
    }

    /**
     * 将parameterMap转换为SQL, 并且以_filter为key, 放入parameterMap
     *
     * @param parameterMap Controller传递过来的参数列表
     * @param looper       有些场景下, 需要从参数列表中取出某个Key, 从来生成额外的条件, 所以添加了这个参数
     *                     false将跳过掉该元素, true则将这个元素生成对应的SQL, 如果没有, 可以为null
     *                     挑出去的元素, 可以用parseInSQLFromValues方法生成单独的条件
     * @param targetTable  这个条件应用在哪个表中, 如果这个表不包含该字段, 则会插入一个恒false的条件
     * @param tableAlias   生成SQL中的表别名, 默认为T
     * @param excludeKeys  需要排除的keys, 这些keys在生成SQL时将被跳过
     */
    public void generateCascaderFilterSQL(Map<String, Object> parameterMap, CascaderLooper looper, String targetTable, String tableAlias, String generateKey, String... excludeKeys) {
        Map<String, List<String>> filterMap = new HashMap<>();
        JSONObject scpFilter = (JSONObject) parameterMap.get("$scpFilter");
        if (scpFilter != null && scpFilter.getJSONArray("cascader") != null && scpFilter.getJSONArray("cascader").isEmpty() == false) {
            JSONArray filterArray = scpFilter.getJSONArray("cascader");
            for (Object subObj : filterArray) {
                CascaderValue value = new CascaderValue(subObj);
                if (Utils.hasInjectionAttack(value.getKey()) == true) {
                    continue;
                }

                String key = Utils.randomStr(8);
                if (looper == null || looper.doLoop(value)) {
                    List<String> fl = filterMap.computeIfAbsent(value.getKey(), k -> new ArrayList<>());
                    fl.add("#{" + key + ",jdbcType=VARCHAR}");
                    parameterMap.put(key, value.getValue());
                }
            }

            if (StringUtils.isEmpty(generateKey)) {
                generateKey = "_filters";
            }
            if (StringUtils.isEmpty(tableAlias)) {
                tableAlias = "T";
            }
            if (StringUtils.endsWith(tableAlias, ".") == false) {
                tableAlias = tableAlias + ".";
            }
            List<String> filterList = new ArrayList<>();
            for (String key : filterMap.keySet()) {
                if (Utils.containsStr(excludeKeys, key) == false) {
                    if (targetTable != null) {
                        if (SCPATableConfiguration.isColumnInTable(targetTable, key) == false) {
                            filterList.add("0 = 1");
                            continue;
                        }
                    }

                    List<String> fl = filterMap.get(key);
                    filterList.add(tableAlias + key + " IN (" + StringUtils.join(fl, ",") + ")");
                }
            }

            parameterMap.put(generateKey, StringUtils.join(filterList, " AND "));
        }
    }

    // 兼容旧版本的Filter结果处理
    public void generateCascaderFilterSQL(String filterArrayKey, Map<String, Object> parameterMap, CascaderLooper looper, String targetTable, String tableAlias, String generateKey, String... excludeKeys) {
        Map<String, List<String>> filterMap = new HashMap<>();
        JSONArray filterArray = (JSONArray) parameterMap.get(filterArrayKey);
        if (filterArray != null && filterArray.isEmpty() == false) {
            for (Object subObj : filterArray) {
                CascaderValue value = new CascaderValue(subObj);
                if (Utils.hasInjectionAttack(value.getKey()) == true) {
                    continue;
                }

                String key = Utils.randomStr(8);
                if (looper == null || looper.doLoop(value)) {
                    List<String> fl = filterMap.computeIfAbsent(value.getKey(), k -> new ArrayList<>());
                    fl.add("#{" + key + ",jdbcType=VARCHAR}");
                    parameterMap.put(key, value.getValue());
                }
            }

            if (StringUtils.isEmpty(generateKey)) {
                generateKey = "_filters";
            }
            if (StringUtils.isEmpty(tableAlias)) {
                tableAlias = "T";
            }
            if (StringUtils.endsWith(tableAlias, ".") == false) {
                tableAlias = tableAlias + ".";
            }
            List<String> filterList = new ArrayList<>();
            for (String key : filterMap.keySet()) {
                if (Utils.containsStr(excludeKeys, key) == false) {
                    if (targetTable != null) {
                        if (SCPATableConfiguration.isColumnInTable(targetTable, key) == false) {
                            filterList.add("0 = 1");
                            continue;
                        }
                    }

                    List<String> fl = filterMap.get(key);
                    filterList.add(tableAlias + key + " IN (" + StringUtils.join(fl, ",") + ")");
                }
            }

            parameterMap.put(generateKey, StringUtils.join(filterList, " AND "));
        }
    }

    /**
     * 生成IN的SQL, 不含AND或者OR连接符
     *
     * @param parameterMap Controller传递过来的参数列表
     * @param column       字段名, 比如T.MATERIAL
     * @param values       值列表
     * @return 拼接好的SQL
     */
    public String parseInSQLFromValues(Map<String, Object> parameterMap, String column, List<String> values) {
        if (values != null && values.isEmpty() == false) {
            List<String> placeHolders = new ArrayList<>();

            for (String value : values) {
                String key = Utils.randomStr(8);
                placeHolders.add("#{" + key + ",jdbcType=VARCHAR}");
                parameterMap.put(key, value);
            }

            return column + " IN (" + StringUtils.join(placeHolders, ",") + ")";
        }
        return null;
    }
}
