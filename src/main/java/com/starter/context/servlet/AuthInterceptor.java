package com.starter.context.servlet;

import com.alibaba.fastjson.JSON;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.Status;
import com.starter.login.bean.Session;
import com.starter.utils.Utils;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.servlet.HandlerInterceptor;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class AuthInterceptor implements HandlerInterceptor {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private Response response;

    public static final List<String> SECURE_METHOD_WHITE_LIST = new ArrayList<>(); // 无需数据鉴权的URL列表

    public static final List<String> SECURE_MENU_WHITE_LIST = new ArrayList<>(); // 无需菜单鉴权的URL列表

    private @Value("${session.timeout-in-minute}")
    long sessionTimeout;

    /**
     * monitor本意是开启本机负载监控, 但是monitor一般只在PROD环境上启用, 所以monitor也被用来识别当前环境是否为PROD
     * 如果以后需要将两者区分开, 可以将这个配置分离出去
     * <p>
     * monitor = true, 代表着正式环境, 正式环境需要启动XSS等一系列安全验证
     */
    @Value("${spring.monitor}")
    private boolean monitor;

    private final String allowedOriginPattern = monitor ? "https://scp-dss.cn.schneider-electric.com" : "*";

    static {
        SECURE_METHOD_WHITE_LIST.add("/flush");
        SECURE_METHOD_WHITE_LIST.add("/query_system_notification");
        SECURE_METHOD_WHITE_LIST.add("/microsoftgraph/mailreply");
        SECURE_METHOD_WHITE_LIST.add("/microsoftgraph/teams/dss_user_community");
        SECURE_METHOD_WHITE_LIST.add("/pingsso/callback");
        SECURE_METHOD_WHITE_LIST.add("/pingsso/login");
        SECURE_METHOD_WHITE_LIST.add("/system/get_image_from_local");
        SECURE_METHOD_WHITE_LIST.add("/components/query_markdown_by_sql");
        SECURE_METHOD_WHITE_LIST.add("/system/material_risk/recreate_material_risk_level_view_scheduled");
        SECURE_METHOD_WHITE_LIST.add("/intelligent_agent/moss/create_new_conversation");
    }

    /**
     * 每次请求都要检查用户是否有访问某个方法的权限, 是否有路由权限不在此方法, 请参考 ILoginService.checkRouteAuth
     * 检查路由是否有权限时, 不会被此方法拦截, 具体配置请参考 AuthConfiguration.addInterceptors
     *
     * @param request  request
     * @param response response
     * @param handler  handler
     * @return return
     * @throws Exception Exception
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestMethod = request.getMethod();
        if ("GET".equals(requestMethod) == false && "POST".equals(requestMethod) == false) {
            response.setStatus(HttpServletResponse.SC_METHOD_NOT_ALLOWED);
            return false;
        }

        // 做下载拦截器, 如果包含download-topic header, 则记录, 以便下载的时候实时反馈进度
        String downloadTopic = this.getHeaderByKey(request, "download-topic");
        if (StringUtils.isBlank(downloadTopic) == true) {
            UserContextHolder.removeDownloadTopicHolder();
        } else {
            UserContextHolder.setDownloadTopicHolder(downloadTopic);
        }


        // 设置线程变量
        // 请求开始时清空线程池中上一个线程遗留下的信息, 防止误读
        UserContextHolder.setDefaultUserid();
        // 将全局缓存设置为默认值
        UserContextHolder.setDefaultGlobalCache();
        // 该请求是否需要debug
        String debug = this.getHeaderByKey(request, "debug");
        UserContextHolder.setDebugHolder(debug);
        // 请求开始时, 获取该请求所对应的token
        String token = this.getHeaderByKey(request, "token");
        UserContextHolder.setTokenHolder(token);
        // 获取当前URL
        String url = request.getRequestURI();
        String url2 = StringUtils.isBlank(request.getQueryString()) ? url : url + "?" + request.getQueryString();
        UserContextHolder.setMethodUrlHolder(url2);

        if (SECURE_METHOD_WHITE_LIST.contains(url)) {
            return true;
        }

        String requestIp = Utils.getIpAddr(request);
        // 如果token为空, 那么返回登录超时
        if (token == null) {
            this.writeUnauthorized(response);
            return false;
        }

        String cacheKey = Configuration.CACHED_SESSION_KEY + token;
        Object valueObj = redisTemplate.opsForValue().get(cacheKey);
        // 判断登录的token
        if (valueObj == null) {
            writeTimeout(response);
            return false;
        }

        // 如果session中有数据, 对session中的数据进行判断
        try {
            Session session = (Session) valueObj;
            // 保存用户ID, 以便根据ID执行缓存操作
            UserContextHolder.setUserID(session.getUserid());
            String ip = session.getLoginIP();
            if (StringUtils.equals(ip, requestIp) == false) {
                if ("127.0.0.1".equals(requestIp) == false && "0:0:0:0:0:0:0:1".equals(requestIp) == false) {
                    writeTimeout(response);
                    return false;
                }
            }

            if (session.getAuthMethod() == null || session.getAuthMethod().containsKey(url) == false) {
                System.err.println("Unauthorized url " + url);
                writeUnauthorized(response);
                return false;
            }
        } catch (Exception e) {
            writeTimeout(response);
            return false;
        }

        request.setAttribute("_cached_session", valueObj);

        // rlogin 远程自动登录, session只有5分钟, 一般用于网页截图等操作, 不会自动刷新session时间
        // rlogin 还会自动添加水印
        // slogin 远程接口调用, session只有5分钟, 一般用于API接口调用, 不会自动刷新session时间
        // 如果不是以rlogin/slogin开头的session, 刷新session时长
        if (StringUtils.startsWith(token, "rlogin-") == false && StringUtils.startsWith(token, "slogin-") == false) {
            redisTemplate.expire(cacheKey, sessionTimeout, TimeUnit.MINUTES);
        }
        return true;
    }

    public void writeTimeout(HttpServletResponse httpResponse) throws IOException {
        response.setStatus(Status.TIME_OUT);
        response.setMessage("Session timeout");
        httpResponse.setHeader("Access-Control-Allow-Origin", allowedOriginPattern);
        httpResponse.setHeader("Access-Control-Allow-Credentials", "true");
        httpResponse.setHeader("Access-Control-Allow-Methods", "POST, GET");
        httpResponse.setHeader("Access-Control-Max-Age", "3600");
        httpResponse.setHeader("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept");
        httpResponse.setContentType("application/json;charset=UTF-8");
        httpResponse.getWriter().print(JSON.toJSONString(response));
        httpResponse.getWriter().close();
    }

    public void writeUnauthorized(HttpServletResponse httpResponse) throws IOException {
        response.setStatus(Status.UNAUTHORIZED);
        response.setMessage("Unauthorized request");
        httpResponse.setHeader("Access-Control-Allow-Origin", allowedOriginPattern);
        httpResponse.setHeader("Access-Control-Allow-Credentials", "true");
        httpResponse.setHeader("Access-Control-Allow-Methods", "POST, GET");
        httpResponse.setHeader("Access-Control-Max-Age", "3600");
        httpResponse.setHeader("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept");
        httpResponse.setContentType("application/json;charset=UTF-8");
        httpResponse.getWriter().print(JSON.toJSONString(response));
        httpResponse.getWriter().close();
    }

    private String getHeaderByKey(HttpServletRequest request, String key) {
        return request.getHeader(key) == null ? request.getParameter(key) : request.getHeader(key);
    }
}
