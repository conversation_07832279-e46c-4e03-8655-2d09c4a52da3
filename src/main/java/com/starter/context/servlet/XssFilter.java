package com.starter.context.servlet;

import java.io.IOException;

import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.annotation.WebFilter;
import jakarta.servlet.http.HttpServletRequest;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;

@WebFilter
@Component
public class XssFilter implements Filter {

	@Override
	public void destroy() {

	}

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
		HttpServletRequest req = (HttpServletRequest) request;
		XssHttpServletRequestWrapper xssRequestWrapper = new XssHttpServletRequestWrapper(req);
		chain.doFilter(xssRequestWrapper, response);
	}

	@Override
	public void init(FilterConfig arg0) throws ServletException {

	}

	@Bean
	@Primary
	public ObjectMapper xssObjectMapper(Jackson2ObjectMapperBuilder builder) {
		ObjectMapper objectMapper = builder.createXmlMapper(false).build();
		SimpleModule xssModule = new SimpleModule();
		xssModule.addSerializer(new XssStringJsonSerializer());
		xssModule.addSerializer(new XssDateJsonSerializer());
		objectMapper.registerModule(xssModule);
		return objectMapper;
	}
}
