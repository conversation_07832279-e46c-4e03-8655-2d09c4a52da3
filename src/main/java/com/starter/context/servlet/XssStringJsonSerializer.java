package com.starter.context.servlet;

import java.io.IOException;

import org.apache.commons.text.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.starter.context.bean.Configuration;

public class XssStringJsonSerializer extends JsonSerializer<String> {

    @Override
    public Class<String> handledType() {
        return String.class;
    }

    /**
     * <pre>
     * 1. JSON在将String对象序列化时,会调用此方法,所有的String都会经过HTML转义提交至前台
     * 2. String 如果是以一个既定值开头,说明此文本不需要进行HTML转义操作, 用来解决一些因转义导致的bug
     * </pre>
     */
    @Override
    public void serialize(String value, JsonGenerator gen, SerializerProvider serializers) throws IOException, JsonProcessingException {
        if (Configuration.OPEN_SERVER_SIDE_XSS_FILTER == true) {
            if (value != null) {
                if (value.startsWith(Configuration.JSON_HTML_ENCODE_ESCAPE_STR)) {
                    value = StringUtils.removeStart(value, Configuration.JSON_HTML_ENCODE_ESCAPE_STR);
                    gen.writeString(value);
                } else {
                    // 先解码再编码,防止出现重复编码的情况
                    String encodedValue = StringEscapeUtils.escapeHtml4(StringEscapeUtils.unescapeHtml4(value));
                    gen.writeString(encodedValue);
                }
            }
        } else {
            gen.writeString(value);
        }
    }
}
