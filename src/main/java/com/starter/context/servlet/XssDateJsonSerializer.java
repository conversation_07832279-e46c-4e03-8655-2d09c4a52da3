package com.starter.context.servlet;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class XssDateJsonSerializer extends JsonSerializer<Date> {

    @Override
    public Class<Date> handledType() {
        return Date.class;
    }

    private final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy/MM/dd");

    /**
     * <pre>
     * 1. 将所有日期在进行格式化的时候都转成yyyy/MM/dd的格式
     * </pre>
     */
    @Override
    public void serialize(Date value, JsonGenerator gen, SerializerProvider serializers) throws IOException, JsonProcessingException {
        gen.writeString(DATE_FORMAT.format(value));
    }
}
