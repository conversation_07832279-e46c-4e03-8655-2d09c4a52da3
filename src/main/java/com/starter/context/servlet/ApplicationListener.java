package com.starter.context.servlet;

import com.starter.context.SpringContext;
import com.starter.context.bean.AuthAnnotation;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.configuration.SCPATableConfiguration;
import com.starter.login.dao.ILoginDao;
import com.starter.utils.EncryptionUtil;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

@Component
public class ApplicationListener implements ApplicationRunner {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private ILoginDao loginDao;

    @Resource
    public void setILoginDao(ILoginDao loginDao) {
        this.loginDao = loginDao;
    }

    @Override
    public void run(ApplicationArguments args) {
        try {
            List<AuthAnnotation> authAnnotationList = new ArrayList<>();

            Map<String, Object> beanList = SpringContext.getApplicationContext().getBeansWithAnnotation(Controller.class);
            Iterator<Entry<String, Object>> iterator = beanList.entrySet().iterator();

            Entry<String, Object> entry;
            while (iterator.hasNext()) {
                entry = iterator.next();
                Method[] methods = ReflectionUtils.getUniqueDeclaredMethods(entry.getValue().getClass());
                for (Method method : methods) {
                    SchneiderRequestMapping methodRequestMapping = AnnotationUtils.findAnnotation(method, SchneiderRequestMapping.class);
                    if (methodRequestMapping != null) {
                        SchneiderRequestMapping classRequestMapping = AnnotationUtils.findAnnotation(entry.getValue().getClass(), SchneiderRequestMapping.class);

                        // 获取类上的注解
                        String[] prefix = new String[]{"/"};
                        String parentClassification = null;
                        String parentParent = null;
                        if (classRequestMapping != null) {
                            prefix = classRequestMapping.value();
                            parentClassification = classRequestMapping.classification();
                            parentParent = classRequestMapping.parent();
                        }

                        // 获取方法的注解
                        String[] urls = methodRequestMapping.value();
                        String classification = methodRequestMapping.classification();
                        String parent = methodRequestMapping.parent();

                        String finalClassification = StringUtils.isEmpty(classification) ? parentClassification : classification;
                        finalClassification = StringUtils.isEmpty(finalClassification) ? SchneiderRequestMapping.classificationDefault : finalClassification;

                        String finalParent = StringUtils.isEmpty(parent) ? parentParent : parent;
                        finalParent = StringUtils.isEmpty(finalParent) ? SchneiderRequestMapping.parentDefault : finalParent;

                        for (String p : prefix) {
                            for (String u : urls) {
                                AuthAnnotation authAnnotation = new AuthAnnotation();
                                authAnnotation.setUrl(p, u);
                                authAnnotation.setClassification(finalClassification);
                                authAnnotation.setMenuCode(EncryptionUtil.md5Short8(authAnnotation.getUrl()));
                                authAnnotation.setParent(finalParent);
                                authAnnotationList.add(authAnnotation);
                            }
                        }
                    }
                }
            }

            if (authAnnotationList.size() > 0) {
                loginDao.saveMethodAuth(authAnnotationList);
            }
            // load all scpa table
            SCPATableConfiguration.setTabCols(loginDao.querySCPATabCols());
            log.info(">>>> Application started! <<<<");
        } catch (Exception e) {
            e.printStackTrace();
            System.exit(1);
        }
    }
}
