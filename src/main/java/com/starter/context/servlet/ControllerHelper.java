package com.starter.context.servlet;

import com.alibaba.fastjson.JSONObject;
import com.starter.context.bean.filter.FilterGenerator;
import com.starter.login.bean.Session;
import com.starter.utils.Utils;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 控制层功能类
 */

public abstract class ControllerHelper {

    public Map<String, Object> parameterMap = new HashMap<>();
    public Session session;

    /**
     * 页面初始加载,初始化session、parameterMap、logger对象
     *
     * @param request request
     */
    public void pageLoad(HttpServletRequest request) {
        this.loadParameter(request);
        this.loadSession(request);
    }

    /**
     * 目前redis缓存是用户隔离的,不同的用户登录系统会使用不同的缓存,但是有些场景需要使用全局缓存,比如无权限控制的报表,这时候就需要设置全局缓存,加快访问速度
     * 设置报表为全局缓存之后, 报表只会缓存一份
     *
     * @param _global_cache 是否是全局缓存,默认是false
     */
    public void setGlobalCache(boolean _global_cache) {
        UserContextHolder.setGlobalCache(_global_cache);
    }

    public String getIpAddr(HttpServletRequest request) {
        return Utils.getIpAddr(request);
    }

    public String[] getIpAddrs(HttpServletRequest request) {
        return new String[]{Utils.getIpAddr(request), Utils.getRealIpAddr(request)};
    }

    /**
     * 私有方法, 获取loadParameter
     * <p>
     *
     * @param request request
     */
    private void loadParameter(HttpServletRequest request) {
        parameterMap = new HashMap<>();
        if ("application/json;charset=UTF-8".equalsIgnoreCase(request.getHeader("Content-Type"))) {
            String reqStr = null;
            try {
                if (request.getInputStream().isFinished()) {
                    reqStr = (String) request.getAttribute("_parameter_str");
                } else {
                    try (BufferedReader streamReader = new BufferedReader(new InputStreamReader(request.getInputStream(), StandardCharsets.UTF_8))) {
                        // @RequestBody 会读取request,导致这里无法继续读取request, 所以, 在使用@RequestBody的方法上,是无法从parameterMap中获取到值的
                        if (request.getInputStream().isFinished()) {
                            return;
                        }
                        StringBuilder responseStrBuilder = new StringBuilder();
                        String inputStr;
                        while ((inputStr = streamReader.readLine()) != null) {
                            responseStrBuilder.append(inputStr);
                        }

                        reqStr = responseStrBuilder.toString();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            if (reqStr != null) {
                Map<String, Object> paramMap = JSONObject.parseObject(reqStr);

                for (String key : paramMap.keySet()) {
                    Object value = paramMap.get(key);
                    if (value instanceof String) {
                        paramMap.put(key, StringUtils.trim((String) value));
                    }
                }
                parameterMap.putAll(paramMap);
            }
        } else {
            Map<String, String[]> parameters = request.getParameterMap();
            Set<String> keySet = parameters.keySet();

            String[] para;
            for (String key : keySet) {
                if (parameters.get(key) != null) {
                    para = parameters.get(key);
                    if (para.length == 1) {
                        parameterMap.put(StringUtils.removeEnd(key, "[]"), StringUtils.trim(para[0]));
                    } else {
                        parameterMap.put(StringUtils.removeEnd(key, "[]"), para);
                    }
                }
            }
        }
        this.loadFilter();
    }

    /**
     * 私有方法, 获取session
     *
     * @param request request
     */
    private void loadSession(HttpServletRequest request) {
        Object cachedSession = request.getAttribute("_cached_session");
        if (cachedSession != null) {
            session = (Session) cachedSession;
        } else {
            session = new Session();
        }

        if (parameterMap.containsKey("session") == false) {
            parameterMap.put("session", session);
        }
    }

    private void loadFilter() {
        new FilterGenerator().generator(parameterMap, "$scpFilter");
    }
}
