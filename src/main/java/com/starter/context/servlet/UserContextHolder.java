package com.starter.context.servlet;


public class UserContextHolder {
    private static final ThreadLocal<String> useridHolder = new ThreadLocal<>();
    private static final ThreadLocal<Boolean> globalCache = new ThreadLocal<>();

    private static final ThreadLocal<String> downloadTopicHolder = new ThreadLocal<>();

    private static final ThreadLocal<String> methodUrlHolder = new ThreadLocal<>();

    private static final ThreadLocal<String> debugHolder = new ThreadLocal<>();

    private static final ThreadLocal<String> tokenHolder = new ThreadLocal<>();

    // 用户是否指定列下载
    // 1. true, 用户指定某些列进行下载
    // 2. false, 用户不指定
    // 3. null, 禁用指定列下载
    private static final ThreadLocal<Boolean> specifyColumnHolder = new ThreadLocal<>();

    public static void setUserID(String userID) {
        useridHolder.set(userID);
    }

    public static String getUserID() {
        return useridHolder.get();
    }

    public static void setDownloadTopicHolder(String topic) {
        downloadTopicHolder.set(topic);
    }

    public static String getDownloadTopic() {
        return downloadTopicHolder.get();
    }

    public static void removeDownloadTopicHolder() {
        downloadTopicHolder.remove();
    }

    public static void setDefaultUserid() {
        useridHolder.remove();
    }

    public static void setDefaultGlobalCache() {
        globalCache.set(false);
    }


    public static void setGlobalCache(Boolean cache) {
        globalCache.set(cache);
    }

    public static void setMethodUrlHolder(String url) {
        methodUrlHolder.set(url);
    }

    public static String getMethodUrlHolder() {
        return methodUrlHolder.get();
    }

    public static String getDebugHolder() {
        return debugHolder.get();
    }

    public static void setDebugHolder(String debug) {
        debugHolder.set(debug);
    }

    public static String getTokenHolder() {
        return tokenHolder.get();
    }

    public static void setTokenHolder(String debug) {
        tokenHolder.set(debug);
    }

    public static Boolean getSpecifyColumnHolder() {
        return specifyColumnHolder.get();
    }

    public static void setSpecifyColumnHolder(Boolean specifyColumn) {
        specifyColumnHolder.set(specifyColumn);
    }


    public static Boolean isGlobalCache() {
        Boolean cache = globalCache.get();
        // 默认不是全局缓存
        if (cache == null) {
            return false;
        }
        return cache;
    }
}
