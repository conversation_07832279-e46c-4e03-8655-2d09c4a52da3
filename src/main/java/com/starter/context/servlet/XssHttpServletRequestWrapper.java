package com.starter.context.servlet;

import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletRequestWrapper;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.text.StringEscapeUtils;

public class XssHttpServletRequestWrapper extends HttpServletRequestWrapper {

	public XssHttpServletRequestWrapper(HttpServletRequest request) {
		super(request);
	}

	@Override
	public String getParameter(String name) {
		String value = super.getParameter(name);
		if (StringUtils.isNotEmpty(value)) {
			value = StringEscapeUtils.escapeHtml4(StringEscapeUtils.unescapeHtml4(value));
		}
		return value;
	}

	@Override
	public Map<String, String[]> getParameterMap() {
		Map<String, String[]> result = new HashMap<>();
		Map<String, String[]> parameters = super.getParameterMap();
		Set<String> keySet = parameters.keySet();

		for (Iterator<String> it = keySet.iterator(); it.hasNext();) {
			String key = it.next();
			if (parameters.get(key) != null) {
				result.put(key, xssFilter(parameters.get(key)));
			}
		}

		return result;
	}

	private String[] xssFilter(String[] param) {
		for (int i = 0; i < param.length; i++) {
			param[i] = StringEscapeUtils.escapeHtml4(StringEscapeUtils.unescapeHtml4(param[i]));
		}
		return param;
	}

	@Override
	public String[] getParameterValues(String name) {
		String[] parameterValues = super.getParameterValues(name);
		if (parameterValues == null) {
			return null;
		}
		for (int i = 0; i < parameterValues.length; i++) {
			parameterValues[i] = StringEscapeUtils.escapeHtml4(StringEscapeUtils.unescapeHtml4(parameterValues[i]));
		}
		return parameterValues;
	}
}
