package com.starter.context.mail;

import com.starter.context.bean.SCPRuntimeException;
import com.starter.utils.Utils;

import java.text.SimpleDateFormat;

public class MailCalendar {

    private final String dateFormat = "yyyyMMdd'T'HHmmss";

    private String start; //yyyyMMdd'T'HHmmss, 如20231211T151000

    private String end; //yyyyMMdd'T'HHmmss, 如20231211T161000

    private String organizer; // organizer email

    private String categories;  // 邮件类别

    private String location; // 会议地点

    private String optional;

    public String getOptional() {
        return optional;
    }

    public void setOptional(String optional) {
        this.optional = optional;
    }

    public String getStart() {
        return start;
    }

    public void setStart(String start) {
        try {
            SimpleDateFormat format = new SimpleDateFormat(dateFormat);
            this.start = format.format(format.parse(start)); // 尝试转换一下日期, 如果没问题就说明日期格式是正确的
        } catch (Exception e) {
            throw new SCPRuntimeException(Utils.getExceptionMessage(e));
        }
    }

    public String getEnd() {
        return end;
    }

    public void setEnd(String end) {
        try {
            SimpleDateFormat format = new SimpleDateFormat(dateFormat);
            this.end = format.format(format.parse(end)); // 尝试转换一下日期, 如果没问题就说明日期格式是正确的
        } catch (Exception e) {
            throw new SCPRuntimeException(Utils.getExceptionMessage(e));
        }
    }

    public String getOrganizer() {
        return organizer;
    }

    public void setOrganizer(String organizer) {
        this.organizer = organizer;
    }

    public String getCategories() {
        return categories;
    }

    public void setCategories(String categories) {
        this.categories = categories;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }
}
