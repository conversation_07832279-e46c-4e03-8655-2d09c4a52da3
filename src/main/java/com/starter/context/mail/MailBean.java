package com.starter.context.mail;

import com.fasterxml.jackson.annotation.JsonIgnore;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class MailBean {
    private String from = "scp.dss";
    private String behalfOf = "";
    private String to = "";
    private String subject = "";
    private String body = "";
    private String cc = "";
    private String bcc = "";
    private MailCalendar calendar;
    private List<String> attachmentBytes = new ArrayList<>();
    private List<String> attachmentName = new ArrayList<>();
    private String token = "d1095eae-0bae-4adb-842d-7b7e12283a15";
    @JsonIgnore
    private boolean defaultStyle = false;

    public String getTo() {
        return to;
    }

    public void setTo(String to) {
        this.to = to;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getBody() {
        if (this.isDefaultStyle()) {
            return this.renderMailBody(body);
        }
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public void setCc(String cc) {
        this.cc = cc;
    }

    public String getCc() {
        return cc;
    }

    public String getBcc() {
        return bcc;
    }

    public void setBcc(String bcc) {
        this.bcc = bcc;
    }

    public String getToken() {
        return token;
    }

    public String getForm() {
        return this.from;
    }

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    public List<String> getAttachmentBytes() {
        return attachmentBytes;
    }

    public void setAttachmentBytes(List<String> attachmentBytes) {
        this.attachmentBytes = attachmentBytes;
    }

    public void addAttachmentBytes(String attachmentBytes) {
        this.attachmentBytes.add(attachmentBytes);
    }

    public List<String> getAttachmentName() {
        return attachmentName;
    }

    public void setAttachmentName(List<String> attachmentName) {
        this.attachmentName = attachmentName;
    }

    public void addAttachmentName(String attachmentName) {
        this.attachmentName.add(attachmentName);
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getBehalfOf() {
        return behalfOf;
    }

    public void setBehalfOf(String behalfOf) {
        this.behalfOf = behalfOf;
    }

    public boolean isDefaultStyle() {
        return defaultStyle;
    }

    public void setDefaultStyle(boolean defaultStyle) {
        this.defaultStyle = defaultStyle;
    }

    public MailCalendar getCalendar() {
        return calendar;
    }

    public void setCalendar(MailCalendar calendar) {
        this.calendar = calendar;
    }

    // default style

    public String renderMailBody(String body) {
        body = StringUtils.replaceIgnoreCase(body, "<table>", this.tableStyle());
        body = StringUtils.replaceIgnoreCase(body, "<th>", this.thStyle());
        body = StringUtils.replaceIgnoreCase(body, "<td>", this.tdStyle());
        body += this.getDefaultStyle();
        return body;
    }


    private String getDefaultStyle() {
        String MAIL_STYLE = "";
        MAIL_STYLE += "<style>";
        MAIL_STYLE += "p{font-size: 9.5pt;font-family:Calibri,DengXian;padding:0;margin:0} ";
        MAIL_STYLE += "span{font-size: 9.5pt;font-family:Calibri,DengXian;} ";
        MAIL_STYLE += "div{font-size: 9.5pt;font-family:Calibri,DengXian;} ";
        MAIL_STYLE += "pre{font-size: 9.5pt;font-family:Calibri,DengXian;} ";
        MAIL_STYLE += "</style>";
        return MAIL_STYLE;
    }

    private String tableStyle() {
        return "<table style=\"border-collapse: collapse;border: 1px solid #ccc\">";
    }

    private String thStyle() {
        return "<th style=\"font-size: 9.5pt;font-family:Calibri,DengXian;background-color: rgb(204, 204, 204);border: 1px solid #fff;padding:3pt;\">";
    }

    private String tdStyle() {
        return "<td style=\"font-size: 9.5pt;font-family:Calibri,DengXian;border: 1px solid #ccc;padding:3pt;\">";
    }
}
