package com.starter.context.mail;

import com.starter.context.bean.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

//@FeignClient(value = "dts-job-prod")
@FeignClient(name = "dts-job-prod", url = "${mail.url}")
public interface MailFeignClient {

    /**
     * 同步发送
     *
     * @param bean mailbean
     * @return result
     */
    @PostMapping(value = "/api/mail/send")
    public Response send(MailBean bean);

    /**
     * 异步发送
     *
     * @param bean mailbean
     */
    @PostMapping(value = "/api/mail/async_send")
    public void sendAsync(MailBean bean);
}
