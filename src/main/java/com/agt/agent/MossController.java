package com.agt.agent;

import com.agt.agent.service.IMossService;
import com.agt.agent.service.impl.MossServiceImpl;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin
@SchneiderRequestMapping(value = "/intelligent_agent/moss", parent = MossServiceImpl.PARENT_CODE)
@Scope("prototype")
public class MossController extends ControllerHelper {

    @Resource
    private IMossService mossService;

    @SchneiderRequestMapping(value = "/search")
    public Response search(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return mossService.search(parameterMap, session);
    }

    @SchneiderRequestMapping(value = "/create_new_conversation")
    public Response createNewConversation(HttpServletRequest request) {
        super.pageLoad(request);
        return mossService.createNewConversation(parameterMap, session);
    }

    @SchneiderRequestMapping(value = "/search_agent_id")
    public Response searchAgentId(HttpServletRequest request) {
        super.pageLoad(request);
        return mossService.searchAgentId(parameterMap, session);
    }

    @SchneiderRequestMapping(value = "/query_data_by_sql")
    public Response queryDataBySql(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return mossService.queryDataBySql(parameterMap);
    }

    @SchneiderRequestMapping(value = "/download_data_by_sql")
    public void downloadDataBySql(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        mossService.downloadDataBySql(parameterMap, response);
    }

    @SchneiderRequestMapping(value = "/init_chart")
    public Response initChart(HttpServletRequest request) {
        super.pageLoad(request);
        return mossService.initChart(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_chart_by_sql")
    public Response queryChartBySql(HttpServletRequest request) {
        super.pageLoad(request);
        return mossService.queryChartBySql(parameterMap);
    }

    @SchneiderRequestMapping(value = "/confirm_table")
    public Response confirmTable(HttpServletRequest request) {
        super.pageLoad(request);
        return mossService.confirmTable(parameterMap, session);
    }
}
