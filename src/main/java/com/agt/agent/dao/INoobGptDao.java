package com.agt.agent.dao;

import com.scp.toolbox.bean.TreeData;
import org.apache.ibatis.annotations.Mapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface INoobGptDao {

    List<Map<String, String>> queryAllScenarioQueryRegAndID();

    Map<String, String> queryScenarioQueryByID(String id);

    List<LinkedHashMap<String, Object>> executeQuery(String sql);

    List<TreeData> queryScenarioList(Map<String, Object> parameterMap);

    void saveScphApiToken(String userid, String token);

    void saveSoUrgingLogs(Map<String, Object> map);
}
