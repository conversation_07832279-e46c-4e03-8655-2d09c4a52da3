package com.agt.agent.dao;

import org.apache.ibatis.annotations.Mapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IMossDao {

    List<LinkedHashMap<String, Object>> querySampleData(String sql);

    int queryDataBySqlCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryDataBySql(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryChartBySql(Map<String, Object> parameterMap);
}
