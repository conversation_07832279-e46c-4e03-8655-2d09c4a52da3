<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.agt.agent.dao.IMossDao">

	<select id="querySampleData" resultType="java.util.LinkedHashMap">
		SELECT * FROM (${sql}) PAGE OFFSET 0 ROWS FETCH NEXT 3 ROWS ONLY
	</select>

	<sql id="queryDataBySqlSql">
		${sql}
	</sql>

	<select id="queryDataBySqlCount" parameterType="java.util.Map" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="queryDataBySqlSql"/>
        <include refid="global.count_footer"/>
	</select>

	<select id="queryDataBySql" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
        <include refid="queryDataBySqlSql"/>
        <include refid="global.select_footer"/>
	</select>

	<select id="queryChartBySql" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT "${xAxis}" AS "xAxis",
		<foreach collection="yAxis" item="y" separator="," index="i">
			SUM("${y}") AS "yAxis${i}"
		</foreach>
		FROM (
			${sql}
		) GROUP BY "${xAxis}"
		ORDER BY "xAxis"
		OFFSET 0 ROWS FETCH NEXT 256 ROWS ONLY
	</select>
</mapper>
