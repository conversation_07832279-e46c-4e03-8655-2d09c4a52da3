package com.agt.agent.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONValidator;
import com.agt.agent.bean.NoobGptLog;
import com.agt.agent.bean.NoobGptResponse;
import com.scp.toolbox.bean.QueryUrgentOrderParam;
import com.scp.toolbox.bean.QueryUrgentOrderResponse;
import com.agt.agent.dao.INoobGptDao;
import com.scp.toolbox.feign.JasonOrderOprFeignClient;
import com.agt.agent.handler.NoobGPTHandler;
import com.agt.agent.handler.NoobGptType;
import com.agt.agent.service.INoobGptService;
import com.starter.context.bean.Response;
import com.starter.context.configuration.MqttConfiguration;
import com.starter.context.configuration.database.DatabaseType;
import com.starter.context.configuration.database.TargetDataSource;
import com.starter.context.servlet.UserContextHolder;
import com.starter.utils.HttpSender;
import com.starter.utils.MarkdownUtil;
import com.starter.utils.Utils;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Scope("prototype")
@Transactional
public class NoobGptServiceImpl implements INoobGptService {

    public static final String PARENT_CODE = "menuABC";

    @Resource
    private INoobGptDao noobGptDao;

    @Resource
    private Response response;

    @Resource
    private JasonOrderOprFeignClient jasonOrderOprFeignClient;

    private final static SimpleDateFormat DEFAULT_DATE_FORMAT = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");

    @Override
    public Response search(Map<String, Object> parameterMap, String userid) {
        String model = (String) parameterMap.getOrDefault("model", "DEFAULT");
        String question = (String) parameterMap.get("question");
        if (model.equals("狄施施")) {
            return response.setBody(this.handleSblvQuery(question, userid));
        }
        if (question == null || question.isEmpty()) {
            sendLog(userid, "无法处理空白提问");
            return response.setBody(new NoobGptResponse("请正确提问~"));
        }
        String freeChatPrefix = "#闲聊@";
        if (question.startsWith(freeChatPrefix)) {
            question = StringUtils.removeStart(question, freeChatPrefix);
            return response.setBody(new NoobGptResponse(NoobGPTHandler.execute(NoobGptType.SE_OFFICIAL, question)));
        }

        sendLog(userid, "得到用户输入: " + question);
        return response.setBody(new NoobGptResponse(this.handleScenarioQuery(question, userid, true)));
    }

    @Override
    public Response queryScenarioList(Map<String, Object> parameterMap) {
        return response.setBody(Utils.parseTreeNodes(noobGptDao.queryScenarioList(parameterMap)));
    }

    @TargetDataSource(DatabaseType.SCP02_READONLY)
    public String queryResultAsMarkdown(String sql) {
        try {
            String regex = "```sql\\s*(.*?)```";
            Pattern pattern = Pattern.compile(regex, Pattern.DOTALL);
            Matcher matcher = pattern.matcher(sql);
            if (matcher.find()) {
                sql = matcher.group(1).trim();
            }
            sql = StringUtils.removeStart(sql, "```sql");
            sql = StringUtils.removeEnd(sql, "```");
            sql = sql.trim();
            sql = StringUtils.removeEnd(sql, ";");
            return MarkdownUtil.generateMarkdownTable(noobGptDao.executeQuery(sql));
        } catch (Exception e) {
            return "\n\n出错啦\n\n``` sql \n" + sql + "\n\n====> " + e.getCause().getMessage() + "\n\n```";
        }
    }

    // 参考文档, https://aivip.cn.se.com/tourist/aiAgent?uuid=model-official-016
    private NoobGptResponse handleSblvQuery(String question, String userid) {
        try {
            String result = NoobGPTHandler.execute(NoobGptType.SO_IDENTIFY, question);
            JSONObject jsonObject = JSONObject.parseObject(result);

            int code = jsonObject.getIntValue("code");
            if (code == 0) {
                JSONObject body = jsonObject.getJSONObject("body").getJSONObject("dss_result");
                // 如果AI回复了, 那么再调用Jason的数据接口, 否则继续问
                boolean flag = body.getBoolean("flag");
                // flag为true时，表示可以结束多轮对话了
                if (flag == true) {
                    String type = body.getString("type");

                    String token = UUID.randomUUID().toString();
                    noobGptDao.saveScphApiToken(userid, token);
                    int lines = 0;
                    if ("query".equals(type)) {
                        QueryUrgentOrderParam params = new QueryUrgentOrderParam();
                        params.setToken(token);

                        JSONArray orders = body.getJSONArray("so_info");

                        for (JSONObject order : orders.toJavaList(JSONObject.class)) {
                            String so = order.getString("so");
                            JSONArray items = order.getJSONArray("item");
                            if (items == null || items.isEmpty()) {
                                return new NoobGptResponse("item信息为空, 任务终止");
                            }
                            for (String item : items.toJavaList(String.class)) {
                                params.addOrder(so, item);
                                lines++;
                            }
                        }

                        String message;
                        try {
                            List<QueryUrgentOrderResponse> queryResult = jasonOrderOprFeignClient.queryUrgentOrder(params);
                            message = StringUtils.join(queryResult.stream().map(QueryUrgentOrderResponse::getResult).toList(), "\r\n");
                        } catch (Exception e) {
                            return new NoobGptResponse("SC1数据接口调用失败, 请联系管理员!");
                        }

                        // 保存日志
                        Map<String, Object> map = new HashMap<>();
                        map.put("id", Utils.randomStr(8));
                        map.put("type", "query");
                        JSONArray questions = JSONArray.parseArray(question);
                        JSONObject object = new JSONObject();
                        object.put("role", "assistant");
                        object.put("timestamp", new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date()));
                        object.put("content", message);
                        questions.add(object);
                        map.put("logs", questions.toJSONString());
                        map.put("userid", userid);
                        map.put("lines", lines);
                        noobGptDao.saveSoUrgingLogs(map);

                        return new NoobGptResponse(message);
                    } else {
                        return new NoobGptResponse(body.getString("result"));
                    }
                } else {
                    return new NoobGptResponse(body.getString("result"), 300);
                }
            } else {
                return new NoobGptResponse("AI模型未按要求反馈结果, 请联系管理员!");
            }
        } catch (Exception e) {
            return new NoobGptResponse("AI模型调用失败, 请联系管理员!");
        }
    }

    private String handleScenarioQuery(String question, String userid, boolean retry) {
        try {
            List<Map<String, String>> scenarioQueries = noobGptDao.queryAllScenarioQueryRegAndID();
            // 生成问题
            List<String> asks = new ArrayList<>();
            asks.add("下面双引号内的问题");
            asks.add("\n");
            asks.add("\"" + question + "\"");
            asks.add("\n");
            asks.add("与下面哪句话的意思比较匹配:");
            asks.add("\n");

            for (Map<String, String> query : scenarioQueries) {
                asks.add(query.get("ID") + " = " + query.get("PREPARED_CONTENT"));
            }
            asks.add("-1 = 以上都不是");
            asks.add("\n");
            asks.add("上述内容是以Key=Value方式配置的, 请仅回答Key的内容, 不要回答Key以外的任何信息");

            String ask = StringUtils.join(asks, "\n");
            sendLog(userid, "提问GPT: " + ask);
            Map<String, String> map = this.queryScenarioQueryByAsk(userid, ask, 2);

            if (map == null) {
                sendLog(userid, "无法从GPT的回答中获取有效信息, 开启闲聊模式");
                return NoobGPTHandler.execute(NoobGptType.SE_OFFICIAL, question);
            } else {
                sendLog(userid, "开始执行API");
            }

            // 如果查询到对应的API, 则拼接API文档
            String apiUrl = map.get("API_URL");
            String apiDesc = map.get("API_DESCRIPTION");
            Calendar today = Calendar.getInstance();
            Calendar yesterday = Calendar.getInstance();
            yesterday.add(Calendar.DAY_OF_MONTH, -1);

            SimpleDateFormat format = new SimpleDateFormat("yyyy/MM/dd");
            apiDesc = StringUtils.replace(apiDesc, "${today}", format.format(today.getTime()));
            apiDesc = StringUtils.replace(apiDesc, "${yesterday}", format.format(yesterday.getTime()));

            // 拼接提示词
            List<String> hints = new ArrayList<>();
            hints.add("我需要调用一个数据接口, 数据接口的参数定义规则如下:");
            hints.add("\n");
            hints.add(apiDesc);
            hints.add("\n");
            hints.add("那么当提问");
            hints.add(" \"" + question + "\" 时");
            hints.add("这个参数应该如何改写, 请仅输出参数, 不要为结果添加解释, 备注和思考过程");

            sendLog(userid, "提问GPT: " + StringUtils.join(hints, "\n"));
            String param = NoobGPTHandler.execute(NoobGptType.SE_OFFICIAL, StringUtils.join(hints, "\n"));
            sendLog(userid, "GPT回复: " + param);
            param = param.split("```\n")[0];
            param = param.substring(StringUtils.indexOf(param, "{"), StringUtils.lastIndexOf(param, "}") + 1);

            String[] ss = param.split("\n");
            List<String> newParam = new ArrayList<>();
            for (String s : ss) {
                newParam.add(s.split("//")[0]);
            }

            sendLog(userid, "清理后的参数: " + StringUtils.join(newParam, "\n"));
            try (JSONValidator validator = JSONValidator.from(param)) {
                if (validator.validate() == false) {
                    if (retry) {
                        sendLog(userid, "参数不是json格式, 重试流程");
                        return this.handleScenarioQuery(question, userid, false);
                    } else {
                        sendLog(userid, "参数不是json格式, 无法继续流程: " + param);
                        return NoobGPTHandler.execute(NoobGptType.SE_OFFICIAL, question);
                    }
                }
            }

            String api = "http://localhost:8081" + apiUrl;

            Map<String, String> requestHeader = new HashMap<>();
            requestHeader.put("token", UserContextHolder.getTokenHolder());
            requestHeader.put("Content-Type", "application/json;charset=UTF-8");

            sendLog(userid, "发送webApi请求: " + api + ", " + param + ", " + JSONObject.toJSONString(requestHeader));

            String webApiRes = new HttpSender().sendRequest(api, param, HttpSender.RequestMethod.POST, requestHeader);
            sendLog(userid, "WebApi回复: " + webApiRes);
            String webApiBody = JSONObject.parseObject(webApiRes).getString("body");
            if (webApiBody == null) {
                sendLog(userid, "未获取到可用信息, 开启闲聊");
                return NoobGPTHandler.execute(NoobGptType.SE_OFFICIAL, question);
            }
            sendLog(userid, "提取到可用信息: " + webApiBody);
            return webApiBody;
        } catch (Exception e) {
            return e.getMessage();
        }
    }

    private Map<String, String> queryScenarioQueryByAsk(String userid, String ask, int retry) {
        String res = NoobGPTHandler.execute(NoobGptType.SE_OFFICIAL, ask);
        sendLog(userid, "GPT回复: " + res);
        res = StringUtils.remove(res, "=");
        res = StringUtils.remove(res, "\r");
        res = StringUtils.remove(res, "`");
        res = StringUtils.remove(res, "答案是: ");
        String[] rs = StringUtils.split(res, "\n");
        res = rs[rs.length - 1];
        res = StringUtils.trim(res);

        if (StringUtils.equalsIgnoreCase(res, "-1")) {
            sendLog(userid, "GPT回复: -1");
            return null;
        }
        sendLog(userid, "检索<" + res + ">对应的API");
        Map<String, String> map = noobGptDao.queryScenarioQueryByID(res);
        if (map == null && retry > 0) {
            sendLog(userid, "无法找到<" + res + ">对应的API, 再次尝试提问, retry=" + retry);
            return this.queryScenarioQueryByAsk(userid, ask, retry - 1);
        } else {
            if (map == null) {
                sendLog(userid, "无法找到<" + res + ">对应的API, 重试次数已用尽");
            }
            return map;
        }
    }

    public static void sendLog(String userid, String message) {
        NoobGptLog log = new NoobGptLog();
        log.setTime(DEFAULT_DATE_FORMAT.format(new Date()));
        log.setMessage(message);
        MqttConfiguration.publishMessage("scp/dss/ui/noob-gpt/" + StringUtils.lowerCase(userid), log.toJSONString());
    }
}
