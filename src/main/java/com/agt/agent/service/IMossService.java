package com.agt.agent.service;

import com.starter.context.bean.Response;
import com.starter.login.bean.Session;
import jakarta.servlet.http.HttpServletResponse;

import java.util.Map;

public interface IMossService {

    Response search(Map<String, Object> parameterMap, Session session);

    Response createNewConversation(Map<String, Object> parameterMap, Session session);

    Response queryDataBySql(Map<String, Object> parameterMap);

    void downloadDataBySql(Map<String, Object> parameterMap, HttpServletResponse response);

    Response initChart(Map<String, Object> parameterMap);

    Response queryChartBySql(Map<String, Object> parameterMap);

    Response searchAgentId(Map<String, Object> parameterMap, Session session);

    Response confirmTable(Map<String, Object> parameterMap, Session session);
}
