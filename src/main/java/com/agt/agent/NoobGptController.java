package com.agt.agent;

import com.agt.agent.service.INoobGptService;
import com.scp.toolbox.service.impl.QuickLookServiceImpl;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin
@SchneiderRequestMapping(value = "/intelligent_agent/noob_gpt", parent = QuickLookServiceImpl.PARENT_CODE)
@Scope("prototype")
public class NoobGptController extends ControllerHelper {

    @Resource
    private INoobGptService noobGptService;

    @SchneiderRequestMapping(value = "/search")
    public Response search(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return noobGptService.search(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping(value = "/query_scenario_list")
    public Response queryScenarioList(HttpServletRequest request) {
        this.pageLoad(request);
        return noobGptService.queryScenarioList(parameterMap);
    }
}
