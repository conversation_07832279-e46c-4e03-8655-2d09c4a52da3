package com.agt.agent.handler;

import com.scp.toolbox.bean.SEGPTOfficialParam;
import com.scp.toolbox.bean.SoIdentifyParam;
import com.scp.toolbox.feign.SEGPTOfficialFeignClient;
import com.scp.toolbox.feign.SoIdentifyFeignClient;
import com.starter.context.SpringContext;

public class Noob<PERSON>THandler {

    public static String execute(NoobGptType noobGptType, String prompt) {
        return switch (noobGptType) {
            case SE_OFFICIAL -> SpringContext.getBean(SEGPTOfficialFeignClient.class).execute(new SEGPTOfficialParam(prompt)).getJSONArray("choices").getJSONObject(0).getJSONObject("message").getString("content");
            case SO_IDENTIFY -> SpringContext.getBean(SoIdentifyFeignClient.class).execute(new SoIdentifyParam(prompt));
        };
    }
}
