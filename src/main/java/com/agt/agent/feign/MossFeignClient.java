package com.agt.agent.feign;

import com.scp.toolbox.bean.AgentChatParam;
import com.starter.context.configuration.FeignClientIgnoreHttpsConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(name = "mossFeignClient", url = MossFeignClient.MOSS_FEIGN_CLIENT, configuration = FeignClientIgnoreHttpsConfig.class)
public interface MossFeignClient {
    // String MOSS_FEIGN_CLIENT = "http://127.0.0.1:8087";
    String MOSS_FEIGN_CLIENT = "http://10.177.21.9:8087";

    @PostMapping(value = "/agent/query")
    String agentQuery(AgentChatParam params);

    @PostMapping(value = "/agent/select")
    String agentSelect(AgentChatParam params);

}
