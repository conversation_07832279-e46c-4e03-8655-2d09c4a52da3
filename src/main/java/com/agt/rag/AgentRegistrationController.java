package com.agt.rag;

import com.agt.rag.service.IAgentRegistrationService;
import com.agt.rag.service.impl.AgentRegistrationServiceImpl;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/rag/agent_registration", parent = AgentRegistrationServiceImpl.PARENT_CODE)
public class AgentRegistrationController extends ControllerHelper {

    @Resource
    private IAgentRegistrationService agentRegistrationService;

    @SchneiderRequestMapping(value = "/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        return agentRegistrationService.initPage(parameterMap);
    }

    @SchneiderRequestMapping(value = "/save_agent")
    public Response saveAgent(HttpServletRequest request) {
        super.pageLoad(request);
        return agentRegistrationService.saveAgent(parameterMap);
    }

    @SchneiderRequestMapping(value = "/modify_agent")
    public Response modifyAgent(HttpServletRequest request) {
        super.pageLoad(request);
        return agentRegistrationService.modifyAgent(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_agent_list")
    public Response queryAgentList(HttpServletRequest request) {
        super.pageLoad(request);
        return agentRegistrationService.queryAgentList(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_agent_with_id")
    public Response queryAgentWithId(HttpServletRequest request) {
        super.pageLoad(request);
        return agentRegistrationService.queryAgentWithId(parameterMap);
    }

    @SchneiderRequestMapping(value = "/delete_agent")
    public Response deleteAgent(HttpServletRequest request) {
        super.pageLoad(request);
        return agentRegistrationService.deleteAgent(parameterMap);
    }
}
