package com.agt.rag;

import com.agt.rag.service.IPromptTemplateService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/rag/prompt_template", parent = "menuE01")
public class PromptTemplateController extends ControllerHelper {

    @Resource
    private IPromptTemplateService promptTemplateService;

    @SchneiderRequestMapping(value = "/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        return promptTemplateService.initPage(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_prompt_list")
    public Response queryPromptList(HttpServletRequest request) {
        super.pageLoad(request);
        return promptTemplateService.queryPromptList(parameterMap);
    }

    @SchneiderRequestMapping(value = "/save_new_prompt")
    public Response saveNewPrompt(HttpServletRequest request) {
        super.pageLoad(request);
        return promptTemplateService.saveNewPrompt(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_prompt")
    public Response queryPrompt(HttpServletRequest request) {
        super.pageLoad(request);
        return promptTemplateService.queryPrompt(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_prompt_with_id")
    public Response queryPromptWithId(HttpServletRequest request) {
        super.pageLoad(request);
        return promptTemplateService.queryPromptWithId(parameterMap);
    }

    @SchneiderRequestMapping(value = "/modify_prompt")
    public Response modifyPrompt(HttpServletRequest request) {
        super.pageLoad(request);
        return promptTemplateService.modifyPrompt(parameterMap);
    }

    @SchneiderRequestMapping(value = "/delete_prompt")
    public Response deletePrompt(HttpServletRequest request) {
        super.pageLoad(request);
        return promptTemplateService.deletePrompt(parameterMap);
    }
}
