package com.agt.rag;

import com.agt.rag.service.IMossQuickLookService;
import com.agt.rag.service.impl.MossQuickLookServiceImpl;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin
@SchneiderRequestMapping(value = "/agent/rag/moss_quick_look", parent = MossQuickLookServiceImpl.PARENT_CODE)
@Scope("prototype")
public class MossQuickLookController extends ControllerHelper {

    @Resource
    private IMossQuickLookService mossQuickLookService;

    @SchneiderRequestMapping(value = "/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return mossQuickLookService.initPage(session.getUserid());
    }

    @SchneiderRequestMapping(value = "/query_admin")
    public Response queryAdmin(HttpServletRequest request) {
        super.pageLoad(request);
        return mossQuickLookService.queryAdmin(session.getUserid());
    }

    @SchneiderRequestMapping(value = "/query_comments_template")
    public Response queryCommentsTemplate(HttpServletRequest request) {
        super.pageLoad(request);
        return mossQuickLookService.queryCommentsTemplate();
    }

    @SchneiderRequestMapping(value = "/query_tab_cols")
    public Response queryTabCols(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return mossQuickLookService.queryTabCols(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_column_comments")
    public Response queryColumnComments(HttpServletRequest request) {
        super.pageLoad(request);
        return mossQuickLookService.queryColumnComments(parameterMap);
    }

    @SchneiderRequestMapping(value = "/save_comments")
    public Response saveComments(HttpServletRequest request) {
        super.pageLoad(request);
        return mossQuickLookService.saveComments(parameterMap);
    }

    @SchneiderRequestMapping(value = "/search")
    public Response search(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return mossQuickLookService.search(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_hist_comments")
    public Response queryHistComments(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return mossQuickLookService.queryHistComments(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_content")
    public Response queryContent(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return mossQuickLookService.queryContent(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_keywords")
    public Response queryKeywords(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return mossQuickLookService.queryKeywords(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_comments")
    public Response queryComments(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return mossQuickLookService.queryComments(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping(value = "/send_comment")
    public Response sendComments(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return mossQuickLookService.sendComments(parameterMap);
    }

    @SchneiderRequestMapping(value = "/delete_comment")
    public Response deleteComments(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return mossQuickLookService.deleteComment(parameterMap);
    }

    @SchneiderRequestMapping(value = "/send_reply")
    public Response sendReply(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return mossQuickLookService.sendReply(parameterMap);
    }

    @SchneiderRequestMapping(value = "/delete_reply")
    public Response deleteReply(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return mossQuickLookService.deleteReply(parameterMap);
    }
}
