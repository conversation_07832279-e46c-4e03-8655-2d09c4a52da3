package com.agt.rag.dao;

import com.scp.toolbox.bean.TreeData;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface IAgentRegistrationDao {

    List<String> queryExistsGroup();

    void saveAgent(Map<String, Object> parameterMap);

    List<TreeData> queryAgentList();

    void modifyAgent(Map<String, Object> parameterMap);

    void deleteAgent(Map<String, Object> parameterMap);

    Map<String, String> queryAgentWithId(Map<String, Object> parameterMap);

    String queryApiIntroduction();
}
