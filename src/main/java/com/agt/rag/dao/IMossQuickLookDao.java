package com.agt.rag.dao;

import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface IMossQuickLookDao {

    List<Map<String, String>> queryAllAvalibleObjects();

    int queryAdminAuth(String userid, String menuCode);

    List<Map<String, String>> queryTabCols(Map<String, Object> parameterMap);

    Map<String, Object> queryColumnComments(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryComments(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReplies(List<Map<String, Object>> comments);

    void saveComments(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryTablesByColumnName(String columnName);

    List<Map<String, Object>> queryCommentsByColumnName(String name);

    Map<String, Object> queryTableInfoByTableName(String tableName);

    List<Map<String, Object>> queryCommentsByTableName(String tableName);

    List<Map<String, Object>> queryColumnsByTableName(String tableName);

    String getDDLByObjectName(String objectName, String type);

    List<String> getIndexByTableName(String tableName);

    List<Map<String, Object>> queryTablesByDependName(String name);

    Map<String, Object> queryMViewInfoByMViewName(String mviewName);

    List<Map<String, Object>> queryTablesByRefName(String name);

    String getQueryByMViewName(String mviewName);

    void deleteComments(Map<String, Object> parameterMap);

    void saveHistComments(Map<String, Object> parameterMap);

    String getQueryByViewName(String objectName);

    List<Map<String, Object>> queryHistComments(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryKeywords(List<String> keywords);

    List<Map<String, Object>> queryDocByIndex(String name);

    List<Map<String, Object>> queryCommentByIndex(String name);

    Object queryContentById(Map<String, Object> parameterMap);

    void sendComment(Map<String, Object> parameterMap);

    void deleteComment(Map<String, Object> parameterMap);

    void sendReply(Map<String, Object> parameterMap);

    void deleteReply(Map<String, Object> parameterMap);

    Object queryCommentsTemplate();
}
