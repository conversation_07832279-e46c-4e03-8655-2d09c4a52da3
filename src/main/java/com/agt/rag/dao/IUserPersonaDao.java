package com.agt.rag.dao;

import com.scp.toolbox.bean.TreeData;
import com.starter.context.bean.Response;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface IUserPersonaDao {

    List<String> queryExistsGroup();

    List<TreeData> queryPersonaList();

    void modifyPersona(Map<String, Object> parameterMap);

    void deletePersona(Map<String, Object> parameterMap);

    int queryPageAdmin(Map<String, Object> parameterMap);

    Map<String, String> queryPersonaWithId(Map<String, Object> parameterMap);

    String queryPersonaTemplate(Map<String, Object> parameterMap);
}
