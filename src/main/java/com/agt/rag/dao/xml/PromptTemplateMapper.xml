<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.agt.rag.dao.IPromptTemplateDao">

	<select id="queryExistsGroup" resultType="java.lang.String">
		SELECT DISTINCT T.GROUPS
		FROM PROMPT_TEMPLATE T
		ORDER BY T.GROUPS
	</select>

	<select id="queryPromptList" resultType="com.scp.toolbox.bean.TreeData">
		SELECT T.PROMPT_ID AS KEY,
			   T.SUBJECT AS LABEL,
			   T.GROUPS
		FROM PROMPT_TEMPLATE T
		ORDER BY T.GROUPS, T.SUBJECT
	</select>

	<insert id="saveNewPrompt">
		INSERT INTO PROMPT_TEMPLATE (PROMPT_ID, SUBJECT, GROUPS, CONTENT, CREATE_BY$, CREATE_DATE$)
		VALUES
		(#{promptid, jdbcType=VARCHAR}, #{subject, jdbcType=VARCHAR}, #{groups, jdbcType=VARCHAR}, #{content, jdbcType=VARCHAR},
		#{session.userid, jdbcType=VARCHAR}, sysdate)
	</insert>

	<select id="queryPrompt" resultType="java.util.HashMap">
		SELECT T.PROMPT_ID,
		       T.CONTENT,
		       T.SUBJECT,
		       T.GROUPS,
		       UMD.USER_NAME AS CREATE_BY,
		       TO_CHAR(T.CREATE_DATE$, 'YYYY/MM/DD HH24:MI:SS') CREATE_DATE,
		       UMD2.USER_NAME AS UPDATE_BY,
		       TO_CHAR(T.UPDATE_DATE$, 'YYYY/MM/DD HH24:MI:SS') UPDATE_DATE
		FROM PROMPT_TEMPLATE T
			 LEFT JOIN SY_USER_MASTER_DATA UMD ON T.CREATE_BY$ = UMD.SESA_CODE
			 LEFT JOIN SY_USER_MASTER_DATA UMD2 ON T.UPDATE_BY$ = UMD2.SESA_CODE
		WHERE T.PROMPT_ID = #{promptid, jdbcType=VARCHAR}
	</select>

	<update id="modifyPrompt">
		BEGIN
			INSERT INTO PROMPT_TEMPLATE_HIST
			(PROMPT_ID, SUBJECT, GROUPS, CONTENT, CREATE_BY$, CREATE_DATE$, UPDATE_BY$, UPDATE_DATE$, DATE$, OPERATOR$)
			SELECT PROMPT_ID, SUBJECT, GROUPS, CONTENT, CREATE_BY$, CREATE_DATE$, UPDATE_BY$, UPDATE_DATE$, SYSDATE, #{session.userid, jdbcType=VARCHAR}
			  FROM PROMPT_TEMPLATE
			 WHERE PROMPT_ID = #{PROMPT_ID, jdbcType=VARCHAR};

			UPDATE PROMPT_TEMPLATE
			   SET CONTENT = #{CONTENT, jdbcType=VARCHAR},
				   SUBJECT = #{SUBJECT, jdbcType=VARCHAR},
				   GROUPS = #{GROUPS, jdbcType=VARCHAR},
				   UPDATE_BY$ = #{session.userid, jdbcType=VARCHAR},
				   UPDATE_DATE$ = SYSDATE
			 WHERE PROMPT_ID = #{PROMPT_ID, jdbcType=VARCHAR};
		END;
	</update>

	<delete id="deletePrompt">
		BEGIN
			INSERT INTO PROMPT_TEMPLATE_HIST
			(PROMPT_ID, SUBJECT, GROUPS, CONTENT, CREATE_BY$, CREATE_DATE$, UPDATE_BY$, UPDATE_DATE$, DATE$, OPERATOR$)
			SELECT PROMPT_ID, SUBJECT, GROUPS, CONTENT, CREATE_BY$, CREATE_DATE$, UPDATE_BY$, UPDATE_DATE$, SYSDATE, #{session.userid, jdbcType=VARCHAR}
			  FROM PROMPT_TEMPLATE
			 WHERE PROMPT_ID = #{promptid, jdbcType=VARCHAR};

			DELETE FROM PROMPT_TEMPLATE WHERE PROMPT_ID = #{promptid, jdbcType=VARCHAR};
		END;
	</delete>
</mapper>
