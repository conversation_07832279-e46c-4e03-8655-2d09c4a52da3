<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.agt.rag.dao.IMossQuickLookDao">
	<select id="queryAllAvalibleObjects" resultType="java.util.Map">
		SELECT T.TABLE_NAME AS "value", 'TABLE' AS "label" FROM ALL_TABLES T
		WHERE T.TABLE_NAME NOT IN (SELECT T.MVIEW_NAME FROM ALL_MVIEWS T)
		  AND T.TABLE_NAME NOT LIKE '%$%' AND T.TABLE_NAME NOT LIKE 'PRE_DEL_%'
          AND TABLESPACE_NAME = 'SCPM'
		UNION
		SELECT T.VIEW_NAME, 'VIEW' FROM ALL_VIEWS T WHERE T.VIEW_NAME NOT LIKE '%$%' AND T.VIEW_NAME NOT LIKE 'PRE_DEL_%'
		                                            AND  OWNER = 'SCPM'
		UNION
		SELECT T.MVIEW_NAME, 'MVIEW' FROM ALL_MVIEWS T WHERE T.MVIEW_NAME NOT LIKE '%$%' AND T.MVIEW_NAME NOT LIKE 'PRE_DEL_%'
		                                               AND OWNER = 'SCPM'
		UNION
		SELECT T.COLUMN_NAME, 'COLUMN' FROM ALL_TAB_COLS T WHERE T.TABLE_NAME NOT LIKE '%$%' AND T.TABLE_NAME NOT LIKE 'PRE_DEL_%'
		                                                    AND OWNER = 'SCPM'
	</select>

	<select id="queryAdminAuth" resultType="java.lang.Integer">
		select count(1)
		  from SY_MENU_AUTH
		 where MENU_CODE = #{menuCode, jdbcType=VARCHAR}
		   and USER_ID = #{userid, jdbcType=VARCHAR}
		   and upper(AUTH_DETAILS) = 'ADMIN'
	</select>

	<select id="queryTabCols" resultType="java.util.Map">
		SELECT T.COLUMN_NAME AS "value",
			   MAX(CASE WHEN DATA_LENGTH IS NOT NULL THEN DATA_TYPE || '(' || DATA_LENGTH || ')'
					    ELSE DATA_TYPE END)  AS "label"
		FROM ALL_TAB_COLS T
		WHERE REGEXP_LIKE(TABLE_NAME, #{tableName, jdbcType=VARCHAR})
		GROUP BY T.COLUMN_NAME
		ORDER BY CASE WHEN COLUMN_NAME LIKE '%$' THEN 'ZZZZ' || COLUMN_NAME ELSE COLUMN_NAME END
	</select>

	<select id="queryColumnComments" resultType="java.util.Map">
		SELECT T.COMMENTS, NVL(T.COMMENTS_TYPE, 'html') AS COMMENTS_TYPE
		FROM SCPM.MOSS_TAB_COL_COMMENTS T
		WHERE T.TABLE_NAME = #{tableName, jdbcType=VARCHAR}
		  AND T.COLUMN_NAME = #{columnName, jdbcType=VARCHAR}
	</select>

	<select id="saveComments">
		DECLARE
        	CLOB_CONTENT CLOB := #{comments, jdbcType=CLOB};
		BEGIN
			 MERGE INTO SCPM.MOSS_TAB_COL_COMMENTS T
			 USING (SELECT #{tableName, jdbcType=VARCHAR} TABLE_NAME, #{columnName, jdbcType=VARCHAR} COLUMN_NAME FROM DUAL) S
			    ON (T.TABLE_NAME = S.TABLE_NAME AND T.COLUMN_NAME = S.COLUMN_NAME)
			WHEN MATCHED THEN
				UPDATE SET T.COMMENTS = CLOB_CONTENT,
				           T.COMMENTS_TYPE = #{editorType, jdbcType=VARCHAR},
				           T.CHINESE_MEANING = #{chineseMeaning, jdbcType=VARCHAR},
				           T.ENGLISH_MEANING = #{englishMeaning, jdbcType=VARCHAR},
				           T.BUSINESS_MEANING = #{businessMeaning, jdbcType=VARCHAR},
				           T.VALUE_EXAMPLE = #{valueExample, jdbcType=VARCHAR},
				           T.TYPICAL_QUESTIONS = #{typicalQuestions, jdbcType=VARCHAR},
				           T.ALTERNATE_TERM = #{alternateTerm, jdbcType=VARCHAR},
				           T.UPDATE_BY$ = #{session.userid, jdbcType=VARCHAR},
				           T.UPDATE_DATE$ = SYSDATE
			WHEN NOT MATCHED THEN
				INSERT (TABLE_NAME, COLUMN_NAME, COMMENTS, CREATE_BY$, CREATE_DATE$, COMMENTS_TYPE,
				        CHINESE_MEANING, ENGLISH_MEANING, BUSINESS_MEANING, VALUE_EXAMPLE, TYPICAL_QUESTIONS, ALTERNATE_TERM)
				VALUES (S.TABLE_NAME, S.COLUMN_NAME, CLOB_CONTENT, #{session.userid, jdbcType=VARCHAR}, SYSDATE, #{editorType, jdbcType=VARCHAR},
                        #{chineseMeaning, jdbcType=VARCHAR}, #{englishMeaning, jdbcType=VARCHAR}, #{businessMeaning, jdbcType=VARCHAR}, #{valueExample, jdbcType=VARCHAR}, #{typicalQuestions, jdbcType=VARCHAR}, #{alternateTerm, jdbcType=VARCHAR}
                       );
		END;
	</select>

	<select id="deleteComments">
		DELETE FROM SCPM.MOSS_TAB_COL_COMMENTS T WHERE T.TABLE_NAME = #{tableName, jdbcType=VARCHAR} AND T.COLUMN_NAME = #{columnName, jdbcType=VARCHAR}
	</select>

	<select id="saveHistComments">
		DECLARE
			CLOB_CONTENT CLOB := #{comments, jdbcType=CLOB};
		BEGIN
			INSERT INTO SCPM.MOSS_TAB_COL_COMMENTS_HIST
			    (TABLE_NAME, COLUMN_NAME, COMMENTS, CREATE_DATE$, CREATE_BY$, COMMENTS_TYPE)
			VALUES
			    (#{tableName, jdbcType=VARCHAR}, #{columnName, jdbcType=VARCHAR}, CLOB_CONTENT, SYSDATE, #{session.userid, jdbcType=VARCHAR}, #{editorType, jdbcType=VARCHAR});
		END;
	</select>

	<select id="queryTablesByColumnName" resultType="java.util.Map">
		SELECT T.TABLE_NAME,
		       T.COLUMN_NAME,
		       T.DATA_TYPE,
		       T.DATA_LENGTH,
		       T.NULLABLE,
		       TO_CHAR(T.NUM_NULLS) NUM_NULLS,
		       TO_CHAR(T.NUM_DISTINCT) NUM_DISTINCT,
		       SCPA.FN_CONVERT_RAW_VALUE(T.LOW_VALUE, T.DATA_TYPE) LOW_VALUE,
		       SCPA.FN_CONVERT_RAW_VALUE(T.HIGH_VALUE, T.DATA_TYPE) HIGH_VALUE,
		       TO_CHAR(T.LAST_ANALYZED, 'YYYY/MM/DD HH24:MI:SS') LAST_ANALYZED
		 FROM ALL_TAB_COLS T
		WHERE T.COLUMN_NAME = #{columnName, jdbcType=VARCHAR}
		  AND T.TABLE_NAME NOT LIKE '%$%' AND T.TABLE_NAME NOT LIKE 'PRE_DEL_%'
		  AND T.OWNER = 'SCPM'
		ORDER BY T.SEGMENT_COLUMN_ID
	</select>

	<select id="queryCommentsByColumnName" resultType="java.util.Map">
		SELECT T.TABLE_NAME, T.COLUMN_NAME, T.COMMENTS, TO_CHAR(NVL(T.UPDATE_DATE$, T.CREATE_DATE$), 'YYYY/MM/DD HH24:MI:SS') CREATE_TIME, T2.USER_NAME AS CREATE_BY
		  FROM SCPM.MOSS_TAB_COL_COMMENTS T LEFT JOIN SCPA.SY_USER_MASTER_DATA T2 ON NVL(T.UPDATE_BY$, T.CREATE_BY$) = T2.SESA_CODE
		 WHERE T.COLUMN_NAME = #{columnName, jdbcType=VARCHAR} ORDER BY T.TABLE_NAME
	</select>

	<select id="queryTableInfoByTableName" resultType="java.util.Map">
		SELECT T.TABLE_NAME,
			   TO_CHAR(T.NUM_ROWS) NUM_ROWS,
			   TO_CHAR(T.LAST_ANALYZED, 'YYYY/MM/DD HH24:MI:SS') LAST_ANALYZED,
			   T.MONITORING,
			   T.LOGGING,
			   ROUND(T.BLOCKS * 8192 / 1024 / 1024, 0) AS SIZE_IN_MB,
			   T.PARTITIONED,
			   T.ROW_MOVEMENT,
			   T.INMEMORY,
			   T.COMPRESSION,
			   T.COMPRESS_FOR
		FROM ALL_TABLES T
	   WHERE T.TABLE_NAME = #{tableName, jdbcType=VARCHAR}
		AND T.TABLESPACE_NAME = 'SCPM'
	</select>

	<select id="queryCommentsByTableName" resultType="java.util.Map">
		WITH TEMP AS (SELECT TABLE_NAME, COLUMN_NAME, COMMENTS, NVL(UPDATE_BY$, CREATE_BY$) CREATE_BY, NVL(UPDATE_DATE$, CREATE_DATE$) CREATE_DATE
		                FROM SCPM.MOSS_TAB_COL_COMMENTS T
		               WHERE T.TABLE_NAME = #{tableName, jdbcType=VARCHAR} AND T.COLUMN_NAME = '[OBJECT_COMMENTS]'),
			 TEMP2 AS (SELECT TABLE_NAME, COLUMN_NAME, COMMENTS, NVL(UPDATE_BY$, CREATE_BY$) CREATE_BY, NVL(UPDATE_DATE$, CREATE_DATE$) CREATE_DATE
					   FROM SCPM.MOSS_TAB_COL_COMMENTS T
					   WHERE REGEXP_LIKE(#{tableName, jdbcType=VARCHAR}, T.TABLE_NAME)
						 AND (SELECT COUNT(1) FROM TEMP) = 0
						 AND T.COLUMN_NAME = '[OBJECT_COMMENTS]'
					   ORDER BY LENGTH(T.TABLE_NAME))
		SELECT TABLE_NAME, COLUMN_NAME, COMMENTS,TO_CHAR(T.CREATE_DATE, 'YYYY/MM/DD HH24:MI:SS') CREATE_TIME, T2.USER_NAME AS CREATE_BY
		FROM TEMP T LEFT JOIN SCPA.SY_USER_MASTER_DATA T2 ON T.CREATE_BY = T2.SESA_CODE

		UNION ALL

		SELECT TABLE_NAME, COLUMN_NAME, COMMENTS,TO_CHAR(T.CREATE_DATE, 'YYYY/MM/DD HH24:MI:SS') CREATE_TIME, USER_NAME AS CREATE_BY
		FROM TEMP2 T LEFT JOIN SCPA.SY_USER_MASTER_DATA T2 ON T.CREATE_BY = T2.SESA_CODE
		FETCH NEXT 1 ROWS ONLY
	</select>

	<select id="queryColumnsByTableName" resultType="java.util.Map">
		WITH BASE AS (SELECT T.COLUMN_NAME,
		                     T.COMMENTS,
		                     NVL(T.UPDATE_BY$, T.CREATE_BY$) CREATE_BY,
		                     NVL(T.UPDATE_DATE$, T.CREATE_DATE$) CREATE_DATE,
		                     T.TABLE_NAME,
                             T.CHINESE_MEANING,
                             T.ENGLISH_MEANING,
                             T.BUSINESS_MEANING,
                             T.VALUE_EXAMPLE,
                             T.TYPICAL_QUESTIONS,
                             T.ALTERNATE_TERM
		     FROM SCPM.MOSS_TAB_COL_COMMENTS T WHERE T.TABLE_NAME = #{tableName, jdbcType=VARCHAR})
		SELECT T.TABLE_NAME,
		       T.COLUMN_NAME,
		       T.DATA_TYPE,
		       T.DATA_LENGTH,
		       SCPA.FN_CONVERT_RAW_VALUE(T.LOW_VALUE, T.DATA_TYPE) LOW_VALUE,
		       SCPA.FN_CONVERT_RAW_VALUE(T.HIGH_VALUE, T.DATA_TYPE) HIGH_VALUE,
		       TO_CHAR(T.LAST_ANALYZED, 'YYYY/MM/DD HH24:MI:SS') LAST_ANALYZED,
               T2.COMMENTS,
               T2.TABLE_NAME AS EXP_TABLE_NAME,
		       UMD.USER_NAME AS CREATE_BY,
		       TO_CHAR(T2.CREATE_DATE, 'YYYY/MM/DD HH24:MI:SS') CREATE_TIME,
               T2.CHINESE_MEANING,
               T2.ENGLISH_MEANING,
               T2.BUSINESS_MEANING,
               T2.VALUE_EXAMPLE,
               T2.TYPICAL_QUESTIONS,
               T2.ALTERNATE_TERM
		 FROM ALL_TAB_COLS T LEFT JOIN BASE T2 ON T.COLUMN_NAME = T2.COLUMN_NAME
		                     LEFT JOIN SCPA.SY_USER_MASTER_DATA UMD ON T2.CREATE_BY = UMD.SESA_CODE
		WHERE T.TABLE_NAME = #{tableName, jdbcType=VARCHAR}
          AND T.OWNER = 'SCPM'
		ORDER BY T.SEGMENT_COLUMN_ID
	</select>

	<select id="queryHistComments" resultType="java.util.Map">
		SELECT T.TABLE_NAME, T.COLUMN_NAME, T.COMMENTS, TO_CHAR(T.CREATE_DATE$, 'YYYY/MM/DD HH24:MI:SS') CREATE_TIME ,T2.USER_NAME AS CREATE_BY
		FROM SCPM.MOSS_TAB_COL_COMMENTS_HIST T LEFT JOIN SCPA.SY_USER_MASTER_DATA T2 ON T.CREATE_BY$ = T2.SESA_CODE
		WHERE T.TABLE_NAME = #{tableName, jdbcType=VARCHAR} AND T.COLUMN_NAME = #{columnName, jdbcType=VARCHAR}
		ORDER BY CREATE_TIME DESC
	</select>

	<select id="getDDLByObjectName" resultType="java.lang.String">
		SELECT DBMS_METADATA.GET_DDL(#{type, jdbcType=VARCHAR}, #{objectName, jdbcType=VARCHAR}, 'SCPM') FROM DUAL
	</select>

	<select id="getIndexByTableName" resultType="java.lang.String">
		SELECT DBMS_METADATA.GET_DDL('INDEX', T.INDEX_NAME, 'SCPM')
		FROM ALL_INDEXES T
		WHERE T.TABLE_NAME = #{tableName, jdbcType=VARCHAR} AND TABLE_OWNER = 'SCPM'
	</select>

	<select id="queryTablesByDependName" resultType="java.util.Map">
		WITH TABS AS (SELECT TT.TABLE_NAME, MAX(NUM_ROWS) NUM_ROWS, MAX(LAST_ANALYZED) LAST_ANALYZED
              FROM (SELECT T.TABLE_NAME, T.NUM_ROWS, T.LAST_ANALYZED
                    FROM ALL_TABLES T WHERE T.TABLESPACE_NAME = 'SCPM'

                    UNION ALL

                    SELECT T.MVIEW_NAME, 0, NULL
                    FROM ALL_MVIEWS T WHERE T.OWNER = 'SCPM'

                    UNION ALL

                    SELECT T.VIEW_NAME, 0, NULL
                    FROM ALL_VIEWS T WHERE OWNER = 'SCPM') TT
              WHERE TT.TABLE_NAME NOT LIKE '%$%' AND TT.TABLE_NAME NOT LIKE 'PRE_DEL_%'
              GROUP BY TT.TABLE_NAME),
			 TEMP AS (/*取出所有表注释*/
				 SELECT T.TABLE_NAME, T.COMMENTS, NVL(T.UPDATE_BY$, T.CREATE_BY$) CREATE_BY, NVL(T.UPDATE_DATE$, T.CREATE_DATE$) CREATE_DATE
				   FROM SCPM.MOSS_TAB_COL_COMMENTS T WHERE T.COLUMN_NAME = '[OBJECT_COMMENTS]'),
			 TEMP2 AS (/*根据表名找到所有依赖该表的对象*/
				 SELECT T.TABLE_NAME,
						MAX(T.NUM_ROWS)                                        NUM_ROWS,
						TO_CHAR(MAX(T.LAST_ANALYZED), 'YYYY/MM/DD HH24:MI:SS') LAST_ANALYZED
				 FROM TABS T
						  INNER JOIN DBA_DEPENDENCIES T2 ON T.TABLE_NAME = T2.NAME AND T2.OWNER = T2.REFERENCED_OWNER AND T2.OWNER = 'SCPM'
				 WHERE T2.REFERENCED_NAME = #{tableName, jdbcType=VARCHAR}
				   AND T2.NAME != #{tableName, jdbcType=VARCHAR}
				 GROUP BY T.TABLE_NAME),
			 TEMP3 AS (/*精确匹配 + 正则匹配找到所有符合条件的COMMENTS*/
				 SELECT T2.TABLE_NAME,
						0 TABLE_NAME_LEN,
						T.COMMENTS,
						T.CREATE_BY,
						T.CREATE_DATE,
						T2.NUM_ROWS,
						T2.LAST_ANALYZED
				 FROM TEMP2 T2
						  INNER JOIN TEMP T ON T.TABLE_NAME = T2.TABLE_NAME
				 UNION ALL
				 SELECT T.TABLE_NAME,
						NVL(LENGTH(T2.TABLE_NAME),0) TABLE_NAME_LEN,
						T2.COMMENTS,
						T2.CREATE_BY,
						T2.CREATE_DATE,
						T.NUM_ROWS,
						T.LAST_ANALYZED
				 FROM TEMP2 T
						  LEFT JOIN TEMP T2 ON REGEXP_LIKE(T.TABLE_NAME, T2.TABLE_NAME)
				 WHERE T.TABLE_NAME NOT IN (SELECT TABLE_NAME FROM TEMP)),
			 TEMP4 AS (/*正则匹配的表, 将按照正则表达式长度最长的匹配(优先匹配精确项)*/
				 SELECT T.TABLE_NAME, MAX(TABLE_NAME_LEN) TABLE_NAME_LEN
				 FROM TEMP3 T
				 GROUP BY T.TABLE_NAME),
			 TEMP5 AS (/*保留正则匹配中正则表达式长度最长的一组*/
				 SELECT TEMP3.*
				 FROM TEMP3
						  INNER JOIN TEMP4 ON TEMP3.TABLE_NAME = TEMP4.TABLE_NAME AND TEMP3.TABLE_NAME_LEN = TEMP4.TABLE_NAME_LEN)
		SELECT T.TABLE_NAME,
			   TO_CHAR(T.NUM_ROWS) NUM_ROWS,
			   LAST_ANALYZED,
			   T.COMMENTS,
			   UMD.USER_NAME AS                                 CREATE_BY,
			   TO_CHAR(T.CREATE_DATE, 'YYYY/MM/DD HH24:MI:SS') CREATE_TIME
		FROM TEMP5 T
				 LEFT JOIN SCPA.SY_USER_MASTER_DATA UMD ON T.CREATE_BY = UMD.SESA_CODE
		ORDER BY T.TABLE_NAME
	</select>

	<select id="queryTablesByRefName" resultType="java.util.Map">
		WITH TABS AS (SELECT TT.TABLE_NAME, MAX(NUM_ROWS) NUM_ROWS, MAX(LAST_ANALYZED) LAST_ANALYZED
              FROM (SELECT T.TABLE_NAME, T.NUM_ROWS, T.LAST_ANALYZED
                    FROM ALL_TABLES T WHERE T.TABLESPACE_NAME = 'SCPM'

                    UNION ALL

                    SELECT T.MVIEW_NAME, 0, NULL
                    FROM ALL_MVIEWS T WHERE T.OWNER = 'SCPM'

                    UNION ALL

                    SELECT T.VIEW_NAME, 0, NULL
                    FROM ALL_VIEWS T WHERE OWNER = 'SCPM') TT
              WHERE TT.TABLE_NAME NOT LIKE '%$%' AND TT.TABLE_NAME NOT LIKE 'PRE_DEL_%'
              GROUP BY TT.TABLE_NAME),
			 TEMP AS (/*取出所有表注释*/
				 SELECT T.TABLE_NAME, T.COMMENTS, NVL(T.UPDATE_BY$, T.CREATE_BY$) CREATE_BY, NVL(T.UPDATE_DATE$, T.CREATE_DATE$) CREATE_DATE
				   FROM SCPM.MOSS_TAB_COL_COMMENTS T WHERE T.COLUMN_NAME = '[OBJECT_COMMENTS]'),
			 TEMP2 AS (/*根据表名找到该表的依赖*/
				 SELECT T.TABLE_NAME,
						MAX(T.NUM_ROWS)                                        NUM_ROWS,
						TO_CHAR(MAX(T.LAST_ANALYZED), 'YYYY/MM/DD HH24:MI:SS') LAST_ANALYZED
				 FROM TABS T
						  INNER JOIN DBA_DEPENDENCIES T2 ON T.TABLE_NAME = T2.REFERENCED_NAME AND T2.OWNER = T2.REFERENCED_OWNER AND T2.OWNER = 'SCPM'
				 WHERE T2.REFERENCED_NAME != #{tableName, jdbcType=VARCHAR}
				   AND T2.NAME = #{tableName, jdbcType=VARCHAR}
				 GROUP BY T.TABLE_NAME),
			 TEMP3 AS (/*精确匹配 + 正则匹配找到所有符合条件的COMMENTS*/
				 SELECT T2.TABLE_NAME,
						0 TABLE_NAME_LEN,
						T.COMMENTS,
						T.CREATE_BY,
						T.CREATE_DATE,
						T2.NUM_ROWS,
						T2.LAST_ANALYZED
				 FROM TEMP2 T2
						  INNER JOIN TEMP T ON T.TABLE_NAME = T2.TABLE_NAME
				 UNION ALL
				 SELECT T.TABLE_NAME,
						NVL(LENGTH(T2.TABLE_NAME),0) TABLE_NAME_LEN,
						T2.COMMENTS,
						T2.CREATE_BY,
						T2.CREATE_DATE,
						T.NUM_ROWS,
						T.LAST_ANALYZED
				 FROM TEMP2 T
						  LEFT JOIN TEMP T2 ON REGEXP_LIKE(T.TABLE_NAME, T2.TABLE_NAME)
				 WHERE T.TABLE_NAME NOT IN (SELECT TABLE_NAME FROM TEMP)),
			 TEMP4 AS (/*正则匹配的表, 将按照正则表达式长度最长的匹配(优先匹配精确项)*/
				 SELECT T.TABLE_NAME, MAX(TABLE_NAME_LEN) TABLE_NAME_LEN
				 FROM TEMP3 T
				 GROUP BY T.TABLE_NAME),
			 TEMP5 AS (/*保留正则匹配中正则表达式长度最长的一组*/
				 SELECT TEMP3.*
				 FROM TEMP3
						  INNER JOIN TEMP4 ON TEMP3.TABLE_NAME = TEMP4.TABLE_NAME AND TEMP3.TABLE_NAME_LEN = TEMP4.TABLE_NAME_LEN)
		SELECT T.TABLE_NAME,
			   TO_CHAR(T.NUM_ROWS) NUM_ROWS,
			   LAST_ANALYZED,
			   T.COMMENTS,
			   UMD.USER_NAME AS                                 CREATE_BY,
			   TO_CHAR(T.CREATE_DATE, 'YYYY/MM/DD HH24:MI:SS') CREATE_TIME
		FROM TEMP5 T
				 LEFT JOIN SCPA.SY_USER_MASTER_DATA UMD ON T.CREATE_BY = UMD.SESA_CODE
		ORDER BY T.TABLE_NAME
	</select>

	<select id="queryMViewInfoByMViewName" resultType="java.util.Map">
		SELECT T.MVIEW_NAME,
			   TO_CHAR(T2.NUM_ROWS) NUM_ROWS,
			   TO_CHAR(T.LAST_REFRESH_DATE, 'YYYY/MM/DD HH24:MI:SS')     LAST_REFRESH_TIME,
			   TO_CHAR(T.LAST_REFRESH_END_TIME, 'YYYY/MM/DD HH24:MI:SS') LAST_REFRESH_END_TIME,
			   T2.MONITORING,
			   T2.LOGGING,
			   ROUND(T2.BLOCKS * 8192 / 1024 / 1024, 0) AS               SIZE_IN_MB,
			   T2.PARTITIONED,
			   T2.INMEMORY,
			   T2.COMPRESSION,
			   T2.COMPRESS_FOR
		FROM ALL_MVIEWS T
				 LEFT JOIN ALL_TABLES T2 ON T.MVIEW_NAME = T2.TABLE_NAME
		                AND T2.TABLESPACE_NAME = 'SCPM'
		WHERE T.MVIEW_NAME = #{mviewName, jdbcType=VARCHAR}
		AND T.OWNER = 'SCPM'
	</select>

	<select id="getQueryByMViewName" resultType="java.lang.String">
		SELECT T.QUERY FROM ALL_MVIEWS T WHERE T.MVIEW_NAME = #{mviewName, jdbcType=VARCHAR} AND T.OWNER = 'SCPM'
	</select>

	<select id="getQueryByViewName">
		SELECT T.TEXT FROM ALL_VIEWS T WHERE T.VIEW_NAME = #{mviewName, jdbcType=VARCHAR} AND T.OWNER = 'SCPM'
	</select>

	<select id="queryKeywords" resultType="java.util.Map">
		SELECT "value", "label" FROM (
		<foreach collection="list" item="keywords" separator="UNION ALL" index="index">
			SELECT * FROM (SELECT NAME AS "value", LABEL AS "label", ${index} AS PRORITY
							FROM SCPM.MOSS_QUICKLOOK_INDEX_TBL_COL
							WHERE (lower(NAME) LIKE '%' || lower(#{keywords,jdbcType=VARCHAR}) || '%')
							OR (lower(INDEX_NAME) LIKE '%' || lower(#{keywords,jdbcType=VARCHAR}) || '%')
							GROUP BY NAME, LABEL
							UNION ALL
							SELECT SENTENCE AS "value",'DOCUMENT' AS "label", ${index} AS PRORITY
							FROM SCPM.MOSS_QUICKLOOK_INDEX_DOC
							WHERE (lower(WORD) LIKE '%' || lower(#{keywords,jdbcType=VARCHAR}) || '%') OR (lower(SENTENCE) LIKE '%' || lower(#{keywords,jdbcType=VARCHAR}) || '%')
							GROUP BY SENTENCE
							UNION ALL
							SELECT SENTENCE AS "value",'COMMENTS' AS "label", ${index} AS PRORITY
							FROM SCPM.MOSS_QUICKLOOK_INDEX_TBL_COL_COMMENTS
							WHERE (lower(WORD) LIKE '%' || lower(#{keywords,jdbcType=VARCHAR}) || '%') OR (lower(SENTENCE) LIKE '%' || lower(#{keywords,jdbcType=VARCHAR}) || '%')
							GROUP BY SENTENCE
					  )
		</foreach>
		) GROUP BY "value", "label"
		ORDER BY MIN(PRORITY), COUNT(1) DESC,
		CASE
			WHEN "label" = 'TABLE' THEN 1
			WHEN "label" = 'VIEW' THEN 2
			WHEN "label" = 'MVIEW' THEN 3
			WHEN "label" = 'COLUMN' THEN 4
			WHEN "label" = 'DOCUMENT' THEN 5
			ELSE 6
		END
		FETCH NEXT 100 ROWS ONLY
	</select>

	<select id="queryDocByIndex" resultType="java.util.Map">
		SELECT T1.DOC_ID, T1.GROUPS, T1.SUBJECT, T1.CONTENT
		FROM SY_DOCUMENTATION T1
				 JOIN
			 (SELECT DOC_ID
			  FROM SCPM.MOSS_QUICKLOOK_INDEX_DOC
			  WHERE SENTENCE LIKE '%' || #{name,jdbcType=VARCHAR} || '%'
			  GROUP BY DOC_ID
			  ORDER BY MAX(COUNT) DESC) T2 ON T1.DOC_ID = T2.DOC_ID
	</select>

	<select id="queryCommentByIndex" resultType="java.util.Map">
		SELECT T1.TABLE_NAME, T1.COLUMN_NAME, T1.COMMENTS
		FROM SCPM.MOSS_TAB_COL_COMMENTS T1
				 JOIN
			 (SELECT TABLE_NAME, COLUMN_NAME
			  FROM SCPM.MOSS_QUICKLOOK_INDEX_TBL_COL_COMMENTS
			  WHERE SENTENCE LIKE '%' || #{name,jdbcType=VARCHAR} || '%'
			  GROUP BY TABLE_NAME, COLUMN_NAME
			  ORDER BY MAX(COUNT) DESC) T2 ON (T1.TABLE_NAME = T2.TABLE_NAME AND T1.COLUMN_NAME = T2.COLUMN_NAME)
	</select>

	<select id="queryContentById" resultType="java.lang.Object">
		SELECT CONTENT
		FROM SY_DOCUMENTATION
		WHERE DOC_ID = #{docId, jdbcType=VARCHAR}
	</select>

	<insert id="sendComment">
		DECLARE
            PARAMS_COLB CLOB := #{content, jdbcType=CLOB};
        BEGIN
			INSERT INTO SCPM.MOSS_QUICKLOOK_COMMENTS (COMMENT_ID, USER_ID, COMMENT_TO, TYPE, CREATE_DATE$, CONTENT)
			VALUES
				(#{commentId, jdbcType=VARCHAR},  #{session.userid, jdbcType=VARCHAR}, #{commentTo, jdbcType=VARCHAR}, #{type, jdbcType=VARCHAR},
				sysdate, PARAMS_COLB);
		END;
	</insert>

	<delete id="deleteComment">
		BEGIN
			DELETE FROM SCPM.MOSS_QUICKLOOK_COMMENTS WHERE COMMENT_ID = #{commentId, jdbcType=VARCHAR};
			DELETE FROM SCPM.MOSS_QUICKLOOK_COMMENTS_REPLY WHERE COMMENT_ID = #{commentId, jdbcType=VARCHAR};
		END;
	</delete>

	<insert id="sendReply">
		DECLARE
            PARAMS_COLB CLOB := #{content, jdbcType=CLOB};
		BEGIN
			INSERT INTO SCPM.MOSS_QUICKLOOK_COMMENTS_REPLY (REPLY_ID, USER_ID, COMMENT_ID, RECIPIENT_ID, CREATE_DATE$, CONTENT) VALUES
			(#{replyId, jdbcType=VARCHAR}, #{session.userid, jdbcType=VARCHAR}, #{commentId, jdbcType=VARCHAR},  #{recipientId, jdbcType=VARCHAR},
			sysdate, PARAMS_COLB);
		END;
	</insert>

	<delete id="deleteReply">
		DELETE FROM SCPM.MOSS_QUICKLOOK_COMMENTS_REPLY WHERE REPLY_ID = #{replyId, jdbcType=VARCHAR}
	</delete>

	<select id="queryComments" resultType="java.util.Map">
		SELECT T.COMMENT_ID, T.USER_ID, T.COMMENT_TO, T.TYPE, TO_CHAR(T.CREATE_DATE$, 'YYYY/MM/DD HH24:MI:SS') CREATE_DATE, T.CONTENT, T2.USER_NAME, T2.IS_MAINTAINER
		FROM SCPM.MOSS_QUICKLOOK_COMMENTS T
		LEFT JOIN SCPA.SY_USER_MASTER_DATA T2 ON T.USER_ID = T2.SESA_CODE
		WHERE T.COMMENT_TO = #{name, jdbcType=VARCHAR} AND T.TYPE = #{type, jdbcType=VARCHAR}
		ORDER BY T.CREATE_DATE$ DESC
	</select>

	<select id="queryReplies" resultType="java.util.Map">
		SELECT T.REPLY_ID, T.USER_ID, TO_CHAR(T.CREATE_DATE$, 'YYYY/MM/DD HH24:MI:SS') CREATE_DATE, T.CONTENT, T2.USER_NAME, T2.IS_MAINTAINER, T.COMMENT_ID
		FROM SCPM.MOSS_QUICKLOOK_COMMENTS_REPLY T
		LEFT JOIN SCPA.SY_USER_MASTER_DATA T2 ON T.USER_ID = T2.SESA_CODE
		WHERE T.COMMENT_ID IN
		<foreach collection="comments" item="item" open="(" close=")" separator=",">
			#{item.COMMENT_ID, jdbcType=VARCHAR}
		</foreach>
		ORDER BY T.CREATE_DATE$ DESC
	</select>

	<select id="queryCommentsTemplate" resultType="java.lang.Object">
		SELECT T.CONTENT FROM SY_DOCUMENTATION T WHERE T.DOC_ID = 'f4490c61b119'
	</select>
</mapper>
