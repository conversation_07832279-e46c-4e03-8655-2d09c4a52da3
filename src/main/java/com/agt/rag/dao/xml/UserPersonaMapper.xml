<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.agt.rag.dao.IUserPersonaDao">

	<select id="queryExistsGroup" resultType="java.lang.String">
		SELECT DISTINCT T.ENTITY_NAME
		FROM SY_USER_MASTER_DATA T
		WHERE T.ENTITY_NAME IS NOT NULL
		ORDER BY T.ENTITY_NAME
	</select>

	<select id="queryPageAdmin" resultType="java.lang.Integer">
		SELECT COUNT(1) CNT
          FROM SY_MENU_AUTH T
         WHERE UPPER(T.AUTH_DETAILS) = 'ADMIN'
		   AND T.USER_ID = #{session.userid, jdbcType=VARCHAR} AND T.MENU_CODE = #{parentCode, jdbcType=VARCHAR}
	</select>

	<select id="queryPersonaList" resultType="com.scp.toolbox.bean.TreeData">
		SELECT 'USER_PERSONA_TEMPLATE'	AS KEY,
			   'USER_PERSONA_TEMPLATE'	AS LABEL,
			   NULL							AS SUB_LABEL,
			   NULL         				AS GROUPS
		FROM DUAL
		UNION ALL
		SELECT T.SESA_CODE                                        AS KEY,
			   T.USER_NAME                                        AS LABEL,
			   CASE WHEN T2.CONTENT IS NULL THEN 'No Persona' END AS SUB_LABEL,
			   T.ENTITY_NAME                                      AS GROUPS
		FROM SY_USER_MASTER_DATA T
				 LEFT JOIN USER_PERSONA T2 ON T.SESA_CODE = T2.USER_ID
		WHERE T.SESA_CODE != 'ADM570303'
		ORDER BY GROUPS NULLS FIRST, LABEL
	</select>

	<select id="queryPersonaWithId" resultType="java.util.HashMap">
		SELECT T.USER_ID,
			   UMD3.USER_NAME,
		       T.CONTENT,
		       UMD.USER_NAME AS CREATE_BY,
		       TO_CHAR(T.CREATE_DATE$, 'YYYY/MM/DD HH24:MI:SS') CREATE_DATE,
		       UMD2.USER_NAME AS UPDATE_BY,
		       TO_CHAR(T.UPDATE_DATE$, 'YYYY/MM/DD HH24:MI:SS') UPDATE_DATE
		FROM USER_PERSONA T
			 LEFT JOIN SY_USER_MASTER_DATA UMD ON T.CREATE_BY$ = UMD.SESA_CODE
			 LEFT JOIN SY_USER_MASTER_DATA UMD2 ON T.UPDATE_BY$ = UMD2.SESA_CODE
			 LEFT JOIN SY_USER_MASTER_DATA UMD3 ON T.USER_ID = UMD3.SESA_CODE
		WHERE T.USER_ID = #{userid, jdbcType=VARCHAR}
	</select>

	<update id="modifyPersona">
		DECLARE
        	CLOB_CONTENT CLOB := #{CONTENT, jdbcType=CLOB};
		BEGIN
			INSERT INTO USER_PERSONA_HIST
			(USER_ID, CONTENT, CREATE_BY$, CREATE_DATE$, UPDATE_BY$, UPDATE_DATE$, DATE$, OPERATOR$)
			SELECT USER_ID, CONTENT, CREATE_BY$, CREATE_DATE$, UPDATE_BY$, UPDATE_DATE$, SYSDATE, #{session.userid, jdbcType=VARCHAR}
			  FROM USER_PERSONA
			 WHERE USER_ID = #{USER_ID, jdbcType=VARCHAR};

			MERGE INTO USER_PERSONA T
			 USING (SELECT #{USER_ID, jdbcType=VARCHAR} USER_ID FROM DUAL) S
			    ON (T.USER_ID = S.USER_ID)
			WHEN MATCHED THEN
				UPDATE SET T.CONTENT = CLOB_CONTENT,
				           T.UPDATE_BY$ = #{session.userid, jdbcType=VARCHAR},
				           T.UPDATE_DATE$ = SYSDATE
			WHEN NOT MATCHED THEN
				INSERT (USER_ID, CONTENT, CREATE_BY$, CREATE_DATE$)
				VALUES (S.USER_ID, CLOB_CONTENT, #{session.userid, jdbcType=VARCHAR}, SYSDATE);
		END;
	</update>

	<delete id="deletePersona">
		BEGIN
			INSERT INTO USER_PERSONA_HIST
			(USER_ID, CONTENT, CREATE_BY$, CREATE_DATE$, UPDATE_BY$, UPDATE_DATE$, DATE$, OPERATOR$)
			SELECT USER_ID, CONTENT, CREATE_BY$, CREATE_DATE$, UPDATE_BY$, UPDATE_DATE$, SYSDATE, #{session.userid, jdbcType=VARCHAR}
			  FROM USER_PERSONA
			 WHERE USER_ID = #{USER_ID, jdbcType=VARCHAR};

			DELETE FROM USER_PERSONA T WHERE T.USER_ID = #{USER_ID, jdbcType=VARCHAR};
		END;
	</delete>

	<select id="queryPersonaTemplate" resultType="java.lang.String">
		SELECT CONTENT FROM USER_PERSONA T WHERE T.USER_ID = 'USER_PERSONA_TEMPLATE'
	</select>
</mapper>
