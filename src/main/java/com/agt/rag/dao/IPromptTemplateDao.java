package com.agt.rag.dao;

import com.scp.toolbox.bean.TreeData;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface IPromptTemplateDao {

    List<String> queryExistsGroup();

    List<TreeData> queryPromptList();

    void saveNewPrompt(Map<String, Object> parameterMap);

    Map<String, String> queryPrompt(Map<String, Object> parameterMap);

    void modifyPrompt(Map<String, Object> parameterMap);

    void deletePrompt(Map<String, Object> parameterMap);
}
