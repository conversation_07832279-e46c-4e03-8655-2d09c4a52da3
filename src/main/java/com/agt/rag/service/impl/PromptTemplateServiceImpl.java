package com.agt.rag.service.impl;

import com.agt.rag.dao.IPromptTemplateDao;
import com.agt.rag.service.IPromptTemplateService;
import com.starter.context.bean.Response;
import com.starter.utils.Utils;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Scope("prototype")
@Transactional
public class PromptTemplateServiceImpl implements IPromptTemplateService {

    @Resource
    private IPromptTemplateDao promptTemplateDao;

    @Resource
    private Response response;

    @Override
    public Response initPage(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("existsGroup", promptTemplateDao.queryExistsGroup());
        return response.setBody(resultMap);
    }

    @Override
    public Response queryPromptList(Map<String, Object> parameterMap) {
        return response.setBody(Utils.parseTreeNodes(promptTemplateDao.queryPromptList()));
    }

    @Override
    public Response saveNewPrompt(Map<String, Object> parameterMap) {
        parameterMap.put("promptid", Utils.randomStr(12));
        promptTemplateDao.saveNewPrompt(parameterMap);
        return response;
    }

    @Override
    public Response queryPrompt(Map<String, Object> parameterMap) {
        return response.setBody(promptTemplateDao.queryPrompt(parameterMap));
    }

    @Override
    public Response modifyPrompt(Map<String, Object> parameterMap) {
        promptTemplateDao.modifyPrompt(parameterMap);
        return response;
    }

    @Override
    public Response deletePrompt(Map<String, Object> parameterMap) {
        promptTemplateDao.deletePrompt(parameterMap);
        return response;
    }

    @Override
    public Response queryPromptWithId(Map<String, Object> parameterMap) {
        Map<String, String> docoment = promptTemplateDao.queryPrompt(parameterMap);
        if (docoment == null) {
            docoment = new HashMap<>();
        }
        return response.setBody(docoment);
    }
}
