package com.agt.rag.service.impl;

import com.agt.rag.dao.IMossQuickLookDao;
import com.agt.rag.service.IMossQuickLookService;
import com.starter.context.bean.CacheRemove;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.utils.MarkdownUtil;
import com.starter.utils.Utils;
import com.vladsch.flexmark.html.HtmlRenderer;
import com.vladsch.flexmark.parser.Parser;
import jakarta.annotation.Resource;
import org.ansj.domain.Term;
import org.ansj.splitWord.analysis.ToAnalysis;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Scope("prototype")
@Transactional
public class MossQuickLookServiceImpl implements IMossQuickLookService {

    public static final String PARENT_CODE = "menuE07";

    @Resource
    private IMossQuickLookDao mossQuickLookDao;

    @Resource
    private Response response;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage(String userid) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("allAvalibleObjects", mossQuickLookDao.queryAllAvalibleObjects());
        return response.setBody(resultMap);
    }

    @Override
    public Response queryAdmin(String userid) {
        return response.setBody(mossQuickLookDao.queryAdminAuth(userid, PARENT_CODE) > 0);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryTabCols(Map<String, Object> parameterMap) {
        return response.setBody(mossQuickLookDao.queryTabCols(parameterMap));
    }

    @Override
    public Response queryColumnComments(Map<String, Object> parameterMap) {
        Map<String, Object> map = mossQuickLookDao.queryColumnComments(parameterMap);
        if (map != null) {
            map.put("COMMENTS", Utils.clob2String(map.get("COMMENTS")));
        } else {
            map = new HashMap<>();
        }
        return response.setBody(map);
    }

    @Override
    public Response queryComments(Map<String, Object> parameterMap, String userid) {
        List<Map<String, Object>> commentsList = mossQuickLookDao.queryComments(parameterMap);
        if (commentsList.isEmpty() == false) {
            List<Map<String, Object>> replyList = mossQuickLookDao.queryReplies(commentsList);
            Map<String, List<Map<String, Object>>> replyMap = new HashMap<>();
            for (Map<String, Object> reply : replyList) {
                reply.put("IS_ME", StringUtils.equalsIgnoreCase(userid, (String) reply.get("USER_ID")));
                List<Map<String, Object>> list = replyMap.computeIfAbsent((String) reply.get("COMMENT_ID"), k -> new ArrayList<>());
                list.add(reply);
            }
            for (Map<String, Object> comment : commentsList) {
                String commentID = (String) comment.get("COMMENT_ID");
                comment.put("IS_ME", StringUtils.equalsIgnoreCase(userid, (String) comment.get("USER_ID")));
                List<Map<String, Object>> replies = replyMap.getOrDefault(commentID, new ArrayList<>());
                comment.put("REPLIES", replies);
                comment.put("REPLIES_CNT", replies.size());
            }
        }

        return response.setBody(commentsList);
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = {"this.search"})
    public Response saveComments(Map<String, Object> parameterMap) {
        String comments = (String) parameterMap.get("comments");
        if (StringUtils.isBlank(comments)) {
            mossQuickLookDao.deleteComments(parameterMap);
            parameterMap.put("comments", "User deleted this comment");
        } else {
            mossQuickLookDao.saveComments(parameterMap);
        }
        mossQuickLookDao.saveHistComments(parameterMap);
        return response;
    }

    @Override
    public Response sendComments(Map<String, Object> parameterMap) {
        if (StringUtils.isNotBlank((String) parameterMap.get("content"))) {
            parameterMap.put("commentId", Utils.randomStr(12));
            mossQuickLookDao.sendComment(parameterMap);
        }
        return response;
    }

    @Override
    public Response deleteComment(Map<String, Object> parameterMap) {
        mossQuickLookDao.deleteComment(parameterMap);
        return response;
    }

    @Override
    public Response sendReply(Map<String, Object> parameterMap) {
        if (StringUtils.isNotBlank((String) parameterMap.get("content"))) {
            parameterMap.put("replyId", Utils.randomStr(12));
            mossQuickLookDao.sendReply(parameterMap);
        }
        return response;
    }

    @Override
    public Response deleteReply(Map<String, Object> parameterMap) {
        mossQuickLookDao.deleteReply(parameterMap);
        return response;
    }

    private static final Map<String, String> HEADER_DISPLAY_MAP = new LinkedHashMap<>();
    static {
        HEADER_DISPLAY_MAP.put("NO.", "序号");
        HEADER_DISPLAY_MAP.put("COLUMN_NAME", "列名");
        HEADER_DISPLAY_MAP.put("DATA_TYPE", "数据类型");
        HEADER_DISPLAY_MAP.put("CHINESE_MEANING", "中文含义");
        HEADER_DISPLAY_MAP.put("ENGLISH_MEANING", "英文含义");
        HEADER_DISPLAY_MAP.put("BUSINESS_MEANING", "业务含义");
        HEADER_DISPLAY_MAP.put("VALUE_EXAMPLE", "值示例");
        HEADER_DISPLAY_MAP.put("TYPICAL_QUESTIONS", "典型问题");
        HEADER_DISPLAY_MAP.put("ALTERNATE_TERM", "别名");
        HEADER_DISPLAY_MAP.put("COMMENTS", "备注");
    }

    @Override
    // @Cacheable(Configuration.APPLICATION_NAME + ":1d") 添加缓存会导致图片内容过大无法序列化
    public Response search(Map<String, Object> parameterMap) {
        StringBuilder result = new StringBuilder();
        String type = (String) parameterMap.get("type");
        String name = (String) parameterMap.get("name");
        if ("COLUMN".equals(type)) {
            result.append("### ");
            result.append("<a class='moss-quicklook-link' doc-table-name='([a-z0-9A-Z_])+' doc-column-name='" + name + "' href='javascript:void(0)' onclick='window.mossQuickLookEditObject(this)'>" + name + "</a>");
            result.append("  &nbsp;&nbsp;`COLUMN`\n");
            result.append(this.getCommentsByDataset(mossQuickLookDao.queryCommentsByColumnName(name)));

            List<Map<String, Object>> columns = mossQuickLookDao.queryTablesByColumnName(name);
            result.append("### WHICH TABLES HAVE FIELD ").append(name).append(" (").append(Utils.thousandBitSeparator(columns.size())).append(")").append("\n");
            String[] fieldKeys = {"NO.", "COLUMN_NAME", "DATA_TYPE", "CHINESE_MEANING", "ENGLISH_MEANING", "BUSINESS_MEANING", "VALUE_EXAMPLE", "TYPICAL_QUESTIONS", "ALTERNATE_TERM", "COMMENTS"};

            String[] chineseHeaders = Arrays.stream(fieldKeys)
                    .map(key -> HEADER_DISPLAY_MAP.getOrDefault(key, key))
                    .toArray(String[]::new);

            result.append(this.getTablClosByDatasetWithHeaders(
                    mossQuickLookDao.queryColumnsByTableName(name),
                    "COLUMN_NAME",
                    fieldKeys,
                    chineseHeaders
            ));
        } else if ("TABLE".equals(type)) {
            // 表名
            result.append("### ");
            result.append("<a class='moss-quicklook-link' doc-table-name='" + name + "' doc-column-name='[OBJECT_COMMENTS]' href='javascript:void(0)' onclick='window.mossQuickLookEditObject(this)'>" + name + "</a>");
            result.append("  &nbsp;&nbsp;`TABLE`\n");
            // 表注释
            result.append(this.getCommentsByDataset(mossQuickLookDao.queryCommentsByTableName(name)));

            // 表结构
            result.append("### 字段级描述 ").append("\n");
            // String[] headers = new String[]{"NO.", "TABLE_NAME", "COLUMN_NAME", "DATA_TYPE", "LOW_VALUE", "HIGH_VALUE", "LAST_ANALYZED", "COMMENTS"};
            String[] fieldKeys = {"NO.", "COLUMN_NAME", "DATA_TYPE", "CHINESE_MEANING", "ENGLISH_MEANING", "BUSINESS_MEANING", "VALUE_EXAMPLE", "TYPICAL_QUESTIONS", "ALTERNATE_TERM", "COMMENTS"};

            String[] chineseHeaders = Arrays.stream(fieldKeys)
                    .map(key -> HEADER_DISPLAY_MAP.getOrDefault(key, key))
                    .toArray(String[]::new);

            result.append(this.getTablClosByDatasetWithHeaders(
                    mossQuickLookDao.queryColumnsByTableName(name),
                    "COLUMN_NAME",
                    fieldKeys,
                    chineseHeaders
            ));
            // 建表语句
            result.append("### DDL ").append("\n");
            result.append(this.getDDLByObjectName(name, "TABLE"));
        } else if ("MVIEW".equals(type)) {
            // MView名
            result.append("### ");
            result.append("<a class='moss-quicklook-link' doc-table-name='" + name + "' doc-column-name='[OBJECT_COMMENTS]' href='javascript:void(0)' onclick='window.mossQuickLookEditObject(this)'>" + name + "</a>");
            result.append("  &nbsp;&nbsp;`MATERIALIZED VIEW`\n");
            // 注释
            result.append(this.getCommentsByDataset(mossQuickLookDao.queryCommentsByTableName(name)));
            result.append("\n\n");

            // MView结构
            result.append("### 字段级描述 ").append("\n");
            // String[] headers = new String[]{"NO.", "TABLE_NAME", "COLUMN_NAME", "DATA_TYPE", "LOW_VALUE", "HIGH_VALUE", "LAST_ANALYZED", "COMMENTS"};
            String[] fieldKeys = {"NO.", "COLUMN_NAME", "DATA_TYPE", "CHINESE_MEANING", "ENGLISH_MEANING", "BUSINESS_MEANING", "VALUE_EXAMPLE", "TYPICAL_QUESTIONS", "ALTERNATE_TERM", "COMMENTS"};

            String[] chineseHeaders = Arrays.stream(fieldKeys)
                    .map(key -> HEADER_DISPLAY_MAP.getOrDefault(key, key))
                    .toArray(String[]::new);

            result.append(this.getTablClosByDatasetWithHeaders(
                    mossQuickLookDao.queryColumnsByTableName(name),
                    "COLUMN_NAME",
                    fieldKeys,
                    chineseHeaders
            ));
        } else if ("VIEW".equals(type)) {
            // View名
            result.append("### ");
            result.append("<a class='moss-quicklook-link' doc-table-name='" + name + "' doc-column-name='[OBJECT_COMMENTS]' href='javascript:void(0)' onclick='window.mossQuickLookEditObject(this)'>" + name + "</a>");
            result.append("  &nbsp;&nbsp;`VIEW`\n");
            // 注释
            result.append(this.getCommentsByDataset(mossQuickLookDao.queryCommentsByTableName(name)));

            // View结构
            result.append("### 字段级描述 ").append("\n");
            // String[] headers = new String[]{"NO.", "TABLE_NAME", "COLUMN_NAME", "DATA_TYPE", "COMMENTS"};
            String[] fieldKeys = {"NO.", "COLUMN_NAME", "DATA_TYPE", "CHINESE_MEANING", "ENGLISH_MEANING", "BUSINESS_MEANING", "VALUE_EXAMPLE", "TYPICAL_QUESTIONS", "ALTERNATE_TERM", "COMMENTS"};

            String[] chineseHeaders = Arrays.stream(fieldKeys)
                    .map(key -> HEADER_DISPLAY_MAP.getOrDefault(key, key))
                    .toArray(String[]::new);

            result.append(this.getTablClosByDatasetWithHeaders(
                    mossQuickLookDao.queryColumnsByTableName(name),
                    "COLUMN_NAME",
                    fieldKeys,
                    chineseHeaders
            ));
        } else if ("DOCUMENT".equals(type)) {
            result.append(getDocsByDataset(mossQuickLookDao.queryDocByIndex(name), name, type));
        } else if ("COMMENTS".equals(type)) {
            result.append(getDocsByDataset(mossQuickLookDao.queryCommentByIndex(name), name, type));
        }
        return response.setBody(result);
    }

    public String getDocsByDataset(List<Map<String, Object>> resultList, String name, String type) {
        StringBuilder result = new StringBuilder();
        if ("DOCUMENT".equals(type)) {
            for (Map<String, Object> map : resultList) {
                String url = "### <a class='moss-quicklook-link'" + " doc-id='" + map.get("DOC_ID") + "' href='javascript:void(0)' onclick='window.mossQuickLookQueryContent(this)'>" + map.get("GROUPS") + "——" + map.get("SUBJECT") + "</a>";
                result.append(url);
                String content = "\n>" + getDesc((String) map.get("CONTENT"), name) + "\n";
                result.append(content);
            }
        } else if ("COMMENTS".equals(type)) {
            for (Map<String, Object> map : resultList) {
                String tableName = (String) map.get("TABLE_NAME");
                String columnName = (String) map.get("COLUMN_NAME");
                String comments = (String) map.get("COMMENTS");
                result.append("### ").append(columnName).append("  &nbsp;&nbsp;`COLUMN`\n");
                result.append("- ").append(tableName).append("  &nbsp;&nbsp;`TABLE`\n>");
                result.append(comments).append("\n");
            }
        }
        return result.toString();
    }

    private String getDesc(String text, String word) {
        Parser parser = Parser.builder().build();
        HtmlRenderer renderer = HtmlRenderer.builder().build();

        // 将Markdown文本解析为HTML
        String html = renderer.render(parser.parse(text));
        // 使用Jsoup库从HTML中获取纯文本内容
        String plainText = org.jsoup.Jsoup.parse(html).text();
        plainText = plainText.replaceAll("\\r?\\n", " ");

        //从firstPos 作为基准，往前找10个字符，作为描述的起始位置
        int firstPos = -1;
        firstPos = plainText.indexOf(word);
        String desc = "";
        int descBeg = firstPos < 80 ? 0 : firstPos - 80;
        if (descBeg + 200 > plainText.length()) {
            desc = plainText.substring(descBeg);
        } else {
            desc = plainText.substring(descBeg, descBeg + 200) + "...";
        }
        return desc;
    }

    @Override
    public Response queryHistComments(Map<String, Object> parameterMap) {
        StringBuilder result = new StringBuilder();
        boolean allColumnSame = true;
        List<Map<String, Object>> histList = mossQuickLookDao.queryHistComments(parameterMap);
        for (Map<String, Object> map : histList) {
            if (!"[OBJECT_COMMENTS]".equals(map.get("COLUMN_NAME"))) {
                allColumnSame = false;
                break;
            }
        }
        if (allColumnSame) {
            String[] headers = new String[]{"NO.", "TABLE_NAME", "COMMENTS", "CREATE_TIME", "CREATE_BY"};
            result.append(this.getHistTablClosByDataset(histList, headers));
        } else {
            String[] headers = new String[]{"NO.", "TABLE_NAME", "COLUMN_NAME", "COMMENTS", "CREATE_TIME", "CREATE_BY"};
            result.append(this.getHistTablClosByDataset(histList, headers));
        }
        return response.setBody(result);
    }

    private String getDDLByObjectName(String objectName, String type) {
        String ddl = "";
        if ("TABLE".equals(type)) {
            ddl = mossQuickLookDao.getDDLByObjectName(objectName, type);
        } else if ("MATERIALIZED_VIEW".equals(type)) {
            ddl = "\n" + mossQuickLookDao.getQueryByMViewName(objectName);
        } else if ("VIEW".equals(type)) {
            ddl = "\n" + mossQuickLookDao.getQueryByViewName(objectName);
        }
        if ("TABLE".equals(type) || "MATERIALIZED_VIEW".equals(type)) {
            try {
                List<String> indexDDL = mossQuickLookDao.getIndexByTableName(objectName);
                ddl += "\n" + StringUtils.join(indexDDL, "\n");
            } catch (Exception ignore) {

            }
        }
        return "```sql" + ddl + "\n```";
    }

    private String getCommentsByDataset(List<Map<String, Object>> comments) {
        StringBuilder result = new StringBuilder();
        if (comments != null && comments.size() > 0) {
            for (Map<String, Object> comment : comments) {
                result.append("- **");
                result.append(comment.get("TABLE_NAME"));
                result.append("**&nbsp;&nbsp;&nbsp;&nbsp; <small><a style='color: var(--scp-text-color-primary);' data-table='" + comment.get("TABLE_NAME") + "'  data-column='" + comment.get("COLUMN_NAME") + "' href='javascript:void(0)' onclick='window.mossQuickLookQueryHistComments(this)'>*by ");
                result.append(comment.get("CREATE_BY"));
                result.append("@");
                result.append(comment.get("CREATE_TIME"));
                result.append("*</a></small>\n");
                result.append(MarkdownUtil.addQuotePrefix(Utils.clob2String(comment.get("COMMENTS"))));
                result.append("\n");
                result.append("\n");
            }
        }
        return result.toString();
    }

    private Map<String, String> tableMap = new HashMap<>();
    private Map<String, String> columnMap = new HashMap<>();

    private String getHistTablClosByDataset(List<Map<String, Object>> columns, String... headers) {
        StringBuilder result = new StringBuilder();
        if (columns == null || columns.size() == 0) {
            return "`NO DATA`\n\n";
        }
        result.append("| ").append(StringUtils.join(headers, " | ").replace("_", " ")).append(" |").append("\n");
        result.append("| ").append(StringUtils.repeat(" - |", headers.length)).append("\n");
        int index = 1;
        for (Map<String, Object> map : columns) {
            result.append("|");
            for (String header : headers) {
                if ("NO.".equals(header)) {
                    result.append(index++);
                } else if ("COMMENTS".equals(header)) {
                    String comments = Utils.clob2String(map.get(header));
                    if (StringUtils.isNotBlank(comments)) {
                        comments = StringUtils.replace(comments, "|", " ");
                        comments = StringUtils.replace(comments, "\r", " ");
                        comments = StringUtils.replace(comments, "\n", " ");
                        result.append(comments);
                    } else {
                        result.append(" ");
                    }
                } else {
                    result.append(Utils.removeMakeDownSpliter(map.get(header)));
                }
                result.append("|");
            }
            result.append("\r\n");
        }
        return result.toString();
    }

    private String getTablClosByDatasetWithHeaders(
            List<Map<String, Object>> columns,
            String quickLinkHeader,
            String[] fieldKeys,        // 英文字段名，用于取值和判断
            String[] displayHeaders) { // 中文表头，用于显示
        if (quickLinkHeader != null) {
            synchronized (this) {
                if (columnMap.isEmpty() || tableMap.isEmpty()) {
                    List<Map<String, String>> allObjects = mossQuickLookDao.queryAllAvalibleObjects();
                    for (Map<String, String> map : allObjects) {
                        if ("COLUMN".equals(map.get("label"))) {
                            columnMap.put(map.get("value"), map.get("label"));
                        } else {
                            tableMap.put(map.get("value"), map.get("label"));
                        }
                    }
                }
            }
        }

        StringBuilder result = new StringBuilder();
        if (columns == null || columns.size() == 0) {
            return "`NO DATA`\n\n";
        }

        // 显示中文表头
        result.append("| ").append(StringUtils.join(displayHeaders, " | ")).append(" |").append("\n");
        result.append("| ").append(StringUtils.repeat(" - |", displayHeaders.length)).append("\n");

        int index = 1;
        for (Map<String, Object> map : columns) {
            result.append("|");
            // 英文字段名
            for (String fieldKey : fieldKeys) {
                if ("NO.".equals(fieldKey)) {
                    result.append(index++);
                } else if ("DATA_TYPE".equals(fieldKey)) {
                    result.append(Utils.removeMakeDownSpliter(map.get("DATA_TYPE")));
                    if (map.get("DATA_LENGTH") != null) {
                        result.append("(");
                        result.append(map.get("DATA_LENGTH"));
                        result.append(")");
                    }
                } else if ("COMMENTS".equals(fieldKey)) {
                    String comments = Utils.clob2String(map.get("COMMENTS"));
                    if (StringUtils.isNotBlank(comments)) {
                        comments = StringUtils.replace(comments, "|", " ");
                        comments = StringUtils.replace(comments, "\r", " ");
                        comments = StringUtils.replace(comments, "\n", " ");
                        comments += "<a class='moss-quicklook-link' style='float:right;' data-table='" + map.get("EXP_TABLE_NAME") + "' data-column='" + map.get("COLUMN_NAME") + "' href='javascript:void(0)' onclick='window.mossQuickLookQueryHistComments(this)'>*by " + map.get("CREATE_BY") + "@" + map.get("CREATE_TIME") + "*</a>";
                        result.append(comments);
                    } else {
                        result.append(" ");
                    }
                } else if (StringUtils.startsWith(fieldKey, "NUM_")) {
                    result.append("<div style='text-align:right;'>");
                    result.append(Utils.thousandBitSeparator(Utils.removeMakeDownSpliter(map.get(fieldKey))));
                    result.append("</div>");
                } else if (StringUtils.endsWith(fieldKey, "_VALUE")) {
                    result.append("<div style='text-align:right;'>");
                    String dataType = (String) map.get("DATA_TYPE");
                    if ("NUMBER".equals(dataType) || "INTEGER".equals(dataType) || "FLOAT".equals(dataType)) {
                        result.append(Utils.thousandBitSeparator(Utils.removeMakeDownSpliter(map.get(fieldKey)), 2));
                    } else {
                        result.append(Utils.removeMakeDownSpliter(map.get(fieldKey)));
                    }
                    result.append("</div>");
                } else if (StringUtils.equals(fieldKey, quickLinkHeader)) {
                    String value = Utils.removeMakeDownSpliter(map.get(fieldKey));
                    String ahtml = value;
                    String label;
                    if ("COLUMN_NAME".equals(quickLinkHeader)) {
                        label = columnMap.get(value);
                    } else {
                        label = tableMap.get(value);
                    }
                    String title = "Learn more about " + value;
                    if (label != null) {
                        ahtml = "<a class='moss-quicklook-link' title='" + title + "' href='https://scp-dss.cn.schneider-electric.com/#/agent/rag/moss_quick_look?s=" + value + "&t=" + label + "' target='_blank'>" + value + "</a>";
                    }
                    result.append(ahtml);
                } else {
                    result.append(Utils.removeMakeDownSpliter(map.get(fieldKey)));
                }
                result.append("|");
            }
            result.append("\r\n");
        }
        return result.toString();
    }

    @Override
    public Response queryKeywords(Map<String, Object> parameterMap) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> resultList = new ArrayList<>();
        String keywords = (String) parameterMap.get("keywords");
        Pattern pattern = Pattern.compile("[a-zA-Z0-9]");
        Matcher matcher = pattern.matcher(keywords);
        List<String> wordList = new ArrayList<>();
        if (!keywords.isEmpty()) {
            wordList.add(keywords);
            while (keywords.contains("  ")) {
                keywords = keywords.replace("  ", "");
            }
            if (keywords.contains(" ")) {
                wordList.addAll(List.of(keywords.split(" ")));
            }
            if (!matcher.find() && keywords.length() > 1) {
                List<Term> terms = ToAnalysis.parse(keywords).getTerms();
                for (Term term : terms) {
                    if (!Objects.equals(term.getNatureStr(), "u")) {
                        wordList.add(term.getName());
                    }
                }
            }
            resultList.addAll(mossQuickLookDao.queryKeywords(wordList));
        }
        result.put("words", wordList);
        result.put("result", resultList);
        return response.setBody(result);
    }

    @Override
    public Response queryContent(Map<String, Object> parameterMap) {
        StringBuilder result = new StringBuilder();
        result.append(Utils.clob2String(mossQuickLookDao.queryContentById(parameterMap)));
        return response.setBody(result);
    }

    @Override
    public Response queryCommentsTemplate() {
        return response.setBody(Utils.clob2String(mossQuickLookDao.queryCommentsTemplate()));
    }
}
