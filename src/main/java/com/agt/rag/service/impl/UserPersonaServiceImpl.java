package com.agt.rag.service.impl;

import com.agt.rag.dao.IUserPersonaDao;
import com.agt.rag.service.IUserPersonaService;
import com.starter.context.bean.Response;
import com.starter.utils.Utils;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

@Service
@Scope("prototype")
@Transactional
public class UserPersonaServiceImpl implements IUserPersonaService {

    public final static String PARENT_CODE = "menuE02";

    @Resource
    private IUserPersonaDao userPersonaDao;

    @Resource
    private Response response;

    @Override
    public Response initPage(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        parameterMap.put("parentCode", UserPersonaServiceImpl.PARENT_CODE);
        boolean isAdmin = userPersonaDao.queryPageAdmin(parameterMap) > 0;
        resultMap.put("isAdmin", isAdmin);
        resultMap.put("existsGroup", userPersonaDao.queryExistsGroup());
        return response.setBody(resultMap);
    }

    @Override
    public Response queryPersonaList(Map<String, Object> parameterMap) {
        return response.setBody(Utils.parseTreeNodes(userPersonaDao.queryPersonaList()));
    }

    @Override
    public Response modifyPersona(Map<String, Object> parameterMap) {
        String content = (String) parameterMap.get("CONTENT");
        if (StringUtils.isNotBlank(content)) {
            userPersonaDao.modifyPersona(parameterMap);
        } else {
            userPersonaDao.deletePersona(parameterMap);
        }
        return response;
    }

    @Override
    public Response queryPersonaWithId(Map<String, Object> parameterMap) {
        Map<String, String> result = userPersonaDao.queryPersonaWithId(parameterMap);
        if (result == null) {
            result = new HashMap<>();
        }
        return response.setBody(result);
    }

    @Override
    public Response queryPersonaTemplate(Map<String, Object> parameterMap) {
        return response.setBody(userPersonaDao.queryPersonaTemplate(parameterMap));
    }
}
