package com.agt.rag.service.impl;

import com.agt.rag.dao.IAgentRegistrationDao;
import com.agt.rag.service.IAgentRegistrationService;
import com.starter.context.bean.Response;
import com.starter.utils.Utils;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

@Service
@Scope("prototype")
@Transactional
public class AgentRegistrationServiceImpl implements IAgentRegistrationService {

    public final static String PARENT_CODE = "menuE06";

    @Resource
    private IAgentRegistrationDao agentRegistrationDao;

    @Resource
    private Response response;

    @Override
    public Response initPage(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("existsGroup", agentRegistrationDao.queryExistsGroup());
        resultMap.put("introduction", agentRegistrationDao.queryApiIntroduction());
        return response.setBody(resultMap);
    }

    @Override
    public Response saveAgent(Map<String, Object> parameterMap) {
        parameterMap.put("AGENT_ID", Utils.randomStr(12));
        if ("BUILT_IN".equals(parameterMap.get("AGENT_TYPE"))) {
            parameterMap.remove("REQUEST_URL");
            parameterMap.remove("REQUEST_HEADER");
            parameterMap.remove("REQUEST_BODY");
        }
        agentRegistrationDao.saveAgent(parameterMap);
        return response;
    }

    @Override
    public Response queryAgentList(Map<String, Object> parameterMap) {
        return response.setBody(Utils.parseTreeNodes(agentRegistrationDao.queryAgentList()));
    }

    @Override
    public Response modifyAgent(Map<String, Object> parameterMap) {
        if ("BUILT_IN".equals(parameterMap.get("AGENT_TYPE"))) {
            parameterMap.remove("REQUEST_URL");
            parameterMap.remove("REQUEST_HEADER");
            parameterMap.remove("REQUEST_BODY");
        }
        agentRegistrationDao.modifyAgent(parameterMap);
        return response;
    }

    @Override
    public Response queryAgentWithId(Map<String, Object> parameterMap) {
        Map<String, String> result = agentRegistrationDao.queryAgentWithId(parameterMap);
        if (result == null) {
            result = new HashMap<>();
        }
        return response.setBody(result);
    }

    @Override
    public Response deleteAgent(Map<String, Object> parameterMap) {
        agentRegistrationDao.deleteAgent(parameterMap);
        return response;
    }
}
