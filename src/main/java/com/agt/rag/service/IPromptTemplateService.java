package com.agt.rag.service;

import com.starter.context.bean.Response;

import java.util.Map;

public interface IPromptTemplateService {

    Response initPage(Map<String, Object> parameterMap);

    Response queryPromptList(Map<String, Object> parameterMap);

    Response saveNewPrompt(Map<String, Object> parameterMap);

    Response queryPrompt(Map<String, Object> parameterMap);

    Response modifyPrompt(Map<String, Object> parameterMap);

    Response deletePrompt(Map<String, Object> parameterMap);

    Response queryPromptWithId(Map<String, Object> parameterMap);
}
