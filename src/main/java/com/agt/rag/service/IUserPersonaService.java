package com.agt.rag.service;

import com.starter.context.bean.Response;
import com.starter.login.bean.Session;

import java.util.Map;

public interface IUserPersonaService {

    Response initPage(Map<String, Object> parameterMap);

    Response queryPersonaList(Map<String, Object> parameterMap);

    Response modifyPersona(Map<String, Object> parameterMap);

    Response queryPersonaWithId(Map<String, Object> parameterMap);

    Response queryPersonaTemplate(Map<String, Object> parameterMap);
}
