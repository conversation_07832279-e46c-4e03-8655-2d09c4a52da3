package com.agt.rag.service;

import com.starter.context.bean.Response;

import java.util.Map;

public interface IAgentRegistrationService {

    Response initPage(Map<String, Object> parameterMap);

    Response saveAgent(Map<String, Object> parameterMap);

    Response queryAgentList(Map<String, Object> parameterMap);

    Response modifyAgent(Map<String, Object> parameterMap);

    Response queryAgentWithId(Map<String, Object> parameterMap);

    Response deleteAgent(Map<String, Object> parameterMap);
}
