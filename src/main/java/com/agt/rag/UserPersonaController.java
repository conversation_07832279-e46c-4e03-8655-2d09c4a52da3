package com.agt.rag;

import com.agt.rag.service.IUserPersonaService;
import com.agt.rag.service.impl.UserPersonaServiceImpl;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/rag/user_persona", parent = UserPersonaServiceImpl.PARENT_CODE)
public class UserPersonaController extends ControllerHelper {

    @Resource
    private IUserPersonaService userPersonaService;

    @SchneiderRequestMapping(value = "/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        return userPersonaService.initPage(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_persona_list")
    public Response queryPersonaList(HttpServletRequest request) {
        super.pageLoad(request);
        return userPersonaService.queryPersonaList(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_persona_with_id")
    public Response queryPersonaWithId(HttpServletRequest request) {
        super.pageLoad(request);
        return userPersonaService.queryPersonaWithId(parameterMap);
    }

    @SchneiderRequestMapping(value = "/modify_persona")
    public Response modifyPersona(HttpServletRequest request) {
        super.pageLoad(request);
        return userPersonaService.modifyPersona(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_persona_template")
    public Response queryPersonaTemplate(HttpServletRequest request) {
        super.pageLoad(request);
        return userPersonaService.queryPersonaTemplate(parameterMap);
    }
}
