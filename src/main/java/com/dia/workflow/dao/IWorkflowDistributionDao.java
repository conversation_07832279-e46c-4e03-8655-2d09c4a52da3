package com.dia.workflow.dao;

import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface IWorkflowDistributionDao {

    List<Map<String, Object>> queryWorkflowList();

    void saveDistribution(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport1(Map<String, Object> parameterMap);

    void deleteDistributionByID(Map<String, Object> parameterMap);

    Map<String, Object> queryDistributionById(Map<String, Object> parameterMap);

    void modifyDistribution(Map<String, Object> parameterMap);

    int queryReport1DetailsCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport1Details(Map<String, Object> parameterMap);
}
