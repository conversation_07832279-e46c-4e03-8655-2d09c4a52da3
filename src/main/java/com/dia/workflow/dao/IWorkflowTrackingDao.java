package com.dia.workflow.dao;

import org.apache.ibatis.annotations.Mapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IWorkflowTrackingDao {

    List<Map<String, Object>> queryReport1(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport2(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport3(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport4(Map<String, Object> parameterMap);

    int queryReport5Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport5(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport6(Map<String, Object> parameterMap);
}
