package com.dia.workflow.dao;

import com.scp.toolbox.bean.TreeData;
import com.starter.context.bean.Response;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface IWorkflowMaintenanceDao {

    String queryAuthDetails(String userid, @Param("menuCode") String parentCode);

    List<TreeData> queryWorkflowList(String userid, String authType);

    List<String> initExistsGroup(String userid, String authType);

    List<Map<String, Object>> queryOwnerOpts(String userid, String authType);

    void createWorkflow(Map<String, Object> parameterMap);

    Map<String, Object> queryInfoById(Map<String, Object> parameterMap);

    void modifyWorkflow(Map<String, Object> parameterMap);

    void deleteWorkflow(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryUserMail();

    List<String> queryTableOutputList();

    List<Map<String, Object>> queryTableColumns(Map<String, Object> parameterMap);

    List<Map<String, String>> queryColumnDefaultValues(Map<String, Object> parameterMap);

    void modifyWorkflowName(Map<String, Object> parameterMap);

    List<String> queryProcList();

    List<Map<String, String>> queryProcArgs(Map<String, Object> parameterMap);

    List<String> queryTables();

    List<String> queryTableCols();

    List<Map<String, String>> queryWorkflowOpts();
}
