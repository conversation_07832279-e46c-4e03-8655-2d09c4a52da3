<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dia.workflow.dao.IWorkflowDistributionDao">
    <select id="queryWorkflowList" resultType="java.util.Map">
        SELECT REPLACE(T.GROUPS, '.', '>') GROUPS, T.NAME, T.ID
          FROM WORKFLOW_CONFIG T
         ORDER BY T.CREATE_DATE$ DESC
    </select>

    <insert id="saveDistribution">
        INSERT INTO WORKFLOW_DISTRIBUTION
        (ID, NAME, WORKFLOW_ID, MONTH, DAY_TYPE, DAY, HOUR, CREATE_BY$, CREATE_DATE$, STATUS, START_DATE, END_DATE)
        VALUES
        (#{id, jdbcType=VARCHAR}, #{name, jdbcType=VARCHAR}, #{wfID, jdbcType=VARCHAR},
        #{month, jdbcType=VARCHAR}, #{dayType, jdbcType=VARCHAR}, #{day, jdbcType=VARCHAR}, #{hour, jdbcType=VARCHAR},
        #{session.userid, jdbcType=VARCHAR}, SYSDATE, #{enable, jdbcType=VARCHAR},
        to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd'), to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'))
    </insert>

    <select id="queryReport1" resultType="java.util.Map">
        SELECT T.ID, T.NAME, T.MONTH, T.DAY, T.HOUR, T.DAY_TYPE, T.LAST_DIS_CNT, UPPER(NVL(T3.GROUPS, 'Default / Deleted')) GROUPS,
               TO_CHAR(T.LAST_EXEC_START_TIME, 'YYYY/MM/DD HH24:MI') LAST_EXEC_START_TIME,
               ROUND((T.LAST_EXEC_END_TIME - T.LAST_EXEC_START_TIME) * 1440, 1) TIME_COST_IN_MIN,
               CASE WHEN SYSDATE BETWEEN T.START_DATE AND T.END_DATE THEN T.STATUS ELSE 'N' END AS STATUS,
               NVL(T2.USER_NAME, T.CREATE_BY$) AS USERNAME
          FROM WORKFLOW_DISTRIBUTION T LEFT JOIN SY_USER_MASTER_DATA T2 ON T.CREATE_BY$ = T2.SESA_CODE
                                       LEFT JOIN WORKFLOW_CONFIG T3 ON T.WORKFLOW_ID = T3.ID
          ORDER BY UPPER(NVL(T3.GROUPS, 'ZZZZZ')), T.STATUS, T.NAME
    </select>

    <delete id="deleteDistributionByID">
        DELETE FROM WORKFLOW_DISTRIBUTION T WHERE T.ID = #{id, jdbcType=VARCHAR}
    </delete>

    <select id="queryDistributionById" resultType="java.util.Map">
        SELECT NAME,
               WORKFLOW_ID,
               START_DATE,
               END_DATE,
               MONTH,
               DAY_TYPE,
               DAY,
               HOUR,
               STATUS
        FROM WORKFLOW_DISTRIBUTION T
        WHERE T.ID = #{id, jdbcType=VARCHAR}
    </select>

    <sql id="queryReport1DetailsSql">
        SELECT DIS_ID, LINES_READ, LINES_WRITTEN, MAIL_SENT, FILE_CREATED,
               TO_CHAR(START_TIME, 'YYYY/MM/DD HH24:MI:SS') START_TIME,
               TO_CHAR(END_TIME, 'YYYY/MM/DD HH24:MI:SS') END_TIME,
               TIME_COST_IN_SECOND, MESSAGE, STATUS
         FROM WORKFLOW_EXEC_LOGS T WHERE T.DIS_ID = #{disID, jdbcType=VARCHAR}
        ORDER BY T.START_TIME DESC
    </sql>

    <select id="queryReport1DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1DetailsSql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1DetailsSql"/>
        <include refid="global.select_footer"/>
    </select>

    <update id="modifyDistribution">
        UPDATE WORKFLOW_DISTRIBUTION
           SET NAME = #{name, jdbcType=VARCHAR},
               WORKFLOW_ID = #{wfID, jdbcType=VARCHAR},
               MONTH = #{month, jdbcType=VARCHAR},
               DAY_TYPE = #{dayType, jdbcType=VARCHAR},
               DAY = #{day, jdbcType=VARCHAR},
               HOUR = #{hour, jdbcType=VARCHAR},
               STATUS = #{enable, jdbcType=VARCHAR},
               START_DATE = to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd'),
               END_DATE = to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'),
               UPDATE_BY$ = #{session.userid, jdbcType=VARCHAR},
               UPDATE_DATE$ = SYSDATE
         WHERE ID = #{id, jdbcType=VARCHAR}
    </update>
</mapper>
