<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dia.workflow.dao.IWorkflowMaintenanceDao">
	<select id="queryAuthDetails" resultType="java.lang.String">
        select auth_details
          from sy_menu_auth
         where lower(user_id) = lower(#{userid, jdbcType=VARCHAR})
           and menu_code = #{menuCode, jdbcType=VARCHAR}
    </select>

    <select id="queryWorkflowList" resultType="com.scp.toolbox.bean.TreeData">
        SELECT ID KEY,
			   NAME AS LABEL,
			   GROUPS
		  FROM WORKFLOW_CONFIG T
		  <if test="authType != 'ADMIN'.toString()">
		 	where USERID = #{userid,jdbcType=VARCHAR}
		  </if>
		 ORDER BY GROUPS, NAME
    </select>

    <select id="initExistsGroup" resultType="java.lang.String">
        SELECT DISTINCT GROUPS
		  FROM WORKFLOW_CONFIG T
		 WHERE T.GROUPS IS NOT NULL
		  <if test="authType != 'ADMIN'.toString()">
		 	AND USERID = #{userid,jdbcType=VARCHAR}
		  </if>
		 ORDER BY GROUPS
    </select>

	<select	id="queryOwnerOpts" resultType="java.util.Map">
		SELECT T.USER_NAME || ' [' || T.SESA_CODE || ']' AS LABEL,T.SESA_CODE AS VAL
		  FROM SY_USER_MASTER_DATA T
		 WHERE SESA_CODE != 'Admin'
		 ORDER BY USER_NAME
	</select>

	<insert id="createWorkflow">
		INSERT INTO WORKFLOW_CONFIG (ID, NAME, GROUPS, USERID, CREATE_BY$, CREATE_DATE$)
		VALUES
		(#{id, jdbcType=VARCHAR}, #{name, jdbcType=VARCHAR}, #{groups, jdbcType=VARCHAR}, #{session.userid, jdbcType=VARCHAR},
		#{session.userid, jdbcType=VARCHAR}, sysdate)
	</insert>

	<select id="queryInfoById" resultType="java.util.Map">
		SELECT T.ID, T.NAME, T.GROUPS, T.CONFIG, T.PARAMS,
		       NVL(T2.SESA_CODE, T.USERID) SESA_CODE,
		       NVL(T2.USER_NAME, T.USERID) USER_NAME
		  FROM WORKFLOW_CONFIG T LEFT JOIN SY_USER_MASTER_DATA T2 ON T.USERID = T2.SESA_CODE
		 WHERE T.ID = #{id, jdbcType=VARCHAR}
	</select>

    <select id="queryUserMail" resultType="java.util.Map">
		SELECT USER_NAME,EMAIL
          FROM SY_USER_MASTER_DATA T
         WHERE EMAIL IS NOT NULL
         ORDER BY T.USER_NAME
	</select>

    <update id="modifyWorkflow">
		DECLARE
            PARAMS_COLB CLOB := #{params, jdbcType=CLOB};
            CONFIG_COLB CLOB := #{config, jdbcType=CLOB};
        BEGIN
			UPDATE WORKFLOW_CONFIG T
			   SET T.CONFIG = CONFIG_COLB,
			       T.PARAMS = PARAMS_COLB,
			       T.UPDATE_DATE$ = SYSDATE,
			       T.UPDATE_BY$ = #{session.userid, jdbcType=VARCHAR}
			 WHERE T.ID = #{id, jdbcType=VARCHAR};
		END;
	</update>

    <update id="modifyWorkflowName">
		UPDATE WORKFLOW_CONFIG T
		   SET T.NAME = #{name, jdbcType=VARCHAR},
		       T.GROUPS = #{groups, jdbcType=VARCHAR},
		       <if test="authType == 'ADMIN'.toString()">
		       		T.USERID = #{owner, jdbcType=VARCHAR},
			   </if>
		       T.UPDATE_DATE$ = SYSDATE,
			   T.UPDATE_BY$ = #{session.userid, jdbcType=VARCHAR}
		 WHERE T.ID = #{id, jdbcType=VARCHAR}
	</update>

    <delete id="deleteWorkflow">
		DELETE FROM WORKFLOW_CONFIG T WHERE T.ID = #{id, jdbcType=VARCHAR}
	</delete>

	<select id="queryTableOutputList" resultType="java.lang.String">
		SELECT PROPERTY FROM WORKFLOW_PROPERTIES T WHERE T.NAME = 'TABLE_OUTPUT' ORDER BY PROPERTY
	</select>

	<select id="queryTableColumns" resultType="java.util.Map">
		SELECT T.COLUMN_NAME, T.DATA_TYPE, T.DATA_LENGTH FROM USER_TAB_COLS T WHERE T.TABLE_NAME = #{table, jdbcType=VARCHAR} ORDER BY T.COLUMN_ID
	</select>

	<select id="queryColumnDefaultValues" resultType="java.util.Map">
		SELECT PROPERTY2, PROPERTY3
		  FROM WORKFLOW_PROPERTIES T
		 WHERE T.NAME = 'COLUMN_DEFAULT_VALUE'
		   AND T.PROPERTY = #{table, jdbcType=VARCHAR}
		 ORDER BY TO_NUMBER(T.PROPERTY4)
	</select>

	<select id="queryProcList" resultType="java.lang.String">
		SELECT PROPERTY FROM WORKFLOW_PROPERTIES T WHERE T.NAME = 'CALL_PROC' ORDER BY PROPERTY
	</select>

	<select id="queryProcArgs" resultType="java.util.Map">
		SELECT T.ARGUMENT_NAME, T.DATA_TYPE, T.IN_OUT FROM USER_ARGUMENTS T WHERE T.OBJECT_NAME = #{proc, jdbcType=VARCHAR} ORDER BY SEQUENCE
	</select>

	<select id="queryTables" resultType="java.lang.String">
        SELECT DISTINCT T.TABLE_NAME FROM ALL_TABLES T WHERE T.OWNER = 'SCPA'
    </select>

    <select id="queryTableCols" resultType="java.lang.String">
        SELECT DISTINCT UPPER(T.COLUMN_NAME) FROM ALL_TAB_COLS T WHERE REGEXP_LIKE(t.COLUMN_NAME, '^(\w+)$') AND T.OWNER = 'SCPA'
    </select>

	<select id="queryWorkflowOpts" resultType="java.util.Map">
		SELECT REPLACE(T.GROUPS, '.', '>') || '>' || T.NAME AS "label", T.ID AS "value"
          FROM WORKFLOW_CONFIG T
         ORDER BY T.GROUPS, T.NAME
	</select>
</mapper>
