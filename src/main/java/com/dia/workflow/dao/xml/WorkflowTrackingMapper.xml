<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dia.workflow.dao.IWorkflowTrackingDao">

    <select id="queryReport1" resultType="java.util.Map">
       WITH TEMP AS (SELECT T.WORKFLOW_ID, T2.USERID
              FROM WORKFLOW_DISTRIBUTION T INNER JOIN WORKFLOW_CONFIG T2 ON T.WORKFLOW_ID = T2.ID
              WHERE T.STATUS = 'Y'
                AND SYSDATE BETWEEN T.START_DATE AND T.END_DATE),
            TEMP2 AS (
                SELECT START_TIME, END_TIME
                  FROM WORKFLOW_DIS_LOGS T
                 WHERE TRUNC(T.START_TIME, 'DD') = TRUNC(TO_DATE(#{date, jdbcType=VARCHAR}, 'YYYY/MM/DD'), 'DD')
                   AND T.START_TIME IS NOT NULL
                   AND T.END_TIME IS NOT NULL
            )
        SELECT COUNT(DISTINCT T.WORKFLOW_ID) AS VAL, 'ACTIVE_WORKFLOW_CNT' AS KEY FROM TEMP T
        UNION ALL
        SELECT COUNT(1) AS CNT, 'DISTRIBUTIONS_CNT' FROM TEMP2 T
        UNION ALL
        SELECT ROUND(SUM((T.END_TIME - T.START_TIME) * 1440.0), 1), 'TIMECOST' FROM TEMP2 T
        UNION ALL
        SELECT ROUND(DECODE(COUNT(1), 0, NULL, SUM((T.END_TIME - T.START_TIME) * 86400.0) / COUNT(1)), 1), 'TIMECOST_PER_TIME' FROM TEMP2 T
        UNION ALL
        SELECT COUNT(DISTINCT T.USERID), 'KEY_USER_CNT' FROM TEMP T
        UNION ALL
        SELECT COUNT(1), 'ACCUMULATED_DISTRIBUTIONS_CNT' FROM WORKFLOW_DIS_LOGS T
    </select>

    <select id="queryReport2" resultType="java.util.Map">
        SELECT
        <choose>
            <when test="report2Type == 'BY_TIME'.toString()">
                ROUND(SUM((T.END_TIME - T.START_TIME) * 1440.0), 1) AS "value"
            </when>
            <otherwise>
                SUM(1) AS "value"
            </otherwise>
        </choose>, T2.NAME AS "name"
          FROM WORKFLOW_DIS_LOGS T INNER JOIN WORKFLOW_DISTRIBUTION T2 ON T.DIS_ID = T2.ID
         WHERE TRUNC(T.START_TIME, 'DD') = TRUNC(TO_DATE(#{date, jdbcType=VARCHAR}, 'YYYY/MM/DD'), 'DD')
           AND T.START_TIME IS NOT NULL
           AND T.END_TIME IS NOT NULL
         GROUP BY T2.NAME
    </select>

    <select id="queryReport3" resultType="java.util.Map">
        WITH TEMP AS (SELECT T.TEXT
                      FROM SY_CALENDAR T
                      WHERE T.NAME = 'National Holidays'
                        AND T.WORKING_DAY = 1
                        AND DATE$ BETWEEN SYSDATE - 30 AND SYSDATE
                      ORDER BY T.DATE$ FETCH NEXT 30 ROWS ONLY),
             TEMP2 AS (SELECT TO_CHAR(T.START_TIME, 'YYYY/MM/DD')                 AS TEXT,
                              COUNT(1)                                            AS "yAxis1",
                              ROUND(SUM((T.END_TIME - T.START_TIME) * 1440.0), 1) AS "yAxis2"
                       FROM WORKFLOW_DIS_LOGS T
                       WHERE T.START_TIME IS NOT NULL
                         AND T.END_TIME IS NOT NULL
                         AND T.START_TIME BETWEEN SYSDATE - 30 AND SYSDATE
                       GROUP BY TO_CHAR(T.START_TIME, 'YYYY/MM/DD'))
        SELECT T.TEXT AS "xAxis",
               T2."yAxis1",
               T2."yAxis2"
        FROM TEMP T LEFT JOIN TEMP2 T2 ON T.TEXT = T2.TEXT
    </select>

    <select id="queryReport4" resultType="java.util.Map">
        SELECT TO_CHAR(START_TIME, 'HH24') LABEL, COUNT(1) CNT
          FROM WORKFLOW_DIS_LOGS T
         WHERE TRUNC(T.START_TIME, 'DD') = TRUNC(TO_DATE(#{date, jdbcType=VARCHAR}, 'YYYY/MM/DD'), 'DD')
           AND T.START_TIME IS NOT NULL
           AND T.END_TIME IS NOT NULL
         GROUP BY TO_CHAR(START_TIME, 'HH24')
    </select>

    <sql id="queryReport5SQL">
        WITH TEMP AS (SELECT T.DIS_ID,
                             TO_CHAR(MIN(T.START_TIME), 'YYYY/MM/DD HH24:MI:SS') AS EXEC_TIME,
                             ROUND(SUM((T.END_TIME - T.START_TIME) * 1440.0), 1) AS TIME_COST_IN_MIN,
                             COUNT(1)                                            AS INVOLVED_USER
                      FROM WORKFLOW_DIS_LOGS T
                      WHERE TRUNC(T.START_TIME, 'DD') = TRUNC(TO_DATE(#{date, jdbcType=VARCHAR}, 'YYYY/MM/DD'), 'DD')
                        AND T.START_TIME IS NOT NULL
                        AND T.END_TIME IS NOT NULL
                      GROUP BY T.DIS_ID)

        SELECT T2.NAME                      AS DIS_NAME,
               T.EXEC_TIME,
               T.INVOLVED_USER,
               T.TIME_COST_IN_MIN,
               T3.NAME                      AS TEMPLATE,
               NVL(T4.USER_NAME, T3.USERID) AS KEY_USER
        FROM TEMP T
                 INNER JOIN WORKFLOW_DISTRIBUTION T2 ON T.DIS_ID = T2.ID
                 INNER JOIN WORKFLOW_CONFIG T3 ON T2.WORKFLOW_ID = T3.ID
                 LEFT JOIN SY_USER_MASTER_DATA T4 ON T3.USERID = T4.SESA_CODE
        ORDER BY T.EXEC_TIME DESC
    </sql>

    <select id="queryReport5Count" parameterType="java.util.Map" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
        <include refid="queryReport5SQL"/>
        <include refid="global.count_footer"/>
	</select>

	<select id="queryReport5" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport5SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport6" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT * FROM (
        SELECT NVL(T2.USER_NAME, T.USERID) USERNAME, T.USERID, COUNT(1) AS CNT
          FROM WORKFLOW_DIS_LOGS T
               LEFT JOIN SY_USER_MASTER_DATA T2 ON T.USERID = T2.SESA_CODE
         WHERE TRUNC(T.START_TIME, 'DD') = TRUNC(TO_DATE(#{date, jdbcType=VARCHAR}, 'YYYY/MM/DD'), 'DD')
           AND T.START_TIME IS NOT NULL
           AND T.END_TIME IS NOT NULL
         GROUP BY T2.USER_NAME, T.USERID
         ORDER BY COUNT(1) DESC
         FETCH NEXT 15 ROWS ONLY ) ORDER BY CNT
    </select>
</mapper>
