package com.dia.workflow;

import com.dia.workflow.service.IWorkflowMaintenanceService;
import com.dia.workflow.service.impl.WorkflowMaintenanceServiceImpl;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/diagnosis/workflow/maintenance", parent = WorkflowMaintenanceServiceImpl.PARENT_CODE)
public class WorkflowMaintenanceController extends ControllerHelper {

    @Resource
    private IWorkflowMaintenanceService workflowService;

    @SchneiderRequestMapping("/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        return workflowService.initPage(session.getUserid());
    }

    @SchneiderRequestMapping("/query_workflow_list")
    public Response queryWorkflowList(HttpServletRequest request) {
        super.pageLoad(request);
        return workflowService.queryWorkflowList(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping("/create_workflow")
    public Response createWorkflow(HttpServletRequest request) {
        super.pageLoad(request);
        return workflowService.createWorkflow(parameterMap);
    }

    @SchneiderRequestMapping("/modify_workflow_name")
    public Response modifyWorkflowName(HttpServletRequest request) {
        super.pageLoad(request);
        return workflowService.modifyWorkflowName(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping("/modify_workflow")
    public Response modifyWorkflow(HttpServletRequest request) {
        super.pageLoad(request);
        return workflowService.modifyWorkflow(parameterMap);
    }

    @SchneiderRequestMapping("/delete_workflow")
    public Response deleteWorkflow(HttpServletRequest request) {
        super.pageLoad(request);
        return workflowService.deleteWorkflow(parameterMap);
    }

    @SchneiderRequestMapping("/query_info_by_id")
    public Response queryInfoById(HttpServletRequest request) {
        super.pageLoad(request);
        return workflowService.queryInfoById(parameterMap);
    }

    @SchneiderRequestMapping("/query_columns_by_sql")
    public Response queryColumnsBySql(HttpServletRequest request) {
        super.pageLoad(request);
        return workflowService.queryColumnsBySql(parameterMap);
    }

    @SchneiderRequestMapping("/format_sql")
    public Response formatSQL(HttpServletRequest request) {
        super.pageLoad(request);
        return workflowService.formatSQL(parameterMap);
    }

    @SchneiderRequestMapping("/query_user_mail")
    public Response queryUserMail(HttpServletRequest request) {
        super.pageLoad(request);
        return workflowService.queryUserMail(parameterMap);
    }

    @SchneiderRequestMapping("/run_test")
    public Response runTest(HttpServletRequest request) {
        super.pageLoad(request);
        return workflowService.runTest(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping(value = "/query_table_output_list")
    public Response queryTableOutputList(HttpServletRequest request) {
        this.pageLoad(request);
        this.setGlobalCache(true);
        return workflowService.queryTableOutputList();
    }

    @SchneiderRequestMapping(value = "/query_table_conf")
    public Response queryTableConf(HttpServletRequest request) {
        this.pageLoad(request);
        this.setGlobalCache(true);
        return workflowService.queryTableConf(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_table_columns")
    public Response queryTableColumns(HttpServletRequest request) {
        this.pageLoad(request);
        this.setGlobalCache(true);
        return workflowService.queryTableColumns(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_table_default_value")
    public Response queryTableDefaultValue(HttpServletRequest request) {
        this.pageLoad(request);
        this.setGlobalCache(true);
        return workflowService.queryTableDefaultValue(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_proc_list")
    public Response queryProcList(HttpServletRequest request) {
        this.pageLoad(request);
        this.setGlobalCache(true);
        return workflowService.queryProcList();
    }

    @SchneiderRequestMapping(value = "/query_proc_args")
    public Response queryProcArgs(HttpServletRequest request) {
        this.pageLoad(request);
        this.setGlobalCache(true);
        return workflowService.queryProcArgs(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_java_client_output")
    public Response queryJavaClientOutput(HttpServletRequest request) {
        this.pageLoad(request);
        this.setGlobalCache(true);
        return workflowService.queryJavaClientOutput(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_workflow_opts")
    public Response queryWorkflowOpts(HttpServletRequest request) {
        this.pageLoad(request);
        this.setGlobalCache(true);
        return workflowService.queryWorkflowOpts();
    }

    @SchneiderRequestMapping(value = "/test_shared_disk")
    public Response testSharedDisk(HttpServletRequest request) {
        this.pageLoad(request);
        return workflowService.testSharedDisk(parameterMap);
    }
}
