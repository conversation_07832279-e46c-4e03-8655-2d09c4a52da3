package com.dia.workflow.engine.javaclient;

import javax.tools.SimpleJavaFileObject;
import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.net.URI;

public class ByteCodeOutputFile extends SimpleJavaFileObject {
    private final ByteArrayOutputStream baos = new ByteArrayOutputStream();

    public ByteCodeOutputFile(String className) {
        super(URI.create("bytes:///" + className), Kind.CLASS);
    }

    @Override
    public OutputStream openOutputStream() {
        return baos;
    }

    public byte[] getBytes() {
        return baos.toByteArray();
    }
}
