package com.dia.workflow.engine.javaclient;

import java.util.Map;

public class InMemoryClassLoader extends ClassLoader {
    private final Map<String, ByteCodeOutputFile> classData;

    public InMemoryClassLoader(Map<String, ByteCodeOutputFile> classData) {
        this.classData = classData;
    }

    @Override
    protected Class<?> findClass(String name) throws ClassNotFoundException {
        ByteCodeOutputFile file = classData.get(name);
        if (file != null) {
            byte[] bytes = file.getBytes();
            return defineClass(name, bytes, 0, bytes.length);
        }
        return super.findClass(name);
    }
}
