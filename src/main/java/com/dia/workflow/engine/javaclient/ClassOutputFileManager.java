package com.dia.workflow.engine.javaclient;

import javax.tools.FileObject;
import javax.tools.ForwardingJavaFileManager;
import javax.tools.JavaFileManager;
import javax.tools.JavaFileObject;
import java.util.HashMap;
import java.util.Map;

public class ClassOutputFileManager extends ForwardingJavaFileManager<JavaFileManager> {

    private final Map<String, ByteCodeOutputFile> byteStreams;

    public ClassOutputFileManager(JavaFileManager fileManager, Map<String, ByteCodeOutputFile> byteStreams) {
        super(fileManager);
        this.byteStreams = byteStreams;
    }

    @Override
    public JavaFileObject getJavaFileForOutput(Location location, String className, JavaFileObject.Kind kind, FileObject sibling) {
        ByteCodeOutputFile outputFile = new ByteCodeOutputFile(className);
        byteStreams.put(className, outputFile);
        return outputFile;
    }
}
