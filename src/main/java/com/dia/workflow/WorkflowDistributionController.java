package com.dia.workflow;

import com.dia.workflow.service.IWorkflowDistributionService;
import com.dia.workflow.service.impl.WorkflowDistributionServiceImpl;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/diagnosis/workflow/distribution", parent = WorkflowDistributionServiceImpl.PARENT_CODE)
public class WorkflowDistributionController extends ControllerHelper {

    @Resource
    private IWorkflowDistributionService workflowDistributionService;

    @SchneiderRequestMapping(value = "/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return workflowDistributionService.initPage(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_workflow")
    public Response queryWorkflow(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return workflowDistributionService.queryWorkflow(parameterMap);
    }

    @SchneiderRequestMapping(value = "/save_distribution")
    public Response saveDistribution(HttpServletRequest request) {
        super.pageLoad(request);
        return workflowDistributionService.saveDistribution(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        return workflowDistributionService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_report1_details")
    public Response queryReport1Details(HttpServletRequest request) {
        super.pageLoad(request);
        return workflowDistributionService.queryReport1Details(parameterMap);
    }

    @SchneiderRequestMapping(value = "/delete_distribution_by_id")
    public Response deleteDistributionByID(HttpServletRequest request) {
        super.pageLoad(request);
        return workflowDistributionService.deleteDistributionByID(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_distribution_by_id")
    public Response queryDistributionById(HttpServletRequest request) {
        super.pageLoad(request);
        return workflowDistributionService.queryDistributionById(parameterMap);
    }

    @SchneiderRequestMapping(value = "/modify_distribution")
    public Response modifyDistribution(HttpServletRequest request) {
        super.pageLoad(request);
        return workflowDistributionService.modifyDistribution(parameterMap);
    }
}
