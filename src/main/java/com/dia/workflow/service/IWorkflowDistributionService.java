package com.dia.workflow.service;

import com.starter.context.bean.Response;

import java.util.Map;

public interface IWorkflowDistributionService {
    Response initPage(Map<String, Object> parameterMap);

    Response queryWorkflow(Map<String, Object> parameterMap);

    Response saveDistribution(Map<String, Object> parameterMap);

    Response queryReport1(Map<String, Object> parameterMap);

    Response deleteDistributionByID(Map<String, Object> parameterMap);

    Response queryDistributionById(Map<String, Object> parameterMap);

    Response modifyDistribution(Map<String, Object> parameterMap);

    Response queryReport1Details(Map<String, Object> parameterMap);
}
