package com.dia.workflow.service;

import com.starter.context.bean.Response;

import java.util.Map;

public interface IWorkflowMaintenanceService {


    Response queryWorkflowList(Map<String, Object> parameterMap, String userid);

    Response initPage(String userid);

    Response createWorkflow(Map<String, Object> parameterMap);

    Response queryInfoById(Map<String, Object> parameterMap);

    Response queryColumnsBySql(Map<String, Object> parameterMap);

    Response modifyWorkflow(Map<String, Object> parameterMap);

    Response deleteWorkflow(Map<String, Object> parameterMap);

    Response runTest(Map<String, Object> parameterMap, String userid);

    Response queryUserMail(Map<String, Object> parameterMap);

    Response queryTableOutputList();

    Response queryTableColumns(Map<String, Object> parameterMap);

    Response queryTableDefaultValue(Map<String, Object> parameterMap);

    Response modifyWorkflowName(Map<String, Object> parameterMap, String userid);

    Response queryProcList();

    Response queryProcArgs(Map<String, Object> parameterMap);

    Response queryTableConf(Map<String, Object> parameterMap);

    Response formatSQL(Map<String, Object> parameterMap);

    Response queryJavaClientOutput(Map<String, Object> parameterMap);

    Response queryWorkflowOpts();

    Response testSharedDisk(Map<String, Object> parameterMap);
}
