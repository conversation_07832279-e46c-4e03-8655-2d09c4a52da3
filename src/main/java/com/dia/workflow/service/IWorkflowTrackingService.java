package com.dia.workflow.service;

import com.starter.context.bean.Response;

import java.util.Map;

public interface IWorkflowTrackingService {

    Response queryReport1(Map<String, Object> parameterMap);

    Response queryReport2(Map<String, Object> parameterMap);

    Response queryReport3(Map<String, Object> parameterMap);

    Response queryReport4(Map<String, Object> parameterMap);

    Response queryReport5(Map<String, Object> parameterMap);

    Response queryReport6(Map<String, Object> parameterMap);
}
