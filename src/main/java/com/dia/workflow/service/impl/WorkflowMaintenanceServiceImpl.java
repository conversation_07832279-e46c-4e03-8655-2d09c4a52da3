package com.dia.workflow.service.impl;

import com.alibaba.druid.sql.SQLUtils;
import com.alibaba.druid.util.JdbcConstants;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dia.workflow.bean.SharedDiskTestParams;
import com.dia.workflow.bean.WorkflowTestParams;
import com.dia.workflow.dao.IWorkflowMaintenanceDao;
import com.dia.workflow.engine.javaclient.ByteCodeOutputFile;
import com.dia.workflow.engine.javaclient.ClassOutputFileManager;
import com.dia.workflow.engine.javaclient.InMemoryClassLoader;
import com.dia.workflow.engine.javaclient.JavaSourceFromString;
import com.dia.workflow.engine.log.WorkflowLogDespatcher;
import com.dia.workflow.feign.WorkflowEngineFeignClient;
import com.dia.workflow.service.IWorkflowMaintenanceService;
import com.starter.context.bean.Response;
import com.starter.context.configuration.MqttConfiguration;
import com.starter.context.configuration.database.DatabaseType;
import com.starter.context.configuration.database.DynamicDataSource;
import com.starter.context.configuration.database.TargetDataSource;
import com.starter.utils.Utils;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.tools.*;
import java.nio.charset.StandardCharsets;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.util.*;


@Service
@Scope("prototype")
@Transactional
public class WorkflowMaintenanceServiceImpl implements IWorkflowMaintenanceService {

    @Resource
    private Response response;

    @Resource
    private IWorkflowMaintenanceDao workflowDao;

    @Resource
    private WorkflowEngineFeignClient workflowEngineFeignClient;

    @Resource
    private DynamicDataSource dynamicDataSource;

    public final static String PARENT_CODE = "menuC11";

    private static boolean TESTING = false;

    @Override
    public Response queryWorkflowList(Map<String, Object> parameterMap, String userid) {
        String authType = StringUtils.upperCase(StringUtils.trim(workflowDao.queryAuthDetails(userid, PARENT_CODE)));
        return response.setBody(Utils.parseTreeNodes(workflowDao.queryWorkflowList(userid, authType)));
    }

    @Override
    public Response initPage(String userid) {
        Map<String, Object> parameterMap = new HashMap<>();
        String authType = StringUtils.upperCase(StringUtils.trim(workflowDao.queryAuthDetails(userid, PARENT_CODE)));
        parameterMap.put("authType", authType);
        parameterMap.put("existsGroup", workflowDao.initExistsGroup(userid, authType));
        parameterMap.put("ownerOpts", workflowDao.queryOwnerOpts(userid, authType));
        return response.setBody(parameterMap);
    }

    @Override
    public Response createWorkflow(Map<String, Object> parameterMap) {
        parameterMap.put("id", Utils.randomStr(12));
        workflowDao.createWorkflow(parameterMap);
        return response;
    }

    @Override
    public Response queryInfoById(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, Object> info = workflowDao.queryInfoById(parameterMap);
        resultMap.put("info", info);
        resultMap.put("config", new JSONObject());

        if (info == null) {
            return response.setBody(resultMap);
        }

        String config = (String) info.remove("CONFIG");
        if (StringUtils.isNotBlank(config)) {
            resultMap.put("config", JSON.parseObject(config));
        }

        String params = (String) info.remove("PARAMS");
        if (StringUtils.isNotBlank(params)) {
            info.put("PARAMS", JSON.toJSONString(JSON.parseObject(params), true));
        } else {
            info.put("PARAMS", "{\r\n\t\r\n}");
        }
        return response.setBody(resultMap);
    }

    @Override
    @TargetDataSource(DatabaseType.SCP02_READONLY)
    public Response queryColumnsBySql(Map<String, Object> parameterMap) {
        String sql = "select * from (" + parameterMap.get("sql") + ") tt where 1 = 0";
        JSONArray singles = (JSONArray) parameterMap.get("singles");

        for (Object element : singles) {
            JSONObject e = (JSONObject) element;
            String label = e.getString("label");
            String value = e.getString("value");
            // Grouping传递过来的value是字段名, 为了减少报错, 将value替换为0, 否则很多sql无法通过预编译
            if (StringUtils.endsWith(label, "_GROUPING")) {
                value = "0";
            }
            sql = sql.replace("{" + label + "}", value);
        }

        List<String> headers = new ArrayList<>();
        try (Connection con = dynamicDataSource.getConnection()) {
            ResultSet rs = con.prepareStatement(sql).executeQuery();

            ResultSetMetaData rsmd = rs.getMetaData();

            for (int i = 1; i <= rsmd.getColumnCount(); i++) {
                headers.add(rsmd.getColumnLabel(i));
            }

            rs.close();
            return response.setBody(headers);
        } catch (Exception e) {
            return response.setBody("[Exception] \r\n" + Utils.getExceptionMessage(e));
        }
    }

    @Override
    public Response modifyWorkflow(Map<String, Object> parameterMap) {
        workflowDao.modifyWorkflow(parameterMap);
        return response;
    }

    @Override
    public Response deleteWorkflow(Map<String, Object> parameterMap) {
        workflowDao.deleteWorkflow(parameterMap);
        return response;
    }

    @Override
    public Response runTest(Map<String, Object> parameterMap, String userid) {
        if (TESTING == true) {
            return response.setBody("-1");
        } else {
            TESTING = true;
            try {
                String topic = (String) parameterMap.get("topic");
                String id = (String) parameterMap.get("id");
                WorkflowTestParams params = new WorkflowTestParams();
                params.setUserid(userid);
                params.setId(id);
                params.setTopic(topic);
                workflowEngineFeignClient.runTest(params);
            } catch (Exception e) {
                e.printStackTrace();
                MqttConfiguration.publishMessage((String) parameterMap.get("topic"), Utils.getExceptionMessage(e));
            } finally {
                TESTING = false;
            }
            return response.setBody("0");
        }
    }

    @Override
    public Response queryUserMail(Map<String, Object> parameterMap) {
        return response.setBody(workflowDao.queryUserMail());
    }

    @Override
    public Response queryTableOutputList() {
        return response.setBody(workflowDao.queryTableOutputList());
    }

    @Override
    public Response queryTableColumns(Map<String, Object> parameterMap) {
        return response.setBody(workflowDao.queryTableColumns(parameterMap));
    }

    @Override
    public Response queryTableDefaultValue(Map<String, Object> parameterMap) {
        List<Map<String, String>> dataList = workflowDao.queryColumnDefaultValues(parameterMap);
        Map<String, List<String>> columnDefaultValues = new HashMap<>();

        for (Map<String, String> map : dataList) {
            String key = map.get("PROPERTY2");
            List<String> list = columnDefaultValues.computeIfAbsent(key, k -> new ArrayList<>());
            list.add(map.get("PROPERTY3"));
        }
        return response.setBody(columnDefaultValues);
    }

    @Override
    public Response modifyWorkflowName(Map<String, Object> parameterMap, String userid) {
        String authType = StringUtils.upperCase(StringUtils.trim(workflowDao.queryAuthDetails(userid, PARENT_CODE)));
        parameterMap.put("authType", authType);
        workflowDao.modifyWorkflowName(parameterMap);
        return response;
    }

    @Override
    public Response queryProcList() {
        return response.setBody(workflowDao.queryProcList());
    }

    @Override
    public Response queryProcArgs(Map<String, Object> parameterMap) {
        return response.setBody(workflowDao.queryProcArgs(parameterMap));
    }

    @Override
    public Response queryTableConf(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("tables", workflowDao.queryTables());
        resultMap.put("cols", workflowDao.queryTableCols());
        return response.setBody(resultMap);
    }

    @Override
    public Response formatSQL(Map<String, Object> parameterMap) {
        String sql = (String) parameterMap.get("scripts");
        sql = SQLUtils.format(sql, JdbcConstants.ORACLE);
        return response.setBody(sql);
    }

    @Override
    public Response queryJavaClientOutput(Map<String, Object> parameterMap) {
        String java = (String) parameterMap.get("java");
        WorkflowLogDespatcher logger = new WorkflowLogDespatcher();
        Map<String, ByteCodeOutputFile> byteStreams = new HashMap<>();

        // 获取Java编译器
        JavaCompiler compiler = ToolProvider.getSystemJavaCompiler();
        JavaFileObject file = new JavaSourceFromString("JavaClient", java);

        // 为编译任务设置一个诊断监听器来收集编译错误信息
        DiagnosticCollector<JavaFileObject> diagnostics = new DiagnosticCollector<>();

        // 编译源代码
        Iterable<String> options = Arrays.asList("-classpath", System.getProperty("java.class.path"));
        Iterable<? extends JavaFileObject> compilationUnits = List.of(file);
        JavaFileManager fileManager = new ClassOutputFileManager(compiler.getStandardFileManager(diagnostics, null, StandardCharsets.UTF_8), byteStreams);
        JavaCompiler.CompilationTask task = compiler.getTask(null, fileManager, diagnostics, options, null, compilationUnits);

        boolean success = task.call();

        // 打印所有编译警告和错误
        StringBuilder warning = new StringBuilder();
        for (Diagnostic<? extends JavaFileObject> diagnostic : diagnostics.getDiagnostics()) {
            warning.append(diagnostic);
        }

        // 如果编译成功
        if (success) {
            InMemoryClassLoader classLoader = new InMemoryClassLoader(byteStreams);
            Map<String, Object> singleValues = new HashMap<>();
            List<LinkedHashMap<String, Object>> tableValues = new ArrayList<>();
            try {
                Class<?> clazz = classLoader.loadClass("JavaClient");
                clazz.getMethod("_process", Map.class, List.class, logger.getClass())
                        .invoke(clazz.getDeclaredConstructor().newInstance(), singleValues, tableValues, logger);

                Map<String, Object> result = new HashMap<>();
                result.put("singles", new ArrayList<>(singleValues.keySet()));
                if (tableValues.isEmpty() == false) {
                    LinkedHashMap<String, Object> firstElement = tableValues.get(0);
                    result.put("lists", new ArrayList<>(firstElement.keySet()));
                }
                response.setBody(result);
            } catch (Exception ex) {
                response.setBody(ex.getMessage());
            }
        } else {
            response.setBody(warning.toString());
        }
        return response;
    }

    @Override
    public Response queryWorkflowOpts() {
        return response.setBody(workflowDao.queryWorkflowOpts());
    }

    @Override
    public Response testSharedDisk(Map<String, Object> parameterMap) {
        SharedDiskTestParams params = new SharedDiskTestParams();
        params.setPath((String) parameterMap.get("path"));
        params.setDomain((String) parameterMap.get("domain"));
        params.setUsername((String) parameterMap.get("username"));
        params.setPassword((String) parameterMap.get("password"));
        return response.setBody(workflowEngineFeignClient.testSharedDisk(params));
    }
}
