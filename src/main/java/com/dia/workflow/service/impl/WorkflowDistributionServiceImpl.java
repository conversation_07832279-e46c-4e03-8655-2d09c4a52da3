package com.dia.workflow.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.dia.workflow.dao.IWorkflowDistributionDao;
import com.dia.workflow.service.IWorkflowDistributionService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.utils.Utils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
@Scope("prototype")
@Transactional
public class WorkflowDistributionServiceImpl implements IWorkflowDistributionService {

    @Resource
    private Response response;

    @Resource
    private IWorkflowDistributionDao workflowDistributionDao;


    public final static String PARENT_CODE = "menuC12";

    @Override
    public Response initPage(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("workflowOpts", workflowDistributionDao.queryWorkflowList());
        return response.setBody(resultMap);
    }

    @Override
    public Response queryWorkflow(Map<String, Object> parameterMap) {
        return response.setBody(workflowDistributionDao.queryWorkflowList());
    }

    @Override
    public Response saveDistribution(Map<String, Object> parameterMap) {
        parameterMap.put("id", Utils.randomStr(16));
        parameterMap.put("month", JSON.toJSONString(parameterMap.get("month")));
        parameterMap.put("day", JSON.toJSONString(parameterMap.get("day")));
        parameterMap.put("hour", JSON.toJSONString(parameterMap.get("hour")));
        workflowDistributionDao.saveDistribution(parameterMap);
        return response;
    }

    @Override
    public Response queryReport1(Map<String, Object> parameterMap) {
        List<Map<String, Object>> dataList = workflowDistributionDao.queryReport1(parameterMap);
        Map<String, List<Map<String, Object>>> resultMap = new HashMap<>();
        for (Map<String, Object> map : dataList) {
            map.put("MONTH", StringUtils.join((JSON.parseArray((String) map.get("MONTH"))), ","));
            map.put("HOUR", StringUtils.join((JSON.parseArray((String) map.get("HOUR"))), ","));
            String dayType = (String) map.get("DAY_TYPE");
            if ("DAY_OF_WEEK".equals(dayType)) {
                List<String> days = (JSON.parseArray((String) map.get("DAY"))).toJavaList(String.class);
                List<String> dayNames = new ArrayList<>();
                for (String day : days) {
                    dayNames.add(Utils.convertDayName(day));
                }
                map.put("DAY", StringUtils.join(dayNames, ","));
            } else if ("DAY_OF_MONTH".equals(dayType)) {
                map.put("DAY", StringUtils.join((JSON.parseArray((String) map.get("DAY"))), ","));
            } else if ("WORKING_DAY".equals(dayType)) {
                map.put("DAY", "WD");
            }
            String groups = (String) map.get("GROUPS");
            if (groups.contains(".") == true) {
                groups = StringUtils.substring(groups, 0, StringUtils.indexOf(groups, "."));
            }
            List<Map<String, Object>> list = resultMap.computeIfAbsent(groups, k -> new ArrayList<>());
            list.add(map);
        }
        return response.setBody(resultMap);
    }

    @Override
    public Response queryReport1Details(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(workflowDistributionDao.queryReport1DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(workflowDistributionDao.queryReport1Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public Response deleteDistributionByID(Map<String, Object> parameterMap) {
        workflowDistributionDao.deleteDistributionByID(parameterMap);
        return response;
    }

    @Override
    public Response queryDistributionById(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = workflowDistributionDao.queryDistributionById(parameterMap);
        return response.setBody(resultMap);
    }

    @Override
    public Response modifyDistribution(Map<String, Object> parameterMap) {
        parameterMap.put("month", JSON.toJSONString(parameterMap.get("month")));
        parameterMap.put("day", JSON.toJSONString(parameterMap.get("day")));
        parameterMap.put("hour", JSON.toJSONString(parameterMap.get("hour")));
        workflowDistributionDao.modifyDistribution(parameterMap);
        return response;
    }
}
