package com.dia.workflow.service.impl;

import com.dia.workflow.dao.IWorkflowTrackingDao;
import com.dia.workflow.service.IWorkflowTrackingService;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.utils.Utils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;


@Service
@Scope("prototype")
@Transactional
public class WorkflowTrackingServiceImpl implements IWorkflowTrackingService {

    @Resource
    private Response response;

    @Resource
    private IWorkflowTrackingDao workflowTrackingDao;


    public final static String PARENT_CODE = "menuC13";

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":5m")
    public Response queryReport1(Map<String, Object> parameterMap) {
        List<Map<String, Object>> dataList = workflowTrackingDao.queryReport1(parameterMap);
        Map<String, Object> resultMap = new HashMap<>();
        for (Map<String, Object> map : dataList) {
            resultMap.put((String) map.get("KEY"), map.get("VAL"));
        }
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":5m")
    public Response queryReport2(Map<String, Object> parameterMap) {
        return response.setBody(workflowTrackingDao.queryReport2(parameterMap));
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":5m")
    public Response queryReport3(Map<String, Object> parameterMap) {
        List<Map<String, Object>> dataList = workflowTrackingDao.queryReport3(parameterMap);
        Map<String, List<Object>> resultMap = new HashMap<>();
        List<Object> xAxis = new ArrayList<>();
        List<Object> yAxis1 = new ArrayList<>();
        List<Object> yAxis2 = new ArrayList<>();
        resultMap.put("xAxis", xAxis);
        resultMap.put("yAxis1", yAxis1);
        resultMap.put("yAxis2", yAxis2);

        for (Map<String, Object> map : dataList) {
            xAxis.add(map.get("xAxis"));
            yAxis1.add(map.get("yAxis1"));
            yAxis2.add(map.get("yAxis2"));
        }
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":5m")
    public Response queryReport4(Map<String, Object> parameterMap) {
        List<Map<String, Object>> dataList = workflowTrackingDao.queryReport4(parameterMap);
        int maxValue = 0;
        for (Map<String, Object> map : dataList) {
            maxValue = Math.max(Utils.parseInt(map.get("CNT")), maxValue);
        }
        BigDecimal maxValueDecimal = BigDecimal.valueOf(maxValue);
        List<List<Object>> resultList = new ArrayList<>();
        for (Map<String, Object> map : dataList) {
            List<Object> list = new ArrayList<>();
            list.add(map.get("LABEL"));
            list.add(map.get("CNT"));
            if (maxValue == 0) {
                list.add(0);
            } else {
                BigDecimal value = Utils.parseBigDecimal(map.get("CNT"));
                list.add(value.divide(maxValueDecimal, 3, RoundingMode.HALF_UP).doubleValue());
            }
            resultList.add(list);
        }
        return response.setBody(resultList);
    }

    @Override
    public Response queryReport5(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(workflowTrackingDao.queryReport5Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(workflowTrackingDao.queryReport5(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public Response queryReport6(Map<String, Object> parameterMap) {
        List<Map<String, Object>> dataList = workflowTrackingDao.queryReport6(parameterMap);
        Map<String, List<Object>> resultMap = new HashMap<>();
        List<Object> yAxis = new ArrayList<>();
        List<Object> yAxisTips = new ArrayList<>();
        List<Object> xAxis = new ArrayList<>();
        resultMap.put("yAxis", yAxis);
        resultMap.put("xAxis", xAxis);
        resultMap.put("yAxisTips", yAxisTips);
        for (Map<String, Object> map : dataList) {
            yAxis.add(map.get("USERNAME"));
            xAxis.add(map.get("CNT"));
            yAxisTips.add(map.get("USERID"));
        }
        return response.setBody(resultMap);
    }
}
