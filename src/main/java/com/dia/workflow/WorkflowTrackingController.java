package com.dia.workflow;

import com.dia.workflow.service.IWorkflowTrackingService;
import com.dia.workflow.service.impl.WorkflowTrackingServiceImpl;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/diagnosis/workflow/tracking", parent = WorkflowTrackingServiceImpl.PARENT_CODE)
public class WorkflowTrackingController extends ControllerHelper {

    @Resource
    private IWorkflowTrackingService workflowTrackingService;

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return workflowTrackingService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping("/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return workflowTrackingService.queryReport2(parameterMap);
    }

    @SchneiderRequestMapping("/query_report3")
    public Response queryReport3(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return workflowTrackingService.queryReport3(parameterMap);
    }

    @SchneiderRequestMapping("/query_report4")
    public Response queryReport4(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return workflowTrackingService.queryReport4(parameterMap);
    }

    @SchneiderRequestMapping("/query_report5")
    public Response queryReport5(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return workflowTrackingService.queryReport5(parameterMap);
    }

    @SchneiderRequestMapping("/query_report6")
    public Response queryReport6(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return workflowTrackingService.queryReport6(parameterMap);
    }
}
