package com.dia.workflow.feign;

import com.dia.workflow.bean.SharedDiskTestParams;
import com.dia.workflow.bean.WorkflowTestParams;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(name = "workflow-engine", url = WorkflowEngineFeignClient.WORKFLOW_ENGINE_URL)
public interface WorkflowEngineFeignClient {
    // String WORKFLOW_ENGINE_URL = "http://127.0.0.1:8083/workflow/engine";

    String WORKFLOW_ENGINE_URL = "http://10.177.21.9:8083/workflow/engine";

    @PostMapping(value = "/run_test")
    void runTest(WorkflowTestParams params);

    @PostMapping(value = "/test_shared_disk")
    String testSharedDisk(SharedDiskTestParams params);
}
