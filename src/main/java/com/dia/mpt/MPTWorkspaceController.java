package com.dia.mpt;

import com.dia.mpt.service.IMPTWorkspaceService;
import com.dia.mpt.service.impl.MPTWorkspaceServiceImpl;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;

import java.util.Map;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/diagnosis/my_planning_today/mpt_workspace", parent = MPTWorkspaceServiceImpl.PARENT_CODE)
public class MPTWorkspaceController extends ControllerHelper {
    @Resource
    private IMPTWorkspaceService mptWorkspaceService;

    @SchneiderRequestMapping(value = "/init_new_tickets")
    public Response initNewTickets(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return mptWorkspaceService.initNewTickets(parameterMap, session);
    }

    @SuppressWarnings("unchecked")
    @SchneiderRequestMapping(value = "/init_view_tickets")
    public Response initViewTickets(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        Response response = mptWorkspaceService.initNewTickets(parameterMap, session);
        ((Map<String, Object>) response.getBody()).putAll(mptWorkspaceService.initViewTickets(parameterMap, session));
        return response;
    }

    @SchneiderRequestMapping(value = "/query_subject_opts")
    public Response querySubjectOpts(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return mptWorkspaceService.querySubjectOpts(parameterMap);
    }


    @SchneiderRequestMapping("/save_new_ticket")
    public void saveNewTicket(HttpServletRequest request) {
        super.pageLoad(request);
        mptWorkspaceService.saveNewTicket(parameterMap, session);
    }

    @SchneiderRequestMapping(value = "/query_tickets_by_status")
    public Response queryTicketsByStatus(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return mptWorkspaceService.queryTicketsByStatus(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_ticket_by_id")
    public Response queryTicketById(HttpServletRequest request) {
        super.pageLoad(request);
        return mptWorkspaceService.queryTicketById(parameterMap, session);
    }

    @SchneiderRequestMapping(value = "/save_ticket_comment")
    public Response saveTicketComment(HttpServletRequest request) {
        super.pageLoad(request);
        return mptWorkspaceService.saveTicketComment(parameterMap, session);
    }

    @SchneiderRequestMapping(value = "/delete_ticket_comment")
    public Response deleteTicketComment(HttpServletRequest request) {
        super.pageLoad(request);
        return mptWorkspaceService.deleteTicketComment(parameterMap, session);
    }

    @SchneiderRequestMapping(value = "/approve_ticket_by_id")
    public Response approveTicketById(HttpServletRequest request) {
        super.pageLoad(request);
        return mptWorkspaceService.approveTicketById(parameterMap, session);
    }

    @SchneiderRequestMapping(value = "/close_ticket_by_id")
    public Response closeTicketById(HttpServletRequest request) {
        super.pageLoad(request);
        return mptWorkspaceService.closeTicketById(parameterMap, session);
    }

    @SchneiderRequestMapping(value = "/update_collaborators")
    public Response updateCollaborators(HttpServletRequest request) {
        super.pageLoad(request);
        return mptWorkspaceService.updateCollaborators(parameterMap, session);
    }

    @SchneiderRequestMapping(value = "/update_action_period")
    public Response updateActionPeriod(HttpServletRequest request) {
        super.pageLoad(request);
        return mptWorkspaceService.updateActionPeriod(parameterMap, session);
    }

    @SchneiderRequestMapping(value = "/update_action_owner")
    public Response updateActionOwner(HttpServletRequest request) {
        super.pageLoad(request);
        return mptWorkspaceService.updateActionOwner(parameterMap, session);
    }

    @SchneiderRequestMapping(value = "/query_quick_access")
    public Response queryQuickAccess(HttpServletRequest request) {
        super.pageLoad(request);
        return mptWorkspaceService.queryQuickAccess(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_latest_news")
    public Response queryLatestNews(HttpServletRequest request) {
        super.pageLoad(request);
        return mptWorkspaceService.queryLatestNews(parameterMap);
    }
}
