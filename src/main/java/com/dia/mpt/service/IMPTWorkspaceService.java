package com.dia.mpt.service;

import com.starter.context.bean.Response;
import com.starter.login.bean.Session;

import java.util.Map;

public interface IMPTWorkspaceService {
    Response initNewTickets(Map<String, Object> parameterMap, Session session);

    Map<String, Object> initViewTickets(Map<String, Object> parameterMap, Session session);

    void saveNewTicket(Map<String, Object> parameterMap, Session session);

    Response queryTicketsByStatus(Map<String, Object> parameterMap);

    Response queryTicketById(Map<String, Object> parameterMap, Session session);

    Response saveTicketComment(Map<String, Object> parameterMap, Session session);

    Response deleteTicketComment(Map<String, Object> parameterMap, Session session);

    Response approveTicketById(Map<String, Object> parameterMap, Session session);

    Response closeTicketById(Map<String, Object> parameterMap, Session session);

    Response updateCollaborators(Map<String, Object> parameterMap, Session session);

    Response queryQuickAccess(Map<String, Object> parameterMap);

    Response queryLatestNews(Map<String, Object> parameterMap);

    Response querySubjectOpts(Map<String, Object> parameterMap);

    Response updateActionPeriod(Map<String, Object> parameterMap, Session session);

    Response updateActionOwner(Map<String, Object> parameterMap, Session session);
}
