package com.dia.mpt.service;

import com.starter.context.bean.Response;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;

public interface IFeedbackService {

    Response queryFilters(Map<String, Object> parameterMap, String userid);

    Response queryPrivileges(Map<String, Object> parameterMap, String userid);

    Response queryReport1(Map<String, Object> parameterMap, String userid);

    void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse response, String userid);

//    Response queryReport2(Map<String, Object> parameterMap);
//
//    Response queryReport2Subjects(Map<String, Object> parameterMap);

    Response queryReport3(Map<String, Object> parameterMap, String userid);

    Response queryReport3Details(Map<String, Object> parameterMap, String userid);

    void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse response, String userid);
}
