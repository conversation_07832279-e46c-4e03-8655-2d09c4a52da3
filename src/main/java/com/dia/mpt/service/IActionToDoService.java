package com.dia.mpt.service;

import com.starter.context.bean.Response;
import jakarta.servlet.http.HttpServletResponse;

import java.util.Map;

public interface IActionToDoService {

    Response queryFilters();
    Response queryReport1(Map<String, Object> parameterMap, String userid);
    Response queryReport1Sub(Map<String, Object> parameterMap, String userid);
    Response queryReport1Details(Map<String, Object> parameterMap, String userid);
    void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse response, String userid);
    Response queryReport2(Map<String, Object> parameterMap);
    Response queryReport3(Map<String, Object> parameterMap, String userid);
    Response queryReport3Details(Map<String, Object> parameterMap, String userid);
    void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse response, String userid);
    void downloadAllSubjects(Map<String, Object> parameterMap, HttpServletResponse response);
    Response queryReport4(Map<String, Object> parameterMap);
    Response queryReport4Details(Map<String, Object> parameterMap);
    void downloadReport4Details(Map<String, Object> parameterMap, HttpServletResponse response);
}
