package com.dia.mpt.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.dia.mpt.bean.FeedbackReport3Bean;
import com.dia.mpt.dao.IFeedbackDao;
import com.dia.mpt.service.IFeedbackService;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Scope("prototype")
@Transactional
public class FeedbackServiceImpl implements IFeedbackService {

    public final static String PARENT_CODE = "menuC02";
    @Resource
    private ExcelTemplate excelTemplate;

    @Resource
    private IFeedbackDao feedbackDao;

    @Resource
    private Response response;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryFilters(Map<String, Object> parameterMap, String userid) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cascader", Utils.parseCascader(feedbackDao.queryFilters()));
        resultMap.put("pendingCascader", Utils.parseCascader(feedbackDao.queryPendingFilters()));
        return response.setBody(resultMap);
    }

    @Override
    public Response queryPrivileges(Map<String, Object> parameterMap, String userid) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("isAdmin", feedbackDao.queryAdminCnt(PARENT_CODE, userid) > 0);
        return response.setBody(resultMap);
    }

    @Override
    public Response queryReport1(Map<String, Object> parameterMap, String userid) {
        this.generateFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        parameterMap.put("isAdmin", feedbackDao.queryAdminCnt(PARENT_CODE, userid) > 0);
        page.setTotal(feedbackDao.queryReport1Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(feedbackDao.queryReport1(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse response, String userid) {
        this.generateFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        parameterMap.put("isAdmin", feedbackDao.queryAdminCnt(PARENT_CODE, userid) > 0);

        String fileName = "feedback_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.dia.mpt.dao.IFeedbackDao.queryReport1", parameterMap);
    }

//    @Override
//    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
//    public Response queryReport2Subjects(Map<String, Object> parameterMap) {
//        this.generateFilter(parameterMap);
//        return response.setBody(feedbackDao.queryReport2Subjects(parameterMap));
//    }
//
//    @Override
//    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
//    public Response queryReport2(Map<String, Object> parameterMap) {
//        this.generateFilter(parameterMap);
//        List<LinkedHashMap<String, Object>> dataList = feedbackDao.queryReport2(parameterMap);
//        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
//        for (LinkedHashMap<String, Object> map : dataList) {
//            LinkedHashMap<String, Object> temp = new LinkedHashMap<>();
//            Map<String, Map<String, Object>> info = new HashMap<>();
//            for (String key : map.keySet()) {
//                if (key.contains("'") == false) {
//                    temp.put(key, map.get(key));
//                } else {
//                    String subject = key.substring(key.indexOf("'") + 1, key.lastIndexOf("'"));
//                    if (key.contains("_OPEN_LINES") == true) {
//                        Map<String, Object> m = info.computeIfAbsent(subject, k -> new HashMap<>());
//                        m.put("OPEN", map.get(key));
//                    } else if (key.contains("_SOLVED") == true) {
//                        Map<String, Object> m = info.computeIfAbsent(subject, k -> new HashMap<>());
//                        m.put("SOLVED", map.get(key));
//                    } else if (key.contains("_ADDED") == true) {
//                        Map<String, Object> m = info.computeIfAbsent(subject, k -> new HashMap<>());
//                        m.put("ADDED", map.get(key));
//                    }
//                }
//            }
//
//            for (String key : info.keySet()) {
//                Map<String, Object> m = info.get(key);
//                // 当用户名下没有任何数据时, 不操作
//                if (m.get("OPEN") == null && m.get("SOLVED") == null && m.get("ADDED") == null) {
//                    continue;
//                }
//                temp.put(key, JSON.toJSONString(info.get(key)));
//            }
//            resultList.add(temp);
//        }
//        return response.setBody(resultList);
//    }

    private void generateFilter(Map<String, Object> parameterMap) {
        JSONArray categoryArray = (JSONArray) parameterMap.get("filterList");
        if (categoryArray != null) {
            Map<String, List<String>> filterMap = new HashMap<>();

            for (Object subObj : categoryArray) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = subArray.getString(0);

                String value = subArray.getString(1);
                String key = Utils.randomStr(8);

                if (Utils.hasInjectionAttack(columnName)) {
                    continue;
                }

                List<String> fl = filterMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                fl.add("#{" + key + ",jdbcType=VARCHAR}");
                parameterMap.put(key, value);
            }

            List<String> filterList = new ArrayList<>();

            for (String key : filterMap.keySet()) {
                List<String> fl = filterMap.get(key);
                filterList.add("t." + key + " in (" + StringUtils.join(fl, ",") + ")");
            }

            parameterMap.put("filters", StringUtils.join(filterList, " and "));
        }

        JSONArray selectedColumn = (JSONArray) parameterMap.get("report1SelectedColumn");
        if (selectedColumn.isEmpty()) {
            selectedColumn.add("OWNER");
            selectedColumn.add("OWNER_NAME");
            selectedColumn.add("ORGANIZATION");
            selectedColumn.add("CATEGORY");
            selectedColumn.add("SUB_CATEGORY");
            selectedColumn.add("SUBJECT");
            parameterMap.put("report1SelectedColumn", selectedColumn);
        }

//        JSONArray selectedColumn2 = (JSONArray) parameterMap.get("report2SelectedColumn");
//        if (selectedColumn2.isEmpty()) {
//            selectedColumn2.add("OWNER");
//            selectedColumn2.add("OWNER_NAME");
//            parameterMap.put("report2SelectedColumn", selectedColumn2);
//        }
    }

    private void generatePendingFilter(Map<String, Object> parameterMap) {
        JSONArray categoryArray = (JSONArray) parameterMap.get("pendingFilterList");
        if (categoryArray != null) {
            Map<String, List<String>> filterMap = new HashMap<>();

            for (Object subObj : categoryArray) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = subArray.getString(0);

                String value = subArray.getString(1);
                String key = Utils.randomStr(8);

                if (Utils.hasInjectionAttack(columnName)) {
                    continue;
                }

                List<String> fl = filterMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                fl.add("#{" + key + ",jdbcType=VARCHAR}");
                parameterMap.put(key, value);
            }

            List<String> filterList = new ArrayList<>();

            for (String key : filterMap.keySet()) {
                List<String> fl = filterMap.get(key);
                filterList.add("t." + key + " in (" + StringUtils.join(fl, ",") + ")");
            }

            parameterMap.put("pendingFilters", StringUtils.join(filterList, " and "));
        }

        String selectedColumn = (String) parameterMap.get("report3SelectedColumn");
        selectedColumn = selectedColumn.isEmpty() ? "TOTAL_PENDING_DAYS_CD_RNAGE" : selectedColumn;
        parameterMap.put("report3SelectedColumn", selectedColumn);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3(Map<String, Object> parameterMap, String userid) {
        this.generatePendingFilter(parameterMap);

        List<FeedbackReport3Bean> dataList;
        dataList = feedbackDao.queryReport3(parameterMap);

        Map<String, BigDecimal> dataMap = new HashMap<>();
        LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();
        Map<String, String> xAxisMap = new HashMap<>();

        List<String> legend = feedbackDao.queryReport3Legend(parameterMap);

        for (FeedbackReport3Bean data : dataList) {
            dataMap.put(data.getKey(), data.getVALUE());
            xAxisMap.put(data.getCALENDAR_DATE(), "");
        }
        List<String> xAxisList = xAxisMap.keySet().stream().sorted(String::compareTo).collect(Collectors.toList());
        for (String l : legend) {
            List<BigDecimal> temp = new ArrayList<>();
            for (String x : xAxisList) {
                temp.add(dataMap.getOrDefault(l + "#" + x, BigDecimal.ZERO));
            }
            resultMap.put(l, temp);
        }
        resultMap.put("xAxis", xAxisList);
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Details(Map<String, Object> parameterMap, String userid) {
        this.generatePendingFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(feedbackDao.queryReport3DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(feedbackDao.queryReport3Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse res, String userid) {
        this.generatePendingFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "mpt_pending_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.dia.mpt.dao.IFeedbackDao.queryReport3Details", parameterMap);
    }
}
