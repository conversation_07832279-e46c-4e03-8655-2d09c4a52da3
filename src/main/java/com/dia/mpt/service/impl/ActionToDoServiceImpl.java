package com.dia.mpt.service.impl;

import com.adm.system.bean.CascaderBean;
import com.alibaba.fastjson.JSONArray;
import com.dia.mpt.bean.*;
import com.dia.mpt.dao.IActionToDoServiceDao;
import com.dia.mpt.service.IActionToDoService;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.login.bean.Session;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import com.starter.utils.excel.SheetInfoWithQueryKey;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/*
 *  MyPlanningTodayFeedback逻辑
 *  1. 系统中有Access, View, Download三种计数方式, 分别代表打开主页面, 查看明细和下载明细时计数
 *  2. Access计数逻辑
 *  - 用户打开主页面, 点击左侧箭头, 展开二级菜单时计数
 *  - 在展开菜单中的Subject会被记录, 不在的不会记录, 比如用户有10个Subject, 但是他只展开过其中3个Subject, 那么只会记录3个
 *  - 计数数量等于用户当前Open的数量
 *  3. View计数逻辑
 *  - 用户右键Sum of Errors进行View Details时计数
 *  - 计数数量等于当前浏览页包含和自己相关的数据行数, 比如当前有10行, 但只有1行是自己的, 那么计数1行
 *  - 多次浏览计数会累加, 但是不会超过Open行数
 *  - 同一条记录被浏览多次, 将会产生多次计数, 因为系统无法对计数去重
 *  4. Download计数逻辑
 *  - 用户在Sum of Errors的View Details时Download All Results时计数
 *  - 系统无法获取文件内部信息, 所以只要用户下载某个Subject的数据, 就将该Subject下的Download计数设置为Open行数
 */
@Service
@Scope("prototype")
@Transactional
public class ActionToDoServiceImpl implements IActionToDoService {

    public final static String PARENT_CODE = "menuC01";
    @Resource
    private ExcelTemplate excelTemplate;

    @Resource
    private IActionToDoServiceDao actionToDoServiceDao;

    @Resource
    private Response response;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryFilters() {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cascader", Utils.parseCascader(actionToDoServiceDao.queryFilters()));
        resultMap.put("report4cascader", Utils.parseCascader(actionToDoServiceDao.queryReport4Filters()));

        return response.setBody(resultMap);
    }

    private void generateReport4Filter(Map<String, Object> parameterMap) {
        // 生成筛选条件
        JSONArray categoryArray = (JSONArray) parameterMap.get("report4FilterList");
        if (categoryArray != null) {
            Map<String, List<String>> filterMap = new HashMap<>();

            for (Object subObj : categoryArray) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = subArray.getString(0);

                String value = subArray.getString(1);
                String key = Utils.randomStr(8);

                if (Utils.hasInjectionAttack(columnName)) {
                    continue;
                }

                List<String> fl = filterMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                fl.add("#{" + key + ",jdbcType=VARCHAR}");
                parameterMap.put(key, value);
            }

            List<String> filterList = new ArrayList<>();

            for (String key : filterMap.keySet()) {
                List<String> fl = filterMap.get(key);
                filterList.add(key + " in (" + StringUtils.join(fl, ",") + ")");
            }

            parameterMap.put("report4Filters", StringUtils.join(filterList, " and "));
        }
    }

    private void generateTreePathFilter(Map<String, Object> parameterMap) {
        String selectedTreePath = (String) parameterMap.get("selectedTreePath");
        if (StringUtils.isNotBlank(selectedTreePath)) {
            List<String> conditions = new ArrayList<>();
            String[] treePaths = selectedTreePath.split(" > ");
            for (int i = 1; i <= Math.min(treePaths.length, 5); i++) {
                String key = Utils.randomStr(8);
                if ("Others".equals(StringUtils.trim(treePaths[i - 1]))) {
                    String name = this.getColumnName(parameterMap.get("level" + i));
                    conditions.add("(" + name + " = #{" + key + ",jdbcType=VARCHAR} or " + name + " is null )");
                } else {
                    conditions.add(this.getColumnName(parameterMap.get("level" + i)) + " = #{" + key + ",jdbcType=VARCHAR}");
                }
                parameterMap.put(key, StringUtils.trim(treePaths[i - 1]));
            }
            parameterMap.put("treePathFilter", "(" + StringUtils.join(conditions, " and ") + ")");
        }
    }

    private void generateFilter(Map<String, Object> parameterMap) {
        // 生成筛选条件
        JSONArray categoryArray = (JSONArray) parameterMap.get("filterList");
        if (categoryArray != null) {
            Map<String, List<String>> filterMap = new HashMap<>();

            for (Object subObj : categoryArray) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = subArray.getString(0);

                String value = subArray.getString(1);
                String key = Utils.randomStr(8);

                if (Utils.hasInjectionAttack(columnName)) {
                    continue;
                }

                List<String> fl = filterMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                fl.add("#{" + key + ",jdbcType=VARCHAR}");
                parameterMap.put(key, value);
            }

            List<String> filterList = new ArrayList<>();

            for (String key : filterMap.keySet()) {
                List<String> fl = filterMap.get(key);
                if ("ORGANIZATION".equalsIgnoreCase(key)) {
                    key = "ENTITY_NAME";
                }
                filterList.add(key + " in (" + StringUtils.join(fl, ",") + ")");
            }

            parameterMap.put("filters", StringUtils.join(filterList, " and "));
        }
        String report3ViewType = (String) parameterMap.get("report3ViewType");
        if ("ORGANIZATION".equalsIgnoreCase(report3ViewType)) {
            parameterMap.put("report3ViewType", "ENTITY_NAME");
        }
    }

    private String convertColumn(Object categoryObj) {
        String category = (String) categoryObj;

        return switch (category) {
            case "Workflow Id" -> "workflow_id";
            case "Category" -> "category";
            case "Sub Category" -> "sub_category";
            case "Owner Name" -> "USER_NAME";
            case "Owner Sesa" -> "SESA_CODE";
            case "Subject" -> "subject";
            case "Description" -> "description";
            case "Classification" -> "classification";
            case "Priority" -> "priority";
            case "Kpi Impacted" -> "kpi_impacted";
            case "Proposal Action" -> "proposal_action";
            case "Create By" -> "create_by$";
            default -> category;
        };
    }

    // ERROR_V里面使用的是OWNER_NAME, 但DETAILS_V中使用的是ACTION_OWNER和MATERIAL_OWNER
    // 我们并不想将ACTION_OWNER和MATERIAL_OWNER的概念传递给用户, 所以前台显示的依旧是OWNER, 只有在需要查询明细时, 才会转换为ACTION_OWNER
    private void renameOwnerToActionOwner(Map<String, Object> parameterMap) {
        JSONArray categoryArray = (JSONArray) parameterMap.get("filterList");
        JSONArray result = new JSONArray();
        if (categoryArray != null) {
            for (Object subObj : categoryArray) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = subArray.getString(0);
                String value = subArray.getString(1);

                if (StringUtils.startsWith(columnName, "OWNER_")) {
                    if ("Forward".equals(parameterMap.get("report1ViewType")) || "Escalation".equals(parameterMap.get("report1ViewType"))) {
                        columnName = StringUtils.replaceOnce(columnName, "OWNER_", "MATERIAL_OWNER_");
                    } else {
                        columnName = StringUtils.replaceOnce(columnName, "OWNER_", "ACTION_OWNER_");
                    }
                }
                JSONArray temp = new JSONArray();
                temp.add(columnName);
                temp.add(value);
                result.add(temp);
            }
        }
        parameterMap.put("filterList", result);
    }

    private void generateReport1Filter(Map<String, Object> parameterMap) {
        parameterMap.put("column1", this.convertColumn(parameterMap.get("category1Value")));
        String columnName2 = this.convertColumn(parameterMap.get("category2Value"));
        if (StringUtils.startsWith(columnName2, "OWNER_")) {
            if ("Forward".equals(parameterMap.get("report1ViewType")) || "Escalation".equals(parameterMap.get("report1ViewType"))) {
                columnName2 = StringUtils.replaceOnce(columnName2, "OWNER_", "MATERIAL_OWNER_");
            } else {
                columnName2 = StringUtils.replaceOnce(columnName2, "OWNER_", "ACTION_OWNER_");
            }
        }
        parameterMap.put("column2", columnName2);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap, String userid) {
        this.generateReport1Filter(parameterMap);
        this.generateFilter(parameterMap);
        return response.setBody(actionToDoServiceDao.queryReport1(parameterMap));
    }

    @Override
    public Response queryReport1Sub(Map<String, Object> parameterMap, String userid) {
        this.generateReport1Filter(parameterMap);
        this.generateFilter(parameterMap);
        List<Map<String, Object>> data = actionToDoServiceDao.queryReport1Sub(parameterMap);
        for (Map<String, Object> map : data) {
            map.put("DESCRIPTION", this.splitLineBySeq(map.get("DESCRIPTION")));
            map.put("PROPOSAL_ACTION", this.splitLineBySeq(map.get("PROPOSAL_ACTION")));
        }

        // 打开折叠菜单时, 记录用户Access Lines
        // 一定要确保category3是Subject, 否则会无法正常记录
        if (data.isEmpty() == false) {
            actionToDoServiceDao.updateFeedbackAccess(userid, data);
        }

        return response.setBody(data);
    }

    private String splitLineBySeq(Object proposalAction) {
        String str = (String) proposalAction;
        if (StringUtils.isNotBlank(str)) {
            for (int i = 2; i < 11; i++) {
                str = str.replace(i + ".", "<br/>" + i + ".");
            }
        }
        return str;
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1Details(Map<String, Object> parameterMap, String userid) {
        this.renameOwnerToActionOwner(parameterMap);
        this.generateReport1Filter(parameterMap);
        this.generateFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(actionToDoServiceDao.queryReport1DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(actionToDoServiceDao.queryReport1Details(parameterMap));

            // 右键访问Sum of Errors时, 记录用户View Lines
            // 一定要确保viewCategory2是Subject, 否则会无法正常记录
            // 只有看到自己的记录才会被计数
            if ("subject".equalsIgnoreCase((String) parameterMap.get("category2Value")) == true) {
                int count = 0;
                for (LinkedHashMap<String, Object> map : page.getData()) {
                    if (StringUtils.equals(userid, (String) map.get("OWNER_SESA"))) {
                        count++;
                    }
                }
                if (count > 0) {
                    actionToDoServiceDao.updateFeedbackView(userid, String.valueOf(parameterMap.get("viewCategory2")), count);
                }
            }
        }

        return response.setBody(page);
    }

    @Override
    public void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse response, String userid) {
        this.renameOwnerToActionOwner(parameterMap);
        this.generateReport1Filter(parameterMap);
        this.generateFilter(parameterMap);
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName;
        if (StringUtils.isNotBlank((String) parameterMap.get("viewCategory2"))) {
            fileName = "my_summary_error_" + parameterMap.get("viewCategory1") + "_" + Utils.randomStr(4) + ".xlsx";
        } else {
            fileName = "my_summary_errors_" + parameterMap.get("viewCategory1") + "_" + Utils.randomStr(4) + ".xlsx";
        }

        // 下载计数
        actionToDoServiceDao.updateFeedbackDownload(userid, String.valueOf(parameterMap.get("viewCategory1")), String.valueOf(parameterMap.get("viewCategory2")));
        excelTemplate.create(response, fileName, "com.dia.mpt.dao.IActionToDoServiceDao.queryReport1Details", parameterMap);
    }

    private void generateReport2Tooltips(Map<String, Object> parameterMap) {
        String resultType = (String) parameterMap.get("resultType");
        List<String> tooltips = ((JSONArray) parameterMap.get("report2Tooltips")).toJavaList(String.class);
        if (!tooltips.isEmpty()) {
            List<String> tooltipsColumns = tooltips.stream().map(this::getColumnName).toList();
            List<String> tooltipsColumnsName = new ArrayList<>();
            List<String> polymorphicColumns = new ArrayList<>(Arrays.asList("PO_AB"));

            for (String c : tooltipsColumns) {
                String tooltip = this.getColumnName(c);
                if (polymorphicColumns.contains(c)) {
                    if ("Quantity".equalsIgnoreCase(resultType)) {
                        tooltipsColumnsName.add("NVL(SUM(" + c + "),0) AS " + tooltip);
                    } else if ("Value".equalsIgnoreCase(resultType)) {
                        tooltipsColumnsName.add("NVL(SUM(" + c + "_VALUE),0) AS " + tooltip);
                    } else if ("Line".equalsIgnoreCase(resultType)) {
                        tooltipsColumnsName.add("NVL(SUM(CASE WHEN " + c + " > 0 THEN 1 ELSE 0 END),0) AS " + tooltip);
                    }
                } else {
                    tooltipsColumnsName.add("NVL(AVG(" + c + "),0) AS " + tooltip);
                }
            }
            parameterMap.put("tooltipsColumns", StringUtils.join(tooltipsColumnsName, ", "));
        }
    }

    private String getColumnName(Object labelObj) {
        String label = (String) labelObj;
        if (label == null) {
            return null;
        }
        if (Utils.hasInjectionAttack(label)) {
            return "";
        }
        return label;
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateReport2Tooltips(parameterMap);
        this.generateTreePathFilter(parameterMap);

        // 将前台传过来的label转换成列名, 同时也可以防止恶意代码注入
        parameterMap.put("level1", this.getColumnName(parameterMap.get("level1")));
        parameterMap.put("level2", this.getColumnName(parameterMap.get("level2")));
        parameterMap.put("level3", this.getColumnName(parameterMap.get("level3")));
        parameterMap.put("level4", this.getColumnName(parameterMap.get("level4")));
        parameterMap.put("level5", this.getColumnName(parameterMap.get("level5")));

        List<ActionToDoReport2Treemap> resultList = new ArrayList<>();
        List<ActionToDoReport2Bean> dataList = actionToDoServiceDao.queryReport2(parameterMap);
        for (ActionToDoReport2Bean data : dataList) {
            this.convertReport2Data(resultList, data);
        }
        return response.setBody(resultList);
    }


    /**
     * 将列表转化为Tree数据
     *
     * @param list 输出树
     * @param data 输入值
     */
    private void convertReport2Data(List<ActionToDoReport2Treemap> list, ActionToDoReport2Bean data) {
        String[] categorysOrg = new String[]{data.getCategory1(), data.getCategory2(), data.getCategory3(), data.getCategory4(), data.getCategory5()};
        List<String> categories = new ArrayList<>();

        for (String category : categorysOrg) {
            if (StringUtils.isNotBlank(category)) {
                categories.add(category);
            } else {
                break;
            }
        }

        // 这边逻辑比较复杂, 所以用最笨的方法来描述了, 以免后期不好维护
        // 先把这一行数据转成treemap的数据
        // 第一个节点
        List<ActionToDoReport2Treemap> child = new ArrayList<>();
        ActionToDoReport2Treemap root = new ActionToDoReport2Treemap();
        root.setName(categories.get(0));
        root.setTips(data.copyTooltips()); // 因为这个tooltips要放在树中全局使用, 所以必须要生成一个新节点
        root.setChildren(child);

        // 中间节点
        for (int i = 1; i < categories.size() - 1; i++) {
            ActionToDoReport2Treemap treemap = new ActionToDoReport2Treemap();
            treemap.setName(categories.get(i));
            treemap.setTips(data.copyTooltips());

            child.add(treemap);
            child = new ArrayList<>();
            treemap.setChildren(child);
        }

        // 最后一个节点
        ActionToDoReport2Treemap lastNode = new ActionToDoReport2Treemap();
        lastNode.setName(categories.get(categories.size() - 1));
        lastNode.setValue(data.getValue());
        lastNode.setTips(data.copyTooltips());
        child.add(lastNode);

        // 将这行treemap与原始数据相加
        // 先找到list中是否有这个数据节点
        Optional<ActionToDoReport2Treemap> beanOpt = list.stream().filter(b -> b.getName().equals(categories.get(0))).findFirst();
        if (beanOpt.isPresent()) {
            ActionToDoReport2Treemap bean = beanOpt.get();
            bean.add(root); // 两个节点合并
        } else { //找不到的时候最省事, 直接放入list就可以了
            list.add(root);
        }
    }


    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3(Map<String, Object> parameterMap, String userid) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        List<ActionToDoReport3Bean> dataList;

        dataList = actionToDoServiceDao.queryReport3(parameterMap);

        Map<String, BigDecimal> dataMap = new HashMap<>();
        LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();
        Map<String, String> xAxisMap = new HashMap<>();

        List<String> legend = actionToDoServiceDao.queryReport3Legend(parameterMap);

        for (ActionToDoReport3Bean data : dataList) {
            dataMap.put(data.getKey(), data.getVALUE());
            xAxisMap.put(data.getCALENDAR_DATE(), "");
        }
        List<String> xAxisList = xAxisMap.keySet().stream().sorted(String::compareTo).collect(Collectors.toList());
        for (String l : legend) {
            List<BigDecimal> temp = new ArrayList<>();

            for (String x : xAxisList) {
                temp.add(dataMap.getOrDefault(l + "#" + x, BigDecimal.ZERO));
            }
            resultMap.put(l, temp);
        }

        resultMap.put("xAxis", xAxisList);
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Details(Map<String, Object> parameterMap, String userid) {
        this.renameOwnerToActionOwner(parameterMap);
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(actionToDoServiceDao.queryReport3DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(actionToDoServiceDao.queryReport3Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse res, String userid) {
        this.renameOwnerToActionOwner(parameterMap);
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "my_summary_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.dia.mpt.dao.IActionToDoServiceDao.queryReport3Details", parameterMap);
    }

    @Override
    public void downloadAllSubjects(Map<String, Object> parameterMap, HttpServletResponse response) {
        this.renameOwnerToActionOwner(parameterMap);
        this.generateFilter(parameterMap);
        // 下载的列表, 需要把ACTION_OWNER和MATERIAL OWNER都下载下来
        String filters = (String) parameterMap.get("filters");
        filters = filters.replaceFirst("(ACTION_OWNER_SESA in (\\([^)]*\\)))", "($1 OR MATERIAL_OWNER_SESA in $2)");
        filters = filters.replaceFirst("(ACTION_OWNER_NAME in (\\([^)]*\\)))", "($1 OR MATERIAL_OWNER_NAME in $2)");
        parameterMap.put("filters", filters);

        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String[] categories = new String[]{"1. Master Data (Mat)", "2. Master Data (Vend)", "3. Sales Order", "5. Manufacturing Order", "4. Purchasing Order", "7. Inventory"};
        SheetInfoWithQueryKey[] sheetInfoWithQueryKeys = new SheetInfoWithQueryKey[categories.length];
        String userid = ((Session) parameterMap.get("session")).getUserid();
        for (int i = 0; i < categories.length; i++) {
            Map<String, Object> param = new HashMap<>();
            param.put("category", categories[i]);
            param.put("userid", userid);
            Stream<Map.Entry<String, Object>> concat = Stream.concat(parameterMap.entrySet().stream(), param.entrySet().stream());
            Map<String, Object> map = concat.collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (v1, v2) -> v2));
            SheetInfoWithQueryKey sheetInfoWithQueryKey = new SheetInfoWithQueryKey();
            sheetInfoWithQueryKey.setSheetName(categories[i]);
            sheetInfoWithQueryKey.setQueryKey("com.dia.mpt.dao.IActionToDoServiceDao.downloadAllSubjects");
            sheetInfoWithQueryKey.setParameterMap(map);
            sheetInfoWithQueryKeys[i] = sheetInfoWithQueryKey;
        }

        // 更新download行为open line行
        excelTemplate.create(response, "action_to_do_all_subjects_" + Utils.randomStr(4) + ".xlsx", sheetInfoWithQueryKeys);
        actionToDoServiceDao.updateAllFeedbackDownload(userid, excelTemplate.getTotalRows());
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport4(Map<String, Object> parameterMap) {
        this.generateReport4Filter(parameterMap);
        return response.setBody(actionToDoServiceDao.queryReport4(parameterMap));
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport4Details(Map<String, Object> parameterMap) {
        this.generateReport4Filter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(actionToDoServiceDao.queryReport4DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(actionToDoServiceDao.queryReport4Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport4Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        this.generateReport4Filter(parameterMap);
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);

        String fileName = "action_to_do_owner_analysis" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.dia.mpt.dao.IActionToDoServiceDao.queryReport4Details", parameterMap);
    }

}
