package com.dia.mpt.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface IMPTWorkspaceDao {

    List<String> queryCategoryOpts();

    List<String> querySubjectOpts(Map<String, Object> parameterMap);

    List<Map<String, String>> queryCollaboratorsOpts(String userid);

    Map<String, String> queryCollaboratorsDefault(String userid);

    void saveTicket(Map<String, Object> parameterMap);

    void saveLog(Map<String, Object> parameterMap);

    void saveNotice(Map<String, Object> parameterMap);

    void saveMat(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryTicketsByStatus(Map<String, Object> parameterMap);

    List<Map<String, String>> queryUserMapping();

    Map<String, Object> queryTicketInfoById(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryTicketLogsById(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryTicketRepliesById(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryTicketDetailsById(Map<String, Object> parameterMap);

    void saveTicketComment(Map<String, Object> parameterMap);

    void deleteTicketComment(Map<String, Object> parameterMap);

    void approveTicketById(Map<String, Object> parameterMap);

    void closeTicketById(Map<String, Object> parameterMap);

    String getCollaboratorsById(Map<String, Object> parameterMap);

    void updateCollaborators(Map<String, Object> parameterMap);

    void saveVend(Map<String, Object> parameterMap);

    void saveSO(Map<String, Object> parameterMap);

    void savePO(Map<String, Object> parameterMap);

    void saveMO(Map<String, Object> parameterMap);

    void saveINV(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryQuickAccessApproval(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryQuickAccessForward(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryQuickAccessEscalation(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryLatestNews(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryOwnerOpts();

    int queryAdminCnt(String parentCode, String userid);

    Map<String, String> getActionPeriodById(Map<String, Object> parameterMap);

    void updateActionPeriod(Map<String, Object> parameterMap);

    void updateActionOwner(Map<String, Object> parameterMap);

    List<Map<String, String>> queryPlantApprovalOptsOpts(String userid);

    List<Map<String, String>> queryCentralApprovalOptsOpts();
}
