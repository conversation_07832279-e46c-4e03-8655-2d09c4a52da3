<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dia.mpt.dao.IActionToDoServiceDao">
    <sql id="Filter">
        <if test="filters != null and filters != ''.toString()">
            and ${filters}
        </if>
	    <if test="treePathFilter != null and treePathFilter != ''.toString()">
		    and ${treePathFilter}
	    </if>
    </sql>

    <select id="queryFilters" resultType="java.util.Map">
        SELECT NAME, CATEGORY
        FROM MY_PLANNING_TODAY_FILTER_V T
        ORDER BY CATEGORY, NAME
    </select>

    <select id="queryReport4Filters" resultType="java.util.Map">
        SELECT NAME, CATEGORY
        FROM MY_PLANNING_TODAY_OWNER_FILTER_V T
        ORDER BY CATEGORY, NAME
    </select>

    <select id="queryAuthDetails" resultType="java.lang.String">
        select auth_details from SY_MENU_AUTH where lower(user_id) = lower(#{userid, jdbcType=VARCHAR})
                                                    and menu_code = #{menuCode, jdbcType=VARCHAR}
    </select>

    <select id="queryReport1" resultType="java.util.Map">
        select T.${column1} category1,
            sum(ERROR_FROM_MPT_LINES) ERROR_FROM_MPT_LINES,
            sum(ERROR_FROM_OTHERS_LINES) ERROR_FROM_OTHERS_LINES,
            sum(FEEDBACK_LINES) FEEDBACK_LINES,
            sum(FORWARD_LINES) FORWARD_LINES,
            sum(ESCALATION_LINES) ESCALATION_LINES,
            sum(EXCLUSION_LINES) EXCLUSION_LINES
        from MY_PLANNING_TODAY_ERROR_V T
        where 1 = 1
        <include refid="Filter"/>
	    <if test="dateRange != null and dateRange.size() > 0">
		    AND TRUNC(T.CREATE_DATE$,'DD') BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
		    AND TRUNC(TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
	    </if>
        group by ${column1}
        order by ${column1}
    </select>

    <select id="queryReport1Sub" resultType="java.util.Map">
        select T.${column2} category2,
               sum(ERROR_FROM_MPT_LINES) ERROR_FROM_MPT_LINES,
               sum(ERROR_FROM_OTHERS_LINES) ERROR_FROM_OTHERS_LINES,
			   sum(FEEDBACK_LINES) FEEDBACK_LINES,
			   sum(FORWARD_LINES) FORWARD_LINES,
			   sum(ESCALATION_LINES) ESCALATION_LINES,
			   sum(EXCLUSION_LINES) EXCLUSION_LINES,
               MAX(T.DESCRIPTION) DESCRIPTION,
               MAX(T.PROPOSAL_ACTION) PROPOSAL_ACTION,
               MAX(T.KPI_IMPACTED) KPI_IMPACTED,
               MAX(T.CLASSIFICATION) CLASSIFICATION,
               MAX(T.SUBJECT_OWNER_SESA) SUBJECT_OWNER_SESA,
               MAX(T.SUBJECT_OWNER_NAME) SUBJECT_OWNER_NAME,
               MAX(T.PRIORITY) PRIORITY,
               MAX(T.WORKFLOW_ID) WORKFLOW_ID
        from MY_PLANNING_TODAY_ERROR_V T
        where T.${column1} = #{expandCategory1, jdbcType=VARCHAR}
        <include refid="Filter"/>
	    <if test="dateRange != null and dateRange.size() > 0">
		    AND TRUNC(T.CREATE_DATE$,'DD') BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
		    AND TRUNC(TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
	    </if>
        group by T.${column2}
        order by T.${column2}
    </select>

    <sql id="queryReport1DetailsSQL">
        SELECT
        <choose>
		    <when test="viewCategory1 == '1. Master Data (Mat)'">
			    CATEGORY,
			    SUBJECT,
			    DESCRIPTION,
			    PROPOSAL_ACTION,
			    CLASSIFICATION,
			    KPI_IMPACTED,
			    PRIORITY,
			    CREATE_DATE$,
			    MATERIAL_OWNER_SESA,
			    MATERIAL_OWNER_NAME,
			    MATERIAL_OWNER_ORGANIZATION,
			    ACTION_OWNER_SESA,
			    ACTION_OWNER_NAME,
			    ACTION_OWNER_ORGANIZATION,
			    TOTAL_PENDING_DAYS_CD,
			    TOTAL_PENDING_DAYS_WD,
			    ACTION,
			    TITLE,
			    TICKET_PENDING_DAYS,
			    MATERIAL,
			    PLANT_CODE,
			    PLANT_TYPE,
			    ENTITY,
			    CLUSTER_NAME,
			    MATERIAL_TYPE,
			    MATERIAL_CATEGORY,
			    CREATED_DATE,
			    MRP_TYPE,
			    MRP_CONTROLLER,
			    PURCHASING_GROUP,
			    PROCUREMENT_TYPE,
			    SPECIAL_PROC_CODE,
			    STOCKING_POLICY,
			    MRP_GROUP,
			    CONSUMPTION_MODE,
			    BWD_CONSUMPTION,
			    FWD_CONSUMPTION,
			    INDIVIDUAL_COLLECT,
			    AVAILABILITY_CHECK,
			    BULK_MATERIAL_IND,
			    BUCKFLUSH_IND,
			    SAFETY_STOCK,
			    REORDER_POINT,
			    PLANNED_DELIV_TIME,
			    T_REPLENISHMENT_LT,
			    IN_HOUSE_PRODN_LT,
			    GR_PROCESSING_TIME,
			    LOT_SIZE_CODE,
			    MINIMUM_LOT_SIZE,
			    FIXED_LOT_SIZE,
			    ROUNDING_VALUE,
			    CALCULATED_ABC,
			    CALCULATED_FMR,
			    AMU,
			    AMU_ONEMM,
			    AMF,
			    AMF_ONEMM,
			    SS3,
			    VENDOR_CODE,
			    VENDOR_SHORT_NAME,
			    VENDOR_FULL_NAME,
			    VENDOR_PARENT_CODE,
			    VENDOR_PARENT_NAME,
			    REPL_STRATEGY,
			    COMMODITY_CODE,
			    UNIT_COST,
			    GRA_TYPE,
			    BU,
			    PRODUCT_LINE,
			    PRODUCTION_LINE,
			    LOCAL_PRODUCT_LINE,
			    PLANT_IN_HOUSE_PRODN_LT,
			    PLANT_GR_PROCESSING_TIME,
			    PLANT_PICK_PACK_TIME,
			    IR_PLANNED_DELIV_TIME,
			    MRP_CONTROLLER_DESCRIPTION,
			    MATERIAL_ST_PLANT,
			    TRIGGER_VALIDATE_FROM,
			    TRIGGER_VALIDATE_TO,
			    QMAX_LEAD_TIME
		    </when>
		    <when test="viewCategory1 == '2. Master Data (Vend)'">
			    CATEGORY,
			    SUBJECT,
			    DESCRIPTION,
			    PROPOSAL_ACTION,
			    CLASSIFICATION,
			    KPI_IMPACTED,
			    PRIORITY,
			    CREATE_DATE$,
			    MATERIAL_OWNER_SESA,
			    MATERIAL_OWNER_NAME,
			    MATERIAL_OWNER_ORGANIZATION,
			    ACTION_OWNER_SESA,
			    ACTION_OWNER_NAME,
			    ACTION_OWNER_ORGANIZATION,
			    TOTAL_PENDING_DAYS_CD,
			    TOTAL_PENDING_DAYS_WD,
			    ACTION,
			    TITLE,
			    TICKET_PENDING_DAYS,
			    MATERIAL,
			    PLANT_CODE,
			    ENTITY,
			    CLUSTER_NAME,
			    ACTIVENESS,
			    PROCUREMENT_PLANT,
			    PURCHASING_ORG,
			    QUOTA,
			    VENDOR_CODE,
			    SOURCE_NUMBER,
			    SOURCE_BLOCK_IND,
			    SOURCE_VALID_FROM,
			    SOURCE_VALID_TO,
			    FIXED_VENDOR_IND,
			    FIXED_ISSUE_PLT_IND,
			    MRP_INDICATOR,
			    PURCH_INFO_RECORD,
			    INFO_CATEGORY,
			    MRP1_MIN_LOT_SIZE,
			    PIR_MIN_LOT_SIZE,
			    MRP1_PDT,
			    PIR_PDT
		    </when>
		    <when test="viewCategory1 == '3. Sales Order'">
			    CATEGORY,
			    SUBJECT,
			    DESCRIPTION,
			    PROPOSAL_ACTION,
			    CLASSIFICATION,
			    KPI_IMPACTED,
			    PRIORITY,
			    CREATE_DATE$,
			    MATERIAL_OWNER_SESA,
			    MATERIAL_OWNER_NAME,
			    MATERIAL_OWNER_ORGANIZATION,
			    ACTION_OWNER_SESA,
			    ACTION_OWNER_NAME,
			    ACTION_OWNER_ORGANIZATION,
			    TOTAL_PENDING_DAYS_CD,
			    TOTAL_PENDING_DAYS_WD,
			    ACTION,
			    TITLE,
			    TICKET_PENDING_DAYS,
			    SALES_ORDER_NUMBER,
			    SALES_ORDER_ITEM,
			    MATERIAL,
			    PLANT_CODE,
			    PLANT_TYPE,
			    ENTITY,
			    CLUSTER_NAME,
			    CREATED_DATE,
			    MRP_TYPE,
			    MRP_CONTROLLER,
			    STOCKING_POLICY,
			    AVAILABILITY_CHECK,
			    CALCULATED_ABC,
			    CALCULATED_FMR,
			    VENDOR_CODE,
			    VENDOR_SHORT_NAME,
			    VENDOR_FULL_NAME,
			    VENDOR_PARENT_CODE,
			    VENDOR_PARENT_NAME,
			    UNIT_COST,
			    DELIVERING_PLANT,
			    DIST_CHANNEL_SP_ST,
			    GRA_TYPE,
			    BU,
			    PRODUCT_LINE,
			    PRODUCTION_LINE,
			    LOCAL_BU,
			    LOCAL_PRODUCT_LINE,
			    LOCAL_PRODUCT_FAMILY,
			    LOCAL_PRODUCT_SUBFAMILY,
			    FULFILL_OR_NOT_NONBLOCK,
			    FULFILL_STRATEGY,
			    FULFILL_STRATEGY_UNBLOCK,
			    PARTIALL_FULFILL_WITH_QTY_UNBLOCK,
			    VIP_SO_INDICATOR,
			    ITEM_CATEGORY,
			    PLANT_SCOPE,
			    SALES_ORGANIZATION,
			    SO_PRODUCT_LINE,
			    SALES_DISTRICT,
			    SO_STOCKING_POLICY,
			    SOLD_TO,
			    SOLD_TO_SHORT_NAME,
			    SOLD_TO_FULL_NAME,
			    SOLD_TO_PARENT_NAME,
			    SOLD_TO_PARENT_CODE,
			    SOLD_TO_COUNTRY,
			    SOLD_TO_REGION,
			    SHIP_TO,
			    SHIP_TO_SHORT_NAME,
			    SHIP_TO_FULL_NAME,
			    SHIP_TO_PARENT_NAME,
			    SHIP_TO_PARENT_CODE,
			    SHIP_TO_COUNTRY,
			    CUST_PO_DATE,
			    CUST_PO_NUMBER,
			    CUST_PO_ITEM,
			    CUSTOMIZED_LABEL,
			    DELIVERY_PRIORITY,
			    DELIVERY_RANGE,
			    CRD_DATE,
			    SO_CONTRACTUAL_GI_DATE,
			    AC2_DATE,
			    CONFIRM_DATE,
			    AC2_TYPE,
			    AC2_RANGE,
			    BTN_DATE,
			    PLAN_GI_DATE,
			    ORDER_QUANTITY,
			    OPEN_SO_QTY,
			    OPEN_UD_QTY,
			    OPEN_UD_VALUE,
			    SO_UU_STOCK,
			    PURCH_ORDER_NUMBER,
			    PURCH_ORDER_ITEM,
			    PO_QTY,
			    PR_NUMBER,
			    PR_ITEM,
			    SHIPPING_POINT,
			    SO_BLOCK_STATUS,
			    UD_LONG_AGING,
			    GRA_STATUS,
			    GRA_EVENT_NAME,
			    COMPL_DELIV_IND,
			    PAST_DUE_INDICATOR,
			    NET_NET_VALUE_RMB,
			    SE_SCOPE,
			    QMAX_LEAD_TIME,
			    VALIDATE_TO,
			    VALIDATE_FROM,
			    COMPL_DELIV_FLAG,
			    MV_SOA_EMAIL,
			    MV_SOA_SESA,
			    MV_SOA_NAME,
			    LV_SOA_EMAIL,
			    LV_SOA_SESA,
			    LV_SOA_NAME
		    </when>
		    <when test="viewCategory1 == '4. Purchasing Order'">
			    CATEGORY,
			    SUBJECT,
			    DESCRIPTION,
			    PROPOSAL_ACTION,
			    CLASSIFICATION,
			    KPI_IMPACTED,
			    PRIORITY,
			    CREATE_DATE$,
			    MATERIAL_OWNER_SESA,
			    MATERIAL_OWNER_NAME,
			    MATERIAL_OWNER_ORGANIZATION,
			    ACTION_OWNER_SESA,
			    ACTION_OWNER_NAME,
			    ACTION_OWNER_ORGANIZATION,
			    TOTAL_PENDING_DAYS_CD,
			    TOTAL_PENDING_DAYS_WD,
			    ACTION,
			    TITLE,
			    TICKET_PENDING_DAYS,
			    PURCH_ORDER_NUMBER,
			    PURCH_ORDER_ITEM,
			    SEQUENTIAL_NUMBER,
			    CONFIRM_CAT,
			    MATERIAL,
			    PLANT_CODE,
			    PLANT_TYPE,
			    ENTITY,
			    MATERIAL_ST_PLANT,
			    CLUSTER_NAME,
			    MATERIAL_TYPE,
			    EXISTING_IN_BOM,
			    MRP_TYPE,
			    MRP_CONTROLLER,
			    PURCHASING_GROUP,
			    PROCUREMENT_TYPE,
			    STOCKING_POLICY,
			    PLANNED_DELIV_TIME,
			    LT_RANGE,
			    GR_PROCESSING_TIME,
			    ROUNDING_VALUE,
			    CALCULATED_ABC,
			    CALCULATED_FMR,
			    PO_CREATED_DATE,
			    PO_AB,
			    PO_LA,
			    PO_NON_ABLA,
			    PO_AB_VALUE,
			    PO_LA_VALUE,
			    PO_NON_ABLA_VALUE,
			    DUEDATE,
			    PASTDUE_QTY,
			    PAST_DUE_INDICATOR,
				RESCHEDULE_OUT_COUNT,
				PAST_DUE_COUNT,
			    EXCEPTION_CODE_1,
			    EXCEPTION_CODE_2,
			    GENERATE_FROM,
			    PR_NUMBER,
			    PR_ITEM,
			    ORDER_QUANTITY,
			    COUNTRY,
			    ITEM_CATEGORY,
			    ABLA_OVERPO,
			    STAT_REL_DEL_DATE,
			    DELIVERY_DATE,
			    OPEN_QTY,
			    SHIPPING_INSTR,
			    MIN_ORDER_QUANTITY,
			    PO_IR,
			    PO_IR_VALUE_CNY,
			    GRA_STATUS,
			    TRANSPORT_TIME,
			    PO_TYPE,
			    PO_STATUS,
			    VENDOR_CODE,
			    VENDOR_SHORT_NAME,
			    VENDOR_FULL_NAME,
			    VENDOR_PARENT_CODE,
			    VENDOR_PARENT_NAME,
			    REPL_STRATEGY,
			    SOURCE_CATEGORY,
			    COMMODITY_CODE,
			    UNIT_COST,
			    GRA_TYPE,
			    GRA_EVENT,
			    BU,
			    PRODUCT_LINE,
			    PRODUCTION_LINE,
			    LOCAL_PRODUCT_LINE
		    </when>
		    <when test="viewCategory1 == '5. Manufacturing Order'">
			    CATEGORY,
			    SUBJECT,
			    DESCRIPTION,
			    PROPOSAL_ACTION,
			    CLASSIFICATION,
			    KPI_IMPACTED,
			    PRIORITY,
			    CREATE_DATE$,
			    MATERIAL_OWNER_SESA,
			    MATERIAL_OWNER_NAME,
			    MATERIAL_OWNER_ORGANIZATION,
			    ACTION_OWNER_SESA,
			    ACTION_OWNER_NAME,
			    ACTION_OWNER_ORGANIZATION,
			    TOTAL_PENDING_DAYS_CD,
			    TOTAL_PENDING_DAYS_WD,
			    ACTION,
			    TITLE,
			    TICKET_PENDING_DAYS,
			    MO_NUMBER,
			    MATERIAL,
			    PLANT_CODE,
			    ENTITY,
			    CLUSTER_NAME,
			    MATERIAL_TYPE,
			    MATERIAL_CATEGORY,
			    CREATED_DATE,
			    BU,
			    PRODUCT_LINE,
			    MRP_TYPE,
			    MRP_CONTROLLER,
			    STOCKING_POLICY,
			    AVAILABILITY_CHECK,
			    SAFETY_STOCK,
			    REORDER_POINT,
			    MO_REQ_DATE,
			    PAST_DUE_INDICATOR,
			    MO_TYPE,
			    OBJECT_NUMBER,
			    TECHNICAL_COMPLETION_DATE,
			    DELETION_FLAG,
			    DELIVERY_RANGE,
			    MO_START_DATE,
			    ACTUAL_END,
			    ACTUAL_FINISH,
			    RELEASE_DATE,
			    RESERVE_NUMBER,
			    MO_QTY,
			    MO_VALUE,
			    CONFIRM_QTY,
			    OPEN_MO_QTY,
			    OPEN_MO_VALUE,
			    GR_QTY,
			    GR_DATE,
			    SALES_ORDER_NUMBER,
			    SALES_ORDER_ITEM,
			    GRA_TYPE,
			    GRA_EVENT,
			    SOLD_TO,
			    MO_STATUS,
			    WBS_NUMBER,
			    CALCULATED_ABC,
			    VENDOR_CODE,
			    VENDOR_SHORT_NAME,
			    VENDOR_FULL_NAME,
			    VENDOR_PARENT_CODE,
			    VENDOR_PARENT_NAME,
			    UNIT_COST
		    </when>
		    <when test="viewCategory1 == '7. Inventory'">
			    CATEGORY,
			    SUBJECT,
			    DESCRIPTION,
			    PROPOSAL_ACTION,
			    CLASSIFICATION,
			    KPI_IMPACTED,
			    PRIORITY,
			    CREATE_DATE$,
			    MATERIAL_OWNER_SESA,
			    MATERIAL_OWNER_NAME,
			    MATERIAL_OWNER_ORGANIZATION,
			    ACTION_OWNER_SESA,
			    ACTION_OWNER_NAME,
			    ACTION_OWNER_ORGANIZATION,
			    TOTAL_PENDING_DAYS_CD,
			    TOTAL_PENDING_DAYS_WD,
			    ACTION,
			    TITLE,
			    TICKET_PENDING_DAYS,
			    MATERIAL,
			    PLANT_CODE,
			    STORAGE_LOCATION,
			    SPECIAL_ST,
			    SPECIAL_ST_NO,
			    SCOPE$,
			    PLANT_TYPE,
			    ENTITY,
			    MATERIAL_ST_PLANT,
			    CLUSTER_NAME,
			    MATERIAL_TYPE,
			    MATERIAL_CATEGORY,
			    EXISTING_IN_BOM,
			    ACTIVENESS,
			    NEW_PRODUCTS,
			    FOLLOW_UP_MATERIAL,
			    MRP_TYPE,
			    MRP_CONTROLLER,
			    PURCHASING_GROUP,
			    PROCUREMENT_TYPE,
			    SPECIAL_PROC_CODE,
			    STOCKING_POLICY,
			    MRP_GROUP,
			    INDIVIDUAL_COLLECT,
			    AVAILABILITY_CHECK,
			    SAFETY_STOCK,
			    REORDER_POINT,
			    PLANNED_DELIV_TIME,
			    LT_RANGE,
			    MINIMUM_LOT_SIZE,
			    CALCULATED_ABC,
			    CALCULATED_FMR,
			    SS2,
			    SS3,
			    VENDOR_CODE,
			    VENDOR_SHORT_NAME,
			    VENDOR_FULL_NAME,
			    VENDOR_PARENT_CODE,
			    VENDOR_PARENT_NAME,
			    REPL_STRATEGY,
			    SOURCE_CATEGORY,
			    COMMODITY_CODE,
			    COMMODITY_CODE_ONE_MM,
			    UNIT_COST,
			    PRICE_UNIT,
			    DIST_CHANNEL_SP_ST,
			    BU,
			    PRODUCT_LINE,
			    PRODUCTION_LINE,
			    LOCAL_BU,
			    LOCAL_PRODUCT_LINE,
			    UU_STOCK,
			    UU_STOCK_VALUE,
			    STOCK_IN_QI,
			    STOCK_IN_QI_VALUE,
			    RESTRICTED_STOCK,
			    RESTRICTED_STOCK_VALUE,
			    BLOCKED_STOCK,
			    BLOCKED_STOCK_VALUE,
			    RETURNS_STOCK,
			    RETURNS_STOCK_VALUE,
			    INTER_STK_TRANSFER,
			    INTER_STK_TRANSFER_VALUE,
			    INVENTORY_CATEGORY,
			    LOCATION_CATEGORY,
			    WIP_QTY,
			    WIP_VALUE,
			    GIT_QTY,
			    GIT_VALUE,
			    OPEN_PO,
			    OPEN_PO_VALUE,
			    OPEN_AB,
			    OPEN_AB_VALUE,
			    OPEN_LA,
			    OPEN_LA_VALUE,
			    OPEN_NON_AB_LA,
			    OPEN_NON_AB_LA_VALUE,
			    OPEN_SO,
			    OPEN_SO_VALUE,
			    MO_RESERVATION,
			    MO_RESERVATION_VALUE,
			    EXCESS_ONE_MM,
			    EXCESS_ONE_MM_VALUE,
			    AMU_ONE_MM,
			    AMU_ONE_MM_VALUE,
			    AMF_ONE_MM,
			    AMF_ONE_MM_VALUE
		    </when>
	    </choose>
        FROM
		    <choose>
			    <when test="viewCategory1 == '1. Master Data (Mat)'">
				    MY_PLANNING_TODAY_MAT_DETAIL_V t
			    </when>
			    <when test="viewCategory1 == '2. Master Data (Vend)'.toString()">
				    MY_PLANNING_TODAY_VEND_DETAIL_V t
			    </when>
			    <when test="viewCategory1 == '3. Sales Order'.toString()">
				    MY_PLANNING_TODAY_SO_DETAIL_V t
			    </when>
			    <when test="viewCategory1 == '4. Purchasing Order'.toString()">
				    MY_PLANNING_TODAY_PO_DETAIL_V t
			    </when>
			    <when test="viewCategory1 == '5. Manufacturing Order'.toString()">
				    MY_PLANNING_TODAY_MO_DETAIL_V t
			    </when>
			    <when test="viewCategory1 == '7. Inventory'.toString()">
				    MY_PLANNING_TODAY_INV_DETAIL_V t
			    </when>
		    </choose>
        WHERE 1=1
	    <include refid="Filter"/>
	    <if test="viewCategory1 != null and viewCategory1 != ''.toString()">
		    and T.${column1} = #{viewCategory1,jdbcType=VARCHAR}
	    </if>
        <if test="viewCategory2 != null and viewCategory2 != ''.toString()">
            and T.${column2} = #{viewCategory2,jdbcType=VARCHAR}
        </if>
	    <if test="dateRange != null and dateRange.size() > 0">
		    AND TRUNC(T.CREATE_DATE$,'DD') BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
		    AND TRUNC(TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
	    </if>
	    <choose>
			<when test="report1ViewType == 'Feedback'.toString()">
				AND ACTION = '1. Feedback'
			</when>
		    <when test="report1ViewType == 'Forward'.toString()">
			    AND ACTION = '3. Forward'
		    </when>
		    <when test="report1ViewType == 'Escalation'.toString()">
			    AND ACTION = '4. Escalation'
		    </when>
		    <when test="report1ViewType == 'Exclusion'.toString()">
			    AND ACTION = '2. Exclusion'
		    </when>
			<otherwise>
				AND NVL(ACTION, 'Others') != '2. Exclusion'
			</otherwise>
	    </choose>
    </sql>

    <select id="queryReport1DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
	    <include refid="queryReport1DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap" flushCache="true" useCache="false">
        <include refid="global.select_header"/>
	    <include refid="queryReport1DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

	<resultMap id="report2ResultMap" type="com.dia.mpt.bean.ActionToDoReport2Bean">
		<result property="category1" column="CATEGORY1"/>
		<result property="category2" column="CATEGORY2"/>
		<result property="category3" column="CATEGORY3"/>
		<result property="category4" column="CATEGORY4"/>
		<result property="category5" column="CATEGORY5"/>
		<result property="value" column="value"/>
		<association property="tooltips" javaType="com.dia.mpt.bean.ActionToDoReport2Tooltips">
		</association>
	</resultMap>

	<select id="queryReport2" resultMap="report2ResultMap">
		SELECT
			NVL(${level1}, 'Others') AS CATEGORY1,
			NVL(${level2}, 'Others') AS CATEGORY2,
			NVL(${level3}, 'Others') AS CATEGORY3,
			<if test="level4 != null and level4 != ''.toString()">
				NVL(${level4}, 'Others') AS CATEGORY4,
			</if>
			<if test="level5 != null and level5 != ''.toString()">
				NVL(${level5}, 'Others') AS CATEGORY5,
			</if>
			SUM(ERROR_FROM_MPT_LINES + ERROR_FROM_OTHERS_LINES) AS VALUE
			<if test="tooltipsColumns != null and tooltipsColumns != ''.toString()">
				,${tooltipsColumns}
			</if>
		FROM MY_PLANNING_TODAY_ERROR_V t
		<where>
			<include refid="Filter"/>
			<if test="dateRange != null and dateRange.size() > 0">
				AND TRUNC(T.CREATE_DATE$,'DD') BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
				AND TRUNC(TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
			</if>
		</where>
		GROUP BY
			${level1}, ${level2}, ${level3}
			<if test="level4 != null and level4 != ''.toString()">,${level4}</if>
			<if test="level5 != null and level5 != ''.toString()">,${level5}</if>
	</select>

    <resultMap id="report3ResultMap" type="com.dia.mpt.bean.ActionToDoReport3Bean">
        <result property="CALENDAR_DATE" column="CALENDAR_DATE"/>
        <result property="NAME" column="NAME"/>
        <result property="VALUE" column="VALUE"/>
    </resultMap>

    <select id="queryReport3Legend" resultType="java.lang.String">
        SELECT DISTINCT nvl(${report3ViewType}, 'Others') AS NAME
        FROM MY_PLANNING_TODAY_ERROR_V T
        LEFT JOIN SY_CALENDAR SY ON TRUNC(T.CREATE_DATE$, 'DD') = SY.DATE$
		    AND SY.NAME = 'MPT Working Calendar'
        WHERE T.CREATE_DATE$ IS NOT NULL
			AND SY.WORKING_DAY = 1
	        <include refid="Filter"/>
	        <if test="report3DateRange != null and report3DateRange.size() > 0">
	            AND TRUNC(T.CREATE_DATE$,'DD') BETWEEN TO_DATE(#{report3DateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
	            AND TRUNC(TO_DATE(#{report3DateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
	        </if>
        ORDER BY
            nvl(T.${report3ViewType}, 'Others')
    </select>

    <select id="queryReport3" parameterType="java.util.Map" resultMap="report3ResultMap">
        SELECT
            TO_CHAR(T.CREATE_DATE$, 'yyyy/mm/dd') AS CALENDAR_DATE,
            nvl(T.${report3ViewType}, 'Others') AS NAME,
	        SUM(ERROR_FROM_MPT_LINES + ERROR_FROM_OTHERS_LINES) AS VALUE
        FROM MY_PLANNING_TODAY_ERROR_V T
        LEFT JOIN SY_CALENDAR SY ON TRUNC(T.CREATE_DATE$, 'DD') = SY.DATE$
	        AND SY.NAME = 'MPT Working Calendar'
        WHERE T.CREATE_DATE$ IS NOT NULL
            AND SY.WORKING_DAY = 1
		    <include refid="Filter"/>
	        <if test="report3DateRange != null and report3DateRange.size() > 0">
	            AND TRUNC(T.CREATE_DATE$,'DD') BETWEEN TO_DATE(#{report3DateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
	            AND TRUNC(TO_DATE(#{report3DateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
	        </if>
        GROUP BY
            TO_CHAR(T.CREATE_DATE$, 'yyyy/mm/dd'),
            nvl(T.${report3ViewType}, 'Others')
    </select>

    <sql id="report3DetailsSQL">
        SELECT  TO_CHAR(CREATE_DATE$, 'YYYY/MM/DD') AS CREATE_DATE,
				CATEGORY,
				SUB_CATEGORY,
				MATERIAL_OWNER_SESA,
				MATERIAL_OWNER_NAME,
				MATERIAL_OWNER_ORGANIZATION,
				ACTION_OWNER_SESA,
				ACTION_OWNER_NAME,
				ACTION_OWNER_ORGANIZATION,
				SUBJECT,
				CLASSIFICATION,
				PRIORITY,
				KPI_IMPACTED
        FROM (
				SELECT  CREATE_DATE$,
						CATEGORY,
						SUB_CATEGORY,
						MATERIAL_OWNER_SESA,
						MATERIAL_OWNER_NAME,
						MATERIAL_OWNER_ORGANIZATION,
						ACTION_OWNER_SESA,
						ACTION_OWNER_NAME,
						ACTION_OWNER_ORGANIZATION,
						SUBJECT,
						CLASSIFICATION,
						PRIORITY,
						KPI_IMPACTED
				FROM MY_PLANNING_TODAY_MAT_DETAIL_V
				UNION ALL
				SELECT  CREATE_DATE$,
						CATEGORY,
						SUB_CATEGORY,
						MATERIAL_OWNER_SESA,
						MATERIAL_OWNER_NAME,
						MATERIAL_OWNER_ORGANIZATION,
						ACTION_OWNER_SESA,
						ACTION_OWNER_NAME,
						ACTION_OWNER_ORGANIZATION,
						SUBJECT,
						CLASSIFICATION,
						PRIORITY,
						KPI_IMPACTED
				FROM MY_PLANNING_TODAY_VEND_DETAIL_V
				UNION ALL
				SELECT  CREATE_DATE$,
						CATEGORY,
						SUB_CATEGORY,
						MATERIAL_OWNER_SESA,
						MATERIAL_OWNER_NAME,
						MATERIAL_OWNER_ORGANIZATION,
						ACTION_OWNER_SESA,
						ACTION_OWNER_NAME,
						ACTION_OWNER_ORGANIZATION,
						SUBJECT,
						CLASSIFICATION,
						PRIORITY,
						KPI_IMPACTED
				FROM MY_PLANNING_TODAY_SO_DETAIL_V
				UNION ALL
				SELECT  CREATE_DATE$,
						CATEGORY,
						SUB_CATEGORY,
						MATERIAL_OWNER_SESA,
						MATERIAL_OWNER_NAME,
						MATERIAL_OWNER_ORGANIZATION,
						ACTION_OWNER_SESA,
						ACTION_OWNER_NAME,
						ACTION_OWNER_ORGANIZATION,
						SUBJECT,
						CLASSIFICATION,
						PRIORITY,
						KPI_IMPACTED
				FROM MY_PLANNING_TODAY_PO_DETAIL_V
				UNION ALL
				SELECT CREATE_DATE$,
						CATEGORY,
						SUB_CATEGORY,
						MATERIAL_OWNER_SESA,
						MATERIAL_OWNER_NAME,
						MATERIAL_OWNER_ORGANIZATION,
						ACTION_OWNER_SESA,
						ACTION_OWNER_NAME,
						ACTION_OWNER_ORGANIZATION,
						SUBJECT,
						CLASSIFICATION,
						PRIORITY,
						KPI_IMPACTED
				FROM MY_PLANNING_TODAY_MO_DETAIL_V
				UNION ALL
				SELECT  CREATE_DATE$,
						CATEGORY,
						SUB_CATEGORY,
						MATERIAL_OWNER_SESA,
						MATERIAL_OWNER_NAME,
						MATERIAL_OWNER_ORGANIZATION,
						ACTION_OWNER_SESA,
						ACTION_OWNER_NAME,
						ACTION_OWNER_ORGANIZATION,
						SUBJECT,
						CLASSIFICATION,
						PRIORITY,
						KPI_IMPACTED
				FROM MY_PLANNING_TODAY_INV_DETAIL_V
		) T
        WHERE TRUNC(T.CREATE_DATE$,'DD') = TO_DATE(#{report3SelectedValue, jdbcType=VARCHAR}, 'yyyy/mm/dd')
	        <include refid="Filter"/>
		    <if test="report3DateRange != null and report3DateRange.size() > 0">
			    AND TRUNC(T.CREATE_DATE$,'DD') BETWEEN TO_DATE(#{report3DateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
			    AND TRUNC(TO_DATE(#{report3DateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
		    </if>
	        and T.${report3ViewType} = #{report3SelectedSeriesName}
    </sql>

    <select id="queryReport3DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="report3DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport3Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report3DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="downloadAllSubjects" resultType="java.util.LinkedHashMap">
	    <choose>
		    <when test="category == '1. Master Data (Mat)'">
		    	SELECT
				    CATEGORY,
				    SUBJECT,
				    DESCRIPTION,
				    PROPOSAL_ACTION,
				    CLASSIFICATION,
				    KPI_IMPACTED,
				    PRIORITY,
				    CREATE_DATE$,
				    MATERIAL_OWNER_SESA,
					MATERIAL_OWNER_NAME,
					MATERIAL_OWNER_ORGANIZATION,
					ACTION_OWNER_SESA,
					ACTION_OWNER_NAME,
					ACTION_OWNER_ORGANIZATION,
				    TOTAL_PENDING_DAYS_CD,
				    TOTAL_PENDING_DAYS_WD,
				    ACTION,
				    TITLE,
				    MATERIAL,
				    PLANT_CODE,
				    PLANT_TYPE,
				    ENTITY,
				    CLUSTER_NAME,
				    MATERIAL_TYPE,
				    MATERIAL_CATEGORY,
				    CREATED_DATE,
				    MRP_TYPE,
				    MRP_CONTROLLER,
				    PURCHASING_GROUP,
				    PROCUREMENT_TYPE,
				    SPECIAL_PROC_CODE,
				    STOCKING_POLICY,
				    MRP_GROUP,
				    CONSUMPTION_MODE,
				    BWD_CONSUMPTION,
				    FWD_CONSUMPTION,
				    INDIVIDUAL_COLLECT,
				    AVAILABILITY_CHECK,
				    BULK_MATERIAL_IND,
				    BUCKFLUSH_IND,
				    SAFETY_STOCK,
				    REORDER_POINT,
				    PLANNED_DELIV_TIME,
				    T_REPLENISHMENT_LT,
				    IN_HOUSE_PRODN_LT,
				    GR_PROCESSING_TIME,
				    LOT_SIZE_CODE,
				    MINIMUM_LOT_SIZE,
				    FIXED_LOT_SIZE,
				    ROUNDING_VALUE,
				    CALCULATED_ABC,
				    CALCULATED_FMR,
				    AMU,
				    AMU_ONEMM,
				    AMF,
				    AMF_ONEMM,
				    SS3,
				    VENDOR_CODE,
				    VENDOR_SHORT_NAME,
				    VENDOR_FULL_NAME,
				    VENDOR_PARENT_CODE,
				    VENDOR_PARENT_NAME,
				    REPL_STRATEGY,
				    COMMODITY_CODE,
				    UNIT_COST,
				    GRA_TYPE,
				    BU,
				    PRODUCT_LINE,
				    PRODUCTION_LINE,
				    LOCAL_PRODUCT_LINE
		    	FROM
			    MY_PLANNING_TODAY_MAT_DETAIL_V t
		    </when>
		    <when test="category == '2. Master Data (Vend)'.toString()">
				SELECT
				    CATEGORY,
				    SUBJECT,
				    DESCRIPTION,
				    PROPOSAL_ACTION,
				    CLASSIFICATION,
				    KPI_IMPACTED,
				    PRIORITY,
				    CREATE_DATE$,
				    MATERIAL_OWNER_SESA,
					MATERIAL_OWNER_NAME,
					MATERIAL_OWNER_ORGANIZATION,
					ACTION_OWNER_SESA,
					ACTION_OWNER_NAME,
					ACTION_OWNER_ORGANIZATION,
				    TOTAL_PENDING_DAYS_CD,
				    TOTAL_PENDING_DAYS_WD,
				    ACTION,
				    TITLE,
				    MATERIAL,
				    PLANT_CODE,
				    ENTITY,
				    CLUSTER_NAME,
				    ACTIVENESS,
				    PROCUREMENT_PLANT,
				    PURCHASING_ORG,
				    QUOTA,
				    VENDOR_CODE,
				    SOURCE_NUMBER,
				    SOURCE_BLOCK_IND,
				    SOURCE_VALID_FROM,
				    SOURCE_VALID_TO,
				    FIXED_VENDOR_IND,
				    FIXED_ISSUE_PLT_IND,
				    MRP_INDICATOR,
				    PURCH_INFO_RECORD,
				    INFO_CATEGORY,
				    MRP1_MIN_LOT_SIZE,
				    PIR_MIN_LOT_SIZE,
				    MRP1_PDT,
				    PIR_PDT
				FROM
			    MY_PLANNING_TODAY_VEND_DETAIL_V t
		    </when>
		    <when test="category == '3. Sales Order'.toString()">
				SELECT
				    CATEGORY,
				    SUBJECT,
				    DESCRIPTION,
				    PROPOSAL_ACTION,
				    CLASSIFICATION,
				    KPI_IMPACTED,
				    PRIORITY,
				    CREATE_DATE$,
				    MATERIAL_OWNER_SESA,
					MATERIAL_OWNER_NAME,
					MATERIAL_OWNER_ORGANIZATION,
					ACTION_OWNER_SESA,
					ACTION_OWNER_NAME,
					ACTION_OWNER_ORGANIZATION,
				    TOTAL_PENDING_DAYS_CD,
				    TOTAL_PENDING_DAYS_WD,
				    ACTION,
				    TITLE,
				    MATERIAL,
				    PLANT_CODE,
				    PLANT_TYPE,
				    ENTITY,
				    CLUSTER_NAME,
				    CREATED_DATE,
				    MRP_TYPE,
				    MRP_CONTROLLER,
				    STOCKING_POLICY,
				    AVAILABILITY_CHECK,
				    CALCULATED_ABC,
				    CALCULATED_FMR,
				    VENDOR_CODE,
				    VENDOR_SHORT_NAME,
				    VENDOR_FULL_NAME,
				    VENDOR_PARENT_CODE,
				    VENDOR_PARENT_NAME,
				    UNIT_COST,
				    DELIVERING_PLANT,
				    DIST_CHANNEL_SP_ST,
				    GRA_TYPE,
				    BU,
				    PRODUCT_LINE,
				    PRODUCTION_LINE,
				    LOCAL_BU,
				    LOCAL_PRODUCT_LINE,
				    LOCAL_PRODUCT_FAMILY,
				    LOCAL_PRODUCT_SUBFAMILY,
				    FULFILL_OR_NOT_NONBLOCK,
				    FULFILL_STRATEGY,
				    FULFILL_STRATEGY_UNBLOCK,
				    PARTIALL_FULFILL_WITH_QTY_UNBLOCK,
				    SALES_ORDER_NUMBER,
				    SALES_ORDER_ITEM,
			    	VIP_SO_INDICATOR,
			    	ITEM_CATEGORY,
				    PLANT_SCOPE,
				    SALES_ORGANIZATION,
				    SO_PRODUCT_LINE,
				    SALES_DISTRICT,
				    SO_STOCKING_POLICY,
				    SOLD_TO,
				    SOLD_TO_SHORT_NAME,
				    SOLD_TO_FULL_NAME,
				    SOLD_TO_PARENT_NAME,
				    SOLD_TO_PARENT_CODE,
				    SOLD_TO_COUNTRY,
				    SOLD_TO_REGION,
				    SHIP_TO,
				    SHIP_TO_SHORT_NAME,
				    SHIP_TO_FULL_NAME,
				    SHIP_TO_PARENT_NAME,
				    SHIP_TO_PARENT_CODE,
				    SHIP_TO_COUNTRY,
				    CUST_PO_DATE,
				    CUST_PO_NUMBER,
				    CUST_PO_ITEM,
				    DELIVERY_PRIORITY,
				    DELIVERY_RANGE,
				    CRD_DATE,
				    SO_CONTRACTUAL_GI_DATE,
				    AC2_DATE,
				    CONFIRM_DATE,
				    AC2_TYPE,
				    AC2_RANGE,
				    BTN_DATE,
				    PLAN_GI_DATE,
				    ORDER_QUANTITY,
				    OPEN_SO_QTY,
				    OPEN_UD_QTY,
				    OPEN_UD_VALUE,
				    SO_UU_STOCK,
				    PURCH_ORDER_NUMBER,
				    PURCH_ORDER_ITEM,
				    PO_QTY,
				    PR_NUMBER,
				    PR_ITEM,
				    SHIPPING_POINT,
				    SO_BLOCK_STATUS,
				    UD_LONG_AGING,
				    GRA_STATUS,
				    GRA_EVENT_NAME,
				    COMPL_DELIV_IND,
				    PAST_DUE_INDICATOR,
				    NET_NET_VALUE_RMB,
				    SE_SCOPE,
				    QMAX_LEAD_TIME,
				    VALIDATE_TO,
				    VALIDATE_FROM,
				    COMPL_DELIV_FLAG,
				    MV_SOA_EMAIL,
				    MV_SOA_SESA,
				    MV_SOA_NAME,
				    LV_SOA_EMAIL,
				    LV_SOA_SESA,
				    LV_SOA_NAME
				FROM
			    MY_PLANNING_TODAY_SO_DETAIL_V t
		    </when>
		    <when test="category == '4. Purchasing Order'.toString()">
				SELECT
				    CATEGORY,
				    SUBJECT,
				    DESCRIPTION,
				    PROPOSAL_ACTION,
				    CLASSIFICATION,
				    KPI_IMPACTED,
				    PRIORITY,
				    CREATE_DATE$,
				    MATERIAL_OWNER_SESA,
					MATERIAL_OWNER_NAME,
					MATERIAL_OWNER_ORGANIZATION,
					ACTION_OWNER_SESA,
					ACTION_OWNER_NAME,
					ACTION_OWNER_ORGANIZATION,
				    TOTAL_PENDING_DAYS_CD,
				    TOTAL_PENDING_DAYS_WD,
				    ACTION,
				    TITLE,
				    MATERIAL,
				    PLANT_CODE,
				    PLANT_TYPE,
				    ENTITY,
				    MATERIAL_ST_PLANT,
				    CLUSTER_NAME,
				    MATERIAL_TYPE,
				    EXISTING_IN_BOM,
				    MRP_TYPE,
				    MRP_CONTROLLER,
				    PURCHASING_GROUP,
				    PROCUREMENT_TYPE,
				    STOCKING_POLICY,
				    PLANNED_DELIV_TIME,
				    LT_RANGE,
				    GR_PROCESSING_TIME,
				    ROUNDING_VALUE,
				    CALCULATED_ABC,
				    CALCULATED_FMR,
				    PURCH_ORDER_NUMBER,
				    PURCH_ORDER_ITEM,
			    	PO_CREATED_DATE,
				    SEQUENTIAL_NUMBER,
				    CONFIRM_CAT,
				    PO_AB,
				    PO_LA,
				    PO_NON_ABLA,
				    PO_AB_VALUE,
				    PO_LA_VALUE,
				    PO_NON_ABLA_VALUE,
				    DUEDATE,
				    PASTDUE_QTY,
				    PAST_DUE_INDICATOR,
					RESCHEDULE_OUT_COUNT,
				    PAST_DUE_COUNT,
			    	EXCEPTION_CODE_1,
			    	EXCEPTION_CODE_2,
				    GENERATE_FROM,
				    PR_NUMBER,
				    PR_ITEM,
				    ORDER_QUANTITY,
				    COUNTRY,
				    ITEM_CATEGORY,
				    ABLA_OVERPO,
				    STAT_REL_DEL_DATE,
				    DELIVERY_DATE,
				    OPEN_QTY,
				    SHIPPING_INSTR,
				    MIN_ORDER_QUANTITY,
				    PO_IR,
				    PO_IR_VALUE_CNY,
				    GRA_STATUS,
				    TRANSPORT_TIME,
				    PO_TYPE,
				    PO_STATUS,
				    VENDOR_CODE,
				    VENDOR_SHORT_NAME,
				    VENDOR_FULL_NAME,
				    VENDOR_PARENT_CODE,
				    VENDOR_PARENT_NAME,
				    REPL_STRATEGY,
				    SOURCE_CATEGORY,
				    COMMODITY_CODE,
				    UNIT_COST,
				    GRA_TYPE,
				    GRA_EVENT,
				    BU,
				    PRODUCT_LINE,
				    PRODUCTION_LINE,
				    LOCAL_PRODUCT_LINE
				FROM
			    MY_PLANNING_TODAY_PO_DETAIL_V t
		    </when>
		    <when test="category == '5. Manufacturing Order'.toString()">
				SELECT
				    CATEGORY,
				    SUBJECT,
				    DESCRIPTION,
				    PROPOSAL_ACTION,
				    CLASSIFICATION,
				    KPI_IMPACTED,
				    PRIORITY,
				    CREATE_DATE$,
				    MATERIAL_OWNER_SESA,
					MATERIAL_OWNER_NAME,
					MATERIAL_OWNER_ORGANIZATION,
					ACTION_OWNER_SESA,
					ACTION_OWNER_NAME,
					ACTION_OWNER_ORGANIZATION,
				    TOTAL_PENDING_DAYS_CD,
				    TOTAL_PENDING_DAYS_WD,
				    ACTION,
				    TITLE,
				    MATERIAL,
				    PLANT_CODE,
				    ENTITY,
				    CLUSTER_NAME,
				    MATERIAL_TYPE,
				    MATERIAL_CATEGORY,
				    CREATED_DATE,
				    BU,
				    PRODUCT_LINE,
				    MRP_TYPE,
				    MRP_CONTROLLER,
				    STOCKING_POLICY,
				    AVAILABILITY_CHECK,
				    SAFETY_STOCK,
				    REORDER_POINT,
				    MO_NUMBER,
				    MO_REQ_DATE,
				    PAST_DUE_INDICATOR,
				    MO_TYPE,
				    OBJECT_NUMBER,
				    TECHNICAL_COMPLETION_DATE,
				    DELETION_FLAG,
				    DELIVERY_RANGE,
				    MO_START_DATE,
				    ACTUAL_END,
				    ACTUAL_FINISH,
				    RELEASE_DATE,
				    RESERVE_NUMBER,
				    MO_QTY,
				    MO_VALUE,
				    CONFIRM_QTY,
				    OPEN_MO_QTY,
				    OPEN_MO_VALUE,
				    GR_QTY,
				    GR_DATE,
				    SALES_ORDER_NUMBER,
				    SALES_ORDER_ITEM,
				    GRA_TYPE,
				    GRA_EVENT,
				    SOLD_TO,
				    MO_STATUS,
				    WBS_NUMBER,
				    CALCULATED_ABC,
				    VENDOR_CODE,
				    VENDOR_SHORT_NAME,
				    VENDOR_FULL_NAME,
				    VENDOR_PARENT_CODE,
				    VENDOR_PARENT_NAME,
				    UNIT_COST
				FROM
				MY_PLANNING_TODAY_MO_DETAIL_V t
		    </when>
		    <when test="category == '7. Inventory'.toString()">
				SELECT
				    CATEGORY,
				    SUBJECT,
				    DESCRIPTION,
				    PROPOSAL_ACTION,
				    CLASSIFICATION,
				    KPI_IMPACTED,
				    PRIORITY,
				    CREATE_DATE$,
				    MATERIAL_OWNER_SESA,
					MATERIAL_OWNER_NAME,
					MATERIAL_OWNER_ORGANIZATION,
					ACTION_OWNER_SESA,
					ACTION_OWNER_NAME,
					ACTION_OWNER_ORGANIZATION,
				    TOTAL_PENDING_DAYS_CD,
				    TOTAL_PENDING_DAYS_WD,
				    ACTION,
				    TITLE,
				    MATERIAL,
				    PLANT_CODE,
				    PLANT_TYPE,
				    ENTITY,
				    MATERIAL_ST_PLANT,
				    CLUSTER_NAME,
				    MATERIAL_TYPE,
				    MATERIAL_CATEGORY,
				    EXISTING_IN_BOM,
				    ACTIVENESS,
				    NEW_PRODUCTS,
				    FOLLOW_UP_MATERIAL,
				    MRP_TYPE,
				    MRP_CONTROLLER,
				    PURCHASING_GROUP,
				    PROCUREMENT_TYPE,
				    SPECIAL_PROC_CODE,
				    STOCKING_POLICY,
				    MRP_GROUP,
				    INDIVIDUAL_COLLECT,
				    AVAILABILITY_CHECK,
				    SAFETY_STOCK,
				    REORDER_POINT,
				    PLANNED_DELIV_TIME,
				    LT_RANGE,
				    MINIMUM_LOT_SIZE,
				    CALCULATED_ABC,
				    CALCULATED_FMR,
				    SS2,
				    SS3,
				    VENDOR_CODE,
				    VENDOR_SHORT_NAME,
				    VENDOR_FULL_NAME,
				    VENDOR_PARENT_CODE,
				    VENDOR_PARENT_NAME,
				    REPL_STRATEGY,
				    SOURCE_CATEGORY,
				    COMMODITY_CODE,
				    COMMODITY_CODE_ONE_MM,
				    UNIT_COST,
				    PRICE_UNIT,
				    DIST_CHANNEL_SP_ST,
				    BU,
				    PRODUCT_LINE,
				    PRODUCTION_LINE,
				    LOCAL_BU,
				    LOCAL_PRODUCT_LINE,
				    SPECIAL_ST,
				    SPECIAL_ST_NO,
				    STORAGE_LOCATION,
				    UU_STOCK,
				    UU_STOCK_VALUE,
				    STOCK_IN_QI,
				    STOCK_IN_QI_VALUE,
				    RESTRICTED_STOCK,
				    RESTRICTED_STOCK_VALUE,
				    BLOCKED_STOCK,
				    BLOCKED_STOCK_VALUE,
				    RETURNS_STOCK,
				    RETURNS_STOCK_VALUE,
				    INTER_STK_TRANSFER,
				    INTER_STK_TRANSFER_VALUE,
				    INVENTORY_CATEGORY,
				    LOCATION_CATEGORY,
				    WIP_QTY,
				    WIP_VALUE,
				    GIT_QTY,
				    GIT_VALUE,
				    OPEN_PO,
				    OPEN_PO_VALUE,
				    OPEN_AB,
				    OPEN_AB_VALUE,
				    OPEN_LA,
				    OPEN_LA_VALUE,
				    OPEN_NON_AB_LA,
				    OPEN_NON_AB_LA_VALUE,
				    OPEN_SO,
				    OPEN_SO_VALUE,
				    MO_RESERVATION,
				    MO_RESERVATION_VALUE,
				    EXCESS_ONE_MM,
				    EXCESS_ONE_MM_VALUE,
				    AMU_ONE_MM,
				    AMU_ONE_MM_VALUE,
				    AMF_ONE_MM,
				    AMF_ONE_MM_VALUE
				FROM
			    MY_PLANNING_TODAY_INV_DETAIL_V t
		    </when>
		    <otherwise>
				SELECT * FROM
			    MY_PLANNING_TODAY_MAT_DETAIL_V t
		    </otherwise>
	    </choose>
	    where 1 = 1
	    <include refid="Filter"/>
	    <if test="dateRange != null and dateRange.size() > 0">
		    AND TRUNC(T.CREATE_DATE$,'DD') BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
		    AND TRUNC(TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
	    </if>
    </select>

	<update id="updateFeedbackAccess">
		BEGIN
		UPDATE MY_PLANNING_TODAY_FEEDBACK T
		SET T.ACCESS_LINES = T.OPEN_LINES
		WHERE T.DATE$ = TRUNC(SYSDATE, 'DD')
		AND T.OWNER = #{userid, jdbcType=VARCHAR};

		UPDATE MY_PLANNING_TODAY_FEEDBACK T
		SET T.ACCESS_CNT = NVL(T.ACCESS_CNT, 0) + 1
		WHERE T.DATE$ = TRUNC(SYSDATE, 'DD')
		AND T.OWNER = #{userid, jdbcType=VARCHAR};
		END;
	</update>

	<update id="updateFeedbackView">
		BEGIN
			UPDATE MY_PLANNING_TODAY_FEEDBACK T
			SET T.VIEW_LINES = LEAST(T.OPEN_LINES, NVL(T.VIEW_LINES, 0) + #{viewLines, jdbcType=VARCHAR})
			WHERE T.DATE$ = TRUNC(SYSDATE, 'DD')
			  AND T.OWNER = #{userid, jdbcType=VARCHAR}
			  AND T.SUBJECT = #{subject, jdbcType=VARCHAR};

			UPDATE MY_PLANNING_TODAY_FEEDBACK T
			SET T.VIEW_CNT = NVL(T.VIEW_CNT, 0) + 1
			WHERE T.DATE$ = TRUNC(SYSDATE, 'DD')
			  AND T.OWNER = #{userid, jdbcType=VARCHAR}
			  AND T.SUBJECT = #{subject, jdbcType=VARCHAR};
		END;
	</update>

	<update id="updateFeedbackDownload">
		BEGIN
			UPDATE MY_PLANNING_TODAY_FEEDBACK T
			SET T.DOWNLOAD_LINES = T.OPEN_LINES
			WHERE T.DATE$ = TRUNC(SYSDATE, 'DD')
			  AND T.OWNER = #{userid, jdbcType=VARCHAR}
			  <choose>
				<when test="subject.isEmpty() == false">
					AND T.SUBJECT = #{subject, jdbcType=VARCHAR}
				</when>
				<otherwise>
					AND T.SUBJECT IN (SELECT DISTINCT T.SUBJECT FROM MY_PLANNING_TODAY T WHERE T.CATEGORY = #{category, jdbcType=VARCHAR})
				</otherwise>
			  </choose>;

			UPDATE MY_PLANNING_TODAY_FEEDBACK T
			SET T.DOWNLOAD_CNT = NVL(T.DOWNLOAD_CNT, 0) + 1
			WHERE T.DATE$ = TRUNC(SYSDATE, 'DD')
			  AND T.OWNER = #{userid, jdbcType=VARCHAR}
			  <choose>
				<when test="subject.isEmpty() == false">
					AND T.SUBJECT = #{subject, jdbcType=VARCHAR}
				</when>
				<otherwise>
					AND T.SUBJECT IN (SELECT DISTINCT T.SUBJECT FROM MY_PLANNING_TODAY T WHERE T.CATEGORY = #{category, jdbcType=VARCHAR})
				</otherwise>
			  </choose>;
		END;
	</update>

	<update id="updateAllFeedbackDownload">
		BEGIN
			UPDATE MY_PLANNING_TODAY_FEEDBACK T
			SET T.DOWNLOAD_LINES = #{lines, jdbcType=INTEGER}
			WHERE T.DATE$ = TRUNC(SYSDATE, 'DD')
			  AND T.OWNER = #{userid, jdbcType=VARCHAR};

			UPDATE MY_PLANNING_TODAY_FEEDBACK T
			SET T.DOWNLOAD_CNT = NVL(T.DOWNLOAD_CNT, 0) + 1
			WHERE T.DATE$ = TRUNC(SYSDATE, 'DD')
			  AND T.OWNER = #{userid, jdbcType=VARCHAR};
		END;
	</update>

	<select id="queryReport4" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT OWNER_TYPE as "name", COUNT(1) as "value"
		FROM MY_PLANNING_TODAY_OWNER_V t
		<where>
			<if test="report4Filters != null and report4Filters != ''.toString()">
				and t.${report4Filters}
			</if>
		</where>
		GROUP BY OWNER_TYPE
	</select>

	<sql id="queryReport4DetailsSQL">
		select * from MY_PLANNING_TODAY_OWNER_V t
		<where>
			<if test="report4DetailsType != null and report4DetailsType != ''.toString()">
				and t.OWNER_TYPE = #{report4DetailsType, jdbcType=VARCHAR}
			</if>
			<if test="report4Filters != null and report4Filters != ''.toString()">
				and t.${report4Filters}
			</if>
		</where>
	</sql>

	<select id="queryReport4DetailsCount" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="queryReport4DetailsSQL"/>
		<include refid="global.count_footer"/>
	</select>

	<select id="queryReport4Details" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		<include refid="queryReport4DetailsSQL"/>
		<include refid="global.select_footer"/>
	</select>

</mapper>
