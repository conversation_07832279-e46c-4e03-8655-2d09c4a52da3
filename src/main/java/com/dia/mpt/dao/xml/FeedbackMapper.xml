<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dia.mpt.dao.IFeedbackDao">

    <sql id="filter">
        <if test="filters != null and filters != ''.toString()">
            and ${filters}
        </if>
    </sql>

    <select id="queryAdminCnt" resultType="java.lang.Integer">
        select count(1) from SY_MENU_AUTH t
         where t.user_id = upper(#{userid,jdbcType=VARCHAR})
           and t.menu_code = #{parentCode,jdbcType=VARCHAR}
           and t.accessible = 'true'
           and lower(t.auth_details) = 'admin'
    </select>

    <select id="queryFilters" resultType="java.util.Map">
        SELECT NAME, DECODE(CATEGORY, 'SESA_CODE', 'OWNER', CATEGORY) CATEGORY FROM MY_PLANNING_TODAY_FILTER_V T ORDER BY CATEGORY, NAME
    </select>

    <sql id="queryReport1Sql">
        WITH TEMP AS (
            SELECT T.SUBJECT,
                   T.OWNER,
                   T.OWNER AS OWNER_SESA,
                   NVL(T2.USER_NAME, T.OWNER) AS OWNER_NAME,
                   T2.ENTITY_NAME             AS ORGANIZATION,
                   T2.ENTITY_NAME             AS OWNER_ORGANIZATION,
                   NVL(T.YESTERDAY_SOLVED, 0) AS YESTERDAY_SOLVED,
                   NVL(T.YESTERDAY_ADDED, 0)  AS YESTERDAY_ADDED,
                   NVL(T.OPEN_LINES, 0)       AS OPEN_LINES,
                   <if test="isAdmin == true">
                       NVL(T.ACCESS_LINES, 0)     AS ACCESS_LINES,
                       NVL(T.VIEW_LINES, 0)       AS VIEW_LINES,
                       NVL(T.DOWNLOAD_LINES, 0)   AS DOWNLOAD_LINES,
                       NVL(T.ACCESS_CNT, 0)       AS ACCESS_CNT,
                       NVL(T.VIEW_CNT, 0)         AS VIEW_CNT,
                       NVL(T.DOWNLOAD_CNT, 0)     AS DOWNLOAD_CNT,
                   </if>
                   T3.CATEGORY,
                   T3.SUB_CATEGORY,
                   T3.CLASSIFICATION,
                   T3.PRIORITY,
                   T3.KPI_IMPACTED
            FROM MY_PLANNING_TODAY_FEEDBACK T
                     LEFT JOIN SY_USER_MASTER_DATA T2 ON T.OWNER = T2.SESA_CODE
                     LEFT JOIN MY_PLANNING_TODAY T3 ON T.SUBJECT = T3.SUBJECT AND T.DATE$ = TRUNC(T3.CREATE_DATE$) AND T.OWNER = T3.OWNER
            WHERE T.DATE$ BETWEEN TO_DATE(#{report1DateRange[0], jdbcType=VARCHAR}, 'YYYY/MM/DD') AND TO_DATE(#{report1DateRange[1], jdbcType=VARCHAR}, 'YYYY/MM/DD')
        )
        SELECT <foreach collection="report1SelectedColumn" separator="," item="item">${item}</foreach>,
               SUM(OPEN_LINES) OPEN_LINES,
               <if test="isAdmin == true">
                   SUM(ACCESS_LINES) ACCESS_LINES,
                   SUM(VIEW_LINES) VIEW_LINES,
                   SUM(DOWNLOAD_LINES) DOWNLOAD_LINES,
                   SUM(ACCESS_CNT) ACCESS_CNT,
                   SUM(VIEW_CNT) VIEW_CNT,
                   SUM(DOWNLOAD_CNT) DOWNLOAD_CNT,
               </if>
               SUM(YESTERDAY_SOLVED) YESTERDAY_SOLVED,
               SUM(YESTERDAY_ADDED) YESTERDAY_ADDED
          FROM TEMP T
        <where>
            <include refid="filter"/>
        </where>
        GROUP BY <foreach collection="report1SelectedColumn" separator="," item="item">${item}</foreach>
        ORDER BY <foreach collection="report1SelectedColumn" separator="," item="item">${item}</foreach>
    </sql>

    <select id="queryReport1Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1Sql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1Sql"/>
        <include refid="global.select_footer"/>
    </select>

<!--    <select id="queryReport2Subjects" resultType="java.util.Map">-->
<!--        WITH TEMP AS (-->
<!--            SELECT T.SUBJECT,-->
<!--                   T.OWNER,-->
<!--                   T.OWNER AS OWNER_SESA,-->
<!--                   NVL(T2.USER_NAME, T.OWNER) AS OWNER_NAME,-->
<!--                   T2.ENTITY_NAME             AS ORGANIZATION,-->
<!--                   T2.ENTITY_NAME             AS OWNER_ORGANIZATION,-->
<!--                   T3.CATEGORY,-->
<!--                   T3.SUB_CATEGORY,-->
<!--                   T3.CLASSIFICATION,-->
<!--                   T3.PRIORITY,-->
<!--                   T3.KPI_IMPACTED-->
<!--            FROM MY_PLANNING_TODAY_FEEDBACK T-->
<!--                     LEFT JOIN SY_USER_MASTER_DATA T2 ON T.OWNER = T2.SESA_CODE-->
<!--                     INNER JOIN MY_PLANNING_TODAY T3 ON T.SUBJECT = T3.SUBJECT AND T.DATE$ = TRUNC(T3.CREATE_DATE$) AND T.OWNER = T3.OWNER-->
<!--            WHERE T.DATE$ BETWEEN TO_DATE(#{report1DateRange[0], jdbcType=VARCHAR}, 'YYYY/MM/DD') AND TO_DATE(#{report1DateRange[1], jdbcType=VARCHAR}, 'YYYY/MM/DD')-->
<!--        )-->
<!--        SELECT T.SUBJECT, T.CATEGORY-->
<!--          FROM TEMP T-->
<!--        <where>-->
<!--            <include refid="filter"/>-->
<!--        </where>-->
<!--        GROUP BY T.SUBJECT, T.CATEGORY-->
<!--        ORDER BY T.CATEGORY, T.SUBJECT-->
<!--    </select>-->

<!--    <select id="queryReport2" resultType="java.util.LinkedHashMap">-->
<!--        WITH TEMP AS (-->
<!--            SELECT T.SUBJECT,-->
<!--                   T.OWNER,-->
<!--                   T.OWNER AS OWNER_SESA,-->
<!--                   'TOTAL' AS TOTAL,-->
<!--                   NVL(T2.USER_NAME, T.OWNER) AS OWNER_NAME,-->
<!--                   T2.ENTITY_NAME             AS ORGANIZATION,-->
<!--                   T2.ENTITY_NAME             AS OWNER_ORGANIZATION,-->
<!--                   NVL(T.YESTERDAY_SOLVED, 0) AS YESTERDAY_SOLVED,-->
<!--                   NVL(T.YESTERDAY_ADDED, 0)  AS YESTERDAY_ADDED,-->
<!--                   NVL(T.OPEN_LINES, 0)       AS OPEN_LINES,-->
<!--                   NVL(T.ACCESS_LINES, 0)     AS ACCESS_LINES,-->
<!--                   NVL(T.VIEW_LINES, 0)       AS VIEW_LINES,-->
<!--                   NVL(T.DOWNLOAD_LINES, 0)   AS DOWNLOAD_LINES,-->
<!--                   T3.CATEGORY,-->
<!--                   T3.SUB_CATEGORY,-->
<!--                   T3.CLASSIFICATION,-->
<!--                   T3.PRIORITY,-->
<!--                   T3.KPI_IMPACTED-->
<!--            FROM MY_PLANNING_TODAY_FEEDBACK T-->
<!--                     LEFT JOIN SY_USER_MASTER_DATA T2 ON T.OWNER = T2.SESA_CODE-->
<!--                     INNER JOIN MY_PLANNING_TODAY T3 ON T.SUBJECT = T3.SUBJECT AND T.DATE$ = TRUNC(T3.CREATE_DATE$) AND T.OWNER = T3.OWNER-->
<!--            WHERE T.DATE$ BETWEEN TO_DATE(#{report2DateRange[0], jdbcType=VARCHAR}, 'YYYY/MM/DD') AND TO_DATE(#{report2DateRange[1], jdbcType=VARCHAR}, 'YYYY/MM/DD')-->
<!--        ), TEMP2 AS (-->
<!--            SELECT <foreach collection="report2SelectedColumn" separator="," item="item" close=",">-->
<!--                    ${item}-->
<!--                   </foreach>-->
<!--                   SUBJECT,-->
<!--                   SUM(OPEN_LINES) OPEN_LINES,-->
<!--                   SUM(YESTERDAY_SOLVED) YESTERDAY_SOLVED,-->
<!--                   SUM(YESTERDAY_ADDED) YESTERDAY_ADDED-->
<!--              FROM TEMP T-->
<!--            <where>-->
<!--                <include refid="filter"/>-->
<!--            </where>-->
<!--            GROUP BY SUBJECT <foreach collection="report2SelectedColumn" open="," separator="," item="item">${item}</foreach>-->
<!--        )-->
<!--        SELECT * FROM TEMP2-->
<!--        PIVOT (-->
<!--                SUM(OPEN_LINES) OPEN_LINES,-->
<!--                SUM(YESTERDAY_SOLVED) YESTERDAY_SOLVED,-->
<!--                SUM(YESTERDAY_ADDED) YESTERDAY_ADDED-->
<!--            FOR SUBJECT IN-->
<!--            <choose>-->
<!--                <when test="subjects == null or subjects.size() == 0">-->
<!--                    ('')-->
<!--                </when>-->
<!--                <otherwise>-->
<!--                    <foreach collection="subjects" separator="," item="item" open="(" close=")">-->
<!--                        '${item}'-->
<!--                    </foreach>-->
<!--                </otherwise>-->
<!--            </choose>-->
<!--        )-->
<!--    </select>-->

    <select id="queryPendingFilters" resultType="java.util.Map">
        SELECT NAME, CATEGORY FROM SCPA.MPT_PENDING_FILTER_V T
        ORDER BY CATEGORY, DECODE(NAME, '0CD', 'a', '1CD', 'b', '2-3CD', 'c', '4-7CD', 'd', '8-14CD', 'e', '15-30CD', 'f',
                                  '31-60CD', 'g', '61-90CD', 'h', '91-180CD', 'i', '181-365CD', 'j', '>365CD', 'k', NAME)
    </select>

    <sql id="pendingFilter">
        <if test="pendingFilters != null and pendingFilters != ''.toString()">
            and ${pendingFilters}
        </if>
        <if test="report3DateRange != null and report3DateRange.size() > 0">
            AND TRUNC(T.CREATE_DATE$,'DD') BETWEEN TO_DATE(#{report3DateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
            AND TRUNC(TO_DATE(#{report3DateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
        </if>
    </sql>

    <resultMap id="report3ResultMap" type="com.dia.mpt.bean.FeedbackReport3Bean">
        <result property="CALENDAR_DATE" column="CALENDAR_DATE"/>
        <result property="NAME" column="NAME"/>
        <result property="VALUE" column="VALUE"/>
    </resultMap>

    <sql id="mptPendingHist">
        (SELECT t.*
         FROM SCPA.MPT_PENDING_HIST t
                  INNER JOIN (SELECT DATE$, WORKING_DAY
                              FROM SY_CALENDAR T
                              WHERE T.NAME = 'MPT Working Calendar'
                                AND WORKING_DAY = 1) T1 ON TRUNC(T.CREATE_DATE$, 'DD') = TRUNC(T1.DATE$, 'DD'))
    </sql>

    <select id="queryReport3Legend" resultType="java.lang.String">
        SELECT DISTINCT nvl(${report3SelectedColumn}, 'Others') AS NAME
        FROM <include refid="mptPendingHist"/> T
        <where>
            <include refid="pendingFilter"/>
        </where>
        ORDER BY DECODE(NVL(T.${report3SelectedColumn}, 'Others'), '0CD', 'a', '1CD', 'b', '2-3CD', 'c', '4-7CD', 'd', '8-14CD', 'e', '15-30CD', 'f',
        '31-60CD', 'g', '61-90CD', 'h', '91-180CD', 'i', '181-365CD', 'j', '>365CD', 'k', 'Others', 'zzzz', NVL(T.${report3SelectedColumn}, 'Others'))
    </select>

    <select id="queryReport3" parameterType="java.util.Map" resultMap="report3ResultMap">
        SELECT  TO_CHAR(T.CREATE_DATE$, 'yyyy/mm/dd')       AS CALENDAR_DATE,
                NVL(T.${report3SelectedColumn}, 'Others')   AS NAME,
                COUNT(1)                                    AS VALUE
        FROM <include refid="mptPendingHist"/> T
        <where>
            <include refid="pendingFilter"/>
        </where>
        GROUP BY
        TO_CHAR(T.CREATE_DATE$, 'yyyy/mm/dd'),
        nvl(T.${report3SelectedColumn}, 'Others')
    </select>

    <sql id="report3DetailsSQL">
        WITH TEMP AS (SELECT
                    CATEGORY,
                    SUBJECT,
                    DESCRIPTION,
                    PROPOSAL_ACTION,
                    CLASSIFICATION,
                    KPI_IMPACTED,
                    CREATE_DATE$,
                    MATERIAL_OWNER_NAME,
                    MATERIAL_OWNER_SESA,
                    MATERIAL_OWNER_ORGANIZATION,
                    ACTION_OWNER_NAME,
                    ACTION_OWNER_SESA,
                    TOTAL_PENDING_DAYS_CD,
                    TOTAL_PENDING_DAYS_WD,
                    ACTION,
                    TITLE,
                    TICKET_PENDING_DAYS_RNAGE
            FROM <include refid="mptPendingHist"/> T
            WHERE TRUNC(T.CREATE_DATE$,'DD') = TO_DATE(#{report3SelectedDate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
            <include refid="pendingFilter"/>
            <if test="report3SelectedSeriesName != ''.toString() and report3SelectedSeriesName != null">
                and T.${report3SelectedColumn} = #{report3SelectedSeriesName}
            </if>
        )
        SELECT *
        FROM TEMP T
    </sql>

    <select id="queryReport3DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="report3DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport3Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report3DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

</mapper>
