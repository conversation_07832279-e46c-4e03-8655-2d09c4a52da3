<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dia.mpt.dao.IMPTWorkspaceDao">
    <select id="queryCategoryOpts" resultType="java.lang.String">
        SELECT DISTINCT CATEGORY FROM MY_PLANNING_TODAY T WHERE T.CREATE_DATE$ BETWEEN SYSDATE - 60 AND SYSDATE ORDER BY CATEGORY
    </select>

    <select id="queryAdminCnt" resultType="java.lang.Integer">
        select count(1) from SY_MENU_AUTH t
         where t.user_id = upper(#{userid,jdbcType=VARCHAR})
           and t.menu_code = #{parentCode,jdbcType=VARCHAR}
           and t.accessible = 'true'
           and lower(t.auth_details) = 'admin'
    </select>

    <select id="querySubjectOpts" resultType="java.lang.String">
        SELECT DISTINCT SUBJECT
        FROM MY_PLANNING_TODAY T
        WHERE T.CATEGORY = #{category, jdbcType=VARCHAR}
          AND T.CREATE_DATE$ BETWEEN SYSDATE - 60 AND SYSDATE
        ORDER BY SUBJECT
    </select>

    <select id="queryOwnerOpts" resultType="java.util.Map">
        SELECT SESA_CODE AS "key", T.USER_NAME || ' [' || T.EMAIL || ']' AS "label"
        FROM SY_USER_MASTER_DATA T
        WHERE T.USER_NAME IS NOT NULL AND T.SESA_CODE NOT LIKE 'ADM%'
        ORDER BY USER_NAME
    </select>

    <select id="queryCollaboratorsOpts" resultType="java.util.Map">
        SELECT USER_NAME || ' [' || SESA_CODE || ']' AS NAME,
               SESA_CODE AS "VALUE",
               LINE_MANAGER_1 AS MANAGER,
               NVL(ENTITY_NAME, 'Others') AS CATEGORY
        FROM SY_USER_MASTER_DATA
        ORDER BY DECODE(NVL(ENTITY_NAME, 'Others'), 'Others', 'ZZZZZ', NVL(ENTITY_NAME, 'Others')), NAME
    </select>

    <select id="queryCollaboratorsDefault" resultType="java.util.Map">
        SELECT T1.SESA_CODE, T1.ENTITY_NAME
        FROM SY_USER_MASTER_DATA T1 INNER JOIN
             (SELECT LINE_MANAGER_1 AS MANAGER
              FROM SY_USER_MASTER_DATA
              WHERE SESA_CODE = #{userid,jdbcType=VARCHAR}) T2
        ON T1.SESA_CODE = T2.MANAGER
    </select>

    <select id="queryPlantApprovalOptsOpts" resultType="java.util.Map">
        SELECT T1.USER_NAME || ' [' || T1.SESA_CODE || ']' AS NAME,
               T1.SESA_CODE                                AS "VALUE",
               NVL(T1.ENTITY_NAME, 'Others')               AS CATEGORY
        FROM SY_USER_MASTER_DATA T1
                 INNER JOIN
             (SELECT T.LINE_MANAGER_1 AS SESA_CODE
              FROM SY_USER_MASTER_DATA T
              WHERE T.LINE_MANAGER_1 LIKE 'SESA%'
              UNION
              SELECT APPROVER_SESA
              FROM MY_PLANNING_TODAY_APPROVER T
              WHERE T.IS_PLANT_APPROVER = 'Y') T2 ON T1.SESA_CODE = T2.SESA_CODE
        WHERE T1.SESA_CODE != #{userid,jdbcType=VARCHAR}
    </select>

    <select id="queryCentralApprovalOptsOpts" resultType="java.util.Map">
        SELECT T1.USER_NAME || ' [' || T1.SESA_CODE || ']' AS NAME,
               T1.SESA_CODE                                AS "VALUE",
               NVL(T1.ENTITY_NAME, 'Others')               AS CATEGORY
        FROM SY_USER_MASTER_DATA T1
                 INNER JOIN
             (SELECT APPROVER_SESA
              FROM MY_PLANNING_TODAY_APPROVER T
              WHERE T.IS_CENTRAL_APPROVER = 'Y') T2 ON T1.SESA_CODE = T2.APPROVER_SESA
    </select>

    <insert id="saveTicket">
        DECLARE
        	CLOB_CONTENT CLOB := #{comments, jdbcType=CLOB};
		BEGIN
            INSERT INTO MY_PLANNING_TODAY_TICKETS
                (ID, TITLE, CONTENT, CATEGORY, SUBJECTS, START_DATE, END_DATE,
                 REQUEST_BY, ACTION_OWNER, APPROVER, ASSIGNED_BY,
                 COLLABORATORS, STATUS, CREATE_BY$, CREATE_DATE$, ACTION)
            VALUES
                (#{id, jdbcType=VARCHAR}, #{title, jdbcType=VARCHAR}, CLOB_CONTENT, #{category, jdbcType=VARCHAR},
                 #{subjects, jdbcType=VARCHAR}, to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd'), to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'),
                 #{requestBy, jdbcType=VARCHAR},  #{actionOwner, jdbcType=VARCHAR}, #{approver, jdbcType=VARCHAR},#{assignedBy, jdbcType=VARCHAR},
                #{collaborators, jdbcType=VARCHAR}, #{status, jdbcType=VARCHAR}, #{requestBy, jdbcType=VARCHAR}, SYSDATE, #{action, jdbcType=VARCHAR});
        END;
    </insert>

    <insert id="saveLog">
        INSERT INTO MY_PLANNING_TODAY_TICKETS_LOGS
            (PARENT_ID,OPR_TIME, OPERATOR, OPERATION)
        VALUES
            (#{id, jdbcType=VARCHAR}, SYSDATE, #{operator, jdbcType=VARCHAR},  #{operation, jdbcType=VARCHAR})
    </insert>

    <insert id="saveNotice">
        INSERT INTO MY_PLANNING_TODAY_TICKETS_NOTICE
            (PARENT_ID, CONTENT, RECIEVER, CREATE_DATE$, CREATE_BY$)
        <foreach collection="receivers" item="item" separator=" union all ">
            SELECT #{id, jdbcType=VARCHAR}, #{content, jdbcType=VARCHAR}, #{item, jdbcType=VARCHAR}, SYSDATE, #{createBy, jdbcType=VARCHAR} FROM DUAL
        </foreach>
    </insert>

    <insert id="saveMat">
        INSERT INTO MY_PLANNING_TODAY_TICKETS_MAT
            (PARENT_ID, PLANT_CODE, MATERIAL)
        <foreach collection="lists" item="item" separator=" union all ">
            SELECT #{id, jdbcType=VARCHAR}, #{item.PLANT_CODE, jdbcType=VARCHAR}, #{item.MATERIAL, jdbcType=VARCHAR} FROM DUAL
        </foreach>
    </insert>

    <insert id="saveVend">
        INSERT INTO MY_PLANNING_TODAY_TICKETS_VEND
            (PARENT_ID, PLANT_CODE, MATERIAL, VENDOR_CODE)
        <foreach collection="lists" item="item" separator=" union all ">
            SELECT #{id, jdbcType=VARCHAR}, #{item.PLANT_CODE, jdbcType=VARCHAR}, #{item.MATERIAL, jdbcType=VARCHAR},
            #{item.VENDOR_CODE, jdbcType=VARCHAR} FROM DUAL
        </foreach>
    </insert>

    <insert id="saveSO">
        INSERT INTO MY_PLANNING_TODAY_TICKETS_SO
            (PARENT_ID, SALES_ORDER_NUMBER, SALES_ORDER_ITEM, MATERIAL, PLANT_CODE)
        <foreach collection="lists" item="item" separator=" union all ">
            SELECT #{id, jdbcType=VARCHAR}, #{item.SALES_ORDER_NUMBER, jdbcType=VARCHAR}, #{item.SALES_ORDER_ITEM, jdbcType=VARCHAR},
            #{item.MATERIAL, jdbcType=VARCHAR}, #{item.PLANT_CODE, jdbcType=VARCHAR} FROM DUAL
        </foreach>
    </insert>

    <insert id="savePO">
        INSERT INTO MY_PLANNING_TODAY_TICKETS_PO
            (PARENT_ID, PURCH_ORDER_NUMBER, PURCH_ORDER_ITEM, MATERIAL, PLANT_CODE)
        <foreach collection="lists" item="item" separator=" union all ">
            SELECT #{id, jdbcType=VARCHAR}, #{item.PURCH_ORDER_NUMBER, jdbcType=VARCHAR}, #{item.PURCH_ORDER_ITEM, jdbcType=VARCHAR},
            #{item.MATERIAL, jdbcType=VARCHAR}, #{item.PLANT_CODE, jdbcType=VARCHAR}  FROM DUAL
        </foreach>
    </insert>

    <insert id="saveMO">
        INSERT INTO MY_PLANNING_TODAY_TICKETS_MO
            (PARENT_ID, MO_NUMBER, MATERIAL, PLANT_CODE)
        <foreach collection="lists" item="item" separator=" union all ">
            SELECT #{id, jdbcType=VARCHAR}, #{item.MO_NUMBER, jdbcType=VARCHAR},
            #{item.MATERIAL, jdbcType=VARCHAR}, #{item.PLANT_CODE, jdbcType=VARCHAR}  FROM DUAL
        </foreach>
    </insert>

    <insert id="saveINV">
        INSERT INTO MY_PLANNING_TODAY_TICKETS_INV
            (PARENT_ID, MATERIAL, PLANT_CODE, STORAGE_LOCATION, SPECIAL_ST, SCOPE$)
        <foreach collection="lists" item="item" separator=" union all ">
            SELECT #{id, jdbcType=VARCHAR}, #{item.MATERIAL, jdbcType=VARCHAR}, #{item.PLANT_CODE, jdbcType=VARCHAR},
                  #{item.STORAGE_LOCATION, jdbcType=VARCHAR}, #{item.SPECIAL_ST, jdbcType=VARCHAR}, #{item.SCOPE$, jdbcType=VARCHAR} FROM DUAL
        </foreach>
    </insert>

    <select id="queryTicketsByStatus" resultType="java.util.Map">
        WITH TEMP AS (
            SELECT ID, TITLE, CATEGORY, SUBJECTS, ACTION_OWNER, COLLABORATORS, APPROVER, ACTION, REQUEST_BY,
                   TO_CHAR(START_DATE, 'YYYY/MM/DD') || ' - ' || TO_CHAR(END_DATE, 'YYYY/MM/DD') AS ACTIVE_PERIOD,
                   CASE WHEN STATUS != 'Taking Effect' THEN STATUS
                        WHEN TRUNC(SYSDATE, 'DD') &lt; START_DATE THEN 'Not Started'
                        WHEN TRUNC(SYSDATE, 'DD') &gt; END_DATE THEN 'Expired' ELSE STATUS END AS STATUS,
                        CREATE_DATE$, TO_CHAR(CREATE_DATE$,'YYYY/MM/DD HH24:MI:SS') CREATE_TIME
            FROM MY_PLANNING_TODAY_TICKETS
            WHERE CREATE_DATE$ BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') AND TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd') + 1
            <choose>
                <when test="activeName == 'MY_REQUEST'.toString()">
                    AND REQUEST_BY = #{session.userid, jdbcType=VARCHAR} AND STATUS NOT IN ('Closed', 'Reject', 'Expired')
                    AND TRUNC(SYSDATE, 'DD') BETWEEN START_DATE AND END_DATE
                </when>
                <when test="activeName == 'MY_APPROVAL'.toString()">
                    AND APPROVER = #{session.userid, jdbcType=VARCHAR}
                </when>
                <when test="activeName == 'REFERRED_TO_ME'.toString()">
                    AND (COLLABORATORS LIKE '%"' || #{session.userid, jdbcType=VARCHAR} || '"%' OR (ACTION_OWNER = #{session.userid, jdbcType=VARCHAR} AND REQUEST_BY != #{session.userid, jdbcType=VARCHAR}))
                </when>
                <when test="activeName == 'FINISHED'.toString()">
                    AND REQUEST_BY = #{session.userid, jdbcType=VARCHAR} AND STATUS IN ('Closed', 'Reject', 'Expired')
                </when>
                <when test="activeName == 'ALL_REQUESTS'.toString()">
                    AND REQUEST_BY = #{session.userid, jdbcType=VARCHAR}
                </when>
                <when test="activeName == 'ADMIN'.toString()">
                    <if test="category.size() > 0">
                        AND CATEGORY IN
                        <foreach collection="category" item="item" open="(" close=")" separator=",">#{item, jdbcType=VARCHAR}</foreach>
                    </if>
                    <if test="owners.size() > 0">
                        AND (REQUEST_BY IN
                        <foreach collection="owners" item="item" open="(" close=")" separator=",">#{item, jdbcType=VARCHAR}</foreach>
                        OR ACTION_OWNER IN
                        <foreach collection="owners" item="item" open="(" close=")" separator=",">#{item, jdbcType=VARCHAR}</foreach>
                        )
                    </if>
                    <if test="actionType.size() > 0">
                        AND ACTION IN
                        <foreach collection="actionType" item="item" open="(" close=")" separator=",">#{item, jdbcType=VARCHAR}</foreach>
                    </if>
                    <if test="title != null and title != ''.toString()">
                        AND (TITLE LIKE '%' || #{title, jdbcType=VARCHAR} || '%' OR SUBJECTS LIKE '%' || #{title, jdbcType=VARCHAR} || '%')
                    </if>
                </when>
                <otherwise>
                    AND 0 = 1
                </otherwise>
            </choose>
        )
        SELECT * FROM TEMP
        <if test="status.size() > 0">
            WHERE STATUS IN
            <foreach collection="status" item="item" open="(" close=")" separator=",">#{item, jdbcType=VARCHAR}</foreach>
        </if>
        ORDER BY DECODE(STATUS, 'Closed', 99, 'Expired', 98, 'Reject', 97, 'Not Started', 96, 0) ASC, CREATE_DATE$ DESC
        OFFSET 0 ROWS FETCH NEXT 20 ROWS ONLY
    </select>

    <select id="queryUserMapping" resultType="java.util.Map">
        SELECT SESA_CODE, CASE WHEN EMAIL IS NOT NULL THEN USER_NAME || ' [' || EMAIL|| ']' ELSE USER_NAME END AS USER_DESC, USER_NAME  FROM SY_USER_MASTER_DATA
    </select>

    <select id="queryTicketInfoById" resultType="java.util.Map">
        SELECT ID, TITLE, CONTENT, CATEGORY, SUBJECTS, REQUEST_BY, ACTION_OWNER, ASSIGNED_BY,
               TO_CHAR(START_DATE, 'YYYY/MM/DD') START_DATE, TO_CHAR(END_DATE, 'YYYY/MM/DD') END_DATE,
               COLLABORATORS, CREATE_BY$ AS CREATE_BY, TO_CHAR(CREATE_DATE$,'YYYY/MM/DD HH24:MI:SS') CREATE_TIME, APPROVER, ACTION,
               CASE WHEN STATUS != 'Taking Effect' THEN STATUS
                    WHEN TRUNC(SYSDATE, 'DD') &lt; START_DATE THEN 'Not Started'
                    WHEN TRUNC(SYSDATE, 'DD') &gt; END_DATE THEN 'Expired' ELSE STATUS END AS STATUS
        FROM MY_PLANNING_TODAY_TICKETS T
        WHERE T.ID = #{id, jdbcType=VARCHAR}
    </select>

    <select id="queryTicketLogsById" resultType="java.util.Map">
        SELECT TO_CHAR(OPR_TIME, 'YYYY/MM/DD HH24:MI') AS OPR_TIME,
               TO_CHAR(OPR_TIME, 'YYYY/MM/DD') AS OPR_DATE, OPERATOR, OPERATION
        FROM MY_PLANNING_TODAY_TICKETS_LOGS T WHERE T.PARENT_ID = #{id, jdbcType=VARCHAR} ORDER BY T.OPR_TIME
    </select>

    <select id="queryTicketRepliesById" resultType="java.util.Map">
        SELECT ROWIDTOCHAR(ROWID) ROW_ID, TO_CHAR(REPLY_TIME, 'YYYY/MM/DD HH24:MI:SS') REPLY_TIME, REPLIER, CONTENT FROM MY_PLANNING_TODAY_TICKETS_REPLIES T WHERE T.PARENT_ID = #{id, jdbcType=VARCHAR} ORDER BY T.REPLY_TIME
    </select>

    <select id="queryTicketDetailsById" resultType="java.util.Map" flushCache="true" useCache="false">
        SELECT * FROM
        <choose>
            <when test="CATEGORY == '1. Master Data (Mat)'.toString()">MY_PLANNING_TODAY_TICKETS_MAT</when>
            <when test="CATEGORY == '2. Master Data (Vend)'.toString()">MY_PLANNING_TODAY_TICKETS_VEND</when>
            <when test="CATEGORY == '3. Sales Order'.toString()">MY_PLANNING_TODAY_TICKETS_SO</when>
            <when test="CATEGORY == '4. Purchasing Order'.toString()">MY_PLANNING_TODAY_TICKETS_PO</when>
            <when test="CATEGORY == '5. Manufacturing Order'.toString()">MY_PLANNING_TODAY_TICKETS_MO</when>
            <when test="CATEGORY == '7. Inventory'.toString()">MY_PLANNING_TODAY_TICKETS_INV</when>
        </choose> T WHERE T.PARENT_ID = #{id, jdbcType=VARCHAR}
    </select>

    <insert id="saveTicketComment">
        DECLARE
        	CLOB_CONTENT CLOB := #{comment, jdbcType=CLOB};
		BEGIN
            INSERT INTO MY_PLANNING_TODAY_TICKETS_REPLIES
                (PARENT_ID, REPLY_TIME, REPLIER, CONTENT)
            VALUES
                (#{id, jdbcType=VARCHAR},SYSDATE, #{session.userid, jdbcType=VARCHAR}, CLOB_CONTENT);
        END;
    </insert>

    <delete id="deleteTicketComment">
        DELETE FROM MY_PLANNING_TODAY_TICKETS_REPLIES T WHERE ROWID = #{rowid, jdbcType=VARCHAR}
    </delete>

    <update id="approveTicketById">
        UPDATE MY_PLANNING_TODAY_TICKETS T
           SET T.STATUS = #{status, jdbcType=VARCHAR},
               T.UPDATE_DATE$ = SYSDATE,
               T.UPDATE_BY$ = #{session.userid, jdbcType=VARCHAR}
         WHERE T.ID = #{id, jdbcType=VARCHAR}
    </update>

    <update id="closeTicketById">
        UPDATE MY_PLANNING_TODAY_TICKETS T
           SET T.STATUS = 'Closed',
               T.UPDATE_DATE$ = SYSDATE,
               T.UPDATE_BY$ = #{session.userid, jdbcType=VARCHAR}
         WHERE T.ID = #{id, jdbcType=VARCHAR}
    </update>

    <select id="getCollaboratorsById" resultType="java.lang.String">
        SELECT T.COLLABORATORS FROM MY_PLANNING_TODAY_TICKETS T WHERE T.ID = #{id, jdbcType=VARCHAR}
    </select>

    <update id="updateCollaborators">
        UPDATE MY_PLANNING_TODAY_TICKETS T
           SET T.COLLABORATORS = #{collaborators, jdbcType=VARCHAR},
               T.UPDATE_DATE$ = SYSDATE,
               T.UPDATE_BY$ = #{session.userid, jdbcType=VARCHAR}
         WHERE T.ID = #{id, jdbcType=VARCHAR}
    </update>

    <select id="getActionPeriodById" resultType="java.util.Map">
        SELECT TO_CHAR(T.START_DATE, 'yyyy/mm/dd') START_DATE,
               TO_CHAR(T.END_DATE, 'yyyy/mm/dd') END_DATE
          FROM MY_PLANNING_TODAY_TICKETS T
         WHERE T.ID = #{id, jdbcType=VARCHAR}
    </select>

    <update id="updateActionPeriod">
        UPDATE MY_PLANNING_TODAY_TICKETS T
           SET T.START_DATE = to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd'),
               T.END_DATE = to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'),
               T.UPDATE_DATE$ = SYSDATE,
               T.UPDATE_BY$ = #{session.userid, jdbcType=VARCHAR}
         WHERE T.ID = #{id, jdbcType=VARCHAR}
    </update>

    <update id="updateActionOwner">
        UPDATE MY_PLANNING_TODAY_TICKETS T
           SET T.ACTION_OWNER = #{owner, jdbcType=VARCHAR},
               T.UPDATE_DATE$ = SYSDATE,
               T.UPDATE_BY$ = #{session.userid, jdbcType=VARCHAR}
         WHERE T.ID = #{id, jdbcType=VARCHAR}
    </update>

    <select id="queryQuickAccessApproval" resultType="java.util.Map">
        SELECT T.ID, T.TITLE, T2.USER_NAME
        FROM MY_PLANNING_TODAY_TICKETS T
                 LEFT JOIN SY_USER_MASTER_DATA T2 ON T.REQUEST_BY = T2.SESA_CODE
        WHERE TRUNC(SYSDATE, 'DD') BETWEEN T.START_DATE AND T.END_DATE
          AND T.STATUS = 'Waiting for Approval'
          AND T.APPROVER = #{session.userid, jdbcType=VARCHAR}
    </select>

    <select id="queryQuickAccessForward" resultType="java.util.Map">
        SELECT T.ID, T.TITLE, T2.USER_NAME
        FROM MY_PLANNING_TODAY_TICKETS T
                 LEFT JOIN SY_USER_MASTER_DATA T2 ON T.REQUEST_BY = T2.SESA_CODE
        WHERE TRUNC(SYSDATE, 'DD') BETWEEN T.START_DATE AND T.END_DATE
          AND T.STATUS = 'Waiting for Acceptance'
          AND T.ACTION_OWNER = #{session.userid, jdbcType=VARCHAR}
    </select>

    <select id="queryQuickAccessEscalation" resultType="java.util.Map">
        SELECT T.ID, T.TITLE, T2.USER_NAME
        FROM MY_PLANNING_TODAY_TICKETS T
                 LEFT JOIN SY_USER_MASTER_DATA T2 ON T.REQUEST_BY = T2.SESA_CODE
        WHERE TRUNC(SYSDATE, 'DD') BETWEEN T.START_DATE AND T.END_DATE
          AND T.STATUS = 'Waiting for Confirmation'
          AND T.ACTION_OWNER = #{session.userid, jdbcType=VARCHAR}
    </select>

    <select id="queryLatestNews" resultType="java.util.Map">
        SELECT T.PARENT_ID, T.CONTENT, T2.USER_NAME, TO_CHAR(T.CREATE_DATE$, 'YYYY/MM/DD HH24:MI:SS') CREATE_DATE,
        CASE WHEN ABS(T.CREATE_DATE$ - SYSDATE) &lt; 1 THEN 'Y' ELSE 'N' END AS IS_NEW
        FROM MY_PLANNING_TODAY_TICKETS_NOTICE T
                 LEFT JOIN SY_USER_MASTER_DATA T2 ON T.CREATE_BY$ = T2.SESA_CODE
        WHERE T.RECIEVER = #{session.userid, jdbcType=VARCHAR}
        ORDER BY T.CREATE_DATE$ DESC
        OFFSET #{offset, jdbcType=NUMERIC} ROWS FETCH NEXT 10 ROWS ONLY
    </select>
</mapper>
