package com.dia.mpt.dao;

import com.dia.mpt.bean.ActionToDoReport2Bean;
import com.dia.mpt.bean.ActionToDoReport3Bean;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IActionToDoServiceDao {

    List<Map<String, String>> queryFilters();
    List<Map<String, String>> queryReport4Filters();

    String queryAuthDetails(String userid, @Param("menuCode") String menuCode);

    List<Map<String, Object>> queryReport1(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport1Sub(Map<String, Object> parameterMap);

    int queryReport1DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1Details(Map<String, Object> parameterMap);

    List<ActionToDoReport2Bean> queryReport2(Map<String, Object> parameterMap);

    List<ActionToDoReport3Bean> queryReport3(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport4(Map<String, Object> parameterMap);

    int queryReport4DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport4Details(Map<String, Object> parameterMap);

    int queryReport3DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3Details(Map<String, Object> parameterMap);

    List<String> queryReport3Legend(Map<String, Object> parameterMap);

    void updateFeedbackAccess(String userid, List<Map<String, Object>> data);

    void updateFeedbackView(String userid, String subject, int viewLines);

    void updateFeedbackDownload(String userid, String category, String subject);

    void updateAllFeedbackDownload(String userid, int lines);

}
