package com.dia.mpt.dao;

import com.dia.mpt.bean.FeedbackReport3Bean;
import org.apache.ibatis.annotations.Mapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IFeedbackDao {

    List<Map<String, String>> queryFilters();

    int queryAdminCnt(String parentCode, String userid);

    int queryReport1Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1(Map<String, Object> parameterMap);

//    List<Map<String, Object>> queryReport2Subjects(Map<String, Object> parameterMap);
//
//    List<LinkedHashMap<String, Object>> queryReport2(Map<String, Object> parameterMap);

    List<Map<String, String>> queryPendingFilters();

    List<FeedbackReport3Bean> queryReport3(Map<String, Object> parameterMap);

    int queryReport3DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3Details(Map<String, Object> parameterMap);

    List<String> queryReport3Legend(Map<String, Object> parameterMap);
}
