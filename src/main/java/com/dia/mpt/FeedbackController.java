package com.dia.mpt;

import com.dia.mpt.service.IFeedbackService;
import com.dia.mpt.service.impl.FeedbackServiceImpl;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/diagnosis/my_planning_today/feedback", parent = FeedbackServiceImpl.PARENT_CODE)
public class FeedbackController extends ControllerHelper {
    @Resource
    private IFeedbackService feedbackService;

    @SchneiderRequestMapping("/query_filters")
    public Response queryFilters(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return feedbackService.queryFilters(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return feedbackService.queryReport1(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping("/query_privileges")
    public Response queryPrivileges(HttpServletRequest request) {
        super.pageLoad(request);
        return feedbackService.queryPrivileges(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping("/download_report1")
    public void downloadReport1(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        feedbackService.downloadReport1(parameterMap, response, session.getUserid());
    }

//    @SchneiderRequestMapping("/query_report2_subjects")
//    public Response queryReport2Subjects(HttpServletRequest request) {
//        super.pageLoad(request);
//        super.setGlobalCache(true);
//        return feedbackService.queryReport2Subjects(parameterMap);
//    }
//
//    @SchneiderRequestMapping("/query_report2")
//    public Response queryReport2(HttpServletRequest request) {
//        super.pageLoad(request);
//        super.setGlobalCache(true);
//        return feedbackService.queryReport2(parameterMap);
//    }

    @SchneiderRequestMapping("/query_report3")
    public Response queryReport3(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return feedbackService.queryReport3(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping("/query_report3_details")
    public Response queryReport3Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return feedbackService.queryReport3Details(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping("/download_report3_details")
    public void downloadReport3Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        feedbackService.downloadReport3Details(parameterMap, response, session.getUserid());
    }

}
