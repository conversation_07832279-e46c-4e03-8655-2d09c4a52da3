package com.dia.mpt;

import com.dia.mpt.service.IActionToDoService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/diagnosis/my_planning_today/action_to_do", parent = "menuC04")
public class ActionToDoController extends ControllerHelper {
    @Resource
    private IActionToDoService actionToDoService;

    @SchneiderRequestMapping("/query_filters")
    public Response queryFilters() {
        super.setGlobalCache(true);
        return actionToDoService.queryFilters();
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return actionToDoService.queryReport1(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping("/query_report1_sub")
    public Response queryReport1Sub(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return actionToDoService.queryReport1Sub(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping("/query_report1_details")
    public Response queryReport1Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return actionToDoService.queryReport1Details(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping("/download_report1_details")
    public void downloadReport1Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        actionToDoService.downloadReport1Details(parameterMap, response, session.getUserid());
    }

    @SchneiderRequestMapping("/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return actionToDoService.queryReport2(parameterMap);
    }

    @SchneiderRequestMapping("/query_report3")
    public Response queryReport3(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return actionToDoService.queryReport3(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping("/query_report3_details")
    public Response queryReport3Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return actionToDoService.queryReport3Details(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping("/download_report3_details")
    public void downloadReport3Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        actionToDoService.downloadReport3Details(parameterMap, response, session.getUserid());
    }

    @SchneiderRequestMapping("/download_all_subjects")
    public void downloadAllMySubjects(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        actionToDoService.downloadAllSubjects(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report4")
    public Response queryReport4(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return actionToDoService.queryReport4(parameterMap);
    }

    @SchneiderRequestMapping("/query_report4_details")
    public Response queryReport4Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return actionToDoService.queryReport4Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report4_details")
    public void downloadReport4Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        actionToDoService.downloadReport4Details(parameterMap, response);
    }
}
