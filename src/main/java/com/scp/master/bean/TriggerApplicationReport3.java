package com.scp.master.bean;

import com.starter.utils.Utils;

import java.math.BigDecimal;

public class TriggerApplicationReport3 {
    private String name;
    private BigDecimal value;

    public TriggerApplicationReport3(){

    }

    public TriggerApplicationReport3(String name, Object value) {
        this.name = name;
        this.value = Utils.parseBigDecimal(value, null);
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }
}
