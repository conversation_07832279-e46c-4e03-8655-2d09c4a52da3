package com.scp.master.bean;

import com.alibaba.fastjson.JSONObject;

public class MasterDataOutputColumn {
    private String name;
    private int length;

    public MasterDataOutputColumn() {

    }

    public MasterDataOutputColumn(Object jsonObject) {
        JSONObject obj = (JSONObject) jsonObject;
        this.name = obj.getString("name");
        this.length = obj.getInteger("length");
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getLength() {
        return length;
    }

    public void setLength(int length) {
        this.length = length;
    }
}
