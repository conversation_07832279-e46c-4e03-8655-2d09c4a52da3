package com.scp.master;

import com.scp.master.service.ITriggerApplicationReportService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.text.ParseException;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/master/trigger_application_report", parent = "menu230")
public class TriggerApplicationReportController extends ControllerHelper {

    @Resource
    private ITriggerApplicationReportService triggerApplicationReportService;

    @SchneiderRequestMapping("/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return triggerApplicationReportService.initPage();
    }

    @SchneiderRequestMapping("/refresh_trigger_report")
    public Response refreshTriggerReport() {
        return triggerApplicationReportService.refreshTriggerReport();
    }

    @SchneiderRequestMapping("/query_report1_columns")
    public Response queryReport1Columns(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return triggerApplicationReportService.queryReport1Columns(parameterMap);
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return triggerApplicationReportService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping("/query_report1_details")
    public Response queryReport1Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return triggerApplicationReportService.queryReport1Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report1_details")
    public void downloadReport1Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        triggerApplicationReportService.downloadReport1Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report2")
    public Response queryReport2(HttpServletRequest request) throws ParseException {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return triggerApplicationReportService.queryReport2(parameterMap);
    }

    @SchneiderRequestMapping("/query_report3")
    public Response queryReport3(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return triggerApplicationReportService.queryReport3(parameterMap);
    }

    @SchneiderRequestMapping("/query_report3_details")
    public Response queryReport3Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return triggerApplicationReportService.queryReport3Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report3_details")
    public void downloadReport3Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        triggerApplicationReportService.downloadReport3Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report4")
    public Response queryReport4(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return triggerApplicationReportService.queryReport4(parameterMap);
    }

    @SchneiderRequestMapping("/download_report4")
    public void downloadReport4(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        triggerApplicationReportService.downloadReport4(parameterMap, response);
    }
}
