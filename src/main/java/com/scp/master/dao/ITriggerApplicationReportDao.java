package com.scp.master.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface ITriggerApplicationReportDao {

    void refreshTriggerReport();

    List<Map<String, String>> queryCascader();

    String queryReport1Columns();

    List<Map<String, Object>> queryReport1RCA(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport1LTRange(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport2(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport3(Map<String, Object> parameterMap);

    int queryReport1DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1Details(Map<String, Object> parameterMap);

    int queryReport3DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3Details(Map<String, Object> parameterMap);

    int queryReport4Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport4(Map<String, Object> parameterMap);
}
