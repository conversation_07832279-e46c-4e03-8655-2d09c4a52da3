<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.master.dao.ISalesMasterDataSummaryDao">
    <select id="queryCascader" resultType="java.util.Map">
        select * from SCPA.SALES_MASTER_DATE_SUMMARY_FILTER_V order by category,decode (name,'Others','zzz',name)
    </select>

    <sql id="queryReport1Sql">
        select /*+ parallel(t 12) */
        <foreach collection="categroy" separator="," item="item">
            nvl(${item},'Others') "${item}"
        </foreach>,
        MATERIAL,
        MATERIAL_DESCRIPTION,
        PRODUCT_HIERARCHY,
        YY_GDP,
        GDP
        from ${SCPA.SALES_MASTER_DATE_SUMMARY_V} t
        <if test="_filters != null and _filters != ''.toString()">
            WHERE ${_filters}
        </if>
        group by
        <foreach collection="categroy" separator="," item="item">
            nvl(${item},'Others')
        </foreach>,
        MATERIAL,
        MATERIAL_DESCRIPTION,
        PRODUCT_HIERARCHY,
        YY_GDP,
        GDP
        order by
        <foreach collection="categroy" separator="," item="item">
            decode (${item},'Others','zzz',${item})
        </foreach>
    </sql>

    <select id="queryReport1Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1Sql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1Sql"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport1DetailsSql">
        select *
        from ${SCPA.SALES_MASTER_DATE_SUMMARY_V} t
        <where>
            <if test="selectedValue != null and selectedValue.isEmpty() == false">
                <foreach collection="categroy" separator=" and " item="item" index="index" open=" and ">
                    <choose>
                        <when test="selectedValue[index] == 'Others'.toString()">
                            (${item} = 'Others' or ${item} is null)
                        </when>
                        <when test="selectedValue[index] != null and selectedValue[index] != ''.toString()">
                            ${item} = #{selectedValue[${index}], jdbcType=VARCHAR}
                        </when>
                    </choose>
                </foreach>
            </if>
            <if test="_filters != null and _filters != ''.toString()">
                and ${_filters}
            </if>
        </where>
    </sql>


    <select id="queryReport1DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1DetailsSql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1DetailsSql"/>
        <include refid="global.select_footer"/>
    </select>
</mapper>
