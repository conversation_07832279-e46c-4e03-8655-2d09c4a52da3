<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.master.dao.ICtoBomDao">
    <select id="queryCascader" resultType="java.util.Map">
        SELECT NAME,
               CATEGORY
        FROM SCPA.CTO_BOM_FILTER_V
        ORDER BY CATEGORY, DECODE(NAME, 'Others', 'zzz', NAME)
    </select>

    <select id="queryReport1Columns" resultType="java.lang.String">
        SELECT DISTINCT TO_CHAR(ACTUAL_RELEASE_DATE, 'YYYYMM') AS ACTUAL_RELEASE_MONTH
        FROM SCPA.CTO_RESERVATION_V
        <where>
            <if test="filters != null and filters != ''.toString()">
                ${filters}
            </if>
            AND ACTUAL_RELEASE_DATE between TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') and to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
            AND <foreach collection="categroy" separator="||" item="item">
            ${item}
        </foreach> || ACTUAL_RELEASE_DATE IS NOT NULL
        </where>
        ORDER BY TO_CHAR(ACTUAL_RELEASE_DATE, 'YYYYMM') DESC
    </select>

    <sql id="queryReport1Sql">
        WITH CTO_BOM AS (SELECT <foreach collection="categroy" separator="," item="item">
                                    ${item}
                                </foreach>,
                                MAX(CURRENT_BASE_QUANTITY) AS CURRENT_BASE_QUANTITY,
                                SUM(CURRENT_COMPONENT_QTY) AS CURRENT_COMPONENT_QTY
                        FROM SCPA.CTO_BOM_V
                        <where>
                            <if test="filters != null and filters != ''.toString()">
                                ${filters}
                            </if>
                            AND <foreach collection="categroy" separator="||" item="item">
                                    ${item}
                                </foreach>IS NOT NULL
                        </where>
                        GROUP BY <foreach collection="categroy" separator="," item="item">
                                    ${item}
                                </foreach>),
            CTO_RESERVATION_HEADER AS (SELECT <foreach collection="categroy" separator="," item="item">
                                                <if test="item != 'PLANNING_BOM_COMPONENT'.toString()">
                                                    ${item}
                                                </if>
                                              </foreach>,
                                                SUM(QUANTITY) AS GROUP_QTY
                                        FROM (SELECT MO_NUMBER,
                                                    <foreach collection="categroy" separator="," item="item">
                                                        <if test="item != 'PLANNING_BOM_COMPONENT'.toString()">
                                                            ${item}
                                                        </if>
                                                    </foreach>,
                                                    MAX(QUANTITY) AS QUANTITY
                                                FROM SCPA.CTO_RESERVATION_V
                                                <where>
                                                    <if test="filters != null and filters != ''.toString()">
                                                        ${filters}
                                                    </if>
                                                    AND ACTUAL_RELEASE_DATE between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') and to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                                                    or ACTUAL_RELEASE_DATE is null
                                                </where>
                                                GROUP BY MO_NUMBER, <foreach collection="categroy" separator="," item="item">
                                                                        <if test="item != 'PLANNING_BOM_COMPONENT'.toString()">
                                                                            ${item}
                                                                        </if>
                                                                    </foreach>)
                                        WHERE <foreach collection="categroy" separator="||" item="item">
                                                <if test="item != 'PLANNING_BOM_COMPONENT'.toString()">
                                                    ${item}
                                                </if>
                                              </foreach>IS NOT NULL
                                        GROUP BY <foreach collection="categroy" separator="," item="item">
                                                    <if test="item != 'PLANNING_BOM_COMPONENT'.toString()">
                                                        ${item}
                                                    </if>
                                                </foreach>),
            CTO_RESERVATION_ITEM AS (SELECT <foreach collection="categroy" separator="," item="item">
                                                ${item}
                                            </foreach>,
                                            SUM(REQUIREMENT_QUANTITY) AS ITEM_QTY
                                    FROM SCPA.CTO_RESERVATION_V
                                    <where>
                                        <if test="filters != null and filters != ''.toString()">
                                            ${filters}
                                        </if>
                                        AND ACTUAL_RELEASE_DATE between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') and to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                                        AND <foreach collection="categroy" separator="||" item="item">
                                                ${item}
                                            </foreach>IS NOT NULL
                                    </where>
                                    GROUP BY <foreach collection="categroy" separator="," item="item">
                                                ${item}
                                            </foreach>),
            CTO_RESERVATION AS (SELECT <foreach collection="categroy" separator="," item="item">
                                            IT.${item}
                                        </foreach>,
                                        HD.GROUP_QTY,
                                        IT.ITEM_QTY
                                FROM CTO_RESERVATION_HEADER HD
                                        LEFT JOIN CTO_RESERVATION_ITEM IT
                                                    ON <foreach collection="categroy" separator="AND" item="item">
                                                            <if test="item != 'PLANNING_BOM_COMPONENT'.toString()">
                                                                HD.${item} = IT.${item}
                                                            </if>
                                                        </foreach>
                                <where>
                                    <foreach collection="categroy" separator="AND" item="item">
                                        <if test="item != 'PLANNING_BOM_COMPONENT'.toString()">
                                           IT.${item} IS NOT NULL
                                        </if>
                                    </foreach>
                                </where>),
            CTO_RESERVATION_HEADER_MONTH AS (SELECT <foreach collection="categroy" separator="," item="item">
                                                        <if test="item != 'PLANNING_BOM_COMPONENT'.toString()">
                                                            ${item}
                                                        </if>
                                                    </foreach>,
                                                    ACTUAL_RELEASE_MONTH,
                                                    SUM(QUANTITY) AS GROUP_QTY
                                            FROM (SELECT MO_NUMBER,
                                                        <foreach collection="categroy" separator="," item="item">
                                                            <if test="item != 'PLANNING_BOM_COMPONENT'.toString()">
                                                                ${item}
                                                            </if>
                                                        </foreach>,
                                                        MAX(QUANTITY) AS QUANTITY,
                                                        TO_CHAR(ACTUAL_RELEASE_DATE, 'YYYYMM') AS ACTUAL_RELEASE_MONTH
                                                    FROM SCPA.CTO_RESERVATION_V
                                                    <where>
                                                        <if test="filters != null and filters != ''.toString()">
                                                            ${filters}
                                                        </if>
                                                        AND ACTUAL_RELEASE_DATE between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') and to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                                                        OR ACTUAL_RELEASE_DATE is null
                                                    </where>
                                                    GROUP BY MO_NUMBER,
                                                            <foreach collection="categroy" separator="," item="item">
                                                                <if test="item != 'PLANNING_BOM_COMPONENT'.toString()">
                                                                    ${item}
                                                                </if>
                                                            </foreach>,
                                                            TO_CHAR(ACTUAL_RELEASE_DATE, 'YYYYMM'))
                                            WHERE <foreach collection="categroy" separator="||" item="item">
                                                        <if test="item != 'PLANNING_BOM_COMPONENT'.toString()">
                                                            ${item}
                                                        </if>
                                                    </foreach> || ACTUAL_RELEASE_MONTH IS NOT NULL
                                            GROUP BY <foreach collection="categroy" separator="," item="item">
                                                        <if test="item != 'PLANNING_BOM_COMPONENT'.toString()">
                                                            ${item}
                                                        </if>
                                                    </foreach>, ACTUAL_RELEASE_MONTH),
            CTO_RESERVATION_ITEM_MOTH AS (SELECT <foreach collection="categroy" separator="," item="item">
                                                    ${item}
                                                </foreach>,
                                                TO_CHAR(ACTUAL_RELEASE_DATE, 'YYYYMM') AS ACTUAL_RELEASE_MONTH,
                                                SUM(REQUIREMENT_QUANTITY) AS ITEM_QTY
                                            FROM SCPA.CTO_RESERVATION_V
                                            <where>
                                                <if test="filters != null and filters != ''.toString()">
                                                    ${filters}
                                                </if>
                                                AND ACTUAL_RELEASE_DATE between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') and to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                                                AND <foreach collection="categroy" separator="||" item="item">
                                                        ${item}
                                                    </foreach> || ACTUAL_RELEASE_DATE IS NOT NULL
                                            </where>
                                            GROUP BY <foreach collection="categroy" separator="," item="item">
                                                        ${item}
                                                    </foreach>,
                                                    TO_CHAR(ACTUAL_RELEASE_DATE, 'YYYYMM')),
            CTO_RESERVATION_MONTH AS (SELECT *
                                    FROM (SELECT <foreach collection="categroy" separator="," item="item">
                                                    IT.${item} AS ${item}_temp
                                                </foreach>,
                                                ROUND(IT.ITEM_QTY / HD.GROUP_QTY,3)  ACCUMULATE_RATIO,
                                                IT.ITEM_QTY,
                                                HD.GROUP_QTY,
                                                HD.ACTUAL_RELEASE_MONTH
                                            FROM CTO_RESERVATION_HEADER_MONTH HD
                                                    LEFT JOIN CTO_RESERVATION_ITEM_MOTH IT
                                                                ON <foreach collection="categroy" separator="AND" item="item">
                                                                        <if test="item != 'PLANNING_BOM_COMPONENT'.toString()">
                                                                            HD.${item} = IT.${item}
                                                                        </if>
                                                                    </foreach>
                                                                    AND HD.ACTUAL_RELEASE_MONTH = IT.ACTUAL_RELEASE_MONTH)
                                        PIVOT (MAX(ACCUMULATE_RATIO) AS RATIO,
                                                MAX(ITEM_QTY) AS ITEM_QTY,
                                                MAX(GROUP_QTY) AS GROUP_QTY
                                        FOR ACTUAL_RELEASE_MONTH IN
                                        (
                                        <foreach collection="report1ColumnNames" separator="," item="item">
                                            '${item}'
                                        </foreach>
                                        )))
        SELECT <foreach collection="categroy" separator="," item="item">
                    COALESCE(T.${item}, MST.${item}, MON.${item}_temp) AS ${item}
                </foreach>,
                ROUND(MST.CURRENT_COMPONENT_QTY / MST.CURRENT_BASE_QUANTITY, 5) AS CURRENT_BOM_RATIO,
                ROUND(MST.CURRENT_COMPONENT_QTY, 5)                             AS CURRENT_COMPONENT_QTY,
                ROUND(MST.CURRENT_BASE_QUANTITY, 5)                             AS CURRENT_BASE_QUANTITY,
                ROUND(T.ITEM_QTY / T.GROUP_QTY, 5)                              AS ACCUMULATE_BOM_RATIO,
                ROUND(T.ITEM_QTY, 5)                                            AS ACCUMULATE_COMPONENT_USAGE,
                ROUND(T.GROUP_QTY, 5)                                           AS ACCUMULATE_PLANNING_MATERIAL_OUTPUT
                <foreach collection="report1ColumnNames" separator="," open="," item="item">
                    &quot;'${item}'_RATIO&quot; AS &quot;${item} RATIO&quot;,
                    &quot;'${item}'_ITEM_QTY&quot; AS &quot;${item} COMPONENT_USAGE&quot;,
                    &quot;'${item}'_GROUP_QTY&quot; AS &quot;${item} PLANNING_MATERIAL_OUTPUT&quot;
                </foreach>
        FROM CTO_RESERVATION T
                FULL OUTER JOIN CTO_BOM MST
                    ON <foreach collection="categroy" separator="AND" item="item">
                            T.${item} = MST.${item}
                        </foreach>
                FULL OUTER JOIN CTO_RESERVATION_MONTH MON
                    ON <foreach collection="categroy" separator="AND" item="item">
                            T.${item} = MON.${item}_temp
                        </foreach>
    </sql>

    <select id="queryReport1Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1Sql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1Sql"/>
        <include refid="global.select_footer"/>
    </select>
</mapper>
