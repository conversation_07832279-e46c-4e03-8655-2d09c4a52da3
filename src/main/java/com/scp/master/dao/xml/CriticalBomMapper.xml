<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.master.dao.ICriticalBomDao">

	<select id="queryAvailablePlants" resultType="java.lang.String">
		SELECT T.PLANT_CODE FROM MR3_PLANT_MASTER_DATA T ORDER BY T.PLANT_CODE
	</select>

	<select id="queryCascader" resultType="java.util.Map">
		SELECT *
		FROM SCPA.MATERIAL_MASTER_FILTER_V T
		ORDER BY CATEGORY,decode (name,'Others','zzz',name)
	</select>

	<select id="queryApplicationAuth" resultType="java.lang.Integer">
		select count(1)
		from SY_MENU_AUTH
		where MENU_CODE = 'menu270'
		  and USER_ID = #{userid, jdbcType=VARCHAR}
		  and upper(AUTH_DETAILS) = 'ADMIN'
	</select>


	<sql id="queryReport1Sql">
		<choose>
			<when test="selectType == 'UP'">
			SELECT * FROM CRITICAL_BOM_ROUTE T
							  INNER JOIN (SELECT MATERIAL, PLANT_CODE
										  FROM CRITICAL_BOM_MANUAL
										  WHERE USER_ID = #{session.userid, jdbcType=VARCHAR}) M
										 ON M.MATERIAL = T.BOTTOM AND M.PLANT_CODE = T.BOTTOM_PLANT_CODE
			         WHERE PLANT_CODE IN
			         <foreach collection="plant" item="plantItem" open="(" separator="," close=")">
			         	#{plantItem, jdbcType=VARCHAR}
			         </foreach>
			         AND
				TOP_PLANT_CODE IN
				<foreach collection="plant" item="plantItem" open="(" separator="," close=")">
					#{plantItem, jdbcType=VARCHAR}
				</foreach>

			order by TOP
			</when>
			<otherwise>
				SELECT * FROM CRITICAL_BOM_ROUTE T
							  INNER JOIN (SELECT MATERIAL, PLANT_CODE
										  FROM CRITICAL_BOM_MANUAL
										  WHERE USER_ID = #{session.userid, jdbcType=VARCHAR}) M
										 ON M.MATERIAL = T.TOP AND M.PLANT_CODE = T.TOP_PLANT_CODE
				WHERE PLANT_CODE IN
				<foreach collection="plant" item="plantItem" open="(" separator="," close=")">
					#{plantItem, jdbcType=VARCHAR}
				</foreach>
				order by TOP
			</otherwise>
		</choose>
	</sql>

	<select id="queryReport1" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		<include refid="queryReport1Sql"/>
		<include refid="global.select_footer"/>
	</select>

	<select id="queryReport1TotalLine" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
		<include refid="queryReport1Sql"/>
	</select>

	<select id="queryReport1Count" parameterType="java.util.Map" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="queryReport1Sql"/>
		<include refid="global.count_footer"/>
	</select>

	<sql id="queryReport2Sql">
		select ROWIDTOCHAR(ROWID) AS ROW_ID, MATERIAL, PLANT_CODE from CRITICAL_BOM_MANUAL
		       WHERE USER_ID = #{session.userid, jdbcType=VARCHAR}
	</sql>

	<select id="queryReport2Count" parameterType="java.util.Map" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="queryReport2Sql"/>
		<include refid="global.count_footer"/>
	</select>

	<select id="queryReport2" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		<include refid="queryReport2Sql"/>
		<include refid="global.select_footer"/>
	</select>

	<select id="downloadReport2" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		select PLANT_CODE,MATERIAL from CRITICAL_BOM_MANUAL WHERE USER_ID = #{session.userid, jdbcType=VARCHAR}
		<include refid="global.select_footer"/>
	</select>

	<update id="updateReport2ByTable">
		UPDATE CRITICAL_BOM_MANUAL
		SET
		<foreach collection="updates" item="col" separator=",">
			${col.key} = #{col.value,jdbcType=VARCHAR}
		</foreach>,
		UPDATE_BY$ = #{userid,jdbcType=VARCHAR},
		UPDATE_DATE$ = SYSDATE
		WHERE ROWID = #{pk,jdbcType=VARCHAR} AND USER_ID = #{userid,jdbcType=VARCHAR}
	</update>

	<insert id="createReport2ByTable">
		INSERT INTO CRITICAL_BOM_MANUAL
		(
		<foreach collection="headers" item="header" separator=",">
			${header}
		</foreach>, USER_ID, CREATE_BY$, CREATE_DATE$
		)
		<foreach collection="inserts" item="list" separator=" union all ">
			select
			<foreach collection="headers" item="header" separator=",">
				#{list.${header}, jdbcType=VARCHAR}
			</foreach>,#{userid,jdbcType=VARCHAR}, #{userid,jdbcType=VARCHAR}, SYSDATE
			from dual
		</foreach>
	</insert>

	<delete id="deleteReport2ByTable">
		DELETE FROM CRITICAL_BOM_MANUAL WHERE ROWID IN
		<foreach collection="deletes" open="(" close=")" separator="," item="item">#{item, jdbcType=VARCHAR}</foreach>
		and USER_ID = #{userid,jdbcType=VARCHAR}
	</delete>

	<insert id="insertReport2Data">
		INSERT /*+ NOLOGGING  */ INTO CRITICAL_BOM_MANUAL
		(MATERIAL, PLANT_CODE,USER_ID,CREATE_BY$,CREATE_DATE$)
		<foreach collection="list" separator=" union all" item="item">
			select
			#{item.MATERIAL, jdbcType=VARCHAR},
			#{item.PLANT_CODE, jdbcType=VARCHAR},
			#{item.USER_ID,jdbcType=VARCHAR},
			#{item.USER_ID,jdbcType=VARCHAR},
			SYSDATE
			from dual
		</foreach>
	</insert>

	<delete id="queryReport2DeplicateRows">
		DELETE FROM CRITICAL_BOM_MANUAL
		WHERE (MATERIAL, PLANT_CODE, USER_ID, ROWID) NOT IN (
			SELECT MATERIAL, PLANT_CODE, USER_ID, MIN(ROWID)
			FROM CRITICAL_BOM_MANUAL
			GROUP BY MATERIAL, PLANT_CODE, USER_ID
		)
	</delete>

	<update id="mergeReport2Data">
		BEGIN
		DELETE FROM DEMAND_SUPPLY_CRITICAL_MATERIAL_MANUAL where USER_ID = #{userid, jdbcType=VARCHAR};
		INSERT INTO DEMAND_SUPPLY_CRITICAL_MATERIAL_MANUAL
			(MATERIAL, PLANT_CODE, USER_ID, CREATE_BY$, CREATE_DATE$)
		SELECT MATERIAL, PLANT_CODE, #{userid,jdbcType=VARCHAR}, #{userid,jdbcType=VARCHAR}, SYSDATE FROM DEMAND_SUPPLY_CRITICAL_MATERIAL_MANUAL_TEMPORARY;
		END;
	</update>

	<select id="queryAllPlant" resultType="java.lang.String">
		SELECT DISTINCT PLANT_CODE FROM MR3_PLANT_MASTER_DATA
	</select>
	
	<select id="querySankeyChart" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
		SELECT MATERIAL, BOM_COMPONENT, COMPONENT_QTY
		FROM CRITICAL_BOM_V
		WHERE PLANT_CODE IN
		<foreach collection="plantList" item="plant" open="(" separator="," close=")">
			#{plant, jdbcType=VARCHAR}
		</foreach>
			START WITH MATERIAL = #{material, jdbcType=VARCHAR}
		CONNECT BY PRIOR BOM_COMPONENT = MATERIAL
	</select>




</mapper>
