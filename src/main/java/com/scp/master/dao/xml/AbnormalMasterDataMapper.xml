<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.master.dao.IAbnormalMasterDataDao">
    <update id="dropResultTable">
        declare
            v_result int;
        begin
            select count(1) into v_result from user_tables where table_name = 'ABNORMAL_MASTER_DATA_RESULT';
            if v_result > 0 then
                execute immediate 'drop table ABNORMAL_MASTER_DATA_RESULT';
            end if;
        end;
    </update>

    <insert id="createResultTable" parameterType="java.util.List">
        create table ABNORMAL_MASTER_DATA_RESULT
        (
            MATERIAL VARCHAR2(64),
            PLANT_CODE VARCHAR2(24),
            <foreach collection="list" item="column">
               ${column.name} VARCHAR2(${column.length}),
            </foreach>
            constraint ABNORMAL_MASTER_DATA_RESULT_PK unique (MATERIAL, PLANT_CODE)
        )
    </insert>

    <select id="getDefineList" resultType="java.util.Map">
        select *
          from ABNORMAL_MASTER_DATA_RULE
         where enable = 1
         <if test="selectedGroupList != null and selectedGroupList.size() > 0">
            and group_name in
            <foreach collection="selectedGroupList" item="item" separator="," open="(" close=")">
                #{item, jdbcType=VARCHAR}
            </foreach>
         </if>
         order by ORDER_NO
    </select>

    <update id="mergeResult" parameterType="java.util.List">
        begin
            merge into ABNORMAL_MASTER_DATA_RESULT t
            using (
                ${sql}
            ) s on (t.material = s.material and t.plant_code = s.plant_code)
            when matched then
                update set
                <foreach collection="columns" item="column" separator=",">
                    t.${column.name} = s.${column.name}
                </foreach>
            when not matched then
                insert (material, plant_code,
                    <foreach collection="columns" item="column" separator=","> ${column.name} </foreach>
                )
                values
                    (s.material, s.plant_code,
                    <foreach collection="columns" item="column" separator=","> s.${column.name} </foreach>
                );
            commit;
        end;
    </update>

    <sql id="filter">
        <if test="filters != null and filters != ''.toString()">and ${filters}</if>
        <if test="filters2 != null and filters2 != ''.toString()">and ${filters2}</if>
        <if test="materialList != null and materialList.isEmpty() == false">
            <foreach collection="materialList" item="list" separator=" or " open=" and (" close=")">
                t.material in
                <foreach collection="list" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </foreach>
        </if>
    </sql>

    <sql id="queryResultSQL">
        select t.MATERIAL,
               t.PLANT_CODE,
               t.MATERIAL_OWNER_NAME,
               t.MATERIAL_OWNER_SESA,
               t.MRP_CONTROLLER,
               t.PRODUCT_LINE,
               t.ENTITY,
               t.CLUSTER_NAME,
               t.BU,
               t.LOCAL_BU,
               t.LOCAL_PRODUCT_FAMILY,
               t.LOCAL_PRODUCT_LINE,
               t.LOCAL_PRODUCT_SUBFAMILY,
               t.ACTIVENESS,
               t.DELETION,
               T.PLANT_TYPE
               <foreach collection="selectedHeaders" open="," separator="," item="item">
                    t.${item}
               </foreach>
        from ABNORMAL_MASTER_DATA_V t
        <where>
            <include refid="filter"/>
            AND t.PLANT_CODE in ('O001', 'N001', 'I001', 'M001', 'I003', 'K001', 'K003', 'A001', 'HN01', 'WG01', 'IB01',
            'SS01', 'BG01', 'XA01', 'XA03', 'AX01', 'AX02', 'LH01', 'PW01', 'SA01', 'J001', 'B001', 'SMF1', 'SMF2', 'R001', 'SP01', 'SP02', 'SP03', 'XM01')
        </where>
    </sql>

    <select id="queryResultCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryResultSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryResult" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryResultSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryRulesCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
            select t.group_name,t.output_column,t.enable from ABNORMAL_MASTER_DATA_RULE t
        <include refid="global.count_footer"/>
    </select>

    <select id="queryRules" parameterType="java.util.Map" resultType="java.util.Map">
        <include refid="global.select_header"/>
            select t.group_name,t.output_column,t.enable, t.ORDER_NO from ABNORMAL_MASTER_DATA_RULE t
        <include refid="global.select_footer"/>
    </select>

    <select id="queryRuleByID" parameterType="java.util.Map" resultType="java.util.Map">
        select t.*,ROWIDTOCHAR(t.rowid) row_id from ABNORMAL_MASTER_DATA_RULE t where t.group_name = #{group_name, jdbcType=VARCHAR}
    </select>

    <update id="updateRule" parameterType="java.util.Map">
        update ABNORMAL_MASTER_DATA_RULE
           set GROUP_NAME = #{ruleName,jdbcType=VARCHAR},
               OUTPUT_COLUMN = #{columnStr,jdbcType=VARCHAR},
               SQL_SCRIPT = #{sqlScript,jdbcType=VARCHAR},
               ENABLE = #{enableStr,jdbcType=VARCHAR}
         where rowid = #{rowid,jdbcType=VARCHAR}
    </update>

    <insert id="saveRule" parameterType="java.util.Map">
        insert into ABNORMAL_MASTER_DATA_RULE (GROUP_NAME,OUTPUT_COLUMN,SQL_SCRIPT,ENABLE)
        values (#{ruleName,jdbcType=VARCHAR},#{columnStr,jdbcType=VARCHAR},#{sqlScript,jdbcType=VARCHAR},#{enableStr,jdbcType=VARCHAR})
    </insert>

    <select id="queryCascader" resultType="java.util.Map">
        select * from ABNORMAL_MASTER_DATA_FILTER_V order by category,decode(name,'Others','zzz',name)
    </select>

    <select id="queryGroupCascader" resultType="java.util.Map">
        SELECT E.GROUP_NAME AS CATEGORY,
               E.ERROR_CODE AS NAME
        FROM ABNORMAL_MASTER_DATA_ERROR_CODE E
                 INNER JOIN ABNORMAL_MASTER_DATA_RULE R ON E.GROUP_NAME = R.GROUP_NAME
        WHERE R.ENABLE = 1
        ORDER BY ORDER_NO, NAME
    </select>

    <select id="queryDashboard" parameterType="java.util.Map" resultType="java.util.Map">
        <include refid="global.select_header"/>
            select <foreach collection="categroies" item="item">
                       mm.${item},
                   </foreach>
                   MMV.TOTAL,
                   <foreach collection="groupList" separator="," item="groupIndex">
                       mm.${groupIndex}_total,
                       decode(MMV.TOTAL, 0, 0, round(mm.${groupIndex}_total / MMV.TOTAL, 4)) ${groupIndex}_percent
                   </foreach>
            from (
                     select <foreach collection="categroies" item="item">
                               t.${item},
                            </foreach>
                            <foreach collection="groupList" separator="," item="groupIndex">
                                count(t.${groupIndex}_REASON_CODE) ${groupIndex}_total
                            </foreach>
                     from ABNORMAL_MASTER_DATA_DASHBOARD_V t
                     <where>
                        <include refid="filter"/>
                     </where>
                     group by
                     <foreach collection="categroies" item="item" separator=",">
                        t.${item}
                     </foreach>
                 ) mm LEFT JOIN (
                    SELECT
                     <foreach collection="categroies" item="item" separator=",">
                        t.${item}
                     </foreach>, COUNT(1) AS TOTAL FROM MATERIAL_MASTER_V T
                     <where>
                         <if test="filters != null and filters != ''.toString()">and ${filters}</if>
                         <if test="materialList != null and materialList.isEmpty() == false">
                            <foreach collection="materialList" item="list" separator=" or " open=" and (" close=")">
                                t.material in
                                <foreach collection="list" item="item" separator="," open="(" close=")">
                                    #{item,jdbcType=VARCHAR}
                                </foreach>
                            </foreach>
                         </if>
                     </where>
                    GROUP BY
                    <foreach collection="categroies" item="item" separator=",">
                        t.${item}
                     </foreach>
                 ) MMV ON
                 <foreach collection="categroies" item="item" separator=" AND ">
                    MM.${item} = MMV.${item}
                 </foreach>
            order by
        <choose>
            <when test="categroies[0] == 'CLUSTER_NAME'.toString()">
                decode(mm.${categroies[0]}, 'LV', '1', 'MV', '2', 'EE', '3', 'H&amp;D', '4', 'Trading', '5', 'Others', 'zzz', mm.${categroies[0]})
                <foreach collection="categroies" item="item" index="index">
                    <if test="index > 0">
                        , mm.${item}
                    </if>
                </foreach>
            </when>
            <otherwise>
                <foreach collection="categroies" item="item" separator=",">
                    decode(mm.${item}, 'Others', 'zzz', mm.${item})
                </foreach>
            </otherwise>
        </choose>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReasonCode" resultType="java.util.Map">
        select t.error_code, t.description from ABNORMAL_MASTER_DATA_ERROR_CODE t
    </select>

    <sql id="queryMasterDataDetailsSql">
        select * from ABNORMAL_MASTER_DATA_DASHBOARD_V t
        where 1 = 1
        <foreach collection="categroies" item="item" index="index">
            <if test="selectedValues[index] != null and selectedValues[index] != ''.toString() and selectedValues[index] != 'Total'.toString()">
                and nvl(t.${item},'Others') = #{selectedValues[${index}], jdbcType=VARCHAR}
            </if>
        </foreach>
        <if test="selectedKey != null and selectedKey != ''.toString()">
            and t.${selectedKey} is not null
        </if>
        <include refid="filter"/>
    </sql>

    <select id="queryMasterDataDetailsCount" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryMasterDataDetailsSql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryMasterDataDetails" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryMasterDataDetailsSql"/>
        <include refid="global.select_footer"/>
    </select>

    <update id="refreshMasterDataMV">
        begin
            execute immediate '
                DROP materialized VIEW ABNORMAL_MASTER_DATA_V
            ';
            execute immediate '
                CREATE materialized VIEW ABNORMAL_MASTER_DATA_V
                    compress basic nologging
                as
                select t.*,
                       t2.material_owner_name,
                       t2.material_owner_sesa,
                       nvl(t2.mrp_controller, ''Others'')                                     mrp_controller,
                       nvl(t2.product_line, ''Others'')                                       product_line,
                       nvl(t2.ENTITY, ''Others'')                                             entity,
                       nvl(t2.cluster_name, ''Others'')                                       cluster_name,
                       nvl(t2.BU, ''Others'')                                                 bu,
                       nvl(t2.LOCAL_BU, ''Others'')                                           local_bu,
                       nvl(t2.local_product_family, ''Others'')                               local_product_family,
                       nvl(t2.local_product_line, ''Others'')                                 local_product_line,
                       nvl(t2.local_product_subfamily, ''Others'')                            local_product_subfamily,
                       nvl(t2.activeness, ''Others'')                                         activeness,
                       nvl(t2.deletion, ''Others'')                                           deletion,
                       nvl(t2.plant_type, ''Others'')                                         plant_type
                from ABNORMAL_MASTER_DATA_RESULT t
                         left join MATERIAL_MASTER_V t2
                                   on t.MATERIAL = t2.MATERIAL and t.PLANT_CODE = t2.PLANT_CODE';

            DBMS_MVIEW.REFRESH(list =>'ABNORMAL_MASTER_DATA_FILTER_V', Method =>'C', refresh_after_errors => false, atomic_refresh => false, out_of_place => true);

            execute immediate '
                DROP materialized VIEW ABNORMAL_MASTER_DATA_DASHBOARD_V
            ';

            execute immediate '
                create materialized view ABNORMAL_MASTER_DATA_DASHBOARD_V
                    compress basic nologging
                as
                select t.MATERIAL,
                       t.PLANT_CODE,
                       t.MATERIAL_OWNER_NAME,
                       t.MATERIAL_OWNER_SESA,
                       nvl(t.mrp_controller, ''Others'')                                      mrp_controller,
                       nvl(t.PRODUCT_LINE, nvl(t.product_line, ''Others''))                   product_line,
                       nvl(t.ENTITY, ''Others'')                                              entity,
                       nvl(t.cluster_name, ''Others'')                                        cluster_name,
                       nvl(t.bu, ''Others'')                                                  bu,
                       nvl(t.LOCAL_BU, ''Others'')                                            local_bu,
                       nvl(t.local_product_family, ''Others'')                                local_product_family,
                       nvl(t.local_product_line, ''Others'')                                  local_product_line,
                       nvl(t.local_product_subfamily, ''Others'')                             local_product_subfamily,
                       t.activeness,
                       t.deletion, t.plant_type,
            <foreach collection="groupList" separator="," item="groupIndex">
                       t2.${groupIndex}_REASON_CODE
            </foreach>
                from MATERIAL_MASTER_V t
                         inner join ABNORMAL_MASTER_DATA_RESULT t2
                                   on t2.MATERIAL = t.MATERIAL and t2.PLANT_CODE = t.PLANT_CODE
                where t.PLANT_CODE in (''O001'', ''N001'', ''I001'', ''M001'', ''I003'', ''K001'', ''K003'', ''A001'', ''HN01'', ''WG01'', ''IB01'', ''SS01'', ''BG01'', ''XA01'', ''XA03'', ''AX01'', ''AX02'', ''LH01'', ''PW01'', ''SA01'', ''J001'', ''B001'', ''SMF1'', ''SMF2'', ''R001'', ''SP01'', ''SP02'', ''SP03'', ''XM01'')';
        end;
    </update>
</mapper>
