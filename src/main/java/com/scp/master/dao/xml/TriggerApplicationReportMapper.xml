<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.master.dao.ITriggerApplicationReportDao">
    <sql id="filter">
        <if test="filters != null and filters != ''.toString()">
            and ${filters}
        </if>
        <if test="specialList != null and specialList.size() > 0">
            <foreach collection="specialList" item="list" separator=" or " open=" and (" close=")">
                t.${specialColumn} in
                <foreach collection="list" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </foreach>
        </if>
        <choose>
            <when test="RCA == null or RCA.size() == 0">
                <if test="hasBlackRCA">
                    and RCA is null
                </if>
            </when>
            <otherwise>
                and ( t.<PERSON> in
                    <foreach collection="RCA" item="item" separator="," open="(" close=")">
                        #{item,jdbcType=VARCHAR}
                    </foreach>
                    <if test="hasBlackRCA">
                        or RCA is null
                    </if>
                )
            </otherwise>
        </choose>
    </sql>

    <update id="refreshTriggerReport">
        begin
			DBMS_MVIEW.REFRESH(list =>'APPLICATION_001_HIST_V', Method =>'C', refresh_after_errors => false, atomic_refresh => false, out_of_place => true);
            DBMS_MVIEW.REFRESH(list =>'APPLICATION_001_ACCUM_V', Method =>'C', refresh_after_errors => false, atomic_refresh => false, out_of_place => true);
            DBMS_MVIEW.REFRESH(list =>'APPLICATION_001_V', Method =>'C', refresh_after_errors => false, atomic_refresh => false, out_of_place => true);
		end;
    </update>

    <select id="queryCascader" resultType="java.util.Map">
        select *
          from MATERIAL_MASTER_FILTER_V
         where CATEGORY in ('MRP_CONTROLLER', 'ENTITY', 'CLUSTER_NAME', 'PRODUCT_LINE', 'BU', 'LOCAL_BU', 'LOCAL_PRODUCT_LINE', 'STOCKING_POLICY', 'MATERIAL_OWNER_NAME', 'MATERIAL_OWNER_SESA')
        order by category,decode (name,'Others','zzz',name)
    </select>

    <select id="queryReport1Columns" resultType="java.lang.String">
        select content from SY_TABLE_DROPDOWN where table_name = 'APPLICATION_001' and FIELD_NAME = 'RCA'
    </select>

    <select id="queryReport1RCA" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        select <foreach collection="categories" item="item">
                   nvl(${item},'Others') ${item},
               </foreach>
               nvl(RCA,'&lt;Blank&gt;') CATEGROY3,
               COUNT(1) CNT
        from
        <choose>
            <when test="selectedType == 'Active'.toString()">
                APPLICATION_001_HIST_V t
            </when>
            <otherwise>
                APPLICATION_001_ACCUM_V t
            </otherwise>
        </choose>
        where to_date(#{date, jdbcType=VARCHAR}, 'yyyy/mm/dd') between t.VALIDATE_FROM and t.VALIDATE_TO
        <include refid="filter"/>
        group by <foreach collection="categories" item="item">
                   nvl(${item},'Others'),
                 </foreach>
                 nvl(RCA,'&lt;Blank&gt;')
        order by <foreach collection="categories" item="item" separator=",">
                   decode (nvl(${item},'Others'),'Others','zzz',nvl(${item},'Others'))
                 </foreach>
    </select>

    <select id="queryReport1LTRange" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        SELECT <foreach collection="categories" item="item">
                   ${item},
               </foreach>
               <![CDATA[
               SUM(QMAX_LT_7)  "<=7",
               SUM(QMAX_LT_14) "<=14",
               SUM(QMAX_LT_30) "<=30",
               SUM(QMAX_LT_45) "<=45",
               SUM(QMAX_GT_45) ">45"
               ]]>
          FROM (
            SELECT <foreach collection="categories" item="item">
                       nvl(${item},'Others') ${item},
                   </foreach>
                   case when QMAX_LEAD_TIME &lt;= 7 then 1 end  as QMAX_LT_7,
                   case when QMAX_LEAD_TIME between 8 and 14 then 1 end as QMAX_LT_14,
                   case when QMAX_LEAD_TIME between 15 and 30 then 1 end as QMAX_LT_30,
                   case when QMAX_LEAD_TIME between 31 and 45 then 1 end as QMAX_LT_45,
                   case when QMAX_LEAD_TIME > 45 then 1 end as QMAX_GT_45
            from
            <choose>
                <when test="selectedType == 'Active'.toString()">
                    APPLICATION_001_HIST_V t
                </when>
                <otherwise>
                    APPLICATION_001_ACCUM_V t
                </otherwise>
            </choose>
            where to_date(#{date, jdbcType=VARCHAR}, 'yyyy/mm/dd') between VALIDATE_FROM and VALIDATE_TO
            <include refid="filter"/>
        )
        GROUP BY <foreach collection="categories" item="item" separator=",">
                   ${item}
                 </foreach>
        order by
        <foreach collection="categories" item="item" separator=",">
           decode (${item},'Others','zzz',${item})
        </foreach>
    </select>

    <select id="queryReport2" parameterType="java.util.Map" resultType="java.util.Map">
        select VALIDATE_FROM,
               VALIDATE_TO,
               BTN_DATE,
               COUNT(1) CNT
          from <choose>
                <when test="type == 'NORMAL'.toString()">
                    APPLICATION_001_HIST_V t
                </when>
                <otherwise>
                    APPLICATION_001_ACCUM_V t
                </otherwise>
              </choose>
         where t.VALIDATE_TO is not null
           and t.VALIDATE_FROM is not null
           and t.VALIDATE_TO >= to_date(#{report1StartDate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
         <include refid="filter"/>
         group by VALIDATE_FROM, VALIDATE_TO, BTN_DATE
    </select>

    <select id="queryReport3" resultType="java.util.Map">
        select * from (
            select QMAX_LEAD_TIME_GROUP AS CATEGORY,
                   sum(${valueColumn}) AS "VALUE"
              from APPLICATION_001_V t
             where USAGE_INDICATOR = 'T'
             <if test="report2Months != null and report2Months.size() > 0">
                and YEAR_MONTH in
                <foreach collection="report2Months" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
             </if>
             <include refid="filter"/>
             group by QMAX_LEAD_TIME_GROUP

            UNION ALL

           select 'No impact', sum(${valueColumn})
             from APPLICATION_001_V t
            where USAGE_INDICATOR = 'Others'
            <if test="report2Months != null and report2Months.size() > 0">
                and YEAR_MONTH in
                <foreach collection="report2Months" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <include refid="filter"/>
        ) mm
        order by decode(CATEGORY, 'No impact', 1, '&lt;7D', 2, '7D-14D', 3, '14D-30D', 4, '30D-45D', 5, 6)
    </select>

    <sql id="queryReport1DetailsSQL">
        select *
         from <choose>
                <when test="selectedType == 'Active'.toString()">
                    APPLICATION_001_HIST_V t
                </when>
                <otherwise>
                    APPLICATION_001_ACCUM_V t
                </otherwise>
              </choose>
        where to_date(#{selectedDate, jdbcType=VARCHAR}, 'yyyy/mm/dd') between VALIDATE_FROM and VALIDATE_TO
        <foreach collection="categories" item="item" index="index">
            <if test="selectedValues[index] != null and selectedValues[index] != ''.toString()">
                and nvl(${item},'Others') = #{selectedValues[${index}], jdbcType=VARCHAR}
            </if>
        </foreach>
        <if test="categroy3 == 'LT_RANGE'.toString()">
            <choose>
                <when test="selectedValue3 == '&lt;=7'.toString()">
                    and t.QMAX_LEAD_TIME &lt;= 7
                </when>
                <when test="selectedValue3 == '&lt;=14'.toString()">
                    and t.QMAX_LEAD_TIME between 8 and 14
                </when>
                <when test="selectedValue3 == '&lt;=30'.toString()">
                    and t.QMAX_LEAD_TIME between 15 and 30
                </when>
                <when test="selectedValue3 == '>30'.toString()">
                    and t.QMAX_LEAD_TIME > 30
                </when>
                <when test="selectedValue3 == '>45'.toString()">
                    and t.QMAX_LEAD_TIME > 45
                </when>
            </choose>
        </if>
        <if test="categroy3 == 'RCA_CODE'.toString() and selectedValue3 != ''.toString() and selectedValue3 != null">
            <choose>
                <when test="selectedValue3 == '&lt;Blank&gt;'.toString()">
                    and t.RCA is null
                </when>
                <otherwise>
                    and t.RCA = #{selectedValue3, jdbcType=VARCHAR}
                </otherwise>
            </choose>
        </if>
        <include refid="filter"/>
    </sql>

    <select id="queryReport1DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport3DetailsSQL">
        select  t.*
          from APPLICATION_001_V t
         where USAGE_INDICATOR = #{indicator, jdbcType=VARCHAR}
         <if test="qmax_group != null and qmax_group != ''.toString()">
             and QMAX_LEAD_TIME_GROUP = #{qmax_group, jdbcType=VARCHAR}
         </if>
         <if test="report2Months != null and report2Months.size() > 0">
            and YEAR_MONTH in
            <foreach collection="report2Months" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
         </if>
         <include refid="filter"/>
    </sql>

    <select id="queryReport3DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport3DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport3Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport3DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport4SQL">
        WITH BASE AS (
                SELECT *
                 FROM <choose>
                        <when test="selectedType == 'Active'.toString()">
                            APPLICATION_001_HIST_V t
                        </when>
                        <otherwise>
                            APPLICATION_001_ACCUM_V t
                        </otherwise>
                      </choose>
                WHERE <choose>
                        <when test="selectedType == 'Projection'.toString()">
                            <!-- 取消对BTN的数据的影响, 暂时 TO_DATE(#{selectedDate, jdbcType=VARCHAR}, 'yyyy/mm/dd') BETWEEN VALIDATE_FROM AND BTN_DATE -->
                            TO_DATE(#{selectedDate, jdbcType=VARCHAR}, 'yyyy/mm/dd') BETWEEN VALIDATE_FROM AND VALIDATE_TO
                            AND TRUNC(SYSDATE, 'DD') BETWEEN VALIDATE_FROM AND VALIDATE_TO
                        </when>
                        <otherwise>
                            TO_DATE(#{selectedDate, jdbcType=VARCHAR}, 'yyyy/mm/dd') BETWEEN VALIDATE_FROM AND VALIDATE_TO
                        </otherwise>
                      </choose>
                       AND t.VALIDATE_TO is not null
                       AND t.VALIDATE_FROM is not null
                       AND t.VALIDATE_TO >= to_date(#{report1StartDate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
                <include refid="filter"/>),
             STOCKING_P AS (SELECT MMV.MATERIAL, MAX(MMV.PLANNED_DELIV_TIME) PLANNED_DELIV_TIME, MAX(MMV.GR_PROCESSING_TIME) GR_PROCESSING_TIME
                            FROM BASE
                                     INNER JOIN MATERIAL_MASTER_V MMV ON BASE.MATERIAL = MMV.MATERIAL AND MMV.PLANT_CODE = 'O001'
                                     GROUP BY MMV.MATERIAL
             ),
             STOCK AS (SELECT T.MATERIAL,
                              SUM(T.AMF_ONE_MM)                              AMF_ONE_MM,
                              SUM(T.AMU_ONE_MM)                              AMU_ONE_MM,
                              SUM(T.UU_STOCK)                                UU_STOCK,
                              SUM(T.STOCK_IN_QI)                             STOCK_IN_QI,
                              SUM(T.OPEN_LA)                                 OPEN_LA,
                              SUM(T.OPEN_PO)                                 OPEN_PO,
                              SUM(T.OPEN_SO)                                 OPEN_SO
                       FROM INVENTORY_STRUCTURE_V T
                                INNER JOIN BASE ON BASE.MATERIAL = T.MATERIAL
                       WHERE T.PLANT_CODE IN ('I001', 'A001', 'M001', 'O001', 'I003', 'N001', 'N005')
                       GROUP BY T.MATERIAL)
        SELECT BASE.*,
               STOCKING_P.PLANNED_DELIV_TIME,
               STOCKING_P.GR_PROCESSING_TIME,
               STOCK.AMF_ONE_MM,
               STOCK.AMU_ONE_MM,
               GREATEST(STOCK.AMF_ONE_MM, STOCK.AMU_ONE_MM) MAX_AMU_AMF,
               STOCK.UU_STOCK,
               STOCK.STOCK_IN_QI,
               STOCK.OPEN_LA,
               STOCK.OPEN_PO,
               STOCK.OPEN_SO,
               DECODE(AMF_ONE_MM, 0, 0, ROUND((STOCK.UU_STOCK + STOCK.STOCK_IN_QI) / AMF_ONE_MM * 30, 2))   STOCK_COV_AMF,
               DECODE(AMU_ONE_MM, 0, 0, ROUND((STOCK.UU_STOCK + STOCK.STOCK_IN_QI) / AMU_ONE_MM * 30, 2))   STOCK_COV_AMU,
               DECODE(GREATEST(STOCK.AMF_ONE_MM, STOCK.AMU_ONE_MM), 0, 0,
                    ROUND((STOCK.UU_STOCK + STOCK.STOCK_IN_QI) / GREATEST(STOCK.AMF_ONE_MM, STOCK.AMU_ONE_MM)  * 30, 2)) STOCK_COV_MAX_AMU_AMF
        FROM BASE
                 LEFT JOIN STOCK ON BASE.MATERIAL = STOCK.MATERIAL
                 LEFT JOIN STOCKING_P ON BASE.MATERIAL = STOCKING_P.MATERIAL
    </sql>

    <select id="queryReport4Count" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport4SQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport4" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport4SQL"/>
        <include refid="global.select_footer"/>
    </select>
</mapper>
