<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.master.dao.IMasterDataSummaryDao">
    <select id="queryCascader" resultType="java.util.Map">
        select * from MATERIAL_MASTER_FILTER_V order by category,decode (name,'Others','zzz',name)
    </select>

    <sql id="queryReport1Sql">
        select /*+ parallel(t 12) */
               <foreach collection="categroy" separator="," item="item">
                    nvl(${item},'Others') "${item}"
               </foreach>,
               count(1) as MATERIAL_CNT,
               ${calcType}(MINIMUM_LOT_SIZE ${costColumn}) MINIMUM_LOT_SIZE,
               ${calcType}(SAFETY_STOCK ${costColumn}) SAFETY_STOCK,
               ${calcType}(REORDER_POINT ${costColumn}) REORDER_POINT,
               ${calcType}(SS2 ${costColumn}) SS2,
               ${calcType}(SS3 ${costColumn}) SS3,
               ${calcType}(LOT_SIZE_MIN ${costColumn}) LOT_SIZE_MIN,
               ${calcType}(MAXIMUM_LOT_SIZE ${costColumn}) MAXIMUM_LOT_SIZE,
               ${calcType}(FIXED_LOT_SIZE ${costColumn}) FIXED_LOT_SIZE,
               ${calcType}(ROUNDING_VALUE ${costColumn}) ROUNDING_VALUE,
               ${calcType}(MOQ_TO_CUSTOMER ${costColumn}) MOQ_TO_CUSTOMER,
               ${calcType}(MDQ_TO_CUSTOMER ${costColumn}) MDQ_TO_CUSTOMER,
               ${calcType}(RV_TO_CUSTOMER ${costColumn}) RV_TO_CUSTOMER,
               ${calcType}(AMU ${costColumn}) AMU,
               ${calcType}(SECI_TP_OUT_PRICE ${costColumn}) SECI_TP_OUT_PRICE,
               ${calcType}(AMF ${costColumn}) AMF
        from ${SCPA.MATERIAL_MASTER_V} t
        <if test="_filters != null and _filters != ''.toString()">
            WHERE ${_filters}
        </if>
        group by
        <foreach collection="categroy" separator="," item="item">
            nvl(${item},'Others')
        </foreach>
        order by
        <foreach collection="categroy" separator="," item="item">
            DECODE(nvl(${item}, 'Others'), 'Others', 'zzzz', nvl(${item}, 'Others'))
        </foreach>
    </sql>

    <select id="queryReport1Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1Sql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1Sql"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport1DetailsSql">
        select MATERIAL,
                PLANT_CODE,
                PLANT_TYPE,
                CLUSTER_NAME,
                ENTITY,
                BU,
                BU_TYPE,
                PRODUCT_LINE,
                PRODUCTION_LINE,
                LOCAL_BU,
                LOCAL_PRODUCT_LINE,
                LOCAL_PRODUCT_FAMILY,
                LOCAL_PRODUCT_SUBFAMILY,
                MRP_CONTROLLER,
                MRP_CONTROLLER_DESCRIPTION,
                MATERIAL_DESCRIPTION,
                STOCKING_POLICY,
                VENDOR_CODE,
                VENDOR_NAME,
                VENDOR_PARENT_CODE,
                VENDOR_PARENT_NAME,
                VENDOR_SHORT_NAME,
                VENDOR_FULL_NAME,
                SOURCE_CATEGORY,
                MATERIAL_TYPE,
                ACTIVENESS,
                PLANNED_DELIV_TIME,
                PALLETIZATION_QTY,
                LT_RANGE,
                PROPOSED_FIN_PROV_QTY,
                PROPOSED_FIN_PROV_VALUE,
                AVG_SELLING_PRICE_RMB,
                AVG_SELLING_PRICE_HKD,
                MOVING_AVERAGE_P,
                STANDARD_PRICE,
                UNIT_COST,
                PRICE_UNIT,
                MINIMUM_LOT_SIZE,
                SAFETY_STOCK,
                WARE_NUM,
                STOR_TYPE_PLACE,
                STOR_TYPE_REMO,
                BULK_MATERIAL_IND,
                BUCKFLUSH_IND,
                AMU,
                AMU_ONEMM,
                AMF,
                AMF_ONEMM,
                COV,
                REORDER_POINT,
                SS2,
                SS3,
                DELIVERING_PLANT,
                DELIVERING_PLANT_HK,
                CALCULATED_ABC,
                CALCULATED_FMR,
                NEW_PRODUCTS,
                HS_SS_CALCULATION_FORMULA,
                LOT_SIZE_MIN,
                SAFETY_STOCK_VALUE_HS,
                SAFETY_STOCK_QTY_HS,
                INDUSTRY_CODE,
                MRP_TYPE,
                CREATED_DATE,
                AVAILABILITY_CHECK,
                BASE_UNIT,
                PURCHASING_GROUP,
                GR_PROCESSING_TIME,
                LOT_SIZE_CODE,
                PROCUREMENT_TYPE,
                SPECIAL_PROC_CODE,
                MAXIMUM_LOT_SIZE,
                FIXED_LOT_SIZE,
                ROUNDING_VALUE,
                INDIVIDUAL_COLLECT,
                FOLLOW_UP_MATERIAL,
                PRODN_SUPERVISOR,
                IN_HOUSE_PRODN_LT,
                T_REPLENISHMENT_LT,
                CONSUMPTION_MODE,
                BWD_CONSUMPTION,
                FWD_CONSUMPTION,
                MRP_GROUP,
                DEL_FLAG_PLANT,
                DEL_FLAG_CLIENT,
                DELETION,
                SAFETY_TIME,
                GROSS_WEIGHT,
                GROSS_WEIGHT_IN_KG,
                NET_WEIGHT,
                NET_WEIGHT_IN_KG,
                WEIGHT_UNIT,
                PRODUCT_HIERARCHY,
                VALUATION_CLASS,
                MATERIAL_CATEGORY,
                FIRST_CONS_DATE,
                FIRST_SO_DATE,
                FIRST_PO_DATE,
                MOQ_TO_CUSTOMER,
                MDQ_TO_CUSTOMER,
                RV_TO_CUSTOMER,
                MAT_PRICING_GROUP,
                ITEM_CATEGORY_GRP,
                MATERIAL_GROUP_1,
                MATERIAL_GROUP_2,
                MATERIAL_GROUP_3,
                MATERIAL_GROUP_4,
                MATERIAL_GROUP_5,
                MATERIAL_ST_PLANT,
                DIST_CHANNEL_SP_ST,
                PRICE_CONTROL,
                COUNTRY_CODE,
                ACCOUNT_GROUP,
                REPL_STRATEGY,
                SUBT_FLAG,
                SUBT_PROJECT_NAME,
                MATERIAL_OWNER_SESA,
                MATERIAL_OWNER_NAME,
                FAM_CODE,
                ACT_CODE,
                STARS_CODE,
                PRODUCT_LINE_INV,
                PRODUCT_FAMILY_INV,
                NONE_SALES_DURATION,
                NONE_SALES_PERIOD,
                MAXIMUM_STK_LEVEL,
                PROCUREMENT_PLANT,
                COMMODITY_CODE_ONE_MM,
                GRA_TYPE,
                GRA_EVENT,
                GRA_METHOD,
                EXISTING_IN_BOM,
                COMMODITY_CODE,
                STARS_DESCRIPTION,
                FIXED_SOURCE,
                TP_CURRENCY,
                QUOTA_ARR_USAGE,
                INVENTORY_MONITOR_TYPE,
                ORGANIZATION,
                ORGANIZATION_CLUSTER_NAME,
                PLANN_STRATEGY_GRP,
                VENDOR_HFM_CODE,
                MATERIAL_CRITICALITY,
                SSS_COVERAGE,
                XBOARD,
                XBOARD_CONTROLLER,
                RES_COV_STK,
                RES_COV_STK_RANGE,
                RES_COV_STK_LA,
                RES_COV_STK_LA_RANGE,
                RES_COV_STK_LA_AB,
                RES_COV_STK_LA_AB_RANGE,
                RES_COV_STK_OPO,
                RES_COV_STK_OPO_RANGE,
                SECI_TP_OUT_PRICE,
                RISK_LEVEL,
                NUM_OF_WHERE_USE,
                NUM_OF_WHERE_USE_RANGE,
                COV_END_ORDER,
                COV_RANGE_END_ORDER,
                (MINIMUM_LOT_SIZE * AVG_SELLING_PRICE_RMB) AS MINIMUM_LOT_SIZE_SELLING_VALUE,
                (MINIMUM_LOT_SIZE * UNIT_COST)             AS MINIMUM_LOT_SIZE_COST_VALUE,
                (SAFETY_STOCK * AVG_SELLING_PRICE_RMB)     AS SAFETY_STOCK_SELLING_VALUE,
                (SAFETY_STOCK * UNIT_COST)                 AS SAFETY_STOCK_COST_VALUE,
                (REORDER_POINT * AVG_SELLING_PRICE_RMB)    AS REORDER_POINT_SELLING_VALUE,
                (REORDER_POINT * UNIT_COST)                AS REORDER_POINT_COST_VALUE,
                (ss2 * AVG_SELLING_PRICE_RMB)              AS SS2_SELLING_VALUE,
                (ss2 * UNIT_COST)                          AS SS2_COST_VALUE,
                (ss3 * AVG_SELLING_PRICE_RMB)              AS SS3_SELLING_VALUE,
                (ss3 * UNIT_COST)                          AS SS3_COST_VALUE,
                (AMU * AVG_SELLING_PRICE_RMB)              AS AMU_SELLING_VALUE,
                (AMU * UNIT_COST)                          AS AMU_COST_VALUE,
                (AMF * AVG_SELLING_PRICE_RMB)              AS AMF_SELLING_VALUE,
                (AMF * UNIT_COST)                          AS AMF_COST_VALUE,
                (LOT_SIZE_MIN * AVG_SELLING_PRICE_RMB)     AS LOT_SIZE_MIN_SELLING_VALUE,
                (LOT_SIZE_MIN * UNIT_COST)                 AS LOT_SIZE_MIN_COST_VALUE,
                (MAXIMUM_LOT_SIZE * AVG_SELLING_PRICE_RMB) AS MAXIMUM_LOT_SIZE_SELLING_VALUE,
                (MAXIMUM_LOT_SIZE * UNIT_COST)             AS MAXIMUM_LOT_SIZE_COST_VALUE,
                (FIXED_LOT_SIZE * AVG_SELLING_PRICE_RMB)   AS FIXED_LOT_SIZE_SELLING_VALUE,
                (FIXED_LOT_SIZE * UNIT_COST)               AS FIXED_LOT_SIZE_COST_VALUE,
                (ROUNDING_VALUE * AVG_SELLING_PRICE_RMB)   AS ROUNDING_VALUE_SELLING_VALUE,
                (ROUNDING_VALUE * UNIT_COST)               AS ROUNDING_VALUE_COST_VALUE,
                (MOQ_TO_CUSTOMER * AVG_SELLING_PRICE_RMB)  AS MOQ_TO_CUSTOMER_SELLING_VALUE,
                (MOQ_TO_CUSTOMER * UNIT_COST)              AS MOQ_TO_CUSTOMER_COST_VALUE,
                (MDQ_TO_CUSTOMER * AVG_SELLING_PRICE_RMB)  AS MDQ_TO_CUSTOMER_SELLING_VALUE,
                (MDQ_TO_CUSTOMER * UNIT_COST)              AS MDQ_TO_CUSTOMER_COST_VALUE,
                (RV_TO_CUSTOMER * AVG_SELLING_PRICE_RMB)   AS RV_TO_CUSTOMER_SELLING_VALUE,
                (RV_TO_CUSTOMER * UNIT_COST)               AS RV_TO_CUSTOMER_COST_VALUE
        from ${SCPA.MATERIAL_MASTER_V} t
        <where>
            <if test="selectedValue != null and selectedValue.isEmpty() == false">
                <foreach collection="categroy" separator=" and " item="item" index="index" open=" and ">
                    <choose>
                        <when test="selectedValue[index] == 'Others'.toString()">
                            (${item} = 'Others' or ${item} is null)
                        </when>
                        <when test="selectedValue[index] != null and selectedValue[index] != ''.toString()">
                            ${item} = #{selectedValue[${index}], jdbcType=VARCHAR}
                        </when>
                    </choose>
                </foreach>
            </if>
            <if test="_filters != null and _filters != ''.toString()">
                and ${_filters}
            </if>
        </where>
    </sql>


    <select id="queryReport1DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1DetailsSql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1DetailsSql"/>
        <include refid="global.select_footer"/>
    </select>
</mapper>
