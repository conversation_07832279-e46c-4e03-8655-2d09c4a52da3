<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.master.dao.IBomStructureP001Dao">
    <select id="queryCascader" resultType="java.util.Map">
        select * from BOM_STRUCTURE_P001_FILTER_V order by category, decode (name,'Others','zzz',name)
    </select>

    <select id="queryOprCascader" resultType="java.util.Map">
        WITH TEMP AS (SELECT T.COLUMN_NAME AS NAME, 'COUNT' AS CATEGORY
                      FROM USER_TAB_COLS T
                      WHERE T.TABLE_NAME = 'BOM_STRUCTURE_P001_V'
                        AND T.DATA_TYPE = 'VARCHAR2'
                      UNION ALL
                      SELECT T.COLUMN_NAME AS NAME, 'COUNT_DISTINCT'
                      FROM USER_TAB_COLS T
                      WHERE T.TABLE_NAME = 'BOM_STRUCTURE_P001_V'
                        AND T.DATA_TYPE = 'VARCHAR2'
                      UNION ALL
                      SELECT T.COLUMN_NAME AS NAME, 'MAX'
                      FROM USER_TAB_COLS T
                      WHERE T.TABLE_NAME = 'BOM_STRUCTURE_P001_V'
                        AND T.DATA_TYPE = 'NUMBER'
                      UNION ALL
                      SELECT T.COLUMN_NAME AS NAME, 'MIN'
                      FROM USER_TAB_COLS T
                      WHERE T.TABLE_NAME = 'BOM_STRUCTURE_P001_V'
                        AND T.DATA_TYPE = 'NUMBER'
                      UNION ALL
                      SELECT T.COLUMN_NAME AS NAME, 'AVG'
                      FROM USER_TAB_COLS T
                      WHERE T.TABLE_NAME = 'BOM_STRUCTURE_P001_V'
                        AND T.DATA_TYPE = 'NUMBER'
                      UNION ALL
                      SELECT T.COLUMN_NAME AS NAME, 'SUM'
                      FROM USER_TAB_COLS T
                      WHERE T.TABLE_NAME = 'BOM_STRUCTURE_P001_V'
                        AND T.DATA_TYPE = 'NUMBER')
        SELECT *
        FROM TEMP
        ORDER BY CATEGORY, NAME
    </select>

    <sql id="queryReport1Sql">
        select /*+ parallel(t 12) */
               <foreach collection="categroy" separator="," item="item">
                    nvl(${item},'Others') "${item}"
               </foreach>,
               <foreach collection="columns" separator="," item="item">
                    ${item}
               </foreach>
        from ${SCPA.BOM_STRUCTURE_P001_V} t
        <if test="_filters != null and _filters != ''.toString()">
            WHERE ${_filters}
        </if>
        GROUP BY
        <foreach collection="categroy" separator="," item="item">
            nvl(${item},'Others')
        </foreach>
        ORDER BY
        <foreach collection="categroy" separator="," item="item">
            nvl(${item},'Others')
        </foreach>
    </sql>

    <select id="queryReport1Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1Sql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1Sql"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport1DetailsSql">
        select *
        from ${SCPA.BOM_STRUCTURE_P001_V} t
        <where>
            <if test="selectedValue != null and selectedValue.isEmpty() == false">
                <foreach collection="categroy" separator=" and " item="item" index="index" open=" and ">
                    <choose>
                        <when test="selectedValue[index] == 'Others'.toString()">
                            (${item} = 'Others' or ${item} is null)
                        </when>
                        <when test="selectedValue[index] != null and selectedValue[index] != ''.toString()">
                            ${item} = #{selectedValue[${index}], jdbcType=VARCHAR}
                        </when>
                    </choose>
                </foreach>
            </if>
            <if test="_filters != null and _filters != ''.toString()">
                and ${_filters}
            </if>
        </where>
    </sql>


    <select id="queryReport1DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1DetailsSql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1DetailsSql"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport1RouteDetailsSql">
        SELECT T.ROUTE
        FROM SCPA.BOM_STRUCTURE_ROUTE_P001_V T
        <choose>
            <when test="selectedDetailsValue != null and selectedDetailsValue.isEmpty() == false">
                WHERE T.MATERIAL = #{selectedDetailsValue[0], jdbcType=VARCHAR}
                  AND T.PLANT_CODE = #{selectedDetailsValue[1], jdbcType=VARCHAR}
                  AND T.BOM_COMPONENT = #{selectedDetailsValue[2], jdbcType=VARCHAR}
                  AND T.BOM_PLANT_CODE = #{selectedDetailsValue[3], jdbcType=VARCHAR}
            </when>
            <otherwise> WHERE 1 = 0 </otherwise>
        </choose>
    </sql>


    <select id="queryReport1RouteDetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1RouteDetailsSql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1RouteDetails" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1RouteDetailsSql"/>
        <include refid="global.select_footer"/>
    </select>
</mapper>
