<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.master.dao.IEtoBomDao">
    <select id="queryCascader" resultType="java.util.Map">
        SELECT NAME,
               CATEGORY
        FROM SCPA.ETO_BOM_FILTER_V
        ORDER BY CATEGORY, DECODE(NAME, 'Others', 'zzz', NAME)
    </select>

    <select id="queryReport1Columns" resultType="java.lang.String">
        SELECT DISTINCT TO_CHAR(ACTUAL_RELEASE_DATE, 'YYYYMM') AS ACTUAL_RELEASE_MONTH
        FROM SCPA.CTO_RESERVATION_V
        <where>
            <if test="filters != null and filters != ''.toString()">
                ${filters}
            </if>
            AND ACTUAL_RELEASE_DATE between TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') and to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
            AND <foreach collection="categroy" separator="||" item="item">
            ${item}
        </foreach> || ACTUAL_RELEASE_DATE IS NOT NULL
        </where>
        ORDER BY TO_CHAR(ACTUAL_RELEASE_DATE, 'YYYYMM') DESC
    </select>

    <sql id="queryReport1Sql">
        WITH ETO_RESERVATION AS (SELECT <foreach collection="categroy" separator="," item="item">
                                    ${item}
                                </foreach>,
                                MO_NUMBER,
                                SUM(REQUIREMENT_QUANTITY) AS REQUIREMENT_QUANTITY
                        FROM SCPA.ETO_RESERVATION_V
                        <where>
                            <if test="filters != null and filters != ''.toString()">
                                ${filters}
                            </if>
                            AND <foreach collection="categroy" separator="||" item="item">
                                    ${item}
                                </foreach>IS NOT NULL
                            AND SO_CREATED_DATE between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                                and to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                        </where>
                        GROUP BY <foreach collection="categroy" separator="," item="item">
                                    ${item}
                                </foreach>,
                            MO_NUMBER),
        ETO_PLAN AS (SELECT PLANNING_MATERIAL,
                            PLANT_CODE,
                            WBS_ELEMENT,
                            MAX(SO_ORDER_QUANTITY) AS SO_ORDER_QUANTITY
                      FROM SCPA.ETO_RESERVATION_V
                        <where>
                            <if test="filters != null and filters != ''.toString()">
                                ${filters}
                            </if>
                            AND <foreach collection="categroy" separator="||" item="item">
                            ${item}
                        </foreach>IS NOT NULL
                            AND SO_CREATED_DATE between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                            and to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                        </where>
                      GROUP BY PLANNING_MATERIAL, PLANT_CODE, WBS_ELEMENT),
        ETO_FREQUENCY AS (SELECT PLANNING_MATERIAL,PLANNING_BOM_COMPONENT, PLANT_CODE, WBS_ELEMENT
                          FROM SCPA.ETO_RESERVATION_V
                          <where>
                                <if test="filters != null and filters != ''.toString()">
                                    ${filters}
                                </if>
                                AND
                            <foreach collection="categroy" separator="||" item="item">
                                ${item}
                            </foreach>IS NOT NULL
                                AND SO_CREATED_DATE between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                                and to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                          </where>
                          GROUP BY PLANNING_MATERIAL,PLANNING_BOM_COMPONENT, PLANT_CODE, WBS_ELEMENT)
        SELECT <foreach collection="categroy" separator="," item="item">
                    T.${item} AS ${item}
                </foreach>,
                ROUND(SUM(REQUIREMENT_QUANTITY) / MAX(PLAN.SO_ORDER_QUANTITY) , 3) AS ACCUMULATE_BOM_RATIO,
                SUM(REQUIREMENT_QUANTITY)                        AS ACCUMULATE_COMPONENT_USAGE,
                MAX(PLAN.SO_ORDER_QUANTITY)                             AS ACCUMULATE_PLANNING_MATERIAL_OUTPUT,
                MAX(WBS_FREQUENCY) AS ACCUMULATE_WBS_FREQUENCY
        FROM ETO_RESERVATION T
        LEFT JOIN (SELECT PLANNING_MATERIAL, PLANT_CODE, SUM(SO_ORDER_QUANTITY) AS SO_ORDER_QUANTITY
                   FROM ETO_PLAN
                   GROUP BY PLANNING_MATERIAL, PLANT_CODE) PLAN
        ON PLAN.PLANNING_MATERIAL = T.PLANNING_MATERIAL AND PLAN.PLANT_CODE = T.PLANT_CODE
        LEFT JOIN (SELECT PLANNING_MATERIAL, PLANNING_BOM_COMPONENT, PLANT_CODE, COUNT(WBS_ELEMENT) AS WBS_FREQUENCY
                   FROM ETO_FREQUENCY
                   GROUP BY PLANNING_MATERIAL, PLANNING_BOM_COMPONENT, PLANT_CODE) F
        ON F.PLANNING_MATERIAL = T.PLANNING_MATERIAL AND F.PLANNING_BOM_COMPONENT =T.PLANNING_BOM_COMPONENT AND F.PLANT_CODE=T.PLANT_CODE
        GROUP BY <foreach collection="categroy" separator="," item="item">
                    T.${item}
                 </foreach>

    </sql>

    <select id="queryReport1Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1Sql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1Sql"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport2Sql">
        WITH MVD_PLAN AS (SELECT 'MVD_PLAN' AS SO_MATERIAL, PLANT_CODE, PLANNING_MATERIAL, COUNT(1) AS RATIO
                    FROM ETO_RESERVATION_V
                    <where>
                        <if test="filters != null and filters != ''.toString()">
                            ${filters}
                        </if>
                        AND
                        SO_MATERIAL IN ('LH-MVD', 'SE-MVD')
                        AND SO_CREATED_DATE between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                        and to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                    </where>
                    GROUP BY PLANNING_MATERIAL, PLANT_CODE),
        MVD_PLAN_TOTAL AS (SELECT 'MVD_PLAN' AS SO_MATERIAL, COUNT(1) AS TOTAL_RATIO
                    FROM ETO_RESERVATION_V
                    <where>
                        <if test="filters != null and filters != ''.toString()">
                            ${filters}
                        </if>
                        AND
                        SO_MATERIAL IN ('LH-MVD', 'SE-MVD')
                        AND SO_CREATED_DATE between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                        and to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                    </where>),
        MVD_PAR_PLAN AS (SELECT 'MVD-PAR_PLAN' AS SO_MATERIAL, PLANT_CODE, PLANNING_BOM_COMPONENT, COUNT(1) AS RATIO
                    FROM ETO_RESERVATION_V
                    <where>
                        <if test="filters != null and filters != ''.toString()">
                            ${filters}
                        </if>
                        AND
                        SO_MATERIAL IN ('LH-MVD-PAR', 'SE-MVD-PAR')
                        AND SO_CREATED_DATE between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                        and to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                    </where>
                    GROUP BY PLANNING_BOM_COMPONENT, PLANT_CODE),
        MVD_PAR_PLAN_TOTAL AS (SELECT 'MVD-PAR_PLAN' AS SO_MATERIAL, COUNT(1) AS TOTAL_RATIO
        FROM ETO_RESERVATION_V
        <where>
            <if test="filters != null and filters != ''.toString()">
                ${filters}
            </if>
            AND
            SO_MATERIAL IN ('LH-MVD-PAR', 'SE-MVD-PAR')
            AND SO_CREATED_DATE between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
            and to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
        </where>)
        SELECT P.SO_MATERIAL, PLANT_CODE, P.PLANNING_MATERIAL, ROUND(P.RATIO / F.TOTAL_RATIO, 6) AS RATIO
            FROM MVD_PLAN P
        LEFT JOIN MVD_PLAN_TOTAL F ON F.SO_MATERIAL = P.SO_MATERIAL
        UNION ALL
        SELECT P.SO_MATERIAL, PLANT_CODE, P.PLANNING_BOM_COMPONENT, ROUND(P.RATIO / F.TOTAL_RATIO, 6) AS RATIO
            FROM MVD_PAR_PLAN P
        LEFT JOIN MVD_PAR_PLAN_TOTAL F ON F.SO_MATERIAL = P.SO_MATERIAL

    </sql>

    <select id="queryReport2Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport2Sql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport2" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport2Sql"/>
        <include refid="global.select_footer"/>
    </select>
</mapper>
