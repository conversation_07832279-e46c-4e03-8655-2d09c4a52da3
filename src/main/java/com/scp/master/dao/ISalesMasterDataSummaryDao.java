package com.scp.master.dao;

import org.apache.ibatis.annotations.Mapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface ISalesMasterDataSummaryDao {

    List<Map<String, String>> queryCascader();

    int queryReport1Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1(Map<String, Object> parameterMap);

    int queryReport1DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1Details(Map<String, Object> parameterMap);
}
