package com.scp.master.dao;

import com.scp.master.bean.MasterDataOutputColumn;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IAbnormalMasterDataDao {

    void dropResultTable();

    void createResultTable(@Param("list") List<MasterDataOutputColumn> columnList);

    List<Map<String, Object>> getDefineList(Map<String, Object> parameterMap);

    void mergeResult(Map<String, Object> parameterMap);

    int queryResultCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryResult(Map<String, Object> parameterMap);

    int queryRulesCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryRules(Map<String, Object> parameterMap);

    Map<String, Object> queryRuleByID(Map<String, Object> parameterMap);

    void updateRule(Map<String, Object> parameterMap);

    void saveRule(Map<String, Object> parameterMap);

    void refreshMasterDataMV(@Param("groupList") List<String> groupList);

    List<Map<String, String>> queryCascader();

    List<Map<String, String>> queryGroupCascader();

    List<Map<String, Object>> queryDashboard(Map<String, Object> parameterMap);

    List<Map<String, String>> queryReasonCode();

    int queryMasterDataDetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryMasterDataDetails(Map<String, Object> parameterMap);
}
