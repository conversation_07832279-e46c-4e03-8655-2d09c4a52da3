package com.scp.master.service;


import com.starter.context.bean.Response;
import jakarta.servlet.http.HttpServletResponse;

import java.util.Map;

public interface IEtoBomService {

    Response initPage();


    Response queryReport1Columns(Map<String, Object> parameterMap);

    Response queryReport1(Map<String, Object> parameterMap);

    void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport2(Map<String, Object> parameterMap);

    void downloadReport2(Map<String, Object> parameterMap, HttpServletResponse response);
}

