package com.scp.master.service;

import com.starter.context.bean.Response;

import jakarta.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.util.Map;

public interface ITriggerApplicationReportService {

    Response refreshTriggerReport();

    Response initPage();

    Response queryReport1Columns(Map<String, Object> parameterMap);

    Response queryReport1(Map<String, Object> parameterMap);

    Response queryReport1Details(Map<String, Object> parameterMap);

    Response queryReport2(Map<String, Object> parameterMap) throws ParseException;

    Response queryReport3(Map<String, Object> parameterMap);

    void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport3Details(Map<String, Object> parameterMap);

    void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport4(Map<String, Object> parameterMap);

    void downloadReport4(Map<String, Object> parameterMap, HttpServletResponse response);
}
