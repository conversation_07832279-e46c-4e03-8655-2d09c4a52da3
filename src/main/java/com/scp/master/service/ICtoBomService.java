package com.scp.master.service;

/**
 * <AUTHOR>
 * @date 2023/12/25 15:23
 * @comment
 */


import com.starter.context.bean.Response;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;

public interface ICtoBomService {

    Response initPage();


    Response queryReport1Columns(Map<String, Object> parameterMap);

    Response queryReport1(Map<String, Object> parameterMap);

    void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse response);
}

