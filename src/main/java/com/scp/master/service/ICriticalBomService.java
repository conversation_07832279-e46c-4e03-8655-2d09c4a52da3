package com.scp.master.service;

import com.starter.context.bean.Response;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

public interface ICriticalBomService {
    Response initPage(Map<String, Object> parameterMap, String user_id);

    Response adminCheck(Map<String, Object> parameterMap, String user_id);

    Response queryReport1(Map<String, Object> parameterMap);

    void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport2(Map<String, Object> parameterMap);

    void downloadReport2(Map<String, Object> parameterMap, HttpServletResponse response);

    Response saveReport2(String userid, Map<String, Object> parameterMap);

    Response uploadReport2(String userid, MultipartFile file) throws Exception;

    void downloadReport2Template(Map<String, Object> parameterMap, HttpServletResponse response);

    Response querySankey<PERSON>hart(Map<String, Object> parameterMap);

}
