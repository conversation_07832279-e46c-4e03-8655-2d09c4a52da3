package com.scp.master.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.starter.utils.Utils;
import com.starter.context.bean.*;
import com.scp.master.bean.MasterDataOutputColumn;
import com.scp.master.dao.IAbnormalMasterDataDao;
import com.scp.master.service.IAbnormalMasterDataService;
import com.starter.utils.excel.ExcelTemplate;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Scope("prototype")
@Transactional
public class AbnormalMasterDataServiceImpl implements IAbnormalMasterDataService {

    @Resource
    private Response response;

    @Resource
    private IAbnormalMasterDataDao abnormalMasterDataDao;

    @Resource
    private ExcelTemplate excelTemplate;

    private static boolean CHECKING = false;

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response checkMasterData() {
        if (CHECKING == true) {
            return response.set(Status.FORBIDDEN, "Job is running, please wait...");
        }
        CHECKING = true;

        try {
            // 查询定义列
            List<Map<String, Object>> defineList = abnormalMasterDataDao.getDefineList(new HashMap<>());
            List<MasterDataOutputColumn> columnList = new ArrayList<>();
            for (Map<String, Object> map : defineList) {
                JSONArray array = JSONArray.parseArray((String) map.get("OUTPUT_COLUMN"));
                for (Object obj : array) {
                    columnList.add(new MasterDataOutputColumn(obj));
                }
            }

            // 删除TRADING_MASTER_DATA_RESULT
            abnormalMasterDataDao.dropResultTable();
            // 重建TRADING_MASTER_DATA_RESULT
            abnormalMasterDataDao.createResultTable(columnList);

            // 执行meger语句
            Map<String, Object> parameterMap = new HashMap<>();
            for (Map<String, Object> map : defineList) {
                String sql = Utils.clob2String(map.get("SQL_SCRIPT"));
                if (this.invalidSQL(sql)) {
                    log.error(map.get("GROUP_NAME") + "存在异常SQL语句, 此语句将不会被执行");
                    continue;
                }
                JSONArray array = JSONArray.parseArray((String) map.get("OUTPUT_COLUMN"));
                columnList = new ArrayList<>();
                for (Object obj : array) {
                    columnList.add(new MasterDataOutputColumn(obj));
                }
                parameterMap.put("columns", columnList);
                parameterMap.put("sql", sql);
                // meger
                try {
                    abnormalMasterDataDao.mergeResult(parameterMap);
                } catch (Exception e) {
                    response.setError(e);
                    log.error(map.get("GROUP_NAME") + "执行失败", e);
                }
            }

            // refresh material view
            List<String> groupList = defineList.stream().map(e -> StringUtils.split((String) e.get("GROUP_NAME"), "-")[0]).collect(Collectors.toList());
            abnormalMasterDataDao.refreshMasterDataMV(groupList);
        } catch (Exception e) {
            response.setError(e);
            log.error("计算Master Data失败", e);
        } finally {
            CHECKING = false;
        }
        return response;
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("filterOpts", Utils.parseCascader(abnormalMasterDataDao.queryCascader()));
        resultMap.put("filterGroupOpts", Utils.parseCascader(abnormalMasterDataDao.queryGroupCascader()));
        resultMap.put("nestedHeader", this.queryNestedHeader(parameterMap));

        List<Map<String, String>> reasonCodes = abnormalMasterDataDao.queryReasonCode();
        Map<String, String> reasonCodeMap = new HashMap<>();
        for (Map<String, String> map : reasonCodes) {
            reasonCodeMap.put(map.get("ERROR_CODE"), map.get("DESCRIPTION"));
        }
        resultMap.put("reasonCode", reasonCodeMap);

        return response.setBody(resultMap);
    }

    private Map<String, List<String>> queryNestedHeader(Map<String, Object> parameterMap) {
        Map<String, List<String>> nestedHeader = new LinkedHashMap<>();
        List<Map<String, Object>> defineList = abnormalMasterDataDao.getDefineList(parameterMap);
        for (Map<String, Object> map : defineList) {
            JSONArray array = new JSONArray();
            try {
                array = JSONArray.parseArray((String) map.get("OUTPUT_COLUMN"));
            } catch (Exception e) {
                log.error("解析TRADING_MASTER_DATA_RULE OUTPUT_COLUMN出错", e);
            }

            String name = (String) map.get("GROUP_NAME");
            List<String> list = nestedHeader.computeIfAbsent(name, k -> new ArrayList<>());
            for (Object obj : array) {
                list.add(((JSONObject) obj).getString("name"));
            }

            list.sort((o1, o2) -> {
                if (StringUtils.containsIgnoreCase(o1, "Reason")) {
                    return -1;
                } else {
                    if (o2 != null) {
                        return 0;
                    } else {
                        return 1;
                    }
                }
            });
        }
        return nestedHeader;
    }

    @Override
    @SuppressWarnings("unchecked")
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryDashboard(Map<String, Object> parameterMap) {
        this.parseCascaderFilter(parameterMap);
        List<String> categroies = (List<String>) parameterMap.get("categroies");

        List<Map<String, Object>> defineList = abnormalMasterDataDao.getDefineList(parameterMap);
        List<String> groupList = defineList.stream().map(e -> StringUtils.split((String) e.get("GROUP_NAME"), "-")[0]).collect(Collectors.toList());
        parameterMap.put("groupList", groupList);

        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setLength(SimplePage.PAGE_MAX);
        List<Map<String, Object>> resultList = abnormalMasterDataDao.queryDashboard(parameterMap);
        Map<String, Object> totalMap = new HashMap<>();

        BigDecimal total = BigDecimal.ZERO;
        for (String groupIndex : groupList) {
            totalMap.put(groupIndex + "_TOTAL", BigDecimal.ZERO);
        }

        for (Map<String, Object> map : resultList) {
            total = total.add(Utils.parseBigDecimal(map.get("TOTAL")));
            for (String groupIndex : groupList) {
                BigDecimal temp = Utils.parseBigDecimal(totalMap.get(groupIndex + "_TOTAL"));
                temp = temp.add(Utils.parseBigDecimal(map.get(groupIndex + "_TOTAL")));
                totalMap.put(groupIndex + "_TOTAL", temp);
            }
        }

        totalMap.put("TOTAL", total);

        if (total.compareTo(BigDecimal.ZERO) != 0) {
            for (String groupIndex : groupList) {
                BigDecimal gtotal = Utils.parseBigDecimal(totalMap.get(groupIndex + "_TOTAL"));
                totalMap.put(groupIndex + "_PERCENT", gtotal.divide(total, 4, RoundingMode.HALF_UP));
            }
        }
        totalMap.put(categroies.get(categroies.size() - 1), "Total");

        resultList.add(totalMap);
        page.setData(resultList);
        page.setTotal(page.getData().size());
        return response.setBody(page);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryRules(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        if (StringUtils.isBlank(page.getSort())) {
            page.setSort("ORDER_NO");
        }
        int total = abnormalMasterDataDao.queryRulesCount(parameterMap);
        page.setTotal(total);
        if (total > 0) {
            page.setData(abnormalMasterDataDao.queryRules(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryRuleByID(Map<String, Object> parameterMap) {
        Map<String, Object> result = abnormalMasterDataDao.queryRuleByID(parameterMap);
        if (result == null) {
            result = new HashMap<>();
        }
        result.put("SQL_SCRIPT", Utils.clob2String(result.get("SQL_SCRIPT")));
        return response.setBody(result);
    }

    @Override
    public void downloadResult(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        this.parseCascaderFilter(parameterMap);

        Map<String, List<String>> selectedHeaderMap = this.queryNestedHeader(parameterMap);
        List<String> selectedHeaders = new ArrayList<>();
        for (List<String> value : selectedHeaderMap.values()) {
            selectedHeaders.addAll(value);
        }
        parameterMap.put("selectedHeaders", selectedHeaders);

        String fileName = "master_data_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.master.dao.IAbnormalMasterDataDao.queryResult", parameterMap);
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response saveRule(Map<String, Object> parameterMap) {
        if (StringUtils.isNotBlank((String) parameterMap.get("rowid"))) {
            abnormalMasterDataDao.updateRule(parameterMap);
        } else {
            abnormalMasterDataDao.saveRule(parameterMap);
        }

        return response;
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryResult(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);

        this.parseCascaderFilter(parameterMap);

        Map<String, List<String>> selectedHeaderMap = this.queryNestedHeader(parameterMap);
        List<String> selectedHeaders = new ArrayList<>();
        for (List<String> value : selectedHeaderMap.values()) {
            selectedHeaders.addAll(value);
        }
        parameterMap.put("selectedHeaders", selectedHeaders);

        page.setTotal(abnormalMasterDataDao.queryResultCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(abnormalMasterDataDao.queryResult(parameterMap));
        }
        return response.setBody(page);
    }

    @SuppressWarnings("unchecked")
    private void parseCascaderFilter(Map<String, Object> parameterMap) {
        List<String> categroies = (List<String>) parameterMap.get("categroies");
        if (categroies == null || categroies.isEmpty() == true) {
            categroies = new ArrayList<>();
            categroies.add("CLUSTER_NAME");
            categroies.add("ENTITY");
        }
        parameterMap.put("categroies", categroies);

        JSONArray categoryObj = (JSONArray) parameterMap.get("filterList");

        if (categoryObj != null) {
            Map<String, List<String>> filterMap = new HashMap<>();
            for (Object subObj : categoryObj) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = subArray.getString(0);
                String value = subArray.getString(1);
                String key = Utils.randomStr(8);

                if (Utils.hasInjectionAttack(columnName)) {
                    continue;
                }

                List<String> fl = filterMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                fl.add("#{" + key + ",jdbcType=VARCHAR}");
                parameterMap.put(key, value);
            }

            List<String> filterList = new ArrayList<>();
            for (String key : filterMap.keySet()) {
                List<String> fl = filterMap.get(key);
                filterList.add(key + " in (" + StringUtils.join(fl, ",") + ")");
            }
            parameterMap.put("filters", StringUtils.join(filterList, " and "));
        }

        String material = (String) parameterMap.get("material");
        if (StringUtils.isNotBlank(material)) {
            parameterMap.put("materialList", Utils.splitValue(material));
        }

        List<String> selectedGroupList = new ArrayList<>();

        // selectedGroup
        JSONArray array2 = (JSONArray) parameterMap.get("selectedGroup");
        if (array2 != null) {
            Map<String, List<String>> filterMap = new HashMap<>();
            for (Object subObj : array2) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = StringUtils.trim(StringUtils.split(subArray.getString(0), "-")[0]) + "_REASON_CODE";
                selectedGroupList.add(subArray.getString(0));
                String value = subArray.getString(1);
                String key = Utils.randomStr(8);

                if (Utils.hasInjectionAttack(columnName)) {
                    continue;
                }

                List<String> fl = filterMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                fl.add("#{" + key + ",jdbcType=VARCHAR}");
                parameterMap.put(key, value);
            }

            List<String> filterList = new ArrayList<>();
            for (String key : filterMap.keySet()) {
                List<String> fl = filterMap.get(key);
                List<String> fw = new ArrayList<>();
                for (String f : fl) {
                    fw.add(key + " like '%' || " + f + " || '%'");
                }
                filterList.add("(" + StringUtils.join(fw, " or ") + ")");
            }
            if (filterList.isEmpty() == false) {
                parameterMap.put("filters2", "(" + StringUtils.join(filterList, " or ") + ")");
            }
            parameterMap.put("selectedGroupList", selectedGroupList);
        }
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryMasterDataDetails(Map<String, Object> parameterMap) {
        this.parseCascaderFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        String selectedKey = (String) parameterMap.get("selectedKey");
        if (StringUtils.startsWith(selectedKey, "G")) {
            selectedKey = selectedKey.split("_")[0] + "_REASON_CODE";
        } else {
            selectedKey = null;
        }
        parameterMap.put("selectedKey", selectedKey);

        page.setTotal(abnormalMasterDataDao.queryMasterDataDetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(abnormalMasterDataDao.queryMasterDataDetails(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadMasterDataDetails(Map<String, Object> parameterMap, HttpServletResponse response) {
        this.parseCascaderFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String selectedKey = (String) parameterMap.get("selectedKey");
        if (StringUtils.startsWith(selectedKey, "G")) {
            selectedKey = selectedKey.split("_")[0] + "_REASON_CODE";
        } else {
            selectedKey = null;
        }
        parameterMap.put("selectedKey", selectedKey);

        String fileName = "master_data_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.master.dao.IAbnormalMasterDataDao.queryMasterDataDetails", parameterMap);
    }


    private boolean invalidSQL(String sql) {
        String[] invalidKeys = new String[]{
                "truncate ", "drop ", "alter ", "grant ", "invoke "
        };
        for (String k : invalidKeys) {
            if (StringUtils.containsIgnoreCase(sql, k)) {
                return true;
            }
        }
        return false;
    }
}
