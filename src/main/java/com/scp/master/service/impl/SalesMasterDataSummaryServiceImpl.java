package com.scp.master.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.scp.master.dao.ISalesMasterDataSummaryDao;
import com.scp.master.service.ISalesMasterDataSummaryService;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@Scope("prototype")
@Transactional
public class SalesMasterDataSummaryServiceImpl extends ServiceHelper implements ISalesMasterDataSummaryService {

    @Resource
    private Response response;

    @Resource
    private ISalesMasterDataSummaryDao salesMasterDataSummaryDao;

    @Resource
    private ExcelTemplate excelTemplate;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage() {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cascader", Utils.parseCascader(salesMasterDataSummaryDao.queryCascader()));
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        page.setTotal(salesMasterDataSummaryDao.queryReport1Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(salesMasterDataSummaryDao.queryReport1(parameterMap));
        }


        return response.setBody(page);
    }

    @Override
    public void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        String fileName = "sales_master_data_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.master.dao.ISalesMasterDataSummaryDao.queryReport1", parameterMap);
    }

    @Override
    public Response queryReport1Details(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateCascaderFilter(parameterMap);

        page.setTotal(salesMasterDataSummaryDao.queryReport1DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(salesMasterDataSummaryDao.queryReport1Details(parameterMap));
        }

        return response.setBody(page);
    }

    @Override
    public void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        String fileName = "details_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.master.dao.ISalesMasterDataSummaryDao.queryReport1Details", parameterMap);
    }

    private void generateValueColumn(Map<String, Object> parameterMap) {
        String type = (String) parameterMap.get("type");
        String calcType = (String) parameterMap.get("calcType");


    }

    /**
     * 生成cascader filter
     *
     * @param parameterMap 参数map
     */
    private void generateCascaderFilter(Map<String, Object> parameterMap) {
        this.generateCascaderFilterSQL(parameterMap);
        // categroy
        JSONArray categroyArray = (JSONArray) parameterMap.get("categroy");
        if (categroyArray == null || categroyArray.isEmpty()) {
            List<String> categroyDefault = new ArrayList<>();
            categroyDefault.add("PRODUCT_LINE");
            parameterMap.put("categroy", categroyDefault);
        }
    }
}
