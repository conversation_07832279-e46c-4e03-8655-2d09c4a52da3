package com.scp.master.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.scp.master.service.ICriticalBomService;
import com.scp.master.dao.ICriticalBomDao;
import com.starter.context.bean.*;
import com.starter.context.bean.scptable.ScpTableHelper;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import com.starter.utils.excel.SimpleSheetContentsHandler;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.model.StylesTable;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

@Service("CriticalBomService")
@Scope("prototype")
@Transactional
public class CriticalBomServiceImpl extends ServiceHelper implements ICriticalBomService {
    @Resource
    private ICriticalBomDao criticalBomDao;

    @Resource
    private Response response;

    @Resource
    private ExcelTemplate excelTemplate;

    @Resource
    private ScpTableHelper scpTableHelper;

    public final static String PARENT_CODE = "menu270";

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage(Map<String, Object> parameterMap, String user_id) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("plantList", criticalBomDao.queryAvailablePlants());
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response adminCheck(Map<String, Object> parameterMap,String userid) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("isAdmin", criticalBomDao.queryApplicationAuth(userid) > 0);
        return response.setBody(resultMap);
    }


    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        page.setTotal(criticalBomDao.queryReport1Count(parameterMap));
        if (page.getTotal() > 0) {
            List<LinkedHashMap<String, Object>> data = criticalBomDao.queryReport1(parameterMap);
            List<LinkedHashMap<String, Object>> resultData = processReport1Data(data, parameterMap, null);
            List<LinkedHashMap<String, Object>> dataTotal = criticalBomDao.queryReport1TotalLine(parameterMap);
            List<LinkedHashMap<String, Object>> resultTotalData = processReport1Data(dataTotal, parameterMap, null);

            // 更新总结果的总行数为去除重复后的行数
            page.setTotal(resultTotalData.size());
            page.setData(resultData);
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        List<LinkedHashMap<String, Object>> data = criticalBomDao.queryReport1TotalLine(parameterMap);
        int maxLevels = 0;
        for (LinkedHashMap<String, Object> item : data) {
            Object material_route = item.get("ROUTE");
            if (material_route instanceof String) {
                String route = (String) material_route;
                String[] routeParts = route.split("->");
                maxLevels = Math.max(maxLevels, routeParts.length);
            }
        }

        List<LinkedHashMap<String, Object>> processedData = processReport1Data(data, parameterMap,maxLevels);
        String fileName = "bom_structure" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, processedData);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(criticalBomDao.queryReport2Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(criticalBomDao.queryReport2(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport2(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "critical_material_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.simulation.dao.ICriticalBomDao.downloadReport2", parameterMap);
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response saveReport2(String userid, Map<String, Object> parameterMap) {
        List<String> avaliblePlant = criticalBomDao.queryAllPlant();
        scpTableHelper.setExcludeColumn(new ArrayList<>() {{
            add("ROW_ID");
        }});
        scpTableHelper.setWarningMessage("You have no privileges to modify data does not belong to you");
        scpTableHelper.setScpTableInsertHandler((headers, inserts) -> {
            if (headers.contains("PLANT_CODE")) {
                for (Map<String, Object> insert : inserts) {
                    String plantCode = (String) insert.get("PLANT_CODE");
                    if (avaliblePlant.contains(plantCode) == false) {
                        throw new SCPRuntimeException("invalid plant code found: " + plantCode);
                    }
                }
            }

            return criticalBomDao.createReport2ByTable(headers, inserts, userid);
        });
        scpTableHelper.setScpTableDeleteHandler(deletes -> criticalBomDao.deleteReport2ByTable(deletes, userid));
        scpTableHelper.setScpTableUpdateHandler((pk, updates) -> {
            if (updates.stream().anyMatch(e -> e.getKey().equals("PLANT_CODE") && avaliblePlant.contains((String) e.getValue()) == false)) {
                String plantCode = (String) updates.stream().filter(e -> e.getKey().equals("PLANT_CODE") && avaliblePlant.contains((String) e.getValue()) == false).collect(Collectors.toList()).get(0).getValue();
                throw new SCPRuntimeException("invalid plant code found: " + plantCode);
            }
            return criticalBomDao.updateReport2ByTable(pk, updates, userid);
        });
        Message message = scpTableHelper.execCRUD(parameterMap);
        criticalBomDao.queryReport2DeplicateRows();
        return response.setBody(message);
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response uploadReport2(String userid, MultipartFile file) throws Exception {
        File tempFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), ".tmp");
        file.transferTo(tempFile);
        List<String> avaliblePlant = criticalBomDao.queryAllPlant();

        List<Map<String, Object>> data = new ArrayList<>();
        excelTemplate.read(tempFile, 1, new SimpleSheetContentsHandler() {
            @Override
            public void handleRow(int rowNum) {

                if (rowNum == 0) {
                    return;
                }

                if (avaliblePlant.contains(row.get(1)) == false) {
                    throw new SCPRuntimeException("invalid plant code found: " + row.get(0));
                }

                Map<String, Object> map = new HashMap<>();
                map.put("MATERIAL", row.get(0));
                map.put("PLANT_CODE", row.get(1));
                map.put("USER_ID", userid);
                data.add(map);
                if (data.size() >= 64) {
                    criticalBomDao.insertReport2Data(data);
                    data.clear();
                }
            }
        }, new StylesTable());

        if (data.size() > 0) {
            criticalBomDao.insertReport2Data(data);
            data.clear();
        }

        criticalBomDao.queryReport2DeplicateRows();


        return response;
    }

    @Override
    public void downloadReport2Template(Map<String, Object> parameterMap, HttpServletResponse response) {
        String fileName = "critical_material_template.xlsx";
        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        resultList.add(map);
        map.put("MATERIAL", null);
        map.put("PLANT_CODE", null);
        excelTemplate.create(response, fileName, resultList);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response querySankeyChart(Map<String, Object> parameterMap) {
        // 从前端参数中提取selectedRow信息
        Map<String, Object> selectedRow = (Map<String, Object>) parameterMap.get("selectedRow");
        if (selectedRow == null) {
            return response.setError(new Exception("selectedRow parameter is required"));
        }

        String material = (String) selectedRow.get("L0_MATERIAL");

        // 从外层参数中获取plant数组
        List<String> plantList = (List<String>) parameterMap.get("plant");
        if (plantList == null || plantList.isEmpty()) {
            return response.setError(new Exception("plant parameter is required and cannot be empty"));
        }

        if (material == null) {
            return response.setError(new Exception("MATERIAL is required in selectedRow"));
        }

        // 设置查询参数
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("material", material);
        queryParams.put("plantList", plantList);

        // 查询BOM数据
        List<LinkedHashMap<String, Object>> bomData = criticalBomDao.querySankeyChart(queryParams);

        // 构建桑基图数据结构
        Map<String, Object> sankeyResult = buildSankeyData(bomData, material);

        return response.setBody(sankeyResult);
    }

    /**
     * 构建桑基图数据结构
     */
    private Map<String, Object> buildSankeyData(List<LinkedHashMap<String, Object>> bomData, String rootMaterial) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> data = new ArrayList<>();
        List<Map<String, Object>> links = new ArrayList<>();

        // 用于存储所有节点的值
        Map<String, Double> nodeValues = new HashMap<>();

        // 首先计算所有节点的值
        for (LinkedHashMap<String, Object> row : bomData) {
            String material = (String) row.get("MATERIAL");
            String component = (String) row.get("BOM_COMPONENT");
            Object qtyObj = row.get("COMPONENT_QTY");

            double qty = 0.0;
            if (qtyObj instanceof Number) {
                qty = ((Number) qtyObj).doubleValue();
            } else if (qtyObj instanceof String) {
                try {
                    qty = Double.parseDouble((String) qtyObj);
                } catch (NumberFormatException e) {
                    qty = 0.0;
                }
            }

            // 累加组件的数量
            nodeValues.put(component, nodeValues.getOrDefault(component, 0.0) + qty);

            // 创建链接
            Map<String, Object> link = new HashMap<>();
            link.put("source", material);
            link.put("target", component);
            link.put("value", qty);
            links.add(link);
        }

        // 添加根节点（如果不在nodeValues中）
        if (!nodeValues.containsKey(rootMaterial)) {
            // 根节点的值是所有直接子组件值的总和
            double rootValue = bomData.stream()
                    .filter(row -> rootMaterial.equals(row.get("MATERIAL")))
                    .mapToDouble(row -> {
                        Object qtyObj = row.get("COMPONENT_QTY");
                        if (qtyObj instanceof Number) {
                            return ((Number) qtyObj).doubleValue();
                        } else if (qtyObj instanceof String) {
                            try {
                                return Double.parseDouble((String) qtyObj);
                            } catch (NumberFormatException e) {
                                return 0.0;
                            }
                        }
                        return 0.0;
                    })
                    .sum();
            nodeValues.put(rootMaterial, rootValue);
        }

        // 构建数据节点
        for (Map.Entry<String, Double> entry : nodeValues.entrySet()) {
            Map<String, Object> node = new HashMap<>();
            node.put("name", entry.getKey());
            node.put("value", entry.getValue().intValue()); // 转换为整数
            data.add(node);
        }

        result.put("data", data);
        result.put("links", links);

        return result;
    }

    /**
     * report1处理数据
     *
     * @param data         原始数据列表
     * @param parameterMap 参数映射
     * @return 处理后的数据列表
     */
    public static List<LinkedHashMap<String, Object>> processReport1Data(List<LinkedHashMap<String, Object>> data, Map<String, Object> parameterMap, Integer maxLevels) {
        List<LinkedHashMap<String, Object>> processedData = new ArrayList<>();

        for (LinkedHashMap<String, Object> item : data) {
            // 创建一个新的 LinkedHashMap 来存储处理后的数据
            LinkedHashMap<String, Object> processedItem = new LinkedHashMap<>();
            processedItem.put("TOP", item.get("TOP"));
            processedItem.put("TOP PLANT CODE", item.get("TOP_PLANT_CODE"));
            processedItem.put("BOTTOM", item.get("BOTTOM"));
            processedItem.put("BOTTOM PLANT CODE", item.get("BOTTOM_PLANT_CODE"));
            processedItem.put("TRANSFER NUMBER", item.get("TRANSFER_NUMBER"));

            // 获取所有需要拆分的字段
            Map<String, String[]> fieldArrays = extractAndSplitFields(item);

            // 初始化所有级别的字段为null（如果指定了maxLevels）
            if (maxLevels != null) {
                for (int i = 0; i < maxLevels; i++) {
                    setLevelFields(processedItem, i, null, null, null, parameterMap, fieldArrays);
                }
            }

            // 将分割后的结果添加到 item 中
            String[] routeParts = fieldArrays.get("ROUTE");
            String[] plantParts = fieldArrays.get("PLANT_ROUTE");

            for (int i = 0; i < routeParts.length; i++) {
                // 检查 plantParts[i].trim() 是否在 parameterMap 中的 plant JSON 数组里
                if (!isPlantInJSONArray(parameterMap, plantParts[i].trim())) {
                    // 如果不在，跳出循环
                    break;
                }

                setLevelFields(processedItem, i, routeParts[i].trim(), plantParts[i].trim(),
                             fieldArrays.get("COMPONENT_QTY_ROUTE")[i].trim(), parameterMap, fieldArrays);

                processedItem.put("BOTTOM", routeParts[i].trim());
                processedItem.put("BOTTOM PLANT CODE", plantParts[i].trim());
            }

            processedData.add(processedItem);
        }

        List<LinkedHashMap<String, Object>> uniqueProcessedData = removeDuplicateRows(processedData);
        return uniqueProcessedData;
    }

    /**
     * 提取并拆分所有需要处理的字段
     */
    private static Map<String, String[]> extractAndSplitFields(LinkedHashMap<String, Object> item) {
        Map<String, String[]> fieldArrays = new HashMap<>();

        // 基础字段
        fieldArrays.put("ROUTE", splitField(item.get("ROUTE")));
        fieldArrays.put("PLANT_ROUTE", splitField(item.get("PLANT_ROUTE")));
        fieldArrays.put("COMPONENT_QTY_ROUTE", splitField(item.get("COMPONENT_QTY_ROUTE")));

        // 新增的所有字段
        fieldArrays.put("PLANT_TYPE_ROUTE", splitField(item.get("PLANT_TYPE_ROUTE")));
        fieldArrays.put("CLUSTER_NAME_ROUTE", splitField(item.get("CLUSTER_NAME_ROUTE")));
        fieldArrays.put("ENTITY_ROUTE", splitField(item.get("ENTITY_ROUTE")));
        fieldArrays.put("PRODUCT_LINE_ROUTE", splitField(item.get("PRODUCT_LINE_ROUTE")));
        fieldArrays.put("PRODUCTION_LINE_ROUTE", splitField(item.get("PRODUCTION_LINE_ROUTE")));
        fieldArrays.put("LOCAL_PRODUCT_LINE_ROUTE", splitField(item.get("LOCAL_PRODUCT_LINE_ROUTE")));
        fieldArrays.put("LOCAL_PRODUCT_FAMILY_ROUTE", splitField(item.get("LOCAL_PRODUCT_FAMILY_ROUTE")));
        fieldArrays.put("LOCAL_PRODUCT_SUBFAMILY_ROUTE", splitField(item.get("LOCAL_PRODUCT_SUBFAMILY_ROUTE")));
        fieldArrays.put("HYPER_CARE_LEVEL_ROUTE", splitField(item.get("HYPER_CARE_LEVEL_ROUTE")));
        fieldArrays.put("MRP_CONTROLLER_ROUTE", splitField(item.get("MRP_CONTROLLER_ROUTE")));
        fieldArrays.put("MRP_CONTROLLER_DESCRIPTION_ROUTE", splitField(item.get("MRP_CONTROLLER_DESCRIPTION_ROUTE")));
        fieldArrays.put("STOCKING_POLICY_ROUTE", splitField(item.get("STOCKING_POLICY_ROUTE")));
        fieldArrays.put("VENDOR_CODE_ROUTE", splitField(item.get("VENDOR_CODE_ROUTE")));
        fieldArrays.put("VENDOR_NAME_ROUTE", splitField(item.get("VENDOR_NAME_ROUTE")));
        fieldArrays.put("VENDOR_SHORT_NAME_ROUTE", splitField(item.get("VENDOR_SHORT_NAME_ROUTE")));
        fieldArrays.put("VENDOR_FULL_NAME_ROUTE", splitField(item.get("VENDOR_FULL_NAME_ROUTE")));
        fieldArrays.put("VENDOR_PARENT_NAME_ROUTE", splitField(item.get("VENDOR_PARENT_NAME_ROUTE")));
        fieldArrays.put("VENDOR_PARENT_CODE_ROUTE", splitField(item.get("VENDOR_PARENT_CODE_ROUTE")));
        fieldArrays.put("VENDOR_MATERIAL_ROUTE", splitField(item.get("VENDOR_MATERIAL_ROUTE")));
        fieldArrays.put("SOURCE_CATEGORY_ROUTE", splitField(item.get("SOURCE_CATEGORY_ROUTE")));
        fieldArrays.put("PLANNED_DELIV_TIME_ROUTE", splitField(item.get("PLANNED_DELIV_TIME_ROUTE")));
        fieldArrays.put("LT_RANGE_ROUTE", splitField(item.get("LT_RANGE_ROUTE")));
        fieldArrays.put("UNIT_COST_ROUTE", splitField(item.get("UNIT_COST_ROUTE")));
        fieldArrays.put("AVG_MONTHLY_ORDER_INTAKE_ROUTE", splitField(item.get("AVG_MONTHLY_ORDER_INTAKE_ROUTE")));
        fieldArrays.put("AMF_ONEMM_ROUTE", splitField(item.get("AMF_ONEMM_ROUTE")));
        fieldArrays.put("COV_ROUTE", splitField(item.get("COV_ROUTE")));
        fieldArrays.put("SS2_ROUTE", splitField(item.get("SS2_ROUTE")));
        fieldArrays.put("SS3_ROUTE", splitField(item.get("SS3_ROUTE")));
        fieldArrays.put("CALCULATED_ABC_ROUTE", splitField(item.get("CALCULATED_ABC_ROUTE")));
        fieldArrays.put("CALCULATED_FMR_ROUTE", splitField(item.get("CALCULATED_FMR_ROUTE")));
        fieldArrays.put("NEW_PRODUCTS_ROUTE", splitField(item.get("NEW_PRODUCTS_ROUTE")));
        fieldArrays.put("PROCUREMENT_TYPE_ROUTE", splitField(item.get("PROCUREMENT_TYPE_ROUTE")));
        fieldArrays.put("FOLLOW_UP_MATERIAL_ROUTE", splitField(item.get("FOLLOW_UP_MATERIAL_ROUTE")));
        fieldArrays.put("PRODN_SUPERVISOR_ROUTE", splitField(item.get("PRODN_SUPERVISOR_ROUTE")));
        fieldArrays.put("IN_HOUSE_PRODN_LT_ROUTE", splitField(item.get("IN_HOUSE_PRODN_LT_ROUTE")));
        fieldArrays.put("STARS_CODE_ROUTE", splitField(item.get("STARS_CODE_ROUTE")));
        fieldArrays.put("LAST_SO_SALES_DATE_ROUTE", splitField(item.get("LAST_SO_SALES_DATE_ROUTE")));
        fieldArrays.put("NONE_SALES_DURATION_ROUTE", splitField(item.get("NONE_SALES_DURATION_ROUTE")));
        fieldArrays.put("NONE_SALES_PERIOD_ROUTE", splitField(item.get("NONE_SALES_PERIOD_ROUTE")));
        fieldArrays.put("GRA_ROUTE", splitField(item.get("GRA_ROUTE")));
        fieldArrays.put("COMMODITY_CODE_ROUTE", splitField(item.get("COMMODITY_CODE_ROUTE")));
        fieldArrays.put("STARS_DESCRIPTION_ROUTE", splitField(item.get("STARS_DESCRIPTION_ROUTE")));
        fieldArrays.put("PLANNING_ALIVE_STATUS_ROUTE", splitField(item.get("PLANNING_ALIVE_STATUS_ROUTE")));
        fieldArrays.put("XBOARD_ROUTE", splitField(item.get("XBOARD_ROUTE")));
        fieldArrays.put("RES_COV_STK_ROUTE", splitField(item.get("RES_COV_STK_ROUTE")));
        fieldArrays.put("RES_COV_STK_RANGE_ROUTE", splitField(item.get("RES_COV_STK_RANGE_ROUTE")));
        fieldArrays.put("RES_COV_STK_LA_ROUTE", splitField(item.get("RES_COV_STK_LA_ROUTE")));
        fieldArrays.put("RES_COV_STK_LA_RANGE_ROUTE", splitField(item.get("RES_COV_STK_LA_RANGE_ROUTE")));
        fieldArrays.put("RES_COV_STK_OPO_ROUTE", splitField(item.get("RES_COV_STK_OPO_ROUTE")));
        fieldArrays.put("RES_COV_STK_OPO_RANGE_ROUTE", splitField(item.get("RES_COV_STK_OPO_RANGE_ROUTE")));
        fieldArrays.put("FIRST_CONSUMPTION_RANGE_ROUTE", splitField(item.get("FIRST_CONSUMPTION_RANGE_ROUTE")));
        fieldArrays.put("FIRST_PO_CREATE_RANGE_ROUTE", splitField(item.get("FIRST_PO_CREATE_RANGE_ROUTE")));
        fieldArrays.put("FIRST_SO_CREATE_RANGE_ROUTE", splitField(item.get("FIRST_SO_CREATE_RANGE_ROUTE")));
        fieldArrays.put("LAST_SO_SALES_RANGE_ROUTE", splitField(item.get("LAST_SO_SALES_RANGE_ROUTE")));
        fieldArrays.put("PRD_BOM_COMPONENT_ROUTE", splitField(item.get("PRD_BOM_COMPONENT_ROUTE")));
        fieldArrays.put("FIRST_CONS_DATE_ROUTE", splitField(item.get("FIRST_CONS_DATE_ROUTE")));
        fieldArrays.put("FIRST_SO_DATE_ROUTE", splitField(item.get("FIRST_SO_DATE_ROUTE")));
        fieldArrays.put("FIRST_PO_DATE_ROUTE", splitField(item.get("FIRST_PO_DATE_ROUTE")));
        fieldArrays.put("COV_RANGE_ROUTE", splitField(item.get("COV_RANGE_ROUTE")));
        fieldArrays.put("RISK_LEVEL_ROUTE", splitField(item.get("RISK_LEVEL_ROUTE")));
        fieldArrays.put("ADU_END_ORDER_ROUTE", splitField(item.get("ADU_END_ORDER_ROUTE")));
        fieldArrays.put("STDDEV_END_ORDER_ROUTE", splitField(item.get("STDDEV_END_ORDER_ROUTE")));
        fieldArrays.put("COV_END_ORDER_ROUTE", splitField(item.get("COV_END_ORDER_ROUTE")));
        fieldArrays.put("COV_RANGE_END_ORDER_ROUTE", splitField(item.get("COV_RANGE_END_ORDER_ROUTE")));
        fieldArrays.put("NUM_OF_WHERE_USE_ROUTE", splitField(item.get("NUM_OF_WHERE_USE_ROUTE")));
        fieldArrays.put("NUM_OF_WHERE_USE_RANGE_ROUTE", splitField(item.get("NUM_OF_WHERE_USE_RANGE_ROUTE")));
        fieldArrays.put("PRODUCT_GROUP_A_ROUTE", splitField(item.get("PRODUCT_GROUP_A_ROUTE")));
        fieldArrays.put("PRODUCT_GROUP_B_ROUTE", splitField(item.get("PRODUCT_GROUP_B_ROUTE")));
        fieldArrays.put("PRODUCT_GROUP_C_ROUTE", splitField(item.get("PRODUCT_GROUP_C_ROUTE")));
        fieldArrays.put("PRODUCT_GROUP_D_ROUTE", splitField(item.get("PRODUCT_GROUP_D_ROUTE")));
        fieldArrays.put("PRODUCT_GROUP_E_ROUTE", splitField(item.get("PRODUCT_GROUP_E_ROUTE")));
        fieldArrays.put("TOTAL_STOCK_QTY_ROUTE", splitField(item.get("TOTAL_STOCK_QTY_ROUTE")));
        fieldArrays.put("UU_STOCK_QTY_ROUTE", splitField(item.get("UU_STOCK_QTY_ROUTE")));
        fieldArrays.put("QI_STOCK_QTY_ROUTE", splitField(item.get("QI_STOCK_QTY_ROUTE")));
        fieldArrays.put("BLOCK_STOCK_QTY_ROUTE", splitField(item.get("BLOCKED_STOCK_ROUTE")));
        fieldArrays.put("PAST_DUE_SO_QTY_ROUTE", splitField(item.get("PAST_DUE_SO_QTY_ROUTE")));
        fieldArrays.put("PAST_DUE_SO_QTY_NON_BLOCK_ROUTE", splitField(item.get("PAST_DUE_SO_QTY_NON_BLOCK_ROUTE")));
        fieldArrays.put("TOTAL_OPEN_SO_QTY_ROUTE", splitField(item.get("TOTAL_OPEN_SO_QTY_ROUTE")));
        fieldArrays.put("WITHIN_LT_SO_QTY_ROUTE", splitField(item.get("WITHIN_LT_SO_QTY_ROUTE")));
        fieldArrays.put("WITHOUT_LT_SO_QTY_ROUTE", splitField(item.get("WITHOUT_LT_SO_QTY_ROUTE")));
        fieldArrays.put("TOTAL_OPEN_SO_QTY_NON_BLOCK_ROUTE", splitField(item.get("TOTAL_OPEN_SO_QTY_NON_BLOCK_ROUTE")));
        fieldArrays.put("WITHIN_LT_SO_NON_BLOCK_ROUTE", splitField(item.get("WITHIN_LT_SO_NON_BLOCK_ROUTE")));
        fieldArrays.put("WITHOUT_LT_SO_NON_BLOCK_ROUTE", splitField(item.get("WITHOUT_LT_SO_NON_BLOCK_ROUTE")));
        fieldArrays.put("OPEN_SO_QTY_ROUTE", splitField(item.get("OPEN_SO_QTY_ROUTE")));
        fieldArrays.put("OPEN_PO_QTY_ROUTE", splitField(item.get("OPEN_PO_QTY_ROUTE")));
        fieldArrays.put("PASTDUE_PO_QTY_ROUTE", splitField(item.get("PASTDUE_PO_QTY_ROUTE")));
        fieldArrays.put("OPEN_PO_LA_QTY_ROUTE", splitField(item.get("OPEN_PO_LA_QTY_ROUTE")));
        fieldArrays.put("OPEN_PO_AB_QTY_ROUTE", splitField(item.get("OPEN_PO_AB_QTY_ROUTE")));
        fieldArrays.put("ORDER_RESERVATION_QTY_ROUTE", splitField(item.get("ORDER_RESERVATION_QTY_ROUTE")));
        fieldArrays.put("PASTDUE_ORDER_RESERVATION_ROUTE", splitField(item.get("PASTDUE_ORDER_RESERVATION_ROUTE")));
        fieldArrays.put("ORDER_RESERVATION_QTY_WITHIN_7_DAYS_ROUTE", splitField(item.get("ORDER_RESERVATION_QTY_WITHIN_7_DAYS_ROUTE")));
        fieldArrays.put("ORDER_RESERVATION_QTY_WITHOUT_7_DAYS_ROUTE", splitField(item.get("ORDER_RESERVATION_QTY_WITHOUT_7_DAYS_ROUTE")));

        return fieldArrays;
    }

    /**
     * 拆分单个字段
     */
    private static String[] splitField(Object fieldValue) {
        if (fieldValue == null) {
            return new String[0];
        }
        return ((String) fieldValue).split("->");
    }

    /**
     * 设置指定级别的字段值
     *
     * @param processedItem 处理后的数据项
     * @param level        级别索引
     * @param material     材料值
     * @param plantCode    工厂代码值
     * @param componentQty 组件数量值
     * @param parameterMap 参数映射
     * @param fieldArrays  所有字段数组的映射
     */
    private static void setLevelFields(LinkedHashMap<String, Object> processedItem, int level,
                                     String material, String plantCode, String componentQty,
                                     Map<String, Object> parameterMap, Map<String, String[]> fieldArrays) {
        processedItem.put("L" + level + "_MATERIAL", material);
        processedItem.put("L" + level + "_PLANT CODE", plantCode);
        processedItem.put("L" + level + "_COMPONENT QTY", componentQty);

        if (parameterMap.get("fields") != null) {
            String fields = parameterMap.get("fields").toString();

            // 新增字段
            setFieldIfExists(processedItem, level, "PLANT_TYPE", "PLANT_TYPE_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "CLUSTER_NAME", "CLUSTER_NAME_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "ENTITY", "ENTITY_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "PRODUCT_LINE", "PRODUCT_LINE_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "PRODUCTION_LINE", "PRODUCTION_LINE_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "LOCAL_PRODUCT_LINE", "LOCAL_PRODUCT_LINE_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "LOCAL_PRODUCT_FAMILY", "LOCAL_PRODUCT_FAMILY_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "LOCAL_PRODUCT_SUBFAMILY", "LOCAL_PRODUCT_SUBFAMILY_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "HYPER_CARE_LEVEL", "HYPER_CARE_LEVEL_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "MRP_CONTROLLER", "MRP_CONTROLLER_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "MRP_CONTROLLER_DESCRIPTION", "MRP_CONTROLLER_DESCRIPTION_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "STOCKING_POLICY", "STOCKING_POLICY_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "VENDOR_CODE", "VENDOR_CODE_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "VENDOR_NAME", "VENDOR_NAME_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "VENDOR_SHORT_NAME", "VENDOR_SHORT_NAME_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "VENDOR_FULL_NAME", "VENDOR_FULL_NAME_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "VENDOR_PARENT_NAME", "VENDOR_PARENT_NAME_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "VENDOR_PARENT_CODE", "VENDOR_PARENT_CODE_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "VENDOR_MATERIAL", "VENDOR_MATERIAL_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "SOURCE_CATEGORY", "SOURCE_CATEGORY_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "PLANNED_DELIV_TIME", "PLANNED_DELIV_TIME_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "LT_RANGE", "LT_RANGE_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "UNIT_COST", "UNIT_COST_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "AVG_MONTHLY_ORDER_INTAKE", "AVG_MONTHLY_ORDER_INTAKE_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "AMF_ONEMM", "AMF_ONEMM_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "COV", "COV_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "SS2", "SS2_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "SS3", "SS3_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "CALCULATED_ABC", "CALCULATED_ABC_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "CALCULATED_FMR", "CALCULATED_FMR_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "NEW_PRODUCTS", "NEW_PRODUCTS_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "PROCUREMENT_TYPE", "PROCUREMENT_TYPE_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "FOLLOW_UP_MATERIAL", "FOLLOW_UP_MATERIAL_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "PRODN_SUPERVISOR", "PRODN_SUPERVISOR_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "IN_HOUSE_PRODN_LT", "IN_HOUSE_PRODN_LT_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "STARS_CODE", "STARS_CODE_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "LAST_SO_SALES_DATE", "LAST_SO_SALES_DATE_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "NONE_SALES_DURATION", "NONE_SALES_DURATION_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "NONE_SALES_PERIOD", "NONE_SALES_PERIOD_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "GRA", "GRA_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "COMMODITY_CODE", "COMMODITY_CODE_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "STARS_DESCRIPTION", "STARS_DESCRIPTION_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "PLANNING_ALIVE_STATUS", "PLANNING_ALIVE_STATUS_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "XBOARD", "XBOARD_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "RES_COV_STK", "RES_COV_STK_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "RES_COV_STK_RANGE", "RES_COV_STK_RANGE_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "RES_COV_STK_LA", "RES_COV_STK_LA_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "RES_COV_STK_LA_RANGE", "RES_COV_STK_LA_RANGE_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "RES_COV_STK_OPO", "RES_COV_STK_OPO_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "RES_COV_STK_OPO_RANGE", "RES_COV_STK_OPO_RANGE_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "FIRST_CONSUMPTION_RANGE", "FIRST_CONSUMPTION_RANGE_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "FIRST_PO_CREATE_RANGE", "FIRST_PO_CREATE_RANGE_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "FIRST_SO_CREATE_RANGE", "FIRST_SO_CREATE_RANGE_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "LAST_SO_SALES_RANGE", "LAST_SO_SALES_RANGE_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "PRD_BOM_COMPONENT", "PRD_BOM_COMPONENT_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "FIRST_CONS_DATE", "FIRST_CONS_DATE_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "FIRST_SO_DATE", "FIRST_SO_DATE_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "FIRST_PO_DATE", "FIRST_PO_DATE_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "COV_RANGE", "COV_RANGE_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "RISK_LEVEL", "RISK_LEVEL_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "ADU_END_ORDER", "ADU_END_ORDER_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "STDDEV_END_ORDER", "STDDEV_END_ORDER_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "COV_END_ORDER", "COV_END_ORDER_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "COV_RANGE_END_ORDER", "COV_RANGE_END_ORDER_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "NUM_OF_WHERE_USE", "NUM_OF_WHERE_USE_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "NUM_OF_WHERE_USE_RANGE", "NUM_OF_WHERE_USE_RANGE_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "PRODUCT_GROUP_A", "PRODUCT_GROUP_A_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "PRODUCT_GROUP_B", "PRODUCT_GROUP_B_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "PRODUCT_GROUP_C", "PRODUCT_GROUP_C_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "PRODUCT_GROUP_D", "PRODUCT_GROUP_D_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "PRODUCT_GROUP_E", "PRODUCT_GROUP_E_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "TOTAL_STOCK_QTY", "TOTAL_STOCK_QTY_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "UU_STOCK_QTY", "UU_STOCK_QTY_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "QI_STOCK_QTY", "QI_STOCK_QTY_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "BLOCK_STOCK_QTY", "BLOCK_STOCK_QTY_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "PAST_DUE_SO_QTY", "PAST_DUE_SO_QTY_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "PAST_DUE_SO_QTY_NON_BLOCK", "PAST_DUE_SO_QTY_NON_BLOCK_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "TOTAL_OPEN_SO_QTY", "TOTAL_OPEN_SO_QTY_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "WITHIN_LT_SO_QTY", "WITHIN_LT_SO_QTY_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "WITHOUT_LT_SO_QTY", "WITHOUT_LT_SO_QTY_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "TOTAL_OPEN_SO_QTY_NON_BLOCK", "TOTAL_OPEN_SO_QTY_NON_BLOCK_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "WITHIN_LT_SO_NON_BLOCK", "WITHIN_LT_SO_NON_BLOCK_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "WITHOUT_LT_SO_NON_BLOCK", "WITHOUT_LT_SO_NON_BLOCK_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "OPEN_SO_QTY", "OPEN_SO_QTY_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "OPEN_PO_QTY", "OPEN_PO_QTY_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "PASTDUE_PO_QTY", "PASTDUE_PO_QTY_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "OPEN_PO_LA_QTY", "OPEN_PO_LA_QTY_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "OPEN_PO_AB_QTY", "OPEN_PO_AB_QTY_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "ORDER_RESERVATION_QTY", "ORDER_RESERVATION_QTY_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "PASTDUE_ORDER_RESERVATION", "PASTDUE_ORDER_RESERVATION_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "ORDER_RESERVATION_QTY_WITHIN_7_DAYS", "ORDER_RESERVATION_QTY_WITHIN_7_DAYS_ROUTE", fields, fieldArrays);
            setFieldIfExists(processedItem, level, "ORDER_RESERVATION_QTY_WITHOUT_7_DAYS", "ORDER_RESERVATION_QTY_WITHOUT_7_DAYS_ROUTE", fields, fieldArrays);
        }
    }

    /**
     * 辅助方法：如果字段存在于fields参数中，则设置对应的级别字段
     */
    private static void setFieldIfExists(LinkedHashMap<String, Object> processedItem, int level,
                                       String fieldName, String routeKey, String fields,
                                       Map<String, String[]> fieldArrays) {
        if (isFieldSelected(fields, fieldName)) {
            String[] fieldArray = fieldArrays.get(routeKey);
            if (fieldArray != null && level < fieldArray.length) {
                String value = fieldArray[level] != null ? fieldArray[level].trim() : null;
                processedItem.put("L" + level + "_" + fieldName, value);
            }
        }
    }

    /**
     * 检查字段是否被选中（精确匹配，避免子字符串匹配问题）
     * 例如：当选择ORDER_RESERVATION_QTY_WITHOUT_7_DAYS时，不应该匹配ORDER_RESERVATION_QTY
     * fields格式: ["ORDER_RESERVATION_QTY_WITHOUT_7_DAYS", "CLUSTER_NAME"]
     */
    private static boolean isFieldSelected(String fields, String fieldName) {
        if (fields == null || fieldName == null) {
            return false;
        }

        try {
            // 处理JSON数组格式的fields参数
            if (fields.trim().startsWith("[") && fields.trim().endsWith("]")) {
                JSONArray fieldArray = JSONArray.parseArray(fields);
                for (int i = 0; i < fieldArray.size(); i++) {
                    String field = fieldArray.getString(i);
                    if (fieldName.equals(field)) {
                        return true;
                    }
                }
                return false;
            }

            // 兼容其他可能的格式：尝试多种可能的分隔符格式进行精确匹配
            String[] possibleSeparators = {",", ";", "|", " "};

            for (String separator : possibleSeparators) {
                if (fields.contains(separator)) {
                    // 使用分隔符分割并进行精确匹配
                    String[] fieldArray = fields.split(separator);
                    for (String field : fieldArray) {
                        if (fieldName.equals(field.trim())) {
                            return true;
                        }
                    }
                    return false;
                }
            }

            // 如果没有找到分隔符，则进行完全匹配
            return fieldName.equals(fields.trim());

        } catch (Exception e) {
            // 如果JSON解析失败，回退到原来的contains方法
            return fields.contains(fieldName);
        }
    }

    public static boolean isPlantInJSONArray(Map<String, Object> parameterMap, String plantCode) {
        // 从 parameterMap 中获取 plant JSON 数组
        Object plantArrayObject = parameterMap.get("plant");

        // 检查 plantArrayObject 是否为 JSONArray
        if (plantArrayObject instanceof JSONArray) {
            JSONArray plantArray = (JSONArray) plantArrayObject;

            // 遍历 plantArray，检查是否包含 plantCode
            for (int j = 0; j < plantArray.size(); j++) {
                if (plantCode.equals(plantArray.getString(j))) {
                    return true; // 找到匹配的 plantCode
                }
            }
        }

        // 如果没有找到，返回 false
        return false;
    }

    /**
     * 去除重复行
     *
     * @param processedData 处理后的数据列表
     * @return 去重后的数据列表
     */
    private static List<LinkedHashMap<String, Object>> removeDuplicateRows(List<LinkedHashMap<String, Object>> processedData) {
        // 使用 LinkedHashSet 保持插入顺序并去重
        Set<String> seenRows = new LinkedHashSet<>();
        List<LinkedHashMap<String, Object>> uniqueData = new ArrayList<>();

        for (LinkedHashMap<String, Object> row : processedData) {
            // 创建行的唯一标识符，基于所有字段的值
            StringBuilder rowKey = new StringBuilder();

            // 按照固定顺序遍历所有字段，确保一致性
            TreeMap<String, Object> sortedRow = new TreeMap<>(row);
            for (Map.Entry<String, Object> entry : sortedRow.entrySet()) {
                rowKey.append(entry.getKey()).append("=");
                if (entry.getValue() != null) {
                    rowKey.append(entry.getValue().toString());
                }
                rowKey.append("|");
            }

            String rowKeyString = rowKey.toString();
            if (!seenRows.contains(rowKeyString)) {
                seenRows.add(rowKeyString);
                uniqueData.add(row);
            }
        }

        return uniqueData;
    }

    private void generateValueColumn(Map<String, Object> parameterMap) {
        String resultType = (String) parameterMap.get("resultType");
        String valueColumn = "COUNT(1)";
        if ("Line".equalsIgnoreCase(resultType)) {
            valueColumn = "COUNT(1)";
        }
        parameterMap.put("valueColumn", valueColumn);
    }

    /**
     * 生成cascader filter
     *
     * @param parameterMap 参数map
     */
    private void generateCascaderFilter(Map<String, Object> parameterMap) {
        this.generateCascaderFilterSQL(parameterMap);
        // categroy
        JSONArray categroyArray = (JSONArray) parameterMap.get("categroy");
        if (categroyArray == null || categroyArray.isEmpty()) {
            List<String> categroyDefault = new ArrayList<>();
            categroyDefault.add("ENTITY");
            parameterMap.put("categroy", categroyDefault);
        }
    }

}
