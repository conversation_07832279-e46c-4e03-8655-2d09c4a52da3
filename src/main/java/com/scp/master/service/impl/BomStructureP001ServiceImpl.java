package com.scp.master.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.scp.master.dao.IBomStructureP001Dao;
import com.scp.master.service.IBomStructureP001Service;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@Scope("prototype")
@Transactional
public class BomStructureP001ServiceImpl extends ServiceHelper implements IBomStructureP001Service {

    @Resource
    private Response response;

    @Resource
    private IBomStructureP001Dao bomStructureP001Dao;

    @Resource
    private ExcelTemplate excelTemplate;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage() {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cascader", Utils.parseCascader(bomStructureP001Dao.queryCascader()));
        resultMap.put("cascaderOpr", Utils.parseCascader(bomStructureP001Dao.queryOprCascader(), false));
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        page.setTotal(bomStructureP001Dao.queryReport1Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(bomStructureP001Dao.queryReport1(parameterMap));
        }

        return response.setBody(page);
    }

    @Override
    public void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        String fileName = "bom_structure_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.master.dao.IBomStructureP001Dao.queryReport1", parameterMap);
    }

    @Override
    public Response queryReport1Details(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateCascaderFilter(parameterMap);

        page.setTotal(bomStructureP001Dao.queryReport1DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(bomStructureP001Dao.queryReport1Details(parameterMap));
        }

        return response.setBody(page);
    }

    @Override
    public void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        String fileName = "bom_details_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.master.dao.IBomStructureP001Dao.queryReport1Details", parameterMap);
    }

    @Override
    public Response queryReport1RouteDetails(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateCascaderFilter(parameterMap);

        page.setTotal(bomStructureP001Dao.queryReport1RouteDetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(bomStructureP001Dao.queryReport1RouteDetails(parameterMap));
        }

        return response.setBody(page);
    }

    @Override
    public void downloadReport1RouteDetails(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        String fileName = "bom_route_details_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.master.dao.IBomStructureP001Dao.queryReport1RouteDetails", parameterMap);
    }

    private void generateValueColumn(Map<String, Object> parameterMap) {
        JSONArray values = (JSONArray) parameterMap.get("values");
        List<String> columns = new ArrayList<>();
        for (int i = 0; i < values.size(); i++) {
            JSONArray subValues = values.getJSONArray(i);
            String opr = subValues.getString(0);
            String name = subValues.getString(1);
            switch (opr) {
                case "COUNT_DISTINCT": {
                    columns.add("COUNT(DISTINCT " + name + ") AS " + opr + "_" + name);
                    break;
                }
                case "COUNT":
                case "AVG":
                case "SUM":
                case "MIN":
                case "MAX": {
                    columns.add(opr + "(" + name + ") AS " + opr + "_" + name);
                    break;
                }
            }
        }
        parameterMap.put("columns", columns);
    }

    /**
     * 生成cascader filter
     *
     * @param parameterMap 参数map
     */
    private void generateCascaderFilter(Map<String, Object> parameterMap) {
        this.generateCascaderFilterSQL(parameterMap);
        // categroy
        JSONArray categroyArray = (JSONArray) parameterMap.get("categroy");
        if (categroyArray == null || categroyArray.isEmpty()) {
            List<String> categroyDefault = new ArrayList<>();
            categroyDefault.add("ENTITY");
            parameterMap.put("categroy", categroyDefault);
        }
    }
}
