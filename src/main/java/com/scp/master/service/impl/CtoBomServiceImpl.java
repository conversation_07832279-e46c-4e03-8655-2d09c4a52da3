package com.scp.master.service.impl;

/**
 * <AUTHOR> FANG
 * @date 2023/12/25 15:55
 * @comment
 */

import com.scp.master.dao.ICtoBomDao;
import com.scp.master.service.ICtoBomService;
import com.alibaba.fastjson.JSONArray;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.util.*;

@Service
@Scope("prototype")
@Transactional
public class CtoBomServiceImpl implements ICtoBomService {

    @Resource
    private Response response;

    @Resource
    private ICtoBomDao ctoBomDao;

    @Resource
    private ExcelTemplate excelTemplate;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage() {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cascader", Utils.parseCascader(ctoBomDao.queryCascader()));
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1Columns(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);
        return response.setBody(ctoBomDao.queryReport1Columns(parameterMap));
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateCascaderFilter(parameterMap);

        page.setTotal(ctoBomDao.queryReport1Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(ctoBomDao.queryReport1(parameterMap));
        }

        return response.setBody(page);
    }

    @Override
    public void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateCascaderFilter(parameterMap);

        String fileName = "cto_bom_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.master.dao.ICtoBomDao.queryReport1", parameterMap);
    }

    /**
     * 生成cascader filter
     *
     * @param parameterMap 参数map
     */
    private void generateCascaderFilter(Map<String, Object> parameterMap) {
        // 生成筛选条件
        JSONArray filterArray = (JSONArray) parameterMap.get("filterList");
        if (filterArray != null) {
            Map<String, List<String>> filterMap = new HashMap<>();

            for (Object subObj : filterArray) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = subArray.getString(0);

                String value = subArray.getString(1);
                String key = Utils.randomStr(8);

                if (Utils.hasInjectionAttack(columnName)) {
                    continue;
                }

                List<String> fl = filterMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                fl.add("#{" + key + ",jdbcType=VARCHAR}");
                parameterMap.put(key, value);
            }

            List<String> filterList = new ArrayList<>();

            for (String key : filterMap.keySet()) {
                List<String> fl = filterMap.get(key);
                filterList.add(key + " in (" + StringUtils.join(fl, ",") + ")");
            }

            parameterMap.put("filters", StringUtils.join(filterList, " and "));
        }

        // load datarange
        String dateRangeType = (String) parameterMap.get("dateRangeType");
        if (Utils.hasInjectionAttack(dateRangeType) == false) {
            parameterMap.put("dateColumn", dateRangeType);
        }
    }
}
