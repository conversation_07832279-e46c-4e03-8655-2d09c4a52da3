package com.scp.master.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.starter.context.bean.CacheRemove;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import com.scp.master.bean.TriggerApplicationReport3;
import com.scp.master.dao.ITriggerApplicationReportDao;
import com.scp.master.service.ITriggerApplicationReportService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Scope("prototype")
@Transactional
public class TriggerApplicationReportServiceImpl implements ITriggerApplicationReportService {

    @Resource
    private Response response;

    @Resource
    private ITriggerApplicationReportDao triggerApplicationReportDao;

    @Resource
    private ExcelTemplate excelTemplate;

    private static final String BLANK = "<Blank>";

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage() {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cascader", Utils.parseCascader(triggerApplicationReportDao.queryCascader()));
        List<String> rcaList = JSON.parseArray(triggerApplicationReportDao.queryReport1Columns()).toJavaList(String.class);
        rcaList.sort(String::compareTo);
        rcaList.add(BLANK);
        resultMap.put("RCA", rcaList);
        return response.setBody(resultMap);
    }

    private static volatile boolean isSync = false;

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response refreshTriggerReport() {
        if (isSync == false) {
            isSync = true;
            triggerApplicationReportDao.refreshTriggerReport();
            isSync = false;
        }
        return response;
    }

    @Override
    @SuppressWarnings("unchecked")
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1Columns(Map<String, Object> parameterMap) {
        String categroy3 = (String) parameterMap.get("categroy3");
        List<String> columns = new ArrayList<>();
        if (StringUtils.equalsIgnoreCase(categroy3, "RCA_CODE")) {
            List<String> selectedRCA = (List<String>) parameterMap.get("RCA");
            if (selectedRCA == null || selectedRCA.isEmpty()) {
                List<String> rcaList = JSON.parseArray(triggerApplicationReportDao.queryReport1Columns()).toJavaList(String.class);
                rcaList.add(BLANK);
                columns.addAll(rcaList);
            } else {
                columns.addAll(selectedRCA);
            }
        } else if (StringUtils.equalsIgnoreCase(categroy3, "LT_RANGE")) {
            columns.add("<=7");
            columns.add("<=14");
            columns.add("<=30");
            columns.add("<=45");
            columns.add(">45");
        }
        return response.setBody(columns);
    }

    @Override
    @SuppressWarnings("unchecked")
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);
        List<String> categories = (List<String>) parameterMap.get("categories");

        String selectedDate = (String) parameterMap.get("selectedDate");
        if (StringUtils.isNotBlank(selectedDate)) {
            parameterMap.put("date", selectedDate);
        } else {
            parameterMap.put("date", new SimpleDateFormat("yyyy/MM/dd").format(new Date()));
        }
        String categroy3 = (String) parameterMap.get("categroy3");
        List<Map<String, Object>> resultList = new ArrayList<>();

        final String SPLIT_REG = "<#>";

        if (StringUtils.equalsIgnoreCase(categroy3, "RCA_CODE")) {
            List<Map<String, Object>> dataList = triggerApplicationReportDao.queryReport1RCA(parameterMap);
            LinkedHashMap<String, Map<String, Object>> dataMap = new LinkedHashMap<>();

            for (Map<String, Object> map : dataList) {
                String key = StringUtils.join(categories.stream().map(e -> String.valueOf(map.get(e))).collect(Collectors.toList()), SPLIT_REG);
                Map<String, Object> temp = dataMap.computeIfAbsent(key, k -> new HashMap<>());
                temp.put((String) map.get("CATEGROY3"), map.get("CNT"));
            }

            for (String key : dataMap.keySet()) {
                Map<String, Object> temp = dataMap.get(key);
                String[] strs = key.split(SPLIT_REG);
                for (int i = 0; i < categories.size(); i++) {
                    temp.put(categories.get(i), strs[i]);
                }
                resultList.add(temp);
            }
        } else {
            resultList = triggerApplicationReportDao.queryReport1LTRange(parameterMap);
        }

        for (Map<String, Object> map : resultList) {
            BigDecimal total = BigDecimal.ZERO;
            for (String key : map.keySet()) {
                if (StringUtils.startsWith(key, "CATEGROY") == false && categories.contains(key) == false) {
                    total = total.add(Utils.parseBigDecimal(map.get(key), BigDecimal.ZERO));
                }
            }
            map.put("TOTAL", total);
        }

        return response.setBody(resultList);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1Details(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateCascaderFilter(parameterMap);
        page.setTotal(triggerApplicationReportDao.queryReport1DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(triggerApplicationReportDao.queryReport1Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        this.generateCascaderFilter(parameterMap);

        String fileName = "trigger_status_details_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.master.dao.ITriggerApplicationReportDao.queryReport1Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2(Map<String, Object> parameterMap) throws ParseException {
        this.generateCascaderFilter(parameterMap);

        Map<String, List<Object>> resultMap = new HashMap<>();
        List<Object> xAxis = new ArrayList<>();
        List<Object> yAxis = new ArrayList<>();
        List<Object> yAxis2 = new ArrayList<>();
        List<Object> yAxis3 = new ArrayList<>();
        resultMap.put("xAxis", xAxis);
        resultMap.put("yAxis", yAxis);
        resultMap.put("yAxis2", yAxis2);
        resultMap.put("yAxis3", yAxis3);
        String strDate = (String) parameterMap.get("report1StartDate");
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");
        Date report1StartDate = dateFormat.parse(strDate);
        Date currentTime = new Date();
        long dayLength = (report1StartDate.getTime() - currentTime.getTime()) / 1000 / 60 / 60 / 24;

        Calendar lastYear = Calendar.getInstance();
        lastYear.add(Calendar.DAY_OF_YEAR, (int) dayLength);
        lastYear.set(Calendar.HOUR_OF_DAY, 0);
        lastYear.set(Calendar.MINUTE, 0);
        lastYear.set(Calendar.SECOND, 0);
        lastYear.set(Calendar.MILLISECOND, 0);
        long now = System.currentTimeMillis();
        long projection = System.currentTimeMillis() + 2L * 30 * 86400000;
        SimpleDateFormat format = new SimpleDateFormat("yyyy/MM/dd");

        parameterMap.put("type", "NORMAL"); // yAxis -> Active
        List<Map<String, Object>> dataList = triggerApplicationReportDao.queryReport2(parameterMap);
        parameterMap.put("type", "ACCUM"); // yAxis3 -> Avctived
        List<Map<String, Object>> dataList2 = triggerApplicationReportDao.queryReport2(parameterMap);

        int lastestActualCount = 0;
        boolean appendLastest = false;
        for (int i = 0; projection > lastYear.getTimeInMillis(); i++) {
            xAxis.add(format.format(lastYear.getTime()));
            if (lastYear.getTimeInMillis() > now) {
                // 将actual的最后一天放到Projection的第一天
                if (appendLastest == false) {
                    yAxis2.remove(yAxis2.size() - 1);
                    yAxis2.add(lastestActualCount);
                    appendLastest = true;
                }
                yAxis.add(null);
                yAxis2.add(this.getReport2RangeCount(lastYear.getTime(), dataList, true));
            } else {
                lastestActualCount = this.getReport2RangeCount(lastYear.getTime(), dataList, false);
                yAxis.add(lastestActualCount);
                yAxis2.add(null);
                yAxis3.add(this.getReport2RangeCount(lastYear.getTime(), dataList2, false));
            }

            lastYear.add(Calendar.DAY_OF_YEAR, 1);
        }
        return response.setBody(resultMap);
    }

    private int getReport2RangeCount(Date time, List<Map<String, Object>> dataList, boolean isProjection) {
        int total = 0;
        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("GMT+8")); // 获取当前时区的日历
        calendar.set(Calendar.HOUR_OF_DAY, 0); // 设置小时为0
        calendar.set(Calendar.MINUTE, 0); // 设置分钟为0
        calendar.set(Calendar.SECOND, 0); // 设置秒为0
        calendar.set(Calendar.MILLISECOND, 0); // 设置毫秒为0
        long today = calendar.getTimeInMillis(); // 获取今天凌晨0点的时间戳

        for (Map<String, Object> map : dataList) {
            Date from = (Date) map.get("VALIDATE_FROM");
            Date to = (Date) map.get("VALIDATE_TO");
            Date btn = (Date) map.get("BTN_DATE");

            // Projection的数据, 只看当前生效的
            // SAP不允许申请未来的Trigger, 所以不用担心存在于未来的Trigger会对数据造成影响
            if (isProjection) {
                if (today < from.getTime() || today > to.getTime()) {
                    continue;
                }
            }

            // 如果输入的时间在今天之前, 那么使用from和to, 否则使用BTN
            Date end;
            if (today >= time.getTime()) {
                end = to;
            } else {
                end = btn;
            }

            if (time.getTime() >= from.getTime() && time.getTime() <= end.getTime()) {
                total += Utils.parseBigDecimal(map.get("CNT")).intValue();
            }
        }
        return total;
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);

        String type = (String) parameterMap.get("type");
        switch (type) {
            case "Order Value" -> parameterMap.put("valueColumn", "ORDER_VALUE");
            case "Qty" -> parameterMap.put("valueColumn", "ORDER_QUANTITY");
            case "Line" -> parameterMap.put("valueColumn", "ORDER_LINES");
        }

        List<Map<String, Object>> dataList = triggerApplicationReportDao.queryReport3(parameterMap);
        List<TriggerApplicationReport3> resultList = new ArrayList<>();
        for (Map<String, Object> data : dataList) {
            String category = (String) data.get("CATEGORY");
            switch (category) {
                case "No impact" -> resultList.add(new TriggerApplicationReport3("No impact", data.get("VALUE")));
                case "<7D" -> resultList.add(new TriggerApplicationReport3("<=7", data.get("VALUE")));
                case "7D-14D" -> resultList.add(new TriggerApplicationReport3("<=14", data.get("VALUE")));
                case "14D-30D" -> resultList.add(new TriggerApplicationReport3("<=30", data.get("VALUE")));
                case "30D-45D" -> resultList.add(new TriggerApplicationReport3("<=45", data.get("VALUE")));
                case ">45D" -> resultList.add(new TriggerApplicationReport3(">45", data.get("VALUE")));
            }
        }

        return response.setBody(resultList);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Details(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateCascaderFilter(parameterMap);
        String selectedReport3Name = (String) parameterMap.get("selectedReport3Name");
        switch (selectedReport3Name) {
            case "No impact" -> {
                parameterMap.put("indicator", "Others");
                parameterMap.put("qmax_group", null);
            }
            case ">45" -> {
                parameterMap.put("indicator", "T");
                parameterMap.put("qmax_group", ">45D");
            }
            case "<=45" -> {
                parameterMap.put("indicator", "T");
                parameterMap.put("qmax_group", "30D-45D");
            }
            case "<=30" -> {
                parameterMap.put("indicator", "T");
                parameterMap.put("qmax_group", "14D-30D");
            }
            case "<=14" -> {
                parameterMap.put("indicator", "T");
                parameterMap.put("qmax_group", "7D-14D");
            }
            case "<=7" -> {
                parameterMap.put("indicator", "T");
                parameterMap.put("qmax_group", "<7D");
            }
        }

        page.setTotal(triggerApplicationReportDao.queryReport3DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(triggerApplicationReportDao.queryReport3Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        this.generateCascaderFilter(parameterMap);
        String selectedReport3Name = (String) parameterMap.get("selectedReport3Name");
        switch (selectedReport3Name) {
            case "No impact" -> {
                parameterMap.put("indicator", "Others");
                parameterMap.put("qmax_group", null);
            }
            case ">45" -> {
                parameterMap.put("indicator", "T");
                parameterMap.put("qmax_group", ">45D");
            }
            case "<=45" -> {
                parameterMap.put("indicator", "T");
                parameterMap.put("qmax_group", "30D-45D");
            }
            case "<=30" -> {
                parameterMap.put("indicator", "T");
                parameterMap.put("qmax_group", "14D-30D");
            }
            case "<=14" -> {
                parameterMap.put("indicator", "T");
                parameterMap.put("qmax_group", "7D-14D");
            }
            case "<=7" -> {
                parameterMap.put("indicator", "T");
                parameterMap.put("qmax_group", "<7D");
            }
        }

        String fileName = "trigger_status_details_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.master.dao.ITriggerApplicationReportDao.queryReport3Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport4(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateCascaderFilter(parameterMap);
        page.setTotal(triggerApplicationReportDao.queryReport4Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(triggerApplicationReportDao.queryReport4(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport4(Map<String, Object> parameterMap, HttpServletResponse response) {
        this.generateCascaderFilter(parameterMap);
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "trigger_report_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.master.dao.ITriggerApplicationReportDao.queryReport4", parameterMap);
    }

    /**
     * 生成cascader filter
     *
     * @param parameterMap 参数map
     */
    @SuppressWarnings("unchecked")
    private void generateCascaderFilter(Map<String, Object> parameterMap) {
        List<String> categories = (List<String>) parameterMap.get("categories");
        if (categories == null || categories.isEmpty() == true) {
            categories = new ArrayList<>();
            categories.add("PRODUCT_LINE");
            categories.add("ENTITY");
        }
        parameterMap.put("categories", categories);

        // 生成筛选条件
        JSONArray categoryArray = (JSONArray) parameterMap.get("filterList");
        if (categoryArray != null) {
            Map<String, List<String>> filterMap = new HashMap<>();

            for (Object subObj : categoryArray) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = subArray.getString(0);

                String value = subArray.getString(1);
                String key = Utils.randomStr(8);

                if (Utils.hasInjectionAttack(columnName)) {
                    continue;
                }

                List<String> fl = filterMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                fl.add("#{" + key + ",jdbcType=VARCHAR}");
                parameterMap.put(key, value);
            }

            List<String> filterList = new ArrayList<>();

            for (String key : filterMap.keySet()) {
                List<String> fl = filterMap.get(key);
                filterList.add("t." + key + " in (" + StringUtils.join(fl, ",") + ")");
            }

            parameterMap.put("filters", StringUtils.join(filterList, " and "));
        }

        JSONArray rcaArray = (JSONArray) parameterMap.get("RCA");
        if (rcaArray.remove(BLANK)) {
            parameterMap.put("hasBlackRCA", true);
        }

        // load special parameter
        String specialContent = (String) parameterMap.get("specialContent");
        String specialColumn = (String) parameterMap.get("specialType");
        if (Utils.hasInjectionAttack(specialColumn) == false) {
            if (StringUtils.isNotBlank(specialContent)) {
                List<List<String>> value = Utils.splitValue(specialContent);
                if (value.isEmpty() == false) {
                    parameterMap.put("specialList", value);
                    parameterMap.put("specialColumn", specialColumn);
                }
            }
        }
    }
}
