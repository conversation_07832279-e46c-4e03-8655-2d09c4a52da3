package com.scp.master.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import com.scp.master.dao.IMasterDataSummaryDao;
import com.scp.master.service.IMasterDataSummaryService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;

import java.util.*;

@Service
@Scope("prototype")
@Transactional
public class MasterDataSummaryServiceImpl extends ServiceHelper implements IMasterDataSummaryService {

    @Resource
    private Response response;

    @Resource
    private IMasterDataSummaryDao masterDataSummaryDao;

    @Resource
    private ExcelTemplate excelTemplate;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage() {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cascader", Utils.parseCascader(masterDataSummaryDao.queryCascader()));
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        page.setTotal(masterDataSummaryDao.queryReport1Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(masterDataSummaryDao.queryReport1(parameterMap));
        }

        return response.setBody(page);
    }

    @Override
    public void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        String fileName = "master_data_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.master.dao.IMasterDataSummaryDao.queryReport1", parameterMap);
    }

    @Override
    public Response queryReport1Details(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateCascaderFilter(parameterMap);

        page.setTotal(masterDataSummaryDao.queryReport1DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(masterDataSummaryDao.queryReport1Details(parameterMap));
        }

        return response.setBody(page);
    }

    @Override
    public void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        String fileName = "details_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.master.dao.IMasterDataSummaryDao.queryReport1Details", parameterMap);
    }

    private void generateValueColumn(Map<String, Object> parameterMap) {
        String type = (String) parameterMap.get("type");
        String calcType = (String) parameterMap.get("calcType");
        switch (type) {
            case "Selling Value" -> parameterMap.put("costColumn", " * AVG_SELLING_PRICE_RMB");
            case "Cost Value" -> parameterMap.put("costColumn", " * UNIT_COST");
            case "Qty" -> parameterMap.put("costColumn", " ");
        }

        switch (calcType) {
            case "Sum" -> parameterMap.put("calcType", "SUM");
            case "Min" -> parameterMap.put("calcType", "MIN");
            case "Max" -> parameterMap.put("calcType", "MAX");
            case "Avg" -> parameterMap.put("calcType", "AVG");
            case "Std" -> parameterMap.put("calcType", "STDDEV");
        }

    }

    /**
     * 生成cascader filter
     *
     * @param parameterMap 参数map
     */
    private void generateCascaderFilter(Map<String, Object> parameterMap) {
        this.generateCascaderFilterSQL(parameterMap);
        // categroy
        JSONArray categroyArray = (JSONArray) parameterMap.get("categroy");
        if (categroyArray == null || categroyArray.isEmpty()) {
            List<String> categroyDefault = new ArrayList<>();
            categroyDefault.add("ENTITY");
            parameterMap.put("categroy", categroyDefault);
        }
    }
}
