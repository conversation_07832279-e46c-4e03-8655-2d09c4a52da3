package com.scp.master.service;

import com.starter.context.bean.Response;
import jakarta.servlet.http.HttpServletResponse;

import java.util.Map;

public interface ISalesMasterDataSummaryService {

    Response initPage();

    Response queryReport1(Map<String, Object> parameterMap);

    void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport1Details(Map<String, Object> parameterMap);

    void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse response);
}
