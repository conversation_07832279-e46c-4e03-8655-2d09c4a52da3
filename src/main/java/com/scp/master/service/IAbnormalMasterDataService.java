package com.scp.master.service;

import com.starter.context.bean.Response;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;

public interface IAbnormalMasterDataService {

    Response checkMasterData();

    Response initPage(Map<String, Object> parameterMap);

    Response queryDashboard(Map<String, Object> parameterMap);

    Response queryResult(Map<String, Object> parameterMap);

    Response queryRules(Map<String, Object> parameterMap);

    Response queryRuleByID(Map<String, Object> parameterMap);

    void downloadResult(Map<String, Object> parameterMap, HttpServletResponse response);

    Response saveRule(Map<String, Object> parameterMap);

    Response queryMasterDataDetails(Map<String, Object> parameterMap);

    void downloadMasterDataDetails(Map<String, Object> parameterMap, HttpServletResponse response);
}
