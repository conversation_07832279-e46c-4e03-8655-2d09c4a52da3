package com.scp.master;

import com.scp.master.service.IAbnormalMasterDataService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/master/abnormal_master_data", parent = "menu210")
public class AbnormalMasterDataController extends ControllerHelper {
    @Resource
    private IAbnormalMasterDataService abnormalMasterDataService;

    @SchneiderRequestMapping("/start_checking")
    public Response checkMasterData() {
        super.setGlobalCache(true);
        return abnormalMasterDataService.checkMasterData();
    }

    @SchneiderRequestMapping("/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return abnormalMasterDataService.initPage(parameterMap);
    }

    @SchneiderRequestMapping("/query_result")
    public Response queryResult(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return abnormalMasterDataService.queryResult(parameterMap);
    }

    @SchneiderRequestMapping("/query_rules")
    public Response queryRules(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return abnormalMasterDataService.queryRules(parameterMap);
    }

    @SchneiderRequestMapping("/query_rule_by_id")
    public Response queryRuleByID(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return abnormalMasterDataService.queryRuleByID(parameterMap);
    }

    @SchneiderRequestMapping("/download_result")
    public void downloadResult(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        abnormalMasterDataService.downloadResult(parameterMap, response);
    }

    @SchneiderRequestMapping("/save_rule")
    public Response saveRule(HttpServletRequest request) {
        super.pageLoad(request);
        return abnormalMasterDataService.saveRule(parameterMap);
    }

    @SchneiderRequestMapping("/query_dashboard")
    public Response queryDashboard(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return abnormalMasterDataService.queryDashboard(parameterMap);
    }

    @SchneiderRequestMapping("/query_master_data_details")
    public Response queryMasterDataDetails(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return abnormalMasterDataService.queryMasterDataDetails(parameterMap);
    }

    @SchneiderRequestMapping("/download_master_data_details")
    public void downloadMasterDataDetails(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        abnormalMasterDataService.downloadMasterDataDetails(parameterMap, response);
    }

}
