package com.scp.master;


import com.scp.master.service.ICriticalBomService;
import com.scp.master.service.impl.CriticalBomServiceImpl;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.util.WebUtils;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "master/bom", parent = CriticalBomServiceImpl.PARENT_CODE)
public class CriticalBomController extends ControllerHelper {

    @Resource
    private ICriticalBomService criticalBomService;

    @Resource
    private Response response;

    @SchneiderRequestMapping("/admin_check")
    public Response adminCheck(HttpServletRequest request) {
        super.pageLoad(request);
        return criticalBomService.adminCheck(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping("/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return criticalBomService.initPage(parameterMap, session.getUserid());
    }


    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return criticalBomService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping("/download_report1")
    public void downloadReport1(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        criticalBomService.downloadReport1(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        super.pageLoad(request);
        return criticalBomService.queryReport2(parameterMap);
    }

    @SchneiderRequestMapping("/download_report2")
    public void downloadReport2(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        criticalBomService.downloadReport2(parameterMap, response);
    }

    @SchneiderRequestMapping("/save_report2")
    public Response saveReport2(HttpServletRequest request) {
        super.pageLoad(request);
        return criticalBomService.saveReport2(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping("/upload_report2")
    public Response uploadReport2(HttpServletRequest request) {
        super.pageLoad(request);
        MultipartHttpServletRequest multipartRequest = WebUtils.getNativeRequest(request, MultipartHttpServletRequest.class);
        if (multipartRequest != null) {
            MultipartFile file = multipartRequest.getFile("file");
            try {
                return criticalBomService.uploadReport2(session.getUserid(), file);
            } catch (Exception e) {
                return response.setError(e);
            }
        } else {
            return response.setError(new Exception("upload failed, multipartRequest is null !"));
        }
    }

    @SchneiderRequestMapping("/download_report2_template")
    public void downloadReport2Template(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        criticalBomService.downloadReport2Template(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_sankey_chart")
    public Response querySankeyChart(HttpServletRequest request) {
        super.pageLoad(request);
        return criticalBomService.querySankeyChart(parameterMap);
    }

}
