package com.scp.master;

/**
 * <AUTHOR>
 * @date 2023/12/25 15:21
 * @comment
 */

import com.scp.master.service.ICtoBomService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/master/cto_bom", parent = "menu220")
public class CtoBomController extends ControllerHelper {

    @Resource
    private ICtoBomService ctoBomService;

    @SchneiderRequestMapping("/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return ctoBomService.initPage();
    }

    @SchneiderRequestMapping("/query_report1_columns")
    public Response queryReport1Columns(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return ctoBomService.queryReport1Columns(parameterMap);
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return ctoBomService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping("/download_report1")
    public void downloadReport1(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        ctoBomService.downloadReport1(parameterMap, response);
    }
}
