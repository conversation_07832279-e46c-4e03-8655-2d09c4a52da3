package com.scp.master;

import com.scp.master.service.IBomStructureP001Service;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/master/bom_structure_p001", parent = "menu250")
public class BomStructureP001Controller extends ControllerHelper {

    @Resource
    private IBomStructureP001Service bomStructureP001Service;

    @SchneiderRequestMapping("/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return bomStructureP001Service.initPage();
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return bomStructureP001Service.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping("/download_report1")
    public void downloadReport1(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bomStructureP001Service.downloadReport1(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report1_details")
    public Response queryReport1Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return bomStructureP001Service.queryReport1Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report1_details")
    public void downloadReport1Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bomStructureP001Service.downloadReport1Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report1_route_details")
    public Response queryReport1RouteDetails(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return bomStructureP001Service.queryReport1RouteDetails(parameterMap);
    }

    @SchneiderRequestMapping("/download_report1_route_details")
    public void downloadReport1RouteDetails(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bomStructureP001Service.downloadReport1RouteDetails(parameterMap, response);
    }
}
