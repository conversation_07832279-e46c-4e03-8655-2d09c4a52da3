package com.scp.po.service.impl;

import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.scp.po.dao.IShippingModeTrackingDao;
import com.scp.po.service.IShippingModeTrackingService;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.util.LinkedHashMap;
import java.util.Map;

@Service("shippingModeTrackingService")
@Scope("prototype")
@Transactional
public class ShippingModeTrackingServiceImpl implements IShippingModeTrackingService {

    @Resource
    private IShippingModeTrackingDao shippingModeTrackingDao;

    @Resource
    private Response response;

    @Resource
    private ExcelTemplate excelTemplate;

    // region report1
    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(String userid, Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        parameterMap.put("userid", userid);
        int total = shippingModeTrackingDao.queryReport1Count(parameterMap);
        page.setTotal(total);
        if (total > 0) {
            page.setData(shippingModeTrackingDao.queryReport1(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        String fileName = "Shipping_Mode_Tracking_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.po.dao.IShippingModeTrackingDao.queryReport1", parameterMap);
    }

    // endregion
}
