package com.scp.po.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.scp.po.bean.POReport1Bean;
import com.scp.po.bean.POReport2Bean;
import com.scp.po.bean.POTreemap;
import com.scp.po.dao.IPOStructureDao;
import com.scp.po.service.IPOStructureService;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.adm.system.bean.CascaderBean;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.starter.context.servlet.ServiceHelper;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service("POStructureService")
@Scope("prototype")
@Transactional
public class POStructureServiceImpl extends ServiceHelper implements IPOStructureService {

    @Resource
    private IPOStructureDao POStructureDao;

    @Resource
    private Response response;

    @Resource
    private ExcelTemplate excelTemplate;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryFilters(Map<String, Object> parameterMap) {
        List<CascaderBean> resultList = new ArrayList<>();
        List<Map<String, String>> dataList = POStructureDao.queryCascader(parameterMap);

        Map<String, List<Map<String, String>>> tempMap = new LinkedHashMap<>();

        for (Map<String, String> map : dataList) {
            List<Map<String, String>> list = tempMap.computeIfAbsent(
                    map.get("CATEGORY"), key -> new ArrayList<>()
            );
            list.add(map);
        }

        for (String key : tempMap.keySet()) {
            CascaderBean bean = new CascaderBean();
            resultList.add(bean);
            bean.setLabel(key);
            bean.setValue(key);
            for (Map<String, String> map : tempMap.get(key)) {
                CascaderBean subBean = new CascaderBean();
                subBean.setLabel(map.get("NAME"));
                subBean.setValue(subBean.getLabel());
                bean.addChild(subBean);
            }
        }

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cascader", resultList);

        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) throws Exception {
        this.generateFilter(parameterMap);
        this.generateReport1Tooltips(parameterMap);
        this.generateValueColumn(parameterMap);

        // 将前台传过来的label转换成列名, 同时也可以防止恶意代码注入
        parameterMap.put("level1", this.getColumnName(parameterMap.get("level1")));
        parameterMap.put("level2", this.getColumnName(parameterMap.get("level2")));
        parameterMap.put("level3", this.getColumnName(parameterMap.get("level3")));
        parameterMap.put("level4", this.getColumnName(parameterMap.get("level4")));
        parameterMap.put("level5", this.getColumnName(parameterMap.get("level5")));

        List<POTreemap> resultList = new ArrayList<>();
        List<POReport1Bean> dataList = POStructureDao.queryReport1(parameterMap);
        for (POReport1Bean data : dataList) {
            this.convertReport1Data(resultList, data);
        }
        return response.setBody(resultList);
    }

    /**
     * 将列表转化为Tree数据
     *
     * @param list 输出树
     * @param data 输入值
     * @throws Exception 异常
     */
    private void convertReport1Data(List<POTreemap> list, POReport1Bean data) throws Exception {
        String[] categorysOrg = new String[]{data.getCategory1(), data.getCategory2(), data.getCategory3(), data.getCategory4(), data.getCategory5()};
        List<String> categories = new ArrayList<>();

        for (String category : categorysOrg) {
            if (StringUtils.isNotBlank(category)) {
                categories.add(category);
            } else {
                break;
            }
        }

        // 这边逻辑比较复杂, 所以用最笨的方法来描述了, 以免后期不好维护
        // 先把这一行数据转成treemap的数据
        // 第一个节点
        List<POTreemap> child = new ArrayList<>();
        POTreemap root = new POTreemap();
        root.setName(categories.get(0));
        root.setTips(data.copyTooltips()); // 因为这个tooltips要放在树中全局使用, 所以必须要生成一个新节点
        root.setChildren(child);

        // 中间节点
        for (int i = 1; i < categories.size() - 1; i++) {
            POTreemap treemap = new POTreemap();
            treemap.setName(categories.get(i));
            treemap.setTips(data.copyTooltips());

            child.add(treemap);
            child = new ArrayList<>();
            treemap.setChildren(child);
        }

        // 最后一个节点
        POTreemap lastNode = new POTreemap();
        lastNode.setName(categories.get(categories.size() - 1));
        lastNode.setValue(data.getValue());
        lastNode.setTips(data.copyTooltips());
        child.add(lastNode);

        // 将这行treemap与原始数据相加
        // 先找到list中是否有这个数据节点
        Optional<POTreemap> beanOpt = list.stream().filter(b -> b.getName().equals(categories.get(0))).findFirst();
        if (beanOpt.isPresent()) {
            POTreemap bean = beanOpt.get();
            bean.add(root); // 两个节点合并
        } else { //找不到的时候最省事, 直接放入list就可以了
            list.add(root);
        }
    }

    //region private functions
    @SuppressWarnings("unchecked")
    private void generateFilter(Map<String, Object> parameterMap) {
        List<String> report3SelectedColumns = (List<String>) parameterMap.get("report3SelectedColumns");
        List<String> listWithoutDuplicates = (List<String>) parameterMap.get("report3ColumnNames");
        if (report3SelectedColumns == null || report3SelectedColumns.isEmpty()) {
            report3SelectedColumns = new ArrayList<>();
            report3SelectedColumns.add("BU");
        }
        parameterMap.put("report3SelectedColumns", report3SelectedColumns);
        listWithoutDuplicates = listWithoutDuplicates.stream().distinct().collect(Collectors.toList());
        parameterMap.put("listWithoutDuplicates", listWithoutDuplicates);
        this.generateCascaderFilterSQL(parameterMap);
    }

    private void generateReport1Tooltips(Map<String, Object> parameterMap) {
        List<String> tooltips = ((JSONArray) parameterMap.get("report1Tooltips")).toJavaList(String.class);
        if (!tooltips.isEmpty()) {
            List<String> tooltipsColumns = tooltips.stream().map(this::getColumnName).collect(Collectors.toList());
            List<String> tooltipsColumnsName = new ArrayList<>();

            if (tooltipsColumns.contains("ORDER_QUANTITY") || tooltipsColumns.contains("ORDER_VALUE_RMB") || tooltipsColumns.contains("GR_VALUE")) {
                parameterMap.put("tooltipsGroupBy", "true");
            }
            for (String c : tooltipsColumns) {
                String tooltip = this.getColumnName(c);
                if ("LINE".equalsIgnoreCase(c)) {
                    tooltipsColumnsName.add("COUNT(1) AS " + tooltip);
                } else {
                    tooltipsColumnsName.add("NVL(AVG(" + c + "),0) AS " + tooltip);
                }
            }
            parameterMap.put("tooltipsColumns", StringUtils.join(tooltipsColumnsName, ", "));
        }
    }

    private void generateTreePathFilter(Map<String, Object> parameterMap) {
        String selectedTreePath = (String) parameterMap.get("selectedTreePath");
        if (StringUtils.isNotBlank(selectedTreePath)) {
            List<String> conditions = new ArrayList<>();
            String[] treePaths = selectedTreePath.split(" > ");
            for (int i = 1; i <= Math.min(treePaths.length, 5); i++) {
                String key = Utils.randomStr(8);
                if ("Others".equals(StringUtils.trim(treePaths[i - 1]))) {
                    String name = this.getColumnName(parameterMap.get("level" + i));
                    conditions.add("(" + name + " = #{" + key + ",jdbcType=VARCHAR} or " + name + " is null )");
                } else {
                    conditions.add(
                            this.getColumnName(parameterMap.get("level" + i))
                                    + " = #{" + key + ",jdbcType=VARCHAR}"
                    );
                }
                parameterMap.put(key, StringUtils.trim(treePaths[i - 1]));
            }
            parameterMap.put("treePathFilter", "(" + StringUtils.join(conditions, " and ") + ")");
        }
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);

        List<POReport2Bean> dataList;

        dataList = POStructureDao.queryReport2(parameterMap);

        Map<String, BigDecimal> dataMap = new HashMap<>();
        LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();
        Map<String, String> xAxisMap = new HashMap<>();

        List<String> legend = new ArrayList<>();

        for (POReport2Bean data : dataList) {
            dataMap.put(data.getKey(), data.getVALUE());
            xAxisMap.put(data.getCALENDAR_DATE(), "");
            if (!legend.contains(data.getNAME())) {
                legend.add(data.getNAME());
            }
        }
        List<String> xAxisList = xAxisMap.keySet().stream().sorted(String::compareTo).collect(Collectors.toList());
        for (String l : legend) {
            List<BigDecimal> temp = new ArrayList<>();

            for (String x : xAxisList) {
                temp.add(dataMap.getOrDefault(l + "#" + x, BigDecimal.ZERO));
            }
            resultMap.put(l, temp);
        }

        resultMap.put("xAxis", xAxisList);
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(POStructureDao.queryReport2DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(POStructureDao.queryReport2Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport2Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);

        String pageType = (String) parameterMap.get("pageType");
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        if ("PO_CREATION".equalsIgnoreCase(pageType)) {
            String fileName = "po_structure_" + Utils.randomStr(4) + ".xlsx";
            excelTemplate.create(res, fileName, "com.scp.po.dao.IPOStructureDao.queryReport2Details", parameterMap);
        } else {
            String fileName = "po_gr_structure_" + Utils.randomStr(4) + ".xlsx";
            excelTemplate.create(res, fileName, "com.scp.po.dao.IPOStructureDao.queryReport2Details", parameterMap);
        }
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        List<LinkedHashMap<String, Object>> dataList = POStructureDao.queryReport3(parameterMap);

        page.setData(dataList);
        page.setTotal(dataList.size());
        return response.setBody(page);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(POStructureDao.queryReport3DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(POStructureDao.queryReport3Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);
        String pageType = (String) parameterMap.get("pageType");
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        parameterMap.put("column1", this.getColumnName(parameterMap.get("column1")));
        parameterMap.put("column2", this.getColumnName(parameterMap.get("column2")));
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        if ("PO_CREATION".equalsIgnoreCase(pageType)) {
            String fileName = "po_structure_" + Utils.randomStr(4) + ".xlsx";
            excelTemplate.create(res, fileName, "com.scp.po.dao.IPOStructureDao.queryReport3Details", parameterMap);
        } else {
            String fileName = "po_gr_structure_" + Utils.randomStr(4) + ".xlsx";
            excelTemplate.create(res, fileName, "com.scp.po.dao.IPOStructureDao.queryReport3Details", parameterMap);
        }
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Columns(Map<String, Object> parameterMap) {
        return response.setBody(POStructureDao.queryReport3Columns(parameterMap));
    }

    private void generateValueColumn(Map<String, Object> parameterMap) {
        String pageType = (String) parameterMap.get("pageType");
        String resultType = (String) parameterMap.get("resultType");
        if ("PO_CREATION".equalsIgnoreCase(pageType)) {
            String valueColumn = "NVL(SUM(ORDER_VALUE_RMB), 0)";
            if ("Value".equalsIgnoreCase(resultType)) {
                valueColumn = "NVL(SUM(ORDER_VALUE_RMB), 0)";
            } else if ("Line".equalsIgnoreCase(resultType)) {
                valueColumn = "COUNT(1)";
            } else if ("Quantity".equalsIgnoreCase(resultType)) {
                valueColumn = "NVL(SUM(ORDER_QUANTITY), 0)";
            } else if ("Weight".equalsIgnoreCase(resultType)) {
                valueColumn = "NVL(SUM(GROSS_WEIGHT_IN_KG), 0)";
            }
            parameterMap.put("valueColumn", valueColumn);
        } else if ("PO_GR".equalsIgnoreCase(pageType)) {
            String valueColumn = "NVL(SUM(GR_VALUE), 0)";
            if ("Value".equalsIgnoreCase(resultType)) {
                valueColumn = "NVL(SUM(GR_VALUE), 0)";
            } else if ("Line".equalsIgnoreCase(resultType)) {
                valueColumn = "COUNT(1)";
            } else if ("Quantity".equalsIgnoreCase(resultType)) {
                valueColumn = "NVL(SUM(GR_QTY), 0)";
            } else if ("Weight".equalsIgnoreCase(resultType)) {
                valueColumn = "NVL(SUM(GROSS_WEIGHT_IN_KG), 0)";
            }
            parameterMap.put("valueColumn", valueColumn);
        }
    }

    private String getColumnName(Object labelObj) {
        String label = (String) labelObj;
        if (label == null) {
            return null;
        }
        if (Utils.hasInjectionAttack(label)) {
            return "";
        }
        if ("PURCH_ORDER_NUMBER_ITEM".equalsIgnoreCase(label)) {
            return "PURCH_ORDER_NUMBER || ' ' || PURCH_ORDER_ITEM";
        } else if ("MATERIAL_PLANT".equalsIgnoreCase(label)) {
            return "MATERIAL || ' ' || PLANT_CODE";
        }
        return label;
    }

    @Override
    public void downloadReport2DisplayData(Map<String, Object> parameterMap, HttpServletResponse res) {
        JSONObject report2DisplayData = (JSONObject) parameterMap.get("report2DisplayData");
        String dateColumn = (String) parameterMap.get("dateColumn");
        String resultType = (String) parameterMap.get("resultType");
        @SuppressWarnings("unchecked")
        List<String> movAvgTempData = (List<String>) report2DisplayData.get("movAvgTempData");
        @SuppressWarnings("unchecked")
        List<String> avgTempData = (List<String>) report2DisplayData.get("avgTempData");
        JSONObject yAxisData = (JSONObject) report2DisplayData.get("yAxisData");
        @SuppressWarnings("unchecked")
        List<String> xAxis = (List<String>) yAxisData.get("xAxis");
        List<LinkedHashMap<String, Object>> dataList = new ArrayList<>();
        for (int i = 0; i < xAxis.size(); i++) {
            LinkedHashMap<String, Object> rowData = new LinkedHashMap<>();
            for (String key : yAxisData.keySet()) {
                List<?> valueList = (List<?>) yAxisData.get(key);
                if (valueList != null && i < valueList.size()) {
                    if ("xAxis".equals(key)) {
                        rowData.put(dateColumn, valueList.get(i));
                    } else {
                        rowData.put(resultType, valueList.get(i));
                    }
                }
            }
            rowData.put("Average Value of Points", avgTempData.get(i));
            rowData.put("Moving Average Value of Points", movAvgTempData.get(i));
            dataList.add(rowData);
        }
        excelTemplate.create(res, "PO Report2 Display Details.xlsx", dataList);
    }
}
