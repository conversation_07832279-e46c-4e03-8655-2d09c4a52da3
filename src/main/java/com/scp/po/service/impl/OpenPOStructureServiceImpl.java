package com.scp.po.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.Utils;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.scp.po.bean.POLAABReport1Bean;
import com.scp.po.bean.POLAABReport3Bean;
import com.scp.po.bean.POLAABTreemap;
import com.scp.po.dao.IOpenPOStructureDao;
import com.scp.po.service.IOpenPOStructureService;
import com.starter.utils.excel.ExcelTemplate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service("OpenPOStructureService")
@Scope("prototype")
@Transactional
public class OpenPOStructureServiceImpl extends ServiceHelper implements IOpenPOStructureService {

    @Resource
    private IOpenPOStructureDao openPOStructureDao;

    @Resource
    private Response response;

    @Resource
    private ExcelTemplate excelTemplate;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryFilters() {
        return response.setBody(Utils.parseCascader(openPOStructureDao.queryCascader()));
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) throws Exception {
        this.generateFilter(parameterMap);
        this.generateReport1Tooltips(parameterMap);
        this.generateValueColumn(parameterMap);

        // 将前台传过来的label转换成列名, 同时也可以防止恶意代码注入
        parameterMap.put("level1", this.getColumnName(parameterMap.get("level1")));
        parameterMap.put("level2", this.getColumnName(parameterMap.get("level2")));
        parameterMap.put("level3", this.getColumnName(parameterMap.get("level3")));
        parameterMap.put("level4", this.getColumnName(parameterMap.get("level4")));
        parameterMap.put("level5", this.getColumnName(parameterMap.get("level5")));

        List<POLAABTreemap> resultList = new ArrayList<>();
        List<POLAABReport1Bean> dataList = openPOStructureDao.queryReport1(parameterMap);
        for (POLAABReport1Bean data : dataList) {
            this.convertReport1Data(resultList, data);
        }
        return response.setBody(resultList);
    }

    /**
     * 将列表转化为Tree数据
     *
     * @param list 输出树
     * @param data 输入值
     * @throws Exception 异常
     */
    private void convertReport1Data(List<POLAABTreemap> list, POLAABReport1Bean data) throws Exception {
        String[] categorysOrg = new String[]{data.getCategory1(), data.getCategory2(), data.getCategory3(), data.getCategory4(), data.getCategory5()};
        List<String> categories = new ArrayList<>();

        for (String category : categorysOrg) {
            if (StringUtils.isNotBlank(category)) {
                categories.add(category);
            } else {
                break;
            }
        }

        // 这边逻辑比较复杂, 所以用最笨的方法来描述了, 以免后期不好维护
        // 先把这一行数据转成treemap的数据
        // 第一个节点
        List<POLAABTreemap> child = new ArrayList<>();
        POLAABTreemap root = new POLAABTreemap();
        root.setName(categories.get(0));
        root.setTips(data.copyTooltips()); // 因为这个tooltips要放在树中全局使用, 所以必须要生成一个新节点
        root.setChildren(child);

        // 中间节点
        for (int i = 1; i < categories.size() - 1; i++) {
            POLAABTreemap treemap = new POLAABTreemap();
            treemap.setName(categories.get(i));
            treemap.setTips(data.copyTooltips());

            child.add(treemap);
            child = new ArrayList<>();
            treemap.setChildren(child);
        }

        // 最后一个节点
        POLAABTreemap lastNode = new POLAABTreemap();
        lastNode.setName(categories.get(categories.size() - 1));
        lastNode.setValue(data.getValue());
        lastNode.setTips(data.copyTooltips());
        child.add(lastNode);

        // 将这行treemap与原始数据相加
        // 先找到list中是否有这个数据节点
        Optional<POLAABTreemap> beanOpt = list.stream().filter(b -> b.getName().equals(categories.get(0))).findFirst();
        if (beanOpt.isPresent()) {
            POLAABTreemap bean = beanOpt.get();
            bean.add(root); // 两个节点合并
        } else { //找不到的时候最省事, 直接放入list就可以了
            list.add(root);
        }
    }

    //region private functions
    @SuppressWarnings("unchecked")
    private void generateFilter(Map<String, Object> parameterMap) {
        List<String> report4SelectedColumns = (List<String>) parameterMap.get("report4SelectedColumns");

        if (report4SelectedColumns == null || report4SelectedColumns.isEmpty()) {
            report4SelectedColumns = new ArrayList<>();
            report4SelectedColumns.add("BU");
            report4SelectedColumns.add("ENTITY");
        }
        parameterMap.put("report4SelectedColumns", report4SelectedColumns);

        // 生成筛选条件
        List<String> materialOwnerNameList = new ArrayList<>();
        List<String> materialOwnerSesaList = new ArrayList<>();
        List<String> inventoryMonitorTypeList = new ArrayList<>();
        List<String> poMsgList = new ArrayList<>();
        this.generateCascaderFilterSQL(parameterMap, value -> {
            String key = value.getKey();
            if (key.startsWith("MATERIAL_OWNER_NAME")) {
                materialOwnerNameList.add(value.getValue());
                return false;
            } else if (key.startsWith("MATERIAL_OWNER_SESA")) {
                materialOwnerSesaList.add(value.getValue());
                return false;
            } else if (key.equals("INVENTORY_MONITOR_TYPE")) {
                inventoryMonitorTypeList.add(value.getValue());
                return false;
            } else if (key.equals("PO_MSG")) {
                poMsgList.add(value.getValue());
                return false;
            }
            return true;
        });
        parameterMap.put("materialOwnerNameFilter", this.parseInSQLFromValues(parameterMap, "T2.MATERIAL_OWNER_NAME", materialOwnerNameList));
        parameterMap.put("materialOwnerSesaFilter", this.parseInSQLFromValues(parameterMap, "T2.MATERIAL_OWNER_SESA", materialOwnerSesaList));
        parameterMap.put("inventoryMonitorTypeFilter", this.parseInSQLFromValues(parameterMap, "T2.INVENTORY_MONITOR_TYPE", inventoryMonitorTypeList));
        parameterMap.put("poMsgFilter", this.generatePOMsgFilter(parameterMap, poMsgList));

        String dateType = ((String) parameterMap.get("dateType")).replace("BY_", "");
        if ("DUE_DATE".equalsIgnoreCase(dateType)) {
            dateType = "DUEDATE";
        } else if ("SNAPSHOT_DATE".equalsIgnoreCase(dateType)) {
            dateType = "DATE$";
        }
        parameterMap.put("dateType", dateType);
    }

    private String generatePOMsgFilter(Map<String, Object> parameterMap, List<String> values) {
        values.remove("Others");
        if (values.size() > 0) {
            Map<String, List<String>> filterMap = new HashMap<>();
            for (String value : values) {
                String key = Utils.randomStr(8);
                parameterMap.put(key, value.split(" - ")[1]);
                filterMap.computeIfAbsent(value.split(" - ")[0], k -> new ArrayList<>()).add("#{" + key + ",jdbcType=VARCHAR}");
            }
            return filterMap.entrySet().stream()
                    .map(entry -> "NVL(T." + entry.getKey() + ", 'Others') IN (" + StringUtils.join(entry.getValue(), ",") + ")")
                    .collect(Collectors.joining(" AND "));
        }
        return null;
    }

    private void generateReport1Tooltips(Map<String, Object> parameterMap) {
        String resultType = (String) parameterMap.get("resultType");
        List<String> tooltips = ((JSONArray) parameterMap.get("report1Tooltips")).toJavaList(String.class);
        if (!tooltips.isEmpty()) {
            List<String> tooltipsColumns = tooltips.stream().map(this::getColumnName).collect(Collectors.toList());
            List<String> tooltipsColumnsName = new ArrayList<>();
            List<String> polymorphicColumns = new ArrayList<>(Arrays.asList("PO_AB", "PO_LA", "PO_NON_ABLA", "DELIVERY_DEPTH"));

            for (String c : tooltipsColumns) {
                String tooltip = this.getColumnName(c);
                if (polymorphicColumns.contains(c)) {
                    if ("Quantity".equalsIgnoreCase(resultType)) {
                        tooltipsColumnsName.add("NVL(SUM(" + c + "),0) AS " + tooltip);
                    } else if ("Value RMB".equalsIgnoreCase(resultType) & !"DELIVERY_DEPTH".equalsIgnoreCase(c)) {
                        tooltipsColumnsName.add("NVL(SUM(" + c + "_VALUE),0) AS " + tooltip);
                    } else if ("Line".equalsIgnoreCase(resultType)) {
                        tooltipsColumnsName.add("NVL(SUM(CASE WHEN " + c + " > 0 THEN 1 ELSE 0 END),0) AS " + tooltip);
                    } else if ("Value HKD".equalsIgnoreCase(resultType) & !"DELIVERY_DEPTH".equalsIgnoreCase(c)) {
                        tooltipsColumnsName.add("NVL(SUM(" + c + "_VALUE_HKD),0) AS " + tooltip);
                    }
                } else {
                    tooltipsColumnsName.add("NVL(AVG(" + c + "),0) AS " + tooltip);
                }
            }
            parameterMap.put("tooltipsColumns", StringUtils.join(tooltipsColumnsName, ", "));
        }
    }

    private void generateTreePathFilter(Map<String, Object> parameterMap) {
        String selectedTreePath = (String) parameterMap.get("selectedTreePath");
        if (StringUtils.isNotBlank(selectedTreePath)) {
            List<String> conditions = new ArrayList<>();
            String[] treePaths = selectedTreePath.split(" > ");
            for (int i = 1; i <= Math.min(treePaths.length, 5); i++) {
                String key = Utils.randomStr(8);
                String name = this.getColumnName(parameterMap.get("level" + i));
                if ("Others".equals(StringUtils.trim(treePaths[i - 1]))) {
                    if (name != null && (name.startsWith("MATERIAL_OWNER") || name.equals("INVENTORY_MONITOR_TYPE") == true)) {
                        conditions.add("(t2." + name + " = #{" + key + ",jdbcType=VARCHAR} or " + "t2." + name + " is null )");
                    } else {
                        conditions.add("(t." + name + " = #{" + key + ",jdbcType=VARCHAR} or " + "t." + name + " is null )");
                    }
                } else {
                    if (name != null && (name.startsWith("MATERIAL_OWNER") == true || name.equals("INVENTORY_MONITOR_TYPE") == true)) {
                        conditions.add("t2." + name + " = #{" + key + ",jdbcType=VARCHAR}");
                    } else {
                        conditions.add("t." + name + " = #{" + key + ",jdbcType=VARCHAR}");
                    }
                }
                parameterMap.put(key, StringUtils.trim(treePaths[i - 1]));
            }

            String filterHist = "(" + StringUtils.join(conditions, " and ") + ")";
            parameterMap.put("treePathFilter", filterHist);
        }
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);

        return response.setBody(openPOStructureDao.queryReport2(parameterMap));
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2Overall(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        parameterMap.put("overall", true);
        page.setTotal(openPOStructureDao.queryReport2DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(openPOStructureDao.queryReport2Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport2Overall(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        parameterMap.put("overall", true);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "open_po_structure_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.po.dao.IOpenPOStructureDao.queryReport2Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(openPOStructureDao.queryReport2DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(openPOStructureDao.queryReport2Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport2Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "open_po_structure_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.po.dao.IOpenPOStructureDao.queryReport2Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);

        List<POLAABReport3Bean> dataList;

        String dateType = (String) parameterMap.get("dateType");

        if ("DATE$".equalsIgnoreCase(dateType)) {
            dataList = openPOStructureDao.queryReport3(parameterMap);
        } else {
            dataList = openPOStructureDao.queryReport3SpecialDate(parameterMap);
        }

        Map<String, BigDecimal> dataMap = new HashMap<>();
        LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();
        Map<String, String> xAxisMap = new HashMap<>();

        List<String> legend = openPOStructureDao.queryReport3Legend(parameterMap);

        for (POLAABReport3Bean data : dataList) {
            dataMap.put(data.getKey(), data.getVALUE());
            xAxisMap.put(data.getCALENDAR_DATE(), "");
        }
        List<String> xAxisList = xAxisMap.keySet().stream().sorted(String::compareTo).collect(Collectors.toList());
        for (String l : legend) {
            List<BigDecimal> temp = new ArrayList<>();

            for (String x : xAxisList) {
                temp.add(dataMap.getOrDefault(l + "#" + x, BigDecimal.ZERO));
            }
            resultMap.put(l, temp);
        }

        resultMap.put("xAxis", xAxisList);
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(openPOStructureDao.queryReport3DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(openPOStructureDao.queryReport3Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "open_po_structure_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.po.dao.IOpenPOStructureDao.queryReport3Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport4(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        List<LinkedHashMap<String, Object>> dataList = openPOStructureDao.queryReport4(parameterMap);

        page.setData(dataList);
        page.setTotal(dataList.size());
        return response.setBody(page);
    }

    @Override
    public void downloadReport4(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        String column1 = this.getColumnName(parameterMap.get("column1"));
        String column2 = this.getColumnName(parameterMap.get("column2"));
        parameterMap.put("column1", column1);
        parameterMap.put("column2", column2);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "open_po_structure_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.po.dao.IOpenPOStructureDao.queryReport4", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport4Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(openPOStructureDao.queryReport4DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(openPOStructureDao.queryReport4Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport4Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        parameterMap.put("column1", this.getColumnName(parameterMap.get("column1")));
        parameterMap.put("column2", this.getColumnName(parameterMap.get("column2")));
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "open_po_structure_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.po.dao.IOpenPOStructureDao.queryReport4Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport4Columns(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);
        return response.setBody(openPOStructureDao.queryReport4Columns(parameterMap));
    }

    private void generateValueColumn(Map<String, Object> parameterMap) {
        String resultType = (String) parameterMap.get("resultType");
        String valueColumn = "NVL(SUM(T.PASTDUE_QTY), 0)";
        if ("Value RMB".equalsIgnoreCase(resultType)) {
            valueColumn = "NVL(SUM(PO_AB_VALUE + T.PO_LA_VALUE + T.PO_NON_ABLA_VALUE), 0)";
        } else if ("Value HKD".equalsIgnoreCase(resultType)) {
            valueColumn = "NVL(SUM(PO_AB_VALUE_HKD + T.PO_LA_VALUE_HKD + T.PO_NON_ABLA_VALUE_HKD), 0)";
        } else if ("Line".equalsIgnoreCase(resultType)) {
            valueColumn = "COUNT(1)";
        } else if ("Weight".equalsIgnoreCase(resultType)) {
            valueColumn = "NVL(SUM(T.GROSS_WEIGHT_IN_KG), 0)";
        }
        parameterMap.put("valueColumn", valueColumn);
    }

    private String getColumnName(Object labelObj) {
        String label = (String) labelObj;
        if (label == null) {
            return null;
        }
        if (Utils.hasInjectionAttack(label)) {
            return "";
        }
        if ("PURCH_ORDER_NUMBER_ITEM".equalsIgnoreCase(label)) {
            return "PURCH_ORDER_NUMBER || ' ' || t.PURCH_ORDER_ITEM";
        }
        if ("MATERIAL_PLANT".equalsIgnoreCase(label)) {
            return "MATERIAL || ' ' || T.PLANT_CODE";
        }
        return label;
    }
}
