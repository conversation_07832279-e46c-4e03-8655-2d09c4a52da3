package com.scp.po.bean;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.math.BigDecimal;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder(value = {"DELIVERY_QTY", "DELIVERY_VALUE", "DELIVERY_COST_VALUE", "UU_STOCK_QTY", "UU_STOCK_VALUE", "UU_STOCK_SALES_VALUE", "OPEN_SO_QTY", "OPEN_SO_COST_VALUE", "OPEN_SO_SALES_VALUE"})
public class POLAABReport2Tooltips {
    private BigDecimal DELIVERY_QTY;
    private BigDecimal DELIVERY_VALUE;
    private BigDecimal DELIVERY_COST_VALUE;
    private BigDecimal UU_STOCK_QTY;
    private BigDecimal UU_STOCK_VALUE;
    private BigDecimal UU_STOCK_SALES_VALUE;
    private BigDecimal OPEN_SO_QTY;
    private BigDecimal OPEN_SO_COST_VALUE;
    private BigDecimal OPEN_SO_SALES_VALUE;

    public BigDecimal getDELIVERY_QTY() {
        return DELIVERY_QTY;
    }

    public void setDELIVERY_QTY(BigDecimal DELIVERY_QTY) {
        this.DELIVERY_QTY = DELIVERY_QTY;
    }

    public BigDecimal getDELIVERY_VALUE() {
        return DELIVERY_VALUE;
    }

    public void setDELIVERY_VALUE(BigDecimal DELIVERY_VALUE) {
        this.DELIVERY_VALUE = DELIVERY_VALUE;
    }

    public BigDecimal getDELIVERY_COST_VALUE() {
        return DELIVERY_COST_VALUE;
    }

    public void setDELIVERY_COST_VALUE(BigDecimal DELIVERY_COST_VALUE) {
        this.DELIVERY_COST_VALUE = DELIVERY_COST_VALUE;
    }

    public BigDecimal getUU_STOCK_QTY() {
        return UU_STOCK_QTY;
    }

    public void setUU_STOCK_QTY(BigDecimal UU_STOCK_QTY) {
        this.UU_STOCK_QTY = UU_STOCK_QTY;
    }

    public BigDecimal getUU_STOCK_VALUE() {
        return UU_STOCK_VALUE;
    }

    public void setUU_STOCK_VALUE(BigDecimal UU_STOCK_VALUE) {
        this.UU_STOCK_VALUE = UU_STOCK_VALUE;
    }

    public BigDecimal getUU_STOCK_SALES_VALUE() {
        return UU_STOCK_SALES_VALUE;
    }

    public void setUU_STOCK_SALES_VALUE(BigDecimal UU_STOCK_SALES_VALUE) {
        this.UU_STOCK_SALES_VALUE = UU_STOCK_SALES_VALUE;
    }

    public BigDecimal getOPEN_SO_QTY() {
        return OPEN_SO_QTY;
    }

    public void setOPEN_SO_QTY(BigDecimal OPEN_SO_QTY) {
        this.OPEN_SO_QTY = OPEN_SO_QTY;
    }

    public BigDecimal getOPEN_SO_COST_VALUE() {
        return OPEN_SO_COST_VALUE;
    }

    public void setOPEN_SO_COST_VALUE(BigDecimal OPEN_SO_COST_VALUE) {
        this.OPEN_SO_COST_VALUE = OPEN_SO_COST_VALUE;
    }

    public BigDecimal getOPEN_SO_SALES_VALUE() {
        return OPEN_SO_SALES_VALUE;
    }

    public void setOPEN_SO_SALES_VALUE(BigDecimal OPEN_SO_SALES_VALUE) {
        this.OPEN_SO_SALES_VALUE = OPEN_SO_SALES_VALUE;
    }
}
