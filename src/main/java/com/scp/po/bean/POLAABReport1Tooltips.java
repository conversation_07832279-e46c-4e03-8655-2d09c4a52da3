package com.scp.po.bean;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class POLAABReport1Tooltips {
    private BigDecimal PO_AB;
    private BigDecimal PO_LA;
    private BigDecimal PO_NON_ABLA;
    private BigDecimal DELIVERY_DEPTH;

    private static final Class<POLAABReport1Tooltips> clazz;
    private static final List<String> fields;

    static {
        clazz = POLAABReport1Tooltips.class;
        fields = Arrays.stream(clazz.getDeclaredFields()).filter(f -> f.getAnnotatedType().getType().getTypeName().equalsIgnoreCase("java.math.BigDecimal")).map(Field::getName).collect(Collectors.toList());
    }

    public POLAABReport1Tooltips copyOf(POLAABReport1Tooltips tooltips) throws Exception {
        for (String f : fields) {
            Method method = clazz.getMethod("get" + f);
            Object value = method.invoke(tooltips);
            if (value != null) {
                clazz.getMethod("set" + f, BigDecimal.class).invoke(this, (BigDecimal) value);
            }
        }
        return this;
    }

    public BigDecimal getPO_AB() {
        return PO_AB;
    }

    public void setPO_AB(BigDecimal PO_AB) {
        this.PO_AB = PO_AB;
    }

    public BigDecimal getPO_LA() {
        return PO_LA;
    }

    public void setPO_LA(BigDecimal PO_LA) {
        this.PO_LA = PO_LA;
    }

    public BigDecimal getPO_NON_ABLA() {
        return PO_NON_ABLA;
    }

    public void setPO_NON_ABLA(BigDecimal PO_NON_ABLA) {
        this.PO_NON_ABLA = PO_NON_ABLA;
    }

    public BigDecimal getDELIVERY_DEPTH() {
        return DELIVERY_DEPTH;
    }

    public void setDELIVERY_DEPTH(BigDecimal DELIVERY_DEPTH) {
        this.DELIVERY_DEPTH = DELIVERY_DEPTH;
    }

    public void add(POLAABReport1Tooltips tips) throws Exception {
        for (String f : fields) {
            Method method = clazz.getMethod("get" + f);
            Object value = method.invoke(tips);
            if (value != null) {
                BigDecimal val = BigDecimal.ZERO;
                Object valueOrg = method.invoke(this);
                if (valueOrg != null) {
                    val = (BigDecimal) valueOrg;
                }
                clazz.getMethod("set" + f, BigDecimal.class).invoke(this, val.add((BigDecimal) value));
            }
        }
    }
}
