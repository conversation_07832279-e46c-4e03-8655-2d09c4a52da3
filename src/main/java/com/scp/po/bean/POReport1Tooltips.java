package com.scp.po.bean;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class POReport1Tooltips {
    private BigDecimal NET_WEIGHT;
    private BigDecimal GROSS_WEIGHT;
    private BigDecimal GROSS_WEIGHT_IN_KG;
    private BigDecimal PAYMENT_TERM;
    private BigDecimal MINIMUM_LOT_SIZE;

    private BigDecimal LINE;

    private BigDecimal PLANNED_DELIV_TIME;
    private BigDecimal ORDER_QUANTITY;
    private BigDecimal EXCHANGE_RATE;
    private BigDecimal UNIT_COST_RMB;
    private BigDecimal ORDER_VALUE_RMB;
    private BigDecimal GR_PROCESSING_TIME;
    private BigDecimal TOTAL_AB_VALUE_RMB;
    private BigDecimal TOTAL_LA_VALUE_RMB;
    private BigDecimal GR_VALUE_RMB;
    private BigDecimal AB_ORDER_QUANTITY;
    private BigDecimal LA_ORDER_QUANTITY;
    private BigDecimal GR_QTY;
    private BigDecimal ACTUAL_LT;

    private BigDecimal PLANNED_DELIV_DATE;

    public BigDecimal getNET_WEIGHT() {
        return NET_WEIGHT;
    }

    public void setNET_WEIGHT(BigDecimal NET_WEIGHT) {
        this.NET_WEIGHT = NET_WEIGHT;
    }

    public BigDecimal getGROSS_WEIGHT() {
        return GROSS_WEIGHT;
    }

    public BigDecimal getGROSS_WEIGHT_IN_KG() {
        return GROSS_WEIGHT_IN_KG;
    }

    public void setLINE(BigDecimal LINE) {
        this.LINE = LINE;
    }

    public BigDecimal getLINE() {
        return LINE;
    }

    public void setGROSS_WEIGHT(BigDecimal GROSS_WEIGHT) {
        this.GROSS_WEIGHT = GROSS_WEIGHT;
    }

    public void setGROSS_WEIGHT_IN_KG(BigDecimal GROSS_WEIGHT_IN_KG) {
        this.GROSS_WEIGHT_IN_KG = GROSS_WEIGHT_IN_KG;
    }

    public BigDecimal getPAYMENT_TERM() {
        return PAYMENT_TERM;
    }

    public void setPAYMENT_TERM(BigDecimal PAYMENT_TERM) {
        this.PAYMENT_TERM = PAYMENT_TERM;
    }

    public BigDecimal getMINIMUM_LOT_SIZE() {
        return MINIMUM_LOT_SIZE;
    }

    public void setMINIMUM_LOT_SIZE(BigDecimal MINIMUM_LOT_SIZE) {
        this.MINIMUM_LOT_SIZE = MINIMUM_LOT_SIZE;
    }

    public BigDecimal getPLANNED_DELIV_TIME() {
        return PLANNED_DELIV_TIME;
    }

    public void setPLANNED_DELIV_TIME(BigDecimal PLANNED_DELIV_TIME) {
        this.PLANNED_DELIV_TIME = PLANNED_DELIV_TIME;
    }

    public BigDecimal getORDER_QUANTITY() {
        return ORDER_QUANTITY;
    }

    public void setORDER_QUANTITY(BigDecimal ORDER_QUANTITY) {
        this.ORDER_QUANTITY = ORDER_QUANTITY;
    }

    public BigDecimal getEXCHANGE_RATE() {
        return EXCHANGE_RATE;
    }

    public void setEXCHANGE_RATE(BigDecimal EXCHANGE_RATE) {
        this.EXCHANGE_RATE = EXCHANGE_RATE;
    }

    public BigDecimal getUNIT_COST_RMB() {
        return UNIT_COST_RMB;
    }

    public void setUNIT_COST_RMB(BigDecimal UNIT_COST_RMB) {
        this.UNIT_COST_RMB = UNIT_COST_RMB;
    }

    public BigDecimal getORDER_VALUE_RMB() {
        return ORDER_VALUE_RMB;
    }

    public void setORDER_VALUE_RMB(BigDecimal ORDER_VALUE_RMB) {
        this.ORDER_VALUE_RMB = ORDER_VALUE_RMB;
    }

    public BigDecimal getGR_PROCESSING_TIME() {
        return GR_PROCESSING_TIME;
    }

    public void setGR_PROCESSING_TIME(BigDecimal GR_PROCESSING_TIME) {
        this.GR_PROCESSING_TIME = GR_PROCESSING_TIME;
    }

    public BigDecimal getTOTAL_AB_VALUE_RMB() {
        return TOTAL_AB_VALUE_RMB;
    }

    public void setTOTAL_AB_VALUE_RMB(BigDecimal TOTAL_AB_VALUE_RMB) {
        this.TOTAL_AB_VALUE_RMB = TOTAL_AB_VALUE_RMB;
    }

    public BigDecimal getTOTAL_LA_VALUE_RMB() {
        return TOTAL_LA_VALUE_RMB;
    }

    public void setTOTAL_LA_VALUE_RMB(BigDecimal TOTAL_LA_VALUE_RMB) {
        this.TOTAL_LA_VALUE_RMB = TOTAL_LA_VALUE_RMB;
    }

    public BigDecimal getGR_VALUE_RMB() {
        return GR_VALUE_RMB;
    }

    public void setGR_VALUE_RMB(BigDecimal GR_VALUE_RMB) {
        this.GR_VALUE_RMB = GR_VALUE_RMB;
    }

    public BigDecimal getAB_ORDER_QUANTITY() {
        return AB_ORDER_QUANTITY;
    }

    public void setAB_ORDER_QUANTITY(BigDecimal AB_ORDER_QUANTITY) {
        this.AB_ORDER_QUANTITY = AB_ORDER_QUANTITY;
    }

    public BigDecimal getLA_ORDER_QUANTITY() {
        return LA_ORDER_QUANTITY;
    }

    public void setLA_ORDER_QUANTITY(BigDecimal LA_ORDER_QUANTITY) {
        this.LA_ORDER_QUANTITY = LA_ORDER_QUANTITY;
    }

    public BigDecimal getGR_QTY() {
        return GR_QTY;
    }

    public void setGR_QTY(BigDecimal GR_QTY) {
        this.GR_QTY = GR_QTY;
    }

    public BigDecimal getACTUAL_LT() {
        return ACTUAL_LT;
    }

    public void setACTUAL_LT(BigDecimal ACTUAL_LT) {
        this.ACTUAL_LT = ACTUAL_LT;
    }

    public BigDecimal getPLANNED_DELIV_DATE() {
        return PLANNED_DELIV_DATE;
    }

    public void setPLANNED_DELIV_DATE(BigDecimal PLANNED_DELIV_DATE) {
        this.PLANNED_DELIV_DATE = PLANNED_DELIV_DATE;
    }

    private static final Class<POReport1Tooltips> clazz;
    private static final List<String> fields;

    static {
        clazz = POReport1Tooltips.class;
        fields = Arrays.stream(clazz.getDeclaredFields()).filter(f -> f.getAnnotatedType().getType().getTypeName().equalsIgnoreCase("java.math.BigDecimal")).map(Field::getName).collect(Collectors.toList());
    }

    public POReport1Tooltips copyOf(POReport1Tooltips tooltips) throws Exception {
        for (String f : fields) {
            Method method = clazz.getMethod("get" + f);
            Object value = method.invoke(tooltips);
            if (value != null) {
                clazz.getMethod("set" + f, BigDecimal.class).invoke(this, (BigDecimal) value);
            }
        }
        return this;
    }


    public void add(POReport1Tooltips tips) throws Exception {
        for (String f : fields) {
            Method method = clazz.getMethod("get" + f);
            Object value = method.invoke(tips);
            if (value != null) {
                BigDecimal val = BigDecimal.ZERO;
                Object valueOrg = method.invoke(this);
                if (valueOrg != null) {
                    val = (BigDecimal) valueOrg;
                }
                clazz.getMethod("set" + f, BigDecimal.class).invoke(this, val.add((BigDecimal) value));
            }
        }
    }
}
