package com.scp.po;

import com.scp.po.service.IShippingModeTrackingService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/po/shipping_mode_tracking", parent = "menu630")
public class ShippingModeTrackingController extends ControllerHelper {

    @Resource
    private IShippingModeTrackingService shippingModeTrackingService;

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.setGlobalCache(true);
        super.pageLoad(request);
        return shippingModeTrackingService.queryReport1(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping("/download_report1")
    public void downloadReport1(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        shippingModeTrackingService.downloadReport1(parameterMap, response);
    }
}
