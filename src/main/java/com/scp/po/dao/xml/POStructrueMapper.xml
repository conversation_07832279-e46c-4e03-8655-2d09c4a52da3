<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.po.dao.IPOStructureDao">

    <sql id="poFilter">
        <if test="_filters != null and _filters != ''.toString()">
            AND ${_filters}
        </if>
        <if test="treePathFilter != null and treePathFilter != ''.toString()">
            AND ${treePathFilter}
        </if>
        <if test="statRelDelDateRange != null and statRelDelDateRange.size() > 0">
            AND STAT_REL_DEL_DATE BETWEEN TO_DATE(#{statRelDelDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
            AND TRUNC(TO_DATE(#{statRelDelDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
        </if>
        <if test="poItemCreatedDateRange != null and poItemCreatedDateRange.size() > 0">
            AND PO_ITEM_CREATED_DATE BETWEEN TO_DATE(#{poItemCreatedDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
            AND TRUNC(TO_DATE(#{poItemCreatedDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
        </if>
        <if test="delDateRange != null and delDateRange.size() > 0">
            AND DELIVERY_DATE BETWEEN TO_DATE(#{delDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
            AND TRUNC(TO_DATE(#{delDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
        </if>
    </sql>

    <sql id="queryBase">
        SELECT /*+ parallel(t 6) */
               *
        FROM ${tableName} t
    </sql>

    <select id="queryCascader" resultType="java.util.Map">
        <choose>
            <when test="pageType == 'PO_CREATION'.toString()">
                SELECT NAME,
                    CATEGORY
                FROM ${SCPA.PO_STRUCTURE_CREATION_FILTER_V} T
                WHERE T.NAME != 'PO_ITEM_CREATED_DATE'
                ORDER BY CATEGORY,NAME
            </when>
            <otherwise>
                SELECT NAME,
                CATEGORY
                FROM ${SCPA.PO_STRUCTURE_GR_FILTER_V} T
                WHERE T.NAME != 'GR_DATE'
                ORDER BY CATEGORY,NAME
            </otherwise>
        </choose>
    </select>

    <resultMap id="report1ResultMap" type="com.scp.po.bean.POReport1Bean">
        <result property="category1" column="CATEGORY1"/>
        <result property="category2" column="CATEGORY2"/>
        <result property="category3" column="CATEGORY3"/>
        <result property="category4" column="CATEGORY4"/>
        <result property="category5" column="CATEGORY5"/>
        <result property="value" column="value"/>
        <association property="tooltips" javaType="com.scp.po.bean.POReport1Tooltips">
            <result property="NET_WEIGHT" column="NET_WEIGHT"/>
            <result property="GROSS_WEIGHT" column="GROSS_WEIGHT"/>
            <result property="GROSS_WEIGHT_IN_KG" column="GROSS_WEIGHT_IN_KG"/>
            <result property="LINE" column="LINE"/>
            <result property="PAYMENT_TERM" column="PAYMENT_TERM"/>
            <result property="MINIMUM_LOT_SIZE" column="MINIMUM_LOT_SIZE"/>
            <result property="PLANNED_DELIV_TIME" column="PLANNED_DELIV_TIME"/>
            <result property="ORDER_QUANTITY" column="ORDER_QUANTITY"/>
            <result property="EXCHANGE_RATE" column="EXCHANGE_RATE"/>
            <result property="UNIT_COST_RMB" column="UNIT_COST_RMB"/>
            <result property="ORDER_VALUE_RMB" column="ORDER_VALUE_RMB"/>
            <result property="GR_PROCESSING_TIME" column="GR_PROCESSING_TIME"/>
            <result property="TOTAL_AB_VALUE_RMB" column="TOTAL_AB_VALUE_RMB"/>
            <result property="TOTAL_LA_VALUE_RMB" column="TOTAL_LA_VALUE_RMB"/>
            <result property="GR_VALUE_RMB" column="GR_VALUE_RMB"/>
            <result property="AB_ORDER_QUANTITY" column="AB_ORDER_QUANTITY"/>
            <result property="LA_ORDER_QUANTITY" column="LA_ORDER_QUANTITY"/>
            <result property="GR_QTY" column="GR_QTY"/>
            <result property="ACTUAL_LT" column="ACTUAL_LT"/>
            <result property="PLANNED_DELIV_DATE" column="PLANNED_DELIV_DATE"/>
        </association>
    </resultMap>

    <select id="queryReport1" resultMap="report1ResultMap">
        <choose>
            <when test="pageType == 'PO_CREATION'.toString()">
                WITH BASE AS (
                <include refid="queryBase">
                    <property name="tableName" value="${SCPA.PO_STRUCTURE_CREATION_V}"/>
                </include>)
            </when>
            <otherwise>
                WITH BASE AS (
                <include refid="queryBase">
                    <property name="tableName" value="${SCPA.PO_STRUCTURE_GR_V}"/>
                </include>)
            </otherwise>
        </choose>
        SELECT
        NVL(${level1}, 'Others') AS CATEGORY1,
        NVL(${level2}, 'Others') AS CATEGORY2,
        NVL(${level3}, 'Others') AS CATEGORY3,
        <if test="level4 != null and level4 != ''.toString()">
            NVL(${level4}, 'Others') AS CATEGORY4,
        </if>
        <if test="level5 != null and level5 != ''.toString()">
            NVL(${level5}, 'Others') AS CATEGORY5,
        </if>
        ${valueColumn} AS VALUE
        <if test="tooltipsColumns != null and tooltipsColumns != ''.toString()">
            ,${tooltipsColumns}
        </if>
        FROM BASE t
        <choose>
            <when test="pageType == 'PO_CREATION'.toString()">
                WHERE PO_ITEM_CREATED_DATE BETWEEN TO_DATE(#{overviewDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                AND TRUNC(TO_DATE(#{overviewDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
            </when>
            <otherwise>
                WHERE GR_DATE BETWEEN TO_DATE(#{overviewDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                AND TRUNC(TO_DATE(#{overviewDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
            </otherwise>
        </choose>
        <include refid="poFilter"/>
        GROUP BY
        ${level1}, ${level2}, ${level3}
        <if test="level4 != null and level4 != ''.toString()">,${level4}</if>
        <if test="level5 != null and level5 != ''.toString()">,${level5}</if>
        <if test="tooltipsGroupBy == 'true'">
            ,PURCH_ORDER_NUMBER,PURCH_ORDER_ITEM
        </if>
    </select>

    <resultMap id="report2ResultMap" type="com.scp.po.bean.POReport2Bean">
        <result property="CALENDAR_DATE" column="CALENDAR_DATE"/>
        <result property="NAME" column="NAME"/>
        <result property="VALUE" column="VALUE"/>
    </resultMap>

    <select id="queryReport2" parameterType="java.util.Map" resultMap="report2ResultMap">
            <choose>
                <when test="pageType == 'PO_CREATION'.toString()">
                    WITH BASE AS (
                    <include refid="queryBase">
                        <property name="tableName" value="${SCPA.PO_STRUCTURE_CREATION_V}"/>
                    </include>)
                    <choose>
                        <when test='report2SelectedType == "VIEW_BY_DAY".toString()'>
                            SELECT TO_CHAR(PO_ITEM_CREATED_DATE, 'yyyy/mm/dd') AS CALENDAR_DATE,
                            <choose>
                                <when test="report2ViewType == 'PO_ITEM_CREATED_DATE'.toString()">
                                    'PO_ITEM_CREATED_DATE' AS NAME,
                                </when>
                                <otherwise>
                                    NVL(t.${report2ViewType}, 'Others') AS NAME,
                                </otherwise>
                            </choose>
                            ROUND(${valueColumn}, 2) AS VALUE
                            FROM BASE T
                            WHERE PO_ITEM_CREATED_DATE BETWEEN TO_DATE(#{overviewDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                            AND TRUNC(TO_DATE(#{overviewDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
                            <include refid="poFilter"/>
                            GROUP BY TO_CHAR(PO_ITEM_CREATED_DATE, 'yyyy/mm/dd'),
                            <choose>
                                <when test="report2ViewType == 'PO_ITEM_CREATED_DATE'.toString()">
                                    'PO_ITEM_CREATED_DATE'
                                </when>
                                <otherwise>
                                    NVL(t.${report2ViewType}, 'Others')
                                </otherwise>
                            </choose>
                            ORDER BY TO_CHAR(PO_ITEM_CREATED_DATE, 'yyyy/mm/dd') DESC
                        </when>
                        <when test='report2SelectedType == "VIEW_BY_WEEK".toString()'>
                            SELECT PO_ITEM_CREATED_WEEK AS CALENDAR_DATE,
                            <choose>
                                <when test="report2ViewType == 'PO_ITEM_CREATED_DATE'.toString()">
                                    'PO_ITEM_CREATED_DATE' AS NAME,
                                </when>
                                <otherwise>
                                    NVL(t.${report2ViewType}, 'Others') AS NAME,
                                </otherwise>
                            </choose>
                            ROUND(${valueColumn}, 2) AS VALUE
                            FROM BASE T
                            WHERE PO_ITEM_CREATED_DATE BETWEEN TO_DATE(#{overviewDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                            AND TRUNC(TO_DATE(#{overviewDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
                            <include refid="poFilter"/>
                            GROUP BY PO_ITEM_CREATED_WEEK,
                            <choose>
                                <when test="report2ViewType == 'PO_ITEM_CREATED_DATE'.toString()">
                                    'PO_ITEM_CREATED_DATE'
                                </when>
                                <otherwise>
                                    NVL(t.${report2ViewType}, 'Others')
                                </otherwise>
                            </choose>
                            ORDER BY PO_ITEM_CREATED_WEEK DESC
                        </when>
                        <when test='report2SelectedType == "VIEW_BY_MONTH".toString()'>
                            SELECT PO_ITEM_CREATED_MONTH AS CALENDAR_DATE,
                            <choose>
                                <when test="report2ViewType == 'PO_ITEM_CREATED_DATE'.toString()">
                                    'PO_ITEM_CREATED_DATE' AS NAME,
                                </when>
                                <otherwise>
                                    NVL(t.${report2ViewType}, 'Others') AS NAME,
                                </otherwise>
                            </choose>
                            ROUND(${valueColumn}, 2) AS VALUE
                            FROM BASE T
                            WHERE PO_ITEM_CREATED_DATE BETWEEN TO_DATE(#{overviewDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                            AND TRUNC(TO_DATE(#{overviewDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
                            <include refid="poFilter"/>
                            GROUP BY PO_ITEM_CREATED_MONTH,
                            <choose>
                                <when test="report2ViewType == 'PO_ITEM_CREATED_DATE'.toString()">
                                    'PO_ITEM_CREATED_DATE'
                                </when>
                                <otherwise>
                                    NVL(t.${report2ViewType}, 'Others')
                                </otherwise>
                            </choose>
                            ORDER BY PO_ITEM_CREATED_MONTH DESC
                        </when>
                        <when test='report2SelectedType == "VIEW_BY_QUARTER".toString()'>
                            SELECT PO_ITEM_CREATED_QUARTER AS CALENDAR_DATE,
                            <choose>
                                <when test="report2ViewType == 'PO_ITEM_CREATED_DATE'.toString()">
                                    'PO_ITEM_CREATED_DATE' AS NAME,
                                </when>
                                <otherwise>
                                    NVL(t.${report2ViewType}, 'Others') AS NAME,
                                </otherwise>
                            </choose>
                            ROUND(${valueColumn}, 2) AS VALUE
                            FROM BASE T
                            WHERE PO_ITEM_CREATED_DATE BETWEEN TO_DATE(#{overviewDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                            AND TRUNC(TO_DATE(#{overviewDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
                            <include refid="poFilter"/>
                            GROUP BY PO_ITEM_CREATED_QUARTER,
                            <choose>
                                <when test="report2ViewType == 'PO_ITEM_CREATED_DATE'.toString()">
                                    'PO_ITEM_CREATED_DATE'
                                </when>
                                <otherwise>
                                    NVL(t.${report2ViewType}, 'Others')
                                </otherwise>
                            </choose>
                            ORDER BY PO_ITEM_CREATED_QUARTER DESC
                        </when>
                        <when test='report2SelectedType == "VIEW_BY_YEAR".toString()'>
                            SELECT PO_ITEM_CREATED_YEAR AS CALENDAR_DATE,
                            <choose>
                                <when test="report2ViewType == 'PO_ITEM_CREATED_DATE'.toString()">
                                    'PO_ITEM_CREATED_DATE' AS NAME,
                                </when>
                                <otherwise>
                                    NVL(t.${report2ViewType}, 'Others') AS NAME,
                                </otherwise>
                            </choose>
                            ROUND(${valueColumn}, 2) AS VALUE
                            FROM BASE T
                            WHERE PO_ITEM_CREATED_DATE BETWEEN TO_DATE(#{overviewDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                            AND TRUNC(TO_DATE(#{overviewDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
                            <include refid="poFilter"/>
                            GROUP BY PO_ITEM_CREATED_YEAR,
                            <choose>
                                <when test="report2ViewType == 'PO_ITEM_CREATED_DATE'.toString()">
                                    'PO_ITEM_CREATED_DATE'
                                </when>
                                <otherwise>
                                    NVL(t.${report2ViewType}, 'Others')
                                </otherwise>
                            </choose>
                            ORDER BY PO_ITEM_CREATED_YEAR DESC
                        </when>
                    </choose>
                </when>
                <otherwise>
                    WITH BASE AS (
                    <include refid="queryBase">
                        <property name="tableName" value="${SCPA.PO_STRUCTURE_GR_V}"/>
                    </include>
                    )
                    <choose>
                        <when test='report2SelectedType == "VIEW_BY_DAY".toString()'>
                            SELECT TO_CHAR(GR_DATE, 'yyyy/mm/dd') AS CALENDAR_DATE,
                            <choose>
                                <when test="report2ViewType == 'GR_DATE'.toString()">
                                    'GR_DATE' AS NAME,
                                </when>
                                <otherwise>
                                    NVL(t.${report2ViewType}, 'Others') AS NAME,
                                </otherwise>
                            </choose>
                            ROUND(${valueColumn}, 2) AS VALUE
                            FROM BASE T
                            WHERE GR_DATE BETWEEN TO_DATE(#{overviewDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                            AND TRUNC(TO_DATE(#{overviewDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
                            <include refid="poFilter"/>
                            GROUP BY TO_CHAR(GR_DATE, 'yyyy/mm/dd'),
                            <choose>
                                <when test="report2ViewType == 'GR_DATE'.toString()">
                                    'GR_DATE'
                                </when>
                                <otherwise>
                                    NVL(t.${report2ViewType}, 'Others')
                                </otherwise>
                            </choose>
                            ORDER BY TO_CHAR(GR_DATE, 'yyyy/mm/dd') DESC
                        </when>
                        <when test='report2SelectedType == "VIEW_BY_WEEK".toString()'>
                            SELECT GR_WEEK AS CALENDAR_DATE,
                            <choose>
                                <when test="report2ViewType == 'GR_DATE'.toString()">
                                    'GR_DATE' AS NAME,
                                </when>
                                <otherwise>
                                    NVL(t.${report2ViewType}, 'Others') AS NAME,
                                </otherwise>
                            </choose>
                            ROUND(${valueColumn}, 2) AS VALUE
                            FROM BASE T
                            WHERE GR_DATE BETWEEN TO_DATE(#{overviewDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                            AND TRUNC(TO_DATE(#{overviewDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
                            <include refid="poFilter"/>
                            GROUP BY GR_WEEK,
                            <choose>
                                <when test="report2ViewType == 'GR_DATE'.toString()">
                                    'GR_DATE'
                                </when>
                                <otherwise>
                                    NVL(t.${report2ViewType}, 'Others')
                                </otherwise>
                            </choose>
                            ORDER BY GR_WEEK DESC
                        </when>
                        <when test='report2SelectedType == "VIEW_BY_MONTH".toString()'>
                            SELECT GR_MONTH AS CALENDAR_DATE,
                            <choose>
                                <when test="report2ViewType == 'GR_DATE'.toString()">
                                    'GR_DATE' AS NAME,
                                </when>
                                <otherwise>
                                    NVL(t.${report2ViewType}, 'Others') AS NAME,
                                </otherwise>
                            </choose>
                            ROUND(${valueColumn}, 2) AS VALUE
                            FROM BASE T
                            WHERE GR_DATE BETWEEN TO_DATE(#{overviewDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                            AND TRUNC(TO_DATE(#{overviewDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
                            <include refid="poFilter"/>
                            GROUP BY GR_MONTH,
                            <choose>
                                <when test="report2ViewType == 'GR_DATE'.toString()">
                                    'GR_DATE'
                                </when>
                                <otherwise>
                                    NVL(t.${report2ViewType}, 'Others')
                                </otherwise>
                            </choose>
                            ORDER BY GR_MONTH DESC
                        </when>
                        <when test='report2SelectedType == "VIEW_BY_QUARTER".toString()'>
                            SELECT GR_QUARTER AS CALENDAR_DATE,
                            <choose>
                                <when test="report2ViewType == 'GR_DATE'.toString()">
                                    'GR_DATE' AS NAME,
                                </when>
                                <otherwise>
                                    NVL(t.${report2ViewType}, 'Others') AS NAME,
                                </otherwise>
                            </choose>
                            ROUND(${valueColumn}, 2) AS VALUE
                            FROM BASE T
                            WHERE GR_DATE BETWEEN TO_DATE(#{overviewDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                            AND TRUNC(TO_DATE(#{overviewDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
                            <include refid="poFilter"/>
                            GROUP BY GR_QUARTER,
                            <choose>
                                <when test="report2ViewType == 'GR_DATE'.toString()">
                                    'GR_DATE'
                                </when>
                                <otherwise>
                                    NVL(t.${report2ViewType}, 'Others')
                                </otherwise>
                            </choose>
                            ORDER BY GR_QUARTER DESC
                        </when>
                        <when test='report2SelectedType == "VIEW_BY_YEAR".toString()'>
                            SELECT GR_YEAR AS CALENDAR_DATE,
                            <choose>
                                <when test="report2ViewType == 'GR_DATE'.toString()">
                                    'GR_DATE' AS NAME,
                                </when>
                                <otherwise>
                                    NVL(t.${report2ViewType}, 'Others') AS NAME,
                                </otherwise>
                            </choose>
                            ROUND(${valueColumn}, 2) AS VALUE
                            FROM BASE T
                            WHERE GR_DATE BETWEEN TO_DATE(#{overviewDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                            AND TRUNC(TO_DATE(#{overviewDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
                            <include refid="poFilter"/>
                            GROUP BY GR_YEAR,
                            <choose>
                                <when test="report2ViewType == 'GR_DATE'.toString()">
                                    'GR_DATE'
                                </when>
                                <otherwise>
                                    NVL(t.${report2ViewType}, 'Others')
                                </otherwise>
                            </choose>
                            ORDER BY GR_YEAR DESC
                        </when>
                    </choose>
                </otherwise>
            </choose>
    </select>

    <sql id="reportDateFilter">
        <choose>
            <when test="${viewType} == 'VIEW_BY_DAY'.toString()">
                T.${viewByDay} = TO_DATE(#{${selectedDate}, jdbcType=VARCHAR}, 'yyyy/mm/dd')
            </when>
            <when test="${viewType} == 'VIEW_BY_WEEK'.toString()">
                T.${viewByWeek} = #{${selectedDate}, jdbcType=VARCHAR}
                and t.${dateRange} BETWEEN TO_DATE(#{overviewDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                AND TO_DATE(#{overviewDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
            </when>
            <when test="${viewType} == 'VIEW_BY_MONTH'.toString()">
                T.${viewByMonth} = #{${selectedDate}, jdbcType=VARCHAR}
                and t.${dateRange} BETWEEN TO_DATE(#{overviewDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                AND TO_DATE(#{overviewDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
            </when>
            <when test="${viewType} == 'VIEW_BY_QUARTER'.toString()">
                T.${viewByQuarter} = #{${selectedDate}, jdbcType=VARCHAR}
                and t.${dateRange} BETWEEN TO_DATE(#{overviewDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                AND TO_DATE(#{overviewDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
            </when>
            <when test="${viewType} == 'VIEW_BY_YEAR'.toString()">
                T.${viewByYear} = #{${selectedDate}, jdbcType=VARCHAR}
                and t.${dateRange} BETWEEN TO_DATE(#{overviewDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                AND TO_DATE(#{overviewDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
            </when>
        </choose>
    </sql>

    <sql id="report2DetailsSQL">
        SELECT *
        FROM
        <choose>
            <when test="pageType == 'PO_CREATION'.toString()">
                ${SCPA.PO_STRUCTURE_CREATION_V} T
                <where>
                    <include refid="reportDateFilter">
                        <property name="selectedDate" value="report2SelectedAxis"/>
                        <property name="dateRange" value="PO_ITEM_CREATED_DATE"/>
                        <property name="viewByDay" value="PO_ITEM_CREATED_DATE"/>
                        <property name="viewByWeek" value="PO_ITEM_CREATED_WEEK"/>
                        <property name="viewByMonth" value="PO_ITEM_CREATED_MONTH"/>
                        <property name="viewByQuarter" value="PO_ITEM_CREATED_QUARTER"/>
                        <property name="viewByYear" value="PO_ITEM_CREATED_YEAR"/>
                        <property name="viewType" value="report2SelectedType"/>
                    </include>
                </where>
            </when>
            <otherwise>
                ${SCPA.PO_STRUCTURE_GR_V} T
                <where>
                    <include refid="reportDateFilter">
                        <property name="selectedDate" value="report2SelectedAxis"/>
                        <property name="dateRange" value="GR_DATE"/>
                        <property name="viewByDay" value="GR_DATE"/>
                        <property name="viewByWeek" value="GR_WEEK"/>
                        <property name="viewByMonth" value="GR_MONTH"/>
                        <property name="viewByQuarter" value="GR_QUARTER"/>
                        <property name="viewByYear" value="GR_YEAR"/>
                        <property name="viewType" value="report2SelectedType"/>
                    </include>
                </where>
            </otherwise>
        </choose>
        <include refid="poFilter"/>
        <if test="report2SelectedValue != null and report2SelectedValue != ''.toString() and report2SelectedValue != 'PO_ITEM_CREATED_DATE'.toString() and report2SelectedValue != 'GR_DATE'.toString()">
            AND T.${report2ViewType} = #{report2SelectedValue, jdbcType=VARCHAR}
        </if>
    </sql>

    <select id="queryReport2DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="report2DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport2Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report2DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="report3SQL">
        <choose>
            <when test="pageType == 'PO_CREATION'.toString()">
                WITH BASE AS (
                <include refid="queryBase">
                    <property name="tableName" value="${SCPA.PO_STRUCTURE_CREATION_V}"/>
                </include>
                <where>
<!--                    <foreach collection="report3SelectedColumns" item="item">-->
<!--                        AND ${item} IS NOT NULL-->
<!--                    </foreach>-->
                    t.PO_ITEM_CREATED_DATE BETWEEN TO_DATE(#{overviewDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                    AND TRUNC(TO_DATE(#{overviewDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
                    <include refid="poFilter"/>
                </where>
                ), NON_ABLA AS (
                SELECT <foreach collection="report3SelectedColumns" item="item">
                NVL(${item}, 'Others')                                    AS ${item},
                </foreach>
                <choose>
                    <when test="report3ViewType == 'VIEW_BY_DAY'.toString()">
                        T.PO_ITEM_CREATED_DATE                                AS PO_ITEM_CREATED_DATE,
                        ROUND(${valueColumn}, 3)                                      AS NON_ABLA_VALUE
                        FROM BASE t
                        GROUP BY
                            <foreach collection="report3SelectedColumns" item="item">
                                ${item},
                            </foreach>
                            PO_ITEM_CREATED_DATE
                        )
                    </when>
                    <when test="report3ViewType == 'VIEW_BY_WEEK'.toString()">
                        T.PO_ITEM_CREATED_WEEK                                AS PO_ITEM_CREATED_WEEK,
                        ROUND(${valueColumn}, 3)                                      AS NON_ABLA_VALUE
                        FROM BASE t
                        GROUP BY
                            <foreach collection="report3SelectedColumns" item="item">
                                ${item},
                            </foreach>
                            PO_ITEM_CREATED_WEEK
                        )
                    </when>
                    <when test="report3ViewType == 'VIEW_BY_MONTH'.toString()">
                        T.PO_ITEM_CREATED_MONTH                                AS PO_ITEM_CREATED_MONTH,
                        ROUND(${valueColumn}, 3)                                      AS NON_ABLA_VALUE
                        FROM BASE t
                        GROUP BY
                            <foreach collection="report3SelectedColumns" item="item">
                                ${item},
                            </foreach>
                            PO_ITEM_CREATED_MONTH
                        )
                    </when>
                    <when test="report3ViewType == 'VIEW_BY_QUARTER'.toString()">
                        T.PO_ITEM_CREATED_QUARTER                                AS PO_ITEM_CREATED_QUARTER,
                        ROUND(${valueColumn}, 3)                                      AS NON_ABLA_VALUE
                        FROM BASE t
                        GROUP BY
                            <foreach collection="report3SelectedColumns" item="item">
                                ${item},
                            </foreach>
                            PO_ITEM_CREATED_QUARTER
                        )
                    </when>
                    <when test="report3ViewType == 'VIEW_BY_YEAR'.toString()">
                        T.PO_ITEM_CREATED_YEAR                                AS PO_ITEM_CREATED_YEAR,
                        ROUND(${valueColumn}, 3)                                      AS NON_ABLA_VALUE
                        FROM BASE t
                        GROUP BY
                            <foreach collection="report3SelectedColumns" item="item">
                                ${item},
                            </foreach>
                            PO_ITEM_CREATED_YEAR
                        )
                    </when>
                </choose>

                SELECT *
                FROM (
                SELECT
                <foreach collection="report3SelectedColumns" item="item">
                    NVL(${item}, 'Others')                                    AS ${item},
                </foreach>
                <choose>
                    <when test="report3ViewType == 'VIEW_BY_DAY'.toString()">
                        TO_CHAR(T.PO_ITEM_CREATED_DATE,'YYYY/MM/DD')                                AS CALENDAR_DATE,
                    </when>
                    <when test="report3ViewType == 'VIEW_BY_WEEK'.toString()">
                        T.PO_ITEM_CREATED_WEEK                              AS CALENDAR_DATE,
                    </when>
                    <when test="report3ViewType == 'VIEW_BY_MONTH'.toString()">
                        T.PO_ITEM_CREATED_MONTH                               AS CALENDAR_DATE,
                    </when>
                    <when test="report3ViewType == 'VIEW_BY_QUARTER'.toString()">
                        T.PO_ITEM_CREATED_QUARTER                               AS CALENDAR_DATE,
                    </when>
                    <when test="report3ViewType == 'VIEW_BY_YEAR'.toString()">
                        T.PO_ITEM_CREATED_YEAR                               AS CALENDAR_DATE,
                    </when>
                </choose>
                NVL(T.NON_ABLA_VALUE, 0) AS TOTAL
                FROM NON_ABLA T
                ) mm
                PIVOT (
                SUM(TOTAL) AS TOTAL
                FOR CALENDAR_DATE
                IN (
                <foreach collection="listWithoutDuplicates" separator="," item="item">
                    '${item}'
                </foreach>
                )
                )
                ORDER BY
                <foreach collection="report3SelectedColumns" item="item" separator=",">
                    DECODE(${item}, 'Others', 'zzz', ${item})
                </foreach>
            </when>
            <otherwise>
                WITH BASE AS (
                <include refid="queryBase">
                    <property name="tableName" value="${SCPA.PO_STRUCTURE_GR_V}"/>
                </include>
                <where>
                    t.GR_DATE BETWEEN TO_DATE(#{overviewDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                    AND TRUNC(TO_DATE(#{overviewDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
                    <include refid="poFilter"/>
                </where>

                ), NON_ABLA AS (
                SELECT <foreach collection="report3SelectedColumns" item="item">
                NVL(${item}, 'Others')                                    AS ${item},
                </foreach>
                <choose>
                    <when test="report3ViewType == 'VIEW_BY_DAY'.toString()">
                        T.GR_DATE                                AS GR_DATE,
                        ROUND(${valueColumn}, 3)                                      AS NON_ABLA_VALUE
                        FROM BASE t
                        GROUP BY
                        <foreach collection="report3SelectedColumns" item="item">
                            ${item},
                        </foreach>
                        GR_DATE
                        )
                    </when>
                    <when test="report3ViewType == 'VIEW_BY_WEEK'.toString()">
                        T.GR_WEEK                                AS GR_WEEK,
                        ROUND(${valueColumn}, 3)                                      AS NON_ABLA_VALUE
                        FROM BASE t
                        GROUP BY
                        <foreach collection="report3SelectedColumns" item="item">
                            ${item},
                        </foreach>
                        GR_WEEK
                        )
                    </when>
                    <when test="report3ViewType == 'VIEW_BY_MONTH'.toString()">
                        T.GR_MONTH                                AS GR_MONTH,
                        ROUND(${valueColumn}, 3)                                      AS NON_ABLA_VALUE
                        FROM BASE t
                        GROUP BY
                        <foreach collection="report3SelectedColumns" item="item">
                            ${item},
                        </foreach>
                        GR_MONTH
                        )
                    </when>
                    <when test="report3ViewType == 'VIEW_BY_QUARTER'.toString()">
                        T.GR_QUARTER                                AS GR_QUARTER,
                        ROUND(${valueColumn}, 3)                                      AS NON_ABLA_VALUE
                        FROM BASE t
                        GROUP BY
                        <foreach collection="report3SelectedColumns" item="item">
                            ${item},
                        </foreach>
                        GR_QUARTER
                        )
                    </when>
                    <when test="report3ViewType == 'VIEW_BY_YEAR'.toString()">
                        T.GR_YEAR                                AS GR_YEAR,
                        ROUND(${valueColumn}, 3)                                      AS NON_ABLA_VALUE
                        FROM BASE t
                        GROUP BY
                        <foreach collection="report3SelectedColumns" item="item">
                            ${item},
                        </foreach>
                        GR_YEAR
                        )
                    </when>
                </choose>
                SELECT *
                FROM (
                SELECT <foreach collection="report3SelectedColumns" item="item">
                NVL(${item}, 'Others')                                    AS ${item},
                </foreach>
                <choose>
                    <when test="report3ViewType == 'VIEW_BY_DAY'.toString()">
                        TO_CHAR(T.GR_DATE,'YYYY/MM/DD')                                AS CALENDAR_DATE,
                    </when>
                    <when test="report3ViewType == 'VIEW_BY_WEEK'.toString()">
                        T.GR_WEEK                              AS CALENDAR_DATE,
                    </when>
                    <when test="report3ViewType == 'VIEW_BY_MONTH'.toString()">
                        T.GR_MONTH                               AS CALENDAR_DATE,
                    </when>
                    <when test="report3ViewType == 'VIEW_BY_QUARTER'.toString()">
                        T.GR_QUARTER                               AS CALENDAR_DATE,
                    </when>
                    <when test="report3ViewType == 'VIEW_BY_YEAR'.toString()">
                        T.GR_YEAR                               AS CALENDAR_DATE,
                    </when>
                </choose>
                NVL(T.NON_ABLA_VALUE, 0) AS TOTAL
                FROM NON_ABLA T
                ) mm
                PIVOT (
                SUM(TOTAL) AS TOTAL
                FOR CALENDAR_DATE
                IN (
                <foreach collection="listWithoutDuplicates" separator="," item="item">
                    '${item}'
                </foreach>
                )
                )
                ORDER BY
                <foreach collection="report3SelectedColumns" item="item" separator=",">
                    DECODE(${item}, 'Others', 'zzz', ${item})
                </foreach>
            </otherwise>
        </choose>
    </sql>

    <select id="queryReport3" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report3SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport3Columns" resultType="java.lang.String">
            SELECT DISTINCT * FROM
                (SELECT
                <choose>
                    <when test='report3ViewType == "VIEW_BY_DAY".toString()'>
                        distinct T.TEXT as result
                    </when>
                    <when test='report3ViewType == "VIEW_BY_WEEK".toString()'>
                        distinct (T.YEAR || T.WEEK_NO) as result
                    </when>
                    <when test='report3ViewType == "VIEW_BY_MONTH".toString()'>
                        distinct (T.YEAR || T.MONTH) as result
                    </when>
                    <when test='report3ViewType == "VIEW_BY_QUARTER".toString()'>
                        distinct TO_CHAR(T.DATE$, 'yyyy"Q"q') as result
                    </when>
                    <when test='report3ViewType == "VIEW_BY_YEAR".toString()'>
                        distinct T.YEAR as result
                    </when>
                    <otherwise>
                        distinct T.TEXT as result
                    </otherwise>
                </choose>
                FROM SCPA.SY_CALENDAR t
                WHERE t.DATE$ BETWEEN TO_DATE(#{overviewDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                    AND TO_DATE(#{overviewDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                AND T.NAME = 'National Holidays'
                ORDER BY t.DATE$ DESC
                )
            order by result desc
            OFFSET 0 ROWS FETCH NEXT 30 ROWS ONLY
    </select>

    <sql id="report3DetailsSQL">
        <choose>
            <when test="pageType == 'PO_CREATION'.toString()">
                <include refid="queryBase">
                    <property name="tableName" value="${SCPA.PO_STRUCTURE_CREATION_V}"/>
                </include>
                <where>
                    <choose>
                        <when test="report3SelectedDate != null and report3SelectedDate != ''.toString()">
                            <include refid="reportDateFilter">
                                <property name="selectedDate" value="report3SelectedDate"/>
                                <property name="dateRange" value="PO_ITEM_CREATED_DATE"/>
                                <property name="viewByDay" value="PO_ITEM_CREATED_DATE"/>
                                <property name="viewByWeek" value="PO_ITEM_CREATED_WEEK"/>
                                <property name="viewByMonth" value="PO_ITEM_CREATED_MONTH"/>
                                <property name="viewByQuarter" value="PO_ITEM_CREATED_QUARTER"/>
                                <property name="viewByYear" value="PO_ITEM_CREATED_YEAR"/>
                                <property name="viewType" value="report3ViewType"/>
                            </include>
                        </when>
                        <otherwise>
                            t.PO_ITEM_CREATED_DATE BETWEEN TO_DATE(#{overviewDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                            AND TO_DATE(#{overviewDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                        </otherwise>
                    </choose>
                    <include refid="poFilter"/>
                    <foreach collection="report3SelectedColumns" item="item" index="index">
                        <if test="report3SelectedValues[index] != null and report3SelectedValues[index] != ''.toString()">
                            and t.${item} = #{report3SelectedValues[${index}], jdbcType=VARCHAR}
                        </if>
                    </foreach>
                </where>
            </when>
            <otherwise>
                <include refid="queryBase">
                    <property name="tableName" value="${SCPA.PO_STRUCTURE_GR_V}"/>
                </include>
                <where>
                    <choose>
                        <when test="report3SelectedDate != null and report3SelectedDate != ''.toString()">
                            <include refid="reportDateFilter">
                                <property name="selectedDate" value="report3SelectedDate"/>
                                <property name="dateRange" value="GR_DATE"/>
                                <property name="viewByDay" value="GR_DATE"/>
                                <property name="viewByWeek" value="GR_WEEK"/>
                                <property name="viewByMonth" value="GR_MONTH"/>
                                <property name="viewByQuarter" value="GR_QUARTER"/>
                                <property name="viewByYear" value="GR_YEAR"/>
                                <property name="viewType" value="report3ViewType"/>
                            </include>
                        </when>
                        <otherwise>
                            t.GR_DATE BETWEEN TO_DATE(#{overviewDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                            AND TO_DATE(#{overviewDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                        </otherwise>
                    </choose>
                    <include refid="poFilter"/>
                    <foreach collection="report3SelectedColumns" item="item" index="index">
                        <if test="report3SelectedValues[index] != null and report3SelectedValues[index] != ''.toString()">
                            and t.${item} = #{report3SelectedValues[${index}], jdbcType=VARCHAR}
                        </if>
                    </foreach>
                </where>
            </otherwise>
        </choose>

    </sql>

    <select id="queryReport3DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="report3DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport3Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report3DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>
</mapper>
