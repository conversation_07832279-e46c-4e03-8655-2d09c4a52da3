<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.po.dao.IShippingModeTrackingDao">
    <sql id="queryReport1SQL">
        select MAIL_ID,
               SEND_TIME,
               SEND_BY,
               PO_NUMBER,
               PO_ITEM,
               PR_NUMBER,
               PR_ITEM,
               MATERIAL,
               PLANT_CODE,
               ORDER_QUANTITY,
               RCA,
               REASON_DETAILS,
               VENDOR_CODE,
               VENDOR_NAME,
               COUNTRY_CODE,
               SHORTAGE_FLAG,
               AB_LA,
               to_char(CREATE_DATE, 'yyyy/mm/dd') CREATE_DATE,
               to_char(DELIVERY_DATE, 'yyyy/mm/dd') DELIVERY_DATE,
               PUR_GROUP,
               SHIP_MODE,
               UNIT_WEIGHT_IN_KG,
               TOTAL_WEIGHT_IN_KG,
               SAP_WEIGHT_UNIT,
               PRODUCT_LINE,
               ENTITY,
               SOH,
               OPEN_SO,
               AVALIABLE_STOCK,
               SS,
               "Cur.Mon M-1 Fulfill",
               "Last.Mon M-1 Fulfill",
               "Cur.Mon M-3 Fulfill",
               "Last.Mon M-3 Fulfill",
               "Cur.Mon M-1 FCST",
               "Last.Mon M-1 FCST",
               "Cur.Mon M-3 FCST",
               "Last.Mon M-3 FCST",
               "Cur.Mon CRD",
               "Last.Mon CRD"
        from APPLICATION_002_HIST t
    </sql>

    <select id="queryReport1Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1SQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1SQL"/>
        <include refid="global.select_footer"/>
    </select>
</mapper>
