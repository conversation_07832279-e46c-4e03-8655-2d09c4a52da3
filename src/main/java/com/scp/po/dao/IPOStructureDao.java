package com.scp.po.dao;
import com.scp.po.bean.POReport1Bean;
import com.scp.po.bean.POReport2Bean;
import org.apache.ibatis.annotations.Mapper;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IPOStructureDao {

    List<Map<String, String>> queryCascader(Map<String, Object> parameterMap);

    List<POReport1Bean> queryReport1(Map<String, Object> parameterMap);

    List<POReport2Bean> queryReport2(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport2Details(Map<String, Object> parameterMap);

    int queryReport2DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3(Map<String, Object> parameterMap);

    List<String> queryReport3Columns(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3Details(Map<String, Object> parameterMap);

    int queryReport3DetailsCount(Map<String, Object> parameterMap);
}
