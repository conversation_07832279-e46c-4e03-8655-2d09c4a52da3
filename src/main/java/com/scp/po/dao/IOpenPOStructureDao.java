package com.scp.po.dao;

import com.scp.po.bean.POLAABReport1Bean;
import com.scp.po.bean.POLAABReport2Bean;
import com.scp.po.bean.POLAABReport3Bean;
import org.apache.ibatis.annotations.Mapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IOpenPOStructureDao {

    List<Map<String, String>> queryCascader();

    List<POLAABReport1Bean> queryReport1(Map<String, Object> parameterMap);

    List<POLAABReport2Bean> queryReport2(Map<String, Object> parameterMap);

    int queryReport2DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport2Details(Map<String, Object> parameterMap);

    List<POLAABReport3Bean> queryReport3(Map<String, Object> parameterMap);

    List<POLAABReport3Bean> queryReport3SpecialDate(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport4(Map<String, Object> parameterMap);

    List<String> queryReport4Columns(Map<String, Object> parameterMap);

    int queryReport4DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport4Details(Map<String, Object> parameterMap);

    int queryReport3DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3Details(Map<String, Object> parameterMap);

    List<String> queryReport3Legend(Map<String, Object> parameterMap);
}
