package com.scp.simulation.bean;

public class RmxCapacity {
    private String day;
    private String shift;
    private int capacity;

    public RmxCapacity(String day, String shift, int capacity) {
        this.day = day;
        this.shift = shift;
        this.capacity = capacity;
    }

    public String getDay() {
        return day;
    }

    public void setDay(String day) {
        this.day = day;
    }

    public String getShift() {
        return shift;
    }

    public void setShift(String shift) {
        this.shift = shift;
    }

    public int getCapacity() {
        return capacity;
    }

    public void setCapacity(int capacity) {
        this.capacity = capacity;
    }
}
