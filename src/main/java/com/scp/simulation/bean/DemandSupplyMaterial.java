package com.scp.simulation.bean;

import java.math.BigDecimal;

public class DemandSupplyMaterial {
    private String material;
    private String plantCode;
    private BigDecimal currentStock;
    private BigDecimal safetyStock;
    private BigDecimal amu;
    private BigDecimal amf;

    public String getMaterial() {
        return material;
    }

    public void setMaterial(String material) {
        this.material = material;
    }

    public String getPlantCode() {
        return plantCode;
    }

    public void setPlantCode(String plantCode) {
        this.plantCode = plantCode;
    }

    public String getKey() {
        return this.material + "@" + this.plantCode;
    }

    public BigDecimal getCurrentStock() {
        return currentStock;
    }

    public void setCurrentStock(BigDecimal currentStock) {
        this.currentStock = currentStock;
    }

    public BigDecimal getSafetyStock() {
        return safetyStock;
    }

    public void setSafetyStock(BigDecimal safetyStock) {
        this.safetyStock = safetyStock;
    }

    public BigDecimal getAmu() {
        return amu;
    }

    public void setAmu(BigDecimal amu) {
        this.amu = amu;
    }

    public BigDecimal getAmf() {
        return amf;
    }

    public void setAmf(BigDecimal amf) {
        this.amf = amf;
    }

    public BigDecimal getAvailableStock() {
        if (currentStock == null) {
            currentStock = BigDecimal.ZERO;
        }
        if (safetyStock == null) {
            safetyStock = BigDecimal.ZERO;
        }
        if (amu == null) {
            amu = BigDecimal.ZERO;
        }
        if (amf == null) {
            amf = BigDecimal.ZERO;
        }
        return currentStock.subtract(safetyStock).subtract(amu).subtract(amf);
    }
}
