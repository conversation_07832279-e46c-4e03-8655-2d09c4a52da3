package com.scp.simulation.bean;

import java.math.BigDecimal;

public class BestCanDoCompare1 {
    private String HORIZONTAL;

    private String NAME;

    private String BATCH_ID;

    private BigDecimal VALUE;

    public String getData() {
        return BATCH_ID + '@' + NAME + '#' + HORIZONTAL;
    }

    public String getKey() {
        return NAME + '#' + HORIZONTAL;
    }

    public String getHORIZONTAL() {
        return HORIZONTAL;
    }

    public void setHORIZONTAL(String HORIZONTAL) {
        this.HORIZONTAL = HORIZONTAL;
    }

    public String getNAME() {
        return NAME;
    }

    public void setNAME(String NAME) {
        this.NAME = NAME;
    }

    public String getBATCH_ID() {
        return BATCH_ID;
    }

    public void setBATCH_ID(String BATCH_ID) {
        this.BATCH_ID = BATCH_ID;
    }

    public BigDecimal getVALUE() {
        return VALUE;
    }

    public void setVALUE(BigDecimal VALUE) {
        this.VALUE = VALUE;
    }
}
