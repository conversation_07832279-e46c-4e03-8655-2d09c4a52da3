package com.scp.simulation.bean;

import com.alibaba.fastjson.JSON;

public class BestCanDoCalculatorLog {
    private String batch_id;
    private String time;
    private String message;

    public String getBatch_id() {
        return batch_id;
    }

    public void setBatch_id(String batch_id) {
        this.batch_id = batch_id;
    }

    public String getTime() {
        return time;
    }

    public void setTime(String time) {
        this.time = time;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String toJSONString() {
        return JSON.toJSONString(this);
    }
}
