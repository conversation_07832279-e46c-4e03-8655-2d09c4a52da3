package com.scp.simulation.bean;

import com.alibaba.fastjson.JSONArray;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class BestCanDoCalculatorParam {
    private String task_name;
    private String user_id;
    private String batch_id;
    private String type;
    private String strategy;
    private int step;
    private String step_mode;
    private String demand_base;
    private String plant;
    private String module;

    private Integer balanceValue;



    public void setTask_name(String task_name) {
        this.task_name = task_name;
    }

    public String getUser_id() {
        return user_id;
    }

    public void setUser_id(String user_id) {
        this.user_id = user_id;
    }

    public String getBatch_id() {
        return batch_id;
    }

    public void setBatch_id(String batch_id) {
        this.batch_id = batch_id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getStep() {
        return step;
    }

    public void setStep(int step) {
        this.step = step;
    }

    public String getDemand_base() {
        return demand_base;
    }

    public void setDemand_base(String demand_base) {
        this.demand_base = demand_base;
    }

    public String getTask_name() {
        return task_name;
    }

    public String getStep_mode() {
        return step_mode;
    }

    public void setStep_mode(String step_mode) {
        this.step_mode = step_mode;
    }

    public String getStrategy() {
        return strategy;
    }

    public void setStrategy(String strategy) {
        this.strategy = strategy;
    }

    public String getPlant() {
        return plant;
    }

    public void setPlant(String plant) {
        this.plant = plant;
    }

    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    public Integer getBalanceValue() {
        return balanceValue;
    }

    public void setBalanceValue(Integer balanceValue) {
        this.balanceValue = balanceValue;
    }
}
