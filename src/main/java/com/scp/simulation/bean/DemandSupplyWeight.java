package com.scp.simulation.bean;

import java.util.HashMap;
import java.util.Map;


public class DemandSupplyWeight {
    private String key;
    private String type;
    private Map<String, Object> orderMap = new HashMap<>();

    public boolean isMaterial() {
        return "MATERIAL".equals(this.type);
    }

    public boolean isGroup() {
        return "GROUP".equals(this.type);
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setOrder(String key, Object value) {
        orderMap.put(key, value);
    }

    public Object getOrder(Object key) {
        return orderMap.getOrDefault((String) key, "");
    }

    public Map<String, Object> getOrderMap() {
        return orderMap;
    }

    public void setOrderMap(Map<String, Object> orderMap) {
        this.orderMap = orderMap;
    }
}
