package com.scp.simulation.bean;

import com.adm.system.bean.LunarBean;
import com.adm.system.utils.Lunar;

import java.util.Calendar;

public class CalendarBean {
    private String M; // 月
    private String W; // 周几
    private String WK; // 周别
    private String D; // 天
    private String WD; // 是否是工作日
    private String LM; // 农历月
    private String LD; // 农历天
    private String TEXT; // 日期全称
    private String capacity; // 日期全称

    public String getM() {
        return M;
    }

    public void setM(String m) {
        M = m;
    }

    public String getW() {
        return W;
    }

    public void setW(String w) {
        W = w;
    }

    public String getD() {
        return D;
    }

    public void setD(String d) {
        D = d;
    }

    public String getWD() {
        return WD;
    }

    public void setWD(String WD) {
        this.WD = WD;
    }

    public String getLM() {
        return LM;
    }

    public void setLM(String LM) {
        this.LM = LM;
    }

    public String getLD() {
        return LD;
    }

    public void setLD(String LD) {
        this.LD = LD;
    }

    public String getTEXT() {
        return TEXT;
    }

    public void setTEXT(String TEXT) {
        this.TEXT = TEXT;
    }

    public String getWK() {
        return WK;
    }

    public void setWK(String WK) {
        this.WK = WK;
    }

    public String getCapacity() {
        return capacity;
    }

    public void setCapacity(String capacity) {
        this.capacity = capacity;
    }

    public void setStartTime(String start_time) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(Long.parseLong(start_time) * 1000);
        LunarBean lunarBean = Lunar.solarToLunar(calendar);
        LM = String.valueOf(lunarBean.lunarMonth);
        LD = String.valueOf(lunarBean.lunarDay);
    }

    public boolean isFirstDayOfWeek() {
        return "Mon".equalsIgnoreCase(W);
    }

    public boolean isFirstDayOfMonth() {
        return "1".equalsIgnoreCase(D);
    }
}
