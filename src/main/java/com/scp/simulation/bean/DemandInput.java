package com.scp.simulation.bean;

import com.starter.utils.Utils;
import org.apache.commons.lang3.StringUtils;

public class DemandInput {
    private String primaryKey;

    private String material;
    private double qty;
    private Integer priority;

    private String plant_code;

    public String getPrimaryKey() {
        if (StringUtils.isNotBlank(primaryKey)) {
            return primaryKey;
        } else {
            primaryKey = Utils.randomStr(16);
        }
        return primaryKey;
    }

    public void setPrimaryKey(String primaryKey) {
        this.primaryKey = primaryKey;
    }

    public String getMaterial() {
        return material;
    }

    public void setMaterial(String material) {
        this.material = material;
    }

    public double getQty() {
        return qty;
    }

    public void setQty(double qty) {
        this.qty = qty;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getPlant_code() {
        return plant_code;
    }

    public void setPlant_code(String plant_code) {
        this.plant_code = plant_code;
    }
}
