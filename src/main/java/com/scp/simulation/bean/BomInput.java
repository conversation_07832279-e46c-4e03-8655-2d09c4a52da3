package com.scp.simulation.bean;

public class BomInput {
    private String material;
    private String bomComponent;
    private double usage;
    private String supplier;

    private String fixRecourse;


    public String getMaterial() {
        return material;
    }

    public void setMaterial(String material) {
        this.material = material;
    }

    public String getBomComponent() {
        return bomComponent;
    }

    public void setBomComponent(String bomComponent) {
        this.bomComponent = bomComponent;
    }

    public double getUsage() {
        return usage;
    }

    public void setUsage(double usage) {
        this.usage = usage;
    }

    public String getSupplier() {
        return supplier;
    }

    public void setSupplier(String supplier) {
        this.supplier = supplier;
    }

    public String getFixRecourse() {
        return fixRecourse;
    }

    public void setFixRecourse(String fixRecourse) {
        this.fixRecourse = fixRecourse;
    }
}
