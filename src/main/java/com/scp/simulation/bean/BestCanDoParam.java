package com.scp.simulation.bean;

import com.alibaba.fastjson.JSONArray;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class BestCanDoParam {
    private String task_name;
    private String user_id;
    private String batch_id;
    private String plant_code;
    private int step;
    private String step_mode;
    private String demand_base;

    private boolean var_resolution = false;

    private boolean resource_block = false;
    private List<Map<String, Object>> soh_scope = new ArrayList<>();

    public BestCanDoParam() {
        Map<String, Object> temp = new HashMap<>();
        temp.put("soh", "UU_STOCK");
        temp.put("consider", true);
        soh_scope.add(temp);

        temp = new HashMap<>();
        temp.put("soh", "STOCK_IN_QI");
        temp.put("consider", true);
        soh_scope.add(temp);

        temp = new HashMap<>();
        temp.put("soh", "BLOCKED_STOCK");
        temp.put("consider", false);
        soh_scope.add(temp);

        temp = new HashMap<>();
        temp.put("soh", "RESTRICTED_STOCK");
        temp.put("consider", false);
        soh_scope.add(temp);

        temp = new HashMap<>();
        temp.put("soh", "RETURNS_STOCK");
        temp.put("consider", false);
        soh_scope.add(temp);

        temp = new HashMap<>();
        temp.put("soh", "INTER_STOCK_TRANSFER");
        temp.put("consider", false);
        soh_scope.add(temp);
    }

    public String getTask_name() {
        return task_name;
    }

    public void setTask_name(String task_name) {
        this.task_name = task_name;
    }

    public String getUser_id() {
        return user_id;
    }

    public void setUser_id(String user_id) {
        this.user_id = user_id;
    }

    public String getBatch_id() {
        return batch_id;
    }

    public void setBatch_id(String batch_id) {
        this.batch_id = batch_id;
    }

    public String getPlant_code() {
        return plant_code;
    }

    public void setPlant_code(String plant_code) {
        this.plant_code = plant_code;
    }

    public int getStep() {
        return step;
    }

    public void setStep(int step) {
        this.step = step;
    }

    public String getDemand_base() {
        return demand_base;
    }

    public void setDemand_base(String demand_base) {
        this.demand_base = demand_base;
    }

    public List<Map<String, Object>> getSoh_scope() {
        return soh_scope;
    }

    public void setSoh_scope(List<Map<String, Object>> soh_scope) {
        this.soh_scope = soh_scope;
    }

    public String getStep_mode() {
        return step_mode;
    }

    public void setStep_mode(String step_mode) {
        this.step_mode = step_mode;
    }

    public boolean isResource_block() {
        return resource_block;
    }

    public void setResource_block(boolean resource_block) {
        this.resource_block = resource_block;
    }


    public boolean isVar_resolution() {
        return var_resolution;
    }

    public void setVar_resolution(boolean var_resolution) {
        this.var_resolution = var_resolution;
    }

    public void syncStockConfig(JSONArray stockConfig) {
        if (stockConfig != null) {
            for (Map<String, Object> map : soh_scope) {
                String name = (String) map.get("soh");
                if (stockConfig.contains(name)) {
                    map.put("consider", true);
                } else {
                    map.put("consider", false);
                }
            }
        }
    }
}
