package com.scp.simulation.bean;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class BestCanDoCompare2Bean {
    private String category1;
    private String category2;
    private BigDecimal value;
    private BestCanDoCompare2Tooltips tooltips = new BestCanDoCompare2Tooltips();

    public String getCategory1() {
        return category1;
    }

    public void setCategory1(String category1) {
        this.category1 = category1;
    }

    public String getCategory2() {
        return category2;
    }

    public void setCategory2(String category2) {
        this.category2 = category2;
    }

    public BigDecimal getValue() {
        return value.setScale(0, RoundingMode.HALF_UP);
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public BestCanDoCompare2Tooltips getTooltips() {
        return tooltips;
    }

    public BestCanDoCompare2Tooltips copyTooltips() throws Exception {
        return new BestCanDoCompare2Tooltips().copyOf(tooltips);
    }

    public void setTooltips(BestCanDoCompare2Tooltips tooltips) {
        this.tooltips = tooltips;
    }
}
