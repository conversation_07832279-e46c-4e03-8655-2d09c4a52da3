package com.scp.simulation.bean;

import com.starter.utils.Utils;
import org.apache.commons.lang3.StringUtils;

public class SOMOPriority {
    private String orderNumber;
    private String plantCode;
    private String material;
    private double qty;
    private String priorityDate;
    private String requestDate;

    public String getOrderNumber() {
        if (StringUtils.isNotBlank(orderNumber)) {
            return orderNumber;
        } else {
            orderNumber = Utils.randomStr(16);
        }
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getPlantCode() {
        return plantCode;
    }

    public void setPlantCode(String plantCode) {
        this.plantCode = plantCode;
    }

    public String getMaterial() {
        return material;
    }

    public void setMaterial(String material) {
        this.material = material;
    }

    public double getQty() {
        return qty;
    }

    public void setQty(double qty) {
        this.qty = qty;
    }

    public String getPriorityDate() {
        return priorityDate;
    }

    public void setPriorityDate(String priorityDate) {
        this.priorityDate = priorityDate;
    }

    public String getRequestDate() {
        return requestDate;
    }

    public void setRequestDate(String requestDate) {
        this.requestDate = requestDate;
    }
}
