package com.scp.simulation.bean;

import com.alibaba.fastjson.JSONObject;

public class RmxConfig {

    // Mult fields
    private boolean multThresholdEnable;
    private int multThreshold;

    // IEC fields
    private boolean iecMaxCapacityEnable;
    private int iecMaxCapacity;
    private boolean iecIncompatibilityEnable;

    // PTCR fields
    private boolean ptcrMaxCapacityEnable;
    private int ptcrMaxCapacity;
    private boolean ptcrIncompatibilityEnable;

    // Q fields
    private boolean qMaxCapacityEnable;
    private int qMaxCapacity;
    private boolean qIncompatibilityEnable;

    // TE fields
    private boolean teMaxCapacityEnable;
    private int teMaxCapacity;
    private boolean teIncompatibilityEnable;

    // V24K fields
    private boolean v24kMaxCapacityEnable;
    private int v24kMaxCapacity;

    // VB capacity
    private boolean vbMaxCapacityEnable;
    private int vbCapacity;

    public RmxConfig(JSONObject obj) {
        JSONObject config = obj.getJSONObject("mult");
        this.multThresholdEnable = config.getBoolean("thresholdEnable");
        this.multThreshold = config.getInteger("threshold");

        config = obj.getJSONObject("iec");
        this.iecMaxCapacityEnable = config.getBoolean("maxCapacityEnable");
        this.iecMaxCapacity = config.getInteger("maxCapacity");
        this.iecIncompatibilityEnable = config.getBoolean("incompatibilityEnable");

        config = obj.getJSONObject("ptcr");
        this.ptcrMaxCapacityEnable = config.getBoolean("maxCapacityEnable");
        this.ptcrMaxCapacity = config.getInteger("maxCapacity");
        this.ptcrIncompatibilityEnable = config.getBoolean("incompatibilityEnable");

        config = obj.getJSONObject("q");
        this.qMaxCapacityEnable = config.getBoolean("maxCapacityEnable");
        this.qMaxCapacity = config.getInteger("maxCapacity");
        this.qIncompatibilityEnable = config.getBoolean("incompatibilityEnable");

        config = obj.getJSONObject("te");
        this.teMaxCapacityEnable = config.getBoolean("maxCapacityEnable");
        this.teMaxCapacity = config.getInteger("maxCapacity");
        this.teIncompatibilityEnable = config.getBoolean("incompatibilityEnable");

        config = obj.getJSONObject("v24k");
        this.v24kMaxCapacityEnable = config.getBoolean("maxCapacityEnable");
        this.v24kMaxCapacity = config.getInteger("maxCapacity");

        config = obj.getJSONObject("vb");
        this.vbMaxCapacityEnable = config.getBoolean("maxCapacityEnable");
        this.vbCapacity = config.getInteger("maxCapacity");
    }


    // Mult getters and setters
    public boolean isMultThresholdEnable() {
        return multThresholdEnable;
    }

    public void setMultThresholdEnable(boolean multThresholdEnable) {
        this.multThresholdEnable = multThresholdEnable;
    }

    public int getMultThreshold() {
        return multThreshold;
    }

    public void setMultThreshold(int multThreshold) {
        this.multThreshold = multThreshold;
    }

    // IEC getters and setters
    public boolean isIecMaxCapacityEnable() {
        return iecMaxCapacityEnable;
    }

    public void setIecMaxCapacityEnable(boolean iecMaxCapacityEnable) {
        this.iecMaxCapacityEnable = iecMaxCapacityEnable;
    }

    public int getIecMaxCapacity() {
        return iecMaxCapacity;
    }

    public void setIecMaxCapacity(int iecMaxCapacity) {
        this.iecMaxCapacity = iecMaxCapacity;
    }

    public boolean isIecIncompatibilityEnable() {
        return iecIncompatibilityEnable;
    }

    public void setIecIncompatibilityEnable(boolean iecIncompatibilityEnable) {
        this.iecIncompatibilityEnable = iecIncompatibilityEnable;
    }

    // PTCR getters and setters
    public boolean isPtcrMaxCapacityEnable() {
        return ptcrMaxCapacityEnable;
    }

    public void setPtcrMaxCapacityEnable(boolean ptcrMaxCapacityEnable) {
        this.ptcrMaxCapacityEnable = ptcrMaxCapacityEnable;
    }

    public int getPtcrMaxCapacity() {
        return ptcrMaxCapacity;
    }

    public void setPtcrMaxCapacity(int ptcrMaxCapacity) {
        this.ptcrMaxCapacity = ptcrMaxCapacity;
    }

    public boolean isPtcrIncompatibilityEnable() {
        return ptcrIncompatibilityEnable;
    }

    public void setPtcrIncompatibilityEnable(boolean ptcrIncompatibilityEnable) {
        this.ptcrIncompatibilityEnable = ptcrIncompatibilityEnable;
    }

    // Q getters and setters
    public boolean isqMaxCapacityEnable() {
        return qMaxCapacityEnable;
    }

    public void setqMaxCapacityEnable(boolean qMaxCapacityEnable) {
        this.qMaxCapacityEnable = qMaxCapacityEnable;
    }

    public int getqMaxCapacity() {
        return qMaxCapacity;
    }

    public void setqMaxCapacity(int qMaxCapacity) {
        this.qMaxCapacity = qMaxCapacity;
    }

    public boolean isqIncompatibilityEnable() {
        return qIncompatibilityEnable;
    }

    public void setqIncompatibilityEnable(boolean qIncompatibilityEnable) {
        this.qIncompatibilityEnable = qIncompatibilityEnable;
    }

    // TE getters and setters
    public boolean isTeMaxCapacityEnable() {
        return teMaxCapacityEnable;
    }

    public void setTeMaxCapacityEnable(boolean teMaxCapacityEnable) {
        this.teMaxCapacityEnable = teMaxCapacityEnable;
    }

    public int getTeMaxCapacity() {
        return teMaxCapacity;
    }

    public void setTeMaxCapacity(int teMaxCapacity) {
        this.teMaxCapacity = teMaxCapacity;
    }

    public boolean isTeIncompatibilityEnable() {
        return teIncompatibilityEnable;
    }

    public void setTeIncompatibilityEnable(boolean teIncompatibilityEnable) {
        this.teIncompatibilityEnable = teIncompatibilityEnable;
    }

    // V24K getters and setters
    public boolean isV24kMaxCapacityEnable() {
        return v24kMaxCapacityEnable;
    }

    public void setV24kMaxCapacityEnable(boolean v24kMaxCapacityEnable) {
        this.v24kMaxCapacityEnable = v24kMaxCapacityEnable;
    }

    public int getV24kMaxCapacity() {
        return v24kMaxCapacity;
    }

    public void setV24kMaxCapacity(int v24kMaxCapacity) {
        this.v24kMaxCapacity = v24kMaxCapacity;
    }

    public int getVbCapacity() {
        return vbCapacity;
    }

    public void setVbCapacity(int vbCapacity) {
        this.vbCapacity = vbCapacity;
    }

    public boolean isVbMaxCapacityEnable() {
        return vbMaxCapacityEnable;
    }

    public void setVbMaxCapacityEnable(boolean vbMaxCapacityEnable) {
        this.vbMaxCapacityEnable = vbMaxCapacityEnable;
    }
}
