package com.scp.simulation.bean;

import com.starter.utils.Utils;
import org.apache.commons.lang3.StringUtils;

public class RecourseInput {
    private String material;
    private String step0;
    private String step1;
    private String step2;
    private String step3;
    private String step4;
    private String step5;
    private String step6;
    private String step7;
    private String step8;
    private String step9;
    private String step10;
    private String step11;
    private String step12;
    private String step13;
    private String step14;
    private String step15;
    private String step16;
    private String step17;
    private String step18;
    private String step19;
    private String step20;
    private String step21;
    private String step22;
    private String step23;
    private String step24;
    private String step25;
    private String step26;
    private String step27;
    private String step28;
    private String step29;
    private String step30;
    private String step31;
    private String step32;
    private String step33;
    private String step34;
    private String step35;

    public String getStep1() {
        if (step1 != null) {
            return step1;
        } else {
            step1 = String.valueOf("");
        }
        return step1;
    }

    public String getMaterial() {
        return material;
    }

    public void setMaterial(String material) {
        this.material = material;
    }


    public String getStep0() {
        return step0;
    }
    public void setStep0(String step0) {
        this.step0 = step0;
    }

    public void setStep1(String step1) {
        this.step1 = step1;
    }

    public String getStep2() {
        return step2;
    }

    public void setStep2(String step2) {
        this.step2 = step2;
    }

    public String getStep3() {
        return step3;
    }

    public void setStep3(String step3) {
        this.step3 = step3;
    }

    public String getStep4() {
        return step4;
    }

    public void setStep4(String step4) {
        this.step4 = step4;
    }

    public String getStep5() {
        return step5;
    }

    public void setStep5(String step5) {
        this.step5 = step5;
    }

    public String getStep6() {
        return step6;
    }

    public void setStep6(String step6) {
        this.step6 = step6;
    }

    public String getStep7() {
        return step7;
    }

    public void setStep7(String step7) {
        this.step7 = step7;
    }

    public String getStep8() {
        return step8;
    }

    public void setStep8(String step8) {
        this.step8 = step8;
    }

    public String getStep9() {
        return step9;
    }

    public void setStep9(String step9) {
        this.step9 = step9;
    }

    public String getStep10() {
        return step10;
    }

    public void setStep10(String step10) {
        this.step10 = step10;
    }

    public String getStep11() {
        return step11;
    }

    public void setStep11(String step11) {
        this.step11 = step11;
    }

    public String getStep12() {
        return step12;
    }

    public void setStep12(String step12) {
        this.step12 = step12;
    }

    public String getStep13() {
        return step13;
    }

    public void setStep13(String step13) {
        this.step13 = step13;
    }

    public String getStep14() {
        return step14;
    }

    public void setStep14(String step14) {
        this.step14 = step14;
    }

    public String getStep15() {
        return step15;
    }

    public void setStep15(String step15) {
        this.step15 = step15;
    }

    public String getStep16() {
        return step16;
    }

    public void setStep16(String step16) {
        this.step16 = step16;
    }

    public String getStep17() {
        return step17;
    }

    public void setStep17(String step17) {
        this.step17 = step17;
    }

    public String getStep18() {
        return step18;
    }

    public void setStep18(String step18) {
        this.step18 = step18;
    }

    public String getStep19() {
        return step19;
    }

    public void setStep19(String step19) {
        this.step19 = step19;
    }

    public String getStep20() {
        return step20;
    }

    public void setStep20(String step20) {
        this.step20 = step20;
    }

    public String getStep21() {
        return step21;
    }

    public void setStep21(String step21) {
        this.step21 = step21;
    }

    public String getStep22() {
        return step22;
    }

    public void setStep22(String step22) {
        this.step22 = step22;
    }

    public String getStep23() {
        return step23;
    }

    public void setStep23(String step23) {
        this.step23 = step23;
    }

    public String getStep24() {
        return step24;
    }

    public void setStep24(String step24) {
        this.step24 = step24;
    }

    public String getStep25() {
        return step25;
    }

    public void setStep25(String step25) {
        this.step25 = step25;
    }

    public String getStep26() {
        return step26;
    }

    public void setStep26(String step26) {
        this.step26 = step26;
    }

    public String getStep27() {
        return step27;
    }

    public void setStep27(String step27) {
        this.step27 = step27;
    }

    public String getStep28() {
        return step28;
    }

    public void setStep28(String step28) {
        this.step28 = step28;
    }

    public String getStep29() {
        return step29;
    }

    public void setStep29(String step29) {
        this.step29 = step29;
    }

    public String getStep30() {
        return step30;
    }

    public void setStep30(String step30) {
        this.step30 = step30;
    }

    public String getStep31() {
        return step31;
    }

    public void setStep31(String step31) {
        this.step31 = step31;
    }

    public String getStep32() {
        return step32;
    }

    public void setStep32(String step32) {
        this.step32 = step32;
    }

    public String getStep33() {
        return step33;
    }

    public void setStep33(String step33) {
        this.step33 = step33;
    }

    public String getStep34() {
        return step34;
    }

    public void setStep34(String step34) {
        this.step34 = step34;
    }

    public String getStep35() {
        return step35;
    }

    public void setStep35(String step35) {
        this.step35 = step35;
    }
}
