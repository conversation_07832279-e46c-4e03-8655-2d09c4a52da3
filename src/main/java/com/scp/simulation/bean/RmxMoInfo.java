package com.scp.simulation.bean;

import com.alibaba.fastjson.JSON;

import java.text.SimpleDateFormat;
import java.util.LinkedHashMap;
import java.util.Map;

public class RmxMoInfo {
    private String no; // 序号
    private String expectedDate; // 预计完成日期
    private String moNumber; //订单号
    private String material; // 成品型号
    private String materialType; //成品类型描述
    private int unitFunctionQty; //每个成品的功能数
    private int openQty; //未完成的成品数量
    private int totalOpenFunctionQty; //订单未完成的功能数

    // 以下是辅助信息
    private int seqNo;
    private String tips;
    private String scheduleShift = "-1"; // 对应排产的第几个班次
    private int scheduleShiftCapacity = -1; // 当前班组产能
    private String scheduleDay = "-1"; // 对应排产的第几天
    private int scheduleDayCapacity; // scheduleDay对应的capacity
    private static final SimpleDateFormat formater = new SimpleDateFormat("yyyy/MM/dd");

    public int getSeqNo() {
        return seqNo;
    }

    public void setSeqNo(int seqNo) {
        this.seqNo = seqNo;
    }

    public String getMoNumber() {
        return moNumber;
    }

    public void setMoNumber(String moNumber) {
        this.moNumber = moNumber;
    }

    public String getMaterialType() {
        return materialType;
    }

    public void setMaterialType(String materialType) {
        this.materialType = materialType;
    }

    public int getUnitFunctionQty() {
        return unitFunctionQty;
    }

    public void setUnitFunctionQty(int unitFunctionQty) {
        this.unitFunctionQty = unitFunctionQty;
    }


    public int getOpenQty() {
        return openQty;
    }

    public void setOpenQty(int openQty) {
        this.openQty = openQty;
    }

    public int getTotalOpenFunctionQty() {
        return totalOpenFunctionQty;
    }

    public void setTotalOpenFunctionQty(int totalOpenFunctionQty) {
        this.totalOpenFunctionQty = totalOpenFunctionQty;
    }

    public String getTips() {
        return tips;
    }

    public void setTips(String tips) {
        this.tips = tips;
    }

    public int getScheduleDayCapacity() {
        return scheduleDayCapacity;
    }

    public void setScheduleDayCapacity(int scheduleDayCapacity) {
        this.scheduleDayCapacity = scheduleDayCapacity;
    }

    public int getScheduleShiftCapacity() {
        return scheduleShiftCapacity;
    }

    public void setScheduleShiftCapacity(int scheduleShiftCapacity) {
        this.scheduleShiftCapacity = scheduleShiftCapacity;
    }

    public String getScheduleShift() {
        return scheduleShift;
    }

    public void setScheduleShift(String scheduleShift) {
        this.scheduleShift = scheduleShift;
    }

    public String getScheduleDay() {
        return scheduleDay;
    }

    public void setScheduleDay(String scheduleDay) {
        this.scheduleDay = scheduleDay;
    }

    public String getMaterial() {
        return material;
    }

    public void setMaterial(String material) {
        this.material = material;
    }

    public String getNo() {
        return no;
    }

    public void setNo(String no) {
        this.no = no;
    }

    public String getExpectedDate() {
        return expectedDate;
    }

    public void setExpectedDate(String expectedDate) {
        this.expectedDate = expectedDate;
    }

    public String getInfo() {
        Map<String, Object> map = new LinkedHashMap<>();
        map.put("mo", this.moNumber);
        map.put("unit_function_qty", this.unitFunctionQty);
        map.put("total_open_func", this.totalOpenFunctionQty);
        map.put("schedule_day", this.getScheduleDay());
        map.put("schedule_shift", this.getScheduleShift());
        map.put("qty", this.getOpenQty());
        map.put("tips", this.getTips());
//        map.put("next_day", this.getNextDayCapacity());
//        map.put("schedule_day_capacity", this.getScheduleDayCapacity());
//        map.put("schedule_day_acc_capacity", this.getScheduleDayAccumulatedCapacity());
        return JSON.toJSONString(map);
    }
}
