package com.scp.simulation.bean;

import com.starter.utils.Utils;
import org.apache.commons.lang3.StringUtils;

public class SubstitutionInput {
    private String materialInBom;
    private String followUpMaterial;


    public String getMaterialInBom() {
        return materialInBom;
    }

    public void setMaterialInBom(String materialInBom) {
        this.materialInBom = materialInBom;
    }

    public String getFollowUpMaterial() {
        return followUpMaterial;
    }

    public void setFollowUpMaterial(String followUpMaterial) {
        this.followUpMaterial = followUpMaterial;
    }
}
