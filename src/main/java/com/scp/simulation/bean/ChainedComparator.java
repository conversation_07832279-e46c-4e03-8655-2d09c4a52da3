package com.scp.simulation.bean;

import java.math.BigDecimal;
import java.util.Comparator;

public abstract class ChainedComparator<T> implements Comparator<T> {

    private ChainedComparator<T> next;
    private String order = "ASC";

    @Override
    public int compare(T o1, T o2) {
        int result = this.doCompare(o1, o2);
        if (result == 0) {
            if (getNext() != null) {
                return getNext().compare(o1, o2);
            }
        }

        return result;
    }

    public abstract int doCompare(T o1, T o2);

    public boolean isAscending() {
        return "ASC".equals(order);
    }

    public int compareObj(Object o1, Object o2) {
        if (o1 instanceof String && o2 instanceof String) {
            if (this.isAscending()) {
                return ((String) o1).compareTo((String) o2);
            } else {
                return ((String) o2).compareTo((String) o1);
            }
        } else if (o1 instanceof BigDecimal && o2 instanceof BigDecimal) {
            if (this.isAscending()) {
                return ((BigDecimal) o1).compareTo((BigDecimal) o2);
            } else {
                return ((BigDecimal) o2).compareTo((BigDecimal) o1);
            }
        } else if (o1 instanceof Integer && o2 instanceof Integer) {
            if (this.isAscending()) {
                return ((Integer) o1).compareTo((Integer) o2);
            } else {
                return ((Integer) o2).compareTo((Integer) o1);
            }
        } else if (o1 instanceof Double && o2 instanceof Double) {
            if (this.isAscending()) {
                return ((Double) o1).compareTo((Double) o2);
            } else {
                return ((Double) o2).compareTo((Double) o1);
            }
        } else {
            if (this.isAscending()) {
                return String.valueOf(o1).compareTo(String.valueOf(o2));
            } else {
                return String.valueOf(o2).compareTo(String.valueOf(o1));
            }
        }
    }

    public Comparator<T> getNext() {
        return next;
    }

    public ChainedComparator<T> setNext(ChainedComparator<T> next) {
        this.next = next;
        return next;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }
}
