package com.scp.simulation;

import com.scp.simulation.service.IPlantDemandSupplyService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.util.WebUtils;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/simulation/plant_demand_supply", parent = "menuB10")
public class PlantDemandSupplyController extends ControllerHelper {

    @Resource
    private Response response;

    @Resource
    private IPlantDemandSupplyService plantDemandSupplyService;

    @SchneiderRequestMapping("/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        return plantDemandSupplyService.initPage();
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        return plantDemandSupplyService.queryReport1(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping("/query_report1_details")
    public Response queryReport1Details(HttpServletRequest request) {
        super.pageLoad(request);
        return plantDemandSupplyService.queryReport1Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report1_details")
    public void downloadReport1Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        plantDemandSupplyService.downloadReport1Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report1_history_comments")
    public Response queryReport1HistoryComments(HttpServletRequest request) {
        super.pageLoad(request);
        return plantDemandSupplyService.queryReport1HistoryComments(parameterMap);
    }

    @SchneiderRequestMapping("/download_report1_history_comments")
    public void downloadReport1HistoryComments(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        plantDemandSupplyService.downloadReport1HistoryComments(parameterMap, response);
    }

    @SchneiderRequestMapping("/save_report1_comments")
    public Response saveReport1Comments(HttpServletRequest request) {
        super.pageLoad(request);
        return plantDemandSupplyService.saveReport1Comments(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping("/query_report1_critical_level")
    public Response queryReport1CriticalLevel(HttpServletRequest request) {
        super.pageLoad(request);
        return plantDemandSupplyService.queryReport1CriticalLevel(parameterMap);
    }

    @SchneiderRequestMapping("/query_report3")
    public Response queryReport3(HttpServletRequest request) {
        super.pageLoad(request);
        return plantDemandSupplyService.queryReport3(parameterMap);
    }

    @SchneiderRequestMapping("/download_report3")
    public void downloadReport3(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        plantDemandSupplyService.downloadReport3(parameterMap, response);
    }

    @SchneiderRequestMapping("/save_report3")
    public Response saveReport3(HttpServletRequest request) {
        super.pageLoad(request);
        return plantDemandSupplyService.saveReport3(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping("/upload_report3")
    public Response uploadReport3(HttpServletRequest request) {
        super.pageLoad(request);
        MultipartHttpServletRequest multipartRequest = WebUtils.getNativeRequest(request, MultipartHttpServletRequest.class);
        if (multipartRequest != null) {
            MultipartFile file = multipartRequest.getFile("file");
            try {
                return plantDemandSupplyService.uploadReport3(session.getUserid(), file);
            } catch (Exception e) {
                return response.setError(e);
            }
        } else {
            return response.setError(new Exception("upload failed, multipartRequest is null !"));
        }
    }

    @SchneiderRequestMapping("/download_report3_template")
    public void downloadReport3Template(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        plantDemandSupplyService.downloadReport3Template(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report4")
    public Response queryReport4(HttpServletRequest request) {
        super.pageLoad(request);
        return plantDemandSupplyService.queryReport4(parameterMap);
    }

    @SchneiderRequestMapping("/download_report4")
    public void downloadReport4(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        plantDemandSupplyService.downloadReport4(parameterMap, response);
    }

    @SchneiderRequestMapping("/save_report4")
    public Response saveReport4(HttpServletRequest request) {
        super.pageLoad(request);
        return plantDemandSupplyService.saveReport4(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping("/upload_report4")
    public Response uploadReport4(HttpServletRequest request) {
        super.pageLoad(request);
        MultipartHttpServletRequest multipartRequest = WebUtils.getNativeRequest(request, MultipartHttpServletRequest.class);
        if (multipartRequest != null) {
            MultipartFile file = multipartRequest.getFile("file");
            try {
                return plantDemandSupplyService.uploadReport4(session.getUserid(), file);
            } catch (Exception e) {
                return response.setError(e);
            }
        } else {
            return response.setError(new Exception("upload failed, multipartRequest is null !"));
        }
    }

    @SchneiderRequestMapping("/download_report4_template")
    public void downloadReport4Template(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        plantDemandSupplyService.downloadReport4Template(parameterMap, response);
    }
}
