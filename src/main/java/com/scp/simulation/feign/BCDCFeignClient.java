package com.scp.simulation.feign;

import com.scp.simulation.bean.BestCanDoCalculatorParam;
import com.scp.simulation.bean.BestCanDoParam;
import com.starter.context.bean.Response;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(name = "BCDCFeignClient", url = BCDCFeignClient.BCDC_FEIGN_CLIENT_URL)
public interface BCDCFeignClient {
    //String BCDC_FEIGN_CLIENT_URL = "http://10.177.21.11:4567";
    String BCDC_FEIGN_CLIENT_URL = "http://127.0.0.1:4567";

    @PostMapping(value = "/scpdss/bcdc")
    Response execute(BestCanDoCalculatorParam params);
}
