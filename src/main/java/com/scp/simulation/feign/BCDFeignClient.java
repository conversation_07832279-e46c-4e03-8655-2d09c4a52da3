package com.scp.simulation.feign;

import com.starter.context.bean.Response;
import com.scp.simulation.bean.BestCanDoParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(name = "BCDFeignClient", url = BCDFeignClient.BCD_FEIGN_CLIENT_URL)
public interface BCDFeignClient {
    String BCD_FEIGN_CLIENT_URL = "http://10.177.21.11:45678";

    @PostMapping(value = "/scpdss/bcd")
    Response execute(BestCanDoParam params);
}
