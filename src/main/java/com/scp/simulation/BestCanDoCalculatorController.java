package com.scp.simulation;

import com.scp.simulation.service.IBestCanDoCalculatorService;

import com.scp.simulation.service.impl.BestCanDoCalculatorServiceImpl;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.util.WebUtils;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/simulation/best_can_do_calculator", parent = BestCanDoCalculatorServiceImpl.PARENT_CODE)
public class BestCanDoCalculatorController extends ControllerHelper {

    @Resource
    private IBestCanDoCalculatorService bestCanDoCalculatorService;

    private Response res;
    @Resource
    private Response response;

    @SchneiderRequestMapping("/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.initPage(session.getUserid());
    }

    @SchneiderRequestMapping("/init_exists_group")
    public Response initExistsGroup(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.initExistsGroup(session.getUserid());
    }

    @SchneiderRequestMapping("/query_batch_list")
    public Response queryBatchList(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.queryBatchList(session.getUserid());
    }

    @SchneiderRequestMapping("/query_batch_info")
    public Response queryBatchInfo(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.queryBatchInfo(parameterMap);
    }

    @SchneiderRequestMapping("/query_task_info")
    public Response queryTaskInfo(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.queryTaskInfo(session.getUserid());
    }

    @SchneiderRequestMapping("/query_execute_logs")
    public Response queryExecuteLogs(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.queryExecuteLogs(parameterMap);
    }

    @SchneiderRequestMapping("/save_new_batch")
    public Response saveNewBatch(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.saveNewBatch(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping("/delete_batch")
    public Response deleteBatch(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.deleteBatch(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping("/send_simulate_request")
    public Response sendSimulateRequest(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.sendSimulateRequest(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping("/download_report1")
    public void downloadReport1(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoCalculatorService.downloadReport1(parameterMap, response);
    }

    @SchneiderRequestMapping("/save_report1")
    public Response saveReport1(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.saveReport1(parameterMap);
    }

    @SchneiderRequestMapping("/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.queryReport2(parameterMap);
    }

    @SchneiderRequestMapping("/download_report2")
    public void downloadReport2(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoCalculatorService.downloadReport2(parameterMap, response);
    }

    @SchneiderRequestMapping("/save_report2")
    public Response saveReport2(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.saveReport2(parameterMap);
    }

    @SchneiderRequestMapping("/query_report11")
    public Response queryReport11(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.queryReport11(parameterMap);
    }

    @SchneiderRequestMapping("/download_report11")
    public void downloadReport11(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoCalculatorService.downloadReport11(parameterMap, response);
    }

    @SchneiderRequestMapping("/save_report11")
    public Response saveReport11(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.saveReport11(parameterMap);
    }

    @SchneiderRequestMapping("/query_report3")
    public Response queryReport3(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.queryReport3(parameterMap);
    }

    @SchneiderRequestMapping("/download_report3")
    public void downloadReport3(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoCalculatorService.downloadReport3(parameterMap, response);
    }

    @SchneiderRequestMapping("/save_report3")
    public Response saveReport3(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.saveReport3(parameterMap);
    }

    @SchneiderRequestMapping("/query_report9")
    public Response queryReport9(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.queryReport9(parameterMap);
    }

    @SchneiderRequestMapping("/download_report9")
    public void downloadReport9(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoCalculatorService.downloadReport9(parameterMap, response);
    }

    @SchneiderRequestMapping("/save_report9")
    public Response saveReport9(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.saveReport9(parameterMap);
    }

    @SchneiderRequestMapping("/download_report7")
    public void downloadReport7(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoCalculatorService.downloadReport7(parameterMap, response);
    }

    @SchneiderRequestMapping("/save_report7")
    public Response saveReport7(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.saveReport7(parameterMap);
    }

    @SchneiderRequestMapping("/download_report1_template")
    public void downloadReport1Template(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoCalculatorService.downloadReport1Template(parameterMap, response);
    }

    @SchneiderRequestMapping("/download_report2_template")
    public void downloadReport2Template(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoCalculatorService.downloadReport2Template(parameterMap, response);
    }

    @SchneiderRequestMapping("/download_report3_template")
    public void downloadReport3Template(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoCalculatorService.downloadReport3Template(parameterMap, response);
    }

    @SchneiderRequestMapping("/download_report9_template")
    public void downloadReport9Template(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoCalculatorService.downloadReport9Template(parameterMap, response);
    }


    @SchneiderRequestMapping("/download_report7_template")
    public void downloadReport7Template(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoCalculatorService.downloadReport7Template(parameterMap, response);
    }


    @SchneiderRequestMapping("/upload_report1")
    public Response uploadReport1(HttpServletRequest request) {
        super.pageLoad(request);
        MultipartHttpServletRequest multipartRequest = WebUtils.getNativeRequest(request, MultipartHttpServletRequest.class);
        if (multipartRequest != null) {
            MultipartFile file = multipartRequest.getFile("file");
            try {
                return bestCanDoCalculatorService.uploadReport1((String) parameterMap.get("batchId"), file);
            } catch (Exception e) {
                return response.setError(e);
            }
        } else {
            return response.setError(new Exception("upload failed, multipartRequest is null !"));
        }
    }

    @SchneiderRequestMapping("/upload_report2")
    public Response uploadReport2(HttpServletRequest request) {
        super.pageLoad(request);
        MultipartHttpServletRequest multipartRequest = WebUtils.getNativeRequest(request, MultipartHttpServletRequest.class);
        if (multipartRequest != null) {
            MultipartFile file = multipartRequest.getFile("file");
            try {
                return bestCanDoCalculatorService.uploadReport2((String) parameterMap.get("batchId"), file);
            } catch (Exception e) {
                return response.setError(e);
            }
        } else {
            return response.setError(new Exception("upload failed, multipartRequest is null !"));
        }
    }

    @SchneiderRequestMapping("/upload_report3")
    public Response uploadReport3(HttpServletRequest request) {
        super.pageLoad(request);
        MultipartHttpServletRequest multipartRequest = WebUtils.getNativeRequest(request, MultipartHttpServletRequest.class);
        if (multipartRequest != null) {
            MultipartFile file = multipartRequest.getFile("file");
            try {
                return bestCanDoCalculatorService.uploadReport3((String) parameterMap.get("batchId"), file);
            } catch (Exception e) {
                return response.setError(e);
            }
        } else {
            return response.setError(new Exception("upload failed, multipartRequest is null !"));
        }
    }

    @SchneiderRequestMapping("/upload_report9")
    public Response uploadReport9(HttpServletRequest request) {
        super.pageLoad(request);
        MultipartHttpServletRequest multipartRequest = WebUtils.getNativeRequest(request, MultipartHttpServletRequest.class);
        if (multipartRequest != null) {
            MultipartFile file = multipartRequest.getFile("file");
            try {
                return bestCanDoCalculatorService.uploadReport9((String) parameterMap.get("batchId"), file);
            } catch (Exception e) {
                return response.setError(e);
            }
        } else {
            return response.setError(new Exception("upload failed, multipartRequest is null !"));
        }
    }

    @SchneiderRequestMapping("/upload_report7")
    public Response uploadReport7(HttpServletRequest request) {
        super.pageLoad(request);
        MultipartHttpServletRequest multipartRequest = WebUtils.getNativeRequest(request, MultipartHttpServletRequest.class);
        if (multipartRequest != null) {
            MultipartFile file = multipartRequest.getFile("file");
            try {
                return bestCanDoCalculatorService.uploadReport7((String) parameterMap.get("batchId"), file);
            } catch (Exception e) {
                return response.setError(e);
            }
        } else {
            return response.setError(new Exception("upload failed, multipartRequest is null !"));
        }
    }

    @SchneiderRequestMapping("/query_task_queue")
    public Response queryTaskQueue(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.queryTaskQueue(parameterMap);
    }

    @SchneiderRequestMapping("/query_report4")
    public Response queryReport4(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.queryReport4(parameterMap);
    }

    @SchneiderRequestMapping("/download_report4")
    public void downloadReport4(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoCalculatorService.downloadReport4(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report5")
    public Response queryReport5(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.queryReport5(parameterMap);
    }

    @SchneiderRequestMapping("/download_report5")
    public void downloadReport5(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoCalculatorService.downloadReport5(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report10")
    public Response queryReport10(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.queryReport10(parameterMap);
    }

    @SchneiderRequestMapping("/download_report10")
    public void downloadReport10(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoCalculatorService.downloadReport10(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report8")
    public Response queryReport8(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.queryReport8(parameterMap);
    }

    @SchneiderRequestMapping("/download_report8")
    public void downloadReport8(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoCalculatorService.downloadReport8(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report6")
    public Response queryReport6(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.queryReport6(parameterMap);
    }

    @SchneiderRequestMapping("/download_report6")
    public void downloadReport6(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoCalculatorService.downloadReport6(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report7")
    public Response queryReport7(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.queryReport7(parameterMap);
    }

    @SchneiderRequestMapping("/compare_report_1")
    public Response compareReport1(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.compareReport1(parameterMap);
    }

    @SchneiderRequestMapping("/compare_report_1_details")
    public Response compareReport1Details(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.compareReport1Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_compare_report_1_details")
    public void downloadCompareReport1Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoCalculatorService.downloadCompareReport1Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/compare_report_2")
    public Response compareReport2(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        try {
            return bestCanDoCalculatorService.compareReport2(parameterMap);
        } catch (Exception e) {
            return res.setError(e);
        }
    }

    @SchneiderRequestMapping("/compare_report_2_details")
    public Response compareReport2Details(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.compareReport2Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_compare_report_2_details")
    public void downloadCompareReport2Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoCalculatorService.downloadCompareReport2Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/compare_report_3")
    public Response compareReport3(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.compareReport3(parameterMap);
    }

    @SchneiderRequestMapping("/compare_report_3_details")
    public Response compareReport3Details(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.compareReport3Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_compare_report_3_details")
    public void downloadCompareReport3Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoCalculatorService.downloadCompareReport3Details(parameterMap, response);
    }


    @SchneiderRequestMapping("/compare_report_4_left")
    public Response compareReport4Left(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.compareReport4Left(parameterMap);
    }

    @SchneiderRequestMapping("/compare_report_4_right")
    public Response compareReport4Right(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.compareReport4Right(parameterMap);
    }

    @SchneiderRequestMapping("/compare_report_4_details")
    public Response compareReport4Details(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.compareReport4Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_compare_report_4_details")
    public void downloadCompareReport4Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoCalculatorService.downloadCompareReport4Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/compare_report_overview")
    public Response compareReportOverview(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.compareReportOverview(parameterMap);
    }

    @SchneiderRequestMapping("/compare_overview_details")
    public Response compareReportOverviewDetails(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.compareOverviewDetails(parameterMap);
    }

    @SchneiderRequestMapping("/download_compare_overview_details")
    public void downloadCompareReportOverviewDetails(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoCalculatorService.downloadCompareOverviewDetails(parameterMap, response);
    }

    @SchneiderRequestMapping(value = "/share_condition")
    public Response shareCondition(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoCalculatorService.shareCondition(session.getUserid(), session.getUsername(), session.getEmail(), parameterMap);
    }
}
