package com.scp.simulation.service;

import com.starter.context.bean.Response;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;

public interface IBestCanDoService {

    Response initPage(String userid);

    Response saveNewBatch(Map<String, Object> parameterMap, String userid);

    Response queryReport1(Map<String, Object> parameterMap);

    Response queryReport2(Map<String, Object> parameterMap);

    Response queryExecuteLogs(Map<String, Object> parameterMap);

    Response queryBatchList(String userid);

    Response deleteBatch(Map<String, Object> parameterMap, String userid);

    Response sendSimulateRequest(Map<String, Object> parameterMap, String userid);

    void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadReport2(Map<String, Object> parameterMap, HttpServletResponse response);

    Response saveReport1(Map<String, Object> parameterMap);

    Response saveReport2(Map<String, Object> parameterMap);

    void downloadReport1Template(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadReport2Template(Map<String, Object> parameterMap, HttpServletResponse response);

    Response uploadReport1(String batchId, MultipartFile file) throws Exception;

    Response uploadReport2(String batchId, MultipartFile file) throws Exception;

    Response queryTaskQueue(Map<String, Object> parameterMap);

    Response queryReport4(Map<String, Object> parameterMap);

    Response queryReport5(Map<String, Object> parameterMap);

    Response queryReport6(Map<String, Object> parameterMap);

    void downloadReport4(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadReport5(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadReport6(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryBatchInfo(Map<String, Object> parameterMap);

    Response initExistsGroup(String userid);

    Response queryTaskInfo(String userid);

    Response compareReport1(Map<String, Object> parameterMap);

    Response compareReport1Details(Map<String, Object> parameterMap);

    void downloadCompareReport1Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response compareReport2(Map<String, Object> parameterMap) throws Exception;

    Response compareReport2Details(Map<String, Object> parameterMap);

    void downloadCompareReport2Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response compareReport3(Map<String, Object> parameterMap);

    Response compareReport3Details(Map<String, Object> parameterMap);

    void downloadCompareReport3Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response compareReport4Left(Map<String, Object> parameterMap);

    Response compareReport4Right(Map<String, Object> parameterMap);

    Response compareReport4Details(Map<String, Object> parameterMap);

    void downloadCompareReport4Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response compareReportOverview(Map<String, Object> parameterMap);

    Response compareOverviewDetails(Map<String, Object> parameterMap);

    void downloadCompareOverviewDetails(Map<String, Object> parameterMap, HttpServletResponse response);

}
