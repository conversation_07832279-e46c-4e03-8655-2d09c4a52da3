package com.scp.simulation.service;

import com.starter.context.bean.Response;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

public interface IRMXSchedulerService {

    Response initPage(Map<String, Object> parameterMap);

    Response queryReport1(Map<String, Object> parameterMap);

    Response queryReport1StartSchedule(Map<String, Object> parameterMap);

    Response uploadReport1(MultipartFile file);

    Response queryReport1Result(Map<String, Object> parameterMap);

    void downloadReport1Result(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport2(Map<String, Object> parameterMap);

    Response queryReport2DayCapacity(Map<String, Object> parameterMap);

    Response updateReport2DayCapacity(Map<String, Object> parameterMap);

    void downloadReport1UploadTemplate(Map<String, Object> parameterMap, HttpServletResponse response);
}
