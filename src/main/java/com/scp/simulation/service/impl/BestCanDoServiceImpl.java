package com.scp.simulation.service.impl;

import com.adm.system.dao.ISystemDao;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.scp.simulation.bean.*;
import com.starter.context.bean.Configuration;
import com.starter.utils.DateCalUtil;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import com.starter.utils.excel.SimpleSheetContentsHandler;
import com.starter.context.bean.Message;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.bean.scptable.ScpTableHelper;
import com.starter.context.configuration.MqttConfiguration;
import com.scp.simulation.dao.IBestCanDoDao;
import com.scp.simulation.feign.BCDFeignClient;
import com.scp.simulation.service.IBestCanDoService;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.model.StylesTable;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.io.File;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service("bestCanDoServiceImpl")
@Scope("prototype")
@Transactional
public class BestCanDoServiceImpl implements IBestCanDoService {

    @Resource
    private Response response;

    @Resource
    private IBestCanDoDao bestCanDoDao;

    @Resource
    private ExcelTemplate excelTemplate;

    @Resource
    private BCDFeignClient bcdFeignClient;

    @Resource
    private ScpTableHelper scpTableHelper;

    private final static SimpleDateFormat DEFAULT_DATE_FORMAT = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");

    public final static String PARENT_CODE = "menuB20";

    @Override
    public Response initPage(String userid) {
        Map<String, Object> resultMap = new HashMap<>();
        List<Map<String, String>> filterList = bestCanDoDao.queryFilterList();
        List<Map<String, String>> moList = new ArrayList<>();
        List<Map<String, String>> soList = new ArrayList<>();
        List<Map<String, String>> poList = new ArrayList<>();

        for (Map<String, String> map : filterList) {
            if ("MO".equals(map.get("TYPE"))) {
                moList.add(map);
            } else if ("SO".equals(map.get("TYPE"))) {
                soList.add(map);
            } else if ("PO".equals(map.get("TYPE"))) {
                poList.add(map);
            }
        }

        resultMap.put("moList", Utils.parseCascader(moList, false));
        resultMap.put("soList", Utils.parseCascader(soList, false));
        resultMap.put("poList", Utils.parseCascader(poList, false));
        resultMap.put("plantList", bestCanDoDao.queryAvailablePlants());
        return response.setBody(resultMap);
    }

    @Override
    public Response queryBatchList(String userid) {
        String authType = StringUtils.upperCase(StringUtils.trim(bestCanDoDao.queryAuthDetails(userid, PARENT_CODE)));
        return response.setBody(Utils.parseTreeNodes(bestCanDoDao.queryUserBatchList(userid, authType)));
    }

    @Override
    public Response queryBatchInfo(Map<String, Object> parameterMap) {
        Map<String, Object> info = bestCanDoDao.queryBatchInfo(parameterMap);
        JSONObject params = JSONObject.parseObject((String) info.get("PARAMS"));
        Map<String, Object> runInfo = new HashMap<>();
        runInfo.put("step", info.get("STEP"));
        runInfo.put("stepMode", info.get("STEP_MODE"));
        params.put("_runInfo", runInfo);

        info.put("PARAMS", JSONObject.toJSONString(params, true));
        return response.setBody(info);
    }

    @Override
    public Response initExistsGroup(String userid) {
        String authType = StringUtils.upperCase(StringUtils.trim(bestCanDoDao.queryAuthDetails(userid, PARENT_CODE)));
        return response.setBody(bestCanDoDao.initExistsGroup(userid, authType));
    }

    @Override
    public Response queryTaskInfo(String userid) {
        Map<String, Object> resultMap = new HashMap<>();
        String authType = StringUtils.upperCase(StringUtils.trim(bestCanDoDao.queryAuthDetails(userid, PARENT_CODE)));
        List<Map<String, String>> result = bestCanDoDao.queryTaskInfo(userid, authType);
        List<Map<String, String>> nameList = new ArrayList<>();
        Map<String, Object> taskInfo = new HashMap<>();

        for (Map<String, String> map : result) {
            nameList.add(map);
            taskInfo.put(map.get("BATCH_ID"), map.get("PARAMS"));
        }
        resultMap.put("nameList", Utils.parseCascader(nameList, false));
        resultMap.put("taskInfo", taskInfo);

        return response.setBody(resultMap);
    }

    @Override
    public Response deleteBatch(Map<String, Object> parameterMap, String userid) {
        parameterMap.put("userid", userid);
        if (bestCanDoDao.queryBatchCount(parameterMap) > 0) {
            bestCanDoDao.deleteBatch(parameterMap);
            response.setBody(0);
        } else {
            response.setBody(-1);
        }

        return response;
    }

    @Override
    public Response sendSimulateRequest(Map<String, Object> parameterMap, String userid) {
        parameterMap.put("userid", userid);
        // 检查任务是否存在, 任务是否处于可运行的状态
        if (bestCanDoDao.querySimulateCount(parameterMap) > 0) {
            // 检查目前有多少任务正在执行, 暂定8个
            int executeCnt = bestCanDoDao.queryExecutingCount(parameterMap);
            if (executeCnt > 8) {
                response.setBody("No more tasks can be submitted at this time(" + executeCnt + " tasks simulating)");
            }

            bestCanDoDao.updateSimulateStep(parameterMap);

            int step = Utils.parseInt(parameterMap.get("step"));
            String stepMode = (String) parameterMap.get("stepMode");
            String batchId = (String) parameterMap.get("batchId");
            String params = bestCanDoDao.queryBatchParams(batchId);
            JSONObject jsonObject = JSONObject.parseObject(params);
            String plant = jsonObject.getString("plant");
            String mode = jsonObject.getString("mode");
            String name = jsonObject.getString("name");
            Boolean resBlock = jsonObject.getBoolean("resBlock");
            String soMode = jsonObject.getString("soMode");

            JSONArray stockConfig = jsonObject.getJSONArray("stockConfig");
            // 分批出货的时候, 回传给求解器的DEMAND_BASE应该是MAT

            Map<String, Object> logMap = new HashMap<>();
            logMap.put("t", step);
            logMap.put("plant", plant);
            logMap.put("batch_id", batchId);

            this.sendLog(batchId, userid, "sending request to ML module, " + JSON.toJSONString(logMap));
            try {
                BestCanDoParam param = new BestCanDoParam();
                param.setTask_name(name);
                param.setUser_id(userid);
                param.setBatch_id(batchId);
                param.setPlant_code(plant);
                param.setDemand_base(mode);
                param.setStep(step);
                param.setStep_mode(stepMode);
                param.syncStockConfig(stockConfig);
                param.setResource_block(resBlock);
                param.setVar_resolution(!"BY_MATERIAL".equals(soMode));
                Response rep = bcdFeignClient.execute(param);
                this.sendLog(batchId, userid, "request sent, " + JSON.toJSONString(rep));
            } catch (Exception e) {
                this.sendLog(batchId, userid, "request sent failed, " + e.getMessage());
            }
            response.setBody("0");
        } else {
            response.setBody("任务已经执行完成, 无法重复计算");
        }
        return response;
    }

    @Override
    public Response saveNewBatch(Map<String, Object> parameterMap, String userid) {
        this.generateNewBatchCascaderFilter(parameterMap, "priority");
        this.generateNewBatchCascaderFilter(parameterMap, "po");

        // create batch id
        String batchID = Utils.randomStr(12);
        String mode = (String) parameterMap.get("mode");

        Map<String, Object> params = new HashMap<>();
        params.put("name", parameterMap.get("name"));
        params.put("groups", parameterMap.get("groups"));
        params.put("plant", parameterMap.get("plant"));
        params.put("mode", parameterMap.get("mode"));
        params.put("soMode", parameterMap.get("soMode"));
        params.put("dateRange", parameterMap.get("dateRange"));
        params.put("priority", parameterMap.get("priority"));
        params.put("po", parameterMap.get("po"));
        params.put("stockConfig", parameterMap.get("stockConfig"));
        params.put("resBlock", parameterMap.get("resBlock"));
        params.put("step", parameterMap.get("step"));
        params.put("step_mode", parameterMap.get("step_mode"));

        parameterMap.put("params", JSON.toJSONString(params));
        parameterMap.put("batch_id", batchID);
        parameterMap.put("userid", userid);

        bestCanDoDao.insertLog(parameterMap);
        this.sendLog(batchID, userid, "new task lauched, task mode: " + mode + ", batch id: " + batchID);

        if ("MO".equals(mode)) {
            int cnt = bestCanDoDao.copyMOData(parameterMap);
            this.sendLog(batchID, userid, (Utils.thousandBitSeparator(String.valueOf(cnt)) + (cnt > 1 ? " lines" : " line")) + " mo copied from bcd_mo_data_v to bcd_input_priority");
        } else if ("SO".equals(mode)) {
            int cnt = bestCanDoDao.copySOData(parameterMap);
            this.sendLog(batchID, userid, (Utils.thousandBitSeparator(String.valueOf(cnt)) + (cnt > 1 ? " lines" : " line")) + " so copied from demand_backlog_v to bcd_input_priority");
        } else if ("MANUAL".equals(mode)) {
            this.sendLog(batchID, userid, "no data was copied in manual mode");
        }
        int cnt = bestCanDoDao.copyPOData(parameterMap);
        this.sendLog(batchID, userid, (Utils.thousandBitSeparator(String.valueOf(cnt)) + (cnt > 1 ? " lines" : " line")) + " po copied from po_management_abla_overdue_v to bcd_input_po");
        return response.setBody(batchID);
    }

    @Override
    public Response queryReport1(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(bestCanDoDao.queryReport1Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(bestCanDoDao.queryReport1(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "priority_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.simulation.dao.IBestCanDoDao.downloadReport1", parameterMap);
    }

    @Override
    public Response saveReport1(Map<String, Object> parameterMap) {
        String batchId = (String) parameterMap.get("batchId");
        scpTableHelper.setScpTableInsertHandler((headers, creates) -> {
            if (headers.contains("ORDER_NUMBER") == false) {
                headers.add("ORDER_NUMBER");

            }
            for (Map<String, Object> map : creates) {
                if (StringUtils.isBlank((String) map.get("ORDER_NUMBER"))) {
                    map.put("ORDER_NUMBER", Utils.randomStr(16));
                }
            }

            return bestCanDoDao.createReport1ByTable(headers, creates, batchId);
        });
        scpTableHelper.setScpTableDeleteHandler(deletes -> bestCanDoDao.deleteReport1ByTable(deletes, batchId));
        scpTableHelper.setScpTableUpdateHandler((pk, updates) -> bestCanDoDao.updateReport1ByTable(pk, updates, batchId));
        Message message = scpTableHelper.execCRUD(parameterMap);
        return response.setBody(message);
    }

    @Override
    public Response queryReport2(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(bestCanDoDao.queryReport2Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(bestCanDoDao.queryReport2(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport2(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "po_delivery_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.simulation.dao.IBestCanDoDao.downloadReport2", parameterMap);
    }

    @Override
    public Response saveReport2(Map<String, Object> parameterMap) {
        String batchId = (String) parameterMap.get("batchId");
        scpTableHelper.setScpTableInsertHandler((headers, creates) -> bestCanDoDao.createReport2ByTable(headers, creates, batchId));
        scpTableHelper.setScpTableDeleteHandler(deletes -> bestCanDoDao.deleteReport2ByTable(deletes, batchId));
        scpTableHelper.setScpTableUpdateHandler((pk, updates) -> bestCanDoDao.updateReport2ByTable(pk, updates, batchId));
        Message message = scpTableHelper.execCRUD(parameterMap);
        return response.setBody(message);
    }

    @Override
    public void downloadReport1Template(Map<String, Object> parameterMap, HttpServletResponse response) {
        String fileName = "mo_so_priority_template.xlsx";
        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        resultList.add(map);
        map.put("ORDER_NUMBER", null);
        map.put("PLANT_CODE", null);
        map.put("MATERIAL", null);
        map.put("QTY", null);
        map.put("PRIORITY_DATE", null);
        map.put("REQUEST_DATE", null);

        excelTemplate.create(response, fileName, resultList);
    }

    @Override
    public void downloadReport2Template(Map<String, Object> parameterMap, HttpServletResponse response) {
        String fileName = "po_delivery_template.xlsx";
        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        resultList.add(map);
        map.put("PLANT_CODE", null);
        map.put("MATERIAL", null);
        map.put("CONFIRM_CAT", null);
        map.put("ETA_DATE", null);
        map.put("QTY", null);
        excelTemplate.create(response, fileName, resultList);
    }

    @Override
    public Response uploadReport1(String batchId, MultipartFile file) throws Exception {
        File tempFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), ".tmp");
        file.transferTo(tempFile);

        List<SOMOPriority> data = new ArrayList<>();
        bestCanDoDao.deleteReport1Data(batchId);
        excelTemplate.read(tempFile, 1, new SimpleSheetContentsHandler() {
            @Override
            public void handleRow(int rowNum) {

                if (rowNum == 0) {
                    return;
                }

                int i = 0;
                SOMOPriority bean = new SOMOPriority();
                bean.setOrderNumber(row.get(i++));
                bean.setPlantCode(row.get(i++));
                bean.setMaterial(row.get(i++));
                bean.setQty(Utils.parseDouble(row.get(i++)));
                bean.setPriorityDate(DateCalUtil.excelNumber2Day(row.get(i++)));
                bean.setRequestDate(DateCalUtil.excelNumber2Day(row.get(i)));
                data.add(bean);

                if (data.size() >= 1000) {
                    bestCanDoDao.insertReport1Data(batchId, data);
                    data.clear();
                }
            }
        }, new StylesTable());

        bestCanDoDao.insertReport1Data(batchId, data);
        return response;
    }

    @Override
    public Response uploadReport2(String batchId, MultipartFile file) throws Exception {
        File tempFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), ".tmp");
        file.transferTo(tempFile);

        List<PODelivery> data = new ArrayList<>();
        bestCanDoDao.deleteReport2Data(batchId);
        excelTemplate.read(tempFile, 1, new SimpleSheetContentsHandler() {
            @Override
            public void handleRow(int rowNum) {

                if (rowNum == 0) {
                    return;
                }

                int i = 0;
                PODelivery bean = new PODelivery();
                bean.setPlantCode(row.get(i++));
                bean.setMaterial(row.get(i++));
                bean.setQty(Double.parseDouble(row.get(i++)));
                bean.setConfirmCat(row.get(i++));
                bean.setEtaDate(DateCalUtil.excelNumber2Day(row.get(i++)));
                data.add(bean);

                if (data.size() >= 1000) {
                    bestCanDoDao.insertReport2Data(batchId, data);
                    data.clear();
                }
            }
        }, new StylesTable());

        bestCanDoDao.insertReport2Data(batchId, data);
        return response;
    }

    @Override
    public Response queryTaskQueue(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(bestCanDoDao.queryTaskQueueCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(bestCanDoDao.queryTaskQueue(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public Response queryReport4(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        Map<String, Object> stepInfo = bestCanDoDao.queryBatchStepInfo(parameterMap);
        if (stepInfo == null || stepInfo.isEmpty() || stepInfo.get("STEP") == null) {
            return response.setBody(page);
        }
        parameterMap.put("steps", bestCanDoDao.queryBatchStepName(stepInfo));
        page.setTotal(bestCanDoDao.queryReport4Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(bestCanDoDao.queryReport4(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport4(Map<String, Object> parameterMap, HttpServletResponse res) {
        Map<String, Object> stepInfo = bestCanDoDao.queryBatchStepInfo(parameterMap);
        parameterMap.put("steps", bestCanDoDao.queryBatchStepName(stepInfo));
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "mo_todo_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.simulation.dao.IBestCanDoDao.queryReport4", parameterMap);
    }

    @Override
    public Response queryReport5(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(bestCanDoDao.queryReport5Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(bestCanDoDao.queryReport5(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport5(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "wip_todo_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.simulation.dao.IBestCanDoDao.queryReport5", parameterMap);
    }

    @Override
    public Response queryReport6(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(bestCanDoDao.queryReport6Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(bestCanDoDao.queryReport6(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport6(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "rm_shortage_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.simulation.dao.IBestCanDoDao.queryReport6", parameterMap);
    }

    @Override
    public Response queryExecuteLogs(Map<String, Object> parameterMap) {
        return response.setBody(bestCanDoDao.queryExecuteLogs(parameterMap));
    }

    private void sendLog(String batchID, String userid, String message) {
        BestCanDoLog log = new BestCanDoLog();
        log.setBatch_id(batchID);
        log.setTime(DEFAULT_DATE_FORMAT.format(new Date()));
        log.setMessage(message);
        MqttConfiguration.publishMessage("scp/dss/ui/bcd/" + StringUtils.lowerCase(userid), log.toJSONString());
    }

    private void generateNewBatchCascaderFilter(Map<String, Object> parameterMap, String filterKey) {
        // 生成筛选条件
        JSONArray categoryArray = (JSONArray) parameterMap.get(filterKey);
        if (categoryArray != null) {
            Map<String, List<String>> filterMap = new HashMap<>();

            for (Object subObj : categoryArray) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = subArray.getString(0);

                String value = subArray.getString(1);
                String key = Utils.randomStr(8);

                if (Utils.hasInjectionAttack(columnName)) {
                    continue;
                }

                List<String> fl = filterMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                if (Utils.containsStr("BASIC_START_DATE,CALENDAR_DATE", columnName)) {
                    fl.add("to_date(#{" + key + ",jdbcType=VARCHAR}, 'yyyy/mm/dd')");
                } else {
                    fl.add("#{" + key + ",jdbcType=VARCHAR}");
                }
                parameterMap.put(key, value);
            }

            List<String> filterList = new ArrayList<>();

            for (String key : filterMap.keySet()) {
                List<String> fl = filterMap.get(key);
                filterList.add("t." + key + " in (" + StringUtils.join(fl, ",") + ")");
            }

            if (filterList.isEmpty()) {
                parameterMap.put(filterKey + "Filters", " 1 = 0 ");
            } else {
                parameterMap.put(filterKey + "Filters", StringUtils.join(filterList, " and "));
            }
        }
    }

    private String getColumnName(Object labelObj) {
        String label = (String) labelObj;
        if (label == null) {
            return null;
        }
        if (Utils.hasInjectionAttack(label)) {
            return "";
        }
        return label;
    }

    private void getCompareTasks(Map<String, Object> parameterMap) {
        List<String> selectedxAxis = ((JSONArray) parameterMap.get("selectedxAxis")).toJavaList(String.class);
        List<String> getCompareTaskL = ((JSONArray) parameterMap.get("compareTaskL")).toJavaList(String.class);
        List<String> getCompareTaskR = ((JSONArray) parameterMap.get("compareTaskR")).toJavaList(String.class);

        parameterMap.put("selectedxAxis", selectedxAxis);
        String compareTaskL = getCompareTaskL.get(getCompareTaskL.size() - 1).split("@")[1];   //获取前端选择的compareTaskL
        parameterMap.put("compareTaskL", compareTaskL);
        if (getCompareTaskR != null && getCompareTaskR.size() != 0) {
            String compareTaskR = getCompareTaskR.get(getCompareTaskR.size() - 1).split("@")[1];   //获取前端选择的compareTaskR
            parameterMap.put("compareTaskR", compareTaskR);
        } else {
            parameterMap.put("compareTaskR", "");
        }
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response compareReport1(Map<String, Object> parameterMap) {
        this.getCompareTasks(parameterMap);
        parameterMap.put("compare1XAxisType", parameterMap.get("compare1XAxisType"));

        LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();
        List<BestCanDoCompare1> dataList;
        Map<String, BigDecimal> dataMap = new HashMap<>();
        Map<String, String> xAxisMap = new HashMap<>();

        String compare1Value = (String) parameterMap.get("compareValue");

        if ("QTY".equalsIgnoreCase(compare1Value)) {
            compare1Value = "SUM(qty)";
        } else if ("VALUE".equalsIgnoreCase(compare1Value)) {
            compare1Value = "SUM(VALUE)";
        } else {
            compare1Value = "COUNT(1)";
        }
        parameterMap.put("compare1Value", compare1Value);

        List<String> legend = bestCanDoDao.compareReport1legend(parameterMap);
        dataList = bestCanDoDao.compareReport1(parameterMap);

        for (BestCanDoCompare1 data : dataList) {
            dataMap.put(data.getKey(), data.getVALUE());
            xAxisMap.put(data.getHORIZONTAL(), "");
        }
        List<String> xAxisList = xAxisMap.keySet().stream().sorted(String::compareTo).collect(Collectors.toList());  //keySet()返回的是map对象的key值的set集合

        for (String l : legend) {
            List<BigDecimal> temp = new ArrayList<>();

            for (String x : xAxisList) {
                temp.add(dataMap.getOrDefault(l + "#" + x, BigDecimal.ZERO));
            }
            resultMap.put(l, temp);
        }
        resultMap.put("xAxis", xAxisList);

        return response.setBody(resultMap);
    }

    @Override
    public Response compareReport1Details(Map<String, Object> parameterMap) {
        this.getCompareTasks(parameterMap);
        String compare1SelectedName = (String) parameterMap.get("compare1SelectedName");
        String compare1SelectedSeriesName = (String) parameterMap.get("compare1SelectedSeriesName");
        parameterMap.put("compare1SelectedName", compare1SelectedName);
        parameterMap.put("compare1SelectedSeriesName", compare1SelectedSeriesName);
        parameterMap.put("compare1XAxisType", parameterMap.get("compare1XAxisType"));

        if (compare1SelectedName.startsWith("L_")) {
            parameterMap.put("isLeftTask", true);
        } else if (compare1SelectedName.startsWith("R_")) {
            parameterMap.put("isLeftTask", false);
        }
        String compare1SelectedValue = (String) parameterMap.get("compare1SelectedValue");
        parameterMap.put("compare3SelectedValue", compare1SelectedValue);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(bestCanDoDao.compareReport1DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(bestCanDoDao.compareReport1Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadCompareReport1Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.getCompareTasks(parameterMap);
        String compare1SelectedName = (String) parameterMap.get("compare1SelectedName");
        String compare1SelectedSeriesName = (String) parameterMap.get("compare1SelectedSeriesName");
        parameterMap.put("compare1SelectedName", compare1SelectedName);
        parameterMap.put("compare1SelectedSeriesName", compare1SelectedSeriesName);
        parameterMap.put("compare1XAxisType", parameterMap.get("compare1XAxisType"));

        if (compare1SelectedName.startsWith("L_")) {
            parameterMap.put("isLeftTask", true);
        } else if (compare1SelectedName.startsWith("R_")) {
            parameterMap.put("isLeftTask", false);
        }
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "bcd_optimization_result_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.simulation.dao.IBestCanDoDao.compareReport1Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response compareReport2(Map<String, Object> parameterMap) throws Exception {
        this.getCompareTasks(parameterMap);
        parameterMap.put("compare2XAxisType", parameterMap.get("compare2XAxisType"));

        // 将前台传过来的label转换成列名, 同时也可以防止恶意代码注入
        parameterMap.put("level1", this.getColumnName(parameterMap.get("level1")));
        parameterMap.put("level2", this.getColumnName(parameterMap.get("level2")));

        String compare2Value = (String) parameterMap.get("compareValue");

        if ("QTY".equalsIgnoreCase(compare2Value)) {
            compare2Value = "SUM(SHORTAGE)";
        } else if ("VALUE".equalsIgnoreCase(compare2Value)) {
            compare2Value = "SUM(SHORTAGE * UNIT_COST)";
        } else {
            compare2Value = "COUNT(1)";
        }
        parameterMap.put("compare2Value", compare2Value);

        List<BestCanDoTreemap> resultList = new ArrayList<>();
        List<BestCanDoCompare2Bean> dataList = bestCanDoDao.compareReport2(parameterMap);
        for (BestCanDoCompare2Bean data : dataList) {
            this.convertReport2Data(resultList, data);
        }
        return response.setBody(resultList);
    }

    @Override
    public Response compareReport2Details(Map<String, Object> parameterMap) {
        this.getCompareTasks(parameterMap);
        String compare2SelectedName = (String) parameterMap.get("compare2SelectedName");
        String compare2SelectedSeriesName = (String) parameterMap.get("compare2SelectedSeriesName");
        parameterMap.put("compare2SelectedName", compare2SelectedName);
        parameterMap.put("compare2SelectedSeriesName", compare2SelectedSeriesName);
        parameterMap.put("compare2XAxisType", parameterMap.get("compare2XAxisType"));

        if ("LEVEL_A".equalsIgnoreCase(compare2SelectedName)) {
            parameterMap.put("isLeftTask", true);
        } else if ("LEVEL_B".equalsIgnoreCase(compare2SelectedName)) {
            parameterMap.put("isLeftTask", false);
        }

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(bestCanDoDao.compareReport2DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(bestCanDoDao.compareReport2Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadCompareReport2Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.getCompareTasks(parameterMap);
        String compare2SelectedName = (String) parameterMap.get("compare2SelectedName");
        String compare2SelectedSeriesName = (String) parameterMap.get("compare2SelectedSeriesName");
        parameterMap.put("compare2SelectedName", compare2SelectedName);
        parameterMap.put("compare2SelectedSeriesName", compare2SelectedSeriesName);
        parameterMap.put("compare2XAxisType", parameterMap.get("compare2XAxisType"));

        if ("LEVEL_A".equalsIgnoreCase(compare2SelectedName)) {
            parameterMap.put("isLeftTask", true);
        } else if ("LEVEL_B".equalsIgnoreCase(compare2SelectedName)) {
            parameterMap.put("isLeftTask", false);
        }
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "bcd_global_shortage_summary" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.simulation.dao.IBestCanDoDao.compareReport2Details", parameterMap);
    }

    /**
     * 将列表转化为Tree数据
     *
     * @param list 输出树
     * @param data 输入值
     * @throws Exception 异常
     */
    private void convertReport2Data(List<BestCanDoTreemap> list, BestCanDoCompare2Bean data) throws Exception {
        String[] categorysOrg = new String[]{data.getCategory1(), data.getCategory2()};
        List<String> categories = new ArrayList<>();

        for (String category : categorysOrg) {
            if (StringUtils.isNotBlank(category)) {
                categories.add(category);
            } else {
                break;
            }
        }
        // 这边逻辑比较复杂, 所以用最笨的方法来描述了, 以免后期不好维护
        // 先把这一行数据转成treemap的数据
        // 第一个节点
        List<BestCanDoTreemap> child = new ArrayList<>();
        BestCanDoTreemap root = new BestCanDoTreemap();
        root.setName(categories.get(0));
        root.setTips(data.copyTooltips()); // 因为这个tooltips要放在树中全局使用, 所以必须要生成一个新节点
        root.setChildren(child);

        // 中间节点
        for (int i = 1; i < categories.size() - 1; i++) {
            BestCanDoTreemap treemap = new BestCanDoTreemap();
            treemap.setName(categories.get(i));
            treemap.setTips(data.copyTooltips());

            child.add(treemap);
            child = new ArrayList<>();
            treemap.setChildren(child);
        }

        // 最后一个节点
        BestCanDoTreemap lastNode = new BestCanDoTreemap();
        lastNode.setName(categories.get(categories.size() - 1));
        lastNode.setValue(data.getValue());
        lastNode.setTips(data.copyTooltips());
        child.add(lastNode);

        // 将这行treemap与原始数据相加
        // 先找到list中是否有这个数据节点
        Optional<BestCanDoTreemap> beanOpt = list.stream().filter(b -> b.getName().equals(categories.get(0))).findFirst();
        if (beanOpt.isPresent()) {
            BestCanDoTreemap bean = beanOpt.get();
            bean.add(root); // 两个节点合并
        } else { //找不到的时候最省事, 直接放入list就可以了
            list.add(root);
        }
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response compareReport3(Map<String, Object> parameterMap) {
        this.getCompareTasks(parameterMap);

        parameterMap.put("compare3XAxisType", parameterMap.get("compare3XAxisType"));
        String compare3Value = (String) parameterMap.get("compareValue");

        if ("QTY".equalsIgnoreCase(compare3Value)) {
            compare3Value = "SUM(RM_SHORTAGE)";
        } else if ("VALUE".equalsIgnoreCase(compare3Value)) {
            compare3Value = "SUM(UNIT_COST * RM_SHORTAGE)";
        } else {
            compare3Value = "COUNT(1)";
        }
        parameterMap.put("compare3Value", compare3Value);

        LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();
        List<BestCanDoCompare1> dataList;
        Map<String, BigDecimal> dataMap = new HashMap<>();
        Map<String, String> xAxisMap = new HashMap<>();

        List<String> legend = bestCanDoDao.compareReport3legend(parameterMap);
        dataList = bestCanDoDao.compareReport3(parameterMap);

        for (BestCanDoCompare1 data : dataList) {
            dataMap.put(data.getKey(), data.getVALUE());
            xAxisMap.put(data.getHORIZONTAL(), "");
        }
        List<String> xAxisList = xAxisMap.keySet().stream().sorted(String::compareTo).collect(Collectors.toList());  //keySet()返回的是map对象的key值的set集合

        for (String l : legend) {
            List<BigDecimal> temp = new ArrayList<>();

            for (String x : xAxisList) {
                temp.add(dataMap.getOrDefault(l + "#" + x, BigDecimal.ZERO));
            }
            resultMap.put(l, temp);
        }
        resultMap.put("xAxis", xAxisList);

        return response.setBody(resultMap);
    }


    @Override
    public Response compareReport3Details(Map<String, Object> parameterMap) {
        this.getCompareTasks(parameterMap);
        String compare3SelectedName = (String) parameterMap.get("compare3SelectedName");
        String compare3SelectedSeriesName = (String) parameterMap.get("compare3SelectedSeriesName");
        parameterMap.put("compare3SelectedName", compare3SelectedName);
        parameterMap.put("compare3SelectedSeriesName", compare3SelectedSeriesName);
        parameterMap.put("compare3XAxisType", parameterMap.get("compare3XAxisType"));

        if (compare3SelectedName == null){
            compare3SelectedName = "L_TITLE";
        }

        if (compare3SelectedName.startsWith("L_")) {
            parameterMap.put("isLeftTask", true);
        } else if (compare3SelectedName.startsWith("R_")) {
            parameterMap.put("isLeftTask", false);
        }

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(bestCanDoDao.compareReport3DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(bestCanDoDao.compareReport3Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadCompareReport3Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.getCompareTasks(parameterMap);
        String compare3SelectedName = (String) parameterMap.get("compare3SelectedName");
        String compare3SelectedSeriesName = (String) parameterMap.get("compare3SelectedSeriesName");
        parameterMap.put("compare3SelectedName", compare3SelectedName);
        parameterMap.put("compare3SelectedSeriesName", compare3SelectedSeriesName);
        parameterMap.put("compare3XAxisType", parameterMap.get("compare3XAxisType"));

        if (compare3SelectedName == null){
            compare3SelectedName = "L_TITLE";
        }

        if (compare3SelectedName.startsWith("L_")) {
            parameterMap.put("isLeftTask", true);
        } else if (compare3SelectedName.startsWith("R_")) {
            parameterMap.put("isLeftTask", false);
        }
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "bcd_shortage_analysis_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.simulation.dao.IBestCanDoDao.compareReport3Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response compareReport4Left(Map<String, Object> parameterMap) {
        this.getCompareTasks(parameterMap);

        LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();
        List<BestCanDoCompare1> dataList;
        Map<String, BigDecimal> dataMap = new HashMap<>();
        Map<String, String> xAxisMap = new HashMap<>();

        String compare4Value = (String) parameterMap.get("compareValue");

        if ("QTY".equalsIgnoreCase(compare4Value)) {
            compare4Value = "SUM(VALUE)";
        } else if ("VALUE".equalsIgnoreCase(compare4Value)) {
            compare4Value = "SUM(UNIT_COST * VALUE)";
        } else {
            compare4Value = "COUNT(1)";
        }
        parameterMap.put("compare4Value", compare4Value);

        List<String> legend = bestCanDoDao.compareReport4legend(parameterMap);
        dataList = bestCanDoDao.compareReport4Left(parameterMap);

        for (BestCanDoCompare1 data : dataList) {
            dataMap.put(data.getKey(), data.getVALUE());
            xAxisMap.put(data.getHORIZONTAL(), "");
        }
        List<String> xAxisList = xAxisMap.keySet().stream().sorted(String::compareTo).collect(Collectors.toList());  //keySet()返回的是map对象的key值的set集合

        for (String l : legend) {
            List<BigDecimal> temp = new ArrayList<>();

            for (String x : xAxisList) {
                temp.add(dataMap.getOrDefault(l + "#" + x, BigDecimal.ZERO));
            }
            resultMap.put(l, temp);
        }
        resultMap.put("xAxis", xAxisList);

        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response compareReport4Right(Map<String, Object> parameterMap) {
        this.getCompareTasks(parameterMap);

        LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();
        List<BestCanDoCompare1> dataList;
        Map<String, BigDecimal> dataMap = new HashMap<>();
        Map<String, String> xAxisMap = new HashMap<>();

        String compare4Value = (String) parameterMap.get("compareValue");

        if ("QTY".equalsIgnoreCase(compare4Value)) {
            compare4Value = "SUM(VALUE)";
        } else if ("VALUE".equalsIgnoreCase(compare4Value)) {
            compare4Value = "SUM(UNIT_COST * VALUE)";
        } else {
            compare4Value = "COUNT(1)";
        }
        parameterMap.put("compare4Value", compare4Value);

        List<String> legend = bestCanDoDao.compareReport4legend(parameterMap);
        dataList = bestCanDoDao.compareReport4Right(parameterMap);

        for (BestCanDoCompare1 data : dataList) {
            dataMap.put(data.getKey(), data.getVALUE());
            xAxisMap.put(data.getHORIZONTAL(), "");
        }
        List<String> xAxisList = xAxisMap.keySet().stream().sorted(String::compareTo).collect(Collectors.toList());  //keySet()返回的是map对象的key值的set集合


        for (String l : legend) {
            List<BigDecimal> temp = new ArrayList<>();

            for (String x : xAxisList) {
                temp.add(dataMap.getOrDefault(l + "#" + x, BigDecimal.ZERO));
            }
            resultMap.put(l, temp);
        }
        resultMap.put("xAxis", xAxisList);

        return response.setBody(resultMap);
    }

    @Override
    public Response compareReport4Details(Map<String, Object> parameterMap) {
        this.getCompareTasks(parameterMap);
        String compare4SelectedName = (String) parameterMap.get("compare4SelectedName");
        parameterMap.put("compare4SelectedName", compare4SelectedName);

        List<String> getCompareTask = ((JSONArray) parameterMap.get("compare4SelectedTaskId")).toJavaList(String.class);
        String compareTask = getCompareTask.get(getCompareTask.size() - 1).split("@")[1];   //获取前端选择的compareTaskL
        parameterMap.put("compareTask", compareTask);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(bestCanDoDao.compareReport4DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(bestCanDoDao.compareReport4Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadCompareReport4Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.getCompareTasks(parameterMap);
        String compare4SelectedName = (String) parameterMap.get("compare4SelectedName");
        parameterMap.put("compare4SelectedName", compare4SelectedName);

        List<String> getCompareTask = ((JSONArray) parameterMap.get("compare4SelectedTaskId")).toJavaList(String.class);
        String compareTask = getCompareTask.get(getCompareTask.size() - 1).split("@")[1];   //获取前端选择的compareTaskL
        parameterMap.put("compareTask", compareTask);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "bcd_soh_evolution_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.simulation.dao.IBestCanDoDao.compareReport4Details", parameterMap);
    }

    @Override
    public Response compareReportOverview(Map<String, Object> parameterMap) {
        String compareOverviewValue = (String) parameterMap.get("compareValue");

        if ("QTY".equalsIgnoreCase(compareOverviewValue)) {
            compareOverviewValue = "SUM(QTY)";
        } else if ("VALUE".equalsIgnoreCase(compareOverviewValue)) {
            compareOverviewValue = "SUM(VALUE)";
        } else {
            compareOverviewValue = "COUNT(1)";
        }
        parameterMap.put("compareOverviewValue", compareOverviewValue);
        this.getCompareTasks(parameterMap);
        return response.setBody(bestCanDoDao.compareReportOverview(parameterMap));
    }

    @Override
    public Response compareOverviewDetails(Map<String, Object> parameterMap) {
        this.getCompareTasks(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(bestCanDoDao.compareOverviewDetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(bestCanDoDao.compareOverviewDetails(parameterMap));
        }
        return response.setBody(page);
    }

    public void downloadCompareOverviewDetails(Map<String, Object> parameterMap, HttpServletResponse response) {
        this.getCompareTasks(parameterMap);
        String compareOverviewDetailsType = (String) parameterMap.get("compareOverviewDetailsType");
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        String fileName = "compare_overview_" + compareOverviewDetailsType.toLowerCase() + "_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.simulation.dao.IBestCanDoDao.compareOverviewDetails", parameterMap);
    }


}
