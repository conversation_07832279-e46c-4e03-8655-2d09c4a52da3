package com.scp.simulation.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.scp.simulation.bean.ChainedComparator;
import com.scp.simulation.bean.DemandSupplyMaterial;
import com.scp.simulation.bean.DemandSupplyWeight;
import com.scp.simulation.dao.IPlantDemandSupplyDao;
import com.scp.simulation.service.IPlantDemandSupplyService;
import com.starter.context.bean.*;
import com.starter.context.bean.scptable.ScpTableHelper;
import com.starter.utils.DateCalUtil;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import com.starter.utils.excel.SimpleSheetContentsHandler;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.model.StylesTable;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service("plantDemandSupplyService")
@Scope("prototype")
@Transactional
public class PlantDemandSupplyServiceImpl implements IPlantDemandSupplyService {

    @Resource
    private Response response;

    @Resource
    private IPlantDemandSupplyDao plantDemandSupplyDao;

    @Resource
    private ExcelTemplate excelTemplate;

    @Resource
    private ScpTableHelper scpTableHelper;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage() {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cascader", Utils.parseCascader(plantDemandSupplyDao.queryDemandSupplyCascader()));
        resultMap.put("dayList", this.getDayList());
        resultMap.put("weekList", this.getWeekList());
        resultMap.put("monthList", this.getMonthList());
        resultMap.put("criticalLevelList", plantDemandSupplyDao.queryCriticalLevelList());
        return response.setBody(resultMap);
    }

    // 方便统一修改起始时间
    private Calendar getToday() {
        return Calendar.getInstance();
    }

    private List<String> getDayList() {
        List<String> dayList = new ArrayList<>();
        Calendar today = this.getToday();
        for (int i = 0; i < 15; i++) {
            int month = today.get(Calendar.MONTH) + 1;
            int dayOfMonth = today.get(Calendar.DAY_OF_MONTH);
            dayList.add(today.get(Calendar.YEAR) + (month < 10 ? "0" + month : "" + month) + (dayOfMonth < 10 ? "0" + dayOfMonth : "" + dayOfMonth));
            today.add(Calendar.DAY_OF_YEAR, 1);
        }
        return dayList;
    }

    private List<String> getWeekList() {
        return plantDemandSupplyDao.queryNext14WeekList();
    }

    private List<String> getMonthList() {
        List<String> monthList = new ArrayList<>();
        Calendar today = this.getToday();
        for (int i = 0; i < 18; i++) {
            int month = today.get(Calendar.MONTH) + 1;
            monthList.add(today.get(Calendar.YEAR) + "" + (month < 10 ? "0" + month : month));
            today.add(Calendar.MONTH, 1);
        }
        return monthList;
    }

    // region report1

    @Override
    @SuppressWarnings("unchecked")
    public Response queryReport1(Map<String, Object> parameterMap, String userid) {
        this.generateCascaderFilter(parameterMap);

        // ratio
        BigDecimal depRatio = BigDecimal.ZERO;
        BigDecimal indepRatio = BigDecimal.ZERO;
        BigDecimal fcstRatio = BigDecimal.ZERO;
        BigDecimal ssRatio = BigDecimal.ZERO;
        BigDecimal amuRatio = BigDecimal.ZERO;
        BigDecimal amfRatio = BigDecimal.ZERO;
        BigDecimal moiRatio = BigDecimal.ZERO;
        BigDecimal msRatio = BigDecimal.ZERO;
        JSONArray ratioArray = (JSONArray) parameterMap.get("ratio");
        for (Object obj : ratioArray) {
            JSONObject jsonObj = (JSONObject) obj;
            if ("Depedent Requirement".equalsIgnoreCase((String) jsonObj.get("value"))) {
                depRatio = BigDecimal.valueOf(Utils.parseDouble(jsonObj.get("ratio")) / 100.0);
            } else if ("Indepedent Requirement".equalsIgnoreCase((String) jsonObj.get("value"))) {
                indepRatio = BigDecimal.valueOf(Utils.parseDouble(jsonObj.get("ratio")) / 100.0);
            } else if ("FCST Commitement".equalsIgnoreCase((String) jsonObj.get("value"))) {
                fcstRatio = BigDecimal.valueOf(Utils.parseDouble(jsonObj.get("ratio")) / 100.0);
                parameterMap.put("fcstRatio", fcstRatio);
            } else if ("Safety Stock".equalsIgnoreCase((String) jsonObj.get("value"))) {
                ssRatio = BigDecimal.valueOf(Utils.parseDouble(jsonObj.get("ratio")) / 100.0);
                parameterMap.put("ssRatio", ssRatio);
            } else if ("AMU".equalsIgnoreCase((String) jsonObj.get("value"))) {
                amuRatio = BigDecimal.valueOf(Utils.parseDouble(jsonObj.get("ratio")) / 100.0);
                parameterMap.put("amuRatio", amuRatio);
            } else if ("AMF".equalsIgnoreCase((String) jsonObj.get("value"))) {
                amfRatio = BigDecimal.valueOf(Utils.parseDouble(jsonObj.get("ratio")) / 100.0);
                parameterMap.put("amfRatio", amfRatio);
            } else if ("Mtd Order Intake".equalsIgnoreCase((String) jsonObj.get("value"))) {
                moiRatio = BigDecimal.valueOf(1);
                parameterMap.put("moiRatio", moiRatio);
            } else if ("Mtd Sales".equalsIgnoreCase((String) jsonObj.get("value"))) {
                msRatio = BigDecimal.valueOf(1);
                parameterMap.put("msRatio", msRatio);
            }
        }

        // 处理OPEN SO的筛选器
        List<String> openSOExcludeFilterList = (List<String>) parameterMap.get("openSOExcludeFilterList");
        // 处理TRANSFER PO的筛选器
        List<String> transferPOExcludeFilterList = (List<String>) parameterMap.get("transferPOExcludeFilterList");

        StringBuilder calOpenSOQtyColumn = new StringBuilder("T.QUANTITY");
        StringBuilder calTransferPOQtyColumn = new StringBuilder("T.QUANTITY");

        for (String qc : openSOExcludeFilterList) {
            calOpenSOQtyColumn.append(" - NVL(").append(qc).append(", 0)");
        }
        for (String qc : transferPOExcludeFilterList) {
            calTransferPOQtyColumn.append(" - NVL(").append(qc).append(", 0)");
        }

        String calQtyColumn = "DECODE(T.DEMAND_CATEGORY, 'Demand_SO', " + calOpenSOQtyColumn.toString() + ", ";
        calQtyColumn += "'Transfer_PO', " + calTransferPOQtyColumn.toString() + ", ";
        calQtyColumn += "'Dep_FCST', T.QUANTITY * " + depRatio.doubleValue() + ", ";
        calQtyColumn += "'Ind_FCST', T.QUANTITY * " + indepRatio.doubleValue() + ", ";
        calQtyColumn += " T.QUANTITY) QUANTITY";
        parameterMap.put("calQtyColumn", calQtyColumn);

        // 开始计算数据主体
        String valueBy = (String) parameterMap.get("valueBy");
        String viewBy = (String) parameterMap.get("viewBy");
        JSONArray orderBy = (JSONArray) parameterMap.get("orderBy");
        String order = (String) parameterMap.get("order");
        if (orderBy == null || orderBy.isEmpty()) {
            orderBy = new JSONArray();
            orderBy.add("WEIGHT");
            order = "ASC";
        }

        switch (valueBy) {
            case "Quantity" -> {
                parameterMap.put("valueColumnName", "");
                parameterMap.put("ablaSuffix", "_QTY");
            }
            case "Moving Average Price" -> {
                parameterMap.put("valueColumnName", " * UNIT_COST");
                parameterMap.put("ablaSuffix", "_MVP");
            }
            case "Net Net Price" -> {
                parameterMap.put("valueColumnName", " * AVG_SELLING_PRICE_RMB");
                parameterMap.put("ablaSuffix", "_VALUE");
            }
        }

        // region 查询符合条件的数据
        List<String> dates = new ArrayList<>();
        dates.add("PAST_DUE");
        switch (viewBy) {
            case "By Day" -> {
                dates.addAll(this.getDayList());
                parameterMap.put("dateColumnName", "BY_DAY");
            }
            case "By Week" -> {
                dates.addAll(this.getWeekList());
                parameterMap.put("dateColumnName", "BY_WEEK");
            }
            case "By Month" -> {
                dates.addAll(this.getMonthList());
                parameterMap.put("dateColumnName", "BY_MONTH");
            }
            default -> {
                return response;
            }
        }
        parameterMap.put("dates", dates);
        List<BigDecimal> weightList = new ArrayList<>();
        int size = dates.size();
        double seed = 1.0 / size;
        for (int i = 0; i < size; i++) {
            if (i < 4) {
                weightList.add(BigDecimal.valueOf(seed * (size - i) * 2).setScale(5, RoundingMode.HALF_UP));
            } else if (i < 8) {
                weightList.add(BigDecimal.valueOf(seed * (size - i)).setScale(5, RoundingMode.HALF_UP));
            } else {
                weightList.add(BigDecimal.valueOf(seed * (size - i) * 0.5).setScale(5, RoundingMode.HALF_UP));
            }
        }

        JSONArray demandDueSelections = (JSONArray) parameterMap.get("demandDueSelections");
        List<String> futureDue = new ArrayList<>();
        List<String> pastDue = new ArrayList<>();
        for (Object item : demandDueSelections) {
            JSONArray dueSelections = (JSONArray) item;
            if ("FUTURE_DUE".equalsIgnoreCase(dueSelections.getString(0))) {
                futureDue.add(dueSelections.getString(1));
            } else if ("PAST_DUE".equalsIgnoreCase(dueSelections.getString(0))) {
                pastDue.add(dueSelections.getString(1));
            }
        }
        List<String> selectedCategory = new ArrayList<>();
        selectedCategory.addAll(futureDue);
        selectedCategory.addAll(pastDue);
        parameterMap.put("futureDue", futureDue);
        parameterMap.put("pastDue", pastDue);
        parameterMap.put("selectedCategory", selectedCategory);
        List<Map<String, Object>> demandList = plantDemandSupplyDao.queryReport1Demand(parameterMap);
        List<Map<String, Object>> supplyList = plantDemandSupplyDao.queryReport1Supply(parameterMap);
        // endregion

        // region 计算 Demand Total
        // 将demand信息分为2类, 一类是有group的, 一类是没有group的
        Map<String, List<Map<String, Object>>> demandGroupMap = new HashMap<>();
        Map<String, List<Map<String, Object>>> demandMaterialMap = new HashMap<>();
        for (Map<String, Object> demand : demandList) {
            String materialKey = demand.get("MATERIAL") + "@" + demand.get("PLANT_CODE");
            String groupKey = demand.get("GROUP_MATERIAL") + "@" + demand.get("PLANT_CODE");
            if (StringUtils.isNotBlank((String) demand.get("GROUP_MATERIAL"))) {
                List<Map<String, Object>> list = demandGroupMap.computeIfAbsent(groupKey, k -> new ArrayList<>());
                list.add(demand);
            } else {
                List<Map<String, Object>> list = demandMaterialMap.computeIfAbsent(materialKey, k -> new ArrayList<>());
                list.add(demand);
            }
        }

        // 对两者分别算Total, 放在两个新的Map里
        Map<String, Map<String, Object>> demandGroupTotalMap = new HashMap<>();
        Map<String, Map<String, Object>> demandMaterialTotalMap = new HashMap<>();

        // demand group
        for (String key : demandGroupMap.keySet()) {
            List<Map<String, Object>> list = demandGroupMap.get(key);
            Map<String, Object> total = new HashMap<>();
            for (Map<String, Object> map : list) {
                for (String key0 : map.keySet()) {
                    if (dates.contains(key0) || key0.startsWith("TIPS_")) { // 数据列
                        BigDecimal t = Utils.parseBigDecimal(total.getOrDefault(key0, BigDecimal.ZERO));
                        t = t.add(Utils.parseBigDecimal(map.get(key0)));
                        total.put(key0, t);
                    } else { //非数据列
                        total.put(key0, map.get(key0));
                    }
                }
            }
            total.put("CATEGORY", "Demand Total"); // CATEGORY = Demand Total
            demandGroupTotalMap.put(key, total);
        }

        // demand material
        for (String key : demandMaterialMap.keySet()) {
            List<Map<String, Object>> list = demandMaterialMap.get(key);
            Map<String, Object> total = new HashMap<>();
            for (Map<String, Object> map : list) {
                for (String key0 : map.keySet()) {
                    if (dates.contains(key0) || key0.startsWith("TIPS_")) { // 数据列
                        BigDecimal t = Utils.parseBigDecimal(total.getOrDefault(key0, BigDecimal.ZERO));
                        t = t.add(Utils.parseBigDecimal(map.get(key0)));
                        total.put(key0, t);
                    } else { //非数据列
                        total.put(key0, map.get(key0));
                    }
                }
            }
            total.put("GROUP_MATERIAL", ""); // 物料行不显示Group
            total.put("CATEGORY", "Demand Total"); // CATEGORY = Demand Total
            demandMaterialTotalMap.put(key, total);
        }
        // endregion

        // region 计算 Supply Total
        // 将supply信息分为2类, 一类是有group的, 一类是没有group的
        Map<String, List<Map<String, Object>>> supplyGroupMap = new HashMap<>();
        Map<String, List<Map<String, Object>>> supplyMaterialMap = new HashMap<>();
        for (Map<String, Object> supply : supplyList) {
            String materialKey = supply.get("MATERIAL") + "@" + supply.get("PLANT_CODE");
            String groupKey = supply.get("GROUP_MATERIAL") + "@" + supply.get("PLANT_CODE");
            if (StringUtils.isNotBlank((String) supply.get("GROUP_MATERIAL"))) {
                List<Map<String, Object>> list = supplyGroupMap.computeIfAbsent(groupKey, k -> new ArrayList<>());
                list.add(supply);
            } else {
                List<Map<String, Object>> list = supplyMaterialMap.computeIfAbsent(materialKey, k -> new ArrayList<>());
                list.add(supply);
            }
        }

        // 对两者分别算Total, 放在两个新的Map里
        Map<String, Map<String, Object>> supplyGroupTotalMap = new HashMap<>();
        Map<String, Map<String, Object>> supplyMaterialTotalMap = new HashMap<>();

        // supply group
        for (String key : supplyGroupMap.keySet()) {
            List<Map<String, Object>> list = supplyGroupMap.get(key);
            Map<String, Object> total = new HashMap<>();
            for (Map<String, Object> map : list) {
                for (String key0 : map.keySet()) {
                    if (dates.contains(key0) || key0.startsWith("TIPS_")) { // 数据列 或 辅助列
                        BigDecimal t = Utils.parseBigDecimal(total.getOrDefault(key0, BigDecimal.ZERO));
                        t = t.add(Utils.parseBigDecimal(map.get(key0)));
                        total.put(key0, t);
                    } else { //非数据列
                        total.put(key0, map.get(key0));
                    }
                }
            }
            total.put("CATEGORY", "Supply Total"); // CATEGORY = Supply Total
            supplyGroupTotalMap.put(key, total);
        }

        // supply material
        for (String key : supplyMaterialMap.keySet()) {
            List<Map<String, Object>> list = supplyMaterialMap.get(key);
            Map<String, Object> total = new HashMap<>();
            for (Map<String, Object> map : list) {
                for (String key0 : map.keySet()) {
                    if (dates.contains(key0) || key0.startsWith("TIPS_")) { // 数据列 或 辅助列
                        BigDecimal t = Utils.parseBigDecimal(total.getOrDefault(key0, BigDecimal.ZERO));
                        t = t.add(Utils.parseBigDecimal(map.get(key0)));
                        total.put(key0, t);
                    } else { //非数据列
                        total.put(key0, map.get(key0));
                    }
                }
            }
            total.put("GROUP_MATERIAL", ""); // 物料行不显示Group
            total.put("CATEGORY", "Supply Total"); // CATEGORY = Supply Total
            supplyMaterialTotalMap.put(key, total);
        }
        // endregion

        // region 查询comments
        String weekNo = plantDemandSupplyDao.queryCurrentWeek();
        if (weekNo == null) {
            weekNo = new SimpleDateFormat("yyyyww").format(new Date());
        }
        parameterMap.put("weekNo", weekNo);
        List<Map<String, Object>> commentsList = plantDemandSupplyDao.queryReport1CommentsByUserid(weekNo, userid);
        Map<String, Object> commentsMap = new HashMap<>();
        for (Map<String, Object> map : commentsList) {
            String key = map.get("MATERIAL") + "@" + map.get("PLANT_CODE");
            commentsMap.put(key, map.get("COMMENTS"));
        }
        // endregion

        // 获取Critical Level表达式
        ScriptEngineManager manager = new ScriptEngineManager();
        ScriptEngine scriptEngine = manager.getEngineByName("javascript");
        String criticalScript = Utils.clob2String(plantDemandSupplyDao.queryReport1CriticalScript(parameterMap));

        // region 计算 Balance
        // 以demand作为主表, 去查询supply的信息
        Map<String, Map<String, Object>> groupBalanceMap = new HashMap<>();
        Map<String, Map<String, Object>> materialBalanceMap = new HashMap<>();

        // group balance
        List<DemandSupplyMaterial> demandGroupList = plantDemandSupplyDao.queryReport1Group(parameterMap);
        for (DemandSupplyMaterial group : demandGroupList) {
            Map<String, Object> demand = demandGroupTotalMap.getOrDefault(group.getKey(), new HashMap<>());
            Map<String, Object> supply = supplyGroupTotalMap.getOrDefault(group.getKey(), new HashMap<>());
            Map<String, Object> balance = new HashMap<>();
            // 先将非数据列复制到balance中去
            if (demand.isEmpty() == false) {
                for (String key : demand.keySet()) {
                    if (dates.contains(key) == false) {
                        balance.put(key, demand.get(key));
                    }
                }
            } else if (supply.isEmpty() == false) {
                for (String key : supply.keySet()) {
                    if (dates.contains(key) == false) {
                        balance.put(key, supply.get(key));
                    }
                }
            } else {
                continue;
            }

            balance.put("CATEGORY", "Balance");

            // 再计算数据列
            BigDecimal b;
            BigDecimal lastBalance = BigDecimal.ZERO;
            BigDecimal weight = BigDecimal.ZERO;
            for (int i = 0; i < dates.size(); i++) {
                String dateKey = dates.get(i);
                if (i == 0) {
                    b = group.getAvailableStock(); // 这个物料的可用库存 = 当前库存 - SS - AMU - AMF
                } else {
                    b = lastBalance;
                }

                // 如果是By Day, 当日供给无法抵消当日需求
                if ("By Day".equals(viewBy)) {
                    b = b.subtract(Utils.parseBigDecimal(demand.get(dateKey))); // balance = 可用库存 - 需求
                    balance.put(dateKey, b);
                    if (supply == null) {
                        lastBalance = b;
                    } else {
                        lastBalance = b.add(Utils.parseBigDecimal(supply.get(dateKey))); // 如果当日供给不为空, 则加到明日
                    }
                } else {
                    if (supply == null) {
                        b = b.subtract(Utils.parseBigDecimal(demand.get(dateKey))); // balance = 可用库存 - 需求
                    } else {
                        b = b.subtract(Utils.parseBigDecimal(demand.get(dateKey))).add(Utils.parseBigDecimal(supply.get(dateKey))); // balance = 可用库存 - 需求 + 供给
                    }
                    lastBalance = b;
                    balance.put(dateKey, b);

                    // 如果是week, 还要计算critical_level的变量
                    String index = i < 10 ? "0" + i : i + "";
                    balance.put("ban" + index, b == null ? 0 : b);
                    balance.put("sup" + index, supply.get(dateKey) == null ? 0 : supply.get(dateKey));
                    balance.put("dem" + index, demand.get(dateKey) == null ? 0 : demand.get(dateKey));
                }

                weight = weight.add(weightList.get(i).multiply(b).setScale(5, RoundingMode.HALF_UP));
            }

            // critical level
            String criticalLevel = "";
            if ("By Week".equals(viewBy) && StringUtils.isNotBlank(criticalScript)) {
                String criticalScriptTemp = criticalScript;
                for (int i = 0; i <= 14; i++) {
                    String index = i < 10 ? "0" + i : i + "";
                    criticalScriptTemp = StringUtils.replace(criticalScriptTemp, "_sup" + index, String.valueOf(balance.get("sup" + index)));
                    criticalScriptTemp = StringUtils.replace(criticalScriptTemp, "_dem" + index, String.valueOf(balance.get("dem" + index)));
                    criticalScriptTemp = StringUtils.replace(criticalScriptTemp, "_ban" + index, String.valueOf(balance.get("ban" + index)));
                }
                try {
                    scriptEngine.eval("function criticalLevel(){" + criticalScriptTemp + "}");
                    if (scriptEngine instanceof Invocable) {
                        Invocable in = (Invocable) scriptEngine;
                        criticalLevel = String.valueOf(in.invokeFunction("criticalLevel"));
                    }
                } catch (Exception e) {
                    criticalLevel = "[Exception]" + e.getMessage();
                }
                balance.put("CRITICAL_LEVEL", criticalLevel);
            }

            balance.put("WEIGHT", weight);
            balance.put("COMMENTS", commentsMap.get(group.getKey()));
            groupBalanceMap.put(group.getKey(), balance);
        }

        // material balance
        List<DemandSupplyMaterial> demandMaterialList = plantDemandSupplyDao.queryReport1Material(parameterMap);
        for (DemandSupplyMaterial material : demandMaterialList) {
            Map<String, Object> demand = demandMaterialTotalMap.getOrDefault(material.getKey(), new HashMap<>());
            Map<String, Object> supply = supplyMaterialTotalMap.getOrDefault(material.getKey(), new HashMap<>());
            Map<String, Object> balance = new HashMap<>();

            // 先将非数据列复制到balance中去
            if (demand.isEmpty() == false) {
                for (String key : demand.keySet()) {
                    if (dates.contains(key) == false) {
                        balance.put(key, demand.get(key));
                    }
                }
            } else if (supply.isEmpty() == false) {
                for (String key : supply.keySet()) {
                    if (dates.contains(key) == false) {
                        balance.put(key, supply.get(key));
                    }
                }
            } else {
                continue;
            }

            balance.put("GROUP_MATERIAL", "");
            balance.put("CATEGORY", "Balance");

            // 再计算数据列
            BigDecimal b;
            BigDecimal lastBalance = BigDecimal.ZERO;
            BigDecimal weight = BigDecimal.ZERO;
            for (int i = 0; i < dates.size(); i++) {
                String dateKey = dates.get(i);
                if (i == 0) {
                    b = material.getAvailableStock(); // 这个物料的可用库存 = 当前库存 - SS - AMU - AMF
                } else {
                    b = lastBalance;
                }

                // 如果是By Day, 当日供给无法抵消当日需求
                if ("By Day".equals(viewBy)) {
                    b = b.subtract(Utils.parseBigDecimal(demand.get(dateKey))); // balance = 可用库存 - 需求
                    balance.put(dateKey, b);
                    if (supply == null) {
                        lastBalance = b;
                    } else {
                        lastBalance = b.add(Utils.parseBigDecimal(supply.get(dateKey))); // 如果当日供给不为空, 则加到明日
                    }
                } else {
                    if (supply == null) {
                        b = b.subtract(Utils.parseBigDecimal(demand.get(dateKey))); // balance = 可用库存 - 需求
                    } else {
                        b = b.subtract(Utils.parseBigDecimal(demand.get(dateKey))).add(Utils.parseBigDecimal(supply.get(dateKey))); // balance = 可用库存 - 需求 + 供给
                    }
                    lastBalance = b;
                    balance.put(dateKey, b);

                    // 如果是week, 还要计算critical_level的变量
                    String index = i < 10 ? "0" + i : i + "";
                    balance.put("ban" + index, b);
                    balance.put("sup" + index, supply.get(dateKey));
                    balance.put("dem" + index, demand.get(dateKey));
                }

                weight = weight.add(weightList.get(i).multiply(b).setScale(5, RoundingMode.HALF_UP));
            }

            // critical level
            String criticalLevel = "";
            if ("By Week".equals(viewBy) && StringUtils.isNotBlank(criticalScript)) {
                String criticalScriptTemp = criticalScript;
                for (int i = 0; i <= 14; i++) {
                    String index = i < 10 ? "0" + i : i + "";
                    criticalScriptTemp = StringUtils.replace(criticalScriptTemp, "_sup" + index, String.valueOf(balance.get("sup" + index)));
                    criticalScriptTemp = StringUtils.replace(criticalScriptTemp, "_dem" + index, String.valueOf(balance.get("dem" + index)));
                    criticalScriptTemp = StringUtils.replace(criticalScriptTemp, "_ban" + index, String.valueOf(balance.get("ban" + index)));
                }
                try {
                    scriptEngine.eval("function criticalLevel(){\r\n" + criticalScriptTemp + "\r\n}");
                    if (scriptEngine instanceof Invocable) {
                        Invocable in = (Invocable) scriptEngine;
                        criticalLevel = String.valueOf(in.invokeFunction("criticalLevel"));
                    }
                } catch (Exception e) {
                    criticalLevel = "[Exception]" + e.getMessage();
                }
                balance.put("CRITICAL_LEVEL", criticalLevel);
            }

            balance.put("WEIGHT", weight);
            balance.put("COMMENTS", commentsMap.get(material.getKey()));
            materialBalanceMap.put(material.getKey(), balance);
        }

        // balance and weight
        List<DemandSupplyWeight> balanceWeightList = new ArrayList<>();
        for (String key : groupBalanceMap.keySet()) {
            Map<String, Object> balance = groupBalanceMap.get(key);
            DemandSupplyWeight temp = new DemandSupplyWeight();

            temp.setKey(key);
            temp.setType("GROUP");
            for (Object orderObj : orderBy) {
                String orderKey = (String) orderObj;
                temp.setOrder(orderKey, balance.get(orderKey));
            }

            balanceWeightList.add(temp);
        }

        for (String key : materialBalanceMap.keySet()) {
            Map<String, Object> balance = materialBalanceMap.get(key);
            DemandSupplyWeight temp = new DemandSupplyWeight();

            temp.setKey(key);
            temp.setType("MATERIAL");
            for (Object orderObj : orderBy) {
                String orderKey = (String) orderObj;
                temp.setOrder(orderKey, balance.get(orderKey));
            }

            balanceWeightList.add(temp);
        }

        // 排序, 支持多列排序
        JSONArray finalOrderBy = orderBy;
        ChainedComparator<DemandSupplyWeight> chainedComparator = new ChainedComparator<>() {
            @Override
            public int doCompare(DemandSupplyWeight o1, DemandSupplyWeight o2) {
                return this.compareObj(o1.getOrder(finalOrderBy.get(0)), o2.getOrder(finalOrderBy.get(0)));
            }
        };
        chainedComparator.setOrder(order);

        for (int i = 1; i < finalOrderBy.size(); i++) {
            final String key = (String) finalOrderBy.get(i);
            chainedComparator.setNext(new ChainedComparator<>() {
                @Override
                public int doCompare(DemandSupplyWeight o1, DemandSupplyWeight o2) {
                    return this.compareObj(o1.getOrder(key), o2.getOrder(key));
                }
            }).setOrder(order);
        }

        balanceWeightList.sort(chainedComparator);
        // endregion

        // 计算Total Demand和Total Supply
        BigDecimal amuTotal = BigDecimal.ZERO;
        BigDecimal amfTotal = BigDecimal.ZERO;
        BigDecimal ssTotal = BigDecimal.ZERO;
        BigDecimal sohTotal = BigDecimal.ZERO;
        BigDecimal opoTotal = BigDecimal.ZERO;
        BigDecimal moiTotal = BigDecimal.ZERO;
        BigDecimal msTotal = BigDecimal.ZERO;
        Map<String, Object> demandTotal = new HashMap<>();
        Map<String, Object> supplyTotal = new HashMap<>();
        Map<String, Object> balanceTotal = new HashMap<>();
        demandTotal.put("PLANT_CODE", "Demand Total");
        demandTotal.put("fontWeight", "bold");
        demandTotal.put("COLOR", 0);
        demandTotal.put("CATEGORY", "Demand Total");
        supplyTotal.put("PLANT_CODE", "Supply Total");
        supplyTotal.put("fontWeight", "bold");
        supplyTotal.put("COLOR", 0);
        supplyTotal.put("CATEGORY", "Supply Total");
        balanceTotal.put("PLANT_CODE", "Balance");
        balanceTotal.put("fontWeight", "bold");
        balanceTotal.put("COLOR", 0);
        balanceTotal.put("CATEGORY", "Balance");

        for (DemandSupplyWeight weight : balanceWeightList) {
            boolean hasAdded = false;
            if (weight.isGroup()) {
                Map<String, Object> temp = demandGroupTotalMap.get(weight.getKey());
                if (temp != null) {
                    for (String key : temp.keySet()) {
                        if (dates.contains(key) || key.startsWith("TIPS_")) {
                            demandTotal.put(key, Utils.parseBigDecimal(temp.get(key)).add(Utils.parseBigDecimal(demandTotal.get(key))));
                        }
                    }

                    ssTotal = ssTotal.add(Utils.parseBigDecimal(temp.get("SAFETY_STOCK")));
                    amuTotal = amuTotal.add(Utils.parseBigDecimal(temp.get("AMU")));
                    amfTotal = amfTotal.add(Utils.parseBigDecimal(temp.get("AMF")));
                    sohTotal = sohTotal.add(Utils.parseBigDecimal(temp.get("STOCK_ON_HAND")));
                    opoTotal = opoTotal.add(Utils.parseBigDecimal(temp.get("OPEN_PO_QTY")));
                    moiTotal = moiTotal.add(Utils.parseBigDecimal(temp.get("MTD_ORDER_INTAKE")));
                    msTotal = msTotal.add(Utils.parseBigDecimal(temp.get("MTD_SALES")));
                    hasAdded = true;
                }

                temp = supplyGroupTotalMap.get(weight.getKey());
                if (temp != null) {
                    for (String key : temp.keySet()) {
                        if (dates.contains(key) || key.startsWith("TIPS_")) {
                            supplyTotal.put(key, Utils.parseBigDecimal(temp.get(key)).add(Utils.parseBigDecimal(supplyTotal.get(key))));
                        }
                    }
                    if (hasAdded == false) {
                        ssTotal = ssTotal.add(Utils.parseBigDecimal(temp.get("SAFETY_STOCK")));
                        amuTotal = amuTotal.add(Utils.parseBigDecimal(temp.get("AMU")));
                        amfTotal = amfTotal.add(Utils.parseBigDecimal(temp.get("AMF")));
                        sohTotal = sohTotal.add(Utils.parseBigDecimal(temp.get("STOCK_ON_HAND")));
                        opoTotal = opoTotal.add(Utils.parseBigDecimal(temp.get("OPEN_PO_QTY")));
                        moiTotal = moiTotal.add(Utils.parseBigDecimal(temp.get("MTD_ORDER_INTAKE")));
                        msTotal = msTotal.add(Utils.parseBigDecimal(temp.get("MTD_SALES")));
                    }
                }
            } else if (weight.isMaterial()) {
                Map<String, Object> temp = demandMaterialTotalMap.get(weight.getKey());
                if (temp != null) {
                    for (String key : temp.keySet()) {
                        if (dates.contains(key) || key.startsWith("TIPS_")) {
                            demandTotal.put(key, Utils.parseBigDecimal(temp.get(key)).add(Utils.parseBigDecimal(demandTotal.get(key))));
                        }
                    }
                    ssTotal = ssTotal.add(Utils.parseBigDecimal(temp.get("SAFETY_STOCK")));
                    amuTotal = amuTotal.add(Utils.parseBigDecimal(temp.get("AMU")));
                    amfTotal = amfTotal.add(Utils.parseBigDecimal(temp.get("AMF")));
                    sohTotal = sohTotal.add(Utils.parseBigDecimal(temp.get("STOCK_ON_HAND")));
                    opoTotal = opoTotal.add(Utils.parseBigDecimal(temp.get("OPEN_PO_QTY")));
                    moiTotal = moiTotal.add(Utils.parseBigDecimal(temp.get("MTD_ORDER_INTAKE")));
                    msTotal = msTotal.add(Utils.parseBigDecimal(temp.get("MTD_SALES")));
                    hasAdded = true;
                }

                temp = supplyMaterialTotalMap.get(weight.getKey());
                if (temp != null) {
                    for (String key : temp.keySet()) {
                        if (dates.contains(key) || key.startsWith("TIPS_")) {
                            supplyTotal.put(key, Utils.parseBigDecimal(temp.get(key)).add(Utils.parseBigDecimal(supplyTotal.get(key))));
                        }
                    }
                    if (hasAdded == false) {
                        ssTotal = ssTotal.add(Utils.parseBigDecimal(temp.get("SAFETY_STOCK")));
                        amuTotal = amuTotal.add(Utils.parseBigDecimal(temp.get("AMU")));
                        amfTotal = amfTotal.add(Utils.parseBigDecimal(temp.get("AMF")));
                        sohTotal = sohTotal.add(Utils.parseBigDecimal(temp.get("STOCK_ON_HAND")));
                        opoTotal = opoTotal.add(Utils.parseBigDecimal(temp.get("OPEN_PO_QTY")));
                        moiTotal = moiTotal.add(Utils.parseBigDecimal(temp.get("MTD_ORDER_INTAKE")));
                        msTotal = msTotal.add(Utils.parseBigDecimal(temp.get("MTD_SALES")));
                    }
                }
            }
        }
        demandTotal.put("SAFETY_STOCK", ssTotal);
        demandTotal.put("AMF", amfTotal);
        demandTotal.put("AMU", amuTotal);
        demandTotal.put("STOCK_ON_HAND", sohTotal);
        demandTotal.put("OPEN_PO_QTY", opoTotal);
        demandTotal.put("MTD_ORDER_INTAKE", moiTotal);
        demandTotal.put("MTD_SALES", msTotal);

        supplyTotal.put("SAFETY_STOCK", ssTotal);
        supplyTotal.put("AMF", amfTotal);
        supplyTotal.put("AMU", amuTotal);
        supplyTotal.put("STOCK_ON_HAND", sohTotal);
        supplyTotal.put("OPEN_PO_QTY", opoTotal);
        supplyTotal.put("MTD_ORDER_INTAKE", moiTotal);
        supplyTotal.put("MTD_SALES", msTotal);

        balanceTotal.put("SAFETY_STOCK", ssTotal);
        balanceTotal.put("AMF", amfTotal);
        balanceTotal.put("AMU", amuTotal);
        balanceTotal.put("STOCK_ON_HAND", sohTotal);
        balanceTotal.put("OPEN_PO_QTY", opoTotal);
        balanceTotal.put("MTD_ORDER_INTAKE", moiTotal);
        balanceTotal.put("MTD_SALES", msTotal);

        // 计算Total Balance
        if (balanceWeightList.isEmpty() == false) {
            BigDecimal b;
            BigDecimal lastBalance = BigDecimal.ZERO;
            for (int i = 0; i < dates.size(); i++) {
                String dateKey = dates.get(i);
                if (i == 0) {
                    b = sohTotal.subtract(ssTotal.multiply(ssRatio)).subtract(amuTotal.multiply(amuRatio)).subtract(amfTotal.multiply(amfRatio)); // 这个物料的可用库存 = 当前库存 - SS - AMU - AMF
                } else {
                    b = lastBalance;
                }

                // 如果是By Day, 当日供给无法抵消当日需求
                if ("By Day".equals(viewBy)) {
                    b = b.subtract(Utils.parseBigDecimal(demandTotal.get(dateKey))); // balance = 可用库存 - 需求
                    balanceTotal.put(dateKey, b);
                    lastBalance = b.add(Utils.parseBigDecimal(supplyTotal.get(dateKey))); // 如果当日供给不为空, 则加到明日
                } else {
                    b = b.subtract(Utils.parseBigDecimal(demandTotal.get(dateKey))).add(Utils.parseBigDecimal(supplyTotal.get(dateKey))); // balance = 可用库存 - 需求 + 供给
                    lastBalance = b;
                    balanceTotal.put(dateKey, b);
                }
            }
        }


        // region 选择显示
        // 根据用户选择的显示行
        JSONArray selections = (JSONArray) parameterMap.get("demandSupplySelections");
        List<String> displayRow = new ArrayList<>();
        for (Object selection : selections) {
            JSONArray subSelections = (JSONArray) selection;
            if ("ROWS".equals(subSelections.getString(0))) {
                displayRow.add(subSelections.getString(1));
            }
        }
        List<Map<String, Object>> resultList = new ArrayList<>();

        /* 计算TOTAL PO_COVERAGE_DAYS */
        // SUPPLY TOTAL
        BigDecimal supplyBigDecimal = (BigDecimal) supplyTotal.get("OPEN_PO_QTY");
        double supplyopenPoQty = Double.parseDouble(supplyBigDecimal.toString());
        supplyBigDecimal = (BigDecimal) supplyTotal.get("AMF");
        double supplyAmf = Double.parseDouble(supplyBigDecimal.toString());
        supplyBigDecimal = (BigDecimal) supplyTotal.get("STOCK_ON_HAND");
        double supplyStockOnHand = Double.parseDouble(supplyBigDecimal.toString());
        double supplyCalAvg = 0;
        if (supplyAmf != 0) {
            supplyCalAvg = (supplyopenPoQty + supplyStockOnHand) / (supplyAmf / 30);
        }
        String totalConvertCalAvg = "0";
        totalConvertCalAvg = String.format("%.2f", supplyCalAvg);

        // 因为supply、demand、balance取值相同，所以统一使用SUPPLY TOTAL对应的PO_COVERAGE_DAYS
        supplyTotal.put("PO_COVERAGE_DAYS", totalConvertCalAvg);
        demandTotal.put("PO_COVERAGE_DAYS", totalConvertCalAvg);
        balanceTotal.put("PO_COVERAGE_DAYS", totalConvertCalAvg);

        resultList.add(demandTotal);
        resultList.add(supplyTotal);
        resultList.add(balanceTotal);

        // 挨个物料的放入待显示区, 筛选
        int i = 1;
        for (DemandSupplyWeight weight : balanceWeightList) {
            List<Map<String, Object>> tempList = new ArrayList<>();
            String convertCalAvg = "0";
            if (weight.isGroup()) {
                if (displayRow.contains("DEMAND_DETAILS") || displayRow.isEmpty()) {
                    if (demandGroupMap.get(weight.getKey()) != null) {
                        // open po value
                        BigDecimal bigDecimal = (BigDecimal) demandGroupMap.get(weight.getKey()).get(0).get("OPEN_PO_QTY");
                        double openPoQty = Double.parseDouble(bigDecimal.toString());
                        // AMF
                        bigDecimal = (BigDecimal) demandGroupMap.get(weight.getKey()).get(0).get("AMF");
                        double AMF = Double.parseDouble(bigDecimal.toString());
                        // stock on hand
                        bigDecimal = (BigDecimal) demandGroupMap.get(weight.getKey()).get(0).get("STOCK_ON_HAND");
                        double stockOnHand = Double.parseDouble(bigDecimal.toString());

                        double calAvg = 0;
                        if (AMF != 0) {
                            calAvg = (openPoQty + stockOnHand) / (AMF / 30);
                        }
                        convertCalAvg = String.format("%.2f", calAvg);
                        demandGroupMap.get(weight.getKey()).get(0).put("PO_COVERAGE_DAYS", convertCalAvg);

                        tempList.addAll(demandGroupMap.get(weight.getKey()));
                    } else {
                        if (displayRow.contains("DEMAND_DETAILS")) {
                            Map<String, Object> temp = new HashMap<>(groupBalanceMap.get(weight.getKey()));
                            temp.put("CATEGORY", "Demand_No Records");
                            temp.put("COMMENTS", ""); // 复制BALANCE对象的时候, 不复制comments
                            temp.put("CRITICAL_LEVEL", ""); // 复制BALANCE对象的时候, 不复制critical_level
                            for (String date : dates) {
                                temp.put(date, null);
                            }
                            tempList.add(temp);
                        }
                    }
                }
                if (displayRow.contains("DEMAND_TOTAL") || displayRow.isEmpty()) {
                    if (demandGroupTotalMap.get(weight.getKey()) != null) {
                        // open po value
                        BigDecimal bigDecimal = (BigDecimal) demandGroupTotalMap.get(weight.getKey()).get("OPEN_PO_QTY");
                        double openPoQty = Double.parseDouble(bigDecimal.toString());
                        // AMF
                        bigDecimal = (BigDecimal) demandGroupTotalMap.get(weight.getKey()).get("AMF");
                        double AMF = Double.parseDouble(bigDecimal.toString());
                        // stock on hand
                        bigDecimal = (BigDecimal) demandGroupTotalMap.get(weight.getKey()).get("STOCK_ON_HAND");
                        double stockOnHand = Double.parseDouble(bigDecimal.toString());

                        double calAvg = 0;
                        if (AMF != 0) {
                            calAvg = (openPoQty + stockOnHand) / (AMF / 30);
                        }
                        convertCalAvg = String.format("%.2f", calAvg);
                        demandGroupTotalMap.get(weight.getKey()).put("PO_COVERAGE_DAYS", convertCalAvg);
                        tempList.add(demandGroupTotalMap.get(weight.getKey()));
                    } else {
                        Map<String, Object> temp = new HashMap<>(groupBalanceMap.get(weight.getKey()));
                        temp.put("CATEGORY", "Demand Total");
                        temp.put("COMMENTS", ""); // 复制BALANCE对象的时候, 不复制comments
                        temp.put("CRITICAL_LEVEL", ""); // 复制BALANCE对象的时候, 不复制critical_level
                        for (String date : dates) {
                            temp.put(date, BigDecimal.ZERO);
                        }
                        tempList.add(temp);
                    }
                }
                if (displayRow.contains("SUPPLY_DETAILS") || displayRow.isEmpty()) {
                    if (supplyGroupMap.get(weight.getKey()) != null) {
                        // open po value
                        BigDecimal bigDecimal = (BigDecimal) supplyGroupMap.get(weight.getKey()).get(0).get("OPEN_PO_QTY");
                        double openPoQty = Double.parseDouble(bigDecimal.toString());
                        // AMF
                        bigDecimal = (BigDecimal) supplyGroupMap.get(weight.getKey()).get(0).get("AMF");
                        double AMF = Double.parseDouble(bigDecimal.toString());
                        // stock on hand
                        bigDecimal = (BigDecimal) supplyGroupMap.get(weight.getKey()).get(0).get("STOCK_ON_HAND");
                        double stockOnHand = Double.parseDouble(bigDecimal.toString());

                        double calAvg = 0;
                        if (AMF != 0) {
                            calAvg = (openPoQty + stockOnHand) / (AMF / 30);
                        }
                        convertCalAvg = String.format("%.2f", calAvg);
                        supplyGroupMap.get(weight.getKey()).get(0).put("PO_COVERAGE_DAYS", convertCalAvg);
                        tempList.addAll(supplyGroupMap.get(weight.getKey()));
                    } else {
                        if (displayRow.contains("SUPPLY_DETAILS")) {
                            Map<String, Object> temp = new HashMap<>(groupBalanceMap.get(weight.getKey()));
                            temp.put("CATEGORY", "Supply_No Records");
                            temp.put("COMMENTS", ""); // 复制BALANCE对象的时候, 不复制comments
                            temp.put("CRITICAL_LEVEL", ""); // 复制BALANCE对象的时候, 不复制critical_level
                            for (String date : dates) {
                                temp.put(date, null);
                            }
                            tempList.add(temp);
                        }
                    }
                }
                if (displayRow.contains("SUPPLY_TOTAL") || displayRow.isEmpty()) {
                    if (supplyGroupTotalMap.get(weight.getKey()) != null) {
                        // open po value
                        BigDecimal bigDecimal = (BigDecimal) supplyGroupTotalMap.get(weight.getKey()).get("OPEN_PO_QTY");
                        double openPoQty = Double.parseDouble(bigDecimal.toString());
                        // AMF
                        bigDecimal = (BigDecimal) supplyGroupTotalMap.get(weight.getKey()).get("AMF");
                        double AMF = Double.parseDouble(bigDecimal.toString());
                        // stock on hand
                        bigDecimal = (BigDecimal) supplyGroupTotalMap.get(weight.getKey()).get("STOCK_ON_HAND");
                        double stockOnHand = Double.parseDouble(bigDecimal.toString());

                        double calAvg = 0;
                        if (AMF != 0) {
                            calAvg = (openPoQty + stockOnHand) / (AMF / 30);
                        }
                        convertCalAvg = String.format("%.2f", calAvg);
                        supplyGroupTotalMap.get(weight.getKey()).put("PO_COVERAGE_DAYS", convertCalAvg);

                        tempList.add(supplyGroupTotalMap.get(weight.getKey()));
                    } else {
                        Map<String, Object> temp = new HashMap<>(groupBalanceMap.get(weight.getKey()));
                        temp.put("CATEGORY", "Supply Total");
                        temp.put("COMMENTS", ""); // 复制BALANCE对象的时候, 不复制comments
                        temp.put("CRITICAL_LEVEL", ""); // 复制BALANCE对象的时候, 不复制critical_level
                        for (String date : dates) {
                            temp.put(date, BigDecimal.ZERO);
                        }
                        tempList.add(temp);
                    }
                }
                if (displayRow.contains("BALANCE") || displayRow.isEmpty()) {
                    // open po value
                    BigDecimal bigDecimal = (BigDecimal) groupBalanceMap.get(weight.getKey()).get("OPEN_PO_QTY");
                    double openPoQty = Double.parseDouble(bigDecimal.toString());
                    // AMF
                    bigDecimal = (BigDecimal) groupBalanceMap.get(weight.getKey()).get("AMF");
                    double AMF = Double.parseDouble(bigDecimal.toString());
                    // stock on hand
                    bigDecimal = (BigDecimal) groupBalanceMap.get(weight.getKey()).get("STOCK_ON_HAND");
                    double stockOnHand = Double.parseDouble(bigDecimal.toString());

                    double calAvg = 0;
                    if (AMF != 0) {
                        calAvg = (openPoQty + stockOnHand) / (AMF / 30);
                    }
                    convertCalAvg = String.format("%.2f", calAvg);
                    groupBalanceMap.get(weight.getKey()).put("PO_COVERAGE_DAYS", convertCalAvg);

                    tempList.add(groupBalanceMap.get(weight.getKey()));
                }
            } else if (weight.isMaterial()) {
                if (displayRow.contains("DEMAND_DETAILS") || displayRow.isEmpty()) {
                    if (demandMaterialMap.get(weight.getKey()) != null) {
                        // open po value
                        BigDecimal bigDecimal = (BigDecimal) demandMaterialMap.get(weight.getKey()).get(0).get("OPEN_PO_QTY");
                        double openPoQty = Double.parseDouble(bigDecimal.toString());
                        // AMF
                        bigDecimal = (BigDecimal) demandMaterialMap.get(weight.getKey()).get(0).get("AMF");
                        double AMF = Double.parseDouble(bigDecimal.toString());
                        // stock on hand
                        bigDecimal = (BigDecimal) demandMaterialMap.get(weight.getKey()).get(0).get("STOCK_ON_HAND");
                        double stockOnHand = Double.parseDouble(bigDecimal.toString());
                        double calAvg = 0;
                        if (AMF != 0) {
                            calAvg = (openPoQty + stockOnHand) / (AMF / 30);
                        }
                        convertCalAvg = String.format("%.2f", calAvg);
                        demandMaterialMap.get(weight.getKey()).get(0).put("PO_COVERAGE_DAYS", convertCalAvg);

                        tempList.addAll(demandMaterialMap.get(weight.getKey()));
                    } else {
                        if (displayRow.contains("DEMAND_DETAILS")) {
                            Map<String, Object> temp = new HashMap<>(materialBalanceMap.get(weight.getKey()));
                            temp.put("CATEGORY", "Demand_No Records");
                            temp.put("COMMENTS", ""); // 复制BALANCE对象的时候, 不复制comments
                            temp.put("CRITICAL_LEVEL", ""); // 复制BALANCE对象的时候, 不复制critical_level
                            for (String date : dates) {
                                temp.put(date, null);
                            }
                            tempList.add(temp);
                        }
                    }
                }
                if (displayRow.contains("DEMAND_TOTAL") || displayRow.isEmpty()) {
                    if (demandMaterialTotalMap.get(weight.getKey()) != null) {
                        // open po value
                        BigDecimal bigDecimal = (BigDecimal) demandMaterialTotalMap.get(weight.getKey()).get("OPEN_PO_QTY");
                        double openPoQty = Double.parseDouble(bigDecimal.toString());
                        // AMF
                        bigDecimal = (BigDecimal) demandMaterialTotalMap.get(weight.getKey()).get("AMF");
                        double AMF = Double.parseDouble(bigDecimal.toString());
                        // stock on hand
                        bigDecimal = (BigDecimal) demandMaterialTotalMap.get(weight.getKey()).get("STOCK_ON_HAND");
                        double stockOnHand = Double.parseDouble(bigDecimal.toString());
                        double calAvg = 0;
                        if (AMF != 0) {
                            calAvg = (openPoQty + stockOnHand) / (AMF / 30);
                        }
                        convertCalAvg = String.format("%.2f", calAvg);
                        demandMaterialTotalMap.get(weight.getKey()).put("PO_COVERAGE_DAYS", convertCalAvg);

                        tempList.add(demandMaterialTotalMap.get(weight.getKey()));
                    } else {
                        Map<String, Object> temp = new HashMap<>(materialBalanceMap.get(weight.getKey()));
                        temp.put("CATEGORY", "Demand Total");
                        temp.put("COMMENTS", ""); // 复制BALANCE对象的时候, 不复制comments
                        temp.put("CRITICAL_LEVEL", ""); // 复制BALANCE对象的时候, 不复制critical_level
                        for (String date : dates) {
                            temp.put(date, BigDecimal.ZERO);
                        }
                        tempList.add(temp);
                    }
                }
                if (displayRow.contains("SUPPLY_DETAILS") || displayRow.isEmpty()) {
                    if (supplyMaterialMap.get(weight.getKey()) != null) {
                        // open po value
                        BigDecimal bigDecimal = (BigDecimal) supplyMaterialMap.get(weight.getKey()).get(0).get("OPEN_PO_QTY");
                        double openPoQty = Double.parseDouble(bigDecimal.toString());
                        // AMF
                        bigDecimal = (BigDecimal) supplyMaterialMap.get(weight.getKey()).get(0).get("AMF");
                        double AMF = Double.parseDouble(bigDecimal.toString());
                        // stock on hand
                        bigDecimal = (BigDecimal) supplyMaterialMap.get(weight.getKey()).get(0).get("STOCK_ON_HAND");
                        double stockOnHand = Double.parseDouble(bigDecimal.toString());

                        double calAvg = 0;
                        if (AMF != 0) {
                            calAvg = (openPoQty + stockOnHand) / (AMF / 30);
                        }
                        convertCalAvg = String.format("%.2f", calAvg);
                        supplyMaterialMap.get(weight.getKey()).get(0).put("PO_COVERAGE_DAYS", convertCalAvg);

                        tempList.addAll(supplyMaterialMap.get(weight.getKey()));
                    } else {
                        if (displayRow.contains("SUPPLY_DETAILS")) {
                            Map<String, Object> temp = new HashMap<>(materialBalanceMap.get(weight.getKey()));
                            temp.put("CATEGORY", "Supply_No Records");
                            temp.put("COMMENTS", ""); // 复制BALANCE对象的时候, 不复制comments
                            temp.put("CRITICAL_LEVEL", ""); // 复制BALANCE对象的时候, 不复制critical_level
                            for (String date : dates) {
                                temp.put(date, null);
                            }
                            tempList.add(temp);
                        }
                    }
                }
                if (displayRow.contains("SUPPLY_TOTAL") || displayRow.isEmpty()) {
                    if (supplyMaterialTotalMap.get(weight.getKey()) != null) {
                        // open po value
                        BigDecimal bigDecimal = (BigDecimal) supplyMaterialTotalMap.get(weight.getKey()).get("OPEN_PO_QTY");
                        double openPoQty = Double.parseDouble(bigDecimal.toString());
                        // AMF
                        bigDecimal = (BigDecimal) supplyMaterialTotalMap.get(weight.getKey()).get("AMF");
                        double AMF = Double.parseDouble(bigDecimal.toString());
                        // stock on hand
                        bigDecimal = (BigDecimal) supplyMaterialTotalMap.get(weight.getKey()).get("STOCK_ON_HAND");
                        double stockOnHand = Double.parseDouble(bigDecimal.toString());

                        double calAvg = 0;
                        if (AMF != 0) {
                            calAvg = (openPoQty + stockOnHand) / (AMF / 30);
                        }
                        convertCalAvg = String.format("%.2f", calAvg);
                        supplyMaterialTotalMap.get(weight.getKey()).put("PO_COVERAGE_DAYS", convertCalAvg);

                        tempList.add(supplyMaterialTotalMap.get(weight.getKey()));
                    } else {
                        Map<String, Object> temp = new HashMap<>(materialBalanceMap.get(weight.getKey()));
                        temp.put("CATEGORY", "Supply Total");
                        temp.put("COMMENTS", ""); // 复制BALANCE对象的时候, 不复制comments
                        temp.put("CRITICAL_LEVEL", ""); // 复制BALANCE对象的时候, 不复制critical_level
                        for (String date : dates) {
                            temp.put(date, BigDecimal.ZERO);
                        }
                        tempList.add(temp);
                    }
                }
                if (displayRow.contains("BALANCE") || displayRow.isEmpty()) {
                    // open po value
                    BigDecimal bigDecimal = (BigDecimal) materialBalanceMap.get(weight.getKey()).get("OPEN_PO_QTY");
                    double openPoQty = Double.parseDouble(bigDecimal.toString());
                    // AMF
                    bigDecimal = (BigDecimal) materialBalanceMap.get(weight.getKey()).get("AMF");
                    double AMF = Double.parseDouble(bigDecimal.toString());
                    // stock on hand
                    bigDecimal = (BigDecimal) materialBalanceMap.get(weight.getKey()).get("STOCK_ON_HAND");
                    double stockOnHand = Double.parseDouble(bigDecimal.toString());

                    double calAvg = 0;
                    if (AMF != 0) {
                        calAvg = (openPoQty + stockOnHand) / (AMF / 30);
                    }
                    convertCalAvg = String.format("%.2f", calAvg);
                    materialBalanceMap.get(weight.getKey()).put("PO_COVERAGE_DAYS", convertCalAvg);

                    tempList.add(materialBalanceMap.get(weight.getKey()));
                }
            }

            for (int j = 0; j < tempList.size(); j++) {
                tempList.get(j).putIfAbsent("PO_COVERAGE_DAYS", convertCalAvg);
            }

            // 染色
            for (Map<String, Object> map : tempList) {
                map.put("COLOR", i % 2);
            }
            i++;
            resultList.addAll(tempList);
        }
        // endregion

        return response.setBody(resultList);
    }

    @Override
    @SuppressWarnings("unchecked")
    public Response queryReport1Details(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);

        String category = (String) ((Map<String, Object>) parameterMap.get("selected")).get("report1Category");
        if (StringUtils.indexOf(category, "Supply") == 0) {
            String category_vendor = StringUtils.remove(category, "Supply_");

            if (category_vendor.contains("(FCST_Cmt)")) {
                parameterMap.put("confirm_cat", "FCST Commitment");
                parameterMap.put("category_vendor", StringUtils.remove(category_vendor, "(FCST_Cmt)"));
            } else {
                parameterMap.remove("confirm_cat");
                parameterMap.put("category_vendor", category_vendor);
            }
        } else {
            parameterMap.put("category", "demand");
        }

        String viewBy = (String) parameterMap.get("viewBy");
        switch (viewBy) {
            case "By Day":
                parameterMap.put("dateColumnName", "BY_DAY");
                break;
            case "By Week":
                parameterMap.put("dateColumnName", "BY_WEEK");
                break;
            case "By Month":
                parameterMap.put("dateColumnName", "BY_MONTH");
                break;
            default:
                return response;
        }

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(plantDemandSupplyDao.queryReport1DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(plantDemandSupplyDao.queryReport1Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    @SuppressWarnings("unchecked")
    public void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateCascaderFilter(parameterMap);

        String category = (String) ((Map<String, Object>) parameterMap.get("selected")).get("report1Category");
        if (StringUtils.indexOf(category, "Supply") == 0) {
            parameterMap.put("category_vendor", StringUtils.remove(category, "Supply_"));
        } else {
            parameterMap.put("category", "demand");
        }

        String viewBy = (String) parameterMap.get("viewBy");
        switch (viewBy) {
            case "By Day":
                parameterMap.put("dateColumnName", "BY_DAY");
                break;
            case "By Week":
                parameterMap.put("dateColumnName", "BY_WEEK");
                break;
            case "By Month":
                parameterMap.put("dateColumnName", "BY_MONTH");
                break;
            default:
                return;
        }

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "demand_supply_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.simulation.dao.IPlantDemandSupplyDao.queryReport1Details", parameterMap);
    }

    @Override
    @SuppressWarnings("unchecked")
    public Response saveReport1Comments(Map<String, Object> parameterMap, String userid) {
        String weekNo = plantDemandSupplyDao.queryCurrentWeek();
        if (weekNo == null) {
            weekNo = new SimpleDateFormat("yyyyww").format(new Date());
        }

        Map<String, String> comments = (Map<String, String>) parameterMap.get("comments");
        List<Map<String, String>> result = new ArrayList<>();

        for (String key : comments.keySet()) {
            String[] ks = StringUtils.split(key, "#");
            if (ks != null && ks.length == 2) {
                Map<String, String> map = new HashMap<>();
                map.put("material", ks[0]);
                map.put("plantCode", ks[1]);
                map.put("comment", comments.get(key));
                result.add(map);
            }
        }
        if (result.isEmpty() == false) {
            plantDemandSupplyDao.mergeReport1Comments(result, userid, weekNo);
        }

        return response;
    }

    @Override
    public Response queryReport1HistoryComments(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(plantDemandSupplyDao.queryReport1HistoryCommentsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(plantDemandSupplyDao.queryReport1HistoryComments(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport1HistoryComments(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "history_comments_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.simulation.dao.IPlantDemandSupplyDao.queryReport1HistoryComments", parameterMap);
    }

    @Override
    public Response queryReport1CriticalLevel(Map<String, Object> parameterMap) {
        Map<String, Object> criticalLevelMap = plantDemandSupplyDao.queryReport1CriticalLevel(parameterMap);
        criticalLevelMap.put("SCRIPTS", Utils.clob2String(criticalLevelMap.get("SCRIPTS")));
        return response.setBody(criticalLevelMap);
    }

    // endregion

    // region report3

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(plantDemandSupplyDao.queryReport3Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(plantDemandSupplyDao.queryReport3(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport3(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "po_commit_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.simulation.dao.IPlantDemandSupplyDao.downloadReport3", parameterMap);
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response saveReport3(String userid, Map<String, Object> parameterMap) {
        scpTableHelper.setExcludeColumn(new ArrayList<>() {{
            add("ROW_ID");
        }});
        scpTableHelper.setWarningMessage("You have no privileges to modify data does not belong to you");
        scpTableHelper.setScpTableInsertHandler((headers, inserts) -> plantDemandSupplyDao.createReport3ByTable(headers, inserts, userid));
        scpTableHelper.setScpTableDeleteHandler(deletes -> plantDemandSupplyDao.deleteReport3ByTable(deletes, userid));
        scpTableHelper.setScpTableUpdateHandler((pk, updates) -> plantDemandSupplyDao.updateReport3ByTable(pk, updates, userid));

        Message message = scpTableHelper.execCRUD(parameterMap);
        return response.setBody(message);
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response uploadReport3(String userid, MultipartFile file) throws Exception {
        File tempFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), ".tmp");
        file.transferTo(tempFile);

        List<Map<String, Object>> data = new ArrayList<>();
        excelTemplate.read(tempFile, 1, new SimpleSheetContentsHandler() {
            @Override
            public void handleRow(int rowNum) {

                if (rowNum == 0) {
                    return;
                }

                Map<String, Object> map = new HashMap<>();

                int i = 0;
                map.put("PURCH_ORDER_NUMBER", row.get(i++));
                map.put("PURCH_ORDER_ITEM", row.get(i++));
                map.put("SEQUENTIAL_NUMBER", row.get(i++));
                map.put("PLANT_CODE", row.get(i++));
                map.put("MATERIAL", row.get(i++));
                map.put("QTY", row.get(i++));
                map.put("COMMIT_DATE", DateCalUtil.getDateStrFromExcel(row.get(i)));

                data.add(map);
                if (data.size() >= 64) {
                    plantDemandSupplyDao.insertReport3DataTemp(data);
                    data.clear();
                }
            }
        }, new StylesTable());

        if (data.size() > 0) {
            plantDemandSupplyDao.insertReport3DataTemp(data);
            data.clear();
        }

        plantDemandSupplyDao.mergeReport3Data(userid);

        if (tempFile.delete() == false) {
            System.err.println(tempFile.getAbsolutePath() + " delete failed");
        }

        return response;
    }

    @Override
    public void downloadReport3Template(Map<String, Object> parameterMap, HttpServletResponse response) {
        String fileName = "po_commit_template.xlsx";
        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        resultList.add(map);
        map.put("PURCH_ORDER_NUMBER", null);
        map.put("PURCH_ORDER_ITEM", null);
        map.put("SEQUENTIAL_NUMBER", null);
        map.put("PLANT_CODE", null);
        map.put("MATERIAL", null);
        map.put("QTY", null);
        map.put("COMMIT_DATE", null);
        excelTemplate.create(response, fileName, resultList);
    }

    // endregion

    // region report4
    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport4(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(plantDemandSupplyDao.queryReport4Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(plantDemandSupplyDao.queryReport4(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport4(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "critical_material_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.simulation.dao.IPlantDemandSupplyDao.downloadReport4", parameterMap);
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response saveReport4(String userid, Map<String, Object> parameterMap) {
        List<String> avaliblePlant = plantDemandSupplyDao.queryAllPlant();
        scpTableHelper.setExcludeColumn(new ArrayList<>() {{
            add("ROW_ID");
        }});
        scpTableHelper.setWarningMessage("You have no privileges to modify data does not belong to you");
        scpTableHelper.setScpTableInsertHandler((headers, inserts) -> {
            if (headers.contains("PLANT_CODE")) {
                for (Map<String, Object> insert : inserts) {
                    String plantCode = (String) insert.get("PLANT_CODE");
                    if (avaliblePlant.contains(plantCode) == false) {
                        throw new SCPRuntimeException("invalid plant code found: " + plantCode);
                    }
                }
            }

            return plantDemandSupplyDao.createReport4ByTable(headers, inserts, userid);
        });
        scpTableHelper.setScpTableDeleteHandler(deletes -> plantDemandSupplyDao.deleteReport4ByTable(deletes, userid));
        scpTableHelper.setScpTableUpdateHandler((pk, updates) -> {
            if (updates.stream().anyMatch(e -> e.getKey().equals("PLANT_CODE") && avaliblePlant.contains((String) e.getValue()) == false)) {
                String plantCode = (String) updates.stream().filter(e -> e.getKey().equals("PLANT_CODE") && avaliblePlant.contains((String) e.getValue()) == false).collect(Collectors.toList()).get(0).getValue();
                throw new SCPRuntimeException("invalid plant code found: " + plantCode);
            }
            return plantDemandSupplyDao.updateReport4ByTable(pk, updates, userid);
        });
        Message message = scpTableHelper.execCRUD(parameterMap);
        plantDemandSupplyDao.refreshCriticalMaterial();
        return response.setBody(message);
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response uploadReport4(String userid, MultipartFile file) throws Exception {
        File tempFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), ".tmp");
        file.transferTo(tempFile);
        List<String> avaliblePlant = plantDemandSupplyDao.queryAllPlant();

        List<Map<String, Object>> data = new ArrayList<>();
        excelTemplate.read(tempFile, 1, new SimpleSheetContentsHandler() {
            @Override
            public void handleRow(int rowNum) {

                if (rowNum == 0) {
                    return;
                }

                if (avaliblePlant.contains(row.get(0)) == false) {
                    throw new SCPRuntimeException("invalid plant code found: " + row.get(0));
                }

                Map<String, Object> map = new HashMap<>();

                map.put("PLANT_CODE", row.get(0));
                map.put("MATERIAL", row.get(1));

                data.add(map);
                if (data.size() >= 64) {
                    plantDemandSupplyDao.insertReport4DataTemp(data);
                    data.clear();
                }
            }
        }, new StylesTable());

        if (data.size() > 0) {
            plantDemandSupplyDao.insertReport4DataTemp(data);
            data.clear();
        }

        List<Map<String, Object>> duplicateRow = plantDemandSupplyDao.queryDeplicateRows();
        if (duplicateRow.isEmpty() == false) {
            throw new SCPRuntimeException("Duplicate rows found <br>" + StringUtils.join(duplicateRow.stream().map(e -> e.get("MATERIAL") + " => " + e.get("CNT")).toList(), "<br>"));
        }

        plantDemandSupplyDao.mergeReport4Data(userid);
        plantDemandSupplyDao.refreshCriticalMaterial();

        if (tempFile.delete() == false) {
            System.err.println(tempFile.getAbsolutePath() + " delete failed");
        }

        return response;
    }

    @Override
    public void downloadReport4Template(Map<String, Object> parameterMap, HttpServletResponse response) {
        String fileName = "critical_material_template.xlsx";
        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        resultList.add(map);
        map.put("PLANT_CODE", null);
        map.put("MATERIAL", null);
        excelTemplate.create(response, fileName, resultList);
    }


    // endregion

    private void generateCascaderFilter(Map<String, Object> parameterMap) {
        // OPENSO 分类
        List<String> openSOTypeList = new ArrayList<>();
        openSOTypeList.add("OSO_CB");
        openSOTypeList.add("UD_CB");
        openSOTypeList.add("UD_LONG_AGING");
        openSOTypeList.add("UD_MID_AGING");
        openSOTypeList.add("OSO_NORMAL");
        openSOTypeList.add("UD_NORMAL");

        // Transfer PO 分类
        List<String> transferPOTypeList = new ArrayList<>();
        transferPOTypeList.add("TRANSFER_PO_AT");
        transferPOTypeList.add("TRANSFER_PO_Q");
        transferPOTypeList.add("TRANSFER_PO_Z");
        transferPOTypeList.add("TRANSFER_PO_E");
        transferPOTypeList.add("TRANSFER_PO_OTHERS");
        // 生成筛选条件
        JSONArray categoryArray = (JSONArray) parameterMap.get("filterList");
        if (categoryArray != null) {
            Map<String, List<String>> filterMap = new HashMap<>();

            for (Object subObj : categoryArray) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = subArray.getString(0);

                String value = subArray.getString(1);
                String key = Utils.randomStr(8);

                if (Utils.hasInjectionAttack(columnName)) {
                    continue;
                }

                List<String> fl = filterMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                fl.add("#{" + key + ",jdbcType=VARCHAR}");
                parameterMap.put(key, value);
            }

            List<String> filterList = new ArrayList<>();

            for (String key : filterMap.keySet()) {
                List<String> fl = filterMap.get(key);
                filterList.add("t." + key + " in (" + StringUtils.join(fl, ",") + ")");
            }

            parameterMap.put("filters", StringUtils.join(filterList, " and "));
        }

        // 生成筛选条件
        JSONArray selectionArray = (JSONArray) parameterMap.get("demandSupplySelections");
        if (selectionArray != null) {
            Map<String, List<String>> filterMap = new HashMap<>();
            Map<String, List<String>> valueMap = new HashMap<>();

            for (Object subObj : selectionArray) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = subArray.getString(0);

                String value = subArray.getString(1);
                String key = Utils.randomStr(8);

                if (Utils.hasInjectionAttack(columnName)) {
                    continue;
                }

                List<String> fl = filterMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                List<String> fv = valueMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                fl.add("#{" + key + ",jdbcType=VARCHAR}");
                fv.add(value);
                parameterMap.put(key, value);
            }

            List<String> demandFilterList = new ArrayList<>();
            List<String> supplyFilterList = new ArrayList<>();
            List<String> openSOExcludeFilterList = new ArrayList<>();
            List<String> transferPOExcludeFilterList = new ArrayList<>();

            List<String> selectedOpenSO = new ArrayList<>();
            List<String> selectedTransferPO = new ArrayList<>();

            for (String key : filterMap.keySet()) {
                List<String> fl = filterMap.get(key);
                List<String> fv = valueMap.get(key);
                if ("DEMAND_CATEGORY".equals(key)) {
                    demandFilterList.add("t." + key + " in (" + StringUtils.join(fl, ",") + ")");
                } else if ("CONFIRM_CAT".equals(key)) {
                    supplyFilterList.add("t." + key + " in (" + StringUtils.join(fl, ",") + ")");
                } else if ("OPEN_SO".equals(key)) {
                    selectedOpenSO.addAll(fv);
                } else if ("TRANSFER_PO".equals(key)) {
                    selectedTransferPO.addAll(fv);
                }
            }

            // 如果用户选择了OPEN SO, 那就说明未选择的部分需要减去
            if (selectedOpenSO.isEmpty() == false) {
                openSOExcludeFilterList = Utils.subtractList(openSOTypeList, selectedOpenSO); // 未选择的部分 = 总 - 选择的部分
            }
            // 如果用户选择了Transfer PO, 那就说明未选择的部分需要减去
            if (selectedTransferPO.isEmpty() == false) {
                transferPOExcludeFilterList = Utils.subtractList(transferPOTypeList, selectedTransferPO); // 未选择的部分 = 总 - 选择的部分
            }

            parameterMap.put("openSOExcludeFilterList", openSOExcludeFilterList);
            parameterMap.put("transferPOExcludeFilterList", transferPOExcludeFilterList);
            parameterMap.put("demandFilterList", StringUtils.join(demandFilterList, " and "));
            parameterMap.put("supplyFilterList", StringUtils.join(supplyFilterList, " and "));
        }

        // load special parameter
        String specialContent = (String) parameterMap.get("specialContent");
        String specialColumn = (String) parameterMap.get("specialType");
        if (Utils.hasInjectionAttack(specialColumn) == false) {
            if (StringUtils.isNotBlank(specialContent)) {
                List<List<String>> value = Utils.splitValue(specialContent, 500);
                if (value.isEmpty() == false) {
                    parameterMap.put("specialList", value);
                    parameterMap.put("specialColumn", specialColumn);
                }
            }
        }
    }
}
