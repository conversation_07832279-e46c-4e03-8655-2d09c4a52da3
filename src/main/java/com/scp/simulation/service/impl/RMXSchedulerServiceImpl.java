package com.scp.simulation.service.impl;

import com.alibaba.fastjson.JSON;
import com.scp.simulation.bean.CalendarBean;
import com.scp.simulation.bean.RmxCapacity;
import com.scp.simulation.bean.RmxConfig;
import com.scp.simulation.bean.RmxMoInfo;
import com.scp.simulation.dao.IRMXSchedulerDao;
import com.scp.simulation.service.IRMXSchedulerService;
import com.scp.simulation.service.rmxscheduler.RmxScheduler;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.utils.DateCalUtil;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import com.starter.utils.excel.SimpleSheetContentsHandler;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.model.StylesTable;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.context.annotation.Scope;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.*;

@Service("rmsSchedulerService")
@Scope("prototype")
@Transactional
public class RMXSchedulerServiceImpl implements IRMXSchedulerService {

    @Resource
    private Response response;

    @Resource
    private IRMXSchedulerDao schedulerDao;

    @Resource
    private ExcelTemplate excelTemplate;

    private static final String DEFAULT_CAPACITY = "60";

    @Override
    public Response initPage(Map<String, Object> parameterMap) {
        Map<String, Object> result = new HashMap<>();
        result.put("startDate", new SimpleDateFormat("yyyy/MM/dd").format(new Date()));
        try {
            String startDate = schedulerDao.querySchedulerStartDate();
            if (startDate != null) {
                result.put("startDate", startDate);
            }
        } catch (Exception ignored) {

        }
        return response.setBody(result);
    }

    @Override
    public Response queryReport1(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        int total = schedulerDao.queryReport1Count(parameterMap);
        page.setTotal(total);
        if (total > 0) {
            page.setData(schedulerDao.queryReport1(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public Response queryReport1StartSchedule(Map<String, Object> parameterMap) {
        parameterMap.put("defaultCapacity", DEFAULT_CAPACITY);
        List<Map<String, String>> capacityList = schedulerDao.querySchedulerCapacity(parameterMap);
        List<RmxCapacity> capacity = new ArrayList<>();

        int defaultCapacity = Integer.parseInt(DEFAULT_CAPACITY);
        for (Map<String, String> map : capacityList) {
            String text = map.get("TEXT");
            String shift1 = map.get("SHIFT1");
            String shift2 = map.get("SHIFT2");
            String shift3 = map.get("SHIFT3");

            if ("-".equals(shift1) == false) {
                capacity.add(new RmxCapacity(text, "早", Utils.parseInt(shift1, defaultCapacity)));
            }
            if ("-".equals(shift2) == false) {
                capacity.add(new RmxCapacity(text, "中", Utils.parseInt(shift2, defaultCapacity)));
            }
            if ("-".equals(shift3) == false) {
                capacity.add(new RmxCapacity(text, "晚", Utils.parseInt(shift3, defaultCapacity)));
            }
        }

        schedulerDao.truncateReport1Result();
        List<RmxMoInfo> dataList = schedulerDao.queryReport1ScheduleRawData();
        RmxConfig rmxConfig = new RmxConfig(JSON.parseObject((String) parameterMap.get("config")));
        List<RmxMoInfo> resultList = new RmxScheduler(rmxConfig).start(dataList, capacity);
        for (int i = 0; i < resultList.size(); i++) {
            resultList.get(i).setSeqNo(i);
        }
        if (resultList.isEmpty() == false) {
            schedulerDao.saveReport1Result(resultList);
        }
        return response;
    }

    @Override
    public Response uploadReport1(MultipartFile file) {
        try {
            // 将文件保存在本地
            File tempFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), ".tmp");
            file.transferTo(tempFile);
            List<Map<String, Object>> dataList = new ArrayList<>();

            schedulerDao.truncateReport1Data();

            final int[] index = {1};
            excelTemplate.read(tempFile, 1, new SimpleSheetContentsHandler() {
                @Override
                public void handleRow(int rowNum) {
                    if (rowNum == 0) {
                        return;
                    }
                    // 从excel中取数据的时候要使用原始的表头
                    Map<String, Object> map = new HashMap<>();
                    boolean isBlackLine = true;
                    for (String v : row) {
                        if (StringUtils.isNotBlank(v)) {
                            isBlackLine = false;
                            break;
                        }
                    }
                    if (isBlackLine == false && row.size() >= 52) {
                        int i = 0;
                        map.put("SEQ_NO", index[0]++);
                        map.put("NO", row.get(i++));
                        map.put("EXPECTED_DATE", DateCalUtil.excelNumber2Date(row.get(i++), "yyyy/MM/dd"));
                        map.put("ACTUAL_DATE", DateCalUtil.excelNumber2Date(row.get(i++), "yyyy/MM/dd"));
                        map.put("MO_NUMBER", row.get(i++));
                        map.put("EXPECTED_FINISH_DATE", DateCalUtil.excelNumber2Date(row.get(i++), "yyyy/MM/dd"));
                        map.put("CUSTOMER_NAME", row.get(i++));
                        map.put("SALES_ORDER_NUMBER", row.get(i++));
                        map.put("SALES_ORDER_ITEM", row.get(i++));
                        map.put("FAMILY_NAME", row.get(i++));
                        map.put("MATERIAL", row.get(i++));
                        map.put("MATERIAL_TYPE", row.get(i++));
                        map.put("UNIT_FUNCTION_QTY", row.get(i++));
                        map.put("QTY", row.get(i++));
                        map.put("OPEN_QTY", row.get(i++));
                        map.put("TOTAL_OPEN_FUNCTION_QTY", row.get(i++));
                        map.put("FINISH_DATE", DateCalUtil.excelNumber2Date(row.get(i++), "yyyy/MM/dd"));
                        map.put("CUSTOMER_TYPE", row.get(i++));
                        map.put("MO_CREATE_DATE", DateCalUtil.excelNumber2Date(row.get(i++), "yyyy/MM/dd"));
                        map.put("CUSTOMER_DELIVERY_DATE", DateCalUtil.excelNumber2Date(row.get(i++), "yyyy/MM/dd"));
                        map.put("STANDARD_DELIVERY_DATE", DateCalUtil.excelNumber2Date(row.get(i++), "yyyy/MM/dd"));
                        map.put("CUSTOMER_REQUEST_DATE", DateCalUtil.excelNumber2Date(row.get(i++), "yyyy/MM/dd"));
                        map.put("STATION_DESC", row.get(i++));
                        map.put("TYPE3", row.get(i++));
                        map.put("LOW_VOLTAGE_ROOM", row.get(i++));
                        map.put("REMARKS", row.get(i++));
                        map.put("EXPECTED_PRODUCTION_DATE", row.get(i++));
                        map.put("SO_CREATE_DATE", DateCalUtil.excelNumber2Date(row.get(i++), "yyyy/MM/dd"));
                        map.put("PACKAGE_INFO", row.get(i++));
                        map.put("VOLTAGE_LEVEL", row.get(i++));
                        map.put("TANK", row.get(i++));
                        map.put("ANNOTATIONS", row.get(i++));
                        map.put("FRONT_FRAME", row.get(i++));
                        map.put("BOTTOM_FRAME", row.get(i++));
                        map.put("SO_ITEM", row.get(i++));
                        map.put("MO_QTY", row.get(i++));
                        map.put("DEMAND", row.get(i++));
                        map.put("NEW_EXCEPTION", row.get(i++));
                        map.put("VCB", row.get(i++));
                        map.put("HHHH", row.get(i++));
                        map.put("IIII", row.get(i++));
                        map.put("JJJJ", row.get(i++));
                        map.put("KKKK", row.get(i++));
                        map.put("LLLL", row.get(i++));
                        map.put("MMMM", row.get(i++));
                        map.put("NNNN", row.get(i++));
                        map.put("OOOO", row.get(i++));
                        map.put("PPPP", row.get(i++));
                        map.put("CUSTOMER_REQUEST_WEEK", row.get(i++));
                        map.put("STANDARD_REQUEST_WEEK", row.get(i++));
                        map.put("ORDER_INTAKE_WEEK", row.get(i++));
                        map.put("TTTT", row.get(i++));
                        map.put("UUUU", row.get(i));
                        dataList.add(map);
                    }

                    // 插入数据的时候要使用去空格之后的表头,否则会出现错行
                    if (dataList.size() >= 200) {
                        schedulerDao.mergeReport1Data(dataList);
                        dataList.clear();
                    }
                }
            }, new StylesTable());

            if (dataList.isEmpty() == false) {
                schedulerDao.mergeReport1Data(dataList);
            }

            if (tempFile.delete() == false) {
                System.err.println(tempFile.getAbsolutePath() + " delete failed");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return response;
    }

    @Override
    public void downloadReport1UploadTemplate(Map<String, Object> parameterMap, HttpServletResponse resp) {
        try {
            try (InputStream is = RMXSchedulerServiceImpl.class.getClassLoader().getResourceAsStream("files/rmx_scheduler_template.xlsx")) {
                if (is == null) {
                    return;
                }
                String fileName = "rmx_scheduler_template_" + Utils.randomStr(4) + ".xlsx";
                resp.resetBuffer();
                resp.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename*=UTF-8''" + fileName);
                resp.addHeader("filename", fileName);
                resp.addHeader("Access-Control-Expose-Headers", "filename");
                resp.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                resp.setHeader(HttpHeaders.CACHE_CONTROL, "no-cache, no-store, must-revalidate");
                resp.setHeader(HttpHeaders.PRAGMA, "no-cache");
                resp.setDateHeader(HttpHeaders.EXPIRES, 0);

                try (OutputStream os = resp.getOutputStream()) {
                    IOUtils.copy(is, os);
                    os.flush();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public Response queryReport1Result(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        int total = schedulerDao.queryReport1ResultCount(parameterMap);
        page.setTotal(total);
        if (total > 0) {
            page.setData(schedulerDao.queryReport1Result(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport1Result(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        List<Map<String, Object>> dataList = schedulerDao.queryReport1Result(parameterMap);

        // 初始化响应头
        String filename = "rmx_schedule_list_" + new SimpleDateFormat("yyyyMMdd").format(new Date()) + "_" + Utils.randomStr(4) + ".xlsx";
        ExcelTemplate.initExcelResponseHeader(response, filename);

        SXSSFWorkbook book = null;
        try {
            InputStream is = ExcelTemplate.class.getClassLoader().getResourceAsStream("files/rmx_scheduler_template.xlsx");
            assert is != null;
            book = new SXSSFWorkbook(new XSSFWorkbook(is), 128);
            SXSSFSheet sheet = book.getSheet("Sheet1");

            CellStyle dateStyle = book.createCellStyle();
            DataFormat format = book.createDataFormat();
            dateStyle.setDataFormat(format.getFormat("yyyy/m/d"));
            dateStyle.setBorderBottom(BorderStyle.DOTTED);
            dateStyle.setBorderLeft(BorderStyle.DOTTED);
            dateStyle.setBorderTop(BorderStyle.DOTTED);
            dateStyle.setBorderRight(BorderStyle.DOTTED);

            CellStyle borderStyle = book.createCellStyle();
            borderStyle.setBorderBottom(BorderStyle.DOTTED);
            borderStyle.setBorderLeft(BorderStyle.DOTTED);
            borderStyle.setBorderTop(BorderStyle.DOTTED);
            borderStyle.setBorderRight(BorderStyle.DOTTED);

            String[] headers = new String[]{"NO", "EXPECTED_DATE", "ACTUAL_DATE", "MO_NUMBER", "EXPECTED_FINISH_DATE", "CUSTOMER_NAME",
                    "SALES_ORDER_NUMBER", "SALES_ORDER_ITEM", "FAMILY_NAME", "MATERIAL", "MATERIAL_TYPE", "UNIT_FUNCTION_QTY", "QTY",
                    "OPEN_QTY", "TOTAL_OPEN_FUNCTION_QTY", "FINISH_DATE", "CUSTOMER_TYPE", "MO_CREATE_DATE", "CUSTOMER_DELIVERY_DATE",
                    "STANDARD_DELIVERY_DATE", "CUSTOMER_REQUEST_DATE", "STATION_DESC", "TYPE3", "LOW_VOLTAGE_ROOM", "REMARKS",
                    "EXPECTED_PRODUCTION_DATE", "SO_CREATE_DATE", "PACKAGE_INFO", "VOLTAGE_LEVEL", "TANK", "ANNOTATIONS", "FRONT_FRAME",
                    "BOTTOM_FRAME", "SO_ITEM", "MO_QTY", "DEMAND", "NEW_EXCEPTION", "VCB", "HHHH", "IIII", "JJJJ", "KKKK", "LLLL",
                    "MMMM", "NNNN", "OOOO", "PPPP", "CUSTOMER_REQUEST_WEEK", "STANDARD_REQUEST_WEEK", "ORDER_INTAKE_WEEK", "TTTT", "UUUU"};

            for (int i = 0; i < dataList.size(); i++) {
                // 从第二行开始写
                int index = i + 1;
                Row rowBody = sheet.createRow(index);

                for (int j = 0; j < headers.length; j++) {
                    String header = headers[j];
                    Cell cellBody = rowBody.createCell(j);
                    Object v = dataList.get(i).get(header);
                    ExcelTemplate.setCellValue(cellBody, v);
                    if (v instanceof Date) {
                        cellBody.setCellStyle(dateStyle);
                    } else {
                        cellBody.setCellStyle(borderStyle);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        ExcelTemplate.writeBookToResponse(book, response);
    }

    @Override
    public Response queryReport2(Map<String, Object> parameterMap) {
        String defaultCapacity = this.renderCapacity(DEFAULT_CAPACITY, DEFAULT_CAPACITY, DEFAULT_CAPACITY);
        List<List<List<CalendarBean>>> year = new ArrayList<>();
        List<List<CalendarBean>> month = new ArrayList<>();
        List<CalendarBean> week = new ArrayList<>();

        List<CalendarBean> list = schedulerDao.queryReport2(parameterMap);
        List<Map<String, String>> capacityData = schedulerDao.queryReport2Capacity(parameterMap);
        Map<String, String> capacityMap = new HashMap<>();

        for (Map<String, String> map : capacityData) {
            capacityMap.put(map.get("DAY"), this.renderCapacity(map.get("SHIFT1"), map.get("SHIFT2"), map.get("SHIFT3")));
        }

        year.add(month);
        month.add(week);

        for (int i = 0; i < list.size(); i++) {
            CalendarBean calendarBean = list.get(i);
            calendarBean.setCapacity(capacityMap.getOrDefault(calendarBean.getTEXT(), defaultCapacity));
            if (calendarBean.isFirstDayOfWeek() && i != 0) {
                week = new ArrayList<>();
                month.add(week);
            }

            if (calendarBean.isFirstDayOfMonth() && i != 0) {
                while (week.isEmpty() == false && week.size() < 7) {
                    week.add(new CalendarBean());
                }

                month = new ArrayList<>();
                week = new ArrayList<>();
                month.add(week);
                year.add(month);
            }

            week.add(calendarBean);
        }

        while (week.isEmpty() == false && week.size() < 7) {
            week.add(new CalendarBean());
        }
        return response.setBody(year);
    }

    @Override
    public Response queryReport2DayCapacity(Map<String, Object> parameterMap) {
        Map<String, String> data = schedulerDao.queryReport2CapacityByDay(parameterMap);
        if (data == null) {
            data = new HashMap<>();
            data.put("shift1", DEFAULT_CAPACITY);
            data.put("shift2", DEFAULT_CAPACITY);
            data.put("shift3", DEFAULT_CAPACITY);
        }
        return response.setBody(data);
    }

    @Override
    public Response updateReport2DayCapacity(Map<String, Object> parameterMap) {
        schedulerDao.updateReport2DayCapacity(parameterMap);
        return response;
    }

    private String renderCapacity(String... capacities) {
        List<String> result = new ArrayList<>();
        for (String capacity : capacities) {
            if ("-".equals(capacity) || DEFAULT_CAPACITY.compareTo(capacity) > 0) {
                result.add("<span class=\"not-full\">" + capacity + "</span>");
            } else if (DEFAULT_CAPACITY.compareTo(capacity) < 0) {
                result.add("<span class=\"overload\">" + capacity + "</span>");
            } else {
                result.add(capacity);
            }
        }
        return StringUtils.join(result, " ");
    }
}
