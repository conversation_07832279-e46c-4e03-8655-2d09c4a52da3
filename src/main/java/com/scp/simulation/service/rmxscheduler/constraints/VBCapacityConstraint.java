package com.scp.simulation.service.rmxscheduler.constraints;

import com.scp.simulation.bean.RmxConfig;
import com.scp.simulation.bean.RmxMoInfo;
import com.scp.simulation.service.rmxscheduler.IScheduleConstraint;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class VBCapacityConstraint extends IScheduleConstraint {

    RmxConfig rmxConfig;

    public VBCapacityConstraint(RmxConfig rmxConfig) {
        this.rmxConfig = rmxConfig;
    }

    @Override
    public boolean canPass(RmxMoInfo currentMO, List<RmxMoInfo> acceptedQueue) {
        if (rmxConfig.isVbMaxCapacityEnable()) {
            // 如果这个订单放入队列, V+B超出产能的rmxConfig.getVbCapacity(), 则不予排产
            if (this.isExceedDayCapacity(currentMO, acceptedQueue) == true) {
                return this.block(currentMO, "V+B超出产能的" + rmxConfig.getVbCapacity() + "%");
            }
        }

        return this.pass(currentMO);
    }

    // 如果将这个MO放入产线, 排产产能超过产线产能10%时, 不可排产
    private boolean isExceedDayCapacity(RmxMoInfo currentMO, List<RmxMoInfo> acceptedQueue) {
        if (acceptedQueue.isEmpty()) {
            return false;
        }
        if (super.isDayCapacityReached(acceptedQueue)) {
            return false;
        }
        if (this.countVB(currentMO) == 0) {
            return false;
        }

        // 如果产线产能没满, 那么新来的MO不会新开一天
        RmxMoInfo lastMoInfo = acceptedQueue.get(acceptedQueue.size() - 1);
        String day = lastMoInfo.getScheduleDay();

        int dayCapacity = lastMoInfo.getScheduleDayCapacity();
        int vbMaxCapacity = dayCapacity * rmxConfig.getVbCapacity() / 100;
        int acceptedVBCapacity = 0;

        List<RmxMoInfo> tempQueue = acceptedQueue.stream().filter(moInfo -> moInfo.getScheduleDay().equals(day)).toList();
        for (RmxMoInfo moInfo : tempQueue) {
            acceptedVBCapacity += this.countVB(moInfo) * moInfo.getOpenQty();
        }

        // 只要产线内的产能小于VB的最大产能, 就可以排产, 哪怕放入之后超了
        return acceptedVBCapacity >= vbMaxCapacity;
    }

    private int countVB(RmxMoInfo currentMO) {
        String materialType = currentMO.getMaterialType();
        int count = 0;
        if (materialType.contains("-")) {
            String type = materialType.split("-")[0];
            materialType = StringUtils.removeStart(materialType, type).toLowerCase();
            materialType = StringUtils.remove(materialType, "kv");

            for (int i = 0; i < materialType.length(); i++) {
                char currentChar = materialType.charAt(i);
                if (currentChar == 'v' || currentChar == 'b') {
                    count++;
                }
            }
        }
        return count;
    }
}
