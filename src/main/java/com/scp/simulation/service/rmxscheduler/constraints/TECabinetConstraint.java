package com.scp.simulation.service.rmxscheduler.constraints;

import com.scp.simulation.bean.RmxConfig;
import com.scp.simulation.bean.RmxMoInfo;
import com.scp.simulation.service.rmxscheduler.IScheduleConstraint;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class TECabinetConstraint extends IScheduleConstraint {

    RmxConfig rmxConfig;

    public TECabinetConstraint(RmxConfig rmxConfig) {
        this.rmxConfig = rmxConfig;
    }

    @Override
    public boolean canPass(RmxMoInfo currentMO, List<RmxMoInfo> acceptedQueue) {
        // 空队列可以放入任何订单
        if (acceptedQueue.isEmpty()) {
            return this.pass(currentMO);
        }

        // 如果不是TE的订单, 不适用当前的约束
        if (this.isTECabinet(currentMO) == false) {
            return this.pass(currentMO);
        }

        // 两个TE订单不能联排
        if (rmxConfig.isTeIncompatibilityEnable()) {
            RmxMoInfo lastMo = acceptedQueue.get(acceptedQueue.size() - 1);
            if (this.isTECabinet(currentMO) && this.isTECabinet(lastMo)) {
                return this.block(currentMO, "两个TE订单不能联排");
            }
        }

        // 如果已排产订单已经满足产能, 那么新加入的这个订单势必是在新的一个班次, 所以不用担心产能问题, 直接排产
        if (this.isShiftCapacityReached(acceptedQueue) == true) {
            return this.pass(currentMO);
        }

        // 班次产能不能超过 maxCapacityPerShift
        if (rmxConfig.isTeMaxCapacityEnable()) {
            int maxCapacityPerShift = rmxConfig.getTeMaxCapacity();
            List<RmxMoInfo> shiftQueue = acceptedQueue.stream().filter(e -> this.isMoInSameShift(e, currentMO) && this.isTECabinet(e)).toList();

            int acceptedCapacity = this.sumCapacityByFunc(shiftQueue);// 按功能数计算产能
            if (acceptedCapacity >= maxCapacityPerShift) { // 如果已经排下去的订单, 大于等于最大产能, 则不再排产, 否则哪怕只有1个产能, 也可以排进去, 可以超出
                return this.block(currentMO, maxCapacityPerShift, acceptedCapacity);
            }
        }
        return this.pass(currentMO);
    }

    private boolean isTECabinet(RmxMoInfo mo) {
        return StringUtils.containsIgnoreCase(mo.getMaterialType(), "TE");
    }
}
