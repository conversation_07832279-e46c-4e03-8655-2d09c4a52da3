package com.scp.simulation.service.rmxscheduler.constraints;

import com.scp.simulation.bean.RmxConfig;
import com.scp.simulation.bean.RmxMoInfo;
import com.scp.simulation.service.rmxscheduler.IScheduleConstraint;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

public class V24KCabinetConstraint extends IScheduleConstraint {

    RmxConfig rmxConfig;

    public V24KCabinetConstraint(RmxConfig rmxConfig) {
        this.rmxConfig = rmxConfig;
    }

    @Override
    public boolean canPass(RmxMoInfo currentMO, List<RmxMoInfo> acceptedQueue) {
        // 空队列可以放入任何订单
        if (acceptedQueue.isEmpty()) {
            return this.pass(currentMO);
        }

        // 如果不是24KV的订单, 不适用当前的约束
        if (this.isV24KCabinet(currentMO) == false) {
            return this.pass(currentMO);
        }

        // 如果已排产订单已经满足产能, 那么新加入的这个订单势必是在新的一天, 所以不用担心产能问题, 直接排产
        if (this.isDayCapacityReached(acceptedQueue) == true) {
            return this.pass(currentMO);
        }

        // 日产能不能超过 maxCapacityPerShift
        // lastMo和currentMo一定在同一天, 因为不在同一天的情况, 早在上一个条件判断过了
        if (rmxConfig.isV24kMaxCapacityEnable()) {
            RmxMoInfo lastMo = acceptedQueue.get(acceptedQueue.size() - 1);
            int maxCapacityPerShift = (int) (lastMo.getScheduleDayCapacity() * rmxConfig.getV24kMaxCapacity() / 100.0); // 不超过当天的25%
            List<RmxMoInfo> dayQueue = acceptedQueue.stream().filter(
                    e -> this.isMoInSameDay(e, currentMO) && this.isV24KCabinet(e)
            ).toList();

            int acceptedCapacity = this.sumCapacityByFunc(dayQueue); // 按功能数计算产能
            if (acceptedCapacity >= maxCapacityPerShift) { // 如果已经排下去的订单, 大于等于最大产能, 则不再排产, 否则哪怕只有1个产能, 也可以排进去, 可以超出
                return this.block(currentMO, maxCapacityPerShift, acceptedCapacity);
            }
        }
        return this.pass(currentMO);
    }

    private boolean isV24KCabinet(RmxMoInfo mo) {
        return StringUtils.contains(mo.getMaterialType(), "24KV")
                && (StringUtils.contains(mo.getMaterial(), "RM6") || StringUtils.contains(mo.getMaterial(), "FBX"));
    }
}
