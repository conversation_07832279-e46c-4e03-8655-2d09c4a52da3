package com.scp.simulation.service.rmxscheduler.constraints;

import com.scp.simulation.bean.RmxMoInfo;
import com.scp.simulation.service.rmxscheduler.IScheduleConstraint;

import java.util.List;

public class CapacityConstraint extends IScheduleConstraint {

    @Override
    public boolean canPass(RmxMoInfo currentMO, List<RmxMoInfo> acceptedQueue) {
        // 如果这个订单放入队列, 会导致当前班次的排产产能超出规定产能的10%, 则不予排产
        if (this.isExceedShiftCapacity(currentMO, acceptedQueue) == true) {
            return this.block(currentMO, "超出产线额定产能10%");
        }
        return this.pass(currentMO);
    }

    // 如果将这个MO放入产线, 排产产能超过产线产能10%时, 不可排产
    private boolean isExceedShiftCapacity(RmxMoInfo currentMO, List<RmxMoInfo> acceptedQueue) {
        if (acceptedQueue.isEmpty()) {
            return false;
        }
        RmxMoInfo lastMoInfo = acceptedQueue.get(acceptedQueue.size() - 1);
        int shiftCapacity = lastMoInfo.getScheduleShiftCapacity();
        String shift = lastMoInfo.getScheduleShift();
        String day = lastMoInfo.getScheduleDay();
        int acceptedCapacity = 0;

        List<RmxMoInfo> tempQueue = acceptedQueue.stream().filter(moInfo -> moInfo.getScheduleShift().equals(shift) && moInfo.getScheduleDay().equals(day)).toList();
        for (RmxMoInfo moInfo : tempQueue) {
            acceptedCapacity += moInfo.getTotalOpenFunctionQty();
        }

        // 如果放入currentMO之前, 产能有空余, 放入currentMO后, 产能超出1.1倍, 那么这个MO不应该被放入, 否则均可放入
        return acceptedCapacity < shiftCapacity && (acceptedCapacity + currentMO.getTotalOpenFunctionQty()) >= shiftCapacity * 1.1;
    }
}
