package com.scp.simulation.service.rmxscheduler;

import com.scp.simulation.bean.RmxMoInfo;

import java.util.List;

public abstract class IScheduleConstraint {

    public boolean canPass(RmxMoInfo currentMO, List<RmxMoInfo> acceptedQueue) {
        return true;
    }

    public boolean isShiftCapacityReached(List<RmxMoInfo> acceptedQueue) {
        RmxMoInfo lastMoInfo = acceptedQueue.get(acceptedQueue.size() - 1);
        String shift = lastMoInfo.getScheduleShift();
        String day = lastMoInfo.getScheduleDay();
        int acceptedCapacity = 0;

        List<RmxMoInfo> tempQueue = acceptedQueue.stream().filter(moInfo -> moInfo.getScheduleShift().equals(shift) && moInfo.getScheduleDay().equals(day)).toList();
        for (RmxMoInfo moInfo : tempQueue) {
            acceptedCapacity += moInfo.getTotalOpenFunctionQty();
        }

        // 如果队列的产能已经超过产线允许产能, 返回true
        return acceptedCapacity >= lastMoInfo.getScheduleShiftCapacity();
    }

    public boolean isDayCapacityReached(List<RmxMoInfo> acceptedQueue) {
        RmxMoInfo lastMoInfo = acceptedQueue.get(acceptedQueue.size() - 1);
        String day = lastMoInfo.getScheduleDay();
        int acceptedCapacity = 0;

        List<RmxMoInfo> tempQueue = acceptedQueue.stream().filter(moInfo -> moInfo.getScheduleDay().equals(day)).toList();
        for (RmxMoInfo moInfo : tempQueue) {
            acceptedCapacity += moInfo.getTotalOpenFunctionQty();
        }

        // 如果队列的产能已经超过产线允许产能, 返回true
        return acceptedCapacity >= lastMoInfo.getScheduleDayCapacity();
    }

    public boolean pass(RmxMoInfo mo) {
        mo.setTips(null);
        return true;
    }

    public boolean block(RmxMoInfo mo, String tips) {
        mo.setTips(tips);
        return false;
    }

    public boolean block(RmxMoInfo mo, int maxCapacity, int queueCapacity) {
        mo.setTips("产能不能超" + maxCapacity + ", 已在队列产能" + queueCapacity + ", 当前MO产能" + mo.getOpenQty());
        return false;
    }

    public boolean isMoInSameShift(RmxMoInfo mo1, RmxMoInfo mo2) {
        return mo1.getScheduleShift().equals(mo2.getScheduleShift()) && mo1.getScheduleDay().equals(mo2.getScheduleDay());
    }

    public boolean isMoInSameDay(RmxMoInfo mo1, RmxMoInfo mo2) {
        return mo1.getScheduleDay().equals(mo2.getScheduleDay());
    }

    //按功能数计算总产能
    public int sumCapacityByFunc(List<RmxMoInfo> queue) {
        int acceptedCapacity = 0;
        for (RmxMoInfo moInfo : queue) {
            acceptedCapacity += moInfo.getTotalOpenFunctionQty();
        }
        return acceptedCapacity;
    }

    public int sumCapacityByQty(List<RmxMoInfo> queue) {
        int acceptedCapacity = 0;
        for (RmxMoInfo moInfo : queue) {
            acceptedCapacity += moInfo.getOpenQty();
        }
        return acceptedCapacity;
    }
}
