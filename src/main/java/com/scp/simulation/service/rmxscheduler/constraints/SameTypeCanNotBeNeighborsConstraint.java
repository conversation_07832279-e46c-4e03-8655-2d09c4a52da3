package com.scp.simulation.service.rmxscheduler.constraints;

import com.scp.simulation.bean.RmxMoInfo;
import com.scp.simulation.service.rmxscheduler.IScheduleConstraint;

import java.util.List;

public class SameTypeCanNotBeNeighborsConstraint extends IScheduleConstraint {

    @Override
    public boolean canPass(RmxMoInfo currentMO, List<RmxMoInfo> acceptedQueue) {
        if (acceptedQueue.size() >= 2) {
            String material = currentMO.getMaterialType();
            RmxMoInfo lastMoInfo = acceptedQueue.get(acceptedQueue.size() - 1);
            RmxMoInfo lastMoInfo2 = acceptedQueue.get(acceptedQueue.size() - 2);

            if (material.equals(lastMoInfo.getMaterialType()) && material.equals(lastMoInfo2.getMaterialType())) {
                return this.block(currentMO, "相同型号不可连续三个订单排产");
            }
        }

        return this.pass(currentMO);
    }
}
