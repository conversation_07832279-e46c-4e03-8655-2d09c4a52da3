package com.scp.simulation.service.rmxscheduler;

import com.scp.simulation.bean.RmxCapacity;
import com.scp.simulation.bean.RmxConfig;
import com.scp.simulation.bean.RmxMoInfo;
import com.scp.simulation.service.rmxscheduler.constraints.*;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

public class RmxScheduler {

    RmxConfig rmxConfig;

    public RmxScheduler(RmxConfig rmxConfig) {
        this.rmxConfig = rmxConfig;
    }

    public List<RmxMoInfo> start(List<RmxMoInfo> sourceQueue, List<RmxCapacity> capacity) {
        List<RmxMoInfo> acceptedQueue = new ArrayList<>(); // 已确定的排产信息
        List<RmxMoInfo> pendingQueue = new ArrayList<>(); // 等待队列
        Map<String, List<RmxMoInfo>> fixedMap = new HashMap<>();
        Iterator<RmxMoInfo> it = sourceQueue.iterator();

        while (it.hasNext()) {
            RmxMoInfo moInfo = it.next();
            if (StringUtils.containsIgnoreCase(moInfo.getNo(), "FIX")) {
                fixedMap.computeIfAbsent(moInfo.getExpectedDate(), k -> new ArrayList<>()).add(moInfo);
                it.remove();
            }
        }

        Map<String, Integer> dayCapacityMap = new HashMap<>();
        for (RmxCapacity c : capacity) {
            int dayCapacity = dayCapacityMap.getOrDefault(c.getDay(), 0);
            dayCapacity += c.getCapacity();
            dayCapacityMap.put(c.getDay(), dayCapacity);
        }

        // 加载插件
        List<IScheduleConstraint> plugins = new ArrayList<>();
        plugins.add(new CapacityConstraint());

        plugins.add(new VBCapacityConstraint(rmxConfig));
        plugins.add(new MultiFuncCanNotBeNeighborsConstraint(rmxConfig));
        plugins.add(new IECCabinetConstraint(rmxConfig));
        plugins.add(new PTCRCabinetConstraint(rmxConfig));
        plugins.add(new TECabinetConstraint(rmxConfig));
        plugins.add(new QCabinetConstraint(rmxConfig));
        plugins.add(new V24KCabinetConstraint(rmxConfig));

        // 我们有三个队列, 分别是tempQueue, sourceQueue, pendingQueue
        // tempQueue 每当有新的元素放入到结果队列, 那么就会重新生成一个tempQueue
        // sourceQueue 原始队列
        // pendingQueue 等待队列

        // 每次根据 sourceQueue 和 pendingQueue生成一个tempQueue
        // 从tempQueue中取出一个元素, 来决定这个元素究竟会放入 pendingQueue 还是 acceptedQueue
        // 不管这个元素最终是放入哪个list, 都要从三个队列中移除, 否则就会出现重复插入的情况

        // 每次放入acceptedQueue之后, 排产逻辑会重置, 所以需要重新生成排产序列, 即重新生成tempQueue
        // 如果遍历整个tempQueue, 都没有一个订单可以放入acceptedQueue, 那么就说明, 按照现有的逻辑, 实在是排不下去了
        boolean interrupted;
        String currentDay = "9999/99/99"; // 目前已经排产到哪一天了
        do {
            interrupted = true;

            List<RmxMoInfo> tempQueue = new ArrayList<>(fixedMap.getOrDefault(currentDay, new ArrayList<>()));
            tempQueue.addAll(pendingQueue);
            tempQueue.addAll(sourceQueue);

            while (tempQueue.isEmpty() == false) {
                RmxMoInfo moInfo = tempQueue.remove(0);
                sourceQueue.remove(moInfo);
                pendingQueue.remove(moInfo);

                boolean canPass = true;
                // 如果通过了所有过滤器, 那么可以添加到排产队列
                for (IScheduleConstraint plugin : plugins) {
                    canPass = canPass && plugin.canPass(moInfo, acceptedQueue);
                }

                if (canPass) {
                    acceptedQueue.add(moInfo);
                    // 更新排产日期
                    allocationCapacity(acceptedQueue, capacity, dayCapacityMap);
                    // 放入acceptedQueue之后, 不管怎么样都尝试从fixedMap中删除这个订单
                    currentDay = moInfo.getScheduleDay();
                    fixedMap.getOrDefault(currentDay, new ArrayList<>()).remove(moInfo);
                    interrupted = false;
                    break;
                } else {
                    // FIX的订单不放入pending, 因为FIX的优先级比pending更高
                    if (StringUtils.containsIgnoreCase(moInfo.getNo(), "FIX") == false) {
                        pendingQueue.add(moInfo);
                    }
                }
            }

        } while (interrupted == false);

        for (String key : fixedMap.keySet()) {
            acceptedQueue.addAll(fixedMap.get(key));
        }

        acceptedQueue.addAll(pendingQueue);
        return acceptedQueue;
    }

    private void allocationCapacity(List<RmxMoInfo> rawList, List<RmxCapacity> capacity, Map<String, Integer> dayCapacityMap) {
        int index = 0;
        int shiftCapacity = capacity.get(index).getCapacity();

        String day = capacity.get(index).getDay();
        String shift = capacity.get(index).getShift();

        for (RmxMoInfo moInfo : rawList) {
            // 如果shift越界, 则index向后移动一位, 因为产能足够, 所以不会出现index越界的情况
            if (shiftCapacity <= 0) {
                index++;
                shiftCapacity = capacity.get(index).getCapacity();

                day = capacity.get(index).getDay();
                shift = capacity.get(index).getShift();
            }
            shiftCapacity -= moInfo.getTotalOpenFunctionQty();

            moInfo.setScheduleDay(day);
            moInfo.setScheduleShift(shift);

            moInfo.setScheduleShiftCapacity(capacity.get(index).getCapacity());
            moInfo.setScheduleDayCapacity(dayCapacityMap.getOrDefault(day, -1));
        }
    }
}
