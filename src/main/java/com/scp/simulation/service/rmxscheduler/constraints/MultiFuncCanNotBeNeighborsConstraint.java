package com.scp.simulation.service.rmxscheduler.constraints;


import com.scp.simulation.bean.RmxConfig;
import com.scp.simulation.bean.RmxMoInfo;
import com.scp.simulation.service.rmxscheduler.IScheduleConstraint;

import java.util.List;

public class MultiFuncCanNotBeNeighborsConstraint extends IScheduleConstraint {

    RmxConfig rmxConfig;

    public MultiFuncCanNotBeNeighborsConstraint(RmxConfig rmxConfig) {
        this.rmxConfig = rmxConfig;
    }

    @Override
    public boolean canPass(RmxMoInfo currentMO, List<RmxMoInfo> acceptedQueue) {
        // 如果不启用当前约束, 直接返回true
        if (rmxConfig.isMultThresholdEnable() == false) {
            return this.pass(currentMO);
        }
        if (!acceptedQueue.isEmpty()) {
            RmxMoInfo lastMoInfo = acceptedQueue.get(acceptedQueue.size() - 1);
            if (this.isMultiFunc(currentMO) && this.isMultiFunc(lastMoInfo)) {
                return this.block(currentMO, "多功能柜不可连续排产");
            }
        }
        return this.pass(currentMO);
    }

    // 判断是否是多功能柜型
    private boolean isMultiFunc(RmxMoInfo mo) {
        return mo.getUnitFunctionQty() > rmxConfig.getMultThreshold();
    }
}
