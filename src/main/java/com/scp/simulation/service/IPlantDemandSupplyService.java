package com.scp.simulation.service;

import com.starter.context.bean.Response;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;

public interface IPlantDemandSupplyService {

    Response initPage();

    Response queryReport1(Map<String, Object> parameterMap, String userid);

    Response queryReport1Details(Map<String, Object> parameterMap);

    void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response saveReport1Comments(Map<String, Object> parameterMap, String userid);

    Response queryReport1HistoryComments(Map<String, Object> parameterMap);

    void downloadReport1HistoryComments(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport3(Map<String, Object> parameterMap);

    void downloadReport3(Map<String, Object> parameterMap, HttpServletResponse response);

    Response saveReport3(String userid, Map<String, Object> parameterMap);

    Response uploadReport3(String userid, MultipartFile file) throws Exception;

    void downloadReport3Template(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport4(Map<String, Object> parameterMap);

    void downloadReport4(Map<String, Object> parameterMap, HttpServletResponse response);

    Response saveReport4(String userid, Map<String, Object> parameterMap);

    Response uploadReport4(String userid, MultipartFile file) throws Exception;

    void downloadReport4Template(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport1CriticalLevel(Map<String, Object> parameterMap);
}
