package com.scp.simulation.message;

import com.alibaba.fastjson.JSONObject;
import com.starter.context.configuration.IMqttMessageHandler;
import com.scp.simulation.bean.BestCanDoLog;
import com.scp.simulation.dao.IBestCanDoDao;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

@Component
public class BestCanDoMQTTHandler implements IMqttMessageHandler {
    @Resource
    private IBestCanDoDao bestCanDoDao;

    @Override
    public void mqttMessageHandler(String topic, Object payload) {
        String message = String.valueOf(payload);
        if (StringUtils.startsWith(topic, "scp/dss/ui/bcd/")) {
            try {
                BestCanDoLog log = JSONObject.parseObject(message).toJavaObject(BestCanDoLog.class);
                bestCanDoDao.saveBestCanDoLog(log);
            } catch (Exception ignore) {
                System.err.println("received invalid message: " + message);
            }
        }
    }
}
