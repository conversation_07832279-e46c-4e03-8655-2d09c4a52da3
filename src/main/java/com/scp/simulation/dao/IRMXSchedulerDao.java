package com.scp.simulation.dao;

import com.scp.simulation.bean.CalendarBean;
import com.scp.simulation.bean.RmxMoInfo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface IRMXSchedulerDao {

    String querySchedulerStartDate();

    List<Map<String, String>> querySchedulerCapacity(Map<String, Object> parameterMap);

    int queryReport1Count(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport1(Map<String, Object> parameterMap);

    List<RmxMoInfo> queryReport1ScheduleRawData();

    void truncateReport1Result();

    void saveReport1Result(List<RmxMoInfo> resultList);

    int queryReport1ResultCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport1Result(Map<String, Object> parameterMap);

    List<CalendarBean> queryReport2(Map<String, Object> parameterMap);

    List<Map<String, String>> queryReport2Capacity(Map<String, Object> parameterMap);

    Map<String, String> queryReport2CapacityByDay(Map<String, Object> parameterMap);

    void updateReport2DayCapacity(Map<String, Object> parameterMap);

    void truncateReport1Data();

    void mergeReport1Data(List<Map<String, Object>> dataList);
}
