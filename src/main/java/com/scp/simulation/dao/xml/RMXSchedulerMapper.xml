<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.simulation.dao.IRMXSchedulerDao">
	<select id="querySchedulerStartDate" resultType="java.lang.String">
		WITH TEMP AS (SELECT REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(T.MO_CREATE_DATE, ' ', ''), '早', ''), '中', ''), '夜', ''), '大', ''), '-', '/') AS DATE_STR
					  FROM RMX_SCHED_MO_SOURCE_DATA T
					  WHERE UPPER(T.NO) = 'FIRM'),
			 TEMP2 AS (SELECT MAX(TO_DATE(CASE
											  WHEN LENGTH(TEMP.DATE_STR) &lt;= 5 THEN TO_CHAR(SYSDATE, 'YYYY') || '/' || TEMP.DATE_STR
											  ELSE TEMP.DATE_STR END, 'YYYY/MM/DD')) AS MO_CREATE_DATE
					   FROM TEMP)
		SELECT T.TEXT
		FROM SCPA.SY_CALENDAR T
		WHERE T.NAME = 'National Holidays'
		  AND DATE$ > (SELECT T2.MO_CREATE_DATE FROM TEMP2 T2)
		  AND WORKING_DAY = 1
		ORDER BY DATE$
			FETCH NEXT 1 ROW ONLY
	</select>

	<select id="querySchedulerCapacity" parameterType="java.util.Map" resultType="java.util.HashMap">
		WITH TEMP AS (SELECT T.TEXT,
		                     NVL(T2.SHIFT1, '${defaultCapacity}') SHIFT1,
		                     NVL(T2.SHIFT2, '${defaultCapacity}') SHIFT2,
		                     NVL(T2.SHIFT3, '${defaultCapacity}') SHIFT3
					  FROM SY_CALENDAR T
							   LEFT JOIN RMX_SCHED_CAPACITY T2 ON T.TEXT = T2.DAY
					  WHERE T.NAME = 'National Holidays'
						AND T.TEXT >= #{startDate,jdbcType=VARCHAR}
					  ORDER BY DATE$
						  FETCH NEXT 100 ROWS ONLY)
		SELECT T.TEXT, SHIFT1, SHIFT2, SHIFT3
		FROM TEMP T
		WHERE (T.SHIFT1 != '-'
			OR T.SHIFT2 != '-'
			OR T.SHIFT3 != '-')
	</select>

	<sql id="queryReport1Sql">
        SELECT * FROM RMX_SCHED_MO_SOURCE_DATA ORDER BY SEQ_NO
    </sql>

    <select id="queryReport1Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1Sql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1Sql"/>
        <include refid="global.select_footer"/>
    </select>

	<select id="queryReport1ScheduleRawData" resultType="com.scp.simulation.bean.RmxMoInfo">
		SELECT UPPER(NO) "no",
			   T.MO_NUMBER,
			   T.EXPECTED_DATE,
			   T.MATERIAL_TYPE,
			   T.UNIT_FUNCTION_QTY,
			   T.OPEN_QTY,
			   T.TOTAL_OPEN_FUNCTION_QTY,
			   T.MATERIAL
		  FROM SCPA.RMX_SCHED_MO_SOURCE_DATA T
               LEFT JOIN RMX_SCHED_VIP_PRIORIRY T2 ON T.CUSTOMER_NAME = T2.CUSTOMER_NAME
         WHERE UPPER(NO) != 'FIRM'
         ORDER BY STANDARD_DELIVERY_DATE, NVL(T2.PRIORITY, 0) DESC, MO_NUMBER
	</select>

	<update id="truncateReport1Result">
		TRUNCATE TABLE RMX_SCHED_RESULT
	</update>

	<insert id="saveReport1Result">
		INSERT INTO RMX_SCHED_RESULT(SEQ_NO, MO_NUMBER, SCHEDULE_DAY, SCHEDULE_SHIFT)
		<foreach collection="resultList" item="item" separator=" UNION ALL ">
            SELECT #{item.seqNo, jdbcType=VARCHAR}, #{item.moNumber, jdbcType=VARCHAR}, #{item.scheduleDay, jdbcType=VARCHAR}, #{item.scheduleShift, jdbcType=VARCHAR} FROM DUAL
        </foreach>
	</insert>

	<sql id="queryReport1ResultSql">
		SELECT T.SEQ_NO,
			   T.NO,
			   T.EXPECTED_DATE,
			   T.ACTUAL_DATE,
			   T.MO_NUMBER,
			   T.EXPECTED_FINISH_DATE,
			   T.CUSTOMER_NAME,
			   T.SALES_ORDER_NUMBER,
			   T.SALES_ORDER_ITEM,
			   T.FAMILY_NAME,
			   T.MATERIAL,
			   T.MATERIAL_TYPE,
			   T.UNIT_FUNCTION_QTY,
			   T.QTY,
			   T.OPEN_QTY,
			   T.TOTAL_OPEN_FUNCTION_QTY,
			   T.FINISH_DATE,
			   T.CUSTOMER_TYPE,
			   CASE WHEN T2.SCHEDULE_DAY IS NULL THEN T.MO_CREATE_DATE ELSE T2.SCHEDULE_DAY || T2.SCHEDULE_SHIFT END MO_CREATE_DATE,
			   T.CUSTOMER_DELIVERY_DATE,
			   T.STANDARD_DELIVERY_DATE,
			   T.CUSTOMER_REQUEST_DATE,
			   T.STATION_DESC,
			   T.TYPE3,
			   T.LOW_VOLTAGE_ROOM,
			   T.REMARKS,
			   T.EXPECTED_PRODUCTION_DATE,
			   T.SO_CREATE_DATE,
			   T.PACKAGE_INFO,
			   T.VOLTAGE_LEVEL,
			   T.TANK,
			   T.ANNOTATIONS,
			   T.FRONT_FRAME,
			   T.BOTTOM_FRAME,
			   T.SO_ITEM,
			   T.MO_QTY,
			   T.DEMAND,
			   T.NEW_EXCEPTION,
			   T.VCB,
			   T.HHHH,
			   T.IIII,
			   T.JJJJ,
			   T.KKKK,
			   T.LLLL,
			   T.MMMM,
			   T.NNNN,
			   T.OOOO,
			   T.PPPP,
			   T.CUSTOMER_REQUEST_WEEK,
			   T.STANDARD_REQUEST_WEEK,
			   T.ORDER_INTAKE_WEEK,
			   T.TTTT,
			   T.UUUU
		FROM SCPA.RMX_SCHED_MO_SOURCE_DATA T
				 LEFT JOIN RMX_SCHED_RESULT T2 ON T.MO_NUMBER = T2.MO_NUMBER
		ORDER BY CASE WHEN UPPER(T.NO) = 'FIRM' THEN T.SEQ_NO ELSE 999999 END,
				 T2.SEQ_NO
	</sql>

	<select id="queryReport1ResultCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1ResultSql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1Result" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1ResultSql"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport2" parameterType="java.util.Map" resultType="com.scp.simulation.bean.CalendarBean">
		select to_char(extract(month from mm.day)) M,
			   to_char(extract(day from mm.day)) D,
			   to_char(INITCAP(substr(to_char(mm.day, 'day', 'NLS_DATE_LANGUAGE=AMERICAN'), 0, 3))) as W,
			   to_char(mm.working_day) WD,
			   to_char((trunc(mm.DATE$,'dd') - to_date('19700101', 'yyyymmdd')) * 86400 - 28800) start_time,
			   mm.week_no WK,
		       text
		from (select to_date(text, 'yyyy/mm/dd') day, working_day, DATE$, text, week_no from sy_calendar
		where NAME = 'National Holidays'
		and text like #{year,jdbcType=VARCHAR} || '%') mm
		order by mm.day
	</select>

	<select id="queryReport2Capacity" parameterType="java.util.Map">
		SELECT DAY, SHIFT1, SHIFT2, SHIFT3
		  FROM SCPA.RMX_SCHED_CAPACITY T WHERE T.DAY LIKE #{year,jdbcType=VARCHAR} || '%'
	</select>

	<select id="queryReport2CapacityByDay" parameterType="java.util.Map">
		SELECT SHIFT1 AS "shift1", SHIFT2 AS "shift2", SHIFT3 AS "shift3"
		  FROM SCPA.RMX_SCHED_CAPACITY T
		 WHERE T.DAY = #{day, jdbcType=VARCHAR}
	</select>

	<update id="updateReport2DayCapacity">
		MERGE INTO RMX_SCHED_CAPACITY T
		USING (SELECT #{day, jdbcType=VARCHAR} DAY, #{shift1, jdbcType=VARCHAR} SHIFT1, #{shift2, jdbcType=VARCHAR} SHIFT2, #{shift3, jdbcType=VARCHAR} SHIFT3 FROM DUAL) S
		ON (T.DAY = S.DAY)
		WHEN MATCHED THEN
			UPDATE SET T.SHIFT1 = S.SHIFT1, T.SHIFT2 = S.SHIFT2, T.SHIFT3 = S.SHIFT3, T.UPDATE_BY$ = #{session.userid, jdbcType=VARCHAR}, T.UPDATE_DATE$ = SYSDATE
		WHEN NOT MATCHED THEN
			INSERT (DAY, SHIFT1, SHIFT2, SHIFT3, T.CREATE_BY$, T.CREATE_DATE$)
			VALUES (S.DAY, S.SHIFT1, S.SHIFT2, S.SHIFT3, #{session.userid, jdbcType=VARCHAR}, SYSDATE)
	</update>

	<insert id="truncateReport1Data">
		TRUNCATE TABLE RMX_SCHED_MO_SOURCE_DATA
	</insert>

	<update id="mergeReport1Data">
		MERGE INTO RMX_SCHED_MO_SOURCE_DATA T
		USING (
			<foreach collection="dataList" item="item" separator="UNION ALL">
			SELECT
				#{item.SEQ_NO, jdbcType=NUMERIC} SEQ_NO,
				#{item.NO, jdbcType=VARCHAR} NO,
				#{item.EXPECTED_DATE, jdbcType=VARCHAR} EXPECTED_DATE,
				#{item.ACTUAL_DATE, jdbcType=VARCHAR} ACTUAL_DATE,
				#{item.MO_NUMBER, jdbcType=VARCHAR} MO_NUMBER,
				#{item.EXPECTED_FINISH_DATE, jdbcType=VARCHAR} EXPECTED_FINISH_DATE,
				#{item.CUSTOMER_NAME, jdbcType=VARCHAR} CUSTOMER_NAME,
				#{item.SALES_ORDER_NUMBER, jdbcType=VARCHAR} SALES_ORDER_NUMBER,
				#{item.SALES_ORDER_ITEM, jdbcType=VARCHAR} SALES_ORDER_ITEM,
				#{item.FAMILY_NAME, jdbcType=VARCHAR} FAMILY_NAME,
				#{item.MATERIAL, jdbcType=VARCHAR} MATERIAL,
				#{item.MATERIAL_TYPE, jdbcType=VARCHAR} MATERIAL_TYPE,
				#{item.UNIT_FUNCTION_QTY, jdbcType=NUMERIC} UNIT_FUNCTION_QTY,
				#{item.QTY, jdbcType=NUMERIC} QTY,
				#{item.OPEN_QTY, jdbcType=NUMERIC} OPEN_QTY,
				#{item.TOTAL_OPEN_FUNCTION_QTY, jdbcType=NUMERIC} TOTAL_OPEN_FUNCTION_QTY,
				#{item.FINISH_DATE, jdbcType=VARCHAR} FINISH_DATE,
				#{item.CUSTOMER_TYPE, jdbcType=VARCHAR} CUSTOMER_TYPE,
				#{item.MO_CREATE_DATE, jdbcType=VARCHAR} MO_CREATE_DATE,
				#{item.CUSTOMER_DELIVERY_DATE, jdbcType=VARCHAR} CUSTOMER_DELIVERY_DATE,
				#{item.STANDARD_DELIVERY_DATE, jdbcType=VARCHAR} STANDARD_DELIVERY_DATE,
				#{item.CUSTOMER_REQUEST_DATE, jdbcType=VARCHAR} CUSTOMER_REQUEST_DATE,
				#{item.STATION_DESC, jdbcType=VARCHAR} STATION_DESC,
				#{item.TYPE3, jdbcType=VARCHAR} TYPE3,
				#{item.LOW_VOLTAGE_ROOM, jdbcType=VARCHAR} LOW_VOLTAGE_ROOM,
				#{item.REMARKS, jdbcType=VARCHAR} REMARKS,
				#{item.EXPECTED_PRODUCTION_DATE, jdbcType=VARCHAR} EXPECTED_PRODUCTION_DATE,
				#{item.SO_CREATE_DATE, jdbcType=VARCHAR} SO_CREATE_DATE,
				#{item.PACKAGE_INFO, jdbcType=VARCHAR} PACKAGE_INFO,
				#{item.VOLTAGE_LEVEL, jdbcType=VARCHAR} VOLTAGE_LEVEL,
				#{item.TANK, jdbcType=VARCHAR} TANK,
				#{item.ANNOTATIONS, jdbcType=VARCHAR} ANNOTATIONS,
				#{item.FRONT_FRAME, jdbcType=VARCHAR} FRONT_FRAME,
				#{item.BOTTOM_FRAME, jdbcType=VARCHAR} BOTTOM_FRAME,
				#{item.SO_ITEM, jdbcType=VARCHAR} SO_ITEM,
				#{item.MO_QTY, jdbcType=VARCHAR} MO_QTY,
				#{item.DEMAND, jdbcType=VARCHAR} DEMAND,
				#{item.NEW_EXCEPTION, jdbcType=VARCHAR} NEW_EXCEPTION,
				#{item.VCB, jdbcType=VARCHAR} VCB,
				#{item.HHHH, jdbcType=VARCHAR} HHHH,
				#{item.IIII, jdbcType=VARCHAR} IIII,
				#{item.JJJJ, jdbcType=VARCHAR} JJJJ,
				#{item.KKKK, jdbcType=VARCHAR} KKKK,
				#{item.LLLL, jdbcType=VARCHAR} LLLL,
				#{item.MMMM, jdbcType=VARCHAR} MMMM,
				#{item.NNNN, jdbcType=VARCHAR} NNNN,
				#{item.OOOO, jdbcType=VARCHAR} OOOO,
				#{item.PPPP, jdbcType=VARCHAR} PPPP,
				#{item.CUSTOMER_REQUEST_WEEK, jdbcType=VARCHAR} CUSTOMER_REQUEST_WEEK,
				#{item.STANDARD_REQUEST_WEEK, jdbcType=VARCHAR} STANDARD_REQUEST_WEEK,
				#{item.ORDER_INTAKE_WEEK, jdbcType=VARCHAR} ORDER_INTAKE_WEEK,
				#{item.TTTT, jdbcType=VARCHAR} TTTT,
				#{item.UUUU, jdbcType=VARCHAR} UUUU FROM DUAL
			</foreach>
		) S
		ON (T.MO_NUMBER = S.MO_NUMBER)
		WHEN MATCHED THEN
			UPDATE SET
				T.SEQ_NO = S.SEQ_NO,
				T.NO = S.NO,
				T.EXPECTED_DATE = S.EXPECTED_DATE,
				T.ACTUAL_DATE = S.ACTUAL_DATE,
				T.EXPECTED_FINISH_DATE = S.EXPECTED_FINISH_DATE,
				T.CUSTOMER_NAME = S.CUSTOMER_NAME,
				T.SALES_ORDER_NUMBER = S.SALES_ORDER_NUMBER,
				T.SALES_ORDER_ITEM = S.SALES_ORDER_ITEM,
				T.FAMILY_NAME = S.FAMILY_NAME,
				T.MATERIAL = S.MATERIAL,
				T.MATERIAL_TYPE = S.MATERIAL_TYPE,
				T.UNIT_FUNCTION_QTY = S.UNIT_FUNCTION_QTY,
				T.QTY = S.QTY,
				T.OPEN_QTY = S.OPEN_QTY,
				T.TOTAL_OPEN_FUNCTION_QTY = S.TOTAL_OPEN_FUNCTION_QTY,
				T.FINISH_DATE = S.FINISH_DATE,
				T.CUSTOMER_TYPE = S.CUSTOMER_TYPE,
				T.MO_CREATE_DATE = S.MO_CREATE_DATE,
				T.CUSTOMER_DELIVERY_DATE = S.CUSTOMER_DELIVERY_DATE,
				T.STANDARD_DELIVERY_DATE = S.STANDARD_DELIVERY_DATE,
				T.CUSTOMER_REQUEST_DATE = S.CUSTOMER_REQUEST_DATE,
				T.STATION_DESC = S.STATION_DESC,
				T.TYPE3 = S.TYPE3,
				T.LOW_VOLTAGE_ROOM = S.LOW_VOLTAGE_ROOM,
				T.REMARKS = S.REMARKS,
				T.EXPECTED_PRODUCTION_DATE = S.EXPECTED_PRODUCTION_DATE,
				T.SO_CREATE_DATE = S.SO_CREATE_DATE,
				T.PACKAGE_INFO = S.PACKAGE_INFO,
				T.VOLTAGE_LEVEL = S.VOLTAGE_LEVEL,
				T.TANK = S.TANK,
				T.ANNOTATIONS = S.ANNOTATIONS,
				T.FRONT_FRAME = S.FRONT_FRAME,
				T.BOTTOM_FRAME = S.BOTTOM_FRAME,
				T.SO_ITEM = S.SO_ITEM,
				T.MO_QTY = S.MO_QTY,
				T.DEMAND = S.DEMAND,
				T.NEW_EXCEPTION = S.NEW_EXCEPTION,
				T.VCB = S.VCB,
				T.HHHH = S.HHHH,
				T.IIII = S.IIII,
				T.JJJJ = S.JJJJ,
				T.KKKK = S.KKKK,
				T.LLLL = S.LLLL,
				T.MMMM = S.MMMM,
				T.NNNN = S.NNNN,
				T.OOOO = S.OOOO,
				T.PPPP = S.PPPP,
				T.CUSTOMER_REQUEST_WEEK = S.CUSTOMER_REQUEST_WEEK,
				T.STANDARD_REQUEST_WEEK = S.STANDARD_REQUEST_WEEK,
				T.ORDER_INTAKE_WEEK = S.ORDER_INTAKE_WEEK,
				T.TTTT = S.TTTT,
				T.UUUU = S.UUUU
		WHEN NOT MATCHED THEN
			INSERT (
            SEQ_NO, NO, EXPECTED_DATE, ACTUAL_DATE, MO_NUMBER, EXPECTED_FINISH_DATE,
            CUSTOMER_NAME, SALES_ORDER_NUMBER, SALES_ORDER_ITEM, FAMILY_NAME, MATERIAL,
            MATERIAL_TYPE, UNIT_FUNCTION_QTY, QTY, OPEN_QTY, TOTAL_OPEN_FUNCTION_QTY,
            FINISH_DATE, CUSTOMER_TYPE, MO_CREATE_DATE, CUSTOMER_DELIVERY_DATE,
            STANDARD_DELIVERY_DATE, CUSTOMER_REQUEST_DATE, STATION_DESC, TYPE3,
            LOW_VOLTAGE_ROOM, REMARKS, EXPECTED_PRODUCTION_DATE, SO_CREATE_DATE,
            PACKAGE_INFO, VOLTAGE_LEVEL, TANK, ANNOTATIONS, FRONT_FRAME, BOTTOM_FRAME,
            SO_ITEM, MO_QTY, DEMAND, NEW_EXCEPTION, VCB, HHHH, IIII, JJJJ, KKKK,
            LLLL, MMMM, NNNN, OOOO, PPPP, CUSTOMER_REQUEST_WEEK, STANDARD_REQUEST_WEEK,
            ORDER_INTAKE_WEEK, TTTT, UUUU
        )
        VALUES (
        	S.SEQ_NO, S.NO, S.EXPECTED_DATE, S.ACTUAL_DATE, S.MO_NUMBER, S.EXPECTED_FINISH_DATE,
            S.CUSTOMER_NAME, S.SALES_ORDER_NUMBER, S.SALES_ORDER_ITEM, S.FAMILY_NAME, S.MATERIAL,
            S.MATERIAL_TYPE, S.UNIT_FUNCTION_QTY, S.QTY, S.OPEN_QTY, S.TOTAL_OPEN_FUNCTION_QTY,
            S.FINISH_DATE, S.CUSTOMER_TYPE, S.MO_CREATE_DATE, S.CUSTOMER_DELIVERY_DATE,
            S.STANDARD_DELIVERY_DATE, S.CUSTOMER_REQUEST_DATE, S.STATION_DESC, S.TYPE3,
            S.LOW_VOLTAGE_ROOM, S.REMARKS, S.EXPECTED_PRODUCTION_DATE, S.SO_CREATE_DATE,
            S.PACKAGE_INFO, S.VOLTAGE_LEVEL, S.TANK, S.ANNOTATIONS, S.FRONT_FRAME, S.BOTTOM_FRAME,
            S.SO_ITEM, S.MO_QTY, S.DEMAND, S.NEW_EXCEPTION, S.VCB, S.HHHH, S.IIII, S.JJJJ, S.KKKK,
            S.LLLL, S.MMMM, S.NNNN, S.OOOO, S.PPPP, S.CUSTOMER_REQUEST_WEEK, S.STANDARD_REQUEST_WEEK,
            S.ORDER_INTAKE_WEEK, S.TTTT, S.UUUU
        )
	</update>
</mapper>
