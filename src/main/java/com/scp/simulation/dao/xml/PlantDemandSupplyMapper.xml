<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.simulation.dao.IPlantDemandSupplyDao">
    <sql id="filter">
        <if test="filters != null and filters != ''.toString()">
            and ${filters}
        </if>
        <if test="specialList != null and specialList.size() > 0">
            <foreach collection="specialList" item="list" separator=" or " open=" and (" close=")">
                t.${specialColumn} in
                <foreach collection="list" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </foreach>
        </if>
    </sql>

    <sql id="demandFilter">
        <if test="demandFilterList != null and demandFilterList != ''.toString()">
            and ${demandFilterList}
        </if>
    </sql>

    <sql id="supplyFilter">
        <if test="supplyFilterList != null and supplyFilterList != ''.toString()">
            and ${supplyFilterList}
        </if>
    </sql>

    <select id="queryDemandSupplyCascader" resultType="java.util.Map">
        select * from DEMAND_SUPPLY_DEMAND_FILTER_V t order by t.category,decode(t.name,'Others','zzz',t.name)
    </select>

    <select id="queryCriticalLevelList" resultType="java.util.Map">
        SELECT CRITICAL_ID AS "VALUE", "NAME" AS "LABEL" FROM DEMAND_SUPPLY_CRITICAL_LEVEL_SETTINGS
    </select>

    <select id="queryReport1CriticalScript" resultType="java.lang.Object">
        SELECT SCRIPTS FROM DEMAND_SUPPLY_CRITICAL_LEVEL_SETTINGS WHERE CRITICAL_ID = #{criticalID, jdbcType=VARCHAR}
    </select>

    <select id="queryReport1CriticalLevel" resultType="java.util.Map">
        SELECT COMMENTS, SCRIPTS FROM DEMAND_SUPPLY_CRITICAL_LEVEL_SETTINGS WHERE CRITICAL_ID = #{criticalID, jdbcType=VARCHAR}
    </select>

    <select id="queryReport1Group" resultType="com.scp.simulation.bean.DemandSupplyMaterial">
        SELECT GROUP_MATERIAL AS "material",
               PLANT_CODE AS "plantCode",
               MAX(OPEN_PO_QTY) as "OPEN_PO_QTY",
               MAX(STOCK_ON_HAND) AS "currentStock",
               MAX(SAFETY_STOCK * ${ssRatio}) AS "safetyStock",
               MAX(AMF * ${amfRatio}) as "amf",
               MAX(AMU * ${amuRatio}) as "amu",
               MAX(MTD_ORDER_INTAKE) as "mtd_order_intake",
               MAX(MTD_SALES) as "mtd_sales"
        FROM (
            SELECT T.GROUP_MATERIAL,
                   T.PLANT_CODE,
                   MAX(T4.OPEN_PO_QTY ${valueColumnName}) AS OPEN_PO_QTY,
                   MAX(T2.STOCK_ON_HAND ${valueColumnName}) AS STOCK_ON_HAND,
                   MAX(MMV.SAFETY_STOCK ${valueColumnName}) AS SAFETY_STOCK,
                   MAX(MMV.AMF ${valueColumnName}) as AMF,
                   MAX(MMV.AMU ${valueColumnName}) as AMU,
                   MAX(MTDOI.MTD_ORDER_INTAKE ${valueColumnName}) as MTD_ORDER_INTAKE,
                   MAX(MTDS.MTD_SALES ${valueColumnName}) as MTD_SALES
            FROM DEMAND_SUPPLY_DEMAND_V T
            LEFT JOIN (select MATERIAL, PLANT_CODE, SUM(ORDER_QUANTITY) AS MTD_ORDER_INTAKE
                     FROM DEMAND_ORDER_INTAKE_V
                     where CALENDAR_MONTH = TO_CHAR(SYSDATE, 'YYYYMM')
                     GROUP BY MATERIAL, PLANT_CODE) MTDOI ON T.MATERIAL = MTDOI.MATERIAL AND T.PLANT_CODE = MTDOI.PLANT_CODE
            LEFT JOIN (select MATERIAL, PLANT_CODE, SUM(ORDER_QUANTITY) AS MTD_SALES
                     from DEMAND_SALES_V
                     where CALENDAR_MONTH = TO_CHAR(SYSDATE, 'YYYYMM')
                     GROUP BY MATERIAL, PLANT_CODE) MTDS ON T.MATERIAL = MTDS.MATERIAL AND T.PLANT_CODE = MTDS.PLANT_CODE
            LEFT JOIN DEMAND_SUPPLY_SOH_V T2 ON T.MATERIAL = T2.MATERIAL AND T.PLANT_CODE = T2.PLANT_CODE AND NVL(T.GROUP_MATERIAL, 'Others') = NVL(T2.GROUP_MATERIAL, 'Others')
            INNER JOIN DEMAND_SUPPLY_CRITICAL_MATERIAL_V T3 ON T.MATERIAL = T3.MATERIAL AND T.PLANT_CODE = T3.PLANT_CODE AND T3.USER_ID = #{session.userid,jdbcType=VARCHAR}
            LEFT JOIN (SELECT MATERIAL, PLANT_CODE, SUM(PO_AB + PO_LA + PO_NON_ABLA) AS OPEN_PO_QTY
                     FROM OPEN_PO_STRUCTURE_V
                     GROUP BY MATERIAL, PLANT_CODE) T4 ON T.MATERIAL = T4.MATERIAL AND T.PLANT_CODE = T4.PLANT_CODE
            LEFT JOIN (SELECT MATERIAL, PLANT_CODE, SAFETY_STOCK, AMF, AMU FROM MATERIAL_MASTER_V) MMV ON T.MATERIAL = MMV.MATERIAL AND T.PLANT_CODE = MMV.PLANT_CODE
            WHERE T.GROUP_MATERIAL IS NOT NULL
               <include refid="filter"/>
               <include refid="demandFilter"/>
            GROUP BY T.GROUP_MATERIAL, T.PLANT_CODE

            UNION ALL

            SELECT T.GROUP_MATERIAL,
                   T.PLANT_CODE,
                   MAX(T4.OPEN_PO_QTY ${valueColumnName}),
                   MAX(T2.STOCK_ON_HAND ${valueColumnName}),
                   MAX(MMV.SAFETY_STOCK ${valueColumnName}),
                   MAX(MMV.AMF ${valueColumnName}),
                   MAX(MMV.AMU ${valueColumnName}),
                   MAX(MTDOI.MTD_ORDER_INTAKE ${valueColumnName}),
                   MAX(MTDS.MTD_SALES ${valueColumnName})
              FROM DEMAND_SUPPLY_SUPPLY_V T
                   LEFT JOIN (select MATERIAL, PLANT_CODE, SUM(ORDER_QUANTITY) AS MTD_ORDER_INTAKE
                            FROM DEMAND_ORDER_INTAKE_V
                            where CALENDAR_MONTH = TO_CHAR(SYSDATE, 'YYYYMM')
                            GROUP BY MATERIAL, PLANT_CODE) MTDOI ON T.MATERIAL = MTDOI.MATERIAL AND T.PLANT_CODE = MTDOI.PLANT_CODE
                   LEFT JOIN (select MATERIAL, PLANT_CODE, SUM(ORDER_QUANTITY) AS MTD_SALES
                            from DEMAND_SALES_V
                            where CALENDAR_MONTH = TO_CHAR(SYSDATE, 'YYYYMM')
                            GROUP BY MATERIAL, PLANT_CODE) MTDS ON T.MATERIAL = MTDS.MATERIAL AND T.PLANT_CODE = MTDS.PLANT_CODE
                   LEFT JOIN DEMAND_SUPPLY_SOH_V T2 ON T.MATERIAL = T2.MATERIAL AND T.PLANT_CODE = T2.PLANT_CODE AND NVL(T.GROUP_MATERIAL, 'Others') = NVL(T2.GROUP_MATERIAL, 'Others')
                   INNER JOIN DEMAND_SUPPLY_CRITICAL_MATERIAL_V T3 ON T.MATERIAL = T3.MATERIAL AND T.PLANT_CODE = T3.PLANT_CODE AND T3.USER_ID = #{session.userid,jdbcType=VARCHAR}
                   LEFT JOIN (SELECT MATERIAL, PLANT_CODE, SUM(PO_AB + PO_LA + PO_NON_ABLA) AS OPEN_PO_QTY
                            FROM OPEN_PO_STRUCTURE_V
                            GROUP BY MATERIAL, PLANT_CODE) T4 ON T.MATERIAL = T4.MATERIAL AND T.PLANT_CODE = T4.PLANT_CODE
                   LEFT JOIN (SELECT MATERIAL, PLANT_CODE, SAFETY_STOCK, AMF, AMU FROM MATERIAL_MASTER_V ) MMV ON T.MATERIAL = MMV.MATERIAL AND T.PLANT_CODE =MMV.PLANT_CODE
             WHERE T.GROUP_MATERIAL IS NOT NULL
                   <include refid="filter"/>
                   <include refid="supplyFilter"/>
             GROUP BY T.GROUP_MATERIAL, T.PLANT_CODE
        ) MM
        GROUP BY GROUP_MATERIAL, PLANT_CODE
    </select>

    <select id="queryReport1Material" resultType="com.scp.simulation.bean.DemandSupplyMaterial">
        SELECT MATERIAL AS "material",
               PLANT_CODE AS "plantCode",
               MAX(OPEN_PO_QTY) AS "OPEN_PO_QTY",
               MAX(STOCK_ON_HAND) AS "currentStock",
               MAX(SAFETY_STOCK * ${ssRatio}) AS "safetyStock",
               MAX(AMF * ${amfRatio}) as "amf",
               MAX(AMU * ${amuRatio}) as "amu",
               MAX(MTD_ORDER_INTAKE) as "mtd_order_intake",
               MAX(MTD_SALES) as "mtd_sales"
        FROM (
            SELECT T.MATERIAL,
                   T.PLANT_CODE,
                   MAX(T4.OPEN_PO_QTY ${valueColumnName}) AS OPEN_PO_QTY,
                   MAX(T2.STOCK_ON_HAND ${valueColumnName}) AS STOCK_ON_HAND,
                   MAX(MMV.SAFETY_STOCK ${valueColumnName}) AS SAFETY_STOCK,
                   MAX(MMV.AMF ${valueColumnName}) as AMF,
                   MAX(MMV.AMU ${valueColumnName}) as AMU,
                   MAX(MTDOI.MTD_ORDER_INTAKE ${valueColumnName}) AS MTD_ORDER_INTAKE,
                   MAX(MTDS.MTD_SALES ${valueColumnName}) AS MTD_SALES
             FROM  DEMAND_SUPPLY_DEMAND_V T
                   LEFT JOIN (select MATERIAL, PLANT_CODE, SUM(ORDER_QUANTITY) AS MTD_ORDER_INTAKE
                                FROM DEMAND_ORDER_INTAKE_V
                                where CALENDAR_MONTH = TO_CHAR(SYSDATE, 'YYYYMM')
                                GROUP BY MATERIAL, PLANT_CODE) MTDOI ON T.MATERIAL = MTDOI.MATERIAL AND T.PLANT_CODE = MTDOI.PLANT_CODE
                   LEFT JOIN (select MATERIAL, PLANT_CODE, SUM(ORDER_QUANTITY) AS MTD_SALES
                                from DEMAND_SALES_V
                                where CALENDAR_MONTH = TO_CHAR(SYSDATE, 'YYYYMM')
                                GROUP BY MATERIAL, PLANT_CODE) MTDS ON T.MATERIAL = MTDS.MATERIAL AND T.PLANT_CODE = MTDS.PLANT_CODE
                   LEFT JOIN DEMAND_SUPPLY_SOH_V T2 ON T.MATERIAL = T2.MATERIAL AND T.PLANT_CODE = T2.PLANT_CODE AND NVL(T.GROUP_MATERIAL, 'Others') = NVL(T2.GROUP_MATERIAL, 'Others')
                   INNER JOIN DEMAND_SUPPLY_CRITICAL_MATERIAL_V T3 ON T.MATERIAL = T3.MATERIAL AND T.PLANT_CODE = T3.PLANT_CODE AND T3.USER_ID = #{session.userid,jdbcType=VARCHAR}
                   LEFT JOIN (SELECT MATERIAL, PLANT_CODE, SUM(PO_AB + PO_LA + PO_NON_ABLA) AS OPEN_PO_QTY
                              FROM OPEN_PO_STRUCTURE_V
                              GROUP BY MATERIAL, PLANT_CODE) T4 ON T.MATERIAL = T4.MATERIAL AND T.PLANT_CODE = T4.PLANT_CODE
                    LEFT JOIN (SELECT MATERIAL, PLANT_CODE, SAFETY_STOCK, AMF, AMU FROM MATERIAL_MASTER_V ) MMV ON T.MATERIAL = MMV.MATERIAL AND T.PLANT_CODE = MMV.PLANT_CODE
            WHERE T.GROUP_MATERIAL IS NULL
            <include refid="filter"/>
            <include refid="demandFilter"/>
            GROUP BY T.MATERIAL, T.PLANT_CODE

            UNION ALL

            SELECT T.MATERIAL,
                   T.PLANT_CODE,
                   MAX(T4.OPEN_PO_QTY ${valueColumnName}),
                   MAX(T2.STOCK_ON_HAND ${valueColumnName}),
                   MAX(MMV.SAFETY_STOCK ${valueColumnName}),
                   MAX(MMV.AMF ${valueColumnName}),
                   MAX(MMV.AMU ${valueColumnName}),
                   MAX(MTDOI.MTD_ORDER_INTAKE ${valueColumnName}),
                   MAX(MTDS.MTD_SALES ${valueColumnName})
             FROM  DEMAND_SUPPLY_SUPPLY_V T
                   LEFT JOIN (select MATERIAL, PLANT_CODE, SUM(ORDER_QUANTITY) AS MTD_ORDER_INTAKE
                                FROM DEMAND_ORDER_INTAKE_V
                                where CALENDAR_MONTH = TO_CHAR(SYSDATE, 'YYYYMM')
                                GROUP BY MATERIAL, PLANT_CODE) MTDOI ON T.MATERIAL = MTDOI.MATERIAL AND T.PLANT_CODE = MTDOI.PLANT_CODE
                   LEFT JOIN (select MATERIAL, PLANT_CODE, SUM(ORDER_QUANTITY) AS MTD_SALES
                                from DEMAND_SALES_V
                                where CALENDAR_MONTH = TO_CHAR(SYSDATE, 'YYYYMM')
                                GROUP BY MATERIAL, PLANT_CODE) MTDS ON T.MATERIAL = MTDS.MATERIAL AND T.PLANT_CODE = MTDS.PLANT_CODE
                   LEFT JOIN DEMAND_SUPPLY_SOH_V T2 ON T.MATERIAL = T2.MATERIAL AND T.PLANT_CODE = T2.PLANT_CODE AND NVL(T.GROUP_MATERIAL, 'Others') = NVL(T2.GROUP_MATERIAL, 'Others')
                   INNER JOIN DEMAND_SUPPLY_CRITICAL_MATERIAL_V T3 ON T.MATERIAL = T3.MATERIAL AND T.PLANT_CODE = T3.PLANT_CODE AND T3.USER_ID = #{session.userid,jdbcType=VARCHAR}
                   LEFT JOIN (SELECT MATERIAL, PLANT_CODE, SUM(PO_AB + PO_LA + PO_NON_ABLA) AS OPEN_PO_QTY
                              FROM OPEN_PO_STRUCTURE_V
                              GROUP BY MATERIAL, PLANT_CODE) T4 ON T.MATERIAL = T4.MATERIAL AND T.PLANT_CODE = T4.PLANT_CODE
                   LEFT JOIN (SELECT MATERIAL, PLANT_CODE, SAFETY_STOCK, AMF, AMU FROM MATERIAL_MASTER_V) MMV ON T.MATERIAL = MMV.MATERIAL AND T.PLANT_CODE = MMV.PLANT_CODE
        WHERE T.GROUP_MATERIAL IS NULL
            <include refid="filter"/>
            <include refid="supplyFilter"/>
            GROUP BY T.MATERIAL, T.PLANT_CODE
        ) MM
        GROUP BY MATERIAL, PLANT_CODE
    </select>

    <select id="queryReport1Demand" resultType="java.util.HashMap">
        SELECT PLANT_CODE,
               MATERIAL,
               GROUP_MATERIAL,
               OPEN_PO_QTY,
               STOCK_ON_HAND,
               SAFETY_STOCK,
               AMF,
               AMU,
               MTD_ORDER_INTAKE,
               MTD_SALES,
               CATEGORY,
               MATERIAL_CATEGORY,
               VENDOR_CODE,
               VENDOR_SEARCH_NAME,
               VENDOR_NAME,
               LT_RANGE,
               MATERIAL_ST_PLANT,
               ABC,
               CALCULATED_ABC,
               CALCULATED_FMR,
               PROCUREMENT_TYPE,
               ACTIVENESS,
               STOCKING_POLICY,
               COUNTRY,
               MATERIAL_OWNER_NAME,
               MATERIAL_OWNER_SESA,
               MRP_CONTROLLER,
               ROUNDING_VALUE,
               PRODUCT_LINE,
               ENTITY,
               CLUSTER_NAME,
               TEMP_UNIT_COST AS UNIT_COST,
               BU,
               LOCAL_BU,
               LOCAL_PRODUCT_FAMILY,
               LOCAL_PRODUCT_LINE,
               LOCAL_PRODUCT_SUBFAMILY,
               PRODUCTION_LINE,
               PLANT_TYPE,
               GROSS_WEIGHT_IN_KG,
               REPL_STRATEGY,
               SOURCE_CATEGORY,
               LEAD_TIME,
               MOQ_TO_CUSTOMER,
               MINIMUM_LOT_SIZE,
               RV_TO_CUSTOMER,
               AVG_MONTHLY_ORDER_INTAKE,
               AVG_MONTHLY_SALES,
               PURCHASING_GROUP,
               SS3,
               AMF_ONEMM,
               AMU_ONEMM,
               TRIGGER_BTN_DATE,
               TRIGGER_DELAY_DAYS_CD,
               <foreach collection="dates" item="item" separator=",">
                    <choose>
                        <when test="selectedCategory != null and selectedCategory.size() > 0">
                            CASE WHEN '${item}' = 'PAST_DUE'
                            THEN (
                                CASE WHEN
                                    <choose>
                                        <when test="pastDue != null and pastDue.size() > 0">
                                            CATEGORY IN(
                                                <foreach collection="pastDue" item="itemitem" separator=",">
                                                    '${itemitem}'
                                                </foreach>)
                                        </when>
                                        <otherwise>
                                            1 = 0
                                        </otherwise>
                                    </choose>
                                THEN "'${item}'_QTY"
                                ELSE 0 END
                            )
                            ELSE (
                            CASE WHEN
                            <choose>
                                <when test="futureDue != null and futureDue.size() > 0">
                                    CATEGORY IN
                                    (<foreach collection="futureDue" item="itemitem" separator=",">
                                    '${itemitem}'
                                </foreach>
                                    )
                                </when>
                                <otherwise>
                                    1 = 0
                                </otherwise>
                            </choose>
                            THEN "'${item}'_QTY"
                            ELSE 0 END
                            ) END AS "${item}",
                        </when>
                        <otherwise>
                            "'${item}'_QTY" AS "${item}",
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="openSOExcludeFilterList.contains('OSO_NORMAL')">
                            0 AS "TIPS_${item}_OSO_NORMAL",
                        </when>
                        <otherwise>
                            "'${item}'_OSO_NORMAL" AS "TIPS_${item}_OSO_NORMAL",
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="openSOExcludeFilterList.contains('OSO_CB')">
                            0 AS "TIPS_${item}_OSO_CB",
                        </when>
                        <otherwise>
                            "'${item}'_OSO_CB" AS "TIPS_${item}_OSO_CB",
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="openSOExcludeFilterList.contains('UD_MID_AGING')">
                            0 AS "TIPS_${item}_UD_MID_AGING",
                        </when>
                        <otherwise>
                            "'${item}'_UD_MID_AGING" AS "TIPS_${item}_UD_MID_AGING",
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="openSOExcludeFilterList.contains('UD_CB')">
                            0 AS "TIPS_${item}_UD_CB",
                        </when>
                        <otherwise>
                            "'${item}'_UD_CB" AS "TIPS_${item}_UD_CB",
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="openSOExcludeFilterList.contains('UD_NORMAL')">
                            0 AS "TIPS_${item}_UD_NORMAL",
                        </when>
                        <otherwise>
                            "'${item}'_UD_NORMAL" AS "TIPS_${item}_UD_NORMAL",
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="openSOExcludeFilterList.contains('UD_LONG_AGING')">
                            0 AS "TIPS_${item}_UD_LONG_AGING",
                        </when>
                        <otherwise>
                            "'${item}'_UD_LONG_AGING" AS "TIPS_${item}_UD_LONG_AGING",
                        </otherwise>
                    </choose>
                    <choose>
                        <when test="transferPOExcludeFilterList.contains('TRANSFER_PO_AT')">
                            0 AS "TIPS_${item}_TRANSFER_PO_AT",
                        </when>
                        <otherwise>
                            "'${item}'_TRANSFER_PO_AT" AS "TIPS_${item}_TRANSFER_PO_AT",
                        </otherwise>
                    </choose>
                    <choose>
                       <when test="transferPOExcludeFilterList.contains('TRANSFER_PO_Q')">
                           0 AS "TIPS_${item}_TRANSFER_PO_Q",
                       </when>
                       <otherwise>
                           "'${item}'_TRANSFER_PO_Q" AS "TIPS_${item}_TRANSFER_PO_Q",
                       </otherwise>
                   </choose>
                    <choose>
                       <when test="transferPOExcludeFilterList.contains('TRANSFER_PO_Z')">
                           0 AS "TIPS_${item}_TRANSFER_PO_Z",
                       </when>
                       <otherwise>
                           "'${item}'_TRANSFER_PO_Z" AS "TIPS_${item}_TRANSFER_PO_Z",
                       </otherwise>
                   </choose>
                    <choose>
                       <when test="transferPOExcludeFilterList.contains('TRANSFER_PO_E')">
                           0 AS "TIPS_${item}_TRANSFER_PO_E",
                       </when>
                       <otherwise>
                           "'${item}'_TRANSFER_PO_E" AS "TIPS_${item}_TRANSFER_PO_E",
                       </otherwise>
                   </choose>
                   <choose>
                       <when test="transferPOExcludeFilterList.contains('TRANSFER_PO_OTHERS')">
                           0 AS "TIPS_${item}_TRANSFER_PO_OTHERS"
                       </when>
                       <otherwise>
                           "'${item}'_TRANSFER_PO_OTHERS" AS "TIPS_${item}_TRANSFER_PO_OTHERS"
                       </otherwise>
                   </choose>
               </foreach>
        FROM (
            SELECT *
                FROM (
                    SELECT T.PLANT_CODE,
                           T.MATERIAL,
                           T.GROUP_MATERIAL,
                           NVL(T4.OPEN_PO_QTY ${valueColumnName}, 0) OPEN_PO_QTY,
                           NVL(T2.STOCK_ON_HAND ${valueColumnName}, 0) STOCK_ON_HAND,
                           NVL(MMV.SAFETY_STOCK ${valueColumnName}, 0) SAFETY_STOCK,
                           NVL(MMV.AMF ${valueColumnName}, 0) AMF,
                           NVL(MMV.AMU ${valueColumnName}, 0) AMU,
                           NVL(MTDOI.MTD_ORDER_INTAKE ${valueColumnName}, 0) MTD_ORDER_INTAKE,
                           NVL(MTDS.MTD_SALES ${valueColumnName}, 0) MTD_SALES,
                           DECODE(T.DEMAND_CATEGORY, 'Demand_SO', 'Open Sales Order', 'Demand_MO', 'Order Reservation', 'Ind_FCST', 'Indepedent Requirement', 'Dep_FCST', 'Depedent Requirement', 'Transfer_PO', 'Transfer PO') CATEGORY,
                           T.MATERIAL_CATEGORY,
                           T.VENDOR_CODE,
                           T.VENDOR_SEARCH_NAME,
                           T.VENDOR_NAME,
                           T.LT_RANGE,
                           T.MATERIAL_ST_PLANT,
                           T.ABC,
                           T.CALCULATED_ABC,
                           T.CALCULATED_FMR,
                           T.PROCUREMENT_TYPE,
                           T.ACTIVENESS,
                           T.STOCKING_POLICY,
                           T.COUNTRY,
                           T.MATERIAL_OWNER_NAME,
                           T.MATERIAL_OWNER_SESA,
                           T.MRP_CONTROLLER,
                           T.ROUNDING_VALUE,
                           T.PRODUCT_LINE,
                           T.ENTITY,
                           T.CLUSTER_NAME,
                           T.BU,
                           T.LOCAL_BU,
                           T.LOCAL_PRODUCT_FAMILY,
                           T.LOCAL_PRODUCT_LINE,
                           T.LOCAL_PRODUCT_SUBFAMILY,
                           T.PRODUCTION_LINE,
                           T.PLANT_TYPE,
                           T.REPL_STRATEGY,
                           T.SOURCE_CATEGORY,
                           ${calQtyColumn},
                           T.UNIT_COST AS TEMP_UNIT_COST,
                           T.UNIT_COST,
                           T.GROSS_WEIGHT_IN_KG,
                           T.AVG_SELLING_PRICE_RMB,
                           T.${dateColumnName},
                           T.OSO_NORMAL,
                           T.OSO_CB,
                           T.UD_MID_AGING,
                           T.UD_CB,
                           T.UD_NORMAL,
                           T.UD_LONG_AGING,
                           T.TRANSFER_PO_AT,
                           T.TRANSFER_PO_Q,
                           T.TRANSFER_PO_Z,
                           T.TRANSFER_PO_E,
                           T.TRANSFER_PO_OTHERS,
                           T.LEAD_TIME,
                           MMV.MOQ_TO_CUSTOMER,
                           MMV.MINIMUM_LOT_SIZE,
                           MMV.RV_TO_CUSTOMER,
                           MMV.AVG_MONTHLY_ORDER_INTAKE,
                           MMV.AVG_MONTHLY_SALES,
                           MMV.PURCHASING_GROUP,
                           MMV.SS3,
                           MMV.AMF_ONEMM,
                           MMV.AMU_ONEMM,
                           QML.VALIDATE_TO AS TRIGGER_BTN_DATE,
                           QML.QMAX_LEAD_TIME AS TRIGGER_DELAY_DAYS_CD
                    FROM DEMAND_SUPPLY_DEMAND_V T
                             LEFT JOIN (select MATERIAL, PLANT_CODE, SUM(ORDER_QUANTITY) AS MTD_ORDER_INTAKE
                                            FROM DEMAND_ORDER_INTAKE_V
                                            where CALENDAR_MONTH = TO_CHAR(SYSDATE, 'YYYYMM')
                                            GROUP BY MATERIAL, PLANT_CODE) MTDOI ON T.MATERIAL = MTDOI.MATERIAL AND T.PLANT_CODE = MTDOI.PLANT_CODE
                             LEFT JOIN (select MATERIAL, PLANT_CODE, SUM(ORDER_QUANTITY) AS MTD_SALES
                                            from DEMAND_SALES_V
                                            where CALENDAR_MONTH = TO_CHAR(SYSDATE, 'YYYYMM')
                                            GROUP BY MATERIAL, PLANT_CODE) MTDS ON T.MATERIAL = MTDS.MATERIAL AND T.PLANT_CODE = MTDS.PLANT_CODE
                             LEFT JOIN DEMAND_SUPPLY_SOH_V T2 ON T.MATERIAL = T2.MATERIAL AND T.PLANT_CODE = T2.PLANT_CODE AND NVL(T.GROUP_MATERIAL, 'Others') = NVL(T2.GROUP_MATERIAL, 'Others')
                             INNER JOIN DEMAND_SUPPLY_CRITICAL_MATERIAL_V T3 ON T.MATERIAL = T3.MATERIAL AND T.PLANT_CODE = T3.PLANT_CODE AND T3.USER_ID = #{session.userid,jdbcType=VARCHAR}
                             LEFT JOIN (SELECT MOQ_TO_CUSTOMER,
                                        MINIMUM_LOT_SIZE,
                                        RV_TO_CUSTOMER,
                                        AVG_MONTHLY_ORDER_INTAKE,
                                        AVG_MONTHLY_SALES,
                                        PURCHASING_GROUP,
                                        SS3,
                                        SAFETY_STOCK,
                                        AMF,
                                        AMU,
                                        AMF_ONEMM,
                                        AMU_ONEMM,
                                        MATERIAL,
                                        PLANT_CODE
                                        FROM MATERIAL_MASTER_V) MMV ON T.MATERIAL = MMV.MATERIAL AND T.PLANT_CODE = MMV.PLANT_CODE
                             LEFT JOIN (SELECT * FROM MM3_TRIGGER_LIST_V WHERE VALIDATE_TO > trunc(SYSDATE,'DD')) QML ON T.MATERIAL = QML.MATERIAL
                             LEFT JOIN (SELECT MATERIAL, PLANT_CODE, SUM(PO_AB + PO_LA + PO_NON_ABLA) AS OPEN_PO_QTY
                                        FROM OPEN_PO_STRUCTURE_V
                                        GROUP BY MATERIAL, PLANT_CODE) T4 ON T.MATERIAL = T4.MATERIAL AND T.PLANT_CODE = T4.PLANT_CODE
                    WHERE T.${dateColumnName} IS NOT NULL
                    <include refid="filter"/>
                    <include refid="demandFilter"/>
                ) PIVOT (
                    SUM(QUANTITY ${valueColumnName}) QTY,
                    SUM(OSO_NORMAL ${valueColumnName}) OSO_NORMAL,
                    SUM(OSO_CB ${valueColumnName}) OSO_CB,
                    SUM(UD_MID_AGING ${valueColumnName}) UD_MID_AGING,
                    SUM(UD_CB ${valueColumnName}) UD_CB,
                    SUM(UD_NORMAL ${valueColumnName}) UD_NORMAL,
                    SUM(UD_LONG_AGING ${valueColumnName}) UD_LONG_AGING,
                    SUM(TRANSFER_PO_AT ${valueColumnName}) TRANSFER_PO_AT,
                    SUM(TRANSFER_PO_Q ${valueColumnName}) TRANSFER_PO_Q,
                    SUM(TRANSFER_PO_Z ${valueColumnName}) TRANSFER_PO_Z,
                    SUM(TRANSFER_PO_E ${valueColumnName}) TRANSFER_PO_E,
                    SUM(TRANSFER_PO_OTHERS ${valueColumnName}) TRANSFER_PO_OTHERS
                    FOR ${dateColumnName} IN
                        (
                        <foreach collection="dates" item="item" separator=",">
                            '${item}'
                        </foreach>
                        )
                    )
            ) NN
        <if test="selectedCategory != null and selectedCategory.size() > 0">
            <where>
                CATEGORY IN (
                <foreach collection="selectedCategory" item="item" separator=",">
                    '${item}'
                </foreach>
                )
            </where>
        </if>
    </select>

    <select id="queryReport1Supply" resultType="java.util.HashMap">
        WITH MA_BASE AS (
            SELECT MATERIAL, PLANT_CODE, SUM(QTY) QTY, COMMIT_DATE
                  FROM DEMAND_SUPPLY_MANUAL_PO_COMMIT
                 WHERE (PURCH_ORDER_NUMBER IS NULL OR PURCH_ORDER_ITEM IS NULL)
                   AND MATERIAL IS NOT NULL
                   AND PLANT_CODE IS NOT NULL
                   AND COMMIT_DATE IS NOT NULL
                   AND QTY IS NOT NULL
                   AND USER_ID = #{session.userid,jdbcType=VARCHAR}
                 GROUP BY MATERIAL, PLANT_CODE, COMMIT_DATE
        ), MA_COMMIT AS (
            SELECT T.MATERIAL,
                   T.PLANT_CODE,
                   'MANUAL'                                   AS CONFIRM_CAT,
                   GM.GROUP_MATERIAL                          AS GROUP_MATERIAL,
                   T.QTY                                      AS QUANTITY,
                   'Supply_PO'                                AS SUPPLY_CATEGORY,
                   CASE WHEN CALENDAR.DATE$ &lt; TRUNC(SYSDATE, 'DD')
                        THEN 'PAST_DUE'
                        ELSE REPLACE(CALENDAR.TEXT, '/', '') END AS BY_DAY,
                   CASE WHEN CALENDAR.DATE$ &lt; TRUNC(SYSDATE, 'DD')
                        THEN 'PAST_DUE'
                        ELSE CALENDAR.YEAR || 'W' || CALENDAR.WEEK_NO END AS BY_WEEK,
                   CASE WHEN CALENDAR.DATE$ &lt; TRUNC(SYSDATE, 'DD')
                        THEN 'PAST_DUE'
                        ELSE CALENDAR.YEAR || CALENDAR.MONTH END AS BY_MONTH,
                   NVL(MAT.MATERIAL_CATEGORY, 'Others')       AS MATERIAL_CATEGORY,
                   MAT.VENDOR_CODE                            AS VENDOR_CODE,
                   'MANUAL'                                   AS VENDOR_SEARCH_NAME,
                   'MANUAL'                                   AS VENDOR_NAME,
                   NVL(MAT.LT_RANGE, 'Others')                as LT_RANGE,
                   NVL(MAT.MATERIAL_ST_PLANT, 'Others')       as MATERIAL_ST_PLANT,
                   NVL(MAT.ABC, 'Others')                 as ABC,
                   NVL(MAT.MOQ_TO_CUSTOMER, 0)                as MOQ_TO_CUSTOMER,
                   NVL(MAT.MINIMUM_LOT_SIZE, 0)               AS MINIMUM_LOT_SIZE,
                   NVL(MAT.RV_TO_CUSTOMER, 0)                 AS RV_TO_CUSTOMER,
                   NVL(MAT.AVG_MONTHLY_ORDER_INTAKE, 0)                  AS AVG_MONTHLY_ORDER_INTAKE,
                   NVL(MAT.AVG_MONTHLY_SALES, 0)                  AS AVG_MONTHLY_SALES,
                   NVL(MAT.PURCHASING_GROUP, 'Others')        AS PURCHASING_GROUP,
                   NVL(MAT.SS3, 0)                            AS SS3,
                   NVL(MAT.SAFETY_STOCK, 0)                   AS SAFETY_STOCK,
                   NVL(MAT.AMU, 0)                            AS AMU,
                   NVL(MAT.AMF, 0)                            AS AMF,
                   NVL(MAT.AMF_ONEMM, 0)                      AS AMF_ONEMM,
                   NVL(MAT.AMU_ONEMM, 0)                      AS AMU_ONEMM,
                   QML.VALIDATE_TO                            AS TRIGGER_BTN_DATE,
                   QML.QMAX_LEAD_TIME                         AS TRIGGER_DELAY_DAYS_CD,
                   NVL(MAT.CALCULATED_ABC, 'Others')          AS CALCULATED_ABC,
                   NVL(MAT.CALCULATED_FMR, 'Others')          AS CALCULATED_FMR,
                   NVL(MAT.PROCUREMENT_TYPE, 'Others')        as PROCUREMENT_TYPE,
                   NVL(MAT.ACTIVENESS, 'Others')              as ACTIVENESS,
                   NVL(MAT.STOCKING_POLICY, 'Others')         AS STOCKING_POLICY,
                   NVL(MAT.COUNTRY_CODE, 'Others')            as COUNTRY,
                   nvl(MAT.MATERIAL_OWNER_NAME, 'Others')     as MATERIAL_OWNER_NAME,
                   nvl(MAT.MATERIAL_OWNER_SESA, 'Others')     as MATERIAL_OWNER_SESA,
                   nvl(MAT.mrp_controller, 'Others')          as MRP_CONTROLLER,
                   nvl(MAT.ROUNDING_VALUE, 0)                 as ROUNDING_VALUE,
                   nvl(MAT.product_line, 'Others')            as PRODUCT_LINE,
                   nvl(MAT.ENTITY, 'Others')                  as ENTITY,
                   nvl(MAT.cluster_name, 'Others')            as CLUSTER_NAME,
                   nvl(MAT.bu, 'Others')                      as BU,
                   nvl(MAT.LOCAL_BU, 'Others')                as LOCAL_BU,
                   nvl(MAT.local_product_family, 'Others')    as LOCAL_PRODUCT_FAMILY,
                   nvl(MAT.local_product_line, 'Others')      as LOCAL_PRODUCT_LINE,
                   nvl(MAT.local_product_subfamily, 'Others') as LOCAL_PRODUCT_SUBFAMILY,
                   Nvl(MAT.PRODUCTION_LINE, 'Others')         as PRODUCTION_LINE,
                   Nvl(MAT.PLANT_TYPE, 'Others')              as PLANT_TYPE,
                   NVL(MAT.REPL_STRATEGY, 'Others')           AS REPL_STRATEGY,
                   'Others'                                   AS ACC_CATEGORY,
                   'Others'                                   AS SOURCE_CATEGORY,
                   MAT.UNIT_COST,
                   MAT.AVG_SELLING_PRICE_RMB,
                   T.QTY AS MANUAL_QTY,
                   T.QTY * MAT.UNIT_COST MANUAL_MVP,
                   T.QTY * MAT.AVG_SELLING_PRICE_RMB MANUAL_VALUE
            FROM MA_BASE T
                     LEFT JOIN (SELECT * FROM MM3_TRIGGER_LIST_V WHERE VALIDATE_TO > trunc(SYSDATE,'DD')) QML ON T.MATERIAL = QML.MATERIAL
                     LEFT JOIN MATERIAL_MASTER_V MAT ON T.MATERIAL = MAT.MATERIAL AND T.PLANT_CODE = MAT.PLANT_CODE
                     LEFT JOIN SY_CALENDAR CALENDAR ON T.COMMIT_DATE = CALENDAR.TEXT AND CALENDAR.NAME = 'National Holidays'
                     LEFT JOIN DEMAND_SUPPLY_GROUP_MATERIAL_V GM ON T.MATERIAL = GM.MATERIAL AND T.PLANT_CODE = GM.PLANT_CODE
        ), PO_BASE AS (
            SELECT PURCH_ORDER_NUMBER, PURCH_ORDER_ITEM, QTY, COMMIT_DATE,
                   CASE WHEN SC.DATE$ &lt; TRUNC(SYSDATE,'DD') THEN 'PAST_DUE' ELSE TO_CHAR(SC.DATE$, 'YYYYMMDD') END BY_DAY,
                   CASE WHEN SC.DATE$ &lt; TRUNC(SYSDATE,'DD') THEN 'PAST_DUE' ELSE SC.YEAR || 'W' || SC.WEEK_NO END BY_WEEK,
                   CASE WHEN SC.DATE$ &lt; TRUNC(SYSDATE,'DD') THEN 'PAST_DUE' ELSE SC.YEAR || SC.MONTH END BY_MONTH
              FROM DEMAND_SUPPLY_MANUAL_PO_COMMIT T
                   LEFT JOIN SY_CALENDAR SC ON T.COMMIT_DATE = SC.TEXT AND SC.NAME = 'National Holidays'
             WHERE PURCH_ORDER_NUMBER IS NOT NULL
               AND PURCH_ORDER_ITEM IS NOT NULL
               AND COMMIT_DATE IS NOT NULL
               AND QTY IS NOT NULL
               AND USER_ID = #{session.userid,jdbcType=VARCHAR}
        ), SUPPLY AS (
            SELECT SUPPLY.PURCH_ORDER_NUMBER,
                   SUPPLY.PURCH_ORDER_ITEM,
                   MAX(SUPPLY.LEAD_TIME)               AS LEAD_TIME,
                   MAX(SUPPLY.BY_DAY)                  AS BY_DAY,
                   MAX(SUPPLY.BY_WEEK)                 AS BY_WEEK,
                   MAX(SUPPLY.BY_MONTH)                AS BY_MONTH,
                   MAX(SUPPLY.MATERIAL)                AS MATERIAL,
                   MAX(SUPPLY.PLANT_CODE)              AS PLANT_CODE,
                   MAX(SUPPLY.CONFIRM_CAT)             AS CONFIRM_CAT,
                   MAX(SUPPLY.GROUP_MATERIAL)          AS GROUP_MATERIAL,
                   MAX(SUPPLY.MATERIAL_CATEGORY)       AS MATERIAL_CATEGORY,
                   MAX(SUPPLY.VENDOR_CODE)             AS VENDOR_CODE,
                   MAX(SUPPLY.LT_RANGE)                AS LT_RANGE,
                   MAX(SUPPLY.MATERIAL_ST_PLANT)       AS MATERIAL_ST_PLANT,
                   MAX(SUPPLY.ABC)                 AS ABC,
                   MAX(MMV.MOQ_TO_CUSTOMER)            AS MOQ_TO_CUSTOMER,
                   MAX(MMV.MINIMUM_LOT_SIZE)           AS MINIMUM_LOT_SIZE,
                   MAX(MMV.RV_TO_CUSTOMER)             AS RV_TO_CUSTOMER,
                   MAX(MMV.AVG_MONTHLY_ORDER_INTAKE)             AS AVG_MONTHLY_ORDER_INTAKE,
                   MAX(MMV.AVG_MONTHLY_SALES)             AS AVG_MONTHLY_SALES,
                   MAX(MMV.PURCHASING_GROUP)           AS PURCHASING_GROUP,
                   MAX(MMV.SS3)                        AS SS3,
                   MAX(MMV.SAFETY_STOCK)               AS SAFETY_STOCK,
                   MAX(MMV.AMU)                        AS AMU,
                   MAX(MMV.AMF)                        AS AMF,
                   MAX(MMV.AMF_ONEMM)                  AS AMF_ONEMM,
                   MAX(MMV.AMU_ONEMM)                  AS AMU_ONEMM,
                   MAX(QML.VALIDATE_TO)                AS TRIGGER_BTN_DATE,
                   MAX(QML.QMAX_LEAD_TIME)             AS TRIGGER_DELAY_DAYS_CD,
                   MAX(SUPPLY.CALCULATED_ABC)          AS CALCULATED_ABC,
                   MAX(SUPPLY.CALCULATED_FMR)          AS CALCULATED_FMR,
                   MAX(SUPPLY.PROCUREMENT_TYPE)        AS PROCUREMENT_TYPE,
                   MAX(SUPPLY.ACTIVENESS)              AS ACTIVENESS,
                   MAX(SUPPLY.STOCKING_POLICY)         AS STOCKING_POLICY,
                   MAX(SUPPLY.COUNTRY)                 AS COUNTRY,
                   MAX(SUPPLY.MATERIAL_OWNER_NAME)     AS MATERIAL_OWNER_NAME,
                   MAX(SUPPLY.MATERIAL_OWNER_SESA)     AS MATERIAL_OWNER_SESA,
                   MAX(SUPPLY.MRP_CONTROLLER)          AS MRP_CONTROLLER,
                   MAX(SUPPLY.ROUNDING_VALUE)          AS ROUNDING_VALUE,
                   MAX(SUPPLY.PRODUCT_LINE)            AS PRODUCT_LINE,
                   MAX(SUPPLY.ENTITY)                  AS ENTITY,
                   MAX(SUPPLY.CLUSTER_NAME)            AS CLUSTER_NAME,
                   MAX(SUPPLY.BU)                      AS BU,
                   MAX(SUPPLY.LOCAL_BU)                AS LOCAL_BU,
                   MAX(SUPPLY.LOCAL_PRODUCT_FAMILY)    AS LOCAL_PRODUCT_FAMILY,
                   MAX(SUPPLY.LOCAL_PRODUCT_LINE)      AS LOCAL_PRODUCT_LINE,
                   MAX(SUPPLY.LOCAL_PRODUCT_SUBFAMILY) AS LOCAL_PRODUCT_SUBFAMILY,
                   MAX(SUPPLY.PRODUCTION_LINE)         AS PRODUCTION_LINE,
                   MAX(SUPPLY.PLANT_TYPE)              AS PLANT_TYPE,
                   MAX(SUPPLY.REPL_STRATEGY)           AS REPL_STRATEGY,
                   AVG(SUPPLY.UNIT_COST)               AS UNIT_COST,
                   AVG(SUPPLY.AVG_SELLING_PRICE_RMB)       AS AVG_SELLING_PRICE_RMB
              FROM DEMAND_SUPPLY_SUPPLY_V SUPPLY
              LEFT JOIN (SELECT MOQ_TO_CUSTOMER,
                        MINIMUM_LOT_SIZE,
                        RV_TO_CUSTOMER,
                        AVG_MONTHLY_ORDER_INTAKE,
                        AVG_MONTHLY_SALES,
                        PURCHASING_GROUP,
                        SS3,
                        SAFETY_STOCK,
                        AMF,
                        AMU,
                        AMF_ONEMM,
                        AMU_ONEMM,
                        MATERIAL,
                        PLANT_CODE
                        FROM MATERIAL_MASTER_V) MMV ON MMV.MATERIAL = SUPPLY.MATERIAL AND MMV.PLANT_CODE = SUPPLY.PLANT_CODE
              LEFT JOIN (SELECT * FROM MM3_TRIGGER_LIST_V WHERE VALIDATE_TO > trunc(SYSDATE,'DD')) QML ON SUPPLY.MATERIAL = QML.MATERIAL
             GROUP BY SUPPLY.PURCH_ORDER_NUMBER, SUPPLY.PURCH_ORDER_ITEM
        ), PO_COMMIT AS (
            SELECT SUPPLY.MATERIAL,
                   SUPPLY.PLANT_CODE,
                   T.PURCH_ORDER_NUMBER,
                   T.PURCH_ORDER_ITEM,
                   T.BY_DAY,
                   T.BY_WEEK,
                   T.BY_MONTH,
                   SUPPLY.CONFIRM_CAT,
                   SUPPLY.GROUP_MATERIAL                         AS GROUP_MATERIAL,
                   t.QTY                                         AS QUANTITY,
                   'Supply_PO'                                   AS SUPPLY_CATEGORY,
                   NVL(SUPPLY.MATERIAL_CATEGORY, 'Others')       as MATERIAL_CATEGORY,
                   SUPPLY.VENDOR_CODE                            as VENDOR_CODE,
                   'Others'                                      as VENDOR_SEARCH_NAME,
                   'Others'                                      AS VENDOR_NAME,
                   SUPPLY.LEAD_TIME,
                   NVL(SUPPLY.LT_RANGE, 'Others')                as LT_RANGE,
                   NVL(SUPPLY.MATERIAL_ST_PLANT, 'Others')       as MATERIAL_ST_PLANT,
                   NVL(SUPPLY.ABC, 'Others')                 as ABC,
                   NVL(SUPPLY.MOQ_TO_CUSTOMER, 0)         as MOQ_TO_CUSTOMER,
                   NVL(SUPPLY.MINIMUM_LOT_SIZE, 0)        as MINIMUM_LOT_SIZE,
                   NVL(SUPPLY.RV_TO_CUSTOMER, 0)          as RV_TO_CUSTOMER,
                   NVL(SUPPLY.AVG_MONTHLY_ORDER_INTAKE, 0)          as AVG_MONTHLY_ORDER_INTAKE,
                   NVL(SUPPLY.AVG_MONTHLY_SALES, 0)          as AVG_MONTHLY_SALES,
                   NVL(SUPPLY.PURCHASING_GROUP, 'Others')        as PURCHASING_GROUP,
                   NVL(SUPPLY.SS3, 0)                     as SS3,
                   NVL(SUPPLY.AMF, 0)                     as AMF,
                   NVL(SUPPLY.AMU, 0)                     as AMU,
                   NVL(SUPPLY.SAFETY_STOCK, 0)            as SAFETY_STOCK,
                   NVL(SUPPLY.AMF_ONEMM, 0)               as AMF_ONEMM,
                   NVL(SUPPLY.AMU_ONEMM, 0)               as AMU_ONEMM,
                   SUPPLY.TRIGGER_BTN_DATE,
                   SUPPLY.TRIGGER_DELAY_DAYS_CD,
                   NVL(SUPPLY.CALCULATED_ABC, 'Others')          AS CALCULATED_ABC,
                   NVL(SUPPLY.CALCULATED_FMR, 'Others')          AS CALCULATED_FMR,
                   NVL(SUPPLY.PROCUREMENT_TYPE, 'Others')        as PROCUREMENT_TYPE,
                   NVL(SUPPLY.ACTIVENESS, 'Others')              as ACTIVENESS,
                   NVL(SUPPLY.STOCKING_POLICY, 'Others')         AS STOCKING_POLICY,
                   NVL(SUPPLY.COUNTRY, 'Others')                 as COUNTRY,
                   nvl(SUPPLY.MATERIAL_OWNER_NAME, 'Others')     as MATERIAL_OWNER_NAME,
                   nvl(SUPPLY.MATERIAL_OWNER_SESA, 'Others')     as MATERIAL_OWNER_SESA,
                   nvl(SUPPLY.mrp_controller, 'Others')          as mrp_controller,
                   nvl(SUPPLY.ROUNDING_VALUE, 0)                 as ROUNDING_VALUE,
                   nvl(SUPPLY.product_line, 'Others')            as product_line,
                   nvl(SUPPLY.ENTITY, 'Others')                  as entity,
                   nvl(SUPPLY.cluster_name, 'Others')            as cluster_name,
                   nvl(SUPPLY.bu, 'Others')                      as bu,
                   nvl(SUPPLY.LOCAL_BU, 'Others')                as local_bu,
                   nvl(SUPPLY.local_product_family, 'Others')    as local_product_family,
                   nvl(SUPPLY.local_product_line, 'Others')      as local_product_line,
                   nvl(SUPPLY.local_product_subfamily, 'Others') as local_product_subfamily,
                   Nvl(SUPPLY.PRODUCTION_LINE, 'Others')         as PRODUCTION_LINE,
                   Nvl(SUPPLY.PLANT_TYPE, 'Others')              as PLANT_TYPE,
                   NVL(SUPPLY.REPL_STRATEGY, 'Others')           AS REPL_STRATEGY,
                   'Others'                                      AS ACC_CATEGORY,
                   'Others'                                      AS SOURCE_CATEGORY,
                   SUPPLY.UNIT_COST,
                   SUPPLY.AVG_SELLING_PRICE_RMB,
                   T.QTY AS MANUAL_QTY,
                   T.QTY * SUPPLY.UNIT_COST MANUAL_MVP,
                   T.QTY * SUPPLY.AVG_SELLING_PRICE_RMB MANUAL_VALUE
            FROM PO_BASE T
                     LEFT JOIN SUPPLY ON T.PURCH_ORDER_NUMBER = SUPPLY.PURCH_ORDER_NUMBER AND T.PURCH_ORDER_ITEM = SUPPLY.PURCH_ORDER_ITEM
            WHERE SUPPLY.${dateColumnName} IS NOT NULL
        )
        SELECT PLANT_CODE,
               MATERIAL,
               GROUP_MATERIAL,
               OPEN_PO_QTY,
               STOCK_ON_HAND,
               SAFETY_STOCK,
               AMF,
               AMU,
               MTD_ORDER_INTAKE,
               MTD_SALES,
               CATEGORY,
               MATERIAL_CATEGORY,
               LEAD_TIME,
               VENDOR_CODE,
               VENDOR_SEARCH_NAME,
               VENDOR_NAME,
               LT_RANGE,
               MATERIAL_ST_PLANT,
               ABC,
               MOQ_TO_CUSTOMER,
               MINIMUM_LOT_SIZE,
               RV_TO_CUSTOMER,
               AVG_MONTHLY_ORDER_INTAKE,
               AVG_MONTHLY_SALES,
               PURCHASING_GROUP,
               SS3,
               AMF_ONEMM,
               AMU_ONEMM,
               TRIGGER_BTN_DATE,
               TRIGGER_DELAY_DAYS_CD,
               CALCULATED_ABC,
               CALCULATED_FMR,
               PROCUREMENT_TYPE,
               ACTIVENESS,
               STOCKING_POLICY,
               COUNTRY,
               MATERIAL_OWNER_NAME,
               MATERIAL_OWNER_SESA,
               MRP_CONTROLLER,
               ROUNDING_VALUE,
               PRODUCT_LINE,
               ENTITY,
               CLUSTER_NAME,
               BU,
               GROSS_WEIGHT_IN_KG,
               TEMP_UNIT_COST AS UNIT_COST,
               LOCAL_BU,
               LOCAL_PRODUCT_FAMILY,
               LOCAL_PRODUCT_LINE,
               LOCAL_PRODUCT_SUBFAMILY,
               PRODUCTION_LINE,
               PLANT_TYPE,
               REPL_STRATEGY,
               SOURCE_CATEGORY,
               <foreach collection="dates" item="item" separator=",">
                    "'${item}'_QTY" AS "${item}",
                    "'${item}'_AB" AS "TIPS_${item}_AB",
                    "'${item}'_LA" AS "TIPS_${item}_LA",
                    "'${item}'_NON" AS "TIPS_${item}_NON",
                    "'${item}'_MANUAL_VALUE" AS "TIPS_${item}_MANUAL"
               </foreach>
        FROM (
            SELECT *
                FROM (
                    SELECT T.PLANT_CODE,
                           T.MATERIAL,
                           T.GROUP_MATERIAL,
                           NVL(T4.OPEN_PO_QTY ${valueColumnName}, 0) OPEN_PO_QTY,
                           NVL(T2.STOCK_ON_HAND ${valueColumnName}, 0) STOCK_ON_HAND,
                           NVL(MMV.SAFETY_STOCK ${valueColumnName}, 0) SAFETY_STOCK,
                           NVL(MMV.AMF ${valueColumnName}, 0) AMF,
                           NVL(MMV.AMU ${valueColumnName}, 0) AMU,
                           NVL(MTDOI.MTD_ORDER_INTAKE ${valueColumnName}, 0) MTD_ORDER_INTAKE,
                           NVL(MTDS.MTD_SALES ${valueColumnName}, 0) MTD_SALES,
                           DECODE(T.CONFIRM_CAT, 'FCST Commitment', 'Supply_' || T.DATA_LINE || '(FCST_Cmt)', 'Supply_' || T.DATA_LINE) CATEGORY,
                           T.MATERIAL_CATEGORY,
                           T.VENDOR_CODE,
                           T.VENDOR_SEARCH_NAME,
                           T.VENDOR_NAME,
                           T.LEAD_TIME,
                           T.LT_RANGE,
                           T.MATERIAL_ST_PLANT,
                           T.ABC,
                           MMV.MOQ_TO_CUSTOMER,
                           MMV.MINIMUM_LOT_SIZE,
                           MMV.RV_TO_CUSTOMER,
                           MMV.AVG_MONTHLY_ORDER_INTAKE,
                           MMV.AVG_MONTHLY_SALES,
                           MMV.PURCHASING_GROUP,
                           MMV.SS3,
                           MMV.AMF_ONEMM,
                           MMV.AMU_ONEMM,
                           QML.VALIDATE_TO AS TRIGGER_BTN_DATE,
                           QML.QMAX_LEAD_TIME AS TRIGGER_DELAY_DAYS_CD,
                           T.CALCULATED_ABC,
                           T.CALCULATED_FMR,
                           T.PROCUREMENT_TYPE,
                           T.ACTIVENESS,
                           T.STOCKING_POLICY,
                           T.COUNTRY,
                           T.MATERIAL_OWNER_NAME,
                           T.MATERIAL_OWNER_SESA,
                           T.MRP_CONTROLLER,
                           T.ROUNDING_VALUE,
                           T.PRODUCT_LINE,
                           T.ENTITY,
                           T.CLUSTER_NAME,
                           T.BU,
                           T.LOCAL_BU,
                           T.LOCAL_PRODUCT_FAMILY,
                           T.LOCAL_PRODUCT_LINE,
                           T.LOCAL_PRODUCT_SUBFAMILY,
                           T.PRODUCTION_LINE,
                           T.PLANT_TYPE,
                           T.REPL_STRATEGY,
                           T.SOURCE_CATEGORY,
                           DECODE(T.CONFIRM_CAT, 'FCST Commitment', T.QUANTITY * ${fcstRatio}, T.QUANTITY) QUANTITY,
                           T.UNIT_COST AS TEMP_UNIT_COST,
                           T.UNIT_COST,
                           T.GROSS_WEIGHT_IN_KG,
                           T.AVG_SELLING_PRICE_RMB,
                           T.${dateColumnName},
                           <choose>
                               <when test="ablaSuffix == '_QTY'.toString()">
                                   NVL(T.PO_AB, 0) AB,
                                   NVL(T.PO_LA, 0) LA,
                                   NVL(T.PO_NON_ABLA, 0) NON,
                               </when>
                               <when test="ablaSuffix == '_MVP'.toString()">
                                   NVL(T.PO_AB * T.UNIT_COST, 0) AB,
                                   NVL(T.PO_LA * T.UNIT_COST, 0) LA,
                                   NVL(T.PO_NON_ABLA * T.UNIT_COST, 0) NON,
                               </when>
                               <when test="ablaSuffix == '_VALUE'.toString()">
                                   NVL(T.PO_AB_VALUE, 0) AB,
                                   NVL(T.PO_LA_VALUE, 0) LA,
                                   NVL(T.PO_NON_ABLA_VALUE, 0) NON,
                               </when>
                           </choose>
                           0 MANUAL_VALUE
                    FROM DEMAND_SUPPLY_SUPPLY_V T
                             LEFT JOIN (select MATERIAL, PLANT_CODE, SUM(ORDER_QUANTITY) AS MTD_ORDER_INTAKE
                                            FROM DEMAND_ORDER_INTAKE_V
                                            where CALENDAR_MONTH = TO_CHAR(SYSDATE, 'YYYYMM')
                                            GROUP BY MATERIAL, PLANT_CODE) MTDOI ON T.MATERIAL = MTDOI.MATERIAL AND T.PLANT_CODE = MTDOI.PLANT_CODE
                             LEFT JOIN (select MATERIAL, PLANT_CODE, SUM(ORDER_QUANTITY) AS MTD_SALES
                                            from DEMAND_SALES_V
                                            where CALENDAR_MONTH = TO_CHAR(SYSDATE, 'YYYYMM')
                                            GROUP BY MATERIAL, PLANT_CODE) MTDS ON T.MATERIAL = MTDS.MATERIAL AND T.PLANT_CODE = MTDS.PLANT_CODE
                             LEFT JOIN DEMAND_SUPPLY_SOH_V T2 ON T.MATERIAL = T2.MATERIAL AND T.PLANT_CODE = T2.PLANT_CODE AND NVL(T.GROUP_MATERIAL, 'Others') = NVL(T2.GROUP_MATERIAL, 'Others')
                             LEFT JOIN (SELECT MOQ_TO_CUSTOMER,
                                        MINIMUM_LOT_SIZE,
                                        RV_TO_CUSTOMER,
                                        AVG_MONTHLY_ORDER_INTAKE,
                                        AVG_MONTHLY_SALES,
                                        PURCHASING_GROUP,
                                        SS3,
                                        SAFETY_STOCK,
                                        AMF,
                                        AMU,
                                        AMF_ONEMM,
                                        AMU_ONEMM,
                                        MATERIAL,
                                        PLANT_CODE
                                        FROM MATERIAL_MASTER_V) MMV ON MMV.MATERIAL = T.MATERIAL AND MMV.PLANT_CODE = T.PLANT_CODE
                             LEFT JOIN (SELECT * FROM MM3_TRIGGER_LIST_V WHERE VALIDATE_TO > trunc(SYSDATE,'DD')) QML ON T.MATERIAL = QML.MATERIAL
                             INNER JOIN DEMAND_SUPPLY_CRITICAL_MATERIAL_V T3 ON T.MATERIAL = T3.MATERIAL AND T.PLANT_CODE = T3.PLANT_CODE AND T3.USER_ID = #{session.userid,jdbcType=VARCHAR}
                             LEFT JOIN (SELECT MATERIAL, PLANT_CODE, SUM(PO_AB + PO_LA + PO_NON_ABLA) AS OPEN_PO_QTY
                                        FROM OPEN_PO_STRUCTURE_V
                                        GROUP BY MATERIAL, PLANT_CODE) T4 ON T.MATERIAL = T4.MATERIAL AND T.PLANT_CODE = T4.PLANT_CODE
                    WHERE T.${dateColumnName} IS NOT NULL
                          AND NOT EXISTS (SELECT 1 FROM MA_COMMIT MA WHERE T.MATERIAL = MA.MATERIAL AND T.PLANT_CODE = MA.PLANT_CODE)
                          AND NOT EXISTS (SELECT 1 FROM PO_COMMIT PO WHERE T.PURCH_ORDER_NUMBER = PO.PURCH_ORDER_NUMBER AND T.PURCH_ORDER_ITEM = PO.PURCH_ORDER_ITEM)
                    <include refid="filter"/>
                    <include refid="supplyFilter"/>

                    UNION ALL

                    SELECT T.PLANT_CODE,
                           T.MATERIAL,
                           T.GROUP_MATERIAL,
                           NVL(T4.OPEN_PO_QTY ${valueColumnName}, 0) OPEN_PO_QTY,
                           NVL(T2.STOCK_ON_HAND ${valueColumnName}, 0) STOCK_ON_HAND,
                           NVL(T.SAFETY_STOCK ${valueColumnName}, 0) SAFETY_STOCK,
                           NVL(T.AMF ${valueColumnName}, 0) AMF,
                           NVL(T.AMU ${valueColumnName}, 0) AMU,
                           NVL(MTDOI.MTD_ORDER_INTAKE ${valueColumnName}, 0) MTD_ORDER_INTAKE,
                           NVL(MTDS.MTD_SALES ${valueColumnName}, 0) MTD_SALES,
                           'Supply_Manual' CATEGORY,
                           T.MATERIAL_CATEGORY,
                           T.VENDOR_CODE,
                           T.VENDOR_SEARCH_NAME,
                           T.VENDOR_NAME,
                           NULL AS LEAD_TIME,
                           T.LT_RANGE,
                           T.MATERIAL_ST_PLANT,
                           T.ABC,
                           T.MOQ_TO_CUSTOMER,
                           T.MINIMUM_LOT_SIZE,
                           T.RV_TO_CUSTOMER,
                           T.AVG_MONTHLY_ORDER_INTAKE,
                           T.AVG_MONTHLY_SALES,
                           T.PURCHASING_GROUP,
                           T.SS3,
                           T.AMF_ONEMM,
                           T.AMU_ONEMM,
                           T.TRIGGER_BTN_DATE,
                           T.TRIGGER_DELAY_DAYS_CD,
                           T.CALCULATED_ABC,
                           T.CALCULATED_FMR,
                           T.PROCUREMENT_TYPE,
                           T.ACTIVENESS,
                           T.STOCKING_POLICY,
                           T.COUNTRY,
                           T.MATERIAL_OWNER_NAME,
                           T.MATERIAL_OWNER_SESA,
                           T.MRP_CONTROLLER,
                           T.ROUNDING_VALUE,
                           T.PRODUCT_LINE,
                           T.ENTITY,
                           T.CLUSTER_NAME,
                           T.BU,
                           T.LOCAL_BU,
                           T.LOCAL_PRODUCT_FAMILY,
                           T.LOCAL_PRODUCT_LINE,
                           T.LOCAL_PRODUCT_SUBFAMILY,
                           T.PRODUCTION_LINE,
                           T.PLANT_TYPE,
                           T.REPL_STRATEGY,
                           T.SOURCE_CATEGORY,
                           DECODE(T.CONFIRM_CAT, 'FCST Commitment', T.QUANTITY * ${fcstRatio}, T.QUANTITY) QUANTITY,
                           T.UNIT_COST AS TEMP_UNIT_COST,
                           T.UNIT_COST,
                           0 GROSS_WEIGHT_IN_KG,
                           T.AVG_SELLING_PRICE_RMB,
                           T.${dateColumnName},
                           0 AB,
                           0 LA,
                           0 NON,
                           NVL(T.MANUAL${ablaSuffix}, 0) MANUAL_VALUE
                    FROM MA_COMMIT T
                             LEFT JOIN (select MATERIAL, PLANT_CODE, SUM(ORDER_QUANTITY) AS MTD_ORDER_INTAKE
                                        FROM DEMAND_ORDER_INTAKE_V
                                        where CALENDAR_MONTH = TO_CHAR(SYSDATE, 'YYYYMM')
                                        GROUP BY MATERIAL, PLANT_CODE) MTDOI ON T.MATERIAL = MTDOI.MATERIAL AND T.PLANT_CODE = MTDOI.PLANT_CODE
                             LEFT JOIN (select MATERIAL, PLANT_CODE, SUM(ORDER_QUANTITY) AS MTD_SALES
                                        from DEMAND_SALES_V
                                        where CALENDAR_MONTH = TO_CHAR(SYSDATE, 'YYYYMM')
                                        GROUP BY MATERIAL, PLANT_CODE) MTDS ON T.MATERIAL = MTDS.MATERIAL AND T.PLANT_CODE = MTDS.PLANT_CODE
                             LEFT JOIN DEMAND_SUPPLY_SOH_V T2 ON T.MATERIAL = T2.MATERIAL AND T.PLANT_CODE = T2.PLANT_CODE AND NVL(T.GROUP_MATERIAL, 'Others') = NVL(T2.GROUP_MATERIAL, 'Others')
                             INNER JOIN DEMAND_SUPPLY_CRITICAL_MATERIAL_V T3 ON T.MATERIAL = T3.MATERIAL AND T.PLANT_CODE = T3.PLANT_CODE AND T3.USER_ID = #{session.userid,jdbcType=VARCHAR}
                             LEFT JOIN (SELECT MATERIAL, PLANT_CODE, SUM(PO_AB + PO_LA + PO_NON_ABLA) AS OPEN_PO_QTY
                                        FROM OPEN_PO_STRUCTURE_V
                                        GROUP BY MATERIAL, PLANT_CODE) T4 ON T.MATERIAL = T4.MATERIAL AND T.PLANT_CODE = T4.PLANT_CODE
                    WHERE T.${dateColumnName} IS NOT NULL
                    <include refid="filter"/>
                    <include refid="supplyFilter"/>

                    UNION ALL

                    SELECT T.PLANT_CODE,
                           T.MATERIAL,
                           T.GROUP_MATERIAL,
                           NVL(T4.OPEN_PO_QTY ${valueColumnName}, 0) OPEN_PO_QTY,
                           NVL(T2.STOCK_ON_HAND ${valueColumnName}, 0) STOCK_ON_HAND,
                           NVL(T.SAFETY_STOCK ${valueColumnName}, 0) SAFETY_STOCK,
                           NVL(T.AMF ${valueColumnName}, 0) AMF,
                           NVL(T.AMU ${valueColumnName}, 0) AMU,
                           NVL(MTDOI.MTD_ORDER_INTAKE ${valueColumnName}, 0) MTD_ORDER_INTAK,
                           NVL(MTDS.MTD_SALES ${valueColumnName}, 0) MTD_SALES,
                           'Supply_Manual' CATEGORY,
                           T.MATERIAL_CATEGORY,
                           T.VENDOR_CODE,
                           T.VENDOR_SEARCH_NAME,
                           T.VENDOR_NAME,
                           T.LEAD_TIME,
                           T.LT_RANGE,
                           T.MATERIAL_ST_PLANT,
                           T.ABC,
                           T.MOQ_TO_CUSTOMER,
                           T.MINIMUM_LOT_SIZE,
                           T.RV_TO_CUSTOMER,
                           T.AVG_MONTHLY_ORDER_INTAKE,
                           T.AVG_MONTHLY_SALES,
                           T.PURCHASING_GROUP,
                           T.SS3,
                           T.AMF_ONEMM,
                           T.AMU_ONEMM,
                           T.TRIGGER_BTN_DATE,
                           T.TRIGGER_DELAY_DAYS_CD,
                           T.CALCULATED_ABC,
                           T.CALCULATED_FMR,
                           T.PROCUREMENT_TYPE,
                           T.ACTIVENESS,
                           T.STOCKING_POLICY,
                           T.COUNTRY,
                           T.MATERIAL_OWNER_NAME,
                           T.MATERIAL_OWNER_SESA,
                           T.MRP_CONTROLLER,
                           T.ROUNDING_VALUE,
                           T.PRODUCT_LINE,
                           T.ENTITY,
                           T.CLUSTER_NAME,
                           T.BU,
                           T.LOCAL_BU,
                           T.LOCAL_PRODUCT_FAMILY,
                           T.LOCAL_PRODUCT_LINE,
                           T.LOCAL_PRODUCT_SUBFAMILY,
                           T.PRODUCTION_LINE,
                           T.PLANT_TYPE,
                           T.REPL_STRATEGY,
                           T.SOURCE_CATEGORY,
                           DECODE(T.CONFIRM_CAT, 'FCST Commitment', T.QUANTITY * ${fcstRatio}, T.QUANTITY) QUANTITY,
                           T.UNIT_COST AS TEMP_UNIT_COST,
                           T.UNIT_COST,
                           0 GROSS_WEIGHT_IN_KG,
                           T.AVG_SELLING_PRICE_RMB,
                           T.${dateColumnName},
                           0 AB,
                           0 LA,
                           0 NON,
                           NVL(T.MANUAL${ablaSuffix}, 0) MANUAL_VALUE
                    FROM PO_COMMIT T
                             LEFT JOIN (select MATERIAL, PLANT_CODE, SUM(ORDER_QUANTITY) AS MTD_ORDER_INTAKE
                                        FROM DEMAND_ORDER_INTAKE_V
                                        where CALENDAR_MONTH = TO_CHAR(SYSDATE, 'YYYYMM')
                                        GROUP BY MATERIAL, PLANT_CODE) MTDOI ON T.MATERIAL = MTDOI.MATERIAL AND T.PLANT_CODE = MTDOI.PLANT_CODE
                             LEFT JOIN (select MATERIAL, PLANT_CODE, SUM(ORDER_QUANTITY) AS MTD_SALES
                                        from DEMAND_SALES_V
                                        where CALENDAR_MONTH = TO_CHAR(SYSDATE, 'YYYYMM')
                                        GROUP BY MATERIAL, PLANT_CODE) MTDS ON T.MATERIAL = MTDS.MATERIAL AND T.PLANT_CODE = MTDS.PLANT_CODE
                             LEFT JOIN DEMAND_SUPPLY_SOH_V T2 ON T.MATERIAL = T2.MATERIAL AND T.PLANT_CODE = T2.PLANT_CODE AND NVL(T.GROUP_MATERIAL, 'Others') = NVL(T2.GROUP_MATERIAL, 'Others')
                             INNER JOIN DEMAND_SUPPLY_CRITICAL_MATERIAL_V T3 ON T.MATERIAL = T3.MATERIAL AND T.PLANT_CODE = T3.PLANT_CODE AND T3.USER_ID = #{session.userid,jdbcType=VARCHAR}
                             LEFT JOIN (SELECT MATERIAL, PLANT_CODE, SUM(PO_AB + PO_LA + PO_NON_ABLA) AS OPEN_PO_QTY
                                        FROM OPEN_PO_STRUCTURE_V
                                        GROUP BY MATERIAL, PLANT_CODE) T4 ON T.MATERIAL = T4.MATERIAL AND T.PLANT_CODE = T4.PLANT_CODE
                             LEFT JOIN (SELECT MOQ_TO_CUSTOMER,
                                               MINIMUM_LOT_SIZE,
                                               RV_TO_CUSTOMER,
                                               AVG_MONTHLY_ORDER_INTAKE,
                                               AVG_MONTHLY_SALES,
                                               PURCHASING_GROUP,
                                               SS3,
                                               SAFETY_STOCK,
                                               AMF,
                                               AMU,
                                               AMF_ONEMM,
                                               AMU_ONEMM,
                                               MATERIAL,
                                               PLANT_CODE
                                               FROM MATERIAL_MASTER_V) MMV ON T.MATERIAL = MMV.MATERIAL AND T.PLANT_CODE = MMV.PLANT_CODE
                    WHERE T.${dateColumnName} IS NOT NULL
                    <include refid="filter"/>
                    <include refid="supplyFilter"/>
                ) PIVOT (
                    SUM(QUANTITY${valueColumnName}) QTY,
                    SUM(AB${valueColumnName}) AB,
                    SUM(LA${valueColumnName}) LA,
                    SUM(NON${valueColumnName}) NON,
                    SUM(MANUAL_VALUE) MANUAL_VALUE
                    FOR ${dateColumnName} IN
                        (
                        <foreach collection="dates" item="item" separator=",">
                            '${item}'
                        </foreach>
                        )
                    )
            ) NN
    </select>

    <sql id="queryReport1DetailSQL">
        <choose>
            <when test="category == 'demand'.toString()">
                SELECT T.${dateColumnName} "DATE",
                       T.MATERIAL,
                       T.PLANT_CODE,
                       T.GROUP_MATERIAL,
                       T.DEMAND_CATEGORY,
                       T.QUANTITY,
                       T.ORDER_NUMBER,
                       T.ORDER_ITEM,
                       T.OSO_NORMAL,
                       T.OSO_CB,
                       T.UD_CB,
                       T.UD_MID_AGING,
                       T.UD_LONG_AGING,
                       T.UD_NORMAL,
                       T.TRANSFER_PO_AT,
                       T.TRANSFER_PO_Q,
                       T.TRANSFER_PO_Z,
                       T.TRANSFER_PO_E,
                       T.TRANSFER_PO_OTHERS,
                       T.MATERIAL_CATEGORY,
                       T.VENDOR_CODE,
                       T.VENDOR_SEARCH_NAME,
                       T.VENDOR_NAME,
                       T.LEAD_TIME,
                       T.LT_RANGE,
                       T.MATERIAL_ST_PLANT,
                       T.ABC,
                       MMV.MOQ_TO_CUSTOMER,
                       MMV.MINIMUM_LOT_SIZE,
                       MMV.RV_TO_CUSTOMER,
                       MMV.AVG_MONTHLY_ORDER_INTAKE,
                       MMV.AVG_MONTHLY_SALES,
                       MMV.PURCHASING_GROUP,
                       MMV.SS3,
                       MMV.AMU,
                       MMV.AMF,
                       MMV.SAFETY_STOCK,
                       MMV.AMF_ONEMM,
                       MMV.AMU_ONEMM,
                       QML.VALIDATE_TO AS TRIGGER_BTN_DATE,
                       QML.QMAX_LEAD_TIME AS TRIGGER_DELAY_DAYS_CD,
                       T.CALCULATED_ABC,
                       T.CALCULATED_FMR,
                       T.PROCUREMENT_TYPE,
                       T.ACTIVENESS,
                       T.STOCKING_POLICY,
                       T.COUNTRY,
                       T.MATERIAL_OWNER_NAME,
                       T.MATERIAL_OWNER_SESA,
                       T.MRP_CONTROLLER,
                       T.ROUNDING_VALUE,
                       T.PRODUCT_LINE,
                       T.ENTITY,
                       T.CLUSTER_NAME,
                       T.BU,
                       T.LOCAL_BU,
                       T.LOCAL_PRODUCT_FAMILY,
                       T.LOCAL_PRODUCT_LINE,
                       T.LOCAL_PRODUCT_SUBFAMILY,
                       T.PRODUCTION_LINE,
                       T.PLANT_TYPE,
                       T.REPL_STRATEGY,
                       T.SOURCE_CATEGORY,
                       T.UNIT_COST,
                       T.AVG_SELLING_PRICE_RMB,
                       T.GROSS_WEIGHT_IN_KG
                  FROM DEMAND_SUPPLY_DEMAND_V T
                       LEFT JOIN (SELECT MOQ_TO_CUSTOMER,
                                MINIMUM_LOT_SIZE,
                                RV_TO_CUSTOMER,
                                AVG_MONTHLY_ORDER_INTAKE,
                                AVG_MONTHLY_SALES,
                                PURCHASING_GROUP,
                                SS3,
                                AMU,
                                AMF,
                                SAFETY_STOCK,
                                AMF_ONEMM,
                                AMU_ONEMM,
                                MATERIAL,
                                PLANT_CODE
                                FROM MATERIAL_MASTER_V) MMV ON MMV.MATERIAL = T.MATERIAL AND MMV.PLANT_CODE = T.PLANT_CODE
                       LEFT JOIN (SELECT * FROM MM3_TRIGGER_LIST_V WHERE VALIDATE_TO > trunc(SYSDATE,'DD')) QML ON T.MATERIAL = QML.MATERIAL
                       LEFT JOIN DEMAND_SUPPLY_SOH_V T2 ON T.MATERIAL = T2.MATERIAL AND T.PLANT_CODE = T2.PLANT_CODE AND NVL(T.GROUP_MATERIAL, 'Others') = NVL(T2.GROUP_MATERIAL, 'Others')
                       INNER JOIN DEMAND_SUPPLY_CRITICAL_MATERIAL_V T3 ON T.MATERIAL = T3.MATERIAL AND T.PLANT_CODE = T3.PLANT_CODE AND T3.USER_ID = #{session.userid,jdbcType=VARCHAR}
                 WHERE T.${dateColumnName} = #{selected.report1Date, jdbcType=VARCHAR}
                 <if test="selected.report1Material != null and selected.report1Material != ''.toString">
                     AND T.MATERIAL = #{selected.report1Material, jdbcType=VARCHAR}
                 </if>
                 <if test="selected.report1GroupMaterial != null and selected.report1GroupMaterial != ''.toString">
                     AND T.GROUP_MATERIAL = #{selected.report1GroupMaterial, jdbcType=VARCHAR}
                 </if>
                 <if test="selected.report1Plant != null and selected.report1Plant != ''.toString()">
                     AND T.PLANT_CODE = #{selected.report1Plant, jdbcType=VARCHAR}
                 </if>
                 <choose>
                     <when test="selected.report1Category == 'Open Sales Order'.toString()">
                         AND T.DEMAND_CATEGORY = 'Demand_SO'
                     </when>
                     <when test="selected.report1Category == 'Order Reservation'.toString()">
                         AND T.DEMAND_CATEGORY = 'Demand_MO'
                     </when>
                     <when test="selected.report1Category == 'Indepedent Requirement'.toString()">
                         AND T.DEMAND_CATEGORY = 'Ind_FCST'
                     </when>
                     <when test="selected.report1Category == 'Depedent Requirement'.toString()">
                         AND T.DEMAND_CATEGORY = 'Dep_FCST'
                     </when>
                     <when test="selected.report1Category == 'Transfer PO'.toString()">
                         AND T.DEMAND_CATEGORY = 'Transfer_PO'
                     </when>
                 </choose>
                 <include refid="filter"/>
                 <include refid="demandFilter"/>
            </when>
            <otherwise>
                WITH MA_BASE AS (
                    SELECT MATERIAL, PLANT_CODE, SUM(QTY) QTY, COMMIT_DATE
                          FROM DEMAND_SUPPLY_MANUAL_PO_COMMIT
                         WHERE (PURCH_ORDER_NUMBER IS NULL OR PURCH_ORDER_ITEM IS NULL)
                           AND MATERIAL IS NOT NULL
                           AND PLANT_CODE IS NOT NULL
                           AND COMMIT_DATE IS NOT NULL
                           AND QTY IS NOT NULL
                           AND USER_ID = #{session.userid,jdbcType=VARCHAR}
                         GROUP BY MATERIAL, PLANT_CODE, COMMIT_DATE
                ), MA_COMMIT AS (
                    SELECT T.MATERIAL,
                           T.PLANT_CODE,
                           'MANUAL'                                   AS CONFIRM_CAT,
                           GM.GROUP_MATERIAL                          AS GROUP_MATERIAL,
                           T.QTY                                      AS QUANTITY,
                           'Supply_PO'                                AS SUPPLY_CATEGORY,
                           CASE WHEN CALENDAR.DATE$ &lt; TRUNC(SYSDATE, 'DD')
                                THEN 'PAST_DUE'
                                ELSE REPLACE(CALENDAR.TEXT, '/', '') END AS BY_DAY,
                           CASE WHEN CALENDAR.DATE$ &lt; TRUNC(SYSDATE, 'DD')
                                THEN 'PAST_DUE'
                                ELSE CALENDAR.YEAR || 'W' || CALENDAR.WEEK_NO END AS BY_WEEK,
                           CASE WHEN CALENDAR.DATE$ &lt; TRUNC(SYSDATE, 'DD')
                                THEN 'PAST_DUE'
                                ELSE CALENDAR.YEAR || CALENDAR.MONTH END AS BY_MONTH,
                           NVL(MAT.MATERIAL_CATEGORY, 'Others')       AS MATERIAL_CATEGORY,
                           MAT.VENDOR_CODE                            AS VENDOR_CODE,
                           'MANUAL'                                   AS VENDOR_SEARCH_NAME,
                           'MANUAL'                                   AS VENDOR_NAME,
                           NVL(MAT.LT_RANGE, 'Others')                as LT_RANGE,
                           NVL(MAT.MATERIAL_ST_PLANT, 'Others')       as MATERIAL_ST_PLANT,
                           NVL(MAT.ABC, 'Others')                 as ABC,
                           NVL(MAT.MOQ_TO_CUSTOMER, 0)                as MOQ_TO_CUSTOMER,
                           NVL(MAT.MINIMUM_LOT_SIZE, 0)               as MINIMUM_LOT_SIZE,
                           NVL(MAT.RV_TO_CUSTOMER, 0)                 as RV_TO_CUSTOMER,
                           NVL(MAT.AVG_MONTHLY_ORDER_INTAKE, 0)                 as AVG_MONTHLY_ORDER_INTAKE,
                           NVL(MAT.AVG_MONTHLY_SALES, 0)                 as AVG_MONTHLY_SALES,
                           NVL(MAT.PURCHASING_GROUP, 'Others')        as PURCHASING_GROUP,
                           NVL(MAT.SS3, 0)                            as SS3,
                           NVL(MAT.SAFETY_STOCK, 0)                   as SAFETY_STOCK,
                           NVL(MAT.AMU, 0)                            as AMU,
                           NVL(MAT.AMF, 0)                            as AMF,
                           NVL(MAT.AMF_ONEMM, 0)                      as AMF_ONEMM,
                           NVL(MAT.AMU_ONEMM, 0)                      as AMU_ONEMM,
                           QML.VALIDATE_TO                            as TRIGGER_BTN_DATE,
                           QML.QMAX_LEAD_TIME                         as TRIGGER_DELAY_DAYS_CD,
                           NVL(MAT.CALCULATED_ABC, 'Others')          AS CALCULATED_ABC,
                           NVL(MAT.CALCULATED_FMR, 'Others')          AS CALCULATED_FMR,
                           NVL(MAT.PROCUREMENT_TYPE, 'Others')        as PROCUREMENT_TYPE,
                           NVL(MAT.ACTIVENESS, 'Others')              as ACTIVENESS,
                           NVL(MAT.STOCKING_POLICY, 'Others')         AS STOCKING_POLICY,
                           NVL(MAT.COUNTRY_CODE, 'Others')            as COUNTRY,
                           nvl(MAT.mrp_controller, 'Others')          as MRP_CONTROLLER,
                           nvl(MAT.ROUNDING_VALUE, 0)                 as ROUNDING_VALUE,
                           nvl(MAT.product_line, 'Others')            as PRODUCT_LINE,
                           nvl(MAT.ENTITY, 'Others')                  as ENTITY,
                           nvl(MAT.cluster_name, 'Others')            as CLUSTER_NAME,
                           nvl(MAT.bu, 'Others')                      as BU,
                           nvl(MAT.LOCAL_BU, 'Others')                as LOCAL_BU,
                           nvl(MAT.local_product_family, 'Others')    as LOCAL_PRODUCT_FAMILY,
                           nvl(MAT.local_product_line, 'Others')      as LOCAL_PRODUCT_LINE,
                           nvl(MAT.local_product_subfamily, 'Others') as LOCAL_PRODUCT_SUBFAMILY,
                           Nvl(MAT.PRODUCTION_LINE, 'Others')         as PRODUCTION_LINE,
                           Nvl(MAT.PLANT_TYPE, 'Others')              as PLANT_TYPE,
                           NVL(MAT.REPL_STRATEGY, 'Others')           AS REPL_STRATEGY,
                           'Others'                                   AS ACC_CATEGORY,
                           'Others'                                   AS SOURCE_CATEGORY,
                           MAT.UNIT_COST,
                           MAT.AVG_SELLING_PRICE_RMB,
                           T.QTY AS MANUAL_QTY,
                           T.QTY * MAT.UNIT_COST MANUAL_MVP,
                           T.QTY * MAT.AVG_SELLING_PRICE_RMB MANUAL_VALUE,
                           MAT.MATERIAL_OWNER_NAME,
                           MAT.MATERIAL_OWNER_SESA,
                           MAT.GROSS_WEIGHT_IN_KG
                    FROM MA_BASE T
                             LEFT JOIN (SELECT * FROM MM3_TRIGGER_LIST_V WHERE VALIDATE_TO > trunc(SYSDATE,'DD')) QML ON T.MATERIAL = QML.MATERIAL
                             LEFT JOIN MATERIAL_MASTER_V MAT ON T.MATERIAL = MAT.MATERIAL AND T.PLANT_CODE = MAT.PLANT_CODE
                             LEFT JOIN SY_CALENDAR CALENDAR ON T.COMMIT_DATE = CALENDAR.TEXT AND CALENDAR.NAME = 'National Holidays'
                             LEFT JOIN DEMAND_SUPPLY_GROUP_MATERIAL_V GM ON T.MATERIAL = GM.MATERIAL AND T.PLANT_CODE = GM.PLANT_CODE
                ), PO_BASE AS (
                    SELECT PURCH_ORDER_NUMBER, PURCH_ORDER_ITEM, QTY, COMMIT_DATE,
                           CASE WHEN SC.DATE$ &lt; TRUNC(SYSDATE,'DD') THEN 'PAST_DUE' ELSE TO_CHAR(SC.DATE$, 'YYYYMMDD') END BY_DAY,
                           CASE WHEN SC.DATE$ &lt; TRUNC(SYSDATE,'DD') THEN 'PAST_DUE' ELSE SC.YEAR || 'W' || SC.WEEK_NO END BY_WEEK,
                           CASE WHEN SC.DATE$ &lt; TRUNC(SYSDATE,'DD') THEN 'PAST_DUE' ELSE SC.YEAR || SC.MONTH END BY_MONTH
                      FROM DEMAND_SUPPLY_MANUAL_PO_COMMIT T
                           LEFT JOIN SY_CALENDAR SC ON T.COMMIT_DATE = SC.TEXT AND SC.NAME = 'National Holidays'
                     WHERE PURCH_ORDER_NUMBER IS NOT NULL
                       AND PURCH_ORDER_ITEM IS NOT NULL
                       AND COMMIT_DATE IS NOT NULL
                       AND QTY IS NOT NULL
                       AND USER_ID = #{session.userid,jdbcType=VARCHAR}
                ), SUPPLY AS (
                    SELECT SUPPLY.PURCH_ORDER_NUMBER,
                           SUPPLY.PURCH_ORDER_ITEM,
                           MAX(SUPPLY.BY_DAY)                  AS BY_DAY,
                           MAX(SUPPLY.BY_WEEK)                 AS BY_WEEK,
                           MAX(SUPPLY.BY_MONTH)                AS BY_MONTH,
                           MAX(SUPPLY.MATERIAL)                AS MATERIAL,
                           MAX(SUPPLY.PLANT_CODE)              AS PLANT_CODE,
                           MAX(SUPPLY.CONFIRM_CAT)             AS CONFIRM_CAT,
                           MAX(SUPPLY.GROUP_MATERIAL)          AS GROUP_MATERIAL,
                           MAX(SUPPLY.MATERIAL_CATEGORY)       AS MATERIAL_CATEGORY,
                           MAX(SUPPLY.VENDOR_CODE)             AS VENDOR_CODE,
                           MAX(SUPPLY.LEAD_TIME)               AS LEAD_TIME,
                           MAX(SUPPLY.LT_RANGE)                AS LT_RANGE,
                           MAX(SUPPLY.MATERIAL_ST_PLANT)       AS MATERIAL_ST_PLANT,
                           MAX(SUPPLY.ABC)                 AS ABC,
                           MAX(MMV.MOQ_TO_CUSTOMER)            AS MOQ_TO_CUSTOMER,
                           MAX(MMV.MINIMUM_LOT_SIZE)           AS MINIMUM_LOT_SIZE,
                           MAX(MMV.RV_TO_CUSTOMER)             AS RV_TO_CUSTOMER,
                           MAX(MMV.AVG_MONTHLY_ORDER_INTAKE)             AS AVG_MONTHLY_ORDER_INTAKE,
                           MAX(MMV.AVG_MONTHLY_SALES)             AS AVG_MONTHLY_SALES,
                           MAX(MMV.PURCHASING_GROUP)           AS PURCHASING_GROUP,
                           MAX(MMV.SS3)                        AS SS3,
                           MAX(MMV.SAFETY_STOCK)               AS SAFETY_STOCK,
                           MAX(MMV.AMF)                        AS AMF,
                           MAX(MMV.AMU)                        AS AMU,
                           MAX(MMV.AMF_ONEMM)                  AS AMF_ONEMM,
                           MAX(MMV.AMU_ONEMM)                  AS AMU_ONEMM,
                           MAX(QML.VALIDATE_TO)                AS TRIGGER_BTN_DATE,
                           MAX(QML.QMAX_LEAD_TIME)             AS TRIGGER_DELAY_DAYS_CD,
                           MAX(SUPPLY.CALCULATED_ABC)          AS CALCULATED_ABC,
                           MAX(SUPPLY.CALCULATED_FMR)          AS CALCULATED_FMR,
                           MAX(SUPPLY.PROCUREMENT_TYPE)        AS PROCUREMENT_TYPE,
                           MAX(SUPPLY.ACTIVENESS)              AS ACTIVENESS,
                           MAX(SUPPLY.STOCKING_POLICY)         AS STOCKING_POLICY,
                           MAX(SUPPLY.COUNTRY)                 AS COUNTRY,
                           MAX(SUPPLY.MATERIAL_OWNER_NAME)     AS MATERIAL_OWNER_NAME,
                           MAX(SUPPLY.MATERIAL_OWNER_SESA)     AS MATERIAL_OWNER_SESA,
                           MAX(SUPPLY.MRP_CONTROLLER)          AS MRP_CONTROLLER,
                           MAX(SUPPLY.ROUNDING_VALUE)          AS ROUNDING_VALUE,
                           MAX(SUPPLY.PRODUCT_LINE)            AS PRODUCT_LINE,
                           MAX(SUPPLY.ENTITY)                  AS ENTITY,
                           MAX(SUPPLY.CLUSTER_NAME)            AS CLUSTER_NAME,
                           MAX(SUPPLY.BU)                      AS BU,
                           MAX(SUPPLY.LOCAL_BU)                AS LOCAL_BU,
                           MAX(SUPPLY.LOCAL_PRODUCT_FAMILY)    AS LOCAL_PRODUCT_FAMILY,
                           MAX(SUPPLY.LOCAL_PRODUCT_LINE)      AS LOCAL_PRODUCT_LINE,
                           MAX(SUPPLY.LOCAL_PRODUCT_SUBFAMILY) AS LOCAL_PRODUCT_SUBFAMILY,
                           MAX(SUPPLY.PRODUCTION_LINE)         AS PRODUCTION_LINE,
                           MAX(SUPPLY.PLANT_TYPE)              AS PLANT_TYPE,
                           MAX(SUPPLY.REPL_STRATEGY)           AS REPL_STRATEGY,
                           AVG(SUPPLY.UNIT_COST)               AS UNIT_COST,
                           AVG(SUPPLY.AVG_SELLING_PRICE_RMB)       AS AVG_SELLING_PRICE_RMB,
                           MAX(SUPPLY.GROSS_WEIGHT_IN_KG)      AS GROSS_WEIGHT_IN_KG
                      FROM DEMAND_SUPPLY_SUPPLY_V SUPPLY
                        LEFT JOIN (SELECT MOQ_TO_CUSTOMER,
                                    MINIMUM_LOT_SIZE,
                                    RV_TO_CUSTOMER,
                                    AVG_MONTHLY_ORDER_INTAKE,
                                    AVG_MONTHLY_SALES,
                                    PURCHASING_GROUP,
                                    SS3,
                                    SAFETY_STOCK,
                                    AMU,
                                    AMF,
                                    AMF_ONEMM,
                                    AMU_ONEMM,
                                    MATERIAL,
                                    PLANT_CODE
                                    FROM MATERIAL_MASTER_V) MMV ON MMV.MATERIAL = SUPPLY.MATERIAL AND MMV.PLANT_CODE = SUPPLY.PLANT_CODE
                        LEFT JOIN (SELECT * FROM MM3_TRIGGER_LIST_V WHERE VALIDATE_TO > trunc(SYSDATE,'DD')) QML ON SUPPLY.MATERIAL = QML.MATERIAL
                     GROUP BY SUPPLY.PURCH_ORDER_NUMBER, SUPPLY.PURCH_ORDER_ITEM
                ), PO_COMMIT AS (
                    SELECT SUPPLY.MATERIAL,
                           SUPPLY.PLANT_CODE,
                           T.PURCH_ORDER_NUMBER,
                           T.PURCH_ORDER_ITEM,
                           T.BY_DAY,
                           T.BY_WEEK,
                           T.BY_MONTH,
                           SUPPLY.CONFIRM_CAT,
                           SUPPLY.GROUP_MATERIAL                         AS GROUP_MATERIAL,
                           t.QTY                                         AS QUANTITY,
                           'Supply_PO'                                   AS SUPPLY_CATEGORY,
                           NVL(SUPPLY.MATERIAL_CATEGORY, 'Others')       as MATERIAL_CATEGORY,
                           SUPPLY.VENDOR_CODE                            as VENDOR_CODE,
                           'Others'                                      as VENDOR_SEARCH_NAME,
                           'Others'                                      AS VENDOR_NAME,
                           SUPPLY.LEAD_TIME,
                           NVL(SUPPLY.LT_RANGE, 'Others')                as LT_RANGE,
                           NVL(SUPPLY.MATERIAL_ST_PLANT, 'Others')       as MATERIAL_ST_PLANT,
                           NVL(SUPPLY.ABC, 'Others')                 as ABC,
                           NVL(SUPPLY.MOQ_TO_CUSTOMER, 0)         as MOQ_TO_CUSTOMER,
                           NVL(SUPPLY.MINIMUM_LOT_SIZE, 0)        as MINIMUM_LOT_SIZE,
                           NVL(SUPPLY.RV_TO_CUSTOMER, 0)          as RV_TO_CUSTOMER,
                           NVL(SUPPLY.AVG_MONTHLY_ORDER_INTAKE, 0)          as AVG_MONTHLY_ORDER_INTAKE,
                           NVL(SUPPLY.AVG_MONTHLY_SALES, 0)          as AVG_MONTHLY_SALES,
                           NVL(SUPPLY.PURCHASING_GROUP, 'Others')        as PURCHASING_GROUP,
                           NVL(SUPPLY.SS3, 0)                     as SS3,
                           NVL(SUPPLY.SAFETY_STOCK, 0)                     as SAFETY_STOCK,
                           NVL(SUPPLY.AMU, 0)                     as AMU,
                           NVL(SUPPLY.AMF, 0)                     as AMF,
                           NVL(SUPPLY.AMF_ONEMM, 0)               as AMF_ONEMM,
                           NVL(SUPPLY.AMU_ONEMM, 0)               as AMU_ONEMM,
                           SUPPLY.TRIGGER_BTN_DATE,
                           SUPPLY.TRIGGER_DELAY_DAYS_CD,
                           NVL(SUPPLY.CALCULATED_ABC, 'Others')          AS CALCULATED_ABC,
                           NVL(SUPPLY.CALCULATED_FMR, 'Others')          AS CALCULATED_FMR,
                           NVL(SUPPLY.PROCUREMENT_TYPE, 'Others')        as PROCUREMENT_TYPE,
                           NVL(SUPPLY.ACTIVENESS, 'Others')              as ACTIVENESS,
                           NVL(SUPPLY.STOCKING_POLICY, 'Others')         AS STOCKING_POLICY,
                           NVL(SUPPLY.COUNTRY, 'Others')                 as COUNTRY,
                           nvl(SUPPLY.mrp_controller, 'Others')          as mrp_controller,
                           nvl(SUPPLY.ROUNDING_VALUE, 0)                 as ROUNDING_VALUE,
                           nvl(SUPPLY.product_line, 'Others')            as product_line,
                           nvl(SUPPLY.ENTITY, 'Others')                  as entity,
                           nvl(SUPPLY.cluster_name, 'Others')            as cluster_name,
                           nvl(SUPPLY.bu, 'Others')                      as bu,
                           nvl(SUPPLY.LOCAL_BU, 'Others')                as local_bu,
                           nvl(SUPPLY.local_product_family, 'Others')    as local_product_family,
                           nvl(SUPPLY.local_product_line, 'Others')      as local_product_line,
                           nvl(SUPPLY.local_product_subfamily, 'Others') as local_product_subfamily,
                           Nvl(SUPPLY.PRODUCTION_LINE, 'Others')         as PRODUCTION_LINE,
                           Nvl(SUPPLY.PLANT_TYPE, 'Others')              as PLANT_TYPE,
                           NVL(SUPPLY.REPL_STRATEGY, 'Others')           AS REPL_STRATEGY,
                           'Others'                                      AS ACC_CATEGORY,
                           'Others'                                      AS SOURCE_CATEGORY,
                           SUPPLY.UNIT_COST,
                           SUPPLY.AVG_SELLING_PRICE_RMB,
                           SUPPLY.MATERIAL_OWNER_NAME,
                           SUPPLY.MATERIAL_OWNER_SESA,
                           SUPPLY.GROSS_WEIGHT_IN_KG,
                           T.QTY AS MANUAL_QTY,
                           T.QTY * SUPPLY.UNIT_COST MANUAL_MVP,
                           T.QTY * SUPPLY.AVG_SELLING_PRICE_RMB MANUAL_VALUE
                    FROM PO_BASE T
                             LEFT JOIN SUPPLY ON T.PURCH_ORDER_NUMBER = SUPPLY.PURCH_ORDER_NUMBER AND T.PURCH_ORDER_ITEM = SUPPLY.PURCH_ORDER_ITEM
                             LEFT JOIN SY_CALENDAR CALENDAR ON T.COMMIT_DATE = CALENDAR.TEXT AND CALENDAR.NAME = 'National Holidays'
                    WHERE SUPPLY.${dateColumnName} IS NOT NULL
                )
                <choose>
                    <when test="selected.report1Category == 'Supply_Manual'.toString()">
                        SELECT T.${dateColumnName} "DATE",
                               T.PLANT_CODE,
                               T.MATERIAL,
                               T.GROUP_MATERIAL,
                               '' AS PURCH_ORDER_NUMBER,
                               '' AS PURCH_ORDER_ITEM,
                               NVL(T4.OPEN_PO_QTY, 0) OPEN_PO_QTY,
                               NVL(T2.STOCK_ON_HAND, 0) STOCK_ON_HAND,
                               NVL(T.SAFETY_STOCK, 0) SAFETY_STOCK,
                               NVL(T.AMF, 0) AMF,
                               NVL(T.AMU, 0) AMU,
                               NVL(MTDOI.MTD_ORDER_INTAKE, 0) MTD_ORDER_INTAKE,
                               NVL(MTDS.MTD_SALES, 0) MTD_SALES,
                               'Supply_Manual' CATEGORY,
                               T.MATERIAL_CATEGORY,
                               T.VENDOR_CODE,
                               T.VENDOR_SEARCH_NAME,
                               T.VENDOR_NAME,
                               NULL AS LEAD_TIME,
                               T.LT_RANGE,
                               T.MATERIAL_ST_PLANT,
                               T.ABC,
                               T.MOQ_TO_CUSTOMER,
                               T.MINIMUM_LOT_SIZE,
                               T.RV_TO_CUSTOMER,
                               T.AVG_MONTHLY_ORDER_INTAKE,
                               T.AVG_MONTHLY_SALES,
                               T.PURCHASING_GROUP,
                               T.SS3,
                               T.AMF_ONEMM,
                               T.AMU_ONEMM,
                               T.TRIGGER_BTN_DATE,
                               T.TRIGGER_DELAY_DAYS_CD,
                               T.CALCULATED_ABC,
                               T.CALCULATED_FMR,
                               T.PROCUREMENT_TYPE,
                               T.ACTIVENESS,
                               T.STOCKING_POLICY,
                               T.COUNTRY,
                               T.MRP_CONTROLLER,
                               T.ROUNDING_VALUE,
                               T.PRODUCT_LINE,
                               T.ENTITY,
                               T.CLUSTER_NAME,
                               T.BU,
                               T.LOCAL_BU,
                               T.LOCAL_PRODUCT_FAMILY,
                               T.LOCAL_PRODUCT_LINE,
                               T.LOCAL_PRODUCT_SUBFAMILY,
                               T.PRODUCTION_LINE,
                               T.PLANT_TYPE,
                               T.REPL_STRATEGY,
                               T.SOURCE_CATEGORY,
                               T.QUANTITY,
                               T.UNIT_COST,
                               T.AVG_SELLING_PRICE_RMB,
                               T.MATERIAL_OWNER_NAME,
                               T.MATERIAL_OWNER_SESA,
                               T.GROSS_WEIGHT_IN_KG,
                               NVL(T.MANUAL_QTY, 0) MANUAL_QTY,
                               NVL(T.MANUAL_MVP, 0) MANUAL_MVP,
                               NVL(T.MANUAL_VALUE, 0) MANUAL_VALUE
                        FROM MA_COMMIT T
                                 LEFT JOIN (select MATERIAL, PLANT_CODE, SUM(ORDER_QUANTITY) AS MTD_ORDER_INTAKE
                                            FROM DEMAND_ORDER_INTAKE_V
                                            where CALENDAR_MONTH = TO_CHAR(SYSDATE, 'YYYYMM')
                                            GROUP BY MATERIAL, PLANT_CODE) MTDOI ON T.MATERIAL = MTDOI.MATERIAL AND T.PLANT_CODE = MTDOI.PLANT_CODE
                                 LEFT JOIN (select MATERIAL, PLANT_CODE, SUM(ORDER_QUANTITY) AS MTD_SALES
                                            from DEMAND_SALES_V
                                            where CALENDAR_MONTH = TO_CHAR(SYSDATE, 'YYYYMM')
                                            GROUP BY MATERIAL, PLANT_CODE) MTDS ON T.MATERIAL = MTDS.MATERIAL AND T.PLANT_CODE = MTDS.PLANT_CODE
                                 LEFT JOIN DEMAND_SUPPLY_SOH_V T2 ON T.MATERIAL = T2.MATERIAL AND T.PLANT_CODE = T2.PLANT_CODE AND NVL(T.GROUP_MATERIAL, 'Others') = NVL(T2.GROUP_MATERIAL, 'Others')
                                 INNER JOIN DEMAND_SUPPLY_CRITICAL_MATERIAL_V T3 ON T.MATERIAL = T3.MATERIAL AND T.PLANT_CODE = T3.PLANT_CODE AND T3.USER_ID = #{session.userid,jdbcType=VARCHAR}
                                 LEFT JOIN (SELECT MATERIAL, PLANT_CODE, SUM(PO_AB + PO_LA + PO_NON_ABLA) AS OPEN_PO_QTY
                                            FROM OPEN_PO_STRUCTURE_V
                                            GROUP BY MATERIAL, PLANT_CODE) T4 ON T.MATERIAL = T4.MATERIAL AND T.PLANT_CODE = T4.PLANT_CODE
                        WHERE T.${dateColumnName} = #{selected.report1Date, jdbcType=VARCHAR}
                        <include refid="filter"/>
                        <include refid="supplyFilter"/>

                        UNION ALL

                        SELECT T.${dateColumnName} "DATE",
                               T.PLANT_CODE,
                               T.MATERIAL,
                               T.GROUP_MATERIAL,
                               T.PURCH_ORDER_NUMBER,
                               T.PURCH_ORDER_ITEM,
                               NVL(T4.OPEN_PO_QTY, 0) OPEN_PO_QTY,
                               NVL(T2.STOCK_ON_HAND, 0) STOCK_ON_HAND,
                               NVL(T.SAFETY_STOCK, 0) SAFETY_STOCK,
                               NVL(T.AMF, 0) AMF,
                               NVL(T.AMU, 0) AMU,
                               NVL(MTDOI.MTD_ORDER_INTAKE, 0) MTD_ORDER_INTAKE,
                               NVL(MTDS.MTD_SALES, 0) MTD_SALES,
                               'Supply_Manual' CATEGORY,
                               T.MATERIAL_CATEGORY,
                               T.VENDOR_CODE,
                               T.VENDOR_SEARCH_NAME,
                               T.VENDOR_NAME,
                               T.LEAD_TIME,
                               T.LT_RANGE,
                               T.MATERIAL_ST_PLANT,
                               T.ABC,
                               T.MOQ_TO_CUSTOMER,
                               T.MINIMUM_LOT_SIZE,
                               T.RV_TO_CUSTOMER,
                               T.AVG_MONTHLY_ORDER_INTAKE,
                               T.AVG_MONTHLY_SALES,
                               T.PURCHASING_GROUP,
                               T.SS3,
                               T.AMF_ONEMM,
                               T.AMU_ONEMM,
                               T.TRIGGER_BTN_DATE,
                               T.TRIGGER_DELAY_DAYS_CD,
                               T.CALCULATED_ABC,
                               T.CALCULATED_FMR,
                               T.PROCUREMENT_TYPE,
                               T.ACTIVENESS,
                               T.STOCKING_POLICY,
                               T.COUNTRY,
                               T.MRP_CONTROLLER,
                               T.ROUNDING_VALUE,
                               T.PRODUCT_LINE,
                               T.ENTITY,
                               T.CLUSTER_NAME,
                               T.BU,
                               T.LOCAL_BU,
                               T.LOCAL_PRODUCT_FAMILY,
                               T.LOCAL_PRODUCT_LINE,
                               T.LOCAL_PRODUCT_SUBFAMILY,
                               T.PRODUCTION_LINE,
                               T.PLANT_TYPE,
                               T.REPL_STRATEGY,
                               T.SOURCE_CATEGORY,
                               T.QUANTITY,
                               T.UNIT_COST,
                               T.AVG_SELLING_PRICE_RMB,
                               T.MATERIAL_OWNER_NAME,
                               T.MATERIAL_OWNER_SESA,
                               T.GROSS_WEIGHT_IN_KG,
                               NVL(T.MANUAL_QTY, 0) MANUAL_QTY,
                               NVL(T.MANUAL_MVP, 0) MANUAL_MVP,
                               NVL(T.MANUAL_VALUE, 0) MANUAL_VALUE
                        FROM PO_COMMIT T
                            LEFT JOIN (select MATERIAL, PLANT_CODE, SUM(ORDER_QUANTITY) AS MTD_ORDER_INTAKE
                                        FROM DEMAND_ORDER_INTAKE_V
                                        where CALENDAR_MONTH = TO_CHAR(SYSDATE, 'YYYYMM')
                                        GROUP BY MATERIAL, PLANT_CODE) MTDOI ON T.MATERIAL = MTDOI.MATERIAL AND T.PLANT_CODE = MTDOI.PLANT_CODE
                            LEFT JOIN (select MATERIAL, PLANT_CODE, SUM(ORDER_QUANTITY) AS MTD_SALES
                                        from DEMAND_SALES_V
                                        where CALENDAR_MONTH = TO_CHAR(SYSDATE, 'YYYYMM')
                                        GROUP BY MATERIAL, PLANT_CODE) MTDS ON T.MATERIAL = MTDS.MATERIAL AND T.PLANT_CODE = MTDS.PLANT_CODE
                            LEFT JOIN DEMAND_SUPPLY_SOH_V T2 ON T.MATERIAL = T2.MATERIAL AND T.PLANT_CODE = T2.PLANT_CODE AND NVL(T.GROUP_MATERIAL, 'Others') = NVL(T2.GROUP_MATERIAL, 'Others')
                                 INNER JOIN DEMAND_SUPPLY_CRITICAL_MATERIAL_V T3 ON T.MATERIAL = T3.MATERIAL AND T.PLANT_CODE = T3.PLANT_CODE AND T3.USER_ID = #{session.userid,jdbcType=VARCHAR}
                            LEFT JOIN (SELECT MATERIAL, PLANT_CODE, SUM(PO_AB + PO_LA + PO_NON_ABLA) AS OPEN_PO_QTY
                                        FROM OPEN_PO_STRUCTURE_V
                                        GROUP BY MATERIAL, PLANT_CODE) T4 ON T.MATERIAL = T4.MATERIAL AND T.PLANT_CODE = T4.PLANT_CODE
                        WHERE T.${dateColumnName} = #{selected.report1Date, jdbcType=VARCHAR}
                        <include refid="filter"/>
                        <include refid="supplyFilter"/>
                    </when>
                    <when test="selected.report1Category == 'Supply Total'.toString()">
                        SELECT T.${dateColumnName} "DATE",
                               T.PLANT_CODE,
                               T.MATERIAL,
                               T.GROUP_MATERIAL,
                               T.PURCH_ORDER_NUMBER,
                               T.PURCH_ORDER_ITEM,
                               NVL(T4.OPEN_PO_QTY, 0) OPEN_PO_QTY,
                               NVL(T2.STOCK_ON_HAND, 0) STOCK_ON_HAND,
                               NVL(MMV.SAFETY_STOCK, 0) SAFETY_STOCK,
                               NVL(MMV.AMF, 0) AMF,
                               NVL(MMV.AMU, 0) AMU,
                               NVL(MTDOI.MTD_ORDER_INTAKE, 0) MTD_ORDER_INTAKE,
                               NVL(MTDS.MTD_SALES, 0) MTD_SALES,
                               'Supply_' || t.DATA_LINE CATEGORY,
                               T.MATERIAL_CATEGORY,
                               T.VENDOR_CODE,
                               T.VENDOR_SEARCH_NAME,
                               T.VENDOR_NAME,
                               T.LEAD_TIME,
                               T.LT_RANGE,
                               T.MATERIAL_ST_PLANT,
                               T.ABC,
                               MMV.MOQ_TO_CUSTOMER,
                               MMV.MINIMUM_LOT_SIZE,
                               MMV.RV_TO_CUSTOMER,
                               MMV.AVG_MONTHLY_ORDER_INTAKE,
                               MMV.AVG_MONTHLY_SALES,
                               MMV.PURCHASING_GROUP,
                               MMV.SS3,
                               MMV.AMF_ONEMM,
                               MMV.AMU_ONEMM,
                               QML.VALIDATE_TO AS TRIGGER_BTN_DATE,
                               QML.QMAX_LEAD_TIME AS TRIGGER_DELAY_DAYS_CD,
                               T.CALCULATED_ABC,
                               T.CALCULATED_FMR,
                               T.PROCUREMENT_TYPE,
                               T.ACTIVENESS,
                               T.STOCKING_POLICY,
                               T.COUNTRY,
                               T.MRP_CONTROLLER,
                               T.ROUNDING_VALUE,
                               T.PRODUCT_LINE,
                               T.ENTITY,
                               T.CLUSTER_NAME,
                               T.BU,
                               T.LOCAL_BU,
                               T.LOCAL_PRODUCT_FAMILY,
                               T.LOCAL_PRODUCT_LINE,
                               T.LOCAL_PRODUCT_SUBFAMILY,
                               T.PRODUCTION_LINE,
                               T.PLANT_TYPE,
                               T.REPL_STRATEGY,
                               T.SOURCE_CATEGORY,
                               T.QUANTITY,
                               T.UNIT_COST,
                               T.AVG_SELLING_PRICE_RMB,
                               T.MATERIAL_OWNER_NAME,
                               T.MATERIAL_OWNER_SESA,
                               T.GROSS_WEIGHT_IN_KG,
                               NVL(T.PO_AB, 0) AB_QTY,
                               NVL(T.PO_LA, 0) LA_QTY,
                               NVL(T.PO_NON_ABLA, 0) NON_ABLA_QTY,
                               NVL(T.PO_AB * T.UNIT_COST, 0) AB_MVP,
                               NVL(T.PO_LA * T.UNIT_COST, 0) LA_MVP,
                               NVL(T.PO_NON_ABLA * T.UNIT_COST, 0) NON_ABLA_MVP,
                               NVL(T.PO_AB_VALUE, 0) AB_NET_NET_PRICE,
                               NVL(T.PO_LA_VALUE, 0) LA_NET_NET_PRICE,
                               NVL(T.PO_NON_ABLA_VALUE, 0) NON_ABLA_NET_NET_PRICE,
                               0 MANUAL_QTY,
                               0 MANUAL_MVP,
                               0 MANUAL_VALUE
                        FROM DEMAND_SUPPLY_SUPPLY_V T
                                 LEFT JOIN (SELECT * FROM MM3_TRIGGER_LIST_V WHERE VALIDATE_TO > trunc(SYSDATE,'DD')) QML ON T.MATERIAL = QML.MATERIAL
                                 LEFT JOIN MATERIAL_MASTER_V MMV ON MMV.MATERIAL = T.MATERIAL AND MMV.PLANT_CODE = T.PLANT_CODE
                                 LEFT JOIN (select MATERIAL, PLANT_CODE, SUM(ORDER_QUANTITY) AS MTD_ORDER_INTAKE
                                                FROM DEMAND_ORDER_INTAKE_V
                                                where CALENDAR_MONTH = TO_CHAR(SYSDATE, 'YYYYMM')
                                                GROUP BY MATERIAL, PLANT_CODE) MTDOI ON T.MATERIAL = MTDOI.MATERIAL AND T.PLANT_CODE = MTDOI.PLANT_CODE
                                 LEFT JOIN (select MATERIAL, PLANT_CODE, SUM(ORDER_QUANTITY) AS MTD_SALES
                                                from DEMAND_SALES_V
                                                where CALENDAR_MONTH = TO_CHAR(SYSDATE, 'YYYYMM')
                                                GROUP BY MATERIAL, PLANT_CODE) MTDS ON T.MATERIAL = MTDS.MATERIAL AND T.PLANT_CODE = MTDS.PLANT_CODE
                                 LEFT JOIN DEMAND_SUPPLY_SOH_V T2 ON T.MATERIAL = T2.MATERIAL AND T.PLANT_CODE = T2.PLANT_CODE AND NVL(T.GROUP_MATERIAL, 'Others') = NVL(T2.GROUP_MATERIAL, 'Others')
                                 INNER JOIN DEMAND_SUPPLY_CRITICAL_MATERIAL_V T3 ON T.MATERIAL = T3.MATERIAL AND T.PLANT_CODE = T3.PLANT_CODE AND T3.USER_ID = #{session.userid,jdbcType=VARCHAR}
                                 LEFT JOIN (SELECT MATERIAL, PLANT_CODE, SUM(PO_AB + PO_LA + PO_NON_ABLA) AS OPEN_PO_QTY
                                            FROM OPEN_PO_STRUCTURE_V
                                            GROUP BY MATERIAL, PLANT_CODE) T4 ON T.MATERIAL = T4.MATERIAL AND T.PLANT_CODE = T4.PLANT_CODE
                        WHERE T.${dateColumnName} = #{selected.report1Date, jdbcType=VARCHAR}
                              AND NOT EXISTS (SELECT 1 FROM MA_COMMIT MA WHERE T.MATERIAL = MA.MATERIAL AND T.PLANT_CODE = MA.PLANT_CODE)
                              AND NOT EXISTS (SELECT 1 FROM PO_COMMIT PO WHERE T.PURCH_ORDER_NUMBER = PO.PURCH_ORDER_NUMBER AND T.PURCH_ORDER_ITEM = PO.PURCH_ORDER_ITEM)
                        <if test="selected.report1Material != null and selected.report1Material != ''.toString">
                            AND T.MATERIAL = #{selected.report1Material, jdbcType=VARCHAR}
                        </if>
                        <if test="selected.report1GroupMaterial != null and selected.report1GroupMaterial != ''.toString">
                            AND T.GROUP_MATERIAL = #{selected.report1GroupMaterial, jdbcType=VARCHAR}
                        </if>
                        <if test="selected.report1Plant != null and selected.report1Plant != ''.toString">
                            AND T.PLANT_CODE = #{selected.report1Plant, jdbcType=VARCHAR}
                        </if>
                        <include refid="filter"/>
                        <include refid="supplyFilter"/>

                        UNION ALL

                        SELECT T.${dateColumnName} "DATE",
                               T.PLANT_CODE,
                               T.MATERIAL,
                               T.GROUP_MATERIAL,
                               '' AS PURCH_ORDER_NUMBER,
                               '' AS PURCH_ORDER_ITEM,
                               NVL(T4.OPEN_PO_QTY, 0) OPEN_PO_QTY,
                               NVL(T2.STOCK_ON_HAND, 0) STOCK_ON_HAND,
                               NVL(T.SAFETY_STOCK, 0) SAFETY_STOCK,
                               NVL(T.AMF, 0) AMF,
                               NVL(T.AMU, 0) AMU,
                               NVL(MTDOI.MTD_ORDER_INTAKE, 0) MTD_ORDER_INTAKE,
                               NVL(MTDS.MTD_SALES, 0) MTD_SALES,
                               'Supply_Manual' CATEGORY,
                               T.MATERIAL_CATEGORY,
                               T.VENDOR_CODE,
                               T.VENDOR_SEARCH_NAME,
                               T.VENDOR_NAME,
                               NULL AS LEAD_TIME,
                               T.LT_RANGE,
                               T.MATERIAL_ST_PLANT,
                               T.ABC,
                               T.MOQ_TO_CUSTOMER,
                               T.MINIMUM_LOT_SIZE,
                               T.RV_TO_CUSTOMER,
                               T.AVG_MONTHLY_ORDER_INTAKE,
                               T.AVG_MONTHLY_SALES,
                               T.PURCHASING_GROUP,
                               T.SS3,
                               T.AMF_ONEMM,
                               T.AMU_ONEMM,
                               T.TRIGGER_BTN_DATE,
                               T.TRIGGER_DELAY_DAYS_CD,
                               T.CALCULATED_ABC,
                               T.CALCULATED_FMR,
                               T.PROCUREMENT_TYPE,
                               T.ACTIVENESS,
                               T.STOCKING_POLICY,
                               T.COUNTRY,
                               T.MRP_CONTROLLER,
                               T.ROUNDING_VALUE,
                               T.PRODUCT_LINE,
                               T.ENTITY,
                               T.CLUSTER_NAME,
                               T.BU,
                               T.LOCAL_BU,
                               T.LOCAL_PRODUCT_FAMILY,
                               T.LOCAL_PRODUCT_LINE,
                               T.LOCAL_PRODUCT_SUBFAMILY,
                               T.PRODUCTION_LINE,
                               T.PLANT_TYPE,
                               T.REPL_STRATEGY,
                               T.SOURCE_CATEGORY,
                               T.QUANTITY,
                               T.UNIT_COST,
                               T.AVG_SELLING_PRICE_RMB,
                               T.MATERIAL_OWNER_NAME,
                               T.MATERIAL_OWNER_SESA,
                               T.GROSS_WEIGHT_IN_KG,
                               0 AB_QTY,
                               0 LA_QTY,
                               0 NON_ABLA_QTY,
                               0 AB_MVP,
                               0 LA_MVP,
                               0 NON_ABLA_MVP,
                               0 AB_NET_NET_PRICE,
                               0 LA_NET_NET_PRICE,
                               0 NON_ABLA_NET_NET_PRICE,
                               NVL(T.MANUAL_QTY, 0) MANUAL_QTY,
                               NVL(T.MANUAL_MVP, 0) MANUAL_MVP,
                               NVL(T.MANUAL_VALUE, 0) MANUAL_VALUE
                        FROM MA_COMMIT T
                                 LEFT JOIN (select MATERIAL, PLANT_CODE, SUM(ORDER_QUANTITY) AS MTD_ORDER_INTAKE
                                                FROM DEMAND_ORDER_INTAKE_V
                                                where CALENDAR_MONTH = TO_CHAR(SYSDATE, 'YYYYMM')
                                                GROUP BY MATERIAL, PLANT_CODE) MTDOI ON T.MATERIAL = MTDOI.MATERIAL AND T.PLANT_CODE = MTDOI.PLANT_CODE
                                 LEFT JOIN (select MATERIAL, PLANT_CODE, SUM(ORDER_QUANTITY) AS MTD_SALES
                                                from DEMAND_SALES_V
                                                where CALENDAR_MONTH = TO_CHAR(SYSDATE, 'YYYYMM')
                                                GROUP BY MATERIAL, PLANT_CODE) MTDS ON T.MATERIAL = MTDS.MATERIAL AND T.PLANT_CODE = MTDS.PLANT_CODE
                                 LEFT JOIN DEMAND_SUPPLY_SOH_V T2 ON T.MATERIAL = T2.MATERIAL AND T.PLANT_CODE = T2.PLANT_CODE AND NVL(T.GROUP_MATERIAL, 'Others') = NVL(T2.GROUP_MATERIAL, 'Others')
                                 INNER JOIN DEMAND_SUPPLY_CRITICAL_MATERIAL_V T3 ON T.MATERIAL = T3.MATERIAL AND T.PLANT_CODE = T3.PLANT_CODE AND T3.USER_ID = #{session.userid,jdbcType=VARCHAR}
                                 LEFT JOIN (SELECT MATERIAL, PLANT_CODE, SUM(PO_AB + PO_LA + PO_NON_ABLA) AS OPEN_PO_QTY
                                            FROM OPEN_PO_STRUCTURE_V
                                            GROUP BY MATERIAL, PLANT_CODE) T4 ON T.MATERIAL = T4.MATERIAL AND T.PLANT_CODE = T4.PLANT_CODE
                        WHERE T.${dateColumnName} = #{selected.report1Date, jdbcType=VARCHAR}
                        <if test="selected.report1Material != null and selected.report1Material != ''.toString">
                            AND T.MATERIAL = #{selected.report1Material, jdbcType=VARCHAR}
                        </if>
                        <if test="selected.report1GroupMaterial != null and selected.report1GroupMaterial != ''.toString">
                            AND T.GROUP_MATERIAL = #{selected.report1GroupMaterial, jdbcType=VARCHAR}
                        </if>
                        <if test="selected.report1Plant != null and selected.report1Plant != ''.toString">
                            AND T.PLANT_CODE = #{selected.report1Plant, jdbcType=VARCHAR}
                        </if>
                        <include refid="filter"/>
                        <include refid="supplyFilter"/>

                        UNION ALL

                        SELECT T.${dateColumnName} "DATE",
                               T.PLANT_CODE,
                               T.MATERIAL,
                               T.GROUP_MATERIAL,
                               T.PURCH_ORDER_NUMBER,
                               T.PURCH_ORDER_ITEM,
                               NVL(T4.OPEN_PO_QTY, 0) OPEN_PO_QTY,
                               NVL(T2.STOCK_ON_HAND, 0) STOCK_ON_HAND,
                               NVL(T.SAFETY_STOCK, 0) SAFETY_STOCK,
                               NVL(T.AMF, 0) AMF,
                               NVL(T.AMU, 0) AMU,
                               NVL(MTDOI.MTD_ORDER_INTAKE, 0) AMU,
                               NVL(MTDS.MTD_SALES, 0) AMU,
                               'Supply_Manual' CATEGORY,
                               T.MATERIAL_CATEGORY,
                               T.VENDOR_CODE,
                               T.VENDOR_SEARCH_NAME,
                               T.VENDOR_NAME,
                               T.LEAD_TIME,
                               T.LT_RANGE,
                               T.MATERIAL_ST_PLANT,
                               T.ABC,
                               T.MOQ_TO_CUSTOMER,
                               T.MINIMUM_LOT_SIZE,
                               T.RV_TO_CUSTOMER,
                               T.AVG_MONTHLY_ORDER_INTAKE,
                               T.AVG_MONTHLY_SALES,
                               T.PURCHASING_GROUP,
                               T.SS3,
                               T.AMF_ONEMM,
                               T.AMU_ONEMM,
                               T.TRIGGER_BTN_DATE,
                               T.TRIGGER_DELAY_DAYS_CD,
                               T.CALCULATED_ABC,
                               T.CALCULATED_FMR,
                               T.PROCUREMENT_TYPE,
                               T.ACTIVENESS,
                               T.STOCKING_POLICY,
                               T.COUNTRY,
                               T.MRP_CONTROLLER,
                               T.ROUNDING_VALUE,
                               T.PRODUCT_LINE,
                               T.ENTITY,
                               T.CLUSTER_NAME,
                               T.BU,
                               T.LOCAL_BU,
                               T.LOCAL_PRODUCT_FAMILY,
                               T.LOCAL_PRODUCT_LINE,
                               T.LOCAL_PRODUCT_SUBFAMILY,
                               T.PRODUCTION_LINE,
                               T.PLANT_TYPE,
                               T.REPL_STRATEGY,
                               T.SOURCE_CATEGORY,
                               T.QUANTITY,
                               T.UNIT_COST,
                               T.AVG_SELLING_PRICE_RMB,
                               T.MATERIAL_OWNER_NAME,
                               T.MATERIAL_OWNER_SESA,
                               T.GROSS_WEIGHT_IN_KG,
                               0 AB_QTY,
                               0 LA_QTY,
                               0 NON_ABLA_QTY,
                               0 AB_MVP,
                               0 LA_MVP,
                               0 NON_ABLA_MVP,
                               0 AB_NET_NET_PRICE,
                               0 LA_NET_NET_PRICE,
                               0 NON_ABLA_NET_NET_PRICE,
                               NVL(T.MANUAL_QTY, 0) MANUAL_QTY,
                               NVL(T.MANUAL_MVP, 0) MANUAL_MVP,
                               NVL(T.MANUAL_VALUE, 0) MANUAL_VALUE
                        FROM PO_COMMIT T
                                 LEFT JOIN (select MATERIAL, PLANT_CODE, SUM(ORDER_QUANTITY) AS MTD_ORDER_INTAKE
                                                FROM DEMAND_ORDER_INTAKE_V
                                                where CALENDAR_MONTH = TO_CHAR(SYSDATE, 'YYYYMM')
                                                GROUP BY MATERIAL, PLANT_CODE) MTDOI ON T.MATERIAL = MTDOI.MATERIAL AND T.PLANT_CODE = MTDOI.PLANT_CODE
                                 LEFT JOIN (select MATERIAL, PLANT_CODE, SUM(ORDER_QUANTITY) AS MTD_SALES
                                                from DEMAND_SALES_V
                                                where CALENDAR_MONTH = TO_CHAR(SYSDATE, 'YYYYMM')
                                                GROUP BY MATERIAL, PLANT_CODE) MTDS ON T.MATERIAL = MTDS.MATERIAL AND T.PLANT_CODE = MTDS.PLANT_CODE
                                 LEFT JOIN DEMAND_SUPPLY_SOH_V T2 ON T.MATERIAL = T2.MATERIAL AND T.PLANT_CODE = T2.PLANT_CODE AND NVL(T.GROUP_MATERIAL, 'Others') = NVL(T2.GROUP_MATERIAL, 'Others')
                                 INNER JOIN DEMAND_SUPPLY_CRITICAL_MATERIAL_V T3 ON T.MATERIAL = T3.MATERIAL AND T.PLANT_CODE = T3.PLANT_CODE AND T3.USER_ID = #{session.userid,jdbcType=VARCHAR}
                                 LEFT JOIN (SELECT MATERIAL, PLANT_CODE, SUM(PO_AB + PO_LA + PO_NON_ABLA) AS OPEN_PO_QTY
                                            FROM OPEN_PO_STRUCTURE_V
                                            GROUP BY MATERIAL, PLANT_CODE) T4 ON T.MATERIAL = T4.MATERIAL AND T.PLANT_CODE = T4.PLANT_CODE
                        WHERE T.${dateColumnName} = #{selected.report1Date, jdbcType=VARCHAR}
                        <if test="selected.report1Material != null and selected.report1Material != ''.toString">
                            AND T.MATERIAL = #{selected.report1Material, jdbcType=VARCHAR}
                        </if>
                        <if test="selected.report1GroupMaterial != null and selected.report1GroupMaterial != ''.toString">
                            AND T.GROUP_MATERIAL = #{selected.report1GroupMaterial, jdbcType=VARCHAR}
                        </if>
                        <if test="selected.report1Plant != null and selected.report1Plant != ''.toString">
                            AND T.PLANT_CODE = #{selected.report1Plant, jdbcType=VARCHAR}
                        </if>
                        <include refid="filter"/>
                        <include refid="supplyFilter"/>
                    </when>
                    <otherwise>
                        SELECT T.${dateColumnName} "DATE",
                               T.PLANT_CODE,
                               T.MATERIAL,
                               T.GROUP_MATERIAL,
                               T.PURCH_ORDER_NUMBER,
                               T.PURCH_ORDER_ITEM,
                               NVL(T4.OPEN_PO_QTY, 0) OPEN_PO_QTY,
                               NVL(T2.STOCK_ON_HAND, 0) STOCK_ON_HAND,
                               NVL(MMV.SAFETY_STOCK, 0) SAFETY_STOCK,
                               NVL(MMV.AMF, 0) AMF,
                               NVL(MMV.AMU, 0) AMU,
                               NVL(MTDOI.MTD_ORDER_INTAKE, 0) MTD_ORDER_INTAKE,
                               NVL(MTDS.MTD_SALES, 0) MTD_SALES,
                               'Supply_' || t.DATA_LINE CATEGORY,
                               T.MATERIAL_CATEGORY,
                               T.VENDOR_CODE,
                               T.VENDOR_SEARCH_NAME,
                               T.VENDOR_NAME,
                               T.LEAD_TIME,
                               T.LT_RANGE,
                               T.MATERIAL_ST_PLANT,
                               T.ABC,
                               MMV.MOQ_TO_CUSTOMER,
                               MMV.MINIMUM_LOT_SIZE,
                               MMV.RV_TO_CUSTOMER,
                               MMV.AVG_MONTHLY_ORDER_INTAKE,
                               MMV.AVG_MONTHLY_SALES,
                               MMV.PURCHASING_GROUP,
                               MMV.SS3,
                               MMV.AMF_ONEMM,
                               MMV.AMU_ONEMM,
                               QML.VALIDATE_TO AS TRIGGER_BTN_DATE,
                               QML.QMAX_LEAD_TIME AS TRIGGER_DELAY_DAYS_CD,
                               T.CALCULATED_ABC,
                               T.CALCULATED_FMR,
                               T.PROCUREMENT_TYPE,
                               T.ACTIVENESS,
                               T.STOCKING_POLICY,
                               T.COUNTRY,
                               T.MRP_CONTROLLER,
                               T.ROUNDING_VALUE,
                               T.PRODUCT_LINE,
                               T.ENTITY,
                               T.CLUSTER_NAME,
                               T.BU,
                               T.LOCAL_BU,
                               T.LOCAL_PRODUCT_FAMILY,
                               T.LOCAL_PRODUCT_LINE,
                               T.LOCAL_PRODUCT_SUBFAMILY,
                               T.PRODUCTION_LINE,
                               T.PLANT_TYPE,
                               T.REPL_STRATEGY,
                               T.SOURCE_CATEGORY,
                               T.QUANTITY,
                               T.UNIT_COST,
                               T.AVG_SELLING_PRICE_RMB,
                               T.MATERIAL_OWNER_NAME,
                               T.MATERIAL_OWNER_SESA,
                               T.GROSS_WEIGHT_IN_KG,
                               NVL(T.PO_AB, 0) AB_QTY,
                               NVL(T.PO_LA, 0) LA_QTY,
                               NVL(T.PO_NON_ABLA, 0) NON_ABLA_QTY,
                               NVL(T.PO_AB * T.UNIT_COST, 0) AB_MVP,
                               NVL(T.PO_LA * T.UNIT_COST, 0) LA_MVP,
                               NVL(T.PO_NON_ABLA * T.UNIT_COST, 0) NON_ABLA_MVP,
                               NVL(T.PO_AB_VALUE, 0) AB_NET_NET_PRICE,
                               NVL(T.PO_LA_VALUE, 0) LA_NET_NET_PRICE,
                               NVL(T.PO_NON_ABLA_VALUE, 0) NON_ABLA_NET_NET_PRICE
                        FROM DEMAND_SUPPLY_SUPPLY_V T
                        LEFT JOIN (select MATERIAL, PLANT_CODE, SUM(ORDER_QUANTITY) AS MTD_ORDER_INTAKE
                                    FROM DEMAND_ORDER_INTAKE_V
                                    where CALENDAR_MONTH = TO_CHAR(SYSDATE, 'YYYYMM')
                                    GROUP BY MATERIAL, PLANT_CODE) MTDOI ON T.MATERIAL = MTDOI.MATERIAL AND T.PLANT_CODE = MTDOI.PLANT_CODE
                        LEFT JOIN (select MATERIAL, PLANT_CODE, SUM(ORDER_QUANTITY) AS MTD_SALES
                                    from DEMAND_SALES_V
                                    where CALENDAR_MONTH = TO_CHAR(SYSDATE, 'YYYYMM')
                                    GROUP BY MATERIAL, PLANT_CODE) MTDS ON T.MATERIAL = MTDS.MATERIAL AND T.PLANT_CODE = MTDS.PLANT_CODE
                        LEFT JOIN (SELECT MOQ_TO_CUSTOMER,
                                            MINIMUM_LOT_SIZE,
                                            RV_TO_CUSTOMER,
                                            AVG_MONTHLY_ORDER_INTAKE,
                                            AVG_MONTHLY_SALES,
                                            PURCHASING_GROUP,
                                            SS3,
                                            AMF,
                                            AMU,
                                            SAFETY_STOCK,
                                            AMF_ONEMM,
                                            AMU_ONEMM,
                                            MATERIAL,
                                            PLANT_CODE
                                            FROM MATERIAL_MASTER_V) MMV ON MMV.MATERIAL = T.MATERIAL AND MMV.PLANT_CODE = T.PLANT_CODE
                                 LEFT JOIN (SELECT * FROM MM3_TRIGGER_LIST_V WHERE VALIDATE_TO > trunc(SYSDATE,'DD')) QML ON T.MATERIAL = QML.MATERIAL
                                 LEFT JOIN DEMAND_SUPPLY_SOH_V T2 ON T.MATERIAL = T2.MATERIAL AND T.PLANT_CODE = T2.PLANT_CODE AND NVL(T.GROUP_MATERIAL, 'Others') = NVL(T2.GROUP_MATERIAL, 'Others')
                                 INNER JOIN DEMAND_SUPPLY_CRITICAL_MATERIAL_V T3 ON T.MATERIAL = T3.MATERIAL AND T.PLANT_CODE = T3.PLANT_CODE AND T3.USER_ID = #{session.userid,jdbcType=VARCHAR}
                                 LEFT JOIN (SELECT MATERIAL, PLANT_CODE, SUM(PO_AB + PO_LA + PO_NON_ABLA) AS OPEN_PO_QTY
                                            FROM OPEN_PO_STRUCTURE_V
                                            GROUP BY MATERIAL, PLANT_CODE) T4 ON T.MATERIAL = T4.MATERIAL AND T.PLANT_CODE = T4.PLANT_CODE
                        WHERE T.${dateColumnName} = #{selected.report1Date, jdbcType=VARCHAR}
                              AND NOT EXISTS (SELECT 1 FROM MA_COMMIT MA WHERE T.MATERIAL = MA.MATERIAL AND T.PLANT_CODE = MA.PLANT_CODE)
                              AND NOT EXISTS (SELECT 1 FROM PO_COMMIT PO WHERE T.PURCH_ORDER_NUMBER = PO.PURCH_ORDER_NUMBER AND T.PURCH_ORDER_ITEM = PO.PURCH_ORDER_ITEM)
                        <if test="selected.report1Material != null and selected.report1Material != ''.toString">
                            AND T.MATERIAL = #{selected.report1Material, jdbcType=VARCHAR}
                        </if>
                        <if test="selected.report1GroupMaterial != null and selected.report1GroupMaterial != ''.toString">
                            AND T.GROUP_MATERIAL = #{selected.report1GroupMaterial, jdbcType=VARCHAR}
                        </if>
                        <if test="selected.report1Plant != null and selected.report1Plant != ''.toString">
                            AND T.PLANT_CODE = #{selected.report1Plant, jdbcType=VARCHAR}
                        </if>
                        AND T.DATA_LINE = #{category_vendor, jdbcType=VARCHAR}
                        <if test="confirm_cat != null and confirm_cat !=''.toString()">
                            AND T.CONFIRM_CAT = #{confirm_cat, jdbcType=VARCHAR}
                        </if>
                        <include refid="filter"/>
                        <include refid="supplyFilter"/>
                    </otherwise>
                </choose>
            </otherwise>
        </choose>
    </sql>

    <select id="queryReport1DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1DetailSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1DetailSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport1HistoryCommentsSQL">
        SELECT WEEK,
               PLANT_CODE,
               MATERIAL,
               COMMENTS,
               C.USER_NAME AS CREATE_BY,
               TO_CHAR(T.CREATE_DATE$, 'YYYY/MM/DD HH24:MI:SS') AS CREATE_DATE,
               U.USER_NAME AS UPDATE_BY,
               TO_CHAR(T.UPDATE_DATE$, 'YYYY/MM/DD HH24:MI:SS') AS UPDATE_DATE
          FROM DEMAND_SUPPLY_COMMENTS T LEFT JOIN SY_USER_MASTER_DATA C ON T.CREATE_BY$ = C.SESA_CODE
                                        LEFT JOIN SY_USER_MASTER_DATA U ON T.CREATE_BY$ = U.SESA_CODE
         WHERE T.PLANT_CODE = #{report1HistPlantCode, jdbcType=VARCHAR}
            <choose>
                <when test="report1HistGroupMaterial != null and report1HistGroupMaterial != ''.toString()">
                    AND T.MATERIAL = #{report1HistGroupMaterial, jdbcType=VARCHAR}
                </when>
                <otherwise>
                    AND T.MATERIAL = #{report1HistMaterial, jdbcType=VARCHAR}
                </otherwise>
            </choose>
         ORDER BY NVL(T.UPDATE_DATE$, T.CREATE_DATE$) DESC
    </sql>

    <select id="queryReport1HistoryCommentsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1HistoryCommentsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1HistoryComments" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1HistoryCommentsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <update id="mergeReport1Comments">
        MERGE INTO DEMAND_SUPPLY_COMMENTS t
        USING
        (
        <foreach collection="comments" item="item" separator="union all">
            SELECT #{weekNo, jdbcType=VARCHAR} WEEK,
                   #{item.plantCode, jdbcType=VARCHAR} PLANT_CODE,
                   #{item.material, jdbcType=VARCHAR} MATERIAL,
                   #{item.comment, jdbcType=VARCHAR} COMMENTS,
                   #{userid, jdbcType=VARCHAR} CREATE_BY$
            from dual
        </foreach>
        ) s on (t.WEEK = s.WEEK and t.PLANT_CODE = s.PLANT_CODE and t.MATERIAL = s.MATERIAL and t.CREATE_BY$ = s.CREATE_BY$)
        when matched then
        update set
            t.COMMENTS = s.COMMENTS,
            t.update_date$ = sysdate,
            t.update_by$ = #{userid, jdbcType=VARCHAR}
        when not matched then
        insert (WEEK, PLANT_CODE, MATERIAL, COMMENTS, CREATE_BY$, CREATE_DATE$)
        values (s.WEEK, s.PLANT_CODE, s.MATERIAL, s.COMMENTS, s.CREATE_BY$, sysdate)
    </update>

    <select id="queryReport1CommentsByUserid" resultType="java.util.Map">
        WITH TEMP AS (
            SELECT MATERIAL, PLANT_CODE, MAX(CREATE_DATE$) CREATE_DATE$ FROM DEMAND_SUPPLY_COMMENTS WHERE CREATE_BY$ = #{userid, jdbcType=VARCHAR} GROUP BY MATERIAL, PLANT_CODE
        )
        SELECT T.MATERIAL, T.PLANT_CODE, MAX(T.COMMENTS) COMMENTS
          FROM DEMAND_SUPPLY_COMMENTS T INNER JOIN TEMP ON T.MATERIAL = TEMP.MATERIAL AND T.PLANT_CODE = TEMP.PLANT_CODE AND T.CREATE_DATE$ = TEMP.CREATE_DATE$
         WHERE CREATE_BY$ = #{userid, jdbcType=VARCHAR} AND T.COMMENTS IS NOT NULL
          GROUP BY T.MATERIAL, T.PLANT_CODE
    </select>

    <sql id="queryReport3Sql">
        SELECT ROWIDTOCHAR(ROWID) AS ROW_ID,
               PURCH_ORDER_NUMBER,
               PURCH_ORDER_ITEM,
               SEQUENTIAL_NUMBER,
               MATERIAL,
               PLANT_CODE,
               QTY,
               COMMIT_DATE
          FROM DEMAND_SUPPLY_MANUAL_PO_COMMIT T WHERE USER_ID = #{session.userid, jdbcType=VARCHAR}
    </sql>

    <select id="queryReport3Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport3Sql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport3" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport3Sql"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="downloadReport3" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        SELECT PURCH_ORDER_NUMBER,
               PURCH_ORDER_ITEM,
               SEQUENTIAL_NUMBER,
               MATERIAL,
               PLANT_CODE,
               QTY,
               COMMIT_DATE
          FROM DEMAND_SUPPLY_MANUAL_PO_COMMIT WHERE USER_ID = #{session.userid, jdbcType=VARCHAR}
        <include refid="global.select_footer"/>
    </select>

    <update id="updateReport3ByTable">
        UPDATE DEMAND_SUPPLY_MANUAL_PO_COMMIT
        SET
        <foreach collection="updates" item="col" separator=",">
            ${col.key} = #{col.value,jdbcType=VARCHAR}
        </foreach>,
        UPDATE_BY$ = #{userid,jdbcType=VARCHAR},
        UPDATE_DATE$ = SYSDATE
        WHERE ROWID = #{pk,jdbcType=VARCHAR} AND USER_ID = #{userid,jdbcType=VARCHAR}
    </update>

    <insert id="createReport3ByTable">
        INSERT INTO DEMAND_SUPPLY_MANUAL_PO_COMMIT
        (
        <foreach collection="headers" item="header" separator=",">
            ${header}
        </foreach>, USER_ID, CREATE_BY$, CREATE_DATE$
        )
        <foreach collection="inserts" item="list" separator=" union all ">
            SELECT
            <foreach collection="headers" item="header" separator=",">
                #{list.${header}, jdbcType=VARCHAR}
            </foreach>, #{userid,jdbcType=VARCHAR}, #{userid,jdbcType=VARCHAR}, SYSDATE
            FROM dual
        </foreach>
    </insert>

    <delete id="deleteReport3ByTable">
        DELETE FROM DEMAND_SUPPLY_MANUAL_PO_COMMIT WHERE ROWID IN
        <foreach collection="deletes" open="(" close=")" separator="," item="item">#{item, jdbcType=VARCHAR}</foreach>
        AND USER_ID = #{userid,jdbcType=VARCHAR}
    </delete>

    <insert id="insertReport3DataTemp">
        INSERT /*+ NOLOGGING  */ INTO DEMAND_SUPPLY_MANUAL_PO_COMMIT_TEMPORARY
        (
            PURCH_ORDER_NUMBER,
            PURCH_ORDER_ITEM,
            SEQUENTIAL_NUMBER,
            MATERIAL,
            PLANT_CODE,
            QTY,
            COMMIT_DATE
        )
        <foreach collection="list" separator=" union all" item="item">
            select
            #{item.PURCH_ORDER_NUMBER, jdbcType=VARCHAR},
            #{item.PURCH_ORDER_ITEM, jdbcType=VARCHAR},
            #{item.SEQUENTIAL_NUMBER, jdbcType=VARCHAR},
            #{item.MATERIAL, jdbcType=VARCHAR},
            #{item.PLANT_CODE, jdbcType=VARCHAR},
            #{item.QTY, jdbcType=VARCHAR},
            #{item.COMMIT_DATE, jdbcType=VARCHAR}
            from dual
        </foreach>
    </insert>

    <update id="mergeReport3Data">
        BEGIN
            DELETE FROM DEMAND_SUPPLY_MANUAL_PO_COMMIT where USER_ID = #{userid, jdbcType=VARCHAR};
            INSERT INTO DEMAND_SUPPLY_MANUAL_PO_COMMIT
            (
                PURCH_ORDER_NUMBER,
                PURCH_ORDER_ITEM,
                SEQUENTIAL_NUMBER,
                MATERIAL,
                PLANT_CODE,
                QTY,
                COMMIT_DATE,
                USER_ID,
                CREATE_BY$,
                CREATE_DATE$
            )
            SELECT PURCH_ORDER_NUMBER,
                   PURCH_ORDER_ITEM,
                   SEQUENTIAL_NUMBER,
                   MATERIAL,
                   PLANT_CODE,
                   QTY,
                   COMMIT_DATE,
                   #{userid,jdbcType=VARCHAR},
                   #{userid,jdbcType=VARCHAR},
                   SYSDATE
              FROM DEMAND_SUPPLY_MANUAL_PO_COMMIT_TEMPORARY;
        END;
    </update>

    <sql id="queryReport4Sql">
        select ROWIDTOCHAR(ROWID) AS ROW_ID, MATERIAL, PLANT_CODE from DEMAND_SUPPLY_CRITICAL_MATERIAL_MANUAL WHERE USER_ID = #{session.userid, jdbcType=VARCHAR}
    </sql>

    <select id="queryReport4Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport4Sql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport4" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport4Sql"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="downloadReport4" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        select PLANT_CODE,MATERIAL from DEMAND_SUPPLY_CRITICAL_MATERIAL_MANUAL WHERE USER_ID = #{session.userid, jdbcType=VARCHAR}
        <include refid="global.select_footer"/>
    </select>

    <update id="updateReport4ByTable">
        UPDATE DEMAND_SUPPLY_CRITICAL_MATERIAL_MANUAL
        SET
        <foreach collection="updates" item="col" separator=",">
            ${col.key} = #{col.value,jdbcType=VARCHAR}
        </foreach>,
        UPDATE_BY$ = #{userid,jdbcType=VARCHAR},
        UPDATE_DATE$ = SYSDATE
        WHERE ROWID = #{pk,jdbcType=VARCHAR} AND USER_ID = #{userid,jdbcType=VARCHAR}
    </update>

    <insert id="createReport4ByTable">
        INSERT INTO DEMAND_SUPPLY_CRITICAL_MATERIAL_MANUAL
        (
        <foreach collection="headers" item="header" separator=",">
            ${header}
        </foreach>, USER_ID, CREATE_BY$, CREATE_DATE$
        )
        <foreach collection="inserts" item="list" separator=" union all ">
            select
            <foreach collection="headers" item="header" separator=",">
                #{list.${header}, jdbcType=VARCHAR}
            </foreach>, #{userid,jdbcType=VARCHAR}, #{userid,jdbcType=VARCHAR}, SYSDATE
            from dual
        </foreach>
    </insert>

    <delete id="deleteReport4ByTable">
        DELETE FROM DEMAND_SUPPLY_CRITICAL_MATERIAL_MANUAL WHERE ROWID IN
        <foreach collection="deletes" open="(" close=")" separator="," item="item">#{item, jdbcType=VARCHAR}</foreach>
        and USER_ID = #{userid,jdbcType=VARCHAR}
    </delete>

    <insert id="insertReport4DataTemp">
        INSERT /*+ NOLOGGING  */ INTO DEMAND_SUPPLY_CRITICAL_MATERIAL_MANUAL_TEMPORARY
        (MATERIAL, PLANT_CODE)
        <foreach collection="list" separator=" union all" item="item">
            select
            #{item.MATERIAL, jdbcType=VARCHAR},
            #{item.PLANT_CODE, jdbcType=VARCHAR}
            from dual
        </foreach>
    </insert>

    <select id="queryDeplicateRows" resultType="java.util.HashMap">
        SELECT MATERIAL, COUNT(1) AS CNT
          FROM DEMAND_SUPPLY_CRITICAL_MATERIAL_MANUAL_TEMPORARY
         GROUP BY MATERIAL
         HAVING COUNT(1) > 1
         ORDER BY CNT DESC
         FETCH NEXT 10 ROWS ONLY
    </select>

    <update id="mergeReport4Data">
        BEGIN
            DELETE FROM DEMAND_SUPPLY_CRITICAL_MATERIAL_MANUAL where USER_ID = #{userid, jdbcType=VARCHAR};
            INSERT INTO DEMAND_SUPPLY_CRITICAL_MATERIAL_MANUAL
            (MATERIAL, PLANT_CODE, USER_ID, CREATE_BY$, CREATE_DATE$)
            SELECT MATERIAL, PLANT_CODE, #{userid,jdbcType=VARCHAR}, #{userid,jdbcType=VARCHAR}, SYSDATE FROM DEMAND_SUPPLY_CRITICAL_MATERIAL_MANUAL_TEMPORARY;
        END;
    </update>

    <update id="refreshCriticalMaterial">
        begin
            dbms_mview.refresh('DEMAND_SUPPLY_CRITICAL_MATERIAL_V');
        end;
    </update>

    <select id="queryCurrentWeek" resultType="java.lang.String">
         SELECT T.YEAR || T.WEEK_NO AS WEEKNO
           FROM SY_CALENDAR T
          WHERE T.DATE$ = TRUNC(SYSDATE, 'DD')
            AND T.NAME = 'National Holidays'
     </select>

    <select id="queryNext14WeekList" resultType="java.lang.String">
        select distinct t.YEAR || 'W' || t.WEEK_NO
          from SY_CALENDAR t
         where t.DATE$ between sysdate and sysdate  + 100
           and t.NAME = 'National Holidays'
           AND NOT (T.WEEK_NO = '01' AND TO_CHAR(T.DATE$, 'MM') = '12')
         order by t.YEAR || 'W' || t.WEEK_NO
         fetch next 14 rows only
    </select>

    <select id="queryAllPlant" resultType="java.lang.String">
        SELECT DISTINCT PLANT_CODE FROM MR3_PLANT_MASTER_DATA
    </select>
</mapper>
