package com.scp.simulation.dao;

import com.scp.simulation.bean.*;
import com.starter.context.bean.scptable.ScpTableCell;
import com.scp.toolbox.bean.TreeData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IBestCanDoDao {

    List<TreeData> queryUserBatchList(@Param("userid") String userid, @Param("authType") String authType);


    void saveBestCanDoLog(BestCanDoLog log);

    List<Map<String, String>> queryFilterList();

    List<Map<String, String>> queryTaskInfo(String userid, String authType);

    int copyPOData(Map<String, Object> parameterMap);

    int copyMOData(Map<String, Object> parameterMap);

    int copySOData(Map<String, Object> parameterMap);

    void insertLog(Map<String, Object> parameterMap);

    int queryReport1Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1(Map<String, Object> parameterMap);

    int queryReport2Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport2(Map<String, Object> parameterMap);

    List<String> queryExecuteLogs(Map<String, Object> parameterMap);

    void deleteBatch(Map<String, Object> parameterMap);

    int queryBatchCount(Map<String, Object> parameterMap);

    int querySimulateCount(Map<String, Object> parameterMap);

    int createReport1ByTable(List<String> headers, List<Map<String, Object>> creates, String batchId);

    int deleteReport1ByTable(List<String> deletes, String batchId);

    int updateReport1ByTable(String rowid, List<ScpTableCell> updates, String batchId);

    int createReport2ByTable(List<String> headers, List<Map<String, Object>> creates, String batchId);

    int deleteReport2ByTable(List<String> deletes, String batchId);

    int updateReport2ByTable(String rowid, List<ScpTableCell> updates, String batchId);

    String queryBatchParams(String batchId);

    void deleteReport1Data(String batchId);

    void insertReport1Data(@Param("batchId") String batchId, @Param("list") List<SOMOPriority> data);

    void deleteReport2Data(String batchId);

    void insertReport2Data(@Param("batchId") String batchId, @Param("list") List<PODelivery> data);

    int queryExecutingCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryTaskQueue(Map<String, Object> parameterMap);

    int queryReport4Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport4(Map<String, Object> parameterMap);

    int queryReport5Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport5(Map<String, Object> parameterMap);

    int queryReport6Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport6(Map<String, Object> parameterMap);

    String queryAuthDetails(String userid, @Param("menuCode") String menuCode);

    Map<String, Object> queryBatchInfo(Map<String, Object> parameterMap);

    List<String> queryAvailablePlants();

    List<String> initExistsGroup(String userid, String authType);

    int queryTaskQueueCount(Map<String, Object> parameterMap);

    void updateSimulateStep(Map<String, Object> parameterMap);

    Map<String, Object> queryBatchStepInfo(Map<String, Object> parameterMap);

    List<String> queryBatchStepName(Map<String, Object> stepInfo);

    List<BestCanDoCompare1> compareReport1(Map<String, Object> parameterMap);

    int compareReport1DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> compareReport1Details(Map<String, Object> parameterMap);

    List<String> compareReport1legend(Map<String, Object> parameterMap);

    List<BestCanDoCompare2Bean> compareReport2(Map<String, Object> parameterMap);

    int compareReport2DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> compareReport2Details(Map<String, Object> parameterMap);

    List<BestCanDoCompare1> compareReport3(Map<String, Object> parameterMap);

    List<String> compareReport3legend(Map<String, Object> parameterMap);

    int compareReport3DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> compareReport3Details(Map<String, Object> parameterMap);

    List<BestCanDoCompare1> compareReport4Left(Map<String, Object> parameterMap);

    List<BestCanDoCompare1> compareReport4Right(Map<String, Object> parameterMap);

    List<String> compareReport4legend(Map<String, Object> parameterMap);

    int compareReport4DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> compareReport4Details(Map<String, Object> parameterMap);

    Map<String, BigDecimal> compareReportOverview(Map<String, Object> parameterMap);

    int compareOverviewDetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> compareOverviewDetails(Map<String, Object> parameterMap);

}
