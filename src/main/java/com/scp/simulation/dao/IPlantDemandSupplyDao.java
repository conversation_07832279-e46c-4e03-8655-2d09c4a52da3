package com.scp.simulation.dao;

import com.scp.simulation.bean.DemandSupplyMaterial;
import com.starter.context.bean.scptable.ScpTableCell;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IPlantDemandSupplyDao {

    List<Map<String, String>> queryDemandSupplyCascader();

    List<Map<String, String>> queryCriticalLevelList();

    Object queryReport1CriticalScript(Map<String, Object> parameterMap);

    Map<String, Object> queryReport1CriticalLevel(Map<String, Object> parameterMap);

    List<DemandSupplyMaterial> queryReport1Group(Map<String, Object> parameterMap);

    List<DemandSupplyMaterial> queryReport1Material(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport1Demand(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport1Supply(Map<String, Object> parameterMap);

    int queryReport1DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1Details(Map<String, Object> parameterMap);

    int queryReport3Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3(Map<String, Object> parameterMap);

    int createReport3ByTable(List<String> headers, List<Map<String, Object>> inserts, String userid);

    int deleteReport3ByTable(List<String> deletes, String userid);

    int updateReport3ByTable(String pk, List<ScpTableCell> updates, String userid);

    void insertReport3DataTemp(List<Map<String, Object>> data);

    void mergeReport3Data(String userid);

    int queryReport4Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport4(Map<String, Object> parameterMap);

    int createReport4ByTable(List<String> headers, List<Map<String, Object>> inserts, String userid);

    int deleteReport4ByTable(List<String> deletes, String userid);

    int updateReport4ByTable(String pk, List<ScpTableCell> updates, String userid);

    void insertReport4DataTemp(List<Map<String, Object>> data);

    void mergeReport4Data(String userid);

    void refreshCriticalMaterial();

    String queryCurrentWeek();

    void mergeReport1Comments(@Param("comments") List<Map<String, String>> comments, @Param("userid") String userid, @Param("weekNo") String weekNo);

    List<Map<String, Object>> queryReport1CommentsByUserid(String weekNo, String userid);

    int queryReport1HistoryCommentsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1HistoryComments(Map<String, Object> parameterMap);

    List<String> queryNext14WeekList();

    List<String> queryAllPlant();

    List<Map<String, Object>> queryDeplicateRows();
}
