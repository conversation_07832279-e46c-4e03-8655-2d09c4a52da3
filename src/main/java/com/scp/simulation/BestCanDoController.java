package com.scp.simulation;

import com.scp.simulation.service.IBestCanDoService;
import com.scp.simulation.service.impl.BestCanDoServiceImpl;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.util.WebUtils;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/simulation/best_can_do", parent = BestCanDoServiceImpl.PARENT_CODE)
public class BestCanDoController extends ControllerHelper {

    @Resource
    private IBestCanDoService bestCanDoService;

    private Response res;
    @Resource
    private Response response;

    @SchneiderRequestMapping("/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoService.initPage(session.getUserid());
    }

    @SchneiderRequestMapping("/init_exists_group")
    public Response initExistsGroup(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoService.initExistsGroup(session.getUserid());
    }

    @SchneiderRequestMapping("/query_batch_list")
    public Response queryBatchList(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoService.queryBatchList(session.getUserid());
    }

    @SchneiderRequestMapping("/query_batch_info")
    public Response queryBatchInfo(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoService.queryBatchInfo(parameterMap);
    }

    @SchneiderRequestMapping("/query_task_info")
    public Response queryTaskInfo(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoService.queryTaskInfo(session.getUserid());
    }

    @SchneiderRequestMapping("/query_execute_logs")
    public Response queryExecuteLogs(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoService.queryExecuteLogs(parameterMap);
    }

    @SchneiderRequestMapping("/save_new_batch")
    public Response saveNewBatch(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoService.saveNewBatch(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping("/delete_batch")
    public Response deleteBatch(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoService.deleteBatch(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping("/send_simulate_request")
    public Response sendSimulateRequest(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoService.sendSimulateRequest(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping("/download_report1")
    public void downloadReport1(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoService.downloadReport1(parameterMap, response);
    }

    @SchneiderRequestMapping("/save_report1")
    public Response saveReport1(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoService.saveReport1(parameterMap);
    }

    @SchneiderRequestMapping("/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoService.queryReport2(parameterMap);
    }

    @SchneiderRequestMapping("/download_report2")
    public void downloadReport2(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoService.downloadReport2(parameterMap, response);
    }

    @SchneiderRequestMapping("/save_report2")
    public Response saveReport2(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoService.saveReport2(parameterMap);
    }

    @SchneiderRequestMapping("/download_report1_template")
    public void downloadReport1Template(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoService.downloadReport1Template(parameterMap, response);
    }

    @SchneiderRequestMapping("/download_report2_template")
    public void downloadReport2Template(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoService.downloadReport2Template(parameterMap, response);
    }

    @SchneiderRequestMapping("/upload_report1")
    public Response uploadReport1(HttpServletRequest request) {
        super.pageLoad(request);
        MultipartHttpServletRequest multipartRequest = WebUtils.getNativeRequest(request, MultipartHttpServletRequest.class);
        if (multipartRequest != null) {
            MultipartFile file = multipartRequest.getFile("file");
            try {
                return bestCanDoService.uploadReport1((String) parameterMap.get("batchId"), file);
            } catch (Exception e) {
                return response.setError(e);
            }
        } else {
            return response.setError(new Exception("upload failed, multipartRequest is null !"));
        }
    }

    @SchneiderRequestMapping("/upload_report2")
    public Response uploadReport2(HttpServletRequest request) {
        super.pageLoad(request);
        MultipartHttpServletRequest multipartRequest = WebUtils.getNativeRequest(request, MultipartHttpServletRequest.class);
        if (multipartRequest != null) {
            MultipartFile file = multipartRequest.getFile("file");
            try {
                return bestCanDoService.uploadReport2((String) parameterMap.get("batchId"), file);
            } catch (Exception e) {
                return response.setError(e);
            }
        } else {
            return response.setError(new Exception("upload failed, multipartRequest is null !"));
        }
    }

    @SchneiderRequestMapping("/query_task_queue")
    public Response queryTaskQueue(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoService.queryTaskQueue(parameterMap);
    }

    @SchneiderRequestMapping("/query_report4")
    public Response queryReport4(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoService.queryReport4(parameterMap);
    }

    @SchneiderRequestMapping("/download_report4")
    public void downloadReport4(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoService.downloadReport4(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report5")
    public Response queryReport5(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoService.queryReport5(parameterMap);
    }

    @SchneiderRequestMapping("/download_report5")
    public void downloadReport5(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoService.downloadReport5(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report6")
    public Response queryReport6(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoService.queryReport6(parameterMap);
    }

    @SchneiderRequestMapping("/download_report6")
    public void downloadReport6(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoService.downloadReport6(parameterMap, response);
    }

    @SchneiderRequestMapping("/compare_report_1")
    public Response compareReport1(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoService.compareReport1(parameterMap);
    }

    @SchneiderRequestMapping("/compare_report_1_details")
    public Response compareReport1Details(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoService.compareReport1Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_compare_report_1_details")
    public void downloadCompareReport1Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoService.downloadCompareReport1Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/compare_report_2")
    public Response compareReport2(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        try {
            return bestCanDoService.compareReport2(parameterMap);
        } catch (Exception e) {
            return res.setError(e);
        }
    }

    @SchneiderRequestMapping("/compare_report_2_details")
    public Response compareReport2Details(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoService.compareReport2Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_compare_report_2_details")
    public void downloadCompareReport2Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoService.downloadCompareReport2Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/compare_report_3")
    public Response compareReport3(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoService.compareReport3(parameterMap);
    }

    @SchneiderRequestMapping("/compare_report_3_details")
    public Response compareReport3Details(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoService.compareReport3Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_compare_report_3_details")
    public void downloadCompareReport3Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoService.downloadCompareReport3Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/compare_report_4_left")
    public Response compareReport4Left(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoService.compareReport4Left(parameterMap);
    }

    @SchneiderRequestMapping("/compare_report_4_right")
    public Response compareReport4Right(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoService.compareReport4Right(parameterMap);
    }

    @SchneiderRequestMapping("/compare_report_4_details")
    public Response compareReport4Details(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoService.compareReport4Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_compare_report_4_details")
    public void downloadCompareReport4Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoService.downloadCompareReport4Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/compare_report_overview")
    public Response compareReportOverview(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoService.compareReportOverview(parameterMap);
    }

    @SchneiderRequestMapping("/compare_overview_details")
    public Response compareReportOverviewDetails(HttpServletRequest request) {
        super.pageLoad(request);
        return bestCanDoService.compareOverviewDetails(parameterMap);
    }

    @SchneiderRequestMapping("/download_compare_overview_details")
    public void downloadCompareReportOverviewDetails(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        bestCanDoService.downloadCompareOverviewDetails(parameterMap, response);
    }
}
