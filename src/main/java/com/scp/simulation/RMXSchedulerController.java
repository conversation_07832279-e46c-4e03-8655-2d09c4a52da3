package com.scp.simulation;

import com.scp.simulation.service.IRMXSchedulerService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.util.WebUtils;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/simulation/rmx_scheduler", parent = "menuB40")
public class RMXSchedulerController extends ControllerHelper {

    @Resource
    private IRMXSchedulerService rmxSchedulerService;

    @Resource
    private Response resp;


    @SchneiderRequestMapping("/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        return rmxSchedulerService.initPage(parameterMap);
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        return rmxSchedulerService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping("/upload_report1")
    public Response uploadReport1(HttpServletRequest request) {
        MultipartHttpServletRequest multipartRequest = WebUtils.getNativeRequest(request, MultipartHttpServletRequest.class);
        if (multipartRequest != null) {
            MultipartFile file = multipartRequest.getFile("file");
            try {
                return rmxSchedulerService.uploadReport1(file);
            } catch (Exception e) {
                return resp.setError(e);
            }
        } else {
            return resp.setError(new Exception("upload failed, multipartRequest is null !"));
        }
    }

    @SchneiderRequestMapping("/download_report1_upload_template")
    public void downloadReport1UploadTemplate(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        rmxSchedulerService.downloadReport1UploadTemplate(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report1_start_schedule")
    public Response queryReport1StartSchedule(HttpServletRequest request) {
        super.pageLoad(request);
        return rmxSchedulerService.queryReport1StartSchedule(parameterMap);
    }

    @SchneiderRequestMapping("/query_report1_result")
    public Response queryReport1Result(HttpServletRequest request) {
        super.pageLoad(request);
        return rmxSchedulerService.queryReport1Result(parameterMap);
    }

    @SchneiderRequestMapping("/download_report1_result")
    public void downloadReport1Result(HttpServletResponse response) {
        rmxSchedulerService.downloadReport1Result(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        super.pageLoad(request);
        return rmxSchedulerService.queryReport2(parameterMap);
    }

    @SchneiderRequestMapping("/query_report2_day_capacity")
    public Response queryReport2DayCapacity(HttpServletRequest request) {
        super.pageLoad(request);
        return rmxSchedulerService.queryReport2DayCapacity(parameterMap);
    }

    @SchneiderRequestMapping("/update_report2_day_capacity")
    public Response updateReport2DayCapacity(HttpServletRequest request) {
        super.pageLoad(request);
        return rmxSchedulerService.updateReport2DayCapacity(parameterMap);
    }
}
