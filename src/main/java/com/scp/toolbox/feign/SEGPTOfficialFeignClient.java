package com.scp.toolbox.feign;

import com.alibaba.fastjson.JSONObject;
import com.scp.toolbox.bean.SEGPTOfficialParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(name = "SEGPTOfficialFeignClient", url = SEGPTOfficialFeignClient.SE_GPT_OFFICIAL_FEIGN_CLIENT_URL)
public interface SEGPTOfficialFeignClient {
    String SE_GPT_OFFICIAL_FEIGN_CLIENT_URL = "http://10.155.96.102:8013";
    String API_HEADER = "Authorization=Bearer 5f78f963-0d6d-61ff-8f16-a1cb2fd7bd83";

    @PostMapping(value = "/v3/chat/completions", headers = API_HEADER)
    JSONObject execute(SEGPTOfficialParam params);
}
