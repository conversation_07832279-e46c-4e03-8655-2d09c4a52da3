package com.scp.toolbox.feign;

import com.scp.toolbox.bean.SoIdentifyParam;
import com.starter.context.configuration.FeignClientIgnoreHttpsConfig;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(name = "SoIdentifyFeignClient", url = SoIdentifyFeignClient.SO_IDENTIFY_FEIGN_CLIENT_URL, configuration = FeignClientIgnoreHttpsConfig.class)
public interface SoIdentifyFeignClient {
    String SO_IDENTIFY_FEIGN_CLIENT_URL = "https://aivip.cn.se.com/prod-api/ai/agent";

    @PostMapping(value = "/third/execute")
    String execute(SoIdentifyParam params);
}
