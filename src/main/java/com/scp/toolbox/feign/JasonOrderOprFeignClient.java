package com.scp.toolbox.feign;

import com.scp.toolbox.bean.QueryUrgentOrderParam;
import com.scp.toolbox.bean.QueryUrgentOrderResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

@FeignClient(name = "JasonOrderOprFeignClient", url = JasonOrderOprFeignClient.JASON_ORDER_OPR_FEIGN_CLIENT_URL)
public interface JasonOrderOprFeignClient {

    String JASON_ORDER_OPR_FEIGN_CLIENT_URL = "http://10.177.21.8:80";

    @PostMapping(value = "/api/urgent-order-query")
    List<QueryUrgentOrderResponse> queryUrgentOrder(QueryUrgentOrderParam params);
}
