package com.scp.toolbox;

import com.scp.toolbox.service.IWeChatAutoReplyService;
import com.scp.toolbox.service.impl.WeChatAutoReplyServiceImpl;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin
@SchneiderRequestMapping(value = "/toolbox/wechat_auto_reply", parent = WeChatAutoReplyServiceImpl.PARENT_CODE)
@Scope("prototype")
public class WeChatAutoReplyController extends ControllerHelper {

    @Resource
    private IWeChatAutoReplyService weChatAutoReplyService;

    @SchneiderRequestMapping(value = "/init_page")
    public Response initPage(HttpServletRequest request) {
        this.pageLoad(request);
        this.setGlobalCache(true);
        return weChatAutoReplyService.initPage(parameterMap, session);
    }

    @SchneiderRequestMapping(value = "/query_wechat_auto_reply_list")
    public Response queryAutoReplyList(HttpServletRequest request) {
        this.pageLoad(request);
        this.setGlobalCache(true);
        return weChatAutoReplyService.queryAutoReplyList(parameterMap, session);
    }

    @SchneiderRequestMapping(value = "/save_notice")
    public Response saveNotice(HttpServletRequest request) {
        this.pageLoad(request);
        return weChatAutoReplyService.saveNotice(parameterMap, session);
    }

    @SchneiderRequestMapping(value = "/modify_notice")
    public Response modifyNotice(HttpServletRequest request) {
        this.pageLoad(request);
        return weChatAutoReplyService.modifyNotice(parameterMap, session);
    }

    @SchneiderRequestMapping(value = "/delete_notice")
    public Response deleteNotice(HttpServletRequest request) {
        this.pageLoad(request);
        return weChatAutoReplyService.deleteNotice(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        this.pageLoad(request);
        this.setGlobalCache(true);
        return weChatAutoReplyService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        this.pageLoad(request);
        this.setGlobalCache(true);
        return weChatAutoReplyService.queryReport2(parameterMap);
    }

    @SchneiderRequestMapping(value = "/check_job_code")
    public Response checkJobCode(HttpServletRequest request) {
        this.pageLoad(request);
        return weChatAutoReplyService.checkJobCode(parameterMap);
    }
}
