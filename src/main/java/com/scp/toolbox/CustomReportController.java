package com.scp.toolbox;

import com.scp.toolbox.service.ICustomReportService;
import com.scp.toolbox.service.impl.CustomReportServiceImpl;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;

@RestController
@CrossOrigin
@SchneiderRequestMapping(value = "/toolbox/custom_report", parent = CustomReportServiceImpl.PARENT_CODE)
@Scope("prototype")
public class CustomReportController extends ControllerHelper {

    @Resource
    private ICustomReportService customReportService;

    @SchneiderRequestMapping(value = "/init_page")
    public Response initPage(HttpServletRequest request) {
        this.pageLoad(request);
        this.setGlobalCache(true);
        return customReportService.initPage(parameterMap, session);
    }

    @SchneiderRequestMapping(value = "/query_report_list")
    public Response queryReportList(HttpServletRequest request) {
        this.pageLoad(request);
        this.setGlobalCache(true);
        return customReportService.queryReportList(parameterMap, session);
    }

    @SchneiderRequestMapping(value = "/query_report_headers")
    public Response queryReportHeaders(HttpServletRequest request) {
        this.pageLoad(request);
        this.setGlobalCache(true);
        parameterMap.put("ip", this.getIpAddr(request));
        Response response = customReportService.queryReportHeaders(parameterMap);
        if (response.getBody() instanceof Map) {
            parameterMap.put("responseCode", 200);
            customReportService.saveReportVisitLog(parameterMap);
        } else {
            parameterMap.put("responseCode", 500);
            customReportService.saveReportVisitLog(parameterMap);
        }
        return response;
    }

    @SchneiderRequestMapping(value = "/query_report")
    public Response queryReport(HttpServletRequest request) {
        this.pageLoad(request);
        this.setGlobalCache(true);
        return customReportService.queryReport(parameterMap, "Y".equals(parameterMap.get("cacheable")));
    }

    @SchneiderRequestMapping(value = "/delete_report")
    public Response deleteReport(HttpServletRequest request) {
        this.pageLoad(request);
        return customReportService.deleteReport(parameterMap, session);
    }

    @SchneiderRequestMapping(value = "/save_new_report")
    public Response saveReport(HttpServletRequest request) {
        this.pageLoad(request);
        return customReportService.saveReport(parameterMap);
    }

    @SchneiderRequestMapping(value = "/modify_selected_report")
    public Response modifySelectedReport(HttpServletRequest request) {
        this.pageLoad(request);
        return customReportService.modifySelectedReport(parameterMap, session);
    }

    @SchneiderRequestMapping(value = "/download_report")
    public void downloadReport(HttpServletRequest request, HttpServletResponse response) {
        this.pageLoad(request);
        customReportService.downloadReport(parameterMap, response);
    }

    @SchneiderRequestMapping(value = "/query_report_config")
    public Response queryReportConfig(HttpServletRequest request) {
        this.pageLoad(request);
        return customReportService.queryReportConfig(parameterMap);
    }

    @SchneiderRequestMapping(value = "/modify_report")
    public Response modifyReport(HttpServletRequest request) {
        this.pageLoad(request);
        return customReportService.saveReport(parameterMap);
    }
}
