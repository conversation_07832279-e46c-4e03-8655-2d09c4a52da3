package com.scp.toolbox;

import com.scp.toolbox.service.IEmailNotificationService;
import com.scp.toolbox.service.impl.EmailNotificationServiceImpl;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;

@RestController
@CrossOrigin
@SchneiderRequestMapping(value = "/toolbox/email_notification", parent = EmailNotificationServiceImpl.PARENT_CODE)
@Scope("prototype")
public class EmailNotificationController extends ControllerHelper {

    @Resource
    private IEmailNotificationService emailNotificationService;


    @SchneiderRequestMapping(value = "/init_page")
    public Response initPage(HttpServletRequest request) {
        this.pageLoad(request);
        this.setGlobalCache(true);
        return emailNotificationService.initPage(parameterMap);
    }


    @SchneiderRequestMapping(value = "/query_email_notification_list")
    public Response queryEmailPushList(HttpServletRequest request) {
        this.pageLoad(request);
        this.setGlobalCache(true);
        return emailNotificationService.queryEmailNotificationList(parameterMap, session);
    }

    @SchneiderRequestMapping(value = "/save_email")
    public Response saveEmail(HttpServletRequest request) {
        this.pageLoad(request);
        return emailNotificationService.saveEmail(parameterMap, session);
    }

    @SchneiderRequestMapping(value = "/modify_email")
    public Response modifyEmail(HttpServletRequest request) {
        this.pageLoad(request);
        return emailNotificationService.modifyEmail(parameterMap, session);
    }

    @SchneiderRequestMapping(value = "/delete_email")
    public Response deleteEmail(HttpServletRequest request) {
        this.pageLoad(request);
        return emailNotificationService.deleteEmail(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        this.pageLoad(request);
        this.setGlobalCache(true);
        return emailNotificationService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        this.pageLoad(request);
        this.setGlobalCache(true);
        return emailNotificationService.queryReport2(parameterMap);
    }
}
