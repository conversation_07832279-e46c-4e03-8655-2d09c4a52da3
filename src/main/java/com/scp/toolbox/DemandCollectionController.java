package com.scp.toolbox;

import com.scp.inventory.MonthlySliceReportController;
import com.scp.toolbox.service.IDemandCollectionService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/toolbox/demand_collection", parent = DemandCollectionController.PARENT_CODE)
public class DemandCollectionController extends ControllerHelper {

    public static final String PARENT_CODE = "menuA84";

    @Resource
    private Response response;

    @Resource
    private IDemandCollectionService demandCollectionService;

    @SchneiderRequestMapping("/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        return demandCollectionService.initPage(parameterMap);
    }

    @SchneiderRequestMapping("/query_report1_details")
    public Response queryReport1Details(HttpServletRequest request) {
        super.pageLoad(request);
        return demandCollectionService.queryReport1Details(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping("/download_report1_details")
    public void downloadReport1Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        demandCollectionService.downloadReport1Details(session.getUserid(), parameterMap, response);
    }

    @SchneiderRequestMapping("/save_report1_details")
    public Response saveReport1Details(HttpServletRequest request) {
        super.pageLoad(request);
        return demandCollectionService.saveReport1Details(session.getUserid(), parameterMap);
    }
}
