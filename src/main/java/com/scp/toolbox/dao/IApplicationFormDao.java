package com.scp.toolbox.dao;

import com.scp.toolbox.bean.ApplicationFormConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IApplicationFormDao {

    List<Map<String, Object>> queryApplicationConfig(@Param("userid") String userid);

    int queryApplicationAuth(@Param("userid") String userid);

    int updateConfig(@Param("config") ApplicationFormConfig config, @Param("configStr") String configStr, @Param("userid") String userid, @Param("calc") String calc, @Param("clear") String clear, @Param("orderBy") String orderBy,@Param("memo") String memo,@Param("excelType") String excelType);

    int saveConfig(@Param("config") ApplicationFormConfig config, @Param("configStr") String configStr, @Param("userid") String userid, @Param("calc") String calc, @Param("clear") String clear, @Param("orderBy") String orderBy,@Param("memo") String memo,@Param("excelType") String excelType);

    List<Map<String, Object>> queryMailtoByKeywords(Map<String, Object> parameterMap);

    String queryConfig(Map<String, Object> parameterMap);

    void deleteConfig(Map<String, Object> parameterMap);

    void saveMailLog(@Param("seqid") String seqid, @Param("id") String id, @Param("userid") String userid, @Param("body") String body, @Param("path") String path);

    String queryLastSentTime(@Param("userid") String userid, @Param("id") String id);

    void executeUpdate(@Param("sql") String sql);

    int queryResultCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> fileList(@Param("userid") String userid);

    List<Map<String, Object>> sqlTemplate(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryResult(Map<String, Object> parameterMap);

    List<Map<String, String>> queryGroupOpts();
}
