package com.scp.toolbox.dao;

import com.starter.context.bean.scptable.ScpTableCell;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IDemandCollectionDao {

    List<Map<String, String>> initPage(Map<String, Object> parameterMap);

    List<Map<String, String>> queryQuestionList();

    List<String> queryAvailableColumns();

    String queryPageAdmin(@Param("userid") String userid, @Param("parentCode") String parentCode);

    int queryReport1DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1Details(Map<String, Object> parameterMap);

    int saveReport1Details(String uuid, List<ScpTableCell> updates, String userid);

    int createReport1ByTable(@Param("headers") List<String> headers, @Param("creates") List<Map<String, Object>> creates, @Param("userid") String userid);

    int deleteReport1ByTable(@Param("deletes") List<String> deletes, @Param("userid") String userid, @Param("isAdmin") boolean isAdmin);

    int updateReport1ByTable(@Param("rowid") String rowid, @Param("updates") List<ScpTableCell> updates, @Param("userid") String userid, @Param("isAdmin") boolean isAdmin);

}
