package com.scp.toolbox.dao;

import com.starter.context.bean.scptable.ScpTableCell;
import org.apache.ibatis.annotations.Mapper;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface ICsfValidationDao {

    List<Map<String, String>> initPage(Map<String, Object> parameterMap);

    List<HashMap<String, String>> queryDateOptions(Map<String, Object> parameterMap);

    List<HashMap<String, String>> queryDefaultFilters(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1(Map<String, Object> parameterMap);

    int queryReport2Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport2(Map<String, Object> parameterMap);

    void insertReport2DataTemp(List<Map<String, Object>> data);

    void mergeReport2Data(String userid);

    int queryReport1DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1Details(Map<String, Object> parameterMap);

    List<Integer> queryForecastVersionOptions(Map<String, Object> parameterMap);

    int saveReport1Details(List<ScpTableCell> updates, String userid);

    int deleteReport1Details(List<String> deletes, String userid);

    int updateReport1Details(String material,
                                    String salesOrganization,
                                    String customerCode,
                                    String salesGroup, List<ScpTableCell> updates, String userid);

    int queryReport1DetailsExists(String material,
                                  String salesOrganization,
                                  String customerCode,
                                  String salesGroup, String userid);

}
