<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.toolbox.dao.IBroadcastHistoryDao">
	<sql id="filter">
		TRUNC(T.CREATED_DATE, 'DD') BETWEEN TO_DATE(#{report1DateRange[0], jdbcType=VARCHAR}, 'YYYY/MM/DD')
		AND TO_DATE(#{report1DateRange[1], jdbcType=VARCHAR}, 'YYYY/MM/DD')
		<if test="_filters != null and _filters != ''.toString()">
			and ${_filters}
		</if>
	</sql>

	<select id="initPage" resultType="java.util.Map">
		SELECT * FROM SCPA.SY_DIALOG_CONFIG_FILTER_V
		ORDER BY CATEGORY, NAME
	</select>

	<select id="queryBroadcastConfig" resultType="java.util.HashMap">
		SELECT URL,
		       REMIND_SUBJECT,
		       VALID_FROM,
		       VALID_TO,
		       FREQUENCY,
		       REMIND_COUNT,
		       DESCRIPTION,
			   CREATED_BY_SESA,
			   CREATED_BY_NAME,
 			   CREATED_DATE,
		       ROW_ID,
			   PAGE_NAME
		FROM ${SCPA.SY_DIALOG_CONFIG_V} T
		<where>
			<include refid="filter"/>
		</where>
		ORDER BY CREATED_DATE DESC
		OFFSET #{page.start,jdbcType=INTEGER} ROWS FETCH NEXT #{page.length,jdbcType=INTEGER} ROWS ONLY
	</select>

	<select id="queryBroadcastConfigCount" parameterType="java.util.Map" resultType="java.lang.Integer">
		SELECT COUNT(1)
		FROM (
			SELECT T.*
			FROM ${SCPA.SY_DIALOG_CONFIG_V} T
			<where>
				<include refid="filter"/>
			</where>
		)
	</select>
</mapper>
