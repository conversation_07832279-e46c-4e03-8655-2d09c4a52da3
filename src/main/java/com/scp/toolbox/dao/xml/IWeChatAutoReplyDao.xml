<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.toolbox.dao.IWeChatAutoReplyDao">
    <select id="queryAutoReplyList" resultType="com.scp.toolbox.bean.TreeData">
        SELECT T.ID AS KEY,
               LOWER(T.JOB_CODE) || ' - ' || T.NAME AS LABEL,
               T.GROUP_NAME AS GROUPS,
               DECODE(STATUS, 'Inactive', 'inactive') AS SUB_LABEL
         FROM REMOTE_NOTIFICATION_WECHAT_AUTO_REPLY T
         <if test="authType != 'ADMIN'.toString()">
            WHERE (T.CREATE_BY$ = #{userid, jdbcType=VARCHAR} OR T.OWNER_SESA = #{userid, jdbcType=VARCHAR})
         </if>
        ORDER BY T.GROUP_NAME, T.NAME
    </select>

    <select id="queryReportIDList" resultType="java.util.Map">
        SELECT T.TITLE, T.REPORT_ID FROM SY_REPORT_DESCRIPTION T ORDER BY T.REPORT_ID
    </select>

    <select id="checkJobCode" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM REMOTE_NOTIFICATION_WECHAT_AUTO_REPLY T WHERE T.JOB_CODE = #{jobCode,jdbcType=VARCHAR}
    </select>

    <select id="queryuserOpts" resultType="java.util.Map">
        SELECT USER_NAME, SESA_CODE FROM SY_USER_MASTER_DATA
    </select>

    <select id="queryExistsGroup" resultType="java.lang.String">
        SELECT DISTINCT GROUP_NAME FROM REMOTE_NOTIFICATION_WECHAT_AUTO_REPLY
    </select>

    <insert id="saveNotice" parameterType="java.util.Map">
        DECLARE
            CONFIG_COLB CLOB := #{config, jdbcType=CLOB};
        BEGIN
            INSERT INTO REMOTE_NOTIFICATION_WECHAT_AUTO_REPLY
                    (ID, JOB_CODE, NAME, GROUP_NAME, STATUS, REMARKS, CONFIG, OWNER_SESA, CREATE_BY$, CREATE_DATE$)
            VALUES
            (#{id,jdbcType=VARCHAR}, #{jobCode,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{groups,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR},
            #{remarks, jdbcType=VARCHAR}, CONFIG_COLB, #{userid, jdbcType=VARCHAR}, #{userid, jdbcType=VARCHAR}, sysdate);
        END;
    </insert>

    <update id="modifyNotice" parameterType="java.util.Map">
        DECLARE
            CONFIG_COLB CLOB := #{config, jdbcType=CLOB};
        BEGIN
            UPDATE REMOTE_NOTIFICATION_WECHAT_AUTO_REPLY
               SET NAME = #{name, jdbcType=VARCHAR},
                   JOB_CODE = #{jobCode, jdbcType=VARCHAR},
                   GROUP_NAME = #{groups, jdbcType=VARCHAR},
                   CONFIG = CONFIG_COLB,
                   STATUS = #{status, jdbcType=VARCHAR},
                   REMARKS = #{remarks, jdbcType=VARCHAR},
                   <if test="owner != null and owner != ''">
                        OWNER_SESA = #{owner, jdbcType=VARCHAR},
                   </if>
                   UPDATE_BY$ = #{userid, jdbcType=VARCHAR},
                   UPDATE_DATE$ = sysdate
             WHERE ID = #{id, jdbcType=VARCHAR};
        END;
    </update>

    <delete id="deleteNotice" parameterType="java.util.Map">
        DELETE FROM REMOTE_NOTIFICATION_WECHAT_AUTO_REPLY T WHERE T.ID = #{id, jdbcType=VARCHAR}
    </delete>

    <select id="queryReport1" resultType="java.util.Map">
         SELECT ID,
                T.JOB_CODE,
                NAME,
                GROUP_NAME,
                CONFIG,
                TO_CHAR(LAST_REPLY_TIME, 'YYYY/MM/DD HH24:MI:SS') LAST_REPLY_TIME,
                LAST_REPLY_RESULT_CODE,
                LAST_SENT_IMG_PATH,
                T.OWNER_SESA AS SESA_CODE,
                UMD.USER_NAME,
                T.STATUS,
                REMARKS
         FROM REMOTE_NOTIFICATION_WECHAT_AUTO_REPLY T LEFT JOIN SY_USER_MASTER_DATA UMD ON T.OWNER_SESA = UMD.SESA_CODE
        WHERE T.ID = #{id, jdbcType=VARCHAR}
    </select>

    <sql id="queryReport2SQL">
        SELECT TO_CHAR(REPLY_TIME, 'YYYY/MM/DD HH24:MI:SS') REPLY_TIME,
               REPLY_TO,
               RESULT_CODE,
               ASK_TEXT
          FROM REMOTE_NOTIFICATION_WECHAT_AUTO_REPLY_LOGS T
         WHERE T.PARENT_ID = #{id, jdbcType=VARCHAR}
         ORDER BY REPLY_TIME DESC
    </sql>

    <select id="queryReport2Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport2SQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport2" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport2SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryAuthDetails" resultType="java.lang.String">
        SELECT AUTH_DETAILS
          FROM SY_MENU_AUTH
         WHERE LOWER(USER_ID) = LOWER(#{userid, jdbcType=VARCHAR})
           AND MENU_CODE = #{menuCode, jdbcType=VARCHAR}
    </select>
</mapper>
