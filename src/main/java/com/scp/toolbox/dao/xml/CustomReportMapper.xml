<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.toolbox.dao.ICustomReportDao">
    <select id="querySharedTo" resultType="java.util.Map">
        SELECT '[ALL USER]' AS "NAME", '[ALL USER]' AS "VALUE" FROM DUAL
        UNION ALL
        SELECT T.USER_NAME AS "NAME", T.SESA_CODE AS "VALUE" FROM SY_USER_MASTER_DATA T
    </select>

    <select id="queryExistsGroup" resultType="java.lang.String">
        SELECT DISTINCT GROUP_NAME FROM SCPA.CUSTOM_REPORT ORDER BY GROUP_NAME
    </select>

    <select id="queryReportList" resultType="com.scp.toolbox.bean.TreeData">
        SELECT T.REPORT_ID AS KEY,
               T.REPORT_NAME AS LABEL,
               T.GROUP_NAME AS GROUPS
         FROM SCPA.CUSTOM_REPORT T
         <if test="isAdmin == false">
            WHERE (T.AUTH_TYPE = 'PUBLIC'
                    OR EXISTS(SELECT 1 FROM SCPA.CUSTOM_REPORT_AUTH T2 WHERE T.REPORT_ID = T2.REPORT_ID AND T2.USER_ID = #{userid, jdbcType=VARCHAR})
                    OR T.CREATE_BY$ = #{userid, jdbcType=VARCHAR})
         </if>
        ORDER BY T.GROUP_NAME, T.REPORT_NAME
    </select>

    <select id="querySQLByReportID" resultType="java.lang.String">
        SELECT SQL FROM SCPA.CUSTOM_REPORT WHERE REPORT_ID = #{selectedId, jdbcType=VARCHAR}
    </select>

    <sql id="queryReportSQL">
		${sql}
	</sql>

	<select id="queryReportCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReportSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReportSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReportConfig" resultType="java.util.Map">
        SELECT REPORT_ID, REPORT_NAME, GROUP_NAME, SQL, AUTH_TYPE, CREATE_BY$, ENABLE_CACHE FROM SCPA.CUSTOM_REPORT WHERE REPORT_ID = #{selectedId, jdbcType=VARCHAR}
    </select>

    <delete id="deleteReport">
        BEGIN
            DELETE FROM SCPA.CUSTOM_REPORT WHERE REPORT_ID = #{selectedId, jdbcType=VARCHAR}
            <if test="isAdmin == false">
                AND CREATE_BY$ = #{session.userid, jdbcType=VARCHAR}
            </if>;

            DELETE FROM SCPA.CUSTOM_REPORT_AUTH T WHERE T.REPORT_ID NOT IN (SELECT T0.REPORT_ID FROM SCPA.CUSTOM_REPORT T0);
        END;
    </delete>

    <insert id="saveSharedTo">
        INSERT INTO SCPA.CUSTOM_REPORT_AUTH (REPORT_ID, USER_ID)
        <foreach collection="shareTo" separator=" union all " item="item">
            SELECT #{reportID, jdbcType=VARCHAR}, #{item, jdbcType=VARCHAR} from dual
        </foreach>
    </insert>

    <insert id="saveReport">
        insert into SCPA.CUSTOM_REPORT
        (REPORT_ID, REPORT_NAME, GROUP_NAME, SQL, CREATE_BY$, CREATE_DATE$, AUTH_TYPE, ENABLE_CACHE)
        values
        (#{reportID, jdbcType=VARCHAR}, #{name, jdbcType=VARCHAR}, #{groups, jdbcType=VARCHAR},
         #{sql, jdbcType=VARCHAR}, #{session.userid, jdbcType=VARCHAR}, sysdate, #{authType, jdbcType=VARCHAR},
         #{cacheable, jdbcType=VARCHAR})
    </insert>

    <insert id="saveReportVisitLog">
        INSERT INTO SCPA.CUSTOM_REPORT_VISIT_LOGS (REPORT_ID, USERID, LOGIN_IP, VISIT_TIME, RESPONSE_CODE)
        VALUES
        (#{selectedId, jdbcType=VARCHAR}, #{session.userid, jdbcType=VARCHAR}, #{ip, jdbcType=VARCHAR}, SYSDATE, #{responseCode, jdbcType=VARCHAR})
    </insert>

    <select id="queryPageAdmin" resultType="java.lang.Integer">
        SELECT COUNT(1) CNT
          FROM SY_MENU_AUTH T
         WHERE UPPER(T.AUTH_DETAILS) = 'ADMIN'
           AND T.USER_ID = #{userid, jdbcType=VARCHAR}
           AND T.MENU_CODE = #{parentCode, jdbcType=VARCHAR}
    </select>

    <select id="querySharedToByReportID" resultType="java.lang.String">
        SELECT USER_ID FROM SCPA.CUSTOM_REPORT_AUTH WHERE REPORT_ID = #{selectedId, jdbcType=VARCHAR}
    </select>

    <delete id="deleteSharedToByReportID">
        DELETE FROM SCPA.CUSTOM_REPORT_AUTH WHERE REPORT_ID = #{reportID, jdbcType=VARCHAR}
    </delete>

    <update id="modifySelectedReport">
        UPDATE SCPA.CUSTOM_REPORT
           SET REPORT_NAME = #{name, jdbcType=VARCHAR},
               GROUP_NAME = #{groups, jdbcType=VARCHAR},
               SQL = #{sql, jdbcType=VARCHAR},
               UPDATE_BY$ = #{session.userid, jdbcType=VARCHAR},
               UPDATE_DATE$ = sysdate,
               AUTH_TYPE = #{authType, jdbcType=VARCHAR},
               ENABLE_CACHE = #{cacheable, jdbcType=VARCHAR}
         WHERE REPORT_ID = #{reportID, jdbcType=VARCHAR}
         <if test="isAdmin == false">
           AND CREATE_BY$ = #{session.userid, jdbcType=VARCHAR}
         </if>
    </update>
</mapper>
