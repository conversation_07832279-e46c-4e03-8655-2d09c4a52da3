<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.toolbox.dao.IEmailNotificationDao">
    <insert id="saveEmail">
        INSERT INTO REMOTE_NOTIFICATION_EMAIL
        (ID, NAME, GROUP_NAME, CONFIG, CREATE_BY$, CREATE_DATE$, STATUS, REMARKS, MONTH, DAY, HOUR, DAY_TYPE)
        VALUES
            (#{id, jdbcType=VARCHAR}, #{name, jdbcType=VARCHAR}, #{groups, jdbcType=VARCHAR}, #{config, jdbcType=VARCHAR},
             #{userid, jdbcType=VARCHAR}, sysdate, #{status, jdbcType=VARCHAR}, #{remarks, jdbcType=VARCHAR}, #{month, jdbcType=VARCHAR},
             #{day, jdbcType=VARCHAR}, #{hour, jdbcType=VARCHAR}, #{dayType, jdbcType=VARCHAR})
    </insert>
    <update id="modifyEmail">
        UPDATE REMOTE_NOTIFICATION_EMAIL
        SET NAME = #{name, jdbcType=VARCHAR},
            GROUP_NAME = #{groups, jdbcType=VARCHAR},
            CONFIG = #{config, jdbcType=VARCHAR},
            STATUS = #{status, jdbcType=VARCHAR},
            REMARKS = #{remarks, jdbcType=VARCHAR},
            MONTH = #{month, jdbcType=VARCHAR},
            DAY = #{day, jdbcType=VARCHAR},
            HOUR = #{hour, jdbcType=VARCHAR},
            DAY_TYPE = #{dayType, jdbcType=VARCHAR},
            UPDATE_BY$ = #{userid, jdbcType=VARCHAR},
            UPDATE_DATE$ = sysdate
        WHERE ID = #{id, jdbcType=VARCHAR}
    </update>
    <delete id="deleteEmail">
        DELETE FROM REMOTE_NOTIFICATION_EMAIL T WHERE T.ID = #{id, jdbcType=VARCHAR}
    </delete>

    <sql id="queryReport2SQL">
        SELECT TO_CHAR(SEND_TIME, 'YYYY/MM/DD HH24:MI:SS') SEND_TIME,
               RECIPIENT,
               CC,
               RESULT_CODE,
               RESULT_TEXT
        FROM REMOTE_NOTIFICATION_EMAIL_LOGS T
        WHERE T.EMAIL_PUSH_ID = #{id, jdbcType=VARCHAR}
        ORDER BY SEND_TIME DESC
    </sql>

    <select id="queryReport2Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport2SQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport2" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport2SQL"/>
        <include refid="global.select_footer"/>
    </select>


    <select id="queryAuthDetails" resultType="java.lang.String">
        SELECT AUTH_DETAILS
        FROM SY_MENU_AUTH
        WHERE LOWER(USER_ID) = LOWER(#{userid, jdbcType=VARCHAR})
          AND MENU_CODE = #{menuCode, jdbcType=VARCHAR}
    </select>
    <select id="queryEmailNotificationList" resultType="com.scp.toolbox.bean.TreeData">
        SELECT T.ID AS KEY,
        T.NAME AS LABEL,
        T.GROUP_NAME AS GROUPS
        FROM REMOTE_NOTIFICATION_EMAIL  T
        <if test="authType != 'ADMIN'.toString()">
            WHERE T.CREATE_BY$ = #{userid, jdbcType=VARCHAR}
        </if>
        ORDER BY T.GROUP_NAME, T.NAME
    </select>
    <select id="queryReportIDList" resultType="java.util.Map">
        SELECT T.TITLE, T.REPORT_ID FROM SY_REPORT_DESCRIPTION T ORDER BY T.REPORT_ID
    </select>
    <select id="queryEmailUserList" resultType="java.util.Map">
        SELECT USER_NAME,EMAIL
        FROM SY_USER_MASTER_DATA T
        where EMAIL is not null
        ORDER BY T.USER_NAME
    </select>
    <select id="queryReport1" resultType="java.util.Map">
        SELECT ID,
               NAME,
               GROUP_NAME,
               CONFIG,
               TO_CHAR(LAST_EXEC_TIME, 'YYYY/MM/DD HH24:MI:SS') LAST_EXEC_TIME,
               LAST_EXEC_RESULT_CODE,
               LAST_EXEC_RESULT,
               LAST_SEND_IMG_PATH,
            MONTH,
            DAY,
            HOUR,
            T.CREATE_BY$ AS SESA_CODE,
            UMD.USER_NAME,
            T.STATUS,
            T.LAST_SEND_RECIPIENT,
            T.LAST_SEND_CC,
            REMARKS,
            DAY_TYPE
        FROM REMOTE_NOTIFICATION_EMAIL T LEFT JOIN SY_USER_MASTER_DATA UMD ON T.CREATE_BY$ = UMD.SESA_CODE
        WHERE T.ID = #{id, jdbcType=VARCHAR}
    </select>
    <select id="queryExistsGroup" resultType="java.lang.String">
        SELECT DISTINCT GROUP_NAME FROM REMOTE_NOTIFICATION_EMAIL
    </select>
</mapper>
