<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.toolbox.dao.IDemandCollectionDao">
	<sql id="demandCollectionFilter">
		<if test="_filters != null and _filters != ''.toString()">
			and ${_filters}
		</if>
	</sql>

	<select id="initPage" resultType="java.util.Map">
		SELECT /*+ parallel */ NAME,
		                       CATEGORY
		FROM SCPA.DEMAND_COLLECTION_FILTER_V
		ORDER BY CATEGORY,NAME
	</select>

	<select id="queryReport1DetailsCount" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="report1DetailsSQL"/>
		<include refid="global.count_footer"/>
	</select>

	<select id="queryReport1Details" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		<include refid="report1DetailsSQL"/>
		<include refid="global.select_footer"/>
	</select>

	<select id="queryAvailableColumns" resultType="java.lang.String">
		SELECT COLUMN_NAME
		FROM USER_TAB_COLS
		WHERE TABLE_NAME = 'DEMAND_COLLECTION_V' ORDER BY COLUMN_NAME
	</select>

	<sql id="report1DetailsSQL">
		SELECT
			ROWIDTOCHAR(t.rowid) row_id,
			T.*
		FROM ${SCPA.DEMAND_COLLECTION_V} T
		<where>
			<include refid="demandCollectionFilter"/>
		</where>
		ORDER BY QUESTION_TYPE, QUESTION
	</sql>

	<select id="queryPageAdmin" resultType="java.lang.String">
		select AUTH_DETAILS
		from SY_MENU_AUTH t
		where t.USER_ID = #{userid, jdbcType=VARCHAR} and t.MENU_CODE = #{parentCode, jdbcType=VARCHAR}
	</select>

	<insert id="createReport1ByTable">
		insert into MR3_DEMAND_COLLECTION
		(<foreach collection="headers" item="header" separator=",">
			${header}
		</foreach>, QUESTION_OWNER_SESA, create_by$, create_date$, QUESTION_OWNER_NAME
		)
		<foreach collection="creates" item="list" separator=" union all ">
			select <foreach collection="headers" item="header" separator=",">
				<choose>
					<when test="header.indexOf('MONTH') != -1 ">
						#{list.${header}, jdbcType=DOUBLE}
					</when>
					<otherwise>
						#{list.${header}, jdbcType=VARCHAR}
					</otherwise>
				</choose>
			</foreach>, #{userid,jdbcType=VARCHAR}, #{userid,jdbcType=VARCHAR}, sysdate, UM.USER_NAME
			from dual T LEFT JOIN SY_USER_MASTER_DATA UM ON UM.SESA_CODE = #{userid,jdbcType=VARCHAR}
		</foreach>
	</insert>

	<delete id="deleteReport1ByTable">
		delete from MR3_DEMAND_COLLECTION where rowid in
		<foreach collection="deletes" open="(" close=")" separator="," item="item">#{item, jdbcType=VARCHAR}</foreach>
		<if test="isAdmin == false">
			AND QUESTION_OWNER_SESA = #{userid, jdbcType=VARCHAR}
		</if>
	</delete>

	<update id="updateReport1ByTable">
		update MR3_DEMAND_COLLECTION
		SET
		<foreach collection="updates" item="col" separator=",">
			${col.key} = #{col.value,jdbcType=VARCHAR}
		</foreach>,
		update_by$ = #{userid,jdbcType=VARCHAR},
		update_date$ = sysdate
		where rowid = #{rowid,jdbcType=VARCHAR}
		<if test="isAdmin == false">
			AND QUESTION_OWNER_SESA = #{userid, jdbcType=VARCHAR}
		</if>
	</update>

	<select id="queryQuestionList" resultType="java.util.Map">
		SELECT QUESTION, QUESTION_TYPE
		FROM SCPA.MR3_DEMAND_COLLECTION_QUESTIONS
		ORDER BY QUESTION_TYPE
	</select>
</mapper>
