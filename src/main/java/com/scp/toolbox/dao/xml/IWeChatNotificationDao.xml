<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.toolbox.dao.IWeChatNotificationDao">
    <select id="queryReportIDList" resultType="java.util.Map">
        SELECT T.TITLE, T.REPORT_ID FROM SY_REPORT_DESCRIPTION T ORDER BY T.REPORT_ID
    </select>

    <select id="queryWeChatNotificationList" resultType="com.scp.toolbox.bean.TreeData">
        SELECT T.ID AS KEY,
               T.NAME AS LABEL,
               T.GROUP_NAME AS GROUPS,
               DECODE(STATUS, 'Inactive', 'inactive') AS SUB_LABEL
         FROM REMOTE_NOTIFICATION_WECHAT T
         <if test="authType != 'ADMIN'.toString()">
            WHERE T.CREATE_BY$ = #{userid, jdbcType=VARCHAR}
         </if>
        ORDER BY T.GROUP_NAME, T.NAME
    </select>

    <select id="queryExistsGroup" resultType="java.lang.String">
        SELECT DISTINCT GROUP_NAME FROM REMOTE_NOTIFICATION_WECHAT
    </select>

    <insert id="saveNotice" parameterType="java.util.Map">
        INSERT INTO REMOTE_NOTIFICATION_WECHAT
        (ID, NAME, GROUP_NAME, CONFIG, CREATE_BY$, CREATE_DATE$, STATUS, REMARKS, MONTH, DAY, HOUR, MINUTE, DAY_TYPE)
        VALUES
        (#{id, jdbcType=VARCHAR}, #{name, jdbcType=VARCHAR}, #{groups, jdbcType=VARCHAR}, #{config, jdbcType=VARCHAR},
         #{userid, jdbcType=VARCHAR}, sysdate, #{status, jdbcType=VARCHAR}, #{remarks, jdbcType=VARCHAR}, #{month, jdbcType=VARCHAR},
         #{day, jdbcType=VARCHAR}, #{hour, jdbcType=VARCHAR}, #{minute, jdbcType=VARCHAR}, #{dayType, jdbcType=VARCHAR})
    </insert>

    <update id="modifyNotice" parameterType="java.util.Map">
        UPDATE REMOTE_NOTIFICATION_WECHAT
           SET NAME = #{name, jdbcType=VARCHAR},
               GROUP_NAME = #{groups, jdbcType=VARCHAR},
               CONFIG = #{config, jdbcType=VARCHAR},
               STATUS = #{status, jdbcType=VARCHAR},
               REMARKS = #{remarks, jdbcType=VARCHAR},
               MONTH = #{month, jdbcType=VARCHAR},
               DAY = #{day, jdbcType=VARCHAR},
               HOUR = #{hour, jdbcType=VARCHAR},
               MINUTE = #{minute, jdbcType=VARCHAR},
               DAY_TYPE = #{dayType, jdbcType=VARCHAR},
               UPDATE_BY$ = #{userid, jdbcType=VARCHAR},
               UPDATE_DATE$ = sysdate
         WHERE ID = #{id, jdbcType=VARCHAR}
    </update>

    <delete id="deleteNotice" parameterType="java.util.Map">
        DELETE FROM REMOTE_NOTIFICATION_WECHAT T WHERE T.ID = #{id, jdbcType=VARCHAR}
    </delete>

    <select id="queryReport1" resultType="java.util.Map">
         SELECT ID,
                NAME,
                GROUP_NAME,
                CONFIG,
                TO_CHAR(LAST_EXEC_TIME, 'YYYY/MM/DD HH24:MI:SS') LAST_EXEC_TIME,
                LAST_EXEC_RESULT_CODE,
                LAST_EXEC_RESULT,
                LAST_SENT_IMG_PATH,
                LAST_SEND_TO,
                MONTH,
                DAY,
                HOUR,
                MINUTE,
                T.CREATE_BY$ AS SESA_CODE,
                UMD.USER_NAME,
                T.STATUS,
                REMARKS,
                DAY_TYPE
         FROM REMOTE_NOTIFICATION_WECHAT T LEFT JOIN SY_USER_MASTER_DATA UMD ON T.CREATE_BY$ = UMD.SESA_CODE
        WHERE T.ID = #{id, jdbcType=VARCHAR}
    </select>

    <sql id="queryReport2SQL">
        SELECT TO_CHAR(SEND_TIME, 'YYYY/MM/DD HH24:MI:SS') SEND_TIME,
               SEND_TO,
               RESULT_CODE,
               RESULT_TEXT
          FROM REMOTE_NOTIFICATION_WECHAT_LOGS T
         WHERE T.NOTICE_ID = #{id, jdbcType=VARCHAR}
         ORDER BY SEND_TIME DESC
    </sql>

    <select id="queryReport2Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport2SQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport2" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport2SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryAuthDetails" resultType="java.lang.String">
        SELECT AUTH_DETAILS
          FROM SY_MENU_AUTH
         WHERE LOWER(USER_ID) = LOWER(#{userid, jdbcType=VARCHAR})
           AND MENU_CODE = #{menuCode, jdbcType=VARCHAR}
    </select>
</mapper>
