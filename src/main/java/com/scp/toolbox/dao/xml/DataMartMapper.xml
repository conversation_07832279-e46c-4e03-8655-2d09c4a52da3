<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.toolbox.dao.IDataMartDao">
	<select id="queryExistsGroup" resultType="java.lang.String">
		select distinct groups from DATA_MART_CONFIG where groups is not null order by groups
	</select>

	<select id="queryDBConfigList" resultType="java.util.Map">
		SELECT ROWIDTOCHAR(ROWID) ROW_ID, NAME, TYPE FROM DATA_MART_DB_CONFIG
	</select>

    <select id="queryAuthDetails" resultType="java.lang.String">
		select auth_details from SY_MENU_AUTH where lower(user_id) = lower(#{userid, jdbcType=VARCHAR}) and menu_code = #{menuCode, jdbcType=VARCHAR}
	</select>

	<select id="queryRootTaskList" resultType="com.scp.toolbox.bean.TreeData">
		SELECT ROWIDTOCHAR(ROWID) KEY,
			   DISPLAY_NAME AS LABEL,
			   DECODE(STATUS, 'N', 'inactive') AS SUB_LABEL,
			   GROUPS
		  FROM DATA_MART_CONFIG
		  <if test="authType != 'ADMIN'.toString()">
		 	where CREATE_BY$ = #{userid,jdbcType=VARCHAR}
		  </if>
		 ORDER BY GROUPS
	</select>

	<select id="querySubTaskList" resultType="java.util.Map">
		SELECT ROWIDTOCHAR(ROWID) KEY,
			   DISPLAY_NAME,
			   GROUPS
		  FROM DATA_MART_CONFIG
		 WHERE GROUPS LIKE #{key,jdbcType=VARCHAR} || '%'
		       <if test="authType != 'ADMIN'.toString()">
		       	AND CREATE_BY$ = #{userid,jdbcType=VARCHAR}
		       </if>
		 ORDER BY DISPLAY_NAME
	</select>

	<select id="queryReport1" resultType="java.util.Map">
		SELECT DECODE(NVL(MM.TOTAL,0) , 0, '--', MM.SUCCESS || '/' || MM.TOTAL)                         AS WIDGET1,
			   ROUND(DECODE(NVL(MM.TIME_COST,0), 0, 0, MM.TIME_COST / 60), 1)                           AS WIDGET2,
			   ROUND(DECODE(NVL(MM.TIME_COST,0), 0, 0, MM.LINES_OUTPUT / MM.TIME_COST), 1)          	AS WIDGET3,
			   LINES_INPUT                                                                      		AS WIDGET4,
			   ROUND(SIZE_IN_B / 1024 / 1024, 1)                                                		AS WIDGET5,
			   DECODE(NVL(LINES_INPUT,0), 0, '--', ROUND(LINES_OUTPUT / LINES_INPUT * 100, 1) || '%')   AS WIDGET6
		FROM (
				 SELECT SUM(LINES_INPUT)               LINES_INPUT,
						SUM(LINES_OUTPUT)              LINES_OUTPUT,
						COUNT(1)                       TOTAL,
						SUM(DECODE(ERRORS, 0, 1, 0))   SUCCESS,
						SUM(LINES_INPUT * AVG_ROW_LEN) SIZE_IN_B,
						SUM(TIME_COST / 1000)          TIME_COST
				 FROM (
					 SELECT T0.LOG_FIELD,
							T0.ERRORS,
							T0.ID_BATCH,
							T0.STATUS,
							T0.TRANS_NAME,
							T0.TIME_COST,
							T0.START_DATE,
							T0.END_DATE,
							LINES_INPUT,
							LINES_OUTPUT
					   FROM DATA_MART_LOG T0
					 ) T LEFT JOIN (
					 SELECT AVG(AVG_ROW_LEN) AVG_ROW_LEN
					 FROM USER_TABLES
					 WHERE TABLE_NAME LIKE '%_V'
					   AND TABLE_NAME NOT LIKE '%_FILTER_V'
					   AND TABLE_NAME NOT LIKE 'SY_%'
					   AND AVG_ROW_LEN > 0
				 ) T2 ON 1 = 1
				 WHERE T.START_DATE BETWEEN TO_DATE(#{date, jdbcType=VARCHAR} || ' 00:00:00', 'YYYY/MM/DD HH24:MI:SS') AND TO_DATE(#{date, jdbcType=VARCHAR} || ' 23:59:59', 'YYYY/MM/DD HH24:MI:SS')
			 ) MM
	</select>

	<select id="queryReport2" resultType="java.util.Map">
		WITH TEMP AS (
			SELECT TO_CHAR(END_DATE, 'HH24') TIME_NAME, SUM(${columns} / 1000) VALUE, T2.SOURCE
			FROM DATA_MART_LOG T
				 LEFT JOIN (SELECT T0.ID, MAX(DECODE(T0.CONNECTION, 'SCP02_WRITER', 1, 0)) SOURCE FROM DATA_MART_STEPS T0 GROUP BY T0.ID) T2
                       ON T.TRANS_NAME = T2.ID
			WHERE T.START_DATE BETWEEN TO_DATE(#{date, jdbcType=VARCHAR} || ' 00:00:00', 'YYYY/MM/DD HH24:MI:SS') AND TO_DATE(#{date, jdbcType=VARCHAR} || ' 23:59:59', 'YYYY/MM/DD HH24:MI:SS')
			GROUP BY TO_CHAR(END_DATE, 'HH24'), T2.SOURCE
			ORDER BY TO_NUMBER(TO_CHAR(END_DATE, 'HH24'))
		)

		SELECT TEMP.TIME_NAME, TEMP.VALUE / KK.MAX_VALUE AS RATE, SOURCE
		FROM TEMP, (SELECT MAX(VALUE) MAX_VALUE FROM TEMP) KK
	</select>

	<select id="queryReport3" resultType="java.util.Map">
		SELECT T2.TRANS_NAME, T3.SOURCE, T2.GROUPS || '.' || T2.DISPLAY_NAME AS YAXIS, SUM(${columns} / 1000) XAXIS
		FROM DATA_MART_LOG T
				 INNER JOIN DATA_MART_CONFIG T2 ON T.TRANS_NAME = T2.TRANS_NAME
				  LEFT JOIN (SELECT T0.ID, MAX(DECODE(T0.CONNECTION, 'SCP02_WRITER', 1, 0)) SOURCE FROM DATA_MART_STEPS T0 GROUP BY T0.ID) T3
                   ON T.TRANS_NAME = T3.ID
		WHERE T.START_DATE BETWEEN TO_DATE(#{date, jdbcType=VARCHAR} || ' 00:00:00', 'YYYY/MM/DD HH24:MI:SS') AND TO_DATE(#{date, jdbcType=VARCHAR} || ' 23:59:59', 'YYYY/MM/DD HH24:MI:SS')
		GROUP BY T2.TRANS_NAME, T2.DISPLAY_NAME, T2.GROUPS, T3.SOURCE
		ORDER BY XAXIS DESC
			FETCH NEXT 15 ROWS ONLY
	</select>

    <select id="queryReport4" resultType="java.util.Map">
		SELECT T.MONTH,
			   T.DAY,
			   T.HOUR,
			   T.MINUTE,
			   T.DISPLAY_NAME,
			   T3.SESA_CODE,
			   T3.USER_NAME,
			   T2.LOG_FIELD,
			   T2.ERRORS,
			   T2.ID_BATCH,
			   T2.STATUS,
			   T2.LINES_INPUT,
			   T2.LINES_OUTPUT,
			   TO_CHAR(T2.END_DATE, 'YYYY/MM/DD HH24:MI:SS') END_DATE,
       		   ROUND(T2.TIME_COST / 1000, 1) TIME_COST,
       		   ROUND(DECODE(NVL(T2.TIME_COST,0), 0, 0, T2.LINES_OUTPUT / T2.TIME_COST * 1000), 1) AVG_SPEED,
       		   ROUND(LINES_INPUT * AVG_ROW_LEN / 1024 / 1024, 1) SIZE_IN_MB,
       		   DECODE(NVL(LINES_INPUT,0), 0, '--', ROUND(LINES_OUTPUT / LINES_INPUT * 100, 1) || '%')  AS EFFICIENCY
		FROM DATA_MART_CONFIG T
				 LEFT JOIN DATA_MART_LOG T2 ON T.TRANS_NAME = T2.TRANS_NAME
				 LEFT JOIN SY_USER_MASTER_DATA T3 ON T.CREATE_BY$ = T3.SESA_CODE
				 LEFT JOIN (
					 SELECT AVG(AVG_ROW_LEN) AVG_ROW_LEN
					 FROM USER_TABLES
					 WHERE TABLE_NAME LIKE '%_V'
					   AND TABLE_NAME NOT LIKE '%_FILTER_V'
					   AND TABLE_NAME NOT LIKE 'SY_%'
					   AND AVG_ROW_LEN > 30
				 ) T2 ON 1 = 1
		WHERE T.ROWID = #{key,jdbcType=VARCHAR}
		ORDER BY T2.START_DATE DESC FETCH NEXT 1 ROWS ONLY
	</select>

	<sql id="queryReport5SQL">
		SELECT * FROM DATA_MART_LOG_DETAILS WHERE ID_BATCH = #{key, jdbcType=VARCHAR}
	</sql>

	<select id="queryReport5Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport5SQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport5" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport5SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport8SQL">
		SELECT ROWIDTOCHAR(T.ROWID) KEY,
		       T.ID_BATCH,
			   T.TRANS_NAME,
			   T.STATUS,
			   T.LINES_READ,
			   T.LINES_WRITTEN,
			   T.LINES_UPDATED,
			   T.LINES_INPUT,
			   T.LINES_OUTPUT,
			   T.LINES_REJECTED,
			   T.ERRORS,
			   TO_CHAR(T.START_DATE, 'yyyy/mm/dd hh24:mi:ss') START_DATE,
			   TO_CHAR(T.END_DATE, 'yyyy/mm/dd hh24:mi:ss') END_DATE,
			   ROUND(T.TIME_COST / 1000, 1) TIME_COST,
			   ROUND(DECODE(NVL(T.TIME_COST, 0), 0, 0 , LINES_OUTPUT / TIME_COST * 1000), 2) SPEED
		FROM DATA_MART_LOG T INNER JOIN DATA_MART_CONFIG T2 ON T.TRANS_NAME = T2.TRANS_NAME
	   WHERE T2.ROWID = #{key, jdbcType=VARCHAR}
		     AND T.START_DATE BETWEEN to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') and to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd') + 1
	   ORDER BY T.START_DATE DESC
	</sql>

	<select id="queryReport8Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport8SQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport8" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport8SQL"/>
        <include refid="global.select_footer"/>
    </select>

	<select id="queryReport8Logs" resultType="java.lang.String">
		SELECT T.LOG_FIELD FROM DATA_MART_LOG T WHERE T.ROWID = #{key, jdbcType=VARCHAR}
	</select>

	<sql id="queryReport8StepsSQL">
		SELECT T.*
		  FROM DATA_MART_LOG_DETAILS T INNER JOIN DATA_MART_LOG T2 ON T.ID_BATCH = T2.ID_BATCH
		  WHERE t2.ROWID = #{key, jdbcType=VARCHAR}
	</sql>

	<select id="queryReport8StepsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport8StepsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport8Steps" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport8StepsSQL"/>
        <include refid="global.select_footer"/>
    </select>

	<insert id="saveNewJob">
		insert into DATA_MART_CONFIG
		(DISPLAY_NAME, TRANS_NAME, GROUPS, REMARKS, MONTH, DAY, HOUR, MINUTE, SCRIPT_PATH, PARAMETERS, CREATE_DATE$, CREATE_BY$, STATUS, DAY_TYPE)
		values
		(#{name, jdbcType=VARCHAR}, #{trans_name, jdbcType=VARCHAR}, #{groups, jdbcType=VARCHAR}, #{remarks, jdbcType=VARCHAR},
		#{month, jdbcType=VARCHAR}, #{day, jdbcType=VARCHAR}, #{hour, jdbcType=VARCHAR}, #{minute, jdbcType=VARCHAR},
		#{uploadedFilePath, jdbcType=VARCHAR}, #{parameters, jdbcType=VARCHAR}, sysdate, #{session.userid,jdbcType=VARCHAR},
		#{enable, jdbcType=VARCHAR}, #{dayType, jdbcType=VARCHAR})
	</insert>

	<update id="modifyJob">
		update DATA_MART_CONFIG
		   set DISPLAY_NAME = #{name, jdbcType=VARCHAR},
		       GROUPS = #{groups, jdbcType=VARCHAR},
		       REMARKS = #{remarks, jdbcType=VARCHAR},
		       MONTH = #{month, jdbcType=VARCHAR},
		       DAY = #{day, jdbcType=VARCHAR},
		       HOUR = #{hour, jdbcType=VARCHAR},
		       MINUTE = #{minute, jdbcType=VARCHAR},
		       SCRIPT_PATH = #{uploadedFilePath, jdbcType=VARCHAR},
		       PARAMETERS = #{parameters, jdbcType=VARCHAR},
		       UPDATE_DATE$ = sysdate,
		       UPDATE_BY$ = #{session.userid,jdbcType=VARCHAR},
		       STATUS = #{enable, jdbcType=VARCHAR},
		       DAY_TYPE = #{dayType, jdbcType=VARCHAR}
		 where TRANS_NAME = #{trans_name, jdbcType=VARCHAR}
	</update>

	<insert id="mergeJobSteps">
		begin
			delete from DATA_MART_STEPS where id = #{id, jdbcType=VARCHAR};

			<if test="steps != null and steps.isEmpty() == false">
				insert into DATA_MART_STEPS (ID, NAME, TYPE, CONNECTION, XLOC, YLOC)
				<foreach collection="steps" separator=" union all " item="item">
					select
					#{item.id, jdbcType=VARCHAR},
					#{item.name, jdbcType=VARCHAR},
					#{item.type, jdbcType=VARCHAR},
					#{item.connection, jdbcType=VARCHAR},
					#{item.xloc, jdbcType=VARCHAR},
					#{item.yloc, jdbcType=VARCHAR}
					from dual
				</foreach>;
			</if>
		end;
	</insert>

	<insert id="mergeJobOrders">
		begin
			delete from DATA_MART_STEPS_ORDER where id = #{id, jdbcType=VARCHAR};

			<if test="orders != null and orders.isEmpty() == false">
				insert into DATA_MART_STEPS_ORDER (ID, STEP_FROM, STEP_TO, ENABLE)
				<foreach collection="orders" separator=" union all " item="item">
					select
					#{item.id, jdbcType=VARCHAR},
					#{item.from, jdbcType=VARCHAR},
					#{item.to, jdbcType=VARCHAR},
					#{item.enabled, jdbcType=VARCHAR}
					from dual
				</foreach>;
			</if>
		end;
	</insert>

	<select id="queryAvaliableConnection" resultType="java.util.Map">
		SELECT * FROM DATA_MART_DB_CONFIG t where t.ROWID IN
		<foreach collection="connectionIDs" separator="," open="(" close=")" item="item">
			#{item,jdbcType=VARCHAR}
		</foreach>
	</select>

	<select id="queryReport7Steps" resultType="java.util.Map">
		select NAME, TYPE, XLOC, YLOC
		  from DATA_MART_STEPS t inner join DATA_MART_CONFIG t2 on t.ID = t2.TRANS_NAME
		 where t2.ROWID = #{key, jdbcType=VARCHAR}
	</select>

	<select id="queryReport7Links" resultType="java.util.Map">
		select STEP_FROM, STEP_TO, ENABLE
		  from DATA_MART_STEPS_ORDER t inner join DATA_MART_CONFIG t2 on t.ID = t2.TRANS_NAME
		 where t2.ROWID = #{key, jdbcType=VARCHAR}
	</select>

	<select id="queryReport6" resultType="java.util.Map">
		select * from DATA_MART_CONFIG where rowid = #{key, jdbcType=VARCHAR}
	</select>

	<delete id="deleteJob">
		begin
			delete from DATA_MART_STEPS where ID = #{trans_name, jdbcType=VARCHAR};
			delete from DATA_MART_STEPS_ORDER where ID = #{trans_name, jdbcType=VARCHAR};
			delete from DATA_MART_LOG_DETAILS where id_batch in (select id_batch from DATA_MART_LOG where trans_name = #{trans_name, jdbcType=VARCHAR});
			delete from DATA_MART_LOG where trans_name = #{trans_name, jdbcType=VARCHAR};
			delete from DATA_MART_CONFIG where trans_name = #{trans_name, jdbcType=VARCHAR};
		end;
	</delete>

	<select id="queryAllConnections" resultType="java.lang.String">
		SELECT LOWER(T.USERNAME || '.' || T.DB_TYPE || '@' || IP || ':' || PORT) FROM DATA_MART_DB_CONFIG T
	</select>

	<select id="queryReport9" resultType="java.util.Map">
		WITH ORDER_BASE (STEP_FROM, STEP_TO, TRANS_ID) AS (SELECT STEP_FROM, STEP_TO, ID
                                                   FROM DATA_MART_STEPS_ORDER T
                                                   UNION ALL

                                                   SELECT P.STEP_FROM, T.STEP_TO, T.ID
                                                   FROM DATA_MART_STEPS_ORDER T
                                                            INNER JOIN ORDER_BASE P ON T.ID = P.TRANS_ID
                                                       AND P.STEP_TO = T.STEP_FROM),
		ORDER_TEMP AS (
			SELECT T.STEP_FROM, T2.TYPE AS STEP_FROM_TYPE, T.STEP_TO, T3.TYPE AS STEP_TO_TYPE, T.TRANS_ID
			  FROM ORDER_BASE T
				   INNER JOIN DATA_MART_STEPS T2 ON T.TRANS_ID = T2.ID AND T.STEP_FROM = T2.NAME
				   INNER JOIN DATA_MART_STEPS T3 ON T.TRANS_ID = T3.ID AND T.STEP_TO = T3.NAME
			 WHERE T2.TYPE IN ('TableInput', 'CsvInput', 'JsonInput', 'ExcelInput', 'AccessInput', 'TextFileInput')
  				   AND T3.TYPE IN ('TableOutput', 'InsertUpdate', 'TextFileOutput', 'Update', 'ExcelOutput')
		),
		ORDER_TEMP2 AS (
			SELECT T2.ENTITY                      AS                                 ENTITY_FROM,
				   NVL(T4.TYPE, 'SOURCE')         AS                                 FROM_TYPE,
				   COALESCE(T3.ENTITY, T5.GROUPS, 'Files Output')                    ENTITY_TO,
				   SUM(DECODE(T2.LINES_INPUT, 0, 0, T2.LINES_INPUT - T2.LINES_READ)) LINES_INPUT,
				   SUM(T3.LINES_OUTPUT + T3.LINES_UPDATED)                           LINES_OUTPUT
			FROM ORDER_TEMP T
					 INNER JOIN DATA_MART_LOG_DETAILS T2 ON T.TRANS_ID = T2.TRANS_NAME AND T2.STEP_NAME = T.STEP_FROM
					 INNER JOIN DATA_MART_LOG_DETAILS T3 ON T.TRANS_ID = T3.TRANS_NAME AND T3.STEP_NAME = T.STEP_TO AND T2.ID_BATCH = T3.ID_BATCH
					 LEFT JOIN DATA_MART_DB_CONFIG T4 ON T2.CONNECTION = T4.NAME
					 LEFT JOIN DATA_MART_CONFIG T5 ON T3.TRANS_NAME = T5.TRANS_NAME
			 WHERE T2.ID_BATCH IN (
				SELECT T3.ID_BATCH FROM DATA_MART_LOG T3 WHERE T3.START_DATE BETWEEN TO_DATE(#{date, jdbcType=VARCHAR} || ' 00:00:00', 'YYYY/MM/DD HH24:MI:SS') AND TO_DATE(#{date, jdbcType=VARCHAR} || ' 23:59:59', 'YYYY/MM/DD HH24:MI:SS')
			 )
			 AND T2.ENTITY IS NOT NULL
			 GROUP BY T2.ENTITY, T3.ENTITY, T4.TYPE, T5.GROUPS
		)
		SELECT ENTITY_FROM,
			   FROM_TYPE,
			   ENTITY_TO,
			   SUM(LINES_INPUT) LINES_INPUT,
			   SUM(LINES_OUTPUT) LINES_OUTPUT
		FROM ORDER_TEMP2
		WHERE ENTITY_FROM != ENTITY_TO
		GROUP BY ENTITY_FROM, FROM_TYPE, ENTITY_TO
		HAVING SUM(LINES_INPUT) > 0 OR SUM(LINES_OUTPUT) > 0
		ORDER BY FROM_TYPE DESC
	</select>

	<select id="queryReport10" resultType="java.util.Map">
		SELECT  T2.GROUPS || ' - ' || T2.DISPLAY_NAME || CASE WHEN T.END_DATE IS NULL THEN '[running]' ELSE '' END KEY,
			   TO_CHAR(T.START_DATE, 'YYYY/MM/DD HH24:MI:SS')                   START_DATE,
			   ROUND(t.TIME_COST / 60000, 4)                                    DURATION
		FROM DATA_MART_LOG T
				 INNER JOIN DATA_MART_CONFIG T2 ON T.TRANS_NAME = T2.TRANS_NAME
		WHERE T.START_DATE between TO_DATE(#{date, jdbcType=VARCHAR} || ' ' || #{time[0], jdbcType=VARCHAR}, 'YYYY/MM/DD HH24:MI:SS')
		                           AND TO_DATE(#{date, jdbcType=VARCHAR} || ' ' || #{time[1], jdbcType=VARCHAR}, 'YYYY/MM/DD HH24:MI:SS')
		ORDER BY START_DATE, END_DATE
	</select>
</mapper>
