<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.toolbox.dao.ICsfValidationDao">
	<sql id="csfFilter">
		<if test="_filters != null and _filters != ''.toString()">
			and ${_filters}
		</if>
	</sql>

	<select id="initPage" resultType="java.util.Map">
		SELECT /*+ parallel */ NAME,
		       CATEGORY
		FROM SCPA.CSF_FILTER_V
		ORDER BY CATEGORY,NAME
	</select>

	<select id="queryForecastVersionOptions" resultType="java.lang.Integer">
		SELECT /*+ parallel */ DISTINCT TO_NUMBER(FCST_VERSION) AS FCST_VERSION
		FROM SCPA.CSF_FCST_V ORDER BY FCST_VERSION DESC
	</select>

	<select id="queryDateOptions" resultType="java.util.HashMap">
		WITH TEMP AS (SELECT TO_DATE(TEXT, 'YYYY/MM/DD') AS TEXT
							FROM SY_CALENDAR
							WHERE NAME = 'National Holidays')
		SELECT /*+ parallel */DISTINCT TO_CHAR(TEXT, 'yyyymm')   AS CALENDAR_MONTH,
							  TO_CHAR(TEXT, 'yyyy"Q"q') 		 AS CALENDAR_QUARTER,
							  TO_CHAR(TEXT, 'YYYY') || CASE
							  WHEN TO_CHAR(TEXT, 'MM') &lt; '07' THEN 'H1'
							  ELSE 'H2'
							  END                   			 AS CALENDAR_HALF_YEAR,
							  TO_CHAR(TEXT, 'YYYY')     		 AS CALENDAR_YEAR
		FROM TEMP
		WHERE TEXT BETWEEN TRUNC(ADD_MONTHS(SYSDATE, -24), 'YYYY')
					AND TRUNC(ADD_MONTHS(SYSDATE, +24), 'MM')
		ORDER BY TO_CHAR(TEXT, 'yyyymm')
	</select>

	<select id="queryDefaultFilters" resultType="java.util.HashMap">
		SELECT CATEGORY, NAME FROM SCPA.CSF_VALIDATION_DEFAULT_FILTER_V T
		<where>
			T.CREATE_BY$ = #{session.userid, jdbcType=VARCHAR}
		</where>
	</select>

	<select id="queryReport1" resultType="java.util.LinkedHashMap">
			WITH TEMP AS (<choose>
								<when test="pivotMonthList.size() > 0">
									SELECT 	T.*,
											<foreach collection="pivotMonthList" item="item" separator=", ">
												T1."${item}"
											</foreach>
									       FROM ${SCPA.CSF_VALIDATION_V} T
									LEFT JOIN (SELECT MATERIAL, SALES_ORGANIZATION, SOLD_TO, SALES_GROUP,CALENDAR_MONTH,
											SUM(NVL(T.${report1ValueType}, 0)) AS VALUE
											FROM <choose>
													<when test="report1CompareType == 'Sales'.toString()">
														SCPA.CSF_SALES_V T
														<where>
															<include refid="csfFilter"/>
														</where>
													</when>
													<when test="report1CompareType == 'Order Intake'.toString()">
														SCPA.CSF_ORDER_V T
														<where>
															<include refid="csfFilter"/>
														</where>
													</when>
													<when test="report1CompareType == 'CRD'.toString()">
														SCPA.CSF_CRD_V T
														<where>
															<include refid="csfFilter"/>
														</where>
													</when>
													<when test="report1CompareType == 'Backlog'.toString()">
														SCPA.CSF_BACKLOG_V T
														<where>
															<include refid="csfFilter"/>
														</where>
													</when>
													<otherwise>
														SCPA.CSF_CRD_V T
														<where>
															<include refid="csfFilter"/>
														</where>
													</otherwise>
												</choose>
									GROUP BY MATERIAL, SALES_ORGANIZATION, SOLD_TO, SALES_GROUP, CALENDAR_MONTH)
									PIVOT (SUM(VALUE)
											FOR CALENDAR_MONTH IN
											<foreach collection="pivotMonthList" item="item" separator=", " open="(" close=")">
												'${item}' AS "${item}"
											</foreach>
											) T1 ON
									T.MATERIAL = T1.MATERIAL AND T.SALES_ORGANIZATION = T1.SALES_ORGANIZATION AND T.SOLD_TO = T1.SOLD_TO AND T.SALES_GROUP = T1.SALES_GROUP
								</when>
								<otherwise>
									SELECT * FROM ${SCPA.CSF_VALIDATION_V} T
									<where>
										T.CREATE_BY$ = #{session.userid, jdbcType=VARCHAR}
										<include refid="csfFilter"/>
									</where>
								</otherwise>
							</choose>
						),
			    BASE AS (
					SELECT /*+ parallel */
						<foreach collection="report1GroupByColumns" separator="," item="item">
							NVL(T.${item},'Others') AS ${item}
						</foreach>,
						<choose>
							<when test="report1ValueType == 'VALUE'.toString()">
								<foreach collection="report1FcstColumns.entrySet()" item="entry" index="key" separator=",">
									SUM(
						   			<foreach collection="entry" item="month" separator="+">
									    NVL(T."${month}" * T.AVG_SELLING_PRICE_RMB, 0)
								    </foreach>
									) AS "${key}"
								</foreach>
							</when>
							<otherwise>
								<foreach collection="report1FcstColumns.entrySet()" item="entry" index="key" separator=",">
									SUM(
									<foreach collection="entry" item="month" separator="+">
										NVL(T."${month}", 0)
									</foreach>
									) AS "${key}"
								</foreach>
							</otherwise>
						</choose>
					FROM TEMP T
					GROUP BY
					<foreach collection="report1GroupByColumns" separator="," item="item">
						NVL(${item},'Others')
					</foreach>
			)
			SELECT /*+ parallel */ T.*,
				   <foreach collection="report1SelectedDates" item="item" separator=", ">
					   NVL(T1.HISTORY_${item}, 0) AS HISTORY_${item}
				   </foreach>
			FROM BASE T
			LEFT JOIN (
				SELECT *
				FROM (SELECT <foreach collection="report1GroupByColumns" item="item" separator=", " close=", ">
							 	T.${item}
							 </foreach>
						     T.${report1DateType}, SUM(NVL(T.${report1ValueType}, 0)) AS VALUE
					    FROM <choose>
						         <when test="report1CompareType == 'Sales'.toString()">
							         SCPA.CSF_SALES_V T
							         <where>
								         <include refid="csfFilter"/>
							         </where>
						         </when>
						         <when test="report1CompareType == 'Order Intake'.toString()">
							         SCPA.CSF_ORDER_V T
							         <where>
								         <include refid="csfFilter"/>
							         </where>
						         </when>
						         <when test="report1CompareType == 'CRD'.toString()">
							         SCPA.CSF_CRD_V T
							         <where>
								         <include refid="csfFilter"/>
							         </where>
						         </when>
						         <when test="report1CompareType == 'Backlog'.toString()">
							         SCPA.CSF_BACKLOG_V T
							         <where>
								         <include refid="csfFilter"/>
							         </where>
						         </when>
						         <when test="report1CompareType == 'History Forecast'.toString()">
							         (SELECT /*+ parallel */ MATERIAL,
							         		PLANT_CODE,
							         		SALES_GROUP,
							         		SALES_ORGANIZATION,
									        BU,
									        ENTITY,
									        LOCAL_PRODUCT_LINE,
									        LOCAL_PRODUCT_FAMILY,
									        LOCAL_PRODUCT_SUBFAMILY,
									        PRODUCT_LINE,
									        LOCAL_BU,
							         		FCST_VERSION_ADJUSTED 							AS CALEANDR_DATE,
									        TO_CHAR(T.FCST_VERSION_ADJUSTED, 'yyyymm')   	AS CALENDAR_MONTH,
									        TO_CHAR(T.FCST_VERSION_ADJUSTED, 'yyyy"Q"q') 	AS CALENDAR_QUARTER,
									        TO_CHAR(T.FCST_VERSION_ADJUSTED, 'YYYY') || CASE
										        WHEN TO_CHAR(T.FCST_VERSION_ADJUSTED, 'MM') &lt; '07' THEN 'H1'
										        ELSE 'H2'
										        END                                     	AS CALENDAR_HALF_YEAR,
									         TO_CHAR(T.FCST_VERSION_ADJUSTED, 'yyyy')     	AS CALENDAR_YEAR,
							         		FCST_QTY         AS QUANTITY,
							         		FCST_VALUE       AS VALUE
							         FROM (SELECT /*+ parallel */ MATERIAL,
							         			  PLANT_CODE,
							         			  FCST_VERSION,
							         			  SALES_GROUP,
										          BU,
										          ENTITY,
										          LOCAL_PRODUCT_LINE,
										          LOCAL_PRODUCT_FAMILY,
										          LOCAL_PRODUCT_SUBFAMILY,
										          PRODUCT_LINE,
										          LOCAL_BU,
							         			  SALES_ORGANIZATION,
							         			  AVG_SELLING_PRICE_RMB,
							         			  TO_DATE(FCST_VERSION, 'YYYYMM') + (TO_NUMBER(SUBSTR(month_column, 6, 2)) - 1) *
							         			  INTERVAL '1' MONTH AS FCST_VERSION_ADJUSTED,
							         			  FCST_QTY,
							         			  FCST_QTY * AVG_SELLING_PRICE_RMB                     AS FCST_VALUE
							         FROM
								         ( SELECT * FROM
								         SCPA.CSF_FCST_V T
								         <where>
								         	 T.FCST_VERSION = #{report1ForecastVersion}
									         <include refid="csfFilter"/>
								         </where>)
							         UNPIVOT (
							         		FCST_QTY FOR MONTH_COLUMN IN (
							         				MONTH01 AS 'MONTH01',
							         				MONTH02 AS 'MONTH02',
							         				MONTH03 AS 'MONTH03',
							         				MONTH04 AS 'MONTH04',
							         				MONTH05 AS 'MONTH05',
							         				MONTH06 AS 'MONTH06',
							         				MONTH07 AS 'MONTH07',
							         				MONTH08 AS 'MONTH08',
							         				MONTH09 AS 'MONTH09',
							         				MONTH10 AS 'MONTH10',
							         				MONTH11 AS 'MONTH11',
							         				MONTH12 AS 'MONTH12',
							         				MONTH13 AS 'MONTH13',
							         				MONTH14 AS 'MONTH14',
							         				MONTH15 AS 'MONTH15',
							         				MONTH16 AS 'MONTH16',
							         				MONTH17 AS 'MONTH17',
							         				MONTH18 AS 'MONTH18',
							         				MONTH19 AS 'MONTH19',
							         				MONTH20 AS 'MONTH20',
							         				MONTH21 AS 'MONTH21',
							         				MONTH22 AS 'MONTH22',
							         				MONTH23 AS 'MONTH23',
							         				MONTH24 AS 'MONTH24',
							         				MONTH25 AS 'MONTH25'
							         		)
							         )) T
							         UNION ALL
							         SELECT /*+ parallel */ MATERIAL,
													        PLANT_CODE,
													        SALES_GROUP,
													        SALES_ORGANIZATION,
													        BU,
													        ENTITY,
													        LOCAL_PRODUCT_LINE,
													        LOCAL_PRODUCT_FAMILY,
													        LOCAL_PRODUCT_SUBFAMILY,
													        PRODUCT_LINE,
													        LOCAL_BU,
													        CALENDAR_DATE,
													        CALENDAR_MONTH,
													        CALENDAR_QUARTER,
													        CALENDAR_HALF_YEAR,
													        CALENDAR_YEAR,
													        QUANTITY,
													        VALUE
							         FROM SCPA.CSF_CRD_V T
							         <where>
								         <include refid="csfFilter"/>
								         AND TO_CHAR(SYSDATE, 'YYYY') = CALENDAR_YEAR
								         AND TO_NUMBER(CALENDAR_MONTH) &lt; ${report1ForecastVersion}
							         </where>
							         ) T
						         </when>
							     <otherwise>
							 	    SCPA.CSF_ORDER_V T
							     </otherwise>
					         </choose>
						GROUP BY
						<foreach collection="report1GroupByColumns" item="item" separator=", " close=", ">
							${item}
						</foreach>
						${report1DateType})
				PIVOT (SUM(VALUE)
					FOR ${report1DateType} IN
					 <foreach collection="report1SelectedDates" item="item" separator=", " open="(" close=")">
						 '${item}' AS HISTORY_${item}
					 </foreach>
				)
			) T1 ON
			<foreach collection="report1GroupByColumns" item="item" separator=" AND ">
				T.${item} = T1.${item}
			</foreach>
			<where>
				<foreach collection="report1GroupByColumns" item="item" separator=" AND ">
					NVL(T.${item}, 'Others') != 'Others'
				</foreach>
			</where>
			ORDER BY
			<foreach collection="report1GroupByColumns" separator="," item="item">
				DECODE(T.${item}, 'Others', CAST(UNISTR('\ffff\ffff') AS VARCHAR2(8)), T.${item})
			</foreach> DESC
	</select>


	<sql id="queryReport2Sql">
		SELECT  MATERIAL,
			    SALES_ORGANIZATION,
				CUSTOMER_CODE,
				IS_VALID$,
				SALES_GROUP,
				NET_NET_PRICE_RMB,
				NET_NET_PRICE_HKD,
				MONTH01,
				MONTH02,
				MONTH03,
				MONTH04,
				MONTH05,
				MONTH06,
				MONTH07,
				MONTH08,
				MONTH09,
				MONTH10,
				MONTH11,
				MONTH12,
				MONTH13,
				MONTH14,
				MONTH15,
				MONTH16,
				MONTH17,
				MONTH18,
				MONTH19,
				MONTH20,
				MONTH21,
				MONTH22,
				MONTH23,
				MONTH24,
				MONTH25,
				CREATE_BY$,
				UPDATE_DATE$,
				UPDATE_BY$,
				CREATE_DATE$
		FROM MR3_CSF_VALIDATION WHERE CREATE_BY$ = #{session.userid, jdbcType=VARCHAR}
	</sql>

	<select id="queryReport2Count" parameterType="java.util.Map" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="queryReport2Sql"/>
		<include refid="global.count_footer"/>
	</select>

	<select id="queryReport2" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		<include refid="queryReport2Sql"/>
		<include refid="global.select_footer"/>
	</select>

	<insert id="insertReport2DataTemp">
		INSERT /*+ NOLOGGING */ INTO MR3_CSF_VALIDATION_TEMPORARY
		(MATERIAL,
		SALES_ORGANIZATION,
		CUSTOMER_CODE,
		IS_VALID$,
		SALES_GROUP,
		NET_NET_PRICE_RMB,
		NET_NET_PRICE_HKD,
		MONTH01,
		MONTH02,
		MONTH03,
		MONTH04,
		MONTH05,
		MONTH06,
		MONTH07,
		MONTH08,
		MONTH09,
		MONTH10,
		MONTH11,
		MONTH12,
		MONTH13,
		MONTH14,
		MONTH15,
		MONTH16,
		MONTH17,
		MONTH18,
		MONTH19,
		MONTH20,
		MONTH21,
		MONTH22,
		MONTH23,
		MONTH24,
		MONTH25)
		<foreach collection="list" separator=" union all" item="item">
			SELECT
			#{item.MATERIAL, jdbcType=VARCHAR},
			#{item.SALES_ORGANIZATION, jdbcType=VARCHAR},
			#{item.CUSTOMER_CODE, jdbcType=VARCHAR},
			#{item.IS_VALID$, jdbcType=VARCHAR},
			#{item.SALES_GROUP, jdbcType=VARCHAR},
			#{item.NET_NET_PRICE_RMB, jdbcType=VARCHAR},
			#{item.NET_NET_PRICE_HKD, jdbcType=VARCHAR},
			#{item.MONTH01, jdbcType=VARCHAR},
			#{item.MONTH02, jdbcType=VARCHAR},
			#{item.MONTH03, jdbcType=VARCHAR},
			#{item.MONTH04, jdbcType=VARCHAR},
			#{item.MONTH05, jdbcType=VARCHAR},
			#{item.MONTH06, jdbcType=VARCHAR},
			#{item.MONTH07, jdbcType=VARCHAR},
			#{item.MONTH08, jdbcType=VARCHAR},
			#{item.MONTH09, jdbcType=VARCHAR},
			#{item.MONTH10, jdbcType=VARCHAR},
			#{item.MONTH11, jdbcType=VARCHAR},
			#{item.MONTH12, jdbcType=VARCHAR},
			#{item.MONTH13, jdbcType=VARCHAR},
			#{item.MONTH14, jdbcType=VARCHAR},
			#{item.MONTH15, jdbcType=VARCHAR},
			#{item.MONTH16, jdbcType=VARCHAR},
			#{item.MONTH17, jdbcType=VARCHAR},
			#{item.MONTH18, jdbcType=VARCHAR},
			#{item.MONTH19, jdbcType=VARCHAR},
			#{item.MONTH20, jdbcType=VARCHAR},
			#{item.MONTH21, jdbcType=VARCHAR},
			#{item.MONTH22, jdbcType=VARCHAR},
			#{item.MONTH23, jdbcType=VARCHAR},
			#{item.MONTH24, jdbcType=VARCHAR},
			#{item.MONTH25, jdbcType=VARCHAR}
			FROM DUAL
		</foreach>
	</insert>

	<update id="mergeReport2Data">
		BEGIN
			DELETE FROM MR3_CSF_VALIDATION where CREATE_BY$ = #{userid, jdbcType=VARCHAR};
			INSERT INTO MR3_CSF_VALIDATION
				(MATERIAL,
				 SALES_ORGANIZATION,
				 CUSTOMER_CODE,
				 IS_VALID$,
				 SALES_GROUP,
				 NET_NET_PRICE_RMB,
				 NET_NET_PRICE_HKD,
				 MONTH01,
				 MONTH02,
				 MONTH03,
				 MONTH04,
				 MONTH05,
				 MONTH06,
				 MONTH07,
				 MONTH08,
				 MONTH09,
				 MONTH10,
				 MONTH11,
				 MONTH12,
				 MONTH13,
				 MONTH14,
				 MONTH15,
				 MONTH16,
				 MONTH17,
				 MONTH18,
				 MONTH19,
				 MONTH20,
				 MONTH21,
				 MONTH22,
				 MONTH23,
				 MONTH24,
				 MONTH25, CREATE_BY$, CREATE_DATE$)
			SELECT MATERIAL,
				SALES_ORGANIZATION,
				CUSTOMER_CODE,
				IS_VALID$,
				SALES_GROUP,
				NET_NET_PRICE_RMB,
				NET_NET_PRICE_HKD,
				MONTH01,
				MONTH02,
				MONTH03,
				MONTH04,
				MONTH05,
				MONTH06,
				MONTH07,
				MONTH08,
				MONTH09,
				MONTH10,
				MONTH11,
				MONTH12,
				MONTH13,
				MONTH14,
				MONTH15,
				MONTH16,
				MONTH17,
				MONTH18,
				MONTH19,
				MONTH20,
				MONTH21,
				MONTH22,
				MONTH23,
				MONTH24,
				MONTH25, #{userid,jdbcType=VARCHAR}, SYSDATE FROM MR3_CSF_VALIDATION_TEMPORARY;
		END;
	</update>

	<select id="queryReport1DetailsCount" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="report1DetailsSQL"/>
		<include refid="global.count_footer"/>
	</select>

	<select id="queryReport1Details" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		<include refid="report1DetailsSQL"/>
		<include refid="global.select_footer"/>
	</select>

	<sql id="report1DetailsSQL">
		WITH BASE AS (
			SELECT DISTINCT MATERIAL, ORI_SALES_ORGANIZATION, SOLD_TO, SALES_GROUP
			FROM ${SCPA.CSF_VALIDATION_V} T
			<where>
				<if test="report1SelectedValue != null and report1SelectedValue.isEmpty() == false">
					<foreach collection="report1GroupByColumns" separator=" and " item="item" index="index" open=" and ">
						<choose>
							<when test="item == 'Others'.toString()">
								(${item} = 'Others' or ${item} is null)
							</when>
							<when test="item != null and item != ''.toString()">
								${item} = #{report1SelectedValue[${index}], jdbcType=VARCHAR}
							</when>
						</choose>
					</foreach>
				</if>
				<include refid="csfFilter"/>
			</where>
		)
		SELECT T.* FROM MR3_CSF_VALIDATION T INNER JOIN BASE T1 ON T.MATERIAL = T1.MATERIAL AND T.SALES_ORGANIZATION = T1.ORI_SALES_ORGANIZATION AND T.CUSTOMER_CODE = T1.SOLD_TO AND T.SALES_GROUP = T1.SALES_GROUP
		<where>
			CREATE_BY$ = #{session.userid, jdbcType=VARCHAR}
		</where>
	</sql>

	<delete id="deleteReport1Details">
		DELETE FROM MR3_CSF_VALIDATION
		WHERE MATERIAL || ',' || SALES_ORGANIZATION || ',' || CUSTOMER_CODE || ',' || SALES_GROUP in
		<foreach collection="deletes" open="(" close=")" separator="," item="item">#{item, jdbcType=VARCHAR}</foreach>
		AND CREATE_BY$ = #{userid, jdbcType=VARCHAR}
	</delete>

	<select id="queryReport1DetailsExists" resultType="java.lang.Integer">
		select count(1) from MR3_CSF_VALIDATION
		WHERE
			MATERIAL = #{material,jdbcType=VARCHAR} AND
			SALES_ORGANIZATION = #{salesOrganization,jdbcType=VARCHAR} AND
			CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR} AND
			SALES_GROUP = #{salesGroup,jdbcType=VARCHAR} AND
			CREATE_BY$ = #{userid,jdbcType=VARCHAR}
	</select>

	<update id="updateReport1Details">
		UPDATE MR3_CSF_VALIDATION
		SET
		<foreach collection="updates" item="col" separator=",">
			${col.key} = #{col.value,jdbcType=VARCHAR}
		</foreach>,
		UPDATE_BY$ = #{userid,jdbcType=VARCHAR},
		UPDATE_DATE$ = SYSDATE
		WHERE
			MATERIAL = #{material,jdbcType=VARCHAR} AND
			SALES_ORGANIZATION = #{salesOrganization,jdbcType=VARCHAR} AND
			CUSTOMER_CODE = #{customerCode,jdbcType=VARCHAR} AND
			SALES_GROUP = #{salesGroup,jdbcType=VARCHAR} AND
			CREATE_BY$ = #{userid,jdbcType=VARCHAR}
	</update>

	<insert id="saveReport1Details">
		insert into MR3_CSF_VALIDATION
		(<foreach collection="updates" item="col" separator=",">
			${col.key}
		</foreach>, create_by$, create_date$, UPDATE_BY$, UPDATE_DATE$)
		values
		<foreach collection="updates" item="col" separator=",">
			#{col.value,jdbcType=VARCHAR}
		</foreach>,
		#{userid,jdbcType=VARCHAR}, sysdate, null, null
		)
	</insert>

</mapper>
