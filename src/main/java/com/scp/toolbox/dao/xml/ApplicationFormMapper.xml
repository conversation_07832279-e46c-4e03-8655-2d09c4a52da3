<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.toolbox.dao.IApplicationFormDao">
	<select id="queryApplicationConfig" parameterType="java.util.Map" resultType="java.util.Map">
		select APP_ID, TABLE_NAME, APP_NAME, TABLE_MODULE, WHERE_SQL, CALCULATION, COLUMNS_ORDER, CLEAR,ORDER_BY,MEMO,EXCEL_TYPE,TASK_GROUP, APP_NAME AS NAME,
			   APP_ID AS "VALUE",
			   NVL(TASK_GROUP, 'Others') AS CATEGORY from APPLICATION_FORM_CONFIG ORDER BY CREATE_DATE$
	</select>

	<select id="queryApplicationAuth" resultType="java.lang.Integer">
		select count(1)
		  from SY_MENU_AUTH
		 where MENU_CODE = 'menuA10'
		   and USER_ID = #{userid, jdbcType=VARCHAR}
		   and upper(AUTH_DETAILS) = 'ADMIN'
	</select>

	<select id="queryGroupOpts" resultType="java.util.Map">
		SELECT APP_NAME AS NAME,
			   APP_ID AS "VALUE",
			   NVL(TASK_GROUP, 'Others') AS CATEGORY
		FROM APPLICATION_FORM_CONFIG
		ORDER BY DECODE(NVL(TASK_GROUP, 'Others'), 'Others', 'ZZZZZ', NVL(TASK_GROUP, 'Others')), NAME
	</select>

	<update id="executeUpdate">
		begin
        	${sql}
        end;
    </update>

	<update id="updateConfig">
		update APPLICATION_FORM_CONFIG
		   set APP_NAME = #{config.name, jdbcType=VARCHAR},
		       TABLE_NAME = #{config.bindTo, jdbcType=VARCHAR},
		       CONFIGS = #{configStr, jdbcType=CLOB},
			   TABLE_MODULE = #{config.module, jdbcType=VARCHAR},
			   WHERE_SQL = #{config.where, jdbcType=VARCHAR},
		       TRIG_MONTH = #{config.month, jdbcType=VARCHAR},
		       TRIG_DAY_TYPE = #{config.dayType, jdbcType=VARCHAR},
		       TRIG_DAY = #{config.day, jdbcType=VARCHAR},
		       TRIG_HOUR = #{config.hour, jdbcType=VARCHAR},
		       TRIG_MINUTE = #{config.minute, jdbcType=VARCHAR},
		       COLUMNS_ORDER = #{config.columnsOrder, jdbcType=VARCHAR},
		       CALCULATION = #{calc, jdbcType=VARCHAR},
		       CLEAR = #{clear, jdbcType=VARCHAR},
		       UPDATE_BY$ = #{userid, jdbcType=VARCHAR},
		       UPDATE_DATE$ = SYSDATE,
			   ORDER_BY = #{orderBy, jdbcType=VARCHAR},
			   MEMO = #{memo, jdbcType=VARCHAR},
			   EXCEL_TYPE = #{excelType, jdbcType=VARCHAR},
			   TASK_GROUP = #{config.group, jdbcType=VARCHAR}
		 where APP_ID = #{config.id, jdbcType=VARCHAR}
	</update>

    <insert id="saveConfig">
		insert into APPLICATION_FORM_CONFIG
		(APP_ID, APP_NAME, TABLE_NAME, CONFIGS, TABLE_MODULE, WHERE_SQL, TRIG_MONTH, TRIG_DAY_TYPE, TRIG_DAY, TRIG_HOUR, TRIG_MINUTE,
		CALCULATION, COLUMNS_ORDER, CLEAR, CREATE_BY$, CREATE_DATE$,ORDER_BY,MEMO,EXCEL_TYPE,TASK_GROUP)
		values
		(#{config.id, jdbcType=VARCHAR}, #{config.name, jdbcType=VARCHAR}, #{config.bindTo, jdbcType=VARCHAR}, #{configStr, jdbcType=CLOB}, #{config.module, jdbcType=VARCHAR},
		#{config.where, jdbcType=VARCHAR}, #{config.month, jdbcType=VARCHAR}, #{config.dayType, jdbcType=VARCHAR}, #{config.day, jdbcType=VARCHAR}, #{config.hour, jdbcType=VARCHAR},
		#{config.minute, jdbcType=VARCHAR}, #{calc, jdbcType=VARCHAR}, #{config.columnsOrder, jdbcType=VARCHAR}, #{clear, jdbcType=VARCHAR}, #{userid, jdbcType=VARCHAR}, SYSDATE,#{orderBy, jdbcType=VARCHAR},
		 #{memo, jdbcType=VARCHAR},#{excelType, jdbcType=VARCHAR},#{config.group, jdbcType=VARCHAR})
	</insert>

	<select id="queryMailtoByKeywords" resultType="java.util.Map">
		select user_name LABEL,
			   email "VALUE",
			   'DSS User' as TYPE
		  from SY_USER_MASTER_DATA
		 where lower(user_name) like '%' || lower(#{keywords, jdbcType=VARCHAR}) ||'%'
		       or lower(email) like '%' || lower(#{keywords, jdbcType=VARCHAR}) ||'%'
		offset 0 rows fetch next 5 rows only
	</select>

	<select id="queryConfig" resultType="java.lang.String">
		select CONFIGS from SCPA.APPLICATION_FORM_CONFIG where APP_ID = #{id, jdbcType=VARCHAR}
	</select>

	<delete id="deleteConfig">
		delete from APPLICATION_FORM_CONFIG where APP_ID = #{id, jdbcType=VARCHAR}
	</delete>

	<insert id="saveMailLog">
		insert into APPLICATION_MAIL_LOGS
		(SEQ_ID, MAIL_ID, SEND_BY, SEND_TIME, MAIL_BODY, ATTACHEMENT_PATH)
		values
		(#{seqid, jdbcType=VARCHAR}, #{id, jdbcType=VARCHAR}, #{userid, jdbcType=VARCHAR}, sysdate, #{body, jdbcType=VARCHAR}, #{path, jdbcType=VARCHAR})
	</insert>

	<select id="queryLastSentTime" resultType="java.lang.String">
		select to_char(max(SEND_TIME), 'yyyy/mm/dd hh24:mi:ss')
		  from SCPA.APPLICATION_MAIL_LOGS
	     where SEND_BY = #{userid, jdbcType=VARCHAR}
	           and mail_id = #{id, jdbcType=VARCHAR}
	</select>

	<select id="queryResultCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
		${scripts}
        <include refid="global.count_footer"/>
    </select>

    <select id="queryResult" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
		${scripts}
        <include refid="global.select_footer"/>
    </select>

	<select id="fileList" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT DISTINCT CATEGORY FROM APPLICATION_TEMPLATE_FILE
	</select>

	<select id="sqlTemplate" parameterType="java.util.Map" resultType="java.util.Map">
		SELECT ID, TABLE_COLUMN_NAME, COALESCE(DESCRIPTION,' ') AS DESCRIPTION, CATEGORY FROM APPLICATION_TEMPLATE_FILE
		WHERE CATEGORY = #{id, jdbcType=VARCHAR}
		ORDER BY ID
	</select>

</mapper>
