package com.scp.toolbox.dao;

import com.scp.toolbox.bean.TreeData;
import org.apache.ibatis.annotations.Mapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IEmailNotificationDao {
    String queryAuthDetails(String userid, String menuCode);

    List<TreeData> queryEmailNotificationList(Map<String, Object> parameterMap);

    List<String> queryExistsGroup();


    List<Map<String, Object>> queryReportIDList();

    void saveEmail(Map<String, Object> parameterMap);

    List<Map<String, Object>>  queryEmailUserList();

    Map<String, Object> queryReport1(Map<String, Object> parameterMap);

    int queryReport2Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport2(Map<String, Object> parameterMap);

    void deleteEmail(Map<String, Object> parameterMap);

    void modifyEmail(Map<String, Object> parameterMap);
}
