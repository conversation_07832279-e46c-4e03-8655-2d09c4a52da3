package com.scp.toolbox.dao;

import com.scp.toolbox.bean.TreeData;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface ICustomReportDao {

    List<Map<String, Object>> querySharedTo();

    List<String> queryExistsGroup();

    List<TreeData> queryReportList(Map<String, Object> parameterMap);

    String querySQLByReportID(Map<String, Object> parameterMap);

    int queryReportCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport(Map<String, Object> parameterMap);

    void deleteReport(Map<String, Object> parameterMap);

    void saveSharedTo(Map<String, Object> parameterMap);

    void saveReport(Map<String, Object> parameterMap);

    Map<String, Object> queryReportConfig(Map<String, Object> parameterMap);

    List<String> querySharedToByReportID(Map<String, Object> parameterMap);

    void deleteSharedToByReportID(Map<String, Object> parameterMap);

    void modifySelectedReport(Map<String, Object> parameterMap);

    void saveReportVisitLog(Map<String, Object> parameterMap);

    int queryPageAdmin(String userid, String parentCode);
}
