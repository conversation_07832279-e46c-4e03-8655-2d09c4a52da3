package com.scp.toolbox.dao;

import com.scp.toolbox.bean.TreeData;
import org.apache.ibatis.annotations.Mapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IWeChatNotificationDao {

    List<TreeData> queryWeChatNotificationList(Map<String, Object> parameterMap);

    List<String> queryExistsGroup();

    Map<String, Object> queryReport1(Map<String, Object> parameterMap);

    int queryReport2Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport2(Map<String, Object> parameterMap);

    void saveNotice(Map<String, Object> parameterMap);

    String queryAuthDetails(String userid, String menuCode);

    void deleteNotice(Map<String, Object> parameterMap);

    void modifyNotice(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReportIDList();
}
