package com.scp.toolbox.dao;

import org.apache.ibatis.annotations.Mapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IBroadcastHistoryDao {

    List<Map<String, String>> initPage(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryBroadcastConfig(Map<String, Object> parameterMap);

    int queryBroadcastConfigCount(Map<String, Object> parameterMap);

}
