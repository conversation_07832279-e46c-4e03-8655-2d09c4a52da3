package com.scp.toolbox.dao;

import com.scp.toolbox.bean.TreeData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface IDataMartDao {

    List<String> queryExistsGroup();

    List<TreeData> queryRootTaskList(@Param("userid") String userid, @Param("authType") String authType);

    List<Map<String, Object>> querySubTaskList(@Param("userid") String userid, @Param("authType") String authType, @Param("key") String key);

    String queryAuthDetails(@Param("userid") String userid, @Param("menuCode") String menuCode);

    Map<String, Object> queryReport1(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport2(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport3(Map<String, Object> parameterMap);

    Map<String, Object> queryReport4(Map<String, Object> parameterMap);

    int queryReport5Count(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport5(Map<String, Object> parameterMap);

    int queryReport8Count(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport8(Map<String, Object> parameterMap);

    String queryReport8Logs(Map<String, Object> parameterMap);

    int queryReport8StepsCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport8Steps(Map<String, Object> parameterMap);

    void saveNewJob(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryDBConfigList();

    List<Map<String, Object>> queryAvaliableConnection(@Param("connectionIDs") List<String> connectionIDs);

    List<Map<String, Object>> queryReport7Steps(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport7Links(Map<String, Object> parameterMap);

    Map<String, Object> queryReport6(Map<String, Object> parameterMap);

    void mergeJobSteps(@Param("steps") List<Map<String, String>> stepList, @Param("id") String id);

    void mergeJobOrders(@Param("orders") List<Map<String, String>> orderList, @Param("id") String id);

    void deleteJob(Map<String, Object> parameterMap);

    void modifyJob(Map<String, Object> parameterMap);

    List<String> queryAllConnections();

    List<Map<String, Object>> queryReport9(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport10(Map<String, Object> parameterMap);
}
