package com.scp.toolbox;

import com.scp.toolbox.service.impl.WeChatNotificationServiceImpl;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import com.scp.toolbox.service.IWeChatNotificationService;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;

@RestController
@CrossOrigin
@SchneiderRequestMapping(value = "/toolbox/wechat_notification", parent = WeChatNotificationServiceImpl.PARENT_CODE)
@Scope("prototype")
public class WeChatNotificationController extends ControllerHelper {

    @Resource
    private IWeChatNotificationService weChatNotificationService;

    @SchneiderRequestMapping(value = "/init_page")
    public Response initPage(HttpServletRequest request) {
        this.pageLoad(request);
        this.setGlobalCache(true);
        return weChatNotificationService.initPage(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_wechat_notification_list")
    public Response queryNotificationList(HttpServletRequest request) {
        this.pageLoad(request);
        this.setGlobalCache(true);
        return weChatNotificationService.queryWeChatNotificationList(parameterMap, session);
    }

    @SchneiderRequestMapping(value = "/save_notice")
    public Response saveNotice(HttpServletRequest request) {
        this.pageLoad(request);
        return weChatNotificationService.saveNotice(parameterMap, session);
    }

    @SchneiderRequestMapping(value = "/modify_notice")
    public Response modifyNotice(HttpServletRequest request) {
        this.pageLoad(request);
        return weChatNotificationService.modifyNotice(parameterMap, session);
    }

    @SchneiderRequestMapping(value = "/delete_notice")
    public Response deleteNotice(HttpServletRequest request) {
        this.pageLoad(request);
        return weChatNotificationService.deleteNotice(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        this.pageLoad(request);
        this.setGlobalCache(true);
        return weChatNotificationService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        this.pageLoad(request);
        this.setGlobalCache(true);
        return weChatNotificationService.queryReport2(parameterMap);
    }
}
