package com.scp.toolbox.bean;

import java.util.HashMap;
import java.util.Map;

public class LinkData {
    private String id;
    private String name;
    private String source;
    private String target;

    private int value;
    private Map<String, Map<String, String>> lineStyle = new HashMap<>() {{
        put("normal", new HashMap<>());
    }};

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public void setColor(String color) {
        lineStyle.get("normal").put("color", color);
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getTarget() {
        return target;
    }

    public void setTarget(String target) {
        this.target = target;
    }

    public Map<String, Map<String, String>> getLineStyle() {
        return lineStyle;
    }

    public void setLineStyle(Map<String, Map<String, String>> lineStyle) {
        this.lineStyle = lineStyle;
    }
}
