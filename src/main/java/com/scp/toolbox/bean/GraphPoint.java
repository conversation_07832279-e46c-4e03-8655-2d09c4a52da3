package com.scp.toolbox.bean;

public class GraphPoint {
    private double x;
    private double y;

    public GraphPoint(double x, double y) {
        this.x = x;
        this.y = y;
    }

    public boolean overlapping(double x, double y) {
        return this.x - x < 60 && this.y - y < 35;
    }

    public double getX() {
        return x;
    }

    public void setX(double x) {
        this.x = x;
    }

    public double getY() {
        return y;
    }

    public void setY(double y) {
        this.y = y;
    }
}
