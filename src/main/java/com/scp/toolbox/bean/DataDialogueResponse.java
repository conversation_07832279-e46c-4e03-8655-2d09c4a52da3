package com.scp.toolbox.bean;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;

public class DataDialogueResponse {

    private String requestId;
    private String answer_chunk;
    private String reasoning_chunk;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getAnswer_chunk() {
        answer_chunk = StringUtils.replace(answer_chunk, "$", "&#36;");
        return answer_chunk;
    }

    public void setAnswer_chunk(String answer_chunk) {
        if (answer_chunk != null && !answer_chunk.isEmpty()) {
            this.answer_chunk = answer_chunk;
        }
    }

    public String getReasoning_chunk() {
        reasoning_chunk = StringUtils.replace(reasoning_chunk, "$", "&#36;");
        return reasoning_chunk;
    }

    public void setReasoning_chunk(String reasoning_chunk) {
        if (reasoning_chunk != null && !reasoning_chunk.isEmpty()) {
            this.reasoning_chunk = reasoning_chunk;
        }
    }

    public String toJSONString() {
        return JSON.toJSONString(this);
    }
}
