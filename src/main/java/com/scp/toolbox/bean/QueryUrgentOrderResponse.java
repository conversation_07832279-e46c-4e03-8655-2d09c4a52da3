package com.scp.toolbox.bean;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

public class QueryUrgentOrderResponse {
    private String salesOrderNumber;
    private String salesOrderItem;
    private String material;
    private double quantity;
    private String queryResult;

    public String getSalesOrderNumber() {
        return salesOrderNumber;
    }

    public void setSalesOrderNumber(String salesOrderNumber) {
        this.salesOrderNumber = salesOrderNumber;
    }

    public String getSalesOrderItem() {
        return salesOrderItem;
    }

    public String getResult() {
        StringBuilder result = new StringBuilder();
        result.append("- ");
        result.append("订单").append(this.salesOrderNumber).append("/").append(this.salesOrderItem);
        if (StringUtils.containsIgnoreCase(this.queryResult, "全部完成出货") || StringUtils.containsIgnoreCase(this.queryResult, "已出发货单") || StringUtils.containsIgnoreCase(this.queryResult, "库房已收货")) {
            result.append(" ");
            result.append(StringUtils.removeStart(this.queryResult, "订单"));
        } else {
            result.append(" 物料号 ").append(this.material).append(" 数量 ").append(new BigDecimal(String.valueOf(this.quantity)).stripTrailingZeros().toPlainString());
            result.append(", ");
            result.append(this.queryResult);
        }
        return result.toString();
    }

    public void setSalesOrderItem(String salesOrderItem) {
        this.salesOrderItem = salesOrderItem;
    }

    public String getMaterial() {
        return material;
    }

    public void setMaterial(String material) {
        this.material = material;
    }

    public double getQuantity() {
        return quantity;
    }

    public void setQuantity(double quantity) {
        this.quantity = quantity;
    }

    public String getQueryResult() {
        return queryResult;
    }

    public void setQueryResult(String queryResult) {
        this.queryResult = queryResult;
    }
}
