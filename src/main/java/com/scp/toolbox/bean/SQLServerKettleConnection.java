package com.scp.toolbox.bean;

import org.w3c.dom.Document;
import org.w3c.dom.Element;

import java.util.ArrayList;
import java.util.List;

public class SQLServerKettleConnection {
    private String name;
    private String server;
    private String type = "MSSQLNATIVE";
    private String access = "Native";
    private String database;
    private String port;
    private String username;
    private String password;
    private String servername;
    private String data_tablespace;
    private String index_tablespace;
    private List<KettleConnectionAttribute> attributes = new ArrayList<>();

    public Element getElement(Document document) {
        org.w3c.dom.Element connection = document.createElement("connection");
        connection.appendChild(document.createElement("name")).setTextContent(this.getName());
        connection.appendChild(document.createElement("server")).setTextContent(this.getServer());
        connection.appendChild(document.createElement("type")).setTextContent(this.getType());
        connection.appendChild(document.createElement("access")).setTextContent(this.getAccess());
        connection.appendChild(document.createElement("database")).setTextContent(this.getDatabase());
        connection.appendChild(document.createElement("port")).setTextContent(this.getPort());
        connection.appendChild(document.createElement("username")).setTextContent(this.getUsername());
        connection.appendChild(document.createElement("password")).setTextContent(this.getPassword());
        connection.appendChild(document.createElement("servername")).setTextContent(this.getServername());
        connection.appendChild(document.createElement("data_tablespace")).setTextContent(this.getData_tablespace());
        connection.appendChild(document.createElement("index_tablespace")).setTextContent(this.getIndex_tablespace());

        org.w3c.dom.Element attributes = document.createElement("attributes");
        for (KettleConnectionAttribute attribute : this.getAttributes()) {
            Element subAttributes = document.createElement("attribute");
            subAttributes.appendChild(document.createElement("code")).setTextContent(attribute.getCode());
            subAttributes.appendChild(document.createElement("attribute")).setTextContent(attribute.getAttribute());
            attributes.appendChild(subAttributes);
        }
        connection.appendChild(attributes);
        return connection;
    }

    public SQLServerKettleConnection(String name, String database, String server, String port, String username, String password) {
        this.name = name;
        this.database = database;
        this.server = server;
        this.port = port;
        this.username = username;
        this.password = password;
        this.initConfig();
    }

    public void initConfig() {
        attributes.add(new KettleConnectionAttribute("FORCE_IDENTIFIERS_TO_LOWERCASE", "N"));
        attributes.add(new KettleConnectionAttribute("FORCE_IDENTIFIERS_TO_UPPERCASE", "N"));
        attributes.add(new KettleConnectionAttribute("IS_CLUSTERED", "N"));
        attributes.add(new KettleConnectionAttribute("MSSQLUseIntegratedSecurity", "false"));
        attributes.add(new KettleConnectionAttribute("MSSQL_DOUBLE_DECIMAL_SEPARATOR", "N"));
        attributes.add(new KettleConnectionAttribute("PORT_NUMBER", this.getPort()));
        attributes.add(new KettleConnectionAttribute("PRESERVE_RESERVED_WORD_CASE", "Y"));
        attributes.add(new KettleConnectionAttribute("QUOTE_ALL_FIELDS", "N"));
        attributes.add(new KettleConnectionAttribute("SUPPORTS_BOOLEAN_DATA_TYPE", "Y"));
        attributes.add(new KettleConnectionAttribute("SUPPORTS_TIMESTAMP_DATA_TYPE", "Y"));
        attributes.add(new KettleConnectionAttribute("USE_POOLING", "N"));
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getServer() {
        return server;
    }

    public void setServer(String server) {
        this.server = server;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getAccess() {
        return access;
    }

    public void setAccess(String access) {
        this.access = access;
    }

    public String getDatabase() {
        return database;
    }

    public void setDatabase(String database) {
        this.database = database;
    }

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getServername() {
        return servername;
    }

    public void setServername(String servername) {
        this.servername = servername;
    }

    public String getData_tablespace() {
        return data_tablespace;
    }

    public void setData_tablespace(String data_tablespace) {
        this.data_tablespace = data_tablespace;
    }

    public String getIndex_tablespace() {
        return index_tablespace;
    }

    public void setIndex_tablespace(String index_tablespace) {
        this.index_tablespace = index_tablespace;
    }

    public List<KettleConnectionAttribute> getAttributes() {
        return attributes;
    }

    public void setAttributes(List<KettleConnectionAttribute> attributes) {
        this.attributes = attributes;
    }
}
