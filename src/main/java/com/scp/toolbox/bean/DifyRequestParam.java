package com.scp.toolbox.bean;

import com.alibaba.fastjson.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DifyRequestParam {

    private Map<String, Object> inputs = new HashMap<>();
    private String response_mode = "streaming";
    private String conversation_id = "";
    private String user = "user";
    private String query = "";
    private List<JSONObject> files = new ArrayList<>();

    public Map<String, Object> getInputs() {
        return inputs;
    }

    public void setInputs(Map<String, Object> inputs) {
        this.inputs = inputs;
    }

    public String getResponse_mode() {
        return response_mode;
    }

    public void setResponse_mode(String response_mode) {
        this.response_mode = response_mode;
    }

    public String getConversation_id() {
        return conversation_id;
    }

    public void setConversation_id(String conversation_id) {
        this.conversation_id = conversation_id;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public List<JSONObject> getFiles() {
        return files;
    }

    public void setFiles(List<JSONObject> files) {
        this.files = files;
    }

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public String toJSONString() {
        return JSONObject.toJSONString(this);
    }

    public void setInput(String key, String value) {
        inputs.put(key, value);
    }
}
