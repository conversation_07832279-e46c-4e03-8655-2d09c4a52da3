package com.scp.toolbox.bean;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SEGPTOfficialParam {
    private final String model = "DeepSeek-V3";
    private final double temperature = 0.00001;
    private final int max_tokens = 8019;
    private List<Map<String, String>> messages = new ArrayList<>();

    public SEGPTOfficialParam(String prompt) {
        Map<String, String> map = new HashMap<>();
        map.put("role", "user");
        map.put("content", prompt);
        messages.add(map);
    }

    public String getModel() {
        return model;
    }

    public double getTemperature() {
        return temperature;
    }

    public int getMax_tokens() {
        return max_tokens;
    }

    public List<Map<String, String>> getMessages() {
        return messages;
    }

    public void setMessages(List<Map<String, String>> messages) {
        this.messages = messages;
    }
}
