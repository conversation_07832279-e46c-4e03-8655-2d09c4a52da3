package com.scp.toolbox.bean;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DataDiaogueHanderParam {
    private String topic;
    private String requestId;
    private List<DataDialogueMessage> messages;
    private Map<String, String> params = new HashMap<>();
    private Map<String, String> requestingMap = new HashMap<>();

    public void setParam(String key, String value) {
        params.put(key, value);
    }

    public String getParam(String key) {
        return params.get(key);
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public List<DataDialogueMessage> getMessages() {
        return messages;
    }

    public void setMessages(List<DataDialogueMessage> messages) {
        this.messages = messages;
    }

    public Map<String, String> getParams() {
        return params;
    }

    public void setParams(Map<String, String> params) {
        this.params = params;
    }

    public Map<String, String> getRequestingMap() {
        return requestingMap;
    }

    public void setRequestingMap(Map<String, String> requestingMap) {
        this.requestingMap = requestingMap;
    }
}
