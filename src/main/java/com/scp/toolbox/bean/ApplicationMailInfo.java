package com.scp.toolbox.bean;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ApplicationMailInfo {
    private String id = "";
    private String subject = "";
    private String html = "";
    private List<String> to = new ArrayList<>();
    private List<String> cc = new ArrayList<>();
    private String attachementName = "";
    private String attachement = "";
    private String attachementPath = "";
    private String lastSentTime = "";

    public ApplicationMailInfo() {

    }

    @SuppressWarnings("unchecked")
    public ApplicationMailInfo(Map<String, Object> parameterMap) {
        this.id = (String) parameterMap.get("id");
        this.cc = (List<String>) parameterMap.get("cc");
        this.to = (List<String>) parameterMap.get("to");
        this.html = (String) parameterMap.get("html");
        this.subject = (String) parameterMap.get("subject");
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getHtml() {
        return html;
    }

    public void setHtml(String html) {
        this.html = html;
    }

    public List<String> getTo() {
        return to;
    }

    public void setTo(List<String> to) {
        this.to = to;
    }

    public List<String> getCc() {
        return cc;
    }

    public void setCc(List<String> cc) {
        this.cc = cc;
    }

    public String getAttachementName() {
        return attachementName;
    }

    public void setAttachementName(String attachementName) {
        this.attachementName = attachementName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAttachementPath() {
        return attachementPath;
    }

    public void setAttachementPath(String attachementPath) {
        this.attachementPath = attachementPath;
    }

    public String getAttachement() {
        return attachement;
    }

    public void setAttachement(String attachement) {
        this.attachement = attachement;
    }

    public String getLastSentTime() {
        return lastSentTime;
    }

    public void setLastSentTime(String lastSentTime) {
        if (lastSentTime == null) {
            lastSentTime = "--";
        }
        this.lastSentTime = lastSentTime;
    }
}
