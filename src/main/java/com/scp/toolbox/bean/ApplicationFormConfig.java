package com.scp.toolbox.bean;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ApplicationFormConfig {
    private String id = "";
    private String name = "";
    private String html = "";
    private String bindTo = "";
    private String module = "";
    private String operation = "";
    private String validate = "";
    private List<String> attachement = new ArrayList<>();
    private List<String> attachementName = new ArrayList<>();
    private String afterSave = "";
    private String afterSend = "";
    private String clearTable = "";
    private String calculation = "";
    private String where = "";
    private String columnsOrder = "";
    private List<String> data = new ArrayList<>();
    private String subject = "";
    private List<String> to = new ArrayList<>();
    private List<String> cc = new ArrayList<>();
    private String month = "";
    private String dayType = "";
    private String day = "";
    private String hour = "";
    private String minute = "";

    private String orderBy = "";

    private String memo = "";

    private String excelType = "";

    private String group = "";

    public ApplicationFormConfig() {

    }

    @SuppressWarnings("unchecked")
    public ApplicationFormConfig(Map<String, Object> parameterMap) {
        this.id = (String) parameterMap.get("id");
        this.name = (String) parameterMap.get("name");
        this.html = (String) parameterMap.get("html");
        this.bindTo = (String) parameterMap.get("bindTo");
        this.operation = (String) parameterMap.get("operation");
        this.afterSave = (String) parameterMap.get("afterSave");
        this.afterSend = (String) parameterMap.get("afterSend");
        this.clearTable = (String) parameterMap.get("clearTable");
        this.calculation = (String) parameterMap.get("calculation");
        this.where = (String) parameterMap.get("where");
        this.columnsOrder = (String) parameterMap.get("columnsOrder");
        this.validate = (String) parameterMap.get("validate");
        this.attachement = (List<String>) parameterMap.get("attachement");
        this.attachementName = (List<String>) parameterMap.get("attachementName");
        this.subject = (String) parameterMap.get("subject");
        this.month = (String) parameterMap.get("month");
        this.dayType = (String) parameterMap.get("dayType");
        this.day = (String) parameterMap.get("day");
        this.hour = (String) parameterMap.get("hour");
        this.minute = (String) parameterMap.get("minute");
        this.to = (List<String>) parameterMap.get("to");
        this.cc = (List<String>) parameterMap.get("cc");
        this.module = StringUtils.join((List<String>) parameterMap.get("module"), ",");
        this.data = (List<String>) parameterMap.get("data");
        this.orderBy = (String) parameterMap.get("orderBy");
        this.memo = (String) parameterMap.get("memo");
        this.excelType =(String) parameterMap.get("excelType");
        this.group = (String) parameterMap.get("group");
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHtml() {
        return html;
    }

    public void setHtml(String html) {
        this.html = html;
    }

    public String getBindTo() {
        return bindTo;
    }

    public void setBindTo(String bindTo) {
        this.bindTo = bindTo;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getValidate() {
        return validate;
    }

    public void setValidate(String validate) {
        this.validate = validate;
    }

    public List<String> getAttachement() {
        return attachement;
    }

    public void setAttachement(List<String> attachement) {
        this.attachement = attachement;
    }

    public List<String> getData() {
        return data;
    }

    public void setData(List<String> data) {
        this.data = data;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public List<String> getTo() {
        return to;
    }

    public void setTo(List<String> to) {
        this.to = to;
    }

    public List<String> getCc() {
        return cc;
    }

    public void setCc(List<String> cc) {
        this.cc = cc;
    }

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }

    public String getDayType() {
        return dayType;
    }

    public void setDayType(String dayType) {
        this.dayType = dayType;
    }

    public String getDay() {
        return day;
    }

    public void setDay(String day) {
        this.day = day;
    }

    public String getHour() {
        return hour;
    }

    public void setHour(String hour) {
        this.hour = hour;
    }

    public String getMinute() {
        return minute;
    }

    public void setMinute(String minute) {
        this.minute = minute;
    }

    public List<String> getAttachementName() {
        return attachementName;
    }

    public void setAttachementName(List<String> attachementName) {
        this.attachementName = attachementName;
    }

    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    public String getAfterSave() {
        return afterSave;
    }

    public void setAfterSave(String afterSave) {
        this.afterSave = afterSave;
    }

    public String getWhere() {
        return where;
    }

    public void setWhere(String where) {
        this.where = where;
    }

    public String getAfterSend() {
        return afterSend;
    }

    public void setAfterSend(String afterSend) {
        this.afterSend = afterSend;
    }

    public String getCalculation() {
        return calculation;
    }

    public void setCalculation(String calculation) {
        this.calculation = calculation;
    }

    public String getColumnsOrder() {
        return columnsOrder;
    }

    public void setColumnsOrder(String columnsOrder) {
        this.columnsOrder = columnsOrder;
    }

    public String getClearTable() {
        return clearTable;
    }

    public void setClearTable(String clearTable) {
        this.clearTable = clearTable;
    }

    public String getOrderBy() {
        return orderBy;
    }

    public void setOrderBy(String orderBy) {
        this.orderBy = orderBy;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getExcelType() {
        return excelType;
    }

    public void setExcelType(String excelType) {
        this.excelType = excelType;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }
}
