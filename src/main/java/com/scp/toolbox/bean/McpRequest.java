package com.scp.toolbox.bean;

import com.alibaba.fastjson.JSONObject;

public class McpRequest {
    private String query;
    private String system_prompts; // 所有的tool调用完毕后, 如果需要进一步指示, 可以写在这里, 这种分开的方式, 比合在一起更准确, 但是对Deepseek管用, 对于qwen, 直接写在query里面就可以
    private boolean enable_reasoning = true;
    private String reasoning_filter = ".*"; // 对输出的reasoning进行过滤, 只有符合filter正则表达式的reasoning才会被输出

    public McpRequest() {

    }

    public McpRequest(String query) {
        this.query = query;
    }

    public McpRequest(String query, boolean enableReasoning) {
        this.query = query;
        this.enable_reasoning = enableReasoning;
    }

    public McpRequest(String query, String reasoningFilter) {
        this.query = query;
        this.reasoning_filter = reasoningFilter;
    }

    public McpRequest(String query, boolean enableReasoning, String reasoningFilter) {
        this.query = query;
        this.enable_reasoning = enableReasoning;
        this.reasoning_filter = reasoningFilter;
    }

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public String getSystem_prompts() {
        return system_prompts;
    }

    public void setSystemPrompts(String systemPrompts) {
        this.system_prompts = systemPrompts;
    }

    public boolean isEnable_reasoning() {
        return enable_reasoning;
    }

    public void setEnableReasoning(boolean enableReasoning) {
        this.enable_reasoning = enableReasoning;
    }

    public String getReasoning_filter() {
        return reasoning_filter;
    }

    public void setReasoningFilter(String reasoningFilter) {
        this.reasoning_filter = reasoningFilter;
    }

    public String toString() {
        return JSONObject.toJSONString(this);
    }
}
