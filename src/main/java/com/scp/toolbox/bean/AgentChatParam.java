package com.scp.toolbox.bean;

import java.util.ArrayList;
import java.util.List;

public class AgentChatParam {
    private String agent = "";
    private String user_id;
    private String query;
    private List<String> images = new ArrayList<>();
    private String response_topic;

    public String getAgent() {
        return agent;
    }

    public void setAgent(String agent) {
        this.agent = agent;
    }

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public String getUser_id() {
        return user_id;
    }

    public void setUser_id(String user_id) {
        this.user_id = user_id;
    }

    public String getResponse_topic() {
        return response_topic;
    }

    public void setResponse_topic(String response_topic) {
        this.response_topic = response_topic;
    }

    public void addImages(String image) {
        images.add(image);
    }


    public List<String> getImages() {
        return images;
    }

    public void setImages(List<String> images) {
        this.images = images;
    }
}
