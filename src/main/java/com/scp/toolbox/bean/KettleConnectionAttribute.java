package com.scp.toolbox.bean;

public class KettleConnectionAttribute {

    public KettleConnectionAttribute(String code, String attribute) {
        this.code = code;
        this.attribute = attribute;
    }

    private String code;
    private String attribute;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getAttribute() {
        return attribute;
    }

    public void setAttribute(String attribute) {
        this.attribute = attribute;
    }
}
