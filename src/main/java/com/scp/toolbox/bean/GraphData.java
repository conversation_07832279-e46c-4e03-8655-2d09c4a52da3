package com.scp.toolbox.bean;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class GraphData {
    private String id;
    private String name;
    private String type;
    private Map<String, Map<String, String>> itemStyle = new HashMap<>() {{
        put("normal", new HashMap<>());
    }};
    private String symbol = "rect";
    private List<Double> symbolSize = new ArrayList<>() {{
        add(60d);
        add(34d);
    }};
    private double x;
    private double y;
    private String value = "0";
    private Map<String, Boolean> label = new HashMap<>() {{
        put("show", true);
    }};
    private int category = 0;

    public void setColor(String color) {
        itemStyle.get("normal").put("color", color);
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Map<String, Map<String, String>> getItemStyle() {
        return itemStyle;
    }

    public void setItemStyle(Map<String, Map<String, String>> itemStyle) {
        this.itemStyle = itemStyle;
    }

    public String getSymbol() {
        return symbol;
    }

    public void setSymbol(String symbol) {
        this.symbol = symbol;
    }

    public List<Double> getSymbolSize() {
        return symbolSize;
    }

    public void setSymbolSize(List<Double> symbolSize) {
        this.symbolSize = symbolSize;
    }

    public double getX() {
        return x;
    }

    public void setX(double x) {
        this.x = x;
    }

    public double getY() {
        return y;
    }

    public void setY(double y) {
        this.y = y;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public Map<String, Boolean> getLabel() {
        return label;
    }

    public void setLabel(Map<String, Boolean> label) {
        this.label = label;
    }

    public int getCategory() {
        return category;
    }

    public void setCategory(int category) {
        this.category = category;
    }
}
