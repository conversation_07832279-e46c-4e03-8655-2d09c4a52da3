package com.scp.toolbox.bean;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

public class SoIdentifyParam {
    private String messages;
    private String agent_uuid = "2ec0010a0c9849bfa7d99523e22b6b42";

    public SoIdentifyParam() {

    }

    public SoIdentifyParam(String messages) {
        this.messages = this.parseMessage(messages);
    }

    public String getMessages() {
        return messages;
    }

    public void setMessages(String message) {
        this.messages = this.parseMessage(message);
    }

    public String getAgent_uuid() {
        return agent_uuid;
    }

    public void setAgent_uuid(String agent_uuid) {
        this.agent_uuid = agent_uuid;
    }

    public String parseMessage(String message) {
        JSONArray array = new JSONArray();
        JSONObject obj = new JSONObject();
        obj.put("role", "user");
        obj.put("content", message);

        array.add(obj);
        return array.toJSONString();
    }
}
