package com.scp.toolbox.bean;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class QwenParam {
    private List<Map<String, String>> content = new ArrayList<>();

    public QwenParam(String content) {
        Map<String, String> map = new HashMap<>();
        map.put("role", "user");
        map.put("content", content);
        this.content.add(map);
    }

    public List<Map<String, String>> getContent() {
        return content;
    }

    public void setContent(List<Map<String, String>> content) {
        this.content = content;
    }
}
