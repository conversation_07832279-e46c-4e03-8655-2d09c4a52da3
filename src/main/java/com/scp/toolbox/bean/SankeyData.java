package com.scp.toolbox.bean;

import java.util.HashMap;
import java.util.Map;

public class SankeyData {
    private String name;
    private Map<String, Map<String, String>> itemStyle = new HashMap<>() {{
        put("normal", new HashMap<>());
    }};
    private long value = 0L;
    private Map<String, Boolean> label = new HashMap<>() {{
        put("show", true);
    }};
    private int category = 0;

    public void setColor(String color) {
        itemStyle.get("normal").put("color", color);
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Map<String, Map<String, String>> getItemStyle() {
        return itemStyle;
    }

    public void setItemStyle(Map<String, Map<String, String>> itemStyle) {
        this.itemStyle = itemStyle;
    }

    public long getValue() {
        return value;
    }

    public void setValue(long value) {
        this.value = value;
    }

    public Map<String, Boolean> getLabel() {
        return label;
    }

    public void setLabel(Map<String, Boolean> label) {
        this.label = label;
    }

    public int getCategory() {
        return category;
    }

    public void setCategory(int category) {
        this.category = category;
    }
}
