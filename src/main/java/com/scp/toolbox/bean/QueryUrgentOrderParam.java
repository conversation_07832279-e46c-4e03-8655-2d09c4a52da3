package com.scp.toolbox.bean;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class QueryUrgentOrderParam {

    private Map<String, String> header = new HashMap<>();

    private List<Map<String, String>> body = new ArrayList<>();

    public void setToken(String token) {
        header.put("token", token);
    }

    public void addOrder(String order, String item) {
        body.add(Map.of("salesOrderNumber", order, "salesOrderItem", item));
    }


    public Map<String, String> getHeader() {
        return header;
    }

    public void setHeader(Map<String, String> header) {
        this.header = header;
    }

    public List<Map<String, String>> getBody() {
        return body;
    }

    public void setBody(List<Map<String, String>> body) {
        this.body = body;
    }
}
