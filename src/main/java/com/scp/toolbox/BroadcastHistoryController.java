package com.scp.toolbox;


import com.scp.toolbox.service.IBroadcastHistoryService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/toolbox/broadcast_history", parent = "menuA00")
public class BroadcastHistoryController  extends ControllerHelper {

    public static final String PARENT_CODE = "menuA30";

    @Resource
    private IBroadcastHistoryService broadcastHistoryService;

    @SchneiderRequestMapping("/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        return broadcastHistoryService.initPage(parameterMap);
    }

    @SchneiderRequestMapping("/query_broadcast_config")
    public Response queryBroadcastConfig(HttpServletRequest request) {
        super.pageLoad(request);
        return broadcastHistoryService.queryBroadcastConfig(parameterMap);
    }

}
