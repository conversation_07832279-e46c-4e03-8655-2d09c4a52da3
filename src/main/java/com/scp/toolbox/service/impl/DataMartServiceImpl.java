package com.scp.toolbox.service.impl;

import com.alibaba.fastjson.JSON;
import com.starter.utils.Utils;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.kettle.KettleFeignClient;
import com.scp.toolbox.bean.*;
import com.scp.toolbox.dao.IDataMartDao;
import com.scp.toolbox.service.IDataMartService;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.File;
import java.io.FileInputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Scope("prototype")
@Transactional
public class DataMartServiceImpl implements IDataMartService {

    public static final String PARENT_CODE = "menuA90";

    @Resource
    private IDataMartDao dataMarketDao;

    @Resource
    private KettleFeignClient kettleFeignClient;

    @Resource
    private Response response;

    @Value("${file.upload.path}")
    private String appUploadPath;

    public static final String SOURCE_COLOR = "#3dcd58";
    public static final String DESC_COLOR = "#fc8452";

    @Override
    public Response initPage(String userid, Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("existsGroup", dataMarketDao.queryExistsGroup());
        List<Map<String, Object>> dbList = dataMarketDao.queryDBConfigList();
        List<Map<String, Object>> source = new ArrayList<>();
        List<Map<String, Object>> destination = new ArrayList<>();
        for (Map<String, Object> db : dbList) {
            String type = (String) db.get("TYPE");
            if (StringUtils.equalsIgnoreCase(type, "SOURCE")) {
                source.add(new HashMap<>() {{
                    put("ROW_ID", db.get("ROW_ID"));
                    put("NAME", db.get("NAME"));
                }});
            } else if (StringUtils.equalsIgnoreCase(type, "DESTINATION")) {
                destination.add(new HashMap<>() {{
                    put("ROW_ID", db.get("ROW_ID"));
                    put("NAME", db.get("NAME"));
                }});
            }
        }
        resultMap.put("source", source);
        resultMap.put("destination", destination);
        return response.setBody(resultMap);
    }

    @Override
    public Response queryTaskList(String userid, Map<String, Object> parameterMap) {
        String authType = StringUtils.upperCase(StringUtils.trim(dataMarketDao.queryAuthDetails(userid, PARENT_CODE)));
        return response.setBody(Utils.parseTreeNodes(dataMarketDao.queryRootTaskList(userid, authType)));
    }


    @Override
    public Response queryReport1(Map<String, Object> parameterMap) {
        return response.setBody(dataMarketDao.queryReport1(parameterMap));
    }

    @Override
    public Response queryReport2(Map<String, Object> parameterMap) {
        String type = (String) parameterMap.get("type");
        if ("By Rows".equalsIgnoreCase(type)) {
            parameterMap.put("columns", "LINES_OUTPUT");
        } else {
            parameterMap.put("columns", "TIME_COST");
        }

        List<Map<String, Object>> dataList = dataMarketDao.queryReport2(parameterMap);
        Map<String, List<List<Object>>> resultMap = new HashMap<>();

        List<List<Object>> descList = new ArrayList<>();
        List<List<Object>> srouceList = new ArrayList<>();

        for (Map<String, Object> map : dataList) {
            if (map != null) {
                List<Object> temp = new ArrayList<>();
                temp.add(map.get("TIME_NAME"));
                temp.add(map.get("RATE"));

                if (Utils.parseBigDecimal(map.get("SOURCE")).compareTo(BigDecimal.ONE) == 0) {
                    srouceList.add(temp);
                } else {
                    descList.add(temp);
                }
            }
        }

        resultMap.put("DESC", descList);
        resultMap.put("SOURCE", srouceList);

        return response.setBody(resultMap);
    }

    @Override
    public Response queryReport3(Map<String, Object> parameterMap) {
        String type = (String) parameterMap.get("type");
        if ("By Rows".equalsIgnoreCase(type)) {
            parameterMap.put("columns", "LINES_OUTPUT");
        } else {
            parameterMap.put("columns", "TIME_COST");
        }

        List<Map<String, Object>> dataList = dataMarketDao.queryReport3(parameterMap);
        Map<String, List<Object>> resultMap = new HashMap<>();
        List<Object> colors = new ArrayList<>();
        List<Object> xAxis = new ArrayList<>();
        List<Object> yAxis = new ArrayList<>();
        resultMap.put("colors", colors);
        resultMap.put("xAxis", xAxis);
        resultMap.put("yAxis", yAxis);
        for (Map<String, Object> map : dataList) {
            xAxis.add(map.get("XAXIS"));
            yAxis.add(map.get("YAXIS"));
            colors.add(Utils.parseBigDecimal(map.get("SOURCE")).compareTo(BigDecimal.ONE) == 0 ? SOURCE_COLOR : DESC_COLOR);
        }
        Collections.reverse(xAxis);
        Collections.reverse(yAxis);
        Collections.reverse(colors);
        return response.setBody(resultMap);
    }

    @Override
    public Response queryReport4(Map<String, Object> parameterMap) {
        return response.setBody(dataMarketDao.queryReport4(parameterMap));
    }

    @Override
    public Response queryReport5(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(dataMarketDao.queryReport5Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(dataMarketDao.queryReport5(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public Response queryReport8(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(dataMarketDao.queryReport8Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(dataMarketDao.queryReport8(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public Response queryReport8Logs(Map<String, Object> parameterMap) {
        return response.setBody(dataMarketDao.queryReport8Logs(parameterMap));
    }

    @Override
    public Response queryReport8Steps(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(dataMarketDao.queryReport8StepsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(dataMarketDao.queryReport8Steps(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public Response saveNewJob(Map<String, Object> parameterMap) {
        parameterMap.put("month", JSON.toJSONString(parameterMap.get("month")));
        parameterMap.put("day", JSON.toJSONString(parameterMap.get("day")));
        parameterMap.put("hour", JSON.toJSONString(parameterMap.get("hour")));
        parameterMap.put("minute", JSON.toJSONString(parameterMap.get("minute")));
        parameterMap.put("parameters", JSON.toJSONString(parameterMap.get("parameters")));
        String trans_name = Utils.randomStr(16);
        parameterMap.put("trans_name", trans_name);

        File file = new File(appUploadPath + parameterMap.get("uploadedFilePath"));
        if (file.exists() == true) {
            try {
                if (this.parseKettleScript(file, trans_name)) {
                    dataMarketDao.saveNewJob(parameterMap);
                } else {
                    response.setBody(500);
                }
            } catch (Exception e) {
                response.setError(new Exception("Kettle script invalid, " + e.getMessage()));
            }
        } else {
            response.setError(new Exception("Kettle script not found!"));
        }
        return response;
    }

    private boolean parseKettleScript(File file, String trans_name) throws Exception {
        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
        DocumentBuilder db = dbf.newDocumentBuilder();
        Document doc = db.parse(file);

        Element root = doc.getDocumentElement();

        List<String> allConnections = dataMarketDao.queryAllConnections();
        // connection
        // getElementsByTagName方法会取所有connection标签, 所以只能用getChildNodes来代替
        NodeList childNodes = root.getChildNodes();
        for (int i = 0; i < childNodes.getLength(); i++) {
            if (childNodes.item(i) instanceof Element childNode) {
                if ("connection".equals(childNode.getTagName())) {
                    String username = childNode.getElementsByTagName("username").item(0).getTextContent();
                    String server = childNode.getElementsByTagName("server").item(0).getTextContent();
                    String type = childNode.getElementsByTagName("type").item(0).getTextContent();
                    String port = childNode.getElementsByTagName("port").item(0).getTextContent();

                    String key = username + '.' + type + '@' + server + ':' + port;
                    if (allConnections.contains(key.toLowerCase()) == false) {
                        return false;
                    }
                }
            }
        }

        // steps
        NodeList stepsChildNodes = root.getChildNodes();
        List<Map<String, String>> stepList = new ArrayList<>();
        for (int i = 0; i < stepsChildNodes.getLength(); i++) {
            if (stepsChildNodes.item(i) instanceof Element) {
                Element childNode = (Element) stepsChildNodes.item(i);
                if ("step".equals(childNode.getTagName())) {
                    Map<String, String> map = new HashMap<>();
                    map.put("id", trans_name);
                    map.put("name", childNode.getElementsByTagName("name").item(0).getTextContent());
                    map.put("type", childNode.getElementsByTagName("type").item(0).getTextContent());
                    Element connection = (Element) childNode.getElementsByTagName("connection").item(0);
                    if (connection != null) {
                        map.put("connection", connection.getTextContent());
                    }
                    Element gui = (Element) childNode.getElementsByTagName("GUI").item(0);
                    map.put("xloc", gui.getElementsByTagName("xloc").item(0).getTextContent());
                    map.put("yloc", gui.getElementsByTagName("yloc").item(0).getTextContent());
                    stepList.add(map);
                }
            }
        }
        dataMarketDao.mergeJobSteps(stepList, trans_name);

        // steps order
        List<Map<String, String>> orderList = new ArrayList<>();
        Element order = (Element) root.getElementsByTagName("order").item(0);
        NodeList hops = order.getElementsByTagName("hop");
        for (int i = 0; i < hops.getLength(); i++) {
            Element hop = (Element) hops.item(i);
            Map<String, String> map = new HashMap<>();
            map.put("id", trans_name);
            map.put("from", hop.getElementsByTagName("from").item(0).getTextContent());
            map.put("to", hop.getElementsByTagName("to").item(0).getTextContent());
            map.put("enabled", hop.getElementsByTagName("enabled").item(0).getTextContent());
            orderList.add(map);
        }
        dataMarketDao.mergeJobOrders(orderList, trans_name);

        return true;
    }

    @Override
    @SuppressWarnings("unchecked")
    public void downloadKettleTemplate(Map<String, Object> parameterMap, HttpServletResponse response) throws Exception {
        List<String> connectionID = new ArrayList<>();
        connectionID.addAll((List<String>) parameterMap.get("source"));
        connectionID.addAll((List<String>) parameterMap.get("destination"));

        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder documentBuilder = factory.newDocumentBuilder();
        Document document = documentBuilder.parse(DataMartServiceImpl.class.getClassLoader().getResourceAsStream("files/KettleTemplate.ktr"));

        if (connectionID.isEmpty() == false) {
            List<Map<String, Object>> connectionList = dataMarketDao.queryAvaliableConnection(connectionID);
            Element root = document.getDocumentElement();

            for (Map<String, Object> map : connectionList) {
                String name = (String) map.get("NAME");
                String ip = (String) map.get("IP");
                String port = (String) map.get("PORT");
                String database = (String) map.get("DATABASE");
                String username = (String) map.get("USERNAME");
                String password = (String) map.get("PASSWORD");
                String dbType = (String) map.get("DB_TYPE");

                if ("MYSQL".equalsIgnoreCase(dbType)) {
                    root.appendChild(new MySQLKettleConnection(name, database, ip, port, username, password).getElement(document));
                } else if ("ORACLE".equalsIgnoreCase(dbType)) {
                    root.appendChild(new OracleKettleConnection(name, database, ip, port, username, password).getElement(document));
                } else if ("MSSQLNATIVE".equalsIgnoreCase(dbType)) {
                    root.appendChild(new SQLServerKettleConnection(name, database, ip, port, username, password).getElement(document));
                } else if ("POSTGRESQL".equalsIgnoreCase(dbType)) {
                    root.appendChild(new PostgreSQLKettleConnection(name, database, ip, port, username, password).getElement(document));
                } else if ("REDSHIFT".equalsIgnoreCase(dbType)) {
                    root.appendChild(new RedshiftKettleConnection(name, database, ip, port, username, password).getElement(document));
                }
            }
        }

        String fileName = "ScriptTemplate_" + Utils.randomStr(4) + ".ktr";
        response.resetBuffer();
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        response.setContentType("APPLICATION/OCTET-STREAM;charset=UTF-8");// 设置类型
        response.setHeader("Cache-Control", "no-cache");// 设置头
        response.addHeader("filename", fileName);// 设置头
        response.addHeader("Access-Control-Expose-Headers", "filename");// 设置头
        response.setDateHeader("Expires", 0);

        TransformerFactory transFactory = TransformerFactory.newInstance();
        Transformer transFormer = transFactory.newTransformer();
        DOMSource domSource = new DOMSource(document);
        StreamResult xmlResult = new StreamResult(response.getOutputStream());
        transFormer.transform(domSource, xmlResult);
    }

    @Override
    public Response uploadJobScript(MultipartFile file) throws Exception {
        File tempFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), ".tmp");
        file.transferTo(tempFile);

        String orgFileName = file.getOriginalFilename();
        orgFileName = orgFileName == null ? "kettleJob.ktr" : orgFileName;
        String newFileName = StringUtils.substring(orgFileName, 0, orgFileName.lastIndexOf("."));
        String newFileSubfix = StringUtils.substring(orgFileName, orgFileName.lastIndexOf("."));
        String subPath = "data-mart/" + new SimpleDateFormat("yyyyMMdd").format(new Date()) + "/" + newFileName + "_" + Utils.randomStr(4) + newFileSubfix;
        File newFile = new File(appUploadPath + subPath);
        Map<String, String> resultMap = new HashMap<>();
        if (newFile.getParentFile().exists() == false) {
            if (newFile.getParentFile().mkdirs() == false) {
                resultMap.put("name", "Upload Error!");
                resultMap.put("path", "");
                return response.setBody(resultMap);
            }
        }
        FileUtils.copyFile(tempFile, newFile);
        resultMap.put("name", orgFileName);
        resultMap.put("path", subPath);

        return response.setBody(resultMap);
    }

    @Override
    public void downloadFile(Map<String, Object> parameterMap, HttpServletResponse response) throws Exception {
        File file = new File(appUploadPath + parameterMap.get("path"));
        if (file.exists()) {
            String fileName = file.getName();
            response.resetBuffer();
            response.setHeader("Content-disposition", "attachment;filename=" + fileName);
            response.setContentType("APPLICATION/OCTET-STREAM;charset=UTF-8");// 设置类型
            response.setHeader("Cache-Control", "no-cache");// 设置头
            response.addHeader("filename", fileName);// 设置头
            response.addHeader("Access-Control-Expose-Headers", "filename");// 设置头
            response.setDateHeader("Expires", 0);

            FileInputStream in = new FileInputStream(file);
            byte[] buf = new byte[4096];
            int readLength;
            while (((readLength = in.read(buf)) != -1)) {
                response.getOutputStream().write(buf, 0, readLength);
            }
            in.close();
        } else {
            throw new Exception("File not found!");
        }
    }

    @Override
    public Response queryReport7(Map<String, Object> parameterMap) {
        List<Map<String, Object>> steps = dataMarketDao.queryReport7Steps(parameterMap);
        Map<String, Object> resultMap = new HashMap<>();
        List<GraphData> stepsList = new ArrayList<>();

        List<Double> symbolSize = new ArrayList<>() {{
            add(80d);
            add(34d);
        }};

        for (Map<String, Object> step : steps) {
            GraphData graphData = new GraphData();
            graphData.setId((String) step.get("NAME"));
            graphData.setType((String) step.get("TYPE"));
            graphData.setName((String) step.get("NAME"));
            double x = Utils.parseDouble(step.get("XLOC"));
            double y = Utils.parseDouble(step.get("YLOC"));
            graphData.setX(x * 1.2);
            graphData.setY(y);
            graphData.setSymbolSize(symbolSize);

            stepsList.add(graphData);
        }

        List<LinkData> linksList = new ArrayList<>();
        List<Map<String, Object>> links = dataMarketDao.queryReport7Links(parameterMap);
        for (int i = 0; i < links.size(); i++) {
            Map<String, Object> link = links.get(i);
            LinkData linkData = new LinkData();
            linkData.setId(String.valueOf(i));
            linkData.setSource((String) link.get("STEP_FROM"));
            linkData.setTarget((String) link.get("STEP_TO"));
            if ("N".equalsIgnoreCase((String) link.get("ENABLE"))) {
                linkData.setColor("#7b7b7b");
            }
            linksList.add(linkData);
        }
        resultMap.put("data", stepsList);
        resultMap.put("links", linksList);
        return response.setBody(resultMap);
    }

    @Override
    public Response queryReport6(Map<String, Object> parameterMap) {
        Map<String, Object> result = dataMarketDao.queryReport6(parameterMap);
        return response.setBody(result);
    }

    @Override
    public Response deleteJob(Map<String, Object> parameterMap) {
        dataMarketDao.deleteJob(parameterMap);
        return response;
    }

    @Override
    public Response modifyJob(Map<String, Object> parameterMap) throws Exception {
        parameterMap.put("month", JSON.toJSONString(parameterMap.get("month")));
        parameterMap.put("day", JSON.toJSONString(parameterMap.get("day")));
        parameterMap.put("hour", JSON.toJSONString(parameterMap.get("hour")));
        parameterMap.put("minute", JSON.toJSONString(parameterMap.get("minute")));
        parameterMap.put("parameters", JSON.toJSONString(parameterMap.get("parameters")));
        String trans_name = (String) parameterMap.get("trans_name");

        File file = new File(appUploadPath + parameterMap.get("uploadedFilePath"));
        if (file.exists() == true) {
            if (this.parseKettleScript(file, trans_name)) {
                dataMarketDao.modifyJob(parameterMap);
            } else {
                response.setBody(500);
            }
        } else {
            response.setError(new Exception("Kettle script not found!"));
        }
        return response;
    }

    @Override
    public Response runJob(Map<String, Object> parameterMap) {
        return kettleFeignClient.call((String) parameterMap.get("trans_name"));
    }

    @Override
    public Response queryReport9(Map<String, Object> parameterMap) {
        List<Map<String, Object>> dataList = dataMarketDao.queryReport9(parameterMap);
        Map<String, Object> resultMap = new HashMap<>();
        List<SankeyData> entityList = new ArrayList<>();

        Map<String, Map<String, Long>> tempMap = new LinkedHashMap<>();
        for (Map<String, Object> data : dataList) {
            // 如果destination是SCP-DSS, 说明输入源是Kettle, 则指明数据源为Kettle
            if ("SCP-DSS".equalsIgnoreCase((String) data.get("ENTITY_TO"))) {
                String from = (String) data.get("ENTITY_FROM");
                Map<String, Long> fromMap = tempMap.computeIfAbsent(from, k -> new HashMap<>());
                Long existsInput = fromMap.getOrDefault("INPUT", 0L);
                fromMap.put("INPUT", existsInput + Utils.parseLong(data.get("LINES_OUTPUT")));
                fromMap.put("DBTYPE", 0L); // 0 Source
            } else {
                String from = (String) data.get("ENTITY_FROM");
                Map<String, Long> fromMap = tempMap.computeIfAbsent(from, k -> new HashMap<>());
                Long existsInput = fromMap.getOrDefault("INPUT", 0L);
                fromMap.put("INPUT", existsInput + Utils.parseLong(data.get("LINES_INPUT")));
                fromMap.put("DBTYPE", 0L); // 0 Source
            }

            String to = (String) data.get("ENTITY_TO");
            Map<String, Long> toMap = tempMap.computeIfAbsent(to, k -> new HashMap<>());
            Long existsOutput = toMap.getOrDefault("INPUT", 0L);
            toMap.put("OUTPUT", existsOutput + Utils.parseLong(data.get("LINES_OUTPUT")));
        }

        for (String key : tempMap.keySet()) {
            Map<String, Long> value = tempMap.get(key);
            SankeyData sankeyData = new SankeyData();
            sankeyData.setName(key);
            sankeyData.setValue(tempMap.get(key).getOrDefault("INPUT", tempMap.get(key).get("OUTPUT")));

            // source的点随机取, 剩下的点围绕source点随机生成
            if (value.get("DBTYPE") != null) {
                sankeyData.setColor(SOURCE_COLOR);
            } else {
                sankeyData.setColor(DESC_COLOR);
            }

            entityList.add(sankeyData);
        }

        List<LinkData> linksList = new ArrayList<>();
        for (Map<String, Object> data : dataList) {
            LinkData linkData = new LinkData();
            if ("SCP-DSS".equalsIgnoreCase((String) data.get("ENTITY_TO"))) {
                linkData.setSource((String) data.get("ENTITY_FROM"));
                linkData.setColor(SOURCE_COLOR);
            } else {
                linkData.setSource((String) data.get("ENTITY_FROM"));
                linkData.setColor(DESC_COLOR);
            }

            linkData.setValue(Utils.parseInt(data.get("LINES_OUTPUT")));
            linkData.setTarget((String) data.get("ENTITY_TO"));
            linksList.add(linkData);
        }
        resultMap.put("data", entityList);
        resultMap.put("links", linksList);
        return response.setBody(resultMap);
    }

    @Override
    public Response queryDiskMapping() {
        return kettleFeignClient.queryDiskMapping();
    }

    public Response queryReport10(Map<String, Object> parameterMap) {
        List<Map<String, Object>> data = dataMarketDao.queryReport10(parameterMap);
        for (int i = 1; i <= data.size(); i++) {
            Map<String, Object> map = data.get(i - 1);
            map.put("KEY", i + ". " + map.get("KEY"));
        }
        return response.setBody(data);
    }
}
