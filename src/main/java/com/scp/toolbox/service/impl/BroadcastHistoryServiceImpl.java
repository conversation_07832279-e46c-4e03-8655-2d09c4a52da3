package com.scp.toolbox.service.impl;

import com.scp.toolbox.dao.IBroadcastHistoryDao;
import com.scp.toolbox.service.IBroadcastHistoryService;
import com.starter.context.bean.Response;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.Utils;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service("broadcastHistoryService")
@Scope("prototype")
@Transactional
public class BroadcastHistoryServiceImpl extends ServiceHelper implements IBroadcastHistoryService {

    @Resource
    private IBroadcastHistoryDao broadcastHistoryDao;

    @Resource
    private Response response;

    @Override
    public Response initPage(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cascader", Utils.parseCascader(broadcastHistoryDao.initPage(parameterMap)));
        return response.setBody(resultMap);
    }

    @Override
    public Response queryBroadcastConfig(Map<String, Object> parameterMap) {
        this.generateCascaderFilterSQL(parameterMap);

        Map<String, Object> result = new HashMap<>();
        result.put("data", broadcastHistoryDao.queryBroadcastConfig(parameterMap));
        result.put("total", broadcastHistoryDao.queryBroadcastConfigCount(parameterMap));
        return response.setBody(result);
    }
}
