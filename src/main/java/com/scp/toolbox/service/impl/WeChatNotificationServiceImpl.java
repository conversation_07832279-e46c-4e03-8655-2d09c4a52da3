package com.scp.toolbox.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.scp.toolbox.dao.IWeChatNotificationDao;
import com.scp.toolbox.service.IWeChatNotificationService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.login.bean.Session;
import com.starter.utils.Utils;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

@Service
@Scope("prototype")
@Transactional
public class WeChatNotificationServiceImpl implements IWeChatNotificationService {

    public static final String PARENT_CODE = "menuAA0";

    @Resource
    private Response response;

    @Resource
    private IWeChatNotificationDao weChatNotificationDao;

    @Override
    public Response initPage(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("existsGroup", weChatNotificationDao.queryExistsGroup());//查询存在的组
        resultMap.put("reportIDList", weChatNotificationDao.queryReportIDList());//维护报表脚标信息
        return response.setBody(resultMap);
    }

    @Override
    public Response queryWeChatNotificationList(Map<String, Object> parameterMap, Session session) {
        String authType = StringUtils.upperCase(StringUtils.trim(weChatNotificationDao.queryAuthDetails(session.getUserid(), PARENT_CODE)));
        parameterMap.put("authType", authType);
        parameterMap.put("userid", session.getUserid());
        return response.setBody(Utils.parseTreeNodes(weChatNotificationDao.queryWeChatNotificationList(parameterMap)));
    }

    @Override
    public Response queryReport1(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = weChatNotificationDao.queryReport1(parameterMap);
        String month = "[" + StringUtils.join(JSON.parseArray((String) resultMap.get("MONTH")), ",") + "]";
        String day = "[" + StringUtils.join(JSON.parseArray((String) resultMap.get("DAY")), ",") + "]";
        String hour = "[" + StringUtils.join(JSON.parseArray((String) resultMap.get("HOUR")), ",") + "]";
        String minute = "[" + StringUtils.join(JSON.parseArray((String) resultMap.get("MINUTE")), ",") + "]";
        resultMap.put("CORN", month.replace("null", " ") + " " + day.replace("null", " ") + " " + hour.replace("null", " ") + " " + minute.replace("null", " "));
        JSONObject configObj = JSON.parseObject((String) resultMap.get("CONFIG"));
        String log = JSON.toJSONString(configObj, true);
        if (resultMap.get("LAST_EXEC_RESULT") != null) {
            log += "\n\n==> " + resultMap.get("LAST_SEND_TO") + "\n\n==> " + resultMap.get("LAST_EXEC_RESULT");
        }
        resultMap.put("LOG_FIELD", log);
        resultMap.putAll(configObj);
        return response.setBody(resultMap);
    }

    @Override
    public Response queryReport2(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(weChatNotificationDao.queryReport2Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(weChatNotificationDao.queryReport2(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public Response saveNotice(Map<String, Object> parameterMap, Session session) {
        String id = Utils.randomStr(12);
        parameterMap.put("id", id);
        parameterMap.put("userid", session.getUserid());
        parameterMap.put("month", JSON.toJSONString(parameterMap.get("month")));
        parameterMap.put("day", JSON.toJSONString(parameterMap.get("day")));
        parameterMap.put("hour", JSON.toJSONString(parameterMap.get("hour")));
        parameterMap.put("minute", JSON.toJSONString(parameterMap.get("minute")));
        JSONObject config = new JSONObject();
        config.put("msg", parameterMap.get("msg"));
        config.put("sleep", parameterMap.get("sleep"));
        config.put("zoom", parameterMap.get("zoom"));
        config.put("id", id);
        config.put("wechat_webhook", parameterMap.get("wechatWebhook"));
        config.put("user", session.getUserid());
        config.put("url", parameterMap.get("url"));
        config.put("areaid", parameterMap.get("areaid"));
        parameterMap.put("config", config.toJSONString());
        weChatNotificationDao.saveNotice(parameterMap);
        return response;
    }

    @Override
    public Response deleteNotice(Map<String, Object> parameterMap) {
        weChatNotificationDao.deleteNotice(parameterMap);
        return response;
    }

    @Override
    public Response modifyNotice(Map<String, Object> parameterMap, Session session) {
        String id = (String) parameterMap.get("id");
        parameterMap.put("userid", session.getUserid());
        parameterMap.put("month", JSON.toJSONString(parameterMap.get("month")));
        parameterMap.put("day", JSON.toJSONString(parameterMap.get("day")));
        parameterMap.put("hour", JSON.toJSONString(parameterMap.get("hour")));
        parameterMap.put("minute", JSON.toJSONString(parameterMap.get("minute")));
        JSONObject config = new JSONObject();
        config.put("msg", parameterMap.get("msg"));
        config.put("sleep", parameterMap.get("sleep"));
        config.put("zoom", parameterMap.get("zoom"));
        config.put("id", id);
        config.put("wechat_webhook", parameterMap.get("wechatWebhook"));
        config.put("user", session.getUserid());
        config.put("url", parameterMap.get("url"));
        config.put("areaid", parameterMap.get("areaid"));
        parameterMap.put("config", config.toJSONString());
        weChatNotificationDao.modifyNotice(parameterMap);
        return response;
    }
}
