package com.scp.toolbox.service.impl;

import com.scp.toolbox.DemandCollectionController;
import com.scp.toolbox.dao.IDemandCollectionDao;
import com.scp.toolbox.service.IDemandCollectionService;
import com.starter.context.bean.scptable.ScpTableHelper;
import com.starter.context.servlet.ServiceHelper;
import com.starter.login.bean.Session;
import com.starter.utils.Utils;
import com.starter.context.bean.*;
import com.starter.utils.excel.ExcelTemplate;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;

import java.util.*;

@Service("demandCollectionService")
@Scope("prototype")
@Transactional
public class DemandCollectionServiceImpl extends ServiceHelper implements IDemandCollectionService {

    @Resource
    private ExcelTemplate excelTemplate;

    @Resource
    private IDemandCollectionDao demandCollectionDao;

    @Resource
    private Response response;

    @Resource
    private ScpTableHelper scpTableHelper;

    @Override
    public Response initPage(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        List<Map<String, String>> questionList;
        List<String> availableColumns;
        questionList = demandCollectionDao.queryQuestionList();
        availableColumns = demandCollectionDao.queryAvailableColumns();
        resultMap.put("questionList", questionList);
        resultMap.put("availableColumns", availableColumns);
        resultMap.put("cascader", Utils.parseCascader(demandCollectionDao.initPage(parameterMap)));
        return response.setBody(resultMap);
    }

    @Override
    public Response queryReport1Details(String userid, Map<String, Object> parameterMap) {
        this.generateCascaderFilterSQL(parameterMap);
        Session session = (Session) parameterMap.get("session");
        String isAdmin = demandCollectionDao.queryPageAdmin(userid, DemandCollectionController.PARENT_CODE);
        parameterMap.put("isAdmin", isAdmin);
        parameterMap.put("userid", session.getUserid());
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(demandCollectionDao.queryReport1DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(demandCollectionDao.queryReport1Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport1Details(String userid, Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateCascaderFilterSQL(parameterMap);
        Session session = (Session) parameterMap.get("session");

        String isAdmin = demandCollectionDao.queryPageAdmin(userid, DemandCollectionController.PARENT_CODE);
        parameterMap.put("isAdmin", isAdmin);
        parameterMap.put("userid", session.getUserid());
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "demand_collection" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.toolbox.dao.IDemandCollectionDao.queryReport1Details", parameterMap);
    }

    @Override
    public Response saveReport1Details(String userid, Map<String, Object> parameterMap) {
        scpTableHelper.setExcludeColumn(new ArrayList<>() {{
            add("UPDATE_BY$");
            add("UPDATE_DATE$");
            add("CREATE_BY$");
            add("CREATE_DATE$");
            add("QUESTION_OWNER_NAME");
            add("QUESTION_OWNER_SESA");
        }});

        boolean isAdmin = "ADMIN".equalsIgnoreCase(demandCollectionDao.queryPageAdmin(userid, DemandCollectionController.PARENT_CODE));

        scpTableHelper.setScpTableInsertHandler((headers, creates) -> demandCollectionDao.createReport1ByTable(headers, creates, userid));
        scpTableHelper.setScpTableDeleteHandler(deletes -> demandCollectionDao.deleteReport1ByTable(deletes, userid, isAdmin));
        scpTableHelper.setScpTableUpdateHandler((pk, updates) -> demandCollectionDao.updateReport1ByTable(pk, updates, userid, isAdmin));
        scpTableHelper.setWarningMessage("You cannot modify the data doesn't belong to you");
        Message message = scpTableHelper.execCRUD(parameterMap);
        return response.setBody(message);
    }
}
