package com.scp.toolbox.service.impl;

import com.scp.toolbox.dao.ICustomReportDao;
import com.scp.toolbox.service.ICustomReportService;
import com.starter.context.bean.CacheRemove;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.configuration.database.DatabaseType;
import com.starter.context.configuration.database.DynamicDataSource;
import com.starter.context.configuration.database.TargetDataSource;
import com.starter.login.bean.Session;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.util.*;

@Service
@Scope("prototype")
@Transactional
public class CustomReportServiceImpl implements ICustomReportService {

    public final static String PARENT_CODE = "menuA20";

    @Resource
    private ICustomReportDao customReportDao;

    @Resource
    private Response response;

    @Resource
    private DynamicDataSource dynamicDataSource;

    @Resource
    private ExcelTemplate excelTemplate;

    @Override
    public Response initPage(Map<String, Object> parameterMap, Session session) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("shareTo", customReportDao.querySharedTo());
        resultMap.put("existsGroup", customReportDao.queryExistsGroup());
        resultMap.put("isAdmin", customReportDao.queryPageAdmin(session.getUserid(), PARENT_CODE) > 0);
        return response.setBody(resultMap);
    }

    @Override
    public Response queryReportList(Map<String, Object> parameterMap, Session session) {
        parameterMap.put("userid", session.getUserid());
        parameterMap.put("isAdmin", customReportDao.queryPageAdmin(session.getUserid(), PARENT_CODE) > 0);
        return response.setBody(Utils.parseTreeNodes(customReportDao.queryReportList(parameterMap)));
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":15m")
    @TargetDataSource(DatabaseType.SCP02_READONLY)
    public Response queryReportHeaders(Map<String, Object> parameterMap) {
        String sql = (String) parameterMap.get("sql");
        Map<String, Object> resultMap = new HashMap<>();
        if (StringUtils.isNotBlank((String) parameterMap.get("selectedId")) == true) {
            Map<String, Object> tempMap = customReportDao.queryReportConfig(parameterMap);
            sql = "select * from (" + tempMap.get("SQL") + ") tt where 1 = 0";
            resultMap.put("cacheable", tempMap.get("ENABLE_CACHE"));
            resultMap.put("group", StringUtils.split((String) tempMap.get("GROUP_NAME"), "."));
            resultMap.put("name", tempMap.get("REPORT_NAME"));
        } else {
            resultMap.put("cacheable", "N");
        }


        List<String> headers = new ArrayList<>();
        List<String> headerTypes = new ArrayList<>();
        try (Connection con = dynamicDataSource.getConnection()) {
            ResultSet rs = con.prepareStatement(sql).executeQuery();

            ResultSetMetaData rsmd = rs.getMetaData();

            for (int i = 1; i <= rsmd.getColumnCount(); i++) {
                headers.add(rsmd.getColumnLabel(i));
                if ("NUMBER".equals(rsmd.getColumnTypeName(i))) {
                    headerTypes.add("numeric");
                } else {
                    headerTypes.add("text");
                }
            }

            rs.close();
            resultMap.put("headers", headers);
            resultMap.put("headerTypes", headerTypes);
            return response.setBody(resultMap);
        } catch (Exception e) {
            return response.setBody("[Exception] \r\n" + Utils.getExceptionMessage(e));
        }
    }

    @Override
    @Cacheable(value = Configuration.APPLICATION_NAME + ":15m", condition = "#cacheable")
    @TargetDataSource(DatabaseType.SCP02_READONLY)
    public Response queryReport(Map<String, Object> parameterMap, boolean cacheable) {
        String sql = (String) parameterMap.get("sql");
        if (StringUtils.isNotBlank((String) parameterMap.get("selectedId")) == true) {
            sql = customReportDao.querySQLByReportID(parameterMap);
        }

        parameterMap.put("sql", sql);
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        int total = customReportDao.queryReportCount(parameterMap);
        page.setTotal(total);
        if (total > 0) {
            page.setData(customReportDao.queryReport(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    @TargetDataSource(DatabaseType.SCP02_READONLY)
    public void downloadReport(Map<String, Object> parameterMap, HttpServletResponse response) {
        String sql = (String) parameterMap.get("sql");
        if (StringUtils.isNotBlank((String) parameterMap.get("selectedId")) == true) {
            sql = customReportDao.querySQLByReportID(parameterMap);
        }

        parameterMap.put("sql", sql);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        String fileName = "query_result_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.toolbox.dao.ICustomReportDao.queryReport", parameterMap);
    }

    @Override
    public Response queryReportConfig(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = customReportDao.queryReportConfig(parameterMap);
        if ("PUBLIC".equals(resultMap.get("AUTH_TYPE"))) {
            resultMap.put("shareTo", new ArrayList<>() {{
                add("[ALL USER]");
            }});
        } else {
            resultMap.put("shareTo", customReportDao.querySharedToByReportID(parameterMap));
        }

        return response.setBody(resultMap);
    }

    @Override
    @SuppressWarnings("unchecked")
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":15m", key = "this")
    public Response modifySelectedReport(Map<String, Object> parameterMap, Session session) {
        customReportDao.deleteSharedToByReportID(parameterMap);

        List<String> shareTo = (List<String>) parameterMap.get("shareTo");
        String authType = shareTo.contains("[ALL USER]") ? "PUBLIC" : "PRIVATE";
        parameterMap.put("authType", authType);
        parameterMap.put("isAdmin", customReportDao.queryPageAdmin(session.getUserid(), PARENT_CODE) > 0);
        if (shareTo.isEmpty() == false) {
            customReportDao.saveSharedTo(parameterMap);
        }
        customReportDao.modifySelectedReport(parameterMap);

        return response;
    }

    @Override
    public void saveReportVisitLog(Map<String, Object> parameterMap) {
        customReportDao.saveReportVisitLog(parameterMap);
    }

    @Override
    public Response deleteReport(Map<String, Object> parameterMap, Session session) {
        parameterMap.put("isAdmin", customReportDao.queryPageAdmin(session.getUserid(), PARENT_CODE) > 0);
        customReportDao.deleteReport(parameterMap);
        return response;
    }

    @Override
    @SuppressWarnings("unchecked")
    public Response saveReport(Map<String, Object> parameterMap) {
        parameterMap.put("reportID", Utils.randomStr(16));
        List<String> shareTo = (List<String>) parameterMap.get("shareTo");
        String authType = shareTo.contains("[ALL USER]") ? "PUBLIC" : "PRIVATE";
        parameterMap.put("authType", authType);
        if (shareTo.isEmpty() == false) {
            customReportDao.saveSharedTo(parameterMap);
        }
        customReportDao.saveReport(parameterMap);
        return response;
    }
}
