package com.scp.toolbox.service.impl;

import com.scp.toolbox.dao.ICsfValidationDao;
import com.scp.toolbox.service.ICsfValidationService;
import com.starter.context.bean.scptable.ScpTableHelper;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.Utils;
import com.starter.context.bean.*;
import com.starter.utils.excel.ExcelTemplate;
import com.starter.utils.excel.SimpleSheetContentsHandler;
import org.apache.poi.xssf.model.StylesTable;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service("csfValidationService")
@Scope("prototype")
@Transactional
public class CsfValidationServiceImpl extends ServiceHelper implements ICsfValidationService {

    @Resource
    private ExcelTemplate excelTemplate;

    @Resource
    private ICsfValidationDao csfValidationDao;

    @Resource
    private Response response;

    @Resource
    private ScpTableHelper scpTableHelper;

    private static Map<String, Map<String, List<String>>> calendarMatchList;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cascader", Utils.parseCascader(csfValidationDao.initPage(parameterMap)));

        LocalDate currentDate = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
        BigDecimal currentMonth = new BigDecimal(currentDate.format(formatter));


        Map<String, List<String>> histResults = new HashMap<>();
        Map<String, List<String>> fullResults = new HashMap<>();
        List<HashMap<String, String>> queryDateOptions = csfValidationDao.queryDateOptions(parameterMap);
        this.generateCalendarMapList(queryDateOptions);
        for (HashMap<String, String> result : queryDateOptions) {
            BigDecimal calendarMonth = new BigDecimal(result.get("CALENDAR_MONTH"));
            String year = result.get("CALENDAR_YEAR");
            String halfYear = result.get("CALENDAR_HALF_YEAR");
            String quarter = result.get("CALENDAR_QUARTER");
            String month = result.get("CALENDAR_MONTH");

            if (calendarMonth.compareTo(currentMonth) <= 0) {
                histResults.computeIfAbsent("CALENDAR_YEAR", k -> new ArrayList<>()).add(year);
                histResults.computeIfAbsent("CALENDAR_HALF_YEAR", k -> new ArrayList<>()).add(halfYear);
                histResults.computeIfAbsent("CALENDAR_QUARTER", k -> new ArrayList<>()).add(quarter);
                histResults.computeIfAbsent("CALENDAR_MONTH", k -> new ArrayList<>()).add(month);
            }
            fullResults.computeIfAbsent("CALENDAR_YEAR", k -> new ArrayList<>()).add(year);
            fullResults.computeIfAbsent("CALENDAR_HALF_YEAR", k -> new ArrayList<>()).add(halfYear);
            fullResults.computeIfAbsent("CALENDAR_QUARTER", k -> new ArrayList<>()).add(quarter);
            fullResults.computeIfAbsent("CALENDAR_MONTH", k -> new ArrayList<>()).add(month);
        }
        histResults.replaceAll((key, monthsList) -> monthsList.stream()
                .sorted(Comparator.reverseOrder())
                .distinct()
                .collect(Collectors.toList()));
        fullResults.replaceAll((key, monthsList) -> monthsList.stream()
                .sorted(Comparator.reverseOrder())
                .distinct()
                .collect(Collectors.toList()));
        resultMap.put("dateOptions", histResults);
        resultMap.put("fullDateOptions", fullResults);

        ArrayList<ArrayList<String>> defaultFilterResults = new ArrayList<>();
        List<HashMap<String, String>> defaultFilters = csfValidationDao.queryDefaultFilters(parameterMap);

        defaultFilters.forEach(defaultFilter -> {
            ArrayList<String> filterList = new ArrayList<>();
            filterList.add(defaultFilter.get("CATEGORY"));
            filterList.add(defaultFilter.get("NAME"));
            defaultFilterResults.add(filterList);
        });
        resultMap.put("defaultFilterResults", defaultFilterResults);
        List<Integer> forecastVersionOptions = csfValidationDao.queryForecastVersionOptions(parameterMap);

        Integer maxFcstVersion = forecastVersionOptions.stream()
                .max(Integer::compareTo)
                .orElse(null);
        resultMap.put("maxFcstVersion", maxFcstVersion);
        resultMap.put("forecastVersionOptions", forecastVersionOptions);
        return response.setBody(resultMap);
    }

    private void generateCalendarMapList(List<HashMap<String, String>> queryDateOptions) {
        Map<String, List<String>> quarterMap = new HashMap<>();
        Map<String, List<String>> halfYearMap = new HashMap<>();
        Map<String, List<String>> yearMap = new HashMap<>();
        for (HashMap<String, String> item : queryDateOptions) {
            quarterMap.computeIfAbsent(item.get("CALENDAR_QUARTER"), k -> new ArrayList<>()).add(item.get("CALENDAR_MONTH"));
            halfYearMap.computeIfAbsent(item.get("CALENDAR_HALF_YEAR"), k -> new ArrayList<>()).add(item.get("CALENDAR_MONTH"));
            yearMap.computeIfAbsent(item.get("CALENDAR_YEAR"), k -> new ArrayList<>()).add(item.get("CALENDAR_MONTH"));
        }
        quarterMap.replaceAll((key, monthsList) -> monthsList.stream()
                .sorted(Comparator.reverseOrder()).distinct().collect(Collectors.toList()));
        halfYearMap.replaceAll((key, monthsList) -> monthsList.stream()
                .sorted(Comparator.reverseOrder()).distinct().collect(Collectors.toList()));
        yearMap.replaceAll((key, monthsList) -> monthsList.stream()
                .sorted(Comparator.reverseOrder()).distinct().collect(Collectors.toList()));

        Map<String, Map<String, List<String>>> temp = new HashMap<>();
        temp.put("CALENDAR_QUARTER", quarterMap);
        temp.put("CALENDAR_HALF_YEAR", halfYearMap);
        temp.put("CALENDAR_YEAR", yearMap);

        calendarMatchList = temp;
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        this.generateCascaderFilterSQL(parameterMap);

        // 针对forecast，by quarter/half-year/year 如果遇到历史和未来的时间断层则采用历史的数据补全，如果未来的时间断层则采用重复运算
        Map<String, Object> report1FcstColumns = (Map<String, Object>) parameterMap.get("report1FcstColumns");

        LocalDate now = LocalDate.now();
        String year = String.valueOf(now.getYear());

        int defaultMonthLength = 0;
        String report1DateType = (String) parameterMap.get("report1DateType");
        defaultMonthLength = switch (report1DateType) {
            case "CALENDAR_QUARTER" -> 3;
            case "CALENDAR_HALF_YEAR" -> 6;
            case "CALENDAR_YEAR" -> 12;
            default -> defaultMonthLength;
        };

        List<String> pivotMonthList = new ArrayList<>();
        if (!"CALENDAR_MONTH".equalsIgnoreCase(report1DateType)) {
            for (Map.Entry<String, Object> entry : report1FcstColumns.entrySet()) {
                List<String> monthList = (List<String>) entry.getValue();
                int currentMonthSize = monthList.size();
                if (year.equals(entry.getKey().substring(0, 4))) {
                    while (currentMonthSize != defaultMonthLength) {
                        String month = calendarMatchList.get(report1DateType).get(entry.getKey()).get(currentMonthSize);
                        monthList.add(month);
                        currentMonthSize += 1;
                        pivotMonthList.add(month);
                    }
                } else {
                    String mon = (String) ((List<?>) entry.getValue()).get(currentMonthSize - 1);
                    while (currentMonthSize != defaultMonthLength) {
                        monthList.add(mon);
                        currentMonthSize += 1;
                    }
                }
                report1FcstColumns.put(entry.getKey(), monthList);
            }
        }
        parameterMap.put("pivotMonthList", pivotMonthList);

        List<LinkedHashMap<String, Object>> dataList = csfValidationDao.queryReport1(parameterMap);
        return response.setBody(dataList);
    }

    private final List<String> report2FileColumns = new ArrayList<>() {
        {
            add("MATERIAL");
            add("SALES_ORGANIZATION");
            add("CUSTOMER_CODE");
            add("IS_VALID$");
            add("SALES_GROUP");
            add("NET_NET_PRICE_RMB");
            add("NET_NET_PRICE_HKD");
            add("MONTH01");
            add("MONTH02");
            add("MONTH03");
            add("MONTH04");
            add("MONTH05");
            add("MONTH06");
            add("MONTH07");
            add("MONTH08");
            add("MONTH09");
            add("MONTH10");
            add("MONTH11");
            add("MONTH12");
            add("MONTH13");
            add("MONTH14");
            add("MONTH15");
            add("MONTH16");
            add("MONTH17");
            add("MONTH18");
            add("MONTH19");
            add("MONTH20");
            add("MONTH21");
            add("MONTH22");
            add("MONTH23");
            add("MONTH24");
            add("MONTH25");
        }
    };

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2(Map<String, Object> parameterMap) {
        parameterMap.put("report2FileColumns", report2FileColumns);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(csfValidationDao.queryReport2Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(csfValidationDao.queryReport2(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport2(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "csf_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.toolbox.dao.ICsfValidationDao.queryReport2", parameterMap);
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response uploadReport2(String userid, MultipartFile file) throws Exception {
        File tempFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), ".tmp");
        file.transferTo(tempFile);

        List<Map<String, Object>> data = new ArrayList<>();
        excelTemplate.read(tempFile, 1, new SimpleSheetContentsHandler() {
            @Override
            public void handleRow(int rowNum) {

                if (rowNum == 0) {
                    return;
                }
                Map<String, Object> map = new HashMap<>();
                for (int i = 0; i < report2FileColumns.size(); i++) {
                    map.put(report2FileColumns.get(i), row.get(i));
                }
                data.add(map);
                if (data.size() >= 64) {
                    csfValidationDao.insertReport2DataTemp(data);
                    data.clear();
                }
            }
        }, new StylesTable());

        if (!data.isEmpty()) {
            csfValidationDao.insertReport2DataTemp(data);
            data.clear();
        }

        csfValidationDao.mergeReport2Data(userid);

        if (!tempFile.delete()) {
            System.err.println(tempFile.getAbsolutePath() + " delete failed");
        }

        return response;
    }

    @Override
    public void downloadReport2Template(Map<String, Object> parameterMap, HttpServletResponse response) {
        String fileName = "mr3_csf_validation_template.xlsx";
        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        resultList.add(map);
        for (String column : report2FileColumns) {
            map.put(column, null);
        }
        excelTemplate.create(response, fileName, resultList);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1Details(Map<String, Object> parameterMap) {
        this.generateCascaderFilterSQL(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(csfValidationDao.queryReport1DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(csfValidationDao.queryReport1Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateCascaderFilterSQL(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "csf_details" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.toolbox.dao.ICsfValidationDao.queryReport1Details", parameterMap);
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response saveReport1Details(String userid, Map<String, Object> parameterMap) {
        scpTableHelper.setExcludeColumn(new ArrayList<>() {{
            add("CREATE_BY$");
            add("CREATE_DATE$");
            add("UPDATE_BY$");
            add("UPDATE_DATE$");
        }});
        scpTableHelper.setWarningMessage("You have no privileges to modify data does not belong to you");
        scpTableHelper.setScpTableDeleteHandler(deletes -> {
            return csfValidationDao.deleteReport1Details(deletes, userid);
        });
        scpTableHelper.setScpTableUpdateHandler((pk, updates) -> {
                    String[] pks = pk.split(",");
                    String material = pks[0];
                    String salesOrganization = pks[1];
                    String customerCode = pks[2];
                    String salesGroup = pks[3];
                    int count = csfValidationDao.queryReport1DetailsExists(material,
                            salesOrganization,
                            customerCode,
                            salesGroup, userid);
                    if (count == 0) {
                        return csfValidationDao.saveReport1Details(updates, userid);
                    } else {
                        return csfValidationDao.updateReport1Details(material,
                                salesOrganization,
                                customerCode,
                                salesGroup, updates, userid);
                    }
                }
        );

        Message message = scpTableHelper.execCRUD(parameterMap);
        return response.setBody(message);
    }
}
