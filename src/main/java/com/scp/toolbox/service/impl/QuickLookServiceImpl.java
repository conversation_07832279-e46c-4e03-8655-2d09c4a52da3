package com.scp.toolbox.service.impl;

import com.scp.toolbox.dao.IQuickLookDao;
import com.scp.toolbox.service.IQuickLookService;
import com.starter.context.bean.CacheRemove;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.utils.MarkdownUtil;
import com.starter.utils.Utils;
import com.vladsch.flexmark.html.HtmlRenderer;
import com.vladsch.flexmark.parser.Parser;
import jakarta.annotation.Resource;
import org.ansj.domain.Term;
import org.ansj.splitWord.analysis.ToAnalysis;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Scope("prototype")
@Transactional
public class QuickLookServiceImpl implements IQuickLookService {

    public static final String PARENT_CODE = "menuAC0";

    @Resource
    private IQuickLookDao quickLookDao;

    @Resource
    private Response response;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage(String userid) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("allAvalibleObjects", quickLookDao.queryAllAvalibleObjects());
        return response.setBody(resultMap);
    }

    @Override
    public Response queryAdmin(String userid) {
        return response.setBody(quickLookDao.queryAdminAuth(userid, PARENT_CODE) > 0);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryTabCols(Map<String, Object> parameterMap) {
        return response.setBody(quickLookDao.queryTabCols(parameterMap));
    }

    @Override
    public Response queryColumnComments(Map<String, Object> parameterMap) {
        Map<String, Object> map = quickLookDao.queryColumnComments(parameterMap);
        if (map != null) {
            map.put("COMMENTS", Utils.clob2String(map.get("COMMENTS")));
        } else {
            map = new HashMap<>();
        }
        return response.setBody(map);
    }

    @Override
    public Response queryComments(Map<String, Object> parameterMap, String userid) {
        List<Map<String, Object>> commentsList = quickLookDao.queryComments(parameterMap);
        if (commentsList.isEmpty() == false) {
            List<Map<String, Object>> replyList = quickLookDao.queryReplies(commentsList);
            Map<String, List<Map<String, Object>>> replyMap = new HashMap<>();
            for (Map<String, Object> reply : replyList) {
                reply.put("IS_ME", StringUtils.equalsIgnoreCase(userid, (String) reply.get("USER_ID")));
                List<Map<String, Object>> list = replyMap.computeIfAbsent((String) reply.get("COMMENT_ID"), k -> new ArrayList<>());
                list.add(reply);
            }
            for (Map<String, Object> comment : commentsList) {
                String commentID = (String) comment.get("COMMENT_ID");
                comment.put("IS_ME", StringUtils.equalsIgnoreCase(userid, (String) comment.get("USER_ID")));
                List<Map<String, Object>> replies = replyMap.getOrDefault(commentID, new ArrayList<>());
                comment.put("REPLIES", replies);
                comment.put("REPLIES_CNT", replies.size());
            }
        }

        return response.setBody(commentsList);
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = {"this.search"})
    public Response saveComments(Map<String, Object> parameterMap) {
        String comments = (String) parameterMap.get("comments");
        if (StringUtils.isBlank(comments)) {
            quickLookDao.deleteComments(parameterMap);
            parameterMap.put("comments", "User deleted this comment");
        } else {
            quickLookDao.saveComments(parameterMap);
        }
        quickLookDao.saveHistComments(parameterMap);
        return response;
    }

    @Override
    public Response sendComments(Map<String, Object> parameterMap) {
        if (StringUtils.isNotBlank((String) parameterMap.get("content"))) {
            parameterMap.put("commentId", Utils.randomStr(12));
            quickLookDao.sendComment(parameterMap);
        }
        return response;
    }

    @Override
    public Response deleteComment(Map<String, Object> parameterMap) {
        quickLookDao.deleteComment(parameterMap);
        return response;
    }

    @Override
    public Response sendReply(Map<String, Object> parameterMap) {
        if (StringUtils.isNotBlank((String) parameterMap.get("content"))) {
            parameterMap.put("replyId", Utils.randomStr(12));
            quickLookDao.sendReply(parameterMap);
        }
        return response;
    }

    @Override
    public Response deleteReply(Map<String, Object> parameterMap) {
        quickLookDao.deleteReply(parameterMap);
        return response;
    }

    @Override
    // @Cacheable(Configuration.APPLICATION_NAME + ":1d") 添加缓存会导致图片内容过大无法序列化
    public Response search(Map<String, Object> parameterMap) {
        StringBuilder result = new StringBuilder();
        String type = (String) parameterMap.get("type");
        String name = (String) parameterMap.get("name");
        if ("COLUMN".equals(type)) {
            result.append("### ");
            result.append("<a class='quicklook-link' doc-table-name='([a-z0-9A-Z_])+' doc-column-name='" + name + "' href='javascript:void(0)' onclick='window.quickLook_edit_object(this)'>" + name + "</a>");
            result.append("  &nbsp;&nbsp;`COLUMN`\n");
            result.append(this.getCommentsByDataset(quickLookDao.queryCommentsByColumnName(name)));

            List<Map<String, Object>> columns = quickLookDao.queryTablesByColumnName(name);
            result.append("### WHICH TABLES HAVE FIELD ").append(name).append(" (").append(Utils.thousandBitSeparator(columns.size())).append(")").append("\n");
            String[] headers = new String[]{"NO.", "TABLE_NAME", "COLUMN_NAME", "DATA_TYPE", "NULLABLE", "NUM_NULLS", "NUM_DISTINCT", "LOW_VALUE", "HIGH_VALUE", "LAST_ANALYZED"};
            result.append(this.getTablClosByDataset(columns, "TABLE_NAME", headers));
        } else if ("TABLE".equals(type)) {
            // 表名
            result.append("### ");
            result.append("<a class='quicklook-link' doc-table-name='" + name + "' doc-column-name='[OBJECT_COMMENTS]' href='javascript:void(0)' onclick='window.quickLook_edit_object(this)'>" + name + "</a>");
            result.append("  &nbsp;&nbsp;`TABLE`\n");
            // 表注释
            result.append(this.getCommentsByDataset(quickLookDao.queryCommentsByTableName(name)));

            // 表基本信息
            Map<String, Object> tableInfo = quickLookDao.queryTableInfoByTableName(name);
            result.append("| TABLE NAME | NUM ROWS | SIZE IN MB | LAST ANALYZED | MONITORING | LOGGING | PARTITIONED | ROW MOVEMENT | INMEMORY | COMPRESSION | COMPRESS_FOR |\n");
            result.append("| - | - | - | - | - | - | - | - | - | - | - |\n");
            result.append("|");
            result.append(Utils.removeMakeDownSpliter(tableInfo.get("TABLE_NAME"))).append("|");
            result.append("<div style='text-align:right;'>").append(Utils.thousandBitSeparator(Utils.removeMakeDownSpliter(tableInfo.get("NUM_ROWS")))).append("</div>|");
            result.append("<div style='text-align:right;'>").append(Utils.thousandBitSeparator(Utils.removeMakeDownSpliter(tableInfo.get("SIZE_IN_MB")))).append("</div>|");
            result.append(Utils.removeMakeDownSpliter(tableInfo.get("LAST_ANALYZED"))).append("|");
            result.append(Utils.removeMakeDownSpliter(tableInfo.get("MONITORING"))).append("|");
            result.append(Utils.removeMakeDownSpliter(tableInfo.get("LOGGING"))).append("|");
            result.append(Utils.removeMakeDownSpliter(tableInfo.get("PARTITIONED"))).append("|");
            result.append(Utils.removeMakeDownSpliter(tableInfo.get("ROW_MOVEMENT"))).append("|");
            result.append(Utils.removeMakeDownSpliter(tableInfo.get("INMEMORY"))).append("|");
            result.append(Utils.removeMakeDownSpliter(tableInfo.get("COMPRESSION"))).append("|");
            result.append(Utils.removeMakeDownSpliter(tableInfo.get("COMPRESS_FOR"))).append("|");
            result.append("\n\n");

            // 表结构
            result.append("### TABLE SCHEMA ").append("\n");
            String[] headers = new String[]{"NO.", "TABLE_NAME", "COLUMN_NAME", "DATA_TYPE", "LOW_VALUE", "HIGH_VALUE", "LAST_ANALYZED", "COMMENTS"};
            result.append(this.getTablClosByDataset(quickLookDao.queryColumnsByTableName(name), "COLUMN_NAME", headers));
            // 谁依赖当前表
            List<Map<String, Object>> columns = quickLookDao.queryTablesByDependName(name);
            result.append("### WHICH TABLES DEPEND ON ").append(name).append(" (").append(Utils.thousandBitSeparator(columns.size())).append(")").append("\n");
            headers = new String[]{"NO.", "TABLE_NAME", "NUM_ROWS", "LAST_ANALYZED", "COMMENTS"};
            result.append(this.getTablClosByDataset(columns, "TABLE_NAME", headers));
            // 建表语句
            result.append("### DDL ").append("\n");
            result.append(this.getDDLByObjectName(name, "TABLE"));
        } else if ("MVIEW".equals(type)) {
            // MView名
            result.append("### ");
            result.append("<a class='quicklook-link' doc-table-name='" + name + "' doc-column-name='[OBJECT_COMMENTS]' href='javascript:void(0)' onclick='window.quickLook_edit_object(this)'>" + name + "</a>");
            result.append("  &nbsp;&nbsp;`MATERIALIZED VIEW`\n");
            // 注释
            result.append(this.getCommentsByDataset(quickLookDao.queryCommentsByTableName(name)));

            // MView基本信息
            Map<String, Object> tableInfo = quickLookDao.queryMViewInfoByMViewName(name);
            result.append("| MVIEW NAME | NUM ROWS | SIZE IN MB | LAST REFRESH TIME | LAST REFRESH END TIME | MONITORING | LOGGING | PARTITIONED | INMEMORY | COMPRESSION | COMPRESS_FOR |\n");
            result.append("| - | - | - | - | - | - | - | - | - | - | - |\n");
            result.append("|");
            result.append(Utils.removeMakeDownSpliter(tableInfo.get("MVIEW_NAME"))).append("|");
            result.append("<div style='text-align:right;'>").append(Utils.thousandBitSeparator(Utils.removeMakeDownSpliter(tableInfo.get("NUM_ROWS")))).append("</div>|");
            result.append("<div style='text-align:right;'>").append(Utils.thousandBitSeparator(Utils.removeMakeDownSpliter(tableInfo.get("SIZE_IN_MB")))).append("</div>|");
            result.append(Utils.removeMakeDownSpliter(tableInfo.get("LAST_REFRESH_TIME"))).append("|");
            result.append(Utils.removeMakeDownSpliter(tableInfo.get("LAST_REFRESH_END_TIME"))).append("|");
            result.append(Utils.removeMakeDownSpliter(tableInfo.get("MONITORING"))).append("|");
            result.append(Utils.removeMakeDownSpliter(tableInfo.get("LOGGING"))).append("|");
            result.append(Utils.removeMakeDownSpliter(tableInfo.get("PARTITIONED"))).append("|");
            result.append(Utils.removeMakeDownSpliter(tableInfo.get("INMEMORY"))).append("|");
            result.append(Utils.removeMakeDownSpliter(tableInfo.get("COMPRESSION"))).append("|");
            result.append(Utils.removeMakeDownSpliter(tableInfo.get("COMPRESS_FOR"))).append("|");
            result.append("\n\n");

            // MView结构
            result.append("### TABLE SCHEMA ").append("\n");
            String[] headers = new String[]{"NO.", "TABLE_NAME", "COLUMN_NAME", "DATA_TYPE", "LOW_VALUE", "HIGH_VALUE", "LAST_ANALYZED", "COMMENTS"};
            result.append(this.getTablClosByDataset(quickLookDao.queryColumnsByTableName(name), "COLUMN_NAME", headers));
            // 哪些表依赖了MView
            List<Map<String, Object>> columns = quickLookDao.queryTablesByDependName(name);
            result.append("### WHICH TABLES DEPEND ON ").append(name).append(" (").append(Utils.thousandBitSeparator(columns.size())).append(")").append("\n");
            headers = new String[]{"NO.", "TABLE_NAME", "NUM_ROWS", "LAST_ANALYZED", "COMMENTS"};
            result.append(this.getTablClosByDataset(columns, "TABLE_NAME", headers));
            // MView依赖了哪些表
            columns = quickLookDao.queryTablesByRefName(name);
            result.append("### WHICH TABLES ").append(name).append(" DEPEND ON").append(" (").append(Utils.thousandBitSeparator(columns.size())).append(")").append("\n");
            headers = new String[]{"NO.", "TABLE_NAME", "NUM_ROWS", "LAST_ANALYZED", "COMMENTS"};
            result.append(this.getTablClosByDataset(columns, "TABLE_NAME", headers));
            // MView代码
            result.append("### QUERY ").append("\n");
            result.append(this.getDDLByObjectName(name, "MATERIALIZED_VIEW"));
        } else if ("VIEW".equals(type)) {
            // View名
            result.append("### ");
            result.append("<a class='quicklook-link' doc-table-name='" + name + "' doc-column-name='[OBJECT_COMMENTS]' href='javascript:void(0)' onclick='window.quickLook_edit_object(this)'>" + name + "</a>");
            result.append("  &nbsp;&nbsp;`VIEW`\n");
            // 注释
            result.append(this.getCommentsByDataset(quickLookDao.queryCommentsByTableName(name)));

            // View结构
            result.append("### TABLE SCHEMA ").append("\n");
            String[] headers = new String[]{"NO.", "TABLE_NAME", "COLUMN_NAME", "DATA_TYPE", "COMMENTS"};
            result.append(this.getTablClosByDataset(quickLookDao.queryColumnsByTableName(name), "COLUMN_NAME", headers));
            // 哪些表依赖了View
            List<Map<String, Object>> columns = quickLookDao.queryTablesByDependName(name);
            result.append("### WHICH TABLES DEPEND ON ").append(name).append(" (").append(Utils.thousandBitSeparator(columns.size())).append(")").append("\n");
            headers = new String[]{"NO.", "TABLE_NAME", "NUM_ROWS", "LAST_ANALYZED", "COMMENTS"};
            result.append(this.getTablClosByDataset(columns, "TABLE_NAME", headers));
            // View依赖了哪些表
            columns = quickLookDao.queryTablesByRefName(name);
            result.append("### WHICH TABLES ").append(name).append(" DEPEND ON").append(" (").append(Utils.thousandBitSeparator(columns.size())).append(")").append("\n");
            headers = new String[]{"NO.", "TABLE_NAME", "NUM_ROWS", "LAST_ANALYZED", "COMMENTS"};
            result.append(this.getTablClosByDataset(columns, "TABLE_NAME", headers));
            // View代码
            result.append("### QUERY ").append("\n");
            result.append(this.getDDLByObjectName(name, "VIEW"));
        } else if ("DOCUMENT".equals(type)) {
            result.append(getDocsByDataset(quickLookDao.queryDocByIndex(name), name, type));
        } else if ("COMMENTS".equals(type)) {
            result.append(getDocsByDataset(quickLookDao.queryCommentByIndex(name), name, type));
        }
        return response.setBody(result);
    }

    public String getDocsByDataset(List<Map<String, Object>> resultList, String name, String type) {
        StringBuilder result = new StringBuilder();
        if ("DOCUMENT".equals(type)) {
            for (Map<String, Object> map : resultList) {
                String url = "### <a class='quicklook-link'" + " doc-id='" + map.get("DOC_ID") + "' href='javascript:void(0)' onclick='window.quickLook_queryContent(this)'>" + map.get("GROUPS") + "——" + map.get("SUBJECT") + "</a>";
                result.append(url);
                String content = "\n>" + getDesc((String) map.get("CONTENT"), name) + "\n";
                result.append(content);
            }
        } else if ("COMMENTS".equals(type)) {
            for (Map<String, Object> map : resultList) {
                String tableName = (String) map.get("TABLE_NAME");
                String columnName = (String) map.get("COLUMN_NAME");
                String comments = (String) map.get("COMMENTS");
                result.append("### ").append(columnName).append("  &nbsp;&nbsp;`COLUMN`\n");
                result.append("- ").append(tableName).append("  &nbsp;&nbsp;`TABLE`\n>");
                result.append(comments).append("\n");
            }
        }
        return result.toString();
    }

    private String getDesc(String text, String word) {
        Parser parser = Parser.builder().build();
        HtmlRenderer renderer = HtmlRenderer.builder().build();

        // 将Markdown文本解析为HTML
        String html = renderer.render(parser.parse(text));
        // 使用Jsoup库从HTML中获取纯文本内容
        String plainText = org.jsoup.Jsoup.parse(html).text();
        plainText = plainText.replaceAll("\\r?\\n", " ");

        //从firstPos 作为基准，往前找10个字符，作为描述的起始位置
        int firstPos = -1;
        firstPos = plainText.indexOf(word);
        String desc = "";
        int descBeg = firstPos < 80 ? 0 : firstPos - 80;
        if (descBeg + 200 > plainText.length()) {
            desc = plainText.substring(descBeg);
        } else {
            desc = plainText.substring(descBeg, descBeg + 200) + "...";
        }
        return desc;
    }

    @Override
    public Response queryHistComments(Map<String, Object> parameterMap) {
        StringBuilder result = new StringBuilder();
        boolean allColumnSame = true;
        List<Map<String, Object>> histList = quickLookDao.queryHistComments(parameterMap);
        for (Map<String, Object> map : histList) {
            if (!"[OBJECT_COMMENTS]".equals(map.get("COLUMN_NAME"))) {
                allColumnSame = false;
                break;
            }
        }
        if (allColumnSame) {
            String[] headers = new String[]{"NO.", "TABLE_NAME", "COMMENTS", "CREATE_TIME", "CREATE_BY"};
            result.append(this.getHistTablClosByDataset(histList, headers));
        } else {
            String[] headers = new String[]{"NO.", "TABLE_NAME", "COLUMN_NAME", "COMMENTS", "CREATE_TIME", "CREATE_BY"};
            result.append(this.getHistTablClosByDataset(histList, headers));
        }
        return response.setBody(result);
    }

    private String getDDLByObjectName(String objectName, String type) {
        String ddl = "";
        if ("TABLE".equals(type)) {
            ddl = quickLookDao.getDDLByObjectName(objectName, type);
        } else if ("MATERIALIZED_VIEW".equals(type)) {
            ddl = "\n" + quickLookDao.getQueryByMViewName(objectName);
        } else if ("VIEW".equals(type)) {
            ddl = "\n" + quickLookDao.getQueryByViewName(objectName);
        }
        if ("TABLE".equals(type) || "MATERIALIZED_VIEW".equals(type)) {
            try {
                List<String> indexDDL = quickLookDao.getIndexByTableName(objectName);
                ddl += "\n" + StringUtils.join(indexDDL, "\n");
            } catch (Exception ignore) {

            }
        }
        return "```sql" + ddl + "\n```";
    }

    private String getCommentsByDataset(List<Map<String, Object>> comments) {
        StringBuilder result = new StringBuilder();
        if (comments != null && comments.size() > 0) {
            for (Map<String, Object> comment : comments) {
                result.append("- **");
                result.append(comment.get("TABLE_NAME"));
                result.append("**&nbsp;&nbsp;&nbsp;&nbsp; <small><a style='color: var(--scp-text-color-primary);' data-table='" + comment.get("TABLE_NAME") + "'  data-column='" + comment.get("COLUMN_NAME") + "' href='javascript:void(0)' onclick='window.quickLook_queryHistComments(this)'>*by ");
                result.append(comment.get("CREATE_BY"));
                result.append("@");
                result.append(comment.get("CREATE_TIME"));
                result.append("*</a></small>\n");
                result.append(MarkdownUtil.addQuotePrefix(Utils.clob2String(comment.get("COMMENTS"))));
                result.append("\n");
                result.append("\n");
            }
        }
        return result.toString();
    }

    private Map<String, String> tableMap = new HashMap<>();
    private Map<String, String> columnMap = new HashMap<>();

    private String getHistTablClosByDataset(List<Map<String, Object>> columns, String... headers) {
        StringBuilder result = new StringBuilder();
        if (columns == null || columns.size() == 0) {
            return "`NO DATA`\n\n";
        }
        result.append("| ").append(StringUtils.join(headers, " | ").replace("_", " ")).append(" |").append("\n");
        result.append("| ").append(StringUtils.repeat(" - |", headers.length)).append("\n");
        int index = 1;
        for (Map<String, Object> map : columns) {
            result.append("|");
            for (String header : headers) {
                if ("NO.".equals(header)) {
                    result.append(index++);
                } else if ("COMMENTS".equals(header)) {
                    String comments = Utils.clob2String(map.get(header));
                    if (StringUtils.isNotBlank(comments)) {
                        comments = StringUtils.replace(comments, "|", " ");
                        comments = StringUtils.replace(comments, "\r", " ");
                        comments = StringUtils.replace(comments, "\n", " ");
                        result.append(comments);
                    } else {
                        result.append(" ");
                    }
                } else {
                    result.append(Utils.removeMakeDownSpliter(map.get(header)));
                }
                result.append("|");
            }
            result.append("\r\n");
        }
        return result.toString();
    }

    private String getTablClosByDataset(List<Map<String, Object>> columns, String quickLinkHeader, String... headers) {
        if (quickLinkHeader != null) {
            synchronized (this) {
                if (columnMap.isEmpty() || tableMap.isEmpty()) {
                    List<Map<String, String>> allObjects = quickLookDao.queryAllAvalibleObjects();
                    for (Map<String, String> map : allObjects) {
                        if ("COLUMN".equals(map.get("label"))) {
                            columnMap.put(map.get("value"), map.get("label"));
                        } else {
                            tableMap.put(map.get("value"), map.get("label"));
                        }
                    }
                }
            }
        }

        StringBuilder result = new StringBuilder();
        if (columns == null || columns.size() == 0) {
            return "`NO DATA`\n\n";
        }

        result.append("| ").append(StringUtils.join(headers, " | ").replace("_", " ")).append(" |").append("\n");
        result.append("| ").append(StringUtils.repeat(" - |", headers.length)).append("\n");

        int index = 1;
        for (Map<String, Object> map : columns) {
            result.append("|");
            for (String header : headers) {
                if ("NO.".equals(header)) {
                    result.append(index++);
                } else if ("DATA_TYPE".equals(header)) {
                    result.append(Utils.removeMakeDownSpliter(map.get("DATA_TYPE")));
                    if (map.get("DATA_LENGTH") != null) {
                        result.append("(");
                        result.append(map.get("DATA_LENGTH"));
                        result.append(")");
                    }
                } else if ("COMMENTS".equals(header)) {
                    String comments = Utils.clob2String(map.get(header));
                    if (StringUtils.isNotBlank(comments)) {
                        comments = StringUtils.replace(comments, "|", " ");
                        comments = StringUtils.replace(comments, "\r", " ");
                        comments = StringUtils.replace(comments, "\n", " ");
                        comments += "<a class='quicklook-link' style='float:right;' data-table='" + map.get("EXP_TABLE_NAME") + "' data-column='" + map.get("COLUMN_NAME") + "' href='javascript:void(0)' onclick='window.quickLook_queryHistComments(this)'>*by " + map.get("CREATE_BY") + "@" + map.get("CREATE_TIME") + "*</a>";
                        result.append(comments);
                    } else {
                        result.append(" ");
                    }
                } else if (StringUtils.startsWith(header, "NUM_")) {
                    result.append("<div style='text-align:right;'>");
                    result.append(Utils.thousandBitSeparator(Utils.removeMakeDownSpliter(map.get(header))));
                    result.append("</div>");
                } else if (StringUtils.endsWith(header, "_VALUE")) {
                    result.append("<div style='text-align:right;'>");
                    if ("NUMBER".equals(map.get("DATA_TYPE")) || "INTEGER".equals(map.get("DATA_TYPE")) || "FLOAT".equals(map.get("DATA_TYPE"))) {
                        result.append(Utils.thousandBitSeparator(Utils.removeMakeDownSpliter(map.get(header)), 2));
                    } else {
                        result.append(Utils.removeMakeDownSpliter(map.get(header)));
                    }
                    result.append("</div>");
                } else if (StringUtils.equals(header, quickLinkHeader)) {
                    String value = Utils.removeMakeDownSpliter(map.get(header));
                    String ahtml = value;
                    String label;
                    if ("COLUMN_NAME".equals(quickLinkHeader)) {
                        label = columnMap.get(value);
                    } else {
                        label = tableMap.get(value);
                    }
                    String title = "Learn more about " + value;
                    if (label != null) {
                        ahtml = "<a class='quicklook-link' title='" + title + "' href='https://scp-dss.cn.schneider-electric.com/#/toolbox/quick_look?s=" + value + "&t=" + label + "' target='_blank'>" + value + "</a>";
                    }
                    result.append(ahtml);
                } else {
                    result.append(Utils.removeMakeDownSpliter(map.get(header)));
                }
                result.append("|");
            }
            result.append("\r\n");
        }
        return result.toString();
    }

    @Override
    public Response queryKeywords(Map<String, Object> parameterMap) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> resultList = new ArrayList<>();
        String keywords = (String) parameterMap.get("keywords");
        Pattern pattern = Pattern.compile("[a-zA-Z0-9]");
        Matcher matcher = pattern.matcher(keywords);
        List<String> wordList = new ArrayList<>();
        if (!keywords.isEmpty()) {
            wordList.add(keywords);
            while (keywords.contains("  ")) {
                keywords = keywords.replace("  ", "");
            }
            if (keywords.contains(" ")) {
                wordList.addAll(List.of(keywords.split(" ")));
            }
            if (!matcher.find() && keywords.length() > 1) {
                List<Term> terms = ToAnalysis.parse(keywords).getTerms();
                for (Term term : terms) {
                    if (!Objects.equals(term.getNatureStr(), "u")) {
                        wordList.add(term.getName());
                    }
                }
            }
            resultList.addAll(quickLookDao.queryKeywords(wordList));
        }
        result.put("words", wordList);
        result.put("result", resultList);
        return response.setBody(result);
    }

    @Override
    public Response queryContent(Map<String, Object> parameterMap) {
        StringBuilder result = new StringBuilder();
        result.append(Utils.clob2String(quickLookDao.queryContentById(parameterMap)));
        return response.setBody(result);
    }

    @Override
    public Response queryCommentsTemplate() {
        return response.setBody(Utils.clob2String(quickLookDao.queryCommentsTemplate()));
    }
}
