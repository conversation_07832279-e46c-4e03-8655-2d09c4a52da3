package com.scp.toolbox.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.scp.toolbox.dao.IEmailNotificationDao;
import com.scp.toolbox.service.IEmailNotificationService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.login.bean.Session;
import com.starter.utils.Utils;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

@Service
@Scope("prototype")
@Transactional
public class EmailNotificationServiceImpl implements IEmailNotificationService {

    public static final String PARENT_CODE = "menuAB0";


    @Resource
    private Response response;

    @Resource
    private IEmailNotificationDao emailNotificationDao;

    @Override
    public Response queryEmailNotificationList(Map<String, Object> parameterMap, Session session) {
        String authType = StringUtils.upperCase(StringUtils.trim(emailNotificationDao.queryAuthDetails(session.getUserid(), PARENT_CODE)));//根据用户id和菜单码查询用户权限
        parameterMap.put("authType", authType);
        parameterMap.put("userid", session.getUserid());
        return response.setBody(Utils.parseTreeNodes(emailNotificationDao.queryEmailNotificationList(parameterMap)));
    }

    @Override
    public Response initPage(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("emailUserList", emailNotificationDao.queryEmailUserList());//查询所有邮箱列表
        resultMap.put("existsGroup", emailNotificationDao.queryExistsGroup());//查询所有的用户分组
        resultMap.put("reportIDList", emailNotificationDao.queryReportIDList());//维护报表脚标信息
        return response.setBody(resultMap);
    }

    @Override
    public Response saveEmail(Map<String, Object> parameterMap, Session session) {
        String id = Utils.randomStr(12);
        parameterMap.put("id", id);
        parameterMap.put("userid", session.getUserid());
        parameterMap.put("month", JSON.toJSONString(parameterMap.get("month")));
        parameterMap.put("day", JSON.toJSONString(parameterMap.get("day")));
        parameterMap.put("hour", JSON.toJSONString(parameterMap.get("hour")));
        JSONObject config = new JSONObject();
        config.put("subject", parameterMap.get("subject"));
        config.put("contents", parameterMap.get("contents"));
        config.put("attachments", parameterMap.get("attachments"));
        config.put("id", id);
        config.put("user", session.getUserid());
        config.put("recipient", parameterMap.get("recipient"));//将收件人邮箱放入config
        config.put("cc", parameterMap.get("cc"));//抄送人邮箱
        config.put("urls", parameterMap.get("urls"));//将urls放到config中
        parameterMap.put("config", config.toJSONString());
        emailNotificationDao.saveEmail(parameterMap);
        return response;
    }

    @Override
    public Response queryReport1(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = emailNotificationDao.queryReport1(parameterMap);
        String month = "[" + StringUtils.join(JSON.parseArray((String) resultMap.get("MONTH")), ",") + "]";
        String day = "[" + StringUtils.join(JSON.parseArray((String) resultMap.get("DAY")), ",") + "]";
        String hour = "[" + StringUtils.join(JSON.parseArray((String) resultMap.get("HOUR")), ",") + "]";
        resultMap.put("CORN", month.replace("null", " ") + " " + day.replace("null", " ") + " " + hour.replace("null", " "));
        JSONObject configObj = JSON.parseObject((String) resultMap.get("CONFIG"));
        String log = JSON.toJSONString(configObj, true);
        if (resultMap.get("LAST_SEND_TO") != null) {
            log += "\n\n==> " + resultMap.get("LAST_SEND_TO") + "\n\n" + resultMap.get("LAST_EXEC_RESULT");
        }
        resultMap.put("LOG_FIELD", log);
        resultMap.putAll(configObj);
        return response.setBody(resultMap);
    }

    @Override
    public Response queryReport2(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(emailNotificationDao.queryReport2Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(emailNotificationDao.queryReport2(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public Response deleteEmail(Map<String, Object> parameterMap) {
        emailNotificationDao.deleteEmail(parameterMap);
        return response;
    }

    @Override
    public Response modifyEmail(Map<String, Object> parameterMap, Session session) {
        String id = (String) parameterMap.get("id");
        parameterMap.put("userid", session.getUserid());
        parameterMap.put("month", JSON.toJSONString(parameterMap.get("month")));
        parameterMap.put("day", JSON.toJSONString(parameterMap.get("day")));
        parameterMap.put("hour", JSON.toJSONString(parameterMap.get("hour")));
        JSONObject config = new JSONObject();
        config.put("id", id);
        config.put("user", session.getUserid());
        config.put("subject", parameterMap.get("subject"));
        config.put("contents", parameterMap.get("contents"));
        config.put("attachments", parameterMap.get("attachments"));
        config.put("recipient", parameterMap.get("recipient"));//将收件人邮箱放入config
        config.put("cc", parameterMap.get("cc"));//抄送人邮箱
        config.put("urls", parameterMap.get("urls"));//将urls放到config中
        parameterMap.put("config", config.toJSONString());
        emailNotificationDao.modifyEmail(parameterMap);
        return response;
    }
}
