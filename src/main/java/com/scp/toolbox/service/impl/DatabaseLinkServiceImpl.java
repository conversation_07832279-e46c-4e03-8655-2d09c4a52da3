package com.scp.toolbox.service.impl;

import com.scp.toolbox.dao.IDatabaseLinkDao;
import com.scp.toolbox.service.IDatabaseLinkService;
import com.starter.context.bean.Response;
import com.starter.context.servlet.ServiceHelper;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

@Service("databaseLinkService")
@Scope("prototype")
@Transactional
public class DatabaseLinkServiceImpl extends ServiceHelper implements IDatabaseLinkService {

    @Resource
    private IDatabaseLinkDao databaseLinkDao;

    @Resource
    private Response response;

    @Override
    public Response queryDatabaseAuth(Map<String, Object> parameterMap) {
        return response.setBody(databaseLinkDao.queryDatabaseAuth(parameterMap));
    }
}
