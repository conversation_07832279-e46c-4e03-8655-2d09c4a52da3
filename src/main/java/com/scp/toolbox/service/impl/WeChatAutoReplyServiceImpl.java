package com.scp.toolbox.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.scp.toolbox.dao.IWeChatAutoReplyDao;
import com.scp.toolbox.service.IWeChatAutoReplyService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.login.bean.Session;
import com.starter.utils.Utils;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

@Service
@Scope("prototype")
@Transactional
public class WeChatAutoReplyServiceImpl implements IWeChatAutoReplyService {

    public static final String PARENT_CODE = "menuAA1";

    @Resource
    private Response response;

    @Resource
    private IWeChatAutoReplyDao weChatAutoReplyDao;

    @Override
    public Response initPage(Map<String, Object> parameterMap, Session session) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("isAdmin", "ADMIN".equalsIgnoreCase(StringUtils.upperCase(StringUtils.trim(weChatAutoReplyDao.queryAuthDetails(session.getUserid(), PARENT_CODE)))));//查询存在的组
        resultMap.put("existsGroup", weChatAutoReplyDao.queryExistsGroup());//查询存在的组
        resultMap.put("reportIDList", weChatAutoReplyDao.queryReportIDList());//维护报表脚标信息
        resultMap.put("userOpts", weChatAutoReplyDao.queryuserOpts());//维护报表脚标信息
        return response.setBody(resultMap);
    }

    @Override
    public Response checkJobCode(Map<String, Object> parameterMap) {
        return response.setBody(weChatAutoReplyDao.checkJobCode(parameterMap));
    }

    @Override
    public Response queryAutoReplyList(Map<String, Object> parameterMap, Session session) {
        String authType = StringUtils.upperCase(StringUtils.trim(weChatAutoReplyDao.queryAuthDetails(session.getUserid(), PARENT_CODE)));
        parameterMap.put("authType", authType);
        parameterMap.put("userid", session.getUserid());
        return response.setBody(Utils.parseTreeNodes(weChatAutoReplyDao.queryAutoReplyList(parameterMap)));
    }

    @Override
    public Response saveNotice(Map<String, Object> parameterMap, Session session) {
        String id = Utils.randomStr(8);
        parameterMap.put("id", id);

        parameterMap.put("userid", session.getUserid());
        JSONObject config = new JSONObject();
        config.put("sleep", parameterMap.get("sleep"));
        config.put("zoom", parameterMap.get("zoom"));
        config.put("id", id);
        config.put("user", session.getUserid());
        config.put("url", parameterMap.get("url"));
        if ("screenshot".equals(parameterMap.get("replyType"))) {
            config.put("areaid", parameterMap.get("areaid"));
        }
        config.put("replyType", parameterMap.get("replyType"));
        config.put("prepend", parameterMap.get("prepend"));
        config.put("append", parameterMap.get("append"));
        config.put("sql", parameterMap.get("sql"));
        parameterMap.put("config", config.toJSONString());

        if (weChatAutoReplyDao.checkJobCode(parameterMap) == 0) {
            weChatAutoReplyDao.saveNotice(parameterMap);
        } else {
            response.setBody("[" + parameterMap.get("jobCode") + "] 已被占用, 请重新选择四位代码");
        }
        return response;
    }

    @Override
    public Response queryReport1(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = weChatAutoReplyDao.queryReport1(parameterMap);
        JSONObject configObj = JSON.parseObject((String) resultMap.get("CONFIG"));
        resultMap.put("LOG_FIELD", JSON.toJSONString(configObj, true));
        resultMap.putAll(configObj);
        return response.setBody(resultMap);
    }

    @Override
    public Response deleteNotice(Map<String, Object> parameterMap) {
        weChatAutoReplyDao.deleteNotice(parameterMap);
        return response;
    }

    @Override
    public Response modifyNotice(Map<String, Object> parameterMap, Session session) {
        JSONObject config = new JSONObject();
        if ("ADMIN".equalsIgnoreCase(StringUtils.upperCase(StringUtils.trim(weChatAutoReplyDao.queryAuthDetails(session.getUserid(), PARENT_CODE)))) == false) {
            parameterMap.remove("owner");
            config.put("user", session.getUserid());
        } else {
            config.put("user", parameterMap.get("owner"));
        }
        String id = (String) parameterMap.get("id");
        parameterMap.put("userid", session.getUserid());
        config.put("sleep", parameterMap.get("sleep"));
        config.put("zoom", parameterMap.get("zoom"));
        config.put("id", id);

        config.put("url", parameterMap.get("url"));
        if ("screenshot".equals(parameterMap.get("replyType"))) {
            config.put("areaid", parameterMap.get("areaid"));
        }
        config.put("replyType", parameterMap.get("replyType"));
        config.put("prepend", parameterMap.get("prepend"));
        config.put("append", parameterMap.get("append"));
        config.put("sql", parameterMap.get("sql"));
        parameterMap.put("config", config.toJSONString());

        try {
            weChatAutoReplyDao.modifyNotice(parameterMap);
        } catch (Exception e) {
            if (e.getClass().getName().equals("org.springframework.dao.DuplicateKeyException")) {
                response.setBody("[" + parameterMap.get("jobCode") + "] 已被占用, 请重新选择四位代码");
            } else {
                System.err.println(e.getMessage());
            }
        }
        return response;
    }

    @Override
    public Response queryReport2(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(weChatAutoReplyDao.queryReport2Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(weChatAutoReplyDao.queryReport2(parameterMap));
        }
        return response.setBody(page);
    }
}
