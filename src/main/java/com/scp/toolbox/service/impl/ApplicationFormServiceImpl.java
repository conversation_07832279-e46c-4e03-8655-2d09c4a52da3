package com.scp.toolbox.service.impl;

import com.alibaba.fastjson.JSON;
import com.starter.utils.Utils;
import com.starter.utils.excel.IExcelTemplate;
import com.starter.context.bean.Response;
import com.starter.context.bean.SCPRuntimeException;
import com.starter.context.bean.SimplePage;
import com.starter.context.configuration.database.DatabaseType;
import com.starter.context.configuration.database.DynamicDataSource;
import com.starter.context.configuration.database.TargetDataSource;
import com.starter.context.mail.MailBean;
import com.starter.context.mail.MailFeignClient;
import com.adm.system.service.ISystemService;
import com.scp.toolbox.bean.ApplicationFormConfig;
import com.scp.toolbox.bean.ApplicationMailInfo;
import com.scp.toolbox.dao.IApplicationFormDao;
import com.scp.toolbox.service.IApplicationFormService;
import com.starter.utils.excel.SheetInfoWithData;
import com.starter.utils.excel.SheetInfoWithQueryKey;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Scope;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.starter.utils.excel.ExcelTemplate.setCellValue;

@Service
@Scope("prototype")
@Transactional
public class ApplicationFormServiceImpl implements IApplicationFormService {

    @Resource
    private IApplicationFormDao applicationFormDao;

    @Resource
    private ISystemService systemService;

    @Resource
    private IExcelTemplate excelTemplate;

    @Resource
    private DynamicDataSource dynamicDataSource;

    @Resource
    private MailFeignClient mailFeignClient;

    @Resource
    private Response response;

    @Value("${file.upload.path}")
    private String appUploadPath;

    private final Pattern emailPattern = Pattern.compile("[\\w[.-]]+@[\\w[.-]]+\\.[\\w]+");

    @Override
    public Response initPage(String userid, Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> appConfigs = applicationFormDao.queryApplicationConfig(userid);
        for (Map<String, Object> map : appConfigs) {
            map.put("WHERE_SQL", this.replaceSQLParams((String) map.get("WHERE_SQL"), userid, null));
        }
        resultMap.put("appConfigs", appConfigs);
        resultMap.put("isAdmin", applicationFormDao.queryApplicationAuth(userid) > 0);
        resultMap.put("groupOpts", Utils.parseCascader(applicationFormDao.queryGroupOpts(), false));
        return response.setBody(resultMap);
    }

    @Override
    public Response formatHtml(Map<String, Object> parameterMap) {
        String html = Utils.delHTMLRemarks((String) parameterMap.get("html"));
        try {
            Document doc = Jsoup.parseBodyFragment(html);
            html = doc.outerHtml();
        } catch (Exception ignore) {

        }

        return response.setBody(html);
    }

    @Override
    @TargetDataSource(DatabaseType.SCP02_READONLY)
    public Response queryResult(String userid, Map<String, Object> parameterMap) {
        String scripts = (String) parameterMap.get("scripts");
        /*scripts=scripts.toLowerCase();
        // 去除方括号
        scripts =scripts.replaceAll("^\\[|\\]$", "");
        // 查找第一个"select"
        int firstSelectIndex = scripts.indexOf("select");
         if (firstSelectIndex != -1) {
            // 从第一个"select"之后查找第二个"select"
            int secondSelectIndex = scripts.indexOf("select", firstSelectIndex + "select".length());

            if (secondSelectIndex != -1) {
                // 去除第二个"select"及其后的所有内容以及它前面的一个字符
                scripts = scripts.substring(0, secondSelectIndex - 1);
                scripts=scripts.trim();
                scripts=scripts.substring(0, scripts.length() - 1);
            }
        }*/
        scripts = this.replaceSQLParams(scripts, userid, null);
        parameterMap.put("scripts", scripts);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        try {
            int total = applicationFormDao.queryResultCount(parameterMap);
            page.setTotal(total);
            if (total > 0) {
                page.setData(applicationFormDao.queryResult(parameterMap));
            }
        } catch (Exception e) {
            if (e instanceof BadSqlGrammarException) {
                throw new SCPRuntimeException(((BadSqlGrammarException) e).getSQLException().getMessage());
            } else {
                throw new SCPRuntimeException(e.getMessage());
            }
        }

        if (page.getTotal() == 0) {
            String sql = "select * from (" + scripts + ") tt where 1 = 0";
            List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
            try (Connection con = dynamicDataSource.getConnection()) {
                ResultSet rs = con.prepareStatement(sql).executeQuery();
                List<String> headers = new ArrayList<>();

                ResultSetMetaData rsmd = rs.getMetaData();

                for (int i = 1; i <= rsmd.getColumnCount(); i++) {
                    headers.add(rsmd.getColumnLabel(i));
                }

                rs.close();

                LinkedHashMap<String, Object> map = new LinkedHashMap<>();
                for (String header : headers) {
                    map.put(header, null);
                }
                resultList.add(map);
                page.setData(resultList);
            } catch (Exception e) {
                throw new SCPRuntimeException(e.getMessage());
            }
        }

        return response.setBody(page);
    }

    @Override
    @TargetDataSource(DatabaseType.SCP02_READONLY)
    public void downloadResult(String userid, Map<String, Object> parameterMap, HttpServletResponse res) {
        String scripts = (String) parameterMap.get("scripts");
        scripts = this.replaceSQLParams(scripts, userid, null);
        parameterMap.put("scripts", scripts);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "application_form_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.toolbox.dao.IApplicationFormDao.queryResult", parameterMap);
    }

    @Override
    @TargetDataSource(DatabaseType.SCP02_READONLY)
    public Response executeQuery(String userid, Map<String, Object> parameterMap) {
        String scripts = (String) parameterMap.get("scripts");
        scripts = this.replaceSQLParams(scripts, userid, null);
        Map<String, Object> resultMap = new HashMap<>();
        List<LinkedHashMap<String, Object>> resultList;
        try {
            resultList = this.executeSQL(scripts);
        } catch (Exception e) {
            resultMap.put("result", "error");
            if (e instanceof BadSqlGrammarException) {
                resultMap.put("data", ((BadSqlGrammarException) e).getSQLException().getMessage());
            } else {
                resultMap.put("data", e.getMessage());
            }
            return response.setBody(resultMap);
        }

        if (resultList.isEmpty()) {
            String sql = "select * from (" + scripts + ") tt where 1 = 0";

            try (Connection con = dynamicDataSource.getConnection()) {
                ResultSet rs = con.prepareStatement(sql).executeQuery();
                List<String> headers = new ArrayList<>();

                ResultSetMetaData rsmd = rs.getMetaData();

                for (int i = 1; i <= rsmd.getColumnCount(); i++) {
                    headers.add(rsmd.getColumnLabel(i));
                }

                rs.close();

                LinkedHashMap<String, Object> map = new LinkedHashMap<>();
                for (String header : headers) {
                    map.put(header, null);
                }
                resultList.add(map);
            } catch (Exception e) {
                resultMap.put("result", "error");
                resultMap.put("data", e.getMessage());
                return response.setBody(resultMap);
            }
        }

        resultMap.put("result", "success");
        resultMap.put("data", resultList);
        return response.setBody(resultMap);
    }

    @Override
    public Response queryMailtoByKeywords(Map<String, Object> parameterMap) {
        List<Map<String, Object>> resultList = applicationFormDao.queryMailtoByKeywords(parameterMap);
        if (resultList.isEmpty()) {
            String keywords = (String) parameterMap.get("keywords");
            if (StringUtils.isNotBlank(keywords)) {
                Matcher m = emailPattern.matcher(keywords);
                if (m.find()) {
                    String email = m.group();
                    resultList.add(new HashMap<>() {{
                        put("LABEL", email);
                        put("VALUE", email);
                        put("TYPE", "MANUAL");
                    }});
                }
            }
        }
        return response.setBody(resultList);
    }

    @Override
    public Response queryConfig(Map<String, Object> parameterMap) {
        String config = applicationFormDao.queryConfig(parameterMap);
        return response.setBody(JSON.parseObject(config, ApplicationFormConfig.class));
    }

    @Override
    public Response deleteConfig(Map<String, Object> parameterMap) {
        applicationFormDao.deleteConfig(parameterMap);
        return response;
    }

    @Override
    @TargetDataSource(DatabaseType.SCP02_READONLY)
    public Response composeMail(String userid, String email, Map<String, Object> parameterMap) {
        Map<String, Object> result = new HashMap<>();
        String configStr = applicationFormDao.queryConfig(parameterMap);
        ApplicationFormConfig config = JSON.parseObject(configStr, ApplicationFormConfig.class);

        String validateSQL = config.getValidate();
        if (StringUtils.isNotBlank(validateSQL)) {
            validateSQL = this.replaceSQLParams(validateSQL, userid, null);
            List<LinkedHashMap<String, Object>> validateResult = this.executeSQL(validateSQL);
            if (validateResult.isEmpty() == false) {
                List<String> vResult = new ArrayList<>();
                for (LinkedHashMap<String, Object> map : validateResult) {
                    if (map != null && map.get("RESULT") != null) {
                        vResult.add((String) map.get("RESULT"));
                    }
                }

                if (vResult.isEmpty() == false) {
                    result.put("RESULT", -1);
                    result.put("CONTENT", StringUtils.join(vResult, "<br/>"));
                    return response.setBody(result);
                }
            }
        }

        ApplicationMailInfo mailInfo = new ApplicationMailInfo();
        mailInfo.setId((String) parameterMap.get("id"));
        mailInfo.setSubject(config.getSubject());
        mailInfo.setTo(config.getTo());
        List<String> cc = config.getCc();
        cc.add(email);
        mailInfo.setCc(cc);
        mailInfo.setAttachementName(StringUtils.isNotBlank(config.getAttachement().toString()) ? String.valueOf(config.getAttachementName()) : "");
        mailInfo.setHtml(this.generateMailBody(userid, config, false));
        mailInfo.setAttachementName(String.valueOf(config.getAttachementName()));
        List<String> attachement = config.getAttachement();
        mailInfo.setAttachement(attachement.get(0));
        mailInfo.setLastSentTime(applicationFormDao.queryLastSentTime(userid, (String) parameterMap.get("id")));

        result.put("RESULT", 0);
        result.put("CONTENT", mailInfo);
        return response.setBody(result);
    }

    @Override
    public Response sendMail(String userid, String username, Map<String, Object> parameterMap) {
        try {
            MailBean mailBean = new MailBean();
            ApplicationMailInfo applicationMailInfo = new ApplicationMailInfo(parameterMap);
            ApplicationFormConfig config = JSON.parseObject(applicationFormDao.queryConfig(parameterMap), ApplicationFormConfig.class);
            List<String> attachement = config.getAttachement();
            List<String> attachementName =config.getAttachementName();
            String excelType = config.getExcelType();
            String attachementPath = appUploadPath + "application/" + new SimpleDateFormat("yyyyMMdd").format(new Date()) + "/";
            if (excelType.equals("xls")) {
                attachementPath += StringUtils.removeIgnoreCase((String) parameterMap.get("subject"), ".xls") + "_" + Utils.randomStr(4) + ".xls";
            } else {
                attachementPath += StringUtils.removeIgnoreCase((String) parameterMap.get("subject"), ".xlsx") + "_" + Utils.randomStr(4) + ".xlsx";
            }
            List<SheetInfoWithData> sheetInfo = new ArrayList<>();

            if (attachement.size()==attachementName.size()){
                for (int i = 0; i < attachementName.size(); i++) {
                    String attachementsql = this.replaceSQLParams(attachement.get(i), userid, null);
                    List<LinkedHashMap<String, Object>> dataList = this.executeSQL(attachementsql);
                    String sheetName =attachementName.get(i);
                    SheetInfoWithData newSheetData = new SheetInfoWithData();
                    newSheetData.setSheetName(sheetName);
                    newSheetData.setDataList(dataList);
                    sheetInfo.add(newSheetData);
                }
            }else {
                System.out.println("sheet名错误");
            }

           File attachementFile = null;

            if (StringUtils.isNotBlank(config.getAttachement().toString())) {

                applicationMailInfo.setAttachementPath(attachementPath);
                attachementFile=excelTemplate.createAsFileSheet(attachementPath,sheetInfo,excelType);

            }

            mailBean.setSubject(applicationMailInfo.getSubject());
            mailBean.setTo(StringUtils.join(applicationMailInfo.getTo(), ","));
            mailBean.setCc(StringUtils.join(applicationMailInfo.getCc(), ","));
            mailBean.setBody(this.getSendMailBody(applicationMailInfo.getHtml(), userid));
            mailBean.setBehalfOf(username);
            if (attachementFile != null) {
                mailBean.addAttachmentBytes(Utils.file2Base64(attachementFile));
                mailBean.addAttachmentName(attachementFile.getName());
            }
            mailFeignClient.sendAsync(mailBean);

            applicationMailInfo.setAttachement(null);
            String mailID = Utils.randomStr(8);
            applicationFormDao.saveMailLog(mailID, applicationMailInfo.getId(), userid, JSON.toJSONString(applicationMailInfo), attachementPath);
            String afterSendSQL = this.replaceSQLParams(config.getAfterSend(), userid, mailID);
            if (StringUtils.isNotBlank(afterSendSQL)) {
                applicationFormDao.executeUpdate(afterSendSQL);
            }
        } catch (Exception e) {
            response.setBody(Utils.getExceptionMessage(e));
        }

        return response;
    }
    public void downloadEmail(Map<String, Object> parameterMap, HttpServletResponse response,String userid) {
        ApplicationMailInfo applicationMailInfo = new ApplicationMailInfo(parameterMap);
        ApplicationFormConfig config = JSON.parseObject(applicationFormDao.queryConfig(parameterMap), ApplicationFormConfig.class);
        List<String> attachement = config.getAttachement();
        List<String> attachementName =config.getAttachementName();
        String excelType = config.getExcelType();
        String name =config.getName();
        String attachementPath = appUploadPath + "application/" + new SimpleDateFormat("yyyyMMdd").format(new Date()) + "/";
        if (excelType.equals("xls")) {
            attachementPath += StringUtils.removeIgnoreCase(config.getName(), ".xls") + "_" + Utils.randomStr(4) + ".xls";
        } else {
            attachementPath += StringUtils.removeIgnoreCase(config.getName(), ".xlsx") + "_" + Utils.randomStr(4) + ".xlsx";
        }
        List<SheetInfoWithData> sheetInfo = new ArrayList<>();

        if (attachement.size()==attachementName.size()){
            for (int i = 0; i < attachementName.size(); i++) {
                String attachementsql = this.replaceSQLParams(attachement.get(i), userid, null);
                List<LinkedHashMap<String, Object>> dataList = this.executeSQL(attachementsql);
                String sheetName =attachementName.get(i);
                SheetInfoWithData newSheetData = new SheetInfoWithData();
                newSheetData.setSheetName(sheetName);
                newSheetData.setDataList(dataList);
                sheetInfo.add(newSheetData);
            }
        }else {
            System.out.println("sheet名错误");
        }

        File attachementFile = null;

        if (StringUtils.isNotBlank(config.getAttachement().toString())) {

            applicationMailInfo.setAttachementPath(attachementPath);
            try {
                attachementFile=excelTemplate.createAsFileSheet(attachementPath,sheetInfo,excelType);
                excelTemplate.createfile(response,attachementPath,attachementFile.getName());
                //excelTemplate.createfile(response,attachementPath,attachementFile.getName());
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }

    }

    @Override
    public Response afterChanged(String userid, Map<String, Object> parameterMap) {
        String configStr = applicationFormDao.queryConfig(parameterMap);
        ApplicationFormConfig config = JSON.parseObject(configStr, ApplicationFormConfig.class);
        String afterChangeSQL = this.replaceSQLParams(config.getAfterSave(), userid, null);
        if (StringUtils.isNotBlank(afterChangeSQL)) {
            if (this.hasInjectionAttack(afterChangeSQL) == false) {
                applicationFormDao.executeUpdate(afterChangeSQL);
            }
        }
        return response;
    }

    @Override
    public Response callCalculation(String userid, Map<String, Object> parameterMap) {
        String configStr = applicationFormDao.queryConfig(parameterMap);
        ApplicationFormConfig config = JSON.parseObject(configStr, ApplicationFormConfig.class);
        String calculationSQL = this.replaceSQLParams(config.getCalculation(), userid, null);
        if (StringUtils.isNotBlank(calculationSQL)) {
            if (this.hasInjectionAttack(calculationSQL) == false) {
                applicationFormDao.executeUpdate(calculationSQL);
            }
        }
        return response;
    }

    public boolean hasInjectionAttack(Object sqlObj, String... whiteList) {
        if (sqlObj == null) {
            return false;
        }
        String sql = String.valueOf(sqlObj);
        String[] injectionWords = {"alter ", "truncate ", "drop ", "kill ", "lock "};
        if (StringUtils.isBlank(sql) == true) {
            return false;
        }

        for (String w : injectionWords) {
            if (Utils.containsStr(whiteList, w) == true) {
                continue;
            }
            if (StringUtils.indexOfIgnoreCase(sql, w) != -1) {
                return true;
            }
        }
        return false;
    }

    private String getSendMailBody(String html, String userid) {
        try {
            Document doc = Jsoup.parseBodyFragment(html);
            Elements tables = doc.getElementsByTag("table");
            for (Element table : tables) {
                String style = table.attr("style");
                if (style == null) {
                    style = "";
                }
                style = "border-collapse:collapse;font-size:9.0pt;font-family:DengXian;" + style;
                table.attr("style", style);

                for (Element tr : table.getElementsByTag("tr")) {
                    for (Element td : tr.getElementsByTag("td")) {
                        style = td.attr("style");
                        if (style == null) {
                            style = "";
                        }
                        style = "border:1px solid #333;padding: 2px 5px;" + style;
                        td.attr("style", style);
                    }
                }
            }
            html = doc.outerHtml();
        } catch (Exception ignore) {

        }

        String body = StringUtils.replaceOnce(html, "{{_Signature}}", systemService.getMailSignature(userid));
        String style = "<style>p{font-size: 10pt;font-family:DengXian;} span{font-size: 10pt;font-family:DengXian;} div{font-size: 10pt;font-family:DengXian;}</style>";
        return style + body;
    }

    @Override
    @TargetDataSource(DatabaseType.SCP02_READONLY)
    public Response previewHtml(String userid, Map<String, Object> parameterMap) {
        Map<String, Object> result = new HashMap<>();
        ApplicationFormConfig config = new ApplicationFormConfig(parameterMap);
        String validateSQL = config.getValidate();
        if (StringUtils.isNotBlank(validateSQL)) {
            validateSQL = this.replaceSQLParams(validateSQL, userid, null);
            List<LinkedHashMap<String, Object>> validateResult = this.executeSQL(validateSQL);
            if (validateResult.isEmpty() == false) {
                List<String> vResult = new ArrayList<>();
                for (LinkedHashMap<String, Object> map : validateResult) {
                    if (map != null && map.get("RESULT") != null) {
                        vResult.add((String) map.get("RESULT"));
                    }
                }

                if (vResult.isEmpty() == false) {
                    result.put("RESULT", -1);
                    result.put("CONTENT", StringUtils.join(vResult, "<br/>"));
                    return response.setBody(result);
                }

            }
        }
        result.put("RESULT", 0);
        result.put("CONTENT", this.generateMailBody(userid, config, true));
        return response.setBody(result);
    }

    @Override
    public Response saveConfig(String userid, Map<String, Object> parameterMap) {
        int cnt = 0;
        ApplicationFormConfig config = new ApplicationFormConfig(parameterMap);
        String calc = StringUtils.isNotBlank(config.getCalculation()) ? "Y" : "N";
        String clear = StringUtils.isNotBlank(config.getClearTable()) ? "Y" : "N";
        String orderBy = config.getOrderBy();
        String memo = config.getMemo();
        String excelType = config.getExcelType();
        if ("Update".equalsIgnoreCase(config.getOperation())) {
            cnt = applicationFormDao.updateConfig(config, JSON.toJSONString(config), userid, calc, clear,orderBy,memo,excelType);
        } else if ("Create".equalsIgnoreCase(config.getOperation())) {
            config.setId(Utils.randomStr(8));
            cnt = applicationFormDao.saveConfig(config, JSON.toJSONString(config), userid, calc, clear,orderBy,memo,excelType);
        }
        return response.setBody(cnt);
    }

    @Override
    public Response clearTable(String userid, Map<String, Object> parameterMap) {
        String configStr = applicationFormDao.queryConfig(parameterMap);
        ApplicationFormConfig config = JSON.parseObject(configStr, ApplicationFormConfig.class);
        String clearTableSQL = this.replaceSQLParams(config.getClearTable(), userid, null);
        if (StringUtils.isNotBlank(clearTableSQL)) {
            if (this.hasInjectionAttack(clearTableSQL, "delete ") == false) {
                applicationFormDao.executeUpdate(clearTableSQL);
            }
        }
        return response;
    }

    @Override
    public Response fileList(String userid,Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> fileList = applicationFormDao.fileList(userid);
        resultMap.put("category", fileList);
        return response.setBody(resultMap);

    }

    @Override
    public Response sqlTemplate(String userid,Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> fileList = applicationFormDao.sqlTemplate(parameterMap);
        StringBuilder tableNameBuilder = new StringBuilder();
        tableNameBuilder.append("SELECT ");
        for (Map<String, Object> map : fileList) {
            String description = map.get("DESCRIPTION").toString();
            String tableColumnName = map.get("TABLE_COLUMN_NAME").toString();
            tableNameBuilder.append("'").append(description).append("' AS \"").append(tableColumnName).append("\"");

            // 如果不是最后一个元素，追加一个逗号，注意不要在最后一个元素后添加
            if (fileList.indexOf(map) < fileList.size() - 1) {
                tableNameBuilder.append(",");
            }
        }
        tableNameBuilder.append("FROM DUAL UNION ALL SELECT ");
        for (Map<String, Object> map2 : fileList) {
            String tableColumnName = map2.get("TABLE_COLUMN_NAME").toString();
            tableNameBuilder.append("NULL AS \"").append(tableColumnName).append("\"");

            // 如果不是最后一个元素，追加一个逗号，注意不要在最后一个元素后添加
            if (fileList.indexOf(map2) < fileList.size() - 1) {
                tableNameBuilder.append(",");
            }
        }
        tableNameBuilder.append("FROM DUAL");

        return response.setBody(tableNameBuilder);

    }

    private String generateMailBody(String userid, ApplicationFormConfig config, boolean replaceSignature) {
        Map<String, Object> valueMap = new HashMap<>();
        Map<String, List<String>> valueList = new HashMap<>();

        for (String dataSQL : config.getData()) {
            if (StringUtils.isNotBlank(dataSQL)) {
                dataSQL = this.replaceSQLParams(dataSQL, userid, null);
                List<LinkedHashMap<String, Object>> dataResult = this.executeSQL(dataSQL);
                if (dataResult.isEmpty()) {
                    continue;
                }

                if (dataResult.size() == 1) {
                    valueMap.putAll(dataResult.get(0));
                } else {
                    for (Map<String, Object> temp : dataResult) {
                        for (String key : temp.keySet()) {
                            List<String> list = valueList.computeIfAbsent(key, k -> new ArrayList<>());
                            Object value = temp.get(key);
                            list.add(value != null ? String.valueOf(value) : "");
                        }
                    }
                }
            }
        }

        String html = config.getHtml();

        if (StringUtils.isNotBlank(html)) {
            // 开始替换单列值
            for (String key : valueMap.keySet()) {
                Object value = valueMap.get(key);
                html = html.replace("{{" + key + "}}", value != null ? String.valueOf(value) : "");
            }

            // 开始替换列表
            if (valueList.isEmpty() == false) {
                String regEx = "\\[\\[([\\s\\S]*?)\\]\\]"; // 定义注释的正则表达式

                Pattern pattern = Pattern.compile(regEx, Pattern.CASE_INSENSITIVE);
                Matcher matcher = pattern.matcher(html);
                List<String> matchList = new ArrayList<>();
                while (matcher.find()) {
                    matchList.add(matcher.group());
                }

                for (String match : matchList) {
                    String temp = StringUtils.removeStart(match, "[[");
                    temp = StringUtils.removeEnd(temp, "]]");

                    StringBuilder td = new StringBuilder();

                    int i = 0;
                    boolean finish = false;
                    do {
                        String temp2 = temp;
                        for (String key : valueList.keySet()) {
                            List<String> value = valueList.get(key);

                            if (i >= value.size()) {
                                finish = true;
                                break;
                            }
                            temp2 = temp2.replace("{{" + key + "}}", value.get(i));
                        }
                        i++;
                        if (!finish) {
                            td.append(temp2);
                        }
                    } while (!finish);

                    html = StringUtils.replaceOnce(html, match, td.toString());
                }
            }
            html = StringUtils.remove(html, "[[");
            html = StringUtils.remove(html, "]]");
        }

        // 替换样式
        html = Utils.prettyMailBody(html);

        if (replaceSignature) {
            html = StringUtils.replaceOnce(html, "{{_Signature}}", systemService.getMailSignature(userid));
        }

        return html;
    }

    private String replaceSQLParams(String sql, String userid, String mailid) {
        sql = StringUtils.replace(sql, "{{_userid}}", userid);
        sql = StringUtils.replace(sql, "{{_mailid}}", mailid);
        return sql;
    }

    private List<LinkedHashMap<String, Object>> executeSQL(String sql) {
        sql = "select * from (" + sql + ") tt ";
        List<LinkedHashMap<String, Object>> list = new ArrayList<>();
        try (Connection con = dynamicDataSource.getConnection()) {
            ResultSet rs = con.prepareStatement(sql).executeQuery();
            List<String> headers = new ArrayList<>();

            ResultSetMetaData rsmd = rs.getMetaData();

            for (int i = 1; i <= rsmd.getColumnCount(); i++) {
                headers.add(rsmd.getColumnLabel(i));
            }

            while (rs.next()) {
                LinkedHashMap<String, Object> map = new LinkedHashMap<>();
                for (String header : headers) {
                    map.put(header, rs.getObject(header));
                }
                list.add(map);
            }
            rs.close();

        } catch (Exception e) {
            LinkedHashMap<String, Object> map = new LinkedHashMap<>();
            map.put("ERROR", Utils.getExceptionMessage(e));
            list.add(map);
        }
        return list;
    }




}
