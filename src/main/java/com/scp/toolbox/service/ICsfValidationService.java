package com.scp.toolbox.service;

import com.starter.context.bean.Response;

import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

public interface ICsfValidationService {

    Response initPage(Map<String, Object> parameterMap);

    Response queryReport1(Map<String, Object> parameterMap);

    Response queryReport2(Map<String, Object> parameterMap);

    void downloadReport2(Map<String, Object> parameterMap, HttpServletResponse response);

    Response uploadReport2(String userid, MultipartFile file) throws Exception;

    void downloadReport2Template(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse res);

    Response queryReport1Details(Map<String, Object> parameterMap);

    Response saveReport1Details(String userid, Map<String, Object> parameterMap);
}
