package com.scp.toolbox.service;

import com.starter.context.bean.Response;

import java.util.Map;

public interface IQuickLookService {
    Response initPage(String userid);

    Response queryAdmin(String userid);

    Response queryTabCols(Map<String, Object> parameterMap);

    Response queryColumnComments(Map<String, Object> parameterMap);

    Response queryComments(Map<String, Object> parameterMap, String userid);

    Response sendComments(Map<String, Object> parameterMap);

    Response deleteComment(Map<String, Object> parameterMap);

    Response sendReply(Map<String, Object> parameterMap);

    Response deleteReply(Map<String, Object> parameterMap);

    Response saveComments(Map<String, Object> parameterMap);

    Response search(Map<String, Object> parameterMap);

    Response queryHistComments(Map<String, Object> parameterMap);

    Response queryKeywords(Map<String, Object> parameterMap);

    Response queryContent(Map<String, Object> parameterMap);

    Response queryCommentsTemplate();
}
