package com.scp.toolbox.service;

import com.starter.context.bean.Response;
import com.starter.login.bean.Session;

import java.util.Map;

public interface IWeChatAutoReplyService {

    Response initPage(Map<String, Object> parameterMap, Session session);

    Response queryAutoReplyList(Map<String, Object> parameterMap, Session session);

    Response queryReport1(Map<String, Object> parameterMap);

    Response queryReport2(Map<String, Object> parameterMap);

    Response saveNotice(Map<String, Object> parameterMap, Session session);

    Response deleteNotice(Map<String, Object> parameterMap);

    Response modifyNotice(Map<String, Object> parameterMap, Session session);

    Response checkJobCode(Map<String, Object> parameterMap);
}
