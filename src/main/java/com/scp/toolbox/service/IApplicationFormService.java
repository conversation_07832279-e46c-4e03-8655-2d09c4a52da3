package com.scp.toolbox.service;

import com.starter.context.bean.Response;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;

public interface IApplicationFormService {

    Response initPage(String userid, Map<String, Object> parameterMap);

    Response formatHtml(Map<String, Object> parameterMap);

    Response previewHtml(String userid, Map<String, Object> parameterMap);

    Response saveConfig(String userid, Map<String, Object> parameterMap);

    Response executeQuery(String userid, Map<String, Object> parameterMap);

    Response queryMailtoByKeywords(Map<String, Object> parameterMap);

    Response queryConfig(Map<String, Object> parameterMap);

    Response deleteConfig(Map<String, Object> parameterMap);

    Response composeMail(String userid, String email, Map<String, Object> parameterMap);

    Response sendMail(String userid, String username, Map<String, Object> parameterMap);

    void downloadEmail(Map<String, Object> parameterMap, HttpServletResponse response,String userid);

    Response afterChanged(String userid, Map<String, Object> parameterMap);

    Response callCalculation(String userid, Map<String, Object> parameterMap);

    Response clearTable(String userid, Map<String, Object> parameterMap);

    Response queryResult(String userid, Map<String, Object> parameterMap);

    void downloadResult(String userid, Map<String, Object> parameterMap, HttpServletResponse response);

    Response fileList(String userid,Map<String, Object> parameterMap);

    Response sqlTemplate(String userid,Map<String, Object> parameterMap);
}
