package com.scp.toolbox.service;

import com.starter.login.bean.Session;
import com.starter.context.bean.Response;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;

public interface ICustomReportService {

    Response initPage(Map<String, Object> parameterMap, Session session);

    Response queryReportList(Map<String, Object> parameterMap, Session session);

    Response queryReportHeaders(Map<String, Object> parameterMap);

    Response queryReport(Map<String, Object> parameterMap, boolean cacheable);

    Response deleteReport(Map<String, Object> parameterMap, Session session);

    Response saveReport(Map<String, Object> parameterMap);

    void downloadReport(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReportConfig(Map<String, Object> parameterMap);

    Response modifySelectedReport(Map<String, Object> parameterMap, Session session);

    void saveReportVisitLog(Map<String, Object> parameterMap);
}
