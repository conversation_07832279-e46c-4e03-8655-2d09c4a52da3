package com.scp.toolbox.service;

import com.starter.context.bean.Response;
import com.starter.login.bean.Session;
import java.util.Map;

public interface IEmailNotificationService {
    Response queryEmailNotificationList(Map<String, Object> parameterMap, Session session);

    Response initPage(Map<String, Object> parameterMap);

    Response saveEmail(Map<String, Object> parameterMap, Session session);

    Response queryReport1(Map<String, Object> parameterMap);

    Response queryReport2(Map<String, Object> parameterMap);

    Response deleteEmail(Map<String, Object> parameterMap);

    Response modifyEmail(Map<String, Object> parameterMap, Session session);
}
