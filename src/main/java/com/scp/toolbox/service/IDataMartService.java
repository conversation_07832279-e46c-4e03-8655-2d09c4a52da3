package com.scp.toolbox.service;

import com.starter.context.bean.Response;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;

public interface IDataMartService {

    Response initPage(String userid, Map<String, Object> parameterMap);

    Response queryTaskList(String userid, Map<String, Object> parameterMap);

    Response queryReport1(Map<String, Object> parameterMap);

    Response queryReport2(Map<String, Object> parameterMap);

    Response queryReport3(Map<String, Object> parameterMap);

    Response queryReport4(Map<String, Object> parameterMap);

    Response queryReport5(Map<String, Object> parameterMap);

    Response queryReport8(Map<String, Object> parameterMap);

    Response queryReport8Logs(Map<String, Object> parameterMap);

    Response queryReport8Steps(Map<String, Object> parameterMap);

    Response saveNewJob(Map<String, Object> parameterMap);

    void downloadKettleTemplate(Map<String, Object> parameterMap, HttpServletResponse response) throws Exception;

    Response uploadJobScript(MultipartFile file) throws Exception;

    void downloadFile(Map<String, Object> parameterMap, HttpServletResponse response) throws Exception;

    Response queryReport7(Map<String, Object> parameterMap);

    Response queryReport6(Map<String, Object> parameterMap);

    Response deleteJob(Map<String, Object> parameterMap);

    Response modifyJob(Map<String, Object> parameterMap) throws Exception;

    Response runJob(Map<String, Object> parameterMap);

    Response queryReport9(Map<String, Object> parameterMap);

    Response queryDiskMapping();

    Response queryReport10(Map<String, Object> parameterMap);
}
