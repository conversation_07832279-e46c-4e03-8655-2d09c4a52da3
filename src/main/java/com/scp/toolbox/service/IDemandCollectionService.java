package com.scp.toolbox.service;

import com.starter.context.bean.Response;

import jakarta.servlet.http.HttpServletResponse;

import java.util.Map;

public interface IDemandCollectionService {

    Response initPage(Map<String, Object> parameterMap);

    void downloadReport1Details(String userid, Map<String, Object> parameterMap, HttpServletResponse res);

    Response queryReport1Details(String userid, Map<String, Object> parameterMap);

    Response saveReport1Details(String userid, Map<String, Object> parameterMap);
}
