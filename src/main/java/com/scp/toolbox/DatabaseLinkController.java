package com.scp.toolbox;

import com.scp.toolbox.service.IDatabaseLinkService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/toolbox/database_link", parent = "menuA00")
public class DatabaseLinkController extends ControllerHelper {

    public static final String PARENT_CODE = "menuAC1";

    @Resource
    private IDatabaseLinkService databaseLinkService;

    @SchneiderRequestMapping("/query_database_auth")
    public Response queryDatabaseAuth(HttpServletRequest request) {
        super.pageLoad(request);
        return databaseLinkService.queryDatabaseAuth(parameterMap);
    }

}
