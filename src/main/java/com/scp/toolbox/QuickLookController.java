package com.scp.toolbox;

import com.scp.toolbox.service.IQuickLookService;
import com.scp.toolbox.service.impl.QuickLookServiceImpl;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin
@SchneiderRequestMapping(value = "/toolbox/quick_look", parent = QuickLookServiceImpl.PARENT_CODE)
@Scope("prototype")
public class QuickLookController extends ControllerHelper {

    @Resource
    private IQuickLookService quickLookService;

    @SchneiderRequestMapping(value = "/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return quickLookService.initPage(session.getUserid());
    }

    @SchneiderRequestMapping(value = "/query_admin")
    public Response queryAdmin(HttpServletRequest request) {
        super.pageLoad(request);
        return quickLookService.queryAdmin(session.getUserid());
    }

    @SchneiderRequestMapping(value = "/query_comments_template")
    public Response queryCommentsTemplate(HttpServletRequest request) {
        super.pageLoad(request);
        return quickLookService.queryCommentsTemplate();
    }

    @SchneiderRequestMapping(value = "/query_tab_cols")
    public Response queryTabCols(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return quickLookService.queryTabCols(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_column_comments")
    public Response queryColumnComments(HttpServletRequest request) {
        super.pageLoad(request);
        return quickLookService.queryColumnComments(parameterMap);
    }

    @SchneiderRequestMapping(value = "/save_comments")
    public Response saveComments(HttpServletRequest request) {
        super.pageLoad(request);
        return quickLookService.saveComments(parameterMap);
    }

    @SchneiderRequestMapping(value = "/search")
    public Response search(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return quickLookService.search(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_hist_comments")
    public Response queryHistComments(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return quickLookService.queryHistComments(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_content")
    public Response queryContent(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return quickLookService.queryContent(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_keywords")
    public Response queryKeywords(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return quickLookService.queryKeywords(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_comments")
    public Response queryComments(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return quickLookService.queryComments(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping(value = "/send_comment")
    public Response sendComments(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return quickLookService.sendComments(parameterMap);
    }

    @SchneiderRequestMapping(value = "/delete_comment")
    public Response deleteComments(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return quickLookService.deleteComment(parameterMap);
    }

    @SchneiderRequestMapping(value = "/send_reply")
    public Response sendReply(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return quickLookService.sendReply(parameterMap);
    }

    @SchneiderRequestMapping(value = "/delete_reply")
    public Response deleteReply(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return quickLookService.deleteReply(parameterMap);
    }
}
