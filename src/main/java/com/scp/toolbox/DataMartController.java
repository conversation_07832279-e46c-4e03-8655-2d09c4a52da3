package com.scp.toolbox;

import com.scp.toolbox.service.IDataMartService;
import com.scp.toolbox.service.impl.DataMartServiceImpl;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.util.WebUtils;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/toolbox/data_mart", parent = DataMartServiceImpl.PARENT_CODE)
public class DataMartController extends ControllerHelper {

    @Resource
    private Response response;

    @Resource
    private IDataMartService dataMarketService;

    @SchneiderRequestMapping(value = "/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        return dataMarketService.initPage(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_task_list")
    public Response queryTaskList(HttpServletRequest request) {
        super.pageLoad(request);
        return dataMarketService.queryTaskList(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        return dataMarketService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        super.pageLoad(request);
        return dataMarketService.queryReport2(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_report3")
    public Response queryReport3(HttpServletRequest request) {
        super.pageLoad(request);
        return dataMarketService.queryReport3(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_report4")
    public Response queryReport4(HttpServletRequest request) {
        super.pageLoad(request);
        return dataMarketService.queryReport4(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_report5")
    public Response queryReport5(HttpServletRequest request) {
        super.pageLoad(request);
        return dataMarketService.queryReport5(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_report8")
    public Response queryReport8(HttpServletRequest request) {
        super.pageLoad(request);
        return dataMarketService.queryReport8(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_report8_logs")
    public Response queryReport8Logs(HttpServletRequest request) {
        super.pageLoad(request);
        return dataMarketService.queryReport8Logs(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_report8_steps")
    public Response queryReport8Steps(HttpServletRequest request) {
        super.pageLoad(request);
        return dataMarketService.queryReport8Steps(parameterMap);
    }

    @SchneiderRequestMapping(value = "/save_new_job")
    public Response saveNewJob(HttpServletRequest request) {
        super.pageLoad(request);
        return dataMarketService.saveNewJob(parameterMap);
    }

    @SchneiderRequestMapping("/download_kettle_template")
    public void downloadKettleTemplate(HttpServletRequest request, HttpServletResponse response) throws Exception {
        super.pageLoad(request);
        dataMarketService.downloadKettleTemplate(parameterMap, response);
    }

    @SchneiderRequestMapping("/upload_job_script")
    public Response uploadJobScript(HttpServletRequest request) {
        super.pageLoad(request);
        MultipartHttpServletRequest multipartRequest = WebUtils.getNativeRequest(request, MultipartHttpServletRequest.class);
        if (multipartRequest != null) {
            MultipartFile file = multipartRequest.getFile("file");
            try {
                return dataMarketService.uploadJobScript(file);
            } catch (Exception e) {
                return response.setError(e);
            }
        } else {
            return response.setError(new Exception("upload failed, multipartRequest is null !"));
        }
    }

    @SchneiderRequestMapping("/download_file")
    public void downloadFile(HttpServletRequest request, HttpServletResponse response) throws Exception {
        super.pageLoad(request);
        dataMarketService.downloadFile(parameterMap, response);
    }

    @SchneiderRequestMapping(value = "/query_report6")
    public Response queryReport6(HttpServletRequest request) {
        super.pageLoad(request);
        return dataMarketService.queryReport6(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_report7")
    public Response queryReport7(HttpServletRequest request) {
        super.pageLoad(request);
        return dataMarketService.queryReport7(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_report9")
    public Response queryReport9(HttpServletRequest request) {
        super.pageLoad(request);
        return dataMarketService.queryReport9(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_report10")
    public Response queryReport10(HttpServletRequest request) {
        super.pageLoad(request);
        return dataMarketService.queryReport10(parameterMap);
    }


    @SchneiderRequestMapping(value = "/delete_job")
    public Response deleteJob(HttpServletRequest request) {
        super.pageLoad(request);
        return dataMarketService.deleteJob(parameterMap);
    }

    @SchneiderRequestMapping(value = "/modify_job")
    public Response modifyJob(HttpServletRequest request) {
        super.pageLoad(request);
        try {
            return dataMarketService.modifyJob(parameterMap);
        } catch (Exception e) {
            return response.setError(e);
        }
    }

    @SchneiderRequestMapping(value = "/run_job")
    public Response runJob(HttpServletRequest request) {
        super.pageLoad(request);
        return dataMarketService.runJob(parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_disk_mapping")
    public Response queryDiskMapping() {
        return dataMarketService.queryDiskMapping();
    }
}
