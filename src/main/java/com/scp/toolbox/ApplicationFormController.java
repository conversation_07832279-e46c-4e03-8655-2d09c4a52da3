package com.scp.toolbox;

import com.scp.toolbox.service.IApplicationFormService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@SchneiderRequestMapping(value = "/toolbox/application_form", parent = "menuA10")
@Scope("prototype")
public class ApplicationFormController extends ControllerHelper {

    @Resource
    private IApplicationFormService applicationFormService;

    @SchneiderRequestMapping(value = "/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        return applicationFormService.initPage(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping(value = "/format_html")
    public Response formatHtml(HttpServletRequest request) {
        super.pageLoad(request);
        return applicationFormService.formatHtml(parameterMap);
    }

    @SchneiderRequestMapping(value = "/preview_mail")
    public Response previewHtml(HttpServletRequest request) {
        super.pageLoad(request);
        return applicationFormService.previewHtml(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_config")
    public Response queryConfig(HttpServletRequest request) {
        super.pageLoad(request);
        return applicationFormService.queryConfig(parameterMap);
    }

    @SchneiderRequestMapping(value = "/delete_config")
    public Response deleteConfig(HttpServletRequest request) {
        super.pageLoad(request);
        return applicationFormService.deleteConfig(parameterMap);
    }

    @SchneiderRequestMapping(value = "/save_config")
    public Response saveConfig(HttpServletRequest request) {
        super.pageLoad(request);
        return applicationFormService.saveConfig(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping("/execute_query")
    public Response executeQuery(HttpServletRequest request) {
        super.pageLoad(request);
        return applicationFormService.executeQuery(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping("/query_result")
    public Response queryResult(HttpServletRequest request) {
        super.pageLoad(request);
        return applicationFormService.queryResult(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping("/download_result")
    public void downloadResult(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        applicationFormService.downloadResult(session.getUserid(), parameterMap, response);
    }

    @SchneiderRequestMapping("/query_mailto_by_keywords")
    public Response queryMailtoByKeywords(HttpServletRequest request) {
        super.pageLoad(request);
        return applicationFormService.queryMailtoByKeywords(parameterMap);
    }

    @SchneiderRequestMapping(value = "/compose_mail")
    public Response composeMail(HttpServletRequest request) {
        super.pageLoad(request);
        return applicationFormService.composeMail(session.getUserid(), session.getEmail(), parameterMap);
    }

    @SchneiderRequestMapping("/download_email")
    public void downloadEmail(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        applicationFormService.downloadEmail(parameterMap, response,session.getUserid());
    }

    @SchneiderRequestMapping(value = "/send_mail")
    public Response sendMail(HttpServletRequest request) {
        super.pageLoad(request);
        return applicationFormService.sendMail(session.getUserid(), session.getUsername(), parameterMap);
    }

    @SchneiderRequestMapping(value = "/after_changed")
    public Response afterChanged(HttpServletRequest request) {
        super.pageLoad(request);
        return applicationFormService.afterChanged(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping(value = "/call_calculation")
    public Response callCalculation(HttpServletRequest request) {
        super.pageLoad(request);
        return applicationFormService.callCalculation(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping(value = "/clear_table")
    public Response clearTable(HttpServletRequest request) {
        super.pageLoad(request);
        return applicationFormService.clearTable(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping(value = "/file_template")
    public Response fileList(HttpServletRequest request) {
        super.pageLoad(request);
        return applicationFormService.fileList(session.getUserid(),parameterMap);
    }
    @SchneiderRequestMapping(value = "/file_sql")
    public Response sqlTemplate(HttpServletRequest request) {
        super.pageLoad(request);
        return applicationFormService.sqlTemplate(session.getUserid(),parameterMap);
    }

}

