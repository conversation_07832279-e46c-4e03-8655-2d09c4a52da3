package com.scp.inventory;

import com.scp.inventory.service.IExcessSOStockService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/inventory/excess_so_stock", parent = "menu790")
public class ExcessSOStockController extends ControllerHelper {

    @Resource
    private IExcessSOStockService excessSOStockService;

    @Resource
    private Response res;

    @SchneiderRequestMapping("/query_filters")
    public Response queryFilters() {
        super.setGlobalCache(true);
        return excessSOStockService.queryCascader();
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.setGlobalCache(true);
        super.pageLoad(request);
        return excessSOStockService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping("/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return excessSOStockService.queryReport2(parameterMap);
    }

    @SchneiderRequestMapping("/query_report2_details")
    public Response queryReport2Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return excessSOStockService.queryReport2Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report2_details")
    public void downloadReport2Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        excessSOStockService.downloadReport2Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report3")
    public Response query_report3(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return excessSOStockService.queryReport3(parameterMap);
    }

    @SchneiderRequestMapping("/query_report3_columns")
    public Response queryReport3Columns(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return excessSOStockService.queryReport3Columns(parameterMap);
    }

    @SchneiderRequestMapping("/download_report3")
    public void downloadReport3(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        excessSOStockService.downloadReport3(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report3_details")
    public Response queryReport3Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return excessSOStockService.queryReport3Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report3_details")
    public void downloadReport3Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        excessSOStockService.downloadReport3Details(parameterMap, response);
    }
}
