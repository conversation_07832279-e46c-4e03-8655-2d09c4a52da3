package com.scp.inventory;

import com.scp.inventory.service.IDistributorInventoryService;
import com.scp.master.service.IMasterDataSummaryService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/inventory/distributor_inventory", parent = "menu221")
public class DistributorInventoryController extends ControllerHelper{

    @Resource
    private IDistributorInventoryService distributorInventoryServiceService;

    @SchneiderRequestMapping("/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return distributorInventoryServiceService.initPage();
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return distributorInventoryServiceService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping("/download_report1")
    public void downloadReport1(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        distributorInventoryServiceService.downloadReport1(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report1_details")
    public Response queryReport1Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return distributorInventoryServiceService.queryReport1Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report1_details")
    public void downloadReport1Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        distributorInventoryServiceService.downloadReport1Details(parameterMap, response);
    }
    @SchneiderRequestMapping("/query_report2")
        public Response queryReport2(HttpServletRequest request) {
            super.pageLoad(request);
            super.setGlobalCache(true);
            return distributorInventoryServiceService.queryReport2(parameterMap);
        }

    @SchneiderRequestMapping("/query_report3")
    public Response query_report3(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return distributorInventoryServiceService.queryReport3(parameterMap);
    }

    @SchneiderRequestMapping("/query_report3_columns")
    public Response queryReport3Columns(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return distributorInventoryServiceService.queryReport3Columns(parameterMap);
    }

    @SchneiderRequestMapping("/download_report3")
    public void downloadReport3(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        distributorInventoryServiceService.downloadReport3(parameterMap, response);
    }
}
