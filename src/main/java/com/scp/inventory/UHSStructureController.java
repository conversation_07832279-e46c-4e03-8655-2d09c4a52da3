package com.scp.inventory;

import com.scp.inventory.service.IUHSStructureService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/inventory/uhs_structure", parent = "menu7A0")
public class UHSStructureController extends ControllerHelper {

    @Resource
    private IUHSStructureService UHSStructureService;

    @Resource
    private Response res;

    @SchneiderRequestMapping("/init_page")
    public Response initPage() {
        super.setGlobalCache(true);
        return UHSStructureService.initPage();
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return UHSStructureService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping("/query_report1_details")
    public Response queryReport1Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return UHSStructureService.queryReport1Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report1_details")
    public void downloadReport1Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        UHSStructureService.downloadReport1Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return UHSStructureService.queryReport2(parameterMap);
    }

    @SchneiderRequestMapping("/query_report3")
    public Response queryReport3(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return UHSStructureService.queryReport3(parameterMap);
    }

    @SchneiderRequestMapping("/query_report4")
    public Response queryReport4(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return UHSStructureService.queryReport4(parameterMap);
    }

    @SchneiderRequestMapping("/query_report5")
    public Response queryReport5(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        try {
            return UHSStructureService.queryReport5(parameterMap);
        } catch (Exception e) {
            return res.setError(e);
        }
    }
}
