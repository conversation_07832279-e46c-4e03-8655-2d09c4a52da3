package com.scp.inventory;

import com.scp.inventory.service.IInventoryRebalanceService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/inventory/rebalance", parent = "menu760")
public class InventoryRebalanceController extends ControllerHelper {

    @Resource
    private IInventoryRebalanceService inventoryRebalanceService;


    @SchneiderRequestMapping(value = "/calc")
    public Response calcRebalance(HttpServletRequest request) {
        super.pageLoad(request);
        return inventoryRebalanceService.calcRebalance();
    }

    @SchneiderRequestMapping("/init_page")
    public Response initPage() {
        super.setGlobalCache(true);
        return inventoryRebalanceService.initPage();
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return inventoryRebalanceService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping("/query_report1_details")
    public Response queryReport1Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return inventoryRebalanceService.queryReport1Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report1_details")
    public void downloadReport1Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        inventoryRebalanceService.downloadReport1Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return inventoryRebalanceService.queryReport2(parameterMap);
    }

    @SchneiderRequestMapping("/download_report2")
    public void downloadReport2(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        inventoryRebalanceService.downloadReport2(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report2_details")
    public Response queryReport2Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return inventoryRebalanceService.queryReport2Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report2_details")
    public void downloadReport2Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        inventoryRebalanceService.downloadReport2Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report2_details_logs")
    public Response queryReport2DetailsLogs(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return inventoryRebalanceService.queryReport2DetailsLogs(parameterMap);
    }
}
