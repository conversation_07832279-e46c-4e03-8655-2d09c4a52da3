package com.scp.inventory.utils;

import com.alibaba.fastjson.JSON;
import com.scp.inventory.bean.RebalanceBean;
import com.scp.inventory.bean.RebalanceTransferBean;
import com.scp.inventory.bean.RebalanceTransferLogs;
import com.scp.inventory.bean.RebalanceTransferResult;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

public class RebalanceCalcUtil {

    static List<String> plants = Arrays.asList("O001", "N001", "M001", "I001", "I003");

    /**
     * 计算的主方法
     *
     * @return RebalanceTransferResult
     */
    public static RebalanceTransferResult calc(RebalanceBean rebalance, BigDecimal target) throws Exception {
        RebalanceTransferResult result = new RebalanceTransferResult();

        List<String> logList = new ArrayList<>();
        logList.add("开始计算物料" + rebalance.getMATERIAL() + ", TARGET = " + target);
        List<RebalanceTransferBean> transferList = new ArrayList<>();
        logList.add("检查可调出库存 [非当前DC发货、超过" + target + "个月用量]");

        for (String plant : plants) {
            // 不筛查库存小于等于0的库存
            if (rebalance.getUU_STOCK_NEW(plant).compareTo(BigDecimal.ZERO) < 1) {
                continue;
            }
            // 先检查不是本DC发货, 但有库存的物料
            // 一旦有此类物料, 执行全额调拨
            if (rebalance.getDELIVERING_PLANT(plant).equalsIgnoreCase(plant) == false) {
                logList.add("\t[非当前DC发货] " + plant + "_DELIVERING_PLANT = " + rebalance.getDELIVERING_PLANT(plant) + ", 放入临时库存池等待分配, 全额分配数量 = " + rebalance.getUU_STOCK_NEW(plant));
                putPool(plant, target, rebalance.getUU_STOCK_NEW(plant), rebalance, transferList, "FULL");
                continue; // 如果找到则不需要校验用量
            }

            BigDecimal availableMonth = rebalance.getAvailableMonth(plant); // availableMonth 为-1代表无AMU
            // 如果DC的AMU为0, 则说明DC无需求, 执行ROUNDING调拨
            if (availableMonth.compareTo(BigDecimal.valueOf(-1)) == 0) {
                logList.add("\t" + rebalance.getMATERIAL() + "在" + plant + "中AMU为0, 识别为无需求物料, 执行调库, 调库数量 = " + rebalance.getUU_STOCK_NEW(plant));
                putPool(plant, target, rebalance.getMaxRoundingQty(plant, target), rebalance, transferList, "ROUNDING");

            } else if (rebalance.getAvailableMonth(plant).compareTo(target) > 0) {
                // 如果DC的AMU不为0, 且可用时间大于用户选择的可用时间, 执行ROUNDING调拨
                logList.add("\t[超过6个月用量]" + plant + "可用库存月 = " + rebalance.getAvailableMonth(plant) + " > " + target);
                putPool(plant, target, rebalance.getMaxRoundingQty(plant, target), rebalance, transferList, "ROUNDING");
                logList.add("\t需要调拨, 暂放临时库存池, 调拨数量 = " + rebalance.getMaxRoundingQty(plant, target) + ", Rounding = " + rebalance.getROUNDING_VALUE(plant));
            }
        }
        logList.add("检查完毕");

        if (transferList.isEmpty() == false) {
            logList.add("临时库存池不为空, 执行调拨操作");
            for (int i = 0; i < transferList.size(); i++) {
                RebalanceTransferBean transfer = transferList.get(i);
                logList.add("- 处理第" + (i + 1) + "批次调拨");
                logList.add("\t当前可用目标DC = " + JSON.toJSONString(transfer.getAVALIABLE_TRANSFER_TO()));

                // 如果有多个可用DC, 需要选择调拨后, TOTAL / AMU最小的DC
                // 计算每个DC的权重
                logList.add("\t根据可调拨库存排序, 能接收数量最多的DC优先调拨");
                logList.add("\t\t排序前 = " + JSON.toJSONString(transfer.getAVALIABLE_TRANSFER_TO()));
                List<Map<String, String>> avaliableMapList = new ArrayList<>();
                for (String p : transfer.getAVALIABLE_TRANSFER_TO()) {
                    Map<String, String> map = new HashMap<>();
                    map.put("plant", p);
                    map.put("weight", rebalance.getTransferWeight(p, transfer.getTRANSFER_QTY(), target, logList));
                    avaliableMapList.add(map);
                }

                // 按权重排序, 权重大的排前面, 优先调拨
                List<String> priorityList = avaliableMapList.stream().sorted((e1, e2) -> {
                    if ("A".equals(e1.get("weight"))) {
                        return -1;
                    }
                    if ("A".equals(e2.get("weight"))) {
                        return 1;
                    }
                    return new BigDecimal(e2.get("weight")).compareTo(new BigDecimal(e1.get("weight")));
                }).map(e -> e.get("plant")).collect(Collectors.toList());
                logList.add("\t\t排序后 = " + JSON.toJSONString(priorityList));
                // 开始调拨
                for (String p : priorityList) {
                    // 全额调拨, 需要考虑AMU
                    logList.add("\t尝试向" + p + "调拨");
                    if (transfer.getTRANSFER_TYEP().equals("FULL")) {
                        if (rebalance.getONE_MM_AMU(p).compareTo(BigDecimal.ZERO) < 1) {
                            logList.add("\t\t全额调拨, 但" + p + "_AMU=0, 终止向" + p + "调拨");
                            transfer.setTRANSFER_PROPOSAL("No Transfer, Target DC AMU = 0"); // 调拨失败, 添加调拨意见, 尝试向下个Plant调拨
                        } else {
                            logList.add("\t\t全额调拨, 且" + p + "_AMU!=0, 不考虑" + p + "空闲库存, 执行调拨数量 = " + transfer.getTRANSFER_QTY());
                            transfer.setTRANSFER_TO(p);
                            rebalance.addUU_STOCK_TEMP(p, transfer.getTRANSFER_QTY()); //加入临时库位, 方便下一次计算空闲库位
                            transfer.setTRANSFER_PROPOSAL("Transfer Recommended"); // 调拨成功, 删除之前的调拨意见
                            break; // 终止调拨
                        }
                    } else {
                        // ROUNDING调拨
                        BigDecimal freeLocation = rebalance.getFreeStockLocation(p, target);
                        logList.add("\t\t" + p + "可用空闲库位 = " + freeLocation);
                        if (freeLocation.compareTo(transfer.getTRANSFER_QTY()) > -1) {
                            if (transfer.getTRANSFER_QTY().compareTo(BigDecimal.ZERO) > 0) {
                                logList.add("\t\t" + p + "可用空闲库位大于等于需要调拨数量 = " + transfer.getTRANSFER_QTY() + ", 执行调拨");
                                transfer.setTRANSFER_TO(p);
                                rebalance.addUU_STOCK_TEMP(p, transfer.getTRANSFER_QTY()); //加入临时库位, 方便下一次计算空闲库位
                                transfer.setTRANSFER_PROPOSAL("Transfer Proposed");
                            } else {
                                logList.add("\t\t调拨库存未到最小调拨数量, 终止调拨");
                                transfer.setTRANSFER_PROPOSAL("No Transfer, Delivery Qty < Rounding Qty");
                            }
                            break; // 终止调拨
                        } else if (freeLocation.compareTo(BigDecimal.ZERO) > 0 && freeLocation.compareTo(transfer.getTRANSFER_QTY()) < 0) {
                            // 如果空闲库存大于零且小于允许调拨库存, 那开始部分调拨
                            // 部分调拨数量 = RoundingDown(空闲库存 / ROUNDING_QTY) * ROUNDING_QTY
                            // 如果ROUNDING_VALUE为0, 则 调拨数量 = 空闲库存
                            BigDecimal transferPartQty;
                            if (rebalance.getROUNDING_VALUE(p).compareTo(BigDecimal.ZERO) == 0) {
                                transferPartQty = freeLocation;
                                logList.add("\t\tROUNDING_VALUE = 0, 根据空闲库位调拨");
                            } else {
                                transferPartQty = freeLocation.divideToIntegralValue(rebalance.getROUNDING_VALUE(p)).multiply(rebalance.getROUNDING_VALUE(p));
                            }
                            // 可部分调拨的数量为0, 则不执行调拨
                            if (transferPartQty.compareTo(BigDecimal.ZERO) == 0) {
                                logList.add("\t\t部分调拨库存未到最小调拨数量, 终止调拨");
                            } else {
                                logList.add("\t\t计划调拨" + transfer.getTRANSFER_QTY() + ", 因" + p + "空闲库存不足原因, 只允许调拨" + transferPartQty);
                                transfer.setTRANSFER_TO(p);
                                transfer.setTRANSFER_PROPOSAL("Transfer Possibility Exist");
                                transfer.setTRANSFER_QTY(transferPartQty);
                                rebalance.addUU_STOCK_TEMP(p, transferPartQty); //加入临时库位, 方便下一次计算空闲库位
                            }
                        }
                    }
                }

                // 如果从可用库存列表中找不到合适的DC(TRANSFER_PROPOSAL is null), 那么设置Proposal为No Transfer, Every DC Stock High
                if (StringUtils.isBlank(transfer.getTRANSFER_PROPOSAL())) {
                    transfer.setTRANSFER_PROPOSAL("No Transfer, Every DC Stock High");
                }

                // 如果无接收DC, 则需要将调拨数量设置为0
                if (StringUtils.isBlank(transfer.getTRANSFER_TO())) {
                    transfer.setTRANSFER_QTY(BigDecimal.ZERO);
                }
            }

            for (RebalanceTransferBean transfer : transferList) {
                if (transfer.hasTransferTo() == false) {
                    logList.add("=> 来自" + transfer.getTRANSFER_FROM() + "的" + transfer.getMATERIAL() + "物料无法执行调拨, 无法调拨数量 = " + transfer.getTRANSFER_QTY() + ", PROPOSAL = " + transfer.getTRANSFER_PROPOSAL());
                } else {
                    logList.add("=> 来自" + transfer.getTRANSFER_FROM() + "的" + transfer.getMATERIAL() + "物料可被执行调拨, 调拨目标 = " + transfer.getTRANSFER_TO() + ", 调拨数量 = " + transfer.getTRANSFER_QTY() + ", PROPOSAL = " + transfer.getTRANSFER_PROPOSAL());
                }
            }
        } else {
            logList.add(rebalance.getMATERIAL() + "未找到可调出的库存"); // 未找到可调出的库存, 需要手动创建一条transfer
        }

        RebalanceTransferLogs logs = new RebalanceTransferLogs();
        logs.setTARGET(target.toPlainString());
        logs.setMATERIAL(rebalance.getMATERIAL());
        logs.setLOGS(StringUtils.join(logList, "\r\n"));
        result.setLogs(logs);
        result.setTransfer(transferList);
        return result;
    }

    private static void putPool(String plant, BigDecimal target, BigDecimal putQty, RebalanceBean rebalanceBean, List<RebalanceTransferBean> transferList, String transferType) throws Exception {
        RebalanceTransferBean transfer = new RebalanceTransferBean(plant, rebalanceBean);
        transfer.setTRANSFER_FROM(plant);
        transfer.setTRANSFER_QTY(putQty);
        transfer.setAVALIABLE_TRANSFER_TO(rebalanceBean.getAvailableTo());
        transfer.setTRANSFER_TYEP(transferType);
        transfer.setTARGET(target.toPlainString());
        transferList.add(transfer);
    }
}
