package com.scp.inventory.service;

import com.starter.context.bean.Response;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;

public interface IInventorySummaryProjectionService {

    Response initPage(Map<String, Object> parameterMap);

    Response initPersonalSettings(String userid);

    Response queryReport1(Map<String, Object> parameterMap);

    void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport2(Map<String, Object> parameterMap, String userid);

    Response saveReport2(Map<String, Object> parameterMap, String userid);

    void downloadReport2(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport2Details(Map<String, Object> parameterMap);

    Response splitInventoryProjection();

    void downloadReport2Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport1Details(Map<String, Object> parameterMap);

    void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport3(Map<String, Object> parameterMap, String userid);

    void downloadReport3(Map<String, Object> parameterMap, HttpServletResponse response);
}
