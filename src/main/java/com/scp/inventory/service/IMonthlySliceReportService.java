package com.scp.inventory.service;

import com.starter.context.bean.Response;
import jakarta.servlet.http.HttpServletResponse;

import java.util.Map;

public interface IMonthlySliceReportService {

    Response initPage(String userid);

    Response saveReport1Calculated(Map<String, Object> parameterMap);

    Response queryReport1(Map<String, Object> parameterMap) throws Exception;

    Response queryReport2(String userid, Map<String, Object> parameterMap);

    Response saveReport2(String userid, Map<String, Object> parameterMap);

    Response syncProjection(Map<String, Object> parameterMap, String userid);

    void downloadDinReport(Map<String, Object> parameterMap, HttpServletResponse response);
}
