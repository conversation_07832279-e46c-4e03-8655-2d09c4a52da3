package com.scp.inventory.service;

import com.starter.context.bean.Response;
import jakarta.servlet.http.HttpServletResponse;

import java.util.Map;

public interface IPreStockService {
    Response initPage(Map<String, Object> parameterMap, String user_id);

    Response queryReport1(Map<String, Object> parameterMap);

    Response saveReport1Details(Map<String, Object> parameterMap);

    void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse response);
}
