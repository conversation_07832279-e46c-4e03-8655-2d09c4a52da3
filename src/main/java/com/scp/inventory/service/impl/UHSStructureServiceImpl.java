package com.scp.inventory.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.scp.inventory.bean.UHSStructureReport5Bean;
import com.scp.inventory.bean.UHSStructureReport5Treemap;
import com.scp.inventory.dao.IUHSStructureDao;
import com.scp.inventory.service.IUHSStructureService;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service("UHSStructureService")
@Scope("prototype")
@Transactional
public class UHSStructureServiceImpl extends ServiceHelper implements IUHSStructureService {

    @Resource
    private IUHSStructureDao UHSStructureDao;

    @Resource
    private Response response;

    @Resource
    private ExcelTemplate excelTemplate;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage() {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cascader", Utils.parseCascader(UHSStructureDao.queryCascader()));
        resultMap.put("week", UHSStructureDao.queryWeeks());
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        double rate = 1;
        if ("EUR".equalsIgnoreCase((String) parameterMap.get("currency"))) {
            rate = UHSStructureDao.queryCurrencyByWeek(parameterMap);
        }
        parameterMap.put("rate", rate);
        return response.setBody(UHSStructureDao.queryReport1(parameterMap));
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(UHSStructureDao.queryReport1DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(UHSStructureDao.queryReport1Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        this.generateFilter(parameterMap);
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "uhs_structure_" + "_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.inventory.dao.IUHSStructureDao.queryReport1Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        JSONArray selectedColumn2 = (JSONArray) parameterMap.get("report2SelectedColumns");
        if (selectedColumn2.isEmpty()) {
            selectedColumn2.add("BU");
            parameterMap.put("report2SelectedColumns", selectedColumn2);
        }

        double rate = 1;
        if ("EUR".equalsIgnoreCase((String) parameterMap.get("currency"))) {
            rate = UHSStructureDao.queryCurrencyByWeek(parameterMap);
        }
        parameterMap.put("rate", rate);
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(UHSStructureDao.queryReport2Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(UHSStructureDao.queryReport2(parameterMap));
        }

        // Grand Total
        Map<String, Object> totalMap = new LinkedHashMap<>();
        totalMap.put(selectedColumn2.getString(0), "Grand Total");

        double stock = 0;
        double grossUHS = 0;
        double grossUHSTop20 = 0;
        double grossUHSEETop1000 = 0;
        double grossUHSNonTop20EETop1000 = 0;
        double grossUHSNonTop20EENonTop1000 = 0;
        double netUHS = 0;
        double special = 0;
        double excess = 0;
        double excessTop20 = 0;
        double theoProv = 0;
        double theoProvTop20 = 0;
        double finProv = 0;
        double proTheoProv = 0;
        double missing = 0;
        double missingTop20 = 0;

        for (Map<String, Object> map : page.getData()) {
            stock += Utils.parseDouble(map.get("STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT"));
            grossUHS += Utils.parseDouble(map.get("GROSS_UNHEALTHY_VALUE"));
            grossUHSTop20 += Utils.parseDouble(map.get("GROSS_UNHEALTHY_VALUE_TOP20"));
            grossUHSEETop1000 += Utils.parseDouble(map.get("GROSS_UNHEALTHY_VALUE_EE_TOP1000"));
            grossUHSNonTop20EETop1000 += Utils.parseDouble(map.get("GROSS_UNHEALTHY_VALUE_NON_TOP20_EE_TOP1000"));
            grossUHSNonTop20EENonTop1000 += Utils.parseDouble(map.get("GROSS_UNHEALTHY_VALUE_NON_TOP20_NON_EE_TOP1000"));
            netUHS += Utils.parseDouble(map.get("NET_UNHEALTHY_VALUE"));
            special += Utils.parseDouble(map.get("SPECIAL_VALUE"));
            excess += Utils.parseDouble(map.get("HS_EXCESS_VALUE"));
            excessTop20 += Utils.parseDouble(map.get("HS_EXCESS_VALUE_TOP20"));
            theoProv += Utils.parseDouble(map.get("HS_THEO_PROV_VALUE"));
            theoProvTop20 += Utils.parseDouble(map.get("HS_THEO_PROV_VALUE_TOP20"));
            finProv += Utils.parseDouble(map.get("FIN_PROV_VALUE"));
            missing += Utils.parseDouble(map.get("HS_MISSING_VALUE"));
            missingTop20 += Utils.parseDouble(map.get("HS_MISSING_VALUE_TOP20"));
            proTheoProv += Utils.parseDouble(map.get("PROPOSAL_FIN_PROV_VALUE"));
        }

        totalMap.put("STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT", stock);
        totalMap.put("GROSS_UNHEALTHY_VALUE", grossUHS);
        totalMap.put("GROSS_UNHEALTHY_VALUE_TOP20", grossUHSTop20);
        totalMap.put("GROSS_UNHEALTHY_VALUE_EE_TOP1000", grossUHSEETop1000);
        totalMap.put("GROSS_UNHEALTHY_VALUE_NON_TOP20_EE_TOP1000", grossUHSNonTop20EETop1000);
        totalMap.put("GROSS_UNHEALTHY_VALUE_NON_TOP20_NON_EE_TOP1000", grossUHSNonTop20EENonTop1000);
        totalMap.put("GROSS_UNHEALTHY_PERCENT", stock == 0 ? 0 : grossUHS / stock * 100);
        totalMap.put("GROSS_UNHEALTHY_PERCENT_TOP20", grossUHS == 0 ? 0 : grossUHSTop20 / grossUHS * 100);
        totalMap.put("NET_UNHEALTHY_PERCENT", stock == 0 ? 0 : netUHS / stock * 100);
        totalMap.put("SPECIAL_VALUE", special);
        totalMap.put("SPECIAL_PERCENT", stock == 0 ? 0 : special / stock * 100);
        totalMap.put("HS_EXCESS_VALUE", excess);
        totalMap.put("HS_EXCESS_VALUE_TOP20", excessTop20);
        totalMap.put("HS_THEO_PROV_VALUE", theoProv);
        totalMap.put("HS_THEO_PROV_VALUE_TOP20", theoProvTop20);
        totalMap.put("FIN_PROV_VALUE", finProv);
        totalMap.put("PROPOSAL_FIN_PROV_VALUE", proTheoProv);
        totalMap.put("FIN_VS_PFP", proTheoProv == 0 ? 0 : finProv / proTheoProv * 100);
        totalMap.put("HS_MISSING_VALUE", missing);
        totalMap.put("HS_MISSING_VALUE_TOP20", missingTop20);
        page.getData().add(totalMap);
        return response.setBody(page);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        JSONArray report3yAxis = (JSONArray) parameterMap.get("report3yAxis");
        List<String> yAxisList = new ArrayList<>();
        if (report3yAxis != null && report3yAxis.size() > 0) {
            for (Object yobj : report3yAxis) {
                String y = String.valueOf(yobj);
                switch (y) {
                    case "GROSS_UNHEALTHY_VALUE" -> yAxisList.add("NVL(T.GROSS_UNHEALTHY_VALUE, 0)");
                    case "NET_UNHEALTHY_VALUE" -> yAxisList.add("NVL(T.NET_UNHEALTHY_VALUE, 0)");
                    case "HS_MISSING_VALUE" -> yAxisList.add("NVL(T.HS_MISSING_VALUE, 0)");
                    case "HS_EXCESS_VALUE" -> yAxisList.add("NVL(T.HS_EXCESS_VALUE, 0)");
                    case "SPECIAL_VALUE" -> yAxisList.add("CASE WHEN T.HS_RCA LIKE 'E98%' THEN (NVL(T.HS_EXCESS_VALUE, 0) + NVL(T.HS_THEO_PROV_VALUE, 0)) ELSE 0 END");
                }
            }
        } else {
            yAxisList.add("NVL(T.GROSS_UNHEALTHY_VALUE, 0)");
        }
        parameterMap.put("yAxis", StringUtils.join(yAxisList, " + "));
        Map<String, Object> resultMap = new LinkedHashMap<>();
        Map<String, List<Double>> legendDataList = new LinkedHashMap<>();
        List<Map<String, Object>> data = UHSStructureDao.queryReport3(parameterMap);

        Map<String, Map<String, Double>> legendDataMap = new LinkedHashMap<>();
        boolean isEur = "EUR".equalsIgnoreCase((String) parameterMap.get("currency"));
        List<String> xAxisList = new ArrayList<>();
        for (Map<String, Object> map : data) {
            String xAxis = String.valueOf(map.get("WEEK"));
            String legend = (String) map.get("LEGEND");
            if (xAxisList.contains(xAxis) == false) {
                xAxisList.add(xAxis);
            }
            Map<String, Double> temp = legendDataMap.computeIfAbsent(legend, k -> new LinkedHashMap<>());

            if (isEur) {
                temp.put(xAxis, Utils.parseDouble(map.get("YAXIS_VALUE_EUR")));
            } else {
                temp.put(xAxis, Utils.parseDouble(map.get("YAXIS_VALUE")));
            }
        }

        for (String key : legendDataMap.keySet()) {
            Map<String, Double> temp = legendDataMap.get(key);
            List<Double> list = legendDataList.computeIfAbsent(key, k -> new ArrayList<>());
            for (String x : xAxisList) {
                list.add(temp.getOrDefault(x, null));
            }
        }
        resultMap.put("xAxis", xAxisList);
        resultMap.putAll(legendDataList);
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport4(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        Map<String, Object> resultMap = new LinkedHashMap<>();
        List<Map<String, Object>> data = UHSStructureDao.queryReport4(parameterMap);

        boolean isEur = "EUR".equalsIgnoreCase((String) parameterMap.get("currency"));
        List<String> xAxisList = new ArrayList<>();
        List<BigDecimal> HS_THEO_PROV = new ArrayList<>();
        List<BigDecimal> HS_THEO_PROV_SPECIAL = new ArrayList<>();
        List<BigDecimal> HS_EXCESS = new ArrayList<>();
        List<BigDecimal> HS_EXCESS_SPECIAL = new ArrayList<>();
        List<BigDecimal> HEALTH_STOCK = new ArrayList<>();
        List<BigDecimal> HS_MISSING = new ArrayList<>();
        List<BigDecimal> NET_UNHEALTHY_PERCENT = new ArrayList<>();
        List<BigDecimal> SPECIAL_PERCENT = new ArrayList<>();
        for (Map<String, Object> map : data) {
            xAxisList.add(String.valueOf(map.get("WEEK")));
            if (isEur) {
                HS_THEO_PROV.add(Utils.parseBigDecimal(map.get("HS_THEO_PROV_VALUE_EUR")));
                HS_THEO_PROV_SPECIAL.add(Utils.parseBigDecimal(map.get("HS_THEO_PROV_VALUE_SPECIAL_EUR")));
                HS_EXCESS.add(Utils.parseBigDecimal(map.get("HS_EXCESS_VALUE_EUR")));
                HS_EXCESS_SPECIAL.add(Utils.parseBigDecimal(map.get("HS_EXCESS_VALUE_SPECIAL_EUR")));
                HEALTH_STOCK.add(Utils.parseBigDecimal(map.get("HEALTH_STOCK_EUR")));
                HS_MISSING.add(Utils.parseBigDecimal(map.get("HS_MISSING_VALUE_EUR")));
            } else {
                HS_THEO_PROV.add(Utils.parseBigDecimal(map.get("HS_THEO_PROV_VALUE")));
                HS_THEO_PROV_SPECIAL.add(Utils.parseBigDecimal(map.get("HS_THEO_PROV_VALUE_SPECIAL")));
                HS_EXCESS.add(Utils.parseBigDecimal(map.get("HS_EXCESS_VALUE")));
                HS_EXCESS_SPECIAL.add(Utils.parseBigDecimal(map.get("HS_EXCESS_VALUE_SPECIAL")));
                HEALTH_STOCK.add(Utils.parseBigDecimal(map.get("HEALTH_STOCK")));
                HS_MISSING.add(Utils.parseBigDecimal(map.get("HS_MISSING_VALUE")));
            }
            NET_UNHEALTHY_PERCENT.add(Utils.parseBigDecimal(map.get("NET_UNHEALTHY_PERCENT")));
            SPECIAL_PERCENT.add(Utils.parseBigDecimal(map.get("SPECIAL_PERCENT")));
        }

        resultMap.put("xAxis", xAxisList);
        resultMap.put("HS_THEO_PROV", HS_THEO_PROV);
        resultMap.put("HS_THEO_PROV_SPECIAL", HS_THEO_PROV_SPECIAL);
        resultMap.put("HS_EXCESS", HS_EXCESS);
        resultMap.put("HS_EXCESS_SPECIAL", HS_EXCESS_SPECIAL);
        resultMap.put("HEALTH_STOCK", HEALTH_STOCK);
        resultMap.put("HS_MISSING", HS_MISSING);
        resultMap.put("NET_UNHEALTHY_PERCENT", NET_UNHEALTHY_PERCENT);
        resultMap.put("SPECIAL_PERCENT", SPECIAL_PERCENT);
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport5(Map<String, Object> parameterMap) throws Exception {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateReport5Tooltips(parameterMap);

        // 将前台传过来的label转换成列名, 同时也可以防止恶意代码注入
        parameterMap.put("level1", this.getColumnNameByLabel(parameterMap.get("level1")));
        parameterMap.put("level2", this.getColumnNameByLabel(parameterMap.get("level2")));
        parameterMap.put("level3", this.getColumnNameByLabel(parameterMap.get("level3")));
        parameterMap.put("level4", this.getColumnNameByLabel(parameterMap.get("level4")));
        parameterMap.put("level5", this.getColumnNameByLabel(parameterMap.get("level5")));

        List<UHSStructureReport5Treemap> resultList = new ArrayList<>();
        List<UHSStructureReport5Bean> dataList = UHSStructureDao.queryReport5(parameterMap);
        for (UHSStructureReport5Bean data : dataList) {
            this.convertReport5Data(resultList, data);
        }
        return response.setBody(resultList);
    }

    @SuppressWarnings("unchecked")
    private void generateValueColumn(Map<String, Object> parameterMap) {
        List<String> report5Value = (List<String>) parameterMap.get("report5Value");
        String currency = (String) parameterMap.get("currency");
        if (report5Value == null || report5Value.isEmpty()) {
            report5Value = new ArrayList<>();
            report5Value.add("THEO_PROV");
            report5Value.add("EXCESS_STOCK");
            report5Value.add("MISSING_STOCK");
        }

        List<String> result = new ArrayList<>();
        for (String c : report5Value) {
            result.add(this.getValueColumnByID(c, currency));
        }

        parameterMap.put("valueColumn", "SUM(" + StringUtils.join(result, " + ") + ")");
    }

    private void convertReport5Data(List<UHSStructureReport5Treemap> list, UHSStructureReport5Bean data) throws Exception {
        String[] categorysOrg = new String[]{data.getCategory1(), data.getCategory2(), data.getCategory3(), data.getCategory4(), data.getCategory5()};
        List<String> categories = new ArrayList<>();

        for (String category : categorysOrg) {
            if (StringUtils.isNotBlank(category)) {
                categories.add(category);
            } else {
                break;
            }
        }

        // 这边逻辑比较复杂, 所以用最笨的方法来描述了, 以免后期不好维护
        // 先把这一行数据转成treemap的数据
        // 第一个节点
        List<UHSStructureReport5Treemap> child = new ArrayList<>();
        UHSStructureReport5Treemap root = new UHSStructureReport5Treemap();
        root.setName(categories.get(0));
        root.setTips(data.copyTooltips()); // 因为这个tooltips要放在树中全局使用, 所以必须要生成一个新节点
        root.setChildren(child);

        // 中间节点
        for (int i = 1; i < categories.size() - 1; i++) {
            UHSStructureReport5Treemap treemap = new UHSStructureReport5Treemap();
            treemap.setName(categories.get(i));
            treemap.setTips(data.copyTooltips());

            child.add(treemap);
            child = new ArrayList<>();
            treemap.setChildren(child);
        }

        // 最后一个节点
        UHSStructureReport5Treemap lastNode = new UHSStructureReport5Treemap();
        lastNode.setName(categories.get(categories.size() - 1));
        lastNode.setValue(data.getValue());
        lastNode.setTips(data.copyTooltips());
        child.add(lastNode);

        // 将这行treemap与原始数据相加
        // 先找到list中是否有这个数据节点
        Optional<UHSStructureReport5Treemap> beanOpt = list.stream().filter(b -> b.getName().equals(categories.get(0))).findFirst();
        if (beanOpt.isPresent()) {
            UHSStructureReport5Treemap bean = beanOpt.get();
            bean.add(root); // 两个节点合并
        } else { //找不到的时候最省事, 直接放入list就可以了
            list.add(root);
        }
    }

    private void generateReport5Tooltips(Map<String, Object> parameterMap) {
        List<String> tooltips = ((JSONArray) parameterMap.get("report5Tooltips")).toJavaList(String.class);
        String currency = (String) parameterMap.get("currency");
        if (!tooltips.isEmpty()) {
            List<String> tooltipsColumns = tooltips.stream().map(
                    this::getColumnNameByLabel).collect(Collectors.toList());
            List<String> tooltipsColumnsName = new ArrayList<>();

            for (String c : tooltipsColumns) {
                tooltipsColumnsName.add("NVL(SUM(" + this.getValueColumnByID(c, currency) + "),0) AS " + c);
            }
            parameterMap.put("tooltipsColumns", StringUtils.join(tooltipsColumnsName, ", "));
        }
    }

    private String getColumnNameByLabel(Object labelObj) {
        String label = (String) labelObj;
        if (label == null) {
            return null;
        }
        if (Utils.hasInjectionAttack(label)) {
            return "";
        }
        return label;
    }

    private String getValueColumnByID(String key, String currency) {
        if ("EUR".equalsIgnoreCase(currency)) {
            switch (key) {
                case "THEO_PROV" -> {
                    return "DECODE(EXCHANGE_RATE, 0, 0, HS_THEO_PROV_VALUE / EXCHANGE_RATE)";
                }
                case "THEO_PROV_SPECIAL_EVENT" -> {
                    return "DECODE(EXCHANGE_RATE, 0, 0, HS_THEO_PROV_VALUE_SPECIAL / EXCHANGE_RATE)";
                }
                case "EXCESS_STOCK" -> {
                    return "DECODE(EXCHANGE_RATE, 0, 0, HS_EXCESS_VALUE / EXCHANGE_RATE)";
                }
                case "EXCESS_SPECIAL_EVENT" -> {
                    return "DECODE(EXCHANGE_RATE, 0, 0, HS_EXCESS_VALUE_SPECIAL / EXCHANGE_RATE)";
                }
                case "MISSING_STOCK" -> {
                    return "DECODE(EXCHANGE_RATE, 0, 0, HS_MISSING_VALUE / EXCHANGE_RATE)";
                }
                case "HEALTH_STOCK" -> {
                    return "DECODE(EXCHANGE_RATE, 0, 0, HEALTH_STOCK / EXCHANGE_RATE)";
                }
            }
        } else {
            switch (key) {
                case "THEO_PROV" -> {
                    return "HS_THEO_PROV_VALUE";
                }
                case "THEO_PROV_SPECIAL_EVENT" -> {
                    return "HS_THEO_PROV_VALUE_SPECIAL";
                }
                case "EXCESS_STOCK" -> {
                    return "HS_EXCESS_VALUE";
                }
                case "EXCESS_SPECIAL_EVENT" -> {
                    return "HS_EXCESS_VALUE_SPECIAL";
                }
                case "MISSING_STOCK" -> {
                    return "HS_MISSING_VALUE";
                }
                case "HEALTH_STOCK" -> {
                    return "HEALTH_STOCK";
                }
            }
        }

        return "";
    }

    private void generateFilter(Map<String, Object> parameterMap) {
        // 生成筛选条件
        this.generateCascaderFilterSQL(parameterMap);

        // tree path
        String selectedTreePath = (String) parameterMap.get("selectedTreePath");
        if (StringUtils.isNotBlank(selectedTreePath)) {
            List<String> conditions = new ArrayList<>();
            String[] treePaths = selectedTreePath.split(" > ");
            for (int i = 1; i <= Math.min(treePaths.length, 5); i++) {
                String key = Utils.randomStr(8);
                if ("Others".equals(StringUtils.trim(treePaths[i - 1]))) {
                    String name = this.getColumnNameByLabel(parameterMap.get("level" + i));
                    conditions.add("(" + name + " = #{" + key + ",jdbcType=VARCHAR} or " + name + " is null )");
                } else {
                    conditions.add(this.getColumnNameByLabel(parameterMap.get("level" + i)) + " = #{" + key + ",jdbcType=VARCHAR}");
                }
                parameterMap.put(key, StringUtils.trim(treePaths[i - 1]));
            }

            parameterMap.put("treePathFilter", "(" + StringUtils.join(conditions, " and ") + ")");
        }
    }
}
