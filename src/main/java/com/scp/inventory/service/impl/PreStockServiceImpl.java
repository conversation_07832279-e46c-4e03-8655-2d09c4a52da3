package com.scp.inventory.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.scp.inventory.dao.IPreStockDao;
import com.scp.inventory.service.IPreStockService;
import com.starter.context.StaticComponent;
import com.starter.context.bean.CacheRemove;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.awt.image.BufferedImage;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service("PreStockService")
@Scope("prototype")
@Transactional
public class PreStockServiceImpl extends ServiceHelper implements IPreStockService {
    @Resource
    private IPreStockDao preStockDao;

    @Resource
    private Response response;

    @Resource
    private ExcelTemplate excelTemplate;

    public final static String PARENT_CODE = "menu223";

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage(Map<String, Object> parameterMap, String user_id) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cascader", Utils.parseCascader(preStockDao.queryCascader()));
        return response.setBody(resultMap);
    }


    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        page.setTotal(preStockDao.queryReport1Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(preStockDao.queryReport1(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response saveReport1Details(Map<String, Object> parameterMap) {
        Object obj = parameterMap.get("report1Update");
        if (obj instanceof JSONObject jsonObj) {
            List<Map<String, Object>> dataList = new ArrayList<>();
            for (String key : jsonObj.keySet()) {
                JSONObject changeObj = jsonObj.getJSONObject(key);
                Map<String, Object> map = new HashMap<>();
                String[] keys = key.split("#");
                map.put("order", keys[0]);
                map.put("item", keys[1]);
                map.put("remain_stock_qty", changeObj.getString("REMAIN_STOCK_QTY"));
                map.put("new_estimated_consumption_date", changeObj.getString("NEW_ESTIMATED_CONSUMPTION_DATE"));
                dataList.add(map);
            }
            if (dataList.isEmpty() == false) {
                parameterMap.put("dataList", dataList);
                preStockDao.saveReport1Details(parameterMap);
            }
        }
        return response;
    }

    @Override
    public void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateValueColumn(parameterMap);

        String fileName = "pre_stock_data_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.inventory.dao.IPreStockDao.queryReport1", parameterMap);
    }


    private void generateValueColumn(Map<String, Object> parameterMap) {
        String resultType = (String) parameterMap.get("resultType");
        String valueColumn = "COUNT(1)";
        if ("Line".equalsIgnoreCase(resultType)) {
            valueColumn = "COUNT(1)";
        }
        parameterMap.put("valueColumn", valueColumn);
    }
    /**
     * 生成cascader filter
     *
     * @param parameterMap 参数map
     */
    private void generateCascaderFilter(Map<String, Object> parameterMap) {
        this.generateCascaderFilterSQL(parameterMap);
        // categroy
        JSONArray categroyArray = (JSONArray) parameterMap.get("categroy");
        if (categroyArray == null || categroyArray.isEmpty()) {
            List<String> categroyDefault = new ArrayList<>();
            categroyDefault.add("ENTITY");
            parameterMap.put("categroy", categroyDefault);
        }
    }

}
