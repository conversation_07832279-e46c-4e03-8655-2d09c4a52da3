package com.scp.inventory.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.scp.inventory.bean.AlertReport1Column;
import com.scp.inventory.dao.IInventoryAlertDao;
import com.scp.inventory.service.IInventoryAlertService;
import com.adm.system.bean.CascaderBean;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.util.*;

@Service("inventoryAlertService")
@Scope("prototype")
@Transactional
public class InventoryAlertServiceImpl implements IInventoryAlertService {

    @Resource
    private IInventoryAlertDao inventoryAlertDao;

    @Resource
    private Response response;

    @Resource
    private ExcelTemplate excelTemplate;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage() {
        Map<String, Object> resultMap = new HashMap<>();
        List<CascaderBean> resultList = new ArrayList<>();
        List<Map<String, String>> dataList = inventoryAlertDao.queryAlertCascader();

        Map<String, List<Map<String, String>>> tempMap = new LinkedHashMap<>();

        for (Map<String, String> map : dataList) {
            List<Map<String, String>> list = tempMap.computeIfAbsent(map.get("CATEGORY"), key -> new ArrayList<>());
            list.add(map);
        }

        for (String key : tempMap.keySet()) {
            CascaderBean bean = new CascaderBean();
            resultList.add(bean);
            bean.setLabel(key);
            bean.setValue(key);
            for (Map<String, String> map : tempMap.get(key)) {
                CascaderBean subBean = new CascaderBean();
                subBean.setLabel(map.get("NAME"));
                subBean.setValue(subBean.getLabel());
                bean.addChild(subBean);
            }
        }

        resultMap.put("cascader", resultList);
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        page.setTotal(inventoryAlertDao.queryReport1Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(inventoryAlertDao.queryReport1(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        String fileName = "alert_data_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.inventory.dao.IInventoryAlertDao.queryReport1", parameterMap);
    }

    @Override
    public Response queryReport1Details(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        page.setTotal(inventoryAlertDao.queryReport1DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(inventoryAlertDao.queryReport1Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        String fileName = "alert_data_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.inventory.dao.IInventoryAlertDao.queryReport1Details", parameterMap);
    }

    private void generateValueColumn(Map<String, Object> parameterMap) {
        String[] types = new String[]{"GOODS_RECEIVE", "PO_CREATION", "LA_CREATION", "NEW_BLOCK", "CONS_STOCK_RELEASE", "NEW_STOCK_GAIN", "NEW_GOODS_RETURN"};
        String[] positive = new String[]{"UU_STOCK", "STOCK_IN_QI", "RESTRICTED_STOCK", "BLOCKED_STOCK", "GIT", "OPEN_PO"};
        String[] negative = new String[]{"OPEN_SO", "OPEN_TRANSFER", "MO_RESERVATION", "LT_DEMAND", "SAFETY_STOCK", "AMU", "AMF"};
        String[] considerConsignmentStocks = new String[]{"UU_STOCK", "STOCK_IN_QI", "RESTRICTED_STOCK", "BLOCKED_STOCK"};
        String type = (String) parameterMap.get("type");
        String scope = (String) parameterMap.get("scope");

        Map<String, Object> configMap = (JSONObject) parameterMap.get("params");

        List<String> sqlList = new ArrayList<>();

        if ("All".equalsIgnoreCase(scope)) {
            parameterMap.put("block", "t.BLOCKED_STOCK");
        } else {
            parameterMap.put("block", "t.BLOCKED_STOCK - t.CONSIGNMENT_BLOCKED_STOCK");
        }

        for (int i = 0; i < types.length; i++) {
            String s = types[i];
            Map<String, Object> config = (JSONObject) configMap.get(s);
            if (config == null) {
                continue;
            }
            List<AlertReport1Column> positiveList = new ArrayList<>();
            List<AlertReport1Column> negativeList = new ArrayList<>();
            for (String p : positive) {
                if (config.containsKey(p)) {
                    positiveList.add(new AlertReport1Column(p, config.get(p)));
                }
            }

            for (String n : negative) {
                if (config.containsKey(n)) {
                    negativeList.add(new AlertReport1Column(n, config.get(n)));
                }
            }

            StringBuilder sql = new StringBuilder();
            sql.append("case when ");
            if (i < 3) {
                // 对于前两行, 如果scope不是all, 需要减去Consider Consignment Stock
                // 需要将Consider Consignment Stock加入negativeList
                if (i < 2) {
                    if ("All".equalsIgnoreCase(scope) == false) {
                        for (AlertReport1Column p : positiveList) {
                            if (Utils.containsStr(considerConsignmentStocks, p.getName())) {
                                negativeList.add(new AlertReport1Column("CONSIGNMENT_" + p.getName(), p.getValue()));
                            }
                        }
                    }
                }

                sql.append(this.joinReport1Column(positiveList));
                sql.append(" - ");
                sql.append(this.joinReport1Column(negativeList));
                sql.append(" >= nvl(mm.");
                sql.append(this.getColumnNameByCategory(s));
                sql.append(", 0)");
            } else {
                positiveList.addAll(negativeList);
                List<String> tempSQL = new ArrayList<>();
                sql.append("mm.").append(this.getColumnNameByCategory(s)).append(" >= ");
                for (AlertReport1Column p : positiveList) {
                    tempSQL.add("nvl(t." + this.getColumnNameByCategory(p.getName()) +
                            ",0) * " + p.getValuePercent()
                    );
                }
                sql.append(" (").append(StringUtils.join(tempSQL, " + ")).append(") ");
            }
            if ("Moving Average Price".equalsIgnoreCase(type)) {
                sql.append(" then ");
                sql.append("mm.");
                sql.append(this.getColumnNameByCategory(s));
                sql.append(" * t.UNIT_COST");
            } else if ("Quantity".equalsIgnoreCase(type)) {
                sql.append(" then ");
                sql.append("mm.");
                sql.append(this.getColumnNameByCategory(s));
            } else {
                sql.append(" and  mm.");
                sql.append(this.getColumnNameByCategory(s));
                sql.append(" > 0");
                sql.append(" then ");
                sql.append("1 else 0 ");
            }
            sql.append(" end as ");
            sql.append(s);
            sqlList.add(sql.toString());
        }
        parameterMap.put("valueColumn", StringUtils.join(sqlList, ","));
    }

    private String joinReport1Column(List<AlertReport1Column> list) {
        if (list.isEmpty()) {
            return " 0 ";
        } else {
            List<String> temp = new ArrayList<>();
            for (AlertReport1Column c : list) {
                Object v = c.getValue();
                if (v instanceof Boolean) {
                    if ((Boolean) v) {
                        if ("SAFETY_STOCK".equalsIgnoreCase(c.getName()) || "LT_DEMAND".equalsIgnoreCase(c.getName())) {
                            temp.add("nvl(decode(t.STOCKING_POLICY, 'MTO', 0, t." + this.getColumnNameByCategory(c.getName()) + "), 0)");
                        } else {
                            temp.add("nvl(t." + this.getColumnNameByCategory(c.getName()) + ", 0)");
                        }
                    }
                } else {
                    if (Utils.isStrictNumeric(String.valueOf(v))) {
                        double percent = c.getValuePercent();
                        if ("SAFETY_STOCK".equalsIgnoreCase(c.getName()) || "LT_DEMAND".equalsIgnoreCase(c.getName())) {
                            temp.add("nvl(decode(t.STOCKING_POLICY, 'MTO', 0, (t." + this.getColumnNameByCategory(c.getName()) + " * " + percent + ")), 0)");
                        } else {
                            temp.add("nvl(t." + this.getColumnNameByCategory(c.getName()) + " * " + percent + ", 0)");
                        }
                    }
                }
            }
            return "(" + StringUtils.join(temp, " + ") + ")";
        }
    }

    private String getColumnNameByCategory(String category) {
        return switch (category) {
            case "GOODS_RECEIVE" -> "NEW_GR";
            case "PO_CREATION" -> "NEW_PO";
            case "LA_CREATION" -> "NEW_LA";
            case "NEW_BLOCK", "BLOCKED_STOCK" -> "BLOCKED_STOCK";
            case "CONS_STOCK_RELEASE" -> "CONSIGNMENT_STOCK";
            case "NEW_STOCK_GAIN" -> "NEW_STOCK_GAIN";
            case "NEW_GOODS_RETURN" -> "NEW_GOODS_RETURN";
            case "UU_STOCK" -> "UU_STOCK";
            case "STOCK_IN_QI" -> "STOCK_IN_QI";
            case "RESTRICTED_STOCK" -> "RESTRICTED_STOCK";
            case "GIT" -> "GIT_QTY";
            case "OPEN_PO" -> "OPEN_PO";
            case "OPEN_SO" -> "OPEN_SO";
            case "OPEN_TRANSFER" -> "OPEN_TRANSFER";
            case "MO_RESERVATION" -> "MO_RESERVATION";
            case "LT_DEMAND" -> "LT_DEMAND";
            case "SAFETY_STOCK" -> "SAFETY_STOCK";
            case "AMU" -> "ONE_MM_AMU";
            case "AMF" -> "AMF";
            default -> category;
        };
    }

    /**
     * 生成cascader filter
     *
     * @param parameterMap 参数map
     */
    @SuppressWarnings("unchecked")
    private void generateCascaderFilter(Map<String, Object> parameterMap) {
        List<String> categories = (List<String>) parameterMap.get("categories");
        if (categories == null || categories.isEmpty()) {
            categories = new ArrayList<>();
            categories.add("CLUSTER_NAME");
            categories.add("ENTITY");
        }
        parameterMap.put("categories", categories);

        // 生成筛选条件
        JSONArray categoryArray = (JSONArray) parameterMap.get("filterList");
        if (categoryArray != null) {
            Map<String, List<String>> filterMap = new HashMap<>();

            for (Object subObj : categoryArray) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = subArray.getString(0);

                String value = subArray.getString(1);
                String key = Utils.randomStr(8);

                if (Utils.hasInjectionAttack(columnName)) {
                    continue;
                }

                List<String> fl = filterMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                fl.add("#{" + key + ",jdbcType=VARCHAR}");
                parameterMap.put(key, value);
            }

            List<String> filterList = new ArrayList<>();

            for (String key : filterMap.keySet()) {
                List<String> fl = filterMap.get(key);
                filterList.add("t." + key + " in (" + StringUtils.join(fl, ",") + ")");
            }

            parameterMap.put("filters", StringUtils.join(filterList, " and "));
        }

        // load special parameter
        String specialContent = (String) parameterMap.get("specialContent");
        String specialColumn = (String) parameterMap.get("specialType");
        if (Utils.hasInjectionAttack(specialColumn) == false) {
            if (StringUtils.isNotBlank(specialContent)) {
                parameterMap.put("specialList", Utils.splitValue(specialContent));
                parameterMap.put("specialColumn", specialColumn);
            }
        }
    }
}
