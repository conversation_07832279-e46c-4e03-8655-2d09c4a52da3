package com.scp.inventory.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import com.starter.utils.excel.SimpleSheetContentsHandler;
import com.starter.context.bean.*;
import com.starter.context.bean.scptable.ScpTableHelper;
import com.scp.inventory.bean.UHSReport2;
import com.scp.inventory.bean.UHSReport3Result;
import com.scp.inventory.dao.IInventoryUHSDao;
import com.scp.inventory.service.IInventoryUHSService;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.model.StylesTable;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

@Service("inventoryUHSService")
@Scope("prototype")
@Transactional
public class InventoryUHSServiceImpl extends ServiceHelper implements IInventoryUHSService {

    @Resource
    private IInventoryUHSDao inventoryUHSDao;

    @Resource
    private Response response;

    @Resource
    private ExcelTemplate excelTemplate;

    @Resource
    private ScpTableHelper scpTableHelper;

    private final int DEFAULT_WEEK_CNT = 8;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage() {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("week", inventoryUHSDao.queryWeeks());

        Map<String, List<String>> report3Dropdown = new HashMap<>();

        List<Map<String, String>> uhsCodes = inventoryUHSDao.queryUHSCode();
        for (Map<String, String> map : uhsCodes) {
            String category = map.get("CATEGORY");
            List<String> value = report3Dropdown.computeIfAbsent(category, k -> new ArrayList<>());
            value.add(map.get("CODE"));
        }
        resultMap.put("report3Dropdown", report3Dropdown);
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1Weeks(Map<String, Object> parameterMap) {
        String week = (String) parameterMap.get("week");
        String year = week.substring(0, 4);
        String weekNo = week.substring(4, 6);
        String lastYear = String.valueOf(Utils.parseInt(year) - 1);

        List<String> weeks = inventoryUHSDao.queryWeekColumns(week, DEFAULT_WEEK_CNT);
        List<Map<String, Object>> weekList = new ArrayList<>();

        String lastYearEnd = inventoryUHSDao.queryReport1LastYearEnd(lastYear);
        String monthEnd = inventoryUHSDao.queryReport1MonthEnd(year, weekNo);

        Map<String, Object> lastYearEndMap = new HashMap<>();
        Map<String, Object> YTDMap = new HashMap<>();
        Map<String, Object> monthEndMap = new HashMap<>();

        lastYearEndMap.put("data", lastYearEnd);
        lastYearEndMap.put("title", "Last Year End");
        weekList.add(lastYearEndMap);

        YTDMap.put("data", year);
        YTDMap.put("title", "YTD");
        weekList.add(YTDMap);

        monthEndMap.put("data", monthEnd);
        monthEndMap.put("title", "Month End");
        weekList.add(monthEndMap);


        for (String w : weeks) {
            Map<String, Object> map = new HashMap<>();
            map.put("data", w);
            map.put("title", w);
            weekList.add(map);
        }
        return response.setBody(weekList);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        // 生成需要查询的周
        String type = (String) parameterMap.get("type");
        String ratioColumn = "_" + StringUtils.upperCase(type) + "_RATIO";

        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        String sort = page.getSort();
        if (StringUtils.startsWith(sort, "\"20")) {
            String[] as = sort.split(" ");
            sort = "nvl(\"" + as[0].replace("\"", "'") + ratioColumn + "\", -1) " + as[1];
        }
        page.setSort(sort);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        this.generateCascaderFilter(parameterMap);
        String week = (String) parameterMap.get("week");
        String year = week.substring(0, 4);
        String lastYear = String.valueOf(Utils.parseInt(year) - 1);
        String weekNo = week.substring(4, 6);

        String lastYearEnd = inventoryUHSDao.queryReport1LastYearEnd(lastYear);
        String monthEnd = inventoryUHSDao.queryReport1MonthEnd(year, weekNo);
        List<String> weekList = inventoryUHSDao.queryWeekColumns(week, DEFAULT_WEEK_CNT);
        if (weekList.contains(lastYearEnd) == false) {
            weekList.add(lastYearEnd);
        }
        if (weekList.contains(monthEnd) == false) {
            weekList.add(monthEnd);
        }
        parameterMap.put("weekList", weekList);
        parameterMap.put("year", year);

        // 查询数据
        List<Map<String, Object>> resultList = new ArrayList<>();
        List<Map<String, Object>> dataList = inventoryUHSDao.queryReport1(parameterMap);

        Map<String, Object> total = new HashMap<>();
        Map<String, Object> excessTotal = new HashMap<>();
        Map<String, Object> provisionTotal = new HashMap<>();
        Map<String, Object> missingTotal = new HashMap<>();
        Map<String, Object> specialTotal = new HashMap<>();
        Map<String, Object> totalStockTotal = new HashMap<>();

        total.put("ENTITY", "Total");
        excessTotal.put("ENTITY", "Excess");
        provisionTotal.put("ENTITY", "Theo.provision");
        missingTotal.put("ENTITY", "Missing");
        totalStockTotal.put("ENTITY", "Total Stock");
        weekList.add(year);

        // 一行变三行
        for (Map<String, Object> map : dataList) {
            Map<String, Object> ratio = new HashMap<>();
            Map<String, Object> excess = new HashMap<>();
            Map<String, Object> provision = new HashMap<>();
            Map<String, Object> missing = new HashMap<>();
            Map<String, Object> totalStock = new HashMap<>();

            ratio.put("ENTITY", map.get("ENTITY"));
            ratio.put("PARENT", map.get("ENTITY"));

            excess.put("ENTITY", "Excess");
            excess.put("PARENT", map.get("ENTITY"));

            provision.put("ENTITY", "Theo.provision");
            provision.put("PARENT", map.get("ENTITY"));

            missing.put("ENTITY", "Missing");
            missing.put("PARENT", map.get("ENTITY"));

            totalStock.put("ENTITY", "Total Stock");
            totalStock.put("PARENT", map.get("ENTITY"));

            for (String w : weekList) {
                BigDecimal excessValue = Utils.parseBigDecimal(map.get("'" + w + "'_EXCESS"), null);
                BigDecimal provisionValue = Utils.parseBigDecimal(map.get("'" + w + "'_PROV"), null);
                BigDecimal missingValue = Utils.parseBigDecimal(map.get("'" + w + "'_MISSING"), null);
                BigDecimal specialValue = Utils.parseBigDecimal(map.get("'" + w + "'_SPECIAL"), null);
                BigDecimal totalValue = Utils.parseBigDecimal(map.get("'" + w + "'_TOTAL"), null);
                BigDecimal ratioValue = Utils.parseBigDecimal(map.get("'" + w + "'" + ratioColumn), null);

                if (excessValue == null && missingValue == null && totalValue == null && provisionValue == null) {
                    continue;
                }

                excess.put(w, excessValue);
                provision.put(w, provisionValue);
                missing.put(w, missingValue);
                ratio.put(w, ratioValue);
                totalStock.put(w, totalValue);

                BigDecimal val = Utils.parseBigDecimal(total.get(w));
                total.put(w, val.add(totalValue));

                val = Utils.parseBigDecimal(provisionTotal.get(w));
                provisionTotal.put(w, val.add(provisionValue));

                val = Utils.parseBigDecimal(excessTotal.get(w));
                excessTotal.put(w, val.add(excessValue));

                val = Utils.parseBigDecimal(missingTotal.get(w));
                missingTotal.put(w, val.add(missingValue));

                val = Utils.parseBigDecimal(specialTotal.get(w));
                specialTotal.put(w, val.add(specialValue));

                val = Utils.parseBigDecimal(totalStockTotal.get(w));
                totalStockTotal.put(w, val.add(totalValue));
            }

            resultList.add(ratio);
            resultList.add(totalStock);
            resultList.add(excess);
            resultList.add(provision);
            resultList.add(missing);
        }
        Map<String, Object> ratioTotal = new HashMap<>();
        ratioTotal.put("ENTITY", "Total");

        for (String w : weekList) {
            BigDecimal totalValue = Utils.parseBigDecimal(total.get(w), null);
            BigDecimal excessValue = Utils.parseBigDecimal(excessTotal.get(w), null);
            BigDecimal provisionValue = Utils.parseBigDecimal(provisionTotal.get(w), null);
            BigDecimal missingValue = Utils.parseBigDecimal(missingTotal.get(w), null);
            BigDecimal specialValue = Utils.parseBigDecimal(specialTotal.get(w), null);

            if (excessValue == null && missingValue == null && totalValue == null && provisionValue == null && specialValue == null) {
                continue;
            }

            if (totalValue == null || totalValue.compareTo(BigDecimal.ZERO) == 0) {
                ratioTotal.put(w, BigDecimal.ZERO);
            } else {
                excessValue = excessValue == null ? BigDecimal.ZERO : excessValue;
                missingValue = missingValue == null ? BigDecimal.ZERO : missingValue;
                provisionValue = provisionValue == null ? BigDecimal.ZERO : provisionValue;
                specialValue = specialValue == null ? BigDecimal.ZERO : specialValue;
                BigDecimal grossTotal = excessValue.add(missingValue).add(provisionValue);
                if ("Net".equalsIgnoreCase(type)) {
                    ratioTotal.put(w, grossTotal.subtract(specialValue).divide(totalValue, 4, RoundingMode.HALF_UP));
                } else if ("Gross".equalsIgnoreCase(type)) {
                    ratioTotal.put(w, grossTotal.divide(totalValue, 4, RoundingMode.HALF_UP));
                } else if ("Special".equalsIgnoreCase(type)) {
                    ratioTotal.put(w, specialValue.divide(totalValue, 4, RoundingMode.HALF_UP));
                }
            }
        }

        resultList.add(ratioTotal);
        resultList.add(totalStockTotal);
        resultList.add(excessTotal);
        resultList.add(provisionTotal);
        resultList.add(missingTotal);

        page.setData(resultList);
        page.setTotal(resultList.size());
        return response.setBody(page);
    }

    @Override
    public Response queryReport1Details(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);

        page.setTotal(inventoryUHSDao.queryReport1DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(inventoryUHSDao.queryReport1Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        this.generateCascaderFilter(parameterMap);
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "uhs_report_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.inventory.dao.IInventoryUHSDao.queryReport1Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);
        List<String> weeks = inventoryUHSDao.queryWeekByRange(parameterMap);
        Collections.reverse(weeks);
        parameterMap.put("weekList", weeks);
        List<Map<String, Object>> dataList = inventoryUHSDao.queryReport2(parameterMap);
        Map<String, Map<String, Object>> dataMap = new HashMap<>();

        for (Map<String, Object> map : dataList) {
            dataMap.put(String.valueOf(map.get("WEEK")), map);
        }

        UHSReport2 report2 = new UHSReport2();

        for (String w : weeks) {
            report2.addxAxis(w);
            Map<String, Object> value = dataMap.get(w);
            if (value != null) {
                BigDecimal stockValue = Utils.parseBigDecimal(value.get("STOCK_VALUE"), null);
                BigDecimal lastStockValue = Utils.parseBigDecimal(value.get("STOCK_VALUE_IN_LAST"), null);
                BigDecimal specialValue = Utils.parseBigDecimal(value.get("SPECIAL"), null);
                BigDecimal missingValue = Utils.parseBigDecimal(value.get("MISSING"), null);
                BigDecimal excessValue = Utils.parseBigDecimal(value.get("EXCESS"), null);
                BigDecimal provisionValue = Utils.parseBigDecimal(value.get("PROV"), null);

                if (stockValue == null && lastStockValue == null && specialValue == null && missingValue == null && excessValue == null && provisionValue == null) {
                    continue;
                }

                stockValue = stockValue == null ? BigDecimal.ZERO : stockValue;
                lastStockValue = lastStockValue == null ? BigDecimal.ZERO : lastStockValue;
                specialValue = specialValue == null ? BigDecimal.ZERO : specialValue;
                missingValue = missingValue == null ? BigDecimal.ZERO : missingValue;
                excessValue = excessValue == null ? BigDecimal.ZERO : excessValue;
                provisionValue = provisionValue == null ? BigDecimal.ZERO : provisionValue;

                BigDecimal netValue = excessValue.add(missingValue).add(provisionValue).subtract(specialValue);

                report2.addyAxis1(stockValue);
                report2.addyAxis2(netValue);
                report2.addyAxis3(specialValue);
                report2.addyAxis4(lastStockValue.compareTo(BigDecimal.ZERO) != 0 ? specialValue.multiply(BigDecimal.valueOf(100)).divide(lastStockValue, 2, RoundingMode.HALF_UP) : null);
                report2.addyAxis5(lastStockValue.compareTo(BigDecimal.ZERO) != 0 ? netValue.multiply(BigDecimal.valueOf(100)).divide(lastStockValue, 2, RoundingMode.HALF_UP) : null);
            } else {
                report2.addyAxis1(null);
                report2.addyAxis2(null);
                report2.addyAxis3(null);
                report2.addyAxis4(null);
                report2.addyAxis5(null);
            }
        }

        return response.setBody(report2);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateReport3Filter(parameterMap);
        this.generateCascaderFilter(parameterMap);
        page.setTotal(inventoryUHSDao.queryReport3Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(inventoryUHSDao.queryReport3(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport3(Map<String, Object> parameterMap, HttpServletResponse response) {
        this.generateCascaderFilter(parameterMap);
        this.generateReport3Filter(parameterMap);
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "uhs_report_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.inventory.dao.IInventoryUHSDao.queryReport3", parameterMap);
    }

    @Override
    public void downloadReport3RecommendRCA(Map<String, Object> parameterMap, HttpServletResponse response) {
        this.generateCascaderFilter(parameterMap);
        this.generateReport3Filter(parameterMap);
        String fileName = "uhs_recommend_rca_" + Utils.randomStr(4) + ".xlsx";

        Map<String, List<String>> validationMap = new HashMap<>();

        List<Map<String, String>> codeList = inventoryUHSDao.queryUHSCode();

        List<String> finalRCAList = new ArrayList<>();
        List<String> finalActionList = new ArrayList<>();
        List<String> actionStatusList = new ArrayList<>();
        List<String> freqRCAList = new ArrayList<>();

        for (Map<String, String> map : codeList) {
            String category = map.get("CATEGORY");
            switch (category) {
                case "RCA_ACTION" -> finalRCAList.add(map.get("CODE"));
                case "COD_ACTION_RCA" -> finalActionList.add(map.get("CODE"));
                case "COD_STATUS_RCA" -> actionStatusList.add(map.get("CODE"));
                case "COD_FREQ_RCA" -> freqRCAList.add(map.get("CODE"));
            }
        }

        validationMap.put("FINAL_RCA", finalRCAList);
        validationMap.put("FINAL_ACTION", finalActionList);
        validationMap.put("COD_STATUS_RCA", actionStatusList);
        validationMap.put("COD_FREQ_RCA", freqRCAList);

        excelTemplate.create(response, fileName, "com.scp.inventory.dao.IInventoryUHSDao.downloadReport3RecommendRCA", parameterMap, validationMap);
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response saveReport3(String userid, Map<String, Object> parameterMap) {
        scpTableHelper.setIncludeColumn(new ArrayList<>() {{
            add("FINAL_RCA");
            add("ACTION_RECOMMEND");
            add("COD_ACTION_RCA");
            add("FINAL_ACTION");
            add("HS_XSMIS_ACT");
            add("DATE_INI_BTN");
            add("DATE_FIN_BTN");
            add("COD_STATUS_RCA");
        }});
        String version = (String) parameterMap.get("week");
        scpTableHelper.setWarningMessage("You have no privileges to modify data does not belong to you");
        scpTableHelper.setScpTableUpdateHandler((pk, updates) -> {
                    String[] pks = pk.split(",");
                    String material = pks[0];
                    String plantCode = pks[1];
                    int count = inventoryUHSDao.queryReport3Exists(version, material, plantCode);
                    if (count == 0) {
                        return inventoryUHSDao.saveReport3Data(version, material, plantCode, updates, userid);
                    } else {
                        return inventoryUHSDao.updateReport3Data(version, material, plantCode, updates, userid);
                    }
                }
        );
        response.setBody(scpTableHelper.execCRUD(parameterMap));
        inventoryUHSDao.deleteEmptyReport3Result();
        return response;
    }

    @Override
    public void downloadReport3OneMM(Map<String, Object> parameterMap, HttpServletResponse response) {
        this.generateCascaderFilter(parameterMap);
        this.generateReport3Filter(parameterMap);
        String fileName = "uhs_for_onemm_" + Utils.randomStr(4) + ".csv";
        excelTemplate.createCSV(response, fileName, "com.scp.inventory.dao.IInventoryUHSDao.downloadReport3OneMM", parameterMap);
    }

    @Override
    public void downloadReport3ResultTemplate(Map<String, Object> parameterMap, HttpServletResponse response) {
        String fileName = "uhs_result_template.xlsx";
        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        resultList.add(map);
        map.put("VERSION", null);
        map.put("MATERIAL", null);
        map.put("PLANT_CODE", null);
        map.put("FINAL_RCA", null);
        map.put("ACTION_RECOMMEND", null);
        map.put("COD_ACTION_RCA", null);
        map.put("FINAL_ACTION", null);
        map.put("HS_XSMIS_ACT", null);
        map.put("DATE_INI_BTN", null);
        map.put("DATE_FIN_BTN", null);
        map.put("HS_XSMIS_ACT_CSV", null);
        map.put("COD_STATUS_RCA", null);
        map.put("COD_FREQ_RCA", null);

        excelTemplate.create(response, fileName, resultList);
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response uploadReport3Result(String userid, MultipartFile file) throws Exception {
        File tempFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), ".tmp");
        file.transferTo(tempFile);

        List<UHSReport3Result> data = new ArrayList<>();
        excelTemplate.read(tempFile, 1, new SimpleSheetContentsHandler() {
            @Override
            public void handleRow(int rowNum) {

                if (rowNum == 0) {
                    return;
                }

                UHSReport3Result bean = new UHSReport3Result();
                bean.setVERSION(row.get(0));
                bean.setMATERIAL(row.get(1));
                bean.setPLANT_CODE(row.get(2));
                bean.setFINAL_RCA(row.get(3));
                bean.setACTION_RECOMMEND(row.get(4));
                bean.setCOD_ACTION_RCA(row.get(5)); // 可以不用
                bean.setFINAL_ACTION(row.get(6));
                bean.setHS_XSMIS_ACT(row.get(7));
                bean.setDATE_INI_BTN(row.get(8));
                bean.setDATE_FIN_BTN(row.get(9));
                bean.setHS_XSMIS_ACT_CSV(row.get(10));
                bean.setCOD_STATUS_RCA(row.get(11));
                bean.setCOD_FREQ_RCA(row.get(12));
                bean.setCREATE_BY(userid);

                data.add(bean);
            }
        }, new StylesTable());

        // check valid
        int invalid = 0;
        List<String> invalidMsg = new ArrayList<>();
        for (UHSReport3Result report : data) {
            if (report.isValid() == false) {
                invalid++;
                if (invalidMsg.size() < 32) {
                    invalidMsg.add("Line" + invalid + ": " + report.getInvalidMsg());
                }
            }
        }

        if (invalid > 0) {
            String errorMsg = "Upload failed, " + invalid + (invalid > 1 ? " rows " : " row ") + " invalid. <hr style='margin:5px 0;border-bottom:0'>";
            errorMsg += "<div style='font-size:11px !important'>";
            errorMsg += StringUtils.join(invalidMsg, "<br>");
            if (invalidMsg.size() != invalid) {
                errorMsg += "...";
            }
            errorMsg += "</div>";
            return response.set(Status.FORBIDDEN, errorMsg);
        }

        List<List<UHSReport3Result>> insertList = Utils.subList(data, 128);
        for (List<UHSReport3Result> list : insertList) {
            if (list.isEmpty() == false) {
                inventoryUHSDao.mergeReport3Data(list);
            }
        }

        if (tempFile.delete() == false) {
            System.err.println(tempFile.getAbsolutePath() + " delete failed");
        }

        inventoryUHSDao.deleteEmptyReport3Result();
        return response;
    }

    @SuppressWarnings("unchecked")
    private void generateReport3Filter(Map<String, Object> parameterMap) {
        List<String> valueColumn = new ArrayList<>();

        List<String> list = (List<String>) parameterMap.get("HSStatus");
        if (list != null && list.isEmpty() == false) {
            for (String status : list) {
                if ("EXCESS".equalsIgnoreCase(status)) {
                    valueColumn.add("CASE WHEN T.HS_RCA LIKE 'E98%' THEN 0 ELSE NVL(T.HS_EXCESS_VALUE, 0) END");
                }
                if ("EXCESS_SPECIAL".equalsIgnoreCase(status)) {
                    valueColumn.add("CASE WHEN T.HS_RCA LIKE 'E98%' THEN NVL(T.HS_EXCESS_VALUE, 0) ELSE 0 END");
                } else if ("PROV".equalsIgnoreCase(status)) {
                    valueColumn.add("T.HS_THEO_PROV_VALUE");
                } else if ("MISSING".equalsIgnoreCase(status)) {
                    valueColumn.add("T.HS_MISSING_VALUE");
                }
            }
        } else {
            valueColumn.add("T.HS_EXCESS_VALUE");
        }

        parameterMap.put("SUM_COLUMN", StringUtils.join(valueColumn, "+"));
    }

    private void generateCascaderFilter(Map<String, Object> parameterMap) {
        // 生成筛选条件
        JSONObject scpFilter = (JSONObject) parameterMap.get("$scpFilter");
        if (scpFilter != null && scpFilter.getJSONArray("cascader") != null && scpFilter.getJSONArray("cascader").isEmpty() == false) {
            JSONArray categoryArray = scpFilter.getJSONArray("cascader");
            if (categoryArray != null) {
                Map<String, List<String>> filterMap = new HashMap<>();

                for (Object subObj : categoryArray) {
                    JSONArray subArray = (JSONArray) subObj;
                    String columnName = subArray.getString(0);

                    String value = subArray.getString(1);
                    String key = Utils.randomStr(8);

                    if (Utils.hasInjectionAttack(columnName)) {
                        continue;
                    }

                    List<String> fl = filterMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                    fl.add("#{" + key + ",jdbcType=VARCHAR}");
                    parameterMap.put(key, value);
                }

                List<String> filterList = new ArrayList<>();

                for (String key : filterMap.keySet()) {
                    List<String> fl = filterMap.get(key);
                    if ("MATERIAL_OWNER_NAME".equalsIgnoreCase(key) || "MATERIAL_OWNER_SESA".equalsIgnoreCase(key)) {
                        String alise = "t_" + Utils.randomStr(6);
                        filterList.add("EXISTS ( SELECT 1 FROM MATERIAL_MASTER_V " + alise +
                                " WHERE " + alise + "." + key + " in (" + StringUtils.join(fl, ", ") + ") AND T.MATERIAL = " + alise + ".MATERIAL AND T.PLANT_CODE = " + alise + ".PLANT_CODE)");
                    } else {
                        filterList.add("t." + key + " in (" + StringUtils.join(fl, ",") + ")");
                    }
                }

                parameterMap.put("filters", StringUtils.join(filterList, " and "));
            }
        }
    }
}
