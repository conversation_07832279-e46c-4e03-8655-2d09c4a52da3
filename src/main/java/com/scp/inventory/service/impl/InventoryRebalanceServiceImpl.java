package com.scp.inventory.service.impl;

import com.adm.system.bean.CascaderBean;
import com.scp.inventory.bean.RebalanceBean;
import com.scp.inventory.bean.RebalanceTransferBean;
import com.scp.inventory.bean.RebalanceTransferLogs;
import com.scp.inventory.bean.RebalanceTransferResult;
import com.scp.inventory.dao.IInventoryRebalanceDao;
import com.scp.inventory.service.IInventoryRebalanceService;
import com.scp.inventory.utils.RebalanceCalcUtil;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

@Service("inventoryRebalanceService")
@Scope("prototype")
@Transactional
public class InventoryRebalanceServiceImpl extends ServiceHelper implements IInventoryRebalanceService {

    @Resource
    private IInventoryRebalanceDao inventoryRebalanceDao;

    @Resource
    private Response response;

    @Resource
    private ExcelTemplate excelTemplate;

    @Override
    public Response calcRebalance() {
        try {
            List<RebalanceBean> dataList = inventoryRebalanceDao.queryRebalanceList();
            inventoryRebalanceDao.truncateTransferTable();
            inventoryRebalanceDao.truncateTransferLogsTable();
            for (int target = 20; target <= 120; target += 5) {
                List<RebalanceTransferBean> resultList = new ArrayList<>();
                List<RebalanceTransferLogs> logList = new ArrayList<>();
                for (RebalanceBean rebalance : dataList) {
                    RebalanceTransferResult result = RebalanceCalcUtil.calc(rebalance, BigDecimal.valueOf(target / 10.0));

                    resultList.addAll(result.getTransfer());
                    logList.add(result.getLogs());

                    if (resultList.size() > 200) {
                        inventoryRebalanceDao.saveRebalanceResult(resultList);
                        inventoryRebalanceDao.saveRebalanceLogs(logList);
                        resultList.clear();
                        logList.clear();
                    }
                }

                if (resultList.size() > 0) {
                    inventoryRebalanceDao.saveRebalanceResult(resultList);
                    inventoryRebalanceDao.saveRebalanceLogs(logList);
                    resultList.clear();
                    logList.clear();
                }
            }
            return response;
        } catch (Exception e) {
            e.printStackTrace();
            return response.setError(e);
        }
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage() {
        Map<String, Object> resultMap = new HashMap<>();
        List<CascaderBean> resultList = new ArrayList<>();
        List<Map<String, String>> dataList = inventoryRebalanceDao.queryRebalanceCascader();

        Map<String, List<Map<String, String>>> tempMap = new LinkedHashMap<>();

        for (Map<String, String> map : dataList) {
            List<Map<String, String>> list = tempMap.computeIfAbsent(map.get("CATEGORY"), key -> new ArrayList<>());
            list.add(map);
        }

        for (String key : tempMap.keySet()) {
            CascaderBean bean = new CascaderBean();
            resultList.add(bean);
            bean.setLabel(key);
            bean.setValue(key);
            for (Map<String, String> map : tempMap.get(key)) {
                CascaderBean subBean = new CascaderBean();
                subBean.setLabel(map.get("NAME"));
                subBean.setValue(subBean.getLabel());
                bean.addChild(subBean);
            }
        }

        resultMap.put("cascader", resultList);
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        return response.setBody(inventoryRebalanceDao.queryReport1(parameterMap));
    }

    @Override
    public Response queryReport1Details(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateCascaderFilter(parameterMap);

        String type = (String) parameterMap.get("type");
        String dc = (String) parameterMap.get("report1SelectedDC");
        if (StringUtils.isNotBlank(dc)) {
            if ("Quantity".equalsIgnoreCase(type)) {
                parameterMap.put("valueColumn1", "(t." + dc + "_UU_STOCK is not null or t." + dc + "_UU_STOCK > 0)");
                parameterMap.put("valueColumn2", "(t." + dc + "_UU_STOCK is not null or t." + dc + "_UU_STOCK > 0)");
            } else if ("Moving Average Price".equalsIgnoreCase(type)) {
                parameterMap.put("valueColumn1", "(t." + dc + "_UU_STOCK_VALUE is not null or t." + dc + "_UU_STOCK_VALUE > 0)");
                parameterMap.put("valueColumn2", "(t." + dc + "_UU_STOCK_VALUE is not null or t." + dc + "_UU_STOCK_VALUE > 0)");
            } else if ("Item".equalsIgnoreCase(type)) {
                parameterMap.put("valueColumn1", "1 = 0");
                parameterMap.put("valueColumn2", "TRANSFER_FROM = '" + dc + "'");
            }
        } else {
            if ("Item".equalsIgnoreCase(type)) {
                parameterMap.put("valueColumn1", "1 = 0");
                parameterMap.put("valueColumn2", "TRANSFER_FROM in ('O001','N001','M001','I001','I003')");
            }
        }

        page.setTotal(inventoryRebalanceDao.queryReport1DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(inventoryRebalanceDao.queryReport1Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateCascaderFilter(parameterMap);

        String type = (String) parameterMap.get("type");
        String dc = (String) parameterMap.get("report1SelectedDC");
        if (StringUtils.isNotBlank(dc)) {
            if ("Quantity".equalsIgnoreCase(type)) {
                parameterMap.put("valueColumn1", "(t." + dc + "_UU_STOCK is not null or t." + dc + "_UU_STOCK > 0)");
                parameterMap.put("valueColumn2", "(t." + dc + "_UU_STOCK is not null or t." + dc + "_UU_STOCK > 0)");
            } else if ("Moving Average Price".equalsIgnoreCase(type)) {
                parameterMap.put("valueColumn1", "(t." + dc + "_UU_STOCK_VALUE is not null or t." + dc + "_UU_STOCK_VALUE > 0)");
                parameterMap.put("valueColumn2", "(t." + dc + "_UU_STOCK_VALUE is not null or t." + dc + "_UU_STOCK_VALUE > 0)");
            } else if ("Item".equalsIgnoreCase(type)) {
                parameterMap.put("valueColumn2", "TRANSFER_FROM = '" + dc + "'");
            }
        } else {
            if ("Item".equalsIgnoreCase(type)) {
                parameterMap.put("valueColumn1", "1 = 0");
                parameterMap.put("valueColumn2", "TRANSFER_FROM in ('O001','N001','M001','I001','I003')");
            }
        }

        String fileName = "stock_rebalance_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.inventory.dao.IInventoryRebalanceDao.queryReport1Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateCascaderFilter(parameterMap);
        if (StringUtils.isBlank(page.getSort())) {
            page.setSort("TRANSFER_VALUE DESC");
        }

        List<LinkedHashMap<String, Object>> data = inventoryRebalanceDao.queryReport2(parameterMap);

        LinkedHashMap<String, Object> totalMap = new LinkedHashMap<>();

        totalMap.put((String) parameterMap.get("report2Type"), "Total");
        String[] columns = new String[]{"TRANSFER_QTY", "TRANSFER_VALUE", "ITEM"};
        for (String c : columns) {
            BigDecimal total = BigDecimal.ZERO;
            for (LinkedHashMap<String, Object> map : data) {
                total = total.add(Utils.parseBigDecimal(map.get(c)));
            }
            totalMap.put(c, total);
        }

        data.add(totalMap);
        page.setData(data);
        page.setTotal(page.getData().size());
        return response.setBody(page);
    }

    @Override
    public void downloadReport2(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateCascaderFilter(parameterMap);
        if (StringUtils.isBlank(page.getSort())) {
            page.setSort("TRANSFER_VALUE DESC");
        }

        String fileName = "stock_rebalance_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.inventory.dao.IInventoryRebalanceDao.queryReport2", parameterMap);
    }

    @Override
    public Response queryReport2Details(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateCascaderFilter(parameterMap);
        page.setTotal(inventoryRebalanceDao.queryReport2DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(inventoryRebalanceDao.queryReport2Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport2Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateCascaderFilter(parameterMap);
        String fileName = "stock_rebalance_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.inventory.dao.IInventoryRebalanceDao.queryReport2Details", parameterMap);
    }

    @Override
    public Response queryReport2DetailsLogs(Map<String, Object> parameterMap) {
        return response.setBody(Utils.clob2String(inventoryRebalanceDao.queryReport2DetailsLogs(parameterMap)));
    }

    /**
     * 生成cascader filter
     *
     * @param parameterMap 参数map
     */
    private void generateCascaderFilter(Map<String, Object> parameterMap) {
        this.generateCascaderFilterSQL(parameterMap);

        // convert target
        Object targetObj = parameterMap.get("target");
        BigDecimal target = new BigDecimal(String.valueOf(targetObj)).setScale(1, RoundingMode.HALF_UP);
        parameterMap.put("target", target.toPlainString());
    }

    private void generateValueColumn(Map<String, Object> parameterMap) {
        String type = (String) parameterMap.get("type");
        List<String> valueList1 = new ArrayList<>();
        List<String> valueList2 = new ArrayList<>();
        if ("Quantity".equalsIgnoreCase(type)) {
            valueList1.add("t.I001_UU_STOCK I001");
            valueList1.add("t.I003_UU_STOCK I003");
            valueList1.add("t.N001_UU_STOCK N001");
            valueList1.add("t.M001_UU_STOCK M001");
            valueList1.add("t.O001_UU_STOCK O001");

            valueList2.add("decode(t0.TRANSFER_FROM, 'I001', t.I001_UU_STOCK, 0) I001");
            valueList2.add("decode(t0.TRANSFER_FROM, 'I003', t.I003_UU_STOCK, 0) I003");
            valueList2.add("decode(t0.TRANSFER_FROM, 'N001', t.N001_UU_STOCK, 0) N001");
            valueList2.add("decode(t0.TRANSFER_FROM, 'M001', t.M001_UU_STOCK, 0) M001");
            valueList2.add("decode(t0.TRANSFER_FROM, 'O001', t.O001_UU_STOCK, 0) O001");
        } else if ("Moving Average Price".equalsIgnoreCase(type)) {
            valueList1.add("t.I001_UU_STOCK_VALUE I001");
            valueList1.add("t.I003_UU_STOCK_VALUE I003");
            valueList1.add("t.N001_UU_STOCK_VALUE N001");
            valueList1.add("t.M001_UU_STOCK_VALUE M001");
            valueList1.add("t.O001_UU_STOCK_VALUE O001");

            valueList2.add("decode(t0.TRANSFER_FROM, 'I001', t.I001_UU_STOCK_VALUE, 0) I001");
            valueList2.add("decode(t0.TRANSFER_FROM, 'I003', t.I003_UU_STOCK_VALUE, 0) I003");
            valueList2.add("decode(t0.TRANSFER_FROM, 'N001', t.N001_UU_STOCK_VALUE, 0) N001");
            valueList2.add("decode(t0.TRANSFER_FROM, 'M001', t.M001_UU_STOCK_VALUE, 0) M001");
            valueList2.add("decode(t0.TRANSFER_FROM, 'O001', t.O001_UU_STOCK_VALUE, 0) O001");
        } else if ("Item".equalsIgnoreCase(type)) {
            valueList1.add("null I001");
            valueList1.add("null I003");
            valueList1.add("null N001");
            valueList1.add("null M001");
            valueList1.add("null O001");

            valueList2.add("decode(t0.TRANSFER_FROM, 'I001', 1, 0) I001");
            valueList2.add("decode(t0.TRANSFER_FROM, 'I003', 1, 0) I003");
            valueList2.add("decode(t0.TRANSFER_FROM, 'N001', 1, 0) N001");
            valueList2.add("decode(t0.TRANSFER_FROM, 'M001', 1, 0) M001");
            valueList2.add("decode(t0.TRANSFER_FROM, 'O001', 1, 0) O001");
        }

        parameterMap.put("valueColumn1", StringUtils.join(valueList1, ","));
        parameterMap.put("valueColumn2", StringUtils.join(valueList2, ","));
    }
}
