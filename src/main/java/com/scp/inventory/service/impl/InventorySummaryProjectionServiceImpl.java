package com.scp.inventory.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import com.starter.context.bean.*;
import com.starter.context.bean.scptable.ScpTableHelper;
import com.scp.inventory.InventorySummaryProjectionController;
import com.scp.inventory.dao.IInventoryStructureDao;
import com.scp.inventory.dao.InventorySummaryProjectionDao;
import com.scp.inventory.service.IInventorySummaryProjectionService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service("inventorySummaryProjection")
@Scope("prototype")
@Transactional
public class InventorySummaryProjectionServiceImpl implements IInventorySummaryProjectionService {

    @Resource
    private InventorySummaryProjectionDao inventorySummaryProjectionDao;

    @Resource
    private Response response;

    @Resource
    private ExcelTemplate excelTemplate;

    @Resource
    private ScpTableHelper scpTableHelper;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cascader", Utils.parseCascader(inventorySummaryProjectionDao.queryCascader()));
        resultMap.put("projectionCascader", Utils.parseCascader(inventorySummaryProjectionDao.queryProjectionCascader()));
        resultMap.put("rca", inventorySummaryProjectionDao.queryRcaCode());
        List<Map<String, Object>> rcaList = inventorySummaryProjectionDao.queryInventoryRCAList();
        Map<String, Object> rcaMap = new HashMap<>();
        for (Map<String, Object> map : rcaList) {
            rcaMap.put((String) map.get("KEY"), map.get("VALUE"));
        }
        resultMap.put("rcaMap", rcaMap);
        return response.setBody(resultMap);
    }

    @Override
    public Response initPersonalSettings(String userid) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("entity", this.queryAvailableOpts(userid, "ENTITY", true));
        resultMap.put("plantCode", this.queryAvailableOpts(userid, "PLANT_CODE", true));
        resultMap.put("plantType", this.queryAvailableOpts(userid, "PLANT_TYPE", false));
        resultMap.put("productLineInv", this.queryAvailableOpts(userid, "PRODUCT_LINE_INV", false));
        resultMap.put("productFamilyInv", this.queryAvailableOpts(userid, "PRODUCT_FAMILY_INV", false));
        resultMap.put("sourceCategory", this.queryAvailableOpts(userid, "SOURCE_CATEGORY", false));
        resultMap.put("vendorName", this.queryAvailableOpts(userid, "VENDOR_NAME", false));
        resultMap.put("isAdmin", inventorySummaryProjectionDao.queryPageAdmin(userid, InventorySummaryProjectionController.PARENT_CODE) > 0);
        resultMap.put("isMaterialOwner", inventorySummaryProjectionDao.queryMaterialOwnerCount(userid) > 0);

        resultMap.put("projectionVersionOpts", inventorySummaryProjectionDao.queryProjectionVersionOpts());
        return response.setBody(resultMap);
    }

    private List<String> queryAvailableOpts(String userid, String column, boolean required) {
        List<String> result = inventorySummaryProjectionDao.queryAvailableOpts(userid, column);
        if (required == false) {
            result.add(0, "");
        }
        return result;
    }

    @Override
    @Cacheable(value = Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(inventorySummaryProjectionDao.queryReport1Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(inventorySummaryProjectionDao.queryReport1(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "inv_summary_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.inventory.dao.InventorySummaryProjectionDao.queryReport1", parameterMap);
    }

    @Override
    public Response queryReport1Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);

        page.setTotal(inventorySummaryProjectionDao.queryReport1DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(inventorySummaryProjectionDao.queryReport1Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "inv_summary_details_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.inventory.dao.InventorySummaryProjectionDao.queryReport1Details", parameterMap);
    }

    @Override
    @Cacheable(value = Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2(Map<String, Object> parameterMap, String userid) {
        this.generateProjectionFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        parameterMap.put("isAdmin", inventorySummaryProjectionDao.queryPageAdmin(userid, InventorySummaryProjectionController.PARENT_CODE) > 0);
        page.setTotal(inventorySummaryProjectionDao.queryReport2Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(inventorySummaryProjectionDao.queryReport2(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    @SuppressWarnings("unchecked")
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response saveReport2(Map<String, Object> parameterMap, String userid) {
        String currentVersion = new SimpleDateFormat("yyyyMM").format(new Date());
        String version = (String) parameterMap.get("version");
        if (version != null && currentVersion.equals(version) == false) {
            Message message = new Message();
            message.setShowClose(true);
            message.setMessage("You cannot modify historical data in " + version);
            message.setType("error");
            return response.setBody(message);
        }

        // 判断是否上传了不属于自己的料号
        List<String> materialList = new ArrayList<>();
        List<Map<String, Object>> createList = Utils.clearList(parameterMap.get("createdRows"));
        Map<String, Object> updateMap = (Map<String, Object>) parameterMap.get("updatedRows");

        for (String ukey : updateMap.keySet()) {
            Map<String, Object> value = (Map<String, Object>) updateMap.get(ukey);
            if (value != null && value.isEmpty() == false && value.containsKey("MATERIAL")) {
                materialList.add((String) value.get("MATERIAL"));
            }
        }

        for (Map<String, Object> map : createList) {
            String material = (String) map.get("MATERIAL");
            if (StringUtils.isNotBlank(material)) {
                materialList.add(material);
            }
        }

        if (materialList.isEmpty() == false) {
            List<List<String>> lists = Utils.subList(materialList, 100);
            for (List<String> list : lists) {
                inventorySummaryProjectionDao.insertTempMaterial(list);
            }

            List<String> invalidMaterialList = inventorySummaryProjectionDao.queryInvalidMaterialList(userid);

            if (invalidMaterialList != null && invalidMaterialList.isEmpty() == false) {
                Message message = new Message();
                String msg = "You have no privileges to modify material:<br>";
                msg += StringUtils.join(invalidMaterialList, "<br>");
                message.setMessage(msg);
                message.setType(Message.TYPE_ERROR);
                message.setDangerouslyUseHTMLString(true);
                return response.setBody(message);
            }
        }

        scpTableHelper.setExcludeColumn(new ArrayList<>() {{
            add("CREATE_BY");
            add("STATUS");
            add("SPLITED");
            add("SPLITED_TIME");
        }});
        scpTableHelper.setWarningMessage("You have no privileges to modify data does not belong to you");

        scpTableHelper.setScpTableInsertHandler((headers, creates) ->
                inventorySummaryProjectionDao.createReport2ByTable(headers, creates, userid, currentVersion)
        );
        scpTableHelper.setScpTableDeleteHandler(deletes ->
                inventorySummaryProjectionDao.deleteReport2ByTable(deletes, userid, currentVersion)
        );
        scpTableHelper.setScpTableUpdateHandler((pk, updates) ->
                inventorySummaryProjectionDao.updateReport2ByTable(pk, updates, userid, currentVersion)
        );
        scpTableHelper.setWarningMessage("You cannot modify the data doesn't belong to you");
        Message message = scpTableHelper.execCRUD(parameterMap);
        inventorySummaryProjectionDao.deleteUnusedProjection();
        return response.setBody(message);
    }

    @Override
    public void downloadReport2(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "inv_projection_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.inventory.dao.InventorySummaryProjectionDao.downloadReport2", parameterMap);
    }

    @Override
    public Response queryReport2Details(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);

        page.setTotal(inventorySummaryProjectionDao.queryReport2DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(inventorySummaryProjectionDao.queryReport2Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport2Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "inv_projection_details_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.inventory.dao.InventorySummaryProjectionDao.queryReport2Details", parameterMap);
    }

    @Override
    @Cacheable(value = Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3(Map<String, Object> parameterMap, String userid) {
        this.generateProjectionResultFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(inventorySummaryProjectionDao.queryReport3Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(inventorySummaryProjectionDao.queryReport3(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport3(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateProjectionResultFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "inv_projection_result_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.inventory.dao.InventorySummaryProjectionDao.queryReport3", parameterMap);
    }

    @Override
    public Response splitInventoryProjection() {
        // 取SPLITED='N'的所有清单
        inventorySummaryProjectionDao.deleteUnusedProjection();
        List<Map<String, Object>> projectionList = inventorySummaryProjectionDao.queryProjectionUnSplitList();
        // 遍历查找符合projection的库存数据
        for (Map<String, Object> map : projectionList) {
            // 获取scope下, 库存总数
            BigDecimal total = inventorySummaryProjectionDao.queryTotalByConditions(map);
            if (total == null || total.compareTo(BigDecimal.ZERO) == 0) {
                // 如果没找到对应的物料, 则直接更新状态
                inventorySummaryProjectionDao.updateProjectionStatus(map);
                continue;
            }
            String rcaCategory = (String) map.get("RCA_CATEGORY");
            BigDecimal projectionValue = BigDecimal.ZERO;
            // 根据总数计算MONTH01到MONTH13的比例
            for (int i = 1; i <= 13; i++) {
                String key = "MONTH" + ((i < 10) ? "0" + i : "" + i);
                projectionValue = projectionValue.add(Utils.parseBigDecimal(map.get(key), 12));

                BigDecimal discount = BigDecimal.ONE;
                // Positive代表库存是下降的
                if (StringUtils.equals(rcaCategory, "Positive")) {
                    // 计算每一行的折扣比例
                    discount = total.subtract(projectionValue).divide(total, 12, RoundingMode.HALF_UP);
                } else if (StringUtils.equals(rcaCategory, "Negative")) {
                    // Negative代表库存是增加的
                    discount = total.add(projectionValue).divide(total, 12, RoundingMode.HALF_UP);
                }

                // 下降的库存比例不可能小于0, 如果discount小于0, 设置为0
                if (discount.compareTo(BigDecimal.ZERO) < 0) {
                    discount = BigDecimal.ZERO;
                }

                map.put(key + "_DISCOUNT", discount);
            }
            // 根据discount将数据库中的数据插入新表
            inventorySummaryProjectionDao.splitInventoryProjection(map);
            // 更新projection的SPLITED='Y'
            inventorySummaryProjectionDao.updateProjectionStatus(map);
        }
        return response;
    }

    private void generateValueColumn(Map<String, Object> parameterMap) {
        String resultType = (String) parameterMap.get("resultType");
        List<String> stockTypes = Utils.object2StrList(parameterMap.get("stock"));
        List<String> columns = stockTypes.stream().map(this::getColumnNameByLabel).collect(Collectors.toList());
        if (columns.isEmpty()) {
            columns.add("UU_STOCK");
        }
        String sohValueColumn = "";
        String suffix = "";
        String gitColumn = "";
        if ("Quantity".equalsIgnoreCase(resultType)) {
            sohValueColumn = " nvl( " + StringUtils.join(columns, ",0) + nvl( ") + ",0)";
            suffix = "";
            gitColumn = "GIT_QTY";
        } else if ("Moving Average Price".equalsIgnoreCase(resultType)) {
            String sql = "";
            List<String> valueColumns = new ArrayList<>();
            for (String c : columns) {
                valueColumns.add(c + "_VALUE");
            }
            sohValueColumn = " nvl( " + StringUtils.join(valueColumns, ",0) + nvl( ") + ",0)";
            sohValueColumn += sql;
            suffix = "_VALUE";
            gitColumn = "GIT_VALUE";
        } else if ("Net Net Price".equalsIgnoreCase(resultType)) {
            sohValueColumn = "( nvl(" + StringUtils.join(columns, ",0) * AVG_SELLING_PRICE_RMB + nvl( ") + ",0))";
            suffix = " * AVG_SELLING_PRICE_RMB";
            gitColumn = "GIT_QTY * AVG_SELLING_PRICE_RMB";
        }

        parameterMap.put("sohValueColumn", sohValueColumn.trim());
        parameterMap.put("suffix", suffix.trim());
        parameterMap.put("gitColumn", gitColumn.trim());
    }

    private void generateFilter(Map<String, Object> parameterMap) {
        // default category
        JSONArray categroyArray = (JSONArray) parameterMap.get("categroy");
        if (categroyArray == null || categroyArray.isEmpty()) {
            List<String> categroyDefault = new ArrayList<>();
            categroyDefault.add("ENTITY");
            categroyDefault.add("PLANT_CODE");
            categroyDefault.add("PLANT_TYPE");
            categroyDefault.add("MATERIAL_OWNER_NAME");
            categroyDefault.add("PRODUCT_LINE_INV");
            categroyDefault.add("PRODUCT_FAMILY_INV");
            categroyDefault.add("SOURCE_CATEGORY");
            categroyDefault.add("VENDOR_NAME");
            parameterMap.put("categroy", categroyDefault);
        }

        // 生成筛选条件
        Object categoryObj = parameterMap.get("filterList");
        List<String> stockIndicator = new ArrayList<>();
        JSONArray categoryArray = (JSONArray) categoryObj;
        if (categoryArray != null) {
            Map<String, List<String>> filterMap = new HashMap<>();

            for (Object subObj : categoryArray) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = subArray.getString(0);

                String value = subArray.getString(1);
                String key = Utils.randomStr(8);
                if (Utils.hasInjectionAttack(columnName)) {
                    continue;
                }
                if ("SPECIAL_ST".equalsIgnoreCase(columnName)) {
                    stockIndicator.add(value);
                    continue;
                }

                List<String> fl = filterMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                fl.add("#{" + key + ",jdbcType=VARCHAR}");
                parameterMap.put(key, value);
            }

            List<String> filterList = new ArrayList<>();

            for (String key : filterMap.keySet()) {
                List<String> fl = filterMap.get(key);
                filterList.add(key + " in (" + StringUtils.join(fl, ",") + ")");
            }

            parameterMap.put("filters", StringUtils.join(filterList, " and "));
        }

        // load special parameter
        String specialContent = (String) parameterMap.get("specialContent");
        if (StringUtils.isNotBlank(specialContent)) {
            parameterMap.put("specialList", Utils.splitValue(specialContent));
            parameterMap.put("specialColumn", this.getColumnNameByLabel(parameterMap.get("specialType")));
        }

        // stock indicator
        if (stockIndicator.isEmpty() == false) {
            String sql = "";
            String sql2 = "";
            if (stockIndicator.contains("#")) {
                stockIndicator.remove("#");
                sql2 = "SPECIAL_ST IS NULL";
            }
            if (stockIndicator.isEmpty() == false) {
                List<String> indicators = new ArrayList<>();

                for (String value : stockIndicator) {
                    String key = Utils.randomStr(8);
                    indicators.add("#{" + key + ",jdbcType=VARCHAR}");
                    parameterMap.put(key, value);
                }


                sql = "SPECIAL_ST in (" + StringUtils.join(indicators, ",") + ")";
            }

            parameterMap.put("stockIndicatorSQL", "(" + sql + (StringUtils.isNotBlank(sql) && StringUtils.isNotBlank(sql2) ? " or " : "") + sql2 + " or SPECIAL_ST = 'Others')");
        }
    }

    private void generateProjectionFilter(Map<String, Object> parameterMap) {
        // 生成筛选条件
        Object categoryObj = parameterMap.get("projectionFilterList");
        JSONArray categoryArray = (JSONArray) categoryObj;
        if (categoryArray != null) {
            Map<String, List<String>> filterMap = new HashMap<>();

            for (Object subObj : categoryArray) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = subArray.getString(0);

                String value = subArray.getString(1);
                String key = Utils.randomStr(8);
                if (Utils.hasInjectionAttack(columnName)) {
                    continue;
                }

                List<String> fl = filterMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                fl.add("#{" + key + ",jdbcType=VARCHAR}");
                parameterMap.put(key, value);
            }

            List<String> filterList = new ArrayList<>();

            for (String key : filterMap.keySet()) {
                List<String> fl = filterMap.get(key);
                filterList.add("nvl(" + key + ", 'Others') in (" + StringUtils.join(fl, ",") + ")");
            }

            parameterMap.put("projectionFilters", StringUtils.join(filterList, " and "));
        }

        // projection material
        String projectionMaterial = (String) parameterMap.get("projectionMaterial");
        if (StringUtils.isNotBlank(projectionMaterial)) {
            parameterMap.put("projectionMaterialList", Utils.splitValue(projectionMaterial));
        }
    }

    private void generateProjectionResultFilter(Map<String, Object> parameterMap) {
        String version = (String) parameterMap.get("projectionResultVersion");
        if (StringUtils.isBlank(version) || version.length() != 6 || Utils.isStrictNumeric(version) == false) {
            version = new SimpleDateFormat("yyyyMM").format(new Date());
        }

        String startVersion = (Utils.parseInt(version.substring(0, 4)) - 1) + "01";
        List<String> versions = new ArrayList<>();

        int maxLoop = 24;
        while (version.compareTo(startVersion) >= 0 && maxLoop > 0) {
            versions.add(startVersion);
            startVersion = Utils.addMonth(startVersion);
            maxLoop--;
        }
        parameterMap.put("versions", versions);

        // default category
        JSONArray categroyArray = (JSONArray) parameterMap.get("projectionResultCategroy");
        if (categroyArray == null || categroyArray.isEmpty()) {
            List<String> categroyDefault = new ArrayList<>();
            categroyDefault.add("ENTITY");
            categroyDefault.add("PLANT_CODE");
            categroyDefault.add("PLANT_TYPE");
            categroyDefault.add("MATERIAL_OWNER_NAME");
            categroyDefault.add("PRODUCT_LINE_INV");
            categroyDefault.add("PRODUCT_FAMILY_INV");
            categroyDefault.add("SOURCE_CATEGORY");
            categroyDefault.add("VENDOR_NAME");
            parameterMap.put("projectionResultCategroy", categroyDefault);
        }

        // 生成筛选条件
        Object categoryObj = parameterMap.get("projectionResultFilterList");
        JSONArray categoryArray = (JSONArray) categoryObj;
        if (categoryArray != null) {
            Map<String, List<String>> filterMap = new HashMap<>();

            for (Object subObj : categoryArray) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = subArray.getString(0);

                String value = subArray.getString(1);
                String key = Utils.randomStr(8);
                if (Utils.hasInjectionAttack(columnName)) {
                    continue;
                }

                List<String> fl = filterMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                fl.add("#{" + key + ",jdbcType=VARCHAR}");
                parameterMap.put(key, value);
            }

            List<String> filterList = new ArrayList<>();
            List<String> mmvColumns = new ArrayList<>() {{
                add("MATERIAL_OWNER_SESA");
                add("MATERIAL_OWNER_NAME");
            }};

            for (String key : filterMap.keySet()) {
                List<String> fl = filterMap.get(key);
                if (mmvColumns.contains(key)) {
                    filterList.add("nvl(t2." + key + ", 'Others') in (" + StringUtils.join(fl, ",") + ")");
                } else {
                    filterList.add("nvl(t." + key + ", 'Others') in (" + StringUtils.join(fl, ",") + ")");
                }
            }

            parameterMap.put("projectionResultFilters", StringUtils.join(filterList, " and "));
        }

        // load special parameter
        String specialContent = (String) parameterMap.get("projectionResultSpecialContent");
        if (StringUtils.isNotBlank(specialContent)) {
            parameterMap.put("projectionResultSpecialList", Utils.splitValue(specialContent));
            parameterMap.put("projectionResultSpecialColumn", this.getColumnNameByLabel(parameterMap.get("projectionResultSpecialType")));
        }
    }

    private String getColumnNameByLabel(Object labelObj) {
        String label = (String) labelObj;
        if (label == null) {
            return null;
        }
        if (Utils.hasInjectionAttack(label) == false) {
            return label;
        } else {
            return "";
        }
    }

    private String getLastDayOfMonth(String version) {
        int year = Utils.parseInt(version.substring(0, 4));
        int month = Utils.parseInt(version.substring(4, 6));
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        return new SimpleDateFormat("yyyy/MM/dd").format(calendar.getTime());
    }
}
