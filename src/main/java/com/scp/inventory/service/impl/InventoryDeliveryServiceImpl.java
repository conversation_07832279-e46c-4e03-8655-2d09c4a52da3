package com.scp.inventory.service.impl;

import com.adm.system.bean.CascaderBean;
import com.alibaba.fastjson.JSONArray;
import com.scp.inventory.bean.DeliveryReport2Data;
import com.scp.inventory.bean.InventoryDeliveryReport4Bean;
import com.scp.inventory.bean.InventoryDeliveryReport4Treemap;
import com.scp.inventory.dao.IInventoryDeliveryDao;
import com.scp.inventory.service.IInventoryDeliveryService;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service("inventoryDeliveryService")
@Scope("prototype")
@Transactional
public class InventoryDeliveryServiceImpl extends ServiceHelper implements IInventoryDeliveryService {

    @Resource
    private IInventoryDeliveryDao inventoryDeliveryDao;

    @Resource
    private Response response;

    @Resource
    private ExcelTemplate excelTemplate;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage() {
        List<CascaderBean> resultList = new ArrayList<>();
        List<Map<String, String>> dataList = inventoryDeliveryDao.queryCascader();

        Map<String, List<Map<String, String>>> tempMap = new LinkedHashMap<>();

        for (Map<String, String> map : dataList) {
            List<Map<String, String>> list = tempMap.computeIfAbsent(map.get("CATEGORY"), key -> new ArrayList<>());
            list.add(map);
        }

        for (String key : tempMap.keySet()) {
            CascaderBean bean = new CascaderBean();
            resultList.add(bean);
            bean.setLabel(key);
            bean.setValue(key);
            for (Map<String, String> map : tempMap.get(key)) {
                CascaderBean subBean = new CascaderBean();
                subBean.setLabel(map.get("NAME"));
                subBean.setValue(subBean.getLabel());
                bean.addChild(subBean);
            }
        }

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cascader", resultList);

        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateFilter(parameterMap);
        this.generateReport1Tooltips(parameterMap);

        return response.setBody(inventoryDeliveryDao.queryReport1(parameterMap));
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1DetailsChart(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateFilter(parameterMap);
        this.generateReport1Tooltips(parameterMap);

        if (Utils.hasInjectionAttack(parameterMap.get("selectedOptions"))) {
            return response;
        }
        return response.setBody(inventoryDeliveryDao.queryReport1DetailsChart(parameterMap));
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1DetailsTable(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);
        this.generateFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(inventoryDeliveryDao.queryReport1DetailsTableCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(inventoryDeliveryDao.queryReport1DetailsTable(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport1DetailsTable(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateCascaderFilter(parameterMap);
        this.generateFilter(parameterMap);
        String fileName = "Unissue_delivery_structure_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.inventory.dao.IInventoryDeliveryDao.queryReport1DetailsTable", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1Overall(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        parameterMap.put("overall", true);
        page.setTotal(inventoryDeliveryDao.queryReport1DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(inventoryDeliveryDao.queryReport1Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport1Overall(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        parameterMap.put("overall", true);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "Unissue_delivery_structure_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.inventory.dao.IInventoryDeliveryDao.queryReport1Details", parameterMap);
    }


    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateFilter(parameterMap);
        List<DeliveryReport2Data> dataList = inventoryDeliveryDao.queryReport2(parameterMap);
        Map<String, BigDecimal> dataMap = new HashMap<>();
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, String> xAxisMap = new HashMap<>();

        List<String> legend = inventoryDeliveryDao.queryReport2Legend(parameterMap);

        for (DeliveryReport2Data data : dataList) {
            dataMap.put(data.getKey(), data.getUD_VALUE());
            xAxisMap.put(data.getUD_DATE(), "");
        }
        List<String> xAxisList = xAxisMap.keySet().stream().sorted(String::compareTo).collect(Collectors.toList());

        for (String l : legend) {
            List<BigDecimal> temp = new ArrayList<>();

            for (String x : xAxisList) {
                temp.add(dataMap.getOrDefault(l + "#" + x, BigDecimal.ZERO));
            }
            resultMap.put(l, temp);
        }

        resultMap.put("xAxis", xAxisList);
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2Details(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);
        this.generateFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(inventoryDeliveryDao.queryReport2DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(inventoryDeliveryDao.queryReport2Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport2Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateCascaderFilter(parameterMap);
        this.generateFilter(parameterMap);
        String fileName = "unissue_delivery_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.inventory.dao.IInventoryDeliveryDao.queryReport2Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateFilter(parameterMap);

        String type = (String) parameterMap.get("type");
        if ("Quantity".equalsIgnoreCase(type)) {
            parameterMap.put("TOTAL_UD", "SUM(t.DELIVERY_QUANTITY)");
        } else if ("Moving Average Price".equalsIgnoreCase(type)) {
            parameterMap.put("TOTAL_UD", "SUM(t.DELIVERY_COST_VALUE)");
        } else if ("Moving Average Price HKD".equalsIgnoreCase(type)) {
            parameterMap.put("TOTAL_UD", "SUM(t.DELIVERY_COST_VALUE_HKD)");
        } else if ("Net Net Price".equalsIgnoreCase(type)) {
            parameterMap.put("TOTAL_UD", "SUM(t.DELIVERY_VALUE)");
        } else if ("Net Net Price HKD".equalsIgnoreCase(type)) {
            parameterMap.put("TOTAL_UD", "SUM(t.DELIVERY_VALUE_HKD)");
        } else if ("Net Price".equalsIgnoreCase(type)) {
            parameterMap.put("TOTAL_UD", "SUM(t.DELIVERY_NET_VALUE)");
        } else {
            parameterMap.put("TOTAL_UD", "count(1)");
        }

        Object report3OrderTypeObj = parameterMap.get("report3OrderType");
        List<String> report3OrderTypeList = Arrays.asList("0-1D", "1-3D", "3-7D", "7-14D", "14-30D", "1-2M", "2-3M", "3-6M", ">6M");
        if (report3OrderTypeObj instanceof JSONArray) {
            JSONArray report3OrderTypeJsonList = (JSONArray) report3OrderTypeObj;
            if (report3OrderTypeJsonList.isEmpty() == false) {
                report3OrderTypeList = report3OrderTypeJsonList.toJavaList(String.class);
            }
        }

        String report3Order = "(nvl(\"'" + StringUtils.join(report3OrderTypeList, "'_TOTAL\",0) + nvl(\"'") + "'_TOTAL\",0))";
        parameterMap.put("report3Order", report3Order);

        page.setData(inventoryDeliveryDao.queryReport3(parameterMap));
        return response.setBody(page);
    }

    @Override
    public void downloadReport3(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateFilter(parameterMap);

        String type = (String) parameterMap.get("type");
        if ("Quantity".equalsIgnoreCase(type)) {
            parameterMap.put("TOTAL_UD", "SUM(t.DELIVERY_QUANTITY)");
        } else if ("Moving Average Price".equalsIgnoreCase(type)) {
            parameterMap.put("TOTAL_UD", "SUM(t.DELIVERY_COST_VALUE)");
        } else if ("Moving Average Price HKD".equalsIgnoreCase(type)) {
            parameterMap.put("TOTAL_UD", "SUM(t.DELIVERY_COST_VALUE_HKD)");
        } else if ("Net Net Price".equalsIgnoreCase(type)) {
            parameterMap.put("TOTAL_UD", "SUM(t.DELIVERY_VALUE)");
        } else if ("Net Net Price HKD".equalsIgnoreCase(type)) {
            parameterMap.put("TOTAL_UD", "SUM(t.DELIVERY_VALUE_HKD)");
        } else if ("Net Price".equalsIgnoreCase(type)) {
            parameterMap.put("TOTAL_UD", "SUM(t.DELIVERY_NET_VALUE)");
        } else {
            parameterMap.put("TOTAL_UD", "count(1)");
        }

        Object report3OrderTypeObj = parameterMap.get("report3OrderType");
        List<String> report3OrderTypeList = Arrays.asList("0-1D", "1-3D", "3-7D", "7-14D", "14-30D", "1-2M", "2-3M", "3-6M", ">6M");
        if (report3OrderTypeObj instanceof JSONArray) {
            JSONArray report3OrderTypeJsonList = (JSONArray) report3OrderTypeObj;
            if (report3OrderTypeJsonList.isEmpty() == false) {
                report3OrderTypeList = report3OrderTypeJsonList.toJavaList(String.class);
            }
        }

        String report3Order = "(nvl(\"'" + StringUtils.join(report3OrderTypeList, "'_TOTAL\",0) + nvl(\"'") + "'_TOTAL\",0))";
        parameterMap.put("report3Order", report3Order);
        parameterMap.put("report3Length", "(select count(*) from TRADING_UNISSUE_DELIVERY_V)");
        page.setData(inventoryDeliveryDao.queryReport3(parameterMap));

        String fileName = "unissue_delivery_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.inventory.dao.IInventoryDeliveryDao.queryReport3", parameterMap);
    }

    @Override
    public Response queryReport3Details(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);
        this.generateFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(inventoryDeliveryDao.queryReport3DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(inventoryDeliveryDao.queryReport3Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateCascaderFilter(parameterMap);
        this.generateFilter(parameterMap);
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "unissue_delivery_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.inventory.dao.IInventoryDeliveryDao.queryReport3Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport4(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateReport4Tooltips(parameterMap);

        // 将前台传过来的label转换成列名, 同时也可以防止恶意代码注入
        parameterMap.put("level1", this.getColumnNameByLabel(parameterMap.get("level1")));
        parameterMap.put("level2", this.getColumnNameByLabel(parameterMap.get("level2")));
        parameterMap.put("level3", this.getColumnNameByLabel(parameterMap.get("level3")));
        parameterMap.put("level4", this.getColumnNameByLabel(parameterMap.get("level4")));
        parameterMap.put("level5", this.getColumnNameByLabel(parameterMap.get("level5")));

        List<InventoryDeliveryReport4Treemap> resultList = new ArrayList<>();
        List<InventoryDeliveryReport4Bean> dataList = inventoryDeliveryDao.queryReport4(parameterMap);
        for (InventoryDeliveryReport4Bean data : dataList) {
            this.convertReport4Data(resultList, data);
        }
        return response.setBody(resultList);
    }

    private void convertReport4Data(List<InventoryDeliveryReport4Treemap> list, InventoryDeliveryReport4Bean data) {
        String[] categorysOrg = new String[]{data.getCategory1(), data.getCategory2(), data.getCategory3(), data.getCategory4(), data.getCategory5()};
        List<String> categories = new ArrayList<>();

        for (String category : categorysOrg) {
            if (StringUtils.isNotBlank(category)) {
                categories.add(category);
            } else {
                break;
            }
        }

        // 这边逻辑比较复杂, 所以用最笨的方法来描述了, 以免后期不好维护
        // 先把这一行数据转成treemap的数据
        // 第一个节点
        List<InventoryDeliveryReport4Treemap> child = new ArrayList<>();
        InventoryDeliveryReport4Treemap root = new InventoryDeliveryReport4Treemap();
        root.setName(categories.get(0));
        root.setTips(data.copyTooltips()); // 因为这个tooltips要放在树中全局使用, 所以必须要生成一个新节点
        root.setChildren(child);

        // 中间节点
        for (int i = 1; i < categories.size() - 1; i++) {
            InventoryDeliveryReport4Treemap treemap = new InventoryDeliveryReport4Treemap();
            treemap.setName(categories.get(i));
            treemap.setTips(data.copyTooltips());

            child.add(treemap);
            child = new ArrayList<>();
            treemap.setChildren(child);
        }

        // 最后一个节点
        InventoryDeliveryReport4Treemap lastNode = new InventoryDeliveryReport4Treemap();
        lastNode.setName(categories.get(categories.size() - 1));
        lastNode.setValue(data.getValue());
        lastNode.setTips(data.copyTooltips());
        child.add(lastNode);

        // 将这行treemap与原始数据相加
        // 先找到list中是否有这个数据节点
        Optional<InventoryDeliveryReport4Treemap> beanOpt = list.stream().filter(b -> b.getName().equals(categories.get(0))).findFirst();
        if (beanOpt.isPresent()) {
            InventoryDeliveryReport4Treemap bean = beanOpt.get();
            bean.add(root); // 两个节点合并
        } else { //找不到的时候最省事, 直接放入list就可以了
            list.add(root);
        }
    }

    /**
     * 生成cascader filter
     *
     * @param parameterMap 参数map
     */
    private void generateCascaderFilter(Map<String, Object> parameterMap) {
        this.generateCascaderFilterSQL(parameterMap);
    }

    private void generateFilter(Map<String, Object> parameterMap) {
        String selectedTreePath = (String) parameterMap.get("selectedTreePath");
        if (StringUtils.isNotBlank(selectedTreePath)) {
            List<String> conditions = new ArrayList<>();
            String[] treePaths = selectedTreePath.split(" > ");
            for (int i = 1; i <= Math.min(treePaths.length, 5); i++) {
                String key = Utils.randomStr(8);
                if ("Others".equals(StringUtils.trim(treePaths[i - 1]))) {
                    String name = this.getColumnNameByLabel(parameterMap.get("level" + i));
                    conditions.add("(" + name + " = #{" + key + ",jdbcType=VARCHAR} or " + name + " is null )");
                } else {
                    conditions.add(this.getColumnNameByLabel(parameterMap.get("level" + i)) + " = #{" + key + ",jdbcType=VARCHAR}");
                }
                parameterMap.put(key, StringUtils.trim(treePaths[i - 1]));
            }

            parameterMap.put("treePathFilter", "(" + StringUtils.join(conditions, " and ") + ")");
        }
    }

    private void generateValueColumn(Map<String, Object> parameterMap) {
        String type = (String) parameterMap.get("type");
        if ("Quantity".equalsIgnoreCase(type)) {
            parameterMap.put("valueColumn", "SUM(t.DELIVERY_QUANTITY)");
        } else if ("Moving Average Price".equalsIgnoreCase(type)) {
            parameterMap.put("valueColumn", "SUM(t.DELIVERY_COST_VALUE)");
        } else if ("Moving Average Price HKD".equalsIgnoreCase(type)) {
            parameterMap.put("valueColumn", "SUM(t.DELIVERY_COST_VALUE_HKD)");
        } else if ("Net Net Price".equalsIgnoreCase(type)) {
            parameterMap.put("valueColumn", "SUM(t.DELIVERY_VALUE)");
        } else if ("Net Net Price HKD".equalsIgnoreCase(type)) {
            parameterMap.put("valueColumn", "SUM(t.DELIVERY_VALUE_HKD)");
        } else if ("Net Price".equalsIgnoreCase(type)) {
            parameterMap.put("valueColumn", "SUM(t.DELIVERY_NET_VALUE)");
        } else {
            parameterMap.put("valueColumn", "count(1)");
        }
    }

    private void generateReport1Tooltips(Map<String, Object> parameterMap) {
        List<String> tooltips = ((JSONArray) parameterMap.get("tooltips")).toJavaList(String.class);
        if (tooltips.isEmpty() == false) {
            List<String> tooltipsColumns = tooltips.stream().map(this::getColumnNameByLabel).collect(Collectors.toList());

            List<String> tooltipsColumnsName = new ArrayList<>();

            for (String c : tooltipsColumns) {
                if (c.startsWith("OPEN_SO_") || c.startsWith("UU_STOCK_")) {
                    tooltipsColumnsName.add("nvl(sum(mm." + c + "),0) AS " + c);
                } else {
                    tooltipsColumnsName.add("nvl(sum(t." + c + "),0) AS " + c);
                }
            }

            parameterMap.put("tooltipsColumns", StringUtils.join(tooltipsColumnsName, ", "));
        }
    }

    private String getColumnNameByLabel(Object labelObj) {
        String label = (String) labelObj;
        if (label == null) {
            return null;
        }
        if (label.equals("DELIVERY_QTY")) {
            return "DELIVERY_QUANTITY";
        }
        if (Utils.hasInjectionAttack(label) == false) {
            return label;
        } else {
            return "";
        }
    }

    private final HashMap<String, String> column4TooltipMap = new HashMap<>();
    private final HashMap<String, String> tooltip4ColumnMap = new HashMap<>();

    private void generateReport4Tooltips(Map<String, Object> parameterMap) {
        String resultType = (String) parameterMap.get("resultType");
        List<String> tooltips = ((JSONArray) parameterMap.get("report4Tooltips")).toJavaList(String.class);
        if (!tooltips.isEmpty()) {
            List<String> tooltipsColumns = tooltips.stream().map(
                    this::getColumnNameByLabel).collect(Collectors.toList());
            List<String> tooltipsColumnsName = new ArrayList<>();
            List<String> polymorphicColumns = new ArrayList<>(Arrays.asList("PO_AB", "PO_LA", "PO_NON_ABLA"));

            for (String c : tooltipsColumns) {
                String tooltip = column4TooltipMap.getOrDefault(c, c);
                if (polymorphicColumns.contains(c)) {
                    if ("Quantity".equalsIgnoreCase(resultType)) {
                        tooltipsColumnsName.add("NVL(SUM(" + c + "),0) AS " + tooltip);
                    } else if ("Value".equalsIgnoreCase(resultType)) {
                        tooltipsColumnsName.add("NVL(SUM(" + c + "_VALUE),0) AS " + tooltip);
                    } else if ("Line".equalsIgnoreCase(resultType)) {
                        tooltipsColumnsName.add("NVL(SUM(CASE WHEN " + c + " > 0 THEN 1 ELSE 0 END),0) AS " + tooltip);
                    }
                } else {
                    tooltipsColumnsName.add("NVL(AVG(" + c + "),0) AS " + tooltip);
                }
            }
            parameterMap.put("tooltipsColumns", StringUtils.join(tooltipsColumnsName, ", "));
        }
    }
}
