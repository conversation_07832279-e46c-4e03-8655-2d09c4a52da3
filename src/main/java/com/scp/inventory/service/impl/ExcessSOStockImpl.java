package com.scp.inventory.service.impl;

import com.adm.system.bean.CascaderBean;
import com.alibaba.fastjson.JSONArray;
import com.scp.inventory.bean.ExcessSOStockReport1Bean;
import com.scp.inventory.bean.ExcessSOStockReport1Treemap;
import com.scp.inventory.bean.ExcessSOStockReport2Bean;
import com.scp.inventory.dao.IExcessSOStockDao;
import com.scp.inventory.service.IExcessSOStockService;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service("ExcessSOStockService")
@Scope("prototype")
@Transactional
public class ExcessSOStockImpl extends ServiceHelper implements IExcessSOStockService {

    @Resource
    private IExcessSOStockDao excessSOStockDao;

    @Resource
    private Response response;

    @Resource
    private ExcelTemplate excelTemplate;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryCascader() {
        List<CascaderBean> resultList = new ArrayList<>();
        List<Map<String, String>> dataList = excessSOStockDao.queryCascader();

        //取出所有filters中的{BU=[{CATEGORY=BU, NAME=SCE SECURE POWER SERV.}, {CATEGORY=BU, NAME=IND PROCESS AUTOMATION}]}
        Map<String, List<Map<String, String>>> tempMap = new LinkedHashMap<>();
        for (Map<String, String> map : dataList) {
            List<Map<String, String>> list = tempMap.computeIfAbsent(map.get("CATEGORY"), key -> new ArrayList<>());
            list.add(map);
        }

        // .keyset() 取出所有键; 生成filter的指定格式List<CascaderBean>
        for (String key : tempMap.keySet()) {
            CascaderBean bean = new CascaderBean();
            resultList.add(bean);
            bean.setLabel(key);
            bean.setValue(key);
            for (Map<String, String> map : tempMap.get(key)) {
                CascaderBean subBean = new CascaderBean();
                subBean.setLabel(map.get("NAME"));
                subBean.setValue(subBean.getLabel());
                bean.addChild(subBean);
            }
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cascader", resultList);

        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateReport1Tooltips(parameterMap);
        this.generateValueColumn(parameterMap);

        // 将前台传过来的label转换成列名, 同时也可以防止恶意代码注入
        parameterMap.put("level1", this.getColumnName(parameterMap.get("level1")));
        parameterMap.put("level2", this.getColumnName(parameterMap.get("level2")));
        parameterMap.put("level3", this.getColumnName(parameterMap.get("level3")));
        parameterMap.put("level4", this.getColumnName(parameterMap.get("level4")));
        parameterMap.put("level5", this.getColumnName(parameterMap.get("level5")));

        List<ExcessSOStockReport1Treemap> resultList = new ArrayList<>();
        List<ExcessSOStockReport1Bean> dataList = excessSOStockDao.queryReport1(parameterMap);
        for (ExcessSOStockReport1Bean data : dataList) {
            this.convertReport1Data(resultList, data);
        }
        return response.setBody(resultList);
    }

    private String getColumnName(Object labelObj) {
        String label = (String) labelObj;
        if (label == null) {
            return null;
        }
        if (Utils.hasInjectionAttack(label)) {
            return "";
        }
        return label;
    }

    private void convertReport1Data(List<ExcessSOStockReport1Treemap> list, ExcessSOStockReport1Bean data) {
        String[] categorysOrg = new String[]{data.getCategory1(), data.getCategory2(), data.getCategory3(), data.getCategory4(), data.getCategory5()};
        List<String> categories = new ArrayList<>();

        for (String category : categorysOrg) {
            if (StringUtils.isNotBlank(category)) {
                categories.add(category);
            } else {
                break;
            }
        }

        // 这边逻辑比较复杂, 所以用最笨的方法来描述了, 以免后期不好维护
        // 先把这一行数据转成treemap的数据
        // 第一个节点
        List<ExcessSOStockReport1Treemap> child = new ArrayList<>();
        ExcessSOStockReport1Treemap root = new ExcessSOStockReport1Treemap();
        root.setName(categories.get(0));
        root.setTips(data.copyTooltips()); // 因为这个tooltips要放在树中全局使用, 所以必须要生成一个新节点
        root.setChildren(child);

        // 中间节点
        for (int i = 1; i < categories.size() - 1; i++) {
            ExcessSOStockReport1Treemap treemap = new ExcessSOStockReport1Treemap();
            treemap.setName(categories.get(i));
            treemap.setTips(data.copyTooltips());

            child.add(treemap);
            child = new ArrayList<>();
            treemap.setChildren(child);
        }

        // 最后一个节点
        ExcessSOStockReport1Treemap lastNode = new ExcessSOStockReport1Treemap();
        lastNode.setName(categories.get(categories.size() - 1));
        lastNode.setValue(data.getValue());
        lastNode.setTips(data.copyTooltips());
        child.add(lastNode);

        // 将这行treemap与原始数据相加
        // 先找到list中是否有这个数据节点
        Optional<ExcessSOStockReport1Treemap> beanOpt = list.stream().filter(b -> b.getName().equals(categories.get(0))).findFirst();
        if (beanOpt.isPresent()) {
            ExcessSOStockReport1Treemap bean = beanOpt.get();
            bean.add(root); // 两个节点合并
        } else { //找不到的时候最省事, 直接放入list就可以了
            list.add(root);
        }
    }

    //region private functions
    @SuppressWarnings("unchecked")
    private void generateFilter(Map<String, Object> parameterMap) {
        List<String> report3SelectedColumns = (List<String>) parameterMap.get("report3SelectedColumns");

        if (report3SelectedColumns == null || report3SelectedColumns.isEmpty()) {
            report3SelectedColumns = new ArrayList<>();
            report3SelectedColumns.add("BU");
            report3SelectedColumns.add("ENTITY");
        }
        parameterMap.put("report3SelectedColumns", report3SelectedColumns);
        // 生成筛选条件
        this.generateCascaderFilterSQL(parameterMap);
    }

    private void generateReport1Tooltips(Map<String, Object> parameterMap) {
        String resultType = (String) parameterMap.get("resultType");
        List<String> tooltips = ((JSONArray) parameterMap.get("report1Tooltips")).toJavaList(String.class);
        if (!tooltips.isEmpty()) {
            List<String> tooltipsColumns = tooltips.stream().map(this::getColumnName).toList();
            List<String> tooltipsColumnsName = new ArrayList<>();
            List<String> polymorphicColumns = new ArrayList<>(List.of(""));

            for (String c : tooltipsColumns) {
                String tooltip = this.getColumnName(c);
                if (polymorphicColumns.contains(c)) {
                    if ("Quantity".equalsIgnoreCase(resultType)) {
                        tooltipsColumnsName.add("NVL(SUM(" + c + "),0) AS " + tooltip);
                    } else if ("Value".equalsIgnoreCase(resultType)) {
                        tooltipsColumnsName.add("NVL(SUM(" + c + "_VALUE),0) AS " + tooltip);
                    } else if ("Line".equalsIgnoreCase(resultType)) {
                        tooltipsColumnsName.add("NVL(SUM(CASE WHEN " + c + " > 0 THEN 1 ELSE 0 END),0) AS " + tooltip);
                    }
                } else {
                    tooltipsColumnsName.add("NVL(SUM(" + c + "),0) AS " + tooltip);
                }
            }
            parameterMap.put("tooltipsColumns", StringUtils.join(tooltipsColumnsName, ", "));
        }
    }

    private void generateTreePathFilter(Map<String, Object> parameterMap) {
        String selectedTreePath = (String) parameterMap.get("selectedTreePath");
        if (StringUtils.isNotBlank(selectedTreePath)) {
            List<String> conditions = new ArrayList<>();
            String[] treePaths = selectedTreePath.split(" > ");
            for (int i = 1; i <= Math.min(treePaths.length, 5); i++) {
                String key = Utils.randomStr(8);
                if ("Others".equals(StringUtils.trim(treePaths[i - 1]))) {
                    String name = this.getColumnName(parameterMap.get("level" + i));
                    conditions.add("(" + name + " = #{" + key + ",jdbcType=VARCHAR} or " + name + " is null )");
                } else {
                    conditions.add(this.getColumnName(parameterMap.get("level" + i)) + " = #{" + key + ",jdbcType=VARCHAR}");
                }
                parameterMap.put(key, StringUtils.trim(treePaths[i - 1]));
            }
            parameterMap.put("treePathFilter", "(" + StringUtils.join(conditions, " and ") + ")");
        }
    }

    private void generateValueColumn(Map<String, Object> parameterMap) {
        String resultType = (String) parameterMap.get("resultType");
        String valueColumn = "NVL(SUM(BAD_INDENT_QTY), 0)";
        if ("Value".equalsIgnoreCase(resultType)) {
            valueColumn = "NVL(SUM(BAD_INDENT_VALUE), 0)";
        } else if ("Quantity".equalsIgnoreCase(resultType)) {
            valueColumn = "NVL(SUM(BAD_INDENT_QTY), 0)";
        } else if ("Line".equalsIgnoreCase(resultType)) {
            valueColumn = "COUNT(1)";
        }
        parameterMap.put("valueColumn", valueColumn);
    }


    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);

        List<ExcessSOStockReport2Bean> dataList;

        dataList = excessSOStockDao.queryReport2(parameterMap);

        Map<String, BigDecimal> dataMap = new HashMap<>();
        LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();
        Map<String, String> xAxisMap = new HashMap<>();

        List<String> legend = new ArrayList<>();

        // 先将数据转存到dataMap、xAxisMap
        for (ExcessSOStockReport2Bean data : dataList) {
            dataMap.put(data.getKey(), data.getVALUE());
            xAxisMap.put(data.getCALENDAR_DATE(), "");
            if (legend.contains(data.getNAME()) == false) {
                legend.add(data.getNAME());
            }
        }
        // 获取x轴
        List<String> xAxisList = xAxisMap.keySet().stream().sorted(String::compareTo).collect(Collectors.toList());
        //对于每一个图例做处理，生成{图例:[VALUE]}的map
        for (String l : legend) {
            List<BigDecimal> temp = new ArrayList<>();
            for (String x : xAxisList) {
                temp.add(dataMap.getOrDefault(l + "#" + x, BigDecimal.ZERO));
            }
            resultMap.put(l, temp);
        }
        // 放置x轴
        resultMap.put("xAxis", xAxisList);
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(excessSOStockDao.queryReport2DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(excessSOStockDao.queryReport2Details(parameterMap));
        }
        return response.setBody(page);
    }

    public void downloadReport2Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "excess_so_stock_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.inventory.dao.IExcessSOStockDao.queryReport2Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        List<LinkedHashMap<String, Object>> dataList = excessSOStockDao.queryReport3(parameterMap);

        page.setData(dataList);
        page.setTotal(dataList.size());
        return response.setBody(page);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(excessSOStockDao.queryReport3DetailsCount(parameterMap));
        if (page.getTotal() > 0 ) {
            page.setData(excessSOStockDao.queryReport3Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Columns(Map<String, Object> parameterMap) {
        return response.setBody(excessSOStockDao.queryReport3Columns(parameterMap));
    }

    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public void downloadReport3(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        String column1 = this.getColumnName(parameterMap.get("column1"));
        String column2 = this.getColumnName(parameterMap.get("column2"));
        parameterMap.put("column1", column1);
        parameterMap.put("column2", column2);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "excess_so_stock_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.inventory.IExcessSOStockDao.queryReport3", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        parameterMap.put("column1", this.getColumnName(parameterMap.get("column1")));
        parameterMap.put("column2", this.getColumnName(parameterMap.get("column2")));
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "excess_so_stock_details_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.inventory.dao.IExcessSOStockDao.queryReport3Details", parameterMap);
    }
}
