package com.scp.inventory.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.scp.inventory.MonthlySliceReportController;
import com.scp.inventory.bean.SliceCurrency;
import com.scp.inventory.bean.SliceFilters;
import com.scp.inventory.bean.SliceRawData;
import com.scp.inventory.dao.IMonthlySliceReportDao;
import com.scp.inventory.service.IMonthlySliceReportService;
import com.starter.context.bean.ForbiddenException;
import com.starter.context.bean.Message;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.bean.scptable.ScpTableHelper;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;

@Service("monthlySliceReportService")
@Scope("prototype")
@Transactional
public class MonthlySliceReportServiceImpl implements IMonthlySliceReportService {

    @Resource
    private IMonthlySliceReportDao monthlySliceReportDao;

    @Resource
    private Response response;

    @Resource
    private ScpTableHelper scpTableHelper;

    @Resource
    private ExcelTemplate excelTemplate;


    @Override
    public Response initPage(String userid) {
        Map<String, Object> resultMap = new HashMap<>();
        List<String> versions = monthlySliceReportDao.querySliceVersion();
        resultMap.put("cascader", Utils.parseCascader(monthlySliceReportDao.queryCascader(), false));
        resultMap.put("version", versions);
        resultMap.put("isAdmin", monthlySliceReportDao.queryPageAdmin(userid, MonthlySliceReportController.PARENT_CODE) > 0);
        resultMap.put("entity", monthlySliceReportDao.queryAvailableEntity());
        resultMap.put("rca", monthlySliceReportDao.queryRcaCode());
        List<Map<String, Object>> rcaList = monthlySliceReportDao.queryInventoryRCAList();
        Map<String, Object> rcaMap = new HashMap<>();
        for (Map<String, Object> map : rcaList) {
            rcaMap.put((String) map.get("KEY"), map.get("VALUE"));
        }
        resultMap.put("rcaMap", rcaMap);
        return response.setBody(resultMap);
    }

    @SuppressWarnings("unchecked")
    public Response saveReport1Calculated(Map<String, Object> parameterMap) {
        try {
            String version = (String) parameterMap.get("version");
            parameterMap.put("currency", "CNY");
            parameterMap.put("filterList", JSONArray.parseArray("[\n" +
                    "      ['CLASS', 'Gross Inventory'],\n" +
                    "      ['CLASS', 'Net Inv'],\n" +
                    "      ['CLASS', 'Yearly DIN_Net Inv']\n" +
                    "    ]"));
            Response res = this.queryReport1(parameterMap);
            List<SliceRawData> dataWithProjection = ((HashMap<String, List<SliceRawData>>) res.getBody()).get("data");
            monthlySliceReportDao.deleteSliceReportCalculated(version);

            List<Map<String, Object>> targetList = monthlySliceReportDao.querySliceTarget();

            Map<String, Object> targetMap = new HashMap<>();

            for (Map<String, Object> map : targetList) {
                String clazz = (String) map.get("CLASS");
                String plantType = (String) map.get("PLANT_TYPE");
                if ("Final target Net Inv".equals(clazz)) {
                    clazz = "Net Inv";
                } else if ("Final target Gross Inv".equals(clazz)) {
                    clazz = "Gross Inventory";
                } else if ("Final target DIN12".equals(clazz)) {
                    clazz = "Yearly DIN_Net Inv";
                }

                if ("FAC".equalsIgnoreCase(plantType)) {
                    plantType = "Plant";
                }

                String key = plantType + "#" + map.get("ENTITY") + "#" + clazz + "#" + map.get("MONTH_YEAR");
                targetMap.put(key, map.get("VAL"));
            }

            for (SliceRawData rawData : dataWithProjection) {
                if ("Total".equalsIgnoreCase(rawData.getClusterName())) {
                    continue;
                }
                List<Map<String, Object>> calcData = this.expandRawData(version, rawData, targetMap);
                monthlySliceReportDao.saveSliceReportCalculated(version, calcData);
            }
            return response;
        } catch (Exception e) {
            return response.setBody(e.getMessage());
        }
    }

    @Override
    public Response queryReport1(Map<String, Object> parameterMap) throws Exception {
        SliceFilters filters = new SliceFilters(parameterMap);
        int exists = monthlySliceReportDao.queryExistsResult(parameterMap);
        if (exists > 0) {
            this.calcReport1();
        }
        String toCurrency = (String) parameterMap.get("currency");
        String version = (String) parameterMap.get("version");

        List<SliceRawData> rawDataList = monthlySliceReportDao.queryReport1(parameterMap);
        // 0. 计算当月的SOH_FG, SOH_RM, GIT, WIP1, GROSS_INV
        this.calcTodayData(rawDataList);
        // 1. 查询并转换汇率
        rawDataList = this.calcCurrency(version, rawDataList, toCurrency);
        // 2. 复制COGS到后面的每一个月
        this.copyActualCOGSToProjection(version, rawDataList);
        // 2. 合并Projection
        rawDataList = this.calcProjectionData(parameterMap, rawDataList, version, toCurrency);
        // 3. 因为China汇总和DC汇总可能被Projection影响, 所以没办法, 只能将汇总放在实时查询中做, 补Cluster和China的Gross COGS
        rawDataList = this.apendPlantCOGSData(rawDataList);
        // 3.1 计算Plant汇总, 包括Provision和Gross Inventory, China Total中将不再求和China_DC的内容
        rawDataList = this.apendPlantSummaryData(rawDataList);
        // 3.2 对汇总后的China Total的Provision, 要减去SE-China的Provision, Kristy
        rawDataList = this.calcChinaTotalProvision(rawDataList, version, toCurrency);
        // 4. 补全DC缺的数据行
        rawDataList = this.apendDCSummaryData(rawDataList);

        // 4.1 China Total 需要加上计算过Projection的Provision和Gross Inventory
        rawDataList = this.apendChinaTotal(rawDataList);

        // 5. 计算6个公式行
        rawDataList = this.calcFormulaData(version, rawDataList);
        // 6. 根据筛选项, 筛掉多余的行
        rawDataList = this.filterResult(rawDataList, filters);
        // 7. 暴力汇总
        this.summaryResult(rawDataList);
        // 8. 重命名Plant Type
        this.renamePlantType(rawDataList);
        // 9. 排序结果集
        this.sort(rawDataList);

        Map<String, Object> resultMap = this.queryReport1Columns(parameterMap);
        resultMap.put("data", rawDataList);

        return response.setBody(resultMap);
    }

    private void calcReport1() {
        String version = new SimpleDateFormat("yyyyMM").format(new Date());
        // 1. 将用户汇率保存副本, 保存之前判断数据库中是否有CNY对HKD的汇率, 如果没有, 还需要手动算
        monthlySliceReportDao.syncForex(version);
        // 2. 查询RawData
        List<SliceRawData> rawDataList = monthlySliceReportDao.queryRawData(version);
        // 3. 补FAC的数据
        rawDataList = this.appendFACLine(rawDataList);
        // 4. 补DCHK的数据
        this.appendDCHKLine(rawDataList);
        // 5. 补DC的数据
        rawDataList = this.appendDCLine(rawDataList);
        // 6. 转换汇率, 只转换INV的汇率, 因为INV可以直接相加减
        rawDataList = this.calcINVCurrency(version, rawDataList);
        // 7. 将Actual最后一天复制到Projection的每一天
        this.copyActualToProjection(version, rawDataList);
        // 8. 将计算的数据存起来
        monthlySliceReportDao.deleteExistReport(version);
        List<List<SliceRawData>> dataList = Utils.subList(rawDataList, 500);
        for (List<SliceRawData> list : dataList) {
            monthlySliceReportDao.saveReport(version, list);
        }
        // 9. 刷新FILTER物化视图
        monthlySliceReportDao.refreshFilterMV();
    }

    /**
     * 将数据中Plant的挑选出来, 然后对Plant的项目进行检查, 如果缺失某个Class, 那么需要补一个对应的数据, 补的Class汇率一律为CNY
     *
     * @param rawDataList 上一步的数据
     * @return 补好行的数据
     */
    private List<SliceRawData> appendFACLine(List<SliceRawData> rawDataList) {
        final String[] facClazzes = new String[]{"Provision", "SOH_FG", "SOH_RM", "SOH_MB", "GIT", "WIP1", "WIP2", "Gross Inventory", "COGS"};

        List<SliceRawData> resultList = new ArrayList<>();
        // 先将数据按照CLUSTER_NAME, PLANT_TYPE和ENTITY分类, only Plant
        Map<String, List<SliceRawData>> dataMap = new HashMap<>();
        for (SliceRawData data : rawDataList) {
            if (data.isPlant()) {
                String key = data.getKey();
                List<SliceRawData> tempList = dataMap.computeIfAbsent(key, k -> new ArrayList<>());
                tempList.add(data);
            } else {
                resultList.add(data);
            }
        }

        // 再对每个分类进行扩行
        for (String key : dataMap.keySet()) {
            List<SliceRawData> list = dataMap.get(key);
            for (String clazz : facClazzes) {
                Optional<SliceRawData> opt = list.stream().filter(e -> StringUtils.equalsIgnoreCase(clazz, e.getClazz())).findFirst();
                if (opt.isPresent()) {
                    resultList.add(opt.get());
                } else {
                    resultList.add(SliceRawData.getInstance(key, clazz));
                }
            }
        }

        return resultList;
    }

    /**
     * 将数据中DC的挑选出来, 然后对DCD的项目进行检查, 如果缺失某个Class, 那么需要补一个对应的数据, 补的Class汇率一律为CNY
     *
     * @param rawDataList 上一步的数据
     * @return 补好行的数据
     */
    private List<SliceRawData> appendDCLine(List<SliceRawData> rawDataList) {
        final String[] dcClazzes = new String[]{"Provision", "SOH_FG", "Gross Inventory", "COGS"};

        List<SliceRawData> resultList = new ArrayList<>();
        // 先将数据按照CLUSTER_NAME, PLANT_TYPE和ENTITY分类, only DC
        Map<String, List<SliceRawData>> dataMap = new HashMap<>();
        for (SliceRawData data : rawDataList) {
            if (data.isDC()) {
                String key = data.getKey();
                List<SliceRawData> tempList = dataMap.computeIfAbsent(key, k -> new ArrayList<>());
                tempList.add(data);
            } else {
                resultList.add(data);
            }
        }

        // 再对每个分类进行扩行
        for (String key : dataMap.keySet()) {
            List<SliceRawData> list = dataMap.get(key);
            for (String clazz : dcClazzes) {
                Optional<SliceRawData> opt = list.stream().filter(e -> StringUtils.equalsIgnoreCase(clazz, e.getClazz())).findFirst();
                if (opt.isPresent()) {
                    resultList.add(opt.get());
                } else {
                    resultList.add(SliceRawData.getInstance(key, clazz));
                }
            }
        }

        return resultList;
    }


    /**
     * Trading_DCHK Gross Inventory = GIT + SOH_FG(手工上传)
     * Trading_DCHK Provision = SEHK Provision
     */
    private void appendDCHKLine(List<SliceRawData> rawDataList) {
        SliceRawData SEHKProvision = new SliceRawData();
        SliceRawData DCHKGit = new SliceRawData();
        SliceRawData DCHKSOH_FG = new SliceRawData();
        for (SliceRawData data : rawDataList) {
            if ("SEHK".equalsIgnoreCase(data.getEntityName()) && "PROVISION".equalsIgnoreCase(data.getClazz())) {
                SEHKProvision = data;
            }

            if ("Trading_DCHK".equalsIgnoreCase(data.getEntityName()) && "GIT".equalsIgnoreCase(data.getClazz())) {
                DCHKGit = data;
            }

            if ("Trading_DCHK".equalsIgnoreCase(data.getEntityName()) && "SOH_FG".equalsIgnoreCase(data.getClazz())) {
                DCHKSOH_FG = data;
            }
        }

        SliceRawData DCHKProvision = new SliceRawData();
        SliceRawData DCHKGrossInventory = new SliceRawData();

        this.copyBasicInfo(DCHKGit, DCHKGrossInventory, "Gross Inventory");

        DCHKProvision.setClusterName("Trading");
        DCHKProvision.setEntityName("Trading_DCHK");
        DCHKProvision.setPlantType("DC");
        DCHKProvision.setClazz("Provision");
        DCHKProvision.setCurrency(SEHKProvision.getCurrency());
        DCHKProvision.setCurrencyType("INV");
        for (int i = 0; i <= 36; i++) {
            DCHKProvision.setMonthValue(i, SEHKProvision.getMonthValue(i));

            BigDecimal git = DCHKGit.getMonthValue(i);
            BigDecimal soh = DCHKSOH_FG.getMonthValue(i);
            DCHKGrossInventory.setMonthValue(i, git.add(soh));
        }
        rawDataList.add(DCHKProvision);
        rawDataList.add(DCHKGrossInventory);
    }

    private void calcTodayData(List<SliceRawData> rawDataList) {
        int month = Calendar.getInstance().get(Calendar.MONTH) + 1 + 12;
        int lastMonthIndex = month - 1;

        SimpleDateFormat dayFormat = new SimpleDateFormat("yyyy/MM/dd");

        // lastMonth
        Calendar cale = Calendar.getInstance();
        String thisMonth = dayFormat.format(cale.getTime());
        cale.set(Calendar.DAY_OF_MONTH, 0);
        String lastMonth = dayFormat.format(cale.getTime());

        // 当天的SOH_FG, SOH_RM, GIT, WIP1, GROSS_INV
        List<Map<String, Object>> todaySOH = monthlySliceReportDao.queryTodaySOH();
        List<Map<String, Object>> todayGIT = monthlySliceReportDao.queryTodayGIT(lastMonth, thisMonth);

        Map<String, BigDecimal> sohMap = new HashMap<>();

        for (Map<String, Object> map : todaySOH) {
            String key = map.get("CLUSTER_NAME") + "." + map.get("ENTITY_NAME") + "." + map.get("INVENTORY_CATEGORY") + "." + map.get("PLANT_TYPE");
            sohMap.put(key, Utils.parseBigDecimal(map.get("STOCK_VALUE")));
        }

        for (SliceRawData data : rawDataList) {
            String key = data.getClusterName() + "." + data.getEntityName() + "." + data.getClazz() + "." + data.getPlantType();
            data.setCurrentMonth(sohMap.get(key));
        }

        Map<String, Map<String, Object>> gitMap = new HashMap<>();

        for (Map<String, Object> map : todayGIT) {
            String key = map.get("CLUSTER_NAME") + "." + map.get("ENTITY_NAME") + "." + map.get("INVENTORY_CATEGORY") + "." + map.get("PLANT_TYPE");
            gitMap.put(key, map);
        }

        // 当月GIT = 当月SAP数据 + MAX(上月财务GIT - 上月SAP数据, 0)
        // 将当月计算结果放入CURRENT_MONTH
        for (SliceRawData data : rawDataList) {
            String key = data.getClusterName() + "." + data.getEntityName() + "." + data.getClazz() + "." + data.getPlantType();
            if (gitMap.containsKey(key)) {
                Map<String, Object> git = gitMap.get(key);
                BigDecimal lastFinanceGIT = Utils.parseBigDecimal(data.getMonthValue(lastMonthIndex));
                BigDecimal lastSAPGIT = Utils.parseBigDecimal(git.get("STOCK_VALUE_LAST_MONTH"));
                BigDecimal thisSAPGIT = Utils.parseBigDecimal(git.get("STOCK_VALUE_THIS_MONTH"));

                BigDecimal gap = lastFinanceGIT.subtract(lastSAPGIT);
                if (gap.compareTo(BigDecimal.ZERO) > 0) {
                    data.setCurrentMonth(thisSAPGIT.add(gap));
                } else {
                    data.setCurrentMonth(thisSAPGIT);
                }
            }
        }

        // 还要计算Plant在Cluster和China_Total层面的CURRENT_MONTH - SOH_FG, SOH_RM, GIT, WIP1, GROSS_INV汇总
        // DC不用计算, 因为后面DC有单独的汇总, Plant没有单独汇总
        // 其他月也不用算, 用户会手动上传汇总, 只有当月的值不会计算汇总
        LinkedHashMap<String, BigDecimal> CLUSTER_SUMMARY = new LinkedHashMap<>();
        LinkedHashMap<String, BigDecimal> CHINA_SUMMARY = new LinkedHashMap<>();

        // 先汇总
        for (SliceRawData data : rawDataList) {
            if (data.isPlant() && data.type() == SliceRawData.ENTITY) {
                String key = data.getClusterName() + "." + data.getClazz() + ".Plant";
                BigDecimal existsValue = CLUSTER_SUMMARY.computeIfAbsent(key, k -> BigDecimal.ZERO);
                existsValue = existsValue.add(data.getCurrentMonth());
                CLUSTER_SUMMARY.put(key, existsValue);

                key = "China_Total." + data.getClazz() + ".Plant";
                existsValue = CHINA_SUMMARY.computeIfAbsent(key, k -> BigDecimal.ZERO);
                existsValue = existsValue.add(data.getCurrentMonth());
                CHINA_SUMMARY.put(key, existsValue);
            }
        }

        // 再回写rawdata, 回写的时候只回写非Entity的行
        for (SliceRawData data : rawDataList) {
            if (data.isPlant() && data.type() != SliceRawData.ENTITY) {
                String key = data.getClusterName() + "." + data.getClazz() + ".Plant";
                if (CLUSTER_SUMMARY.containsKey(key)) {
                    data.setCurrentMonth(CLUSTER_SUMMARY.get(key));
                }
                if (CHINA_SUMMARY.containsKey(key)) {
                    data.setCurrentMonth(CHINA_SUMMARY.get(key));
                }
            }
        }
    }

    private List<SliceRawData> calcProjectionData(Map<String, Object> parameterMap, List<SliceRawData> rawDataList, String version, String toCurrency) throws Exception {
        List<SliceRawData> resultList = new ArrayList<>();
        int lastestActualMonthIndex = Utils.parseInt(version.substring(4, 6)) + 11; // 最后一个Actual月的Index

        // 根据用户的输入进行加减
        List<SliceRawData> projectionList = monthlySliceReportDao.queryProjection(parameterMap);
        // 将Projection的数据进行汇率转换, 因为Projection的汇率不会出现COGS计算的问题, 所以直接求汇率就行
        projectionList = this.calcCurrency(version, projectionList, toCurrency);

        // 开始合并
        Map<String, List<SliceRawData>> projectionMap = new HashMap<>();
        for (SliceRawData data : projectionList) {
            String key0 = data.getPlantType() + "." + data.getEntityName() + "." + data.getClazz();
            String key1 = data.getPlantType() + "." + data.getClusterName() + "." + data.getClazz();
            String key2 = data.getPlantType() + ".China_Total." + data.getClazz();
            String key3 = "China_Total." + data.getClazz();

            List<SliceRawData> temp0 = projectionMap.computeIfAbsent(key0, k -> new ArrayList<>());
            temp0.add(data);

            List<SliceRawData> temp1 = projectionMap.computeIfAbsent(key1, k -> new ArrayList<>());
            temp1.add(data);

            List<SliceRawData> temp2 = projectionMap.computeIfAbsent(key2, k -> new ArrayList<>());
            temp2.add(data);

            List<SliceRawData> temp3 = projectionMap.computeIfAbsent(key3, k -> new ArrayList<>());
            temp3.add(data);
        }

        // 将raw数据与用户输入数据进行合并
        // 即将Projection第一天的数据与用户输入的month01的数据合并
        // projection 只会影响明细, 并不会影响汇总, 所以这边需要将汇总的数字排除
        for (SliceRawData data : rawDataList) {
            String key = data.getPlantType() + "." + data.getEntityName() + "." + data.getClazz();
            if ("China_Total".equalsIgnoreCase(data.getEntityName()) && "Plant".equalsIgnoreCase(data.getPlantType())) {
                key = "China_Total." + data.getClazz();
            }
            if (projectionMap.containsKey(key) && data.type() == SliceRawData.ENTITY) {
                List<SliceRawData> projectionDataList = projectionMap.get(key);
                for (SliceRawData projectionData : projectionDataList) {
                    BigDecimal lineTotal = BigDecimal.ZERO;
                    int projectionIndex = 1;
                    for (int i = lastestActualMonthIndex + 1; i <= 36; i++) {
                        BigDecimal rawValue = data.getMonthValue(i);
                        BigDecimal projectionValue = projectionData.getMonthValue(projectionIndex);

                        lineTotal = lineTotal.add(projectionValue);

                        // Negative是加
                        if (StringUtils.equalsIgnoreCase(projectionData.getRcaCategory(), "Negative")) {
                            data.setMonthValue(i, rawValue.add(lineTotal));
                            // Positive是减
                        } else if (StringUtils.equalsIgnoreCase(projectionData.getRcaCategory(), "Positive")) {
                            data.setMonthValue(i, rawValue.subtract(lineTotal));
                        } else if (StringUtils.equalsIgnoreCase(projectionData.getRcaCategory(), "Target")) {
                            data.setMonthValue(i, projectionValue);
                        } else {
                            data.setMonthValue(i, projectionValue);
                        }
                        projectionIndex++;
                    }
                }

            }
            resultList.add(data);
        }

        return resultList;
    }

    private List<SliceRawData> calcFormulaData(String version, List<SliceRawData> rawDataList) {
        int currentMonthIndex = Utils.parseInt(version.substring(4, 6)) + 12;

        List<SliceRawData> resultList = new ArrayList<>();
        // 按照Cluster, Entity, Plant Type分类
        LinkedHashMap<String, List<SliceRawData>> dataMap = new LinkedHashMap<>();
        for (SliceRawData data : rawDataList) {
            String key = data.getClusterName() + "." + data.getEntityName() + "." + data.getPlantType();
            List<SliceRawData> tempList = dataMap.computeIfAbsent(key, k -> new ArrayList<>());
            tempList.add(data);
        }

        for (String key : dataMap.keySet()) {
            List<SliceRawData> values = dataMap.get(key);

            SliceRawData provision = new SliceRawData();
            SliceRawData grossInventory = new SliceRawData();
            SliceRawData COGS = new SliceRawData();
            SliceRawData grossCOGS = new SliceRawData();

            SliceRawData netInv = new SliceRawData();
            SliceRawData COGSConvert = new SliceRawData();
            SliceRawData spotDINNetInv = new SliceRawData();
            SliceRawData spotDINGrossInv = new SliceRawData();
            SliceRawData yearlyDINNetInv = new SliceRawData();
            SliceRawData yearlyDINGrossInv = new SliceRawData();

            // 把这组中的Provision, Gross Inventory, COGS, Gross_COGS挑选出来
            boolean hasGrossCogs = false;
            for (SliceRawData data : values) {
                resultList.add(data);
                String clazz = data.getClazz();

                if (StringUtils.equalsIgnoreCase(clazz, "Provision")) {
                    provision = data;
                } else if (StringUtils.equalsIgnoreCase(clazz, "Gross Inventory")) {
                    grossInventory = data;
                } else if (StringUtils.equalsIgnoreCase(clazz, "COGS")) {
                    COGS = data;
                } else if (StringUtils.equalsIgnoreCase(clazz, "Gross_COGS")) {
                    grossCOGS = data;
                    hasGrossCogs = true;
                }

                // DC是没有Gross COGS的, 所以需要补全一下COGS的信息
                if (hasGrossCogs == false) {
                    this.copyBasicInfo(data, grossCOGS, "Gross_COGS");
                }

                this.copyBasicInfo(data, netInv, "Net Inv");
                this.copyBasicInfo(data, COGSConvert, "COGS convert%");
                this.copyBasicInfo(data, spotDINNetInv, "Spot DIN_Net Inv");
                this.copyBasicInfo(data, spotDINGrossInv, "Spot DIN_Gross Inv");
                this.copyBasicInfo(data, yearlyDINNetInv, "Yearly DIN_Net Inv");
                this.copyBasicInfo(data, yearlyDINGrossInv, "Yearly DIN_Gross Inv");
            }

            for (int i = 1; i <= 36; i++) {
                // 计算currentMonth的DIN
                // TODO 当月没有provision, COGS, grossCOGS, 所以算出来的值是不准的, 这边还没测过
                if (currentMonthIndex == i) {
                    // 复制当月数据
                    provision.setCurrentMonth(provision.getMonthValue(i));
                    grossCOGS.setCurrentMonth(grossCOGS.getMonthValue(i));
                    COGS.setCurrentMonth(COGS.getMonthValue(i));

                    // Net Inv = Gross Inventory - Provision
                    netInv.setCurrentMonth(this.subtract(grossInventory.getCurrentMonth(), provision.getCurrentMonth()));
                    COGSConvert.setCurrentMonth(this.divideInPercent(COGS.getCurrentMonth(), grossCOGS.getCurrentMonth()));
                    spotDINNetInv.setCurrentMonth(this.divide(netInv.getCurrentMonth(), this.avgLast3Month(COGS, i, true)).multiply(BigDecimal.valueOf(30)));
                    spotDINGrossInv.setCurrentMonth(this.divide(grossInventory.getCurrentMonth(), this.avgLast3Month(COGS, i, true)).multiply(BigDecimal.valueOf(30)));
                    yearlyDINNetInv.setCurrentMonth(this.divide(this.avgAllLastMonth(netInv, i, true), this.avgAllLastMonth(COGS, i, true)).multiply(BigDecimal.valueOf(30)));
                    yearlyDINGrossInv.setCurrentMonth(this.divide(this.avgAllLastMonth(grossInventory, i, true), this.avgAllLastMonth(COGS, i, true)).multiply(BigDecimal.valueOf(30)));
                }

                if (COGS.isDC()) {
                    grossCOGS.setMonthValue(i, COGS.getMonthValue(i)); //DC的Gross COGS就是COGS
                }

                netInv.setMonthValue(i, this.subtract(grossInventory.getMonthValue(i), provision.getMonthValue(i)));  // Net Inv = Gross Inventory - Provision
                COGSConvert.setMonthValue(i, this.divideInPercent(COGS.getMonthValue(i), grossCOGS.getMonthValue(i)));
                spotDINNetInv.setMonthValue(i, this.divide(netInv.getMonthValue(i), this.avgLast3Month(COGS, i, false)).multiply(BigDecimal.valueOf(30)));
                spotDINGrossInv.setMonthValue(i, this.divide(grossInventory.getMonthValue(i), this.avgLast3Month(COGS, i, false)).multiply(BigDecimal.valueOf(30)));
                yearlyDINNetInv.setMonthValue(i, this.divide(this.avgAllLastMonth(netInv, i, false), this.avgAllLastMonth(COGS, i, false)).multiply(BigDecimal.valueOf(30)));
                yearlyDINGrossInv.setMonthValue(i, this.divide(this.avgAllLastMonth(grossInventory, i, false), this.avgAllLastMonth(COGS, i, false)).multiply(BigDecimal.valueOf(30)));
            }

            if (hasGrossCogs == false && grossCOGS.type() != SliceRawData.ENTITY) {
                resultList.add(grossCOGS);
            }

            resultList.add(netInv);
            if (COGSConvert.type() != SliceRawData.ENTITY && COGSConvert.isPlant()) {
                resultList.add(COGSConvert);
            }
            resultList.add(spotDINNetInv);
            resultList.add(spotDINGrossInv);
            resultList.add(yearlyDINNetInv);
            resultList.add(yearlyDINGrossInv);
        }
        return resultList;
    }

    private void copyBasicInfo(SliceRawData source, SliceRawData target, String clazz) {
        target.setClusterName(source.getClusterName());
        target.setEntityName(source.getEntityName());
        target.setPlantType(source.getPlantType());
        target.setClazz(clazz);
        target.setCurrency(source.getCurrency());
        if (clazz.contains("DIN")) {
            target.setUnitOfMeasure("Days");
        } else if (clazz.equals("Net Inv") || clazz.equals("Gross_COGS") || clazz.equals("Provision")) {
            target.setUnitOfMeasure(source.getUnitOfMeasure());
        }
        target.setCurrencyType(source.getCurrencyType());
    }

    private BigDecimal subtract(Object s1, Object s2) {
        return Utils.parseBigDecimal(s1, BigDecimal.ZERO).subtract(Utils.parseBigDecimal(s2, BigDecimal.ZERO));
    }

    private BigDecimal divide(Object s1, Object s2) {
        BigDecimal d1 = Utils.parseBigDecimal(s1, BigDecimal.ZERO);
        BigDecimal d2 = Utils.parseBigDecimal(s2, BigDecimal.ZERO);

        if (d2.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        } else {
            return d1.divide(d2, 3, RoundingMode.HALF_UP);
        }
    }

    private BigDecimal divideInPercent(Object s1, Object s2) {
        BigDecimal d1 = Utils.parseBigDecimal(s1, BigDecimal.ZERO);
        BigDecimal d2 = Utils.parseBigDecimal(s2, BigDecimal.ZERO);

        if (d2.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        } else {
            return d1.multiply(BigDecimal.valueOf(100)).divide(d2, 1, RoundingMode.HALF_UP);
        }
    }

    /**
     * @param source                数据
     * @param startMonthIndex       开始月份, 往前最多推三天
     * @param replaceStartByCurrent 是否使用当前月替换第一个月
     * @return 平均值
     */
    private BigDecimal avgLast3Month(SliceRawData source, int startMonthIndex, boolean replaceStartByCurrent) {
        BigDecimal total = BigDecimal.ZERO;
        int cnt = 0;
        for (int i = 0; i < 3; i++) {
            if (startMonthIndex < 1) {
                break;
            }

            // 当月替换的时候,
            if (replaceStartByCurrent && i == 0) {
                total = total.add(source.getCurrentMonth());
            } else {
                total = total.add(source.getMonthValue(startMonthIndex));
            }
            cnt++;
            startMonthIndex--;
        }

        return total.divide(BigDecimal.valueOf(cnt), 4, RoundingMode.HALF_UP);
    }

    private BigDecimal avgAllLastMonth(SliceRawData source, int startMonthIndex, boolean replaceStartByCurrent) {
        BigDecimal total = BigDecimal.ZERO;
        int cnt = 0;
        for (int i = 0; i < 12; i++) {
            if (startMonthIndex < 1) {
                break;
            }

            // 当月替换的时候,
            if (replaceStartByCurrent && i == 0) {
                total = total.add(source.getCurrentMonth());
            } else {
                if (source.getMonthValue(startMonthIndex) != null) {
                    total = total.add(source.getMonthValue(startMonthIndex));
                }
            }
            cnt++;
            startMonthIndex--;
        }

        return total.divide(BigDecimal.valueOf(cnt), 4, RoundingMode.HALF_UP);
    }

    /*
     * 1. 将Plant的COGS汇总到Cluster级别和China级别
     * 2. 先将记录中属于PLANT_TYPE=Plant的FAC记录找出来
     * China Total COGS = LV + MV + EE + H&D + China_Other_OPS
     * Cluster COGS = LV + MV + EE + H&D
     */
    private List<SliceRawData> apendPlantCOGSData(List<SliceRawData> rawData) {
        Map<String, List<SliceRawData>> dataMap = new HashMap<>();

        List<SliceRawData> resultList = new ArrayList<>();
        // 根据Entity计算Cluster汇总
        for (SliceRawData data : rawData) {
            // 将Plant的信息筛选出来, 汇总COGS, 做Cluster和China的Gross COGS
            if (data.isPlant() && data.type() == SliceRawData.ENTITY && (data.getClusterName().equalsIgnoreCase("EE") || data.getClusterName().equalsIgnoreCase("H&D") || data.getClusterName().equalsIgnoreCase("LV") || data.getEntityName().equalsIgnoreCase("MV") || data.getEntityName().equalsIgnoreCase("China_Other_OPS") || data.getEntityName().equalsIgnoreCase("SEHK") || data.getEntityName().equalsIgnoreCase("China_DC") || data.getEntityName().equalsIgnoreCase("Trading_SPC"))) {
                List<SliceRawData> tempList = dataMap.computeIfAbsent(data.getClusterName(), k -> new ArrayList<>());
                tempList.add(data);
            } else {
                resultList.add(data);
            }
        }

        SliceRawData china_Gross_COGS = SliceRawData.getInstance("China_Total.Plant.China_Total", "Gross_COGS");
        for (String key : dataMap.keySet()) {
            List<SliceRawData> value = dataMap.get(key);
            String clusterKey = key + ".Plant." + key;

            SliceRawData cluster_Gross_COGS = SliceRawData.getInstance(clusterKey, "Gross_COGS");

            for (SliceRawData data : value) {
                if ("COGS".equalsIgnoreCase(data.getClazz())) {
                    BigDecimal temp;
                    BigDecimal temp1;

                    for (int i = 0; i <= 36; i++) {
                        temp = cluster_Gross_COGS.getMonthValue(i);
                        temp = temp.add(data.getMonthValue(i));
                        cluster_Gross_COGS.setMonthValue(i, temp);

                        temp1 = china_Gross_COGS.getMonthValue(i);
                        temp1 = temp1.add(data.getMonthValue(i));
                        china_Gross_COGS.setMonthValue(i, temp1);
                    }
                    cluster_Gross_COGS.setUnitOfMeasure(data.getUnitOfMeasure());
                    china_Gross_COGS.setUnitOfMeasure(data.getUnitOfMeasure());
                }
                resultList.add(data);
            }

            // 以Cluster Name分类时, 并不是所有的ClusterName都是Cluster, 比如SEHK, 所以这类数据不需要有Gross COGS
            // China_Other_OPS不做Cluster汇总
            if (cluster_Gross_COGS.getClusterName().equalsIgnoreCase("China_Other_OPS") == false) {
                resultList.add(cluster_Gross_COGS);
            }
        }
        resultList.add(china_Gross_COGS);

        return resultList;
    }

    /*
     * 因为Provision和Gross_Inventory 可能会被Projection改变, 所以需要重新计算汇总
     * 1. 将Plant的Provision和Gross_Inventory汇总到Cluster级别和China级别
     * 2. 先将系统中已有的汇总记录全部删掉, 在将记录中属于PLANT_TYPE=Plant的FAC记录找出来
     * China Total COGS = LV + MV + EE + H&D + China_Other_OPS
     * Cluster COGS = LV + MV + EE + H&D
     */
    private List<SliceRawData> apendPlantSummaryData(List<SliceRawData> rawDataList) {
        List<SliceRawData> resultList = new ArrayList<>();
        Map<String, List<SliceRawData>> dataMap = new HashMap<>();

        // 删除已有的Provision和Gross Inventory汇总记录
        rawDataList.removeIf(raw -> raw.isPlant() && raw.type() != SliceRawData.ENTITY && ("Provision".equals(raw.getClazz()) || "Gross Inventory".equals(raw.getClazz())));

        // 根据Entity计算Cluster汇总
        // 将所有Plant Entity的数据取出来
        // SPC 没有Cluster, 不用汇总
        for (SliceRawData data : rawDataList) {
            if (data.isPlant() && data.type() == SliceRawData.ENTITY) {
                List<SliceRawData> tempList = dataMap.computeIfAbsent(data.getClusterName(), k -> new ArrayList<>());
                tempList.add(data);
            } else {
                resultList.add(data);
            }
        }

        String chinaKey = "China_Total.Plant.China_Total";
        SliceRawData china_Provision = SliceRawData.getInstance(chinaKey, "Provision");
        SliceRawData china_Gross_Inventory = SliceRawData.getInstance(chinaKey, "Gross Inventory");

        for (String key : dataMap.keySet()) {
            List<SliceRawData> value = dataMap.get(key);
            String clusterKey = key + ".Plant." + key;

            SliceRawData cluster_Provision = SliceRawData.getInstance(clusterKey, "Provision");
            SliceRawData cluster_Gross_Inventory = SliceRawData.getInstance(clusterKey, "Gross Inventory");

            for (SliceRawData data : value) {
                for (int i = 0; i <= 36; i++) {
                    switch (data.getClazz().toUpperCase()) {
                        case "PROVISION":
                            BigDecimal temp = cluster_Provision.getMonthValue(i);
                            temp = temp.add(data.getMonthValue(i));
                            cluster_Provision.setMonthValue(i, temp);

                            temp = china_Provision.getMonthValue(i);
                            temp = temp.add(data.getMonthValue(i));
                            china_Provision.setMonthValue(i, temp);
                            break;
                        case "GROSS INVENTORY":
                            temp = cluster_Gross_Inventory.getMonthValue(i);
                            temp = temp.add(data.getMonthValue(i));
                            cluster_Gross_Inventory.setMonthValue(i, temp);

                            // 不将China_DC汇总与China Total, 后面会再补上去
                            if ("China_DC".equals(data.getEntityName()) == false) {
                                temp = china_Gross_Inventory.getMonthValue(i);
                                temp = temp.add(data.getMonthValue(i));
                                china_Gross_Inventory.setMonthValue(i, temp);
                            }

                            break;
                    }
                }

                resultList.add(data);

                // 添加UnitOfMeasure字段
                if (data.getClazz().equalsIgnoreCase("PROVISION") && StringUtils.isBlank(cluster_Provision.getUnitOfMeasure())) {
                    cluster_Provision.setUnitOfMeasure(data.getUnitOfMeasure());
                    cluster_Gross_Inventory.setUnitOfMeasure(data.getUnitOfMeasure());

                    china_Provision.setUnitOfMeasure(data.getUnitOfMeasure());
                    china_Gross_Inventory.setUnitOfMeasure(data.getUnitOfMeasure());
                }
            }

            // SPC只做China Total汇总, 不做Cluster 汇总
            if (cluster_Provision.getEntityName().equals("SPC") == false) {
                resultList.add(cluster_Provision);
                resultList.add(cluster_Gross_Inventory);
            }
        }

        resultList.add(china_Provision);
        resultList.add(china_Gross_Inventory);

        return resultList;
    }

    // SE China Provision是RMB汇率
    private List<SliceRawData> calcChinaTotalProvision(List<SliceRawData> rawDataList, String version, String toCurrency) {
        // 先获取SE_China Provision的数据
        Map<String, Object> seChinaProvision = monthlySliceReportDao.querySEChinaProvision(version);
        if (seChinaProvision == null) {
            seChinaProvision = new HashMap<>();
        }
        // 从rawDataList中找到China Total, DC+PLANT, Provision的数据
        SliceRawData chinaTotal = null;
        for (SliceRawData sliceRawData : rawDataList) {
            if (sliceRawData.type() == SliceRawData.CHINA && sliceRawData.getClazz().equalsIgnoreCase("Provision")) {
                chinaTotal = sliceRawData;
                break;
            }
        }

        String startMonth = (Utils.parseInt(StringUtils.substring(version, 0, 4)) - 1) + "01";
        String fromCurrency = (String) seChinaProvision.getOrDefault("CURRENCY", "CNY");
        String currencyType = (String) seChinaProvision.getOrDefault("CURRENCY_TYPE", "INV");
        if (chinaTotal != null) {
            for (int i = 1; i <= 24; i++) {
                String month = Utils.addMonth(startMonth, i - 1); // month01,02...转换为202001,202002...
                BigDecimal currency = this.getCurrency(version, currencyType, month, fromCurrency, toCurrency);

                String key = i < 10 ? "MONTH0" + i : "MONTH" + i;

                BigDecimal currentValue = chinaTotal.getMonthValue(i);
                BigDecimal seValue = Utils.parseBigDecimal(seChinaProvision.get(key));
                chinaTotal.setMonthValue(i, currentValue.subtract(seValue.divide(currency, 4, RoundingMode.HALF_UP)));
            }

        }
        return rawDataList;
    }

    private List<SliceRawData> apendDCSummaryData(List<SliceRawData> rawDataList) {
        List<SliceRawData> resultList = new ArrayList<>();
        Map<String, List<SliceRawData>> dataMap = new HashMap<>();

        // 根据Entity计算Cluster汇总
        // 将所有DC Entity的数据取出来
        for (SliceRawData data : rawDataList) {
            if (data.isDC() && data.type() == SliceRawData.ENTITY) {
                List<SliceRawData> tempList = dataMap.computeIfAbsent(data.getClusterName(), k -> new ArrayList<>());
                tempList.add(data);
            } else {
                resultList.add(data);
            }
        }

        String chinaKey = "China_Total.DC.China_Total";
        SliceRawData china_Provision = SliceRawData.getInstance(chinaKey, "Provision");
        SliceRawData china_SOH_FG = SliceRawData.getInstance(chinaKey, "SOH_FG");
        SliceRawData china_Gross_Inventory = SliceRawData.getInstance(chinaKey, "Gross Inventory");
        SliceRawData china_COGS = SliceRawData.getInstance(chinaKey, "COGS");

        for (String key : dataMap.keySet()) {
            List<SliceRawData> value = dataMap.get(key);
            String clusterKey = key + ".DC." + key;

            SliceRawData cluster_Provision = SliceRawData.getInstance(clusterKey, "Provision");
            SliceRawData cluster_SOH_FG = SliceRawData.getInstance(clusterKey, "SOH_FG");
            SliceRawData cluster_Gross_Inventory = SliceRawData.getInstance(clusterKey, "Gross Inventory");
            SliceRawData cluster_COGS = SliceRawData.getInstance(clusterKey, "COGS");

            for (SliceRawData data : value) {
                for (int i = 0; i <= 36; i++) {
                    switch (data.getClazz().toUpperCase()) {
                        case "PROVISION":
                            BigDecimal temp = cluster_Provision.getMonthValue(i);
                            temp = temp.add(data.getMonthValue(i));
                            cluster_Provision.setMonthValue(i, temp);

                            if (data.getEntityName().equalsIgnoreCase("Trading_DCHK") == false) {
                                temp = china_Provision.getMonthValue(i);
                                temp = temp.add(data.getMonthValue(i));
                                china_Provision.setMonthValue(i, temp);
                            }
                            break;
                        case "SOH_FG":
                            temp = cluster_SOH_FG.getMonthValue(i);
                            temp = temp.add(data.getMonthValue(i));
                            cluster_SOH_FG.setMonthValue(i, temp);

                            if (data.getEntityName().equalsIgnoreCase("Trading_DCHK") == false) {
                                temp = china_SOH_FG.getMonthValue(i);
                                temp = temp.add(data.getMonthValue(i));
                                china_SOH_FG.setMonthValue(i, temp);
                            }
                            break;
                        case "GROSS INVENTORY":
                            temp = cluster_Gross_Inventory.getMonthValue(i);
                            temp = temp.add(data.getMonthValue(i));
                            cluster_Gross_Inventory.setMonthValue(i, temp);

                            if (data.getEntityName().equalsIgnoreCase("Trading_DCHK") == false) {
                                temp = china_Gross_Inventory.getMonthValue(i);
                                temp = temp.add(data.getMonthValue(i));
                                china_Gross_Inventory.setMonthValue(i, temp);
                            }
                            break;
                        case "COGS":
                            temp = cluster_COGS.getMonthValue(i);
                            temp = temp.add(data.getMonthValue(i));
                            cluster_COGS.setMonthValue(i, temp);

                            if (data.getEntityName().equalsIgnoreCase("Trading_DCHK") == false) {
                                temp = china_COGS.getMonthValue(i);
                                temp = temp.add(data.getMonthValue(i));
                                china_COGS.setMonthValue(i, temp);
                            }
                            break;
                    }
                }

                resultList.add(data);

                // 添加UnitOfMeasure字段
                if (data.getClazz().equalsIgnoreCase("PROVISION") && StringUtils.isBlank(cluster_Provision.getUnitOfMeasure())) {
                    cluster_Provision.setUnitOfMeasure(data.getUnitOfMeasure());
                    cluster_SOH_FG.setUnitOfMeasure(data.getUnitOfMeasure());
                    cluster_Gross_Inventory.setUnitOfMeasure(data.getUnitOfMeasure());
                    cluster_COGS.setUnitOfMeasure(data.getUnitOfMeasure());

                    china_Provision.setUnitOfMeasure(data.getUnitOfMeasure());
                    china_SOH_FG.setUnitOfMeasure(data.getUnitOfMeasure());
                    china_Gross_Inventory.setUnitOfMeasure(data.getUnitOfMeasure());
                    china_COGS.setUnitOfMeasure(data.getUnitOfMeasure());
                }
            }

            resultList.add(cluster_Provision);
            resultList.add(cluster_SOH_FG);
            resultList.add(cluster_Gross_Inventory);
            resultList.add(cluster_COGS);
        }

        resultList.add(china_Provision);
        resultList.add(china_SOH_FG);
        resultList.add(china_Gross_Inventory);
        resultList.add(china_COGS);

        return resultList;
    }

    // TODO
    private List<SliceRawData> apendChinaTotal(List<SliceRawData> rawDataList) {
        String[] types = new String[]{"PROVISION", "GROSS INVENTORY"};

        for (String type : types) {
            SliceRawData chinaPlantDCTotal = null;
            SliceRawData chinaDCTotal = null;
            // 挑选出China Total那行
            for (SliceRawData data : rawDataList) {
                if ("China_Total".equals(data.getClusterName()) && "China_Total".equals(data.getEntityName()) && data.isDC() && type.equals(data.getClazz().toUpperCase())) {
                    chinaDCTotal = data;
                }

                if ("China_Total".equals(data.getClusterName()) && "China_Total".equals(data.getEntityName()) && data.isPlant() && type.equals(data.getClazz().toUpperCase())) {
                    chinaPlantDCTotal = data;
                }
            }

            if (chinaDCTotal != null && chinaPlantDCTotal != null) {
                for (int i = 0; i <= 36; i++) {
                    BigDecimal temp1 = chinaPlantDCTotal.getMonthValue(i);
                    temp1 = temp1.add(chinaDCTotal.getMonthValue(i));
                    chinaPlantDCTotal.setMonthValue(i, temp1);
                }
            }
        }

        return rawDataList;
    }

    /**
     * 将INV的汇率转为CNY
     *
     * @param version     汇率版本
     * @param rawDataList 需要转换的数据表
     * @return 转换完成的汇率表
     */
    private List<SliceRawData> calcINVCurrency(String version, List<SliceRawData> rawDataList) {
        List<SliceRawData> resultList = new ArrayList<>();

        String startMonth = (Utils.parseInt(StringUtils.substring(version, 0, 4)) - 1) + "01";
        for (SliceRawData data : rawDataList) {
            String currencyType = data.getCurrencyType();
            String fromCurrency = data.getCurrency();

            // 计算calcINVCurrency的时候, 还没有Current Month
            if ("INV".equalsIgnoreCase(currencyType)) {
                for (int i = 1; i <= 36; i++) {
                    String month = Utils.addMonth(startMonth, i - 1); // month01,02...转换为202001,202002...
                    BigDecimal toCNYCurrency = this.getCurrency(version, currencyType, month, fromCurrency, "CNY");
                    data.setCurrency("CNY");
                    data.convertCurrency(i, toCNYCurrency);
                }
            }
            resultList.add(data);
        }
        return resultList;
    }

    public void copyActualToProjection(String version, List<SliceRawData> rawDataList) {
        int lastestActualMonthIndex = Utils.parseInt(version.substring(4, 6)) + 11; // 最后一个Actual月的Index
        // Actual最后一天的数据复制到Projection的每一天
        for (SliceRawData data : rawDataList) {
            BigDecimal lastMonthValue = data.getMonthValue(lastestActualMonthIndex);
            for (int i = lastestActualMonthIndex + 1; i <= 36; i++) {
                data.setMonthValue(i, lastMonthValue);
            }
        }
    }

    public void copyActualCOGSToProjection(String version, List<SliceRawData> rawDataList) {
        int lastestActualMonthIndex = Utils.parseInt(version.substring(4, 6)) + 11; // 最后一个Actual月的Index
        // Actual最后一天的数据复制到Projection的每一天
        for (SliceRawData data : rawDataList) {
            if (data.getClazz().equalsIgnoreCase("COGS")) {
                BigDecimal lastMonthValue = data.getMonthValue(lastestActualMonthIndex);
                for (int i = lastestActualMonthIndex + 1; i <= 36; i++) {
                    data.setMonthValue(i, lastMonthValue);
                }
            }
        }
    }

    /**
     * 计算Result的汇率
     *
     * @param version     版本, 获取汇率的时候需要
     * @param rawDataList 数据
     * @param toCurrency  需要转换的汇率
     * @return 转换后的数据
     */
    private List<SliceRawData> calcCurrency(String version, List<SliceRawData> rawDataList, String toCurrency) throws Exception {
        List<SliceRawData> resultList = new ArrayList<>();

        String startMonth = (Utils.parseInt(StringUtils.substring(version, 0, 4)) - 1) + "01";
        for (SliceRawData data : rawDataList) {
            String currencyType = data.getCurrencyType();
            String fromCurrency = data.getCurrency();

            // i = 0 , key = current month
            if ("INV".equalsIgnoreCase(currencyType)) {
                for (int i = 0; i <= 36; i++) {
                    String month;
                    if (i == 0) {
                        month = version; // 如果是0, 那么取当前月的数据, 当前月就是version
                    } else {
                        month = Utils.addMonth(startMonth, i - 1); // month01,02...转换为202001,202002...
                    }
                    BigDecimal exchangeRate = this.getCurrency(version, currencyType, month, fromCurrency, toCurrency);
                    data.convertCurrency(i, exchangeRate);
                }
            } else if ("COGS".equalsIgnoreCase(currencyType)) {
                BigDecimal totalOrgValue = BigDecimal.ZERO; // 包含当月的, 所有未转换汇率总和
                BigDecimal totalCalcValue = BigDecimal.ZERO; // 不包含当月的所有转换过汇率的数据总和

                for (int i = 0; i <= 36; i++) {
                    String month;
                    if (i == 0) {
                        month = version; // 如果是0, 那么取当前月的数据, 当前月就是version
                    } else if ("Y".equals(data.getProjection())) { // Projection的汇率也是从VERSION开始算的
                        month = version;
                    } else {
                        month = Utils.addMonth(startMonth, i - 1);
                    }
                    BigDecimal currentOrgValue = data.getMonthValue(i);
                    if (currentOrgValue != null && currentOrgValue.compareTo(BigDecimal.ZERO) != 0) {
                        totalOrgValue = totalOrgValue.add(currentOrgValue);

                        BigDecimal exchangeRate = this.getCurrency(version, currencyType, month, fromCurrency, toCurrency);
                        if (exchangeRate == null || BigDecimal.ZERO.compareTo(exchangeRate) == 0) {
                            throw new ForbiddenException("未找到VERSION=" + version + ", COGS从" + fromCurrency + "到" + toCurrency + "的汇率信息. 请在维护后再进行查询.");
                        }
                        BigDecimal totalCalcValue0 = totalOrgValue.divide(exchangeRate, 4, RoundingMode.HALF_UP);

                        BigDecimal monthValue = totalCalcValue0.subtract(totalCalcValue);

                        data.setMonthValue(i, monthValue);
                        totalCalcValue = totalCalcValue.add(monthValue);
                    }

                    // 遇到12月的时候将汇总归零
                    if (i != 0 && i % 12 == 0) {
                        totalOrgValue = BigDecimal.ZERO;
                        totalCalcValue = BigDecimal.ZERO;
                    }
                }
            }
            resultList.add(data);
        }

        // 将Actual COGS 复制到Projection的每一天, 因为COGS重新计算了
        return resultList;
    }

    Map<String, BigDecimal> cacheMap = new HashMap<>();
    // version >> type.from.to >> month >> exchangeRate
    Map<String, Map<String, Map<String, BigDecimal>>> currencyMap = new HashMap<>();

    public BigDecimal getCurrency(String version, String type, String month, String from, String to) {
        Map<String, Map<String, BigDecimal>> versionCurrency = currencyMap.get(version);
        if (versionCurrency == null) {
            versionCurrency = new HashMap<>();
            List<SliceCurrency> currencyList = monthlySliceReportDao.getCurrency(version);

            // 将汇率分类
            // 先计算用户上传的汇率
            for (SliceCurrency currency : currencyList) {
                String currencyKey = currency.getCurrencyType() + "." + currency.getCurrencyFrom() + "." + currency.getCurrencyTo();
                Map<String, BigDecimal> map = versionCurrency.computeIfAbsent(currencyKey, k -> new HashMap<>());
                map.put(currency.getMonth(), currency.getExchangeRate());
            }

            // 然后计算没有上传的汇率
            for (SliceCurrency currency : currencyList) {
                String currencyKey = currency.getCurrencyType() + "." + currency.getCurrencyTo() + "." + currency.getCurrencyFrom();
                if (versionCurrency.containsKey(currencyKey) == false || versionCurrency.get(currencyKey).containsKey(currency.getMonth()) == false) {
                    Map<String, BigDecimal> map = versionCurrency.computeIfAbsent(currencyKey, k -> new HashMap<>());
                    if (currency.getExchangeRate().compareTo(BigDecimal.ZERO) == 0) {
                        map.put(currency.getMonth(), BigDecimal.ZERO);
                    } else {
                        map.put(currency.getMonth(), BigDecimal.ONE.divide(currency.getExchangeRate(), 9, RoundingMode.HALF_UP));
                    }
                }
            }

            currencyMap.put(version, versionCurrency);
        }

        if (StringUtils.equalsIgnoreCase(from, to)) {
            return BigDecimal.ONE;
        }

        String cacheKey = version + "." + type + "." + month + "." + from + "." + to;
        BigDecimal cacheRate = cacheMap.get(cacheKey);
        if (cacheRate != null) {
            return cacheRate;
        } else {
            String cogsKey = "COGS." + from + "." + to;
            String invKey = "INV." + from + "." + to;
            Map<String, BigDecimal> rateMapCOGS = versionCurrency.get(cogsKey);
            Map<String, BigDecimal> rateMapINV = versionCurrency.get(invKey);

            // COGS, 如果COGS MONTH在未来, 则使用INV汇率
            BigDecimal rate = BigDecimal.ZERO;

            // 如果COGS表中有输入月的汇率, 以COGS表中汇率为准, 如果没有, 则把COGS当做INV来计算
            if ("COGS".equalsIgnoreCase(type)) {
                if (rateMapCOGS == null) {
                    return BigDecimal.ZERO;
                }
                if (rateMapCOGS.containsKey(month)) {
                    rate = rateMapCOGS.get(month);
                } else {
                    type = "INV";
                }
            }

            if ("INV".equalsIgnoreCase(type)) {
                // 取汇率的最大最小区间
                String minMonth = "999999";
                String maxMonth = "000000";

                if (rateMapINV == null) {
                    return BigDecimal.ZERO;
                }

                for (String key : rateMapINV.keySet()) {
                    if (key.compareTo(maxMonth) > 0) {
                        maxMonth = key;
                    } else if (key.compareTo(minMonth) < 0) {
                        minMonth = key;
                    }
                }
                // 如果输入月比汇率表中最大的月还大, 那直接输出最大月的汇率
                if (month.compareTo(maxMonth) > 0) {
                    rate = rateMapINV.get(maxMonth);
                } else if (month.compareTo(minMonth) < 0) {
                    // 如果输入月比汇率表中最小的月还小, 那直接输出最小月的汇率
                    rate = rateMapINV.get(minMonth);
                } else {
                    // 在区间内, 输出汇率表的汇率
                    rate = rateMapINV.get(month);
                }
            }
            cacheMap.put(cacheKey, rate);
            return rate;
        }
    }

    private List<SliceRawData> filterResult(List<SliceRawData> rawDataList, SliceFilters filters) {
        List<SliceRawData> resultList = new ArrayList<>();
        for (SliceRawData data : rawDataList) {
            if (filters.match(data)) {
                resultList.add(data);
            }
        }
        return resultList;
    }

    private void summaryResult(List<SliceRawData> rawDataList) {
        SliceRawData summary = new SliceRawData();
        summary.setClusterName("Total");
        for (SliceRawData data : rawDataList) {
            for (int i = 0; i <= 36; i++) {
                BigDecimal value = summary.getMonthValue(i);
                value = value.add(data.getMonthValue(i));
                summary.setMonthValue(i, value);
            }
        }
        rawDataList.add(summary);
    }

    /**
     * 重命名Plant Type, 当Cluster Name和Entity Name都是China Total并且Plant Type是Plant的时候, Plant Type设置为Plant+DC
     *
     * @param rawDataList 输入/输出
     */
    private void renamePlantType(List<SliceRawData> rawDataList) {
        for (SliceRawData data : rawDataList) {
            if (data.getClusterName().equals("China_Total") && data.getEntityName().equals("China_Total") && data.getPlantType().equals("Plant")) {
                data.setPlantType("Plant+DC");
            }
        }
    }

    private void sort(List<SliceRawData> data) {
        data.sort((e1, e2) -> this.sortOrder(e1.getClusterName()).compareTo(this.sortOrder(e2.getClusterName())));
    }

    private String sortOrder(String key) {
        return switch (key) {
            case "China_Total" -> "0";
            case "LV" -> "1";
            case "MV" -> "2";
            case "EE" -> "3";
            case "H&D" -> "31";
            case "Trading" -> "4";
            case "CNDC_FS" -> "5";
            case "CNDC_FS_PS" -> "6";
            case "CNDC_PEC" -> "7";
            case "CNDC_Others" -> "8";
            case "China_Other_OPS" -> "9";
            case "China_DC" -> "91";
            default -> key;
        };
    }

    private Map<String, Object> queryReport1Columns(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        String version = (String) parameterMap.get("version");
        String start = (Utils.parseInt(StringUtils.substring(version, 0, 4)) - 1) + "01";

        int actual = 0;
        int demand = 0;

        List<Map<String, String>> columns = new ArrayList<>();
        for (int i = 1; i <= 36; i++) {
            if (start.compareTo(version) < 0) {
                actual++;
            } else {
                demand++;
            }

            Map<String, String> map = new HashMap<>();
            map.put("data", "MONTH" + (i < 10 ? "0" + i : i));
            map.put("title", Utils.convertMonth(start));
            start = Utils.addMonth(start);
            columns.add(map);
        }

        resultMap.put("columns", columns);
        resultMap.put("columnActual", actual);
        resultMap.put("columnDemand", demand);
        return resultMap;
    }

    @Override
    public Response queryReport2(String userid, Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        parameterMap.put("isAdmin", monthlySliceReportDao.queryPageAdmin(userid, MonthlySliceReportController.PARENT_CODE) > 0);
        page.setTotal(monthlySliceReportDao.queryReport2Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(monthlySliceReportDao.queryReport2(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public Response saveReport2(String userid, Map<String, Object> parameterMap) {
        String currentVersion = new SimpleDateFormat("yyyyMM").format(new Date());
        String version = (String) parameterMap.get("version");
        if (currentVersion.equals(version) == false) {
            Message message = new Message();
            message.setShowClose(true);
            message.setMessage("You cannot modify historical data in " + version);
            message.setType("error");
            return response.setBody(message);
        }


        scpTableHelper.setExcludeColumn(new ArrayList<>() {{
            add("CREATE_BY");
            add("UPDATE_TIME");
        }});

        boolean isAdmin = monthlySliceReportDao.queryPageAdmin(userid, MonthlySliceReportController.PARENT_CODE) > 0;

        scpTableHelper.setScpTableInsertHandler((headers, creates) -> monthlySliceReportDao.createReport2ByTable(headers, creates, userid, currentVersion));
        scpTableHelper.setScpTableDeleteHandler(deletes -> monthlySliceReportDao.deleteReport2ByTable(deletes, userid, currentVersion, isAdmin));
        scpTableHelper.setScpTableUpdateHandler((pk, updates) -> monthlySliceReportDao.updateReport2ByTable(pk, updates, userid, currentVersion, isAdmin));
        scpTableHelper.setWarningMessage("You cannot modify the data doesn't belong to you");
        Message message = scpTableHelper.execCRUD(parameterMap);
        return response.setBody(message);
    }

    @Override
    public Response syncProjection(Map<String, Object> parameterMap, String userid) {
        SimpleDateFormat dayFormat = new SimpleDateFormat("yyyyMM");
        Calendar cale = Calendar.getInstance();
        String thisMonth = dayFormat.format(cale.getTime());
        cale.set(Calendar.DAY_OF_MONTH, 0);
        String lastMonth = dayFormat.format(cale.getTime());
        monthlySliceReportDao.syncProjection(lastMonth, thisMonth, userid);
        return response;
    }

    @Override
    public void downloadDinReport(Map<String, Object> parameterMap, HttpServletResponse response) {
        String version = (String) parameterMap.get("version");
        parameterMap.put("variable", Utils.addMonth(version, -1));
        List<String> spotList = new ArrayList<>();
        spotList.add(version);
        spotList.add(Utils.addMonth(version, -1));
        spotList.add(Utils.addMonth(version, -2));
        parameterMap.put("spotList", "'" + StringUtils.join(spotList, "','") + "'");

        List<String> yearlyList = new ArrayList<>();
        yearlyList.add(version);
        yearlyList.add(Utils.addMonth(version, -1));
        yearlyList.add(Utils.addMonth(version, -2));
        yearlyList.add(Utils.addMonth(version, -3));
        yearlyList.add(Utils.addMonth(version, -4));
        yearlyList.add(Utils.addMonth(version, -5));
        yearlyList.add(Utils.addMonth(version, -6));
        yearlyList.add(Utils.addMonth(version, -7));
        yearlyList.add(Utils.addMonth(version, -8));
        yearlyList.add(Utils.addMonth(version, -9));
        yearlyList.add(Utils.addMonth(version, -10));
        yearlyList.add(Utils.addMonth(version, -11));
        parameterMap.put("yearlyList", "'" + StringUtils.join(yearlyList, "','") + "'");

        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "slice_din_" + version + "_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.inventory.dao.IMonthlySliceReportDao.downloadDinReport", parameterMap);
    }

    private void generateFilter(Map<String, Object> parameterMap) {
        // 生成筛选条件
        Object category = parameterMap.get("filterList");
        if (category instanceof JSONArray) {
            JSONArray categoryArray = (JSONArray) parameterMap.get("filterList");

            Map<String, List<String>> filterMap = new HashMap<>();

            for (Object subObj : categoryArray) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = subArray.getString(0);

                String value = subArray.getString(1);
                String key = Utils.randomStr(8);

                // projection 没有CLUSTER_NAME字段, 所以不参与筛选
                if (Utils.hasInjectionAttack(columnName) || "CLUSTER_NAME".equals(columnName)) {
                    continue;
                }

                List<String> fl = filterMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                fl.add("#{" + key + ",jdbcType=VARCHAR}");
                parameterMap.put(key, value);
            }

            List<String> filterList = new ArrayList<>();

            for (String key : filterMap.keySet()) {
                List<String> fl = filterMap.get(key);
                if ("ENTITY_NAME".equalsIgnoreCase(key)) {
                    key = "ENTITY";
                }
                filterList.add("t." + key + " in (" + StringUtils.join(fl, ",") + ")");
            }

            parameterMap.put("filters", StringUtils.join(filterList, " and "));
        }
    }

    private List<Map<String, Object>> expandRawData(String version, SliceRawData rawData, Map<String, Object> targetMap) {
        List<Map<String, Object>> result = new ArrayList<>();
        String startMonth = (Utils.parseInt(StringUtils.substring(version, 0, 4)) - 1) + "01";
        for (int i = 1; i <= 36; i++) {
            String month = Utils.addMonth(startMonth, i - 1);
            Map<String, Object> map = new HashMap<>();
            map.put("VERSION", version);
            map.put("CLUSTER_NAME", rawData.getClusterName());
            map.put("ENTITY_NAME", rawData.getEntityName());
            map.put("PLANT_TYPE", rawData.getPlantType());
            map.put("CLASS", rawData.getClazz());
            map.put("UNIT_OF_MEASURE", rawData.getUnitOfMeasure());
            map.put("CURRENCY_FROM", "CNY");
            map.put("TO_EUR_CURRENCY", this.getCurrency(version, rawData.getCurrencyType(), month, "CNY", "EUR"));
            map.put("TO_HKD_CURRENCY", this.getCurrency(version, rawData.getCurrencyType(), month, "CNY", "HKD"));
            map.put("CURRENCY_TYPE", rawData.getCurrencyType());
            map.put("MONTH_YEAR", month);
            map.put("PROJECTION_VAL", rawData.getMonthValue(i));
            map.put("ACTUAL_VAL", rawData.getCurrentMonth());

            String key = rawData.getPlantType() + "#" + rawData.getEntityName() + "#" + rawData.getClazz() + "#" + month;
            map.put("TARGET_VAL", targetMap.get(key));
            result.add(map);
        }
        return result;
    }
}
