package com.scp.inventory.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.scp.customer.dao.IOTDMDao;
import com.scp.inventory.bean.DistributorInventoryReport2Result;
import com.scp.inventory.dao.IDistributorInventoryDao;
import com.scp.inventory.service.IDistributorInventoryService;
import com.scp.master.dao.IMasterDataSummaryDao;
import com.scp.master.service.IMasterDataSummaryService;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

@Service
@Scope("prototype")
@Transactional
public class DistributorInventoryServiceImpl extends ServiceHelper implements IDistributorInventoryService {

    @Resource
    private Response response;

    @Resource
    private IDistributorInventoryDao distributorInventoryDao;

    @Resource
    private IOTDMDao otdmDataDao;

    @Resource
    private ExcelTemplate excelTemplate;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage() {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cascader", Utils.parseCascader(distributorInventoryDao.queryCascader()));
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        page.setTotal(distributorInventoryDao.queryReport1Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(distributorInventoryDao.queryReport1(parameterMap));
        }

        return response.setBody(page);
    }

    @Override
    public void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        String fileName = "distributor_data_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.inventory.dao.IDistributorInventoryDao.queryReport1", parameterMap);
    }

    @Override
    public Response queryReport1Details(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateCascaderFilter(parameterMap);

        page.setTotal(distributorInventoryDao.queryReport1DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(distributorInventoryDao.queryReport1Details(parameterMap));
        }

        return response.setBody(page);
    }

    @Override
    public void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        String fileName = "details_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.inventory.dao.IDistributorInventoryDao.queryReport1Details", parameterMap);
    }
    @Override
    @SuppressWarnings("unchecked")
    public Response queryReport2(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);
        List<String> xAxis;

        DistributorInventoryReport2Result result = new DistributorInventoryReport2Result();
        List<Map<String, Object>> dataList = distributorInventoryDao.queryReport2(parameterMap);
        Map<String, Map<String, BigDecimal>> dataMap = new TreeMap<>();

        for (Map<String, Object> map : dataList) {
            String key =map.get("xAxis").toString();
            String key2 = (String) map.get("RANGE");
            Map<String, BigDecimal> submap = dataMap.computeIfAbsent(key, k -> new HashMap<>());
            submap.put(key2, Utils.parseBigDecimal(map.get("CNT")));
        }
        xAxis = new ArrayList<>(dataMap.keySet());
        for (String x : xAxis) {
            result.setxAxis(xAxis);
            result.setYAxis(dataMap.get(x));
        }
        return response.setBody(result);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        String report3ResultType = (String) parameterMap.get("report3ResultType");
        String report3ValueColumn = "NVL(SUM(SALES_VALUE), 0)";
        if ("SALES_VALUE".equalsIgnoreCase(report3ResultType)) {
            report3ValueColumn = "NVL(SUM(SALES_VALUE), 0)";
        } else if ("INVENTORY_VALUE".equalsIgnoreCase(report3ResultType)) {
            report3ValueColumn = "NVL(SUM(INVENTORY_VALUE), 0)";
        } else if ("SPOT_DIN".equalsIgnoreCase(report3ResultType)) {
            report3ValueColumn = "INVENTORY_VALUE/((SUM(SALES_VALUE) OVER ( ORDER BY MONTH RANGE BETWEEN INTERVAL '2' MONTH PRECEDING AND CURRENT ROW )) / 3) *30";
        }
        parameterMap.put("report3ValueColumn", report3ValueColumn);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        List<LinkedHashMap<String, Object>> dataList = distributorInventoryDao.queryReport3(parameterMap);

        page.setData(dataList);
        page.setTotal(dataList.size());
        return response.setBody(page);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Columns(Map<String, Object> parameterMap) {
        return response.setBody(distributorInventoryDao.queryReport3Columns(parameterMap));
    }

    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public void downloadReport3(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateCascaderFilter(parameterMap);

        this.generateValueColumn(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
      /*  String column1 = this.getColumnName(parameterMap.get("column1"));
        String column2 = this.getColumnName(parameterMap.get("column2"));
        parameterMap.put("column1", column1);
        parameterMap.put("column2", column2);*/
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "clo_lt_evolution_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.customer.dao.ICloLtEvolutionDao.queryReport3", parameterMap);
    }


    private void generateValueColumn(Map<String, Object> parameterMap) {
        String type = (String) parameterMap.get("type");
        String calcType = (String) parameterMap.get("calcType");
        switch (type) {
            case "Selling Value" -> parameterMap.put("costColumn", " * AVG_SELLING_PRICE_RMB");
            case "Cost Value" -> parameterMap.put("costColumn", " * UNIT_COST");
            case "Qty" -> parameterMap.put("costColumn", " ");
        }

        switch (calcType) {
            case "Sum" -> parameterMap.put("calcType", "SUM");
            case "Min" -> parameterMap.put("calcType", "MIN");
            case "Max" -> parameterMap.put("calcType", "MAX");
            case "Avg" -> parameterMap.put("calcType", "AVG");
            case "Std" -> parameterMap.put("calcType", "STDDEV");
        }

    }

    /**
     * 生成cascader filter
     *
     * @param parameterMap 参数map
     */
    private void generateCascaderFilter(Map<String, Object> parameterMap) {
        this.generateCascaderFilterSQL(parameterMap);
        // categroy
        JSONArray categroyArray = (JSONArray) parameterMap.get("categroy");
        if (categroyArray == null || categroyArray.isEmpty()) {
            List<String> categroyDefault = new ArrayList<>();
            categroyDefault.add("ENTITY");
            parameterMap.put("categroy", categroyDefault);
        }
    }




}
