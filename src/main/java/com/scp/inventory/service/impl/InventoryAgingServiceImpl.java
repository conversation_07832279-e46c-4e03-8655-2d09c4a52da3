package com.scp.inventory.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.scp.inventory.dao.IInventoryAgingDao;
import com.scp.inventory.service.IInventoryAgingService;
import com.adm.system.bean.CascaderBean;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;

import java.math.BigDecimal;
import java.util.*;

@Service("inventoryAgingService")
@Scope("prototype")
@Transactional
public class InventoryAgingServiceImpl extends ServiceHelper implements IInventoryAgingService {

    @Resource
    private IInventoryAgingDao inventoryAgingDao;

    @Resource
    private Response response;

    @Resource
    private ExcelTemplate excelTemplate;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage() {
        Map<String, Object> resultMap = new HashMap<>();
        List<CascaderBean> resultList = new ArrayList<>();
        List<Map<String, String>> dataList = inventoryAgingDao.queryAgingCascader();

        Map<String, List<Map<String, String>>> tempMap = new LinkedHashMap<>();

        for (Map<String, String> map : dataList) {
            List<Map<String, String>> list = tempMap.computeIfAbsent(map.get("CATEGORY"), key -> new ArrayList<>());
            list.add(map);
        }

        for (String key : tempMap.keySet()) {
            CascaderBean bean = new CascaderBean();
            resultList.add(bean);
            bean.setLabel(key);
            bean.setValue(key);
            for (Map<String, String> map : tempMap.get(key)) {
                CascaderBean subBean = new CascaderBean();
                subBean.setLabel(map.get("NAME"));
                subBean.setValue(subBean.getLabel());
                bean.addChild(subBean);
            }
        }

        resultMap.put("cascader", resultList);
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        return response.setBody(inventoryAgingDao.queryReport1(parameterMap));
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1Details(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateCascaderFilter(parameterMap);
        page.setTotal(inventoryAgingDao.queryReport1DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(inventoryAgingDao.queryReport1Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateCascaderFilter(parameterMap);
        String fileName = "stock_aging_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.inventory.dao.IInventoryAgingDao.queryReport1Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        String reportType = (String) parameterMap.get("report2Type");
        if ("by_month".equalsIgnoreCase(reportType)) {
            parameterMap.put("reportType", "TO_CHAR(T2.DATE$, 'YYYY/MM')");
        } else if ("by_week".equalsIgnoreCase(reportType)) {
            parameterMap.put("reportType", "T2.YEAR || 'W' ||T2.WEEK_NO");
        } else {
            parameterMap.put("reportType", "TO_CHAR(T2.DATE$, 'YYYY/MM/DD')");
        }

        List<Map<String, Object>> resultList = inventoryAgingDao.queryReport2(parameterMap);

        Map<String, Object> report2Data = new HashMap<>();

        List<String> xAxis = new ArrayList<>();
        List<Double> yAxis = new ArrayList<>();
        for (Map<String, Object> map : resultList) {
            xAxis.add((String) map.get("xAxis"));
            yAxis.add(Utils.parseBigDecimal(map.get("yAxis")).doubleValue());
        }
        report2Data.put("xAxis", xAxis);
        report2Data.put("yAxis", yAxis);
        return response.setBody(report2Data);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2Details(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateCascaderFilter(parameterMap);
        page.setTotal(inventoryAgingDao.queryReport2DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(inventoryAgingDao.queryReport2Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport2Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateCascaderFilter(parameterMap);
        String fileName = "stock_aging_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.inventory.dao.IInventoryAgingDao.queryReport2Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Columns(Map<String, Object> parameterMap) {
        List<String> result = new ArrayList<>();
        JSONArray dateRange = (JSONArray) parameterMap.get("dateRange3");
        String start = dateRange.getString(0);
        String end = dateRange.getString(1);
        while (start.compareTo(end) <= 0) {
            result.add(start);
            start = Utils.addMonth(start, 1);
        }
        result.sort(Comparator.reverseOrder());
        return response.setBody(result);
    }

    @Override
    @SuppressWarnings("unchecked")
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        List<LinkedHashMap<String, Object>> dataList = inventoryAgingDao.queryReport3(parameterMap);

        List<String> selectedColumn = (List<String>) parameterMap.get("report3SelectedColumn");
        Object columnsObj = parameterMap.get("report3ColumnNames");

        // 汇总Total
        if (columnsObj instanceof JSONArray) {
            LinkedHashMap<String, Object> totalMap = new LinkedHashMap<>();
            totalMap.put(selectedColumn.get(0), "Total");
            JSONArray columns = (JSONArray) columnsObj;
            for (Object o : columns) {
                BigDecimal total = BigDecimal.ZERO;
                String key = "'" + o + "'_TOTAL";
                for (LinkedHashMap<String, Object> map : dataList) {
                    total = total.add(Utils.parseBigDecimal(map.get(key)));
                }
                totalMap.put(key, total);
            }
            dataList.add(totalMap);
        }
        page.setData(dataList);
        return response.setBody(page);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Details(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateCascaderFilter(parameterMap);
        page.setTotal(inventoryAgingDao.queryReport3DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(inventoryAgingDao.queryReport3Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateCascaderFilter(parameterMap);
        String fileName = "stock_aging_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.inventory.dao.IInventoryAgingDao.queryReport3Details", parameterMap);
    }

    private void generateValueColumn(Map<String, Object> parameterMap) {
        String type = (String) parameterMap.get("type");
        String qtyColumn = "Normal".equalsIgnoreCase((String) parameterMap.get("stockType")) ? "STOCK_NON_K" : "STOCK_K";
        if ("Moving Average Price".equalsIgnoreCase(type)) {
            parameterMap.put("valueColumn", qtyColumn + "_VALUE");
        } else if ("Pallet".equalsIgnoreCase(type)) {
            parameterMap.put("valueColumn", "decode(nvl(PALLETIZATION_QTY, 0), 0, 0, " + qtyColumn + " / PALLETIZATION_QTY)");
        } else {
            parameterMap.put("valueColumn", qtyColumn);
        }
    }

    /**
     * 生成cascader filter
     *
     * @param parameterMap 参数map
     */
    private void generateCascaderFilter(Map<String, Object> parameterMap) {
        this.generateCascaderFilterSQL(parameterMap);
    }
}
