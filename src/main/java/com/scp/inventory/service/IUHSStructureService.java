package com.scp.inventory.service;

import com.starter.context.bean.Response;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;

public interface IUHSStructureService {
    Response initPage();

    Response queryReport1(Map<String, Object> parameterMap);

    Response queryReport1Details(Map<String, Object> parameterMap);

    Response queryReport2(Map<String, Object> parameterMap);

    Response queryReport3(Map<String, Object> parameterMap);

    Response queryReport4(Map<String, Object> parameterMap);

    void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport5(Map<String, Object> parameterMap) throws Exception;
}
