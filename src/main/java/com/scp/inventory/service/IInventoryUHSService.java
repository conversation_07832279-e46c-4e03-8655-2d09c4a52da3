package com.scp.inventory.service;

import com.starter.context.bean.Response;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;

public interface IInventoryUHSService {

    Response initPage();

    Response queryReport1Weeks(Map<String, Object> parameterMap);

    Response queryReport1(Map<String, Object> parameterMap);

    Response queryReport2(Map<String, Object> parameterMap);

    Response queryReport1Details(Map<String, Object> parameterMap);

    void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport3(Map<String, Object> parameterMap);

    void downloadReport3(Map<String, Object> parameterMap, HttpServletResponse response);

    Response saveReport3(String userid, Map<String, Object> parameterMap);

    Response uploadReport3Result(String userid, MultipartFile file) throws Exception;

    void downloadReport3OneMM(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadReport3ResultTemplate(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadReport3RecommendRCA(Map<String, Object> parameterMap, HttpServletResponse response);
}
