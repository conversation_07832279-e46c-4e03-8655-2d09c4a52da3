package com.scp.inventory.service;

import com.starter.context.bean.Response;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;

public interface IInventoryDeliveryService {

    Response initPage();

    Response queryReport1(Map<String, Object> parameterMap);

    Response queryReport1DetailsChart(Map<String, Object> parameterMap);

    Response queryReport1DetailsTable(Map<String, Object> parameterMap);

    void downloadReport1Overall(Map<String, Object> parameterMap, HttpServletResponse res);

    Response queryReport1Overall(Map<String, Object> parameterMap);

    void downloadReport1DetailsTable(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport2(Map<String, Object> parameterMap);

    Response queryReport2Details(Map<String, Object> parameterMap);

    void downloadReport2Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport3(Map<String, Object> parameterMap);

    void downloadReport3(Map<String, Object> parameterMap, HttpServletResponse res);

    Response queryReport3Details(Map<String, Object> parameterMap);

    void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport4(Map<String, Object> parameterMap);
}
