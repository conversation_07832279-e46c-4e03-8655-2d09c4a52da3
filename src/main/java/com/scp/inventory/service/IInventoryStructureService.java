package com.scp.inventory.service;

import com.starter.context.bean.Response;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;

public interface IInventoryStructureService {

    Response queryFilters();

    Response clearCache();

    Response queryReport1(Map<String, Object> parameterMap) throws Exception;

    Response queryReport2(Map<String, Object> parameterMap);

    Response queryReport3Columns(Map<String, Object> parameterMap);

    Response queryReport3(Map<String, Object> parameterMap);

    Response queryReport3Details(Map<String, Object> parameterMap);

    void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadReport3(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport4(Map<String, Object> parameterMap);

    Response queryReport4Details(Map<String, Object> parameterMap);

    void downloadReport4Details(Map<String, Object> parameterMap, HttpServletResponse response);

}
