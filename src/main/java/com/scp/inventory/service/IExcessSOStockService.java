package com.scp.inventory.service;

import com.starter.context.bean.Response;
import io.netty.handler.codec.serialization.ObjectEncoder;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;

public interface IExcessSOStockService {

    Response queryCascader();

    Response queryReport1(Map<String, Object> parameterMap);

    Response queryReport2(Map<String, Object> parameterMap);

    Response queryReport2Details(Map<String, Object> parameterMap);

    void downloadReport2Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport3(Map<String, Object> parameterMap);

    Response queryReport3Columns(Map<String, Object> parameterMap);

    void downloadReport3(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport3Details(Map<String, Object> parameterMap);

    void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse response);
}
