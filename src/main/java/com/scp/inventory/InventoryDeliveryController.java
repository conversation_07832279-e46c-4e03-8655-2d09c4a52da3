package com.scp.inventory;

import com.scp.inventory.service.IInventoryDeliveryService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/inventory/delivery", parent = "menu750")
public class InventoryDeliveryController extends ControllerHelper {

    @Resource
    private IInventoryDeliveryService inventoryDeliveryService;

    @SchneiderRequestMapping("/init_page")
    public Response queryFilters() {
        super.setGlobalCache(true);
        return inventoryDeliveryService.initPage();
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return inventoryDeliveryService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping("/query_report1_details_chart")
    public Response queryReport1DetailsChart(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return inventoryDeliveryService.queryReport1DetailsChart(parameterMap);
    }

    @SchneiderRequestMapping("/query_report1_details_table")
    public Response queryReport1DetailsTable(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return inventoryDeliveryService.queryReport1DetailsTable(parameterMap);
    }

    @SchneiderRequestMapping("/download_report1_details_table")
    public void downloadReport1DetailsTable(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        inventoryDeliveryService.downloadReport1DetailsTable(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report1_overall")
    public Response queryReport1Overall(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return inventoryDeliveryService.queryReport1Overall(parameterMap);
    }

    @SchneiderRequestMapping("/download_report1_overall")
    public void downloadReport2Overall(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        inventoryDeliveryService.downloadReport1Overall(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return inventoryDeliveryService.queryReport2(parameterMap);
    }

    @SchneiderRequestMapping("/query_report2_details")
    public Response queryReport2Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return inventoryDeliveryService.queryReport2Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report2_details")
    public void downloadReport2Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        inventoryDeliveryService.downloadReport2Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report3")
    public Response queryReport3(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return inventoryDeliveryService.queryReport3(parameterMap);
    }

    @SchneiderRequestMapping("/download_report3")
    public void downloadReport3(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        inventoryDeliveryService.downloadReport3(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report3_details")
    public Response queryReport3Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return inventoryDeliveryService.queryReport3Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report3_details")
    public void downloadReport3Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        inventoryDeliveryService.downloadReport3Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report4")
    public Response queryReport4(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return inventoryDeliveryService.queryReport4(parameterMap);
    }
}
