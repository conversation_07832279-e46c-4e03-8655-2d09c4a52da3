package com.scp.inventory.bean;

import com.alibaba.fastjson.JSONArray;
import com.starter.utils.Utils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class SliceFilters {
    private List<String> classList = new ArrayList<>();
    private List<String> plantTypeList = new ArrayList<>();
    private List<String> clusterList = new ArrayList<>();
    private List<String> entityList = new ArrayList<>();

    public SliceFilters(Map<String, Object> parameterMap) {
        JSONArray categoryArray = (JSONArray) parameterMap.get("filterList");
        if (categoryArray != null) {
            for (Object subObj : categoryArray) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = subArray.getString(0);
                String value = subArray.getString(1);

                if (Utils.hasInjectionAttack(columnName)) {
                    continue;
                }

                switch (columnName.toUpperCase()) {
                    case "CLASS" -> classList.add(value);
                    case "PLANT_TYPE" -> plantTypeList.add(value);
                    case "ENTITY_NAME" -> entityList.add(value);
                    case "CLUSTER_NAME" -> clusterList.add(value);
                }
            }
        }
    }

    public List<String> getClassList() {
        return classList;
    }

    public void addClassList(String clazz) {
        this.classList.add(clazz);
    }

    public boolean match(SliceRawData data) {
        boolean result = this.classList.isEmpty() != false || this.classList.contains(data.getClazz()) != false;

        if (result && this.plantTypeList.isEmpty() == false && this.plantTypeList.contains(data.getPlantType()) == false) {
            result = false;
        }

        if (result && this.clusterList.isEmpty() == false && this.clusterList.contains(data.getClusterName()) == false) {
            result = false;
        }

        if (result && this.entityList.isEmpty() == false && this.entityList.contains(data.getEntityName()) == false) {
            result = false;
        }
        return result;
    }

    public void setClassList(List<String> classList) {
        this.classList = classList;
    }

    public List<String> getPlantTypeList() {
        return plantTypeList;
    }

    public void setPlantTypeList(List<String> plantTypeList) {
        this.plantTypeList = plantTypeList;
    }

    public void addPlantTypeList(String plantType) {
        this.plantTypeList.add(plantType);
    }

    public List<String> getClusterList() {
        return clusterList;
    }

    public void setClusterList(List<String> clusterList) {
        this.clusterList = clusterList;
    }

    public void addClusterList(String cluster) {
        this.clusterList.add(cluster);
    }

    public List<String> getEntityList() {
        return entityList;
    }

    public void setEntityList(List<String> entityList) {
        this.entityList = entityList;
    }

    public void addEntityList(String entity) {
        this.entityList.add(entity);
    }
}
