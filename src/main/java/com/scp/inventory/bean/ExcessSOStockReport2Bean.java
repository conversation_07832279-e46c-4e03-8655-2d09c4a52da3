package com.scp.inventory.bean;

import java.math.BigDecimal;

public class ExcessSOStockReport2Bean {

    private String CALENDAR_DATE;
    private String NAME;
    private BigDecimal VALUE;

    public String getKey() {
        return NAME + '#' + CALENDAR_DATE;
    }

    public String getCALENDAR_DATE() {
        return CALENDAR_DATE;
    }

    public void setCALENDAR_DATE(String CALENDAR_DATE) {
        this.CALENDAR_DATE = CALENDAR_DATE;
    }

    public String getNAME() {
        return NAME;
    }

    public void setNAME(String NAME) {
        this.NAME = NAME;
    }

    public BigDecimal getVALUE() {
        return VALUE;
    }

    public void setVALUE(BigDecimal VALUE) {
        this.VALUE = VALUE;
    }
}
