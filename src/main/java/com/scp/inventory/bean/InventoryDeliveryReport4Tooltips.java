package com.scp.inventory.bean;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.starter.context.bean.SCPRuntimeException;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class InventoryDeliveryReport4Tooltips {
    private BigDecimal DELIVERY_QUANTITY;
    private BigDecimal DELIVERY_VALUE;
    private BigDecimal DELIVERY_NET_VALUE;
    private BigDecimal DELIVERY_COST_VALUE;
    private BigDecimal UU_STOCK_QTY;
    private BigDecimal UU_STOCK_VALUE;
    private BigDecimal UU_STOCK_SALES_VALUE;
    private BigDecimal OPEN_SO_QTY;
    private BigDecimal OPEN_SO_COST_VALUE;
    private BigDecimal OPEN_SO_SALES_VALUE;

    private static final Class<InventoryDeliveryReport4Tooltips> clazz;
    private static final List<String> fields;

    static {
        clazz = InventoryDeliveryReport4Tooltips.class;
        fields = Arrays.stream(clazz.getDeclaredFields()).filter(f -> f.getAnnotatedType().getType().getTypeName().equalsIgnoreCase("java.math.BigDecimal")).map(Field::getName).collect(Collectors.toList());
    }

    public InventoryDeliveryReport4Tooltips copyOf(InventoryDeliveryReport4Tooltips tooltips) throws Exception {
        for (String f : fields) {
            Method method = clazz.getMethod("get" + f);
            Object value = method.invoke(tooltips);
            if (value != null) {
                clazz.getMethod("set" + f, BigDecimal.class).invoke(this, (BigDecimal) value);
            }
        }
        return this;
    }

    public BigDecimal getDELIVERY_QUANTITY() {
        return DELIVERY_QUANTITY;
    }

    public void setDELIVERY_QUANTITY(BigDecimal DELIVERY_QUANTITY) {
        this.DELIVERY_QUANTITY = DELIVERY_QUANTITY;
    }

    public BigDecimal getDELIVERY_VALUE() {
        return DELIVERY_VALUE;
    }

    public void setDELIVERY_VALUE(BigDecimal DELIVERY_VALUE) {
        this.DELIVERY_VALUE = DELIVERY_VALUE;
    }

    public BigDecimal getDELIVERY_NET_VALUE() {
        return DELIVERY_NET_VALUE;
    }

    public void setDELIVERY_NET_VALUE(BigDecimal DELIVERY_NET_VALUE) {
        this.DELIVERY_NET_VALUE = DELIVERY_NET_VALUE;
    }

    public BigDecimal getDELIVERY_COST_VALUE() {
        return DELIVERY_COST_VALUE;
    }

    public void setDELIVERY_COST_VALUE(BigDecimal DELIVERY_COST_VALUE) {
        this.DELIVERY_COST_VALUE = DELIVERY_COST_VALUE;
    }

    public BigDecimal getUU_STOCK_QTY() {
        return UU_STOCK_QTY;
    }

    public void setUU_STOCK_QTY(BigDecimal UU_STOCK_QTY) {
        this.UU_STOCK_QTY = UU_STOCK_QTY;
    }

    public BigDecimal getUU_STOCK_VALUE() {
        return UU_STOCK_VALUE;
    }

    public void setUU_STOCK_VALUE(BigDecimal UU_STOCK_VALUE) {
        this.UU_STOCK_VALUE = UU_STOCK_VALUE;
    }

    public BigDecimal getUU_STOCK_SALES_VALUE() {
        return UU_STOCK_SALES_VALUE;
    }

    public void setUU_STOCK_SALES_VALUE(BigDecimal UU_STOCK_SALES_VALUE) {
        this.UU_STOCK_SALES_VALUE = UU_STOCK_SALES_VALUE;
    }

    public BigDecimal getOPEN_SO_QTY() {
        return OPEN_SO_QTY;
    }

    public void setOPEN_SO_QTY(BigDecimal OPEN_SO_QTY) {
        this.OPEN_SO_QTY = OPEN_SO_QTY;
    }

    public BigDecimal getOPEN_SO_COST_VALUE() {
        return OPEN_SO_COST_VALUE;
    }

    public void setOPEN_SO_COST_VALUE(BigDecimal OPEN_SO_COST_VALUE) {
        this.OPEN_SO_COST_VALUE = OPEN_SO_COST_VALUE;
    }

    public BigDecimal getOPEN_SO_SALES_VALUE() {
        return OPEN_SO_SALES_VALUE;
    }

    public void setOPEN_SO_SALES_VALUE(BigDecimal OPEN_SO_SALES_VALUE) {
        this.OPEN_SO_SALES_VALUE = OPEN_SO_SALES_VALUE;
    }

    public void add(InventoryDeliveryReport4Tooltips tips) {
        try {
            for (String f : fields) {
                Method method = clazz.getMethod("get" + f);
                Object value = method.invoke(tips);
                if (value != null) {
                    BigDecimal val = BigDecimal.ZERO;
                    Object valueOrg = method.invoke(this);
                    if (valueOrg != null) {
                        val = (BigDecimal) valueOrg;
                    }
                    clazz.getMethod("set" + f, BigDecimal.class).invoke(this, val.add((BigDecimal) value));
                }
            }
        } catch (Exception e) {
            throw new SCPRuntimeException(e.getMessage());
        }
    }
}
