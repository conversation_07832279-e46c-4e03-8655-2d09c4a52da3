package com.scp.inventory.bean;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class Report1Tooltips {
    private BigDecimal AMF_ONE_MM;
    private BigDecimal AMU_ONE_MM;
    private BigDecimal BLOCKED_STOCK;
    private BigDecimal EXCESS_ONE_MM;
    private BigDecimal GIT_QTY;
    private BigDecimal GIT_VALUE;
    private BigDecimal GR_LAST_WD;
    private BigDecimal INTER_STK_TRANSFER;
    private BigDecimal LA_LAST_WD;
    private BigDecimal MINIMUM_LOT_SIZE;
    private BigDecimal MISSING_ONE_MM;
    private BigDecimal MO_RESERVATION;
    private BigDecimal OPEN_AB;
    private BigDecimal OPEN_LA;
    private BigDecimal OPEN_NON_AB_LA;
    private BigDecimal OPEN_PO;
    private BigDecimal OPEN_SO;
    private BigDecimal PO_CREATION_LAST_WD;
    private BigDecimal PROPOSED_FIN_PROV;
    private BigDecimal REAL_PROV_ONE_MM;
    private BigDecimal REORDER_POINT;
    private BigDecimal RESTRICTED_STOCK;
    private BigDecimal RETURNS_STOCK;
    private BigDecimal SAFETY_STOCK;
    private BigDecimal SS2;
    private BigDecimal SS3;
    private BigDecimal STOCK_IN_QI;
    private BigDecimal THEO_PROV_ONE_MM;
    private BigDecimal UD_LT_30_CD;
    private BigDecimal UD_LT_7_CD;
    private BigDecimal UD_GT_30_CD;
    private BigDecimal UU_STOCK;
    private BigDecimal WIP_QTY;
    private BigDecimal WIP_VALUE;
    private BigDecimal K = BigDecimal.ZERO;

    private static final Class<Report1Tooltips> clazz;
    private static final List<String> fields;

    static {
        clazz = Report1Tooltips.class;
        fields = Arrays.stream(clazz.getDeclaredFields()).filter(f -> f.getAnnotatedType().getType().getTypeName().equalsIgnoreCase("java.math.BigDecimal")).map(Field::getName).collect(Collectors.toList());
    }

    public Report1Tooltips copyOf(Report1Tooltips tooltips) throws Exception {
        for (String f : fields) {
            Method method = clazz.getMethod("get" + f);
            Object value = method.invoke(tooltips);
            if (value != null) {
                clazz.getMethod("set" + f, BigDecimal.class).invoke(this, (BigDecimal) value);
            }
        }
        return this;
    }

    public BigDecimal getAMF_ONE_MM() {
        return AMF_ONE_MM;
    }

    public void setAMF_ONE_MM(BigDecimal AMF_ONE_MM) {
        this.AMF_ONE_MM = AMF_ONE_MM;
    }

    public BigDecimal getAMU_ONE_MM() {
        return AMU_ONE_MM;
    }

    public void setAMU_ONE_MM(BigDecimal AMU_ONE_MM) {
        this.AMU_ONE_MM = AMU_ONE_MM;
    }

    public BigDecimal getBLOCKED_STOCK() {
        return BLOCKED_STOCK;
    }

    public void setBLOCKED_STOCK(BigDecimal BLOCKED_STOCK) {
        this.BLOCKED_STOCK = BLOCKED_STOCK;
    }

    public BigDecimal getEXCESS_ONE_MM() {
        return EXCESS_ONE_MM;
    }

    public void setEXCESS_ONE_MM(BigDecimal EXCESS_ONE_MM) {
        this.EXCESS_ONE_MM = EXCESS_ONE_MM;
    }

    public BigDecimal getGIT_QTY() {
        return GIT_QTY;
    }

    public void setGIT_QTY(BigDecimal GIT_QTY) {
        this.GIT_QTY = GIT_QTY;
    }

    public BigDecimal getGIT_VALUE() {
        return GIT_VALUE;
    }

    public void setGIT_VALUE(BigDecimal GIT_VALUE) {
        this.GIT_VALUE = GIT_VALUE;
    }

    public BigDecimal getGR_LAST_WD() {
        return GR_LAST_WD;
    }

    public void setGR_LAST_WD(BigDecimal GR_LAST_WD) {
        this.GR_LAST_WD = GR_LAST_WD;
    }

    public BigDecimal getINTER_STK_TRANSFER() {
        return INTER_STK_TRANSFER;
    }

    public void setINTER_STK_TRANSFER(BigDecimal INTER_STK_TRANSFER) {
        this.INTER_STK_TRANSFER = INTER_STK_TRANSFER;
    }

    public BigDecimal getLA_LAST_WD() {
        return LA_LAST_WD;
    }

    public void setLA_LAST_WD(BigDecimal LA_LAST_WD) {
        this.LA_LAST_WD = LA_LAST_WD;
    }

    public BigDecimal getMINIMUM_LOT_SIZE() {
        return MINIMUM_LOT_SIZE;
    }

    public void setMINIMUM_LOT_SIZE(BigDecimal MINIMUM_LOT_SIZE) {
        this.MINIMUM_LOT_SIZE = MINIMUM_LOT_SIZE;
    }

    public BigDecimal getMISSING_ONE_MM() {
        return MISSING_ONE_MM;
    }

    public void setMISSING_ONE_MM(BigDecimal MISSING_ONE_MM) {
        this.MISSING_ONE_MM = MISSING_ONE_MM;
    }

    public BigDecimal getMO_RESERVATION() {
        return MO_RESERVATION;
    }

    public void setMO_RESERVATION(BigDecimal MO_RESERVATION) {
        this.MO_RESERVATION = MO_RESERVATION;
    }

    public BigDecimal getOPEN_PO() {
        return OPEN_PO;
    }

    public void setOPEN_PO(BigDecimal OPEN_PO) {
        this.OPEN_PO = OPEN_PO;
    }

    public BigDecimal getOPEN_SO() {
        return OPEN_SO;
    }

    public void setOPEN_SO(BigDecimal OPEN_SO) {
        this.OPEN_SO = OPEN_SO;
    }

    public BigDecimal getOPEN_AB() {
        return OPEN_AB;
    }

    public void setOPEN_AB(BigDecimal OPEN_AB) {
        this.OPEN_AB = OPEN_AB;
    }

    public BigDecimal getOPEN_LA() {
        return OPEN_LA;
    }

    public void setOPEN_LA(BigDecimal OPEN_LA) {
        this.OPEN_LA = OPEN_LA;
    }

    public BigDecimal getOPEN_NON_AB_LA() {
        return OPEN_NON_AB_LA;
    }

    public void setOPEN_NON_AB_LA(BigDecimal OPEN_NON_AB_LA) {
        this.OPEN_NON_AB_LA = OPEN_NON_AB_LA;
    }

    public BigDecimal getPO_CREATION_LAST_WD() {
        return PO_CREATION_LAST_WD;
    }

    public void setPO_CREATION_LAST_WD(BigDecimal PO_CREATION_LAST_WD) {
        this.PO_CREATION_LAST_WD = PO_CREATION_LAST_WD;
    }

    public BigDecimal getREORDER_POINT() {
        return REORDER_POINT;
    }

    public void setREORDER_POINT(BigDecimal REORDER_POINT) {
        this.REORDER_POINT = REORDER_POINT;
    }

    public BigDecimal getRESTRICTED_STOCK() {
        return RESTRICTED_STOCK;
    }

    public void setRESTRICTED_STOCK(BigDecimal RESTRICTED_STOCK) {
        this.RESTRICTED_STOCK = RESTRICTED_STOCK;
    }

    public BigDecimal getRETURNS_STOCK() {
        return RETURNS_STOCK;
    }

    public void setRETURNS_STOCK(BigDecimal RETURNS_STOCK) {
        this.RETURNS_STOCK = RETURNS_STOCK;
    }

    public BigDecimal getSAFETY_STOCK() {
        return SAFETY_STOCK;
    }

    public void setSAFETY_STOCK(BigDecimal SAFETY_STOCK) {
        this.SAFETY_STOCK = SAFETY_STOCK;
    }

    public BigDecimal getSS2() {
        return SS2;
    }

    public void setSS2(BigDecimal SS2) {
        this.SS2 = SS2;
    }

    public BigDecimal getSS3() {
        return SS3;
    }

    public void setSS3(BigDecimal SS3) {
        this.SS3 = SS3;
    }

    public BigDecimal getSTOCK_IN_QI() {
        return STOCK_IN_QI;
    }

    public void setSTOCK_IN_QI(BigDecimal STOCK_IN_QI) {
        this.STOCK_IN_QI = STOCK_IN_QI;
    }

    public BigDecimal getTHEO_PROV_ONE_MM() {
        return THEO_PROV_ONE_MM;
    }

    public void setTHEO_PROV_ONE_MM(BigDecimal THEO_PROV_ONE_MM) {
        this.THEO_PROV_ONE_MM = THEO_PROV_ONE_MM;
    }

    public BigDecimal getUD_LT_30_CD() {
        return UD_LT_30_CD;
    }

    public void setUD_LT_30_CD(BigDecimal UD_LT_30_CD) {
        this.UD_LT_30_CD = UD_LT_30_CD;
    }

    public BigDecimal getUD_LT_7_CD() {
        return UD_LT_7_CD;
    }

    public void setUD_LT_7_CD(BigDecimal UD_LT_7_CD) {
        this.UD_LT_7_CD = UD_LT_7_CD;
    }

    public BigDecimal getUD_GT_30_CD() {
        return UD_GT_30_CD;
    }

    public void setUD_GT_30_CD(BigDecimal UD_GT_30_CD) {
        this.UD_GT_30_CD = UD_GT_30_CD;
    }

    public BigDecimal getUU_STOCK() {
        return UU_STOCK;
    }

    public void setUU_STOCK(BigDecimal UU_STOCK) {
        this.UU_STOCK = UU_STOCK;
    }

    public BigDecimal getWIP_QTY() {
        return WIP_QTY;
    }

    public void setWIP_QTY(BigDecimal WIP_QTY) {
        this.WIP_QTY = WIP_QTY;
    }

    public BigDecimal getWIP_VALUE() {
        return WIP_VALUE;
    }

    public void setWIP_VALUE(BigDecimal WIP_VALUE) {
        this.WIP_VALUE = WIP_VALUE;
    }

    public BigDecimal getK() {
        return K;
    }

    public void setK(BigDecimal k) {
        K = k;
    }

    public BigDecimal getREAL_PROV_ONE_MM() {
        return REAL_PROV_ONE_MM;
    }

    public void setREAL_PROV_ONE_MM(BigDecimal REAL_PROV_ONE_MM) {
        this.REAL_PROV_ONE_MM = REAL_PROV_ONE_MM;
    }

    public BigDecimal getPROPOSED_FIN_PROV() {
        return PROPOSED_FIN_PROV;
    }

    public void setPROPOSED_FIN_PROV(BigDecimal PROPOSED_FIN_PROV) {
        this.PROPOSED_FIN_PROV = PROPOSED_FIN_PROV;
    }

    public void add(Report1Tooltips tips) throws Exception {
        for (String f : fields) {
            Method method = clazz.getMethod("get" + f);
            Object value = method.invoke(tips);
            if (value != null) {
                BigDecimal val = BigDecimal.ZERO;
                Object valueOrg = method.invoke(this);
                if (valueOrg != null) {
                    val = (BigDecimal) valueOrg;
                }
                clazz.getMethod("set" + f, BigDecimal.class).invoke(this, val.add((BigDecimal) value));
            }
        }
    }
}
