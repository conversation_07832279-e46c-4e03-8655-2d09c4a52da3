package com.scp.inventory.bean;

import com.starter.utils.DateCalUtil;
import com.starter.utils.Utils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class UHSReport3Result {

    private String VERSION;
    private String MATERIAL;
    private String PLANT_CODE;
    private String FINAL_RCA;
    private String ACTION_RECOMMEND;
    private String COD_ACTION_RCA;
    private String FINAL_ACTION;
    private String HS_XSMIS_ACT;
    private String HS_XSMIS_ACT_CSV;
    private String DATE_INI_BTN;
    private String DATE_FIN_BTN;
    private String COD_STATUS_RCA;
    private String COD_FREQ_RCA;
    private String CREATE_BY;

    // TODO 添加校验

    public String getCOD_FREQ_RCA() {
        return COD_FREQ_RCA;
    }

    public void setCOD_FREQ_RCA(String COD_FREQ_RCA) {
        this.COD_FREQ_RCA = COD_FREQ_RCA;
    }

    private final List<String> invalidMsg = new ArrayList<>();

    public String getVERSION() {
        return VERSION;
    }

    public void setVERSION(String VERSION) {
        this.VERSION = VERSION;
    }

    public String getMATERIAL() {
        return MATERIAL;
    }

    public void setMATERIAL(String MATERIAL) {
        this.MATERIAL = MATERIAL;
    }

    public String getPLANT_CODE() {
        return PLANT_CODE;
    }

    public void setPLANT_CODE(String PLANT_CODE) {
        this.PLANT_CODE = PLANT_CODE;
    }

    public String getFINAL_RCA() {
        return FINAL_RCA;
    }

    public void setFINAL_RCA(String FINAL_RCA) {
        this.FINAL_RCA = FINAL_RCA;
    }

    public String getACTION_RECOMMEND() {
        return ACTION_RECOMMEND;
    }

    public void setACTION_RECOMMEND(String ACTION_RECOMMEND) {
        this.ACTION_RECOMMEND = ACTION_RECOMMEND;
    }

    public String getCOD_ACTION_RCA() {
        return COD_ACTION_RCA;
    }

    public void setCOD_ACTION_RCA(String COD_ACTION_RCA) {
        this.COD_ACTION_RCA = COD_ACTION_RCA;
    }

    public String getFINAL_ACTION() {
        return FINAL_ACTION;
    }

    public void setFINAL_ACTION(String FINAL_ACTION) {
        this.FINAL_ACTION = FINAL_ACTION;
    }

    public String getHS_XSMIS_ACT() {
        return HS_XSMIS_ACT;
    }

    public void setHS_XSMIS_ACT(String HS_XSMIS_ACT) {
        this.HS_XSMIS_ACT = HS_XSMIS_ACT;
        if (HS_XSMIS_ACT != null) {
            String temp = HS_XSMIS_ACT.replaceAll("[^A-Za-z0-9 ]", ""); // 将所有非字母数字的字符替换为空格
            while (temp.contains("  ") == true) { // 如果有包含两个以上的空格, 那就将两个空格替换成一个
                temp = temp.replace("  ", " ");
            }

            this.HS_XSMIS_ACT_CSV = temp;
        }
    }

    public String getDATE_INI_BTN() {
        if (Utils.isStrictNumeric(DATE_INI_BTN)) {
            DATE_INI_BTN = DateCalUtil.excelNumber2Date(DATE_INI_BTN, "yyyy/MM/dd");
        }
        return DATE_INI_BTN;
    }

    public String getHS_XSMIS_ACT_CSV() {
        return HS_XSMIS_ACT_CSV;
    }

    public void setHS_XSMIS_ACT_CSV(String HS_XSMIS_ACT_CSV) {
        this.HS_XSMIS_ACT_CSV = HS_XSMIS_ACT_CSV;
    }

    public void setDATE_INI_BTN(String DATE_INI_BTN) {
        this.DATE_INI_BTN = DATE_INI_BTN;
    }

    public String getDATE_FIN_BTN() {
        if (Utils.isStrictNumeric(DATE_FIN_BTN)) {
            DATE_FIN_BTN = DateCalUtil.excelNumber2Date(DATE_FIN_BTN, "yyyy/MM/dd");
        }
        return DATE_FIN_BTN;
    }

    public void setDATE_FIN_BTN(String DATE_FIN_BTN) {
        this.DATE_FIN_BTN = DATE_FIN_BTN;
    }

    public String getCOD_STATUS_RCA() {
        return COD_STATUS_RCA;
    }

    public void setCOD_STATUS_RCA(String COD_STATUS_RCA) {
        this.COD_STATUS_RCA = COD_STATUS_RCA;
    }

    public String getCREATE_BY() {
        return CREATE_BY;
    }

    public void setCREATE_BY(String CREATE_BY) {
        this.CREATE_BY = CREATE_BY;
    }

    public boolean isValid() {
        return invalidMsg.size() == 0;
    }

    public String getInvalidMsg() {
        if (invalidMsg.size() > 0) {
            return "Invalid fields = " + StringUtils.join(invalidMsg, ", ");
        } else {
            return null;
        }
    }
}
