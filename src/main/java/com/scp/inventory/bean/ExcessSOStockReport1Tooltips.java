package com.scp.inventory.bean;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.starter.context.bean.SCPRuntimeException;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class ExcessSOStockReport1Tooltips {
    private BigDecimal BAD_INDENT_QTY;
    private BigDecimal BAD_INDENT_VALUE;
    private BigDecimal OPEN_SO_QTY;
    private BigDecimal STOCK_PRICE;
    private BigDecimal UNIT_COST;

    public BigDecimal getOPEN_SO_QTY() {
        return OPEN_SO_QTY;
    }

    public void setOPEN_SO_QTY(BigDecimal OPEN_SO_QTY) {
        this.OPEN_SO_QTY = OPEN_SO_QTY;
    }

    public BigDecimal getBAD_INDENT_QTY() {
        return BAD_INDENT_QTY;
    }

    public void setBAD_INDENT_QTY(BigDecimal BAD_INDENT_QTY) {
        this.BAD_INDENT_QTY = BAD_INDENT_QTY;
    }

    public BigDecimal getBAD_INDENT_VALUE() {
        return BAD_INDENT_VALUE;
    }

    public void setBAD_INDENT_VALUE(BigDecimal BAD_INDENT_VALUE) {
        this.BAD_INDENT_VALUE = BAD_INDENT_VALUE;
    }

    public BigDecimal getSTOCK_PRICE() {
        return STOCK_PRICE;
    }

    public void setSTOCK_PRICE(BigDecimal STOCK_PRICE) {
        this.STOCK_PRICE = STOCK_PRICE;
    }

    public BigDecimal getUNIT_COST() {
        return UNIT_COST;
    }

    public void setUNIT_COST(BigDecimal UNIT_COST) {
        this.UNIT_COST = UNIT_COST;
    }

    private static final Class<ExcessSOStockReport1Tooltips> clazz;
    private static final List<String> fields;

    static {
        clazz = ExcessSOStockReport1Tooltips.class;
        fields = Arrays.stream(clazz.getDeclaredFields()).filter(f -> f.getAnnotatedType().getType().getTypeName().equalsIgnoreCase("java.math.BigDecimal")).map(Field::getName).collect(Collectors.toList());
    }

    public ExcessSOStockReport1Tooltips copyOf(ExcessSOStockReport1Tooltips tooltips) {
        try {
            for (String f : fields) {
                Method method = clazz.getMethod("get" + f);
                Object value = method.invoke(tooltips);
                if (value != null) {
                    clazz.getMethod("set" + f, BigDecimal.class).invoke(this, (BigDecimal) value);
                }
            }
            return this;
        } catch (Exception e) {
            throw new SCPRuntimeException(e.getMessage());
        }

    }

    public void add(ExcessSOStockReport1Tooltips tips) throws Exception {
        for (String f : fields) {
            Method method = clazz.getMethod("get" + f);
            Object value = method.invoke(tips);
            if (value != null) {
                BigDecimal val = BigDecimal.ZERO;
                Object valueOrg = method.invoke(this);
                if (valueOrg != null) {
                    val = (BigDecimal) valueOrg;
                }
                clazz.getMethod("set" + f, BigDecimal.class).invoke(this, val.add((BigDecimal) value));
            }
        }
    }
}
