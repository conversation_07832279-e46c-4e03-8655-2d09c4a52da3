package com.scp.inventory.bean;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class UHSStructureReport5Tooltips {

    private BigDecimal THEO_PROV;
    private BigDecimal THEO_PROV_SPECIAL_EVENT;
    private BigDecimal EXCESS_STOCK;
    private BigDecimal EXCESS_SPECIAL_EVENT;
    private BigDecimal MISSING_STOCK;
    private BigDecimal HEALTH_STOCK;

    private static final Class<UHSStructureReport5Tooltips> clazz;
    private static final List<String> fields;

    static {
        clazz = UHSStructureReport5Tooltips.class;
        fields = Arrays.stream(clazz.getDeclaredFields()).filter(f -> f.getAnnotatedType().getType().getTypeName().equalsIgnoreCase("java.math.BigDecimal")).map(Field::getName).collect(Collectors.toList());
    }

    public UHSStructureReport5Tooltips copyOf(UHSStructureReport5Tooltips tooltips) throws Exception {
        for (String f : fields) {
            Method method = clazz.getMethod("get" + f);
            Object value = method.invoke(tooltips);
            if (value != null) {
                clazz.getMethod("set" + f, BigDecimal.class).invoke(this, (BigDecimal) value);
            }
        }
        return this;
    }

    public BigDecimal getTHEO_PROV() {
        return THEO_PROV;
    }

    public void setTHEO_PROV(BigDecimal THEO_PROV) {
        this.THEO_PROV = THEO_PROV;
    }

    public BigDecimal getTHEO_PROV_SPECIAL_EVENT() {
        return THEO_PROV_SPECIAL_EVENT;
    }

    public void setTHEO_PROV_SPECIAL_EVENT(BigDecimal THEO_PROV_SPECIAL_EVENT) {
        this.THEO_PROV_SPECIAL_EVENT = THEO_PROV_SPECIAL_EVENT;
    }

    public BigDecimal getEXCESS_STOCK() {
        return EXCESS_STOCK;
    }

    public void setEXCESS_STOCK(BigDecimal EXCESS_STOCK) {
        this.EXCESS_STOCK = EXCESS_STOCK;
    }

    public BigDecimal getEXCESS_SPECIAL_EVENT() {
        return EXCESS_SPECIAL_EVENT;
    }

    public void setEXCESS_SPECIAL_EVENT(BigDecimal EXCESS_SPECIAL_EVENT) {
        this.EXCESS_SPECIAL_EVENT = EXCESS_SPECIAL_EVENT;
    }

    public BigDecimal getMISSING_STOCK() {
        return MISSING_STOCK;
    }

    public void setMISSING_STOCK(BigDecimal MISSING_STOCK) {
        this.MISSING_STOCK = MISSING_STOCK;
    }

    public BigDecimal getHEALTH_STOCK() {
        return HEALTH_STOCK;
    }

    public void setHEALTH_STOCK(BigDecimal HEALTH_STOCK) {
        this.HEALTH_STOCK = HEALTH_STOCK;
    }

    public void add(UHSStructureReport5Tooltips tips) throws Exception {
        for (String f : fields) {
            Method method = clazz.getMethod("get" + f);
            Object value = method.invoke(tips);
            if (value != null) {
                BigDecimal val = BigDecimal.ZERO;
                Object valueOrg = method.invoke(this);
                if (valueOrg != null) {
                    val = (BigDecimal) valueOrg;
                }
                clazz.getMethod("set" + f, BigDecimal.class).invoke(this, val.add((BigDecimal) value));
            }
        }
    }
}
