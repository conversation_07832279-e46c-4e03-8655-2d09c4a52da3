package com.scp.inventory.bean;

import com.starter.utils.Utils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class DistributorInventoryReport2Result {
    List<String> xAxis = new ArrayList<>();
    List<BigDecimal> yAxis1 = new ArrayList<>();
    List<BigDecimal> yAxis2 = new ArrayList<>();
    List<BigDecimal> yAxis3 = new ArrayList<>();


    public void setYAxis(Map<String, BigDecimal> map) {
        if (map == null) {
            yAxis1.add(null);
            yAxis2.add(null);
            yAxis3.add(null);
        } else {
            yAxis1.add(Utils.parseBigDecimal(map.get("SPOT DIN"), null));
            yAxis2.add(Utils.parseBigDecimal(map.get("Sales Value"), null));
            yAxis3.add(Utils.parseBigDecimal(map.get("Inventory Value"), null));

        }
    }


    public List<String> getxAxis() {
        return xAxis;
    }

    public void setxAxis(List<String> xAxis) {
        this.xAxis = xAxis;
    }

    public List<BigDecimal> getyAxis1() {
        return yAxis1;
    }

    public void setyAxis1(List<BigDecimal> yAxis1) {
        this.yAxis1 = yAxis1;
    }

    public List<BigDecimal> getyAxis2() {
        return yAxis2;
    }

    public void setyAxis2(List<BigDecimal> yAxis2) {
        this.yAxis2 = yAxis2;
    }

    public List<BigDecimal> getyAxis3() {
        return yAxis3;
    }

    public void setyAxis3(List<BigDecimal> yAxis3) {
        this.yAxis3 = yAxis3;
    }

}
