package com.scp.inventory.bean;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class PositiveNagitiveList {
    private List<BigDecimal> positiveList = new ArrayList<>();
    private List<BigDecimal> nagitiveList = new ArrayList<>();

    public List<BigDecimal> getPositiveList() {
        return positiveList;
    }

    public void setPositiveList(List<BigDecimal> positiveList) {
        this.positiveList = positiveList;
    }

    public List<BigDecimal> getNagitiveList() {
        return nagitiveList;
    }

    public void setNagitiveList(List<BigDecimal> nagitiveList) {
        this.nagitiveList = nagitiveList;
    }
}
