package com.scp.inventory.bean;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class SliceRawData {
    public final static int CHINA = 0;
    public final static int CLUSTER = 1;
    public final static int ENTITY = 2;

    private String clusterName;
    private String entityName;
    private String plantType;
    private String clazz;
    private String currency;
    private String currencyType;
    private String rcaCategory;
    private String rcaCode;
    private String unitOfMeasure;
    private BigDecimal currentMonth = BigDecimal.ZERO;
    private BigDecimal month01 = BigDecimal.ZERO;
    private BigDecimal month02 = BigDecimal.ZERO;
    private BigDecimal month03 = BigDecimal.ZERO;
    private BigDecimal month04 = BigDecimal.ZERO;
    private BigDecimal month05 = BigDecimal.ZERO;
    private BigDecimal month06 = BigDecimal.ZERO;
    private BigDecimal month07 = BigDecimal.ZERO;
    private BigDecimal month08 = BigDecimal.ZERO;
    private BigDecimal month09 = BigDecimal.ZERO;
    private BigDecimal month10 = BigDecimal.ZERO;
    private BigDecimal month11 = BigDecimal.ZERO;
    private BigDecimal month12 = BigDecimal.ZERO;
    private BigDecimal month13 = BigDecimal.ZERO;
    private BigDecimal month14 = BigDecimal.ZERO;
    private BigDecimal month15 = BigDecimal.ZERO;
    private BigDecimal month16 = BigDecimal.ZERO;
    private BigDecimal month17 = BigDecimal.ZERO;
    private BigDecimal month18 = BigDecimal.ZERO;
    private BigDecimal month19 = BigDecimal.ZERO;
    private BigDecimal month20 = BigDecimal.ZERO;
    private BigDecimal month21 = BigDecimal.ZERO;
    private BigDecimal month22 = BigDecimal.ZERO;
    private BigDecimal month23 = BigDecimal.ZERO;
    private BigDecimal month24 = BigDecimal.ZERO;
    private BigDecimal month25 = BigDecimal.ZERO;
    private BigDecimal month26 = BigDecimal.ZERO;
    private BigDecimal month27 = BigDecimal.ZERO;
    private BigDecimal month28 = BigDecimal.ZERO;
    private BigDecimal month29 = BigDecimal.ZERO;
    private BigDecimal month30 = BigDecimal.ZERO;
    private BigDecimal month31 = BigDecimal.ZERO;
    private BigDecimal month32 = BigDecimal.ZERO;
    private BigDecimal month33 = BigDecimal.ZERO;
    private BigDecimal month34 = BigDecimal.ZERO;
    private BigDecimal month35 = BigDecimal.ZERO;
    private BigDecimal month36 = BigDecimal.ZERO;
    private String projection = "N";

    public static SliceRawData getInstance(String key, String clazz) {
        SliceRawData sliceRawData = new SliceRawData();
        String[] ks = StringUtils.split(key, ".");
        sliceRawData.setClusterName(ks[0]);
        sliceRawData.setPlantType(ks[1]);
        sliceRawData.setEntityName(ks[2]);
        sliceRawData.setClazz(clazz);

        // 非COGS的数据最终都会以CNY结算
        // COGS的数据, 只有在Plant才会以EUR结算, DC均以CNY结算
        if ("COGS".equalsIgnoreCase(clazz) && sliceRawData.isPlant()) {
            sliceRawData.setCurrency("EUR");
        } else {
            sliceRawData.setCurrency("CNY");
        }

        sliceRawData.setCurrencyType(StringUtils.indexOfIgnoreCase(clazz, "COGS") != -1 ? "COGS" : "INV");

        return sliceRawData;
    }

    public void convertCurrency(int monthIndex, BigDecimal exchangeRate) {
        try {
            String key = (monthIndex == 0) ? "CurrentMonth" : "Month" + (monthIndex < 10 ? "0" + monthIndex : monthIndex);
            String getter = "get" + key;
            String setter = "set" + key;
            BigDecimal value = (BigDecimal) SliceRawData.class.getMethod(getter).invoke(this);
            if (value != null && value.compareTo(BigDecimal.ZERO) != 0) {
                if (exchangeRate.compareTo(BigDecimal.ZERO) == 0) {
                    SliceRawData.class.getMethod(setter, BigDecimal.class).invoke(this, value);
                } else {
                    BigDecimal newValue = value.divide(exchangeRate, 4, RoundingMode.HALF_UP);
                    SliceRawData.class.getMethod(setter, BigDecimal.class).invoke(this, newValue);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public BigDecimal getMonthValue(int monthIndex) {
        try {
            String key = (monthIndex == 0) ? "CurrentMonth" : "Month" + (monthIndex < 10 ? "0" + monthIndex : monthIndex);
            String getter = "get" + key;
            BigDecimal value = (BigDecimal) SliceRawData.class.getMethod(getter).invoke(this);
            return value == null ? BigDecimal.ZERO : value;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public void setMonthValue(int monthIndex, BigDecimal value) {
        try {
            String key = (monthIndex == 0) ? "CurrentMonth" : "Month" + (monthIndex < 10 ? "0" + monthIndex : monthIndex);
            String setter = "set" + key;
            SliceRawData.class.getMethod(setter, BigDecimal.class).invoke(this, value);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean isDC() {
        return "DC".equalsIgnoreCase(plantType);
    }

    public boolean isPlant() {
        return "Plant".equalsIgnoreCase(plantType);
    }

    public BigDecimal getCurrentMonth() {
        if (currentMonth == null) {
            return BigDecimal.ZERO;
        }
        return currentMonth;
    }

    public void setCurrentMonth(BigDecimal currentMonth) {
        this.currentMonth = currentMonth;
    }

    public int type() {
        if (StringUtils.equalsIgnoreCase(clusterName, "China_Total")) {
            return CHINA;
        } else if ("EE".equalsIgnoreCase(entityName) || "H&D".equalsIgnoreCase(entityName) || "Trading".equalsIgnoreCase(entityName) || "LV".equalsIgnoreCase(entityName) || "MV".equalsIgnoreCase(entityName)
        || "ETO_MV".equalsIgnoreCase(entityName) || "MTO_CTO".equalsIgnoreCase(entityName) || "MTS_MTO".equalsIgnoreCase(entityName)) {
            return CLUSTER;
        } else {
            return ENTITY;
        }
    }

    public String getKey() {
        return this.getClusterName() + "." + this.getPlantType() + "." + this.getEntityName();
    }

    public String getClusterName() {
        return clusterName;
    }

    public void setClusterName(String clusterName) {
        this.clusterName = clusterName;
    }

    public String getEntityName() {
        return entityName;
    }

    public void setEntityName(String entityName) {
        this.entityName = entityName;
    }

    public String getPlantType() {
        return plantType;
    }

    public void setPlantType(String plantType) {
        this.plantType = plantType;
    }

    public String getClazz() {
        return clazz;
    }

    public void setClazz(String clazz) {
        this.clazz = clazz;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getCurrencyType() {
        return currencyType;
    }

    public void setCurrencyType(String currencyType) {
        this.currencyType = currencyType;
    }

    public BigDecimal getMonth01() {
        return month01;
    }

    public void setMonth01(BigDecimal month01) {
        this.month01 = month01;
    }

    public BigDecimal getMonth02() {
        return month02;
    }

    public void setMonth02(BigDecimal month02) {
        this.month02 = month02;
    }

    public BigDecimal getMonth03() {
        return month03;
    }

    public void setMonth03(BigDecimal month03) {
        this.month03 = month03;
    }

    public BigDecimal getMonth04() {
        return month04;
    }

    public void setMonth04(BigDecimal month04) {
        this.month04 = month04;
    }

    public BigDecimal getMonth05() {
        return month05;
    }

    public void setMonth05(BigDecimal month05) {
        this.month05 = month05;
    }

    public BigDecimal getMonth06() {
        return month06;
    }

    public void setMonth06(BigDecimal month06) {
        this.month06 = month06;
    }

    public BigDecimal getMonth07() {
        return month07;
    }

    public void setMonth07(BigDecimal month07) {
        this.month07 = month07;
    }

    public BigDecimal getMonth08() {
        return month08;
    }

    public void setMonth08(BigDecimal month08) {
        this.month08 = month08;
    }

    public BigDecimal getMonth09() {
        return month09;
    }

    public void setMonth09(BigDecimal month09) {
        this.month09 = month09;
    }

    public BigDecimal getMonth10() {
        return month10;
    }

    public void setMonth10(BigDecimal month10) {
        this.month10 = month10;
    }

    public BigDecimal getMonth11() {
        return month11;
    }

    public void setMonth11(BigDecimal month11) {
        this.month11 = month11;
    }

    public BigDecimal getMonth12() {
        return month12;
    }

    public void setMonth12(BigDecimal month12) {
        this.month12 = month12;
    }

    public BigDecimal getMonth13() {
        return month13;
    }

    public void setMonth13(BigDecimal month13) {
        this.month13 = month13;
    }

    public BigDecimal getMonth14() {
        return month14;
    }

    public void setMonth14(BigDecimal month14) {
        this.month14 = month14;
    }

    public BigDecimal getMonth15() {
        return month15;
    }

    public void setMonth15(BigDecimal month15) {
        this.month15 = month15;
    }

    public BigDecimal getMonth16() {
        return month16;
    }

    public void setMonth16(BigDecimal month16) {
        this.month16 = month16;
    }

    public BigDecimal getMonth17() {
        return month17;
    }

    public void setMonth17(BigDecimal month17) {
        this.month17 = month17;
    }

    public BigDecimal getMonth18() {
        return month18;
    }

    public void setMonth18(BigDecimal month18) {
        this.month18 = month18;
    }

    public BigDecimal getMonth19() {
        return month19;
    }

    public void setMonth19(BigDecimal month19) {
        this.month19 = month19;
    }

    public BigDecimal getMonth20() {
        return month20;
    }

    public void setMonth20(BigDecimal month20) {
        this.month20 = month20;
    }

    public BigDecimal getMonth21() {
        return month21;
    }

    public void setMonth21(BigDecimal month21) {
        this.month21 = month21;
    }

    public BigDecimal getMonth22() {
        return month22;
    }

    public void setMonth22(BigDecimal month22) {
        this.month22 = month22;
    }

    public BigDecimal getMonth23() {
        return month23;
    }

    public void setMonth23(BigDecimal month23) {
        this.month23 = month23;
    }

    public BigDecimal getMonth24() {
        return month24;
    }

    public void setMonth24(BigDecimal month24) {
        this.month24 = month24;
    }

    public BigDecimal getMonth25() {
        return month25;
    }

    public void setMonth25(BigDecimal month25) {
        this.month25 = month25;
    }

    public BigDecimal getMonth26() {
        return month26;
    }

    public void setMonth26(BigDecimal month26) {
        this.month26 = month26;
    }

    public BigDecimal getMonth27() {
        return month27;
    }

    public void setMonth27(BigDecimal month27) {
        this.month27 = month27;
    }

    public BigDecimal getMonth28() {
        return month28;
    }

    public void setMonth28(BigDecimal month28) {
        this.month28 = month28;
    }

    public BigDecimal getMonth29() {
        return month29;
    }

    public void setMonth29(BigDecimal month29) {
        this.month29 = month29;
    }

    public BigDecimal getMonth30() {
        return month30;
    }

    public void setMonth30(BigDecimal month30) {
        this.month30 = month30;
    }

    public BigDecimal getMonth31() {
        return month31;
    }

    public void setMonth31(BigDecimal month31) {
        this.month31 = month31;
    }

    public BigDecimal getMonth32() {
        return month32;
    }

    public void setMonth32(BigDecimal month32) {
        this.month32 = month32;
    }

    public BigDecimal getMonth33() {
        return month33;
    }

    public void setMonth33(BigDecimal month33) {
        this.month33 = month33;
    }

    public BigDecimal getMonth34() {
        return month34;
    }

    public void setMonth34(BigDecimal month34) {
        this.month34 = month34;
    }

    public BigDecimal getMonth35() {
        return month35;
    }

    public void setMonth35(BigDecimal month35) {
        this.month35 = month35;
    }

    public BigDecimal getMonth36() {
        return month36;
    }

    public void setMonth36(BigDecimal month36) {
        this.month36 = month36;
    }

    public String getRcaCategory() {
        return rcaCategory;
    }

    public void setRcaCategory(String rcaCategory) {
        this.rcaCategory = rcaCategory;
    }

    public String getRcaCode() {
        return rcaCode;
    }

    public void setRcaCode(String rcaCode) {
        this.rcaCode = rcaCode;
    }

    public String getUnitOfMeasure() {
        return unitOfMeasure;
    }

    public void setUnitOfMeasure(String unitOfMeasure) {
        this.unitOfMeasure = unitOfMeasure;
    }

    public String getProjection() {
        return projection;
    }

    public void setProjection(String projection) {
        this.projection = projection;
    }
}
