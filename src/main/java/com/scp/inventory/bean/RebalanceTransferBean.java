package com.scp.inventory.bean;

import java.math.BigDecimal;
import java.util.List;

public class RebalanceTransferBean {
    private String TARGET;
    private String MATERIAL;
    private String TRANSFER_FROM;
    private String TRANSFER_TO;
    private List<String> AVALIABLE_TRANSFER_TO;
    private BigDecimal TRANSFER_QTY;
    private BigDecimal TRANSFER_VALUE;
    private BigDecimal UNIT_COST;
    private String CLUSTER_NAME;
    private String ENTITY;
    private String BU;
    private String PRODUCT_LINE;
    private String PRODUCTION_LINE;
    private String LOCAL_BU;
    private String LOCAL_PRODUCT_LINE;
    private String LOCAL_PRODUCT_FAMILY;
    private String LOCAL_PRODUCT_SUBFAMILY;
    private String VENDOR_CODE;
    private String VENDOR_NAME;
    private String SOURCE_CATEGORY;
    private String MRP_CONTROLLER;
    private String TRANSFER_PROPOSAL;
    private String TRANSFER_TYEP;

    public RebalanceTransferBean() {

    }

    public RebalanceTransferBean(String plant, RebalanceBean rebalanceBean) throws Exception {
        this.MATERIAL = rebalanceBean.getMATERIAL();
        this.CLUSTER_NAME = rebalanceBean.getCLUSTER_NAME();
        this.ENTITY = rebalanceBean.getENTITY();
        this.BU = rebalanceBean.getBU();
        this.PRODUCT_LINE = rebalanceBean.getPRODUCT_LINE();
        this.PRODUCTION_LINE = rebalanceBean.getPRODUCTION_LINE();
        this.LOCAL_BU = rebalanceBean.getLOCAL_BU();
        this.LOCAL_PRODUCT_LINE = rebalanceBean.getLOCAL_PRODUCT_LINE();
        this.LOCAL_PRODUCT_FAMILY = rebalanceBean.getLOCAL_PRODUCT_FAMILY();
        this.LOCAL_PRODUCT_SUBFAMILY = rebalanceBean.getLOCAL_PRODUCT_SUBFAMILY();
        this.VENDOR_CODE = rebalanceBean.getVENDOR_CODE();
        this.VENDOR_NAME = rebalanceBean.getVENDOR_NAME();
        this.SOURCE_CATEGORY = rebalanceBean.getSOURCE_CATEGORY();
        this.MRP_CONTROLLER = rebalanceBean.getMRP_CONTROLLER();
        this.UNIT_COST = rebalanceBean.getUNIT_COST(plant);
    }

    public String getTARGET() {
        return TARGET;
    }

    public void setTARGET(String TARGET) {
        this.TARGET = TARGET;
    }

    public String getTRANSFER_TYEP() {
        return TRANSFER_TYEP;
    }

    public void setTRANSFER_TYEP(String TRANSFER_TYEP) {
        this.TRANSFER_TYEP = TRANSFER_TYEP;
    }

    /**
     * 是否已经找到了调库目标
     *
     * @return true/false
     */
    public boolean hasTransferTo() {
        return TRANSFER_TO != null && "".equalsIgnoreCase(TRANSFER_TO) == false;
    }

    public List<String> getAVALIABLE_TRANSFER_TO() {
        return AVALIABLE_TRANSFER_TO;
    }

    public void setAVALIABLE_TRANSFER_TO(List<String> AVALIABLE_TRANSFER_TO) {
        this.AVALIABLE_TRANSFER_TO = AVALIABLE_TRANSFER_TO;
    }

    public String getMATERIAL() {
        return MATERIAL;
    }

    public void setMATERIAL(String MATERIAL) {
        this.MATERIAL = MATERIAL;
    }

    public String getTRANSFER_FROM() {
        return TRANSFER_FROM;
    }

    public void setTRANSFER_FROM(String TRANSFER_FROM) {
        this.TRANSFER_FROM = TRANSFER_FROM;
    }

    public String getTRANSFER_TO() {
        return TRANSFER_TO;
    }

    public void setTRANSFER_TO(String TRANSFER_TO) {
        this.TRANSFER_TO = TRANSFER_TO;
    }

    public BigDecimal getUNIT_COST() {
        return UNIT_COST;
    }

    public void setUNIT_COST(BigDecimal UNIT_COST) {
        this.UNIT_COST = UNIT_COST;
    }

    public BigDecimal getTRANSFER_QTY() {
        return TRANSFER_QTY;
    }

    public void setTRANSFER_QTY(BigDecimal TRANSFER_QTY) {
        this.TRANSFER_QTY = TRANSFER_QTY;
        this.TRANSFER_VALUE = TRANSFER_QTY.multiply(this.UNIT_COST);
    }

    public BigDecimal getTRANSFER_VALUE() {
        return TRANSFER_VALUE;
    }

    public void setTRANSFER_VALUE(BigDecimal TRANSFER_VALUE) {
        this.TRANSFER_VALUE = TRANSFER_VALUE;
    }

    public String getCLUSTER_NAME() {
        return CLUSTER_NAME;
    }

    public void setCLUSTER_NAME(String CLUSTER_NAME) {
        this.CLUSTER_NAME = CLUSTER_NAME;
    }

    public String getENTITY() {
        return ENTITY;
    }

    public void setENTITY(String ENTITY) {
        this.ENTITY = ENTITY;
    }

    public String getBU() {
        return BU;
    }

    public void setBU(String BU) {
        this.BU = BU;
    }

    public String getPRODUCT_LINE() {
        return PRODUCT_LINE;
    }

    public void setPRODUCT_LINE(String PRODUCT_LINE) {
        this.PRODUCT_LINE = PRODUCT_LINE;
    }

    public String getPRODUCTION_LINE() {
        return PRODUCTION_LINE;
    }

    public void setPRODUCTION_LINE(String PRODUCTION_LINE) {
        this.PRODUCTION_LINE = PRODUCTION_LINE;
    }

    public String getLOCAL_BU() {
        return LOCAL_BU;
    }

    public void setLOCAL_BU(String LOCAL_BU) {
        this.LOCAL_BU = LOCAL_BU;
    }

    public String getLOCAL_PRODUCT_LINE() {
        return LOCAL_PRODUCT_LINE;
    }

    public void setLOCAL_PRODUCT_LINE(String LOCAL_PRODUCT_LINE) {
        this.LOCAL_PRODUCT_LINE = LOCAL_PRODUCT_LINE;
    }

    public String getLOCAL_PRODUCT_FAMILY() {
        return LOCAL_PRODUCT_FAMILY;
    }

    public void setLOCAL_PRODUCT_FAMILY(String LOCAL_PRODUCT_FAMILY) {
        this.LOCAL_PRODUCT_FAMILY = LOCAL_PRODUCT_FAMILY;
    }

    public String getLOCAL_PRODUCT_SUBFAMILY() {
        return LOCAL_PRODUCT_SUBFAMILY;
    }

    public void setLOCAL_PRODUCT_SUBFAMILY(String LOCAL_PRODUCT_SUBFAMILY) {
        this.LOCAL_PRODUCT_SUBFAMILY = LOCAL_PRODUCT_SUBFAMILY;
    }

    public String getVENDOR_CODE() {
        return VENDOR_CODE;
    }

    public void setVENDOR_CODE(String VENDOR_CODE) {
        this.VENDOR_CODE = VENDOR_CODE;
    }

    public String getVENDOR_NAME() {
        return VENDOR_NAME;
    }

    public void setVENDOR_NAME(String VENDOR_NAME) {
        this.VENDOR_NAME = VENDOR_NAME;
    }

    public String getSOURCE_CATEGORY() {
        return SOURCE_CATEGORY;
    }

    public void setSOURCE_CATEGORY(String SOURCE_CATEGORY) {
        this.SOURCE_CATEGORY = SOURCE_CATEGORY;
    }

    public String getMRP_CONTROLLER() {
        return MRP_CONTROLLER;
    }

    public void setMRP_CONTROLLER(String MRP_CONTROLLER) {
        this.MRP_CONTROLLER = MRP_CONTROLLER;
    }

    public String getTRANSFER_PROPOSAL() {
        return TRANSFER_PROPOSAL;
    }

    public void setTRANSFER_PROPOSAL(String TRANSFER_PROPOSAL) {
        this.TRANSFER_PROPOSAL = TRANSFER_PROPOSAL;
    }
}
