package com.scp.inventory.bean;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class RebalanceBean {
    private String MATERIAL;
    private String O001_DELIVERING_PLANT;
    private String N001_DELIVERING_PLANT;
    private String M001_DELIVERING_PLANT;
    private String I001_DELIVERING_PLANT;
    private String I003_DELIVERING_PLANT;
    private BigDecimal O001_ONE_MM_AMU;
    private BigDecimal N001_ONE_MM_AMU;
    private BigDecimal M001_ONE_MM_AMU;
    private BigDecimal I001_ONE_MM_AMU;
    private BigDecimal I003_ONE_MM_AMU;
    private BigDecimal O001_UU_STOCK_NEW;
    private BigDecimal N001_UU_STOCK_NEW;
    private BigDecimal M001_UU_STOCK_NEW;
    private BigDecimal I001_UU_STOCK_NEW;
    private BigDecimal I003_UU_STOCK_NEW;
    private BigDecimal O001_UNIT_COST;
    private BigDecimal N001_UNIT_COST;
    private BigDecimal M001_UNIT_COST;
    private BigDecimal I001_UNIT_COST;
    private BigDecimal I003_UNIT_COST;
    private BigDecimal O001_ROUNDING_VALUE;
    private BigDecimal N001_ROUNDING_VALUE;
    private BigDecimal M001_ROUNDING_VALUE;
    private BigDecimal I001_ROUNDING_VALUE;
    private BigDecimal I003_ROUNDING_VALUE;
    private String CLUSTER_NAME;
    private String ENTITY;
    private String BU;
    private String PRODUCT_LINE;
    private String PRODUCTION_LINE;
    private String LOCAL_BU;
    private String LOCAL_PRODUCT_LINE;
    private String LOCAL_PRODUCT_FAMILY;
    private String LOCAL_PRODUCT_SUBFAMILY;
    private String VENDOR_CODE;
    private String VENDOR_NAME;
    private String SOURCE_CATEGORY;
    private String MRP_CONTROLLER;
    private String TRANSFER_PROPOSAL;

    private String MATERIAL_OWNER_NAME;

    private String MATERIAL_OWNER_SESA;

    private BigDecimal O001_UU_STOCK_TEMP = BigDecimal.ZERO;
    private BigDecimal N001_UU_STOCK_TEMP = BigDecimal.ZERO;
    private BigDecimal M001_UU_STOCK_TEMP = BigDecimal.ZERO;
    private BigDecimal I001_UU_STOCK_TEMP = BigDecimal.ZERO;
    private BigDecimal I003_UU_STOCK_TEMP = BigDecimal.ZERO;

    public String getDELIVERING_PLANT(String plantCode) throws Exception {
        Method method = RebalanceBean.class.getMethod("get" + plantCode + "_DELIVERING_PLANT");
        return (String) method.invoke(this);
    }

    public BigDecimal getONE_MM_AMU(String plantCode) throws Exception {
        Method method = RebalanceBean.class.getMethod("get" + plantCode + "_ONE_MM_AMU");
        BigDecimal amu = (BigDecimal) method.invoke(this);
        return amu == null ? BigDecimal.ZERO : amu;
    }

    public BigDecimal getUU_STOCK_NEW(String plantCode) throws Exception {
        Method method = RebalanceBean.class.getMethod("get" + plantCode + "_UU_STOCK_NEW");
        return (BigDecimal) method.invoke(this);
    }

    public BigDecimal getUU_STOCK_TEMP(String plantCode) throws Exception {
        Method method = RebalanceBean.class.getMethod("get" + plantCode + "_UU_STOCK_TEMP");
        return (BigDecimal) method.invoke(this);
    }

    public void setUU_STOCK_TEMP(String plantCode, BigDecimal value) throws Exception {
        Method method = RebalanceBean.class.getMethod("set" + plantCode + "_UU_STOCK_TEMP", BigDecimal.class);
        method.invoke(this, value);
    }

    public BigDecimal getUNIT_COST(String plantCode) throws Exception {
        Method method = RebalanceBean.class.getMethod("get" + plantCode + "_UNIT_COST");
        return (BigDecimal) method.invoke(this);
    }

    public BigDecimal getROUNDING_VALUE(String plantCode) throws Exception {
        Method method = RebalanceBean.class.getMethod("get" + plantCode + "_ROUNDING_VALUE");
        return (BigDecimal) method.invoke(this);
    }

    /**
     * 获取AMU可以用时间(月)
     *
     * @param plant DC代码
     * @return 可用月份; -1, 无需求;
     * @throws Exception 调用异常
     */
    public BigDecimal getAvailableMonth(String plant) throws Exception {
        BigDecimal uustock = (BigDecimal) RebalanceBean.class.getMethod("get" + plant + "_UU_STOCK_NEW").invoke(this);
        BigDecimal amu = (BigDecimal) RebalanceBean.class.getMethod("get" + plant + "_ONE_MM_AMU").invoke(this);
        if (amu == null || amu.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.valueOf(-1);
        } else {
            return uustock.divide(amu, 2, RoundingMode.HALF_UP);
        }
    }

    /**
     * 获取最大可调配数量
     *
     * @param plant  DC代码
     * @param target 目标使用月
     * @return 需要调拨的数量
     */
    public BigDecimal getMaxRoundingQty(String plant, BigDecimal target) throws Exception {
        BigDecimal totalQty = this.getUU_STOCK_NEW(plant);
        BigDecimal keepQty = this.getONE_MM_AMU(plant).multiply(target);
        BigDecimal roundingQty = this.getROUNDING_VALUE(plant);
        BigDecimal gap = totalQty.subtract(keepQty);
        if (roundingQty.compareTo(BigDecimal.ZERO) == 0) { // roundingQty为0, 则无最小调拨数量
            return gap;
        } else {
            return roundingQty.multiply(gap.divideToIntegralValue(roundingQty));
        }
    }

    /**
     * 获取当前Plant有多少空闲库位
     *
     * @param plant DC代码
     * @return 空闲库位
     */
    public BigDecimal getFreeStockLocation(String plant, BigDecimal target) throws Exception {
        BigDecimal max = this.getONE_MM_AMU(plant).multiply(target);
        BigDecimal exists = this.getUU_STOCK_TEMP(plant);
        BigDecimal free = max.subtract(this.getUU_STOCK_NEW(plant)).subtract(exists);
        return free.compareTo(BigDecimal.ZERO) > 0 ? free : BigDecimal.ZERO;
    }

    public void addUU_STOCK_TEMP(String plant, BigDecimal add) throws Exception {
        BigDecimal exists = this.getUU_STOCK_TEMP(plant);
        this.setUU_STOCK_TEMP(plant, exists.add(add));
    }

    /**
     * 获取可被调拨的DC列表
     *
     * @return 可被调拨的DC
     */
    static List<String> plants = Arrays.asList("O001", "N001", "M001", "I001", "I003");

    public List<String> getAvailableTo() {
        return Stream.of(O001_DELIVERING_PLANT, N001_DELIVERING_PLANT, M001_DELIVERING_PLANT, I001_DELIVERING_PLANT, I003_DELIVERING_PLANT).filter(e -> plants.contains(e)).distinct().collect(Collectors.toList());
    }

    /**
     * 根据调拨数量试算最大可接收数量
     *
     * @param plant       DC代码
     * @param transferQty 调拨数量
     * @return 最大需求数量
     */
    public String getTransferWeight(String plant, BigDecimal transferQty, BigDecimal target, List<String> logs) throws Exception {
        BigDecimal exists = this.getUU_STOCK_NEW(plant).add(this.getUU_STOCK_TEMP(plant)).add(transferQty);
        BigDecimal max = this.getONE_MM_AMU(plant).multiply(target);
        logs.add("\t\t" + plant + "已有库存=" + exists + ", 允许最大库存=" + this.getONE_MM_AMU(plant) + "*" + target + "=" + max + ", 最多可接收=" + max.subtract(exists));
        return max.subtract(exists).toPlainString();
    }

    public BigDecimal getO001_ROUNDING_VALUE() {
        return O001_ROUNDING_VALUE;
    }

    public void setO001_ROUNDING_VALUE(BigDecimal o001_ROUNDING_VALUE) {
        O001_ROUNDING_VALUE = o001_ROUNDING_VALUE;
    }

    public BigDecimal getN001_ROUNDING_VALUE() {
        return N001_ROUNDING_VALUE;
    }

    public void setN001_ROUNDING_VALUE(BigDecimal n001_ROUNDING_VALUE) {
        N001_ROUNDING_VALUE = n001_ROUNDING_VALUE;
    }

    public BigDecimal getM001_ROUNDING_VALUE() {
        return M001_ROUNDING_VALUE;
    }

    public void setM001_ROUNDING_VALUE(BigDecimal m001_ROUNDING_VALUE) {
        M001_ROUNDING_VALUE = m001_ROUNDING_VALUE;
    }

    public BigDecimal getI001_ROUNDING_VALUE() {
        return I001_ROUNDING_VALUE;
    }

    public void setI001_ROUNDING_VALUE(BigDecimal i001_ROUNDING_VALUE) {
        I001_ROUNDING_VALUE = i001_ROUNDING_VALUE;
    }

    public BigDecimal getI003_ROUNDING_VALUE() {
        return I003_ROUNDING_VALUE;
    }

    public void setI003_ROUNDING_VALUE(BigDecimal i003_ROUNDING_VALUE) {
        I003_ROUNDING_VALUE = i003_ROUNDING_VALUE;
    }

    public String getMATERIAL() {
        return MATERIAL;
    }

    public void setMATERIAL(String MATERIAL) {
        this.MATERIAL = MATERIAL;
    }

    public String getO001_DELIVERING_PLANT() {
        return O001_DELIVERING_PLANT;
    }

    public void setO001_DELIVERING_PLANT(String o001_DELIVERING_PLANT) {
        O001_DELIVERING_PLANT = o001_DELIVERING_PLANT;
    }

    public String getN001_DELIVERING_PLANT() {
        return N001_DELIVERING_PLANT;
    }

    public void setN001_DELIVERING_PLANT(String n001_DELIVERING_PLANT) {
        N001_DELIVERING_PLANT = n001_DELIVERING_PLANT;
    }

    public String getM001_DELIVERING_PLANT() {
        return M001_DELIVERING_PLANT;
    }

    public void setM001_DELIVERING_PLANT(String m001_DELIVERING_PLANT) {
        M001_DELIVERING_PLANT = m001_DELIVERING_PLANT;
    }

    public String getI001_DELIVERING_PLANT() {
        return I001_DELIVERING_PLANT;
    }

    public void setI001_DELIVERING_PLANT(String i001_DELIVERING_PLANT) {
        I001_DELIVERING_PLANT = i001_DELIVERING_PLANT;
    }

    public String getI003_DELIVERING_PLANT() {
        return I003_DELIVERING_PLANT;
    }

    public void setI003_DELIVERING_PLANT(String i003_DELIVERING_PLANT) {
        I003_DELIVERING_PLANT = i003_DELIVERING_PLANT;
    }

    public BigDecimal getO001_ONE_MM_AMU() {
        return O001_ONE_MM_AMU;
    }

    public void setO001_ONE_MM_AMU(BigDecimal o001_ONE_MM_AMU) {
        O001_ONE_MM_AMU = o001_ONE_MM_AMU;
    }

    public BigDecimal getN001_ONE_MM_AMU() {
        return N001_ONE_MM_AMU;
    }

    public void setN001_ONE_MM_AMU(BigDecimal n001_ONE_MM_AMU) {
        N001_ONE_MM_AMU = n001_ONE_MM_AMU;
    }

    public BigDecimal getM001_ONE_MM_AMU() {
        return M001_ONE_MM_AMU;
    }

    public void setM001_ONE_MM_AMU(BigDecimal m001_ONE_MM_AMU) {
        M001_ONE_MM_AMU = m001_ONE_MM_AMU;
    }

    public BigDecimal getI001_ONE_MM_AMU() {
        return I001_ONE_MM_AMU;
    }

    public void setI001_ONE_MM_AMU(BigDecimal i001_ONE_MM_AMU) {
        I001_ONE_MM_AMU = i001_ONE_MM_AMU;
    }

    public BigDecimal getI003_ONE_MM_AMU() {
        return I003_ONE_MM_AMU;
    }

    public void setI003_ONE_MM_AMU(BigDecimal i003_ONE_MM_AMU) {
        I003_ONE_MM_AMU = i003_ONE_MM_AMU;
    }

    public BigDecimal getO001_UU_STOCK_NEW() {
        return O001_UU_STOCK_NEW;
    }

    public void setO001_UU_STOCK_NEW(BigDecimal o001_UU_STOCK_NEW) {
        O001_UU_STOCK_NEW = o001_UU_STOCK_NEW;
    }

    public BigDecimal getN001_UU_STOCK_NEW() {
        return N001_UU_STOCK_NEW;
    }

    public void setN001_UU_STOCK_NEW(BigDecimal n001_UU_STOCK_NEW) {
        N001_UU_STOCK_NEW = n001_UU_STOCK_NEW;
    }

    public BigDecimal getM001_UU_STOCK_NEW() {
        return M001_UU_STOCK_NEW;
    }

    public void setM001_UU_STOCK_NEW(BigDecimal m001_UU_STOCK_NEW) {
        M001_UU_STOCK_NEW = m001_UU_STOCK_NEW;
    }

    public BigDecimal getI001_UU_STOCK_NEW() {
        return I001_UU_STOCK_NEW;
    }

    public void setI001_UU_STOCK_NEW(BigDecimal i001_UU_STOCK_NEW) {
        I001_UU_STOCK_NEW = i001_UU_STOCK_NEW;
    }

    public BigDecimal getI003_UU_STOCK_NEW() {
        return I003_UU_STOCK_NEW;
    }

    public void setI003_UU_STOCK_NEW(BigDecimal i003_UU_STOCK_NEW) {
        I003_UU_STOCK_NEW = i003_UU_STOCK_NEW;
    }

    public BigDecimal getO001_UNIT_COST() {
        return O001_UNIT_COST;
    }

    public void setO001_UNIT_COST(BigDecimal o001_UNIT_COST) {
        O001_UNIT_COST = o001_UNIT_COST;
    }

    public BigDecimal getN001_UNIT_COST() {
        return N001_UNIT_COST;
    }

    public void setN001_UNIT_COST(BigDecimal n001_UNIT_COST) {
        N001_UNIT_COST = n001_UNIT_COST;
    }

    public BigDecimal getM001_UNIT_COST() {
        return M001_UNIT_COST;
    }

    public void setM001_UNIT_COST(BigDecimal m001_UNIT_COST) {
        M001_UNIT_COST = m001_UNIT_COST;
    }

    public BigDecimal getI001_UNIT_COST() {
        return I001_UNIT_COST;
    }

    public void setI001_UNIT_COST(BigDecimal i001_UNIT_COST) {
        I001_UNIT_COST = i001_UNIT_COST;
    }

    public BigDecimal getI003_UNIT_COST() {
        return I003_UNIT_COST;
    }

    public void setI003_UNIT_COST(BigDecimal i003_UNIT_COST) {
        I003_UNIT_COST = i003_UNIT_COST;
    }

    public String getCLUSTER_NAME() {
        return CLUSTER_NAME;
    }

    public void setCLUSTER_NAME(String CLUSTER_NAME) {
        this.CLUSTER_NAME = CLUSTER_NAME;
    }

    public String getENTITY() {
        return ENTITY;
    }

    public void setENTITY(String ENTITY) {
        this.ENTITY = ENTITY;
    }

    public String getBU() {
        return BU;
    }

    public void setBU(String BU) {
        this.BU = BU;
    }

    public String getPRODUCT_LINE() {
        return PRODUCT_LINE;
    }

    public void setPRODUCT_LINE(String PRODUCT_LINE) {
        this.PRODUCT_LINE = PRODUCT_LINE;
    }

    public String getPRODUCTION_LINE() {
        return PRODUCTION_LINE;
    }

    public void setPRODUCTION_LINE(String PRODUCTION_LINE) {
        this.PRODUCTION_LINE = PRODUCTION_LINE;
    }

    public String getLOCAL_BU() {
        return LOCAL_BU;
    }

    public void setLOCAL_BU(String LOCAL_BU) {
        this.LOCAL_BU = LOCAL_BU;
    }

    public String getLOCAL_PRODUCT_LINE() {
        return LOCAL_PRODUCT_LINE;
    }

    public void setLOCAL_PRODUCT_LINE(String LOCAL_PRODUCT_LINE) {
        this.LOCAL_PRODUCT_LINE = LOCAL_PRODUCT_LINE;
    }

    public String getLOCAL_PRODUCT_FAMILY() {
        return LOCAL_PRODUCT_FAMILY;
    }

    public void setLOCAL_PRODUCT_FAMILY(String LOCAL_PRODUCT_FAMILY) {
        this.LOCAL_PRODUCT_FAMILY = LOCAL_PRODUCT_FAMILY;
    }

    public String getLOCAL_PRODUCT_SUBFAMILY() {
        return LOCAL_PRODUCT_SUBFAMILY;
    }

    public void setLOCAL_PRODUCT_SUBFAMILY(String LOCAL_PRODUCT_SUBFAMILY) {
        this.LOCAL_PRODUCT_SUBFAMILY = LOCAL_PRODUCT_SUBFAMILY;
    }

    public String getVENDOR_CODE() {
        return VENDOR_CODE;
    }

    public void setVENDOR_CODE(String VENDOR_CODE) {
        this.VENDOR_CODE = VENDOR_CODE;
    }

    public String getVENDOR_NAME() {
        return VENDOR_NAME;
    }

    public void setVENDOR_NAME(String VENDOR_NAME) {
        this.VENDOR_NAME = VENDOR_NAME;
    }

    public String getSOURCE_CATEGORY() {
        return SOURCE_CATEGORY;
    }

    public void setSOURCE_CATEGORY(String SOURCE_CATEGORY) {
        this.SOURCE_CATEGORY = SOURCE_CATEGORY;
    }

    public String getMRP_CONTROLLER() {
        return MRP_CONTROLLER;
    }

    public void setMRP_CONTROLLER(String MRP_CONTROLLER) {
        this.MRP_CONTROLLER = MRP_CONTROLLER;
    }

    public String getTRANSFER_PROPOSAL() {
        return TRANSFER_PROPOSAL;
    }

    public void setTRANSFER_PROPOSAL(String TRANSFER_PROPOSAL) {
        this.TRANSFER_PROPOSAL = TRANSFER_PROPOSAL;
    }

    public String getMATERIAL_OWNER_NAME() {
        return MATERIAL_OWNER_NAME;
    }

    public void setMATERIAL_OWNER_NAME(String MATERIAL_OWNER_NAME) {
        this.MATERIAL_OWNER_NAME = MATERIAL_OWNER_NAME;
    }

    public String getMATERIAL_OWNER_SESA() {
        return MATERIAL_OWNER_SESA;
    }

    public void setMATERIAL_OWNER_SESA(String MATERIAL_OWNER_SESA) {
        this.MATERIAL_OWNER_SESA = MATERIAL_OWNER_SESA;
    }

    public BigDecimal getO001_UU_STOCK_TEMP() {
        return O001_UU_STOCK_TEMP;
    }

    public void setO001_UU_STOCK_TEMP(BigDecimal o001_UU_STOCK_TEMP) {
        O001_UU_STOCK_TEMP = o001_UU_STOCK_TEMP;
    }

    public BigDecimal getN001_UU_STOCK_TEMP() {
        return N001_UU_STOCK_TEMP;
    }

    public void setN001_UU_STOCK_TEMP(BigDecimal n001_UU_STOCK_TEMP) {
        N001_UU_STOCK_TEMP = n001_UU_STOCK_TEMP;
    }

    public BigDecimal getM001_UU_STOCK_TEMP() {
        return M001_UU_STOCK_TEMP;
    }

    public void setM001_UU_STOCK_TEMP(BigDecimal m001_UU_STOCK_TEMP) {
        M001_UU_STOCK_TEMP = m001_UU_STOCK_TEMP;
    }

    public BigDecimal getI001_UU_STOCK_TEMP() {
        return I001_UU_STOCK_TEMP;
    }

    public void setI001_UU_STOCK_TEMP(BigDecimal i001_UU_STOCK_TEMP) {
        I001_UU_STOCK_TEMP = i001_UU_STOCK_TEMP;
    }

    public BigDecimal getI003_UU_STOCK_TEMP() {
        return I003_UU_STOCK_TEMP;
    }

    public void setI003_UU_STOCK_TEMP(BigDecimal i003_UU_STOCK_TEMP) {
        I003_UU_STOCK_TEMP = i003_UU_STOCK_TEMP;
    }
}
