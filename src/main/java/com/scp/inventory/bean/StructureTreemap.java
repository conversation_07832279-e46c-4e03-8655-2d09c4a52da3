package com.scp.inventory.bean;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.math.BigDecimal;
import java.util.*;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class StructureTreemap {
    private String name;
    private BigDecimal value;
    private double k = -9999;
    private Report1Tooltips tips = new Report1Tooltips();
    private Map<String, String> itemStyle = null;
    private List<StructureTreemap> children;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public Map<String, String> getItemStyle() {
        return itemStyle;
    }

    public void setItemStyle(Map<String, String> itemStyle) {
        this.itemStyle = itemStyle;
    }

    public List<StructureTreemap> getChildren() {
        if (children == null) {
            children = new ArrayList<>();
        }
        return children;
    }

    public boolean hasChildren() {
        return this.getChildren() != null && this.getChildren().isEmpty() == false;
    }

    public void setChildren(List<StructureTreemap> children) {
        this.children = children;
    }

    public void setTips(Report1Tooltips tips) {
        this.tips = tips;
    }

    public Report1Tooltips getTips() {
        return tips;
    }

    public void setColor(String color) {
        if (itemStyle == null) {
            itemStyle = new HashMap<>();
        }
        itemStyle.put("color", color);
    }

    public double getK() {
        if (k == -9999) {
            return this.tips.getK().doubleValue();
        }
        return k;
    }

    public void setK(double k) {
        this.k = k;
        this.tips.setK(BigDecimal.valueOf(k));
    }

    // 合并两个节点
    public void add(StructureTreemap addElement) throws Exception {
        StructureTreemap mainElement = this;

        mainElement.getTips().add(addElement.getTips()); // 先相加根节点

        // 再相加子节点
        while (addElement.hasChildren()) {
            List<StructureTreemap> mainChildren = mainElement.getChildren();
            StructureTreemap child = addElement.getChildren().get(0); // 加数节点只有一个子节点

            Optional<StructureTreemap> beanOpt = mainChildren.stream().filter(b -> b.getName().equals(child.getName())).findFirst();
            if (beanOpt.isPresent()) {
                StructureTreemap bean = beanOpt.get();
                bean.getTips().add(child.getTips()); // 如果找到了, 那就合并两个子节点

                // 向下移动一层
                addElement = child;
                mainElement = bean;
            } else {
                mainChildren.add(child);// 如果找不到子节点, 那直接把需要相加的节点附在这个子节点下面
                break; // 然后直接跳出循环, 相加结束
            }
        }
    }
}
