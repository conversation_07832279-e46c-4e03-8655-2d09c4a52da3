package com.scp.inventory.bean;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class UHSReport2 {
    private List<String> xAxis = new ArrayList<>();
    private List<BigDecimal> yAxis1 = new ArrayList<>();
    private List<BigDecimal> yAxis2 = new ArrayList<>();
    private List<BigDecimal> yAxis3 = new ArrayList<>();
    private List<BigDecimal> yAxis4 = new ArrayList<>();
    private List<BigDecimal> yAxis5 = new ArrayList<>();

    public List<String> getxAxis() {
        return xAxis;
    }

    public void setxAxis(List<String> xAxis) {
        this.xAxis = xAxis;
    }

    public void addxAxis(String x) {
        this.xAxis.add(x);
    }

    public List<BigDecimal> getyAxis1() {
        return yAxis1;
    }

    public void setyAxis1(List<BigDecimal> yAxis1) {
        this.yAxis1 = yAxis1;
    }

    public void addyAxis1(BigDecimal y1) {
        this.yAxis1.add(y1);
    }

    public void addyAxis2(BigDecimal y2) {
        this.yAxis2.add(y2);
    }

    public void addyAxis3(BigDecimal y3) {
        this.yAxis3.add(y3);
    }

    public void addyAxis4(BigDecimal y4) {
        this.yAxis4.add(y4);
    }

    public void addyAxis5(BigDecimal y5) {
        this.yAxis5.add(y5);
    }

    public List<BigDecimal> getyAxis2() {
        return yAxis2;
    }

    public void setyAxis2(List<BigDecimal> yAxis2) {
        this.yAxis2 = yAxis2;
    }

    public List<BigDecimal> getyAxis3() {
        return yAxis3;
    }

    public void setyAxis3(List<BigDecimal> yAxis3) {
        this.yAxis3 = yAxis3;
    }

    public List<BigDecimal> getyAxis4() {
        return yAxis4;
    }

    public void setyAxis4(List<BigDecimal> yAxis4) {
        this.yAxis4 = yAxis4;
    }

    public List<BigDecimal> getyAxis5() {
        return yAxis5;
    }

    public void setyAxis5(List<BigDecimal> yAxis5) {
        this.yAxis5 = yAxis5;
    }
}
