package com.scp.inventory.bean;

import java.math.BigDecimal;

public class DeliveryReport1Pie {
    private String name;
    private BigDecimal value;
    private DeliveryReport1PieTooltips tooltips = new DeliveryReport1PieTooltips();

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public DeliveryReport1PieTooltips getTooltips() {
        return tooltips;
    }

    public void setTooltips(DeliveryReport1PieTooltips tooltips) {
        this.tooltips = tooltips;
    }
}
