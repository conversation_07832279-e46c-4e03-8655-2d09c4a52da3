package com.scp.inventory.bean;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class AlertReport1Column {
    private String name;
    private Object value;

    public AlertReport1Column(String name, Object value) {
        this.name = name;
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Object getValue() {
        return value;
    }

    public double getValuePercent() {
        if (value instanceof Integer) {
            return BigDecimal.valueOf(((Integer) value) / 100.0).setScale(2, RoundingMode.HALF_UP).doubleValue();
        } else {
            return 0;
        }
    }

    public void setValue(Object value) {
        this.value = value;
    }
}
