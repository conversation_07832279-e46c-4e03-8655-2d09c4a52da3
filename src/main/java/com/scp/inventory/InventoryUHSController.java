package com.scp.inventory;

import com.scp.inventory.service.IInventoryUHSService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.util.WebUtils;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/inventory/uhs", parent = "menu740")
public class InventoryUHSController extends ControllerHelper {

    @Resource
    private IInventoryUHSService inventoryUHSService;

    @Resource
    private Response response;

    @SchneiderRequestMapping("/init_page")
    public Response initPage() {
        super.setGlobalCache(true);
        return inventoryUHSService.initPage();
    }

    @SchneiderRequestMapping("/query_report1_weeks")
    public Response queryReport1Weeks(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return inventoryUHSService.queryReport1Weeks(parameterMap);
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return inventoryUHSService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping("/query_report1_details")
    public Response queryReport1Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return inventoryUHSService.queryReport1Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report1_details")
    public void downloadReport1Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        inventoryUHSService.downloadReport1Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return inventoryUHSService.queryReport2(parameterMap);
    }

    @SchneiderRequestMapping("/query_report3")
    public Response queryReport3(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return inventoryUHSService.queryReport3(parameterMap);
    }

    @SchneiderRequestMapping("/save_report3")
    public Response saveReport3(HttpServletRequest request) {
        super.pageLoad(request);
        return inventoryUHSService.saveReport3(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping("/download_report3")
    public void downloadReport3(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        inventoryUHSService.downloadReport3(parameterMap, response);
    }

    @SchneiderRequestMapping("/download_report3_recommend_rca")
    public void downloadReport3RecommendRCA(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        inventoryUHSService.downloadReport3RecommendRCA(parameterMap, response);
    }

    @SchneiderRequestMapping("/download_report3_one_mm")
    public void downloadReport3OneMM(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        inventoryUHSService.downloadReport3OneMM(parameterMap, response);
    }

    @SchneiderRequestMapping("/download_report3_result_template")
    public void downloadReport3ResultTemplate(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        inventoryUHSService.downloadReport3ResultTemplate(parameterMap, response);
    }

    @SchneiderRequestMapping("/upload_report3_result")
    public Response uploadReport3Result(HttpServletRequest request) {
        super.pageLoad(request);
        MultipartHttpServletRequest multipartRequest = WebUtils.getNativeRequest(request, MultipartHttpServletRequest.class);
        if (multipartRequest != null) {
            MultipartFile file = multipartRequest.getFile("file");
            try {
                return inventoryUHSService.uploadReport3Result(session.getUserid(), file);
            } catch (Exception e) {
                return response.setError(e);
            }
        } else {
            return response.setError(new Exception("upload failed, multipartRequest is null !"));
        }
    }
}
