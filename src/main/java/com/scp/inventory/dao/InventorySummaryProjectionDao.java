package com.scp.inventory.dao;

import com.starter.context.bean.scptable.ScpTableCell;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface InventorySummaryProjectionDao {

    int queryPageAdmin(@Param("userid") String userid, @Param("parentCode") String parentCode);

    int queryMaterialOwnerCount(@Param("userid") String userid);

    List<Map<String, String>> queryCascader();

    List<Map<String, String>> queryProjectionCascader();

    List<String> queryRcaCode();

    List<Map<String, Object>> queryInventoryRCAList();

    List<String> queryProjectionVersionOpts();

    List<String> queryAvailableOpts(@Param("userid") String userid, @Param("column") String column);

    int queryReport1Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1(Map<String, Object> parameterMap);

    int queryReport1DetailsCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport1Details(Map<String, Object> parameterMap);

    int queryReport2Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport2(Map<String, Object> parameterMap);

    int createReport2ByTable(@Param("headers") List<String> headers, @Param("creates") List<Map<String, Object>> creates, @Param("userid") String userid, @Param("version") String version);

    int deleteReport2ByTable(@Param("deletes") List<String> deletes, @Param("userid") String userid, @Param("version") String version);

    int updateReport2ByTable(@Param("rowid") String rowid, @Param("updates") List<ScpTableCell> updates, @Param("userid") String userid, @Param("version") String version);

    List<Map<String, Object>> queryProjectionUnSplitList();

    void updateProjectionStatus(Map<String, Object> map);

    void splitInventoryProjection(Map<String, Object> map);

    BigDecimal queryTotalByConditions(Map<String, Object> map);

    int queryReport2DetailsCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport2Details(Map<String, Object> parameterMap);

    void deleteUnusedProjection();

    List<String> queryInvalidMaterialList(@Param("userid") String userid);

    void insertTempMaterial(@Param("list") List<String> list);

    int queryReport3Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3(Map<String, Object> parameterMap);
}
