package com.scp.inventory.dao;

import com.scp.inventory.bean.ExcessSOStockReport1Bean;
import com.scp.inventory.bean.ExcessSOStockReport2Bean;
import io.netty.handler.codec.serialization.ObjectEncoder;
import org.apache.ibatis.annotations.Mapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IExcessSOStockDao {

    List<Map<String, String>> queryCascader();

    List<ExcessSOStockReport1Bean> queryReport1(Map<String, Object> parameterMap);

    List<ExcessSOStockReport2Bean> queryReport2(Map<String, Object> parameterMap);

    int queryReport2DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport2Details(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3(Map<String, Object> parameterMap);

    List<String> queryReport3Columns(Map<String, Object> parameterMap);

    int queryReport3DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3Details(Map<String, Object> parameterMap);

}
