package com.scp.inventory.dao;

import com.scp.inventory.bean.UHSStructureReport5Bean;
import org.apache.ibatis.annotations.Mapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IUHSStructureDao {

    List<Map<String, String>> queryCascader();

    List<String> queryWeeks();

    Map<String, Object> queryReport1(Map<String, Object> parameterMap);

    int queryReport2Count(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport2(Map<String, Object> parameterMap);

    double queryCurrencyByWeek(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport3(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport4(Map<String, Object> parameterMap);

    int queryReport1DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1Details(Map<String, Object> parameterMap);

    List<UHSStructureReport5Bean> queryReport5(Map<String, Object> parameterMap);
}
