package com.scp.inventory.dao;

import com.scp.inventory.bean.DeliveryReport1Pie;
import com.scp.inventory.bean.DeliveryReport2Data;
import org.apache.ibatis.annotations.Mapper;
import com.scp.inventory.bean.InventoryDeliveryReport4Bean;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IInventoryDeliveryDao {

    List<Map<String, String>> queryCascader();

    List<DeliveryReport1Pie> queryReport1DetailsChart(Map<String, Object> parameterMap);

    int queryReport1DetailsTableCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1DetailsTable(Map<String, Object> parameterMap);

    List<DeliveryReport2Data> queryReport2(Map<String, Object> parameterMap);

    List<String> queryReport2Legend(Map<String, Object> parameterMap);

    int queryReport2DetailsCount(Map<String, Object> parameterMap);

    List<DeliveryReport1Pie> queryReport1(Map<String, Object> parameterMap);

    int queryReport1DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1Details(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport2Details(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3(Map<String, Object> parameterMap);

    int queryReport3DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3Details(Map<String, Object> parameterMap);

    List<InventoryDeliveryReport4Bean> queryReport4(Map<String, Object> parameterMap);
}
