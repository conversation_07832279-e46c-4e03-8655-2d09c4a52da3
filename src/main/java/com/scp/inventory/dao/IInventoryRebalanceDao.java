package com.scp.inventory.dao;

import com.scp.inventory.bean.RebalanceBean;
import com.scp.inventory.bean.RebalanceTransferBean;
import com.scp.inventory.bean.RebalanceTransferLogs;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IInventoryRebalanceDao {

    List<RebalanceBean> queryRebalanceList();

    void saveRebalanceResult(@Param("list") List<RebalanceTransferBean> resultList);

    void saveRebalanceLogs(@Param("list") List<RebalanceTransferLogs> resultList);

    void truncateTransferTable();

    void truncateTransferLogsTable();

    List<Map<String, String>> queryRebalanceCascader();

    List<LinkedHashMap<String, Object>> queryReport1(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport2(Map<String, Object> parameterMap);

    int queryReport2DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport2Details(Map<String, Object> parameterMap);

    Object queryReport2DetailsLogs(Map<String, Object> parameterMap);

    int queryReport1DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1Details(Map<String, Object> parameterMap);
}
