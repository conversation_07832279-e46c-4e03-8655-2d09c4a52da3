package com.scp.inventory.dao;

import com.scp.inventory.bean.SliceCurrency;
import com.scp.inventory.bean.SliceRawData;
import com.starter.context.bean.scptable.ScpTableCell;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IMonthlySliceReportDao {

    List<String> querySliceVersion();

    List<String> queryAvailableEntity();

    List<String> queryRcaCode();

    List<Map<String, String>> queryCascader();

    List<Map<String, Object>> queryInventoryRCAList();

    List<SliceRawData> queryReport1(Map<String, Object> parameterMap);

    void syncForex(@Param("version") String version);

    int queryExistsResult(Map<String, Object> parameterMap);

    List<SliceCurrency> getCurrency(@Param("version") String version);

    List<SliceRawData> queryRawData(String version);

    void deleteExistReport(@Param("version") String version);

    void saveReport(@Param("version") String version, @Param("rawData") List<SliceRawData> rawData);

    List<Map<String, Object>> queryTodaySOH();

    List<SliceRawData> queryProjection(Map<String, Object> parameterMap);

    int queryReport2Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport2(Map<String, Object> parameterMap);

    int createReport2ByTable(@Param("headers") List<String> headers, @Param("creates") List<Map<String, Object>> creates, @Param("userid") String userid, @Param("version") String version);

    int deleteReport2ByTable(@Param("deletes") List<String> deletes, @Param("userid") String userid, @Param("version") String version, @Param("isAdmin") boolean isAdmin);

    int updateReport2ByTable(@Param("rowid") String rowid, @Param("updates") List<ScpTableCell> updates, @Param("userid") String userid, @Param("version") String version, @Param("isAdmin") boolean isAdmin);

    int queryPageAdmin(@Param("userid") String userid, @Param("parentCode") String parentCode);

    void refreshFilterMV();

    List<Map<String, Object>> queryTodayGIT(@Param("lastMonth") String lastMonth, @Param("thisMonth") String thisMonth);

    void syncProjection(@Param("lastMonth") String lastMonth, @Param("thisMonth") String thisMonth, @Param("createBy") String createBy);

    Map<String, Object> querySEChinaProvision(String version);

    void deleteSliceReportCalculated(String version);

    void saveSliceReportCalculated(String version, List<Map<String, Object>> list);

    void mergeReportCalculated(String version, List<Map<String, Object>> list);

    List<Map<String, Object>> querySliceTarget();
}
