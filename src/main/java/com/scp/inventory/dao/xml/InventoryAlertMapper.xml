<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.inventory.dao.IInventoryAlertDao">
    <sql id="alert_filter">
        <if test="filters != null and filters != ''.toString()">
            and ${filters}
        </if>
        <if test="specialList != null and specialList.size() > 0">
            <foreach collection="specialList" item="list" separator=" or " open=" and (" close=")">
                t.${specialColumn} in
                <foreach collection="list" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </foreach>
        </if>
    </sql>

    <select id="queryAlertCascader" resultType="java.util.Map">
        select * from ABN_CHANGE_TO_INVS_FILTER_V order by category,decode (name,'Others','zzz',name)
    </select>

    <sql id="queryReport1SQL">
        select <foreach collection="categories" item="item">
                   tt.${item},
               </foreach>
               sum(tt.GOODS_RECEIVE) GOODS_RECEIVE,
               sum(tt.LA_CREATION) LA_CREATION,
               sum(tt.PO_CREATION) PO_CREATION,
               sum(tt.NEW_BLOCK) NEW_BLOCK,
               sum(tt.NEW_STOCK_GAIN) NEW_STOCK_GAIN,
               sum(tt.NEW_GOODS_RETURN) NEW_GOODS_RETURN,
               sum(tt.CONS_STOCK_RELEASE) CONS_STOCK_RELEASE,
               NVL(sum(tt.GOODS_RECEIVE), 0) +
               NVL(sum(tt.LA_CREATION), 0) +
               NVL(sum(tt.PO_CREATION), 0) +
               NVL(sum(tt.NEW_BLOCK), 0) +
               NVL(sum(tt.NEW_STOCK_GAIN), 0) +
               NVL(sum(tt.NEW_GOODS_RETURN), 0) +
               NVL(sum(tt.CONS_STOCK_RELEASE), 0) SUB_TOTAL,
               sum(tt.AMF) AMF
        from (
        select /*+ parallel */ <foreach collection="categories" item="item">
                   nvl(t.${item}, 'Others') ${item},
               </foreach>
               t.AMF,
               ${valueColumn}
          from ABN_CHANGE_TO_INVS_V t inner join
               (
                select kk1.material,
                       kk1.plant_code,
                       kk1.new_gr,
                       kk1.new_la,
                       kk1.new_po,
                       kk0.blocked_stock,
                       kk1.new_goods_return,
                       kk1.new_stock_gain,
                       kk0.consignment_stock
                from (
                    select nvl(tt0.material, tt1.material) material,
                           nvl(tt0.plant_code, tt1.plant_code) plant_code,
                           <![CDATA[
                           case when nvl(tt1.blocked_stock, 0) - nvl(tt0.blocked_stock, 0) < 0 then 0 else nvl(tt1.blocked_stock, 0) - nvl(tt0.blocked_stock, 0) end blocked_stock,
                           case when nvl(tt1.consignment_stock, 0) - nvl(tt0.consignment_stock, 0) < 0 then 0 else nvl(tt1.consignment_stock, 0) - nvl(tt0.consignment_stock, 0) end consignment_stock
                           ]]>
                    from (
                        select t.MATERIAL,
                               t.PLANT_CODE,
                               ${block} as BLOCKED_STOCK,
                               t.CONSIGNMENT_STOCK
                          from abn_change_to_invf_v t
                         where t.DATE$ = to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                         ) tt0 full join (
                         select t.MATERIAL,
                                t.PLANT_CODE,
                                ${block} as BLOCKED_STOCK,
                                t.CONSIGNMENT_STOCK
                                from abn_change_to_invf_v t
                                where t.DATE$ = to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                         ) tt1 on tt0.MATERIAL = tt1.MATERIAL and tt0.PLANT_CODE = tt1.PLANT_CODE ) kk0 inner join
                        (select t.MATERIAL,
                           t.PLANT_CODE,
                           sum(t.NEW_GR)            NEW_GR,
                           sum(t.NEW_LA)            NEW_LA,
                           sum(t.NEW_PO)            NEW_PO,
                           sum(t.NEW_STOCK_GAIN)    NEW_STOCK_GAIN,
                           sum(t.NEW_GOODS_RETURN)  NEW_GOODS_RETURN
                      from abn_change_to_invf_v t
                     where t.DATE$ between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') and to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                     group by t.MATERIAL, t.PLANT_CODE) kk1
                    on kk0.material = kk1.material and kk0.plant_code = kk1.plant_code
                ) mm on t.MATERIAL = mm.MATERIAL and t.PLANT_CODE = mm.PLANT_CODE
         where 1 = 1
         <include refid="alert_filter"/>
        ) tt
        group by
        <foreach collection="categories" item="item" separator=",">
           tt.${item}
        </foreach>
        order by
        <foreach collection="categories" item="item" separator=",">
           decode(tt.${item}, 'Others', 'zzz', 'No Owner', 'zzx', tt.${item})
        </foreach>
    </sql>

    <select id="queryReport1Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1SQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport1DetailsSQL">
         select kk.*
          from  (select
                        t.MATERIAL,
                        t.PLANT_CODE,
                        ${valueColumn},
                        t.UU_STOCK,
                        t.STOCK_IN_QI,
                        t.RESTRICTED_STOCK,
                        t.BLOCKED_STOCK,
                        t.RETURNS_STOCK,
                        t.INTER_STK_TRANSFER,
                        t.GIT_QTY,
                        t.UNISSUE_DELIVERY,
                        t.OPEN_SO,
                        t.OPEN_PO,
                        t.OPEN_TRANSFER,
                        t.MO_RESERVATION,
                        t.ONE_MM_AMU,
                        t.AMF,
                        t.LT_DEMAND,
                        t.SAFETY_STOCK,
                        t.REORDER_POINT,
                        to_char(t.CREATED_DATE,'yyyy/mm/dd') as MATERIAL_CREATED_DATE,
                        t.PLANT_TYPE,
                        t.CLUSTER_NAME,
                        t.ENTITY,
                        t.BU,
                        t.PRODUCT_LINE,
                        t.PRODUCTION_LINE,
                        t.LOCAL_BU,
                        t.LOCAL_PRODUCT_LINE,
                        t.LOCAL_PRODUCT_FAMILY,
                        t.LOCAL_PRODUCT_SUBFAMILY,
                        t.MATERIAL_OWNER_NAME,
                        t.MATERIAL_OWNER_SESA,
                        t.MRP_CONTROLLER,
                        t.STOCKING_POLICY,
                        t.VENDOR_CODE,
                        t.VENDOR_NAME,
                        t.SOURCE_CATEGORY,
                        t.MATERIAL_TYPE,
                        t.ACTIVENESS,
                        t.LT_RANGE,
                        t.CALCULATED_ABC,
                        t.CALCULATED_FMR,
                        t.NEW_PRODUCTS,
                        t.INDUSTRY_CODE,
                        t.MRP_TYPE,
                        t.AVAILABILITY_CHECK,
                        t.ABC,
                        t.PURCHASING_GROUP,
                        t.PROCUREMENT_TYPE,
                        t.SPECIAL_PROC_CODE,
                        t.INDIVIDUAL_COLLECT,
                        t.FOLLOW_UP_MATERIAL,
                        t.PRODN_SUPERVISOR,
                        t.MRP_GROUP,
                        t.MAT_PRICING_GROUP,
                        t.MATERIAL_GROUP_1,
                        t.MATERIAL_GROUP_2,
                        t.MATERIAL_GROUP_3,
                        t.MATERIAL_GROUP_4,
                        t.MATERIAL_GROUP_5,
                        t.MATERIAL_ST_PLANT,
                        t.DIST_CHANNEL_SP_ST,
                        t.ACCOUNT_GROUP,
                        t.GR_PROCESSING_TIME,
                        t.PLANNED_DELIV_TIME,
                        t.REPL_STRATEGY
                  from ABN_CHANGE_TO_INVS_V t inner join
                       (
                            select kk1.material,
                                   kk1.plant_code,
                                   kk1.new_gr,
                                   kk1.new_la,
                                   kk1.new_po,
                                   kk0.blocked_stock,
                                   kk1.new_goods_return,
                                   kk1.new_stock_gain,
                                   kk0.consignment_stock
                            from (
                                select nvl(tt0.material, tt1.material) material,
                                       nvl(tt0.plant_code, tt1.plant_code) plant_code,
                                       <![CDATA[
                                       case when nvl(tt1.blocked_stock, 0) - nvl(tt0.blocked_stock, 0) < 0 then 0 else nvl(tt1.blocked_stock, 0) - nvl(tt0.blocked_stock, 0) end blocked_stock,
                                       case when nvl(tt1.consignment_stock, 0) - nvl(tt0.consignment_stock, 0) < 0 then 0 else nvl(tt1.consignment_stock, 0) - nvl(tt0.consignment_stock, 0) end consignment_stock
                                       ]]>
                                from (
                                    select t.MATERIAL,
                                           t.PLANT_CODE,
                                           ${block} as BLOCKED_STOCK,
                                           t.CONSIGNMENT_STOCK
                                      from abn_change_to_invf_v t
                                     where t.DATE$ = to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                                     ) tt0 full join (
                                     select t.MATERIAL,
                                            t.PLANT_CODE,
                                            ${block} as BLOCKED_STOCK,
                                            t.CONSIGNMENT_STOCK
                                            from abn_change_to_invf_v t
                                            where t.DATE$ = to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                                     ) tt1 on tt0.MATERIAL = tt1.MATERIAL and tt0.PLANT_CODE = tt1.PLANT_CODE ) kk0 inner join
                                    (select t.MATERIAL,
                                       t.PLANT_CODE,
                                       sum(t.NEW_GR)            NEW_GR,
                                       sum(t.NEW_LA)            NEW_LA,
                                       sum(t.NEW_PO)            NEW_PO,
                                       sum(t.NEW_STOCK_GAIN)    NEW_STOCK_GAIN,
                                       sum(t.NEW_GOODS_RETURN)  NEW_GOODS_RETURN
                                  from abn_change_to_invf_v t
                                 where t.DATE$ between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') and to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                                 group by t.MATERIAL, t.PLANT_CODE) kk1
                                on kk0.material = kk1.material and kk0.plant_code = kk1.plant_code
                 ) mm on t.MATERIAL = mm.MATERIAL and t.PLANT_CODE = mm.PLANT_CODE
                 where 1 = 1
                 <include refid="alert_filter"/>
                 <foreach collection="selectedCategories" index="index" item="item">
                     <if test="item != null and item != ''.toString()">
                        and t.${categories[index]} = #{item, jdbcType=VARCHAR}
                     </if>
                 </foreach>
            ) kk
         <if test="selectedCategory3 != null and selectedCategory3 != ''.toString()">
            where kk.${selectedCategory3} > 0
         </if>
    </sql>

    <select id="queryReport1DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>
</mapper>
