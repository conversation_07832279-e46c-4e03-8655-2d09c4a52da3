<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.inventory.dao.IUHSStructureDao">
    <sql id="filter">
        <if test="_filters != null and _filters != ''.toString()">
            and ${_filters}
        </if>
        <if test="treePathFilter != null and treePathFilter != ''.toString()">
            and ${treePathFilter}
        </if>
    </sql>

    <select id="queryCascader" resultType="java.util.Map">
        SELECT * FROM UHS_RCA_FILTER_V T
        ORDER BY T.CATEGORY,DECODE(T.NAME,'Others','zzz',T.NAME)
    </select>

    <select id="queryWeeks" resultType="java.lang.String">
        SELECT DISTINCT WEEK FROM UHS_RCA_HIST ORDER BY WEEK DESC
    </select>

    <select id="queryCurrencyByWeek" resultType="java.lang.Double">
        SELECT 1 / T.EXCHANGE_RATE
        FROM MM3_EXCHANGE_RATE_V T
        WHERE T.TO_CURRENCY = 'RMB'
          AND T.FROM_CURRENCY = 'EUR'
          AND T.VALID_FROM &lt;= (SELECT MAX(T.YEAR || T.MONTH) AS MONTH FROM SY_CALENDAR T WHERE T.YEAR || T.WEEK_NO = '202315' AND T.NAME = 'National Holidays')
        ORDER BY T.VALID_FROM DESC
            FETCH NEXT 1 ROWS ONLY
    </select>

    <select id="queryReport1" resultType="java.util.Map">
        WITH TEMP AS (SELECT SUM(T.STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT * #{rate, jdbcType=NUMERIC})                                                                    AS STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT,
                             SUM(T.GROSS_UNHEALTHY_VALUE * #{rate, jdbcType=NUMERIC})                                                                                   AS GROSS_UNHEALTHY_VALUE,
                             SUM(T.NET_UNHEALTHY_VALUE * #{rate, jdbcType=NUMERIC})                                                                                     AS NET_UNHEALTHY_VALUE,
                             SUM(CASE WHEN T.HS_RCA LIKE 'E98%' THEN (NVL(T.HS_EXCESS_VALUE, 0) + NVL(T.HS_THEO_PROV_VALUE, 0)) ELSE 0 END * #{rate, jdbcType=NUMERIC}) AS SPECIAL_VALUE,
                             SUM(T.HS_EXCESS_VALUE * #{rate, jdbcType=NUMERIC})                                                                                         AS HS_EXCESS_VALUE,
                             SUM(T.HS_THEO_PROV_VALUE * #{rate, jdbcType=NUMERIC})                                                                                      AS HS_THEO_PROV_VALUE,
                             SUM(T.FIN_PROV_VALUE * #{rate, jdbcType=NUMERIC})                                                                                          AS FIN_PROV_VALUE,
                             SUM(T.PROPOSED_FIN_PROV_VALUE * #{rate, jdbcType=NUMERIC})                                                                                 AS PROPOSAL_FIN_PROV_VALUE
                      FROM ${SCPA.UHS_RCA_HIST} T
                     WHERE T.WEEK = #{week, jdbcType=VARCHAR}
                     <include refid="filter"/>)

        SELECT PROPOSAL_FIN_PROV_VALUE,
               NVL(GROSS_UNHEALTHY_VALUE,0) AS GROSS_UNHEALTHY_VALUE,
               DECODE(STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT, 0, 0, ROUND(GROSS_UNHEALTHY_VALUE / STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT * 100, 1)) AS GROSS_UNHEALTHY_PERCENT,
               DECODE(STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT, 0, 0, ROUND(NET_UNHEALTHY_VALUE / STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT * 100, 1))   AS NET_UNHEALTHY_PERCENT,
               DECODE(STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT, 0, 0, ROUND(SPECIAL_VALUE / STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT * 100, 1))         AS SPECIAL_PERCENT,
               HS_EXCESS_VALUE,
               HS_THEO_PROV_VALUE,
               DECODE(PROPOSAL_FIN_PROV_VALUE, 0, 0, ROUND(FIN_PROV_VALUE / PROPOSAL_FIN_PROV_VALUE * 100, 2))                                  AS FIN_VS_PFP
        FROM TEMP
    </select>

    <sql id="queryReport2SQL">
        WITH TEMP AS (SELECT <foreach collection="report2SelectedColumns" separator="," item="item" close=",">${item}</foreach>
                             SUM(T.STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT * #{rate, jdbcType=NUMERIC})                                                                    AS STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT,
                             SUM(T.GROSS_UNHEALTHY_VALUE * #{rate, jdbcType=NUMERIC})                                                                                   AS GROSS_UNHEALTHY_VALUE,
                             SUM(DECODE(T.INV_TOP20_PLANNER_LABEL, NULL, 0, T.GROSS_UNHEALTHY_VALUE * #{rate, jdbcType=NUMERIC}))                                       AS GROSS_UNHEALTHY_VALUE_TOP20,
                             SUM(DECODE(T.GROSS_UHS_EE_TOP1000, 'Y', T.GROSS_UNHEALTHY_VALUE * #{rate, jdbcType=NUMERIC}, 0))                                           AS GROSS_UNHEALTHY_VALUE_EE_TOP1000,
                             SUM(DECODE(T.GROSS_UHS_NON_TOP20_EE_TOP1000, 'Y', T.GROSS_UNHEALTHY_VALUE * #{rate, jdbcType=NUMERIC}, 0))                                 AS GROSS_UNHEALTHY_VALUE_NON_TOP20_EE_TOP1000,
                             SUM(DECODE(T.GROSS_UHS_NON_TOP20_NON_EE_TOP1000, 'Y', T.GROSS_UNHEALTHY_VALUE * #{rate, jdbcType=NUMERIC}, 0))                             AS GROSS_UNHEALTHY_VALUE_NON_TOP20_NON_EE_TOP1000,
                             SUM(T.NET_UNHEALTHY_VALUE * #{rate, jdbcType=NUMERIC})                                                                                     AS NET_UNHEALTHY_VALUE,
                             SUM(CASE WHEN T.HS_RCA LIKE 'E98%' THEN (NVL(T.HS_EXCESS_VALUE, 0) + NVL(T.HS_THEO_PROV_VALUE, 0)) ELSE 0 END * #{rate, jdbcType=NUMERIC}) AS SPECIAL_VALUE,
                             SUM(T.HS_EXCESS_VALUE * #{rate, jdbcType=NUMERIC})                                                                                         AS HS_EXCESS_VALUE,
                             SUM(DECODE(T.INV_TOP20_PLANNER_LABEL, NULL, 0, T.HS_EXCESS_VALUE * #{rate, jdbcType=NUMERIC}))                                             AS HS_EXCESS_VALUE_TOP20,
                             SUM(T.HS_THEO_PROV_VALUE * #{rate, jdbcType=NUMERIC})                                                                                      AS HS_THEO_PROV_VALUE,
                             SUM(DECODE(T.INV_TOP20_PLANNER_LABEL, NULL, 0, T.HS_THEO_PROV_VALUE * #{rate, jdbcType=NUMERIC}))                                          AS HS_THEO_PROV_VALUE_TOP20,
                             SUM(T.FIN_PROV_VALUE * #{rate, jdbcType=NUMERIC})                                                                                          AS FIN_PROV_VALUE,
                             SUM(T.HS_MISSING_VALUE * #{rate, jdbcType=NUMERIC})                                                                                        AS HS_MISSING_VALUE,
                             SUM(DECODE(T.INV_TOP20_PLANNER_LABEL, NULL, 0, T.HS_MISSING_VALUE * #{rate, jdbcType=NUMERIC}))                                            AS HS_MISSING_VALUE_TOP20,
                             SUM(T.PROPOSED_FIN_PROV_VALUE * #{rate, jdbcType=NUMERIC})                                                                                 AS PROPOSAL_FIN_PROV_VALUE
                      FROM ${SCPA.UHS_RCA_HIST} T
                     WHERE T.WEEK = #{week, jdbcType=VARCHAR}
                     <include refid="filter"/>
                     GROUP BY <foreach collection="report2SelectedColumns" separator="," item="item">${item}</foreach>
                     )

        SELECT <foreach collection="report2SelectedColumns" separator="," item="item" close=",">NVL(${item}, 'Others') AS "${item}"</foreach>
               T.STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT,
               T.GROSS_UNHEALTHY_VALUE,
               T.GROSS_UNHEALTHY_VALUE_TOP20,
               T.GROSS_UNHEALTHY_VALUE_EE_TOP1000,
               T.GROSS_UNHEALTHY_VALUE_NON_TOP20_EE_TOP1000,
               T.GROSS_UNHEALTHY_VALUE_NON_TOP20_NON_EE_TOP1000,
               T.NET_UNHEALTHY_VALUE,
               T.SPECIAL_VALUE,
               DECODE(T.GROSS_UNHEALTHY_VALUE, 0, 0, ROUND(T.GROSS_UNHEALTHY_VALUE_TOP20 / T.GROSS_UNHEALTHY_VALUE * 100, 1))                         AS GROSS_UNHEALTHY_PERCENT_TOP20,
               DECODE(T.STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT, 0, 0, ROUND(T.GROSS_UNHEALTHY_VALUE / T.STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT * 100, 1)) AS GROSS_UNHEALTHY_PERCENT,
               DECODE(T.STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT, 0, 0, ROUND(T.NET_UNHEALTHY_VALUE / T.STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT * 100, 1))   AS NET_UNHEALTHY_PERCENT,
               DECODE(T.STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT, 0, 0, ROUND(T.SPECIAL_VALUE / T.STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT * 100, 1))         AS SPECIAL_PERCENT,
               T.HS_EXCESS_VALUE,
               T.HS_EXCESS_VALUE_TOP20,
               T.HS_THEO_PROV_VALUE,
               T.HS_THEO_PROV_VALUE_TOP20,
               T.FIN_PROV_VALUE,
               DECODE(T.PROPOSAL_FIN_PROV_VALUE, 0, 0, ROUND(T.FIN_PROV_VALUE / T.PROPOSAL_FIN_PROV_VALUE * 100, 2))                                  AS FIN_VS_PFP,
               T.HS_MISSING_VALUE,
               T.HS_MISSING_VALUE_TOP20,
               PROPOSAL_FIN_PROV_VALUE
        FROM TEMP T
        ORDER BY <foreach collection="report2SelectedColumns" separator="," item="item">
        DECODE(${item},'Others','zzz',${item})
        </foreach>
    </sql>

    <select id="queryReport2Count" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport2SQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport2" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport2SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport3" resultType="java.util.Map">
        WITH CALENDAR AS (SELECT T.YEAR || T.WEEK_NO AS WEEK, MIN(T.YEAR || T.MONTH) AS MONTH, MIN(T.DATE$) DATE$
                          FROM SY_CALENDAR T
                          WHERE T.NAME = 'National Holidays'
                            AND T.DATE$ &lt; SYSDATE
                          GROUP BY T.YEAR || T.WEEK_NO),
             CURRENCY_BASE AS (SELECT T.EXCHANGE_RATE, T.VALID_FROM FROM MM3_EXCHANGE_RATE_V T WHERE T.FROM_CURRENCY = 'EUR' AND T.TO_CURRENCY = 'RMB'),
             CURRENCY AS (SELECT T.WEEK, T.MONTH, T.DATE$, NVL(T2.EXCHANGE_RATE, T3.EXCHANGE_RATE) EXCHANGE_RATE
                          FROM CALENDAR T
                                   LEFT JOIN CURRENCY_BASE T2 ON T.MONTH = T2.VALID_FROM
                                   LEFT JOIN (SELECT T.EXCHANGE_RATE FROM CURRENCY_BASE T ORDER BY VALID_FROM DESC FETCH NEXT 1 ROW ONLY) T3
                                             ON 1 = 1
                          WHERE T.MONTH BETWEEN #{dateRange[0], jdbcType=VARCHAR} AND #{dateRange[1], jdbcType=VARCHAR})
        SELECT /*+ parallel(6) */ T.WEEK,
                                  SUM(${yAxis})                                                      YAXIS_VALUE,
                                  SUM(DECODE(T2.EXCHANGE_RATE, 0, 0, (${yAxis}) / T2.EXCHANGE_RATE)) YAXIS_VALUE_EUR,
                                  NVL(T.${report3SplitBy}, 'Others')                                 LEGEND
        FROM ${SCPA.UHS_RCA_HIST} T
                 LEFT JOIN CURRENCY T2 ON T.WEEK = T2.WEEK
        WHERE T2.MONTH BETWEEN #{dateRange[0], jdbcType=VARCHAR} AND #{dateRange[1], jdbcType=VARCHAR}
          <include refid="filter"/>
        GROUP BY T.WEEK, NVL(T.${report3SplitBy}, 'Others'), T.${report3SplitBy}, T2.EXCHANGE_RATE
        ORDER BY T.WEEK, DECODE(T.${report3SplitBy},'Others','zzz',T.${report3SplitBy})
    </select>

    <select id="queryReport4" resultType="java.util.Map">
        WITH CALENDAR AS (SELECT T.YEAR || T.WEEK_NO AS WEEK, MIN(T.YEAR || T.MONTH) AS MONTH, MIN(T.DATE$) DATE$
                          FROM SY_CALENDAR T
                          WHERE T.NAME = 'National Holidays'
                            AND T.DATE$ &lt; SYSDATE
                          GROUP BY T.YEAR || T.WEEK_NO),
             CURRENCY_BASE AS (SELECT T.EXCHANGE_RATE, T.VALID_FROM FROM MM3_EXCHANGE_RATE_V T WHERE T.FROM_CURRENCY = 'EUR' AND T.TO_CURRENCY = 'RMB'),
             CURRENCY AS (SELECT T.WEEK, T.MONTH, T.DATE$, NVL(T2.EXCHANGE_RATE, T3.EXCHANGE_RATE) EXCHANGE_RATE
                          FROM CALENDAR T
                                   LEFT JOIN CURRENCY_BASE T2 ON T.MONTH = T2.VALID_FROM
                                   LEFT JOIN (SELECT T.EXCHANGE_RATE FROM CURRENCY_BASE T ORDER BY VALID_FROM DESC FETCH NEXT 1 ROW ONLY) T3
                                             ON 1 = 1
                          WHERE T.MONTH BETWEEN #{dateRange[0], jdbcType=VARCHAR} AND #{dateRange[1], jdbcType=VARCHAR}),
             UHS AS (SELECT /*+ parallel(6) */ T.WEEK,
                                               T2.EXCHANGE_RATE,
                                               SUM(T.HS_THEO_PROV_VALUE)                                                        AS HS_THEO_PROV_VALUE,
                                               SUM(CASE WHEN T.HS_RCA LIKE 'E98%' THEN NVL(T.HS_THEO_PROV_VALUE, 0) ELSE 0 END) AS HS_THEO_PROV_VALUE_SPECIAL,
                                               SUM(T.HS_EXCESS_VALUE)                                                           AS HS_EXCESS_VALUE,
                                               SUM(CASE WHEN T.HS_RCA LIKE 'E98%' THEN NVL(T.HS_EXCESS_VALUE, 0) ELSE 0 END)    AS HS_EXCESS_VALUE_SPECIAL,
                                               SUM(T.HS_MISSING_VALUE)                                                          AS HS_MISSING_VALUE,
                                               SUM(T.NET_UNHEALTHY_VALUE)                                                       AS NET_UNHEALTHY_VALUE,
                                               SUM(T.STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT)                                      AS STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT

                     FROM ${SCPA.UHS_RCA_HIST} T
                              LEFT JOIN CURRENCY T2 ON T.WEEK = T2.WEEK

                         <include refid="filter"/>
                     WHERE T2.MONTH BETWEEN #{dateRange[0], jdbcType=VARCHAR} AND #{dateRange[1], jdbcType=VARCHAR}
                     GROUP BY T.WEEK, T2.EXCHANGE_RATE)
        SELECT T.WEEK,
               HS_THEO_PROV_VALUE,
               HS_THEO_PROV_VALUE_SPECIAL,
               HS_EXCESS_VALUE,
               HS_EXCESS_VALUE_SPECIAL,
               HS_MISSING_VALUE,
               STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT,

               DECODE(EXCHANGE_RATE, 0, 0, HS_THEO_PROV_VALUE / EXCHANGE_RATE)                                                                  AS HS_THEO_PROV_VALUE_EUR,
               DECODE(EXCHANGE_RATE, 0, 0, HS_THEO_PROV_VALUE_SPECIAL / EXCHANGE_RATE)                                                          AS HS_THEO_PROV_VALUE_SPECIAL_EUR,
               DECODE(EXCHANGE_RATE, 0, 0, HS_EXCESS_VALUE / EXCHANGE_RATE)                                                                     AS HS_EXCESS_VALUE_EUR,
               DECODE(EXCHANGE_RATE, 0, 0, HS_EXCESS_VALUE_SPECIAL / EXCHANGE_RATE)                                                             AS HS_EXCESS_VALUE_SPECIAL_EUR,
               DECODE(EXCHANGE_RATE, 0, 0, HS_MISSING_VALUE / EXCHANGE_RATE)                                                                    AS HS_MISSING_VALUE_EUR,
               DECODE(EXCHANGE_RATE, 0, 0, STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT / EXCHANGE_RATE)                                                AS STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT_EUR,

               DECODE(STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT, 0, 0, NET_UNHEALTHY_VALUE / STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT * 100)             AS NET_UNHEALTHY_PERCENT,
               DECODE(STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT, 0, 0, (HS_THEO_PROV_VALUE_SPECIAL + HS_EXCESS_VALUE_SPECIAL) / STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT * 100)             AS SPECIAL_PERCENT,
               NVL(STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT, 0) - NVL(HS_THEO_PROV_VALUE, 0) - NVL(HS_EXCESS_VALUE, 0)                              AS HEALTH_STOCK,
               DECODE(EXCHANGE_RATE, 0, 0, (NVL(STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT, 0) - NVL(HS_THEO_PROV_VALUE, 0)
                        - NVL(HS_EXCESS_VALUE, 0)) / EXCHANGE_RATE)                                                                             AS HEALTH_STOCK_EUR
        FROM UHS T
        ORDER BY T.WEEK
    </select>

    <sql id="queryReport1DetailsSQL">
        SELECT * FROM ${SCPA.UHS_RCA_HIST} T
         WHERE 1 = 1
         <include refid="filter"/>
         <choose>
             <when test="detailIndex == 'report1'.toString()">
                 AND T.WEEK = #{week, jdbcType=VARCHAR}
             </when>
             <when test="detailIndex == 'report2'.toString()">
                AND T.WEEK = #{week, jdbcType=VARCHAR}
                <foreach collection="report2SelectedColumns" item="item" index="index">
                    <if test="report2SelectedValue[index] != null and report2SelectedValue[index] != 'Grand Total'.toString() and report2SelectedValue[index] != ''.toString()">
                        <choose>
                            <when test="report2SelectedValue[index] == 'Others'.toString()">
                                and (t.${item} is null or t.${item} = 'Others')
                            </when>
                            <otherwise>
                                and t.${item} = #{report2SelectedValue[${index}], jdbcType=VARCHAR}
                            </otherwise>
                        </choose>
                    </if>
                </foreach>
             </when>
             <when test="detailIndex == 'report3'.toString()">
                 AND T.WEEK = #{report3SelectedWeek, jdbcType=VARCHAR}
             </when>
             <when test="detailIndex == 'report4'.toString()">
                 AND T.WEEK = #{report4SelectedWeek, jdbcType=VARCHAR}
             </when>
         </choose>
    </sql>

    <select id="queryReport1DetailsCount" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1Details" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <resultMap id="report5ResultMap" type="com.scp.inventory.bean.UHSStructureReport5Bean">
        <result property="category1" column="CATEGORY1"/>
        <result property="category2" column="CATEGORY2"/>
        <result property="category3" column="CATEGORY3"/>
        <result property="category4" column="CATEGORY4"/>
        <result property="category5" column="CATEGORY5"/>
        <result property="value" column="value"/>
        <association property="tooltips" javaType="com.scp.inventory.bean.UHSStructureReport5Tooltips">
            <result property="THEO_PROV" column="THEO_PROV"/>
            <result property="THEO_PROV_SPECIAL_EVENT" column="THEO_PROV_SPECIAL_EVENT"/>
            <result property="EXCESS_STOCK" column="EXCESS_STOCK"/>
            <result property="EXCESS_SPECIAL_EVENT" column="EXCESS_SPECIAL_EVENT"/>
            <result property="MISSING_STOCK" column="MISSING_STOCK"/>
            <result property="HEALTH_STOCK" column="HEALTH_STOCK"/>
        </association>
    </resultMap>

    <select id="queryReport5" resultMap="report5ResultMap">
        WITH CALENDAR AS (SELECT T.YEAR || T.WEEK_NO AS WEEK, MIN(T.YEAR || T.MONTH) AS MONTH, MIN(T.DATE$) DATE$
                          FROM SY_CALENDAR T
                          WHERE T.NAME = 'National Holidays'
                            AND T.DATE$ &lt; SYSDATE
                          GROUP BY T.YEAR || T.WEEK_NO),
             CURRENCY_BASE AS (SELECT T.EXCHANGE_RATE, T.VALID_FROM FROM MM3_EXCHANGE_RATE_V T WHERE T.FROM_CURRENCY = 'EUR' AND T.TO_CURRENCY = 'RMB'),
             CURRENCY AS (SELECT T.WEEK, T.MONTH, T.DATE$, NVL(T2.EXCHANGE_RATE, T3.EXCHANGE_RATE) EXCHANGE_RATE
                          FROM CALENDAR T
                                   LEFT JOIN CURRENCY_BASE T2 ON T.MONTH = T2.VALID_FROM
                                   LEFT JOIN (SELECT T.EXCHANGE_RATE FROM CURRENCY_BASE T ORDER BY VALID_FROM DESC FETCH NEXT 1 ROW ONLY) T3
                                             ON 1 = 1
                          WHERE T.WEEK = #{week, jdbcType=VARCHAR}),
             UHS AS (SELECT /*+ parallel(6) */ MATERIAL,
                                               PLANT_CODE,
                                               PLANT_TYPE,
                                               ACTIVENESS,
                                               STOCKING_POLICY,
                                               DIST_CHANNEL_SP_ST,
                                               NEW_PRODUCTS,
                                               GRA,
                                               SPECIAL_PROC_CODE,
                                               MRP_CONTROLLER,
                                               HS_STATUS,
                                               CLUSTER_NAME,
                                               ENTITY,
                                               BU,
                                               PRODUCT_LINE,
                                               PRODUCTION_LINE,
                                               LOCAL_BU,
                                               LOCAL_PRODUCT_LINE,
                                               LOCAL_PRODUCT_FAMILY,
                                               LOCAL_PRODUCT_SUBFAMILY,
                                               VENDOR_CODE,
                                               VENDOR_NAME,
                                               VENDOR_PARENT_CODE,
                                               VENDOR_PARENT_NAME,
                                               VENDOR_SHORT_NAME,
                                               VENDOR_FULL_NAME,
                                               SOURCE_CATEGORY,
                                               MATERIAL_TYPE,
                                               LT_RANGE,
                                               CALCULATED_ABC,
                                               CALCULATED_FMR,
                                               INDUSTRY_CODE,
                                               ABC,
                                               PURCHASING_GROUP,
                                               PROCUREMENT_TYPE,
                                               FOLLOW_UP_MATERIAL,
                                               PRODN_SUPERVISOR,
                                               MAT_PRICING_GROUP,
                                               DES_ACTION_RCA,
                                               MATERIAL_OWNER_NAME,
                                               MATERIAL_OWNER_SESA,
                                               COMMODITY_CODE,
                                               EXISTING_IN_BOM,
                                               LOCATION_CATEGORY,
                                               MATERIAL_CATEGORY,
                                               PRODUCT_FAMILY_INV,
                                               PRODUCT_LINE_INV,
                                               REPL_STRATEGY,
                                               T.COV_RANGE,
                                               T.WEEKLY_ADI_RANGE,
                                               T2.EXCHANGE_RATE,
                                               T.PROPOSED_FIN_PROV_QTY,
                                               T.PROPOSED_FIN_PROV_VALUE,
                                               T.PROPOSED_PROVISION_RULE,
                                               T.NETTED_STOCK_QTY,
                                               T.NETTED_STOCK_VALUE,
                                               T.M1_NOC_QTY,
                                               T.M1_NOC_VAL,
                                               T.INV_TOP20_PLANNER_LABEL,
                                               T.INV_TOP20_ENTITY_LABEL,
                                               T.HS_THEO_PROV_VALUE                                                        AS HS_THEO_PROV_VALUE,
                                               CASE WHEN T.HS_RCA LIKE 'E98%' THEN NVL(T.HS_THEO_PROV_VALUE, 0) ELSE 0 END AS HS_THEO_PROV_VALUE_SPECIAL,
                                               T.HS_EXCESS_VALUE                                                           AS HS_EXCESS_VALUE,
                                               CASE WHEN T.HS_RCA LIKE 'E98%' THEN NVL(T.HS_EXCESS_VALUE, 0) ELSE 0 END    AS HS_EXCESS_VALUE_SPECIAL,
                                               T.HS_MISSING_VALUE                                                          AS HS_MISSING_VALUE,
                                               T.NET_UNHEALTHY_VALUE                                                       AS NET_UNHEALTHY_VALUE,
                                               T.STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT                                      AS STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT,
                                               T.PRODUCT_GROUP_A,
                                               T.PRODUCT_GROUP_B,
                                               T.PRODUCT_GROUP_C,
                                               T.PRODUCT_GROUP_D,
                                               T.PRODUCT_GROUP_E

                     FROM ${SCPA.UHS_RCA_HIST} T INNER JOIN CURRENCY T2 ON T.WEEK = T2.WEEK
                    <include refid="filter"/>
             ),
             BASE AS (
                SELECT T.*,
                       NVL(STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT, 0) - NVL(HS_THEO_PROV_VALUE, 0) - NVL(HS_EXCESS_VALUE, 0) AS HEALTH_STOCK
                 FROM UHS T
             )
        SELECT
        NVL(${level1}, 'Others') AS CATEGORY1,
        NVL(${level2}, 'Others') AS CATEGORY2,
        NVL(${level3}, 'Others') AS CATEGORY3,
        <if test="level4 != null and level4 != ''.toString()">
            NVL(${level4}, 'Others') AS CATEGORY4,
        </if>
        <if test="level5 != null and level5 != ''.toString()">
            NVL(${level5}, 'Others') AS CATEGORY5,
        </if>
        ${valueColumn} AS VALUE
        <if test="tooltipsColumns != null and tooltipsColumns != ''.toString()">
            ,${tooltipsColumns}
        </if>
        FROM BASE T
        <where>
            <include refid="filter"/>
        </where>
        GROUP BY
        ${level1}, ${level2}, ${level3}
        <if test="level4 != null and level4 != ''.toString()">,${level4}</if>
        <if test="level5 != null and level5 != ''.toString()">,${level5}</if>
    </select>
</mapper>
