<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.inventory.dao.IPreStockDao">

	<sql id="PreStockFilter">
		<if test="_filters != null and _filters != ''.toString()">
			AND ${_filters}
		</if>
		<if test="treePathFilter != null and treePathFilter != ''.toString()">
			and ${treePathFilter}
		</if>
	</sql>

	<select id="queryCascader" resultType="java.util.Map">
		SELECT *
		FROM SCPA.PRE_STOCK_FILTER_V T
		ORDER BY CATEGORY,decode (name,'Others','zzz',name)
	</select>

	<sql id="queryReport1Sql">
		SELECT
		<foreach collection="fields" separator="," item="item">
			T."${item}" AS ${item}
		</foreach>,
		T.ESTIMATED_CONSUMPRION_DATE,
		T.CREATE_DATE,
		T.IWORK_NUMBER,
		T.PURCH_ORDER_NUMBER,
		T.PURCH_ORDER_ITEM,
		T.MATERIAL,
		T.MATERIAL_DESCRIPTION,
		T.ORDER_QUANTITY,
		T.OPEN_QTY,
		T.GR_QTY,
		T.LATEST_GR_DATE,
		T.PO_UNCONSUMED_QTY,
		T.CURRENT_STOCK,
		T.REMAIN_STOCK_QTY,
		T.NEW_ESTIMATED_CONSUMPTION_DATE,
		T.REMAIN_STOCK_VALUE,
		T.STATUS
		FROM (SELECT PE.*,
		R.REMAIN_STOCK_QTY,
		R.NEW_ESTIMATED_CONSUMPTION_DATE,
		R.REMAIN_STOCK_QTY * MMV.UNIT_COST AS REMAIN_STOCK_VALUE,
		CASE WHEN PE.OPEN_QTY =0 AND R.REMAIN_STOCK_QTY =0 THEN 'CLOSED'
		ELSE 'OPEN' END AS STATUS FROM ${SCPA.PRE_STOCK_V} PE
		LEFT JOIN ${SCPA.PRE_STOCK_REMAIN} R ON R.PURCH_ORDER_NUMBER =PE.PURCH_ORDER_NUMBER AND R.PURCH_ORDER_ITEM =PE.PURCH_ORDER_ITEM
		LEFT JOIN ${SCPA.MATERIAL_MASTER_V} MMV ON MMV.MATERIAL =PE.MATERIAL AND MMV.PLANT_CODE =PE.PLANT_CODE)T
		<where>
			TO_CHAR(T.CREATE_DATE, 'yyyymm') between #{dateRange[0], jdbcType=VARCHAR} and
			#{dateRange[1], jdbcType=VARCHAR}
			<include refid="PreStockFilter"/>
		</where>
	</sql>


	<select id="queryReport1" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		<include refid="queryReport1Sql"/>
		<include refid="global.select_footer"/>
	</select>

	<select id="queryReport1Count" parameterType="java.util.Map" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="queryReport1Sql"/>
		<include refid="global.count_footer"/>
	</select>

	<insert id="saveReport1Details" parameterType="java.util.Map">
				merge into ${SCPA.PRE_STOCK_REMAIN} t
		using
		(
		<foreach collection="dataList" item="item" separator="union all">
			SELECT #{item.order, jdbcType=VARCHAR} PURCH_ORDER_NUMBER,
				   #{item.item, jdbcType=VARCHAR} PURCH_ORDER_ITEM,
				   #{item.remain_stock_qty, jdbcType=VARCHAR} REMAIN_STOCK_QTY,
				   #{item.new_estimated_consumption_date, jdbcType=VARCHAR} NEW_ESTIMATED_CONSUMPTION_DATE
			FROM DUAL
		</foreach>
		) S ON (T.PURCH_ORDER_NUMBER = S.PURCH_ORDER_NUMBER AND T.PURCH_ORDER_ITEM = S.PURCH_ORDER_ITEM)
		when matched then
		update set
			t.remain_stock_qty = s.remain_stock_qty,
		    t.new_estimated_consumption_date = new_estimated_consumption_date,
			t.update_date$ = sysdate,
			t.update_by$ = #{session.userid, jdbcType=VARCHAR}
	</insert>
</mapper>
