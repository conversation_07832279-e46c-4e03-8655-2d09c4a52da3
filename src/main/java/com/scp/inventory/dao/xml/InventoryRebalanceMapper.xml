<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.inventory.dao.IInventoryRebalanceDao">

    <resultMap id="queryRebalanceListResultMap" type="com.scp.inventory.bean.RebalanceBean">
        <result property="MATERIAL" column="MATERIAL"/>
        <result property="O001_DELIVERING_PLANT" column="O001_DELIVERING_PLANT"/>
        <result property="N001_DELIVERING_PLANT" column="N001_DELIVERING_PLANT"/>
        <result property="M001_DELIVERING_PLANT" column="M001_DELIVERING_PLANT"/>
        <result property="I001_DELIVERING_PLANT" column="I001_DELIVERING_PLANT"/>
        <result property="I003_DELIVERING_PLANT" column="I003_DELIVERING_PLANT"/>
        <result property="O001_ONE_MM_AMU" column="O001_ONE_MM_AMU"/>
        <result property="N001_ONE_MM_AMU" column="N001_ONE_MM_AMU"/>
        <result property="M001_ONE_MM_AMU" column="M001_ONE_MM_AMU"/>
        <result property="I001_ONE_MM_AMU" column="I001_ONE_MM_AMU"/>
        <result property="I003_ONE_MM_AMU" column="I003_ONE_MM_AMU"/>
        <result property="O001_UU_STOCK_NEW" column="O001_UU_STOCK_NEW"/>
        <result property="N001_UU_STOCK_NEW" column="N001_UU_STOCK_NEW"/>
        <result property="M001_UU_STOCK_NEW" column="M001_UU_STOCK_NEW"/>
        <result property="I001_UU_STOCK_NEW" column="I001_UU_STOCK_NEW"/>
        <result property="I003_UU_STOCK_NEW" column="I003_UU_STOCK_NEW"/>
        <result property="O001_UNIT_COST" column="O001_UNIT_COST"/>
        <result property="N001_UNIT_COST" column="N001_UNIT_COST"/>
        <result property="M001_UNIT_COST" column="M001_UNIT_COST"/>
        <result property="I001_UNIT_COST" column="I001_UNIT_COST"/>
        <result property="I003_UNIT_COST" column="I003_UNIT_COST"/>
        <result property="CLUSTER_NAME" column="CLUSTER_NAME"/>
        <result property="ENTITY" column="ENTITY"/>
        <result property="BU" column="BU"/>
        <result property="PRODUCT_LINE" column="PRODUCT_LINE"/>
        <result property="PRODUCTION_LINE" column="PRODUCTION_LINE"/>
        <result property="LOCAL_BU" column="LOCAL_BU"/>
        <result property="LOCAL_PRODUCT_LINE" column="LOCAL_PRODUCT_LINE"/>
        <result property="LOCAL_PRODUCT_FAMILY" column="LOCAL_PRODUCT_FAMILY"/>
        <result property="LOCAL_PRODUCT_SUBFAMILY" column="LOCAL_PRODUCT_SUBFAMILY"/>
        <result property="VENDOR_CODE" column="VENDOR_CODE"/>
        <result property="VENDOR_NAME" column="VENDOR_NAME"/>
        <result property="SOURCE_CATEGORY" column="SOURCE_CATEGORY"/>
        <result property="MATERIAL_OWNER_NAME" column="MATERIAL_OWNER_NAME"/>
        <result property="MATERIAL_OWNER_SESA" column="MATERIAL_OWNER_SESA"/>
        <result property="MRP_CONTROLLER" column="MRP_CONTROLLER"/>
        <result property="O001_ROUNDING_VALUE" column="O001_ROUNDING_VALUE"/>
        <result property="N001_ROUNDING_VALUE" column="N001_ROUNDING_VALUE"/>
        <result property="M001_ROUNDING_VALUE" column="M001_ROUNDING_VALUE"/>
        <result property="I001_ROUNDING_VALUE" column="I001_ROUNDING_VALUE"/>
        <result property="I003_ROUNDING_VALUE" column="I003_ROUNDING_VALUE"/>
    </resultMap>

    <select id="queryRebalanceList" resultMap="queryRebalanceListResultMap">
        select material,
               o001_delivering_plant,
               n001_delivering_plant,
               m001_delivering_plant,
               i001_delivering_plant,
               i003_delivering_plant,
               o001_one_mm_amu,
               n001_one_mm_amu,
               m001_one_mm_amu,
               i001_one_mm_amu,
               i003_one_mm_amu,
               o001_uu_stock_new,
               n001_uu_stock_new,
               m001_uu_stock_new,
               i001_uu_stock_new,
               i003_uu_stock_new,
               o001_unit_cost,
               n001_unit_cost,
               m001_unit_cost,
               i001_unit_cost,
               i003_unit_cost,
               cluster_name,
               entity,
               bu,
               product_line,
               production_line,
               local_bu,
               local_product_line,
               local_product_family,
               local_product_subfamily,
               vendor_code,
               vendor_name,
               source_category,
               mrp_controller,
               material_owner_name,
               material_owner_sesa,
               o001_rounding_value,
               n001_rounding_value,
               m001_rounding_value,
               i001_rounding_value,
               i003_rounding_value
          from dc_stk_rebalance_v
         where transfer_proposal not like 'No Transfer%'
            or transfer_proposal is null
    </select>

    <update id="truncateTransferTable">
        truncate table DC_STK_TRANSFER
    </update>

    <update id="truncateTransferLogsTable">
        truncate table DC_STK_TRANSFER_LOGS
    </update>

    <insert id="saveRebalanceResult">
        begin
            insert /*+ append */ into DC_STK_TRANSFER
            (target, material, transfer_from, transfer_to, transfer_qty, transfer_value, cluster_name, entity, bu, product_line, production_line, local_bu,
             local_product_line, local_product_family, local_product_subfamily, vendor_code, vendor_name, source_category, mrp_controller, transfer_proposal)
            <foreach collection="list" index="index" item="item" open="" separator="union all" close="">
                select  #{item.TARGET,jdbcType=VARCHAR},#{item.MATERIAL,jdbcType=VARCHAR},#{item.TRANSFER_FROM,jdbcType=VARCHAR},
                        #{item.TRANSFER_TO,jdbcType=VARCHAR},#{item.TRANSFER_QTY,jdbcType=DECIMAL},#{item.TRANSFER_VALUE,jdbcType=DECIMAL},
                        #{item.CLUSTER_NAME,jdbcType=VARCHAR},#{item.ENTITY,jdbcType=VARCHAR},#{item.BU,jdbcType=VARCHAR},
                        #{item.PRODUCT_LINE,jdbcType=VARCHAR},#{item.PRODUCTION_LINE,jdbcType=VARCHAR},#{item.LOCAL_BU,jdbcType=VARCHAR},
                        #{item.LOCAL_PRODUCT_LINE,jdbcType=VARCHAR},#{item.LOCAL_PRODUCT_FAMILY,jdbcType=VARCHAR},#{item.LOCAL_PRODUCT_SUBFAMILY,jdbcType=VARCHAR},
                        #{item.VENDOR_CODE,jdbcType=VARCHAR},#{item.VENDOR_NAME,jdbcType=VARCHAR},#{item.SOURCE_CATEGORY,jdbcType=VARCHAR},
                        #{item.MRP_CONTROLLER,jdbcType=VARCHAR},#{item.TRANSFER_PROPOSAL,jdbcType=VARCHAR}
            from dual
            </foreach>;
            commit;
        end;
    </insert>

    <insert id="saveRebalanceLogs">
        begin
            insert into DC_STK_TRANSFER_LOGS (target, material, logs)
            <foreach collection="list" index="index" item="item" open="" separator="union all" close="">
                select #{item.TARGET,jdbcType=VARCHAR},#{item.MATERIAL,jdbcType=VARCHAR},#{item.LOGS,jdbcType=VARCHAR} from dual
            </foreach>;
        end;
    </insert>

    <sql id="rebalance_filter">
        <if test="_filters != null and _filters != ''.toString()">
            and ${_filters}
        </if>
    </sql>

    <select id="queryRebalanceCascader" resultType="java.util.Map">
        select * from DC_STK_REBALANCE_FILTER_V order by category,decode (name,'Others','zzz',name)
    </select>

    <select id="queryReport1" resultType="java.util.LinkedHashMap">
        select tt.*,
               nvl(I001, 0) + nvl(I003, 0) + nvl(M001, 0) + nvl(N001, 0) + nvl(O001, 0) TOTAL
        from (
            select nvl(TRANSFER_PROPOSAL, 'No Transfer, Every DC Stock are Below') TRANSFER_PROPOSAL,
                   sum(I001) I001,
                   sum(I003) I003,
                   sum(M001) M001,
                   sum(N001) N001,
                   sum(O001) O001
            from (
                     select t.TRANSFER_PROPOSAL,
                            ${valueColumn1}
                     FROM ${SCPA.DC_STK_REBALANCE_V} t
                     where not exists (select 1 from dc_stk_transfer t2 where t.MATERIAL = t2.MATERIAL and t2.target = #{target, jdbcType=VARCHAR})
                     <include refid="rebalance_filter"/>

                     union all

                     select t0.TRANSFER_PROPOSAL,
                            ${valueColumn2}
                     FROM DC_STK_TRANSFER t0 inner join ${SCPA.DC_STK_REBALANCE_V} t on t0.MATERIAL = t.MATERIAL
                     WHERE t0.target = #{target, jdbcType=VARCHAR}
                     <include refid="rebalance_filter"/>) mm
            group by nvl(TRANSFER_PROPOSAL, 'No Transfer, Every DC Stock are Below')
            order by TRANSFER_PROPOSAL) tt
    </select>

    <sql id="queryReport1DetailsSql">
       select   t.material,
                t.o001_uu_stock,
                t.n001_uu_stock,
                t.m001_uu_stock,
                t.i001_uu_stock,
                t.i003_uu_stock,
                t.o001_open_so,
                t.n001_open_so,
                t.m001_open_so,
                t.i001_open_so,
                t.i003_open_so,
                t.o001_ud,
                t.n001_ud,
                t.m001_ud,
                t.i001_ud,
                t.i003_ud,
                t.o001_open_transfer,
                t.n001_open_transfer,
                t.m001_open_transfer,
                t.i001_open_transfer,
                t.i003_open_transfer,
                t.o001_delivering_plant,
                t.n001_delivering_plant,
                t.m001_delivering_plant,
                t.i001_delivering_plant,
                t.i003_delivering_plant,
                t.o001_one_mm_amu,
                t.n001_one_mm_amu,
                t.m001_one_mm_amu,
                t.i001_one_mm_amu,
                t.i003_one_mm_amu,
                t.o001_uu_stock_new,
                t.n001_uu_stock_new,
                t.m001_uu_stock_new,
                t.i001_uu_stock_new,
                t.i003_uu_stock_new,
                t.activeness,
                t.deletion,
                t.stocking_policy,
                t.o001_mvp,
                t.n001_mvp,
                t.m001_mvp,
                t.i001_mvp,
                t.i003_mvp,
                t.o001_unit_cost,
                t.n001_unit_cost,
                t.m001_unit_cost,
                t.i001_unit_cost,
                t.i003_unit_cost,
                t.cluster_name,
                t.entity,
                t.bu,
                t.product_line,
                t.production_line,
                t.local_bu,
                t.local_product_line,
                t.local_product_family,
                t.local_product_subfamily,
                t.vendor_code,
                t.vendor_name,
                t.source_category,
                t.material_owner_name,
                t.material_owner_sesa,
                t.mrp_controller,
                t.o001_rounding_value,
                t.n001_rounding_value,
                t.m001_rounding_value,
                t.i001_rounding_value,
                t.i003_rounding_value,
                null transfer_from,
                null transfer_to,
                null transfer_qty,
                t.transfer_proposal
        from ${SCPA.DC_STK_REBALANCE_V} t
        where not exists (select 1 from dc_stk_transfer t2 where t.MATERIAL = t2.MATERIAL and t2.target = #{target, jdbcType=VARCHAR})
        <if test="report1SelectedType != null and report1SelectedType != ''.toString()">
            <choose>
                <when test="report1SelectedType == 'No Transfer, Every DC Stock are Below'.toString()">
                    and t.transfer_proposal is null
                </when>
                <otherwise>
                    and t.transfer_proposal = #{report1SelectedType, jdbcType=VARCHAR}
                </otherwise>
            </choose>
        </if>
        <if test="valueColumn1 != null and valueColumn1 != ''.toString()">
            and ${valueColumn1}
        </if>
        <include refid="rebalance_filter"/>

        union all

        select  t.material,
                t.o001_uu_stock,
                t.n001_uu_stock,
                t.m001_uu_stock,
                t.i001_uu_stock,
                t.i003_uu_stock,
                t.o001_open_so,
                t.n001_open_so,
                t.m001_open_so,
                t.i001_open_so,
                t.i003_open_so,
                t.o001_ud,
                t.n001_ud,
                t.m001_ud,
                t.i001_ud,
                t.i003_ud,
                t.o001_open_transfer,
                t.n001_open_transfer,
                t.m001_open_transfer,
                t.i001_open_transfer,
                t.i003_open_transfer,
                t.o001_delivering_plant,
                t.n001_delivering_plant,
                t.m001_delivering_plant,
                t.i001_delivering_plant,
                t.i003_delivering_plant,
                t.o001_one_mm_amu,
                t.n001_one_mm_amu,
                t.m001_one_mm_amu,
                t.i001_one_mm_amu,
                t.i003_one_mm_amu,
                t.o001_uu_stock_new,
                t.n001_uu_stock_new,
                t.m001_uu_stock_new,
                t.i001_uu_stock_new,
                t.i003_uu_stock_new,
                t.activeness,
                t.deletion,
                t.stocking_policy,
                t.o001_mvp,
                t.n001_mvp,
                t.m001_mvp,
                t.i001_mvp,
                t.i003_mvp,
                t.o001_unit_cost,
                t.n001_unit_cost,
                t.m001_unit_cost,
                t.i001_unit_cost,
                t.i003_unit_cost,
                t.cluster_name,
                t.entity,
                t.bu,
                t.product_line,
                t.production_line,
                t.local_bu,
                t.local_product_line,
                t.local_product_family,
                t.local_product_subfamily,
                t.vendor_code,
                t.vendor_name,
                t.source_category,
                t.material_owner_name,
                t.material_owner_sesa,
                t.mrp_controller,
                t.o001_rounding_value,
                t.n001_rounding_value,
                t.m001_rounding_value,
                t.i001_rounding_value,
                t.i003_rounding_value,
                t0.transfer_from,
                t0.transfer_to,
                t0.transfer_qty,
                nvl(t0.transfer_proposal,'No Transfer, Every DC Stock are Below') transfer_proposal
        FROM DC_STK_TRANSFER t0 inner join ${SCPA.DC_STK_REBALANCE_V} t on t0.MATERIAL = t.MATERIAL
        WHERE t0.target = #{target, jdbcType=VARCHAR}
        <if test="report1SelectedType != null and report1SelectedType != ''.toString()">
            <choose>
                <when test="report1SelectedType == 'No Transfer, Every DC Stock are Below'.toString()">
                    and t0.transfer_proposal is null
                </when>
                <otherwise>
                    and t0.transfer_proposal = #{report1SelectedType, jdbcType=VARCHAR}
                </otherwise>
            </choose>
        </if>
        <if test="valueColumn2 != null and valueColumn2 != ''.toString()">
            and ${valueColumn1}
        </if>
        <include refid="rebalance_filter"/>
    </sql>

    <select id="queryReport1DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1DetailsSql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1DetailsSql"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport2Sql">
        select t.${report2Type},
               t.transfer_from,
               t.transfer_to,
               sum(t.transfer_qty) transfer_qty,
               sum(t.transfer_value) transfer_value,
               count(1) item
        from DC_STK_TRANSFER t
        where t.target = #{target, jdbcType=VARCHAR}
        <include refid="rebalance_filter"/>
        group by t.${report2Type}, t.transfer_from, t.transfer_to
    </sql>

    <select id="queryReport2" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport2Sql"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport2DetailsSql">
        select  target,
                material,
                transfer_from,
                transfer_to,
                transfer_qty,
                transfer_value,
                cluster_name,
                entity,
                bu,
                product_line,
                production_line,
                local_bu,
                local_product_line,
                local_product_family,
                local_product_subfamily,
                vendor_code,
                vendor_name,
                source_category,
                mrp_controller,
                transfer_proposal
        from DC_STK_TRANSFER t
        where t.target = #{target, jdbcType=VARCHAR}
        <if test="report2SelectedType != 'Total'.toString()">
            and t.${report2Type} = #{report2SelectedType, jdbcType=VARCHAR}
            and transfer_from = #{report2SelectedFrom, jdbcType=VARCHAR}
            <choose>
                <when test="report2SelectedTo != null and report2SelectedTo != ''.toString()">
                    and transfer_to = #{report2SelectedTo, jdbcType=VARCHAR}
                </when>
                <otherwise>
                    and transfer_to is null
                </otherwise>
            </choose>
        </if>
        <include refid="rebalance_filter"/>
    </sql>

    <select id="queryReport2DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport2DetailsSql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport2Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport2DetailsSql"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport2DetailsLogs" resultType="java.lang.Object">
        select t.logs from dc_stk_transfer_logs t
         where material = #{report2DetailsSelectedType, jdbcType=VARCHAR}
               and target = #{target, jdbcType=VARCHAR}
         fetch next 1 rows only
    </select>
</mapper>
