<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.inventory.dao.IInventoryDeliveryDao">
    <sql id="delivery_filter">
        <if test="_filters != null and _filters != ''.toString()">
            and ${_filters}
        </if>
        <if test="treePathFilter != null and treePathFilter != ''.toString()">
            and ${treePathFilter}
        </if>
    </sql>

    <sql id="order_by_range">
        order by decode(UD_AGING_RANGE, '0-1D', 0, '1-3D', 1, '3-7D', 2, '7-14D', 3, '14-30D', 4, '1-2M', 5, '2-3M', 6, '3-6M', 7, '>6M', 8, 9)
    </sql>

    <select id="queryCascader" resultType="java.util.Map">
        select * from SCPA.UD_FILTER_V order by category,decode(name,'Others','zzz',name)
    </select>

    <resultMap id="report1ResultMap" type="com.scp.inventory.bean.DeliveryReport1Pie">
        <result property="name" column="name"/>
        <result property="value" column="value"/>
        <association property="tooltips" javaType="com.scp.inventory.bean.DeliveryReport1PieTooltips">
            <result property="DELIVERY_QUANTITY" column="DELIVERY_QUANTITY"/>
            <result property="DELIVERY_VALUE" column="DELIVERY_VALUE"/>
            <result property="DELIVERY_COST_VALUE" column="DELIVERY_COST_VALUE"/>
            <result property="UU_STOCK_QTY" column="UU_STOCK_QTY"/>
            <result property="UU_STOCK_VALUE" column="UU_STOCK_VALUE"/>
            <result property="UU_STOCK_SALES_VALUE" column="UU_STOCK_SALES_VALUE"/>
            <result property="OPEN_SO_QTY" column="OPEN_SO_QTY"/>
            <result property="OPEN_SO_COST_VALUE" column="OPEN_SO_COST_VALUE"/>
            <result property="OPEN_SO_SALES_VALUE" column="OPEN_SO_SALES_VALUE"/>
        </association>
    </resultMap>

    <select id="queryReport1" resultMap="report1ResultMap">
        WITH UD AS (
            SELECT * FROM ${SCPA.UD_HIST} T
            <where>
                DATE$ = to_date(#{report1DateRange, jdbcType=VARCHAR}, 'yyyy/mm/dd')
            </where>
        )
        select t.UD_AGING_RANGE as "name",
               ${valueColumn} as "value"
               <if test="tooltipsColumns != null and tooltipsColumns != ''.toString()">
                    ,${tooltipsColumns}
               </if>
          from UD t
               inner join (
                    select material,
                           plant_code,
                           avg(UU_STOCK_QTY) UU_STOCK_QTY,
                           avg(UU_STOCK_VALUE) UU_STOCK_VALUE,
                           avg(UU_STOCK_SALES_VALUE) UU_STOCK_SALES_VALUE,
                           avg(OPEN_SO_QTY) OPEN_SO_QTY,
                           avg(OPEN_SO_COST_VALUE) OPEN_SO_COST_VALUE,
                           avg(OPEN_SO_SALES_VALUE) OPEN_SO_SALES_VALUE
                    from UD t
                    where 1 = 1
                    <include refid="delivery_filter"/>
                    group by material,plant_code
               ) mm on t.material = mm.material and t.plant_code = mm.plant_code
         where 1 = 1
         <include refid="delivery_filter"/>
         group by t.UD_AGING_RANGE
         <include refid="order_by_range"/>
    </select>

    <select id="queryReport1DetailsChart" resultMap="report1ResultMap">
        WITH UD AS (
            SELECT * FROM ${SCPA.UD_HIST} T
            <where>
                DATE$ = to_date(#{report1DateRange, jdbcType=VARCHAR}, 'yyyy/mm/dd')
            </where>
        )
        select t.${selectedOptions} as "name",
               ${valueColumn} as "value"
               <if test="tooltipsColumns != null and tooltipsColumns != ''.toString()">
                    ,${tooltipsColumns}
               </if>
          from UD t
               inner join (
                    select material,
                           plant_code,
                           avg(UU_STOCK_QTY) UU_STOCK_QTY,
                           avg(UU_STOCK_VALUE) UU_STOCK_VALUE,
                           avg(UU_STOCK_SALES_VALUE) UU_STOCK_SALES_VALUE,
                           avg(OPEN_SO_QTY) OPEN_SO_QTY,
                           avg(OPEN_SO_COST_VALUE) OPEN_SO_COST_VALUE,
                           avg(OPEN_SO_SALES_VALUE) OPEN_SO_SALES_VALUE
                    from UD t
                    where 1 = 1
                    <include refid="delivery_filter"/>
                    group by material,plant_code
               ) mm on t.material = mm.material and t.plant_code = mm.plant_code
        <where>
            <if test="selectedReport1Type != null and selectedReport1Type != ''">
                UD_AGING_RANGE IN (
                <foreach collection="selectedReport1Type" separator="," item="item">
                    '${item}'
                </foreach>
                )
            </if>
            <include refid="delivery_filter"/>
        </where>
         group by t.${selectedOptions}
         order by t.${selectedOptions}
    </select>

    <sql id="queryReport1DetailsTableSql">
        WITH UD AS (
            SELECT * FROM ${SCPA.UD_HIST} T
            <where>
                DATE$ = to_date(#{report1DateRange, jdbcType=VARCHAR}, 'yyyy/mm/dd')
            </where>
        )
        select t.*
        from UD t
        <where>
            <if test="selectedReport1Type != null and selectedReport1Type != ''">
                UD_AGING_RANGE IN (
                <foreach collection="selectedReport1Type" separator="," item="item">
                    '${item}'
                </foreach>
                )
            </if>
            <include refid="delivery_filter"/>
        </where>
    </sql>

    <select id="queryReport1DetailsTableCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1DetailsTableSql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1DetailsTable" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1DetailsTableSql"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport1DetailsSelectedSql">
        WITH UD AS (
            SELECT * FROM ${SCPA.UD_HIST} T
            <where>
                DATE$ = to_date(#{report1DateRange, jdbcType=VARCHAR}, 'yyyy/mm/dd')
            </where>
        )
        select t.*
        from UD t
        <where>
            <if test="selectedReport1Type != null and selectedReport1Type != ''">
                UD_AGING_RANGE IN (
                <foreach collection="selectedReport1Type" separator="," item="item">
                    '${item}'
                </foreach>
                )
            </if>
            <include refid="delivery_filter"/>
        </where>

    </sql>

    <select id="queryReport1DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1DetailsSelectedSql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1DetailsSelectedSql"/>
        <include refid="global.select_footer"/>
    </select>

    <resultMap id="report2ResultMap" type="com.scp.inventory.bean.DeliveryReport2Data">
        <result property="UD_DATE" column="UD_DATE"/>
        <result property="NAME" column="NAME"/>
        <result property="UD_VALUE" column="UD_VALUE"/>
    </resultMap>

    <select id="queryReport2Legend" resultType="java.lang.String">
        select DISTINCT  NVL(${report2Type}, 'Others') AS NAME
        from ${SCPA.UD_HIST} t
        where date$ between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') and last_day(to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'))
        <include refid="delivery_filter"/>
    </select>

    <select id="queryReport2" parameterType="java.util.Map" resultMap="report2ResultMap">
        select to_char(DATE$,'yyyy/mm/dd') AS UD_DATE,
            NVL(T.${report2Type}, 'Others') AS NAME,
            round(${valueColumn},2) UD_VALUE
        from ${SCPA.UD_HIST} t
        where date$ between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') and last_day(to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'))
            <include refid="delivery_filter"/>
        group by DATE$, T.${report2Type}
        <choose>
            <when test="report2Type == 'UD_AGING_RANGE'.toString()">
                <include refid="order_by_range"/>
            </when>
            <otherwise>
                order by NVL(T.${report2Type}, 'Others')
            </otherwise>
        </choose>

    </select>

    <sql id="queryReport2DetailsSql">
        select  t.*
        from ${SCPA.UD_HIST} t
        where 1 = 1

        <choose>
            <when test="selectedReport2Date == null and selectedReport2Type == null">
            </when>
            <when test="selectedReport2Type == 'Others'.toString()">
                and T.${report2Type} is null
                and DATE$ = to_date(#{selectedReport2Date, jdbcType=VARCHAR},'yyyy/mm/dd')
            </when>
            <otherwise>
                and T.${report2Type} = #{selectedReport2Type, jdbcType=VARCHAR}
                and DATE$ = to_date(#{selectedReport2Date, jdbcType=VARCHAR},'yyyy/mm/dd')
            </otherwise>
        </choose>

        <include refid="delivery_filter"/>
    </sql>

    <select id="queryReport2DetailsCount" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport2DetailsSql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport2Details" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport2DetailsSql"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport3" resultType="java.util.LinkedHashMap">
        with ud as (
            select * from ${SCPA.UD_HIST} t
            <where>
                DATE$ = to_date(#{report3Date, jdbcType=VARCHAR}, 'yyyy/mm/dd')
                <include refid="delivery_filter"/>
            </where>
        ),
        base as (
             SELECT
                <choose>
                    <when test="report3Type.size() == 0">
                        key,
                    </when>
                    <otherwise>
                        <foreach collection="report3Type" separator="" item="item" index="index">
                            <if test="index != (report3Type.size()-1) and item!=null">
                                ${item},
                            </if>
                        </foreach>
                        key,
                    </otherwise>
                </choose>
                "'0-1D'_TOTAL",
                "'1-3D'_TOTAL",
                "'3-7D'_TOTAL",
                "'7-14D'_TOTAL",
                "'14-30D'_TOTAL",
                "'1-2M'_TOTAL",
                "'2-3M'_TOTAL",
                "'3-6M'_TOTAL",
                "'>6M'_TOTAL",
                nvl("'0-1D'_TOTAL",0) +  nvl("'1-3D'_TOTAL",0) +   nvl("'3-7D'_TOTAL",0) +
                nvl("'7-14D'_TOTAL",0) + nvl("'14-30D'_TOTAL",0) + nvl("'1-2M'_TOTAL",0) +
                nvl("'2-3M'_TOTAL",0) +  nvl("'3-6M'_TOTAL",0) +   nvl("'>6M'_TOTAL",0) TOTAL_UD
             FROM (select
                    <choose>
                        <when test="report3Type.size() == 0">
                            SOLD_TO_SHORT_NAME key,
                        </when>
                        <otherwise>
                            <foreach collection="report3Type" separator="," item="item" close="," index="index">
                                <choose>
                                    <when test="index !=(report3Type.size()-1)">
                                        ${item}
                                    </when>
                                    <otherwise>
                                        to_char(${item}) key
                                    </otherwise>
                                </choose>
                            </foreach>
                        </otherwise>
                    </choose>
                    t.UD_AGING_RANGE as UAR,
                    ${valueColumn} total
                  FROM UD t
                  group by
                    <choose>
                        <when test="report3Type.size() == 0">
                            SOLD_TO_SHORT_NAME,
                        </when>
                        <otherwise>
                            <foreach collection="report3Type" separator="," item="item" close=",">
                                ${item}
                            </foreach>
                        </otherwise>
                    </choose>
                  t.UD_AGING_RANGE) mm PIVOT (
                        sum(total) total FOR
                            UAR IN ( '0-1D', '1-3D', '3-7D', '7-14D', '14-30D', '1-2M', '2-3M','3-6M', '>6M'))
             order by ${report3Order} desc
             fetch next ${report3Length} rows only),
             total_sub as (
                 select 'Sub-Total'                       key,
                        round(sum(t.TOTAL_UD), 2)         TOTAL_UD,
                        round(sum(t."'0-1D'_TOTAL"), 2)   "'0-1D'_TOTAL",
                        round(sum(t."'1-3D'_TOTAL"), 2)   "'1-3D'_TOTAL",
                        round(sum(t."'3-7D'_TOTAL"), 2)   "'3-7D'_TOTAL",
                        round(sum(t."'7-14D'_TOTAL"), 2)  "'7-14D'_TOTAL",
                        round(sum(t."'14-30D'_TOTAL"), 2) "'14-30D'_TOTAL",
                        round(sum(t."'1-2M'_TOTAL"), 2)   "'1-2M'_TOTAL",
                        round(sum(t."'2-3M'_TOTAL"), 2)   "'2-3M'_TOTAL",
                        round(sum(t."'3-6M'_TOTAL"), 2)   "'3-6M'_TOTAL",
                        round(sum(t."'>6M'_TOTAL"), 2)    "'>6M'_TOTAL"
                 from base t
             ),
             total as (
                 SELECT key,
                        "'0-1D'_TOTAL",
                        "'1-3D'_TOTAL",
                        "'3-7D'_TOTAL",
                        "'7-14D'_TOTAL",
                        "'14-30D'_TOTAL",
                        "'1-2M'_TOTAL",
                        "'2-3M'_TOTAL",
                        "'3-6M'_TOTAL",
                        "'>6M'_TOTAL",
                        nvl("'0-1D'_TOTAL",0) +  nvl("'1-3D'_TOTAL",0) +   nvl("'3-7D'_TOTAL",0) +
                        nvl("'7-14D'_TOTAL",0) + nvl("'14-30D'_TOTAL",0) + nvl("'1-2M'_TOTAL",0) +
                        nvl("'2-3M'_TOTAL",0) +  nvl("'3-6M'_TOTAL",0) +   nvl("'>6M'_TOTAL",0) TOTAL_UD
                 FROM (select 'Grand-Total' key, t.UD_AGING_RANGE, ${valueColumn} total
                       FROM UD t
                       group by t.UD_AGING_RANGE) mm PIVOT (
                     sum(total) total
                     FOR UD_AGING_RANGE IN ( '0-1D', '1-3D', '3-7D', '7-14D', '14-30D', '1-2M', '2-3M','3-6M', '>6M')
                 )
             )

        select
        <choose>
            <when test="report3Type.size() == 0">
                key,
            </when>
            <otherwise>
                <foreach collection="report3Type" separator="," item="item" close="," index="index">
                    <choose>
                        <when test="index !=(report3Type.size()-1)">
                            ${item}
                        </when>
                        <otherwise>
                            key
                        </otherwise>
                    </choose>
                </foreach>
            </otherwise>
        </choose>
               t.TOTAL_UD,
               t."'0-1D'_TOTAL",
               t."'1-3D'_TOTAL",
               t."'3-7D'_TOTAL",
               t."'7-14D'_TOTAL",
               t."'14-30D'_TOTAL",
               t."'1-2M'_TOTAL",
               t."'2-3M'_TOTAL",
               t."'3-6M'_TOTAL",
               t."'>6M'_TOTAL" from base t
        union all
        select <foreach collection="report3Type" separator="" index="index" item="item">
        <if test="index != 0">
            null,
        </if>
    </foreach>
               t.key,
               t.TOTAL_UD,
               t."'0-1D'_TOTAL",
               t."'1-3D'_TOTAL",
               t."'3-7D'_TOTAL",
               t."'7-14D'_TOTAL",
               t."'14-30D'_TOTAL",
               t."'1-2M'_TOTAL",
               t."'2-3M'_TOTAL",
               t."'3-6M'_TOTAL",
               t."'>6M'_TOTAL" from total_sub t
        union all
        select <foreach collection="report3Type" separator="" index="index" item="item">
        <if test="index != 0">
            null,
        </if>
    </foreach>
               t.key,
               t.TOTAL_UD,
               t."'0-1D'_TOTAL",
               t."'1-3D'_TOTAL",
               t."'3-7D'_TOTAL",
               t."'7-14D'_TOTAL",
               t."'14-30D'_TOTAL",
               t."'1-2M'_TOTAL",
               t."'2-3M'_TOTAL",
               t."'3-6M'_TOTAL",
               t."'>6M'_TOTAL" from total t
        union all
        select
        <foreach collection="report3Type" separator="" index="index" item="item">
            <if test="index != 0">
                null,
            </if>
        </foreach>
               '%',
               round(decode(total.TOTAL_UD, 0, 0, total_sub.TOTAL_UD / total.TOTAL_UD) * 100, 2)                            TOTAL_UD,
               round(decode(total."'0-1D'_TOTAL", 0, 0, total_sub."'0-1D'_TOTAL" / total."'0-1D'_TOTAL") * 100, 2)          "'0-1D'_TOTAL",
               round(decode(total."'1-3D'_TOTAL", 0, 0, total_sub."'1-3D'_TOTAL" / total."'1-3D'_TOTAL") * 100, 2)          "'1-3D'_TOTAL",
               round(decode(total."'3-7D'_TOTAL", 0, 0, total_sub."'3-7D'_TOTAL" / total."'3-7D'_TOTAL") * 100, 2)          "'3-7D'_TOTAL",
               round(decode(total."'7-14D'_TOTAL", 0, 0, total_sub."'7-14D'_TOTAL" / total."'7-14D'_TOTAL") * 100, 2)       "'7-14D'_TOTAL",
               round(decode(total."'14-30D'_TOTAL", 0, 0, total_sub."'14-30D'_TOTAL" / total."'14-30D'_TOTAL") * 100, 2)    "'14-30D'_TOTAL",
               round(decode(total."'1-2M'_TOTAL", 0, 0, total_sub."'1-2M'_TOTAL" / total."'1-2M'_TOTAL") * 100, 2)          "'1-2M'_TOTAL",
               round(decode(total."'2-3M'_TOTAL", 0, 0, total_sub."'2-3M'_TOTAL" / total."'2-3M'_TOTAL") * 100, 2)          "'2-3M'_TOTAL",
               round(decode(total."'3-6M'_TOTAL", 0, 0, total_sub."'3-6M'_TOTAL" / total."'3-6M'_TOTAL") * 100, 2)          "'3-6M'_TOTAL",
               round(decode(total."'>6M'_TOTAL", 0, 0, total_sub."'>6M'_TOTAL" / total."'>6M'_TOTAL") * 100, 2)             "'>6M'_TOTAL"
        from total, total_sub
    </select>

    <sql id="queryReport3DetailsSql">
        WITH UD AS (
            SELECT * FROM ${SCPA.UD_HIST} T
            <where>
                DATE$ = to_date(#{report3Date, jdbcType=VARCHAR}, 'yyyy/mm/dd')
            </where>
        )
        select  t.*
        from UD t
        where 1 = 1
        <if test="selectedReport3Type != null and selectedReport3Type != ''.toString()">
            and nvl(to_char(${report3Type[report3Type.size()-1]}),'Others') = #{selectedReport3Type, jdbcType=VARCHAR}
        </if>
        <if test="selectedReport3Date != null and selectedReport3Date != ''.toString()">
            and ud_aging_range = #{selectedReport3Date, jdbcType=VARCHAR}
        </if>
        <include refid="delivery_filter"/>
    </sql>

    <select id="queryReport3DetailsCount" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport3DetailsSql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport3Details" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport3DetailsSql"/>
        <include refid="global.select_footer"/>
    </select>
    <resultMap id="report4ResultMap" type="com.scp.inventory.bean.InventoryDeliveryReport4Bean">
        <result property="category1" column="CATEGORY1"/>
        <result property="category2" column="CATEGORY2"/>
        <result property="category3" column="CATEGORY3"/>
        <result property="category4" column="CATEGORY4"/>
        <result property="category5" column="CATEGORY5"/>
        <result property="value" column="value"/>
        <association property="tooltips" javaType="com.scp.inventory.bean.InventoryDeliveryReport4Tooltips">
            <result property="DELIVERY_QUANTITY" column="DELIVERY_QUANTITY"/>
            <result property="DELIVERY_VALUE" column="DELIVERY_VALUE"/>
            <result property="DELIVERY_NET_VALUE" column="DELIVERY_NET_VALUE"/>
            <result property="DELIVERY_COST_VALUE" column="DELIVERY_COST_VALUE"/>
            <result property="UU_STOCK_QTY" column="UU_STOCK_QTY"/>
            <result property="UU_STOCK_VALUE" column="UU_STOCK_VALUE"/>
            <result property="UU_STOCK_SALES_VALUE" column="UU_STOCK_SALES_VALUE"/>
            <result property="OPEN_SO_QTY" column="OPEN_SO_QTY"/>
            <result property="OPEN_SO_COST_VALUE" column="OPEN_SO_COST_VALUE"/>
            <result property="OPEN_SO_SALES_VALUE" column="OPEN_SO_SALES_VALUE"/>
        </association>
    </resultMap>

    <sql id="queryBase">
        SELECT /*+ parallel(t 6) */
            *
        FROM ${tableName} t
    </sql>

    <select id="queryReport4" resultMap="report4ResultMap">
        SELECT
        NVL(${level1}, 'Others') AS CATEGORY1,
        NVL(${level2}, 'Others') AS CATEGORY2,
        NVL(${level3}, 'Others') AS CATEGORY3,
        <if test="level4 != null and level4 != ''.toString()">
            NVL(${level4}, 'Others') AS CATEGORY4,
        </if>
        <if test="level5 != null and level5 != ''.toString()">
            NVL(${level5}, 'Others') AS CATEGORY5,
        </if>
        ${valueColumn} AS VALUE
        <if test="tooltipsColumns != null and tooltipsColumns != ''.toString()">
            ,${tooltipsColumns}
        </if>
        FROM ${SCPA.UD_HIST} t
        <where>
            DATE$ = to_date(#{report4DateRange, jdbcType=VARCHAR}, 'yyyy/mm/dd')
            <include refid="delivery_filter"/>
        </where>
        GROUP BY
        ${level1}, ${level2}, ${level3}
        <if test="level4 != null and level4 != ''.toString()">,${level4}</if>
        <if test="level5 != null and level5 != ''.toString()">,${level5}</if>
    </select>
</mapper>
