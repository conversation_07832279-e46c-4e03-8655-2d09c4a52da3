<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.inventory.dao.IInventoryUHSDao">
    <sql id="filter">
        <if test="filters != null and filters != ''.toString()">
            and ${filters}
        </if>
    </sql>

    <select id="queryUHSCode" resultType="java.util.Map">
        SELECT CATEGORY, CODE FROM UHS_CODE
    </select>

    <select id="queryWeeks" resultType="java.lang.String">
        SELECT DISTINCT WEEK FROM SCPA.UHS_RCA_HIST ORDER BY WEEK DESC
    </select>

    <select id="queryWeekColumns" resultType="java.lang.String">
        select distinct week from SCPA.UHS_RCA_HIST
         where week &lt;= #{week, jdbcType=VARCHAR}
         order by week desc
         fetch next #{count, jdbcType=INTEGER} rows only
    </select>

    <select id="queryReport1" resultType="java.util.Map">
        WITH UHS_RCA_TEMP AS (
            SELECT T.MATERIAL,
                   T.PLANT_CODE,
                   T.WEEK,
                   T.ENTITY,
                   SUBSTR(T.WEEK, 1, 4) AS YEAR,
                   T.STOCK_VALUE,
                   T.STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT,
                   T.HS_THEO_PROV_VALUE,
                   T.HS_EXCESS_VALUE,
                   T.HS_MISSING_VALUE,
                   CASE WHEN T.HS_RCA LIKE 'E98%' THEN (NVL(T.HS_EXCESS_VALUE, 0) + NVL(T.HS_THEO_PROV_VALUE, 0)) END AS SPECIAL
            FROM ${SCPA.UHS_RCA_HIST} T
            WHERE T.ENTITY IS NOT NULL
              AND T.ENTITY NOT IN ('SSES', 'CMHL', 'SMC', 'PCW', 'SEBJ')
              AND (T.WEEK like #{year, jdbcType=VARCHAR} || '%' OR WEEK IN
                  <foreach collection="weekList" separator="," item="item" open="(" close=")">
                    '${item}'
                  </foreach>)
              <include refid="filter"/>
        ),
        BY_WEEK AS (
            SELECT *
              FROM (
                 SELECT TOTAL.WEEK,
                        TOTAL.ENTITY,
                        ROUND(DECODE(STOCK_VALUE_IN_LAST, 0, 0, (EXCESS + MISSING + PROV) / STOCK_VALUE_IN_LAST), 4)           GROSS_RATIO,
                        ROUND(DECODE(STOCK_VALUE_IN_LAST, 0, 0, (EXCESS + MISSING + PROV - SPECIAL) / STOCK_VALUE_IN_LAST), 4) NET_RATIO,
                        ROUND(DECODE(STOCK_VALUE_IN_LAST, 0, 0, SPECIAL / STOCK_VALUE_IN_LAST), 4)                             SPECIAL_RATIO,
                        PROV,
                        STOCK_VALUE_IN_LAST AS TOTAL,
                        EXCESS,
                        MISSING,
                        SPECIAL
                 FROM (
                          SELECT T.WEEK,
                                 T.ENTITY,
                                 NVL(SUM(T.STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT), 0) STOCK_VALUE_IN_LAST,
                                 NVL(SUM(T.HS_THEO_PROV_VALUE), 0) PROV,
                                 NVL(SUM(T.HS_EXCESS_VALUE), 0)          EXCESS,
                                 NVL(SUM(T.HS_MISSING_VALUE), 0)         MISSING,
                                 NVL(SUM(T.SPECIAL), 0)            SPECIAL
                            FROM UHS_RCA_TEMP T
                           WHERE T.WEEK IN
                           <foreach collection="weekList" separator="," item="item" open="(" close=")">
                                '${item}'
                           </foreach>
                          GROUP BY T.WEEK, T.ENTITY
                      ) TOTAL
                 )
                 PIVOT (sum(TOTAL) TOTAL,
                        sum(PROV) PROV,
                        sum(EXCESS) EXCESS,
                        sum(MISSING) MISSING,
                        sum(SPECIAL) SPECIAL,
                        sum(GROSS_RATIO) GROSS_RATIO,
                        sum(NET_RATIO) NET_RATIO,
                        sum(SPECIAL_RATIO) SPECIAL_RATIO
                   FOR WEEK IN
                 <foreach collection="weekList" separator="," item="item" open="(" close=")">
                      '${item}'
                 </foreach>)
        ),
        BY_YTD AS (
                 SELECT *
                 FROM (
                     SELECT NN.YEAR,
                            ENTITY,
                            PROV,
                            TOTAL,
                            EXCESS,
                            MISSING,
                            SPECIAL,
                            ROUND(DECODE(TOTAL, 0, 0, (PROV + EXCESS + MISSING) / TOTAL), 4)           GROSS_RATIO,
                            ROUND(DECODE(TOTAL, 0, 0, (PROV + EXCESS + MISSING - SPECIAL) / TOTAL), 4) NET_RATIO,
                            ROUND(DECODE(TOTAL, 0, 0, SPECIAL / TOTAL), 4)                             SPECIAL_RATIO
                     FROM (
                              SELECT YEAR,
                                     ENTITY,
                                     AVG(MM.TOTAL)   TOTAL,
                                     AVG(MM.PROV)    PROV,
                                     AVG(MM.EXCESS)  EXCESS,
                                     AVG(MM.MISSING) MISSING,
                                     AVG(MM.SPECIAL) SPECIAL
                              FROM (
                                    SELECT MAX(T.YEAR) YEAR,
                                           T.WEEK,
                                           T.ENTITY,
                                           NVL(SUM(T.STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT), 0)  TOTAL,
                                           NVL(SUM(T.HS_THEO_PROV_VALUE), 0)                    PROV,
                                           NVL(SUM(T.HS_EXCESS_VALUE), 0)                             EXCESS,
                                           NVL(SUM(T.HS_MISSING_VALUE), 0)                            MISSING,
                                           NVL(SUM(T.SPECIAL), 0)                               SPECIAL
                                      FROM UHS_RCA_TEMP T
                                     WHERE T.YEAR = #{year, jdbcType=VARCHAR}
                                     GROUP BY T.WEEK, T.ENTITY
                                   ) MM
                              GROUP BY MM.YEAR, MM.ENTITY
                          ) NN
                 ) KK
                     PIVOT (sum(TOTAL) TOTAL,
                            sum(PROV) PROV,
                            sum(EXCESS) EXCESS,
                            sum(MISSING) MISSING,
                            sum(SPECIAL) SPECIAL,
                            sum(GROSS_RATIO)   GROSS_RATIO,
                            sum(NET_RATIO)     NET_RATIO,
                            sum(SPECIAL_RATIO) SPECIAL_RATIO
                       FOR YEAR IN ( '${year}' ) )
             )

        SELECT * FROM (
            SELECT T.*,
                   T2."'${year}'_TOTAL",
                   T2."'${year}'_PROV",
                   T2."'${year}'_EXCESS",
                   T2."'${year}'_MISSING",
                   T2."'${year}'_SPECIAL",
                   T2."'${year}'_GROSS_RATIO",
                   T2."'${year}'_NET_RATIO"
            FROM BY_WEEK T INNER JOIN BY_YTD T2 ON T.ENTITY = T2.ENTITY
        ) TT
        <if test="_page.sort != null and _page.sort != ''.toString">
             ORDER BY ${_page.sort}
        </if>
    </select>

    <select id="queryReport1LastYearEnd" resultType="java.lang.String">
        select ${lastYear} || max(WEEK_NO)
          from SY_CALENDAR t
         where t.year = #{lastYear, jdbcType=VARCHAR}
    </select>

    <select id="queryReport1MonthEnd" resultType="java.lang.String">
        select year || WEEK_NO from SY_CALENDAR t where t.name = 'National Holidays' and t.DATE$ =
        (select add_months(min(t.DATE$), -1) from SY_CALENDAR t where year = #{thisYear, jdbcType=VARCHAR} and WEEK_NO = #{weekNo, jdbcType=VARCHAR})
    </select>

    <select id="queryWeekByRange" resultType="java.lang.String">
        SELECT DISTINCT T.WEEK FROM ${SCPA.UHS_RCA_HIST} T
         WHERE T.WEEK BETWEEN #{report2WeekStart,jdbcType=VARCHAR} AND #{report2WeekEnd,jdbcType=VARCHAR}
         ORDER BY T.WEEK
    </select>

    <select id="queryReport2" resultType="java.util.Map">
        SELECT t.WEEK,
               nvl(sum(t.STOCK_VALUE),0) STOCK_VALUE,
               nvl(sum(t.STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT),0) STOCK_VALUE_IN_LAST,
               nvl(sum(T.HS_THEO_PROV_VALUE),0) PROV,
               nvl(sum(T.HS_EXCESS_VALUE),0) EXCESS,
               nvl(sum(T.HS_MISSING_VALUE),0) MISSING,
               nvl(sum(CASE WHEN T.HS_RCA LIKE 'E98%' THEN (NVL(T.HS_EXCESS_VALUE, 0) + NVL(T.HS_MISSING_VALUE, 0) + NVL(T.HS_THEO_PROV_VALUE, 0)) END),0) AS SPECIAL
         FROM ${SCPA.UHS_RCA_HIST} t
        WHERE WEEK in <foreach collection="weekList" separator="," item="item" open="(" close=")">
                               '${item}'
                      </foreach>
              <include refid="filter"/>
        GROUP BY WEEK
    </select>

    <sql id="queryReport1DetailsSQL">
        SELECT T.MATERIAL,
               T.PLANT_CODE,
               T.WEEK,
               T.HS_RCA,
               T.HS_EXCESS_VALUE,
               T.HS_MISSING_VALUE,
               T.SYSTEM,
               T.PLANT_TYPE,
               T.ACTIVENESS,
               T.STOCKING_POLICY,
               T.BLOCKED_QTY,
               T.MOQ_ERP,
               T.MOQ_THEO,
               T.MOQ_TO_CUSTOMER,
               T.STOCK,
               T.STOCK_VALUE,
               T.FS_SOH,
               T.FS_SOH_VALUE,
               T.PROPOSED_FIN_PROV_QTY,
               T.PROPOSED_FIN_PROV_VALUE,
               T.PROPOSED_PROVISION_RULE,
               T.NETTED_STOCK_QTY,
               T.NETTED_STOCK_VALUE,
               T.M1_NOC_QTY,
               T.M1_NOC_VAL,
               T.DIST_CHANNEL_SP_ST,
               T.CUS_RETURN_PAST_1W,
               T.CUS_RETURN_PAST_2W,
               T.CUS_RETURN_PAST_3W,
               T.CUS_RETURN_PAST_4W,
               T.OPEN_SO_ALL_QTY_TOTAL,
               T.OPEN_SO_QTY_HS,
               T.CUS_CONSIGNMENT,
               T.CUS_CONSIGNMENT_VALUE,
               T.STOCK_CONS_QTY,
               T.EARLY_GR_1W,
               T.EARLY_GR_2W,
               T.EARLY_GR_3W,
               T.EARLY_GR_4W,
               T.EARLY_GR_MTO_1W,
               T.EARLY_GR_MTO_2W,
               T.EARLY_GR_MTO_3W,
               T.EARLY_GR_MTO_4W,
               T.SS,
               T.JNI_SS,
               T.SUBCON_STOCK_COMPANY,
               T.SUBCON_STOCK_COMPANY_VALUE,
               T.SUBCON_STOCK_CONSIGN,
               T.SUBCON_STOCK_CONSIGN_VALUE,
               T.DAYS_SINCE_LAST_CONSUMPTION,
               T.NEW_PRODUCTS,
               T.DC_CURR_MONTH_CRD_QTY,
               T.DC_LAST_MONTH_CRD_QTY,
               T.DC_CURR_MONTH_FCST_M0,
               T.DC_CURR_MONTH_FCST_M1,
               T.DC_CURR_MONTH_FCST_M3,
               T.DC_LAST_MONTH_FCST_M1,
               T.DC_LAST_MONTH_FCST_M3,
               T.DC_FULFILL_RATIO_MTD_M0,
               T.DC_FULFILL_RATIO_MTD_M1,
               T.DC_FULFILL_RATIO_MTD_M3,
               T.DC_FULFILL_RATIO_LMTD_M1,
               T.DC_FULFILL_RATIO_LMTD_M3,
               T.CREATION_DATE,
               T.FIRST_CONS_DATE,
               T.FIRST_SO_DATE,
               T.GRA,
               T.PLANT_CURR_MONTH_CONSUMPTION_QTY,
               T.PLANT_LAST_MONTH_CONSUMPTION_QTY,
               T.PLANT_CURR_MONTH_FCST_M1,
               T.PLANT_CURR_MONTH_FCST_M2,
               T.PLANT_CURR_MONTH_FCST_M3,
               T.PLANT_LAST_MONTH_FCST_M1,
               T.PLANT_LAST_MONTH_FCST_M2,
               T.PLANT_LAST_MONTH_FCST_M3,
               T.PLANT_FULFILL_RATIO_MTD_M1,
               T.PLANT_FULFILL_RATIO_MTD_M2,
               T.PLANT_FULFILL_RATIO_MTD_M3,
               T.PLANT_FULFILL_RATIO_LMTD_M1,
               T.PLANT_FULFILL_RATIO_LMTD_M2,
               T.PLANT_FULFILL_RATIO_LMTD_M3,
               T.SPECIAL_PROC_CODE,
               T.LATE_OPEN_PO_QTY,
               T.LATE_OPEN_PO_QTY_1W,
               T.LATE_OPEN_PO_QTY_2W,
               T.LATE_OPEN_PO_QTY_3W,
               T.LATE_OPEN_PO_QTY_4W,
               T.MRP_CONTROLLER,
               T.STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT,
               T.HS_STATUS,
               T.GROSS_UNHEALTHY_VALUE,
               T.NET_UNHEALTHY_VALUE,
               T.HS_THEO_PROV_VALUE,
               T.HS_THEO_PROV_QTY,
               T.FIN_PROV_VALUE,
               T.DECLARED_SPECIAL_EVENTS,
               T.CALCULATION_BASIS,
               T.ADU_TOTAL_QTY,
               T.ADU_TOTAL_VALUE,
               T.ADF_QTY,
               T.ADF_VALUE,
               T.CURRENT_STOCK_QTY,
               T.CURRENT_STOCK_VALUE,
               T.SAFETY_STOCK_VALUE_ERP,
               T.SAFETY_STOCK_VALUE_HS,
               T.HS_SS_CALCULATION_FORMULA,
               T.BLOCKED_VALUE,
               T.OPEN_SO_VALUE_HS,
               T.OPEN_REQUIREMENT_QTY,
               T.OPEN_REQUIREMENT_VALUE,
               T.FIXED_LOT_SIZE_QTY,
               T.MANUAL_STOCKING_POLICY,
               T.CLUSTER_NAME,
               T.ENTITY,
               T.BU,
               T.PRODUCT_LINE,
               T.PRODUCTION_LINE,
               T.LOCAL_BU,
               T.LOCAL_PRODUCT_LINE,
               T.LOCAL_PRODUCT_FAMILY,
               T.LOCAL_PRODUCT_SUBFAMILY,
               T.VENDOR_CODE,
               T.VENDOR_NAME,
               T.SOURCE_CATEGORY,
               T.MATERIAL_TYPE,
               T.LT_RANGE,
               T.CALCULATED_ABC,
               T.CALCULATED_FMR,
               T.INDUSTRY_CODE,
               T.ABC,
               T.PURCHASING_GROUP,
               T.PROCUREMENT_TYPE,
               T.FOLLOW_UP_MATERIAL,
               T.PRODN_SUPERVISOR,
               T.MAT_PRICING_GROUP,
               T.HS_ACTION,
               T.HS_ACTION_LEADER_CODE,
               T.HS_ACTION_OWNER_CODE,
               T.HS_REVIEW_FREQUENCY,
               T.MATERIAL_OWNER_NAME,
               T.MATERIAL_OWNER_SESA,
               T.EXISTING_IN_BOM,
               T.COMMODITY_CODE,
               T.PRODUCT_LINE_INV,
               T.LOCATION_CATEGORY,
               T.REPL_STRATEGY,
               T.MATERIAL_CATEGORY,
               T.PRODUCT_FAMILY_INV
        FROM ${SCPA.UHS_RCA_HIST} T
       WHERE T.WEEK LIKE #{report1SelectedWeek, jdbcType=VARCHAR} || '%'
             <if test="report1SelectedEntity != null and report1SelectedEntity !=''.toString()">
                 AND T.ENTITY = #{report1SelectedEntity, jdbcType=VARCHAR}
             </if>
             <choose>
                 <when test="report1SelectedType == 'Excess'.toString()">
                     AND T.HS_EXCESS_VALUE &gt; 0
                 </when>
                 <when test="report1SelectedType == 'Theo.provision'.toString()">
                     AND T.HS_THEO_PROV_VALUE &gt; 0
                 </when>
                 <when test="report1SelectedType == 'Missing'.toString()">
                     AND T.HS_MISSING_VALUE &gt; 0
                 </when>
             </choose>
             <include refid="filter"/>
    </sql>

    <select id="queryReport1DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport3SQL">
        with TOTAL_TEMP AS (SELECT SUM(${SUM_COLUMN}) CNT FROM ${SCPA.UHS_RCA_HIST} T WHERE WEEK = #{week, jdbcType=VARCHAR} <include refid="filter"/>)
        SELECT * FROM (
            SELECT DECODE(TOTAL, 0, 0, ROUND(SUM_OVER / TOTAL * 100,2)) GROSS_UHS_VALUE_OF_ALL,
                   COD_PLANT,
                   COD_MATERIAL_COM,
                   RCA_RECOMMEND,
                   HS_XSMIS_RCA,
                   FINAL_RCA,
                   COD_ACTION_RCA,
                   HS_XSMIS_ACT,
                   DATE_INI_BTN,
                   DATE_FIN_BTN,
                   COD_STATUS_RCA,
                   FINAL_ACTION,
                   ACTION_RECOMMEND,
                   COD_FREQ_RCA,
                   STOCK_VS_HS,
                   GROSS_UNHEALTHY_VALUE,
                   JNI_STOCK_VALUE,
                   HS_EXCESS_VALUE,
                   THEO_PROV_VALUE,
                   PROPOSED_FIN_PROV_QTY,
                   PROPOSED_FIN_PROV_VALUE,
                   PROPOSED_PROVISION_RULE,
                   NETTED_STOCK_QTY,
                   NETTED_STOCK_VALUE,
                   M1_NOC_QTY,
                   M1_NOC_VAL,
                   HS_MISSING_VALUE,
                   DECLARED_SPECIAL_EVENTS,
                   JNI_SS_VALUE,
                   SS,
                   SS_VALUE,
                   STOCK,
                   STOCK_VALUE,
                   ADU_ADF_BASIS,
                   ADU_TOTAL,
                   ADU_TOTAL_VALUE,
                   ADF,
                   ADF_VALUE,
                   OPEN_SO_ALL_QTY_HS,
                   OPEN_SO_ALL_VAL_HS,
                   OPEN_SO_ALL_QTY,
                   OPEN_SO_ALL_VAL,
                   OPEN_PR_QTY_HS,
                   OPEN_PR_VAL_HS,
                   FIXED_LOT_SIZE,
                   JNI_LOT_SIZE,
                   STOCKING_POLICY,
                   MTO_MANUAL_RULE,
                   DATE_SALES,
                   BLOCKED_QTY,
                   BLOCKED_VAL,
                   COD_SYSTEM,
                   DATE_ACTION_RCA,
                   COD_ACTION_LEADER,
                   SHORT_NAME_LEADER,
                   COD_ACTION_OWNER,
                   SHORT_NAME_OWNER,
                   STAKE,
                   BU,
                   CLUSTER_NAME,
                   PLANT_TYPE,
                   ENTITY,
                   COD_PM0,
                   ID_SQ_MRP_CONTROLLER,
                   NEW_PRODUCTS,
                   FLG_FIXED_SUPPLIER,
                   EXISTING_IN_BOM,
                   COMMODITY_CODE,
                   PRODUCT_LINE_INV,
                   LOCATION_CATEGORY,
                   REPL_STRATEGY,
                   MATERIAL_CATEGORY,
                   PRODUCT_FAMILY_INV,
                   MATERIAL_OWNER_NAME,
                   MATERIAL_OWNER_SESA,
                   PLANT_CODE,
                   PRODUCT_LINE,
                   LOCAL_PRODUCT_FAMILY,
                   LOCAL_PRODUCT_SUBFAMILY,
                   LOCAL_PRODUCT_LINE,
                   MRP_CONTROLLER,
                   VENDOR_NAME,
                   MATERIAL_TYPE,
                   SPECIAL_PROC_CODE,
                   ACTIVENESS,
                   PROCUREMENT_TYPE,
                   SOURCE_CATEGORY,
                   CALCULATED_FMR,
                   PRODUCTION_LINE
            FROM (
                select TOTAL.CNT                               TOTAL,
                       SUM(${SUM_COLUMN}) OVER (ORDER BY TO_NUMBER(${SUM_COLUMN}) DESC) SUM_OVER,
                       T.PLANT_CODE                            COD_PLANT,
                       T.MATERIAL                              COD_MATERIAL_COM,
                       CASE
                        WHEN EXCESS.RCA_CODES IS NOT NULL AND MISSING.RCA_CODE IS NULL THEN EXCESS.RCA_CODES
                        WHEN EXCESS.RCA_CODES IS NOT NULL AND MISSING.RCA_CODE IS NOT NULL THEN EXCESS.RCA_CODES || ',' || MISSING.RCA_CODE
                        WHEN EXCESS.RCA_CODES IS NULL AND MISSING.RCA_CODE IS NOT NULL THEN MISSING.RCA_CODE
                       END RCA_RECOMMEND,
                       T.HS_RCA                                HS_XSMIS_RCA,
                       RESULT.FINAL_RCA,
                       DECODE(CODE.OPT2, NULL, CODE.OPT1, CODE.OPT1 || ';' || CODE.OPT2) AS ACTION_RECOMMEND,
                       RESULT.FINAL_ACTION,
                       RESULT.HS_XSMIS_ACT,
                       RESULT.DATE_INI_BTN,
                       RESULT.DATE_FIN_BTN,
                       RESULT.COD_STATUS_RCA,
                       NVL(RESULT.COD_FREQ_RCA, T.HS_REVIEW_FREQUENCY) COD_FREQ_RCA,
                       T.HS_ACTION                             COD_ACTION_RCA,
                       T.HS_STATUS                             STOCK_VS_HS,
                       T.GROSS_UNHEALTHY_VALUE                 GROSS_UNHEALTHY_VALUE,
                       T.STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT  JNI_STOCK_VALUE,
                       T.HS_EXCESS_VALUE                       HS_EXCESS_VALUE,
                       T.HS_THEO_PROV_VALUE                    THEO_PROV_VALUE,
                       T.HS_MISSING_VALUE                      HS_MISSING_VALUE,
                       T.DECLARED_SPECIAL_EVENTS               DECLARED_SPECIAL_EVENTS,
                       T.SAFETY_STOCK_VALUE_HS                 JNI_SS_VALUE,
                       T.SS                                    SS,
                       T.SAFETY_STOCK_VALUE_ERP                SS_VALUE,
                       T.CURRENT_STOCK_QTY                     STOCK,
                       T.CURRENT_STOCK_VALUE                   STOCK_VALUE,
                       T.CALCULATION_BASIS                     ADU_ADF_BASIS,
                       T.ADU_TOTAL_QTY                         ADU_TOTAL,
                       T.ADU_TOTAL_VALUE                       ADU_TOTAL_VALUE,
                       T.ADF_QTY                               ADF,
                       T.ADF_VALUE                             ADF_VALUE,
                       T.OPEN_SO_ALL_QTY_TOTAL                 OPEN_SO_ALL_QTY_HS,
                       T.OPEN_SO_VALUE_HS                      OPEN_SO_ALL_VAL_HS,
                       T.OPEN_SO_ALL_QTY                       OPEN_SO_ALL_QTY,
                       T.OPEN_SO_ALL_VAL                       OPEN_SO_ALL_VAL,
                       T.OPEN_PR_QTY_HS                        OPEN_PR_QTY_HS,
                       T.OPEN_PR_VAL_HS                        OPEN_PR_VAL_HS,
                       T.FIXED_LOT_SIZE_QTY                    FIXED_LOT_SIZE,
                       T.MOQ_THEO                              JNI_LOT_SIZE,
                       T.STOCKING_POLICY                       STOCKING_POLICY,
                       T.MANUAL_STOCKING_POLICY                MTO_MANUAL_RULE,
                       T.DAYS_SINCE_LAST_CONSUMPTION           DATE_SALES,
                       T.BLOCKED_QTY                           BLOCKED_QTY,
                       T.BLOCKED_VALUE                         BLOCKED_VAL,
                       T.SYSTEM                                COD_SYSTEM,
                       TO_CHAR(SYSDATE, 'YYYY/MM/DD')          DATE_ACTION_RCA,
                       #{session.userid,jdbcType=VARCHAR}      COD_ACTION_LEADER,
                       ''                                      SHORT_NAME_LEADER,
                       #{session.userid,jdbcType=VARCHAR}      COD_ACTION_OWNER,
                       ''                                      SHORT_NAME_OWNER,
                       ''                                      STAKE,
                       T.BU                                    BU,
                       T.CLUSTER_NAME                          CLUSTER_NAME,
                       T.PLANT_TYPE                            PLANT_TYPE,
                       T.ENTITY                                ENTITY,
                       T.PRODUCT_LINE                          COD_PM0,
                       T.MRP_CONTROLLER                        ID_SQ_MRP_CONTROLLER,
                       T.NEW_PRODUCTS                          NEW_PRODUCTS,
                       T.VENDOR_CODE                           FLG_FIXED_SUPPLIER,
                       T.EXISTING_IN_BOM,
                       T.COMMODITY_CODE,
                       T.PRODUCT_LINE_INV,
                       T.LOCATION_CATEGORY,
                       T.REPL_STRATEGY,
                       T.MATERIAL_CATEGORY,
                       T.PRODUCT_FAMILY_INV,
                       T.MATERIAL_OWNER_NAME,
                       T.MATERIAL_OWNER_SESA,
                       T.PLANT_CODE,
                       T.PRODUCT_LINE,
                       T.LOCAL_PRODUCT_FAMILY,
                       T.LOCAL_PRODUCT_SUBFAMILY,
                       T.LOCAL_PRODUCT_LINE,
                       T.MRP_CONTROLLER,
                       T.VENDOR_NAME,
                       T.MATERIAL_TYPE,
                       T.SPECIAL_PROC_CODE,
                       T.ACTIVENESS,
                       T.PROCUREMENT_TYPE,
                       T.SOURCE_CATEGORY,
                       T.CALCULATED_FMR,
                       T.PRODUCTION_LINE,
                       T.PROPOSED_FIN_PROV_QTY,
                       T.PROPOSED_FIN_PROV_VALUE,
                       T.PROPOSED_PROVISION_RULE,
                       T.NETTED_STOCK_QTY,
                       T.NETTED_STOCK_VALUE,
                       T.M1_NOC_QTY,
                       T.M1_NOC_VAL
                from ${SCPA.UHS_RCA_V} T
                         LEFT JOIN UHS_RCA_EXCESS_RESULT_V EXCESS ON T.MATERIAL = EXCESS.MATERIAL AND T.PLANT_CODE = EXCESS.PLANT_CODE
                         LEFT JOIN UHS_RCA_MISSING_RESULT_V MISSING ON T.MATERIAL = MISSING.MATERIAL AND T.PLANT_CODE = MISSING.PLANT_CODE
                         LEFT JOIN TOTAL_TEMP TOTAL ON 1 = 1
                         LEFT JOIN UHS_RCA_RESULT RESULT ON RESULT.VERSION = #{week, jdbcType=VARCHAR} AND RESULT.MATERIAL = T.MATERIAL AND RESULT.PLANT_CODE = T.PLANT_CODE
                         LEFT JOIN UHS_CODE CODE ON RESULT.FINAL_RCA = CODE.CODE AND CODE.CATEGORY = 'RCA_ACTION'
                where 1 = 1
                <include refid="filter"/>
            ) MM ) TT
        <choose>
            <when test="topNType == 'PERCENT'.toString()">
              where TT.GROSS_UHS_VALUE_OF_ALL &lt; #{topN, jdbcType=DOUBLE}
            </when>
            <otherwise>
              fetch next #{topN, jdbcType=INTEGER} rows only
            </otherwise>
        </choose>
    </sql>

    <select id="queryReport3Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport3SQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport3" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport3SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="downloadReport3RecommendRCA" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        with TOTAL_TEMP AS (SELECT SUM(${SUM_COLUMN}) CNT FROM ${SCPA.UHS_RCA_HIST} T WHERE WEEK = #{week, jdbcType=VARCHAR} <include refid="filter"/>)
        SELECT * FROM (
            SELECT DECODE(TOTAL, 0, 0, ROUND(SUM_OVER / TOTAL * 100,2)) GROSS_UHS_VALUE_OF_ALL,
                   COD_PLANT,
                   COD_MATERIAL_COM,
                   RCA_RECOMMEND,
                   HS_XSMIS_RCA,
                   FINAL_RCA,
                   COD_ACTION_RCA,
                   FINAL_ACTION,
                   HS_XSMIS_ACT,
                   DATE_INI_BTN,
                   DATE_FIN_BTN,
                   COD_STATUS_RCA,
                   COD_FREQ_RCA,
                   STOCK_VS_HS,
                   GROSS_UNHEALTHY_VALUE,
                   JNI_STOCK_VALUE,
                   HS_EXCESS_VALUE,
                   THEO_PROV_VALUE,
                   HS_MISSING_VALUE,
                   DECLARED_SPECIAL_EVENTS,
                   JNI_SS_VALUE,
                   SS,
                   SS_VALUE,
                   STOCK,
                   STOCK_VALUE,
                   ADU_ADF_BASIS,
                   ADU_TOTAL,
                   ADU_TOTAL_VALUE,
                   ADF,
                   ADF_VALUE,
                   PROPOSED_FIN_PROV_QTY,
                   PROPOSED_FIN_PROV_VALUE,
                   PROPOSED_PROVISION_RULE,
                   NETTED_STOCK_QTY,
                   NETTED_STOCK_VALUE,
                   M1_NOC_QTY,
                   M1_NOC_VAL,
                   OPEN_SO_ALL_QTY_HS,
                   OPEN_SO_ALL_VAL_HS,
                   OPEN_SO_ALL_QTY,
                   OPEN_SO_ALL_VAL,
                   OPEN_PR_QTY_HS,
                   OPEN_PR_VAL_HS,
                   FIXED_LOT_SIZE,
                   JNI_LOT_SIZE,
                   STOCKING_POLICY,
                   MTO_MANUAL_RULE,
                   DATE_SALES,
                   BLOCKED_QTY,
                   BLOCKED_VAL,
                   COD_SYSTEM,
                   DATE_ACTION_RCA,
                   COD_ACTION_LEADER,
                   SHORT_NAME_LEADER,
                   COD_ACTION_OWNER,
                   SHORT_NAME_OWNER,
                   STAKE,
                   BU,
                   CLUSTER_NAME,
                   PLANT_TYPE,
                   ENTITY,
                   COD_PM0,
                   ID_SQ_MRP_CONTROLLER,
                   NEW_PRODUCTS,
                   FLG_FIXED_SUPPLIER
            FROM (
                select TOTAL.CNT                               TOTAL,
                       SUM(${SUM_COLUMN}) OVER (ORDER BY TO_NUMBER(${SUM_COLUMN}) DESC) SUM_OVER,
                       T.PLANT_CODE                            COD_PLANT,
                       T.MATERIAL                              COD_MATERIAL_COM,
                       CASE
                        WHEN EXCESS.RCA_CODES IS NOT NULL AND MISSING.RCA_CODE IS NULL THEN EXCESS.RCA_CODES
                        WHEN EXCESS.RCA_CODES IS NOT NULL AND MISSING.RCA_CODE IS NOT NULL THEN EXCESS.RCA_CODES || ',' || MISSING.RCA_CODE
                        WHEN EXCESS.RCA_CODES IS NULL AND MISSING.RCA_CODE IS NOT NULL THEN MISSING.RCA_CODE
                       END RCA_RECOMMEND,
                       T.HS_RCA                                HS_XSMIS_RCA,
                       RESULT.FINAL_RCA,
                       RESULT.FINAL_ACTION,
                       RESULT.HS_XSMIS_ACT,
                       TO_CHAR(RESULT.DATE_INI_BTN, 'yyyy/mm/dd') DATE_INI_BTN,
                       TO_CHAR(RESULT.DATE_FIN_BTN, 'yyyy/mm/dd') DATE_FIN_BTN,
                       RESULT.COD_STATUS_RCA,
                       NVL(RESULT.COD_FREQ_RCA, T.HS_REVIEW_FREQUENCY) COD_FREQ_RCA,
                       T.HS_ACTION                             COD_ACTION_RCA,
                       T.HS_STATUS                             STOCK_VS_HS,
                       T.GROSS_UNHEALTHY_VALUE                 GROSS_UNHEALTHY_VALUE,
                       T.STOCK_VALUE_IN_LAST_HS_WEEKLY_REPORT  JNI_STOCK_VALUE,
                       T.HS_EXCESS_VALUE                       HS_EXCESS_VALUE,
                       T.HS_THEO_PROV_VALUE                    THEO_PROV_VALUE,
                       T.HS_MISSING_VALUE                      HS_MISSING_VALUE,
                       T.DECLARED_SPECIAL_EVENTS               DECLARED_SPECIAL_EVENTS,
                       T.SAFETY_STOCK_VALUE_HS                 JNI_SS_VALUE,
                       T.SS                                    SS,
                       T.SAFETY_STOCK_VALUE_ERP                SS_VALUE,
                       T.CURRENT_STOCK_QTY                     STOCK,
                       T.CURRENT_STOCK_VALUE                   STOCK_VALUE,
                       T.CALCULATION_BASIS                     ADU_ADF_BASIS,
                       T.ADU_TOTAL_QTY                         ADU_TOTAL,
                       T.ADU_TOTAL_VALUE                       ADU_TOTAL_VALUE,
                       T.ADF_QTY                               ADF,
                       T.ADF_VALUE                             ADF_VALUE,
                       T.OPEN_SO_ALL_QTY_TOTAL                 OPEN_SO_ALL_QTY_HS,
                       T.OPEN_SO_VALUE_HS                      OPEN_SO_ALL_VAL_HS,
                       T.OPEN_SO_ALL_QTY                       OPEN_SO_ALL_QTY,
                       T.OPEN_SO_ALL_VAL                       OPEN_SO_ALL_VAL,
                       T.OPEN_PR_QTY_HS                        OPEN_PR_QTY_HS,
                       T.OPEN_PR_VAL_HS                        OPEN_PR_VAL_HS,
                       T.FIXED_LOT_SIZE_QTY                    FIXED_LOT_SIZE,
                       T.MOQ_THEO                              JNI_LOT_SIZE,
                       T.STOCKING_POLICY                       STOCKING_POLICY,
                       T.MANUAL_STOCKING_POLICY                MTO_MANUAL_RULE,
                       T.DAYS_SINCE_LAST_CONSUMPTION           DATE_SALES,
                       T.BLOCKED_QTY                           BLOCKED_QTY,
                       T.BLOCKED_VALUE                         BLOCKED_VAL,
                       T.SYSTEM                                COD_SYSTEM,
                       TO_CHAR(SYSDATE, 'YYYY/MM/DD')          DATE_ACTION_RCA,
                       #{session.userid,jdbcType=VARCHAR}      COD_ACTION_LEADER,
                       ''                                      SHORT_NAME_LEADER,
                       #{session.userid,jdbcType=VARCHAR}      COD_ACTION_OWNER,
                       ''                                      SHORT_NAME_OWNER,
                       ''                                      STAKE,
                       T.BU                                    BU,
                       T.CLUSTER_NAME                          CLUSTER_NAME,
                       T.PLANT_TYPE                            PLANT_TYPE,
                       T.ENTITY                                ENTITY,
                       T.PRODUCT_LINE                          COD_PM0,
                       T.MRP_CONTROLLER                        ID_SQ_MRP_CONTROLLER,
                       T.NEW_PRODUCTS                          NEW_PRODUCTS,
                       T.VENDOR_CODE                           FLG_FIXED_SUPPLIER,
                       T.PROPOSED_FIN_PROV_QTY,
                       T.PROPOSED_FIN_PROV_VALUE,
                       T.PROPOSED_PROVISION_RULE,
                       T.NETTED_STOCK_QTY,
                       T.NETTED_STOCK_VALUE,
                       T.M1_NOC_QTY,
                       T.M1_NOC_VAL
                from ${SCPA.UHS_RCA_V} T
                         LEFT JOIN UHS_RCA_EXCESS_RESULT_V EXCESS ON T.MATERIAL = EXCESS.MATERIAL AND T.PLANT_CODE = EXCESS.PLANT_CODE
                         LEFT JOIN UHS_RCA_MISSING_RESULT_V MISSING ON T.MATERIAL = MISSING.MATERIAL AND T.PLANT_CODE = MISSING.PLANT_CODE
                         LEFT JOIN TOTAL_TEMP TOTAL ON 1 = 1
                         LEFT JOIN UHS_RCA_RESULT RESULT ON RESULT.VERSION = #{week, jdbcType=VARCHAR} AND RESULT.MATERIAL = T.MATERIAL AND RESULT.PLANT_CODE = T.PLANT_CODE
                where 1 = 1
                <include refid="filter"/>
            ) MM ) TT
        <choose>
            <when test="topNType == 'PERCENT'.toString()">
              where TT.GROSS_UHS_VALUE_OF_ALL &lt; #{topN, jdbcType=DOUBLE}
            </when>
            <otherwise>
              fetch next #{topN, jdbcType=INTEGER} rows only
            </otherwise>
        </choose>
    </select>

    <select id="queryReport3Exists" resultType="java.lang.Integer">
        select count(1) from UHS_RCA_RESULT where version = #{version, jdbcType=VARCHAR} and material = #{material, jdbcType=VARCHAR} and plant_code = #{plantCode, jdbcType=VARCHAR}
    </select>

    <insert id="saveReport3Data">
        insert into UHS_RCA_RESULT
        (version, MATERIAL, PLANT_CODE,
        <foreach collection="updates" item="col" separator=",">
            ${col.key}
        </foreach>, create_by$, create_date$
        )
        values
        (#{version,jdbcType=VARCHAR}, #{material,jdbcType=VARCHAR}, #{plantCode,jdbcType=VARCHAR},
        <foreach collection="updates" item="col" separator=",">
            <choose>
                <when test="col.key == 'DATE_FIN_BTN'.toString()">
                    to_date(#{col.value,jdbcType=VARCHAR}, 'yyyy/mm/dd')
                </when>
                <when test="col.key == 'DATE_INI_BTN'.toString()">
                    to_date(#{col.value,jdbcType=VARCHAR}, 'yyyy/mm/dd')
                </when>
                <otherwise>
                    #{col.value,jdbcType=VARCHAR}
                </otherwise>
            </choose>
        </foreach>,
         #{userid,jdbcType=VARCHAR}, sysdate
        )
    </insert>

    <update id="updateReport3Data">
        update UHS_RCA_RESULT
        SET
        <foreach collection="updates" item="col" separator=",">
            <choose>
                <when test="col.key == 'DATE_FIN_BTN'.toString()">
                    ${col.key} = to_date(#{col.value,jdbcType=VARCHAR}, 'yyyy/mm/dd')
                </when>
                <when test="col.key == 'DATE_INI_BTN'.toString()">
                    ${col.key} = to_date(#{col.value,jdbcType=VARCHAR}, 'yyyy/mm/dd')
                </when>
                <otherwise>
                    ${col.key} = #{col.value,jdbcType=VARCHAR}
                </otherwise>
            </choose>
        </foreach>,
        update_by$ = #{userid,jdbcType=VARCHAR},
        update_date$ = sysdate
        where version = #{version, jdbcType=VARCHAR}
          and material = #{material, jdbcType=VARCHAR}
          and plant_code = #{plantCode, jdbcType=VARCHAR}
    </update>

    <delete id="deleteEmptyReport3Result">
        delete UHS_RCA_RESULT where FINAL_RCA is null
    </delete>

    <update id="mergeReport3Data">
        begin
            merge into UHS_RCA_RESULT t
            using (
                <foreach collection="list" item="item" separator=" union all ">
                    select  #{item.VERSION, jdbcType=VARCHAR}                                VERSION,
                            #{item.MATERIAL, jdbcType=VARCHAR}                               MATERIAL,
                            #{item.PLANT_CODE, jdbcType=VARCHAR}                             PLANT_CODE,
                            #{item.FINAL_RCA, jdbcType=VARCHAR}                              FINAL_RCA,
                            #{item.ACTION_RECOMMEND, jdbcType=VARCHAR}                       ACTION_RECOMMEND,
                            #{item.COD_ACTION_RCA, jdbcType=VARCHAR}                         COD_ACTION_RCA,
                            #{item.FINAL_ACTION, jdbcType=VARCHAR}                           FINAL_ACTION,
                            #{item.HS_XSMIS_ACT, jdbcType=VARCHAR}                           HS_XSMIS_ACT,
                            #{item.HS_XSMIS_ACT_CSV, jdbcType=VARCHAR}                       HS_XSMIS_ACT_CSV,
                            to_date(#{item.DATE_INI_BTN, jdbcType=VARCHAR}, 'yyyy/mm/dd')    DATE_INI_BTN,
                            to_date(#{item.DATE_FIN_BTN, jdbcType=VARCHAR}, 'yyyy/mm/dd')    DATE_FIN_BTN,
                            #{item.COD_STATUS_RCA, jdbcType=VARCHAR}                         COD_STATUS_RCA,
                            #{item.COD_FREQ_RCA, jdbcType=VARCHAR}                           COD_FREQ_RCA,
                            #{item.CREATE_BY, jdbcType=VARCHAR}                              CREATE_BY
                    from dual
                </foreach>
            ) s on (t.VERSION = s.VERSION and t.MATERIAL = s.MATERIAL and t.PLANT_CODE = s.PLANT_CODE)
            when matched then
                update set
                t.FINAL_RCA = s.FINAL_RCA,
                t.ACTION_RECOMMEND = s.ACTION_RECOMMEND,
                t.COD_ACTION_RCA = s.COD_ACTION_RCA,
                t.FINAL_ACTION = s.FINAL_ACTION,
                t.HS_XSMIS_ACT = s.HS_XSMIS_ACT,
                t.HS_XSMIS_ACT_CSV = s.HS_XSMIS_ACT_CSV,
                t.DATE_INI_BTN = s.DATE_INI_BTN,
                t.DATE_FIN_BTN = s.DATE_FIN_BTN,
                t.COD_STATUS_RCA = s.COD_STATUS_RCA,
                t.COD_FREQ_RCA = s.COD_FREQ_RCA,
                t.UPDATE_BY$ = s.CREATE_BY,
                t.UPDATE_DATE$ = SYSDATE
            when not matched then
                insert (VERSION, MATERIAL, PLANT_CODE, FINAL_RCA, ACTION_RECOMMEND, COD_ACTION_RCA, FINAL_ACTION, HS_XSMIS_ACT, DATE_INI_BTN, DATE_FIN_BTN, COD_STATUS_RCA, CREATE_BY$, CREATE_DATE$, COD_FREQ_RCA)
                values
                    (s.VERSION, s.MATERIAL, s.PLANT_CODE, s.FINAL_RCA, s.ACTION_RECOMMEND, s.COD_ACTION_RCA, s.FINAL_ACTION, s.HS_XSMIS_ACT, s.DATE_INI_BTN, s.DATE_FIN_BTN, s.COD_STATUS_RCA, s.CREATE_BY, SYSDATE, s.COD_FREQ_RCA);
            commit;
        end;
    </update>

    <select id="downloadReport3OneMM" resultType="java.util.LinkedHashMap">
        select T.SYSTEM                                   COD_SYSTEM,
               T.PLANT_CODE                               COD_PLANT,
               T.MATERIAL                                 COD_MATERIAL,
               RESULT.FINAL_RCA                           HS_XSMIS_RCA,
               RESULT.FINAL_ACTION                        COD_ACTION_RCA,
               RESULT.HS_XSMIS_ACT_CSV                    HS_XSMIS_ACT,
               RESULT.COD_FREQ_RCA                        COD_FREQ_RCA,
               TO_CHAR(SYSDATE, 'DD/MM/YYYY')             DATE_ACTION_RCA,
               #{session.userid,jdbcType=VARCHAR}         COD_ACTION_LEADER,
               ''                                         SHORT_NAME_LEADER,
               #{session.userid,jdbcType=VARCHAR}         COD_ACTION_OWNER,
               ''                                         SHORT_NAME_OWNER,
               TO_CHAR(RESULT.DATE_INI_BTN, 'DD/MM/YYYY') DATE_INI_BTN,
               TO_CHAR(RESULT.DATE_FIN_BTN, 'DD/MM/YYYY') DATE_FIN_BTN,
               RESULT.COD_STATUS_RCA                      COD_STATUS_RCA,
               ''                                         STAKE
        from ${SCPA.UHS_RCA_V} T
                 INNER JOIN UHS_RCA_RESULT RESULT ON RESULT.VERSION = #{week, jdbcType=VARCHAR} AND RESULT.MATERIAL = T.MATERIAL AND RESULT.PLANT_CODE = T.PLANT_CODE
       where 1 = 1
       <include refid="filter"/>
    </select>
</mapper>
