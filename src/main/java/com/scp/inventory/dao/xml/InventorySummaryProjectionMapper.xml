<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.inventory.dao.InventorySummaryProjectionDao">
    <sql id="filter">
        <if test="filters != null and filters != ''.toString()">
            and ${filters}
        </if>
        <if test="specialList != null and specialList.size() > 0">
            <foreach collection="specialList" item="list" separator=" or " open=" and (" close=")">
                ${specialColumn} in
                <foreach collection="list" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </foreach>
        </if>
        <if test="stockIndicatorSQL != null and stockIndicatorSQL != ''.toString()">
            and ${stockIndicatorSQL}
        </if>
    </sql>

    <sql id="projectionFilter">
        AND VERSION = #{projectionVersion, jdbcType=VARCHAR}
        <if test="projectionType == 'My Projection'.toString()">
            AND T.OWNER = #{session.userid, jdbcType=VARCHAR}
        </if>
        <if test="projectionFilters != null and projectionFilters != ''.toString()">
            and ${projectionFilters}
        </if>
        <if test="projectionMaterialList != null and projectionMaterialList.size() > 0">
            <foreach collection="projectionMaterialList" item="list" separator=" or " open=" and (" close=")">
                MATERIAL in
                <foreach collection="list" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </foreach>
        </if>
    </sql>

    <sql id="projectionResultFilter">
        <if test="projectionResultFilters != null and projectionResultFilters != ''.toString()">
            and ${projectionResultFilters}
        </if>
        <if test="projectionResultSpecialList != null and projectionResultSpecialList.size() > 0">
            <foreach collection="projectionResultSpecialList" item="list" separator=" or " open=" and (" close=")">
                T2.${projectionResultSpecialColumn} IN
                <foreach collection="list" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </foreach>
        </if>
    </sql>

    <select id="queryCascader" resultType="java.util.Map">
        SELECT * FROM (
            SELECT CATEGORY, NAME, NAME AS "VALUE" FROM INVENTORY_STRUCTURE_FILTER_V T
            UNION ALL
            SELECT 'SPECIAL_ST',DESCRIPTION || '(' || SPECIAL_STOCK_IND || ')', SPECIAL_STOCK_IND FROM MR3_STOCK_INDICATOR
        ) TT
        ORDER BY CATEGORY,DECODE(NAME,'Others','zzz','No Owner','zzx',NAME)
    </select>

    <select id="queryProjectionCascader" resultType="java.util.Map">
        SELECT T.*
        FROM INVENTORY_STRUCTURE_FILTER_V T
        WHERE T.CATEGORY IN ('ENTITY', 'PLANT_CODE', 'PLANT_TYPE', 'PRODUCT_LINE_INV', 'PRODUCT_FAMILY_INV', 'SOURCE_CATEGORY', 'VENDOR_NAME')
        ORDER BY CATEGORY,DECODE(NAME,'Others','zzz',NAME)
    </select>

    <select id="queryMaterialOwnerCount" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM MATERIAL_MASTER_V T WHERE T.MATERIAL_OWNER_SESA = #{userid, jdbcType=VARCHAR}
    </select>

    <select id="queryInventoryRCAList" resultType="java.util.Map">
        SELECT RCA_CODE AS KEY, DESCRIPTION AS "VALUE" FROM MR3_INVENTORY_PROJECTION_RCA_CODE
    </select>

    <select id="queryProjectionVersionOpts" resultType="java.lang.String">
        select DISTINCT VERSION from MR3_INVENTORY_PROJECTION ORDER BY VERSION DESC
    </select>

    <select id="queryRcaCode" resultType="java.lang.String">
        SELECT RCA_CODE FROM MR3_INVENTORY_PROJECTION_RCA_CODE WHERE RCA_CODE NOT IN ('COGS', 'PROVISION') ORDER BY RCA_CODE
    </select>

    <select id="queryAvailableOpts" resultType="java.lang.String">
        SELECT DISTINCT T.${column}
         FROM INVENTORY_STRUCTURE_V T
        WHERE T.${column} IS NOT NULL
          AND T.MATERIAL_OWNER_SESA = #{userid, jdbcType=VARCHAR}
        ORDER BY DECODE(T.${column}, 'Others', 'zzz', T.${column})
    </select>

    <sql id="queryReport1SQL">
        WITH <include refid="mv.inventory_structure_hist_v"/>
        SELECT <foreach collection="categroy" item="item" separator=",">${item}</foreach>,
               COUNT(DISTINCT MATERIAL || '#' || PLANT_CODE)    NO_OF_MATERIAL,
               SUM(${sohValueColumn})                           SOH,
               SUM(${gitColumn})                                GIT,
               SUM(OPEN_PO${suffix})                            OPEN_PO,
               SUM(OPEN_SO${suffix})                            OPEN_SO,
               SUM(MO_RESERVATION${suffix})                     MO_RESERVATION,
               SUM(AMU_ONE_MM${suffix})                         AMU_ONE_MM,
               SUM(AMF_ONE_MM${suffix})                         AMF_ONE_MM,
               SUM(UD_LT_7_CD${suffix})                         UD_LT_7_CD,
               SUM(UD_LT_30_CD${suffix})                        UD_LT_30_CD,
               SUM(UD_GT_30_CD${suffix})                        UD_GT_30_CD,
               SUM(SAFETY_STOCK${suffix})                       SAFETY_STOCK,
               SUM(SS2${suffix})                                SS2,
               SUM(SS3${suffix})                                SS3,
               SUM(REORDER_POINT${suffix})                      REORDER_POINT,
               SUM(EXCESS_ONE_MM${suffix})                      EXCESS_ONE_MM,
               SUM(MISSING_ONE_MM${suffix})                     MISSING_ONE_MM,
               SUM(THEO_PROV_ONE_MM${suffix})                   THEO_PROV_ONE_MM,
               SUM(OPEN_AB${suffix})                            OPEN_AB,
               SUM(OPEN_LA${suffix})                            OPEN_LA,
               SUM(OPEN_NON_AB_LA${suffix})                     OPEN_NON_AB_LA,
               SUM(GR_LAST_WD${suffix})                         GR_LAST_WD,
               SUM(LA_LAST_WD${suffix})                         LA_LAST_WD,
               SUM(PO_CREATION_LAST_WD${suffix})                PO_CREATION_LAST_WD,
               SUM(MINIMUM_LOT_SIZE${suffix})                   MINIMUM_LOT_SIZE,
               AVG(UNIT_COST)                                   UNIT_COST
          FROM inventory_structure_hist_v T
        <where>
            AND DATE$ = TO_DATE(#{report1Date, jdbcType=VARCHAR}, 'yyyy/mm/dd')
            <include refid="filter"/>
        </where>
        GROUP BY
        <foreach collection="categroy" item="item" separator=",">${item}</foreach>
        ORDER BY
        <foreach collection="categroy" item="item" separator=",">${item}</foreach>
    </sql>

    <select id="queryReport1Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1SQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport1DetailsSQL">
        WITH <include refid="mv.inventory_structure_hist_v"/>
        SELECT *
          FROM inventory_structure_hist_v T
        <where>
            <include refid="filter"/>
            <foreach collection="categroy" index="index" item="item">
                <choose>
                    <when test="report1SelectedValues[index] == null or report1SelectedValues[index] == ''.toString">
                        AND ${item} IS NULL
                    </when>
                    <when test="report1SelectedValues[index] == 'Total'.toString"/>
                    <otherwise>
                        AND ${item} = #{report1SelectedValues[${index}], jdbcType=VARCHAR}
                    </otherwise>
                </choose>
            </foreach>
        </where>
    </sql>

    <select id="queryReport1DetailsCount" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1Details" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport2SQL">
        SELECT ROWIDTOCHAR(T.ROWID) ROW_ID,
               T.VERSION,
               T.ENTITY,
               T.PLANT_CODE,
               T.PLANT_TYPE,
               T.PRODUCT_LINE_INV,
               T.PRODUCT_FAMILY_INV,
               T.SOURCE_CATEGORY,
               T.RCA_CATEGORY,
               T.RCA_CODE,
               T.OWNER,
               T.STATUS,
               T.MONTH01,
               T.MONTH02,
               T.MONTH03,
               T.MONTH04,
               T.MONTH05,
               T.MONTH06,
               T.MONTH07,
               T.MONTH08,
               T.MONTH09,
               T.MONTH10,
               T.MONTH11,
               T.MONTH12,
               T.MONTH13,
               T.RULE_ID,
               T.SPLITED,
               T.VENDOR_NAME,
               T.MATERIAL,
               TO_CHAR(T.SPLITED_TIME, 'YYYY/MM/DD HH24:MI:SS') SPLITED_TIME,
               T2.USER_NAME || '[' || T2.SESA_CODE || ']' AS CREATE_BY
          FROM MR3_INVENTORY_PROJECTION T LEFT JOIN SY_USER_MASTER_DATA T2 ON T.CREATE_BY$ = T2.SESA_CODE
          <where>
              <include refid="projectionFilter"/>
          </where>
    </sql>

    <select id="queryReport2Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport2SQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport2" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport2SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="downloadReport2" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
            SELECT T.VERSION,
                   T.ENTITY,
                   T.PLANT_CODE,
                   T.PLANT_TYPE,
                   T.PRODUCT_LINE_INV,
                   T.PRODUCT_FAMILY_INV,
                   T.SOURCE_CATEGORY,
                   T.VENDOR_NAME,
                   T.RCA_CATEGORY,
                   T.RCA_CODE,
                   T.OWNER,
                   T.MONTH01,
                   T.MONTH02,
                   T.MONTH03,
                   T.MONTH04,
                   T.MONTH05,
                   T.MONTH06,
                   T.MONTH07,
                   T.MONTH08,
                   T.MONTH09,
                   T.MONTH10,
                   T.MONTH11,
                   T.MONTH12,
                   T.MONTH13,
                   T.STATUS,
                   UMD.USER_NAME || '[' || UMD.USER_NAME || ']' AS CREATE_BY
            FROM MR3_INVENTORY_PROJECTION T LEFT JOIN SY_USER_MASTER_DATA UMD on T.CREATE_BY$ = UMD.SESA_CODE
            <where>
              <include refid="projectionFilter"/>
            </where>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryPageAdmin" resultType="java.lang.Integer">
        SELECT COUNT(1) CNT
          FROM SY_MENU_AUTH T
         WHERE UPPER(T.AUTH_DETAILS) = 'ADMIN'
           AND T.USER_ID = #{userid, jdbcType=VARCHAR}
           AND T.MENU_CODE = #{parentCode, jdbcType=VARCHAR}
    </select>

    <insert id="createReport2ByTable">
        INSERT INTO MR3_INVENTORY_PROJECTION
        (VERSION,
        <foreach collection="headers" item="header" separator=",">
            ${header}
        </foreach>, CREATE_BY$, CREATE_DATE$, STATUS, RULE_ID, SPLITED, OWNER
        )
        <foreach collection="creates" item="list" separator=" union all ">
            SELECT #{version, jdbcType=VARCHAR},
            <foreach collection="headers" item="header" separator=",">
                #{list.${header}, jdbcType=VARCHAR}
            </foreach>, #{userid,jdbcType=VARCHAR}, SYSDATE, 'ManualInput', LOWER(SUBSTR(SYS_GUID(), 0, 12)), 'N', #{userid,jdbcType=VARCHAR}
            FROM DUAL
        </foreach>
    </insert>

    <delete id="deleteReport2ByTable">
        DELETE FROM MR3_INVENTORY_PROJECTION WHERE ROWID IN
        <foreach collection="deletes" open="(" close=")" separator="," item="item">#{item, jdbcType=VARCHAR}</foreach>
        AND VERSION = #{version, jdbcType=VARCHAR}
        AND OWNER = #{userid, jdbcType=VARCHAR}
    </delete>

    <update id="updateReport2ByTable">
        UPDATE MR3_INVENTORY_PROJECTION
        SET
        <foreach collection="updates" item="col" separator=",">
            ${col.key} = #{col.value,jdbcType=VARCHAR}
        </foreach>,
        UPDATE_BY$ = #{userid,jdbcType=VARCHAR},
        UPDATE_DATE$ = SYSDATE,
        STATUS = 'ManualInput',
        SPLITED = 'N',
        SPLITED_TIME = NULL
        WHERE ROWID = #{rowid,jdbcType=VARCHAR}
              AND VERSION = #{version, jdbcType=VARCHAR}
              AND OWNER = #{userid, jdbcType=VARCHAR}
    </update>

    <select id="queryProjectionUnSplitList" resultType="java.util.Map">
        SELECT * FROM MR3_INVENTORY_PROJECTION T WHERE T.SPLITED = 'N'
    </select>

    <update id="updateProjectionStatus">
        UPDATE MR3_INVENTORY_PROJECTION T
          SET T.SPLITED = 'Y',
              T.SPLITED_TIME = SYSDATE
        WHERE T.RULE_ID = #{RULE_ID, jdbcType=VARCHAR}
    </update>

    <delete id="deleteUnusedProjection">
        DELETE FROM MR3_INVENTORY_PROJECTION_DETAILS T WHERE T.RULE_ID NOT IN
        (SELECT T0.RULE_ID FROM MR3_INVENTORY_PROJECTION T0)
    </delete>

    <select id="queryTotalByConditions" resultType="java.math.BigDecimal">
        WITH <include refid="mv.inventory_structure_hist_v"/>
        SELECT SUM(T.BLOCKED_STOCK_VALUE + T.RESTRICTED_STOCK_VALUE + T.RETURNS_STOCK_VALUE + T.STOCK_IN_QI_VALUE + T.UU_STOCK_VALUE)
          FROM inventory_structure_hist_v T
         WHERE T.MATERIAL_OWNER_SESA = #{OWNER, jdbcType=VARCHAR}
           <if test="ENTITY != null and ENTITY != ''.toString()">
               AND T.ENTITY = #{ENTITY, jdbcType=VARCHAR}
           </if>
           <if test="PLANT_CODE != null and PLANT_CODE != ''.toString()">
               AND T.PLANT_CODE = #{PLANT_CODE, jdbcType=VARCHAR}
           </if>
           <if test="PLANT_TYPE != null and PLANT_TYPE != ''.toString()">
               AND T.PLANT_TYPE = #{PLANT_TYPE, jdbcType=VARCHAR}
           </if>
           <if test="PRODUCT_LINE_INV != null and PRODUCT_LINE_INV != ''.toString()">
               AND T.PRODUCT_LINE_INV = #{PRODUCT_LINE_INV, jdbcType=VARCHAR}
           </if>
           <if test="PRODUCT_FAMILY_INV != null and PRODUCT_FAMILY_INV != ''.toString()">
               AND T.PRODUCT_FAMILY_INV = #{PRODUCT_FAMILY_INV, jdbcType=VARCHAR}
           </if>
           <if test="SOURCE_CATEGORY != null and SOURCE_CATEGORY != ''.toString()">
               AND T.SOURCE_CATEGORY = #{SOURCE_CATEGORY, jdbcType=VARCHAR}
           </if>
           <if test="VENDOR_NAME != null and VENDOR_NAME != ''.toString()">
               AND T.VENDOR_NAME = #{VENDOR_NAME, jdbcType=VARCHAR}
           </if>
           <if test="MATERIAL != null and MATERIAL != ''.toString()">
               AND T.MATERIAL = #{MATERIAL, jdbcType=VARCHAR}
           </if>
           AND (T.SPECIAL_ST != 'Others' or SPECIAL_ST IS NULL)
           AND T.DATE$ = TO_DATE(#{VERSION, jdbcType=VARCHAR} || '01', 'YYYYMMDD')
    </select>

    <insert id="splitInventoryProjection">
        BEGIN
            DELETE FROM MR3_INVENTORY_PROJECTION_DETAILS T WHERE T.RULE_ID = #{RULE_ID, jdbcType=VARCHAR};

            INSERT INTO MR3_INVENTORY_PROJECTION_DETAILS
            (VERSION, RULE_ID, MATERIAL, PLANT_CODE, MONTH00,
            MONTH01, MONTH02, MONTH03, MONTH04, MONTH05,
            MONTH06, MONTH07, MONTH08, MONTH09, MONTH10,
            MONTH11, MONTH12, MONTH13, CREATE_DATE$)
            WITH <include refid="mv.inventory_structure_hist_v"/>
            SELECT #{VERSION, jdbcType=VARCHAR},
                   #{RULE_ID, jdbcType=VARCHAR},
                   T.MATERIAL,
                   T.PLANT_CODE,
                   (T.BLOCKED_STOCK_VALUE + T.RESTRICTED_STOCK_VALUE + T.RETURNS_STOCK_VALUE + T.STOCK_IN_QI_VALUE + T.UU_STOCK_VALUE),
                   (T.BLOCKED_STOCK_VALUE + T.RESTRICTED_STOCK_VALUE + T.RETURNS_STOCK_VALUE + T.STOCK_IN_QI_VALUE + T.UU_STOCK_VALUE) * #{MONTH01_DISCOUNT, jdbcType=NUMERIC},
                   (T.BLOCKED_STOCK_VALUE + T.RESTRICTED_STOCK_VALUE + T.RETURNS_STOCK_VALUE + T.STOCK_IN_QI_VALUE + T.UU_STOCK_VALUE) * #{MONTH02_DISCOUNT, jdbcType=NUMERIC},
                   (T.BLOCKED_STOCK_VALUE + T.RESTRICTED_STOCK_VALUE + T.RETURNS_STOCK_VALUE + T.STOCK_IN_QI_VALUE + T.UU_STOCK_VALUE) * #{MONTH03_DISCOUNT, jdbcType=NUMERIC},
                   (T.BLOCKED_STOCK_VALUE + T.RESTRICTED_STOCK_VALUE + T.RETURNS_STOCK_VALUE + T.STOCK_IN_QI_VALUE + T.UU_STOCK_VALUE) * #{MONTH04_DISCOUNT, jdbcType=NUMERIC},
                   (T.BLOCKED_STOCK_VALUE + T.RESTRICTED_STOCK_VALUE + T.RETURNS_STOCK_VALUE + T.STOCK_IN_QI_VALUE + T.UU_STOCK_VALUE) * #{MONTH05_DISCOUNT, jdbcType=NUMERIC},
                   (T.BLOCKED_STOCK_VALUE + T.RESTRICTED_STOCK_VALUE + T.RETURNS_STOCK_VALUE + T.STOCK_IN_QI_VALUE + T.UU_STOCK_VALUE) * #{MONTH06_DISCOUNT, jdbcType=NUMERIC},
                   (T.BLOCKED_STOCK_VALUE + T.RESTRICTED_STOCK_VALUE + T.RETURNS_STOCK_VALUE + T.STOCK_IN_QI_VALUE + T.UU_STOCK_VALUE) * #{MONTH07_DISCOUNT, jdbcType=NUMERIC},
                   (T.BLOCKED_STOCK_VALUE + T.RESTRICTED_STOCK_VALUE + T.RETURNS_STOCK_VALUE + T.STOCK_IN_QI_VALUE + T.UU_STOCK_VALUE) * #{MONTH08_DISCOUNT, jdbcType=NUMERIC},
                   (T.BLOCKED_STOCK_VALUE + T.RESTRICTED_STOCK_VALUE + T.RETURNS_STOCK_VALUE + T.STOCK_IN_QI_VALUE + T.UU_STOCK_VALUE) * #{MONTH09_DISCOUNT, jdbcType=NUMERIC},
                   (T.BLOCKED_STOCK_VALUE + T.RESTRICTED_STOCK_VALUE + T.RETURNS_STOCK_VALUE + T.STOCK_IN_QI_VALUE + T.UU_STOCK_VALUE) * #{MONTH10_DISCOUNT, jdbcType=NUMERIC},
                   (T.BLOCKED_STOCK_VALUE + T.RESTRICTED_STOCK_VALUE + T.RETURNS_STOCK_VALUE + T.STOCK_IN_QI_VALUE + T.UU_STOCK_VALUE) * #{MONTH11_DISCOUNT, jdbcType=NUMERIC},
                   (T.BLOCKED_STOCK_VALUE + T.RESTRICTED_STOCK_VALUE + T.RETURNS_STOCK_VALUE + T.STOCK_IN_QI_VALUE + T.UU_STOCK_VALUE) * #{MONTH12_DISCOUNT, jdbcType=NUMERIC},
                   (T.BLOCKED_STOCK_VALUE + T.RESTRICTED_STOCK_VALUE + T.RETURNS_STOCK_VALUE + T.STOCK_IN_QI_VALUE + T.UU_STOCK_VALUE) * #{MONTH13_DISCOUNT, jdbcType=NUMERIC},
                   SYSDATE
              FROM inventory_structure_hist_v T
             WHERE T.MATERIAL_OWNER_SESA = #{OWNER, jdbcType=VARCHAR}
               <if test="ENTITY != null and ENTITY != ''.toString()">
                   AND T.ENTITY = #{ENTITY, jdbcType=VARCHAR}
               </if>
               <if test="PLANT_CODE != null and PLANT_CODE != ''.toString()">
                   AND T.PLANT_CODE = #{PLANT_CODE, jdbcType=VARCHAR}
               </if>
               <if test="PLANT_TYPE != null and PLANT_TYPE != ''.toString()">
                   AND T.PLANT_TYPE = #{PLANT_TYPE, jdbcType=VARCHAR}
               </if>
               <if test="PRODUCT_LINE_INV != null and PRODUCT_LINE_INV != ''.toString()">
                   AND T.PRODUCT_LINE_INV = #{PRODUCT_LINE_INV, jdbcType=VARCHAR}
               </if>
               <if test="PRODUCT_FAMILY_INV != null and PRODUCT_FAMILY_INV != ''.toString()">
                   AND T.PRODUCT_FAMILY_INV = #{PRODUCT_FAMILY_INV, jdbcType=VARCHAR}
               </if>
               <if test="SOURCE_CATEGORY != null and SOURCE_CATEGORY != ''.toString()">
                   AND T.SOURCE_CATEGORY = #{SOURCE_CATEGORY, jdbcType=VARCHAR}
               </if>
               <if test="VENDOR_NAME != null and VENDOR_NAME != ''.toString()">
                   AND T.VENDOR_NAME = #{VENDOR_NAME, jdbcType=VARCHAR}
               </if>
               <if test="MATERIAL != null and MATERIAL != ''.toString()">
                   AND T.MATERIAL = #{MATERIAL, jdbcType=VARCHAR}
               </if>
               AND (T.SPECIAL_ST != 'Others' or SPECIAL_ST IS NULL)
               AND T.DATE$ = TO_DATE(#{VERSION, jdbcType=VARCHAR} || '01', 'YYYYMMDD');
        END;
    </insert>

    <sql id="report2DetailsSQL">
        SELECT T.VERSION, T.MATERIAL, T.PLANT_CODE, MMV.ENTITY, MMV.PLANT_TYPE,
               MMV.PRODUCT_LINE_INV, MMV.PRODUCT_FAMILY_INV, MMV.SOURCE_CATEGORY, MMV.VENDOR_NAME,
               T.MONTH01, T.MONTH02, T.MONTH03, T.MONTH04, T.MONTH05, T.MONTH06,
               T.MONTH07, T.MONTH08, T.MONTH09, T.MONTH10, T.MONTH11, T.MONTH12, T.MONTH13
          FROM MR3_INVENTORY_PROJECTION_DETAILS T INNER JOIN MR3_INVENTORY_PROJECTION T2 ON T.RULE_ID = T2.RULE_ID
                                              INNER JOIN MATERIAL_MASTER_V MMV on T.MATERIAL = MMV.MATERIAL AND T.PLANT_CODE = MMV.PLANT_CODE
         WHERE T2.ROWID = #{report2SelectedID, jdbcType=VARCHAR}
    </sql>

    <select id="queryReport2DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="report2DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport2Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report2DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <insert id="insertTempMaterial">
        INSERT INTO MR3_INVENTORY_PROJECTION_MATERIAL_TEMPORARY (MATERIAL)
        <foreach collection="list" separator=" union all" item="item">
            SELECT #{item, jdbcType=VARCHAR} FROM DUAL
        </foreach>
    </insert>

    <select id="queryInvalidMaterialList" resultType="java.lang.String">
        SELECT MATERIAL FROM MR3_INVENTORY_PROJECTION_MATERIAL_TEMPORARY T
         WHERE NOT EXISTS
        (SELECT 1 FROM MATERIAL_MASTER_V T2 WHERE T.MATERIAL  = T2.MATERIAL AND T2.MATERIAL_OWNER_SESA = #{userid, jdbcType=VARCHAR})
    </select>

    <sql id="queryReport3SQL">
        WITH inventory_structure_hist_v AS (
            SELECT /*+ parallel(t 6) */
                TO_CHAR(T.DATE$, 'YYYYMM')  DATE_MONTH,
                NVL(T.BLOCKED_STOCK_VALUE, 0) + NVL(T.RESTRICTED_STOCK_VALUE, 0) + NVL(T.RETURNS_STOCK_VALUE, 0) + NVL(T.STOCK_IN_QI_VALUE, 0) +
                NVL(T.UU_STOCK_VALUE, 0) AS VAL,
                <foreach collection="projectionResultCategroy" item="item" separator=",">
                    <choose>
                        <when test="item == 'MATERIAL_OWNER_SESA'.toString() or item == 'MATERIAL_OWNER_NAME'.toString()">
                            T2.${item}
                        </when>
                        <otherwise>
                            T.${item}
                        </otherwise>
                    </choose>
                </foreach>
            FROM INVENTORY_STRUCTURE_HIST T
                     INNER JOIN MATERIAL_MASTER_V T2
                                ON T.MATERIAL = T2.MATERIAL AND T.PLANT_CODE = T2.PLANT_CODE
            WHERE DATE$ IN
            <foreach collection="versions" item="version" separator="," open="(" close=")">
                TO_DATE(#{version, jdbcType=VARCHAR}, 'YYYYMM')
            </foreach>
            <include refid="projectionResultFilter"/>
        ),
             TEMP AS (
                 SELECT DATE_MONTH,
                        <foreach collection="projectionResultCategroy" item="item" separator="," close=",">${item}</foreach>
                        SUM(VAL) AS VAL
                 FROM inventory_structure_hist_v T
                 GROUP BY DATE_MONTH,
                 <foreach collection="projectionResultCategroy" item="item" separator=",">${item}</foreach>
             ), TEMP2 AS (
                 SELECT *
                 FROM TEMP PIVOT (
                    SUM(VAL) VAL
                    FOR DATE_MONTH IN
                    <foreach collection="versions" separator="," item="version" open="(" close=")">
                        '${version}'
                    </foreach>
                 )
             ),
             TEMP3 AS (
                SELECT TEMP2.*, T2.CNT AS MATERIAL_CNT
                 FROM TEMP2 LEFT JOIN (
                     SELECT COUNT(1) CNT,
                            <foreach collection="projectionResultCategroy" item="item" separator=",">${item}</foreach>
                     FROM MATERIAL_MASTER_V MMV
                     GROUP BY
                     <foreach collection="projectionResultCategroy" item="item" separator=",">${item}</foreach>
                 ) T2 ON
                     <foreach collection="projectionResultCategroy" item="item" separator=" AND ">
                         TEMP2.${item} = T2.${item}
                     </foreach>),
             PROJECTION_GAP AS (
                 SELECT <foreach collection="projectionResultCategroy" item="item">
                            MMV.${item},
                        </foreach>
                        SUM(T.MONTH00 - T.MONTH01) GAP01,
                        SUM(T.MONTH00 - T.MONTH02) GAP02,
                        SUM(T.MONTH00 - T.MONTH03) GAP03,
                        SUM(T.MONTH00 - T.MONTH04) GAP04,
                        SUM(T.MONTH00 - T.MONTH05) GAP05,
                        SUM(T.MONTH00 - T.MONTH06) GAP06,
                        SUM(T.MONTH00 - T.MONTH07) GAP07,
                        SUM(T.MONTH00 - T.MONTH08) GAP08,
                        SUM(T.MONTH00 - T.MONTH09) GAP09,
                        SUM(T.MONTH00 - T.MONTH10) GAP10,
                        SUM(T.MONTH00 - T.MONTH11) GAP11,
                        SUM(T.MONTH00 - T.MONTH12) GAP12,
                        SUM(T.MONTH00 - T.MONTH13) GAP13
                 FROM MR3_INVENTORY_PROJECTION_DETAILS T
                          INNER JOIN MATERIAL_MASTER_V MMV on T.MATERIAL = MMV.MATERIAL AND T.PLANT_CODE = MMV.PLANT_CODE
                 GROUP BY
                 <foreach collection="projectionResultCategroy" item="item" separator=",">
                     MMV.${item}
                 </foreach>
             )
        SELECT <foreach collection="projectionResultCategroy" item="item">
                   TEMP3.${item},
               </foreach>
               TEMP3.MATERIAL_CNT AS NO_OF_MATERIAL,
               <foreach collection="versions" item="version" index="index">
                    <choose>
                        <when test="index &lt; 9">
                            TEMP3."'${version}'_VAL" AS ACTUAL0${index + 1},
                        </when>
                        <otherwise>
                            TEMP3."'${version}'_VAL" AS ACTUAL${index + 1},
                        </otherwise>
                    </choose>
               </foreach>
               TEMP3."'${projectionResultVersion}'_VAL" - NVL(PROJECTION_GAP.GAP01, 0) AS PROJECTION01,
               TEMP3."'${projectionResultVersion}'_VAL" - NVL(PROJECTION_GAP.GAP02, 0) AS PROJECTION02,
               TEMP3."'${projectionResultVersion}'_VAL" - NVL(PROJECTION_GAP.GAP03, 0) AS PROJECTION03,
               TEMP3."'${projectionResultVersion}'_VAL" - NVL(PROJECTION_GAP.GAP04, 0) AS PROJECTION04,
               TEMP3."'${projectionResultVersion}'_VAL" - NVL(PROJECTION_GAP.GAP05, 0) AS PROJECTION05,
               TEMP3."'${projectionResultVersion}'_VAL" - NVL(PROJECTION_GAP.GAP06, 0) AS PROJECTION06,
               TEMP3."'${projectionResultVersion}'_VAL" - NVL(PROJECTION_GAP.GAP07, 0) AS PROJECTION07,
               TEMP3."'${projectionResultVersion}'_VAL" - NVL(PROJECTION_GAP.GAP08, 0) AS PROJECTION08,
               TEMP3."'${projectionResultVersion}'_VAL" - NVL(PROJECTION_GAP.GAP09, 0) AS PROJECTION09,
               TEMP3."'${projectionResultVersion}'_VAL" - NVL(PROJECTION_GAP.GAP10, 0) AS PROJECTION10,
               TEMP3."'${projectionResultVersion}'_VAL" - NVL(PROJECTION_GAP.GAP11, 0) AS PROJECTION11,
               TEMP3."'${projectionResultVersion}'_VAL" - NVL(PROJECTION_GAP.GAP12, 0) AS PROJECTION12,
               TEMP3."'${projectionResultVersion}'_VAL" - NVL(PROJECTION_GAP.GAP13, 0) AS PROJECTION13
        FROM TEMP3 LEFT JOIN PROJECTION_GAP ON
             <foreach collection="projectionResultCategroy" item="item" separator=" AND ">
                 TEMP3.${item} = PROJECTION_GAP.${item}
             </foreach>
        ORDER BY <foreach collection="projectionResultCategroy" item="item" separator=",">TEMP3.${item}</foreach>
    </sql>

    <select id="queryReport3Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport3SQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport3" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport3SQL"/>
        <include refid="global.select_footer"/>
    </select>
</mapper>
