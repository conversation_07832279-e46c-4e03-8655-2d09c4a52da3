<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.inventory.dao.IInventoryStructureDao">
    <sql id="structure_filter">
        <if test="_filters != null and _filters != ''.toString()">
            and ${_filters}
        </if>
        <if test="stockIndicatorSQL != null and stockIndicatorSQL != ''.toString()">
            and ${stockIndicatorSQL}
        </if>
        <if test="treePathFilter != null and treePathFilter != ''.toString()">
            and ${treePathFilter}
        </if>
    </sql>

    <select id="queryCascader" resultType="java.util.Map">
        SELECT * FROM (
            SELECT CATEGORY, NAME, NAME AS "VALUE" FROM INVENTORY_STRUCTURE_FILTER_V T
            UNION ALL
            SELECT 'SPECIAL_ST',DESCRIPTION || '(' || SPECIAL_STOCK_IND || ')', SPECIAL_STOCK_IND FROM MR3_STOCK_INDICATOR
        ) TT
        ORDER BY CATEGORY,DECODE(NAME,'Others','zzz','No Owner','zzx',NAME)
    </select>

    <select id="queryAvailableEntity" resultType="java.lang.String">
        SELECT NAME FROM SCPA.INVENTORY_STRUCTURE_FILTER_V T WHERE CATEGORY = 'ENTITY' ORDER BY NAME
    </select>

    <select id="queryAvailablePlantCode" resultType="java.lang.String">
        SELECT NAME FROM INVENTORY_STRUCTURE_FILTER_V T WHERE CATEGORY = 'PLANT_CODE' ORDER BY NAME
    </select>

    <select id="queryProjectionVersionOpts" resultType="java.lang.String">
        SELECT DISTINCT T.VERSION FROM MR3_INVENTORY_PROJECTION T ORDER BY T.VERSION DESC
    </select>

    <resultMap id="report1ResultMap" type="com.scp.inventory.bean.Report1DataBean">
        <result property="category1" column="category1"/>
        <result property="category2" column="category2"/>
        <result property="category3" column="category3"/>
        <result property="category4" column="category4"/>
        <result property="category5" column="category5"/>
        <result property="value" column="value"/>
        <association property="tooltips" javaType="com.scp.inventory.bean.Report1Tooltips">
            <result property="AVAILABLE_STOCK" column="AVAILABLE_STOCK"/>
            <result property="AMF_ONE_MM" column="AMF_ONE_MM"/>
            <result property="AMU_ONE_MM" column="AMU_ONE_MM"/>
            <result property="BLOCKED_STOCK" column="BLOCKED_STOCK"/>
            <result property="EXCESS_ONE_MM" column="EXCESS_ONE_MM"/>
            <result property="GIT_QTY" column="GIT_QTY"/>
            <result property="GIT_VALUE" column="GIT_VALUE"/>
            <result property="GR_LAST_WD" column="GR_LAST_WD"/>
            <result property="INTER_STK_TRANSFER" column="INTER_STK_TRANSFER"/>
            <result property="LA_LAST_WD" column="LA_LAST_WD"/>
            <result property="MINIMUM_LOT_SIZE" column="MINIMUM_LOT_SIZE"/>
            <result property="MISSING_ONE_MM" column="MISSING_ONE_MM"/>
            <result property="MO_RESERVATION" column="MO_RESERVATION"/>
            <result property="OPEN_PO" column="OPEN_PO"/>
            <result property="OPEN_SO" column="OPEN_SO"/>
            <result property="OPEN_AB" column="OPEN_AB"/>
            <result property="OPEN_LA" column="OPEN_LA"/>
            <result property="OPEN_NON_AB_LA" column="OPEN_NON_AB_LA"/>
            <result property="PO_CREATION_LAST_WD" column="PO_CREATION_LAST_WD"/>
            <result property="PROPOSED_FIN_PROV" column="PROPOSED_FIN_PROV"/>
            <result property="REAL_PROV_ONE_MM" column="REAL_PROV_ONE_MM"/>
            <result property="REORDER_POINT" column="REORDER_POINT"/>
            <result property="RESTRICTED_STOCK" column="RESTRICTED_STOCK"/>
            <result property="RETURNS_STOCK" column="RETURNS_STOCK"/>
            <result property="SAFETY_STOCK" column="SAFETY_STOCK"/>
            <result property="SS2" column="SS2"/>
            <result property="SS3" column="SS3"/>
            <result property="STOCK_IN_QI" column="STOCK_IN_QI"/>
            <result property="THEO_PROV_ONE_MM" column="THEO_PROV_ONE_MM"/>
            <result property="UD_LT_30_CD" column="UD_LT_30_CD"/>
            <result property="UD_LT_7_CD" column="UD_LT_7_CD"/>
            <result property="UD_GT_30_CD" column="UD_GT_30_CD"/>
            <result property="UU_STOCK" column="UU_STOCK"/>
            <result property="WIP_QTY" column="WIP_QTY"/>
            <result property="WIP_VALUE" column="WIP_VALUE"/>
            <result property="K" column="K"/>
        </association>
    </resultMap>

    <select id="queryReport1" resultMap="report1ResultMap">
        WITH <include refid="mv.inventory_structure_hist_v"/>
        select nvl(${level1},'Others')     category1,
               nvl(${level2},'Others')     category2,
               nvl(${level3},'Others')     category3,
               <if test="level4 != null and level4 != ''.toString()">
               nvl(${level4},'Others')     category4,
               </if>
               <if test="level5 != null and level5 != ''.toString()">
               nvl(${level5},'Others')     category5,
               </if>
               nvl(sum(${valueColumn}),0)  value,
               avg(${slopeType}K) K
               <if test="tooltipsColumns != null and tooltipsColumns != ''.toString()">
                   ,${tooltipsColumns}
               </if>
          from inventory_structure_hist_v t
         where 1 = 1
         AND DATE$ = to_date(#{report1DateRange, jdbcType=VARCHAR}, 'yyyy/mm/dd')
         <include refid="structure_filter"/>
         group by
            ${level1}, ${level2},
            <if test="level4 != null and level4 != ''.toString()">${level4},</if>
            <if test="level5 != null and level5 != ''.toString()">${level5},</if>
            ${level3}
    </select>

    <select id="queryReport1Today" resultMap="report1ResultMap">
        select nvl(${level1},'Others')     category1,
               nvl(${level2},'Others')     category2,
               nvl(${level3},'Others')     category3,
               <if test="level4 != null and level4 != ''.toString()">
               nvl(${level4},'Others')     category4,
               </if>
               <if test="level5 != null and level5 != ''.toString()">
               nvl(${level5},'Others')     category5,
               </if>
               nvl(sum(${valueColumn}),0)  value,
               avg(${slopeType}K) K
               <if test="tooltipsColumns != null and tooltipsColumns != ''.toString()">
                   ,${tooltipsColumns}
               </if>
          from ${SCPA.INVENTORY_STRUCTURE_V} t
         where 1 = 1
         AND DATE$ = to_date(#{report1DateRange, jdbcType=VARCHAR}, 'yyyy/mm/dd')
         <include refid="structure_filter"/>
         group by
            ${level1}, ${level2},
            <if test="level4 != null and level4 != ''.toString()">${level4},</if>
            <if test="level5 != null and level5 != ''.toString()">${level5},</if>
            ${level3}
    </select>

    <select id="queryReport2" resultType="java.util.Map">
        WITH <include refid="mv.inventory_structure_hist_v"/>
        select to_char(t.DATE$, 'yyyy/mm/dd') "xAxis",
               nvl(sum(${valueColumn}),0)     "yAxis",
               nvl(sum(${amuValueColumn}),0)  "aAxis"
               <foreach collection="stackedVariablesColumns" separator="," item="item" open=",">
                   ${item}
               </foreach>
          from inventory_structure_hist_v t
         where t.DATE$ between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') and to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
         <include refid="structure_filter"/>
         group by t.DATE$
         order by t.DATE$
    </select>

    <select id="queryReport2WithPriceReference" resultType="java.util.Map">
        WITH <include refid="mv.inventory_structure_hist_v"/>,
        PRICE_BASE AS (
            SELECT DATE$, MATERIAL, PLANT_CODE,
                   DECODE(SUM(T.UU_STOCK), 0, NULL, SUM(T.UU_STOCK_VALUE) / SUM(T.UU_STOCK)) UU_STOCK_PRICE,
                   DECODE(SUM(T.GIT_QTY), 0, NULL, SUM(T.GIT_VALUE) / SUM(T.GIT_QTY)) GIT_PRICE,
                   DECODE(SUM(T.INTER_STK_TRANSFER), 0, NULL, SUM(T.INTER_STK_TRANSFER_VALUE) / SUM(T.INTER_STK_TRANSFER)) INTER_STK_TRANSFER_PRICE,
                   DECODE(SUM(T.RESTRICTED_STOCK), 0, NULL, SUM(T.RESTRICTED_STOCK_VALUE) / SUM(T.RESTRICTED_STOCK)) RESTRICTED_STOCK_PRICE,
                   DECODE(SUM(T.RETURNS_STOCK), 0, NULL, SUM(T.RETURNS_STOCK_VALUE) / SUM(T.RETURNS_STOCK)) RETURNS_STOCK_PRICE,
                   DECODE(SUM(T.STOCK_IN_QI), 0, NULL, SUM(T.STOCK_IN_QI_VALUE) / SUM(T.STOCK_IN_QI)) STOCK_IN_QI_PRICE,
                   DECODE(SUM(T.BLOCKED_STOCK), 0, NULL, SUM(T.BLOCKED_STOCK_VALUE) / SUM(T.BLOCKED_STOCK)) BLOCKED_STOCK_PRICE,
                   DECODE(SUM(T.WIP_QTY), 0, NULL, SUM(T.WIP_VALUE) / SUM(T.WIP_QTY)) WIP_PRICE,
                   AVG(DECODE(T.PRICE_UNIT, 0, T.UNIT_COST, T.MOVING_AVERAGE_P / T.PRICE_UNIT)) MVP_PRICE
                FROM ${SCPA.INVENTORY_STRUCTURE_HIST} T WHERE T.DATE$ = TO_DATE(#{priceReference, jdbcType=VARCHAR}, 'YYYY/MM/DD')
                <include refid="structure_filter"/>
            GROUP BY MATERIAL, PLANT_CODE, DATE$
        ),
        PRICE_BASE2 AS (SELECT T.DATE$,
                                MATERIAL,
                                PLANT_CODE,
                                UU_STOCK_PRICE,
                                NVL(GIT_PRICE, MVP_PRICE) GIT_PRICE,
                                INTER_STK_TRANSFER_PRICE,
                                RESTRICTED_STOCK_PRICE,
                                RETURNS_STOCK_PRICE,
                                STOCK_IN_QI_PRICE,
                                BLOCKED_STOCK_PRICE,
                                NVL(WIP_PRICE, MVP_PRICE) WIP_PRICE,
                                MVP_PRICE
                         FROM PRICE_BASE T)
        select to_char(t.DATE$, 'yyyy/mm/dd') "xAxis",
               nvl(sum(${valueColumn}),0)     "yAxis",
               nvl(sum(${amuValueColumn}),0)  "aAxis",
               nvl(sum(${priceReferenceValueColumn}), 0) "prAxis"
               <foreach collection="stackedVariablesColumns" separator="," item="item" open=",">
                   ${item}
               </foreach>
          from inventory_structure_hist_v t
               LEFT JOIN PRICE_BASE2 T2 ON T.MATERIAL = T2.MATERIAL AND T.PLANT_CODE = T2.PLANT_CODE
         where t.DATE$ between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') and to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
         <include refid="structure_filter"/>
         group by t.DATE$
         order by t.DATE$
    </select>

    <select id="queryReport2WithPriceReferenceAccountingView" resultType="java.util.Map">
        WITH <include refid="mv.inventory_structure_hist_v"/>,
        PRICE_BASE AS (
            SELECT DATE$, MATERIAL, PLANT_CODE,
                   AVG(DECODE(T.PRICE_UNIT, 0, T.UNIT_COST, NVL(T.MOVING_AVERAGE_P_ETO, T.MOVING_AVERAGE_P / T.PRICE_UNIT))) UU_STOCK_PRICE,
                   DECODE(SUM(T.GIT_QTY), 0, NULL, SUM(T.GIT_VALUE) / SUM(T.GIT_QTY)) GIT_PRICE,
                   AVG(DECODE(T.PRICE_UNIT, 0, T.UNIT_COST, NVL(T.MOVING_AVERAGE_P_ETO, T.MOVING_AVERAGE_P / T.PRICE_UNIT))) INTER_STK_TRANSFER_PRICE,
                   AVG(DECODE(T.PRICE_UNIT, 0, T.UNIT_COST, NVL(T.MOVING_AVERAGE_P_ETO, T.MOVING_AVERAGE_P / T.PRICE_UNIT))) RESTRICTED_STOCK_PRICE,
                   AVG(DECODE(T.PRICE_UNIT, 0, T.UNIT_COST, NVL(T.MOVING_AVERAGE_P_ETO, T.MOVING_AVERAGE_P / T.PRICE_UNIT))) RETURNS_STOCK_PRICE,
                   AVG(DECODE(T.PRICE_UNIT, 0, T.UNIT_COST, NVL(T.MOVING_AVERAGE_P_ETO, T.MOVING_AVERAGE_P / T.PRICE_UNIT))) STOCK_IN_QI_PRICE,
                   AVG(DECODE(T.PRICE_UNIT, 0, T.UNIT_COST, NVL(T.MOVING_AVERAGE_P_ETO, T.MOVING_AVERAGE_P / T.PRICE_UNIT))) BLOCKED_STOCK_PRICE,
                   DECODE(SUM(T.WIP_QTY), 0, NULL, SUM(T.WIP_VALUE) / SUM(T.WIP_QTY)) WIP_PRICE,
                   AVG(DECODE(T.PRICE_UNIT, 0, T.UNIT_COST, NVL(T.MOVING_AVERAGE_P_ETO, T.MOVING_AVERAGE_P / T.PRICE_UNIT))) MVP_PRICE
                FROM ${SCPA.INVENTORY_STRUCTURE_HIST} T WHERE T.DATE$ = TO_DATE(#{priceReference, jdbcType=VARCHAR}, 'YYYY/MM/DD')
                <include refid="structure_filter"/>
            GROUP BY MATERIAL, PLANT_CODE, DATE$
        ),
        PRICE_BASE2 AS (SELECT T.DATE$,
                                MATERIAL,
                                PLANT_CODE,
                                UU_STOCK_PRICE,
                                NVL(GIT_PRICE, MVP_PRICE) GIT_PRICE,
                                INTER_STK_TRANSFER_PRICE,
                                RESTRICTED_STOCK_PRICE,
                                RETURNS_STOCK_PRICE,
                                STOCK_IN_QI_PRICE,
                                BLOCKED_STOCK_PRICE,
                                NVL(WIP_PRICE, MVP_PRICE) WIP_PRICE,
                                MVP_PRICE
                         FROM PRICE_BASE T)
        select to_char(t.DATE$, 'yyyy/mm/dd') "xAxis",
               nvl(sum(${valueColumn}),0)     "yAxis",
               nvl(sum(${amuValueColumn}),0)  "aAxis",
               nvl(sum(${priceReferenceValueColumn}), 0) "prAxis"
               <foreach collection="stackedVariablesColumns" separator="," item="item" open=",">
                   ${item}
               </foreach>
          from inventory_structure_hist_v t
               LEFT JOIN PRICE_BASE2 T2 ON T.MATERIAL = T2.MATERIAL AND T.PLANT_CODE = T2.PLANT_CODE
         where t.DATE$ between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') and to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
         <include refid="structure_filter"/>
         group by t.DATE$
         order by t.DATE$
    </select>

    <select id="queryReport2ProjectionData" resultType="java.util.Map">
        WITH <include refid="mv.inventory_structure_hist_v"/>,
        TEMP2 AS ( /* 根据基准, 算出来每个物料的差 */
                 SELECT T.MATERIAL,
                        T.PLANT_CODE,
                        T.MONTH00 - MONTH01 MONTH01,
                        T.MONTH00 - MONTH02 MONTH02,
                        T.MONTH00 - MONTH03 MONTH03,
                        T.MONTH00 - MONTH04 MONTH04,
                        T.MONTH00 - MONTH05 MONTH05,
                        T.MONTH00 - MONTH06 MONTH06,
                        T.MONTH00 - MONTH07 MONTH07,
                        T.MONTH00 - MONTH08 MONTH08,
                        T.MONTH00 - MONTH09 MONTH09,
                        T.MONTH00 - MONTH10 MONTH10,
                        T.MONTH00 - MONTH11 MONTH11,
                        T.MONTH00 - MONTH12 MONTH12,
                        T.MONTH00 - MONTH13 MONTH13
                 FROM INVENTORY_STRUCTURE_PROJECTION_DETAILS T
             ),
             TEMP3 AS ( /* 将差汇总 */
                 SELECT MATERIAL,
                        PLANT_CODE,
                        SUM(MONTH01) MONTH01,
                        SUM(MONTH02) MONTH02,
                        SUM(MONTH03) MONTH03,
                        SUM(MONTH04) MONTH04,
                        SUM(MONTH05) MONTH05,
                        SUM(MONTH06) MONTH06,
                        SUM(MONTH07) MONTH07,
                        SUM(MONTH08) MONTH08,
                        SUM(MONTH09) MONTH09,
                        SUM(MONTH10) MONTH10,
                        SUM(MONTH11) MONTH11,
                        SUM(MONTH12) MONTH12,
                        SUM(MONTH13) MONTH13
                 FROM TEMP2
                 GROUP BY MATERIAL, PLANT_CODE
             ),
             TEMP4 AS ( /*查询用户指定条件的STOCK VALUE*/
                 SELECT MATERIAL,
                        PLANT_CODE,
                        SUM(${valueColumn}) AS MONTH00
                 FROM inventory_structure_hist_v T
                 WHERE T.DATE$ = TO_DATE(#{projectionVersion, jdbcType=VARCHAR} || '01', 'YYYYMMDD')
                 <include refid="structure_filter"/>
                 GROUP BY MATERIAL, PLANT_CODE
             ),
             TEMP5 AS ( /* 将用户指定条件的VALUE与差进行合并 */
                 SELECT T.PLANT_CODE,
                        T.MATERIAL,
                        T.MONTH00,
                        CASE WHEN T.MONTH00 > 0 AND T.MONTH00 - NVL(T2.MONTH01, 0) &lt; 0 THEN 0 WHEN T.MONTH00 &lt; 0 THEN T.MONTH00 ELSE T.MONTH00 - NVL(T2.MONTH01, 0) END AS MONTH01,
                        CASE WHEN T.MONTH00 > 0 AND T.MONTH00 - NVL(T2.MONTH02, 0) &lt; 0 THEN 0 WHEN T.MONTH00 &lt; 0 THEN T.MONTH00 ELSE T.MONTH00 - NVL(T2.MONTH02, 0) END AS MONTH02,
                        CASE WHEN T.MONTH00 > 0 AND T.MONTH00 - NVL(T2.MONTH03, 0) &lt; 0 THEN 0 WHEN T.MONTH00 &lt; 0 THEN T.MONTH00 ELSE T.MONTH00 - NVL(T2.MONTH03, 0) END AS MONTH03,
                        CASE WHEN T.MONTH00 > 0 AND T.MONTH00 - NVL(T2.MONTH04, 0) &lt; 0 THEN 0 WHEN T.MONTH00 &lt; 0 THEN T.MONTH00 ELSE T.MONTH00 - NVL(T2.MONTH04, 0) END AS MONTH04,
                        CASE WHEN T.MONTH00 > 0 AND T.MONTH00 - NVL(T2.MONTH05, 0) &lt; 0 THEN 0 WHEN T.MONTH00 &lt; 0 THEN T.MONTH00 ELSE T.MONTH00 - NVL(T2.MONTH05, 0) END AS MONTH05,
                        CASE WHEN T.MONTH00 > 0 AND T.MONTH00 - NVL(T2.MONTH06, 0) &lt; 0 THEN 0 WHEN T.MONTH00 &lt; 0 THEN T.MONTH00 ELSE T.MONTH00 - NVL(T2.MONTH06, 0) END AS MONTH06,
                        CASE WHEN T.MONTH00 > 0 AND T.MONTH00 - NVL(T2.MONTH07, 0) &lt; 0 THEN 0 WHEN T.MONTH00 &lt; 0 THEN T.MONTH00 ELSE T.MONTH00 - NVL(T2.MONTH07, 0) END AS MONTH07,
                        CASE WHEN T.MONTH00 > 0 AND T.MONTH00 - NVL(T2.MONTH08, 0) &lt; 0 THEN 0 WHEN T.MONTH00 &lt; 0 THEN T.MONTH00 ELSE T.MONTH00 - NVL(T2.MONTH08, 0) END AS MONTH08,
                        CASE WHEN T.MONTH00 > 0 AND T.MONTH00 - NVL(T2.MONTH09, 0) &lt; 0 THEN 0 WHEN T.MONTH00 &lt; 0 THEN T.MONTH00 ELSE T.MONTH00 - NVL(T2.MONTH09, 0) END AS MONTH09,
                        CASE WHEN T.MONTH00 > 0 AND T.MONTH00 - NVL(T2.MONTH10, 0) &lt; 0 THEN 0 WHEN T.MONTH00 &lt; 0 THEN T.MONTH00 ELSE T.MONTH00 - NVL(T2.MONTH10, 0) END AS MONTH10,
                        CASE WHEN T.MONTH00 > 0 AND T.MONTH00 - NVL(T2.MONTH11, 0) &lt; 0 THEN 0 WHEN T.MONTH00 &lt; 0 THEN T.MONTH00 ELSE T.MONTH00 - NVL(T2.MONTH11, 0) END AS MONTH11,
                        CASE WHEN T.MONTH00 > 0 AND T.MONTH00 - NVL(T2.MONTH12, 0) &lt; 0 THEN 0 WHEN T.MONTH00 &lt; 0 THEN T.MONTH00 ELSE T.MONTH00 - NVL(T2.MONTH12, 0) END AS MONTH12,
                        CASE WHEN T.MONTH00 > 0 AND T.MONTH00 - NVL(T2.MONTH13, 0) &lt; 0 THEN 0 WHEN T.MONTH00 &lt; 0 THEN T.MONTH00 ELSE T.MONTH00 - NVL(T2.MONTH13, 0) END AS MONTH13
                 FROM TEMP4 T
                          LEFT JOIN TEMP3 T2 ON T.MATERIAL = T2.MATERIAL AND T.PLANT_CODE = T2.PLANT_CODE
             )

        /* 最后将明细汇总 */
        SELECT NVL(SUM(MONTH00), 0) MONTH00,
               NVL(SUM(MONTH01), 0) MONTH01,
               NVL(SUM(MONTH02), 0) MONTH02,
               NVL(SUM(MONTH03), 0) MONTH03,
               NVL(SUM(MONTH04), 0) MONTH04,
               NVL(SUM(MONTH05), 0) MONTH05,
               NVL(SUM(MONTH06), 0) MONTH06,
               NVL(SUM(MONTH07), 0) MONTH07,
               NVL(SUM(MONTH08), 0) MONTH08,
               NVL(SUM(MONTH09), 0) MONTH09,
               NVL(SUM(MONTH10), 0) MONTH10,
               NVL(SUM(MONTH11), 0) MONTH11,
               NVL(SUM(MONTH12), 0) MONTH12,
               NVL(SUM(MONTH13), 0) MONTH13
        FROM TEMP5
    </select>

    <select id="queryReport3Columns" resultType="java.lang.String">
        select distinct to_char(t.DATE$, 'yyyy/mm/dd')
        from ${SCPA.INVENTORY_STRUCTURE_HIST} t
        where t.DATE$ between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') and to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
        order by to_char(t.DATE$, 'yyyy/mm/dd') desc
        offset 0 rows fetch next 60 rows only
    </select>

    <sql id="report3SQL">
        WITH <include refid="mv.inventory_structure_hist_v"/>
        select *
            from (
                select /*+ parallel(t 6) */
                       <foreach collection="report3SelectedColumn" separator="," item="item">
                            nvl(${item},'Others') ${item}
                       </foreach>,
                       to_char(t.date$, 'yyyy/mm/dd') days,
                       nvl(sum(${valueColumn}),0)  total
                 from inventory_structure_hist_v t
                where t.DATE$ between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') and to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                <include refid="structure_filter"/>
                group by
                <foreach collection="report3SelectedColumn" separator="," item="item">
                    ${item}
                </foreach>
                , to_char(t.date$, 'yyyy/mm/dd')
            ) mm
            PIVOT (
                sum(total) total
                FOR days
                IN (
                <foreach collection="report3ColumnNames" separator="," item="item">
                    '${item}'
                </foreach>
                )
            )
        order by
        <foreach collection="report3SelectedColumn" separator="," item="item">
            decode(${item}, 'Others', 'zzz', 'No Owner', 'zzx', ${item})
        </foreach>
    </sql>

    <select id="queryReport3" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report3SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="report3DetailsSQL">
        WITH <include refid="mv.inventory_structure_hist_v"/>
        select /*+ parallel(t 6) */ to_char(t.DATE$, 'yyyy/mm/dd') DATE_STR, t.*
        from inventory_structure_hist_v t
        where
              <choose>
                  <when test="report3SelectedDate != null and report3SelectedDate != ''.toString()">
                      t.DATE$ = to_date(#{report3SelectedDate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
                  </when>
                  <otherwise>
                      t.DATE$ between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') and to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                  </otherwise>
              </choose>
        <include refid="structure_filter"/>

        <foreach collection="report3SelectedColumn" item="item" index="index">
            <if test="report3SelectedValue[index] != null and report3SelectedValue[index] != 'Total'.toString() and report3SelectedValue[index] != ''.toString()">
                <choose>
                    <when test="report3SelectedValue[index] == 'Others'.toString()">
                        and ( t.${item} is null
                           or t.${item} = #{report3SelectedValue[${index}],jdbcType=VARCHAR})
                    </when>
                    <otherwise>
                        and t.${item} = #{report3SelectedValue[${index}],jdbcType=VARCHAR}
                    </otherwise>
                </choose>
            </if>
        </foreach>
    </sql>

    <select id="queryReport3DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="report3DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport3Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report3DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport4" resultType="java.util.LinkedHashMap">
        WITH inventory_structure_hist_v AS (
            SELECT /*+ parallel */ t.*,
                NVL(T2.MATERIAL_OWNER_SESA, 'Others') AS MATERIAL_OWNER_SESA,
                NVL(T2.MATERIAL_OWNER_NAME, 'Others') AS MATERIAL_OWNER_NAME,
                T2.INVENTORY_MONITOR_TYPE,
                SUM(T.AMU_ONE_MM) OVER ( PARTITION BY T.MATERIAL,T.PLANT_CODE) AS AMU_ONE_MM_MODIFIED,
                SUM(T.AMF_ONE_MM) OVER ( PARTITION BY T.MATERIAL,T.PLANT_CODE) AS AMF_ONE_MM_MODIFIED
            FROM ${SCPA.INVENTORY_STRUCTURE_HIST} T
                     LEFT JOIN MATERIAL_MASTER_V T2 ON T.MATERIAL = T2.MATERIAL AND T.PLANT_CODE = T2.PLANT_CODE
            where T.DATE$ = TO_DATE(#{report4Date, jdbcType=VARCHAR}, 'YYYY/MM/DD')
         )
        select *
        from (
            select <foreach collection="report4SelectedColumn" separator="," item="item">
                        ${item}
                   </foreach>,
                   AMU_GROUP,
                   SUM(STOCK_VALUE) STOCK_VALUE
            from (
                     select <foreach collection="report4SelectedColumn" separator="," item="item">
                                nvl(tt.${item}, 'Others') ${item}
                            </foreach>,
                            tt.STOCK_VALUE,
                            <![CDATA[
                            CASE
                                WHEN tt.amu_day <= 7 THEN '<1W'
                                WHEN tt.amu_day <= 14 THEN '1W-2W'
                                WHEN tt.amu_day <= 21 THEN '2W-3W'
                                WHEN tt.amu_day <= 28 THEN '3W-4W'
                                WHEN tt.amu_day <= 60 THEN '4W-2M'
                                WHEN tt.amu_day <= 90 THEN '2M-3M'
                                WHEN tt.amu_day <= 180 THEN '3M-6M'
                                WHEN tt.amu_day <= 365 THEN '6M-1Y'
                                WHEN tt.amu_day <= 730 THEN '1Y-2Y'
                                WHEN TT.AMU_DAY = 999999 * 30 THEN 'No Demand'
                                WHEN tt.amu_day > 730 THEN '>2Y' END as AMU_GROUP
                     from (
                              select
                                CASE t2.LT_RANGE
                                    WHEN '1W' THEN '<1W'
                                    WHEN '2W' THEN '1W-2W'
                                    WHEN '3W' THEN '2W-3W'
                                    WHEN '4W' THEN '3W-4W'
                                    WHEN '2M' THEN '4W-2M'
                                    WHEN '3M' THEN '2M-3M'
                                    WHEN '6M' THEN '3M-6M'
                                    WHEN '1Y' THEN '6M-1Y'
                                    WHEN '2Y' THEN '1Y-2Y'
                                    ELSE '>2Y' END as LT_GROUP,
                                    DECODE(t.AMU_ONE_MM, 0, 999999, t.STOCK_QTY / t.AMU_ONE_MM) * 30 AMU_DAY,
                                    t.STOCK_VALUE,
                                    t2.*
                              from (select MATERIAL,
                                           PLANT_CODE,
                                           max(${convColumn})   AMU_ONE_MM,
                                           sum(${qtyColumn})    STOCK_QTY,
                                           sum(${valueColumn})  STOCK_VALUE
                                    from inventory_structure_hist_v T
                                    where 1 = 1
                              ]]>
                                    <include refid="structure_filter"/>
                                    group by MATERIAL, PLANT_CODE
                                   ) t
                                       inner join MATERIAL_MASTER_V t2
                                                  on t.MATERIAL = t2.MATERIAL and t.PLANT_CODE = t2.PLANT_CODE) tt) mm

            GROUP BY
            <foreach collection="report4SelectedColumn" separator="," item="item">
                ${item}
            </foreach>,
            AMU_GROUP) NN
            PIVOT (
                SUM(STOCK_VALUE) TOTAL
                FOR AMU_GROUP IN <![CDATA[('<1W','1W-2W','2W-3W','3W-4W','4W-2M','2M-3M','3M-6M','6M-1Y','1Y-2Y', '>2Y', 'No Demand')]]>
            )
            ORDER BY
            <foreach collection="report4SelectedColumn" separator="," item="item">
                <![CDATA[ DECODE(NVL(${item}, 'Others'), '<1W', 'a', '1W-2W', 'b', '2W-3W', 'c', '3W-4W', 'd', '4W-2M', 'e', '2M-3M', 'f',
                '3M-6M', 'g',
                '6M-1Y', 'h',
                '1Y-2Y', 'i', '>2Y', 'j', 'Others', 'zzz', NVL(${item}, 'Others'))]]>
            </foreach>
    </select>

    <sql id="report4DetailsSQL">
        WITH inventory_structure_hist_v AS (
            SELECT /*+ parallel */ t.*,
                NVL(T2.MATERIAL_OWNER_SESA, 'Others') AS MATERIAL_OWNER_SESA,
                NVL(T2.MATERIAL_OWNER_NAME, 'Others') AS MATERIAL_OWNER_NAME,
                T2.INVENTORY_MONITOR_TYPE,
                SUM(T.AMU_ONE_MM) OVER ( PARTITION BY T.MATERIAL,T.PLANT_CODE) AS AMU_ONE_MM_MODIFIED,
                SUM(T.AMF_ONE_MM) OVER ( PARTITION BY T.MATERIAL,T.PLANT_CODE) AS AMF_ONE_MM_MODIFIED
            FROM ${SCPA.INVENTORY_STRUCTURE_HIST} T
                     LEFT JOIN MATERIAL_MASTER_V T2 ON T.MATERIAL = T2.MATERIAL AND T.PLANT_CODE = T2.PLANT_CODE
            where T.DATE$ = TO_DATE(#{report4Date, jdbcType=VARCHAR}, 'YYYY/MM/DD')
         )
        <![CDATA[
        SELECT * FROM (
            SELECT MM.*,
                   CASE
                        WHEN mm.STOCK_AVAILABLE_DAY <= 7 THEN '<1W'
                        WHEN mm.STOCK_AVAILABLE_DAY <= 14 THEN '1W-2W'
                        WHEN mm.STOCK_AVAILABLE_DAY <= 21 THEN '2W-3W'
                        WHEN mm.STOCK_AVAILABLE_DAY <= 28 THEN '3W-4W'
                        WHEN mm.STOCK_AVAILABLE_DAY <= 60 THEN '4W-2M'
                        WHEN mm.STOCK_AVAILABLE_DAY <= 90 THEN '2M-3M'
                        WHEN mm.STOCK_AVAILABLE_DAY <= 180 THEN '3M-6M'
                        WHEN mm.STOCK_AVAILABLE_DAY <= 365 THEN '6M-1Y'
                        WHEN mm.STOCK_AVAILABLE_DAY <= 730 THEN '1Y-2Y'
                        WHEN mm.STOCK_AVAILABLE_DAY = 999999 * 30 THEN 'No Demand'
                        WHEN mm.STOCK_AVAILABLE_DAY > 730 THEN '>2Y' END AMU_GROUP,
                   CASE mm.LT_RANGE
                        WHEN '1W' THEN '<1W'
                        WHEN '2W' THEN '1W-2W'
                        WHEN '3W' THEN '2W-3W'
                        WHEN '4W' THEN '3W-4W'
                        WHEN '2M' THEN '4W-2M'
                        WHEN '3M' THEN '2M-3M'
                        WHEN '6M' THEN '3M-6M'
                        WHEN '1Y' THEN '6M-1Y'
                        WHEN '2Y' THEN '1Y-2Y'
                        ELSE '>2Y' END as LT_GROUP
              FROM (
                select
                        T.AMU_AMF_ONE_MM,
                        T.UU_STOCK,
                        T.STOCK_IN_QI,
                        T.RESTRICTED_STOCK,
                        T.BLOCKED_STOCK,
                        T.RETURNS_STOCK,
                        T.INTER_STK_TRANSFER,
                        T.WIP_QTY,
                        T.WIP_VALUE,
                        T.GIT_QTY,
                        T.GIT_VALUE,
                        T.OPEN_PO,
                        T."UD_<7CD",
                        T."UD_<30CD",
                        T."UD_>30CD",
                        T.SELECTED_QTY,
                        T.SELECTED_SUMMARY,
                        DECODE(t.AMU_AMF_ONE_MM, 0, 999999, t.SELECTED_QTY / t.AMU_AMF_ONE_MM) * 30 STOCK_AVAILABLE_DAY,
                        t2.*
                  from (select MATERIAL,
                               PLANT_CODE,
                               MAX(${convColumn})      AMU_AMF_ONE_MM,
                               sum(UU_STOCK)           UU_STOCK,
                               sum(STOCK_IN_QI)        STOCK_IN_QI,
                               sum(RESTRICTED_STOCK)   RESTRICTED_STOCK,
                               sum(BLOCKED_STOCK)      BLOCKED_STOCK,
                               sum(RETURNS_STOCK)      RETURNS_STOCK,
                               sum(INTER_STK_TRANSFER) INTER_STK_TRANSFER,
                               sum(WIP_QTY)            WIP_QTY,
                               sum(WIP_VALUE)          WIP_VALUE,
                               sum(GIT_QTY)            GIT_QTY,
                               sum(GIT_VALUE)          GIT_VALUE,
                               sum(OPEN_PO)            OPEN_PO,
                               sum(UD_LT_7_CD)         "UD_<7CD",
                               sum(UD_LT_30_CD)        "UD_<30CD",
                               sum(UD_GT_30_CD)        "UD_>30CD",
                               sum(${qtyColumn})       SELECTED_QTY,
                               sum(${valueColumn})     SELECTED_SUMMARY
                        from inventory_structure_hist_v T
                       where 1 = 1
                ]]>
                        <include refid="structure_filter"/>
                        GROUP BY MATERIAL, PLANT_CODE
                       ) t inner join MATERIAL_MASTER_V t2
                                      on t.MATERIAL = t2.MATERIAL and t.PLANT_CODE = t2.PLANT_CODE
            ) mm
        ) NN
        <where>
            <foreach collection="report4SelectedColumn" item="item" index="index">
                <if test="report4SelectedValue[index] != null and report4SelectedValue[index] != 'Total'.toString() and report4SelectedValue[index] != ''.toString()">
                    <choose>
                        <when test="report4SelectedValue[index] == 'Others'.toString()">
                            and NN.${item} is null
                        </when>
                        <otherwise>
                            and NN.${item} = #{report4SelectedValue[${index}],jdbcType=VARCHAR}
                        </otherwise>
                    </choose>
                </if>
            </foreach>
            <if test="report4SelectedDate != null and report4SelectedDate != ''.toString()">
                AND AMU_GROUP = #{report4SelectedDate, jdbcType=VARCHAR}
            </if>
        </where>
    </sql>

    <select id="queryReport4DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="report4DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport4Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report4DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport1Level1Slope" resultType="java.util.Map">
        WITH inventory_structure_hist_v AS (
         select * from (
                SELECT /*+ parallel */ t.*,
                    NVL(T2.MATERIAL_OWNER_SESA, 'Others') AS MATERIAL_OWNER_SESA,
                    NVL(T2.MATERIAL_OWNER_NAME, 'Others') AS MATERIAL_OWNER_NAME,
                    T2.INVENTORY_MONITOR_TYPE
                FROM ${SCPA.INVENTORY_STRUCTURE_HIST} T
                LEFT JOIN MATERIAL_MASTER_V T2 ON T.MATERIAL = T2.MATERIAL AND T.PLANT_CODE = T2.PLANT_CODE
                <where>
                    t.DATE$ between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') and to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                </where>) T
         <where>
             <include refid="structure_filter"/>
         </where>
         )
        SELECT /*+ parallel  */
               TT.KEY,
               ROUND(REGR_SLOPE(TT.STOCK_VALUE, DAY_GAP), 2) AS K
        FROM (SELECT T.${level1} AS KEY,
                     T.DATE$ - TRUNC(ADD_MONTHS(SYSDATE, -1))                                      DAY_GAP,
                     <choose>
                        <when test="slopeType == 'VALUE_'.toString()">
                         SUM(nvl(UU_STOCK_VALUE, 0) + nvl(STOCK_IN_QI_VALUE, 0) + nvl(RESTRICTED_STOCK_VALUE, 0) +
                             nvl(BLOCKED_STOCK_VALUE, 0) + nvl(RETURNS_STOCK_VALUE, 0) +
                             nvl(INTER_STK_TRANSFER_VALUE, 0) + nvl(WIP_VALUE, 0) + nvl(GIT_VALUE, 0)) STOCK_VALUE
                        </when>
                        <otherwise>
                            SUM(nvl(UU_STOCK, 0) + nvl(STOCK_IN_QI, 0) + nvl(RESTRICTED_STOCK, 0) +
                                nvl(BLOCKED_STOCK, 0) + nvl(RETURNS_STOCK, 0) +
                                nvl(INTER_STK_TRANSFER, 0) + nvl(WIP_QTY, 0) + nvl(GIT_QTY, 0))        STOCK_VALUE
                        </otherwise>
                    </choose>
              FROM inventory_structure_hist_v T
              WHERE T.${level1} IS NOT NULL
              GROUP BY T.${level1}, T.DATE$) TT
        GROUP BY TT.KEY
    </select>

    <select id="queryReport1Level2Slope" resultType="java.util.Map">
        WITH inventory_structure_hist_v AS (
            SELECT * FROM (
            SELECT /*+ parallel(8) */ t.*,
                NVL(T2.MATERIAL_OWNER_SESA, 'Others') AS MATERIAL_OWNER_SESA,
                NVL(T2.MATERIAL_OWNER_NAME, 'Others') AS MATERIAL_OWNER_NAME,
                T2.INVENTORY_MONITOR_TYPE
            FROM ${SCPA.INVENTORY_STRUCTURE_HIST} T
                     LEFT JOIN MATERIAL_MASTER_V T2 ON T.MATERIAL = T2.MATERIAL AND T.PLANT_CODE = T2.PLANT_CODE
            <where>
                t.DATE$ between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') and to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
            </where>) T
            <where>
                <include refid="structure_filter"/>
            </where>
         )
        SELECT /*+ parallel(8)  */
               TT.KEY,
               ROUND(REGR_SLOPE(TT.STOCK_VALUE, DAY_GAP), 2) AS K
        FROM (SELECT T.${level1} || '[#]' || T.${level2} AS KEY,
                     T.DATE$ - TRUNC(ADD_MONTHS(SYSDATE, -1))                                      DAY_GAP,
                     <choose>
                        <when test="slopeType == 'VALUE_'.toString()">
                         SUM(nvl(UU_STOCK_VALUE, 0) + nvl(STOCK_IN_QI_VALUE, 0) + nvl(RESTRICTED_STOCK_VALUE, 0) +
                             nvl(BLOCKED_STOCK_VALUE, 0) + nvl(RETURNS_STOCK_VALUE, 0) +
                             nvl(INTER_STK_TRANSFER_VALUE, 0) + nvl(WIP_VALUE, 0) + nvl(GIT_VALUE, 0)) STOCK_VALUE
                        </when>
                        <otherwise>
                            SUM(nvl(UU_STOCK, 0) + nvl(STOCK_IN_QI, 0) + nvl(RESTRICTED_STOCK, 0) +
                                nvl(BLOCKED_STOCK, 0) + nvl(RETURNS_STOCK, 0) +
                                nvl(INTER_STK_TRANSFER, 0) + nvl(WIP_QTY, 0) + nvl(GIT_QTY, 0))        STOCK_VALUE
                        </otherwise>
                    </choose>
              FROM inventory_structure_hist_v T
              WHERE T.${level1} IS NOT NULL
                    AND T.${level2} IS NOT NULL
              GROUP BY T.${level1},T.${level2}, T.DATE$) TT
        GROUP BY TT.KEY
    </select>

    <select id="queryPriceReferenceOpts" resultType="java.lang.String">
        SELECT TO_CHAR(DATE$, 'YYYY/MM/DD')
        FROM (SELECT DISTINCT DATE$
              FROM SCPA.INVENTORY_STRUCTURE_HIST)
        ORDER BY DATE$ DESC
    </select>
</mapper>
