<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.inventory.dao.IMonthlySliceReportDao">
    <select id="queryCascader" resultType="java.util.Map">
        select *
          from SLICE_FILTER_V
         order by category,
         decode(category, 'CLUSTER_NAME',
             decode(NAME, 'China_Total', '0',
                          'LV', '1',
                          'MV', '2',
                          'EE', '3',
                          'H&amp;D', '31',
                          'Trading', '4',
                          'CNDC_FS', '5',
                          'CNDC_FS_PS', '6',
                          'CNDC_PEC', '7',
                          'CNDC_Others', '8',
                          'China_Other_OPS', '9',
                          'China_DC', '91', NAME),
             'ENTITY_NAME',
             decode(NAME,  'LV', 'ZZ<PERSON>',
                                  'MV', 'ZZB',
                                  'EE', 'ZZC',
                                  'H&amp;D', 'ZZD',
                                  'Trading', 'ZZE',
                                  'CNDC_FS', 'ZZF',
                                  'CNDC_FS_PS', 'ZZG',
                                  'CNDC_PEC', 'ZZH',
                                  'CNDC_Others', 'ZZI',
                                  'China_Other_OPS', 'ZZJ',
                                  'China_DC', 'ZZK',
                                  'China_Total', 'ZZL', NAME),
             decode (name,'Others','zzz',name)
         )
    </select>

    <select id="querySliceVersion" resultType="java.lang.String">
        SELECT DISTINCT VERSION FROM SLICE_RAW_DATA_V ORDER BY VERSION DESC
    </select>

    <select id="queryAvailableEntity" resultType="java.lang.String">
        select NAME from SLICE_FILTER_V t where CATEGORY = 'ENTITY_NAME' order by NAME
    </select>

    <select id="queryRcaCode" resultType="java.lang.String">
        select RCA_CODE from MR3_INVENTORY_PROJECTION_RCA_CODE
    </select>

    <select id="queryInventoryRCAList" resultType="java.util.Map">
        SELECT RCA_CODE AS KEY, DESCRIPTION AS "VALUE" FROM MR3_INVENTORY_PROJECTION_RCA_CODE
    </select>

    <select id="queryPageAdmin" resultType="java.lang.Integer">
        select count(1) cnt
          from SY_MENU_AUTH t
         where upper(t.AUTH_DETAILS) = 'ADMIN' and t.USER_ID = #{userid, jdbcType=VARCHAR} and t.MENU_CODE = #{parentCode, jdbcType=VARCHAR}
    </select>

    <insert id="syncForex">
        declare
            cnt number;
        begin
            select count(1) into cnt from SLICE_FOREX where (CURRENCY_TO = 'CNY' and CURRENCY_FROM = 'HKD') or (CURRENCY_TO = 'HKD' and CURRENCY_FROM = 'CNY');
            delete from SLICE_FOREX_HIST where version = #{version, jdbcType=VARCHAR};
            INSERT INTO SLICE_FOREX_HIST
                (VERSION, CURRENCY_TYPE, CURRENCY_FROM, CURRENCY_TO, MONTH_YEAR, EXCHANGE_RATE)
            SELECT #{version, jdbcType=VARCHAR}, CURRENCY_TYPE, CURRENCY_FROM, CURRENCY_TO, MONTH_YEAR, EXCHANGE_RATE
            FROM SLICE_FOREX;

            if cnt = 0
            then
                INSERT INTO SLICE_FOREX_HIST (VERSION, CURRENCY_TYPE, CURRENCY_FROM, CURRENCY_TO, MONTH_YEAR, EXCHANGE_RATE)
                select #{version, jdbcType=VARCHAR},
                       t1.CURRENCY_TYPE,
                       t1.CURRENCY_TO,
                       t2.CURRENCY_TO,
                       t1.MONTH_YEAR,
                       t2.EXCHANGE_RATE / t1.EXCHANGE_RATE as EXCHANGE_RATE
                from SLICE_FOREX t1
                         inner join SLICE_FOREX t2 on t1.CURRENCY_TYPE = t2.CURRENCY_TYPE and t1.MONTH_YEAR = t2.MONTH_YEAR
                    and t1.CURRENCY_FROM = t2.CURRENCY_FROM and t1.CURRENCY_FROM = 'EUR' and t1.CURRENCY_TO = 'CNY' and t2.CURRENCY_TO = 'HKD';
            end if;
        end;
    </insert>

    <delete id="deleteExistReport">
        delete from SLICE_RESULT where version = #{version, jdbcType=VARCHAR}
    </delete>

    <insert id="saveReport">
        insert into SLICE_RESULT
        (VERSION, CLUSTER_NAME, ENTITY_NAME, PLANT_TYPE, CLASS, UNIT_OF_MEASURE,
        MONTH01, MONTH02, MONTH03, MONTH04, MONTH05, MONTH06, MONTH07, MONTH08, MONTH09, MONTH10, MONTH11, MONTH12,
        MONTH13, MONTH14, MONTH15, MONTH16, MONTH17, MONTH18, MONTH19, MONTH20, MONTH21, MONTH22, MONTH23, MONTH24,
        MONTH25, MONTH26, MONTH27, MONTH28, MONTH29, MONTH30, MONTH31, MONTH32, MONTH33, MONTH34, MONTH35, MONTH36,
        UPDATE_DATE$, CURRENCY, CURRENCY_TYPE, CURRENT_MONTH)
        <foreach collection="rawData" item="item" separator=" union all ">
            select
                #{version, jdbcType=VARCHAR},
                #{item.clusterName, jdbcType=VARCHAR},
                #{item.entityName, jdbcType=VARCHAR},
                #{item.plantType, jdbcType=VARCHAR},
                #{item.clazz, jdbcType=VARCHAR},
                '',
                #{item.month01, jdbcType=DOUBLE},
                #{item.month02, jdbcType=DOUBLE},
                #{item.month03, jdbcType=DOUBLE},
                #{item.month04, jdbcType=DOUBLE},
                #{item.month05, jdbcType=DOUBLE},
                #{item.month06, jdbcType=DOUBLE},
                #{item.month07, jdbcType=DOUBLE},
                #{item.month08, jdbcType=DOUBLE},
                #{item.month09, jdbcType=DOUBLE},
                #{item.month10, jdbcType=DOUBLE},
                #{item.month11, jdbcType=DOUBLE},
                #{item.month12, jdbcType=DOUBLE},
                #{item.month13, jdbcType=DOUBLE},
                #{item.month14, jdbcType=DOUBLE},
                #{item.month15, jdbcType=DOUBLE},
                #{item.month16, jdbcType=DOUBLE},
                #{item.month17, jdbcType=DOUBLE},
                #{item.month18, jdbcType=DOUBLE},
                #{item.month19, jdbcType=DOUBLE},
                #{item.month20, jdbcType=DOUBLE},
                #{item.month21, jdbcType=DOUBLE},
                #{item.month22, jdbcType=DOUBLE},
                #{item.month23, jdbcType=DOUBLE},
                #{item.month24, jdbcType=DOUBLE},
                #{item.month25, jdbcType=DOUBLE},
                #{item.month26, jdbcType=DOUBLE},
                #{item.month27, jdbcType=DOUBLE},
                #{item.month28, jdbcType=DOUBLE},
                #{item.month29, jdbcType=DOUBLE},
                #{item.month30, jdbcType=DOUBLE},
                #{item.month31, jdbcType=DOUBLE},
                #{item.month32, jdbcType=DOUBLE},
                #{item.month33, jdbcType=DOUBLE},
                #{item.month34, jdbcType=DOUBLE},
                #{item.month35, jdbcType=DOUBLE},
                #{item.month36, jdbcType=DOUBLE},
                sysdate,
                #{item.currency, jdbcType=VARCHAR},
                #{item.currencyType, jdbcType=VARCHAR},
                #{item.currentMonth, jdbcType=DOUBLE}
            from dual
        </foreach>
    </insert>

    <select id="queryRawData" resultType="com.scp.inventory.bean.SliceRawData">
        select NVL(T3.CLUSTER_NAME, T2.ENTITY_NAME) CLUSTER_NAME,
               T2.ENTITY_NAME,
               T.PLANT_TYPE,
               T.CLASS                           AS "CLAZZ",
               T.CURRENCY,
               T.CURRENCY_TYPE,
               SUM(T."M01_Y-1")                     MONTH01,
               SUM(T."M02_Y-1")                     MONTH02,
               SUM(T."M03_Y-1")                     MONTH03,
               SUM(T."M04_Y-1")                     MONTH04,
               SUM(T."M05_Y-1")                     MONTH05,
               SUM(T."M06_Y-1")                     MONTH06,
               SUM(T."M07_Y-1")                     MONTH07,
               SUM(T."M08_Y-1")                     MONTH08,
               SUM(T."M09_Y-1")                     MONTH09,
               SUM(T."M10_Y-1")                     MONTH10,
               SUM(T."M11_Y-1")                     MONTH11,
               SUM(T."M12_Y-1")                     MONTH12,
               SUM(T."M01_Y-0")                     MONTH13,
               SUM(T."M02_Y-0")                     MONTH14,
               SUM(T."M03_Y-0")                     MONTH15,
               SUM(T."M04_Y-0")                     MONTH16,
               SUM(T."M05_Y-0")                     MONTH17,
               SUM(T."M06_Y-0")                     MONTH18,
               SUM(T."M07_Y-0")                     MONTH19,
               SUM(T."M08_Y-0")                     MONTH20,
               SUM(T."M09_Y-0")                     MONTH21,
               SUM(T."M10_Y-0")                     MONTH22,
               SUM(T."M11_Y-0")                     MONTH23,
               SUM(T."M12_Y-0")                     MONTH24
        from SLICE_RAW_DATA_V T
                 INNER JOIN SLICE_HFM_ENTITY T2 ON T.HFM_CODE = T2.HFM_CODE
                 LEFT JOIN MR3_ENTITY_INFORMATION T3 ON T2.ENTITY_NAME = T3.ENTITY_NAME
        WHERE T.VERSION = #{version, jdbcType=VARCHAR} AND T2.ENTITY_NAME != 'IGNORE'
        GROUP BY T3.CLUSTER_NAME,
                 T2.ENTITY_NAME,
                 T.PLANT_TYPE,
                 T.CLASS,
                 T.CURRENCY,
                 T.CURRENCY_TYPE
    </select>

    <select id="queryExistsResult" resultType="java.lang.Integer">
        select sum(cnt) from (
            select count(1) cnt from SLICE_RESULT t where t.UPDATE_DATE$ &lt; (select max(nvl(t2.UPDATE_DATE$, t2.CREATE_DATE$)) from SLICE_RAW_DATA_V t2)
            and t.VERSION = (select max(t0.VERSION) from SLICE_RESULT t0)

            union all

            select count(1) from SLICE_RESULT t where t.UPDATE_DATE$ &lt; (select max(nvl(t2.UPDATE_DATE$, t2.CREATE_DATE$)) from SLICE_HFM_ENTITY t2)
            and t.VERSION = (select max(t0.VERSION) from SLICE_RESULT t0)

            union all

            select count(1) from SLICE_RESULT t where t.UPDATE_DATE$ &lt; (select max(nvl(t2.UPDATE_DATE$, t2.CREATE_DATE$)) from SLICE_FOREX t2)
            and t.VERSION = (select max(t0.VERSION) from SLICE_RESULT t0)
        )
    </select>

    <select id="getCurrency" resultType="com.scp.inventory.bean.SliceCurrency">
        select CURRENCY_TYPE, CURRENCY_FROM, CURRENCY_TO, MONTH_YEAR as "MONTH", EXCHANGE_RATE
          from SLICE_FOREX_HIST t
         where VERSION = #{version, jdbcType=VARCHAR} order by MONTH_YEAR
    </select>

    <select id="queryReport1" parameterType="java.util.Map" resultType="com.scp.inventory.bean.SliceRawData">
        select CLUSTER_NAME,
               ENTITY_NAME,
               PLANT_TYPE,
               CLASS                           AS "CLAZZ",
               CURRENCY,
               CURRENCY_TYPE,
               decode(#{currency, jdbcType=VARCHAR}, 'CNY', 'MRMB', 'EUR', 'MEUR', 'HKD', 'MHKD') as UNIT_OF_MEASURE,
               CURRENT_MONTH,
               MONTH01,
               MONTH02,
               MONTH03,
               MONTH04,
               MONTH05,
               MONTH06,
               MONTH07,
               MONTH08,
               MONTH09,
               MONTH10,
               MONTH11,
               MONTH12,
               MONTH13,
               MONTH14,
               MONTH15,
               MONTH16,
               MONTH17,
               MONTH18,
               MONTH19,
               MONTH20,
               MONTH21,
               MONTH22,
               MONTH23,
               MONTH24,
               MONTH25,
               MONTH26,
               MONTH27,
               MONTH28,
               MONTH29,
               MONTH30,
               MONTH31,
               MONTH32,
               MONTH33,
               MONTH34,
               MONTH35,
               MONTH36
          from SLICE_RESULT t
         where version = #{version, jdbcType=VARCHAR}
               and t.entity_name != 'IGNORE'
        order by decode(CLUSTER_NAME, 'China_Total', '0',
                                      'LV', '1',
                                      'MV', '2',
                                      'EE', '3',
                                      'H&amp;D', '31',
                                      'Trading', '4',
                                      'CNDC_FS', '5',
                                      'CNDC_FS_PS', '6',
                                      'CNDC_PEC', '7',
                                      'CNDC_Others', '8',
                                      'China_Other_OPS', '9',
                                      'China_DC', '91'),
         decode(ENTITY_NAME,  'LV', 'ZZA',
                                  'MV', 'ZZB',
                                  'EE', 'ZZC',
                                  'H&amp;D', 'ZZD',
                                  'Trading', 'ZZE',
                                  'CNDC_FS', 'ZZF',
                                  'CNDC_FS_PS', 'ZZG',
                                  'CNDC_PEC', 'ZZH',
                                  'CNDC_Others', 'ZZI',
                                  'China_Other_OPS', 'ZZJ',
                                  'China_DC', 'ZZK',
                                  'China_Total', 'ZZL', ENTITY_NAME),
         PLANT_TYPE,
         decode(class,  'Provision', '1',
                        'SOH_FG', '2',
                        'SOH_RM', '3',
                        'SOH_MB', '4',
                        'SOH', '4',
                        'GIT', '5',
                        'WIP1', '6',
                        'WIP2', '7',
                        'Gross Inventory', '8',
                        'COGS', '9',
                        'Net Inv', '91',
                        'Gross_COGS', '92',
                        'COGS convert%', '93',
                        'Spot DIN_Net Inv', '94',
                        'Spot DIN_Gross Inv', '95',
                        'Yearly DIN_Net Inv', '96',
                        'Yearly DIN_Gross Inv', '97')
    </select>

    <select id="queryTodaySOH" resultType="java.util.Map">
        SELECT CLUSTER_NAME,
               ENTITY AS ENTITY_NAME,
               DECODE(INVENTORY_CATEGORY, 'WIP', 'WIP1', 'FG', 'SOH_FG', 'RM', 'SOH_RM', INVENTORY_CATEGORY) AS INVENTORY_CATEGORY,
               DECODE(PLANT_TYPE, 'FAC', 'Plant', PLANT_TYPE) PLANT_TYPE,
               STOCK_VALUE / 1000000 AS STOCK_VALUE
          FROM (
                SELECT T.CLUSTER_NAME,
                       T.ENTITY,
                       T.INVENTORY_CATEGORY,
                       T.PLANT_TYPE,
                       SUM(DECODE(T.INVENTORY_CATEGORY, 'WIP', T.WIP_VALUE,
                                 (T.UU_STOCK + T.STOCK_IN_QI + T.RESTRICTED_STOCK + T.BLOCKED_STOCK + T.RETURNS_STOCK) * T.UNIT_COST)) STOCK_VALUE
                FROM SCPA.INVENTORY_STRUCTURE_V T
                WHERE T.CLUSTER_NAME IS NOT NULL
                  AND T.INVENTORY_CATEGORY IN ('WIP', 'FG', 'RM')
                GROUP BY T.CLUSTER_NAME, T.ENTITY, T.INVENTORY_CATEGORY, T.PLANT_TYPE

                UNION ALL

                SELECT T.CLUSTER_NAME,
                       T.ENTITY,
                       'Gross Inventory',
                       NVL(T.PLANT_TYPE, 'FAC'),
                       SUM(nvl(UU_STOCK_VALUE, 0) + nvl(STOCK_IN_QI_VALUE, 0) + nvl(RESTRICTED_STOCK_VALUE, 0) + nvl(BLOCKED_STOCK_VALUE, 0) +
                           nvl(RETURNS_STOCK_VALUE, 0) + nvl(WIP_VALUE, 0) + nvl(GIT_VALUE, 0)) STOCK_VALUE
                FROM SCPA.INVENTORY_STRUCTURE_V T
                WHERE T.CLUSTER_NAME IS NOT NULL
                  AND (SPECIAL_ST IN ('W', 'E', 'O', 'P', 'Q', 'V', 'M', 'Y') OR SPECIAL_ST IS NULL OR SPECIAL_ST = 'Others')
                  AND MATERIAL_TYPE NOT IN ('ZMNT-MAINTAINENCE')
                GROUP BY T.CLUSTER_NAME, T.ENTITY, NVL(T.PLANT_TYPE, 'FAC')

                UNION ALL

                SELECT 'SPC',
                       'SPC',
                       T.INVENTORY_CATEGORY,
                       T.PLANT_TYPE,
                       SUM(DECODE(T.INVENTORY_CATEGORY, 'WIP', T.WIP_VALUE,
                                 (T.UU_STOCK + T.STOCK_IN_QI + T.RESTRICTED_STOCK + T.BLOCKED_STOCK + T.RETURNS_STOCK) * T.UNIT_COST)) STOCK_VALUE
                FROM SCPA.INVENTORY_STRUCTURE_V T
                WHERE T.CLUSTER_NAME IS NOT NULL
                  AND T.INVENTORY_CATEGORY IN ('WIP', 'FG', 'RM')
                 AND T.PLANT_CODE IN ('SP01', 'SP02')
                GROUP BY T.CLUSTER_NAME, T.ENTITY, T.INVENTORY_CATEGORY, T.PLANT_TYPE

                UNION ALL

                SELECT 'SPC',
                       'SPC',
                       'Gross Inventory',
                       NVL(T.PLANT_TYPE, 'FAC'),
                       SUM(nvl(UU_STOCK_VALUE, 0) + nvl(STOCK_IN_QI_VALUE, 0) + nvl(RESTRICTED_STOCK_VALUE, 0) + nvl(BLOCKED_STOCK_VALUE, 0) +
                           nvl(RETURNS_STOCK_VALUE, 0) + nvl(WIP_VALUE, 0) + nvl(GIT_VALUE, 0)) STOCK_VALUE
                FROM SCPA.INVENTORY_STRUCTURE_V T
                WHERE T.CLUSTER_NAME IS NOT NULL
                  AND (SPECIAL_ST IN ('W', 'E', 'O', 'P', 'Q', 'V', 'M', 'Y') OR SPECIAL_ST IS NULL OR SPECIAL_ST = 'Others')
                  AND MATERIAL_TYPE NOT IN ('ZMNT-MAINTAINENCE')
                  AND T.PLANT_CODE IN ('SP01', 'SP02')
                GROUP BY NVL(T.PLANT_TYPE, 'FAC')
        ) MM
    </select>

    <select id="queryTodayGIT" resultType="java.util.Map">
        SELECT CLUSTER_NAME,
               ENTITY_NAME,
               INVENTORY_CATEGORY,
               PLANT_TYPE,
               STOCK_VALUE_LAST_MONTH / 1000000 AS STOCK_VALUE_LAST_MONTH,
               STOCK_VALUE_THIS_MONTH / 1000000 AS STOCK_VALUE_THIS_MONTH
          FROM (SELECT T.CLUSTER_NAME,
                       T.ENTITY AS ENTITY_NAME,
                       T.INVENTORY_CATEGORY,
                       DECODE(T.PLANT_TYPE, 'FAC', 'Plant', T.PLANT_TYPE) PLANT_TYPE,
                       SUM(DECODE(t.date$, to_date(#{lastMonth, jdbcType=VARCHAR}, 'yyyy/mm/dd'),
                           DECODE(T.INVENTORY_CATEGORY, 'GIT', T.GIT_VALUE, (T.UU_STOCK + T.STOCK_IN_QI + T.RESTRICTED_STOCK + T.BLOCKED_STOCK + T.RETURNS_STOCK) * T.UNIT_COST), 0)) STOCK_VALUE_LAST_MONTH,
                       SUM(DECODE(t.date$, to_date(#{thisMonth, jdbcType=VARCHAR}, 'yyyy/mm/dd'),
                           DECODE(T.INVENTORY_CATEGORY, 'GIT', T.GIT_VALUE, (T.UU_STOCK + T.STOCK_IN_QI + T.RESTRICTED_STOCK + T.BLOCKED_STOCK + T.RETURNS_STOCK) * T.UNIT_COST), 0)) STOCK_VALUE_THIS_MONTH
                FROM SCPA.INVENTORY_STRUCTURE_HIST T
                WHERE T.CLUSTER_NAME IS NOT NULL
                  AND T.INVENTORY_CATEGORY = 'GIT'
                  and t.date$ in (to_date(#{lastMonth, jdbcType=VARCHAR}, 'yyyy/mm/dd'), to_date(#{thisMonth, jdbcType=VARCHAR}, 'yyyy/mm/dd'))
                GROUP BY T.CLUSTER_NAME, T.ENTITY, T.INVENTORY_CATEGORY, T.PLANT_TYPE

                UNION ALL

                SELECT 'SPC',
                       'SPC' AS ENTITY_NAME,
                       T.INVENTORY_CATEGORY,
                       DECODE(T.PLANT_TYPE, 'FAC', 'Plant', T.PLANT_TYPE) PLANT_TYPE,
                       SUM(DECODE(t.date$, to_date(#{lastMonth, jdbcType=VARCHAR}, 'yyyy/mm/dd'),
                           DECODE(T.INVENTORY_CATEGORY, 'GIT', T.GIT_VALUE, (T.UU_STOCK + T.STOCK_IN_QI + T.RESTRICTED_STOCK + T.BLOCKED_STOCK + T.RETURNS_STOCK) * T.UNIT_COST), 0)) STOCK_VALUE_LAST_MONTH,
                       SUM(DECODE(t.date$, to_date(#{thisMonth, jdbcType=VARCHAR}, 'yyyy/mm/dd'),
                           DECODE(T.INVENTORY_CATEGORY, 'GIT', T.GIT_VALUE, (T.UU_STOCK + T.STOCK_IN_QI + T.RESTRICTED_STOCK + T.BLOCKED_STOCK + T.RETURNS_STOCK) * T.UNIT_COST), 0)) STOCK_VALUE_THIS_MONTH
                FROM SCPA.INVENTORY_STRUCTURE_HIST T
                WHERE T.CLUSTER_NAME IS NOT NULL
                  AND T.INVENTORY_CATEGORY = 'GIT'
                  AND T.PLANT_CODE IN ('SP01', 'SP02')
                  AND t.date$ in (to_date(#{lastMonth, jdbcType=VARCHAR}, 'yyyy/mm/dd'), to_date(#{thisMonth, jdbcType=VARCHAR}, 'yyyy/mm/dd'))
                GROUP BY T.INVENTORY_CATEGORY, T.PLANT_TYPE) TT
    </select>

    <select id="queryProjection" resultType="com.scp.inventory.bean.SliceRawData">
        select NVL(T2.CLUSTER_NAME, T.ENTITY)  AS CLUSTER_NAME,
               T.ENTITY                        AS ENTITY_NAME,
               PLANT_TYPE,
               CLASS                           AS "CLAZZ",
               CURRENCY,
               decode(t.CLASS, 'COGS', 'COGS', 'Gross_COGS', 'COGS', 'INV') AS CURRENCY_TYPE,
               RCA_CATEGORY,
               RCA_CODE,
               MONTH01,
               MONTH02,
               MONTH03,
               MONTH04,
               MONTH05,
               MONTH06,
               MONTH07,
               MONTH08,
               MONTH09,
               MONTH10,
               MONTH11,
               MONTH12,
               MONTH13,
               MONTH14,
               MONTH15,
               MONTH16,
               MONTH17,
               MONTH18,
               MONTH19,
               MONTH20,
               MONTH21,
               MONTH22,
               MONTH23,
               MONTH24,
               MONTH25,
               'Y' AS PROJECTION
        from SLICE_PROJECTION t
                 LEFT JOIN MR3_ENTITY_INFORMATION T2 ON T.ENTITY = T2.ENTITY_NAME
         where version = #{version, jdbcType=VARCHAR}
         <choose>
            <when test="type == 'Projection'.toString()">
                and (t.RCA_CATEGORY in ('Positive', 'Negative') or t.RCA_CATEGORY is null)
            </when>
            <otherwise>
                and t.RCA_CATEGORY = 'Target'
            </otherwise>
        </choose>
    </select>

    <select id="querySEChinaProvision" resultType="java.util.Map">
        SELECT CURRENCY_TYPE,
               CURRENCY,
               "M01_Y-1" AS MONTH01,
               "M02_Y-1" AS MONTH02,
               "M03_Y-1" AS MONTH03,
               "M04_Y-1" AS MONTH04,
               "M05_Y-1" AS MONTH05,
               "M06_Y-1" AS MONTH06,
               "M07_Y-1" AS MONTH07,
               "M08_Y-1" AS MONTH08,
               "M09_Y-1" AS MONTH09,
               "M10_Y-1" AS MONTH10,
               "M11_Y-1" AS MONTH11,
               "M12_Y-1" AS MONTH12,
               "M01_Y-0" AS MONTH13,
               "M02_Y-0" AS MONTH14,
               "M03_Y-0" AS MONTH15,
               "M04_Y-0" AS MONTH16,
               "M05_Y-0" AS MONTH17,
               "M06_Y-0" AS MONTH18,
               "M07_Y-0" AS MONTH19,
               "M08_Y-0" AS MONTH20,
               "M09_Y-0" AS MONTH21,
               "M10_Y-0" AS MONTH22,
               "M11_Y-0" AS MONTH23,
               "M12_Y-0" AS MONTH24
         FROM SLICE_RAW_DATA_V T WHERE T.VERSION = #{version, jdbcType=VARCHAR} AND T.HFM_CODE = 'SE-China' AND T.CLASS = 'Provision'
    </select>

    <sql id="queryReport2SQL">
       select ROWIDTOCHAR(t.rowid) row_id,
              t.*,
              CASE WHEN UMD.USER_NAME IS NULL THEN t.CREATE_BY$ ELSE UMD.USER_NAME || ' [' || UMD.EMAIL || ']' END as CREATE_BY,
              TO_CHAR(t.UPDATE_DATE$, 'yyyy/mm/dd HH24:mi:ss') as UPDATE_TIME
         from SLICE_PROJECTION t left join SY_USER_MASTER_DATA UMD on t.CREATE_BY$ = UMD.SESA_CODE
        where t.VERSION = #{version, jdbcType=VARCHAR}
        <if test="filters != null and filters != ''.toString()">
            and ${filters}
        </if>
        <choose>
            <when test="type == 'Projection'.toString()">
                and (t.RCA_CATEGORY in ('Positive', 'Negative') or t.RCA_CATEGORY is null)
            </when>
            <otherwise>
                and t.RCA_CATEGORY = 'Target'
            </otherwise>
        </choose>
    </sql>

    <select id="queryReport2Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport2SQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport2" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport2SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <insert id="createReport2ByTable">
        insert into SLICE_PROJECTION
        (VERSION,
        <foreach collection="headers" item="header" separator=",">
            ${header}
        </foreach>, create_by$, create_date$
        )
        <foreach collection="creates" item="list" separator=" union all ">
            select #{version, jdbcType=VARCHAR},
            <foreach collection="headers" item="header" separator=",">
                <choose>
                    <when test="header.indexOf('MONTH') != -1 ">
                        #{list.${header}, jdbcType=DOUBLE}
                    </when>
                    <otherwise>
                        #{list.${header}, jdbcType=VARCHAR}
                    </otherwise>
                </choose>
            </foreach>, #{userid,jdbcType=VARCHAR}, sysdate
            from dual
        </foreach>
    </insert>

    <delete id="deleteReport2ByTable">
        delete from SLICE_PROJECTION where rowid in
        <foreach collection="deletes" open="(" close=")" separator="," item="item">#{item, jdbcType=VARCHAR}</foreach>
        and version = #{version, jdbcType=VARCHAR}
        <if test="isAdmin == false">
            AND CREATE_BY$ = #{userid, jdbcType=VARCHAR}
        </if>
    </delete>

    <update id="updateReport2ByTable">
        update SLICE_PROJECTION
        SET
        <foreach collection="updates" item="col" separator=",">
            ${col.key} = #{col.value,jdbcType=VARCHAR}
        </foreach>,
        update_by$ = #{userid,jdbcType=VARCHAR},
        update_date$ = sysdate
        where rowid = #{rowid,jdbcType=VARCHAR}
              and version = #{version, jdbcType=VARCHAR}
              <if test="isAdmin == false">
                AND CREATE_BY$ = #{userid, jdbcType=VARCHAR}
              </if>
    </update>

    <update id="refreshFilterMV">
        begin
            DBMS_MVIEW.REFRESH(list =>'SLICE_FILTER_V', Method =>'C', refresh_after_errors => false, atomic_refresh => false, out_of_place => true);
        end;
    </update>

    <update id="syncProjection">
        begin
            delete from SLICE_PROJECTION where VERSION = #{thisMonth, jdbcType=VARCHAR};
            insert into SLICE_PROJECTION
            (VERSION, ENTITY, PLANT_TYPE, RCA_CATEGORY, RCA_CODE, CLASS, CURRENCY, MONTH01, MONTH02, MONTH03, MONTH04, MONTH05, MONTH06, MONTH07, MONTH08, MONTH09, MONTH10, MONTH11, MONTH12,
             MONTH13, MONTH14, MONTH15, MONTH16, MONTH17, MONTH18, MONTH19, MONTH20, MONTH21, MONTH22, MONTH23, MONTH24, MONTH25, CREATE_BY$, CREATE_DATE$, UPDATE_BY$, UPDATE_DATE$)

            select #{thisMonth, jdbcType=VARCHAR},
                   ENTITY,
                   PLANT_TYPE,
                   RCA_CATEGORY,
                   RCA_CODE,
                   CLASS,
                   CURRENCY,
                   MONTH02,
                   MONTH03,
                   MONTH04,
                   MONTH05,
                   MONTH06,
                   MONTH07,
                   MONTH08,
                   MONTH09,
                   MONTH10,
                   MONTH11,
                   MONTH12,
                   MONTH13,
                   MONTH14,
                   MONTH15,
                   MONTH16,
                   MONTH17,
                   MONTH18,
                   MONTH19,
                   MONTH20,
                   MONTH21,
                   MONTH22,
                   MONTH23,
                   MONTH24,
                   MONTH25,
                   null,
                   CREATE_BY$,
                   sysdate,
                   #{createBy, jdbcType=VARCHAR},
                   sysdate
            from SLICE_PROJECTION
            where VERSION = #{lastMonth, jdbcType=VARCHAR};
        end;
    </update>

    <select id="downloadDinReport" resultType="java.util.LinkedHashMap">
        WITH SLICE_SOURCE_BASE AS (SELECT HFM_CODE, CLASS, VARIABLE, VALUE AS VAL
                           FROM SCPL.IM2_SLICE_SOURCE
                           WHERE VERSION = #{version, jdbcType=VARCHAR}
                             AND CLASS IN ('COGS', 'Gross Inventory')),
             SLICE_SOURCE AS (SELECT HFM_CODE, CLASS, VARIABLE, VAL
                              FROM SLICE_SOURCE_BASE
                              UNION ALL
                              SELECT 'China_Trading_Total', CLASS, VARIABLE, SUM(VAL) VAL
                              FROM SLICE_SOURCE_BASE T
                              WHERE T.HFM_CODE IN ('Trading_DEC', 'Trading_IAC', 'Trading_HDC', 'Trading_LVC')
                              GROUP BY CLASS, VARIABLE
                              UNION ALL

                              SELECT 'Trading_Total', CLASS, VARIABLE, SUM(VAL) VAL
                              FROM SLICE_SOURCE_BASE T
                              WHERE T.HFM_CODE IN ('Trading_DEC', 'Trading_IAC', 'Trading_HDC', 'Trading_LVC', 'Mini DC')
                              GROUP BY CLASS, VARIABLE

                              UNION ALL

                              SELECT 'VMI_Plant', CLASS, VARIABLE, SUM(VAL) VAL
                              FROM SLICE_SOURCE_BASE T
                              WHERE T.HFM_CODE IN ('SSIC', 'SELV', 'SBMLV', 'SSLVTA', 'WPF', 'SEMW', 'SWD', 'SEMC', 'SFCL', 'SSAP')
                              GROUP BY CLASS, VARIABLE

                              UNION ALL

                              SELECT 'CNDC_Total', CLASS, VARIABLE, SUM(VAL) VAL
                              FROM SLICE_SOURCE_BASE T
                              WHERE T.HFM_CODE IN ('CNDC_FS_PS', 'CNDC_PEC', 'CNDC_FS', 'CNDC_Others', 'CNDC_FS_ENG')
                              GROUP BY CLASS, VARIABLE

                              UNION ALL

                              SELECT 'SE_China', CLASS, VARIABLE, SUM(VAL) VAL
                              FROM SLICE_SOURCE_BASE T
                              WHERE T.HFM_CODE IN ('SSIC', 'SELV', 'SBMLV', 'SSLVTA', 'WPF', 'SEMW', 'SWD', 'SEMC', 'SFCL', 'SSAP')
                                 OR T.HFM_CODE IN ('CNDC_FS_PS', 'CNDC_PEC', 'CNDC_FS', 'CNDC_Others', 'CNDC_FS_ENG')
                                 OR T.HFM_CODE IN ('Trading_DEC', 'Trading_IAC', 'Trading_HDC', 'Trading_LVC', 'Mini DC')
                              GROUP BY CLASS, VARIABLE),

             ENTITY AS (SELECT 'SSIC' AS ENTITY FROM DUAL
                        UNION ALL
                        SELECT 'SELV' FROM DUAL
                        UNION ALL
                        SELECT 'SBMLV' FROM DUAL
                        UNION ALL
                        SELECT 'SSLVTA' FROM DUAL
                        UNION ALL
                        SELECT 'WPF' FROM DUAL
                        UNION ALL
                        SELECT 'SEMW' FROM DUAL
                        UNION ALL
                        SELECT 'SWD' FROM DUAL
                        UNION ALL
                        SELECT 'SEMC' FROM DUAL
                        UNION ALL
                        SELECT 'SFCL' FROM DUAL
                        UNION ALL
                        SELECT 'SSAP' FROM DUAL
                        UNION ALL
                        SELECT 'VMI_Plant' FROM DUAL
                        UNION ALL
                        SELECT 'Trading_DEC' FROM DUAL
                        UNION ALL
                        SELECT 'Trading_IAC' FROM DUAL
                        UNION ALL
                        SELECT 'Trading_HDC' FROM DUAL
                        UNION ALL
                        SELECT 'Trading_LVC' FROM DUAL
                        UNION ALL
                        SELECT 'China_Trading_Total' FROM DUAL
                        UNION ALL
                        SELECT 'Mini_DC' FROM DUAL
                        UNION ALL
                        SELECT 'Trading_Total' FROM DUAL
                        UNION ALL
                        SELECT 'CNDC_FS_PS' FROM DUAL
                        UNION ALL
                        SELECT 'CNDC_PEC' FROM DUAL
                        UNION ALL
                        SELECT 'CNDC_FS' FROM DUAL
                        UNION ALL
                        SELECT 'CNDC_Others' FROM DUAL
                        UNION ALL
                        SELECT 'CNDC_FS_ENG' FROM DUAL
                        UNION ALL
                        SELECT 'CNDC_Total' FROM DUAL
                        UNION ALL
                        SELECT 'SE_China' FROM DUAL),
             CATEGORY AS (SELECT 'Gross Inventory' AS CATEGORY FROM DUAL
                          UNION ALL
                          SELECT 'COGS' FROM DUAL
                          UNION ALL
                          SELECT 'DIN in Days(Spot)' FROM DUAL
                          UNION ALL
                          SELECT 'DIN in Days(Yearly)' FROM DUAL),
             JOIN_BASE AS (SELECT * FROM ENTITY CROSS JOIN CATEGORY),
             COGS AS (SELECT t.HFM_CODE, t.CLASS, round(t.VAL, 1) VAL
                      FROM SLICE_SOURCE T
                      where t.VARIABLE = #{variable, jdbcType=VARCHAR}
                        and t.CLASS IN ('COGS')),
             INV AS (SELECT t.HFM_CODE, t.CLASS, round(t.VAL, 1) VAL
                     FROM SLICE_SOURCE T
                     where t.VARIABLE = #{variable, jdbcType=VARCHAR}
                       and t.CLASS IN ('Gross Inventory')),
             SPOT_DIN_SOURCE AS (SELECT T.HFM_CODE,
                                        AVG(CASE WHEN T.VARIABLE = #{variable, jdbcType=VARCHAR} AND T.CLASS = 'Gross Inventory' THEN T.VAL END) AS GRO_INV,
                                        AVG(CASE WHEN T.CLASS = 'COGS' THEN T.VAL END)                                      AS AVG_COGS
                                 FROM SLICE_SOURCE T
                                 WHERE T.VARIABLE IN (${spotList})
                                   and t.CLASS IN ('COGS', 'Gross Inventory')
                                 GROUP BY t.HFM_CODE),
             SPOT_DIN AS (SELECT T.GRO_INV, T.AVG_COGS, T.HFM_CODE, ROUND(DECODE(T.AVG_COGS, 0, 0, GRO_INV / T.AVG_COGS * 30), 2) AS SPOT_DIN FROM SPOT_DIN_SOURCE T),
             YEALY_DIN_SOURCE AS (SELECT T.HFM_CODE,
                                         AVG(CASE WHEN T.CLASS = 'Gross Inventory' THEN T.VAL END) AS AVG_GRO_INV,
                                         AVG(CASE WHEN T.CLASS = 'COGS' THEN T.VAL END)            AS AVG_COGS
                                  FROM SLICE_SOURCE T
                                  WHERE T.VARIABLE IN (${yearlyList})
                                    and t.CLASS IN ('COGS', 'Gross Inventory')
                                  GROUP BY t.HFM_CODE),
             YEALY_DIN AS (SELECT T.AVG_GRO_INV, T.AVG_COGS, T.HFM_CODE, ROUND(DECODE(T.AVG_COGS, 0, 0, AVG_GRO_INV / T.AVG_COGS * 30), 2) AS YEALY_DIN FROM YEALY_DIN_SOURCE T),
             VAL_BASE AS (SELECT INV.HFM_CODE, 'Gross Inventory' AS CLASS, INV.VAL
                          FROM INV
                          UNION ALL
                          SELECT COGS.HFM_CODE, 'COGS', COGS.VAL
                          FROM COGS
                          UNION ALL
                          SELECT SPOT_DIN.HFM_CODE, 'DIN in Days(Spot)', SPOT_DIN.SPOT_DIN
                          FROM SPOT_DIN
                          UNION ALL
                          SELECT YEALY_DIN.HFM_CODE, 'DIN in Days(Yearly)', YEALY_DIN.YEALY_DIN
                          FROM YEALY_DIN)
        SELECT T.ENTITY, T.CATEGORY, NVL(T2.VAL, 0) AS "${variable}"
        FROM JOIN_BASE T LEFT JOIN VAL_BASE T2 ON T.ENTITY = T2.HFM_CODE AND T.CATEGORY = T2.CLASS
        ORDER BY DECODE(
                         T.ENTITY, 'SSIC', 1,
                         'SELV', 2,
                         'SBMLV', 3,
                         'SSLVTA', 4,
                         'WPF', 5,
                         'SEMW', 6,
                         'SWD', 7,
                         'SEMC', 8,
                         'SFCL', 9,
                         'SSAP', 10,
                         'VMI_Plant', 11,
                         'Trading_DEC', 12,
                         'Trading_IAC', 13,
                         'Trading_HDC', 14,
                         'Trading_LVC', 15,
                         'China_Trading_Total', 16,
                         'Mini_DC', 17,
                         'Trading_Total', 18,
                         'CNDC_FS_PS', 19,
                         'CNDC_PEC', 20,
                         'CNDC_FS', 21,
                         'CNDC_Others', 22,
                         'CNDC_FS_ENG', 23,
                         'CNDC_Total', 24,
                         'SE_China', 25
                     ),
                 DECODE(
                         T.CATEGORY, 'Gross Inventory', 1,
                         'COGS', 2,
                         'DIN in Days(Spot)', 3,
                         'DIN in Days(Yearly)', 4
                 )
    </select>

    <delete id="deleteSliceReportCalculated">
        BEGIN
            DELETE FROM SLICE_REPORT_CALCULATED T WHERE T.VERSION = #{version,jdbcType=VARCHAR};
            COMMIT;
        END;
    </delete>

    <insert id="saveSliceReportCalculated">
        INSERT /*+  IGNORE_ROW_ON_DUPKEY_INDEX (SLICE_REPORT_CALCULATED, SLICE_REPORT_CALCULATED_PK) */
        INTO SLICE_REPORT_CALCULATED
        (VERSION, CLUSTER_NAME, ENTITY_NAME,
        PLANT_TYPE, CLASS, UNIT_OF_MEASURE,
        CURRENCY_FROM, TO_EUR_CURRENCY, TO_HKD_CURRENCY,
        CURRENCY_TYPE, MONTH_YEAR, ACTUAL_VAL, PROJECTION_VAL, TARGET_VAL, UPDATE_DATE$)
        <foreach collection="list" item="item" separator=" union all ">
            SELECT #{item.VERSION, jdbcType=VARCHAR}, #{item.CLUSTER_NAME, jdbcType=VARCHAR}, #{item.ENTITY_NAME, jdbcType=VARCHAR},
            #{item.PLANT_TYPE, jdbcType=VARCHAR}, #{item.CLASS, jdbcType=VARCHAR} , #{item.UNIT_OF_MEASURE, jdbcType=VARCHAR},
            #{item.CURRENCY_FROM, jdbcType=VARCHAR}, #{item.TO_EUR_CURRENCY, jdbcType=NUMERIC} , #{item.TO_HKD_CURRENCY, jdbcType=NUMERIC},
            #{item.CURRENCY_TYPE, jdbcType=VARCHAR}, #{item.MONTH_YEAR, jdbcType=VARCHAR} , #{item.ACTUAL_VAL, jdbcType=NUMERIC},
            #{item.PROJECTION_VAL, jdbcType=NUMERIC}, #{item.TARGET_VAL, jdbcType=NUMERIC}, SYSDATE
            FROM DUAL
        </foreach>
    </insert>

    <select id="querySliceTarget" resultType="java.util.HashMap">
        SELECT * FROM SCPA.IM3_SLICE_TARGET_V T
         WHERE T.MONTH_YEAR LIKE TO_CHAR(SYSDATE, 'YYYY') || '%'
         AND T.CLASS IN ('Final target Net Inv','Final target Gross Inv','Final target DIN12')
    </select>
</mapper>
