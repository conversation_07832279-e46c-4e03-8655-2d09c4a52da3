<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.inventory.dao.IExcessSOStockDao">

    <sql id="ExcessFilter">
        <if test="_filters != null and _filters != ''.toString()">
            and ${_filters}
        </if>
        <if test="treePathFilter != null and treePathFilter != ''.toString()">
            and ${treePathFilter}
        </if>
    </sql>

    <resultMap id="report1ResultMap" type="com.scp.inventory.bean.ExcessSOStockReport1Bean">
        <result property="category1" column="CATEGORY1"/>
        <result property="category2" column="CATEGORY2"/>
        <result property="category3" column="CATEGORY3"/>
        <result property="category4" column="CATEGORY4"/>
        <result property="category5" column="CATEGORY5"/>
        <result property="value" column="value"/>
        <association property="tooltips" javaType="com.scp.inventory.bean.ExcessSOStockReport1Tooltips">
            <result property="BAD_INDENT_QTY" column="BAD_INDENT_QTY"/>
            <result property="BAD_INDENT_VALUE" column="BAD_INDENT_VALUE"/>
            <result property="OPEN_SO_QTY" column="OPEN_SO_QTY"/>
            <result property="STOCK_PRICE" column="STOCK_PRICE"/>
            <result property="UNIT_COST" column="UNIT_COST"/>
        </association>
    </resultMap>

    <select id="queryCascader" resultType="java.util.Map">
        SELECT NAME,
               CATEGORY
        FROM BAD_INDENT_FILTER_V T
        ORDER BY CATEGORY
    </select>

    <select id="queryReport1" resultMap="report1ResultMap">
        WITH BASE AS (SELECT * FROM ${SCPA.BAD_INDENT_V})
        SELECT NVL(${level1}, 'Others') AS CATEGORY1,
               NVL(${level2}, 'Others') AS CATEGORY2,
               NVL(${level3}, 'Others') AS CATEGORY3,
               <if test="level4 != null and level4 != ''.toString()">
                   NVL(${level4}, 'Others') AS CATEGORY4,
               </if>
               <if test="level5 != null and level5 != ''.toString()">
                   NVL(${level5}, 'Others') AS CATEGORY5,
               </if>
               ${valueColumn} AS VALUE
               <if test="tooltipsColumns != null and tooltipsColumns != ''.toString()">
                   ,${tooltipsColumns}
               </if>
        FROM BASE T
        <where>
            <include refid="ExcessFilter"/>
        </where>
        GROUP BY ${level1},
                 ${level2},
                 ${level3}
                 <if test="level4 != null and level4 != ''.toString()">,${level4}</if>
                 <if test="level5 != null and level5 != ''.toString()">,${level5}</if>
    </select>

    <resultMap id="report2ResultMap" type="com.scp.inventory.bean.ExcessSOStockReport2Bean">
        <result property="CALENDAR_DATE" column="CALENDAR_DATE"/>
        <result property="NAME" column="NAME"/>
        <result property="VALUE" column="VALUE"/>
    </resultMap>

    <select id="queryReport2" parameterType="java.util.Map" resultMap="report2ResultMap">
        SELECT
            TO_CHAR(T.DATE$, 'YYYY/MM/DD') AS CALENDAR_DATE,
            NVL(${report2ViewType}, 'Others') AS NAME,
            ROUND(${valueColumn}, 2) AS VALUE
        FROM ${SCPA.BAD_INDENT_HIST} T
        LEFT JOIN SY_CALENDAR CALENDAR ON T.DATE$ = CALENDAR.DATE$ AND CALENDAR.NAME = 'National Holidays'
        WHERE T.DATE$ BETWEEN TO_DATE(#{report2DateRange[0], jdbcType=VARCHAR}, 'YYYY/MM/DD')
                      AND TO_DATE(#{report2DateRange[1], jdbcType=VARCHAR}, 'YYYY/MM/DD')
              <include refid="ExcessFilter"/>
              <choose>
                  <when test='report2SelectedType == "VIEW_BY_DAY".toString()'/>
                  <when test='report2SelectedType == "VIEW_BY_WEEK".toString()'>
                      AND TO_CHAR(T.DATE$, 'D') = 7
                  </when>
                  <when test='report2SelectedType == "VIEW_BY_MONTH".toString()'>
                      AND TO_CHAR(T.DATE$, 'DD') = 1
                  </when>
                  <when test='report2SelectedType == "VIEW_BY_QUARTER".toString()'>
                      AND TO_CHAR(T.DATE$, 'MM-DD') IN ('01-01', '04-01', '07-01', '10-01')
                  </when>
                  <when test='report2SelectedType == "VIEW_BY_YEAR".toString()'>
                      AND TO_CHAR(T.DATE$, 'MM-DD') = '01-01'
                  </when>
                  <otherwise>
                      AND TO_CHAR(T.DATE$, 'DD') = 1
                  </otherwise>
              </choose>
        GROUP BY
            T.DATE$,
            NVL(${report2ViewType}, 'Others')
        ORDER BY NVL(${report2ViewType}, 'Others')
    </select>

    <sql id="report2DetailsSQL">
        SELECT * FROM ${SCPA.BAD_INDENT_HIST} T
        LEFT JOIN SY_CALENDAR CALENDAR ON T.DATE$ = CALENDAR.DATE$ AND CALENDAR.NAME = 'National Holidays'
        WHERE T.DATE$ = TO_DATE(#{report2SelectedValue, jdbcType=VARCHAR}, 'yyyy/mm/dd')
        <include refid="ExcessFilter"/>
    </sql>

    <select id="queryReport2DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="report2DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport2Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report2DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="report3SQL">
        WITH BAD_INDENT_BASE AS (
                    SELECT * FROM ${SCPA.BAD_INDENT_HIST} T
                    <where>
                        T.DATE$ BETWEEN TO_DATE(#{report3DateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                        AND TO_DATE(#{report3DateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                        <include refid="ExcessFilter"/>
                    </where>),
            BASE AS (
                    SELECT
                        <foreach collection="report3SelectedColumns" item="item">
                                NVL(${item}, 'Others')               AS ${item},
                        </foreach>
                        TO_CHAR(T.DATE$, 'yyyy/mm/dd')       AS CALENDAR_DATE,
                        ROUND(${valueColumn}, 3)                     AS VALUE
                    FROM BAD_INDENT_BASE t
                    LEFT JOIN SY_CALENDAR CALENDAR ON T.DATE$ = CALENDAR.DATE$ AND CALENDAR.NAME = 'National Holidays'
                    GROUP BY
                        <foreach collection="report3SelectedColumns" item="item">
                            ${item},
                        </foreach>
                        T.DATE$)
        SELECT * FROM
            ( SELECT <foreach collection="report3SelectedColumns" item="item">
                          NVL(${item}, 'Others')         AS ${item},
                     </foreach>
                     NVL(T.CALENDAR_DATE, 'Others') AS CALENDAR_DATE,
                     NVL(T.VALUE, 0) AS TOTAL
                     FROM BASE T
            ) MM
            PIVOT (
                SUM(TOTAL) AS TOTAL
                FOR CALENDAR_DATE
                    IN (
                    <foreach collection="report3ColumnNames" separator="," item="item">
                        '${item}'
                    </foreach>)
            )
        ORDER BY
        <foreach collection="report3SelectedColumns" item="item" separator=",">
            DECODE(${item}, 'Others', 'zzz', ${item})
        </foreach>
    </sql>

    <select id="queryReport3Columns" resultType="java.lang.String">
        SELECT DISTINCT TO_CHAR(t.DATE$, 'YYYY/MM/DD') AS RESULT
        FROM ${SCPA.BAD_INDENT_HIST} t
        WHERE TRUNC(t.DATE$, 'dd') BETWEEN TO_DATE(#{report3DateRange[0], jdbcType=VARCHAR}, 'yyyy-mm-dd')
        AND TO_DATE(#{report3DateRange[1], jdbcType=VARCHAR}, 'yyyy-mm-dd')
        <choose>
            <when test='report3ViewType == "VIEW_BY_DAY".toString()'/>
            <when test='report3ViewType == "VIEW_BY_WEEK".toString()'>
                AND TO_CHAR(T.DATE$, 'D') = 7
            </when>
            <when test='report3ViewType == "VIEW_BY_MONTH".toString()'>
                AND TO_CHAR(T.DATE$, 'DD') = 1
            </when>
            <when test='report3ViewType == "VIEW_BY_QUARTER".toString()'>
                AND TO_CHAR(T.DATE$, 'MM-DD') IN ('01-01', '04-01', '07-01', '10-01')
            </when>
            <when test='report3ViewType == "VIEW_BY_YEAR".toString()'>
                AND TO_CHAR(T.DATE$, 'MM-DD') = '01-01'
            </when>
            <otherwise>
                AND TO_CHAR(T.DATE$, 'DD') = 1
            </otherwise>
        </choose>
        ORDER BY t.DATE$ DESC
        OFFSET 0 ROWS FETCH NEXT 30 ROWS ONLY
    </select>

    <select id="queryReport3" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report3SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="report3DetailsSQL">
        SELECT * FROM
        (SELECT * FROM ${SCPA.BAD_INDENT_HIST} T
           LEFT JOIN SY_CALENDAR CALENDAR ON T.DATE$ = CALENDAR.DATE$ AND CALENDAR.NAME = 'National Holidays'
        <choose>
            <when test="report3SelectedDate != null and report3SelectedDate != ''.toString()">
                WHERE t.DATE$ = TO_DATE(#{report3SelectedDate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
            </when>
            <otherwise>
                WHERE t.DATE$ BETWEEN TO_DATE(#{report3DateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                AND TO_DATE(#{report3DateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
            </otherwise>
        </choose>
        <include refid="ExcessFilter"/>
        <foreach collection="report3SelectedColumns" item="item" index="index">
            <if test="report3SelectedValues[index] != null and report3SelectedValues[index] != ''.toString()">
                <if test="report3SelectedValues[index] == 'Others'">
                    AND t.${item} IS NULL
                </if>
                <if test="report3SelectedValues[index] != 'Others'">
                    AND t.${item} = #{report3SelectedValues[${index}], jdbcType=VARCHAR}
                </if>
            </if>
        </foreach>)
    </sql>

    <select id="queryReport3DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="report3DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport3Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report3DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>
</mapper>
