<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.inventory.dao.IInventoryAgingDao">
    <sql id="aging_filter">
        <if test="_filters != null and _filters != ''.toString()">
            and ${_filters}
        </if>
    </sql>

    <select id="queryAgingCascader" resultType="java.util.Map">
        select * from STOCK_AGING_FILTER_V order by category,decode (name,'Others','zzz',name)
    </select>

    <select id="queryReport1" resultType="java.util.Map">
        SELECT aging_group as "name",
               SUM(${valueColumn}) "value"
            FROM ${SCPA.STOCK_AGING_V} t
           WHERE 1 = 1
           <include refid="aging_filter"/>
        GROUP BY t.aging_group
        <![CDATA[
         ORDER BY decode(aging_group, '<1W', 0, '1W-2W', 1, '2W-3W', 2, '3W-4W', 3, '4W-2M', 4, '2M-3M', 5, '3M-6M', 6, '6M-1Y', 7, '1Y-2Y', 8, '>2Y', 9, 10)
        ]]>
    </select>

    <sql id="queryReport1DetailsSql">
        select *
        from ${SCPA.STOCK_AGING_V} t
        <where>
            <if test="selectedCategory != ''.toString() and selectedCategory != null">
                t.aging_group = #{selectedCategory, jdbcType=VARCHAR}
            </if>
            <include refid="aging_filter"/>
        </where>
    </sql>

    <select id="queryReport1DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1DetailsSql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1DetailsSql"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport2" parameterType="java.util.Map" resultType="java.util.Map">
         SELECT ${reportType} "xAxis",
                SUM(${valueColumn}) "yAxis"
           FROM ${SCPA.STOCK_AGING_V} t
                LEFT JOIN SY_CALENDAR T2
                      ON CASE WHEN T.POSTING_DATE > TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                         THEN t.POSTING_DATE ELSE TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') END = T2.DATE$
                         AND T2.NAME = 'National Holidays'
         WHERE T2.DATE$ BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') AND TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
         <include refid="aging_filter"/>
         GROUP BY ${reportType}
         ORDER BY ${reportType}
    </select>

    <sql id="queryReport2DetailsSql">
        select *
        from ${SCPA.STOCK_AGING_V} t
        <where>
            <if test="report2ViewDetailsType == 'part'.toString()">
                <choose>
                    <when test="report2Type == 'by_month'">
                        to_char(t.posting_date, 'yyyy/mm') = #{selectedCategory, jdbcType=VARCHAR}
                    </when>
                    <when test="report2Type == 'by_day'">
                        to_char(t.posting_date, 'yyyy/mm/dd') = #{selectedCategory, jdbcType=VARCHAR}
                    </when>
                    <when test="report2Type == 'by_week'">
                        t.posting_date in (SELECT T2.DATE$ FROM SY_CALENDAR T2 WHERE T2.YEAR || 'W' || T2.WEEK_NO = #{selectedCategory, jdbcType=VARCHAR} AND T2.NAME = 'National Holidays')
                    </when>
                    <otherwise>
                        0 = 1
                    </otherwise>
                </choose>
            </if>
            <if test="report2ViewDetailsType == 'all'.toString()">
                t.posting_date BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') AND TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
            </if>
            <include refid="aging_filter"/>
        </where>
    </sql>

    <select id="queryReport2DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport2DetailsSql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport2Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport2DetailsSql"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport3" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        select /*+ parallel(4) */ *
            from (
                select <foreach collection="report3SelectedColumn" separator="," item="item">
                            nvl(${item},'Others') ${item}
                       </foreach>,
                       to_char(t.POSTING_DATE, 'yyyymm') month,
                       nvl(sum(${valueColumn}),0)  total
                 from ${SCPA.STOCK_AGING_V} t
                where t.POSTING_DATE between to_date(#{dateRange3[0], jdbcType=VARCHAR}, 'yyyymm') and last_day(to_date(#{dateRange3[1], jdbcType=VARCHAR}, 'yyyymm'))
                <include refid="aging_filter"/>
                group by
                <foreach collection="report3SelectedColumn" separator="," item="item">
                    ${item}
                </foreach>
                , to_char(t.POSTING_DATE, 'yyyymm')
            ) mm
            PIVOT (
                sum(total) total
                FOR month
                IN (
                <foreach collection="report3ColumnNames" separator="," item="item">
                    '${item}'
                </foreach>
                )
            )
        order by
        <foreach collection="report3SelectedColumn" separator="," item="item">
            decode(${item}, 'Others', 'zzz', 'No Owner', 'zzx', ${item})
        </foreach>
        fetch next 1024 rows only
        <include refid="global.select_footer"/>
    </select>

    <sql id="report3DetailsSQL">
        select /*+ parallel(4) */ *
        from ${SCPA.STOCK_AGING_V} t
        where
              <choose>
                  <when test="report3SelectedDate != null and report3SelectedDate != ''.toString()">
                      to_char(t.POSTING_DATE, 'yyyymm') = #{report3SelectedDate, jdbcType=VARCHAR}
                  </when>
                  <otherwise>
                      t.POSTING_DATE between to_date(#{dateRange3[0], jdbcType=VARCHAR}, 'yyyymm') and last_day(to_date(#{dateRange3[1], jdbcType=VARCHAR}, 'yyyymm'))
                  </otherwise>
              </choose>
        <include refid="aging_filter"/>

        <foreach collection="report3SelectedColumn" item="item" index="index">
            <if test="report3SelectedValue[index] != null and report3SelectedValue[index] != 'Total'.toString() and report3SelectedValue[index] != ''.toString()">
                <choose>
                    <when test="report3SelectedValue[index] == 'Others'.toString()">
                        and t.${item} is null
                    </when>
                    <otherwise>
                        and t.${item} = #{report3SelectedValue[${index}],jdbcType=VARCHAR}
                    </otherwise>
                </choose>
            </if>
        </foreach>
    </sql>

    <select id="queryReport3DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="report3DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport3Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report3DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>
</mapper>
