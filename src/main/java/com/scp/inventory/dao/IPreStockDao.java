package com.scp.inventory.dao;

import org.apache.ibatis.annotations.Mapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IPreStockDao {

    List<Map<String, String>> queryCascader();

    List<LinkedHashMap<String, Object>> queryReport1(Map<String, Object> parameterMap);

    int queryReport1Count(Map<String, Object> parameterMap);

    void saveReport1Details(Map<String, Object> parameterMap);
}
