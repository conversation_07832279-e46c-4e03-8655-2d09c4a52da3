package com.scp.inventory.dao;

import com.scp.inventory.bean.UHSReport3Result;
import com.starter.context.bean.scptable.ScpTableCell;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface IInventoryUHSDao {

    List<Map<String, String>> queryUHSCode();

    List<String> queryWeeks();

    List<String> queryWeekColumns(@Param("week") String week, @Param("count") int count);

    List<Map<String, Object>> queryReport1(Map<String, Object> parameterMap);

    String queryReport1LastYearEnd(@Param("lastYear") String lastYear);

    String queryReport1MonthEnd(@Param("thisYear") String thisYear, @Param("weekNo") String weekNo);

    List<Map<String, Object>> queryReport2(Map<String, Object> parameterMap);

    int queryReport1DetailsCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport1Details(Map<String, Object> parameterMap);

    int queryReport3Count(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport3(Map<String, Object> parameterMap);

    int updateReport3Data(@Param("version") String version, @Param("material") String material, @Param("plantCode") String plantCode, @Param("updates") List<ScpTableCell> updates, @Param("userid") String userid);

    int queryReport3Exists(@Param("version") String version, @Param("material") String material, @Param("plantCode") String plantCode);

    int saveReport3Data(@Param("version") String version, @Param("material") String material, @Param("plantCode") String plantCode, @Param("updates") List<ScpTableCell> updates, @Param("userid") String userid);

    void deleteEmptyReport3Result();

    void mergeReport3Data(@Param("list") List<UHSReport3Result> list);

    List<String> queryWeekByRange(Map<String, Object> parameterMap);
}
