package com.scp.inventory.dao;

import com.scp.inventory.bean.Report1DataBean;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IInventoryStructureDao {

    List<Map<String, String>> queryCascader();

    List<String> queryProjectionVersionOpts();

    List<Report1DataBean> queryReport1(Map<String, Object> parameterMap);

    List<Report1DataBean> queryReport1Today(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport2(Map<String, Object> parameterMap);

    Map<String, BigDecimal> queryReport2ProjectionData(Map<String, Object> parameterMap);

    List<String> queryReport3Columns(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3(Map<String, Object> parameterMap);

    int queryReport3DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3Details(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport4(Map<String, Object> parameterMap);

    int queryReport4DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport4Details(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport1Level1Slope(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport1Level2Slope(Map<String, Object> parameterMap);

    List<String> queryPriceReferenceOpts();

    List<Map<String, String>> queryTop20FilterOpts();

    List<Map<String, Object>> queryReport2WithPriceReference(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport2WithPriceReferenceAccountingView(Map<String, Object> parameterMap);
}
