package com.scp.inventory;

import com.scp.customer.service.IPONORService;

import com.scp.inventory.service.IPreStockService;
import com.scp.inventory.service.impl.PreStockServiceImpl;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "inventory/pre_stock", parent = PreStockServiceImpl.PARENT_CODE)
public class PreStockController extends ControllerHelper {

    @Resource
    private IPreStockService preStockService;

    @SchneiderRequestMapping("/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return preStockService.initPage(parameterMap, session.getUserid());
    }


    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return preStockService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping("/download_report1")
    public void downloadReport1(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        preStockService.downloadReport1(parameterMap, response);
    }


    @SchneiderRequestMapping("/save_report1_details")
    public Response saveReport1Details(HttpServletRequest request) {
        super.pageLoad(request);
        return preStockService.saveReport1Details(parameterMap);
    }

}
