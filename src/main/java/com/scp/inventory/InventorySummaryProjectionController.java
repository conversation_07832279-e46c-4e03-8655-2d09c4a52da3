package com.scp.inventory;

import com.scp.inventory.service.IInventorySummaryProjectionService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/inventory/summary_projection", parent = InventorySummaryProjectionController.PARENT_CODE)
public class InventorySummaryProjectionController extends ControllerHelper {

    @Resource
    private IInventorySummaryProjectionService inventorySummaryProjectionService;

    public static final String PARENT_CODE = "menu780";

    @SchneiderRequestMapping("/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return inventorySummaryProjectionService.initPage(parameterMap);
    }

    @SchneiderRequestMapping("/init_personal_settings")
    public Response initPersonalSettings(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return inventorySummaryProjectionService.initPersonalSettings(session.getUserid());
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return inventorySummaryProjectionService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping("/download_report1")
    public void downloadReport1(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        inventorySummaryProjectionService.downloadReport1(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report1_details")
    public Response queryReport1Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return inventorySummaryProjectionService.queryReport1Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report1_details")
    public void downloadReport1Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        inventorySummaryProjectionService.downloadReport1Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return inventorySummaryProjectionService.queryReport2(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping("/save_report2")
    public Response saveReport2(HttpServletRequest request) {
        super.pageLoad(request);
        return inventorySummaryProjectionService.saveReport2(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping("/download_report2")
    public void downloadReport2(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        inventorySummaryProjectionService.downloadReport2(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report2_details")
    public Response queryReport2Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return inventorySummaryProjectionService.queryReport2Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report2_details")
    public void downloadReport2Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        inventorySummaryProjectionService.downloadReport2Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report3")
    public Response queryReport3(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return inventorySummaryProjectionService.queryReport3(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping("/download_report3")
    public void downloadReport3(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        inventorySummaryProjectionService.downloadReport3(parameterMap, response);
    }

    @SchneiderRequestMapping("/split_inventory_projection")
    public Response splitInventoryProjection(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        return inventorySummaryProjectionService.splitInventoryProjection();
    }
}
