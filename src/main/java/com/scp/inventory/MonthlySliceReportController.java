package com.scp.inventory;

import com.scp.inventory.service.IMonthlySliceReportService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/inventory/monthly_slice_report", parent = MonthlySliceReportController.PARENT_CODE)
public class MonthlySliceReportController extends ControllerHelper {

    public static final String PARENT_CODE = "menu770";

    @Resource
    private Response response;

    @Resource
    private IMonthlySliceReportService monthlySliceReportService;

    @SchneiderRequestMapping("/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return monthlySliceReportService.initPage(session.getUserid());
    }

    @SchneiderRequestMapping("/sync_projection")
    public Response syncProjection(HttpServletRequest request) {
        super.pageLoad(request);
        return monthlySliceReportService.syncProjection(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping("/save_report1_calculated")
    public Response saveReport1Calculated(HttpServletRequest request) throws Exception {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return monthlySliceReportService.saveReport1Calculated(parameterMap);
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) throws Exception {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return monthlySliceReportService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping("/download_din_report")
    public void downloadDinReport(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        monthlySliceReportService.downloadDinReport(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        super.pageLoad(request);
        return monthlySliceReportService.queryReport2(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping("/save_report2")
    public Response saveReport2(HttpServletRequest request) {
        super.pageLoad(request);
        return monthlySliceReportService.saveReport2(session.getUserid(), parameterMap);
    }
}
