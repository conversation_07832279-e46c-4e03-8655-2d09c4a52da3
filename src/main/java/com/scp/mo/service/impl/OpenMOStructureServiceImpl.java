package com.scp.mo.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.scp.mo.bean.OpenMOReport1Bean;
import com.scp.mo.bean.OpenMOReport3Bean;
import com.scp.mo.bean.OpenMOTreemap;
import com.scp.mo.dao.IOpenMOStructureDao;
import com.scp.mo.service.IOpenMOStructureService;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.Utils;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.utils.excel.ExcelTemplate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service("OpenMOStructureService")
@Scope("prototype")
@Transactional
public class OpenMOStructureServiceImpl  extends ServiceHelper implements IOpenMOStructureService {

    @Resource
    private IOpenMOStructureDao openMOStructureDao;

    @Resource
    private Response response;

    @Resource
    private ExcelTemplate excelTemplate;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryFilters(Map<String, Object> parameterMap) {
        return response.setBody(Utils.parseCascader(openMOStructureDao.queryCascader(parameterMap)));
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) throws Exception {
        this.generateFilter(parameterMap);
        this.generateReport1Tooltips(parameterMap);
        this.generateValueColumn(parameterMap);

        // 将前台传过来的label转换成列名, 同时也可以防止恶意代码注入
        parameterMap.put("level1", this.getColumnName(parameterMap.get("level1")));
        parameterMap.put("level2", this.getColumnName(parameterMap.get("level2")));
        parameterMap.put("level3", this.getColumnName(parameterMap.get("level3")));
        parameterMap.put("level4", this.getColumnName(parameterMap.get("level4")));
        parameterMap.put("level5", this.getColumnName(parameterMap.get("level5")));

        List<OpenMOTreemap> resultList = new ArrayList<>();
        List<OpenMOReport1Bean> dataList = openMOStructureDao.queryReport1(parameterMap);
        for (OpenMOReport1Bean data : dataList) {
            this.convertReport1Data(resultList, data);
        }
        return response.setBody(resultList);
    }

    /**
     * 将列表转化为Tree数据
     *
     * @param list 输出树
     * @param data 输入值
     * @throws Exception 异常
     */
    private void convertReport1Data(List<OpenMOTreemap> list, OpenMOReport1Bean data) throws Exception {
        String[] categorysOrg = new String[]{data.getCategory1(), data.getCategory2(), data.getCategory3(), data.getCategory4(), data.getCategory5()};
        List<String> categories = new ArrayList<>();

        for (String category : categorysOrg) {
            if (StringUtils.isNotBlank(category)) {
                categories.add(category);
            } else {
                break;
            }
        }

        // 这边逻辑比较复杂, 所以用最笨的方法来描述了, 以免后期不好维护
        // 先把这一行数据转成treemap的数据
        // 第一个节点
        List<OpenMOTreemap> child = new ArrayList<>();
        OpenMOTreemap root = new OpenMOTreemap();
        root.setName(categories.get(0));
        root.setTips(data.copyTooltips()); // 因为这个tooltips要放在树中全局使用, 所以必须要生成一个新节点
        root.setChildren(child);

        // 中间节点
        for (int i = 1; i < categories.size() - 1; i++) {
            OpenMOTreemap treemap = new OpenMOTreemap();
            treemap.setName(categories.get(i));
            treemap.setTips(data.copyTooltips());

            child.add(treemap);
            child = new ArrayList<>();
            treemap.setChildren(child);
        }

        // 最后一个节点
        OpenMOTreemap lastNode = new OpenMOTreemap();
        lastNode.setName(categories.get(categories.size() - 1));
        lastNode.setValue(data.getValue());
        lastNode.setTips(data.copyTooltips());
        child.add(lastNode);

        // 将这行treemap与原始数据相加
        // 先找到list中是否有这个数据节点
        Optional<OpenMOTreemap> beanOpt = list.stream().filter(b -> b.getName().equals(categories.get(0))).findFirst();
        if (beanOpt.isPresent()) {
            OpenMOTreemap bean = beanOpt.get();
            bean.add(root); // 两个节点合并
        } else { //找不到的时候最省事, 直接放入list就可以了
            list.add(root);
        }
    }

    //region private functions
    @SuppressWarnings("unchecked")
    private void generateFilter(Map<String, Object> parameterMap) {
        List<String> report4SelectedColumns = (List<String>) parameterMap.get("report4SelectedColumns");

        if (report4SelectedColumns == null || report4SelectedColumns.isEmpty()) {
            report4SelectedColumns = new ArrayList<>();
            report4SelectedColumns.add("BU");
            report4SelectedColumns.add("ENTITY");
        }
        parameterMap.put("report4SelectedColumns", report4SelectedColumns);

        // 生成筛选条件
        List<String> moMsgList = new ArrayList<>();
        this.generateCascaderFilterSQL(parameterMap, value -> {
            String key = value.getKey();
            if (key.equals("MO_MSG")) {
                moMsgList.add(value.getValue());
                return false;
            }
            return true;
        });
        parameterMap.put("moMsgList", this.generateMOMsgFilter(parameterMap, moMsgList));

        String dateType = ((String) parameterMap.get("dateType")).replace("BY_", "");
        if ("REQ_DATE".equalsIgnoreCase(dateType)) {
            dateType = "MO_REQ_DATE";
        } else if ("SNAPSHOT_DATE".equalsIgnoreCase(dateType)) {
            dateType = "DATE$";
        } else if ("RESCHEDULE_DATE".equalsIgnoreCase(dateType)) {
            dateType = "RESCHEDULING_DATE";
        }
        parameterMap.put("dateType", dateType);
    }

    private String generateMOMsgFilter(Map<String, Object> parameterMap, List<String> values) {
        values.remove("Others");
        if (values.size() > 0) {
            Map<String, List<String>> filterMap = new HashMap<>();
            for (String value : values) {
                String key = Utils.randomStr(8);
                parameterMap.put(key, value.split(" - ")[1]);
                filterMap.computeIfAbsent(value.split(" - ")[0], k -> new ArrayList<>()).add("#{" + key + ",jdbcType=VARCHAR}");
            }
            return filterMap.entrySet().stream()
                    .map(entry -> "NVL(T." + entry.getKey() + ", 'Others') IN (" + StringUtils.join(entry.getValue(), ",") + ")")
                    .collect(Collectors.joining(" AND "));
        }
        return null;
    }

    private void generateReport1Tooltips(Map<String, Object> parameterMap) {
        String resultType = (String) parameterMap.get("resultType");
        List<String> tooltips = ((JSONArray) parameterMap.get("report1Tooltips")).toJavaList(String.class);
        if (!tooltips.isEmpty()) {
            List<String> tooltipsColumns = tooltips.stream().map(this::getColumnName).collect(Collectors.toList());
            List<String> tooltipsColumnsName = new ArrayList<>();
            List<String> polymorphicColumns = new ArrayList<>(Arrays.asList("DELIVERY_DEPTH", "DELIVERY_DEPTH", "OPEN_MO_QTY", "OPEN_MO_VALUE", "CONFIRM_QTY", "GR_QTY", "GR_VALUE", "UNIT_COST", "REORDER_POINT", "SAFETY_STOCK", "AMU"));

            for (String c : tooltipsColumns) {
                String tooltip = this.getColumnName(c);
                tooltipsColumnsName.add("NVL(SUM(" + c + "),0) AS " + tooltip);
            }
            parameterMap.put("tooltipsColumns", StringUtils.join(tooltipsColumnsName, ", "));
        }
    }

    private void generateTreePathFilter(Map<String, Object> parameterMap) {
        String selectedTreePath = (String) parameterMap.get("selectedTreePath");
        if (StringUtils.isNotBlank(selectedTreePath)) {
            List<String> conditions = new ArrayList<>();
            String[] treePaths = selectedTreePath.split(" > ");
            for (int i = 1; i <= Math.min(treePaths.length, 5); i++) {
                String key = Utils.randomStr(8);
                String name = this.getColumnName(parameterMap.get("level" + i));
                if ("Others".equals(StringUtils.trim(treePaths[i - 1]))) {
                    if (name != null && (name.startsWith("MATERIAL_OWNER") || name.equals("INVENTORY_MONITOR_TYPE") == true)) {
                        conditions.add("(t2." + name + " = #{" + key + ",jdbcType=VARCHAR} or " + "t2." + name + " is null )");
                    } else {
                        conditions.add("(t." + name + " = #{" + key + ",jdbcType=VARCHAR} or " + "t." + name + " is null )");
                    }
                } else {
                    if (name != null && (name.startsWith("MATERIAL_OWNER") == true || name.equals("INVENTORY_MONITOR_TYPE") == true)) {
                        conditions.add("t2." + name + " = #{" + key + ",jdbcType=VARCHAR}");
                    } else {
                        conditions.add("t." + name + " = #{" + key + ",jdbcType=VARCHAR}");
                    }
                }
                parameterMap.put(key, StringUtils.trim(treePaths[i - 1]));
            }

            String filterHist = "(" + StringUtils.join(conditions, " and ") + ")";
            parameterMap.put("treePathFilter", filterHist);
        }
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);

        return response.setBody(openMOStructureDao.queryReport2(parameterMap));
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2Overall(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        parameterMap.put("overall", true);
        page.setTotal(openMOStructureDao.queryReport2DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(openMOStructureDao.queryReport2Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport2Overall(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        parameterMap.put("overall", true);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "open_mo_structure_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.mo.dao.IOpenMOStructureDao.queryReport2Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(openMOStructureDao.queryReport2DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(openMOStructureDao.queryReport2Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport2Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "open_mo_structure_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.mo.dao.IOpenMOStructureDao.queryReport2Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);

        List<OpenMOReport3Bean> dataList;

        String dateType = (String) parameterMap.get("dateType");

        if ("DATE$".equalsIgnoreCase(dateType)) {
            dataList = openMOStructureDao.queryReport3(parameterMap);
        } else {
            dataList = openMOStructureDao.queryReport3SpecialDate(parameterMap);
        }

        Map<String, BigDecimal> dataMap = new HashMap<>();
        LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();
        Map<String, String> xAxisMap = new HashMap<>();

        List<String> legend = openMOStructureDao.queryReport3Legend(parameterMap);

        for (OpenMOReport3Bean data : dataList) {
            dataMap.put(data.getKey(), data.getVALUE());
            xAxisMap.put(data.getCALENDAR_DATE(), "");
        }
        List<String> xAxisList = xAxisMap.keySet().stream().sorted(String::compareTo).collect(Collectors.toList());
        for (String l : legend) {
            List<BigDecimal> temp = new ArrayList<>();

            for (String x : xAxisList) {
                temp.add(dataMap.getOrDefault(l + "#" + x, BigDecimal.ZERO));
            }
            resultMap.put(l, temp);
        }

        resultMap.put("xAxis", xAxisList);
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(openMOStructureDao.queryReport3DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(openMOStructureDao.queryReport3Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "open_mo_structure_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.mo.dao.IOpenMOStructureDao.queryReport3Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport4(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        List<LinkedHashMap<String, Object>> dataList = openMOStructureDao.queryReport4(parameterMap);

        page.setData(dataList);
        page.setTotal(dataList.size());
        return response.setBody(page);
    }

    @Override
    public void downloadReport4(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        String column1 = this.getColumnName(parameterMap.get("column1"));
        String column2 = this.getColumnName(parameterMap.get("column2"));
        parameterMap.put("column1", column1);
        parameterMap.put("column2", column2);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "open_mo_structure_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.mo.dao.IOpenMOStructureDao.queryReport4", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport4Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(openMOStructureDao.queryReport4DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(openMOStructureDao.queryReport4Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport4Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        parameterMap.put("column1", this.getColumnName(parameterMap.get("column1")));
        parameterMap.put("column2", this.getColumnName(parameterMap.get("column2")));
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "open_mo_structure_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.mo.dao.IOpenMOStructureDao.queryReport4Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport4Columns(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);
        return response.setBody(openMOStructureDao.queryReport4Columns(parameterMap));
    }

    private void generateValueColumn(Map<String, Object> parameterMap) {
        String resultType = (String) parameterMap.get("resultType");
        String valueColumn = "NVL(SUM(OPEN_MO_VALUE), 0)";
        if ("Value RMB".equalsIgnoreCase(resultType)) {
            valueColumn = "NVL(SUM(OPEN_MO_VALUE), 0)";
        } else if ("Line".equalsIgnoreCase(resultType)) {
            valueColumn = "COUNT(1)";
        } else if ("Quantity".equalsIgnoreCase(resultType)) {
            valueColumn = "NVL(SUM(OPEN_MO_QTY), 0)";
        }
        parameterMap.put("valueColumn", valueColumn);
    }

    private String getColumnName(Object labelObj) {
        String label = (String) labelObj;
        if (label == null) {
            return null;
        }
        if (Utils.hasInjectionAttack(label)) {
            return "";
        }
        return label;
    }
}
