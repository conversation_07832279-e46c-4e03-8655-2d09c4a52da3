package com.scp.mo.service;

import com.starter.context.bean.Response;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;

public interface IOpenMOStructureService {

    Response queryFilters(Map<String, Object> parameterMap);

    Response queryReport1(Map<String, Object> parameterMap) throws Exception;

    Response queryReport2(Map<String, Object> parameterMap);

    Response queryReport2Overall(Map<String, Object> parameterMap);

    void downloadReport2Overall(Map<String, Object> parameterMap, HttpServletResponse res);

    Response queryReport2Details(Map<String, Object> parameterMap);

    void downloadReport2Details(Map<String, Object> parameterMap, HttpServletResponse res);

    Response queryReport3(Map<String, Object> parameterMap);

    Response queryReport3Details(Map<String, Object> parameterMap);

    void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport4(Map<String, Object> parameterMap);

    Response queryReport4Columns(Map<String, Object> parameterMap);

    void downloadReport4(Map<String, Object> parameterMap, HttpServletResponse res);

    Response queryReport4Details(Map<String, Object> parameterMap);

    void downloadReport4Details(Map<String, Object> parameterMap, HttpServletResponse res);
}
