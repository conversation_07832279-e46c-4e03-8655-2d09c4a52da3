package com.scp.mo;

import com.scp.mo.service.IOpenMOStructureService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/mo/open_mo_structure", parent = "menu646")
public class OpenMOStructureController extends ControllerHelper {

    @Resource
    private IOpenMOStructureService openMOStructureService;

    @Resource
    private Response res;

    @SchneiderRequestMapping("/query_filters")
    public Response queryFilters(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return openMOStructureService.queryFilters(parameterMap);
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        try {
            return openMOStructureService.queryReport1(parameterMap);
        } catch (Exception e) {
            return res.setError(e);
        }
    }

    @SchneiderRequestMapping("/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return openMOStructureService.queryReport2(parameterMap);
    }

    @SchneiderRequestMapping("/query_report2_overall")
    public Response queryReport2Overall(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return openMOStructureService.queryReport2Overall(parameterMap);
    }

    @SchneiderRequestMapping("/download_report2_overall")
    public void downloadReport2Overall(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        openMOStructureService.downloadReport2Overall(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report2_details")
    public Response queryReport2Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return openMOStructureService.queryReport2Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report2_details")
    public void downloadReport2Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        openMOStructureService.downloadReport2Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report3")
    public Response queryReport3(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return openMOStructureService.queryReport3(parameterMap);
    }

    @SchneiderRequestMapping("/query_report3_details")
    public Response queryReport3Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return openMOStructureService.queryReport3Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report3_details")
    public void downloadReport3Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        openMOStructureService.downloadReport3Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report4_columns")
    public Response queryReport4Columns(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return openMOStructureService.queryReport4Columns(parameterMap);
    }

    @SchneiderRequestMapping("/query_report4")
    public Response queryReport4(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return openMOStructureService.queryReport4(parameterMap);
    }

    @SchneiderRequestMapping("/download_report4")
    public void downloadReport4(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        openMOStructureService.downloadReport4(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report4_details")
    public Response queryReport4Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return openMOStructureService.queryReport4Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report4_details")
    public void downloadReport4Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        openMOStructureService.downloadReport4Details(parameterMap, response);
    }

}
