package com.scp.mo.bean;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.starter.context.bean.SCPRuntimeException;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class OpenMOReport1Tooltips {
    private BigDecimal DELIVERY_DEPTH;
    private BigDecimal OPEN_MO_QTY;
    private BigDecimal OPEN_MO_VALUE;
    private BigDecimal CONFIRM_QTY;
    private BigDecimal GR_QTY;
    private BigDecimal GR_VALUE;
    private BigDecimal UNIT_COST;
    private BigDecimal REORDER_POINT;
    private BigDecimal SAFETY_STOCK;
    private BigDecimal AMU;
    private BigDecimal AMF;

    public BigDecimal getDELIVERY_DEPTH() {
        return DELIVERY_DEPTH;
    }

    public void setDELIVERY_DEPTH(BigDecimal DELIVERY_DEPTH) {
        this.DELIVERY_DEPTH = DELIVERY_DEPTH;
    }

    public BigDecimal getOPEN_MO_QTY() {
        return OPEN_MO_QTY;
    }

    public void setOPEN_MO_QTY(BigDecimal OPEN_MO_QTY) {
        this.OPEN_MO_QTY = OPEN_MO_QTY;
    }

    public BigDecimal getOPEN_MO_VALUE() {
        return OPEN_MO_VALUE;
    }

    public void setOPEN_MO_VALUE(BigDecimal OPEN_MO_VALUE) {
        this.OPEN_MO_VALUE = OPEN_MO_VALUE;
    }

    public BigDecimal getCONFIRM_QTY() {
        return CONFIRM_QTY;
    }

    public void setCONFIRM_QTY(BigDecimal CONFIRM_QTY) {
        this.CONFIRM_QTY = CONFIRM_QTY;
    }

    public BigDecimal getGR_QTY() {
        return GR_QTY;
    }

    public void setGR_QTY(BigDecimal GR_QTY) {
        this.GR_QTY = GR_QTY;
    }

    public BigDecimal getGR_VALUE() {
        return GR_VALUE;
    }

    public void setGR_VALUE(BigDecimal GR_VALUE) {
        this.GR_VALUE = GR_VALUE;
    }

    public BigDecimal getUNIT_COST() {
        return UNIT_COST;
    }

    public void setUNIT_COST(BigDecimal UNIT_COST) {
        this.UNIT_COST = UNIT_COST;
    }

    public BigDecimal getREORDER_POINT() {
        return REORDER_POINT;
    }

    public void setREORDER_POINT(BigDecimal REORDER_POINT) {
        this.REORDER_POINT = REORDER_POINT;
    }

    public BigDecimal getSAFETY_STOCK() {
        return SAFETY_STOCK;
    }

    public void setSAFETY_STOCK(BigDecimal SAFETY_STOCK) {
        this.SAFETY_STOCK = SAFETY_STOCK;
    }

    public BigDecimal getAMU() {
        return AMU;
    }

    public void setAMU(BigDecimal AMU) {
        this.AMU = AMU;
    }

    public BigDecimal getAMF() {
        return AMF;
    }

    public void setAMF(BigDecimal AMF) {
        this.AMF = AMF;
    }

    private static final Class<OpenMOReport1Tooltips> clazz;
    private static final List<String> fields;

    static {
        clazz = OpenMOReport1Tooltips.class;
        fields = Arrays.stream(clazz.getDeclaredFields()).filter(f -> f.getAnnotatedType().getType().getTypeName().equalsIgnoreCase("java.math.BigDecimal")).map(Field::getName).collect(Collectors.toList());
    }

    public OpenMOReport1Tooltips copyOf(OpenMOReport1Tooltips tooltips) {
        try {
            for (String f : fields) {
                Method method = clazz.getMethod("get" + f);
                Object value = method.invoke(tooltips);
                if (value != null) {
                    clazz.getMethod("set" + f, BigDecimal.class).invoke(this, (BigDecimal) value);
                }
            }
            return this;
        } catch (Exception e) {
            throw new SCPRuntimeException(e.getMessage());
        }

    }

    public void add(OpenMOReport1Tooltips tips) throws Exception {
        for (String f : fields) {
            Method method = clazz.getMethod("get" + f);
            Object value = method.invoke(tips);
            if (value != null) {
                BigDecimal val = BigDecimal.ZERO;
                Object valueOrg = method.invoke(this);
                if (valueOrg != null) {
                    val = (BigDecimal) valueOrg;
                }
                clazz.getMethod("set" + f, BigDecimal.class).invoke(this, val.add((BigDecimal) value));
            }
        }
    }
}
