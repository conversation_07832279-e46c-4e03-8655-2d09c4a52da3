package com.scp.mo.bean;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class OpenMOReport1Bean {
    private String category1;
    private String category2;
    private String category3;
    private String category4;
    private String category5;
    private BigDecimal value;
    private OpenMOReport1Tooltips tooltips = new OpenMOReport1Tooltips();

    public String getCategory1() {
        return category1;
    }

    public void setCategory1(String category1) {
        this.category1 = category1;
    }

    public String getCategory2() {
        return category2;
    }

    public void setCategory2(String category2) {
        this.category2 = category2;
    }

    public String getCategory3() {
        return category3;
    }

    public void setCategory3(String category3) {
        this.category3 = category3;
    }

    public String getCategory4() {
        return category4;
    }

    public void setCategory4(String category4) {
        this.category4 = category4;
    }

    public String getCategory5() {
        return category5;
    }

    public void setCategory5(String category5) {
        this.category5 = category5;
    }

    public BigDecimal getValue() {
        return value.setScale(0, RoundingMode.HALF_UP);
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public OpenMOReport1Tooltips getTooltips() {
        return tooltips;
    }

    public OpenMOReport1Tooltips copyTooltips() throws Exception {
        return new OpenMOReport1Tooltips().copyOf(tooltips);
    }

    public void setTooltips(OpenMOReport1Tooltips tooltips) {
        this.tooltips = tooltips;
    }
}
