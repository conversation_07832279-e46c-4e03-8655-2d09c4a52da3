package com.scp.mo.bean;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.math.BigDecimal;
import java.util.*;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class OpenMOTreemap {
    private String name;
    private BigDecimal value;
    private OpenMOReport1Tooltips tips = new OpenMOReport1Tooltips();
    private List<OpenMOTreemap> children;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public List<OpenMOTreemap> getChildren() {
        if (children == null) {
            children = new ArrayList<>();
        }
        return children;
    }

    public boolean hasChildren() {
        return this.getChildren() != null && !this.getChildren().isEmpty();
    }

    public void setChildren(List<OpenMOTreemap> children) {
        this.children = children;
    }

    public void setTips(OpenMOReport1Tooltips tips) {
        this.tips = tips;
    }

    public OpenMOReport1Tooltips getTips() {
        return tips;
    }

    // 合并两个节点
    public void add(OpenMOTreemap addElement) throws Exception {
        OpenMOTreemap mainElement = this;

        mainElement.getTips().add(addElement.getTips()); // 先相加根节点

        // 再相加子节点
        while (addElement.hasChildren()) {
            List<OpenMOTreemap> mainChildren = mainElement.getChildren();
            OpenMOTreemap child = addElement.getChildren().get(0); // 加数节点只有一个子节点

            Optional<OpenMOTreemap> beanOpt = mainChildren.stream().filter(b -> b.getName().equals(child.getName())).findFirst();
            if (beanOpt.isPresent()) {
                OpenMOTreemap bean = beanOpt.get();
                bean.getTips().add(child.getTips()); // 如果找到了, 那就合并两个子节点

                // 向下移动一层
                addElement = child;
                mainElement = bean;
            } else {
                mainChildren.add(child);// 如果找不到子节点, 那直接把需要相加的节点附在这个子节点下面
                break; // 然后直接跳出循环, 相加结束
            }
        }
    }
}
