<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.mo.dao.IOpenMOStructureDao">

    <sql id="OpenMOFilter">
        <if test="_filters != null and _filters != ''.toString()">
            and ${_filters}
        </if>
        <if test="moMsgFilter != null and moMsgFilter != ''.toString()">
            AND ${moMsgFilter}
        </if>
        <if test="treePathFilter != null and treePathFilter != ''.toString()">
            and ${treePathFilter}
        </if>
        <if test="specialRightDateRange != null and specialRightDateRange.size() > 0">
            <choose>
                <when test="specialRightDateType == 'RELEASE_DATE'.toString()">
                    AND T.RELEASE_DATE BETWEEN TO_DATE(#{specialRightDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                    AND TRUNC(TO_DATE(#{specialRightDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
                </when>
                <otherwise>
                    AND T.MO_REQ_DATE BETWEEN TO_DATE(#{specialRightDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                    AND TRUNC(TO_DATE(#{specialRightDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
                </otherwise>
            </choose>
        </if>
        <if test="specialLeftDateRange != null and specialLeftDateRange.size() > 0">
            AND T.${specialRightDateType} BETWEEN TO_DATE(#{specialLeftDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
            AND TRUNC(TO_DATE(#{specialLeftDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
        </if>
        <if test="dateRange != null and dateRange.size() > 0">
            AND T.${dateType} BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
            AND TRUNC(TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd') + 1
        </if>
        <if test="excludeSnapshotDate == N and dateType != 'DATE$'.toString()">
            AND T.DATE$ = TO_DATE(#{specificDate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
        </if>
    </sql>

    <sql id="decodeName">
        DECODE(
                  NAME,
                  'P >6M', 'a',
                  'P 3-6M', 'b',
                  'P 2-3M', 'c',
                  'P 1-2M', 'd',
                  'P 14-30D', 'e',
                  'P 7-14D', 'f',
                  'P 3-7D', 'g',
                  'P 0-3D', 'h',
                  'F 0-3D', 'i',
                  'F 3-7D', 'j',
                  'F 7-14D', 'k',
                  'F 14-30D', 'l',
                  'F 1-2M', 'm',
                  'F 2-3M', 'n',
                  'F 3-6M', 'o',
                  'F >6M', 'p',
                  '1W', 'a',
                  '2W', 'b',
                  '3W', 'c',
                  '4W', 'd',
                  '2M', 'e',
                  '3M', 'f',
                  '6M', 'g',
                  '1Y', 'h',
                  '2Y', 'i',
                  '>2Y', 'j',
                  'Others', CAST(UNISTR('\ffff\ffff') AS VARCHAR2(8)),
                  "NAME")
    </sql>

    <sql id="queryBaseHist">
        SELECT /*+ parallel(t 6) */
        T.*,
        CALENDAR.YEAR AS CALENDAR_YEAR,
        CALENDAR.WEEK_NO AS CALENDAR_WEEK_NO,
        CALENDAR.MONTH AS CALENDAR_MONTH
        FROM ${SCPA.OPEN_MO_STRUCTURE_HIST} t
        LEFT JOIN SY_CALENDAR CALENDAR ON T.${dateType} = CALENDAR.DATE$ AND CALENDAR.NAME = 'National Holidays'
        WHERE 1 = 1
        <include refid="OpenMOFilter">
            <property name="excludeSnapshotDate" value="${excludeSnapshotDate}"/>
        </include>
    </sql>

    <select id="queryCascader" resultType="java.util.Map">
        SELECT NAME,
        CATEGORY
        FROM ${SCPA.OPEN_MO_STRUCTURE_FILTER_V} T
        ORDER BY CATEGORY,
        <include refid="decodeName"/>
    </select>

    <resultMap id="report1ResultMap" type="com.scp.mo.bean.OpenMOReport1Bean">
        <result property="category1" column="CATEGORY1"/>
        <result property="category2" column="CATEGORY2"/>
        <result property="category3" column="CATEGORY3"/>
        <result property="category4" column="CATEGORY4"/>
        <result property="category5" column="CATEGORY5"/>
        <result property="value" column="value"/>
        <association property="tooltips" javaType="com.scp.mo.bean.OpenMOReport1Tooltips">
            <result property="DELIVERY_DEPTH" column="DELIVERY_DEPTH"/>
            <result property="OPEN_MO_QTY" column="OPEN_MO_QTY"/>
            <result property="OPEN_MO_VALUE" column="OPEN_MO_VALUE"/>
            <result property="CONFIRM_QTY" column="CONFIRM_QTY"/>
            <result property="GR_QTY" column="GR_QTY"/>
            <result property="GR_VALUE" column="GR_VALUE"/>
            <result property="UNIT_COST" column="UNIT_COST"/>
            <result property="REORDER_POINT" column="REORDER_POINT"/>
            <result property="SAFETY_STOCK" column="SAFETY_STOCK"/>
            <result property="AMU" column="AMU"/>
        </association>
    </resultMap>

    <select id="queryReport1" resultMap="report1ResultMap">
        WITH BASE AS (
        <include refid="queryBaseHist">
            <property name="excludeSnapshotDate" value="Y"/>
        </include>
        AND T.DATE$ = TO_DATE(#{report1SpecificDate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
        )
        SELECT
        NVL(${level1}, 'Others') AS CATEGORY1,
        NVL(${level2}, 'Others') AS CATEGORY2,
        NVL(${level3}, 'Others') AS CATEGORY3,
        <if test="level4 != null and level4 != ''.toString()">
            NVL(${level4}, 'Others') AS CATEGORY4,
        </if>
        <if test="level5 != null and level5 != ''.toString()">
            NVL(${level5}, 'Others') AS CATEGORY5,
        </if>
        ${valueColumn} AS VALUE
        <if test="tooltipsColumns != null and tooltipsColumns != ''.toString()">
            ,${tooltipsColumns}
        </if>
        FROM BASE t
        GROUP BY
        ${level1}, ${level2}, ${level3}
        <if test="level4 != null and level4 != ''.toString()">,${level4}</if>
        <if test="level5 != null and level5 != ''.toString()">,${level5}</if>
    </select>

    <resultMap id="report2ResultMap" type="com.scp.mo.bean.OpenMOReport2Bean">
        <result property="name" column="NAME"/>
        <result property="value" column="VALUE"/>
    </resultMap>

    <select id="queryReport2" resultMap="report2ResultMap">
        WITH BASE AS (
        <include refid="queryBaseHist">
            <property name="excludeSnapshotDate" value="Y"/>
        </include>
        AND T.DATE$ = TO_DATE(#{report2SpecificDate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
        )
        SELECT
        DELIVERY_RANGE AS NAME,
        ${valueColumn} AS VALUE
        FROM BASE T
        GROUP BY DELIVERY_RANGE
        ORDER BY <include refid="decodeName"/>
    </select>

    <sql id="report2DetailsSQL">
        <include refid="queryBaseHist">
            <property name="excludeSnapshotDate" value="Y"/>
        </include>
        AND T.DATE$ = TO_DATE(#{report2SpecificDate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
        <if test="report2SelectedValue != null and report2SelectedValue != ''">
            AND T.DELIVERY_RANGE IN (
            <foreach collection="report2SelectedValue" separator="," item="item">
                '${item}'
            </foreach>
            )
        </if>
    </sql>

    <select id="queryReport2DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="report2DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport2Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report2DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <resultMap id="report3ResultMap" type="com.scp.mo.bean.OpenMOReport3Bean">
        <result property="CALENDAR_DATE" column="CALENDAR_DATE"/>
        <result property="NAME" column="NAME"/>
        <result property="VALUE" column="VALUE"/>
    </resultMap>

    <select id="queryReport3Legend" resultType="java.lang.String">
        <choose>
            <when test="report3SelectedColumn == 'DELIVERY_RANGE'.toString() or report3SelectedColumn == 'LT_RANGE'.toString()">
                SELECT NAME FROM ${SCPA.OPEN_MO_STRUCTURE_FILTER_V} T
                WHERE T.CATEGORY = #{report3SelectedColumn, jdbcType=VARCHAR}
                ORDER BY <include refid="decodeName"/>
            </when>
            <otherwise>
                <choose>
                    <when test="dateType == 'DATE$'">
                        WITH BASE AS (<include refid="queryBaseHist">
                        <property name="excludeSnapshotDate" value="Y"/>
                    </include>)
                        SELECT /*+ parallel(t 6) */
                        DISTINCT NVL(t.${report3SelectedColumn}, 'Others') AS NAME
                        FROM BASE t
                        WHERE 1 = 1
                        <choose>
                            <when test='report3SelectedType == "VIEW_BY_DAY".toString()'/>
                            <when test='report3SelectedType == "VIEW_BY_WEEK".toString()'>
                                AND ((T.WEEKDAY$ = '2'
                                AND T.DATE$ &gt;= ADD_MONTHS(TRUNC(SYSDATE, 'dd'), -2))
                                OR
                                (T.WEEKDAY$ = 7 AND T.DATE$ &lt; ADD_MONTHS(TRUNC(SYSDATE, 'dd'), -2)))
                            </when>
                            <otherwise>
                                AND to_char(T.DATE$ ,'dd')=1
                            </otherwise>
                        </choose>
                    </when>
                    <otherwise>
                        WITH BASE AS (<include refid="queryBaseHist">
                        <property name="excludeSnapshotDate" value="N"/>
                    </include>)
                        SELECT /*+ parallel(t 6) */
                        DISTINCT NVL(t.${report3SelectedColumn}, 'Others') AS NAME
                        FROM BASE t
                    </otherwise>
                </choose>
                ORDER BY NAME
            </otherwise>
        </choose>
    </select>

    <select id="queryReport3" parameterType="java.util.Map" resultMap="report3ResultMap">
        WITH BASE AS (<include refid="queryBaseHist">
                            <property name="excludeSnapshotDate" value="Y"/>
                        </include>)
        SELECT /*+ parallel(t 6) */
        TO_CHAR(T.DATE$, 'yyyy/mm/dd') AS CALENDAR_DATE,
        NVL(t.${report3SelectedColumn}, 'Others') AS NAME,
        ROUND(${valueColumn}, 2) AS VALUE
        FROM BASE t
        <choose>
            <when test='report3SelectedType == "VIEW_BY_WEEK".toString()'>
                WHERE TO_CHAR(T.DATE$, 'D') = 7
            </when>
            <when test='report3SelectedType == "VIEW_BY_MONTH".toString()'>
                WHERE T.DATE$ = TRUNC(T.DATE$, 'MM')
            </when>
        </choose>
        GROUP BY T.DATE$,
        NVL(t.${report3SelectedColumn}, 'Others')
        ORDER BY <include refid="decodeName"/>
    </select>

    <select id="queryReport3SpecialDate" parameterType="java.util.Map" resultMap="report3ResultMap">
        WITH BASE AS (
        <include refid="queryBaseHist">
            <property name="excludeSnapshotDate" value="N"/>
        </include>
        )
        SELECT /*+ parallel(t 6) */
        <choose>
            <when test='report3SelectedType == "VIEW_BY_DAY".toString()'>
                TO_CHAR(T.${dateType}, 'yyyy/mm/dd') AS CALENDAR_DATE,
            </when>
            <when test='report3SelectedType == "VIEW_BY_WEEK".toString()'>
                T.CALENDAR_YEAR || T.CALENDAR_WEEK_NO AS CALENDAR_DATE,
            </when>
            <when test='report3SelectedType == "VIEW_BY_MONTH".toString()'>
                T.CALENDAR_YEAR || T.CALENDAR_MONTH AS CALENDAR_DATE,
            </when>
            <otherwise>
                T.CALENDAR_YEAR || T.CALENDAR_MONTH AS CALENDAR_DATE,
            </otherwise>
        </choose>
        NVL(T.${report3SelectedColumn}, 'Others') AS NAME,
        ROUND(${valueColumn}, 2) AS VALUE
        FROM BASE t
        GROUP BY
        <choose>
            <when test='report3SelectedType == "VIEW_BY_DAY".toString()'>
                T.${dateType},
            </when>
            <when test='report3SelectedType == "VIEW_BY_WEEK".toString()'>
                T.CALENDAR_YEAR || T.CALENDAR_WEEK_NO,
            </when>
            <when test='report3SelectedType == "VIEW_BY_MONTH".toString()'>
                T.CALENDAR_YEAR || T.CALENDAR_MONTH,
            </when>
            <otherwise>
                T.CALENDAR_YEAR || T.CALENDAR_MONTH,
            </otherwise>
        </choose>
        NVL(t.${report3SelectedColumn}, 'Others')
        ORDER BY <include refid="decodeName"/>
    </select>

    <sql id="report3DetailsSQL">
        WITH TEMP AS (
        <include refid="queryBaseHist">
            <property name="excludeSnapshotDate" value="Y"/>
        </include>)
        SELECT * FROM TEMP T
        WHERE 1 = 1
        <choose>
            <when test="dateType == 'DATE$'.toString()">
                AND T.DATE$ = TO_DATE(#{report3SelectedAxis, jdbcType=VARCHAR}, 'yyyy/mm/dd')
            </when>
            <otherwise>
                <choose>
                    <when test='report3SelectedType == "VIEW_BY_DAY".toString()'>
                        AND T.${dateType} = TO_DATE(#{report3SelectedAxis, jdbcType=VARCHAR}, 'yyyy/mm/dd')
                    </when>
                    <when test='report3SelectedType == "VIEW_BY_WEEK".toString()'>
                        AND T.CALENDAR_YEAR || T.CALENDAR_WEEK_NO = #{report3SelectedAxis, jdbcType=VARCHAR}
                    </when>
                    <when test='report3SelectedType == "VIEW_BY_MONTH".toString()'>
                        AND T.CALENDAR_YEAR || T.CALENDAR_MONTH = #{report3SelectedAxis, jdbcType=VARCHAR}
                    </when>
                </choose>
                AND T.DATE$ = TO_DATE(#{specificDate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
            </otherwise>
        </choose>
        <if test="report3SelectedValue != null and report3SelectedValue != ''.toString()">
            AND T.${report3SelectedColumn} = #{report3SelectedValue, jdbcType=VARCHAR}
        </if>
    </sql>

    <select id="queryReport3DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="report3DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport3Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report3DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport4Columns" parameterType="java.util.Map" resultType="java.lang.String">
        <choose>
            <when test="dateType == 'DATE$'.toString()">
                WITH BASE AS (<include refid="queryBaseHist">
                <property name="excludeSnapshotDate" value="Y"/>
            </include>)
                SELECT * FROM (SELECT DISTINCT TO_CHAR(T.DATE$,'YYYY/MM/DD') AS CALENDAR_DATE
                FROM BASE t
                <choose>
                    <when test='report4ViewType == "VIEW_BY_WEEK".toString()'>
                        WHERE TO_CHAR(T.DATE$, 'D') = 7
                    </when>
                    <when test='report4ViewType == "VIEW_BY_MONTH".toString()'>
                        WHERE T.DATE$ = TRUNC(T.DATE$, 'MM')
                    </when>
                </choose>)
                WHERE CALENDAR_DATE IS NOT NULL
                ORDER BY CALENDAR_DATE DESC
                OFFSET 0 ROWS FETCH NEXT 30 ROWS ONLY
            </when>
            <otherwise>
                WITH BASE AS (<include refid="queryBaseHist">
                <property name="excludeSnapshotDate" value="N"/>
            </include>)
                SELECT * FROM (SELECT DISTINCT
                <choose>
                    <when test='report4ViewType == "VIEW_BY_DAY".toString()'>
                        TO_CHAR(T.${dateType}, 'yyyy/mm/dd') AS CALENDAR_DATE
                    </when>
                    <when test='report4ViewType == "VIEW_BY_WEEK".toString()'>
                        T.CALENDAR_YEAR || T.CALENDAR_WEEK_NO AS CALENDAR_DATE
                    </when>
                    <when test='report4ViewType == "VIEW_BY_MONTH".toString()'>
                        T.CALENDAR_YEAR || T.CALENDAR_MONTH AS CALENDAR_DATE
                    </when>
                    <otherwise>
                        T.CALENDAR_YEAR || T.CALENDAR_MONTH AS CALENDAR_DATE
                    </otherwise>
                </choose>
                FROM BASE t
                )
                WHERE CALENDAR_DATE IS NOT NULL
                ORDER BY CALENDAR_DATE DESC
                OFFSET 0 ROWS FETCH NEXT 30 ROWS ONLY
            </otherwise>
        </choose>
    </select>

    <select id="queryReport4" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report4SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="report4SQL">
        <choose>
            <when test="dateType == 'DATE$'.toString()">
                WITH
                BASE AS (
                <include refid="queryBaseHist">
                    <property name="excludeSnapshotDate" value="Y"/>
                </include>
                <if test="report4ColumnNames != null and report4ColumnNames.size() > 0">
                    AND t.DATE$ IN
                    <foreach collection="report4ColumnNames" index="index" open="(" close=")" item="item" separator=",">
                        TO_DATE(#{item, jdbcType=VARCHAR},
                        'yyyy/mm/dd')
                    </foreach>
                </if>
                ),
                TEMP AS (
                SELECT
                <foreach collection="report4SelectedColumns" item="item">
                    NVL(${item}, 'Others')                                    AS ${item},
                </foreach>
                TO_CHAR(t.DATE$, 'yyyy/mm/dd')                                AS CALENDAR_DATE,
                ROUND(${valueColumn}, 3)                                      AS VALUE
                FROM BASE t
                GROUP BY
                <foreach collection="report4SelectedColumns" item="item">
                    ${item},
                </foreach>
                TO_CHAR(t.DATE$, 'yyyy/mm/dd')
                )
                SELECT *
                FROM (
                SELECT  <foreach collection="report4SelectedColumns" item="item">
                            NVL(${item}, 'Others')                                    AS ${item},
                        </foreach>
                NVL(T.CALENDAR_DATE, 'Others')                                AS CALENDAR_DATE,
                NVL(T.VALUE, 0) AS TOTAL
                FROM TEMP T
                ) mm
                PIVOT (SUM(TOTAL) AS TOTAL
                FOR CALENDAR_DATE
                IN (
                <foreach collection="report4ColumnNames" separator="," item="item">
                    '${item}'
                </foreach>
                ))
                ORDER BY
                <foreach collection="report4SelectedColumns" item="item" separator=",">
                    DECODE(${item}, 'Others', 'zzz', ${item})
                </foreach>
            </when>
            <otherwise>
                WITH
                BASE AS (
                <include refid="queryBaseHist">
                    <property name="excludeSnapshotDate" value="N"/>
                </include>
                ),
                TEMP AS (
                SELECT
                <foreach collection="report4SelectedColumns" item="item">
                    NVL(${item}, 'Others')                                AS ${item},
                </foreach>
                <choose>
                    <when test='report4ViewType == "VIEW_BY_DAY".toString()'>
                        TO_CHAR(T.${dateType}, 'yyyy/mm/dd') AS CALENDAR_DATE,
                    </when>
                    <when test='report4ViewType == "VIEW_BY_WEEK".toString()'>
                        T.CALENDAR_YEAR || T.CALENDAR_WEEK_NO AS CALENDAR_DATE,
                    </when>
                    <when test='report4ViewType == "VIEW_BY_MONTH".toString()'>
                        T.CALENDAR_YEAR || T.CALENDAR_MONTH AS CALENDAR_DATE,
                    </when>
                    <otherwise>
                        T.CALENDAR_YEAR || T.CALENDAR_MONTH AS CALENDAR_DATE,
                    </otherwise>
                </choose>
                ROUND(${valueColumn}, 3)                                  AS VALUE
                FROM BASE t
                GROUP BY
                <foreach collection="report4SelectedColumns" item="item">
                    ${item},
                </foreach>
                <choose>
                    <when test='report4ViewType == "VIEW_BY_DAY".toString()'>
                        T.${dateType}
                    </when>
                    <when test='report4ViewType == "VIEW_BY_WEEK".toString()'>
                        T.CALENDAR_YEAR || T.CALENDAR_WEEK_NO
                    </when>
                    <when test='report4ViewType == "VIEW_BY_MONTH".toString()'>
                        T.CALENDAR_YEAR || T.CALENDAR_MONTH
                    </when>
                    <otherwise>
                        T.CALENDAR_YEAR || T.CALENDAR_MONTH
                    </otherwise>
                </choose>
                )
                SELECT *
                FROM (
                SELECT  <foreach collection="report4SelectedColumns" item="item">
                            NVL(${item}, 'Others')                                    AS ${item},
                        </foreach>
                NVL(T.CALENDAR_DATE, 'Others')                                AS CALENDAR_DATE,
                NVL(T.VALUE, 0) AS TOTAL
                FROM TEMP T
                ) mm
                PIVOT (SUM(TOTAL) AS TOTAL
                FOR CALENDAR_DATE
                IN (
                <foreach collection="report4ColumnNames" separator="," item="item">
                    '${item}'
                </foreach>
                ))
                ORDER BY
                <foreach collection="report4SelectedColumns" item="item" separator=",">
                    DECODE(${item}, 'Others', 'zzz', ${item})
                </foreach>
            </otherwise>
        </choose>

    </sql>

    <sql id="report4DetailsSQL">
        WITH TEMP AS (
        <choose>
            <when test="dateType == 'DATE$'.toString()">
                <include refid="queryBaseHist">
                    <property name="excludeSnapshotDate" value="Y"/>
                </include>
            </when>
            <otherwise>
                <include refid="queryBaseHist">
                    <property name="excludeSnapshotDate" value="N"/>
                </include>
            </otherwise>
        </choose>)
        SELECT * FROM TEMP T
        WHERE 1 = 1
        <if test="report4SelectedDate != null and report4SelectedDate != ''.toString()">
            <choose>
                <when test="dateType == 'DATE$'.toString()">
                    AND t.DATE$ = TO_DATE(#{report4SelectedDate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
                </when>
                <otherwise>
                    AND t.DATE$ = TO_DATE(#{specificDate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
                    <choose>
                        <when test='report3SelectedType == "VIEW_BY_DAY".toString()'>
                            AND T.${dateType} = TO_DATE(#{report4SelectedDate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
                        </when>
                        <when test='report3SelectedType == "VIEW_BY_WEEK".toString()'>
                            AND T.CALENDAR_YEAR || T.CALENDAR_WEEK_NO = #{report4SelectedDate, jdbcType=VARCHAR}
                        </when>
                        <when test='report3SelectedType == "VIEW_BY_MONTH".toString()'>
                            AND T.CALENDAR_YEAR || T.CALENDAR_MONTH = #{report4SelectedDate, jdbcType=VARCHAR}
                        </when>
                    </choose>
                </otherwise>
            </choose>
        </if>
        <foreach collection="report4SelectedColumns" item="item" index="index">
            <if test="report4SelectedValues[index] != null and report4SelectedValues[index] != ''.toString()">
                and NVL(t.${item}, 'Others') = #{report4SelectedValues[${index}], jdbcType=VARCHAR}
            </if>
        </foreach>
    </sql>

    <select id="queryReport4DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="report4DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport4Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report4DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>
</mapper>
