package com.scp.components.service;

import com.starter.context.bean.Response;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Map;

public interface IComponentsService {

    Response refreshO2LongTail();

    Response refreshMaterial2ProductionLine();

    Response uploadMppFcst(String userid, String uploadVersion, MultipartFile file) throws Exception;

    Response initMppPage(String userid);

    void downloadMppTemplate(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadMppFcstData(String userid, Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryMppFcstColumns(String fcst_version);

    Response queryMppFcstData(String userid, Map<String, Object> parameterMap);

    Response saveMppForcastSource(String userid, Map<String, Object> parameterMap);

    Response otdsUploadReport(String userid, String uploadVersion, MultipartFile file) throws Exception;

    Response initOtdsPage();

    Response queryOtdsReportData(Map<String, Object> parameterMap);

    Response refreshOtdsOfficialReportMV();

    void downloadOTDSTemplate(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadOTDSData(Map<String, Object> parameterMap, HttpServletResponse response);

    Response saveOtdsData(String userid, Map<String, Object> parameterMap);

    Response initMppCommitPage(String userid);

    Response uploadMppCommitData(String userid, String uploadVersion, MultipartFile file) throws Exception;

    void downloadMppCommitTemplate(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadMppCommitData(String userid, Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryMppCommitColumns(String commitVersion);

    Response queryMppCommitData(String userid, Map<String, Object> parameterMap);

    Response saveMppCommitSource(String userid, Map<String, Object> parameterMap);

    Response sliceInitPage(String userid, Map<String, Object> parameterMap);

    Response querySliceColumns(Map<String, Object> parameterMap);

    void downloadSliceTemplate(Map<String, Object> parameterMap, HttpServletResponse response);

    Response uploadSlice(String userid, String uploadVersion, MultipartFile file) throws Exception;

    Response querySliceData(Map<String, Object> parameterMap);

    void downloadSliceData(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryManualSliceData(Map<String, Object> parameterMap);

    void downloadManualSliceData(Map<String, Object> parameterMap, HttpServletResponse response);

    Response saveManualSliceData(String userid, Map<String, Object> parameterMap);

    Response queryMaterialOwnerData(Map<String, Object> parameterMap);

    Response materialOwnerUploadData(String userid, MultipartFile file) throws Exception;

    Response materialOwnerUploadDataApplyConflict(String userid, Map<String, Object> parameterMap);

    Response queryMaterialOwnerInvalidData(Map<String, Object> parameterMap);

    void downloadMaterialOwnerData(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadMaterialOwnerInvalidData(Map<String, Object> parameterMap, HttpServletResponse response);

    Response deleteAbnormalMaterial(Map<String, Object> parameterMap);

    Response queryMaterialOwnerInitPage(Map<String, Object> parameterMap);

    Response siopInitPage(String userid, Map<String, Object> parameterMap);

    Response querySiopColumns(Map<String, Object> parameterMap);

    void downloadSiopTemplate(Map<String, Object> parameterMap, HttpServletResponse response);

    Response uploadSiop(String userid, String uploadVersion, MultipartFile file) throws Exception;

    Response querySiopData(Map<String, Object> parameterMap);

    void downloadSiopData(Map<String, Object> parameterMap, HttpServletResponse response);

    Response saveSiopData(String userid, Map<String, Object> parameterMap);

    void refreshSiopData();

    Response queryConsignmentTimeLimitData(Map<String, Object> parameterMap);

    void downloadConsignmentTimeLimitData(Map<String, Object> parameterMap, HttpServletResponse response);

    Response saveConsignmentTimeLimitData(Map<String, Object> parameterMap, String userid);

    void downloadConsignmentTimeLimitTemplate(Map<String, Object> parameterMap, HttpServletResponse response);

    Response uploadConsignmentTimeLimitData(String userid, MultipartFile file) throws IOException;

    Response queryWechatBotPresetationReport(Map<String, Object> parameterMap);

    Response cloUploadReport(String userid, MultipartFile file) throws Exception;

    Response initCloPage();

    Response queryCloReportData(Map<String, Object> parameterMap);

    void downloadCloTemplate(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadCloData(Map<String, Object> parameterMap, HttpServletResponse response);

    Response saveCloData(String userid, Map<String, Object> parameterMap);

    Response queryMarkdownBySql(Map<String, Object> parameterMap);

    Response productGroupUpload(String userid, MultipartFile file) throws Exception;

    Response queryProductGroupData(Map<String, Object> parameterMap);

    void downloadProductGroupTemplate(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadProductGroupData(Map<String, Object> parameterMap, HttpServletResponse response);

    Response saveProductGroupData(String userid, Map<String, Object> parameterMap);

    Response productGroupInitPage();
}
