package com.scp.components.service.impl;

import com.adm.system.service.ISystemService;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.scp.components.bean.*;
import com.scp.components.dao.IComponentsDao;
import com.scp.components.service.IComponentsService;
import com.scp.simulation.feign.BCDFeignClient;
import com.starter.context.bean.*;
import com.starter.context.bean.scptable.ScpTableCell;
import com.starter.context.bean.scptable.ScpTableHelper;
import com.starter.context.configuration.database.DatabaseType;
import com.starter.context.configuration.database.DynamicDataSource;
import com.starter.context.configuration.database.TargetDataSource;
import com.starter.context.mail.MailBean;
import com.starter.context.mail.MailFeignClient;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.MarkdownUtil;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import com.starter.utils.excel.SimpleSheetContentsHandler;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.builder.xml.XMLMapperBuilder;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.session.SqlSessionFactory;
import org.apache.poi.xssf.model.StylesTable;
import org.jetbrains.annotations.NotNull;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.sql.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.*;
import java.util.stream.Stream;

@Service("componentsService")
@Scope("prototype")
@Transactional
public class ComponentsServiceImpl extends ServiceHelper implements IComponentsService {

    //region Define region
    @Resource
    private IComponentsDao componentsDao;

    @Resource
    private Response response;

    @Resource
    private BCDFeignClient bcdFeignClient;

    @Resource
    private ExcelTemplate excelTemplate;

    @Resource
    private MailFeignClient mailFeignClient;

    @Resource
    private ISystemService systemService;

    @Resource
    private ScpTableHelper scpTableHelper;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private SqlSessionFactory sqlSessionFactory;

    @Resource
    private DynamicDataSource dynamicDataSource;
    //endregion

    // region O2 Long Tail
    @Override
    public Response refreshO2LongTail() {
        componentsDao.refreshO2LongTail();
        return response;
    }
    // endregion

    //region MPP Demand FCST
    @Override
    public Response refreshMaterial2ProductionLine() {
        componentsDao.refreshMaterial2ProductionLine();
        return response;
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initMppPage(String userid) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("fcstVersion", componentsDao.queryFcstVersion());
        return response.setBody(resultMap);
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response uploadMppFcst(String userid, String uploadVersion, MultipartFile file) throws Exception {
        File tempFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), ".tmp");
        file.transferTo(tempFile);

        final String version = StringUtils.isBlank(uploadVersion) ? new SimpleDateFormat("yyyyMM").format(new Date()) : uploadVersion;

        List<MPPFcstBean> data = new ArrayList<>();
        excelTemplate.read(tempFile, 1, new SimpleSheetContentsHandler() {
            @Override
            public void handleRow(int rowNum) {

                if (rowNum == 0) {
                    return;
                }

                int i = 0;
                MPPFcstBean bean = new MPPFcstBean();
                bean.setMaterial(row.get(i++));
                bean.setSales_org(row.get(i++));
                bean.setDemand_details(row.get(i++));
                bean.setCustomer_code(row.get(i++));
                bean.setParent_site(row.get(i++));
                bean.setMonth01(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth02(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth03(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth04(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth05(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth06(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth07(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth08(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth09(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth10(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth11(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth12(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth13(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth14(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth15(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth16(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth17(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth18(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth19(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth20(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth21(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth22(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth23(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth24(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth25(Utils.parseBigDecimal(row.get(i), 2));

                data.add(bean);

                if (data.size() >= 1000) {
                    componentsDao.insertFsctDataTemp(version, data);
                    data.clear();
                }
            }
        }, new StylesTable());

        if (data.size() > 0) {
            componentsDao.insertFsctDataTemp(version, data);
            data.clear();
        }

        componentsDao.deleteFcstData(userid, version);
        componentsDao.mergeFcstData(userid);

        if (tempFile.delete() == false) {
            System.err.println(tempFile.getAbsolutePath() + " delete failed");
        }

        return response;
    }

    @Override
    public void downloadMppTemplate(Map<String, Object> parameterMap, HttpServletResponse response) {
        String uploadVersion = (String) parameterMap.get("uploadVersion");
        uploadVersion = StringUtils.isBlank(uploadVersion) ? new SimpleDateFormat("yyyyMM").format(new Date()) : uploadVersion;
        String fileName = "mpp_fcst_tempalte_" + uploadVersion + ".xlsx";
        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        resultList.add(map);
        map.put("Material", null);
        map.put("Factory", null);
        map.put("Demand Details", null);
        map.put("Customer Code", null);
        map.put("Parent Site", null);
        for (int i = 0; i < 25; i++) {
            map.put(this.convertMonth(uploadVersion), null);
            uploadVersion = this.addMonth(uploadVersion);
        }
        excelTemplate.create(response, fileName, resultList);
    }

    @Override
    public void downloadMppFcstData(String userid, Map<String, Object> parameterMap, HttpServletResponse response) {
        String fcstVersion = (String) parameterMap.get("fcstVersion");
        fcstVersion = StringUtils.isBlank(fcstVersion) ? new SimpleDateFormat("yyyyMM").format(new Date()) : fcstVersion;
        String fileName = "mpp_fcst_data_" + fcstVersion + ".xlsx";
        parameterMap.put("fcst_version", fcstVersion);
        parameterMap.put("userid", userid);
        List<Map<String, String>> columns = new ArrayList<>();
        for (int i = 1; i <= 25; i++) {
            Map<String, String> map = new HashMap<>();
            map.put("value", "MONTH" + (i < 10 ? "0" + i : i));
            map.put("name", this.convertMonth(fcstVersion));
            fcstVersion = this.addMonth(fcstVersion);
            columns.add(map);
        }
        parameterMap.put("columns", columns);

        excelTemplate.create(response, fileName, "com.scp.components.dao.IComponentsDao.downloadMppFcstData", parameterMap);
    }

    @Override
    public Response queryMppFcstColumns(String fcst_version) {
        List<Map<String, String>> resultList = new ArrayList<>();
        for (int i = 1; i <= 25; i++) {
            Map<String, String> map = new HashMap<>();
            map.put("data", "MONTH" + (i < 10 ? "0" + i : i));
            map.put("title", this.convertMonth(fcst_version));
            fcst_version = this.addMonth(fcst_version);
            resultList.add(map);
        }

        return response.setBody(resultList);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryMppFcstData(String userid, Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        parameterMap.put("userid", userid);
        int total = componentsDao.queryMppFcstDataCount(parameterMap);
        page.setTotal(total);
        if (total > 0) {
            page.setData(componentsDao.queryMppFcstData(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    @SuppressWarnings("unchecked")
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response saveMppForcastSource(String userid, Map<String, Object> parameterMap) {
        Map<String, Object> updateMap = (Map<String, Object>) parameterMap.get("forcastSourceUpdated");
        Map<String, Object> param = new HashMap<>();
        for (String key : updateMap.keySet()) {
            List<Map<String, Object>> cols = new ArrayList<>();
            param.put("rowid", key);
            param.put("cols", cols);

            Map<String, Object> colMap = (Map<String, Object>) updateMap.get(key);
            for (String k : colMap.keySet()) {
                if (Utils.hasInjectionAttack(k) == true) {
                    return response.setError(new Exception("Invalid update parameters!"));
                }

                Map<String, Object> col = new HashMap<>();
                col.put("key", k);
                col.put("value", colMap.get(k));
                cols.add(col);
            }
            param.put("userid", userid);
            componentsDao.saveMppForcastSource(param);
        }
        return response;
    }
    // endregion

    // region OTDS Offical Report
    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response otdsUploadReport(String userid, String uploadVersion, MultipartFile file) throws Exception {
        File tempFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), ".tmp");
        file.transferTo(tempFile);

        if (StringUtils.isBlank(uploadVersion)) {
            return response.set(Status.FORBIDDEN, "OTDS Version invalid");
        }

        List<OTDSReport> data = new ArrayList<>();
        excelTemplate.read(tempFile, 1, new SimpleSheetContentsHandler() {
            @Override
            public void handleRow(int rowNum) {

                if (rowNum == 0) {
                    return;
                }

                int i = 0;
                OTDSReport bean = new OTDSReport();
                bean.setSALES_ORDER_NUMBER(row.get(i++));
                bean.setSALES_ORDER_ITEM(row.get(i++));
                bean.setHIGHER_LEVEL_ITEM(row.get(i++));
                bean.setSTOCK_INDENT(row.get(i++));
                bean.setSALES_ORGANIZATION(row.get(i++));
                bean.setONTIME(row.get(i++));
                bean.setREA_SOBLOCK(row.get(i++));
                bean.setREA_PUR(row.get(i++));
                bean.setREA_VENDOR(row.get(i++));
                bean.setREA_INV(row.get(i++));
                bean.setREA_GRPAGE(row.get(i++));
                bean.setREA_DEL_CREN_BLOCK(row.get(i++));
                bean.setREA_WAREHOUSE(row.get(i++));
                bean.setREA_DEL_CREAT(row.get(i++));
                bean.setREA_CUST_BLOCK(row.get(i++));
                bean.setREA_FINANCE(row.get(i++));
                bean.setREA_OTHER(row.get(i++));
                bean.setREA_CUSTOVERBLK(row.get(i++));
                bean.setREA_DEL_GIBLOCK(row.get(i++));
                bean.setMATERIAL(row.get(i++));
                bean.setITEM_CATEGORY(row.get(i++));
                bean.setMAT_PRC_GRP(row.get(i++));
                bean.setPRODUCT_LINE(row.get(i++));
                bean.setCOMPLETE_DELIV_IND(row.get(i++));
                bean.setDEL_GROUP(row.get(i++));
                bean.setPLANT_CODE(row.get(i++));
                bean.setSALES_DISTRICT(row.get(i++));
                bean.setCUSTOMER_GROUP(row.get(i++));
                bean.setVENDOR_CODE(row.get(i++));
                bean.setPR_NUMBER(row.get(i++));
                bean.setMO_NUMBER(row.get(i++));
                bean.setSO04_CRD(row.get(i++));
                bean.setCREATED_DATE(row.get(i++));
                bean.setDELIVERY_RELEASE_DATE(row.get(i++));
                bean.setSALES_ORDER_RELEASE(row.get(i++));
                bean.setFIRST_DELIVERY_DATE(row.get(i++));
                bean.setMATERIAL_AVAILIBALE_DATE(row.get(i++));
                bean.setCONFIRMED_DATE(row.get(i++));
                bean.setPURCHASE_GR_DATE(row.get(i++));
                bean.setDELIVERY_CREATE_DATE(row.get(i++));
                bean.setDELIVERY_PACKING_DATE(row.get(i++));
                bean.setDELIVERY_PICKING_DATE(row.get(i++));
                bean.setMO_CONFRIMED_FINISH_DATE(row.get(i++));
                bean.setPURCHASE_CREATE_DATE(row.get(i++));
                bean.setMO_SCHEDULE_FINISH_DATE(row.get(i++));
                bean.setPO_STATISTIC_DATE(row.get(i++));
                bean.setDELIVERY_GOODS_ISSUE_DATE(row.get(i++));
                bean.setSOLD_TO(row.get(i++));
                bean.setBASE_UNIT(row.get(i++));
                bean.setNUM_DOC(Utils.parseBigDecimal(row.get(i++), null));
                bean.setORDER_QTY_RAW(row.get(i++));
                bean.setINVOICED_QUANTITY_RAW(row.get(i++));
                bean.setINVOICED_AMOUNT_RAW(row.get(i++));
                bean.setDELAY_DAYS(Utils.parseInt(row.get(i), null));

                data.add(bean);
                if (data.size() >= 256) {
                    componentsDao.insertOtdsReportDataTemp(uploadVersion, data);
                    data.clear();
                }
            }
        }, new StylesTable());

        if (data.size() > 0) {
            componentsDao.insertOtdsReportDataTemp(uploadVersion, data);
            data.clear();
        }

        componentsDao.deleteOtdsReportData(uploadVersion);
        componentsDao.mergeOtdsReportData(userid);

        if (tempFile.delete() == false) {
            System.err.println(tempFile.getAbsolutePath() + " delete failed");
        }

        return response;
    }

    private volatile static boolean RUN_REFRESH_OTDS_OFFICIAL_REPORT = false; // 执行控制, 防止重复执行
    private volatile static String RUN_REFRESH_OTDS_OFFICIAL_REPORT_TIME = "";

    public Response refreshOtdsOfficialReportMV() {
        if (RUN_REFRESH_OTDS_OFFICIAL_REPORT == true) {
            return response.setBody(RUN_REFRESH_OTDS_OFFICIAL_REPORT_TIME + " 已经开始一项任务, 请不要重复执行");
        }

        try {
            RUN_REFRESH_OTDS_OFFICIAL_REPORT = true;
            RUN_REFRESH_OTDS_OFFICIAL_REPORT_TIME = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss").format(new Date());
            componentsDao.refreshOtdsOfficialReportMV(); //执行时间太长, 手动执行
            Set<String> stringSet = redisTemplate.keys("scp-service-prod:1d::OTDSServiceImpl.c.s.c.s.i:*");
            if (stringSet != null) {
                redisTemplate.delete(stringSet);
            }
        } catch (Exception e) {
            response.setError(e);
        } finally {
            RUN_REFRESH_OTDS_OFFICIAL_REPORT = false;
            RUN_REFRESH_OTDS_OFFICIAL_REPORT_TIME = "";
        }

        return response.setBody("Data synchronized!");
    }


    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initOtdsPage() {
        Map<String, Object> resultMap = new HashMap<>();
        List<String> uploadVersions = new ArrayList<>();

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.YEAR, -1);
        calendar.set(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_YEAR, 1);
        do {
            String week = (calendar.get(Calendar.WEEK_OF_YEAR) < 10 ? "0" + calendar.get(Calendar.WEEK_OF_YEAR) : String.valueOf(calendar.get(Calendar.WEEK_OF_YEAR)));
            String y = calendar.get(Calendar.YEAR) + week;
            if (calendar.get(Calendar.MONTH) >= 11 && calendar.get(Calendar.WEEK_OF_YEAR) <= 1) {
                uploadVersions.add(calendar.get(Calendar.YEAR) + "53");
            }
            if (uploadVersions.contains(y)) {
                uploadVersions.add((calendar.get(Calendar.YEAR) + 1) + week);
            } else {
                uploadVersions.add(y);
            }
            calendar.add(Calendar.WEEK_OF_YEAR, 1);
        } while (calendar.getTimeInMillis() < System.currentTimeMillis());

        Collections.reverse(uploadVersions);

        resultMap.put("reportVersion", componentsDao.queryOtdsReportVersion());
        resultMap.put("uploadVersions", uploadVersions);
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryOtdsReportData(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        int total = componentsDao.queryOtdsReportDataCount(parameterMap);
        page.setTotal(total);
        if (total > 0) {
            page.setData(componentsDao.queryOtdsReportData(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadOTDSTemplate(Map<String, Object> parameterMap, HttpServletResponse response) {
        String fileName = "otds_report_tempalte.xlsx";
        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        resultList.add(map);
        map.put("SALES_ORDER_NUMBER", null);
        map.put("SALES_ORDER_ITEM", null);
        map.put("HIGHER_LEVEL_ITEM", null);
        map.put("STOCK_INDENT", null);
        map.put("SALES_ORGANIZATION", null);
        map.put("ONTIME", null);
        map.put("REA_SOBLOCK", null);
        map.put("REA_PUR", null);
        map.put("REA_VENDOR", null);
        map.put("REA_INV", null);
        map.put("REA_GRPAGE", null);
        map.put("REA_DEL_CREN_BLOCK", null);
        map.put("REA_WAREHOUSE", null);
        map.put("REA_DEL_CREAT", null);
        map.put("REA_CUST_BLOCK", null);
        map.put("REA_FINANCE", null);
        map.put("REA_OTHER", null);
        map.put("REA_CUSTOVERBLK", null);
        map.put("REA_DEL_GIBLOCK", null);
        map.put("MATERIAL", null);
        map.put("ITEM_CATEGORY", null);
        map.put("MAT_PRC_GRP", null);
        map.put("PRODUCT_LINE", null);
        map.put("COMPLETE_DELIV_IND", null);
        map.put("DEL_GROUP", null);
        map.put("PLANT_CODE", null);
        map.put("SALES_DISTRICT", null);
        map.put("CUSTOMER_GROUP", null);
        map.put("VENDOR_CODE", null);
        map.put("PR_NUMBER", null);
        map.put("MO_NUMBER", null);
        map.put("SO04_CRD", null);
        map.put("CREATED_DATE", null);
        map.put("DELIVERY_RELEASE_DATE", null);
        map.put("SALES_ORDER_RELEASE", null);
        map.put("FIRST_DELIVERY_DATE", null);
        map.put("MATERIAL_AVAILIBALE_DATE", null);
        map.put("CONFIRMED_DATE", null);
        map.put("PURCHASE_GR_DATE", null);
        map.put("DELIVERY_CREATE_DATE", null);
        map.put("DELIVERY_PACKING_DATE", null);
        map.put("DELIVERY_PICKING_DATE", null);
        map.put("MO_CONFRIMED_FINISH_DATE", null);
        map.put("PURCHASE_CREATE_DATE", null);
        map.put("MO_SCHEDULE_FINISH_DATE", null);
        map.put("PO_STATISTIC_DATE", null);
        map.put("DELIVERY_GOODS_ISSUE_DATE", null);
        map.put("SOLD_TO", null);
        map.put("BASE_UNIT", null);
        map.put("NUM_DOC", null);
        map.put("ORDER_QTY", null);
        map.put("INVOICED_QUANTITY", null);
        map.put("INVOICED_AMOUNT", null);
        map.put("DELAY_DAYS", null);

        excelTemplate.create(response, fileName, resultList);
    }

    @Override
    public void downloadOTDSData(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String reportVersion = (String) parameterMap.get("report_version");
        String fileName = "otds_official_data_" + reportVersion + ".xlsx";
        parameterMap.put("report_version", reportVersion);
        excelTemplate.create(response, fileName, "com.scp.components.dao.IComponentsDao.downloadOTDSData", parameterMap);
    }

    @Override
    @SuppressWarnings("unchecked")
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response saveOtdsData(String userid, Map<String, Object> parameterMap) {
        Map<String, Object> updateMap = (Map<String, Object>) parameterMap.get("reportSourceUpdated");
        Map<String, Object> param = new HashMap<>();
        for (String key : updateMap.keySet()) {
            List<Map<String, Object>> cols = new ArrayList<>();
            param.put("rowid", key);
            param.put("cols", cols);


            Map<String, Object> colMap = (Map<String, Object>) updateMap.get(key);
            for (String k : colMap.keySet()) {
                if (Utils.hasInjectionAttack(k) == true) {
                    return response.setError(new Exception("Invalid update parameters!"));
                }

                Map<String, Object> col = new HashMap<>();
                col.put("key", k);
                if (k.endsWith("_DATE") || "SO04_CRD".equalsIgnoreCase(k) || "SALES_ORDER_RELEASE".equalsIgnoreCase(k)) {
                    col.put("type", "date");
                }
                col.put("value", colMap.get(k));
                cols.add(col);
            }
            param.put("userid", userid);
            componentsDao.saveOtdsData(param);
        }
        return response;
    }
    // endregion

    //region MPP Commit
    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initMppCommitPage(String userid) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("commitVersion", componentsDao.queryMppCommitVersion());
        return response.setBody(resultMap);
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response uploadMppCommitData(String userid, String uploadVersion, MultipartFile file) throws Exception {
        File tempFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), ".tmp");
        file.transferTo(tempFile);

        final String version = StringUtils.isBlank(uploadVersion) ? new SimpleDateFormat("yyyyMM").format(new Date()) : uploadVersion;

        List<MPPCommitBean> data = new ArrayList<>();
        excelTemplate.read(tempFile, 1, new SimpleSheetContentsHandler() {
            @Override
            public void handleRow(int rowNum) {

                if (rowNum == 0) {
                    return;
                }

                int i = 0;
                MPPCommitBean bean = new MPPCommitBean();
                bean.setMaterial(row.get(i++));
                bean.setSales_org(row.get(i++));
                bean.setCustomer_code(row.get(i++));
                bean.setMonth01(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth02(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth03(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth04(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth05(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth06(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth07(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth08(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth09(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth10(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth11(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth12(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth13(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth14(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth15(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth16(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth17(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth18(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth19(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth20(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth21(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth22(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth23(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth24(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth25(Utils.parseBigDecimal(row.get(i), 2));

                data.add(bean);

                if (data.size() >= 1000) {
                    componentsDao.insertMppCommitDataTemp(version, data);
                    data.clear();
                }
            }
        }, new StylesTable());

        if (data.size() > 0) {
            componentsDao.insertMppCommitDataTemp(version, data);
            data.clear();
        }

        componentsDao.deleteMppCommitData(userid, version);
        componentsDao.mergeMppCommitData(userid);

        if (tempFile.delete() == false) {
            System.err.println(tempFile.getAbsolutePath() + " delete failed");
        }

        return response;
    }

    @Override
    public void downloadMppCommitTemplate(Map<String, Object> parameterMap, HttpServletResponse response) {
        String uploadVersion = (String) parameterMap.get("uploadVersion");
        uploadVersion = StringUtils.isBlank(uploadVersion) ? new SimpleDateFormat("yyyyMM").format(new Date()) : uploadVersion;
        String fileName = "mpp_commit_tempalte_" + uploadVersion + ".xlsx";
        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        resultList.add(map);
        map.put("Material", null);
        map.put("Site", null);
        map.put("Customer Code", null);
        for (int i = 0; i < 25; i++) {
            map.put(this.convertMonth(uploadVersion), null);
            uploadVersion = this.addMonth(uploadVersion);
        }
        excelTemplate.create(response, fileName, resultList);
    }

    @Override
    public void downloadMppCommitData(String userid, Map<String, Object> parameterMap, HttpServletResponse response) {
        String commitVersion = (String) parameterMap.get("fcstVersion");
        commitVersion = StringUtils.isBlank(commitVersion) ? new SimpleDateFormat("yyyyMM").format(new Date()) : commitVersion;
        String fileName = "mpp_commit_data_" + commitVersion + ".xlsx";
        parameterMap.put("commit_version", commitVersion);
        parameterMap.put("userid", userid);
        List<Map<String, String>> columns = new ArrayList<>();
        for (int i = 1; i <= 25; i++) {
            Map<String, String> map = new HashMap<>();
            map.put("value", "MONTH" + (i < 10 ? "0" + i : i));
            map.put("name", this.convertMonth(commitVersion));
            commitVersion = this.addMonth(commitVersion);
            columns.add(map);
        }
        parameterMap.put("columns", columns);

        excelTemplate.create(response, fileName, "com.scp.components.dao.IComponentsDao.downloadMppCommitData", parameterMap);
    }

    @Override
    public Response queryMppCommitColumns(String commitVersion) {
        List<Map<String, String>> resultList = new ArrayList<>();
        for (int i = 1; i <= 25; i++) {
            Map<String, String> map = new HashMap<>();
            map.put("data", "MONTH" + (i < 10 ? "0" + i : i));
            map.put("title", this.convertMonth(commitVersion));
            commitVersion = this.addMonth(commitVersion);
            resultList.add(map);
        }

        return response.setBody(resultList);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryMppCommitData(String userid, Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        parameterMap.put("userid", userid);
        int total = componentsDao.queryMppCommitDataCount(parameterMap);
        page.setTotal(total);
        if (total > 0) {
            page.setData(componentsDao.queryMppCommitData(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    @SuppressWarnings("unchecked")
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response saveMppCommitSource(String userid, Map<String, Object> parameterMap) {
        Map<String, Object> updateMap = (Map<String, Object>) parameterMap.get("commitSourceUpdated");
        Map<String, Object> param = new HashMap<>();
        for (String key : updateMap.keySet()) {
            List<Map<String, Object>> cols = new ArrayList<>();
            param.put("rowid", key);
            param.put("cols", cols);

            Map<String, Object> colMap = (Map<String, Object>) updateMap.get(key);
            for (String k : colMap.keySet()) {
                if (Utils.hasInjectionAttack(k) == true) {
                    return response.setError(new Exception("Invalid update parameters!"));
                }

                Map<String, Object> col = new HashMap<>();
                col.put("key", k);
                col.put("value", colMap.get(k));
                cols.add(col);
            }
            param.put("userid", userid);
            componentsDao.saveMppCommitSource(param);
        }
        return response;
    }

    // endregion

    // region slice

    @Override
    public Response sliceInitPage(String userid, Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("sliceVersion", componentsDao.querySliceVersion());
        return response.setBody(resultMap);
    }

    @Override
    public Response querySliceColumns(Map<String, Object> parameterMap) {
        String selectedVersion = (String) parameterMap.get("selectedVersion");
        selectedVersion = StringUtils.isBlank(selectedVersion) ? new SimpleDateFormat("yyyyMM").format(new Date()) : selectedVersion;
        selectedVersion = (Utils.parseInt(StringUtils.substring(selectedVersion, 0, 4)) - 1) + "01";
        List<Map<String, String>> resultList = new ArrayList<>();
        for (int i = 1; i >= 0; i--) {
            for (int j = 1; j <= 12; j++) {
                Map<String, String> map = new HashMap<>();
                map.put("data", "M" + (j < 10 ? "0" + j : j) + "_Y-" + i);
                map.put("title", this.convertMonth(selectedVersion));
                selectedVersion = this.addMonth(selectedVersion);
                resultList.add(map);
            }
        }

        return response.setBody(resultList);
    }

    @Override
    public void downloadSliceTemplate(Map<String, Object> parameterMap, HttpServletResponse response) {
        String fileName = "slice_raw_data_template.xlsx";
        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        resultList.add(map);
        map.put("HFM_CODE", null);
        map.put("CLASS", null);
        map.put("PLANT_TYPE", null);
        map.put("CURRENCY_TYPE", null);
        map.put("CURRENCY", null);

        String version = (String) parameterMap.get("uploadVersion");
        version = (Utils.parseInt(StringUtils.substring(version, 0, 4)) - 1) + "01";
        for (int i = 1; i <= 24; i++) {
            map.put(Utils.convertMonth(version), null);
            version = Utils.addMonth(version);
        }

        excelTemplate.create(response, fileName, resultList);
    }

    @Override
    public Response uploadSlice(String userid, String uploadVersion, MultipartFile file) throws Exception {
        File tempFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), ".tmp");
        file.transferTo(tempFile);

        final String version = StringUtils.isBlank(uploadVersion) ? new SimpleDateFormat("yyyyMM").format(new Date()) : uploadVersion;

        List<SliceRawData> data = new ArrayList<>();
        excelTemplate.read(tempFile, 1, new SimpleSheetContentsHandler() {
            @Override
            public void handleRow(int rowNum) {

                if (rowNum == 0) {
                    return;
                }

                int i = 0;
                SliceRawData bean = new SliceRawData();
                bean.setVersion(version);
                bean.setHfm_code(row.get(i++));
                bean.setClazz(row.get(i++));
                bean.setPlant_type(row.get(i++));
                bean.setCurrency_type(row.get(i++));
                bean.setCurrency(row.get(i++));
                bean.setMonth01(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth02(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth03(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth04(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth05(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth06(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth07(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth08(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth09(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth10(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth11(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth12(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth13(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth14(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth15(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth16(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth17(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth18(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth19(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth20(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth21(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth22(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth23(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth24(Utils.parseBigDecimal(row.get(i), 2));

                data.add(bean);

                if (data.size() >= 1000) {
                    componentsDao.insertSliceDataTemp(data);
                    data.clear();
                }
            }
        }, new StylesTable());

        if (data.size() > 0) {
            componentsDao.insertSliceDataTemp(data);
            data.clear();
        }

        componentsDao.deleteSliceRawData(userid, version);
        componentsDao.mergeSliceRawData(userid);

        if (tempFile.delete() == false) {
            System.err.println(tempFile.getAbsolutePath() + " delete failed");
        }

        return response;
    }

    @Override
    public Response querySliceData(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(componentsDao.querySliceDataCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(componentsDao.querySliceData(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadSliceData(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        String fileName = "slice_raw_data_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.components.dao.IComponentsDao.downloadSliceData", parameterMap);
    }

    @Override
    public Response queryManualSliceData(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(componentsDao.queryManualSliceDataCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(componentsDao.queryManualSliceData(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadManualSliceData(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        String fileName = "slice_raw_data_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.components.dao.IComponentsDao.downloadManualSliceData", parameterMap);
    }

    @Override
    public Response saveManualSliceData(String userid, Map<String, Object> parameterMap) {
        scpTableHelper.setExcludeColumn(new ArrayList<>() {{
            add("ROW_ID");
        }});
        String version = (String) parameterMap.get("selectedVersion");
        scpTableHelper.setWarningMessage("You have no privileges to modify data does not belong to you");
        scpTableHelper.setScpTableInsertHandler((headers, inserts) -> componentsDao.createSliceData(userid, headers, inserts, version));
        scpTableHelper.setScpTableDeleteHandler(deletes -> componentsDao.deleteSliceData(userid, deletes, version));
        scpTableHelper.setScpTableUpdateHandler((pk, updates) -> componentsDao.updateSliceData(pk, updates, userid));
        return response.setBody(scpTableHelper.execCRUD(parameterMap));
    }

    //endregion

    // region siop

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response siopInitPage(String userid, Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("siopVersion", componentsDao.querySiopVersion());
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response querySiopColumns(Map<String, Object> parameterMap) {
        String selectedVersion = (String) parameterMap.get("selectedVersion");
        selectedVersion = StringUtils.isBlank(selectedVersion) ? new SimpleDateFormat("yyyyMM").format(new Date()) : selectedVersion;
        selectedVersion = (Utils.parseInt(StringUtils.substring(selectedVersion, 0, 4)) - 2) + "01";
        List<Map<String, String>> resultList = new ArrayList<>();
        String valueSelectedVersion = selectedVersion;
        String quantitySelectedVersion = selectedVersion;
        for (int i = 2; i >= 0; i--) {
            for (int j = 1; j <= 12; j++) {
                Map<String, String> map = new HashMap<>();
                map.put("data", "M" + (j < 10 ? "0" + j : j) + "_Y-" + i);
                map.put("title", "V-" + this.convertMonth(valueSelectedVersion));
                valueSelectedVersion = this.addMonth(valueSelectedVersion);
                resultList.add(map);
            }
        }
        for (int i = 1; i < 3; i++) {
            for (int j = 1; j <= 12; j++) {
                Map<String, String> map = new HashMap<>();
                map.put("data", "M" + (j < 10 ? "0" + j : j) + "_Y+" + i);
                map.put("title", "V-" + this.convertMonth(valueSelectedVersion));
                valueSelectedVersion = this.addMonth(valueSelectedVersion);
                resultList.add(map);
            }
        }

        for (int i = 2; i >= 0; i--) {
            for (int j = 1; j <= 12; j++) {
                Map<String, String> map = new HashMap<>();
                map.put("data", "Q_M" + (j < 10 ? "0" + j : j) + "_Y-" + i);
                map.put("title", "Q-" + this.convertMonth(quantitySelectedVersion));
                quantitySelectedVersion = this.addMonth(quantitySelectedVersion);
                resultList.add(map);
            }
        }
        for (int i = 1; i < 3; i++) {
            for (int j = 1; j <= 12; j++) {
                Map<String, String> map = new HashMap<>();
                map.put("data", "Q_M" + (j < 10 ? "0" + j : j) + "_Y+" + i);
                map.put("title", "Q-" + this.convertMonth(quantitySelectedVersion));
                quantitySelectedVersion = this.addMonth(quantitySelectedVersion);
                resultList.add(map);
            }
        }

        return response.setBody(resultList);
    }

    @Override
    public void downloadSiopTemplate(Map<String, Object> parameterMap, HttpServletResponse response) {
        String fileName = "siop_all_bu_data_template.xlsx";
        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        resultList.add(map);
        map.put("PRODUCT_LINE", null);
        map.put("SIOP_SCOPE", null);
        map.put("LOCAL_PRODUCT_LINE", null);
        map.put("LOCAL_PRODUCT_FAMILY", null);
        map.put("LOCAL_PRODUCT_SUB_FAMILY", null);
        map.put("ACC_LABEL", null);

        String version = (String) parameterMap.get("uploadVersion");
        version = (Utils.parseInt(StringUtils.substring(version, 0, 4)) - 2) + "01";
        String valueVersion = version;
        String qtyVersion = version;
        for (int i = 1; i <= 60; i++) {
            map.put(Utils.convertMonth(valueVersion), null);
            valueVersion = Utils.addMonth(valueVersion);
        }
        for (int i = 1; i <= 60; i++) {
            map.put("Q_" + Utils.convertMonth(qtyVersion), null);
            qtyVersion = Utils.addMonth(qtyVersion);
        }

        excelTemplate.create(response, fileName, resultList);
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = {"this.siopInitPage", "this.querySiopData"})
    public Response uploadSiop(String userid, String uploadVersion, MultipartFile file) throws Exception {
        File tempFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), ".tmp");
        file.transferTo(tempFile);

        final String version = StringUtils.isBlank(uploadVersion) ? new SimpleDateFormat("yyyyMM").format(new Date()) : uploadVersion;

        componentsDao.deleteSiopRawDataTemp(userid, version);

        List<SiopAllBuData> data = new ArrayList<>();
        excelTemplate.read(tempFile, 1, new SimpleSheetContentsHandler() {
            @Override
            public void handleRow(int rowNum) {

                if (rowNum == 0) {
                    return;
                }

                int i = 0;
                SiopAllBuData bean = new SiopAllBuData();
                bean.setFCST_VERSION(version);
                bean.setPRODUCT_LINE(row.get(i++));
                bean.setSIOP_SCOPE(row.get(i++));
                bean.setLOCAL_PRODUCT_LINE(row.get(i++));
                bean.setLOCAL_PRODUCT_FAMILY(row.get(i++));
                bean.setLOCAL_PRODUCT_SUB_FAMILY(row.get(i++));
                bean.setACC_LABEL(row.get(i++));
                bean.setMonth01Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth02Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth03Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth04Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth05Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth06Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth07Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth08Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth09Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth10Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth11Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth12Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth13Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth14Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth15Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth16Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth17Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth18Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth19Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth20Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth21Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth22Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth23Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth24Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth25Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth26Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth27Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth28Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth29Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth30Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth31Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth32Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth33Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth34Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth35Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth36Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth37Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth38Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth39Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth40Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth41Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth42Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth43Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth44Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth45Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth46Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth47Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth48Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth49Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth50Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth51Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth52Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth53Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth54Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth55Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth56Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth57Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth58Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth59Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth60Value(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth01Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth02Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth03Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth04Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth05Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth06Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth07Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth08Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth09Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth10Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth11Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth12Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth13Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth14Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth15Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth16Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth17Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth18Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth19Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth20Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth21Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth22Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth23Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth24Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth25Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth26Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth27Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth28Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth29Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth30Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth31Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth32Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth33Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth34Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth35Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth36Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth37Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth38Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth39Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth40Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth41Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth42Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth43Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth44Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth45Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth46Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth47Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth48Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth49Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth50Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth51Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth52Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth53Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth54Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth55Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth56Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth57Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth58Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth59Qty(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth60Qty(Utils.parseBigDecimal(row.get(i++), 2));

                data.add(bean);

                if (data.size() >= 1000) {
                    componentsDao.insertSiopDataTemp(data);
                    data.clear();
                }
            }
        }, new StylesTable());

        if (data.size() > 0) {
            componentsDao.insertSiopDataTemp(data);
            data.clear();
        }

        componentsDao.mergeSiopRawData(userid);

        if (!tempFile.delete()) {
            System.err.println(tempFile.getAbsolutePath() + " delete failed");
        }

        return response;
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response querySiopData(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(componentsDao.querySiopDataCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(componentsDao.querySiopData(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadSiopData(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        String fileName = "siop_all_bu_data" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.components.dao.IComponentsDao.downloadSiopData", parameterMap);
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = {"this.siopInitPage", "this.querySiopData"})
    public Response saveSiopData(String userid, Map<String, Object> parameterMap) {
        scpTableHelper.setExcludeColumn(new ArrayList<>() {{
            add("ROW_ID");
        }});
        String version = (String) parameterMap.get("selectedVersion");
        scpTableHelper.setWarningMessage("You have no privileges to modify data does not belong to you");
        scpTableHelper.setScpTableInsertHandler((headers, inserts) -> componentsDao.createSiopData(userid, headers, inserts, version));
        scpTableHelper.setScpTableDeleteHandler(deletes -> componentsDao.deleteSiopData(userid, deletes, version));
        scpTableHelper.setScpTableUpdateHandler((pk, updates) -> componentsDao.updateSiopData(pk, updates, userid));
        return response.setBody(scpTableHelper.execCRUD(parameterMap));
    }

    @Override
    public void refreshSiopData() {
        componentsDao.refreshSiopData();
    }
    //endregion

    // region Material Owner
    @Override
    public Response queryMaterialOwnerInitPage(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("materialOwnerList", componentsDao.queryMaterialOwnerList());
        return response.setBody(resultMap);
    }

    @Override
    public Response queryMaterialOwnerData(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(componentsDao.queryMaterialOwnerDataCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(componentsDao.queryMaterialOwnerData(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadMaterialOwnerData(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        String fileName = "material_owner_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.components.dao.IComponentsDao.queryMaterialOwnerData", parameterMap);
    }

    @Override
    public Response materialOwnerUploadData(String userid, MultipartFile file) throws Exception {
        File tempFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), ".tmp");
        file.transferTo(tempFile);

        List<MaterialOwnerBean> data = new ArrayList<>();
        final List<String> avalibleClaimType = new ArrayList<>() {{
            add("MATERIAL");
            add("MRP_CONTROLLER");
            add("PURCHASING_GROUP");
            add("VENDOR_CODE");
            add("MRPCN#VENDOR_CODE");
        }};
        excelTemplate.read(tempFile, 1, new SimpleSheetContentsHandler() {
            @Override
            public void handleRow(int rowNum) {

                if (rowNum == 0) {
                    return;
                }

                int i = 0;
                MaterialOwnerBean bean = new MaterialOwnerBean();
                bean.setPlantCode(row.get(i++));
                bean.setClaimData(row.get(i++));
                bean.setClaimType(row.get(i++));
                bean.setComments(row.get(i));

                if (StringUtils.isBlank(bean.getClaimType()) || avalibleClaimType.contains(bean.getClaimType()) == false) {
                    throw new SCPRuntimeException("Invalid claim type: " + (StringUtils.isBlank(bean.getClaimType()) ? "null" : bean.getClaimType()));
                }

                data.add(bean);

                if (data.size() >= 1000) {
                    componentsDao.insertMaterialOwnerTemp(data);
                    data.clear();
                }
            }
        }, new StylesTable());

        if (data.size() > 0) {
            componentsDao.insertMaterialOwnerTemp(data);
            data.clear();
        }

        Map<String, Object> resultMap = new HashMap<>();
        // 检查是否有冲突数据, 如果有冲突显示到前台
        List<LinkedHashMap<String, Object>> conflictList = componentsDao.queryConflictData(userid);
        if (conflictList != null && conflictList.isEmpty() == false) {
            resultMap.put("tempFilePath", tempFile.getAbsolutePath());
            resultMap.put("conflictList", conflictList);
        } else {
            componentsDao.backupMaterialOwner(userid);
            componentsDao.deleteMaterialOwner(userid);
            componentsDao.syncMaterialOwner(userid);

            if (tempFile.delete() == false) {
                System.err.println(tempFile.getAbsolutePath() + " delete failed");
            }
        }
        return response.setBody(resultMap);
    }

    @Override
    public Response materialOwnerUploadDataApplyConflict(String userid, Map<String, Object> parameterMap) {
        File tempFile = new File((String) parameterMap.get("confictDataFile"));
        if (tempFile.exists() == false) {
            return response.setBody("File does not exist!");
        }
        List<MaterialOwnerBean> data = new ArrayList<>();
        excelTemplate.read(tempFile, 1, new SimpleSheetContentsHandler() {
            @Override
            public void handleRow(int rowNum) {
                if (rowNum == 0) {
                    return;
                }

                int i = 0;
                MaterialOwnerBean bean = new MaterialOwnerBean();
                bean.setPlantCode(row.get(i++));
                bean.setClaimData(row.get(i++));
                bean.setClaimType(row.get(i++));
                bean.setComments(row.get(i));

                data.add(bean);

                if (data.size() >= 1000) {
                    componentsDao.insertMaterialOwnerTemp(data);
                    data.clear();
                }
            }
        }, new StylesTable());

        if (data.size() > 0) {
            componentsDao.insertMaterialOwnerTemp(data);
            data.clear();
        }

        List<LinkedHashMap<String, Object>> conflictList = componentsDao.queryConflictData(userid);

        componentsDao.backupMaterialOwnerConflict(userid);
        componentsDao.deleteMaterialOwnerConflict(userid);
        componentsDao.syncMaterialOwner(userid);

        // send notice mail
        StringBuilder body = new StringBuilder();

        MailBean mailBean = new MailBean();
        mailBean.setSubject("【Material Owner Changed】Your materials has been transferred");
        List<String> to = new ArrayList<>();
        for (Map<String, Object> map : conflictList) {
            String orgOwner = map.get("ORG_OWNER") + "@se.com";
            if (to.contains(orgOwner) == false) {
                to.add(orgOwner);
            }
        }
        mailBean.setTo(StringUtils.join(to, ","));
        List<String> cc = new ArrayList<>();
        cc.add(userid + "@se.com");
        mailBean.setCc(StringUtils.join(cc, ","));

        try {
            File attachementFile = excelTemplate.createAsFile(tempFile.getAbsolutePath() + "_" + Utils.randomStr(4), conflictList);
            mailBean.addAttachmentName("material_owner_changed_list.xlsx");
            mailBean.addAttachmentBytes(Utils.file2Base64(attachementFile));
        } catch (Exception e) {
            e.printStackTrace();
        }

        body.append("<div style='font-size:10pt;font-family:DengXian;'>");
        body.append("<p>");
        body.append("Your materials have been transferred, please refer to the attachment for details");
        body.append("</p>");

        String style = "<style>p{font-size: 10pt;font-family:DengXian;padding:0;margin:0} span{font-size: 10pt;font-family:DengXian;} div{font-size: 10pt;font-family:DengXian;}</style>";

        String signatrue = systemService.getMailSignature(userid);
        if (StringUtils.isBlank(signatrue)) {
            signatrue = "";
        }
        signatrue = "<br><br><br>" + signatrue;
        mailBean.setBody(style + body + signatrue);
        mailFeignClient.sendAsync(mailBean);

        if (tempFile.delete() == false) {
            System.err.println(tempFile.getAbsolutePath() + " delete failed");
        }

        return response;
    }

    @Override
    public Response queryMaterialOwnerInvalidData(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(componentsDao.queryMaterialOwnerInvalidDataCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(componentsDao.queryMaterialOwnerInvalidData(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadMaterialOwnerInvalidData(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        String fileName = "abnormal_material_owner_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.components.dao.IComponentsDao.queryMaterialOwnerInvalidData", parameterMap);
    }

    @Override
    public Response deleteAbnormalMaterial(Map<String, Object> parameterMap) {
        componentsDao.deleteAbnormalMaterial(parameterMap);
        return response;
    }
    // endregion

    // region consignment time limit
    public Response queryConsignmentTimeLimitData(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(componentsDao.queryConsignmentTimeLimitDataCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(componentsDao.queryConsignmentTimeLimitData(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadConsignmentTimeLimitData(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        String fileName = "consignment_time_limit_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.components.dao.IComponentsDao.queryConsignmentTimeLimitData", parameterMap);
    }

    @Override
    public Response saveConsignmentTimeLimitData(Map<String, Object> parameterMap, String userid) {
        scpTableHelper.setExcludeColumn(new ArrayList<>() {{
            add("PLANT_CODE");
            add("MATERIAL");
            add("VENDOR_CODE");
            add("CREATE_BY");
            add("CREATE_DATE");
            add("UPDATE_BY");
            add("UPDATE_DATE");
        }});
        scpTableHelper.setScpTableUpdateHandler((pk, updates) -> {
            String[] pks = pk.split(",");
            String plantCode = pks[0];
            String material = pks[1];
            String vendorCode = pks[2];
            ScpTableCell update = updates.get(0);
            String agingDay = (String) update.getValue(); // 因为这个报表, 只允许改一个theo aging day, 所以只要有值, 一定是aging day
            return componentsDao.updateConsignmentTimeLimitData(plantCode, material, vendorCode, Utils.parseDouble(agingDay, -1), userid);
        });
        return response.setBody(scpTableHelper.execCRUD(parameterMap));
    }

    @Override
    public void downloadConsignmentTimeLimitTemplate(Map<String, Object> parameterMap, HttpServletResponse response) {
        String fileName = "consignment_time_limit_template.xlsx";
        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        resultList.add(map);
        map.put("PLANT_CODE", null);
        map.put("MATERIAL", null);
        map.put("VENDOR_CODE", null);
        map.put("CONSIGN_TIME_LIMIT", null);
        excelTemplate.create(response, fileName, resultList);
    }

    @Override
    public Response uploadConsignmentTimeLimitData(String userid, MultipartFile file) throws IOException {
        File tempFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), ".tmp");
        file.transferTo(tempFile);

        List<Map<String, Object>> data = new ArrayList<>();
        excelTemplate.read(tempFile, 1, new SimpleSheetContentsHandler() {
            @Override
            public void handleRow(int rowNum) {
                if (rowNum == 0) {
                    return;
                }

                int i = 0;
                Map<String, Object> map = new HashMap<>();
                map.put("PLANT_CODE", row.get(i++));
                map.put("MATERIAL", row.get(i++));
                map.put("VENDOR_CODE", row.get(i++));
                map.put("CONSIGN_TIME_LIMIT", row.get(i));

                data.add(map);

                if (data.size() >= 1000) {
                    componentsDao.mergeConsignmentTimeLimitData(userid, data);
                    data.clear();
                }
            }
        }, new StylesTable());

        if (data.size() > 0) {
            componentsDao.mergeConsignmentTimeLimitData(userid, data);
            data.clear();
        }

        return response;
    }
    // endregion

    // region local functions
    private String convertMonth(String fullMonth) {
        if (StringUtils.length(fullMonth) != 6) {
            return fullMonth;
        }
        String year = fullMonth.substring(2, 4);
        String month = fullMonth.substring(4, 6);
        switch (month) {
            case "01":
                return "JAN-" + year;
            case "02":
                return "FEB-" + year;
            case "03":
                return "MAR-" + year;
            case "04":
                return "APR-" + year;
            case "05":
                return "MAY-" + year;
            case "06":
                return "JUN-" + year;
            case "07":
                return "JUL-" + year;
            case "08":
                return "AUG-" + year;
            case "09":
                return "SEP-" + year;
            case "10":
                return "OCT-" + year;
            case "11":
                return "NOV-" + year;
            case "12":
                return "DEC-" + year;
            default:
                return fullMonth;
        }
    }

    private String addMonth(String fullMonth) {
        if (StringUtils.length(fullMonth) != 6) {
            return fullMonth;
        }
        int year = Utils.parseInt(fullMonth.substring(0, 4));
        int month = Utils.parseInt(fullMonth.substring(4, 6));

        if (month == 12) {
            month = 1;
            year++;
        } else {
            month++;
        }
        return year + String.valueOf(month > 9 ? month : "0" + month);
    }
    // endregion

    // region wechat bot presetation
    @Override
    @TargetDataSource(DatabaseType.SCP02_READONLY)
    public Response queryWechatBotPresetationReport(Map<String, Object> parameterMap) {
        String markdown = "";
        String params = (String) parameterMap.get("params");

        Map<String, String> configMap = componentsDao.queryWechatBotPresetationReportByID(parameterMap);
        if (configMap == null || configMap.isEmpty()) {
            return response;
        }

        String config = configMap.get("CONFIG");
        JSONObject configObj = JSONObject.parseObject(config);

        String sql = configObj.getString("sql");
        parameterMap.put("sql", sql);

        if (StringUtils.isNotBlank(params)) {
            params = params.replace("，", ",");
            params = params.replace("；", ",");
            params = params.replace(";", ",");
            while (params.contains("  ")) {
                params = params.replace("  ", " ");
            }
            String[] ps = params.split(",");

            for (int i = 0; i < ps.length; i++) {
                parameterMap.put("parameter" + (i + 1), Stream.of(ps[i].split(" ")).map(StringUtils::trim).toList());
            }
            Map<String, List<List<String>>> listParams = new HashMap<>();
            for (int i = 0; i < ps.length; i++) {
                List<List<String>> temp = listParams.computeIfAbsent("parameter_list", key -> new ArrayList<>());
                temp.add(Stream.of(ps[i].split(" ")).map(StringUtils::trim).toList());

                parameterMap.put("parameter" + (i + 1), Stream.of(ps[i].split(" ")).map(StringUtils::trim).toList());
            }
            parameterMap.putAll(listParams);
        }
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        new XMLMapperBuilder(this.getInputStream(sql), configuration, "wechatbot.temp.prepare", new HashMap<>(), "wechatbot.temp").parse();
        MappedStatement mappedStatement = configuration.getMappedStatement("wechatbot.temp.prepare");
        BoundSql boundSql = mappedStatement.getBoundSql(parameterMap);

        try {
            try (Connection connection = sqlSessionFactory.getConfiguration().getEnvironment().getDataSource().getConnection(); PreparedStatement preparedStatement = this.getPrepareStatement(connection, boundSql)) {
                ResultSet rs = preparedStatement.executeQuery();

                List<String> headers = new ArrayList<>();
                ResultSetMetaData rsmd = rs.getMetaData();

                for (int i = 1; i <= rsmd.getColumnCount(); i++) {
                    headers.add(rsmd.getColumnLabel(i));
                }

                List<LinkedHashMap<String, Object>> data = new ArrayList<>();
                while (rs.next()) {
                    LinkedHashMap<String, Object> map = new LinkedHashMap<>();
                    for (String header : headers) {
                        map.put(header, rs.getObject(header));
                    }
                    data.add(map);
                }
                rs.close();
                markdown = MarkdownUtil.generateMarkdownTable(data);
            }
        } catch (Exception e) {
            markdown = e.getMessage();
        }


        if (StringUtils.isNotBlank(configObj.getString("prepend"))) {
            markdown = configObj.getString("prepend") + markdown;
        }
        if (StringUtils.isNotBlank(configObj.getString("append"))) {
            markdown = markdown + configObj.getString("append");
        }
        return response.setBody(markdown);
    }

    @NotNull
    private InputStream getInputStream(String sql) {
        StringBuilder xmlConfig = new StringBuilder("<!DOCTYPE mapper PUBLIC \"-//mybatis.org//DTD Mapper 3.0//EN\" \"http://mybatis.org/dtd/mybatis-3-mapper.dtd\">");
        xmlConfig.append("<mapper namespace=\"wechatbot.temp\">");
        xmlConfig.append("<select id=\"prepare\" parameterType=\"java.util.LinkedHashMap\" useCache=\"false\" flushCache=\"true\">");
        xmlConfig.append(sql);
        xmlConfig.append("</select>");
        xmlConfig.append("</mapper>");
        return new ByteArrayInputStream(xmlConfig.toString().getBytes(StandardCharsets.UTF_8));
    }

    @SuppressWarnings("unchecked")
    private PreparedStatement getPrepareStatement(Connection conn, BoundSql boundSql) throws SQLException {
        Map<String, Object> allParams = (Map<String, Object>) boundSql.getParameterObject();
        allParams.putAll(boundSql.getAdditionalParameters());
        List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
        PreparedStatement ps = conn.prepareStatement(boundSql.getSql());
        for (int i = 0; i < parameterMappings.size(); i++) {
            ps.setObject(i + 1, allParams.get(parameterMappings.get(i).getProperty()));
        }
        return ps;
    }
    // endregion

    // region clo target
    @Override
    public Response initCloPage() {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("reportVersion", componentsDao.queryCloReportVersion());
        return response.setBody(resultMap);
    }

    @Override
    public Response queryCloReportData(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        int total = componentsDao.queryCloReportDataCount(parameterMap);
        page.setTotal(total);
        if (total > 0) {
            page.setData(componentsDao.queryCloReportData(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadCloTemplate(Map<String, Object> parameterMap, HttpServletResponse response) {
        String fileName = "clo_target_tempalte.xlsx";
        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        resultList.add(map);

        map.put("MATERIAL", null);
        map.put("COMPETITIVE_LT_CRITERIA", null);
        map.put("COMMENTS", null);
        map.put("BEST_CLO_LT_IN_THE_MARKET_WD", null);
        map.put("NAME_OF_COMPETITOR_WITH_BEST_CLO_LT", null);
        map.put("CURRENT_CLO_LT", null);
        map.put("PLANT_CODE", null);

        excelTemplate.create(response, fileName, resultList);
    }

    @Override
    public void downloadCloData(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String version = (String) parameterMap.get("version");
        String fileName = "clo_target_" + version + ".xlsx";
        parameterMap.put("version", version);
        excelTemplate.create(response, fileName, "com.scp.components.dao.IComponentsDao.queryCloReportData", parameterMap);
    }

    @Override
    public Response saveCloData(String userid, Map<String, Object> parameterMap) {
        String version = (String) parameterMap.get("version");
        scpTableHelper.setScpTableInsertHandler((headers, creates) -> componentsDao.createCloDataByTable(headers, creates, userid, version));
        scpTableHelper.setScpTableDeleteHandler(deletes -> componentsDao.deleteCloDataByTable(deletes));
        scpTableHelper.setScpTableUpdateHandler((pk, updates) -> componentsDao.updateCloDataByTable(pk, updates, userid));
        Message message = scpTableHelper.execCRUD(parameterMap);
        return response.setBody(message);
    }

    @Override
    @TargetDataSource(DatabaseType.SCP02_READONLY)
    public Response queryMarkdownBySql(Map<String, Object> parameterMap) {
        String markdown = "";
        String sql = "select * from (" + parameterMap.get("sql_query") + ") tt fetch next 50 rows only";

        try {
            try (Connection connection = dynamicDataSource.getConnection();
                 PreparedStatement preparedStatement = connection.prepareStatement(sql)) {
                ResultSet rs = preparedStatement.executeQuery();

                List<String> headers = new ArrayList<>();
                ResultSetMetaData rsmd = rs.getMetaData();

                for (int i = 1; i <= rsmd.getColumnCount(); i++) {
                    headers.add(rsmd.getColumnLabel(i));
                }

                List<LinkedHashMap<String, Object>> data = new ArrayList<>();
                while (rs.next()) {
                    LinkedHashMap<String, Object> map = new LinkedHashMap<>();
                    for (String header : headers) {
                        map.put(header, rs.getObject(header));
                    }
                    data.add(map);
                }
                rs.close();
                markdown = MarkdownUtil.generateMarkdownTable(data);
            }
        } catch (Exception e) {
            markdown = e.getMessage();
        }

        return response.setBody(markdown);
    }

    @Override
    public Response cloUploadReport(String userid, MultipartFile file) throws Exception {
        File tempFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), ".tmp");
        file.transferTo(tempFile);

        List<Map<String, String>> dataList = new ArrayList<>();
        excelTemplate.read(tempFile, 1, new SimpleSheetContentsHandler() {
            @Override
            public void handleRow(int rowNum) {

                if (rowNum == 0) {
                    return;
                }

                int i = 0;
                Map<String, String> map = new HashMap<>();

                map.put("MATERIAL", row.get(i++));
                map.put("COMPETITIVE_LT_CRITERIA", row.get(i++));
                map.put("COMMENTS", row.get(i++));
                map.put("BEST_CLO_LT_IN_THE_MARKET_WD", row.get(i++));
                map.put("NAME_OF_COMPETITOR_WITH_BEST_CLO_LT", row.get(i++));
                map.put("CURRENT_CLO_LT", row.get(i++));
                map.put("PLANT_CODE", row.get(i));

                dataList.add(map);
                if (dataList.size() >= 256) {
                    componentsDao.mergeCloReportData(dataList, userid);
                    dataList.clear();
                }
            }
        }, new StylesTable());

        if (dataList.isEmpty() == false) {
            componentsDao.mergeCloReportData(dataList, userid);
            dataList.clear();
        }

        if (tempFile.delete() == false) {
            System.err.println(tempFile.getAbsolutePath() + " delete failed");
        }
        return response;
    }
    // endregion

    // region product group a/b/c start

    @Override
    public Response productGroupInitPage() {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cascader", Utils.parseCascader(componentsDao.queryProductGroupCascader()));
        return response.setBody(resultMap);
    }

    @Override
    public Response queryProductGroupData(Map<String, Object> parameterMap) {
        this.generateCascaderFilterSQL(parameterMap);
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        int total = componentsDao.queryProductGroupDataCount(parameterMap);
        page.setTotal(total);
        if (total > 0) {
            page.setData(componentsDao.queryProductGroupData(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadProductGroupTemplate(Map<String, Object> parameterMap, HttpServletResponse response) {
        String fileName = "product_group_tempalte.xlsx";
        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        resultList.add(map);

        map.put("MATERIAL", null);
        map.put("PLANT_CODE", null);
        map.put("PRODUCT_GROUP_A", null);
        map.put("PRODUCT_GROUP_B", null);
        map.put("PRODUCT_GROUP_C", null);
        map.put("PRODUCT_GROUP_D", null);
        map.put("PRODUCT_GROUP_E", null);

        excelTemplate.create(response, fileName, resultList);
    }

    @Override
    public void downloadProductGroupData(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String version = (String) parameterMap.get("version");
        String fileName = "product_group_" + version + ".xlsx";
        parameterMap.put("version", version);
        excelTemplate.create(response, fileName, "com.scp.components.dao.IComponentsDao.queryProductGroupData", parameterMap);
    }

    private static void validateAndUpdate(
            List<Map<String, String>> productGroupData,
            Object updatedRows,
            String dataType,
            List<Map<String, String>> resultList,
            String currentUser
    ) {
        Map<String, Map<String, String>> productGroupMap = new HashMap<>();
        for (Map<String, String> row : productGroupData) {
            String key = getKeyFromRow(row);
            productGroupMap.put(key, row);
        }

        if ("updatedRows".equals(dataType)) {
            handleUpdatedRows(productGroupMap, (JSONObject) updatedRows, resultList, currentUser);
        } else if ("createdRows".equals(dataType)) {
            handleCreatedRows(productGroupMap, (JSONArray) updatedRows, resultList, currentUser);
        }
    }

    private static void handleUpdatedRows(
            Map<String, Map<String, String>> productGroupMap,
            JSONObject updatedRows,
            List<Map<String, String>> resultList,
            String currentUser
    ) {
        updatedRows.forEach((key, value) -> {
            JSONObject updateFields = (JSONObject) value;
            Map<String, String> rowData = productGroupMap.get(key);

            if (rowData != null) {
                processFields(rowData, updateFields, resultList, currentUser);
            }
        });
    }

    private static void handleCreatedRows(
            Map<String, Map<String, String>> productGroupMap,
            JSONArray updatedRows,
            List<Map<String, String>> resultList,
            String currentUser
    ) {
        for (Object rowObj : updatedRows) {
            JSONObject updateFields = (JSONObject) rowObj;
            String key = getKeyFromRow(updateFields);

            Map<String, String> rowData = productGroupMap.get(key);

            if (rowData != null) {
                processFields(rowData, updateFields, resultList, currentUser);
            } else {
                // 处理新创建的行，没有对应原始数据的情况
                List<String> fieldsToUpdateOwner = new ArrayList<>();

                for (String field : updateFields.keySet()) {
                    if (field.startsWith("PRODUCT_GROUP") &&
                            StringUtils.isNotEmpty(updateFields.getString(field))) {
                        fieldsToUpdateOwner.add(field);
                    }
                }

                for (String field : fieldsToUpdateOwner) {
                    updateFields.put(field + "_OWNER", currentUser);
                }
            }
        }
    }

    /**
     * 公共字段处理逻辑
     */
    private static void processFields(
            Map<String, String> rowData,
            JSONObject updateFields,
            List<Map<String, String>> resultList,
            String currentUser
    ) {
        List<String> fieldsToRemove = new ArrayList<>();
        Map<String, String> fieldsToUpdateOwner = new HashMap<>();

        for (Map.Entry<String, Object> entry : updateFields.entrySet()) {
            String field = entry.getKey();

            if (!field.startsWith("PRODUCT_GROUP")) {
                continue;
            }
            String owner = rowData.get(field + "_OWNER");
            String newValue = updateFields.getString(field);
            String originalValue = rowData.get(field);
            boolean newValueEmpty = newValue == null || newValue.trim().isEmpty();
            boolean currentUserIsOwner = currentUser.equals(owner);
            boolean originalValueNotEmpty = originalValue != null && !originalValue.trim().isEmpty();

            if (newValueEmpty && (currentUserIsOwner || !originalValueNotEmpty)) {
                fieldsToUpdateOwner.put(field + "_OWNER", "No Owner");
                fieldsToRemove.add(field);
                continue;
            }

            if (originalValue != null && !originalValue.trim().isEmpty()) {
                if (owner != null && !owner.equals(currentUser) && !"No Owner".equalsIgnoreCase(owner)) {
                    Map<String, String> errorData = new HashMap<>();
                    errorData.put("MATERIAL", rowData.get("MATERIAL"));
                    errorData.put("PLANT_CODE", rowData.get("PLANT_CODE"));
                    errorData.put("ERROR_COLUMN", field);
                    errorData.put("COLUMN_OWNER", owner);
                    errorData.put("ORIGINAL_CONTENT", originalValue);
                    errorData.put("NEW_CONTENT", newValue);

                    resultList.add(errorData);
                    fieldsToRemove.add(field);
                } else {
                    fieldsToUpdateOwner.put(field + "_OWNER", currentUser);
                }
            } else {
                fieldsToUpdateOwner.put(field + "_OWNER", currentUser);
            }
        }

        // 批量移除字段
        for (String field : fieldsToRemove) {
            updateFields.remove(field);
        }

        // 批量更新 OWNER 字段
        for (Map.Entry<String, String> entry : fieldsToUpdateOwner.entrySet()) {
            updateFields.put(entry.getKey(), entry.getValue());
        }
    }

    /**
     * 获取行标识 key（MATERIAL + PLANT_CODE）
     */
    private static String getKeyFromRow(Map<String, String> row) {
        return row.get("MATERIAL") + "," + row.get("PLANT_CODE");
    }

    private static String getKeyFromRow(JSONObject row) {
        return row.getString("MATERIAL") + "," + row.getString("PLANT_CODE");
    }

    @Override
    public Response saveProductGroupData(String userid, Map<String, Object> parameterMap) {
        List<Map<String, String>> productGroupData = componentsDao.queryProductGroupDataAll(null);
        List<Map<String, String>> resultList = new ArrayList<>();

        validateAndUpdate(productGroupData, parameterMap.get("updatedRows"), "updatedRows", resultList, userid);
        validateAndUpdate(productGroupData, parameterMap.get("createdRows"), "createdRows", resultList, userid);
        JSONObject jsonObject = new JSONObject();
        if (resultList.isEmpty()) {
            scpTableHelper.setScpTableInsertHandler((headers, creates) -> componentsDao.createProductGroupDataByTable(headers, creates, userid));
            scpTableHelper.setScpTableDeleteHandler(deletes -> componentsDao.deleteProductGroupDataByTable(deletes));
            scpTableHelper.setScpTableUpdateHandler((pk, updates) -> componentsDao.updateProductGroupDataByTable(pk, updates, userid));
            Message message = scpTableHelper.execCRUD(parameterMap);
            jsonObject = (JSONObject) JSONObject.toJSON(message);
        }
        jsonObject.put("errorList", resultList);
        return response.setBody(jsonObject);
    }

    private static JSONArray convertToJSONArray(List<Map<String, String>> dataList) {
        JSONArray array = new JSONArray();
        for (Map<String, String> map : dataList) {
            JSONObject obj = new JSONObject();
            for (Map.Entry<String, String> entry : map.entrySet()) {
                obj.put(entry.getKey(), entry.getValue());
            }
            array.add(obj);
        }
        return array;
    }

    private static List<Map<String, String>> convertToList(JSONArray jsonArray) {
        List<Map<String, String>> list = new ArrayList<>();
        for (Object obj : jsonArray) {
            JSONObject jsonObject = (JSONObject) obj;
            Map<String, String> map = new HashMap<>();
            for (String key : jsonObject.keySet()) {
                map.put(key, jsonObject.getString(key));
            }
            list.add(map);
        }
        return list;
    }

    @Override
    public Response productGroupUpload(String userid, MultipartFile file) throws Exception {
        File tempFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), ".tmp");
        file.transferTo(tempFile);
        List<Map<String, String>> productGroupData = componentsDao.queryProductGroupDataAll(null);
        List<Map<String, String>> dataList = new ArrayList<>();
        List<Map<String, String>> resultList = new ArrayList<>();
        excelTemplate.read(tempFile, 1, new SimpleSheetContentsHandler() {
            @Override
            public void handleRow(int rowNum) {

                if (rowNum == 0) {
                    return;
                }

                int i = 0;
                Map<String, String> map = new HashMap<>();

                map.put("MATERIAL", row.get(i++));
                map.put("PLANT_CODE", row.get(i++));
                map.put("PRODUCT_GROUP_A", row.get(i++));
                map.put("PRODUCT_GROUP_B", row.get(i++));
                map.put("PRODUCT_GROUP_C", row.get(i++));
                map.put("PRODUCT_GROUP_D", row.get(i++));
                map.put("PRODUCT_GROUP_E", row.get(i++));

                dataList.add(map);
                if (dataList.size() >= 256) {
                    JSONArray newDataList = convertToJSONArray(dataList);
                    validateAndUpdate(productGroupData, newDataList, "createdRows", resultList, userid);
                    if (resultList.isEmpty()) {
                        componentsDao.mergeProductGroupData(convertToList(newDataList), userid);
                    }
                    dataList.clear();
                }
            }
        }, new StylesTable());

        if (dataList.isEmpty() == false) {
            JSONArray newDataList = convertToJSONArray(dataList);
            validateAndUpdate(productGroupData, newDataList, "createdRows", resultList, userid);
            if (resultList.isEmpty()) {
                componentsDao.mergeProductGroupData(convertToList(newDataList), userid);
            }
            dataList.clear();
        }

        if (tempFile.delete() == false) {
            System.err.println(tempFile.getAbsolutePath() + " delete failed");
        }
        Map<String, List<Map<String, String>>> res = new HashMap<>();
        res.put("errorList", resultList);
        response.setBody(res);
        return response;
    }
    // endregion product group a/b/c
}
