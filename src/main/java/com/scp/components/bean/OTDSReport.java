package com.scp.components.bean;

import com.starter.utils.DateCalUtil;
import com.starter.utils.Utils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

public class OTDSReport {
    private String SALES_ORDER_NUMBER;
    private String SALES_ORDER_ITEM;
    private String HIGHER_LEVEL_ITEM;
    private String STOCK_INDENT;
    private String SALES_ORGANIZATION;
    private String ONTIME;
    private String REA_SOBLOCK;
    private String REA_PUR;
    private String REA_VENDOR;
    private String REA_INV;
    private String REA_GRPAGE;
    private String REA_DEL_CREN_BLOCK;
    private String REA_WAREHOUSE;
    private String REA_DEL_CREAT;
    private String REA_CUST_BLOCK;
    private String REA_FINANCE;
    private String REA_OTHER;
    private String REA_CUSTOVERBLK;
    private String REA_DEL_GIBLOCK;
    private String MATERIAL;
    private String ITEM_CATEGORY;
    private String MAT_PRC_GRP;
    private String PRODUCT_LINE;
    private String COMPLETE_DELIV_IND;
    private String DEL_GROUP;
    private String PLANT_CODE;
    private String SALES_DISTRICT;
    private String CUSTOMER_GROUP;
    private String VENDOR_CODE;
    private String PR_NUMBER;
    private String MO_NUMBER;
    private String SO04_CRD;
    private String CREATED_DATE;
    private String DELIVERY_RELEASE_DATE;
    private String SALES_ORDER_RELEASE;
    private String FIRST_DELIVERY_DATE;
    private String MATERIAL_AVAILIBALE_DATE;
    private String CONFIRMED_DATE;
    private String PURCHASE_GR_DATE;
    private String DELIVERY_CREATE_DATE;
    private String DELIVERY_PACKING_DATE;
    private String DELIVERY_PICKING_DATE;
    private String MO_CONFRIMED_FINISH_DATE;
    private String PURCHASE_CREATE_DATE;
    private String MO_SCHEDULE_FINISH_DATE;
    private String PO_STATISTIC_DATE;
    private String DELIVERY_GOODS_ISSUE_DATE;
    private String SOLD_TO;
    private String BASE_UNIT;
    private BigDecimal NUM_DOC;
    private String ORDER_QTY_RAW;
    private BigDecimal ORDER_QTY;
    private String ORDER_QTY_BASE_UNIT;
    private String INVOICED_QUANTITY_RAW;
    private BigDecimal INVOICED_QUANTITY;
    private String INVOICED_QUANTITY_BASE_UNIT;
    private String INVOICED_AMOUNT_RAW;
    private BigDecimal INVOICED_AMOUNT;
    private String INVOICED_AMOUNT_BASE_UNIT;
    private int DELAY_DAYS;

    public String getSALES_ORDER_NUMBER() {
        return SALES_ORDER_NUMBER;
    }

    public void setSALES_ORDER_NUMBER(String SALES_ORDER_NUMBER) {
        this.SALES_ORDER_NUMBER = StringUtils.trim(SALES_ORDER_NUMBER);
    }

    public String getSALES_ORDER_ITEM() {
        return SALES_ORDER_ITEM;
    }

    public void setSALES_ORDER_ITEM(String SALES_ORDER_ITEM) {
        this.SALES_ORDER_ITEM = StringUtils.trim(SALES_ORDER_ITEM);
    }

    public String getHIGHER_LEVEL_ITEM() {
        return HIGHER_LEVEL_ITEM;
    }

    public void setHIGHER_LEVEL_ITEM(String HIGHER_LEVEL_ITEM) {
        this.HIGHER_LEVEL_ITEM = StringUtils.trim(HIGHER_LEVEL_ITEM);
    }

    public String getSTOCK_INDENT() {
        return STOCK_INDENT;
    }

    public void setSTOCK_INDENT(String STOCK_INDENT) {
        this.STOCK_INDENT = StringUtils.trim(STOCK_INDENT);
    }

    public String getSALES_ORGANIZATION() {
        return SALES_ORGANIZATION;
    }

    public void setSALES_ORGANIZATION(String SALES_ORGANIZATION) {
        this.SALES_ORGANIZATION = StringUtils.trim(SALES_ORGANIZATION);
    }

    public String getONTIME() {
        return ONTIME;
    }

    public void setONTIME(String ONTIME) {
        String s = StringUtils.trim(ONTIME);
        if ("A".equals(s)) {
            this.ONTIME = "+";
        } else {
            this.ONTIME = StringUtils.trim(ONTIME);
        }
    }

    public String getREA_SOBLOCK() {
        return REA_SOBLOCK;
    }

    public void setREA_SOBLOCK(String REA_SOBLOCK) {
        this.REA_SOBLOCK = StringUtils.trim(REA_SOBLOCK);
    }

    public String getREA_PUR() {
        return REA_PUR;
    }

    public void setREA_PUR(String REA_PUR) {
        this.REA_PUR = StringUtils.trim(REA_PUR);
    }

    public String getREA_VENDOR() {
        return REA_VENDOR;
    }

    public void setREA_VENDOR(String REA_VENDOR) {
        this.REA_VENDOR = StringUtils.trim(REA_VENDOR);
    }

    public String getREA_INV() {
        return REA_INV;
    }

    public void setREA_INV(String REA_INV) {
        this.REA_INV = StringUtils.trim(REA_INV);
    }

    public String getREA_GRPAGE() {
        return REA_GRPAGE;
    }

    public void setREA_GRPAGE(String REA_GRPAGE) {
        this.REA_GRPAGE = StringUtils.trim(REA_GRPAGE);
    }

    public String getREA_DEL_CREN_BLOCK() {
        return REA_DEL_CREN_BLOCK;
    }

    public void setREA_DEL_CREN_BLOCK(String REA_DEL_CREN_BLOCK) {
        this.REA_DEL_CREN_BLOCK = StringUtils.trim(REA_DEL_CREN_BLOCK);
    }

    public String getREA_WAREHOUSE() {
        return REA_WAREHOUSE;
    }

    public void setREA_WAREHOUSE(String REA_WAREHOUSE) {
        this.REA_WAREHOUSE = StringUtils.trim(REA_WAREHOUSE);
    }

    public String getREA_DEL_CREAT() {
        return REA_DEL_CREAT;
    }

    public void setREA_DEL_CREAT(String REA_DEL_CREAT) {
        this.REA_DEL_CREAT = StringUtils.trim(REA_DEL_CREAT);
    }

    public String getREA_CUST_BLOCK() {
        return REA_CUST_BLOCK;
    }

    public void setREA_CUST_BLOCK(String REA_CUST_BLOCK) {
        this.REA_CUST_BLOCK = StringUtils.trim(REA_CUST_BLOCK);
    }

    public String getREA_FINANCE() {
        return REA_FINANCE;
    }

    public void setREA_FINANCE(String REA_FINANCE) {
        this.REA_FINANCE = StringUtils.trim(REA_FINANCE);
    }

    public String getREA_OTHER() {
        return REA_OTHER;
    }

    public void setREA_OTHER(String REA_OTHER) {
        this.REA_OTHER = StringUtils.trim(REA_OTHER);
    }

    public String getREA_CUSTOVERBLK() {
        return REA_CUSTOVERBLK;
    }

    public void setREA_CUSTOVERBLK(String REA_CUSTOVERBLK) {
        this.REA_CUSTOVERBLK = StringUtils.trim(REA_CUSTOVERBLK);
    }

    public String getREA_DEL_GIBLOCK() {
        return REA_DEL_GIBLOCK;
    }

    public void setREA_DEL_GIBLOCK(String REA_DEL_GIBLOCK) {
        this.REA_DEL_GIBLOCK = StringUtils.trim(REA_DEL_GIBLOCK);
    }

    public String getMATERIAL() {
        return MATERIAL;
    }

    public void setMATERIAL(String MATERIAL) {
        this.MATERIAL = StringUtils.trim(MATERIAL);
    }

    public String getITEM_CATEGORY() {
        return ITEM_CATEGORY;
    }

    public void setITEM_CATEGORY(String ITEM_CATEGORY) {
        this.ITEM_CATEGORY = StringUtils.trim(ITEM_CATEGORY);
    }

    public String getMAT_PRC_GRP() {
        return MAT_PRC_GRP;
    }

    public void setMAT_PRC_GRP(String MAT_PRC_GRP) {
        this.MAT_PRC_GRP = StringUtils.trim(MAT_PRC_GRP);
    }

    public String getPRODUCT_LINE() {
        return PRODUCT_LINE;
    }

    public void setPRODUCT_LINE(String PRODUCT_LINE) {
        this.PRODUCT_LINE = StringUtils.trim(PRODUCT_LINE);
    }

    public String getCOMPLETE_DELIV_IND() {
        return COMPLETE_DELIV_IND;
    }

    public void setCOMPLETE_DELIV_IND(String COMPLETE_DELIV_IND) {
        this.COMPLETE_DELIV_IND = StringUtils.trim(COMPLETE_DELIV_IND);
    }

    public String getDEL_GROUP() {
        return DEL_GROUP;
    }

    public void setDEL_GROUP(String DEL_GROUP) {
        this.DEL_GROUP = StringUtils.trim(DEL_GROUP);
    }

    public String getPLANT_CODE() {
        return PLANT_CODE;
    }

    public void setPLANT_CODE(String PLANT_CODE) {
        this.PLANT_CODE = StringUtils.trim(PLANT_CODE);
    }

    public String getSALES_DISTRICT() {
        return SALES_DISTRICT;
    }

    public void setSALES_DISTRICT(String SALES_DISTRICT) {
        this.SALES_DISTRICT = StringUtils.trim(SALES_DISTRICT);
    }

    public String getCUSTOMER_GROUP() {
        return CUSTOMER_GROUP;
    }

    public void setCUSTOMER_GROUP(String CUSTOMER_GROUP) {
        this.CUSTOMER_GROUP = StringUtils.trim(CUSTOMER_GROUP);
    }

    public String getVENDOR_CODE() {
        return VENDOR_CODE;
    }

    public void setVENDOR_CODE(String VENDOR_CODE) {
        this.VENDOR_CODE = StringUtils.trim(VENDOR_CODE);
    }

    public String getPR_NUMBER() {
        return PR_NUMBER;
    }

    public void setPR_NUMBER(String PR_NUMBER) {
        this.PR_NUMBER = StringUtils.trim(PR_NUMBER);
    }

    public String getMO_NUMBER() {
        return MO_NUMBER;
    }

    public void setMO_NUMBER(String MO_NUMBER) {
        this.MO_NUMBER = StringUtils.trim(MO_NUMBER);
    }

    public String getSO04_CRD() {
        return SO04_CRD;
    }

    public void setSO04_CRD(String SO04_CRD) {
        String s = StringUtils.trim(SO04_CRD);
        if ("#".equalsIgnoreCase(s) == false) {
            if (Utils.isStrictNumeric(s)) {
                this.SO04_CRD = DateCalUtil.excelNumber2Date(s, "yyyy/MM/dd");
            } else {
                this.SO04_CRD = s;
            }
        }
    }

    public String getCREATED_DATE() {
        return CREATED_DATE;
    }

    public void setCREATED_DATE(String CREATED_DATE) {
        String s = StringUtils.trim(CREATED_DATE);
        if ("#".equalsIgnoreCase(s) == false) {
            if (Utils.isStrictNumeric(s)) {
                this.CREATED_DATE = DateCalUtil.excelNumber2Date(s, "yyyy/MM/dd");
            } else {
                this.CREATED_DATE = s;
            }
        }
    }

    public String getDELIVERY_RELEASE_DATE() {
        return DELIVERY_RELEASE_DATE;
    }

    public void setDELIVERY_RELEASE_DATE(String DELIVERY_RELEASE_DATE) {
        String s = StringUtils.trim(DELIVERY_RELEASE_DATE);
        if ("#".equalsIgnoreCase(s) == false) {
            if (Utils.isStrictNumeric(s)) {
                this.DELIVERY_RELEASE_DATE = DateCalUtil.excelNumber2Date(s, "yyyy/MM/dd");
            } else {
                this.DELIVERY_RELEASE_DATE = s;
            }
        }
    }

    public String getSALES_ORDER_RELEASE() {
        return SALES_ORDER_RELEASE;
    }

    public void setSALES_ORDER_RELEASE(String SALES_ORDER_RELEASE) {
        String s = StringUtils.trim(SALES_ORDER_RELEASE);
        if ("#".equalsIgnoreCase(s) == false) {
            if (Utils.isStrictNumeric(s)) {
                this.SALES_ORDER_RELEASE = DateCalUtil.excelNumber2Date(s, "yyyy/MM/dd");
            } else {
                this.SALES_ORDER_RELEASE = s;
            }
        }
    }

    public String getFIRST_DELIVERY_DATE() {
        return FIRST_DELIVERY_DATE;
    }

    public void setFIRST_DELIVERY_DATE(String FIRST_DELIVERY_DATE) {
        String s = StringUtils.trim(FIRST_DELIVERY_DATE);
        if ("#".equalsIgnoreCase(s) == false) {
            if (Utils.isStrictNumeric(s)) {
                this.FIRST_DELIVERY_DATE = DateCalUtil.excelNumber2Date(s, "yyyy/MM/dd");
            } else {
                this.FIRST_DELIVERY_DATE = s;
            }
        }
    }

    public String getMATERIAL_AVAILIBALE_DATE() {
        return MATERIAL_AVAILIBALE_DATE;
    }

    public void setMATERIAL_AVAILIBALE_DATE(String MATERIAL_AVAILIBALE_DATE) {
        String s = StringUtils.trim(MATERIAL_AVAILIBALE_DATE);
        if ("#".equalsIgnoreCase(s) == false) {
            if (Utils.isStrictNumeric(s)) {
                this.MATERIAL_AVAILIBALE_DATE = DateCalUtil.excelNumber2Date(s, "yyyy/MM/dd");
            } else {
                this.MATERIAL_AVAILIBALE_DATE = s;
            }
        }
    }

    public String getCONFIRMED_DATE() {
        return CONFIRMED_DATE;
    }

    public void setCONFIRMED_DATE(String CONFIRMED_DATE) {
        String s = StringUtils.trim(CONFIRMED_DATE);
        if ("#".equalsIgnoreCase(s) == false) {
            if (Utils.isStrictNumeric(s)) {
                this.CONFIRMED_DATE = DateCalUtil.excelNumber2Date(s, "yyyy/MM/dd");
            } else {
                this.CONFIRMED_DATE = s;
            }
        }
    }

    public String getPURCHASE_GR_DATE() {
        return PURCHASE_GR_DATE;
    }

    public void setPURCHASE_GR_DATE(String PURCHASE_GR_DATE) {
        String s = StringUtils.trim(PURCHASE_GR_DATE);
        if ("#".equalsIgnoreCase(s) == false) {
            if (Utils.isStrictNumeric(s)) {
                this.PURCHASE_GR_DATE = DateCalUtil.excelNumber2Date(s, "yyyy/MM/dd");
            } else {
                this.PURCHASE_GR_DATE = s;
            }
        }
    }

    public String getDELIVERY_CREATE_DATE() {
        return DELIVERY_CREATE_DATE;
    }

    public void setDELIVERY_CREATE_DATE(String DELIVERY_CREATE_DATE) {
        String s = StringUtils.trim(DELIVERY_CREATE_DATE);
        if ("#".equalsIgnoreCase(s) == false) {
            if (Utils.isStrictNumeric(s)) {
                this.DELIVERY_CREATE_DATE = DateCalUtil.excelNumber2Date(s, "yyyy/MM/dd");
            } else {
                this.DELIVERY_CREATE_DATE = s;
            }
        }
    }

    public String getDELIVERY_PACKING_DATE() {
        return DELIVERY_PACKING_DATE;
    }

    public void setDELIVERY_PACKING_DATE(String DELIVERY_PACKING_DATE) {
        String s = StringUtils.trim(DELIVERY_PACKING_DATE);
        if ("#".equalsIgnoreCase(s) == false) {
            if (Utils.isStrictNumeric(s)) {
                this.DELIVERY_PACKING_DATE = DateCalUtil.excelNumber2Date(s, "yyyy/MM/dd");
            } else {
                this.DELIVERY_PACKING_DATE = s;
            }
        }
    }

    public String getDELIVERY_PICKING_DATE() {
        return DELIVERY_PICKING_DATE;
    }

    public void setDELIVERY_PICKING_DATE(String DELIVERY_PICKING_DATE) {
        String s = StringUtils.trim(DELIVERY_PICKING_DATE);
        if ("#".equalsIgnoreCase(s) == false) {
            if (Utils.isStrictNumeric(s)) {
                this.DELIVERY_PICKING_DATE = DateCalUtil.excelNumber2Date(s, "yyyy/MM/dd");
            } else {
                this.DELIVERY_PICKING_DATE = s;
            }
        }
    }

    public String getMO_CONFRIMED_FINISH_DATE() {
        return MO_CONFRIMED_FINISH_DATE;
    }

    public void setMO_CONFRIMED_FINISH_DATE(String MO_CONFRIMED_FINISH_DATE) {
        String s = StringUtils.trim(MO_CONFRIMED_FINISH_DATE);
        if ("#".equalsIgnoreCase(s) == false) {
            if (Utils.isStrictNumeric(s)) {
                this.MO_CONFRIMED_FINISH_DATE = DateCalUtil.excelNumber2Date(s, "yyyy/MM/dd");
            } else {
                this.MO_CONFRIMED_FINISH_DATE = s;
            }
        }
    }

    public String getPURCHASE_CREATE_DATE() {
        return PURCHASE_CREATE_DATE;
    }

    public void setPURCHASE_CREATE_DATE(String PURCHASE_CREATE_DATE) {
        String s = StringUtils.trim(PURCHASE_CREATE_DATE);
        if ("#".equalsIgnoreCase(s) == false) {
            if (Utils.isStrictNumeric(s)) {
                this.PURCHASE_CREATE_DATE = DateCalUtil.excelNumber2Date(s, "yyyy/MM/dd");
            } else {
                this.PURCHASE_CREATE_DATE = s;
            }
        }
    }

    public String getMO_SCHEDULE_FINISH_DATE() {
        return MO_SCHEDULE_FINISH_DATE;
    }

    public void setMO_SCHEDULE_FINISH_DATE(String MO_SCHEDULE_FINISH_DATE) {
        String s = StringUtils.trim(MO_SCHEDULE_FINISH_DATE);
        if ("#".equalsIgnoreCase(s) == false) {
            if (Utils.isStrictNumeric(s)) {
                this.MO_SCHEDULE_FINISH_DATE = DateCalUtil.excelNumber2Date(s, "yyyy/MM/dd");
            } else {
                this.MO_SCHEDULE_FINISH_DATE = s;
            }
        }
    }

    public String getPO_STATISTIC_DATE() {
        return PO_STATISTIC_DATE;
    }

    public void setPO_STATISTIC_DATE(String PO_STATISTIC_DATE) {
        String s = StringUtils.trim(PO_STATISTIC_DATE);
        if ("#".equalsIgnoreCase(s) == false) {
            if (Utils.isStrictNumeric(s)) {
                this.PO_STATISTIC_DATE = DateCalUtil.excelNumber2Date(s, "yyyy/MM/dd");
            } else {
                this.PO_STATISTIC_DATE = s;
            }
        }
    }

    public String getDELIVERY_GOODS_ISSUE_DATE() {
        return DELIVERY_GOODS_ISSUE_DATE;
    }

    public void setDELIVERY_GOODS_ISSUE_DATE(String DELIVERY_GOODS_ISSUE_DATE) {
        String s = StringUtils.trim(DELIVERY_GOODS_ISSUE_DATE);
        if ("#".equalsIgnoreCase(s) == false) {
            if (Utils.isStrictNumeric(s)) {
                this.DELIVERY_GOODS_ISSUE_DATE = DateCalUtil.excelNumber2Date(s, "yyyy/MM/dd");
            } else {
                this.DELIVERY_GOODS_ISSUE_DATE = s;
            }
        }
    }

    public String getSOLD_TO() {
        return SOLD_TO;
    }

    public void setSOLD_TO(String SOLD_TO) {
        this.SOLD_TO = StringUtils.trim(SOLD_TO);
    }

    public String getBASE_UNIT() {
        return BASE_UNIT;
    }

    public void setBASE_UNIT(String BASE_UNIT) {
        this.BASE_UNIT = StringUtils.trim(BASE_UNIT);
    }

    public BigDecimal getNUM_DOC() {
        return NUM_DOC;
    }

    public void setNUM_DOC(BigDecimal NUM_DOC) {
        this.NUM_DOC = NUM_DOC;
    }

    public String getORDER_QTY_RAW() {
        return ORDER_QTY_RAW;
    }

    public void setORDER_QTY_RAW(String ORDER_QTY_RAW) {
        this.ORDER_QTY_RAW = StringUtils.trim(ORDER_QTY_RAW);

        String[] raw = StringUtils.split(this.ORDER_QTY_RAW, " ");
        if (raw.length == 2) {
            this.ORDER_QTY = Utils.parseBigDecimal(raw[0], null);
            this.ORDER_QTY_BASE_UNIT = raw[1];
        } else if (raw.length == 1) {
            this.ORDER_QTY = Utils.parseBigDecimal(StringUtils.trim(raw[0]), null);
        }
    }

    public BigDecimal getORDER_QTY() {
        return ORDER_QTY;
    }

    public void setORDER_QTY(BigDecimal ORDER_QTY) {
        this.ORDER_QTY = ORDER_QTY;
    }

    public String getORDER_QTY_BASE_UNIT() {
        return ORDER_QTY_BASE_UNIT;
    }

    public void setORDER_QTY_BASE_UNIT(String ORDER_QTY_BASE_UNIT) {
        this.ORDER_QTY_BASE_UNIT = StringUtils.trim(ORDER_QTY_BASE_UNIT);
    }

    public String getINVOICED_QUANTITY_RAW() {
        return INVOICED_QUANTITY_RAW;
    }

    public void setINVOICED_QUANTITY_RAW(String INVOICED_QUANTITY_RAW) {
        this.INVOICED_QUANTITY_RAW = StringUtils.trim(INVOICED_QUANTITY_RAW);
        String[] raw = StringUtils.split(this.INVOICED_QUANTITY_RAW, " ");
        if (raw.length == 2) {
            this.INVOICED_QUANTITY = Utils.parseBigDecimal(raw[0], null);
            this.INVOICED_QUANTITY_BASE_UNIT = raw[1];
        } else if (raw.length == 1) {
            this.INVOICED_QUANTITY = Utils.parseBigDecimal(StringUtils.trim(raw[0]), null);
        }
    }

    public BigDecimal getINVOICED_QUANTITY() {
        return INVOICED_QUANTITY;
    }

    public void setINVOICED_QUANTITY(BigDecimal INVOICED_QUANTITY) {
        this.INVOICED_QUANTITY = INVOICED_QUANTITY;
    }

    public String getINVOICED_QUANTITY_BASE_UNIT() {
        return INVOICED_QUANTITY_BASE_UNIT;
    }

    public void setINVOICED_QUANTITY_BASE_UNIT(String INVOICED_QUANTITY_BASE_UNIT) {
        this.INVOICED_QUANTITY_BASE_UNIT = StringUtils.trim(INVOICED_QUANTITY_BASE_UNIT);
    }

    public String getINVOICED_AMOUNT_RAW() {
        return INVOICED_AMOUNT_RAW;
    }

    public void setINVOICED_AMOUNT_RAW(String INVOICED_AMOUNT_RAW) {
        this.INVOICED_AMOUNT_RAW = StringUtils.trim(INVOICED_AMOUNT_RAW);

        String[] raw = StringUtils.split(this.INVOICED_AMOUNT_RAW, " ");
        if (raw.length == 2) {
            this.INVOICED_AMOUNT = Utils.parseBigDecimal(raw[0], null);
            this.INVOICED_AMOUNT_BASE_UNIT = raw[1];
        } else if (raw.length == 1) {
            this.INVOICED_AMOUNT = Utils.parseBigDecimal(StringUtils.trim(raw[0]), null);
        }
    }

    public BigDecimal getINVOICED_AMOUNT() {
        return INVOICED_AMOUNT;
    }

    public void setINVOICED_AMOUNT(BigDecimal INVOICED_AMOUNT) {
        this.INVOICED_AMOUNT = INVOICED_AMOUNT;
    }

    public String getINVOICED_AMOUNT_BASE_UNIT() {
        return INVOICED_AMOUNT_BASE_UNIT;
    }

    public void setINVOICED_AMOUNT_BASE_UNIT(String INVOICED_AMOUNT_BASE_UNIT) {
        this.INVOICED_AMOUNT_BASE_UNIT = StringUtils.trim(INVOICED_AMOUNT_BASE_UNIT);
    }

    public int getDELAY_DAYS() {
        return DELAY_DAYS;
    }

    public void setDELAY_DAYS(int DELAY_DAYS) {
        this.DELAY_DAYS = DELAY_DAYS;
    }
}
