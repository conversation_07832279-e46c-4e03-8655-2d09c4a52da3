<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.components.dao.IComponentsDao">
    <update id="refreshO2LongTail">
        BEGIN
            DBMS_MVIEW.REFRESH(list =>'O2_LONG_TAIL_SOURCE_V', Method =>'C', refresh_after_errors => false, atomic_refresh => false, out_of_place => true);

            UPDATE MR3_O2_LONG_TAIL T0
            SET (T0.MRP_CONTROLLER,
                 T0.ENTITY_NAME,
                 T0.MATERIAL_ST_PLANT,
                 T0.DIST_CHANNEL_SP_ST,
                 T0.TOTAL_STOCK,
                 T0.TOTAL_STOCK_VALUE,
                 T0.DEL_FLAG,
                 T0.OPEN_SO_QTY,
                 T0.AMU,
                 T0.AMF,
                 T0.PRODUCT_LINE,
                 T0.DELIVERING_PLANT) =
                    (
                        SELECT T.MRP_CONTROLLER,
                               T.ENTITY,
                               T.MATERIAL_ST_PLANT,
                               T.DIST_CHANNEL_SP_ST,
                               T.QTY,
                               T.VAL,
                               T.DEL_FLAG_PLANT,
                               T.OPEN_SO_QTY,
                               T.AMU,
                               T.AMF,
                               T.PRODUCT_LINE,
                               T.DELIVERING_PLANT
                        FROM O2_LONG_TAIL_SOURCE_V T
                        WHERE T.PLANT_CODE = T0.PLANT_CODE
                          AND T.MATERIAL = T0.MATERIAL
                    );

                update MR3_O2_LONG_TAIL t set t.material_status = null;
                update MR3_O2_LONG_TAIL t set t.material_status = 'EOL' where (t.del_flag = 'X' and t.total_stock_value = 0) or (t.material_st_plant = '06' and t.dist_channel_sp_st in ('01','02','P6') and t.total_stock_value = 0);
                update MR3_O2_LONG_TAIL t set t.material_status = 'Pending EOL for Stock' where (t.material_st_plant = '06' and t.dist_channel_sp_st in ('01','02','P6') and t.total_stock_value > 0);
                update MR3_O2_LONG_TAIL t set t.material_status = 'Not EOL' where t.material_status is null;
                update MR3_O2_LONG_TAIL t set t.delivering_plant = t.plant_code where t.delivering_plant is null;
        end;
    </update>

    <update id="refreshMaterial2ProductionLine">
        update MR3_MPP_MATERIAL_TO_PRODUCTION_LINE t
           set t.PLANT_CODE = (
                   select t1.PLANT_CODE
                     from MR3_PLANT_MASTER_DATA t1
                    where substr(t.SITE, instr(t.SITE, '_', 1, 2) + 1) = t1.ENTITY_NAME
               )
    </update>

    <insert id="insertFsctDataTemp">
        insert into MR3_mpp_forcast_data_temporary
        (fcst_version, material, sales_organization, customer_code,parent_site, demand_details, month01,month02,month03,month04,month05,month06,month07,month08,month09,month10,month11,month12,month13,
        month14,month15,month16,month17,month18,month19,month20,month21,month22,month23,month24,month25)
        <foreach collection="list" separator=" union all" item="item">
            select
            #{version, jdbcType=VARCHAR},
            #{item.material, jdbcType=VARCHAR},
            #{item.sales_org, jdbcType=VARCHAR},
            #{item.customer_code, jdbcType=VARCHAR},
            #{item.parent_site, jdbcType=VARCHAR},
            #{item.demand_details, jdbcType=VARCHAR},
            #{item.month01, jdbcType=DOUBLE},
            #{item.month02, jdbcType=DOUBLE},
            #{item.month03, jdbcType=DOUBLE},
            #{item.month04, jdbcType=DOUBLE},
            #{item.month05, jdbcType=DOUBLE},
            #{item.month06, jdbcType=DOUBLE},
            #{item.month07, jdbcType=DOUBLE},
            #{item.month08, jdbcType=DOUBLE},
            #{item.month09, jdbcType=DOUBLE},
            #{item.month10, jdbcType=DOUBLE},
            #{item.month11, jdbcType=DOUBLE},
            #{item.month12, jdbcType=DOUBLE},
            #{item.month13, jdbcType=DOUBLE},
            #{item.month14, jdbcType=DOUBLE},
            #{item.month15, jdbcType=DOUBLE},
            #{item.month16, jdbcType=DOUBLE},
            #{item.month17, jdbcType=DOUBLE},
            #{item.month18, jdbcType=DOUBLE},
            #{item.month19, jdbcType=DOUBLE},
            #{item.month20, jdbcType=DOUBLE},
            #{item.month21, jdbcType=DOUBLE},
            #{item.month22, jdbcType=DOUBLE},
            #{item.month23, jdbcType=DOUBLE},
            #{item.month24, jdbcType=DOUBLE},
            #{item.month25, jdbcType=DOUBLE}
            from dual
        </foreach>
    </insert>

    <select id="queryFcstVersion" resultType="com.starter.context.bean.SimpleSelect">
        select distinct fcst_version as "value" from MR3_mpp_forcast_data order by fcst_version desc
    </select>

    <delete id="deleteFcstData">
        delete from MR3_mpp_forcast_data where create_by$ = #{userid, jdbcType=VARCHAR} and fcst_version = #{version, jdbcType=VARCHAR}
    </delete>

    <update id="mergeFcstData">
        merge into MR3_mpp_forcast_data t
        using
        (
        select fcst_version,
               t0.material,
               nvl(t2.plant_code,  t0.sales_organization) sales_organization,
               nvl(t0.customer_code, t1.customer_id) customer_code,
               sum(month01) month01,
               sum(month02) month02,
               sum(month03) month03,
               sum(month04) month04,
               sum(month05) month05,
               sum(month06) month06,
               sum(month07) month07,
               sum(month08) month08,
               sum(month09) month09,
               sum(month10) month10,
               sum(month11) month11,
               sum(month12) month12,
               sum(month13) month13,
               sum(month14) month14,
               sum(month15) month15,
               sum(month16) month16,
               sum(month17) month17,
               sum(month18) month18,
               sum(month19) month19,
               sum(month20) month20,
               sum(month21) month21,
               sum(month22) month22,
               sum(month23) month23,
               sum(month24) month24,
               sum(month25) month25
        from MR3_mpp_forcast_data_temporary t0
             left join MR3_mpp_customer_site t1 on t0.sales_organization = t1.site and t0.parent_site = t1.rr_site_value
             left join MR3_PLANT_MASTER_DATA t2 on substr(t0.sales_organization, instr(t0.sales_organization, '_', 1, 2) + 1) = t2.entity_name
        where upper(t0.demand_details) != 'BOM'
        group by fcst_version, material, nvl(t2.plant_code,  t0.sales_organization), nvl(t0.customer_code, t1.customer_id)
        ) s on (t.fcst_version = s.fcst_version and t.material = s.material and t.sales_organization = s.sales_organization and t.customer_code = s.customer_code)
        when matched then
        update set
        t.month01 = s.month01,
        t.month02 = s.month02,
        t.month03 = s.month03,
        t.month04 = s.month04,
        t.month05 = s.month05,
        t.month06 = s.month06,
        t.month07 = s.month07,
        t.month08 = s.month08,
        t.month09 = s.month09,
        t.month10 = s.month10,
        t.month11 = s.month11,
        t.month12 = s.month12,
        t.month13 = s.month13,
        t.month14 = s.month14,
        t.month15 = s.month15,
        t.month16 = s.month16,
        t.month17 = s.month17,
        t.month18 = s.month18,
        t.month19 = s.month19,
        t.month20 = s.month20,
        t.month21 = s.month21,
        t.month22 = s.month22,
        t.month23 = s.month23,
        t.month24 = s.month24,
        t.month25 = s.month25,
        t.update_by$ = #{userid, jdbcType=VARCHAR},
        t.update_date$ = sysdate
        when not matched then
        insert
        (fcst_version, material, sales_organization, customer_code, month01,month02,month03,month04,month05,month06,month07,month08,month09,month10,month11,month12,month13,
        month14,month15,month16,month17,month18,month19,month20,month21,month22,month23,month24,month25,create_by$,create_date$)
        values (s.fcst_version, s.material, s.sales_organization, s.customer_code,s.month01,s.month02,s.month03,s.month04,s.month05,s.month06,s.month07,s.month08,s.month09,s.month10,
        s.month11,s.month12,s.month13,s.month14,s.month15,s.month16,s.month17,s.month18,s.month19,s.month20,s.month21,s.month22,s.month23,s.month24,s.month25,#{userid, jdbcType=VARCHAR},sysdate)
    </update>

    <select id="queryMppFcstDataCount" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        select * from MR3_MPP_FORCAST_DATA where create_by$ = #{userid,jdbcType=VARCHAR} and fcst_version = #{fcst_version, jdbcType=VARCHAR}
        <include refid="global.count_footer"/>
    </select>

    <select id="queryMppFcstData" resultType="java.util.Map">
        <include refid="global.select_header"/>
        select ROWIDTOCHAR(rowid) row_id, t.* from MR3_MPP_FORCAST_DATA t
         where create_by$ = #{userid,jdbcType=VARCHAR}
           and fcst_version = #{fcst_version, jdbcType=VARCHAR}
        <include refid="global.select_footer"/>
    </select>

    <update id="saveMppForcastSource">
        update MR3_MPP_FORCAST_DATA
        SET
        <foreach collection="cols" item="col" separator=",">
            ${col.key} = #{col.value,jdbcType=VARCHAR}
        </foreach>,
        update_by$ = #{userid,jdbcType=VARCHAR},
        update_date$ = sysdate
        where rowid = #{rowid,jdbcType=VARCHAR}
    </update>

    <select id="downloadMppFcstData" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        select MATERIAL,SALES_ORGANIZATION,CUSTOMER_CODE,
        <foreach collection="columns" item="item" separator=",">
            ${item.value} "${item.name}"
        </foreach>
        from MR3_MPP_FORCAST_DATA t
        where create_by$ = #{userid,jdbcType=VARCHAR}
          and fcst_version = #{fcst_version, jdbcType=VARCHAR}
    </select>

    <select id="queryOtdsReportVersion" resultType="com.starter.context.bean.SimpleSelect">
        select distinct t.week as "value" from MR3_OTDS_OFFICIAL_REPORT t order by t.week desc
    </select>

    <select id="queryOtdsReportDataCount" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
            select * from MR3_OTDS_OFFICIAL_REPORT where week = #{report_version, jdbcType=VARCHAR}
        <include refid="global.count_footer"/>
    </select>

    <select id="queryOtdsReportData" resultType="java.util.Map">
        <include refid="global.select_header"/>
            select ROWIDTOCHAR(rowid) row_id,
                   sales_order_number,
                   sales_order_item,
                   higher_level_item,
                   stock_indent,
                   sales_organization,
                   ontime,
                   rea_soblock,
                   rea_pur,
                   rea_vendor,
                   rea_inv,
                   rea_grpage,
                   rea_del_cren_block,
                   rea_warehouse,
                   rea_del_creat,
                   rea_cust_block,
                   rea_finance,
                   rea_other,
                   rea_custoverblk,
                   rea_del_giblock,
                   material,
                   entity,
                   item_category,
                   mat_prc_grp,
                   product_line,
                   complete_deliv_ind,
                   del_group,
                   plant_code,
                   sales_district,
                   customer_group,
                   vendor_code,
                   pr_number,
                   mo_number,
                   to_char(so04_crd,'yyyy/mm/dd') so04_crd,
                   to_char(created_date,'yyyy/mm/dd') created_date,
                   to_char(delivery_release_date,'yyyy/mm/dd') delivery_release_date,
                   to_char(sales_order_release,'yyyy/mm/dd') sales_order_release,
                   to_char(first_delivery_date,'yyyy/mm/dd') first_delivery_date,
                   to_char(material_availibale_date,'yyyy/mm/dd') material_availibale_date,
                   to_char(confirmed_date,'yyyy/mm/dd') confirmed_date,
                   to_char(purchase_gr_date,'yyyy/mm/dd') purchase_gr_date,
                   to_char(delivery_create_date,'yyyy/mm/dd') delivery_create_date,
                   to_char(delivery_packing_date,'yyyy/mm/dd') delivery_packing_date,
                   to_char(delivery_picking_date,'yyyy/mm/dd') delivery_picking_date,
                   to_char(mo_confrimed_finish_date,'yyyy/mm/dd') mo_confrimed_finish_date,
                   to_char(purchase_create_date,'yyyy/mm/dd') purchase_create_date,
                   to_char(mo_schedule_finish_date,'yyyy/mm/dd') mo_schedule_finish_date,
                   to_char(po_statistic_date,'yyyy/mm/dd') po_statistic_date,
                   to_char(delivery_goods_issue_date,'yyyy/mm/dd') delivery_goods_issue_date,
                   sold_to,
                   base_unit,
                   num_doc,
                   order_qty_raw,
                   order_qty,
                   order_qty_base_unit,
                   invoiced_quantity_raw,
                   invoiced_quantity,
                   invoiced_quantity_base_unit,
                   invoiced_amount_raw,
                   invoiced_amount,
                   invoiced_amount_base_unit,
                   delay_days,
                   week,
                   order_type,
                   business_unit,
                   local_business_unit,
                   local_product_family,
                   local_product_line,
                   local_product_subfamily,
                   mrp_controller,
                   region,
                   import_vendor,
                   vendor_name,
                   cluster_name,
                   gra_type
            from MR3_OTDS_OFFICIAL_REPORT t
            where week = #{report_version, jdbcType=VARCHAR}
        <include refid="global.select_footer"/>
    </select>

    <select id="downloadOTDSData" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        select * from MR3_OTDS_OFFICIAL_REPORT t where week = #{report_version, jdbcType=VARCHAR}
        <include refid="global.select_footer"/>
    </select>

    <insert id="saveOtdsData">
        update MR3_OTDS_OFFICIAL_REPORT
        SET

        <foreach collection="cols" item="col" separator=",">
            <choose>
                <when test="col.type == 'date'.toString()">
                    ${col.key} = to_date(#{col.value,jdbcType=VARCHAR},'yyyy/mm/dd')
                </when>
                <otherwise>
                    ${col.key} = #{col.value,jdbcType=VARCHAR}
                </otherwise>
            </choose>
        </foreach>,
        update_by$ = #{userid,jdbcType=VARCHAR},
        update_date$ = sysdate
        where rowid = #{rowid,jdbcType=VARCHAR}
    </insert>

    <insert id="insertOtdsReportDataTemp">
        insert /*+ NOLOGGING  */ into MR3_SO04_HIST_TEMPORARY t
        (WEEK,
        SALES_ORDER_NUMBER,
        SALES_ORDER_ITEM,
        HIGHER_LEVEL_ITEM,
        STOCK_INDENT,
        SALES_ORGANIZATION,
        ONTIME,
        REA_SOBLOCK,
        REA_PUR,
        REA_VENDOR,
        REA_INV,
        REA_GRPAGE,
        REA_DEL_CREN_BLOCK,
        REA_WAREHOUSE,
        REA_DEL_CREAT,
        REA_CUST_BLOCK,
        REA_FINANCE,
        REA_OTHER,
        REA_CUSTOVERBLK,
        REA_DEL_GIBLOCK,
        MATERIAL,
        ITEM_CATEGORY,
        MAT_PRC_GRP,
        PRODUCT_LINE,
        COMPLETE_DELIV_IND,
        DEL_GROUP,
        PLANT_CODE,
        SALES_DISTRICT,
        CUSTOMER_GROUP,
        VENDOR_CODE,
        PR_NUMBER,
        MO_NUMBER,
        SO04_CRD,
        CREATED_DATE,
        DELIVERY_RELEASE_DATE,
        SALES_ORDER_RELEASE,
        FIRST_DELIVERY_DATE,
        MATERIAL_AVAILIBALE_DATE,
        CONFIRMED_DATE,
        PURCHASE_GR_DATE,
        DELIVERY_CREATE_DATE,
        DELIVERY_PACKING_DATE,
        DELIVERY_PICKING_DATE,
        MO_CONFRIMED_FINISH_DATE,
        PURCHASE_CREATE_DATE,
        MO_SCHEDULE_FINISH_DATE,
        PO_STATISTIC_DATE,
        DELIVERY_GOODS_ISSUE_DATE,
        SOLD_TO,
        BASE_UNIT,
        NUM_DOC,
        ORDER_QTY_RAW,
        ORDER_QTY,
        ORDER_QTY_BASE_UNIT,
        INVOICED_QUANTITY_RAW,
        INVOICED_QUANTITY,
        INVOICED_QUANTITY_BASE_UNIT,
        INVOICED_AMOUNT_RAW,
        INVOICED_AMOUNT,
        INVOICED_AMOUNT_BASE_UNIT,
        DELAY_DAYS)
        <foreach collection="list" separator=" union all" item="item">
            select
            #{version, jdbcType=VARCHAR},
            #{item.SALES_ORDER_NUMBER, jdbcType=VARCHAR},
            #{item.SALES_ORDER_ITEM, jdbcType=VARCHAR},
            #{item.HIGHER_LEVEL_ITEM, jdbcType=VARCHAR},
            #{item.STOCK_INDENT, jdbcType=VARCHAR},
            #{item.SALES_ORGANIZATION, jdbcType=VARCHAR},
            #{item.ONTIME, jdbcType=VARCHAR},
            #{item.REA_SOBLOCK, jdbcType=VARCHAR},
            #{item.REA_PUR, jdbcType=VARCHAR},
            #{item.REA_VENDOR, jdbcType=VARCHAR},
            #{item.REA_INV, jdbcType=VARCHAR},
            #{item.REA_GRPAGE, jdbcType=VARCHAR},
            #{item.REA_DEL_CREN_BLOCK, jdbcType=VARCHAR},
            #{item.REA_WAREHOUSE, jdbcType=VARCHAR},
            #{item.REA_DEL_CREAT, jdbcType=VARCHAR},
            #{item.REA_CUST_BLOCK, jdbcType=VARCHAR},
            #{item.REA_FINANCE, jdbcType=VARCHAR},
            #{item.REA_OTHER, jdbcType=VARCHAR},
            #{item.REA_CUSTOVERBLK, jdbcType=VARCHAR},
            #{item.REA_DEL_GIBLOCK, jdbcType=VARCHAR},
            #{item.MATERIAL, jdbcType=VARCHAR},
            #{item.ITEM_CATEGORY, jdbcType=VARCHAR},
            #{item.MAT_PRC_GRP, jdbcType=VARCHAR},
            #{item.PRODUCT_LINE, jdbcType=VARCHAR},
            #{item.COMPLETE_DELIV_IND, jdbcType=VARCHAR},
            #{item.DEL_GROUP, jdbcType=VARCHAR},
            #{item.PLANT_CODE, jdbcType=VARCHAR},
            #{item.SALES_DISTRICT, jdbcType=VARCHAR},
            #{item.CUSTOMER_GROUP, jdbcType=VARCHAR},
            #{item.VENDOR_CODE, jdbcType=VARCHAR},
            #{item.PR_NUMBER, jdbcType=VARCHAR},
            #{item.MO_NUMBER, jdbcType=VARCHAR},
            to_date(#{item.SO04_CRD, jdbcType=VARCHAR},'yyyy/mm/dd'),
            to_date(#{item.CREATED_DATE, jdbcType=VARCHAR},'yyyy/mm/dd'),
            to_date(#{item.DELIVERY_RELEASE_DATE, jdbcType=VARCHAR},'yyyy/mm/dd'),
            to_date(#{item.SALES_ORDER_RELEASE, jdbcType=VARCHAR},'yyyy/mm/dd'),
            to_date(#{item.FIRST_DELIVERY_DATE, jdbcType=VARCHAR},'yyyy/mm/dd'),
            to_date(#{item.MATERIAL_AVAILIBALE_DATE, jdbcType=VARCHAR},'yyyy/mm/dd'),
            to_date(#{item.CONFIRMED_DATE, jdbcType=VARCHAR},'yyyy/mm/dd'),
            to_date(#{item.PURCHASE_GR_DATE, jdbcType=VARCHAR},'yyyy/mm/dd'),
            to_date(#{item.DELIVERY_CREATE_DATE, jdbcType=VARCHAR},'yyyy/mm/dd'),
            to_date(#{item.DELIVERY_PACKING_DATE, jdbcType=VARCHAR},'yyyy/mm/dd'),
            to_date(#{item.DELIVERY_PICKING_DATE, jdbcType=VARCHAR},'yyyy/mm/dd'),
            to_date(#{item.MO_CONFRIMED_FINISH_DATE, jdbcType=VARCHAR},'yyyy/mm/dd'),
            to_date(#{item.PURCHASE_CREATE_DATE, jdbcType=VARCHAR},'yyyy/mm/dd'),
            to_date(#{item.MO_SCHEDULE_FINISH_DATE, jdbcType=VARCHAR},'yyyy/mm/dd'),
            to_date(#{item.PO_STATISTIC_DATE, jdbcType=VARCHAR},'yyyy/mm/dd'),
            to_date(#{item.DELIVERY_GOODS_ISSUE_DATE, jdbcType=VARCHAR},'yyyy/mm/dd'),
            #{item.SOLD_TO, jdbcType=VARCHAR},
            #{item.BASE_UNIT, jdbcType=VARCHAR},
            #{item.NUM_DOC, jdbcType=VARCHAR},
            #{item.ORDER_QTY_RAW, jdbcType=VARCHAR},
            #{item.ORDER_QTY, jdbcType=VARCHAR},
            #{item.ORDER_QTY_BASE_UNIT, jdbcType=VARCHAR},
            #{item.INVOICED_QUANTITY_RAW, jdbcType=VARCHAR},
            #{item.INVOICED_QUANTITY, jdbcType=VARCHAR},
            #{item.INVOICED_QUANTITY_BASE_UNIT, jdbcType=VARCHAR},
            #{item.INVOICED_AMOUNT_RAW, jdbcType=VARCHAR},
            #{item.INVOICED_AMOUNT, jdbcType=VARCHAR},
            #{item.INVOICED_AMOUNT_BASE_UNIT, jdbcType=VARCHAR},
            #{item.DELAY_DAYS, jdbcType=VARCHAR}
            from dual
        </foreach>
    </insert>

    <delete id="deleteOtdsReportData">
        delete from MR3_OTDS_OFFICIAL_REPORT where week = #{version, jdbcType=VARCHAR}
    </delete>

    <insert id="mergeOtdsReportData">
        insert
        /*+ PARALLEL IGNORE_ROW_ON_DUPKEY_INDEX (MR3_OTDS_OFFICIAL_REPORT,PK_MR3_OTDS_OFFICIAL_REPORT) */
        into MR3_OTDS_OFFICIAL_REPORT
        (week, sales_order_number, sales_order_item, higher_level_item, stock_indent, sales_organization, ontime, rea_soblock, rea_pur,
         rea_vendor, rea_inv, rea_grpage, rea_del_cren_block, rea_warehouse, rea_del_creat, rea_cust_block, rea_finance, rea_other,
         rea_custoverblk, rea_del_giblock, material, entity, item_category, mat_prc_grp, product_line, complete_deliv_ind, del_group,
         plant_code, sales_district, customer_group, vendor_code, pr_number, mo_number, so04_crd, created_date, delivery_release_date,
         sales_order_release, first_delivery_date, material_availibale_date, confirmed_date, purchase_gr_date, delivery_create_date,
         delivery_packing_date, delivery_picking_date, mo_confrimed_finish_date, purchase_create_date, mo_schedule_finish_date, po_statistic_date,
         delivery_goods_issue_date, sold_to, base_unit, num_doc, order_qty_raw, order_qty, order_qty_base_unit, invoiced_quantity_raw, invoiced_quantity,
         invoiced_quantity_base_unit, invoiced_amount_raw, invoiced_amount, invoiced_amount_base_unit, delay_days, create_by$, create_date$, update_by$,
         update_date$, business_unit, local_business_unit, local_product_family, local_product_line, local_product_subfamily, mrp_controller,
         region, import_vendor, vendor_name, cluster_name, GRA_TYPE, order_type)
         select
             week, t.sales_order_number, t.sales_order_item, t.higher_level_item, stock_indent, t.sales_organization, ontime, rea_soblock, rea_pur,
             rea_vendor, rea_inv, rea_grpage, rea_del_cren_block, rea_warehouse, rea_del_creat, rea_cust_block, rea_finance, rea_other,
             rea_custoverblk, rea_del_giblock, t.material, NVL(mat.entity, 'Others'), t.item_category, mat_prc_grp, NVL(mat.product_line, 'Others'),
             t.complete_deliv_ind, del_group, t.plant_code, t.sales_district, t.customer_group, t.vendor_code, pr_number, mo_number, so04_crd, t.created_date,
             delivery_release_date, sales_order_release, first_delivery_date, material_availibale_date, confirmed_date, purchase_gr_date, delivery_create_date,
             delivery_packing_date, delivery_picking_date, mo_confrimed_finish_date, purchase_create_date, mo_schedule_finish_date, po_statistic_date,
             delivery_goods_issue_date, t.sold_to, t.base_unit, num_doc, order_qty_raw, order_qty, order_qty_base_unit, invoiced_quantity_raw, invoiced_quantity,
             invoiced_quantity_base_unit, invoiced_amount_raw, invoiced_amount, invoiced_amount_base_unit, delay_days, 'root', sysdate, 'root',sysdate,
             NVL(mat.bu, 'Others'), NVL(mat.local_bu, 'Others'), NVL(mat.local_product_family, 'Others'),
             NVL(mat.local_product_line, 'Others'), NVL(mat.LOCAL_PRODUCT_SUBFAMILY, 'Others'),
             NVL(MAT.mrp_controller, 'Others'), NVL(KNA1.REGION, 'Others'), DECODE(KNA1.COUNTRY_CODE, 'CN', 'LOCAL', 'IMPORT'),
             NVL(MAT.SEARCH_TERM, 'Others'), NVL(MAT.CLUSTER_NAME, 'Others'), GRA.TYPE, VBAK.ORDER_TYPE
        from MR3_SO04_HIST_TEMPORARY t LEFT JOIN MATERIAL_MASTER_V MAT ON t.MATERIAL = MAT.MATERIAL AND t.PLANT_CODE = MAT.PLANT_CODE
                                   LEFT JOIN CM3_CUSTOMER_MASTER_V KNA1 ON t.SOLD_TO = KNA1.CUSTOMER_CODE
                                   LEFT JOIN MM4_GRA_V GRA ON T.MATERIAL = GRA.MATERIAL
                                   LEFT JOIN SD3_SALES_ORDER_V VBAK ON T.SALES_ORDER_NUMBER = VBAK.SALES_ORDER_NUMBER AND T.SALES_ORDER_ITEM = VBAK.SALES_ORDER_ITEM

    </insert>

    <update id="refreshOtdsOfficialReportMV">
        begin
            DBMS_MVIEW.REFRESH(list =>'OTDS_SOURCE_WEEKLY_V', Method =>'C', refresh_after_errors => false, atomic_refresh => false, out_of_place => true);
            DBMS_MVIEW.REFRESH(list =>'OTDS_RCA_V', Method =>'C', refresh_after_errors => false, atomic_refresh => false, out_of_place => true);
        end;
    </update>

    <select id="queryMppCommitVersion" resultType="com.starter.context.bean.SimpleSelect">
        select distinct commit_version as "value" from MR3_MPP_COMMIT_DATA order by commit_version desc
    </select>

    <insert id="insertMppCommitDataTemp">
        insert into MR3_MPP_COMMIT_DATA_TEMPORARY
        (commit_version, material, sales_organization, customer_code, month01,month02,month03,month04,month05,month06,month07,month08,month09,month10,month11,month12,month13,
        month14,month15,month16,month17,month18,month19,month20,month21,month22,month23,month24,month25)
        <foreach collection="list" separator=" union all" item="item">
            select
            #{version, jdbcType=VARCHAR},
            #{item.material, jdbcType=VARCHAR},
            #{item.sales_org, jdbcType=VARCHAR},
            #{item.customer_code, jdbcType=VARCHAR},
            #{item.month01, jdbcType=DOUBLE},
            #{item.month02, jdbcType=DOUBLE},
            #{item.month03, jdbcType=DOUBLE},
            #{item.month04, jdbcType=DOUBLE},
            #{item.month05, jdbcType=DOUBLE},
            #{item.month06, jdbcType=DOUBLE},
            #{item.month07, jdbcType=DOUBLE},
            #{item.month08, jdbcType=DOUBLE},
            #{item.month09, jdbcType=DOUBLE},
            #{item.month10, jdbcType=DOUBLE},
            #{item.month11, jdbcType=DOUBLE},
            #{item.month12, jdbcType=DOUBLE},
            #{item.month13, jdbcType=DOUBLE},
            #{item.month14, jdbcType=DOUBLE},
            #{item.month15, jdbcType=DOUBLE},
            #{item.month16, jdbcType=DOUBLE},
            #{item.month17, jdbcType=DOUBLE},
            #{item.month18, jdbcType=DOUBLE},
            #{item.month19, jdbcType=DOUBLE},
            #{item.month20, jdbcType=DOUBLE},
            #{item.month21, jdbcType=DOUBLE},
            #{item.month22, jdbcType=DOUBLE},
            #{item.month23, jdbcType=DOUBLE},
            #{item.month24, jdbcType=DOUBLE},
            #{item.month25, jdbcType=DOUBLE}
            from dual
        </foreach>
    </insert>

    <delete id="deleteMppCommitData">
        delete from MR3_MPP_COMMIT_DATA where create_by$ = #{userid, jdbcType=VARCHAR} and commit_version = #{version, jdbcType=VARCHAR}
    </delete>

    <update id="mergeMppCommitData">
        merge into MR3_MPP_COMMIT_DATA t
        using
        (
        select commit_version,
               t0.material,
               nvl(t2.plant_code, t0.sales_organization) sales_organization,
               nvl(t1.customer_id,t0.customer_code) customer_code,
               sum(month01) month01,
               sum(month02) month02,
               sum(month03) month03,
               sum(month04) month04,
               sum(month05) month05,
               sum(month06) month06,
               sum(month07) month07,
               sum(month08) month08,
               sum(month09) month09,
               sum(month10) month10,
               sum(month11) month11,
               sum(month12) month12,
               sum(month13) month13,
               sum(month14) month14,
               sum(month15) month15,
               sum(month16) month16,
               sum(month17) month17,
               sum(month18) month18,
               sum(month19) month19,
               sum(month20) month20,
               sum(month21) month21,
               sum(month22) month22,
               sum(month23) month23,
               sum(month24) month24,
               sum(month25) month25
        from MR3_MPP_COMMIT_DATA_TEMPORARY t0
             left join MR3_MPP_CUSTOMER_SITE t1 on t0.customer_code = t1.rr_site_value and t0.sales_organization = t1.site
             left join MR3_PLANT_MASTER_DATA t2 on substr(t0.sales_organization, instr(t0.sales_organization, '_', 1, 2) + 1) = t2.entity_name
        group by commit_version, material, t1.customer_id, t2.plant_code, t0.customer_code, t0.sales_organization
        ) s on (t.commit_version = s.commit_version
                and t.material = s.material
                and t.sales_organization = s.sales_organization
                and nvl(t.customer_code,CAST(UNISTR('\ffff\ffff') AS VARCHAR2(8))) = nvl(s.customer_code,CAST(UNISTR('\ffff\ffff') AS VARCHAR2(8))))
        when matched then
        update set
        t.month01 = s.month01,
        t.month02 = s.month02,
        t.month03 = s.month03,
        t.month04 = s.month04,
        t.month05 = s.month05,
        t.month06 = s.month06,
        t.month07 = s.month07,
        t.month08 = s.month08,
        t.month09 = s.month09,
        t.month10 = s.month10,
        t.month11 = s.month11,
        t.month12 = s.month12,
        t.month13 = s.month13,
        t.month14 = s.month14,
        t.month15 = s.month15,
        t.month16 = s.month16,
        t.month17 = s.month17,
        t.month18 = s.month18,
        t.month19 = s.month19,
        t.month20 = s.month20,
        t.month21 = s.month21,
        t.month22 = s.month22,
        t.month23 = s.month23,
        t.month24 = s.month24,
        t.month25 = s.month25,
        t.update_by$ = #{userid, jdbcType=VARCHAR},
        t.update_date$ = sysdate
        when not matched then
        insert
        (commit_version, material, sales_organization, customer_code, month01,month02,month03,month04,month05,month06,month07,month08,month09,month10,month11,month12,month13,
        month14,month15,month16,month17,month18,month19,month20,month21,month22,month23,month24,month25,create_by$,create_date$)
        values (s.commit_version, s.material, s.sales_organization, s.customer_code,s.month01,s.month02,s.month03,s.month04,s.month05,s.month06,s.month07,s.month08,s.month09,s.month10,
        s.month11,s.month12,s.month13,s.month14,s.month15,s.month16,s.month17,s.month18,s.month19,s.month20,s.month21,s.month22,s.month23,s.month24,s.month25,#{userid, jdbcType=VARCHAR},sysdate)
    </update>

    <select id="downloadMppCommitData" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        select MATERIAL,SALES_ORGANIZATION,CUSTOMER_CODE,
        <foreach collection="columns" item="item" separator=",">
            ${item.value} "${item.name}"
        </foreach>
        from MR3_MPP_COMMIT_DATA t
        where create_by$ = #{userid,jdbcType=VARCHAR}
        and commit_version = #{commit_version, jdbcType=VARCHAR}
    </select>

    <select id="queryMppCommitDataCount" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        select * from MR3_MPP_COMMIT_DATA where create_by$ = #{userid,jdbcType=VARCHAR} and commit_version = #{commit_version, jdbcType=VARCHAR}
        <include refid="global.count_footer"/>
    </select>

    <select id="queryMppCommitData" resultType="java.util.Map">
        <include refid="global.select_header"/>
        select ROWIDTOCHAR(rowid) row_id, t.*
        from MR3_MPP_COMMIT_DATA t
        where create_by$ = #{userid,jdbcType=VARCHAR}
        and commit_version = #{commit_version, jdbcType=VARCHAR}
        <include refid="global.select_footer"/>
    </select>

    <update id="saveMppCommitSource">
        update MR3_MPP_COMMIT_DATA
        SET
        <foreach collection="cols" item="col" separator=",">
            ${col.key} = #{col.value,jdbcType=VARCHAR}
        </foreach>,
        update_by$ = #{userid,jdbcType=VARCHAR},
        update_date$ = sysdate
        where rowid = #{rowid,jdbcType=VARCHAR}
    </update>

    <select id="querySliceVersion" resultType="java.lang.String">
        select distinct version from SLICE_RAW_DATA_MANUAL order by version desc
    </select>

    <insert id="insertSliceDataTemp">
        insert into SLICE_RAW_DATA_MANUAL_TEMPORARY
        (VERSION, HFM_CODE, CLASS, PLANT_TYPE, CURRENCY_TYPE, CURRENCY, "M01_Y-1", "M02_Y-1", "M03_Y-1", "M04_Y-1",
        "M05_Y-1", "M06_Y-1", "M07_Y-1", "M08_Y-1", "M09_Y-1", "M10_Y-1", "M11_Y-1", "M12_Y-1", "M01_Y-0", "M02_Y-0",
        "M03_Y-0", "M04_Y-0", "M05_Y-0", "M06_Y-0", "M07_Y-0", "M08_Y-0", "M09_Y-0", "M10_Y-0", "M11_Y-0", "M12_Y-0")
        <foreach collection="list" separator=" union all" item="item">
            select
            #{item.version, jdbcType=VARCHAR},
            #{item.hfm_code, jdbcType=VARCHAR},
            #{item.clazz, jdbcType=VARCHAR},
            #{item.plant_type, jdbcType=VARCHAR},
            #{item.currency_type, jdbcType=VARCHAR},
            #{item.currency, jdbcType=VARCHAR},
            #{item.month01, jdbcType=DOUBLE},
            #{item.month02, jdbcType=DOUBLE},
            #{item.month03, jdbcType=DOUBLE},
            #{item.month04, jdbcType=DOUBLE},
            #{item.month05, jdbcType=DOUBLE},
            #{item.month06, jdbcType=DOUBLE},
            #{item.month07, jdbcType=DOUBLE},
            #{item.month08, jdbcType=DOUBLE},
            #{item.month09, jdbcType=DOUBLE},
            #{item.month10, jdbcType=DOUBLE},
            #{item.month11, jdbcType=DOUBLE},
            #{item.month12, jdbcType=DOUBLE},
            #{item.month13, jdbcType=DOUBLE},
            #{item.month14, jdbcType=DOUBLE},
            #{item.month15, jdbcType=DOUBLE},
            #{item.month16, jdbcType=DOUBLE},
            #{item.month17, jdbcType=DOUBLE},
            #{item.month18, jdbcType=DOUBLE},
            #{item.month19, jdbcType=DOUBLE},
            #{item.month20, jdbcType=DOUBLE},
            #{item.month21, jdbcType=DOUBLE},
            #{item.month22, jdbcType=DOUBLE},
            #{item.month23, jdbcType=DOUBLE},
            #{item.month24, jdbcType=DOUBLE}
            from dual
        </foreach>
    </insert>

    <delete id="deleteSliceRawData">
        delete from SLICE_RAW_DATA_MANUAL where version = #{version, jdbcType=VARCHAR}
    </delete>

    <update id="mergeSliceRawData">
        insert into SLICE_RAW_DATA_MANUAL
        (VERSION, HFM_CODE, CLASS, PLANT_TYPE, CURRENCY_TYPE, CURRENCY, "M01_Y-1", "M02_Y-1", "M03_Y-1", "M04_Y-1",
        "M05_Y-1", "M06_Y-1", "M07_Y-1", "M08_Y-1", "M09_Y-1", "M10_Y-1", "M11_Y-1", "M12_Y-1", "M01_Y-0", "M02_Y-0",
        "M03_Y-0", "M04_Y-0", "M05_Y-0", "M06_Y-0", "M07_Y-0", "M08_Y-0", "M09_Y-0", "M10_Y-0", "M11_Y-0", "M12_Y-0",
        CREATE_BY$, CREATE_DATE$)
        select VERSION, HFM_CODE, CLASS, PLANT_TYPE, CURRENCY_TYPE, CURRENCY, "M01_Y-1", "M02_Y-1", "M03_Y-1", "M04_Y-1",
        "M05_Y-1", "M06_Y-1", "M07_Y-1", "M08_Y-1", "M09_Y-1", "M10_Y-1", "M11_Y-1", "M12_Y-1", "M01_Y-0", "M02_Y-0",
        "M03_Y-0", "M04_Y-0", "M05_Y-0", "M06_Y-0", "M07_Y-0", "M08_Y-0", "M09_Y-0", "M10_Y-0", "M11_Y-0", "M12_Y-0",
        #{userid, jdbcType=VARCHAR}, sysdate
        from SLICE_RAW_DATA_MANUAL_TEMPORARY
    </update>

    <sql id="querySliceDataSQL">
        select ROWIDTOCHAR(rowid) ROW_ID,
               VERSION,
               HFM_CODE,
               CLASS,
               PLANT_TYPE,
               CURRENCY_TYPE,
               CURRENCY,
               "M01_Y-1",
               "M02_Y-1",
               "M03_Y-1",
               "M04_Y-1",
               "M05_Y-1",
               "M06_Y-1",
               "M07_Y-1",
               "M08_Y-1",
               "M09_Y-1",
               "M10_Y-1",
               "M11_Y-1",
               "M12_Y-1",
               "M01_Y-0",
               "M02_Y-0",
               "M03_Y-0",
               "M04_Y-0",
               "M05_Y-0",
               "M06_Y-0",
               "M07_Y-0",
               "M08_Y-0",
               "M09_Y-0",
               "M10_Y-0",
               "M11_Y-0",
               "M12_Y-0"
        from SLICE_RAW_DATA
         where version = #{selectedVersion, jdbcType=VARCHAR}
    </sql>

    <select id="querySliceDataCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="querySliceDataSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="querySliceData" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="querySliceDataSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="downloadSliceData" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        select VERSION,
               HFM_CODE,
               CLASS,
               PLANT_TYPE,
               CURRENCY_TYPE,
               CURRENCY,
               "M01_Y-1",
               "M02_Y-1",
               "M03_Y-1",
               "M04_Y-1",
               "M05_Y-1",
               "M06_Y-1",
               "M07_Y-1",
               "M08_Y-1",
               "M09_Y-1",
               "M10_Y-1",
               "M11_Y-1",
               "M12_Y-1",
               "M01_Y-0",
               "M02_Y-0",
               "M03_Y-0",
               "M04_Y-0",
               "M05_Y-0",
               "M06_Y-0",
               "M07_Y-0",
               "M08_Y-0",
               "M09_Y-0",
               "M10_Y-0",
               "M11_Y-0",
               "M12_Y-0"
        from SLICE_RAW_DATA
         where version = #{selectedVersion, jdbcType=VARCHAR}
         <include refid="global.select_footer"/>
    </select>

    <sql id="queryManualSliceDataSQL">
        select ROWIDTOCHAR(rowid) ROW_ID,
               VERSION,
               HFM_CODE,
               CLASS,
               PLANT_TYPE,
               CURRENCY_TYPE,
               CURRENCY,
               "M01_Y-1",
               "M02_Y-1",
               "M03_Y-1",
               "M04_Y-1",
               "M05_Y-1",
               "M06_Y-1",
               "M07_Y-1",
               "M08_Y-1",
               "M09_Y-1",
               "M10_Y-1",
               "M11_Y-1",
               "M12_Y-1",
               "M01_Y-0",
               "M02_Y-0",
               "M03_Y-0",
               "M04_Y-0",
               "M05_Y-0",
               "M06_Y-0",
               "M07_Y-0",
               "M08_Y-0",
               "M09_Y-0",
               "M10_Y-0",
               "M11_Y-0",
               "M12_Y-0"
        from SLICE_RAW_DATA_MANUAL
         where version = #{selectedVersion, jdbcType=VARCHAR}
    </sql>

    <select id="queryManualSliceDataCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryManualSliceDataSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryManualSliceData" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryManualSliceDataSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="downloadManualSliceData" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        select VERSION,
               HFM_CODE,
               CLASS,
               PLANT_TYPE,
               CURRENCY_TYPE,
               CURRENCY,
               "M01_Y-1",
               "M02_Y-1",
               "M03_Y-1",
               "M04_Y-1",
               "M05_Y-1",
               "M06_Y-1",
               "M07_Y-1",
               "M08_Y-1",
               "M09_Y-1",
               "M10_Y-1",
               "M11_Y-1",
               "M12_Y-1",
               "M01_Y-0",
               "M02_Y-0",
               "M03_Y-0",
               "M04_Y-0",
               "M05_Y-0",
               "M06_Y-0",
               "M07_Y-0",
               "M08_Y-0",
               "M09_Y-0",
               "M10_Y-0",
               "M11_Y-0",
               "M12_Y-0"
        from SLICE_RAW_DATA_MANUAL
         where version = #{selectedVersion, jdbcType=VARCHAR}
         <include refid="global.select_footer"/>
    </select>

    <update id="updateSliceData">
        update SLICE_RAW_DATA_MANUAL
        SET
        <foreach collection="updates" item="col" separator=",">
            "${col.key}" = #{col.value,jdbcType=VARCHAR}
        </foreach>,
        update_by$ = #{userid,jdbcType=VARCHAR},
        update_date$ = sysdate
        where rowid = #{rowid,jdbcType=VARCHAR}
    </update>

     <delete id="deleteSliceData">
        delete from SLICE_RAW_DATA_MANUAL where rowid in
        <foreach collection="deletes" open="(" close=")" separator="," item="item">#{item, jdbcType=VARCHAR}</foreach>
        and version = #{version,jdbcType=VARCHAR}
     </delete>

    <insert id="createSliceData">
        insert into SLICE_RAW_DATA_MANUAL
        (
        VERSION,
        <foreach collection="headers" item="header" separator=",">
            "${header}"
        </foreach>, create_by$, create_date$
        )
        <foreach collection="creates" item="list" separator=" union all ">
            select #{version, jdbcType=VARCHAR},
            <foreach collection="headers" item="header" separator=",">
                #{list.${header}, jdbcType=VARCHAR}
            </foreach>, #{userid,jdbcType=VARCHAR}, sysdate
            from dual
        </foreach>
    </insert>

    <select id="querySiopVersion" resultType="java.lang.String">
        select distinct FCST_VERSION from MR3_SIOP_ALL_BU_DATA_ADD_ACC_LABEL order by FCST_VERSION desc
    </select>

    <insert id="insertSiopDataTemp">
        insert into MR3_SIOP_ALL_BU_DATA_TEMPORARY
                    (FCST_VERSION,
                    PRODUCT_LINE,
                    SIOP_SCOPE,
                    LOCAL_PRODUCT_LINE,
                    LOCAL_PRODUCT_FAMILY,
                    LOCAL_PRODUCT_SUB_FAMILY,
                    ACC_LABEL,
                    "M01_Y-2",
                    "M02_Y-2",
                    "M03_Y-2",
                    "M04_Y-2",
                    "M05_Y-2",
                    "M06_Y-2",
                    "M07_Y-2",
                    "M08_Y-2",
                    "M09_Y-2",
                    "M10_Y-2",
                    "M11_Y-2",
                    "M12_Y-2",
                    "M01_Y-1",
                    "M02_Y-1",
                    "M03_Y-1",
                    "M04_Y-1",
                    "M05_Y-1",
                    "M06_Y-1",
                    "M07_Y-1",
                    "M08_Y-1",
                    "M09_Y-1",
                    "M10_Y-1",
                    "M11_Y-1",
                    "M12_Y-1",
                    "M01_Y-0",
                    "M02_Y-0",
                    "M03_Y-0",
                    "M04_Y-0",
                    "M05_Y-0",
                    "M06_Y-0",
                    "M07_Y-0",
                    "M08_Y-0",
                    "M09_Y-0",
                    "M10_Y-0",
                    "M11_Y-0",
                    "M12_Y-0",
                    "M01_Y+1",
                    "M02_Y+1",
                    "M03_Y+1",
                    "M04_Y+1",
                    "M05_Y+1",
                    "M06_Y+1",
                    "M07_Y+1",
                    "M08_Y+1",
                    "M09_Y+1",
                    "M10_Y+1",
                    "M11_Y+1",
                    "M12_Y+1",
                    "M01_Y+2",
                    "M02_Y+2",
                    "M03_Y+2",
                    "M04_Y+2",
                    "M05_Y+2",
                    "M06_Y+2",
                    "M07_Y+2",
                    "M08_Y+2",
                    "M09_Y+2",
                    "M10_Y+2",
                    "M11_Y+2",
                    "M12_Y+2",
                    "Q_M01_Y-2",
                    "Q_M02_Y-2",
                    "Q_M03_Y-2",
                    "Q_M04_Y-2",
                    "Q_M05_Y-2",
                    "Q_M06_Y-2",
                    "Q_M07_Y-2",
                    "Q_M08_Y-2",
                    "Q_M09_Y-2",
                    "Q_M10_Y-2",
                    "Q_M11_Y-2",
                    "Q_M12_Y-2",
                    "Q_M01_Y-1",
                    "Q_M02_Y-1",
                    "Q_M03_Y-1",
                    "Q_M04_Y-1",
                    "Q_M05_Y-1",
                    "Q_M06_Y-1",
                    "Q_M07_Y-1",
                    "Q_M08_Y-1",
                    "Q_M09_Y-1",
                    "Q_M10_Y-1",
                    "Q_M11_Y-1",
                    "Q_M12_Y-1",
                    "Q_M01_Y-0",
                    "Q_M02_Y-0",
                    "Q_M03_Y-0",
                    "Q_M04_Y-0",
                    "Q_M05_Y-0",
                    "Q_M06_Y-0",
                    "Q_M07_Y-0",
                    "Q_M08_Y-0",
                    "Q_M09_Y-0",
                    "Q_M10_Y-0",
                    "Q_M11_Y-0",
                    "Q_M12_Y-0",
                    "Q_M01_Y+1",
                    "Q_M02_Y+1",
                    "Q_M03_Y+1",
                    "Q_M04_Y+1",
                    "Q_M05_Y+1",
                    "Q_M06_Y+1",
                    "Q_M07_Y+1",
                    "Q_M08_Y+1",
                    "Q_M09_Y+1",
                    "Q_M10_Y+1",
                    "Q_M11_Y+1",
                    "Q_M12_Y+1",
                    "Q_M01_Y+2",
                    "Q_M02_Y+2",
                    "Q_M03_Y+2",
                    "Q_M04_Y+2",
                    "Q_M05_Y+2",
                    "Q_M06_Y+2",
                    "Q_M07_Y+2",
                    "Q_M08_Y+2",
                    "Q_M09_Y+2",
                    "Q_M10_Y+2",
                    "Q_M11_Y+2",
                    "Q_M12_Y+2")
        <foreach collection="list" separator=" union all" item="item">
            select
            #{item.FCST_VERSION, jdbcType=VARCHAR},
            #{item.PRODUCT_LINE, jdbcType=VARCHAR},
            #{item.SIOP_SCOPE, jdbcType=VARCHAR},
            #{item.LOCAL_PRODUCT_LINE, jdbcType=VARCHAR},
            #{item.LOCAL_PRODUCT_FAMILY, jdbcType=VARCHAR},
            #{item.LOCAL_PRODUCT_SUB_FAMILY, jdbcType=VARCHAR},
            #{item.ACC_LABEL, jdbcType=VARCHAR},
            #{item.month01Value, jdbcType=DOUBLE},
            #{item.month02Value, jdbcType=DOUBLE},
            #{item.month03Value, jdbcType=DOUBLE},
            #{item.month04Value, jdbcType=DOUBLE},
            #{item.month05Value, jdbcType=DOUBLE},
            #{item.month06Value, jdbcType=DOUBLE},
            #{item.month07Value, jdbcType=DOUBLE},
            #{item.month08Value, jdbcType=DOUBLE},
            #{item.month09Value, jdbcType=DOUBLE},
            #{item.month10Value, jdbcType=DOUBLE},
            #{item.month11Value, jdbcType=DOUBLE},
            #{item.month12Value, jdbcType=DOUBLE},
            #{item.month13Value, jdbcType=DOUBLE},
            #{item.month14Value, jdbcType=DOUBLE},
            #{item.month15Value, jdbcType=DOUBLE},
            #{item.month16Value, jdbcType=DOUBLE},
            #{item.month17Value, jdbcType=DOUBLE},
            #{item.month18Value, jdbcType=DOUBLE},
            #{item.month19Value, jdbcType=DOUBLE},
            #{item.month20Value, jdbcType=DOUBLE},
            #{item.month21Value, jdbcType=DOUBLE},
            #{item.month22Value, jdbcType=DOUBLE},
            #{item.month23Value, jdbcType=DOUBLE},
            #{item.month24Value, jdbcType=DOUBLE},
            #{item.month25Value, jdbcType=DOUBLE},
            #{item.month26Value, jdbcType=DOUBLE},
            #{item.month27Value, jdbcType=DOUBLE},
            #{item.month28Value, jdbcType=DOUBLE},
            #{item.month29Value, jdbcType=DOUBLE},
            #{item.month30Value, jdbcType=DOUBLE},
            #{item.month31Value, jdbcType=DOUBLE},
            #{item.month32Value, jdbcType=DOUBLE},
            #{item.month33Value, jdbcType=DOUBLE},
            #{item.month34Value, jdbcType=DOUBLE},
            #{item.month35Value, jdbcType=DOUBLE},
            #{item.month36Value, jdbcType=DOUBLE},
            #{item.month37Value, jdbcType=DOUBLE},
            #{item.month38Value, jdbcType=DOUBLE},
            #{item.month39Value, jdbcType=DOUBLE},
            #{item.month40Value, jdbcType=DOUBLE},
            #{item.month41Value, jdbcType=DOUBLE},
            #{item.month42Value, jdbcType=DOUBLE},
            #{item.month43Value, jdbcType=DOUBLE},
            #{item.month44Value, jdbcType=DOUBLE},
            #{item.month45Value, jdbcType=DOUBLE},
            #{item.month46Value, jdbcType=DOUBLE},
            #{item.month47Value, jdbcType=DOUBLE},
            #{item.month48Value, jdbcType=DOUBLE},
            #{item.month49Value, jdbcType=DOUBLE},
            #{item.month50Value, jdbcType=DOUBLE},
            #{item.month51Value, jdbcType=DOUBLE},
            #{item.month52Value, jdbcType=DOUBLE},
            #{item.month53Value, jdbcType=DOUBLE},
            #{item.month54Value, jdbcType=DOUBLE},
            #{item.month55Value, jdbcType=DOUBLE},
            #{item.month56Value, jdbcType=DOUBLE},
            #{item.month57Value, jdbcType=DOUBLE},
            #{item.month58Value, jdbcType=DOUBLE},
            #{item.month59Value, jdbcType=DOUBLE},
            #{item.month60Value, jdbcType=DOUBLE},
            #{item.month01Qty, jdbcType=DOUBLE},
            #{item.month02Qty, jdbcType=DOUBLE},
            #{item.month03Qty, jdbcType=DOUBLE},
            #{item.month04Qty, jdbcType=DOUBLE},
            #{item.month05Qty, jdbcType=DOUBLE},
            #{item.month06Qty, jdbcType=DOUBLE},
            #{item.month07Qty, jdbcType=DOUBLE},
            #{item.month08Qty, jdbcType=DOUBLE},
            #{item.month09Qty, jdbcType=DOUBLE},
            #{item.month10Qty, jdbcType=DOUBLE},
            #{item.month11Qty, jdbcType=DOUBLE},
            #{item.month12Qty, jdbcType=DOUBLE},
            #{item.month13Qty, jdbcType=DOUBLE},
            #{item.month14Qty, jdbcType=DOUBLE},
            #{item.month15Qty, jdbcType=DOUBLE},
            #{item.month16Qty, jdbcType=DOUBLE},
            #{item.month17Qty, jdbcType=DOUBLE},
            #{item.month18Qty, jdbcType=DOUBLE},
            #{item.month19Qty, jdbcType=DOUBLE},
            #{item.month20Qty, jdbcType=DOUBLE},
            #{item.month21Qty, jdbcType=DOUBLE},
            #{item.month22Qty, jdbcType=DOUBLE},
            #{item.month23Qty, jdbcType=DOUBLE},
            #{item.month24Qty, jdbcType=DOUBLE},
            #{item.month25Qty, jdbcType=DOUBLE},
            #{item.month26Qty, jdbcType=DOUBLE},
            #{item.month27Qty, jdbcType=DOUBLE},
            #{item.month28Qty, jdbcType=DOUBLE},
            #{item.month29Qty, jdbcType=DOUBLE},
            #{item.month30Qty, jdbcType=DOUBLE},
            #{item.month31Qty, jdbcType=DOUBLE},
            #{item.month32Qty, jdbcType=DOUBLE},
            #{item.month33Qty, jdbcType=DOUBLE},
            #{item.month34Qty, jdbcType=DOUBLE},
            #{item.month35Qty, jdbcType=DOUBLE},
            #{item.month36Qty, jdbcType=DOUBLE},
            #{item.month37Qty, jdbcType=DOUBLE},
            #{item.month38Qty, jdbcType=DOUBLE},
            #{item.month39Qty, jdbcType=DOUBLE},
            #{item.month40Qty, jdbcType=DOUBLE},
            #{item.month41Qty, jdbcType=DOUBLE},
            #{item.month42Qty, jdbcType=DOUBLE},
            #{item.month43Qty, jdbcType=DOUBLE},
            #{item.month44Qty, jdbcType=DOUBLE},
            #{item.month45Qty, jdbcType=DOUBLE},
            #{item.month46Qty, jdbcType=DOUBLE},
            #{item.month47Qty, jdbcType=DOUBLE},
            #{item.month48Qty, jdbcType=DOUBLE},
            #{item.month49Qty, jdbcType=DOUBLE},
            #{item.month50Qty, jdbcType=DOUBLE},
            #{item.month51Qty, jdbcType=DOUBLE},
            #{item.month52Qty, jdbcType=DOUBLE},
            #{item.month53Qty, jdbcType=DOUBLE},
            #{item.month54Qty, jdbcType=DOUBLE},
            #{item.month55Qty, jdbcType=DOUBLE},
            #{item.month56Qty, jdbcType=DOUBLE},
            #{item.month57Qty, jdbcType=DOUBLE},
            #{item.month58Qty, jdbcType=DOUBLE},
            #{item.month59Qty, jdbcType=DOUBLE},
            #{item.month60Qty, jdbcType=DOUBLE}
            from dual
        </foreach>
    </insert>

    <delete id="deleteSiopRawDataTemp">
        delete MR3_SIOP_ALL_BU_DATA_TEMPORARY where FCST_VERSION = #{version, jdbcType=VARCHAR}
    </delete>

    <update id="mergeSiopRawData">
        MERGE into MR3_SIOP_ALL_BU_DATA_ADD_ACC_LABEL T_OLD
            USING (select FCST_VERSION,
                          PRODUCT_LINE,
                          SIOP_SCOPE,
                          LOCAL_PRODUCT_LINE,
                          LOCAL_PRODUCT_FAMILY,
                          LOCAL_PRODUCT_SUB_FAMILY,
                          ACC_LABEL,
                          "M01_Y-2",
                          "M02_Y-2",
                          "M03_Y-2",
                          "M04_Y-2",
                          "M05_Y-2",
                          "M06_Y-2",
                          "M07_Y-2",
                          "M08_Y-2",
                          "M09_Y-2",
                          "M10_Y-2",
                          "M11_Y-2",
                          "M12_Y-2",
                          "M01_Y-1",
                          "M02_Y-1",
                          "M03_Y-1",
                          "M04_Y-1",
                          "M05_Y-1",
                          "M06_Y-1",
                          "M07_Y-1",
                          "M08_Y-1",
                          "M09_Y-1",
                          "M10_Y-1",
                          "M11_Y-1",
                          "M12_Y-1",
                          "M01_Y-0",
                          "M02_Y-0",
                          "M03_Y-0",
                          "M04_Y-0",
                          "M05_Y-0",
                          "M06_Y-0",
                          "M07_Y-0",
                          "M08_Y-0",
                          "M09_Y-0",
                          "M10_Y-0",
                          "M11_Y-0",
                          "M12_Y-0",
                          "M01_Y+1",
                          "M02_Y+1",
                          "M03_Y+1",
                          "M04_Y+1",
                          "M05_Y+1",
                          "M06_Y+1",
                          "M07_Y+1",
                          "M08_Y+1",
                          "M09_Y+1",
                          "M10_Y+1",
                          "M11_Y+1",
                          "M12_Y+1",
                          "M01_Y+2",
                          "M02_Y+2",
                          "M03_Y+2",
                          "M04_Y+2",
                          "M05_Y+2",
                          "M06_Y+2",
                          "M07_Y+2",
                          "M08_Y+2",
                          "M09_Y+2",
                          "M10_Y+2",
                          "M11_Y+2",
                          "M12_Y+2",
                          "Q_M01_Y-2",
                          "Q_M02_Y-2",
                          "Q_M03_Y-2",
                          "Q_M04_Y-2",
                          "Q_M05_Y-2",
                          "Q_M06_Y-2",
                          "Q_M07_Y-2",
                          "Q_M08_Y-2",
                          "Q_M09_Y-2",
                          "Q_M10_Y-2",
                          "Q_M11_Y-2",
                          "Q_M12_Y-2",
                          "Q_M01_Y-1",
                          "Q_M02_Y-1",
                          "Q_M03_Y-1",
                          "Q_M04_Y-1",
                          "Q_M05_Y-1",
                          "Q_M06_Y-1",
                          "Q_M07_Y-1",
                          "Q_M08_Y-1",
                          "Q_M09_Y-1",
                          "Q_M10_Y-1",
                          "Q_M11_Y-1",
                          "Q_M12_Y-1",
                          "Q_M01_Y-0",
                          "Q_M02_Y-0",
                          "Q_M03_Y-0",
                          "Q_M04_Y-0",
                          "Q_M05_Y-0",
                          "Q_M06_Y-0",
                          "Q_M07_Y-0",
                          "Q_M08_Y-0",
                          "Q_M09_Y-0",
                          "Q_M10_Y-0",
                          "Q_M11_Y-0",
                          "Q_M12_Y-0",
                          "Q_M01_Y+1",
                          "Q_M02_Y+1",
                          "Q_M03_Y+1",
                          "Q_M04_Y+1",
                          "Q_M05_Y+1",
                          "Q_M06_Y+1",
                          "Q_M07_Y+1",
                          "Q_M08_Y+1",
                          "Q_M09_Y+1",
                          "Q_M10_Y+1",
                          "Q_M11_Y+1",
                          "Q_M12_Y+1",
                          "Q_M01_Y+2",
                          "Q_M02_Y+2",
                          "Q_M03_Y+2",
                          "Q_M04_Y+2",
                          "Q_M05_Y+2",
                          "Q_M06_Y+2",
                          "Q_M07_Y+2",
                          "Q_M08_Y+2",
                          "Q_M09_Y+2",
                          "Q_M10_Y+2",
                          "Q_M11_Y+2",
                          "Q_M12_Y+2",
                          #{userid, jdbcType=VARCHAR},
                          sysdate
                   FROM MR3_SIOP_ALL_BU_DATA_TEMPORARY) T_NEW
            ON (NVL(T_OLD.FCST_VERSION, 'Others') = NVL(T_NEW.FCST_VERSION, 'Others')
                AND NVL(T_OLD.PRODUCT_LINE, 'Others') = NVL(T_NEW.PRODUCT_LINE, 'Others')
                AND NVL(T_OLD.SIOP_SCOPE, 'Others') = NVL(T_NEW.SIOP_SCOPE, 'Others')
                AND NVL(T_OLD.LOCAL_PRODUCT_LINE, 'Others') = NVL(T_NEW.LOCAL_PRODUCT_LINE, 'Others')
                AND NVL(T_OLD.LOCAL_PRODUCT_FAMILY, 'Others') = NVL(T_NEW.LOCAL_PRODUCT_FAMILY, 'Others')
                AND NVL(T_OLD.LOCAL_PRODUCT_SUB_FAMILY, 'Others') = NVL(T_NEW.LOCAL_PRODUCT_SUB_FAMILY, 'Others')
                AND NVL(T_OLD.ACC_LABEL, 'Others') = NVL(T_NEW.ACC_LABEL, 'Others')
                )
            WHEN MATCHED THEN
                UPDATE
                    SET T_OLD."M01_Y-2"                = T_NEW."M01_Y-2",
                        T_OLD."M02_Y-2"                = T_NEW."M02_Y-2",
                        T_OLD."M03_Y-2"                = T_NEW."M03_Y-2",
                        T_OLD."M04_Y-2"                = T_NEW."M04_Y-2",
                        T_OLD."M05_Y-2"                = T_NEW."M05_Y-2",
                        T_OLD."M06_Y-2"                = T_NEW."M06_Y-2",
                        T_OLD."M07_Y-2"                = T_NEW."M07_Y-2",
                        T_OLD."M08_Y-2"                = T_NEW."M08_Y-2",
                        T_OLD."M09_Y-2"                = T_NEW."M09_Y-2",
                        T_OLD."M10_Y-2"                = T_NEW."M10_Y-2",
                        T_OLD."M11_Y-2"                = T_NEW."M11_Y-2",
                        T_OLD."M12_Y-2"                = T_NEW."M12_Y-2",
                        T_OLD."M01_Y-1"                = T_NEW."M01_Y-1",
                        T_OLD."M02_Y-1"                = T_NEW."M02_Y-1",
                        T_OLD."M03_Y-1"                = T_NEW."M03_Y-1",
                        T_OLD."M04_Y-1"                = T_NEW."M04_Y-1",
                        T_OLD."M05_Y-1"                = T_NEW."M05_Y-1",
                        T_OLD."M06_Y-1"                = T_NEW."M06_Y-1",
                        T_OLD."M07_Y-1"                = T_NEW."M07_Y-1",
                        T_OLD."M08_Y-1"                = T_NEW."M08_Y-1",
                        T_OLD."M09_Y-1"                = T_NEW."M09_Y-1",
                        T_OLD."M10_Y-1"                = T_NEW."M10_Y-1",
                        T_OLD."M11_Y-1"                = T_NEW."M11_Y-1",
                        T_OLD."M12_Y-1"                = T_NEW."M12_Y-1",
                        T_OLD."M01_Y-0"                = T_NEW."M01_Y-0",
                        T_OLD."M02_Y-0"                = T_NEW."M02_Y-0",
                        T_OLD."M03_Y-0"                = T_NEW."M03_Y-0",
                        T_OLD."M04_Y-0"                = T_NEW."M04_Y-0",
                        T_OLD."M05_Y-0"                = T_NEW."M05_Y-0",
                        T_OLD."M06_Y-0"                = T_NEW."M06_Y-0",
                        T_OLD."M07_Y-0"                = T_NEW."M07_Y-0",
                        T_OLD."M08_Y-0"                = T_NEW."M08_Y-0",
                        T_OLD."M09_Y-0"                = T_NEW."M09_Y-0",
                        T_OLD."M10_Y-0"                = T_NEW."M10_Y-0",
                        T_OLD."M11_Y-0"                = T_NEW."M11_Y-0",
                        T_OLD."M12_Y-0"                = T_NEW."M12_Y-0",
                        T_OLD."M01_Y+1"                = T_NEW."M01_Y+1",
                        T_OLD."M02_Y+1"                = T_NEW."M02_Y+1",
                        T_OLD."M03_Y+1"                = T_NEW."M03_Y+1",
                        T_OLD."M04_Y+1"                = T_NEW."M04_Y+1",
                        T_OLD."M05_Y+1"                = T_NEW."M05_Y+1",
                        T_OLD."M06_Y+1"                = T_NEW."M06_Y+1",
                        T_OLD."M07_Y+1"                = T_NEW."M07_Y+1",
                        T_OLD."M08_Y+1"                = T_NEW."M08_Y+1",
                        T_OLD."M09_Y+1"                = T_NEW."M09_Y+1",
                        T_OLD."M10_Y+1"                = T_NEW."M10_Y+1",
                        T_OLD."M11_Y+1"                = T_NEW."M11_Y+1",
                        T_OLD."M12_Y+1"                = T_NEW."M12_Y+1",
                        T_OLD."M01_Y+2"                = T_NEW."M01_Y+2",
                        T_OLD."M02_Y+2"                = T_NEW."M02_Y+2",
                        T_OLD."M03_Y+2"                = T_NEW."M03_Y+2",
                        T_OLD."M04_Y+2"                = T_NEW."M04_Y+2",
                        T_OLD."M05_Y+2"                = T_NEW."M05_Y+2",
                        T_OLD."M06_Y+2"                = T_NEW."M06_Y+2",
                        T_OLD."M07_Y+2"                = T_NEW."M07_Y+2",
                        T_OLD."M08_Y+2"                = T_NEW."M08_Y+2",
                        T_OLD."M09_Y+2"                = T_NEW."M09_Y+2",
                        T_OLD."M10_Y+2"                = T_NEW."M10_Y+2",
                        T_OLD."M11_Y+2"                = T_NEW."M11_Y+2",
                        T_OLD."M12_Y+2"                = T_NEW."M12_Y+2",
                        T_OLD."Q_M01_Y-2"                = T_NEW."Q_M01_Y-2",
                        T_OLD."Q_M02_Y-2"                = T_NEW."Q_M02_Y-2",
                        T_OLD."Q_M03_Y-2"                = T_NEW."Q_M03_Y-2",
                        T_OLD."Q_M04_Y-2"                = T_NEW."Q_M04_Y-2",
                        T_OLD."Q_M05_Y-2"                = T_NEW."Q_M05_Y-2",
                        T_OLD."Q_M06_Y-2"                = T_NEW."Q_M06_Y-2",
                        T_OLD."Q_M07_Y-2"                = T_NEW."Q_M07_Y-2",
                        T_OLD."Q_M08_Y-2"                = T_NEW."Q_M08_Y-2",
                        T_OLD."Q_M09_Y-2"                = T_NEW."Q_M09_Y-2",
                        T_OLD."Q_M10_Y-2"                = T_NEW."Q_M10_Y-2",
                        T_OLD."Q_M11_Y-2"                = T_NEW."Q_M11_Y-2",
                        T_OLD."Q_M12_Y-2"                = T_NEW."Q_M12_Y-2",
                        T_OLD."Q_M01_Y-1"                = T_NEW."Q_M01_Y-1",
                        T_OLD."Q_M02_Y-1"                = T_NEW."Q_M02_Y-1",
                        T_OLD."Q_M03_Y-1"                = T_NEW."Q_M03_Y-1",
                        T_OLD."Q_M04_Y-1"                = T_NEW."Q_M04_Y-1",
                        T_OLD."Q_M05_Y-1"                = T_NEW."Q_M05_Y-1",
                        T_OLD."Q_M06_Y-1"                = T_NEW."Q_M06_Y-1",
                        T_OLD."Q_M07_Y-1"                = T_NEW."Q_M07_Y-1",
                        T_OLD."Q_M08_Y-1"                = T_NEW."Q_M08_Y-1",
                        T_OLD."Q_M09_Y-1"                = T_NEW."Q_M09_Y-1",
                        T_OLD."Q_M10_Y-1"                = T_NEW."Q_M10_Y-1",
                        T_OLD."Q_M11_Y-1"                = T_NEW."Q_M11_Y-1",
                        T_OLD."Q_M12_Y-1"                = T_NEW."Q_M12_Y-1",
                        T_OLD."Q_M01_Y-0"                = T_NEW."Q_M01_Y-0",
                        T_OLD."Q_M02_Y-0"                = T_NEW."Q_M02_Y-0",
                        T_OLD."Q_M03_Y-0"                = T_NEW."Q_M03_Y-0",
                        T_OLD."Q_M04_Y-0"                = T_NEW."Q_M04_Y-0",
                        T_OLD."Q_M05_Y-0"                = T_NEW."Q_M05_Y-0",
                        T_OLD."Q_M06_Y-0"                = T_NEW."Q_M06_Y-0",
                        T_OLD."Q_M07_Y-0"                = T_NEW."Q_M07_Y-0",
                        T_OLD."Q_M08_Y-0"                = T_NEW."Q_M08_Y-0",
                        T_OLD."Q_M09_Y-0"                = T_NEW."Q_M09_Y-0",
                        T_OLD."Q_M10_Y-0"                = T_NEW."Q_M10_Y-0",
                        T_OLD."Q_M11_Y-0"                = T_NEW."Q_M11_Y-0",
                        T_OLD."Q_M12_Y-0"                = T_NEW."Q_M12_Y-0",
                        T_OLD."Q_M01_Y+1"                = T_NEW."Q_M01_Y+1",
                        T_OLD."Q_M02_Y+1"                = T_NEW."Q_M02_Y+1",
                        T_OLD."Q_M03_Y+1"                = T_NEW."Q_M03_Y+1",
                        T_OLD."Q_M04_Y+1"                = T_NEW."Q_M04_Y+1",
                        T_OLD."Q_M05_Y+1"                = T_NEW."Q_M05_Y+1",
                        T_OLD."Q_M06_Y+1"                = T_NEW."Q_M06_Y+1",
                        T_OLD."Q_M07_Y+1"                = T_NEW."Q_M07_Y+1",
                        T_OLD."Q_M08_Y+1"                = T_NEW."Q_M08_Y+1",
                        T_OLD."Q_M09_Y+1"                = T_NEW."Q_M09_Y+1",
                        T_OLD."Q_M10_Y+1"                = T_NEW."Q_M10_Y+1",
                        T_OLD."Q_M11_Y+1"                = T_NEW."Q_M11_Y+1",
                        T_OLD."Q_M12_Y+1"                = T_NEW."Q_M12_Y+1",
                        T_OLD."Q_M01_Y+2"                = T_NEW."Q_M01_Y+2",
                        T_OLD."Q_M02_Y+2"                = T_NEW."Q_M02_Y+2",
                        T_OLD."Q_M03_Y+2"                = T_NEW."Q_M03_Y+2",
                        T_OLD."Q_M04_Y+2"                = T_NEW."Q_M04_Y+2",
                        T_OLD."Q_M05_Y+2"                = T_NEW."Q_M05_Y+2",
                        T_OLD."Q_M06_Y+2"                = T_NEW."Q_M06_Y+2",
                        T_OLD."Q_M07_Y+2"                = T_NEW."Q_M07_Y+2",
                        T_OLD."Q_M08_Y+2"                = T_NEW."Q_M08_Y+2",
                        T_OLD."Q_M09_Y+2"                = T_NEW."Q_M09_Y+2",
                        T_OLD."Q_M10_Y+2"                = T_NEW."Q_M10_Y+2",
                        T_OLD."Q_M11_Y+2"                = T_NEW."Q_M11_Y+2",
                        T_OLD."Q_M12_Y+2"                = T_NEW."Q_M12_Y+2",
                        T_OLD.UPDATE_BY$               = #{userid, jdbcType=VARCHAR},
                        T_OLD.UPDATE_DATE$ = sysdate
            WHEN NOT MATCHED THEN
                INSERT (
                        T_OLD.FCST_VERSION,
                        T_OLD.PRODUCT_LINE,
                        T_OLD.SIOP_SCOPE,
                        T_OLD.LOCAL_PRODUCT_LINE,
                        T_OLD.LOCAL_PRODUCT_FAMILY,
                        T_OLD.LOCAL_PRODUCT_SUB_FAMILY,
                        T_OLD.ACC_LABEL,
                        T_OLD."M01_Y-2",
                        T_OLD."M02_Y-2",
                        T_OLD."M03_Y-2",
                        T_OLD."M04_Y-2",
                        T_OLD."M05_Y-2",
                        T_OLD."M06_Y-2",
                        T_OLD."M07_Y-2",
                        T_OLD."M08_Y-2",
                        T_OLD."M09_Y-2",
                        T_OLD."M10_Y-2",
                        T_OLD."M11_Y-2",
                        T_OLD."M12_Y-2",
                        T_OLD."M01_Y-1",
                        T_OLD."M02_Y-1",
                        T_OLD."M03_Y-1",
                        T_OLD."M04_Y-1",
                        T_OLD."M05_Y-1",
                        T_OLD."M06_Y-1",
                        T_OLD."M07_Y-1",
                        T_OLD."M08_Y-1",
                        T_OLD."M09_Y-1",
                        T_OLD."M10_Y-1",
                        T_OLD."M11_Y-1",
                        T_OLD."M12_Y-1",
                        T_OLD."M01_Y-0",
                        T_OLD."M02_Y-0",
                        T_OLD."M03_Y-0",
                        T_OLD."M04_Y-0",
                        T_OLD."M05_Y-0",
                        T_OLD."M06_Y-0",
                        T_OLD."M07_Y-0",
                        T_OLD."M08_Y-0",
                        T_OLD."M09_Y-0",
                        T_OLD."M10_Y-0",
                        T_OLD."M11_Y-0",
                        T_OLD."M12_Y-0",
                        T_OLD."M01_Y+1",
                        T_OLD."M02_Y+1",
                        T_OLD."M03_Y+1",
                        T_OLD."M04_Y+1",
                        T_OLD."M05_Y+1",
                        T_OLD."M06_Y+1",
                        T_OLD."M07_Y+1",
                        T_OLD."M08_Y+1",
                        T_OLD."M09_Y+1",
                        T_OLD."M10_Y+1",
                        T_OLD."M11_Y+1",
                        T_OLD."M12_Y+1",
                        T_OLD."M01_Y+2",
                        T_OLD."M02_Y+2",
                        T_OLD."M03_Y+2",
                        T_OLD."M04_Y+2",
                        T_OLD."M05_Y+2",
                        T_OLD."M06_Y+2",
                        T_OLD."M07_Y+2",
                        T_OLD."M08_Y+2",
                        T_OLD."M09_Y+2",
                        T_OLD."M10_Y+2",
                        T_OLD."M11_Y+2",
                        T_OLD."M12_Y+2",
                        T_OLD."Q_M01_Y-2",
                        T_OLD."Q_M02_Y-2",
                        T_OLD."Q_M03_Y-2",
                        T_OLD."Q_M04_Y-2",
                        T_OLD."Q_M05_Y-2",
                        T_OLD."Q_M06_Y-2",
                        T_OLD."Q_M07_Y-2",
                        T_OLD."Q_M08_Y-2",
                        T_OLD."Q_M09_Y-2",
                        T_OLD."Q_M10_Y-2",
                        T_OLD."Q_M11_Y-2",
                        T_OLD."Q_M12_Y-2",
                        T_OLD."Q_M01_Y-1",
                        T_OLD."Q_M02_Y-1",
                        T_OLD."Q_M03_Y-1",
                        T_OLD."Q_M04_Y-1",
                        T_OLD."Q_M05_Y-1",
                        T_OLD."Q_M06_Y-1",
                        T_OLD."Q_M07_Y-1",
                        T_OLD."Q_M08_Y-1",
                        T_OLD."Q_M09_Y-1",
                        T_OLD."Q_M10_Y-1",
                        T_OLD."Q_M11_Y-1",
                        T_OLD."Q_M12_Y-1",
                        T_OLD."Q_M01_Y-0",
                        T_OLD."Q_M02_Y-0",
                        T_OLD."Q_M03_Y-0",
                        T_OLD."Q_M04_Y-0",
                        T_OLD."Q_M05_Y-0",
                        T_OLD."Q_M06_Y-0",
                        T_OLD."Q_M07_Y-0",
                        T_OLD."Q_M08_Y-0",
                        T_OLD."Q_M09_Y-0",
                        T_OLD."Q_M10_Y-0",
                        T_OLD."Q_M11_Y-0",
                        T_OLD."Q_M12_Y-0",
                        T_OLD."Q_M01_Y+1",
                        T_OLD."Q_M02_Y+1",
                        T_OLD."Q_M03_Y+1",
                        T_OLD."Q_M04_Y+1",
                        T_OLD."Q_M05_Y+1",
                        T_OLD."Q_M06_Y+1",
                        T_OLD."Q_M07_Y+1",
                        T_OLD."Q_M08_Y+1",
                        T_OLD."Q_M09_Y+1",
                        T_OLD."Q_M10_Y+1",
                        T_OLD."Q_M11_Y+1",
                        T_OLD."Q_M12_Y+1",
                        T_OLD."Q_M01_Y+2",
                        T_OLD."Q_M02_Y+2",
                        T_OLD."Q_M03_Y+2",
                        T_OLD."Q_M04_Y+2",
                        T_OLD."Q_M05_Y+2",
                        T_OLD."Q_M06_Y+2",
                        T_OLD."Q_M07_Y+2",
                        T_OLD."Q_M08_Y+2",
                        T_OLD."Q_M09_Y+2",
                        T_OLD."Q_M10_Y+2",
                        T_OLD."Q_M11_Y+2",
                        T_OLD."Q_M12_Y+2",
                        T_OLD.CREATE_BY$,
                        T_OLD.CREATE_DATE$)
                    VALUES(
                              T_NEW.FCST_VERSION,
                              T_NEW.PRODUCT_LINE,
                              T_NEW.SIOP_SCOPE,
                              T_NEW.LOCAL_PRODUCT_LINE,
                              T_NEW.LOCAL_PRODUCT_FAMILY,
                              T_NEW.LOCAL_PRODUCT_SUB_FAMILY,
                              T_NEW.ACC_LABEL,
                              T_NEW."M01_Y-2",
                              T_NEW."M02_Y-2",
                              T_NEW."M03_Y-2",
                              T_NEW."M04_Y-2",
                              T_NEW."M05_Y-2",
                              T_NEW."M06_Y-2",
                              T_NEW."M07_Y-2",
                              T_NEW."M08_Y-2",
                              T_NEW."M09_Y-2",
                              T_NEW."M10_Y-2",
                              T_NEW."M11_Y-2",
                              T_NEW."M12_Y-2",
                              T_NEW."M01_Y-1",
                              T_NEW."M02_Y-1",
                              T_NEW."M03_Y-1",
                              T_NEW."M04_Y-1",
                              T_NEW."M05_Y-1",
                              T_NEW."M06_Y-1",
                              T_NEW."M07_Y-1",
                              T_NEW."M08_Y-1",
                              T_NEW."M09_Y-1",
                              T_NEW."M10_Y-1",
                              T_NEW."M11_Y-1",
                              T_NEW."M12_Y-1",
                              T_NEW."M01_Y-0",
                              T_NEW."M02_Y-0",
                              T_NEW."M03_Y-0",
                              T_NEW."M04_Y-0",
                              T_NEW."M05_Y-0",
                              T_NEW."M06_Y-0",
                              T_NEW."M07_Y-0",
                              T_NEW."M08_Y-0",
                              T_NEW."M09_Y-0",
                              T_NEW."M10_Y-0",
                              T_NEW."M11_Y-0",
                              T_NEW."M12_Y-0",
                              T_NEW."M01_Y+1",
                              T_NEW."M02_Y+1",
                              T_NEW."M03_Y+1",
                              T_NEW."M04_Y+1",
                              T_NEW."M05_Y+1",
                              T_NEW."M06_Y+1",
                              T_NEW."M07_Y+1",
                              T_NEW."M08_Y+1",
                              T_NEW."M09_Y+1",
                              T_NEW."M10_Y+1",
                              T_NEW."M11_Y+1",
                              T_NEW."M12_Y+1",
                              T_NEW."M01_Y+2",
                              T_NEW."M02_Y+2",
                              T_NEW."M03_Y+2",
                              T_NEW."M04_Y+2",
                              T_NEW."M05_Y+2",
                              T_NEW."M06_Y+2",
                              T_NEW."M07_Y+2",
                              T_NEW."M08_Y+2",
                              T_NEW."M09_Y+2",
                              T_NEW."M10_Y+2",
                              T_NEW."M11_Y+2",
                              T_NEW."M12_Y+2",
                              T_NEW."Q_M01_Y-2",
                              T_NEW."Q_M02_Y-2",
                              T_NEW."Q_M03_Y-2",
                              T_NEW."Q_M04_Y-2",
                              T_NEW."Q_M05_Y-2",
                              T_NEW."Q_M06_Y-2",
                              T_NEW."Q_M07_Y-2",
                              T_NEW."Q_M08_Y-2",
                              T_NEW."Q_M09_Y-2",
                              T_NEW."Q_M10_Y-2",
                              T_NEW."Q_M11_Y-2",
                              T_NEW."Q_M12_Y-2",
                              T_NEW."Q_M01_Y-1",
                              T_NEW."Q_M02_Y-1",
                              T_NEW."Q_M03_Y-1",
                              T_NEW."Q_M04_Y-1",
                              T_NEW."Q_M05_Y-1",
                              T_NEW."Q_M06_Y-1",
                              T_NEW."Q_M07_Y-1",
                              T_NEW."Q_M08_Y-1",
                              T_NEW."Q_M09_Y-1",
                              T_NEW."Q_M10_Y-1",
                              T_NEW."Q_M11_Y-1",
                              T_NEW."Q_M12_Y-1",
                              T_NEW."Q_M01_Y-0",
                              T_NEW."Q_M02_Y-0",
                              T_NEW."Q_M03_Y-0",
                              T_NEW."Q_M04_Y-0",
                              T_NEW."Q_M05_Y-0",
                              T_NEW."Q_M06_Y-0",
                              T_NEW."Q_M07_Y-0",
                              T_NEW."Q_M08_Y-0",
                              T_NEW."Q_M09_Y-0",
                              T_NEW."Q_M10_Y-0",
                              T_NEW."Q_M11_Y-0",
                              T_NEW."Q_M12_Y-0",
                              T_NEW."Q_M01_Y+1",
                              T_NEW."Q_M02_Y+1",
                              T_NEW."Q_M03_Y+1",
                              T_NEW."Q_M04_Y+1",
                              T_NEW."Q_M05_Y+1",
                              T_NEW."Q_M06_Y+1",
                              T_NEW."Q_M07_Y+1",
                              T_NEW."Q_M08_Y+1",
                              T_NEW."Q_M09_Y+1",
                              T_NEW."Q_M10_Y+1",
                              T_NEW."Q_M11_Y+1",
                              T_NEW."Q_M12_Y+1",
                              T_NEW."Q_M01_Y+2",
                              T_NEW."Q_M02_Y+2",
                              T_NEW."Q_M03_Y+2",
                              T_NEW."Q_M04_Y+2",
                              T_NEW."Q_M05_Y+2",
                              T_NEW."Q_M06_Y+2",
                              T_NEW."Q_M07_Y+2",
                              T_NEW."Q_M08_Y+2",
                              T_NEW."Q_M09_Y+2",
                              T_NEW."Q_M10_Y+2",
                              T_NEW."Q_M11_Y+2",
                              T_NEW."Q_M12_Y+2",
                              #{userid, jdbcType= VARCHAR},
                              sysdate)
    </update>

    <sql id="querySiopDataSQL">
        select ROWIDTOCHAR(rowid) ROW_ID,
               FCST_VERSION,
               PRODUCT_LINE,
               SIOP_SCOPE,
               LOCAL_PRODUCT_LINE,
               LOCAL_PRODUCT_FAMILY,
               LOCAL_PRODUCT_SUB_FAMILY,
               ACC_LABEL,
               "M01_Y-2",
               "M02_Y-2",
               "M03_Y-2",
               "M04_Y-2",
               "M05_Y-2",
               "M06_Y-2",
               "M07_Y-2",
               "M08_Y-2",
               "M09_Y-2",
               "M10_Y-2",
               "M11_Y-2",
               "M12_Y-2",
               "M01_Y-1",
               "M02_Y-1",
               "M03_Y-1",
               "M04_Y-1",
               "M05_Y-1",
               "M06_Y-1",
               "M07_Y-1",
               "M08_Y-1",
               "M09_Y-1",
               "M10_Y-1",
               "M11_Y-1",
               "M12_Y-1",
               "M01_Y-0",
               "M02_Y-0",
               "M03_Y-0",
               "M04_Y-0",
               "M05_Y-0",
               "M06_Y-0",
               "M07_Y-0",
               "M08_Y-0",
               "M09_Y-0",
               "M10_Y-0",
               "M11_Y-0",
               "M12_Y-0",
               "M01_Y+1",
               "M02_Y+1",
               "M03_Y+1",
               "M04_Y+1",
               "M05_Y+1",
               "M06_Y+1",
               "M07_Y+1",
               "M08_Y+1",
               "M09_Y+1",
               "M10_Y+1",
               "M11_Y+1",
               "M12_Y+1",
               "M01_Y+2",
               "M02_Y+2",
               "M03_Y+2",
               "M04_Y+2",
               "M05_Y+2",
               "M06_Y+2",
               "M07_Y+2",
               "M08_Y+2",
               "M09_Y+2",
               "M10_Y+2",
               "M11_Y+2",
               "M12_Y+2",
               "Q_M01_Y-2",
               "Q_M02_Y-2",
               "Q_M03_Y-2",
               "Q_M04_Y-2",
               "Q_M05_Y-2",
               "Q_M06_Y-2",
               "Q_M07_Y-2",
               "Q_M08_Y-2",
               "Q_M09_Y-2",
               "Q_M10_Y-2",
               "Q_M11_Y-2",
               "Q_M12_Y-2",
               "Q_M01_Y-1",
               "Q_M02_Y-1",
               "Q_M03_Y-1",
               "Q_M04_Y-1",
               "Q_M05_Y-1",
               "Q_M06_Y-1",
               "Q_M07_Y-1",
               "Q_M08_Y-1",
               "Q_M09_Y-1",
               "Q_M10_Y-1",
               "Q_M11_Y-1",
               "Q_M12_Y-1",
               "Q_M01_Y-0",
               "Q_M02_Y-0",
               "Q_M03_Y-0",
               "Q_M04_Y-0",
               "Q_M05_Y-0",
               "Q_M06_Y-0",
               "Q_M07_Y-0",
               "Q_M08_Y-0",
               "Q_M09_Y-0",
               "Q_M10_Y-0",
               "Q_M11_Y-0",
               "Q_M12_Y-0",
               "Q_M01_Y+1",
               "Q_M02_Y+1",
               "Q_M03_Y+1",
               "Q_M04_Y+1",
               "Q_M05_Y+1",
               "Q_M06_Y+1",
               "Q_M07_Y+1",
               "Q_M08_Y+1",
               "Q_M09_Y+1",
               "Q_M10_Y+1",
               "Q_M11_Y+1",
               "Q_M12_Y+1",
               "Q_M01_Y+2",
               "Q_M02_Y+2",
               "Q_M03_Y+2",
               "Q_M04_Y+2",
               "Q_M05_Y+2",
               "Q_M06_Y+2",
               "Q_M07_Y+2",
               "Q_M08_Y+2",
               "Q_M09_Y+2",
               "Q_M10_Y+2",
               "Q_M11_Y+2",
               "Q_M12_Y+2"
        from MR3_SIOP_ALL_BU_DATA_ADD_ACC_LABEL
        where FCST_VERSION = #{selectedVersion, jdbcType=VARCHAR}
    </sql>

    <select id="querySiopDataCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="querySiopDataSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="querySiopData" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="querySiopDataSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="downloadSiopData" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        select FCST_VERSION,
                PRODUCT_LINE,
                SIOP_SCOPE,
                LOCAL_PRODUCT_LINE,
                LOCAL_PRODUCT_FAMILY,
                LOCAL_PRODUCT_SUB_FAMILY,
                ACC_LABEL,
                "M01_Y-2",
                "M02_Y-2",
                "M03_Y-2",
                "M04_Y-2",
                "M05_Y-2",
                "M06_Y-2",
                "M07_Y-2",
                "M08_Y-2",
                "M09_Y-2",
                "M10_Y-2",
                "M11_Y-2",
                "M12_Y-2",
                "M01_Y-1",
                "M02_Y-1",
                "M03_Y-1",
                "M04_Y-1",
                "M05_Y-1",
                "M06_Y-1",
                "M07_Y-1",
                "M08_Y-1",
                "M09_Y-1",
                "M10_Y-1",
                "M11_Y-1",
                "M12_Y-1",
                "M01_Y-0",
                "M02_Y-0",
                "M03_Y-0",
                "M04_Y-0",
                "M05_Y-0",
                "M06_Y-0",
                "M07_Y-0",
                "M08_Y-0",
                "M09_Y-0",
                "M10_Y-0",
                "M11_Y-0",
                "M12_Y-0",
                "M01_Y+1",
                "M02_Y+1",
                "M03_Y+1",
                "M04_Y+1",
                "M05_Y+1",
                "M06_Y+1",
                "M07_Y+1",
                "M08_Y+1",
                "M09_Y+1",
                "M10_Y+1",
                "M11_Y+1",
                "M12_Y+1",
                "M01_Y+2",
                "M02_Y+2",
                "M03_Y+2",
                "M04_Y+2",
                "M05_Y+2",
                "M06_Y+2",
                "M07_Y+2",
                "M08_Y+2",
                "M09_Y+2",
                "M10_Y+2",
                "M11_Y+2",
                "M12_Y+2",
                "Q_M01_Y-2",
                "Q_M02_Y-2",
                "Q_M03_Y-2",
                "Q_M04_Y-2",
                "Q_M05_Y-2",
                "Q_M06_Y-2",
                "Q_M07_Y-2",
                "Q_M08_Y-2",
                "Q_M09_Y-2",
                "Q_M10_Y-2",
                "Q_M11_Y-2",
                "Q_M12_Y-2",
                "Q_M01_Y-1",
                "Q_M02_Y-1",
                "Q_M03_Y-1",
                "Q_M04_Y-1",
                "Q_M05_Y-1",
                "Q_M06_Y-1",
                "Q_M07_Y-1",
                "Q_M08_Y-1",
                "Q_M09_Y-1",
                "Q_M10_Y-1",
                "Q_M11_Y-1",
                "Q_M12_Y-1",
                "Q_M01_Y-0",
                "Q_M02_Y-0",
                "Q_M03_Y-0",
                "Q_M04_Y-0",
                "Q_M05_Y-0",
                "Q_M06_Y-0",
                "Q_M07_Y-0",
                "Q_M08_Y-0",
                "Q_M09_Y-0",
                "Q_M10_Y-0",
                "Q_M11_Y-0",
                "Q_M12_Y-0",
                "Q_M01_Y+1",
                "Q_M02_Y+1",
                "Q_M03_Y+1",
                "Q_M04_Y+1",
                "Q_M05_Y+1",
                "Q_M06_Y+1",
                "Q_M07_Y+1",
                "Q_M08_Y+1",
                "Q_M09_Y+1",
                "Q_M10_Y+1",
                "Q_M11_Y+1",
                "Q_M12_Y+1",
                "Q_M01_Y+2",
                "Q_M02_Y+2",
                "Q_M03_Y+2",
                "Q_M04_Y+2",
                "Q_M05_Y+2",
                "Q_M06_Y+2",
                "Q_M07_Y+2",
                "Q_M08_Y+2",
                "Q_M09_Y+2",
                "Q_M10_Y+2",
                "Q_M11_Y+2",
                "Q_M12_Y+2"
        from MR3_SIOP_ALL_BU_DATA_ADD_ACC_LABEL
        where FCST_VERSION = #{selectedVersion, jdbcType=VARCHAR}
        <include refid="global.select_footer"/>
    </select>

    <update id="updateSiopData">
        update MR3_SIOP_ALL_BU_DATA_ADD_ACC_LABEL
        SET
        <foreach collection="updates" item="col" separator=",">
            "${col.key}" = #{col.value,jdbcType=VARCHAR}
        </foreach>,
        update_by$ = #{userid,jdbcType=VARCHAR},
        update_date$ = sysdate
        where rowid = #{rowid,jdbcType=VARCHAR}
    </update>

    <update id="refreshSiopData">
        begin
            SP_REFRESH_SIOP_ALL_BU_DATA();
        end;
    </update>

    <delete id="deleteSiopData">
        delete from MR3_SIOP_ALL_BU_DATA_ADD_ACC_LABEL where rowid in
        <foreach collection="deletes" open="(" close=")" separator="," item="item">#{item, jdbcType=VARCHAR}</foreach>
        and FCST_VERSION = #{version,jdbcType=VARCHAR}
    </delete>

    <insert id="createSiopData">
        insert into MR3_SIOP_ALL_BU_DATA_ADD_ACC_LABEL
        (
        FCST_VERSION,
        <foreach collection="headers" item="header" separator=",">
            "${header}"
        </foreach>, create_by$, create_date$
        )
        <foreach collection="creates" item="list" separator=" union all ">
            select #{version, jdbcType=VARCHAR},
            <foreach collection="headers" item="header" separator=",">
                #{list.${header}, jdbcType=VARCHAR}
            </foreach>, #{userid,jdbcType=VARCHAR}, sysdate
            from dual
        </foreach>
    </insert>

    <select id="queryMaterialOwnerList" resultType="java.util.Map">
        SELECT DISTINCT UMD.SESA_CODE AS VAL, UMD.USER_NAME || ' [' || UMD.EMAIL || ']' AS LABEL
        FROM MR3_MATERIAL_OWNER T
                 INNER JOIN SY_USER_MASTER_DATA UMD ON T.OWNER = UMD.SESA_CODE
        ORDER BY UMD.USER_NAME || ' [' || UMD.EMAIL || ']'
    </select>

    <sql id="queryMaterialOwnerDataSQL">
        SELECT PLANT_CODE, CLAIM_DATA, CLAIM_TYPE, COMMENTS, OWNER, UMD.USER_NAME AS OWNER_NAME
          FROM MR3_MATERIAL_OWNER T
          LEFT JOIN SY_USER_MASTER_DATA UMD ON T.OWNER = UMD.SESA_CODE
          <if test="materialOwner != null and materialOwner.size() > 0">
              WHERE T.OWNER IN
              <foreach collection="materialOwner" open="(" close=")" separator="," item="item">
                #{item, jdbcType=VARCHAR}
              </foreach>
          </if>
    </sql>

    <select id="queryMaterialOwnerDataCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryMaterialOwnerDataSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryMaterialOwnerData" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryMaterialOwnerDataSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <insert id="insertMaterialOwnerTemp">
        INSERT INTO MR3_MATERIAL_OWNER_TEMPORARY (PLANT_CODE, CLAIM_DATA, CLAIM_TYPE, COMMENTS)
        <foreach collection="list" separator=" union all" item="item">
            select
            #{item.plantCode, jdbcType=VARCHAR},
            #{item.claimData, jdbcType=VARCHAR},
            #{item.claimType, jdbcType=VARCHAR},
            #{item.comments, jdbcType=VARCHAR}
            from dual
        </foreach>
    </insert>

    <insert id="backupMaterialOwner">
        INSERT INTO MATERIAL_OWNER_HIST
        (CLAIM_DATA, CLAIM_TYPE, PLANT_CODE, COMMENTS, OWNER, VALIDATE_FROM, VALIDATE_TO)
        SELECT CLAIM_DATA, CLAIM_TYPE, PLANT_CODE, COMMENTS, OWNER, CREATE_DATE$, SYSDATE
        FROM MR3_MATERIAL_OWNER T
        WHERE T.OWNER = #{userid, jdbcType=VARCHAR}
    </insert>

    <delete id="deleteMaterialOwner">
        DELETE FROM MR3_MATERIAL_OWNER T WHERE T.OWNER = #{userid, jdbcType=VARCHAR}
    </delete>

    <insert id="syncMaterialOwner">
        INSERT
        /*+  IGNORE_ROW_ON_DUPKEY_INDEX (MATERIAL_OWNER,MATERIAL_OWNER_PK) */
        INTO MR3_MATERIAL_OWNER
        (CLAIM_DATA, PLANT_CODE, COMMENTS, CREATE_BY$, CREATE_DATE$, OWNER, CLAIM_TYPE)
        SELECT CLAIM_DATA, PLANT_CODE, COMMENTS, #{userid, jdbcType=VARCHAR}, SYSDATE,
               #{userid, jdbcType=VARCHAR}, CLAIM_TYPE
          FROM MR3_MATERIAL_OWNER_TEMPORARY
    </insert>

    <select id="queryConflictData" resultType="java.util.LinkedHashMap">
        SELECT T.PLANT_CODE,
               T.CLAIM_DATA,
               T.CLAIM_TYPE,
               T.COMMENTS,
               T.OWNER AS ORG_OWNER,
               UMD.USER_NAME AS ORG_OWNER_NAME,
               TT.SESA_CODE AS NEW_OWNER,
               TT.USER_NAME AS NEW_OWNER_NAME
          FROM MR3_MATERIAL_OWNER T LEFT JOIN SY_USER_MASTER_DATA UMD ON T.OWNER = UMD.SESA_CODE
                                LEFT JOIN (SELECT UMD2.SESA_CODE, UMD2.USER_NAME FROM SY_USER_MASTER_DATA UMD2 WHERE UMD2.SESA_CODE = #{userid, jdbcType=VARCHAR}) TT ON 1 = 1
         WHERE T.OWNER != #{userid, jdbcType=VARCHAR}
           AND EXISTS (SELECT 1 FROM MR3_MATERIAL_OWNER_TEMPORARY T2 WHERE T.CLAIM_TYPE = T2.CLAIM_TYPE AND T.CLAIM_DATA = T2.CLAIM_DATA AND T.PLANT_CODE = T2.PLANT_CODE)
    </select>

    <insert id="backupMaterialOwnerConflict">
        INSERT INTO MATERIAL_OWNER_HIST
        (CLAIM_DATA, CLAIM_TYPE, PLANT_CODE, COMMENTS, OWNER, VALIDATE_FROM, VALIDATE_TO)

            SELECT CLAIM_DATA, CLAIM_TYPE, PLANT_CODE, COMMENTS, OWNER, CREATE_DATE$, SYSDATE
             FROM MR3_MATERIAL_OWNER T
            WHERE T.OWNER = #{userid, jdbcType=VARCHAR}

            UNION ALL

            SELECT
               T.CLAIM_DATA,
               T.CLAIM_TYPE,
               T.PLANT_CODE,
               T.COMMENTS,
               T.OWNER,
               T.CREATE_DATE$,
               SYSDATE
          FROM MR3_MATERIAL_OWNER T
         WHERE T.OWNER != #{userid, jdbcType=VARCHAR}
           AND EXISTS (SELECT 1 FROM MR3_MATERIAL_OWNER_TEMPORARY T2 WHERE T.CLAIM_TYPE = T2.CLAIM_TYPE AND T.CLAIM_DATA = T2.CLAIM_DATA AND T.PLANT_CODE = T2.PLANT_CODE)
    </insert>

    <delete id="deleteMaterialOwnerConflict">
        DELETE FROM MR3_MATERIAL_OWNER T
         WHERE T.OWNER = #{userid, jdbcType=VARCHAR}
            OR EXISTS (SELECT 1 FROM MR3_MATERIAL_OWNER_TEMPORARY T2 WHERE T.CLAIM_TYPE = T2.CLAIM_TYPE AND T.CLAIM_DATA = T2.CLAIM_DATA AND T.PLANT_CODE = T2.PLANT_CODE)
    </delete>

    <sql id="queryMaterialOwnerInvalidDataSQL">
        SELECT PLANT_CODE, T.CLAIM_DATA, T.CLAIM_TYPE, COMMENTS, OWNER, UMD.USER_NAME AS OWNER_NAME
          FROM MR3_MATERIAL_OWNER T
          LEFT JOIN SY_USER_MASTER_DATA UMD ON T.OWNER = UMD.SESA_CODE
         WHERE NOT EXISTS (
            SELECT 1 FROM MATERIAL_MASTER_V T2 WHERE T.CLAIM_DATA = T2.MATERIAL AND T.PLANT_CODE = T2.PLANT_CODE
         ) AND T.OWNER = #{session.userid,jdbcType=VARCHAR}
         AND T.CLAIM_TYPE = 'MATERIAL'

         UNION ALL

         SELECT PLANT_CODE, T.CLAIM_DATA, T.CLAIM_TYPE, COMMENTS, OWNER, UMD.USER_NAME AS OWNER_NAME
          FROM MR3_MATERIAL_OWNER T
          LEFT JOIN SY_USER_MASTER_DATA UMD ON T.OWNER = UMD.SESA_CODE
         WHERE NOT EXISTS (
            SELECT 1 FROM MATERIAL_MASTER_V T2 WHERE T.CLAIM_DATA = T2.MRP_CONTROLLER AND T.PLANT_CODE = T2.PLANT_CODE
         ) AND T.OWNER = #{session.userid,jdbcType=VARCHAR}
         AND T.CLAIM_TYPE = 'MRP_CONTROLLER'

         UNION ALL

         SELECT PLANT_CODE, T.CLAIM_DATA, T.CLAIM_TYPE, COMMENTS, OWNER, UMD.USER_NAME AS OWNER_NAME
          FROM MR3_MATERIAL_OWNER T
          LEFT JOIN SY_USER_MASTER_DATA UMD ON T.OWNER = UMD.SESA_CODE
         WHERE NOT EXISTS (
            SELECT 1 FROM MATERIAL_MASTER_V T2 WHERE T.CLAIM_DATA = T2.PURCHASING_GROUP AND T.PLANT_CODE = T2.PLANT_CODE
         ) AND T.OWNER = #{session.userid,jdbcType=VARCHAR}
         AND T.CLAIM_TYPE = 'PURCHASING_GROUP'

         UNION ALL

         SELECT PLANT_CODE, T.CLAIM_DATA, T.CLAIM_TYPE, COMMENTS, OWNER, UMD.USER_NAME AS OWNER_NAME
          FROM MR3_MATERIAL_OWNER T
          LEFT JOIN SY_USER_MASTER_DATA UMD ON T.OWNER = UMD.SESA_CODE
         WHERE NOT EXISTS (
            SELECT 1 FROM MATERIAL_MASTER_V T2 WHERE T.CLAIM_DATA = T2.VENDOR_CODE AND T.PLANT_CODE = T2.PLANT_CODE
         ) AND T.OWNER = #{session.userid,jdbcType=VARCHAR}
         AND T.CLAIM_TYPE = 'VENDOR_CODE'
    </sql>

    <select id="queryMaterialOwnerInvalidDataCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryMaterialOwnerInvalidDataSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryMaterialOwnerInvalidData" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryMaterialOwnerInvalidDataSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <delete id="deleteAbnormalMaterial">
        BEGIN
            DELETE FROM MR3_MATERIAL_OWNER T
             WHERE NOT EXISTS (
                SELECT 1 FROM MATERIAL_MASTER_V T2 WHERE T.CLAIM_DATA = T2.MATERIAL AND T.PLANT_CODE = T2.PLANT_CODE
             ) AND T.OWNER = #{session.userid,jdbcType=VARCHAR}
             AND T.CLAIM_TYPE = 'MATERIAL';

             DELETE FROM MR3_MATERIAL_OWNER T
             WHERE NOT EXISTS (
                SELECT 1 FROM MATERIAL_MASTER_V T2 WHERE T.CLAIM_DATA = T2.MRP_CONTROLLER AND T.PLANT_CODE = T2.PLANT_CODE
             ) AND T.OWNER = #{session.userid,jdbcType=VARCHAR}
             AND T.CLAIM_TYPE = 'MRP_CONTROLLER';

             DELETE FROM MR3_MATERIAL_OWNER T
             WHERE NOT EXISTS (
                SELECT 1 FROM MATERIAL_MASTER_V T2 WHERE T.CLAIM_DATA = T2.PURCHASING_GROUP AND T.PLANT_CODE = T2.PLANT_CODE
             ) AND T.OWNER = #{session.userid,jdbcType=VARCHAR}
             AND T.CLAIM_TYPE = 'PURCHASING_GROUP';

             DELETE FROM MR3_MATERIAL_OWNER T
             WHERE NOT EXISTS (
                SELECT 1 FROM MATERIAL_MASTER_V T2 WHERE T.CLAIM_DATA = T2.VENDOR_CODE AND T.PLANT_CODE = T2.PLANT_CODE
             ) AND T.OWNER = #{session.userid,jdbcType=VARCHAR}
             AND T.CLAIM_TYPE = 'VENDOR_CODE';
        END;
    </delete>

    <sql id="queryConsignmentTimeLimitDataSql">
        SELECT T.MATERIAL,
               T.PLANT_CODE,
               T.VENDOR_CODE,
               T2.CONSIGN_TIME_LIMIT,
               CASE
                   WHEN T3.USER_NAME IS NULL THEN T2.CREATE_BY$
                   ELSE T3.USER_NAME || '[' || T2.CREATE_BY$ || ']' END AS CREATE_BY,
               TO_CHAR(T2.CREATE_DATE$, 'YYYY/MM/DD HH24:MI:SS')        AS CREATE_DATE,
               CASE
                   WHEN T4.USER_NAME IS NULL THEN T2.UPDATE_BY$
                   ELSE T4.USER_NAME || '[' || T2.UPDATE_BY$ || ']' END AS UPDATE_BY,
               TO_CHAR(T2.UPDATE_DATE$, 'YYYY/MM/DD HH24:MI:SS')        AS UPDATE_DATE
        FROM CONSIGNMENT_SOURCE_V T
                 LEFT JOIN MR3_CONSIGNMENT_TIME_LIMIT T2 ON T.PLANT_CODE = T2.PLANT_CODE AND T.MATERIAL = T2.MATERIAL AND T.VENDOR_CODE = T2.VENDOR_CODE
                 LEFT JOIN SY_USER_MASTER_DATA T3 ON T2.CREATE_BY$ = T3.SESA_CODE
                 LEFT JOIN SY_USER_MASTER_DATA T4 ON T2.UPDATE_BY$ = T4.SESA_CODE
            AND T.VENDOR_CODE = T2.VENDOR_CODE
    </sql>

    <select id="queryConsignmentTimeLimitDataCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryConsignmentTimeLimitDataSql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryConsignmentTimeLimitData" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryConsignmentTimeLimitDataSql"/>
        <include refid="global.select_footer"/>
    </select>

    <update id="updateConsignmentTimeLimitData">
        MERGE INTO MR3_CONSIGNMENT_TIME_LIMIT T_OLD
        USING (
            SELECT #{material, jdbcType=VARCHAR} AS MATERIAL,
                   #{plantCode, jdbcType=VARCHAR} AS PLANT_CODE,
                   #{vendorCode, jdbcType=VARCHAR} AS VENDOR_CODE,
                   <choose>
                       <when test="agingDay >=0">
                           #{agingDay, jdbcType=DOUBLE} AS AGING_DAY
                       </when>
                       <otherwise>
                           null AS AGING_DAY
                       </otherwise>
                   </choose>
            FROM DUAL
        ) T_NEW ON (
            T_OLD.MATERIAL = T_NEW.MATERIAL
            AND T_OLD.PLANT_CODE = T_NEW.PLANT_CODE
            AND T_OLD.VENDOR_CODE = T_NEW.VENDOR_CODE
        )
        WHEN MATCHED THEN
            UPDATE SET T_OLD.CONSIGN_TIME_LIMIT = T_NEW.AGING_DAY,
                       T_OLD.UPDATE_BY$ = #{userid,jdbcType=VARCHAR},
                       T_OLD.UPDATE_DATE$ = SYSDATE
        WHEN NOT MATCHED THEN
            INSERT (MATERIAL, PLANT_CODE, VENDOR_CODE, CONSIGN_TIME_LIMIT, CREATE_BY$, CREATE_DATE$)
            VALUES (T_NEW.MATERIAL, T_NEW.PLANT_CODE, T_NEW.VENDOR_CODE, T_NEW.AGING_DAY, #{userid,jdbcType=VARCHAR},SYSDATE)
    </update>

    <update id="mergeConsignmentTimeLimitData">
        MERGE INTO MR3_CONSIGNMENT_TIME_LIMIT T_OLD
        USING (
            <foreach collection="data" item="item" separator=" UNION ALL ">
                SELECT #{item.MATERIAL, jdbcType=VARCHAR} AS MATERIAL,
                       #{item.PLANT_CODE, jdbcType=VARCHAR} AS PLANT_CODE,
                       #{item.VENDOR_CODE, jdbcType=VARCHAR} AS VENDOR_CODE,
                       <choose>
                           <when test="item.CONSIGN_TIME_LIMIT >=0">
                               #{item.CONSIGN_TIME_LIMIT, jdbcType=DOUBLE} AS AGING_DAY
                           </when>
                           <otherwise>
                               null AS AGING_DAY
                           </otherwise>
                       </choose>
                FROM DUAL
            </foreach>

        ) T_NEW ON (
            T_OLD.MATERIAL = T_NEW.MATERIAL
            AND T_OLD.PLANT_CODE = T_NEW.PLANT_CODE
            AND T_OLD.VENDOR_CODE = T_NEW.VENDOR_CODE
        )
        WHEN MATCHED THEN
            UPDATE SET T_OLD.CONSIGN_TIME_LIMIT = T_NEW.AGING_DAY,
                       T_OLD.UPDATE_BY$ = #{userid,jdbcType=VARCHAR},
                       T_OLD.UPDATE_DATE$ = SYSDATE
        WHEN NOT MATCHED THEN
            INSERT (MATERIAL, PLANT_CODE, VENDOR_CODE, CONSIGN_TIME_LIMIT, CREATE_BY$, CREATE_DATE$)
            VALUES (T_NEW.MATERIAL, T_NEW.PLANT_CODE, T_NEW.VENDOR_CODE, T_NEW.AGING_DAY, #{userid,jdbcType=VARCHAR},SYSDATE)
    </update>

    <select id="queryWechatBotPresetationReportByID" parameterType="java.util.Map">
        SELECT T.CONFIG FROM SCPA.REMOTE_NOTIFICATION_WECHAT_AUTO_REPLY T WHERE UPPER(T.JOB_CODE) = UPPER(#{id,jdbcType=VARCHAR})
    </select>

    <select id="queryCloReportVersion" parameterType="java.lang.String">
        SELECT DISTINCT YEAR FROM SCPA.MR3_CLO_TARGET ORDER BY YEAR DESC
    </select>

    <sql id="queryCloReportDataSql">
        SELECT T.YEAR || ',' || T.MATERIAL AS ROW_ID,
               T.MATERIAL, COMPETITIVE_LT_CRITERIA, COMMENTS, BEST_CLO_LT_IN_THE_MARKET_WD, NAME_OF_COMPETITOR_WITH_BEST_CLO_LT, CURRENT_CLO_LT, PLANT_CODE
          FROM MR3_CLO_TARGET T
         WHERE YEAR = #{version, jdbcType=VARCHAR}
    </sql>

    <select id="queryCloReportDataCount">
        <include refid="global.count_header"/>
        <include refid="queryCloReportDataSql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryCloReportData" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryCloReportDataSql"/>
        <include refid="global.select_footer"/>
    </select>

    <update id="mergeCloReportData">
        MERGE INTO SCPA.MR3_CLO_TARGET T
        USING
        (
        <foreach collection="dataList" item="item" separator="union all">
            SELECT TO_CHAR(SYSDATE, 'YYYY') YEAR,
                   #{item.MATERIAL, jdbcType=VARCHAR} MATERIAL,
                   #{item.PLANT_CODE, jdbcType=VARCHAR} PLANT_CODE,

                   #{item.COMMENTS, jdbcType=VARCHAR} COMMENTS,
                   #{item.CURRENT_CLO_LT, jdbcType=VARCHAR} CURRENT_CLO_LT,
                   #{item.COMPETITIVE_LT_CRITERIA, jdbcType=VARCHAR} COMPETITIVE_LT_CRITERIA,
                   #{item.BEST_CLO_LT_IN_THE_MARKET_WD, jdbcType=VARCHAR} BEST_CLO_LT_IN_THE_MARKET_WD,
                   #{item.NAME_OF_COMPETITOR_WITH_BEST_CLO_LT, jdbcType=VARCHAR} NAME_OF_COMPETITOR_WITH_BEST_CLO_LT
            FROM DUAL
        </foreach>
        ) S ON (T.YEAR = S.YEAR AND T.MATERIAL = S.MATERIAL AND NVL(T.PLANT_CODE, 'NULL') = NVL(S.PLANT_CODE, 'NULL'))
        WHEN MATCHED THEN
        UPDATE SET
            T.COMMENTS = S.COMMENTS,
            T.CURRENT_CLO_LT = S.CURRENT_CLO_LT,
            T.COMPETITIVE_LT_CRITERIA = S.COMPETITIVE_LT_CRITERIA,
            T.BEST_CLO_LT_IN_THE_MARKET_WD = S.BEST_CLO_LT_IN_THE_MARKET_WD,
            T.NAME_OF_COMPETITOR_WITH_BEST_CLO_LT = S.NAME_OF_COMPETITOR_WITH_BEST_CLO_LT,
            T.UPDATE_DATE$ = SYSDATE,
            T.UPDATE_BY$ = #{userid, jdbcType=VARCHAR}
        WHEN NOT MATCHED THEN
        INSERT ("YEAR", MATERIAL, PLANT_CODE, COMMENTS, CURRENT_CLO_LT, COMPETITIVE_LT_CRITERIA, BEST_CLO_LT_IN_THE_MARKET_WD, NAME_OF_COMPETITOR_WITH_BEST_CLO_LT, CREATE_BY$, CREATE_DATE$)
        VALUES (S.YEAR, S.MATERIAL, S.PLANT_CODE, S.COMMENTS, S.CURRENT_CLO_LT, S.COMPETITIVE_LT_CRITERIA, S.BEST_CLO_LT_IN_THE_MARKET_WD, S.NAME_OF_COMPETITOR_WITH_BEST_CLO_LT, #{userid, jdbcType=VARCHAR}, SYSDATE)
    </update>

    <insert id="createCloDataByTable">
        INSERT INTO MR3_CLO_TARGET
        (YEAR,
        <foreach collection="headers" item="header" separator=",">
            ${header}
        </foreach>
        )
        <foreach collection="creates" item="list" separator=" union all ">
            SELECT #{year, jdbcType=VARCHAR},
            <foreach collection="headers" item="header" separator=",">
                #{list.${header}, jdbcType=VARCHAR}
            </foreach>
            FROM DUAL
        </foreach>
    </insert>

    <delete id="deleteCloDataByTable">
		DELETE FROM MR3_CLO_TARGET
		 WHERE YEAR || ',' || MATERIAL IN
		<foreach collection="deletes" open="(" close=")" separator="," item="item">#{item, jdbcType=VARCHAR}</foreach>
	</delete>

    <update id="updateCloDataByTable">
		UPDATE MR3_CLO_TARGET
		SET
		<foreach collection="updates" item="col" separator=",">
			${col.key} = #{col.value,jdbcType=VARCHAR}
		</foreach>,
		update_by$ = #{userid,jdbcType=VARCHAR},
		update_date$ = sysdate
		where YEAR || ',' || MATERIAL = #{pk, jdbcType=VARCHAR}
	</update>

    <select id="queryProductGroupCascader" resultType="java.util.Map">
        SELECT NAME,
               CATEGORY
        FROM SCPA.PRODUCT_GROUP_ABC_FILTER_V T
        ORDER BY CATEGORY,NAME
    </select>

    <sql id="queryProductGroupDataSql">
        SELECT T.MATERIAL || ',' || T.PLANT_CODE AS ROW_ID,
               T.MATERIAL, T.PLANT_CODE,
               T.PRODUCT_GROUP_A,
               T.PRODUCT_GROUP_B,
               T.PRODUCT_GROUP_C,
               T.PRODUCT_GROUP_D,
               T.PRODUCT_GROUP_E
        FROM ${SCPA.MR3_PRODUCT_GROUP_ABC} T
        <where>
            <if test="_filters != null and _filters != ''.toString()">
                and ${_filters}
            </if>
        </where>
    </sql>

    <select id="queryProductGroupDataCount">
        <include refid="global.count_header"/>
        <include refid="queryProductGroupDataSql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryProductGroupData" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryProductGroupDataSql"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryProductGroupDataAll" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        SELECT T.MATERIAL || ',' || T.PLANT_CODE AS ROW_ID,
               T.MATERIAL, T.PLANT_CODE,
               T.PRODUCT_GROUP_A,
               T.PRODUCT_GROUP_A_OWNER,
               T.PRODUCT_GROUP_B,
               T.PRODUCT_GROUP_B_OWNER,
               T.PRODUCT_GROUP_C,
               T.PRODUCT_GROUP_C_OWNER,
               T.PRODUCT_GROUP_D,
               T.PRODUCT_GROUP_D_OWNER,
               T.PRODUCT_GROUP_E,
               T.PRODUCT_GROUP_E_OWNER
        FROM SCPA.MR3_PRODUCT_GROUP_ABC T
    </select>

    <update id="mergeProductGroupData">
        MERGE INTO SCPA.MR3_PRODUCT_GROUP_ABC T
        USING
        (
        <foreach collection="dataList" item="item" separator="union all">
            SELECT
                #{item.MATERIAL, jdbcType=VARCHAR} MATERIAL,
                #{item.PLANT_CODE, jdbcType=VARCHAR} PLANT_CODE,
                #{item.PRODUCT_GROUP_A, jdbcType=VARCHAR} PRODUCT_GROUP_A,
                #{item.PRODUCT_GROUP_B, jdbcType=VARCHAR} PRODUCT_GROUP_B,
                #{item.PRODUCT_GROUP_C, jdbcType=VARCHAR} PRODUCT_GROUP_C,
                #{item.PRODUCT_GROUP_D, jdbcType=VARCHAR} PRODUCT_GROUP_D,
                #{item.PRODUCT_GROUP_E, jdbcType=VARCHAR} PRODUCT_GROUP_E,
                #{item.PRODUCT_GROUP_A_OWNER, jdbcType=VARCHAR} PRODUCT_GROUP_A_OWNER,
                #{item.PRODUCT_GROUP_B_OWNER, jdbcType=VARCHAR} PRODUCT_GROUP_B_OWNER,
                #{item.PRODUCT_GROUP_C_OWNER, jdbcType=VARCHAR} PRODUCT_GROUP_C_OWNER,
                #{item.PRODUCT_GROUP_D_OWNER, jdbcType=VARCHAR} PRODUCT_GROUP_D_OWNER,
                #{item.PRODUCT_GROUP_E_OWNER, jdbcType=VARCHAR} PRODUCT_GROUP_E_OWNER
            FROM DUAL
        </foreach>
        ) S ON (T.MATERIAL = S.MATERIAL AND T.PLANT_CODE = S.PLANT_CODE)
        WHEN MATCHED THEN
        UPDATE SET
            T.PRODUCT_GROUP_A = NVL(S.PRODUCT_GROUP_A, T.PRODUCT_GROUP_A),
            T.PRODUCT_GROUP_B = NVL(S.PRODUCT_GROUP_B, T.PRODUCT_GROUP_B),
            T.PRODUCT_GROUP_C = NVL(S.PRODUCT_GROUP_C, T.PRODUCT_GROUP_C),
            T.PRODUCT_GROUP_D = NVL(S.PRODUCT_GROUP_D, T.PRODUCT_GROUP_D),
            T.PRODUCT_GROUP_E = NVL(S.PRODUCT_GROUP_E, T.PRODUCT_GROUP_E),
            T.PRODUCT_GROUP_A_OWNER = NVL(S.PRODUCT_GROUP_A_OWNER, T.PRODUCT_GROUP_A_OWNER),
            T.PRODUCT_GROUP_B_OWNER = NVL(S.PRODUCT_GROUP_B_OWNER, T.PRODUCT_GROUP_B_OWNER),
            T.PRODUCT_GROUP_C_OWNER = NVL(S.PRODUCT_GROUP_C_OWNER, T.PRODUCT_GROUP_C_OWNER),
            T.PRODUCT_GROUP_D_OWNER = NVL(S.PRODUCT_GROUP_D_OWNER, T.PRODUCT_GROUP_D_OWNER),
            T.PRODUCT_GROUP_E_OWNER = NVL(S.PRODUCT_GROUP_E_OWNER, T.PRODUCT_GROUP_E_OWNER),
            T.UPDATE_DATE$ = SYSDATE,
            T.UPDATE_BY$ = #{userid, jdbcType=VARCHAR}
        WHEN NOT MATCHED THEN
        INSERT (MATERIAL, PLANT_CODE, PRODUCT_GROUP_A, PRODUCT_GROUP_B, PRODUCT_GROUP_C, PRODUCT_GROUP_D, PRODUCT_GROUP_E,
            PRODUCT_GROUP_A_OWNER, PRODUCT_GROUP_B_OWNER, PRODUCT_GROUP_C_OWNER, PRODUCT_GROUP_D_OWNER, PRODUCT_GROUP_E_OWNER, CREATE_BY$, CREATE_DATE$)
        VALUES (S.MATERIAL, S.PLANT_CODE, S.PRODUCT_GROUP_A, S.PRODUCT_GROUP_B, S.PRODUCT_GROUP_C, S.PRODUCT_GROUP_D, S.PRODUCT_GROUP_E,
            S.PRODUCT_GROUP_A_OWNER, S.PRODUCT_GROUP_B_OWNER, S.PRODUCT_GROUP_C_OWNER, S.PRODUCT_GROUP_D_OWNER, S.PRODUCT_GROUP_E_OWNER, #{userid, jdbcType=VARCHAR}, SYSDATE)
    </update>

    <insert id="createProductGroupDataByTable">
        INSERT INTO MR3_PRODUCT_GROUP_ABC
        (
            <foreach collection="headers" item="header" separator=",">
                ${header}
            </foreach>
        )
        <foreach collection="creates" item="list" separator=" union all ">
            SELECT
                <foreach collection="headers" item="header" separator=",">
                    #{list.${header}, jdbcType=VARCHAR}
                </foreach>
            FROM DUAL
        </foreach>
    </insert>

    <delete id="deleteProductGroupDataByTable">
        DELETE FROM MR3_PRODUCT_GROUP_ABC
        WHERE MATERIAL || ',' || PLANT_CODE IN
        <foreach collection="deletes" open="(" close=")" separator="," item="item">#{item, jdbcType=VARCHAR}</foreach>
    </delete>

    <update id="updateProductGroupDataByTable">
        UPDATE MR3_PRODUCT_GROUP_ABC
        SET
        <foreach collection="updates" item="col" separator=",">
            ${col.key} = #{col.value,jdbcType=VARCHAR}
        </foreach>,
        update_by$ = #{userid,jdbcType=VARCHAR},
        update_date$ = sysdate
        where MATERIAL || ',' || PLANT_CODE = #{pk, jdbcType=VARCHAR}
    </update>
</mapper>
