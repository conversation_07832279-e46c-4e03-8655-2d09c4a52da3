package com.scp.components.dao;

import com.scp.components.bean.*;
import com.starter.context.bean.SimpleSelect;
import com.starter.context.bean.scptable.ScpTableCell;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.SelectProvider;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IComponentsDao {

    void refreshO2LongTail();

    void refreshMaterial2ProductionLine();

    List<SimpleSelect> queryFcstVersion();

    void insertFsctDataTemp(@Param("version") String version, @Param("list") List<MPPFcstBean> data);

    void deleteFcstData(@Param("userid") String userid, @Param("version") String version);

    void mergeFcstData(@Param("userid") String userid);

    int queryMppFcstDataCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryMppFcstData(Map<String, Object> parameterMap);

    void saveMppForcastSource(Map<String, Object> param);

    List<SimpleSelect> queryOtdsReportVersion();

    int queryOtdsReportDataCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryOtdsReportData(Map<String, Object> parameterMap);

    void saveOtdsData(Map<String, Object> param);

    void insertOtdsReportDataTemp(@Param("version") String uploadVersion, @Param("list") List<OTDSReport> data);

    void deleteOtdsReportData(@Param("version") String uploadVersion);

    void mergeOtdsReportData(String userid);

    void refreshOtdsOfficialReportMV();

    List<SimpleSelect> queryMppCommitVersion();

    void insertMppCommitDataTemp(@Param("version") String version, @Param("list") List<MPPCommitBean> data);

    void deleteMppCommitData(@Param("userid") String userid, @Param("version") String version);

    void mergeMppCommitData(@Param("userid") String userid);

    int queryMppCommitDataCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryMppCommitData(Map<String, Object> parameterMap);

    void saveMppCommitSource(Map<String, Object> param);

    List<String> querySliceVersion();

    void insertSliceDataTemp(List<SliceRawData> data);

    void deleteSliceRawData(String userid, @Param("version") String version);

    void mergeSliceRawData(@Param("userid") String userid);

    int querySliceDataCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> querySliceData(Map<String, Object> parameterMap);

    int deleteSliceData(@Param("userid") String userid, @Param("deletes") List<String> removeList, @Param("version") String version);

    int createSliceData(@Param("userid") String userid, @Param("headers") List<String> headers, @Param("creates") List<Map<String, Object>> createList, @Param("version") String version);

    int updateSliceData(@Param("rowid") String pk, @Param("updates") List<ScpTableCell> updates, @Param("userid") String userid);

    List<Map<String, String>> queryMaterialOwnerList();

    int queryMaterialOwnerDataCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryMaterialOwnerData(Map<String, Object> parameterMap);

    void insertMaterialOwnerTemp(List<MaterialOwnerBean> data);

    void backupMaterialOwner(@Param("userid") String userid);

    void deleteMaterialOwner(@Param("userid") String userid);

    void syncMaterialOwner(String userid);

    List<LinkedHashMap<String, Object>> queryConflictData(String userid);

    void backupMaterialOwnerConflict(String userid);

    void deleteMaterialOwnerConflict(String userid);

    int queryMaterialOwnerInvalidDataCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryMaterialOwnerInvalidData(Map<String, Object> parameterMap);

    void deleteAbnormalMaterial(Map<String, Object> parameterMap);

    List<String> querySiopVersion();

    void insertSiopDataTemp(List<SiopAllBuData> data);

    void deleteSiopRawDataTemp(String userid, @Param("version") String version);

    void mergeSiopRawData(@Param("userid") String userid);

    int querySiopDataCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> querySiopData(Map<String, Object> parameterMap);

    int deleteSiopData(@Param("userid") String userid, @Param("deletes") List<String> removeList, @Param("version") String version);

    int createSiopData(@Param("userid") String userid, @Param("headers") List<String> headers, @Param("creates") List<Map<String, Object>> createList, @Param("version") String version);

    int updateSiopData(@Param("rowid") String pk, @Param("updates") List<ScpTableCell> updates, @Param("userid") String userid);

    void refreshSiopData();

    int queryManualSliceDataCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryManualSliceData(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryConsignmentTimeLimitData(Map<String, Object> parameterMap);

    int queryConsignmentTimeLimitDataCount(Map<String, Object> parameterMap);

    int updateConsignmentTimeLimitData(String plantCode, String material, String vendorCode, double agingDay, String userid);

    void mergeConsignmentTimeLimitData(String userid, List<Map<String, Object>> data);

    Map<String, String> queryWechatBotPresetationReportByID(Map<String, Object> parameterMap);

    List<String> queryCloReportVersion();

    int queryCloReportDataCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryCloReportData(Map<String, Object> parameterMap);

    void mergeCloReportData(List<Map<String, String>> dataList, String userid);

    int createCloDataByTable(List<String> headers, List<Map<String, Object>> creates, String userid, String year);

    int deleteCloDataByTable(List<String> deletes);

    int updateCloDataByTable(String pk, List<ScpTableCell> updates, String userid);

    int queryProductGroupDataCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryProductGroupData(Map<String, Object> parameterMap);

    List<Map<String, String>> queryProductGroupDataAll(Map<String, Object> parameterMap);

    void mergeProductGroupData(List<Map<String, String>> dataList, String userid);

    int createProductGroupDataByTable(List<String> headers, List<Map<String, Object>> creates, String userid);

    int deleteProductGroupDataByTable(List<String> deletes);

    int updateProductGroupDataByTable(String pk, List<ScpTableCell> updates, String userid);

    List<Map<String, String>> queryProductGroupCascader();
}
