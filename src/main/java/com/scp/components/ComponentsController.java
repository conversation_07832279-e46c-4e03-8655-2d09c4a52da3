package com.scp.components;

import com.scp.components.service.IComponentsService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.util.WebUtils;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/components", parent = "-1")
public class ComponentsController extends ControllerHelper {

    @Resource
    private IComponentsService componentsService;

    @Resource
    private Response response;

    @SchneiderRequestMapping("/o2/refresh_o2_long_tail")
    public Response refreshO2LongTail() {
        try {
            response = componentsService.refreshO2LongTail();
        } catch (Exception e) {
            response.setError(e);
        }
        return response;
    }

    @SchneiderRequestMapping("/mpp/refresh_material_2_production_line")
    public Response refreshMaterial2ProductionLine() {
        try {
            response = componentsService.refreshMaterial2ProductionLine();
        } catch (Exception e) {
            response.setError(e);
        }
        return response;
    }

    @SchneiderRequestMapping("/mpp/upload_fcst")
    public Response mppUploadFcst(HttpServletRequest request) {
        super.pageLoad(request);
        MultipartHttpServletRequest multipartRequest = WebUtils.getNativeRequest(request, MultipartHttpServletRequest.class);
        if (multipartRequest != null) {
            MultipartFile file = multipartRequest.getFile("file");
            try {
                return componentsService.uploadMppFcst(session.getUserid(), (String) parameterMap.get("uploadVersion"), file);
            } catch (Exception e) {
                return response.setError(e);
            }
        } else {
            return response.setError(new Exception("upload failed, multipartRequest is null !"));
        }
    }

    @SchneiderRequestMapping("/mpp/init_page")
    public Response mppInitPage(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return componentsService.initMppPage(session.getUserid());
    }

    @SchneiderRequestMapping("/mpp/download_template")
    public void mppDownloadTemplate(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        componentsService.downloadMppTemplate(parameterMap, response);
    }

    @SchneiderRequestMapping("/mpp/download_fcst_data")
    public void mppDownloadFcstData(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        componentsService.downloadMppFcstData(session.getUserid(), parameterMap, response);
    }

    @SchneiderRequestMapping("/mpp/query_fcst_columns")
    public Response mppQueryFcstColumns(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return componentsService.queryMppFcstColumns((String) parameterMap.get("fcst_version"));
    }

    @SchneiderRequestMapping("/mpp/query_fcst_data")
    public Response mppQueryFcstData(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return componentsService.queryMppFcstData(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping("/mpp/save_forcast_source")
    public Response mppSaveForcastSource(HttpServletRequest request) {
        super.pageLoad(request);
        return componentsService.saveMppForcastSource(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping("/otds/init_page")
    public Response initOtdsPage(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return componentsService.initOtdsPage();
    }

    @SchneiderRequestMapping("/otds/upload_report")
    public Response otdsUploadReport(HttpServletRequest request) {
        super.pageLoad(request);
        MultipartHttpServletRequest multipartRequest = WebUtils.getNativeRequest(request, MultipartHttpServletRequest.class);
        if (multipartRequest != null) {
            MultipartFile file = multipartRequest.getFile("file");
            try {
                return componentsService.otdsUploadReport(session.getUserid(), (String) parameterMap.get("uploadVersion"), file);
            } catch (Exception e) {
                return response.setError(e);
            }
        } else {
            return response.setError(new Exception("upload failed, multipartRequest is null !"));
        }
    }

    @SchneiderRequestMapping("/otds/query_otds_report_data")
    public Response queryOtdsReportData(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return componentsService.queryOtdsReportData(parameterMap);
    }

    @SchneiderRequestMapping("/otds/download_otds_template")
    public void downloadOTDSTemplate(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        componentsService.downloadOTDSTemplate(parameterMap, response);
    }

    @SchneiderRequestMapping("/otds/download_otds_data")
    public void downloadOTDSData(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        componentsService.downloadOTDSData(parameterMap, response);
    }

    @SchneiderRequestMapping("/otds/save_otds_data")
    public Response saveOtdsData(HttpServletRequest request) {
        super.pageLoad(request);
        return componentsService.saveOtdsData(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping("/otds/refresh_otds_official_report_mv")
    public Response refreshOtdsOfficialReportMV() {
        return componentsService.refreshOtdsOfficialReportMV();
    }

    @SchneiderRequestMapping("/mpp/commit/init_page")
    public Response initMppCommitPage(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return componentsService.initMppCommitPage(session.getUserid());
    }

    @SchneiderRequestMapping("/mpp/commit/upload_data")
    public Response uploadMppCommitData(HttpServletRequest request) {
        super.pageLoad(request);
        MultipartHttpServletRequest multipartRequest = WebUtils.getNativeRequest(request, MultipartHttpServletRequest.class);
        if (multipartRequest != null) {
            MultipartFile file = multipartRequest.getFile("file");
            try {
                return componentsService.uploadMppCommitData(session.getUserid(), (String) parameterMap.get("uploadVersion"), file);
            } catch (Exception e) {
                return response.setError(e);
            }
        } else {
            return response.setError(new Exception("upload failed, multipartRequest is null !"));
        }
    }

    @SchneiderRequestMapping("/mpp/commit/download_template")
    public void downloadMppCommitTemplate(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        componentsService.downloadMppCommitTemplate(parameterMap, response);
    }

    @SchneiderRequestMapping("/mpp/commit/download_data")
    public void downloadMppCommitData(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        componentsService.downloadMppCommitData(session.getUserid(), parameterMap, response);
    }

    @SchneiderRequestMapping("/mpp/commit/query_columns")
    public Response queryMppCommitColumns(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return componentsService.queryMppCommitColumns((String) parameterMap.get("commit_version"));
    }

    @SchneiderRequestMapping("/mpp/commit/query_data")
    public Response queryMppCommitData(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return componentsService.queryMppCommitData(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping("/mpp/commit/save_source")
    public Response saveMppCommitSource(HttpServletRequest request) {
        super.pageLoad(request);
        return componentsService.saveMppCommitSource(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping("/slice/init_page")
    public Response sliceInitPage(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return componentsService.sliceInitPage(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping("/slice/query_slice_columns")
    public Response querySliceColumns(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return componentsService.querySliceColumns(parameterMap);
    }

    @SchneiderRequestMapping("/slice/download_template")
    public void downloadSliceTemplate(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        componentsService.downloadSliceTemplate(parameterMap, response);
    }

    @SchneiderRequestMapping("/slice/upload_slice")
    public Response uploadSlice(HttpServletRequest request) {
        super.pageLoad(request);
        MultipartHttpServletRequest multipartRequest = WebUtils.getNativeRequest(request, MultipartHttpServletRequest.class);
        if (multipartRequest != null) {
            MultipartFile file = multipartRequest.getFile("file");
            try {
                return componentsService.uploadSlice(session.getUserid(), (String) parameterMap.get("uploadVersion"), file);
            } catch (Exception e) {
                return response.setError(e);
            }
        } else {
            return response.setError(new Exception("upload failed, multipartRequest is null !"));
        }
    }

    @SchneiderRequestMapping("/slice/query_slice_data")
    public Response querySliceData(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return componentsService.querySliceData(parameterMap);
    }

    @SchneiderRequestMapping("/slice/download_slice_data")
    public void downloadSliceData(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        componentsService.downloadSliceData(parameterMap, response);
    }

    @SchneiderRequestMapping("/slice/query_manual_slice_data")
    public Response queryManualSliceData(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return componentsService.queryManualSliceData(parameterMap);
    }

    @SchneiderRequestMapping("/slice/download_manual_slice_data")
    public void downloadManualSliceData(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        componentsService.downloadManualSliceData(parameterMap, response);
    }

    @SchneiderRequestMapping("/slice/save_manual_slice_data")
    public Response saveManualSliceData(HttpServletRequest request) {
        super.pageLoad(request);
        return componentsService.saveManualSliceData(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping("/siop/init_page")
    public Response siopInitPage(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return componentsService.siopInitPage(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping("/siop/query_siop_columns")
    public Response querySiopColumns(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return componentsService.querySiopColumns(parameterMap);
    }

    @SchneiderRequestMapping("/siop/download_template")
    public void downloadSiopTemplate(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        componentsService.downloadSiopTemplate(parameterMap, response);
    }

    @SchneiderRequestMapping("/siop/upload_siop")
    public Response uploadSiop(HttpServletRequest request) {
        super.pageLoad(request);
        MultipartHttpServletRequest multipartRequest = WebUtils.getNativeRequest(request, MultipartHttpServletRequest.class);
        if (multipartRequest != null) {
            MultipartFile file = multipartRequest.getFile("file");
            try {
                return componentsService.uploadSiop(session.getUserid(), (String) parameterMap.get("uploadVersion"), file);
            } catch (Exception e) {
                return response.setError(e);
            }
        } else {
            return response.setError(new Exception("upload failed, multipartRequest is null !"));
        }
    }

    @SchneiderRequestMapping("/siop/query_siop_data")
    public Response querySiopData(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return componentsService.querySiopData(parameterMap);
    }

    @SchneiderRequestMapping("/siop/download_siop_data")
    public void downloadSiopData(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        componentsService.downloadSiopData(parameterMap, response);
    }

    @SchneiderRequestMapping("/siop/save_siop_data")
    public Response saveSiopData(HttpServletRequest request) {
        super.pageLoad(request);
        return componentsService.saveSiopData(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping("/siop/refresh_siop_data")
    public Response refreshSiopData() {
        componentsService.refreshSiopData();
        return response;
    }

    @SchneiderRequestMapping("/material_owner/init_page")
    public Response queryMaterialOwnerInitPage(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return componentsService.queryMaterialOwnerInitPage(parameterMap);
    }

    @SchneiderRequestMapping("/material_owner/query_data")
    public Response queryMaterialOwnerData(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return componentsService.queryMaterialOwnerData(parameterMap);
    }

    @SchneiderRequestMapping("/material_owner/download_data")
    public void downloadMaterialOwnerData(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        componentsService.downloadMaterialOwnerData(parameterMap, response);
    }

    @SchneiderRequestMapping("/material_owner/upload_data")
    public Response materialOwnerUploadData(HttpServletRequest request) {
        super.pageLoad(request);
        MultipartHttpServletRequest multipartRequest = WebUtils.getNativeRequest(request, MultipartHttpServletRequest.class);
        if (multipartRequest != null) {
            MultipartFile file = multipartRequest.getFile("file");
            try {
                return componentsService.materialOwnerUploadData(session.getUserid(), file);
            } catch (Exception e) {
                return response.setError(e);
            }
        } else {
            return response.setError(new Exception("upload failed, multipartRequest is null !"));
        }
    }

    @SchneiderRequestMapping("/material_owner/upload_data_apply_conflict")
    public Response materialOwnerUploadDataApplyConflict(HttpServletRequest request) {
        super.pageLoad(request);
        try {
            return componentsService.materialOwnerUploadDataApplyConflict(session.getUserid(), parameterMap);
        } catch (Exception e) {
            return response.setError(e);
        }
    }

    @SchneiderRequestMapping("/material_owner/query_invalid_data")
    public Response queryMaterialOwnerInvalidData(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return componentsService.queryMaterialOwnerInvalidData(parameterMap);
    }

    @SchneiderRequestMapping("/material_owner/download_invalid_data")
    public void downloadMaterialOwnerInvalidData(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        componentsService.downloadMaterialOwnerInvalidData(parameterMap, response);
    }

    @SchneiderRequestMapping("/material_owner/delete_abnormal_material")
    public Response deleteAbnormalMaterial(HttpServletRequest request) {
        super.pageLoad(request);
        return componentsService.deleteAbnormalMaterial(parameterMap);
    }

    @SchneiderRequestMapping("/consignment_time_limit/query_data")
    public Response queryConsignmentTimeLimitData(HttpServletRequest request) {
        super.pageLoad(request);
        return componentsService.queryConsignmentTimeLimitData(parameterMap);
    }

    @SchneiderRequestMapping("/consignment_time_limit/download_data")
    public void downloadConsignmentTimeLimitData(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        componentsService.downloadConsignmentTimeLimitData(parameterMap, response);
    }

    @SchneiderRequestMapping("/consignment_time_limit/save_data")
    public Response saveConsignmentTimeLimitData(HttpServletRequest request) {
        super.pageLoad(request);
        return componentsService.saveConsignmentTimeLimitData(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping("/consignment_time_limit/download_template")
    public void downloadConsignmentTimeLimitTemplate(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        componentsService.downloadConsignmentTimeLimitTemplate(parameterMap, response);
    }

    @SchneiderRequestMapping("/consignment_time_limit/upload_data")
    public Response uploadConsignmentTimeLimitData(HttpServletRequest request) {
        super.pageLoad(request);
        MultipartHttpServletRequest multipartRequest = WebUtils.getNativeRequest(request, MultipartHttpServletRequest.class);
        if (multipartRequest != null) {
            MultipartFile file = multipartRequest.getFile("file");
            try {
                return componentsService.uploadConsignmentTimeLimitData(session.getUserid(), file);
            } catch (Exception e) {
                return response.setError(e);
            }
        } else {
            return response.setError(new Exception("upload failed, multipartRequest is null !"));
        }
    }

    @SchneiderRequestMapping("/wechat_bot_presetation/query_report")
    public Response queryWechatBotPresetationReport(HttpServletRequest request) {
        super.pageLoad(request);
        return componentsService.queryWechatBotPresetationReport(parameterMap);
    }

    // CLO Target
    @SchneiderRequestMapping("/clo/init_page")
    public Response initCloPage(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return componentsService.initCloPage();
    }

    @SchneiderRequestMapping("/clo/upload_report")
    public Response cloUploadReport(HttpServletRequest request) {
        super.pageLoad(request);
        MultipartHttpServletRequest multipartRequest = WebUtils.getNativeRequest(request, MultipartHttpServletRequest.class);
        if (multipartRequest != null) {
            MultipartFile file = multipartRequest.getFile("file");
            try {
                return componentsService.cloUploadReport(session.getUserid(), file);
            } catch (Exception e) {
                return response.setError(e);
            }
        } else {
            return response.setError(new Exception("upload failed, multipartRequest is null !"));
        }
    }

    @SchneiderRequestMapping("/clo/query_clo_report_data")
    public Response queryCloReportData(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return componentsService.queryCloReportData(parameterMap);
    }

    @SchneiderRequestMapping("/clo/download_clo_template")
    public void downloadCloTemplate(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        componentsService.downloadCloTemplate(parameterMap, response);
    }

    @SchneiderRequestMapping("/clo/download_clo_data")
    public void downloadCloData(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        componentsService.downloadCloData(parameterMap, response);
    }

    @SchneiderRequestMapping("/clo/save_clo_data")
    public Response saveCloData(HttpServletRequest request) {
        super.pageLoad(request);
        return componentsService.saveCloData(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping("/query_markdown_by_sql")
    public Response queryMarkdownBySql(HttpServletRequest request) {
        super.pageLoad(request);
        return componentsService.queryMarkdownBySql(parameterMap);
    }

    // Product Group
    @SchneiderRequestMapping("/product_group/init_page")
    public Response productGroupInitPage(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return componentsService.productGroupInitPage();
    }

    @SchneiderRequestMapping("/product_group/upload_report")
    public Response productGroupUploadReport(HttpServletRequest request) {
        super.pageLoad(request);
        MultipartHttpServletRequest multipartRequest = WebUtils.getNativeRequest(request, MultipartHttpServletRequest.class);
        if (multipartRequest != null) {
            MultipartFile file = multipartRequest.getFile("file");
            try {
                return componentsService.productGroupUpload(session.getUserid(), file);
            } catch (Exception e) {
                return response.setError(e);
            }
        } else {
            return response.setError(new Exception("upload failed, multipartRequest is null !"));
        }
    }

    @SchneiderRequestMapping("/product_group/query_product_group_data")
    public Response queryProductGroupData(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return componentsService.queryProductGroupData(parameterMap);
    }

    @SchneiderRequestMapping("/product_group/download_product_group_template")
    public void downloadProductGroupTemplate(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        componentsService.downloadProductGroupTemplate(parameterMap, response);
    }

    @SchneiderRequestMapping("/product_group/download_product_group_data")
    public void downloadProductGroupData(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        componentsService.downloadProductGroupData(parameterMap, response);
    }

    @SchneiderRequestMapping("/product_group/save_product_group_data")
    public Response saveProductGroupData(HttpServletRequest request) {
        super.pageLoad(request);
        return componentsService.saveProductGroupData(session.getUserid(), parameterMap);
    }
}
