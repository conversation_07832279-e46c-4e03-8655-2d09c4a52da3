package com.scp.customer;

import com.scp.customer.service.IOTDMService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/customer/otdm", parent = "menu860")
public class OTDMController extends ControllerHelper {

    @Resource
    private IOTDMService otdmService;

    @SchneiderRequestMapping("/query_report1_columns")
    public Response queryReport1Columns(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return otdmService.queryReport1Columns(parameterMap);
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return otdmService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping("/query_report1_details")
    public Response queryReport1Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return otdmService.queryReport1Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report1_details")
    public void downloadReport1Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        otdmService.downloadReport1Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return otdmService.queryReport2(parameterMap);
    }

    @SchneiderRequestMapping("/query_otdm_rca_tips_list")
    public Response queryOtdmRcaTipsList(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return otdmService.queryOtdmRcaTipsList(parameterMap);
    }

    @SchneiderRequestMapping("/save_otdm_report1_details")
    public Response saveOtdmReport1Details(HttpServletRequest request) {
        super.pageLoad(request);
        return otdmService.saveOtdmReport1Details(parameterMap);
    }
}
