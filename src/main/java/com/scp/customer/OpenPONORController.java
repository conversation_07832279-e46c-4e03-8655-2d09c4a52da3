package com.scp.customer;

import com.scp.customer.service.IOpenPONORService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/customer/open_po_nor", parent = "menu855")
public class OpenPONORController extends ControllerHelper {
    @Resource
    private Response res;

    @Resource
    private IOpenPONORService openPONORService;

    @SchneiderRequestMapping("/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        return openPONORService.initPage(parameterMap);
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        try {
            return openPONORService.queryReport1(parameterMap);
        } catch (Exception e) {
            return res.setError(e);
        }
    }

    @SchneiderRequestMapping("/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        super.pageLoad(request);
        return openPONORService.queryReport2(parameterMap);
    }

    @SchneiderRequestMapping("/query_report2_details")
    public Response queryReport2Details(HttpServletRequest request) {
        super.pageLoad(request);
        return openPONORService.queryReport2Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report2_details")
    public void downloadReport2Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        openPONORService.downloadReport2Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report3")
    public Response queryReport3(HttpServletRequest request) {
        super.pageLoad(request);
        return openPONORService.queryReport3(parameterMap);
    }

    @SchneiderRequestMapping("/query_report3_details")
    public Response queryReport3Details(HttpServletRequest request) {
        super.pageLoad(request);
        return openPONORService.queryReport3Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report3_details")
    public void downloadReport3Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        openPONORService.downloadReport3Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report4")
    public Response queryReport4(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return openPONORService.queryReport4(parameterMap);
    }

    @SchneiderRequestMapping("/query_report4_details")
    public Response queryReport4Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return openPONORService.queryReport4Details(parameterMap);
    }

    @SchneiderRequestMapping("/query_report4_mo_details")
    public Response queryReport4MODetails(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return openPONORService.queryReport4MODetails(parameterMap);
    }

    @SchneiderRequestMapping("/download_report4")
    public void downloadReport4(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        openPONORService.downloadReport4(parameterMap, response);
    }

    @SchneiderRequestMapping("/download_report4_details")
    public void downloadReport4Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        openPONORService.downloadReport4Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/download_report4_mo_details")
    public void downloadReport4MODetails(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        openPONORService.downloadReport4MODetails(parameterMap, response);
    }
}
