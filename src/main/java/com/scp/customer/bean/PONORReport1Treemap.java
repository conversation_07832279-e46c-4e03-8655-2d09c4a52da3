package com.scp.customer.bean;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.starter.context.bean.SCPRuntimeException;

import java.math.BigDecimal;
import java.util.*;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class PONORReport1Treemap {
    private String name;
    private BigDecimal value;

    private double k = -9999;
    private PONORReport1Tooltips tips = new PONORReport1Tooltips();

    private Map<String, String> itemStyle = null;
    private List<PONORReport1Treemap> children;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public Map<String, String> getItemStyle() {
        return itemStyle;
    }

    public void setItemStyle(Map<String, String> itemStyle) {
        this.itemStyle = itemStyle;
    }

    public List<PONORReport1Treemap> getChildren() {
        if (children == null) {
            children = new ArrayList<>();
        }
        return children;
    }

    public boolean hasChildren() {
        return this.getChildren() != null && !this.getChildren().isEmpty();
    }

    public void setChildren(List<PONORReport1Treemap> children) {
        this.children = children;
    }

    public void setTips(PONORReport1Tooltips tips) {
        this.tips = tips;
    }

    public PONORReport1Tooltips getTips() {
        return tips;
    }

    public double getK() {
        if (k == -9999) {
            return this.tips.getK().doubleValue();
        }
        return k;
    }

    public void setK(double k) {
        this.k = k;
        this.tips.setK(BigDecimal.valueOf(k));
    }

    public void setColor(String color) {
        if (itemStyle == null) {
            itemStyle = new HashMap<>();
        }
        itemStyle.put("color", color);
    }


    // 合并两个节点
    public void add(PONORReport1Treemap addElement) {
        try {
            PONORReport1Treemap mainElement = this;

            mainElement.getTips().add(addElement.getTips()); // 先相加根节点

            // 再相加子节点
            while (addElement.hasChildren()) {
                List<PONORReport1Treemap> mainChildren = mainElement.getChildren();
                PONORReport1Treemap child = addElement.getChildren().get(0); // 加数节点只有一个子节点

                Optional<PONORReport1Treemap> beanOpt = mainChildren.stream().filter(b -> b.getName().equals(child.getName())).findFirst();
                if (beanOpt.isPresent()) {
                    PONORReport1Treemap bean = beanOpt.get();
                    bean.getTips().add(child.getTips()); // 如果找到了, 那就合并两个子节点

                    // 向下移动一层
                    addElement = child;
                    mainElement = bean;
                } else {
                    mainChildren.add(child);// 如果找不到子节点, 那直接把需要相加的节点附在这个子节点下面
                    break; // 然后直接跳出循环, 相加结束
                }
            }
        } catch (Exception e) {
            throw new SCPRuntimeException(e.getMessage());
        }
    }
}
