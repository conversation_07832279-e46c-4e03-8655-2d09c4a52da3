package com.scp.customer.bean;

import org.apache.commons.lang3.StringUtils;

public class BOLUpload {

    private String SALES_ORDER_NUMBER;
    private String SALES_ORDER_ITEM;
    private String BOL_RCA;
    private String COMMENTS;

    public String getSALES_ORDER_NUMBER() {
        return SALES_ORDER_NUMBER;
    }

    public void setSALES_ORDER_NUMBER(String SALES_ORDER_NUMBER) {
        this.SALES_ORDER_NUMBER = StringUtils.trim(SALES_ORDER_NUMBER);
    }

    public String getSALES_ORDER_ITEM() {
        return SALES_ORDER_ITEM;
    }

    public void setSALES_ORDER_ITEM(String SALES_ORDER_ITEM) {
        this.SALES_ORDER_ITEM = StringUtils.trim(SALES_ORDER_ITEM);
    }

    public String getBOL_RCA() {
        return BOL_RCA;
    }

    public void setBOL_RCA(String BOL_RCA) {
        this.BOL_RCA = StringUtils.trim(BOL_RCA);
    }

    public String getCOMMENTS() {
        return COMMENTS;
    }

    public void setCOMMENTS(String COMMENTS) {
        this.COMMENTS = StringUtils.trim(COMMENTS);
    }
}
