package com.scp.customer.bean;

import java.math.BigDecimal;

public class TEXReport6Bean {

    private String XAXIS;
    private String NAME;
    private BigDecimal VALUE;

    public String getKey() {
        return NAME + '#' + XAXIS;
    }

    public String getXAXIS() {
        return XAXIS;
    }

    public void setXAXIS(String XAXIS) {
        this.XAXIS = XAXIS;
    }

    public String getNAME() {
        return NAME;
    }

    public void setNAME(String NAME) {
        this.NAME = NAME;
    }

    public BigDecimal getVALUE() {
        return VALUE;
    }

    public void setVALUE(BigDecimal VALUE) {
        this.VALUE = VALUE;
    }
}
