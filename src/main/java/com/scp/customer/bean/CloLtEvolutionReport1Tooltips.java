package com.scp.customer.bean;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.starter.context.bean.SCPRuntimeException;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class CloLtEvolutionReport1Tooltips {
    private BigDecimal AVG_VALUE;
    private BigDecimal AVG_LINES;

    public BigDecimal getAVG_VALUE() {
        return AVG_VALUE;
    }

    public void setAVG_VALUE(BigDecimal AVG_VALUE) {
        this.AVG_VALUE = AVG_VALUE;
    }

    public BigDecimal getAVG_LINES() {
        return AVG_LINES;
    }

    public void setAVG_LINES(BigDecimal AVG_LINES) {
        this.AVG_LINES = AVG_LINES;
    }

    private static final Class<CloLtEvolutionReport1Tooltips> clazz;
    private static final List<String> fields;

    static {
        clazz = CloLtEvolutionReport1Tooltips.class;
        fields = Arrays.stream(clazz.getDeclaredFields()).filter(f -> f.getAnnotatedType().getType().getTypeName().equalsIgnoreCase("java.math.BigDecimal")).map(Field::getName).collect(Collectors.toList());
    }

    public CloLtEvolutionReport1Tooltips copyOf(CloLtEvolutionReport1Tooltips tooltips) {
        try {
            for (String f : fields) {
                Method method = clazz.getMethod("get" + f);
                Object value = method.invoke(tooltips);
                if (value != null) {
                    clazz.getMethod("set" + f, BigDecimal.class).invoke(this, (BigDecimal) value);
                }
            }
            return this;
        } catch (Exception e) {
            throw new SCPRuntimeException(e.getMessage());
        }

    }

    public void add(CloLtEvolutionReport1Tooltips tips) throws Exception {
        for (String f : fields) {
            Method method = clazz.getMethod("get" + f);
            Object value = method.invoke(tips);
            if (value != null) {
                BigDecimal val = BigDecimal.ZERO;
                Object valueOrg = method.invoke(this);
                if (valueOrg != null) {
                    val = (BigDecimal) valueOrg;
                }
                clazz.getMethod("set" + f, BigDecimal.class).invoke(this, val.add((BigDecimal) value));
            }
        }
    }
}
