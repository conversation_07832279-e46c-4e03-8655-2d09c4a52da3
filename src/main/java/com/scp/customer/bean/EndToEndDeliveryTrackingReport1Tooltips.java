package com.scp.customer.bean;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.starter.context.bean.SCPRuntimeException;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class EndToEndDeliveryTrackingReport1Tooltips {

    private BigDecimal COUNT;

    public BigDecimal getCOUNT() {
        return COUNT;
    }

    public void setCOUNT(BigDecimal COUNT) {
        this.COUNT = COUNT;
    }

    private static final Class<EndToEndDeliveryTrackingReport1Tooltips> clazz;
    private static final List<String> fields;

    static {
        clazz = EndToEndDeliveryTrackingReport1Tooltips.class;
        fields = Arrays.stream(clazz.getDeclaredFields()).filter(f -> f.getAnnotatedType().getType().getTypeName().equalsIgnoreCase("java.math.BigDecimal")).map(Field::getName).collect(Collectors.toList());
    }

    public EndToEndDeliveryTrackingReport1Tooltips copyOf(EndToEndDeliveryTrackingReport1Tooltips tooltips) {
        try {
            for (String f : fields) {
                Method method = clazz.getMethod("get" + f);
                Object value = method.invoke(tooltips);
                if (value != null) {
                    clazz.getMethod("set" + f, BigDecimal.class).invoke(this, (BigDecimal) value);
                }
            }
            return this;
        } catch (Exception e) {
            throw new SCPRuntimeException(e.getMessage());
        }

    }

    public void add(EndToEndDeliveryTrackingReport1Tooltips tips) throws Exception {
        for (String f : fields) {
            Method method = clazz.getMethod("get" + f);
            Object value = method.invoke(tips);
            if (value != null) {
                BigDecimal val = BigDecimal.ZERO;
                Object valueOrg = method.invoke(this);
                if (valueOrg != null) {
                    val = (BigDecimal) valueOrg;
                }
                clazz.getMethod("set" + f, BigDecimal.class).invoke(this, val.add((BigDecimal) value));
            }
        }
    }
}
