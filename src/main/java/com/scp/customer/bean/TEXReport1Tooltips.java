package com.scp.customer.bean;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.starter.context.bean.SCPRuntimeException;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class TEXReport1Tooltips {
    private BigDecimal OVERALL_CYCLE_TIME;
    private BigDecimal OVERALL_CYCLE_TIME_CLOSED;

    public BigDecimal getOVERALL_CYCLE_TIME() {
        return OVERALL_CYCLE_TIME;
    }
    public void setOVERALL_CYCLE_TIME(BigDecimal OVERALL_CYCLE_TIME) {
        this.OVERALL_CYCLE_TIME = OVERALL_CYCLE_TIME;
    }

    public BigDecimal getOVERALL_CYCLE_TIME_CLOSED() {
        return OVERALL_CYCLE_TIME_CLOSED;
    }
    public void setOVERALL_CYCLE_TIME_CLOSED(BigDecimal OVERALL_CYCLE_TIME_CLOSED) {
        this.OVERALL_CYCLE_TIME_CLOSED = OVERALL_CYCLE_TIME_CLOSED;
    }

    private static final Class<TEXReport1Tooltips> clazz;
    private static final List<String> fields;

    static {
        clazz = TEXReport1Tooltips.class;
        fields = Arrays.stream(clazz.getDeclaredFields()).filter(f -> f.getAnnotatedType().getType().getTypeName().equalsIgnoreCase("java.math.BigDecimal")).map(Field::getName).collect(Collectors.toList());
    }

    public TEXReport1Tooltips copyOf(TEXReport1Tooltips tooltips) {
        try {
            for (String f : fields) {
                Method method = clazz.getMethod("get" + f);
                Object value = method.invoke(tooltips);
                if (value != null) {
                    clazz.getMethod("set" + f, BigDecimal.class).invoke(this, (BigDecimal) value);
                }
            }
            return this;
        } catch (Exception e) {
            throw new SCPRuntimeException(e.getMessage());
        }

    }

    public void add(TEXReport1Tooltips tips) throws Exception {
        for (String f : fields) {
            Method method = clazz.getMethod("get" + f);
            Object value = method.invoke(tips);
            if (value != null) {
                BigDecimal val = BigDecimal.ZERO;
                Object valueOrg = method.invoke(this);
                if (valueOrg != null) {
                    val = (BigDecimal) valueOrg;
                }
                clazz.getMethod("set" + f, BigDecimal.class).invoke(this, val.add((BigDecimal) value));
            }
        }
    }
}
