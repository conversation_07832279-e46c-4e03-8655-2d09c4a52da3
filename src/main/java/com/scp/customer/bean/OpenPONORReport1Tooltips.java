package com.scp.customer.bean;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class OpenPONORReport1Tooltips {
    private BigDecimal AVG_SELLING_PRICE_RMB;

    private static final Class<OpenPONORReport1Tooltips> clazz;
    private static final List<String> fields;

    static {
        clazz = OpenPONORReport1Tooltips.class;
        fields = Arrays.stream(clazz.getDeclaredFields()).filter(f -> f.getAnnotatedType().getType().getTypeName().equalsIgnoreCase("java.math.BigDecimal")).map(Field::getName).collect(Collectors.toList());
    }

    public OpenPONORReport1Tooltips copyOf(OpenPONORReport1Tooltips tooltips) throws Exception {
        for (String f : fields) {
            Method method = clazz.getMethod("get" + f);
            Object value = method.invoke(tooltips);
            if (value != null) {
                clazz.getMethod("set" + f, BigDecimal.class).invoke(this, (BigDecimal) value);
            }
        }
        return this;
    }

    public BigDecimal getAVG_SELLING_PRICE_RMB() {
        return AVG_SELLING_PRICE_RMB;
    }

    public void setAVG_SELLING_PRICE_RMB(BigDecimal AVG_SELLING_PRICE_RMB) {
        this.AVG_SELLING_PRICE_RMB = AVG_SELLING_PRICE_RMB;
    }

    public void add(OpenPONORReport1Tooltips tips) throws Exception {
        for (String f : fields) {
            Method method = clazz.getMethod("get" + f);
            Object value = method.invoke(tips);
            if (value != null) {
                BigDecimal val = BigDecimal.ZERO;
                Object valueOrg = method.invoke(this);
                if (valueOrg != null) {
                    val = (BigDecimal) valueOrg;
                }
                clazz.getMethod("set" + f, BigDecimal.class).invoke(this, val.add((BigDecimal) value));
            }
        }
    }
}
