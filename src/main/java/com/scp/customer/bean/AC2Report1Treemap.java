package com.scp.customer.bean;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.starter.context.bean.SCPRuntimeException;

import java.math.BigDecimal;
import java.util.*;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class AC2Report1Treemap {
    private String name;
    private BigDecimal value;

    private double k = -9999;
    private AC2Report1Tooltips tips = new AC2Report1Tooltips();

    private Map<String, String> itemStyle = null;
    private List<AC2Report1Treemap> children;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public Map<String, String> getItemStyle() {
        return itemStyle;
    }

    public void setItemStyle(Map<String, String> itemStyle) {
        this.itemStyle = itemStyle;
    }

    public List<AC2Report1Treemap> getChildren() {
        if (children == null) {
            children = new ArrayList<>();
        }
        return children;
    }

    public boolean hasChildren() {
        return this.getChildren() != null && !this.getChildren().isEmpty();
    }

    public void setChildren(List<AC2Report1Treemap> children) {
        this.children = children;
    }

    public void setTips(AC2Report1Tooltips tips) {
        this.tips = tips;
    }

    public AC2Report1Tooltips getTips() {
        return tips;
    }
    public double getK() {
        if (k == -9999) {
            return this.tips.getK().doubleValue();
        }
        return k;
    }

    public void setK(double k) {
        this.k = k;
        this.tips.setK(BigDecimal.valueOf(k));
    }

    public void setColor(String color) {
        if (itemStyle == null) {
            itemStyle = new HashMap<>();
        }
        itemStyle.put("color", color);
    }


    // 合并两个节点
    public void add(AC2Report1Treemap addElement) {
        try {
            AC2Report1Treemap mainElement = this;

            mainElement.getTips().add(addElement.getTips()); // 先相加根节点

            // 再相加子节点
            while (addElement.hasChildren()) {
                List<AC2Report1Treemap> mainChildren = mainElement.getChildren();
                AC2Report1Treemap child = addElement.getChildren().get(0); // 加数节点只有一个子节点

                Optional<AC2Report1Treemap> beanOpt = mainChildren.stream().filter(b -> b.getName().equals(child.getName())).findFirst();
                if (beanOpt.isPresent()) {
                    AC2Report1Treemap bean = beanOpt.get();
                    bean.getTips().add(child.getTips()); // 如果找到了, 那就合并两个子节点

                    // 向下移动一层
                    addElement = child;
                    mainElement = bean;
                } else {
                    mainChildren.add(child);// 如果找不到子节点, 那直接把需要相加的节点附在这个子节点下面
                    break; // 然后直接跳出循环, 相加结束
                }
            }
        }catch (Exception e){
            throw new SCPRuntimeException(e.getMessage());
        }
    }
}
