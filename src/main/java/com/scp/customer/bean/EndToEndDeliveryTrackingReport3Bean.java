package com.scp.customer.bean;

import java.math.BigDecimal;

public class EndToEndDeliveryTrackingReport3Bean {

    private String CALENDAR;
    private String NAME;
    private BigDecimal VALUE;

    public String getKey() {
        return NAME + '#' + CALENDAR;
    }

    public String getCALENDAR() {
        return CALENDAR;
    }

    public void setCALENDAR(String CALENDAR) {
        this.CALENDAR = CALENDAR;
    }

    public String getNAME() {
        return NAME;
    }

    public void setNAME(String NAME) {
        this.NAME = NAME;
    }

    public BigDecimal getVALUE() {
        return VALUE;
    }

    public void setVALUE(BigDecimal VALUE) {
        this.VALUE = VALUE;
    }
}
