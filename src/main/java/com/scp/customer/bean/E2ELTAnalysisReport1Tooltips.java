package com.scp.customer.bean;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.starter.context.bean.SCPRuntimeException;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class E2ELTAnalysisReport1Tooltips {

    private BigDecimal LINES;
    private BigDecimal NUM_OF_RECORDS;
    private BigDecimal QUANTITY;
    private BigDecimal NET_VALUE;
    private BigDecimal CUSTOMER_REQUESTED_LT;
    private BigDecimal LOGISTICS_OFFER_LT;
    private BigDecimal FIRST_CONFIRMED_LT;
    private BigDecimal ACTUAL_LT;
    private BigDecimal CONTRACTUAL_LT;

    public BigDecimal getLINES() {
        return LINES;
    }

    public void setLINES(BigDecimal LINES) {
        this.LINES = LINES;
    }

    public BigDecimal getNUM_OF_RECORDS() {
        return NUM_OF_RECORDS;
    }

    public void setNUM_OF_RECORDS(BigDecimal NUM_OF_RECORDS) {
        this.NUM_OF_RECORDS = NUM_OF_RECORDS;
    }

    public BigDecimal getQUANTITY() {
        return QUANTITY;
    }

    public void setQUANTITY(BigDecimal QUANTITY) {
        this.QUANTITY = QUANTITY;
    }

    public BigDecimal getNET_VALUE() {
        return NET_VALUE;
    }

    public void setNET_VALUE(BigDecimal NET_VALUE) {
        this.NET_VALUE = NET_VALUE;
    }

    public BigDecimal getCUSTOMER_REQUESTED_LT() {
        return CUSTOMER_REQUESTED_LT;
    }

    public void setCUSTOMER_REQUESTED_LT(BigDecimal CUSTOMER_REQUESTED_LT) {
        this.CUSTOMER_REQUESTED_LT = CUSTOMER_REQUESTED_LT;
    }

    public BigDecimal getLOGISTICS_OFFER_LT() {
        return LOGISTICS_OFFER_LT;
    }

    public void setLOGISTICS_OFFER_LT(BigDecimal LOGISTICS_OFFER_LT) {
        this.LOGISTICS_OFFER_LT = LOGISTICS_OFFER_LT;
    }

    public BigDecimal getFIRST_CONFIRMED_LT() {
        return FIRST_CONFIRMED_LT;
    }

    public void setFIRST_CONFIRMED_LT(BigDecimal FIRST_CONFIRMED_LT) {
        this.FIRST_CONFIRMED_LT = FIRST_CONFIRMED_LT;
    }

    public BigDecimal getACTUAL_LT() {
        return ACTUAL_LT;
    }

    public void setACTUAL_LT(BigDecimal ACTUAL_LT) {
        this.ACTUAL_LT = ACTUAL_LT;
    }

    public BigDecimal getCONTRACTUAL_LT() {
        return CONTRACTUAL_LT;
    }

    public void setCONTRACTUAL_LT(BigDecimal CONTRACTUAL_LT) {
        this.CONTRACTUAL_LT = CONTRACTUAL_LT;
    }

    private static final Class<E2ELTAnalysisReport1Tooltips> clazz;
    private static final List<String> fields;

    static {
        clazz = E2ELTAnalysisReport1Tooltips.class;
        fields = Arrays.stream(clazz.getDeclaredFields()).filter(f -> f.getAnnotatedType().getType().getTypeName().equalsIgnoreCase("java.math.BigDecimal")).map(Field::getName).collect(Collectors.toList());
    }

    public E2ELTAnalysisReport1Tooltips copyOf(E2ELTAnalysisReport1Tooltips tooltips) {
        try {
            for (String f : fields) {
                Method method = clazz.getMethod("get" + f);
                Object value = method.invoke(tooltips);
                if (value != null) {
                    clazz.getMethod("set" + f, BigDecimal.class).invoke(this, (BigDecimal) value);
                }
            }
            return this;
        } catch (Exception e) {
            throw new SCPRuntimeException(e.getMessage());
        }

    }

    public void add(E2ELTAnalysisReport1Tooltips tips) throws Exception {
        for (String f : fields) {
            Method method = clazz.getMethod("get" + f);
            Object value = method.invoke(tips);
            if (value != null) {
                BigDecimal val = BigDecimal.ZERO;
                Object valueOrg = method.invoke(this);
                if (valueOrg != null) {
                    val = (BigDecimal) valueOrg;
                }
                clazz.getMethod("set" + f, BigDecimal.class).invoke(this, val.add((BigDecimal) value));
            }
        }
    }
}
