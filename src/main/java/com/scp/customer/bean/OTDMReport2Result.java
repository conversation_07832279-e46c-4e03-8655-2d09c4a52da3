package com.scp.customer.bean;

import com.starter.utils.Utils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class OTDMReport2Result {
    List<String> xAxis = new ArrayList<>();
    List<BigDecimal> yAxis1 = new ArrayList<>();
    List<BigDecimal> yAxis2 = new ArrayList<>();
    List<BigDecimal> yAxis3 = new ArrayList<>();
    List<BigDecimal> yAxis4 = new ArrayList<>();
    List<BigDecimal> yAxis5 = new ArrayList<>();
    List<BigDecimal> yAxis6 = new ArrayList<>();

    public void setYAxis(Map<String, BigDecimal> map) {
        if (map == null) {
            yAxis1.add(null);
            yAxis2.add(null);
            yAxis3.add(null);
            yAxis4.add(null);
            yAxis5.add(null);
            yAxis6.add(null);
        } else {
            yAxis1.add(Utils.parseBigDecimal(map.get("ON_TIME_RATIO"), null));
            yAxis2.add(Utils.parseBigDecimal(map.get("ON_TIME"), null));
            yAxis3.add(Utils.parseBigDecimal(map.get("<=7"), null));
            yAxis4.add(Utils.parseBigDecimal(map.get("8~15"), null));
            yAxis5.add(Utils.parseBigDecimal(map.get("16~30"), null));
            yAxis6.add(Utils.parseBigDecimal(map.get(">30"), null));
        }
    }


    public List<String> getxAxis() {
        return xAxis;
    }

    public void setxAxis(List<String> xAxis) {
        this.xAxis = xAxis;
    }

    public List<BigDecimal> getyAxis1() {
        return yAxis1;
    }

    public void setyAxis1(List<BigDecimal> yAxis1) {
        this.yAxis1 = yAxis1;
    }

    public List<BigDecimal> getyAxis2() {
        return yAxis2;
    }

    public void setyAxis2(List<BigDecimal> yAxis2) {
        this.yAxis2 = yAxis2;
    }

    public List<BigDecimal> getyAxis3() {
        return yAxis3;
    }

    public void setyAxis3(List<BigDecimal> yAxis3) {
        this.yAxis3 = yAxis3;
    }

    public List<BigDecimal> getyAxis4() {
        return yAxis4;
    }

    public void setyAxis4(List<BigDecimal> yAxis4) {
        this.yAxis4 = yAxis4;
    }

    public List<BigDecimal> getyAxis5() {
        return yAxis5;
    }

    public void setyAxis5(List<BigDecimal> yAxis5) {
        this.yAxis5 = yAxis5;
    }

    public List<BigDecimal> getyAxis6() {
        return yAxis6;
    }

    public void setyAxis6(List<BigDecimal> yAxis6) {
        this.yAxis6 = yAxis6;
    }
}
