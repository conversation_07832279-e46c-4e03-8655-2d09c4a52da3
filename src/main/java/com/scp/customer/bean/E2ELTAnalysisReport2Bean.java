package com.scp.customer.bean;

import java.math.BigDecimal;

public class E2ELTAnalysisReport2Bean {

    private String CALENDAR_DATE;
    private String NAME;
    private BigDecimal VALUE;
    private BigDecimal CUSTOMER_REQUESTED_LT;
    private BigDecimal LOGISTICS_OFFER_LT;
    private BigDecimal FIRST_CONFIRMED_LT;
    private BigDecimal ACTUAL_LT;
    private BigDecimal CONTRACTUAL_LT;

    public BigDecimal getCUSTOMER_REQUESTED_LT() {
        return CUSTOMER_REQUESTED_LT;
    }

    public void setCUSTOMER_REQUESTED_LT(BigDecimal CUSTOMER_REQUESTED_LT) {
        this.CUSTOMER_REQUESTED_LT = CUSTOMER_REQUESTED_LT;
    }

    public BigDecimal getLOGISTICS_OFFER_LT() {
        return LOGISTICS_OFFER_LT;
    }

    public void setLOGISTICS_OFFER_LT(BigDecimal LOGISTICS_OFFER_LT) {
        this.LOGISTICS_OFFER_LT = LOGISTICS_OFFER_LT;
    }

    public BigDecimal getFIRST_CONFIRMED_LT() {
        return FIRST_CONFIRMED_LT;
    }

    public void setFIRST_CONFIRMED_LT(BigDecimal FIRST_CONFIRMED_LT) {
        this.FIRST_CONFIRMED_LT = FIRST_CONFIRMED_LT;
    }


    public BigDecimal getACTUAL_LT() {
        return ACTUAL_LT;
    }

    public void setACTUAL_LT(BigDecimal ACTUAL_LT) {
        this.ACTUAL_LT = ACTUAL_LT;
    }

    public BigDecimal getCONTRACTUAL_LT() {
        return CONTRACTUAL_LT;
    }

    public void setCONTRACTUAL_LT(BigDecimal CONTRACTUAL_LT) {
        this.CONTRACTUAL_LT = CONTRACTUAL_LT;
    }

    public String getKey() {
        return NAME + '#' + CALENDAR_DATE;
    }

    public String getCALENDAR_DATE() {
        return CALENDAR_DATE;
    }

    public void setCALENDAR_DATE(String CALENDAR_DATE) {
        this.CALENDAR_DATE = CALENDAR_DATE;
    }

    public String getNAME() {
        return NAME;
    }

    public void setNAME(String NAME) {
        this.NAME = NAME;
    }

    public BigDecimal getVALUE() {
        return VALUE;
    }

    public void setVALUE(BigDecimal VALUE) {
        this.VALUE = VALUE;
    }
}
