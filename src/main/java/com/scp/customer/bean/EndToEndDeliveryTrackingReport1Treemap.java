package com.scp.customer.bean;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.starter.context.bean.SCPRuntimeException;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class EndToEndDeliveryTrackingReport1Treemap {
    private String name;
    private BigDecimal value;
    private EndToEndDeliveryTrackingReport1Tooltips tips = new EndToEndDeliveryTrackingReport1Tooltips();
    private List<EndToEndDeliveryTrackingReport1Treemap> children;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public List<EndToEndDeliveryTrackingReport1Treemap> getChildren() {
        if (children == null) {
            children = new ArrayList<>();
        }
        return children;
    }

    public boolean hasChildren() {
        return this.getChildren() != null && !this.getChildren().isEmpty();
    }

    public void setChildren(List<EndToEndDeliveryTrackingReport1Treemap> children) {
        this.children = children;
    }

    public void setTips(EndToEndDeliveryTrackingReport1Tooltips tips) {
        this.tips = tips;
    }

    public EndToEndDeliveryTrackingReport1Tooltips getTips() {
        return tips;
    }

    // 合并两个节点
    public void add(EndToEndDeliveryTrackingReport1Treemap addElement) {
        try {
            EndToEndDeliveryTrackingReport1Treemap mainElement = this;

            mainElement.getTips().add(addElement.getTips()); // 先相加根节点

            // 再相加子节点
            while (addElement.hasChildren()) {
                List<EndToEndDeliveryTrackingReport1Treemap> mainChildren = mainElement.getChildren();
                EndToEndDeliveryTrackingReport1Treemap child = addElement.getChildren().get(0); // 加数节点只有一个子节点

                Optional<EndToEndDeliveryTrackingReport1Treemap> beanOpt = mainChildren.stream().filter(b -> b.getName().equals(child.getName())).findFirst();
                if (beanOpt.isPresent()) {
                    EndToEndDeliveryTrackingReport1Treemap bean = beanOpt.get();
                    bean.getTips().add(child.getTips()); // 如果找到了, 那就合并两个子节点

                    // 向下移动一层
                    addElement = child;
                    mainElement = bean;
                } else {
                    mainChildren.add(child);// 如果找不到子节点, 那直接把需要相加的节点附在这个子节点下面
                    break; // 然后直接跳出循环, 相加结束
                }
            }
        }catch (Exception e){
            throw new SCPRuntimeException(e.getMessage());
        }
    }
}
