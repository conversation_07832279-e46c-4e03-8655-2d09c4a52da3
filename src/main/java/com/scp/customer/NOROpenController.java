package com.scp.customer;

import com.scp.customer.service.INOROpenService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/customer/nor_open", parent = "menu870")
public class NOROpenController extends ControllerHelper {

    @Resource
    private INOROpenService norOpenService;

    @SchneiderRequestMapping("/query_cascader")
    public Response queryNorOpenCascader(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return norOpenService.queryNorOpenCascader(parameterMap);
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return norOpenService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping("/query_report1_details")
    public Response queryReport1Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return norOpenService.queryReport1Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report1")
    public void downloadReport1(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        norOpenService.downloadReport1(parameterMap, response);
    }

    @SchneiderRequestMapping("/download_report1_details")
    public void downloadReport1Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        norOpenService.downloadReport1Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return norOpenService.queryReport2(parameterMap);
    }

    @SchneiderRequestMapping("/query_report3")
    public Response queryReport3(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return norOpenService.queryReport3(parameterMap);
    }

    @SchneiderRequestMapping("/query_report3_ratios_columns")
    public Response queryReport3RatiosColumns(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return norOpenService.queryReport3RatiosColumns(parameterMap);
    }

    @SchneiderRequestMapping("/query_report3_ratios")
    public Response queryReport3Ratios(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return norOpenService.queryReport3Ratios(parameterMap);
    }

    @SchneiderRequestMapping("/query_report4")
    public Response queryReport4(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return norOpenService.queryReport4(parameterMap);
    }

    @SchneiderRequestMapping("/query_report4_details")
    public Response queryReport4Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return norOpenService.queryReport4Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report4")
    public void downloadReport4(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        norOpenService.downloadReport4(parameterMap, response);
    }

    @SchneiderRequestMapping("/download_report4_details")
    public void downloadReport4Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        norOpenService.downloadReport4Details(parameterMap, response);
    }

}
