package com.scp.customer;

import com.scp.customer.service.ICloStructureService;
import com.scp.customer.service.impl.CloStructureServiceImpl;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/customer/clo_structure", parent = CloStructureServiceImpl.PARENT_CODE)
public class CloStructureController extends ControllerHelper {
    @Resource
    private ICloStructureService cloStructureService;

    @SchneiderRequestMapping("/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return cloStructureService.initPage(parameterMap);
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return cloStructureService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping("/download_report1")
    public void downloadReport1(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        cloStructureService.downloadReport1(parameterMap, response);
    }

    @SchneiderRequestMapping("/save_report1_comments")
    public Response saveReport1Comments(HttpServletRequest request) {
        super.pageLoad(request);
        return cloStructureService.saveReport1Comments(parameterMap);
    }

    @SchneiderRequestMapping("/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return cloStructureService.queryReport2(parameterMap);
    }

    @SchneiderRequestMapping("/query_report2_sub")
    public Response queryReport2Sub(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return cloStructureService.queryReport2Sub(parameterMap);
    }

    @SchneiderRequestMapping("/query_report2_details")
    public Response queryReport2Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return cloStructureService.queryReport2Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report2_details")
    public void downloadReport2Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        cloStructureService.downloadReport2Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report3")
    public Response queryReport3(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return cloStructureService.queryReport3(parameterMap);
    }

    @SchneiderRequestMapping("/query_report3_details")
    public Response queryReport3Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return cloStructureService.queryReport3Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report3_details")
    public void downloadReport3Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        cloStructureService.downloadReport3Details(parameterMap, response);
    }
}
