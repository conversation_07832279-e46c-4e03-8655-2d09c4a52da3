package com.scp.customer;

import com.scp.customer.service.impl.PONORServiceImpl;
import com.starter.context.bean.Response;
import jakarta.annotation.Resource;
import com.scp.customer.service.IPONORService;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "customer/po_nor", parent = PONORServiceImpl.PARENT_CODE)
public class PONORController extends ControllerHelper {

    @Resource
    private IPONORService poNorService;

    @SchneiderRequestMapping("/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return poNorService.initPage(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.setGlobalCache(true);
        super.pageLoad(request);
        return poNorService.queryReport1(parameterMap);
    }


    @SchneiderRequestMapping("/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        super.setGlobalCache(true);
        super.pageLoad(request);
        return poNorService.queryReport2(parameterMap);
    }

    @SchneiderRequestMapping("/query_report2_details")
    public Response queryReport2Details(HttpServletRequest request) {
        super.setGlobalCache(true);
        super.pageLoad(request);
        return poNorService.queryReport2Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report2_details")
    public void downloadReport2Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        poNorService.downloadReport2Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report3_columns")
    public Response queryReport3Columns(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return poNorService.queryReport3Columns(parameterMap);
    }

    @SchneiderRequestMapping("/query_report3")
    public Response queryReport3(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return poNorService.queryReport3(parameterMap);
    }

    @SchneiderRequestMapping("/query_report3_details")
    public Response queryReport3Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return poNorService.queryReport3Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report3_details")
    public void downloadReport3Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        poNorService.downloadReport3Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report3_mo_details")
    public Response queryReport3MoDetails(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return poNorService.queryReport3MoDetails(parameterMap);
    }

    @SchneiderRequestMapping("/download_report3_mo_details")
    public void downloadReport3MoDetails(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        poNorService.downloadReport3MoDetails(parameterMap, response);
    }

    @SchneiderRequestMapping("/save_report3_details")
    public Response saveReport3Details(HttpServletRequest request) {
        super.pageLoad(request);
        return poNorService.saveReport3Details(parameterMap);
    }

}
