package com.scp.customer;

import com.scp.customer.service.IE2ELTAnalysisService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/customer/e2e_lt_analysis", parent = E2ELTAnalysisController.PARENT_CODE)
public class E2ELTAnalysisController extends ControllerHelper {

    public static final String PARENT_CODE = "menu896";

    @Resource
    private IE2ELTAnalysisService e2eLTAnalysisService;

    @SchneiderRequestMapping("/query_filters")
    public Response queryFilters(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return e2eLTAnalysisService.queryFilters(parameterMap);
    }

    /*
    * E2E LT Overview Start
    */

    @SchneiderRequestMapping("/overview/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.setGlobalCache(true);
        super.pageLoad(request);
        return e2eLTAnalysisService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping("/overview/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        super.setGlobalCache(true);
        super.pageLoad(request);
        return e2eLTAnalysisService.queryReport2(parameterMap);
    }

    @SchneiderRequestMapping("/overview/query_report2_details")
    public Response queryReport2Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return e2eLTAnalysisService.queryReport2Details(parameterMap);
    }

    @SchneiderRequestMapping("/overview/download_report2_details")
    public void downloadReport2Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        e2eLTAnalysisService.downloadReport2Details(parameterMap, response);
    }


    @SchneiderRequestMapping("/overview/query_report3")
    public Response queryReport3(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return e2eLTAnalysisService.queryReport3(parameterMap);
    }

    @SchneiderRequestMapping("/overview/download_report3")
    public void downloadReport3(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        e2eLTAnalysisService.downloadReport3(parameterMap, response);
    }

    @SchneiderRequestMapping("/overview/query_report3_details")
    public Response queryReport3Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return e2eLTAnalysisService.queryReport3Details(parameterMap);
    }

    @SchneiderRequestMapping("/overview/download_report3_details")
    public void downloadReport3Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        e2eLTAnalysisService.downloadReport3Details(parameterMap, response);
    }

    /*
     * E2E LT Overview End
     */

    /*
     * E2E LT Analysis Start
     */

    @SchneiderRequestMapping("/analysis/init_page")
    public Response initPage(HttpServletRequest request) {
        super.setGlobalCache(true);
        super.pageLoad(request);
        return e2eLTAnalysisService.initAnalysisPage(parameterMap);
    }

    @SchneiderRequestMapping("/analysis/query_report1")
    public Response queryAnalysisReport1(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return e2eLTAnalysisService.queryAnalysisReport1(parameterMap);
    }

    @SchneiderRequestMapping("/analysis/query_report1_sub")
    public Response queryAnalysisReport1Sub(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return e2eLTAnalysisService.queryAnalysisReport1Sub(parameterMap);
    }

    @SchneiderRequestMapping("/analysis/query_report1_details")
    public Response queryAnalysisReport1Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return e2eLTAnalysisService.queryAnalysisReport1Details(parameterMap);
    }

    @SchneiderRequestMapping("/analysis/download_report1_details")
    public void downloadAnalysisReport1Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        e2eLTAnalysisService.downloadAnalysisReport1Details(parameterMap, response);
    }



    @SchneiderRequestMapping("/analysis/query_report2")
    public Response queryAnalysisReport2(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return e2eLTAnalysisService.queryAnalysisReport2(parameterMap);
    }

    @SchneiderRequestMapping("/analysis/query_report2_details")
    public Response queryAnalysisReport2Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return e2eLTAnalysisService.queryAnalysisReport2Details(parameterMap);
    }

    @SchneiderRequestMapping("/analysis/download_report2_details")
    public void downloadAnalysisReport2Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        e2eLTAnalysisService.downloadAnalysisReport2Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/analysis/query_report3")
    public Response queryAnalysisReport3(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return e2eLTAnalysisService.queryAnalysisReport3(parameterMap);
    }

    @SchneiderRequestMapping("/analysis/query_report3_columns")
    public Response queryAnalysisReport3Columns(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return e2eLTAnalysisService.queryAnalysisReport3Columns(parameterMap);
    }

    @SchneiderRequestMapping("/analysis/download_report3")
    public void downloadAnalysisReport3(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        e2eLTAnalysisService.downloadAnalysisReport3(parameterMap, response);
    }

    @SchneiderRequestMapping("/analysis/query_report3_details")
    public Response queryAnalysisReport3Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return e2eLTAnalysisService.queryAnalysisReport3Details(parameterMap);
    }

    @SchneiderRequestMapping("/analysis/download_report3_details")
    public void downloadAnalysisReport3Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        e2eLTAnalysisService.downloadAnalysisReport3Details(parameterMap, response);
    }
    /*
     * E2E LT Analysis end
     */
}
