package com.scp.customer;

import com.scp.customer.service.IEndToEndDeliveryTrackingService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/customer/end_to_end_delivery_tracking", parent = EndToEndDeliveryTrackingController.PARENT_CODE)
public class EndToEndDeliveryTrackingController extends ControllerHelper {

    public static final String PARENT_CODE = "menu891";

    @Resource
    private IEndToEndDeliveryTrackingService endToEndDeliveryTrackingService;

    @SchneiderRequestMapping("/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return endToEndDeliveryTrackingService.initPage();
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.setGlobalCache(true);
        super.pageLoad(request);
        return endToEndDeliveryTrackingService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping("/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        super.setGlobalCache(true);
        super.pageLoad(request);
        return endToEndDeliveryTrackingService.queryReport2(parameterMap);
    }

    @SchneiderRequestMapping("/query_report3")
    public Response queryReport3(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return endToEndDeliveryTrackingService.queryReport3(parameterMap);
    }

    @SchneiderRequestMapping("/query_report3_details")
    public Response queryReport3Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return endToEndDeliveryTrackingService.queryReport3Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report3_details")
    public void downloadReport3Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        endToEndDeliveryTrackingService.downloadReport3Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report4")
    public Response queryReport4(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return endToEndDeliveryTrackingService.queryReport4(parameterMap);
    }

    @SchneiderRequestMapping("/download_report4")
    public void downloadReport4(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        endToEndDeliveryTrackingService.downloadReport4(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report4_details")
    public Response queryReport4Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return endToEndDeliveryTrackingService.queryReport4Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report4_details")
    public void downloadReport4Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        endToEndDeliveryTrackingService.downloadReport4Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report5")
    public Response queryReport5(HttpServletRequest request) {
        super.setGlobalCache(true);
        super.pageLoad(request);
        return endToEndDeliveryTrackingService.queryReport5(parameterMap);
    }

    @SchneiderRequestMapping("/query_report5_details")
    public Response queryReport5Details(HttpServletRequest request) {
        super.pageLoad(request);
        return endToEndDeliveryTrackingService.queryReport5Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report5_details")
    public void downloadReport5Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        endToEndDeliveryTrackingService.downloadReport5Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_configuration")
    public Response queryConfigurationDetails(HttpServletRequest request) {
        super.pageLoad(request);
        return endToEndDeliveryTrackingService.queryConfiguration(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping("/download_configuration")
    public void downloadConfigurationDetails(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        endToEndDeliveryTrackingService.downloadConfiguration(session.getUserid(), parameterMap, response);
    }

    @SchneiderRequestMapping("/save_configuration")
    public Response saveConfigurationDetails(HttpServletRequest request) {
        super.pageLoad(request);
        return endToEndDeliveryTrackingService.saveConfiguration(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping("/query_cot_configuration")
    public Response queryCotConfigurationDetails(HttpServletRequest request) {
        super.pageLoad(request);
        return endToEndDeliveryTrackingService.queryCotConfiguration(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping("/download_cot_configuration")
    public void downloadCotConfigurationDetails(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        endToEndDeliveryTrackingService.downloadCotConfiguration(session.getUserid(), parameterMap, response);
    }

    @SchneiderRequestMapping("/save_cot_configuration")
    public Response saveCotConfigurationDetails(HttpServletRequest request) {
        super.pageLoad(request);
        return endToEndDeliveryTrackingService.saveCotConfiguration(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping("/init_rca_page")
    public Response initRCAPage(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return endToEndDeliveryTrackingService.initRCAPage();
    }

    @SchneiderRequestMapping("/query_rca_weekly_details")
    public Response queryRCAWeeklyDetails(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return endToEndDeliveryTrackingService.queryRCAWeeklyDetails(parameterMap);
    }

    @SchneiderRequestMapping("/download_rca_weekly_details")
    public void downloadRCAWeeklyDetails(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        endToEndDeliveryTrackingService.downloadRCAWeeklyDetails(parameterMap, response);
    }

    @SchneiderRequestMapping("/save_rca_weekly_details")
    public Response saveRCAWeeklyDetails(HttpServletRequest request) {
        super.pageLoad(request);
        return endToEndDeliveryTrackingService.saveRCAWeeklyDetails(parameterMap);
    }

    @SchneiderRequestMapping("/query_rca_tips_list")
    public Response queryRCATipsList(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return endToEndDeliveryTrackingService.queryRCATipsList(parameterMap);
    }

    @SchneiderRequestMapping("/query_rca_weekly")
    public Response queryRCAWeekly(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return endToEndDeliveryTrackingService.queryRCAWeekly(parameterMap);
    }

    @SchneiderRequestMapping("/query_rca_columns_by_daterange")
    public Response queryRCAColumnsByDaterange(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return endToEndDeliveryTrackingService.queryRCAColumnsByDaterange(parameterMap);
    }

    @SchneiderRequestMapping("/query_manual_d0")
    public Response queryManualD0(HttpServletRequest request) {
        super.pageLoad(request);
        return endToEndDeliveryTrackingService.queryManualD0(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping("/download_manual_d0")
    public void downloadManualD0(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        endToEndDeliveryTrackingService.downloadManualD0(session.getUserid(), parameterMap, response);
    }

    @SchneiderRequestMapping("/save_manual_d0")
    public Response saveManualD0(HttpServletRequest request) {
        super.pageLoad(request);
        return endToEndDeliveryTrackingService.saveManualD0(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping("/manual_d0_init_page")
    public Response manualD0Cascader(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return endToEndDeliveryTrackingService.queryManualD0Cascader();
    }

    @SchneiderRequestMapping("/query_manual_d0_details")
    public Response queryManualD0Details(HttpServletRequest request) {
        super.pageLoad(request);
        return endToEndDeliveryTrackingService.queryManualD0Details(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping("/download_manual_d0_details")
    public void downloadManualD0Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        endToEndDeliveryTrackingService.downloadManualD0Details(session.getUserid(), parameterMap, response);
    }
}
