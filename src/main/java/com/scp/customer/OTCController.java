package com.scp.customer;

import com.scp.customer.service.IOTCService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/customer/otc", parent = "menu950")
public class OTCController extends ControllerHelper {

    @Resource
    private IOTCService otcService;

    @SchneiderRequestMapping("/query_columns_by_daterange")
    public Response queryColumnsByDaterange(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return otcService.queryColumnsByDaterange(parameterMap);
    }

    @SchneiderRequestMapping("/query_otc_weekly")
    public Response queryOtcWeekly(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return otcService.queryOtcWeekly(parameterMap);
    }

    @SchneiderRequestMapping("/query_otc_weekly_details")
    public Response queryOtcWeeklyDetails(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return otcService.queryOtcWeeklyDetails(parameterMap);
    }

    @SchneiderRequestMapping("/query_otc_weekly_chart")
    public Response queryOtcWeeklyChart(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return otcService.queryOtcWeeklyChart(parameterMap);
    }

    @SchneiderRequestMapping("/download_otc_weekly_details")
    public void downloadOtcWeeklyDetails(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        otcService.downloadOtcWeeklyDetails(parameterMap, response);
    }

    /*
    @SchneiderRequestMapping("/query_otds_weekly_summary")
    public Response queryOtdsWeeklySummary(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return otcService.queryOtdsWeeklySummary(parameterMap);
    }

     */
}
