package com.scp.customer;

import com.scp.customer.service.IOTDSService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/customer/otds", parent = "menu810")
public class OTDSController extends ControllerHelper {

    @Resource
    private IOTDSService otdsService;

    @SchneiderRequestMapping("/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return otdsService.initPage();
    }

    @SchneiderRequestMapping("/query_columns_by_daterange")
    public Response queryColumnsByDaterange(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return otdsService.queryColumnsByDaterange(parameterMap);
    }

    @SchneiderRequestMapping("/query_otds_weekly")
    public Response queryOtdsWeekly(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return otdsService.queryOtdsWeekly(parameterMap);
    }

    @SchneiderRequestMapping("/query_otds_daily")
    public Response queryOtdsDaily(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return otdsService.queryOtdsDaily(parameterMap);
    }

    @SchneiderRequestMapping("/query_otds_weekly_details")
    public Response queryOtdsWeeklyDetails(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return otdsService.queryOtdsWeeklyDetails(parameterMap);
    }

    @SchneiderRequestMapping("/query_otds_weekly_chart")
    public Response queryOtdsWeeklyChart(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return otdsService.queryOtdsWeeklyChart(parameterMap);
    }

    @SchneiderRequestMapping("/query_otds_daily_details")
    public Response queryOtdsDailyDetails(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return otdsService.queryOtdsDailyDetails(parameterMap);
    }

    @SchneiderRequestMapping("/query_otds_tips_description")
    public Response queryOtdsTipsDescription(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return otdsService.queryOtdsTipsDescription(parameterMap);
    }

    @SchneiderRequestMapping("/query_otds_daily_chart")
    public Response queryOtdsDailyChart(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return otdsService.queryOtdsDailyChart(parameterMap);
    }

    @SchneiderRequestMapping("/download_otds_daily_details")
    public void downloadOtdsDailyDetails(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        otdsService.downloadOtdsDailyDetails(parameterMap, response);
    }

    @SchneiderRequestMapping("/download_otds_weekly_details")
    public void downloadOtdsWeeklyDetails(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        otdsService.downloadOtdsWeeklyDetails(parameterMap, response);
    }

    @SchneiderRequestMapping("/save_otds_daily_details")
    public Response saveOtdsDailyDetails(HttpServletRequest request) {
        super.pageLoad(request);
        return otdsService.saveOtdsDailyDetails(parameterMap);
    }

    @SchneiderRequestMapping("/save_otds_weekly_details")
    public Response saveOtdsWeeklyDetails(HttpServletRequest request) {
        super.pageLoad(request);
        return otdsService.saveOtdsWeeklyDetails(parameterMap);
    }

    @SchneiderRequestMapping("/query_otds_rca_tips_list")
    public Response queryOtdsRcaTipsList(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return otdsService.queryOtdsRcaTipsList(parameterMap);
    }

    @SchneiderRequestMapping("/query_otds_weekly_summary")
    public Response queryOtdsWeeklySummary(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return otdsService.queryOtdsWeeklySummary(parameterMap);
    }
}
