package com.scp.customer;

import com.scp.customer.service.ITEXService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/customer/tex", parent = "menu883")
public class TEXController extends ControllerHelper {

    @Resource
    private ITEXService TEXService;

    @SchneiderRequestMapping("/query_filters")
    public Response queryFilters() {
        return TEXService.queryCascader();
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.setGlobalCache(true);
        super.pageLoad(request);
        return TEXService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping("/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return TEXService.queryReport2(parameterMap);
    }
    @SchneiderRequestMapping("/query_report2_details")
    public Response queryReport2Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return TEXService.queryReport2Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report2_details")
    public void downloadReport2Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        TEXService.downloadReport2Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report3")
    public Response queryReport3(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return TEXService.queryReport3(parameterMap);
    }

    @SchneiderRequestMapping("/query_report3_details")
    public Response queryReport3Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return TEXService.queryReport3Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report3_details")
    public void downloadReport3Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        TEXService.downloadReport3Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report4")
    public Response query_report4(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return TEXService.queryReport4(parameterMap);
    }

    @SchneiderRequestMapping("/query_report4_columns")
    public Response queryReport4Columns(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return TEXService.queryReport4Columns(parameterMap);
    }

    @SchneiderRequestMapping("/download_report4")
    public void downloadReport4(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        TEXService.downloadReport4(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report4_details")
    public Response queryReport4Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return TEXService.queryReport4Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report4_details")
    public void downloadReport4Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        TEXService.downloadReport4Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report5")
    public Response queryReport5(HttpServletRequest request) {
        super.pageLoad(request);
        return TEXService.queryReport5(parameterMap);
    }

    @SchneiderRequestMapping("/query_report5_details")
    public Response queryReport5Details(HttpServletRequest request) {
        super.pageLoad(request);
        return TEXService.queryReport5Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report5_details")
    public void downloadReport5Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        TEXService.downloadReport5Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report6")
    public Response queryReport6(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return TEXService.queryReport6(parameterMap);
    }
}
