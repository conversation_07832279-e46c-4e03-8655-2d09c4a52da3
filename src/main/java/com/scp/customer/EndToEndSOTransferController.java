package com.scp.customer;

import com.scp.customer.service.IEndToEndSOTransferService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.util.WebUtils;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/customer/end_to_end_so_transfer", parent = EndToEndSOTransferController.PARENT_CODE)
public class EndToEndSOTransferController extends ControllerHelper {

    public static final String PARENT_CODE = "menu892";

    @Resource
    private Response response;

    @SchneiderRequestMapping("/allowlist_init_page")
    public Response allowlistInitPage(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return endToEndSOTransferService.allowlistInitPage();
    }

    @Resource
    private IEndToEndSOTransferService endToEndSOTransferService;

    @SchneiderRequestMapping("/allowlist_query_report1")
    public Response allowlistQueryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        return endToEndSOTransferService.allowlistQueryReport1(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping("/allowlist_download_report1")
    public void allowlistDownloadReport1(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        endToEndSOTransferService.allowlistDownloadReport1(session.getUserid(), parameterMap, response);
    }

    @SchneiderRequestMapping("/allowlist_save_report1")
    public Response allowlistSaveReport1(HttpServletRequest request) {
        super.pageLoad(request);
        return endToEndSOTransferService.allowlistSaveReport1(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping("/download_report1_template")
    public void downloadReport1Template(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        endToEndSOTransferService.downloadReport1Template(parameterMap, response);
    }

    @SchneiderRequestMapping("/upload_report1")
    public Response uploadReport1(HttpServletRequest request) {
        super.pageLoad(request);
        MultipartHttpServletRequest multipartRequest = WebUtils.getNativeRequest(request, MultipartHttpServletRequest.class);
        if (multipartRequest != null) {
            MultipartFile file = multipartRequest.getFile("file");
            try {
                return endToEndSOTransferService.uploadReport1(file, parameterMap);
            } catch (Exception e) {
                return response.setError(e);
            }
        } else {
            return response.setError(new Exception("upload failed, multipartRequest is null !"));
        }
    }

    @SchneiderRequestMapping("/allowlist_query_report2")
    public Response allowlistQueryReport2(HttpServletRequest request) {
        super.pageLoad(request);
        return endToEndSOTransferService.allowlistQueryReport2(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping("/allowlist_download_report2")
    public void allowlistDownloadReport2(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        endToEndSOTransferService.allowlistDownloadReport2(session.getUserid(), parameterMap, response);
    }
}
