package com.scp.customer.service;

import com.starter.context.bean.Response;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;

public interface IOpenPONORService {

    Response initPage(Map<String, Object> parameterMap);

    Response queryReport1(Map<String, Object> parameterMap) throws Exception;

    Response queryReport2(Map<String, Object> parameterMap);

    Response queryReport2Details(Map<String, Object> parameterMap);

    void downloadReport2Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport3(Map<String, Object> parameterMap);

    Response queryReport3Details(Map<String, Object> parameterMap);

    void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport4(Map<String, Object> parameterMap);

    void downloadReport4(Map<String, Object> parameterMap, HttpServletResponse res);

    Response queryReport4Details(Map<String, Object> parameterMap);

    void downloadReport4Details(Map<String, Object> parameterMap, HttpServletResponse res);

    Response queryReport4MODetails(Map<String, Object> parameterMap);

    void downloadReport4MODetails(Map<String, Object> parameterMap, HttpServletResponse res);

}
