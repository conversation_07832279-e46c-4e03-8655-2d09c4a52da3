package com.scp.customer.service;

import com.starter.context.bean.Response;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

public interface IEndToEndSOTransferService {
    Response allowlistInitPage();

    Response allowlistQueryReport1(String userid, Map<String, Object> parameterMap);

    void allowlistDownloadReport1(String userid, Map<String, Object> parameterMap, HttpServletResponse res);

    Response allowlistSaveReport1(String userid, Map<String, Object> parameterMap);

    void downloadReport1Template(Map<String, Object> parameterMap, HttpServletResponse response);

    Response uploadReport1(MultipartFile file, Map<String, Object> parameterMap) throws Exception;

    Response allowlistQueryReport2(String userid, Map<String, Object> parameterMap);

    void allowlistDownloadReport2(String userid, Map<String, Object> parameterMap, HttpServletResponse res);


}
