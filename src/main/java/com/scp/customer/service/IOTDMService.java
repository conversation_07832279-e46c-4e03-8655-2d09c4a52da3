package com.scp.customer.service;

import com.starter.context.bean.Response;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;

public interface IOTDMService {

    Response queryReport1Columns(Map<String, Object> parameterMap);

    Response queryReport1(Map<String, Object> parameterMap);

    Response queryReport1Details(Map<String, Object> parameterMap);

    void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport2(Map<String, Object> parameterMap);

    Response queryOtdmRcaTipsList(Map<String, Object> parameterMap);

    Response saveOtdmReport1Details(Map<String, Object> parameterMap);
}
