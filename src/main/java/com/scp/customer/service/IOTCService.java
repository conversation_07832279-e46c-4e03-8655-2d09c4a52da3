package com.scp.customer.service;

import com.starter.context.bean.Response;
import jakarta.servlet.http.HttpServletResponse;

import java.util.Map;

public interface IOTCService {

    Response queryColumnsByDaterange(Map<String, Object> parameterMap);

    Response queryOtcWeekly(Map<String, Object> parameterMap);

    void downloadOtcWeeklyDetails(Map<String, Object> parameterMap, HttpServletResponse response);


    Response queryOtcWeeklyDetails(Map<String, Object> parameterMap);

    Response queryOtcWeeklyChart(Map<String, Object> parameterMap);

    /*
    Response queryOtdsWeeklySummary(Map<String, Object> parameterMap);
     */

}
