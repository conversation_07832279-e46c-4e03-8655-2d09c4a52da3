package com.scp.customer.service;

import com.starter.context.bean.Response;
import jakarta.servlet.http.HttpServletResponse;

import java.util.Map;

public interface ICloStructureService {

    Response initPage(Map<String, Object> parameterMap);

    Response queryReport1(Map<String, Object> parameterMap);

    void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse response);

    Response saveReport1Comments(Map<String, Object> parameterMap);

    Response queryReport2(Map<String, Object> parameterMap);

    Response queryReport2Sub(Map<String, Object> parameterMap);

    Response queryReport2Details(Map<String, Object> parameterMap);

    void downloadReport2Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport3(Map<String, Object> parameterMap);

    Response queryReport3Details(Map<String, Object> parameterMap);

    void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse response);
}
