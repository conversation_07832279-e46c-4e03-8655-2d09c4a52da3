package com.scp.customer.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.scp.customer.dao.IMyCPDao;
import com.scp.customer.service.IMyCPService;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import com.starter.utils.excel.SheetInfoWithQueryKey;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

@Service("MyCPService")
@Scope("prototype")
@Transactional
public class MyCPServiceImpl extends ServiceHelper implements IMyCPService {

    @Resource
    private IMyCPDao mycpDao;

    @Resource
    private Response response;

    @Resource
    private ExcelTemplate excelTemplate;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage(Map<String, Object> parameterMap) {
        String pageType = (String) parameterMap.get("pageType");
        if ("MyCP by Response".equalsIgnoreCase(pageType)) {
            parameterMap.put("tableName", "MYCP_RESPONSE_V");
            parameterMap.put("filterTableName", "SCPA.MYCP_RESPONSE_FILTER_V");
        } else {
            parameterMap.put("tableName", "MYCP_COMMITMENT_V");
            parameterMap.put("filterTableName", "SCPA.MYCP_COMMITMENT_FILTER_V");
        }

        List<LinkedHashMap<String, Object>> report3RCADesc = mycpDao.queryReport3RCADesc(parameterMap);
        Map<String, String> rcaDesc = new HashMap<>();
        for (Map<String, Object> map : report3RCADesc) {
            rcaDesc.put(String.valueOf(map.get("RCA_CODE")), String.valueOf(map.get("DESCRIPTION")));
        }

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cascader", Utils.parseCascader(mycpDao.queryCascader(parameterMap)));
        resultMap.put("pivotOpts", mycpDao.queryPivotOpts(parameterMap));
        resultMap.put("rcaDesc", rcaDesc);
        resultMap.put("filterDateColumns", mycpDao.queryFilterDateColumns(parameterMap));
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);

        JSONArray report1Categories = (JSONArray) parameterMap.get("report1Categories");
        if (report1Categories.isEmpty()) {
            report1Categories.add("BU");
            report1Categories.add("PRODUCT_LINE");
            report1Categories.add("ENTITY");
            report1Categories.add("VENDOR_NAME");
            report1Categories.add("STOCKING_POLICY");
        }
        parameterMap.put("report1Category", report1Categories.get(0));
        List<Map<String, Object>> resultList = mycpDao.queryReport1(parameterMap);
        double max = this.renderReport1Width(resultList, 0);
        for (Map<String, Object> map : resultList) {
            map.put("parentMaxWidth", max);
            map.put("parent", new ArrayList<>());
            map.put("id", Utils.randomStr(12));
            map.put("hasChildren", report1Categories.size() > 1 && "Total".equals(map.get("category")) == false);
        }
        return response.setBody(resultList);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1Sub(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);

        JSONArray report1Categories = (JSONArray) parameterMap.get("report1Categories");
        JSONArray parent = (JSONArray) parameterMap.get("parent");
        double parentMaxWidth = Utils.parseDouble(parameterMap.get("parentMaxWidth"));
        String expandValue = (String) parameterMap.get("expandValue");
        String expandColumn = report1Categories.getString(parent.size());
        parameterMap.put("expandValue", expandValue);
        parameterMap.put("expandColumn", expandColumn);
        parameterMap.put("category", report1Categories.getString(parent.size() + 1));
        List<Map<String, Object>> resultList = mycpDao.queryReport1Sub(parameterMap);

        this.renderReport1Width(resultList, parentMaxWidth);
        for (Map<String, Object> map : resultList) {
            map.put("id", Utils.randomStr(12));
            map.put("hasChildren", report1Categories.size() > parent.size() + 2);
            JSONArray temp = new JSONArray();
            temp.addAll(parent);
            temp.add(expandValue);
            map.put("parent", temp);
            map.put("parentMaxWidth", parentMaxWidth);
        }
        return response.setBody(resultList);
    }

    private double renderReport1Width(List<Map<String, Object>> data, double parentMax) {
        double max = Math.max(0, parentMax);
        for (Map<String, Object> map : data) {
            if ("Total".equals(map.get("category")) == false) {
                max = Math.max(max, Utils.parseDouble(map.get("fail")));
                max = Math.max(max, Utils.parseDouble(map.get("onTime")));
            }
        }

        for (Map<String, Object> map : data) {
            if ("Total".equals(map.get("category")) == false) {
                if (max != 0) {
                    map.put("failWidth", Utils.parseDouble(map.get("fail")) / max * 100);
                    map.put("onTimeWidth", Utils.parseDouble(map.get("onTime")) / max * 100);
                } else {
                    map.put("failWidth", 0);
                    map.put("onTimeWidth", 0);
                }
            }
        }
        return max;
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTrendFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(mycpDao.queryReport1DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(mycpDao.queryReport1Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateTrendFilter(parameterMap);

        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "mycp_details_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.customer.dao.IMyCPDao.queryReport1Details", parameterMap);
    }

    private void generateTrendFilter(Map<String, Object> parameterMap) {
        JSONObject selectObj = JSON.parseObject((String) parameterMap.get("report1SelectedValues"));
        if (selectObj != null) {
            if ("Total".equalsIgnoreCase(selectObj.getString("category"))) {
                parameterMap.put("report1Values", new ArrayList<>());
            } else {
                List<String> values = new ArrayList<>(selectObj.getJSONArray("parent").toJavaList(String.class));
                values.add(selectObj.getString("category"));
                parameterMap.put("report1Values", values);
            }
        }
    }


    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTrendFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        String report2ViewType = (String) parameterMap.get("report2ViewType");

        Map<String, Object> resultMap = new HashMap<>();

        List<String> xAxis = mycpDao.queryReport2XAxis(parameterMap);

        parameterMap.put("xAxis", xAxis);

        Map<String, Object> lineData = mycpDao.queryReport2Line(parameterMap);
        Map<String, Object> replacedMap = new HashMap<>();
        if (lineData != null) {
            for (Map.Entry<String, Object> entry : lineData.entrySet()) {
                String newKey = entry.getKey().replace("'", "");
                replacedMap.put(newKey, entry.getValue());
            }
        }
        List<String> lineResult = new ArrayList<>();
        for (String x : xAxis) {
            lineResult.add(replacedMap.get(x).toString());
        }
        resultMap.put("lineData", lineResult);

        List<Map<String, Object>> yAxisData = mycpDao.queryReport2YAxis(parameterMap);
        resultMap.put("xAxis", xAxis);

        Map<String, Map<String, BigDecimal>> yAxisMap = new HashMap<>();
        for (Map<String, Object> data : yAxisData) {
            String range = (String) data.get(report2ViewType);
            Object yAxis = data.get("yAxis");
            String y = yAxis.toString();
            Map<String, BigDecimal> tempMap = yAxisMap.computeIfAbsent(range, key -> new HashMap<>());
            tempMap.put(y, Utils.parseBigDecimal(data.get("VAL")));
        }

        Map<String, List<BigDecimal>> yAxis = new HashMap<>();
        for (String key : yAxisMap.keySet()) {
            Map<String, BigDecimal> val = yAxisMap.get(key);
            List<BigDecimal> tempList = yAxis.computeIfAbsent(key, k -> new ArrayList<>());
            for (String x : xAxis) {
                tempList.add(val.get(x));
            }
        }
        resultMap.put("yAxis", yAxis);
        return response.setBody(resultMap);
    }

    @Override
    public Response queryReport2Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTrendFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);

        page.setTotal(mycpDao.queryReport2DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(mycpDao.queryReport2Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport2Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        this.generateFilter(parameterMap);
        this.generateTrendFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "mycp_report2_details_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.customer.dao.ImycpDao.queryReport2Details", parameterMap);
    }


    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Columns(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        List<String> report3DateColumns = mycpDao.queryReport3DateColumns(parameterMap);
        return response.setBody(report3DateColumns);
    }

    @Override
    @SuppressWarnings("unchecked")
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTrendFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);

        List<String> fields = (List<String>) parameterMap.get("fields");
        List<String> report3DateColumns = (List<String>) parameterMap.get("report3DateColumns");

        String sort = page.getSort();
        if (!fields.contains(page.getSortColumn())) {
            sort = sort.replace("\" ", "_RATIO\" ");
            sort = sort.replace("TOTAL", "Total");
            page.setSort(sort);
        }

        List<Map<String, Object>> dataList = mycpDao.queryReport3(parameterMap);


        List<Map<String, Object>> resultList = new ArrayList<>();

        Map<String, Object> ontimeTotal = new HashMap<>();
        Map<String, Object> failTotal = new HashMap<>();
        Map<String, Object> othersTotal = new HashMap<>();
        Map<String, Object> ratioTotal = new HashMap<>();

        ontimeTotal.put("TYPE", "ONTIME");
        failTotal.put("TYPE", "FAIL");
        othersTotal.put("TYPE", "OTHERS");
        ratioTotal.put("TYPE", "RATIO");

        String tk = fields.get(fields.size() - 1);
        ontimeTotal.put(tk, "Total");
        failTotal.put(tk, "Total");
        othersTotal.put(tk, "Total");
        ratioTotal.put(tk, "Total");

        for (Map<String, Object> data : dataList) {
            Map<String, Object> ontime = new HashMap<>();
            Map<String, Object> fail = new HashMap<>();
            Map<String, Object> others = new HashMap<>();
            Map<String, Object> ratio = new HashMap<>();

            ontime.put("TYPE", "ONTIME");
            fail.put("TYPE", "FAIL");
            others.put("TYPE", "OTHERS");
            ratio.put("TYPE", "RATIO");

            for (String field : fields) {
                ontime.put(field, data.get(field));
                fail.put(field, data.get(field));
                others.put(field, data.get(field));
                ratio.put(field, data.get(field));
            }
            for (String column : report3DateColumns) {
                ontime.put(column, data.get(column + "_ONTIME"));
                fail.put(column, data.get(column + "_FAIL"));
                others.put(column, data.get(column + "_OTHERS"));
                ratio.put(column, data.get(column + "_RATIO"));

                BigDecimal t = Utils.parseBigDecimal(ontimeTotal.getOrDefault(column, BigDecimal.ZERO));
                ontimeTotal.put(column, t.add(Utils.parseBigDecimal(data.get(column + "_ONTIME"))));

                t = Utils.parseBigDecimal(failTotal.getOrDefault(column, BigDecimal.ZERO));
                failTotal.put(column, t.add(Utils.parseBigDecimal(data.get(column + "_FAIL"))));

                t = Utils.parseBigDecimal(othersTotal.getOrDefault(column, BigDecimal.ZERO));
                othersTotal.put(column, t.add(Utils.parseBigDecimal(data.get(column + "_OTHERS"))));
            }

            resultList.add(ratio);
            resultList.add(ontime);
            resultList.add(fail);
            resultList.add(others);
        }

        for (String column : report3DateColumns) {
            BigDecimal ontime = Utils.parseBigDecimal(ontimeTotal.getOrDefault(column, BigDecimal.ZERO));
            BigDecimal fail = Utils.parseBigDecimal(failTotal.getOrDefault(column, BigDecimal.ZERO));
            BigDecimal total = ontime.add(fail);
            if (total.compareTo(BigDecimal.ZERO) == 0) {
                ratioTotal.put(column, "");
            } else {
                ratioTotal.put(column, ontime.divide(total, 3, RoundingMode.HALF_UP));
            }
        }

        resultList.add(ratioTotal);
        resultList.add(ontimeTotal);
        resultList.add(failTotal);
        resultList.add(othersTotal);

        page.setTotal(resultList.size());
        page.setData(resultList);
        return response.setBody(page);
    }

    @Override
    @SuppressWarnings("unchecked")
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTrendFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(mycpDao.queryReport3DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(mycpDao.queryReport3Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    @SuppressWarnings("unchecked")
    public void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        this.generateFilter(parameterMap);
        this.generateTrendFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "mycp_report3_details" + Utils.randomStr(4) + ".xlsx";

        SheetInfoWithQueryKey sheetInfoWithQueryKey1 = new SheetInfoWithQueryKey();
        sheetInfoWithQueryKey1.setParameterMap(parameterMap);
        sheetInfoWithQueryKey1.setQueryKey("com.scp.customer.dao.IMyCPDao.queryReport3Details");
        sheetInfoWithQueryKey1.setSheetName("MyCP Details");

        SheetInfoWithQueryKey sheetInfoWithQueryKey2 = new SheetInfoWithQueryKey();
        sheetInfoWithQueryKey2.setParameterMap(parameterMap);
        sheetInfoWithQueryKey2.setQueryKey("com.scp.customer.dao.IMyCPDao.queryReport3RCADesc");
        sheetInfoWithQueryKey2.setSheetName("RCA Code Description");

        excelTemplate.create(response, fileName, sheetInfoWithQueryKey1, sheetInfoWithQueryKey2);
    }

    //region private functions
    @SuppressWarnings("unchecked")
    private void generateFilter(Map<String, Object> parameterMap) {
        String reportViewType = (String) parameterMap.get("reportViewType");
        String dateColumn = "CALENDAR_DATE";
        switch (reportViewType) {
            case "VIEW_BY_DAY":
                dateColumn = "CALENDAR_DATE";
                break;
            case "VIEW_BY_WEEK":
                dateColumn = "CALENDAR_WEEK";
                break;
            case "VIEW_BY_MONTH":
                dateColumn = "CALENDAR_MONTH";
                break;
            case "VIEW_BY_QUARTER":
                dateColumn = "CALENDAR_QUARTER";
                break;
            case "VIEW_BY_YEAR":
                dateColumn = "CALENDAR_YEAR";
                break;
        }
        parameterMap.put("dateColumn", dateColumn);

        this.generateCascaderFilterSQL(parameterMap);
    }

    private void generateValueColumn(Map<String, Object> parameterMap) {
        parameterMap.put("valueColumn", "COUNT(1)");
    }
}
