package com.scp.customer.service.impl;

import com.adm.system.bean.CascaderBean;
import com.alibaba.fastjson.JSONArray;
import com.scp.customer.bean.CloLtEvolutionReport1Bean;
import com.scp.customer.bean.CloLtEvolutionReport1Treemap;
import com.scp.customer.bean.CloLtEvolutionReport2Bean;
import com.scp.customer.dao.ICloLtEvolutionDao;
import com.scp.customer.service.ICloLtEvolutionService;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service("CloLtEvolutionService")
@Scope("prototype")
@Transactional
public class CloLtEvolutionServiceImpl implements ICloLtEvolutionService {

    @Resource
    private ICloLtEvolutionDao cloLtEvolutionDao;

    @Resource
    private Response response;

    @Resource
    private ExcelTemplate excelTemplate;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryCascader() {
        List<CascaderBean> resultList = new ArrayList<>();
        List<Map<String, String>> dataList = cloLtEvolutionDao.queryCascader();

        //取出所有filters中的{BU=[{CATEGORY=BU, NAME=SCE SECURE POWER SERV.}, {CATEGORY=BU, NAME=IND PROCESS AUTOMATION}]}
        Map<String, List<Map<String, String>>> tempMap = new LinkedHashMap<>();
        for (Map<String, String> map : dataList) {
            List<Map<String, String>> list = tempMap.computeIfAbsent(map.get("CATEGORY"), key -> new ArrayList<>());
            list.add(map);
        }

        // .keyset() 取出所有键; 生成filter的指定格式List<CascaderBean>
        for (String key : tempMap.keySet()) {
            CascaderBean bean = new CascaderBean();
            resultList.add(bean);
            bean.setLabel(key);
            bean.setValue(key);
            for (Map<String, String> map : tempMap.get(key)) {
                CascaderBean subBean = new CascaderBean();
                subBean.setLabel(map.get("NAME"));
                subBean.setValue(subBean.getLabel());
                bean.addChild(subBean);
            }
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cascader", resultList);

        return response.setBody(resultMap);
    }

    private String getColumnName(Object labelObj) {
        String label = (String) labelObj;
        if (label == null) {
            return null;
        }
        if (Utils.hasInjectionAttack(label)) {
            return "";
        }
        return label;
    }

    //region private functions
    @SuppressWarnings("unchecked")
    private void generateFilter(Map<String, Object> parameterMap) {
        List<String> report3SelectedColumns = (List<String>) parameterMap.get("report3SelectedColumns");

        if (report3SelectedColumns == null || report3SelectedColumns.isEmpty()) {
            report3SelectedColumns = new ArrayList<>();
            report3SelectedColumns.add("BU");
            report3SelectedColumns.add("ENTITY");
        }
        parameterMap.put("report3SelectedColumns", report3SelectedColumns);
        // 生成筛选条件
        JSONArray categoryArray = (JSONArray) parameterMap.get("filterList");
        if (categoryArray != null) {
            Map<String, List<String>> filterMap = new HashMap<>();

            for (Object subObj : categoryArray) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = subArray.getString(0);

                String value = subArray.getString(1);
                String key = Utils.randomStr(8);

                if (Utils.hasInjectionAttack(columnName)) {
                    continue;
                }

                List<String> fl = filterMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                fl.add("#{" + key + ",jdbcType=VARCHAR}");
                parameterMap.put(key, value);
            }

            List<String> filterList = new ArrayList<>();

            for (String key : filterMap.keySet()) {
                List<String> fl = filterMap.get(key);
                filterList.add("t." + key + " in (" + StringUtils.join(fl, ",") + ")");
            }

            parameterMap.put("filters", StringUtils.join(filterList, " and "));
        }

        // load special parameter
        String specialContent = (String) parameterMap.get("specialContent");
        if (StringUtils.isNotBlank(specialContent)) {
            parameterMap.put("specialList", Utils.splitValue(specialContent));
            parameterMap.put("specialColumn", this.getColumnName(parameterMap.get("specialType")));
        }
    }

    private void generateTreePathFilter(Map<String, Object> parameterMap) {
        String selectedTreePath = (String) parameterMap.get("selectedTreePath");
        if (StringUtils.isNotBlank(selectedTreePath)) {
            List<String> conditions = new ArrayList<>();
            String[] treePaths = selectedTreePath.split(" > ");
            for (int i = 1; i <= Math.min(treePaths.length, 5); i++) {
                String key = Utils.randomStr(8);
                if ("Others".equals(StringUtils.trim(treePaths[i - 1]))) {
                    String name = this.getColumnName(parameterMap.get("level" + i));
                    conditions.add("(" + name + " = #{" + key + ",jdbcType=VARCHAR} or " + name + " is null )");
                } else {
                    conditions.add(this.getColumnName(parameterMap.get("level" + i)) + " = #{" + key + ",jdbcType=VARCHAR}");
                }
                parameterMap.put(key, StringUtils.trim(treePaths[i - 1]));
            }
            parameterMap.put("treePathFilter", "(" + StringUtils.join(conditions, " and ") + ")");
        }
    }

    private void generateValueColumn(Map<String, Object> parameterMap) {
        String resultType = (String) parameterMap.get("resultType");
        String valueColumn = "NVL(SUM(AVG_VALUE), 0)";
        if ("AVG_VALUE".equalsIgnoreCase(resultType)) {
            valueColumn = "NVL(SUM(AVG_VALUE), 0)";
        } else if ("AVG_LINES".equalsIgnoreCase(resultType)) {
            valueColumn = "NVL(SUM(AVG_LINES), 0)";
        } else if ("MATERIAL".equalsIgnoreCase(resultType)) {
            valueColumn = "COUNT(distinct material)";
        }
        parameterMap.put("valueColumn", valueColumn);
    }

    private void generateReport1Tooltips(Map<String, Object> parameterMap) {
        List<String> tooltips = ((JSONArray) parameterMap.get("report1Tooltips")).toJavaList(String.class);
        if (!tooltips.isEmpty()) {
            List<String> tooltipsColumns = tooltips.stream().map(this::getColumnName).toList();
            List<String> tooltipsColumnsName = new ArrayList<>();

            for (String c : tooltipsColumns) {
                String tooltip = this.getColumnName(c);
                tooltipsColumnsName.add("NVL(SUM(" + c + "),0) AS " + tooltip);
            }
            parameterMap.put("tooltipsColumns", StringUtils.join(tooltipsColumnsName, ", "));
        }
    }

    private void convertReport1Data(List<CloLtEvolutionReport1Treemap> list, CloLtEvolutionReport1Bean data) {
        String[] categorysOrg = new String[]{data.getCategory1(), data.getCategory2(), data.getCategory3(), data.getCategory4(), data.getCategory5()};
        List<String> categories = new ArrayList<>();

        for (String category : categorysOrg) {
            if (StringUtils.isNotBlank(category)) {
                categories.add(category);
            } else {
                break;
            }
        }

        // 这边逻辑比较复杂, 所以用最笨的方法来描述了, 以免后期不好维护
        // 先把这一行数据转成treemap的数据
        // 第一个节点
        List<CloLtEvolutionReport1Treemap> child = new ArrayList<>();
        CloLtEvolutionReport1Treemap root = new CloLtEvolutionReport1Treemap();
        root.setName(categories.get(0));
        root.setTips(data.copyTooltips()); // 因为这个tooltips要放在树中全局使用, 所以必须要生成一个新节点
        root.setChildren(child);

        // 中间节点
        for (int i = 1; i < categories.size() - 1; i++) {
            CloLtEvolutionReport1Treemap treemap = new CloLtEvolutionReport1Treemap();
            treemap.setName(categories.get(i));
            treemap.setTips(data.copyTooltips());

            child.add(treemap);
            child = new ArrayList<>();
            treemap.setChildren(child);
        }

        // 最后一个节点
        CloLtEvolutionReport1Treemap lastNode = new CloLtEvolutionReport1Treemap();
        lastNode.setName(categories.get(categories.size() - 1));
        lastNode.setValue(data.getValue());
        lastNode.setTips(data.copyTooltips());
        child.add(lastNode);

        // 将这行treemap与原始数据相加
        // 先找到list中是否有这个数据节点
        Optional<CloLtEvolutionReport1Treemap> beanOpt = list.stream().filter(b -> b.getName().equals(categories.get(0))).findFirst();
        if (beanOpt.isPresent()) {
            CloLtEvolutionReport1Treemap bean = beanOpt.get();
            bean.add(root); // 两个节点合并
        } else { //找不到的时候最省事, 直接放入list就可以了
            list.add(root);
        }
    }


    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateReport1Tooltips(parameterMap);
        this.generateValueColumn(parameterMap);

        // 将前台传过来的label转换成列名, 同时也可以防止恶意代码注入
        parameterMap.put("level1", this.getColumnName(parameterMap.get("level1")));
        parameterMap.put("level2", this.getColumnName(parameterMap.get("level2")));
        parameterMap.put("level3", this.getColumnName(parameterMap.get("level3")));
        parameterMap.put("level4", this.getColumnName(parameterMap.get("level4")));
        parameterMap.put("level5", this.getColumnName(parameterMap.get("level5")));

        List<CloLtEvolutionReport1Treemap> resultList = new ArrayList<>();
        List<CloLtEvolutionReport1Bean> dataList = cloLtEvolutionDao.queryReport1(parameterMap);
        for (CloLtEvolutionReport1Bean data : dataList) {
            this.convertReport1Data(resultList, data);
        }
        return response.setBody(resultList);
    }


    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);

        List<CloLtEvolutionReport2Bean> dataList;

        dataList = cloLtEvolutionDao.queryReport2(parameterMap);

        Map<String, BigDecimal> dataMap = new HashMap<>();
        LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();
        Map<String, String> xAxisMap = new HashMap<>();

        List<String> legend = new ArrayList<>();

        // 先将数据转存到dataMap、xAxisMap
        for (CloLtEvolutionReport2Bean data : dataList) {
            dataMap.put(data.getKey(), data.getVALUE());
            xAxisMap.put(data.getCALENDAR_DATE(), "");
            if (legend.contains(data.getNAME()) == false) {
                legend.add(data.getNAME());
            }
        }
        // 获取x轴
        List<String> xAxisList = xAxisMap.keySet().stream().sorted(String::compareTo).collect(Collectors.toList());
        //对于每一个图例做处理，生成{图例:[VALUE]}的map
        for (String l : legend) {
            List<BigDecimal> temp = new ArrayList<>();
            for (String x : xAxisList) {
                temp.add(dataMap.getOrDefault(l + "#" + x, BigDecimal.ZERO));
            }
            resultMap.put(l, temp);
        }
        // 放置x轴
        resultMap.put("xAxis", xAxisList);
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(cloLtEvolutionDao.queryReport2DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(cloLtEvolutionDao.queryReport2Details(parameterMap));
        }
        return response.setBody(page);
    }

    public void downloadReport2Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "clo_lt_evolution_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.customer.dao.ICloLtEvolutionDao.queryReport2Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        String report3ResultType = (String) parameterMap.get("report3ResultType");
        String report3ValueColumn = "NVL(SUM(AVG_VALUE), 0)";
        if ("AVG_VALUE".equalsIgnoreCase(report3ResultType)) {
            report3ValueColumn = "NVL(SUM(AVG_VALUE), 0)";
        } else if ("AVG_LINES".equalsIgnoreCase(report3ResultType)) {
            report3ValueColumn = "NVL(SUM(AVG_LINES), 0)";
        } else if ("MATERIAL".equalsIgnoreCase(report3ResultType)) {
            report3ValueColumn = "COUNT(distinct material)";
        } else if ("CLO_LT".equalsIgnoreCase(report3ResultType)) {
            report3ValueColumn = "sum(nvl(CLO_LT, 0))";
        } else if ("PLANNED_DELIV_TIME".equalsIgnoreCase(report3ResultType)) {
            report3ValueColumn = "sum(nvl(PLANNED_DELIV_TIME, 0))";
        } else if ("PICK_PACK_TIME".equalsIgnoreCase(report3ResultType)) {
            report3ValueColumn = "sum(nvl(PICK_PACK_TIME, 0))";
        } else if ("GR_PROCESSING_TIME".equalsIgnoreCase(report3ResultType)) {
            report3ValueColumn = "sum(nvl(GR_PROCESSING_TIME, 0))";
        }
        parameterMap.put("report3ValueColumn", report3ValueColumn);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        List<LinkedHashMap<String, Object>> dataList = cloLtEvolutionDao.queryReport3(parameterMap);

        page.setData(dataList);
        page.setTotal(dataList.size());
        return response.setBody(page);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(cloLtEvolutionDao.queryReport3DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(cloLtEvolutionDao.queryReport3Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Columns(Map<String, Object> parameterMap) {
        return response.setBody(cloLtEvolutionDao.queryReport3Columns(parameterMap));
    }

    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public void downloadReport3(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        String column1 = this.getColumnName(parameterMap.get("column1"));
        String column2 = this.getColumnName(parameterMap.get("column2"));
        parameterMap.put("column1", column1);
        parameterMap.put("column2", column2);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "clo_lt_evolution_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.customer.dao.ICloLtEvolutionDao.queryReport3", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        parameterMap.put("column1", this.getColumnName(parameterMap.get("column1")));
        parameterMap.put("column2", this.getColumnName(parameterMap.get("column2")));
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "clo_lt_evolution_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.customer.dao.ICloLtEvolutionDao.queryReport3Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport4(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);

        LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();
        List<LinkedHashMap<String, Object>> dataList = cloLtEvolutionDao.queryReport4(parameterMap);

        Collections.reverse(dataList);

        Set<String> legend = new HashSet<>();
        List<String> xAxisList = new ArrayList<>();

        dataList.forEach(data -> {
            data.keySet().stream()
                    .filter(key -> !"XAXIS".equalsIgnoreCase(key))
                    .forEach(legend::add);

            xAxisList.add((String) data.get("XAXIS"));
        });

        legend.forEach(key -> {
                    List<BigDecimal> temp = dataList.stream()
                            .map(data -> (BigDecimal) data.getOrDefault(key, BigDecimal.ZERO))
                            .collect(Collectors.toList());
                    resultMap.put(key, temp);
                });
        resultMap.put("legend", legend);
        resultMap.put("xAxis", xAxisList);
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport4Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(cloLtEvolutionDao.queryReport4DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(cloLtEvolutionDao.queryReport4Details(parameterMap));
        }
        return response.setBody(page);
    }

    public void downloadReport4Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "clo_lt_evolution_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.customer.dao.ICloLtEvolutionDao.queryReport4Details", parameterMap);
    }

    public void downloadReport4(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "clo_lt_evolution_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.customer.dao.ICloLtEvolutionDao.downloadReport4", parameterMap);
    }
}
