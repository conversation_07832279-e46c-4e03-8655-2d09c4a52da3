package com.scp.customer.service.impl;

import com.scp.customer.dao.IOTDCDao;
import com.scp.customer.service.IOTDCService;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@Scope("prototype")
@Transactional
public class OTDCServiceImpl extends ServiceHelper implements IOTDCService {

    @Resource
    private Response response;

    @Resource
    private IOTDCDao otdcDao;

    @Resource
    private ExcelTemplate excelTemplate;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryDateColumns(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("years", otdcDao.queryOtdcYears(parameterMap));
        resultMap.put("months", otdcDao.queryOtdcMonths(parameterMap));
        resultMap.put("weeks", otdcDao.queryOtdcWeeks(parameterMap));
        return response.setBody(resultMap);
    }

    @Override
    @SuppressWarnings("unchecked")
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap, "OTDC_DC_SOURCE_V");
        List<String> fields = (List<String>) parameterMap.get("selectedField");

        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        String sort = page.getSort();
        if (fields.contains(page.getSortColumn()) == false) {
            sort = sort.replace("\"", "\"'").replace("\"' ", "'_RATIO\" ");
            page.setSort(sort);
        }

        List<String> years = (List<String>) parameterMap.get("selectedYears");
        List<String> months = (List<String>) parameterMap.get("selectedMonths");

        List<Map<String, Object>> dataList = otdcDao.queryReport1(parameterMap);
        List<Map<String, Object>> resultList = new ArrayList<>();

        Map<String, Object> totalMap = null;
        List<String> monthHeaders = new ArrayList<>();
        for (String month : months) {
            monthHeaders.add(month.substring(0, 4) + month.substring(4));
        }
        for (Map<String, Object> map : dataList) {
            boolean isTotal = false;
            for (String field : fields) {
                if ("Total".equals(map.get(field))) {
                    totalMap = map;
                    isTotal = true;
                    break;
                }
            }
            if (isTotal) {
                continue;
            }

            this.transformReport1RawRow(resultList, fields, map, years, months, monthHeaders);
        }
        if (totalMap != null) {
            this.transformReport1RawRow(resultList, fields, totalMap, years, months, monthHeaders);
        }

        page.setData(resultList);
        page.setTotal(resultList.size());

        return response.setBody(page);
    }

    private void transformReport1RawRow(List<Map<String, Object>> resultList, List<String> fields, Map<String, Object> totalMap, List<String> years, List<String> months, List<String> monthHeaders) {
        Map<String, Object> tempMap = new HashMap<>();

        // Ratio
        for (String field : fields) {
            tempMap.put(field, totalMap.get(field));
        }
        tempMap.put("TYPE", "RATIO");
        for (int i = 0; i < months.size(); ++i) {
            tempMap.put(monthHeaders.get(i), totalMap.get("'" + months.get(i) + "'_RATIO"));
        }
        for (String year : years) {
            tempMap.put(year, totalMap.get("'" + year + "'_RATIO"));
        }
        resultList.add(tempMap);
        // On Time
        tempMap = new HashMap<>();
        for (String field : fields) {
            tempMap.put(field, totalMap.get(field));
        }
        tempMap.put("TYPE", "On Time");
        for (int i = 0; i < months.size(); ++i) {
            tempMap.put(monthHeaders.get(i), totalMap.get("'" + months.get(i) + "'_ONTIME"));
        }
        for (String year : years) {
            tempMap.put(year, totalMap.get("'" + year + "'_ONTIME"));
        }
        resultList.add(tempMap);
        // Delay
        tempMap = new HashMap<>();
        for (String field : fields) {
            tempMap.put(field, totalMap.get(field));
        }
        tempMap.put("TYPE", "Delay");
        for (int i = 0; i < months.size(); ++i) {
            tempMap.put(monthHeaders.get(i), totalMap.get("'" + months.get(i) + "'_DELAYED"));
        }
        for (String year : years) {
            tempMap.put(year, totalMap.get("'" + year + "'_DELAYED"));
        }
        resultList.add(tempMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap, "OTDC_DC_SOURCE_V");
        this.generateReport1DetailsFilter(parameterMap);
        return response.setBody(otdcDao.queryReport2(parameterMap));
    }

    private void generateReport1DetailsFilter(Map<String, Object> parameterMap) {
        String selectedColumn = (String) parameterMap.get("report1SelectedColumn");
        if ("-1".equals(selectedColumn)) {
            parameterMap.put("months", parameterMap.get("selectedMonths"));
            parameterMap.put("dateField", "OTDC_MONTH");
        } else {
            List<String> temp = new ArrayList<>();
            temp.add(selectedColumn);
            if (selectedColumn.length() == 6) {
                parameterMap.put("months", temp);
                parameterMap.put("dateField", "OTDC_MONTH");
            } else {
                temp.add(selectedColumn);
                parameterMap.put("years", temp);
                parameterMap.put("dateField", "OTDC_YEAR");
            }
        }

        List<String> ontimeCategories = new ArrayList<>();
        parameterMap.put("ontimeCategories", ontimeCategories);
        String type = (String) parameterMap.get("report1SelectedType");
        switch (type) {
            case "RATIO" -> {
                ontimeCategories.add("O");
                ontimeCategories.add("D");
            }
            case "On Time" -> ontimeCategories.add("O");
            case "Delay" -> ontimeCategories.add("D");
        }
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1Details(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateCascaderFilter(parameterMap, "OTDC_DC_SOURCE_V");
        this.generateReport1DetailsFilter(parameterMap);

        page.setTotal(otdcDao.queryReport1DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(otdcDao.queryReport1Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateCascaderFilter(parameterMap, "OTDC_DC_SOURCE_V");
        this.generateReport1DetailsFilter(parameterMap);

        String fileName = "otdc_monthly_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.customer.dao.IOTDCDao.queryReport1Details", parameterMap);
    }

    @Override
    @SuppressWarnings("unchecked")
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap, "OTDC_DC_COMPARE_V");
        List<String> fields = (List<String>) parameterMap.get("selectedField");

        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        String sort = page.getSort();
        if (fields.contains(page.getSortColumn()) == false) {
            sort = sort.replace(
                    "\"", "\"'"
            ).replace(
                    "/", ""
            ).replace(
                    "\"' ", "'_RATIO\" "
            );
            page.setSort(sort);
        }

        List<String> weeks = (List<String>) parameterMap.get("selectedWeeks");
        parameterMap.put("weeks", weeks);
        List<Map<String, Object>> dataList = otdcDao.queryReport3(parameterMap);
        List<Map<String, Object>> resultList = new ArrayList<>();

        Map<String, Object> totalMap = null;
        for (Map<String, Object> map : dataList) {
            boolean isTotal = false;
            for (String field : fields) {
                if ("Total".equals(map.get(field))) {
                    totalMap = map;
                    isTotal = true;
                    break;
                }
            }
            if (isTotal) {
                continue;
            }

            this.transformReport3RawRow(resultList, fields, map, weeks);
        }
        if (totalMap != null) {
            this.transformReport3RawRow(resultList, fields, totalMap, weeks);
        }

        page.setData(resultList);
        page.setTotal(resultList.size());

        return response.setBody(page);
    }

    private void transformReport3RawRow(List<Map<String, Object>> resultList, List<String> fields, Map<String, Object> map, List<String> weeks) {
        Map<String, Object> tempMap = new HashMap<>();

        // Ratio
        for (String field : fields) {
            tempMap.put(field, map.get(field));
        }
        tempMap.put("TYPE", "RATIO");
        for (String week : weeks) {
            tempMap.put(week, map.get("'" + week + "'_RATIO"));
        }
        resultList.add(tempMap);
        // On Time
        tempMap = new HashMap<>();
        for (String field : fields) {
            tempMap.put(field, map.get(field));
        }
        tempMap.put("TYPE", "On Time");
        for (String week : weeks) {
            tempMap.put(week, map.get("'" + week + "'_ONTIME"));
        }
        resultList.add(tempMap);
        // Delay
        tempMap = new HashMap<>();
        for (String field : fields) {
            tempMap.put(field, map.get(field));
        }
        tempMap.put("TYPE", "Delay");
        for (String week : weeks) {
            tempMap.put(week, map.get("'" + week + "'_DELAYED"));
        }
        resultList.add(tempMap);
    }

    @SuppressWarnings("unchecked")
    private void generateReport3DetailsFilter(Map<String, Object> parameterMap) {
        String selectedColumn = (String) parameterMap.get("report1SelectedColumn");

        List<String> columns = new ArrayList<>();
        if ("-1".equals(selectedColumn)) {
            columns = (List<String>) parameterMap.get("selectedWeeks");
        } else {
            columns.add(selectedColumn);
        }
        parameterMap.put("weeks", columns);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Details(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateCascaderFilter(parameterMap, "OTDC_DC_COMPARE_V");
        this.generateReport3DetailsFilter(parameterMap);

        page.setTotal(otdcDao.queryReport3DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(otdcDao.queryReport3Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateCascaderFilter(parameterMap, "OTDC_DC_COMPARE_V");
        this.generateReport3DetailsFilter(parameterMap);

        String fileName = "otdc_weekly_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.customer.dao.IOTDCDao.queryReport3Details", parameterMap);
    }

    /**
     * 生成cascader filter
     *
     * @param parameterMap 参数map
     */
    @SuppressWarnings("unchecked")
    private void generateCascaderFilter(Map<String, Object> parameterMap, String targetTable, String... blackList) {
        // 生成筛选条件
        this.generateCascaderFilterSQL(parameterMap, null, targetTable, "T", "_filters", blackList);

        List<String> defaultField = new ArrayList<>();
        defaultField.add("ENTITY");
        List<String> fields = (List<String>) parameterMap.get("selectedField");
        if (fields.isEmpty()) {
            parameterMap.put("selectedField", defaultField);
        } else {
            for (String field : fields) {
                if (Utils.hasInjectionAttack(field)) {
                    parameterMap.put("selectedField", defaultField);
                    break;
                }
            }
        }
    }
}
