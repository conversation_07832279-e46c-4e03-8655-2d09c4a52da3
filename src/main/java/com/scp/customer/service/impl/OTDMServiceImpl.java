package com.scp.customer.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.scp.customer.bean.OTDMReport2Result;
import com.scp.customer.dao.IOTDMDao;
import com.scp.customer.service.IOTDMService;
import com.starter.context.bean.CacheRemove;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import com.starter.utils.excel.SheetInfoWithData;
import com.starter.utils.excel.SheetInfoWithQueryKey;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Scope("prototype")
@Transactional
public class OTDMServiceImpl extends ServiceHelper implements IOTDMService {

    @Resource
    private Response response;

    @Resource
    private IOTDMDao otdmDataDao;

    @Resource
    private ExcelTemplate excelTemplate;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1Columns(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = this.queryReport1ColumnsSource(parameterMap);
        resultMap.put("rcaCode", otdmDataDao.queryRacCode());

        List<LinkedHashMap<String, Object>> tipsList = otdmDataDao.queryOtdmRcaTips();
        Map<String, String> tipsMap = new HashMap<>();
        for (Map<String, Object> map : tipsList) {
            tipsMap.put(String.valueOf(map.get("CODE")), (String) map.get("DESCRIPTION"));
        }
        resultMap.put("rcaTips", tipsMap);

        return response.setBody(resultMap);
    }

    @Override
    @SuppressWarnings("unchecked")
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);

        parameterMap.putAll(this.queryReport1ColumnsSource(parameterMap));
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);

        List<String> fields = (List<String>) parameterMap.get("fields");
        List<String> years = (List<String>) parameterMap.get("years");
        List<String> months = (List<String>) parameterMap.get("months");
        List<String> weeks = (List<String>) parameterMap.get("weeks");

        parameterMap.put("defaultOrderColumn", years.get(0));

        List<Map<String, Object>> dataList = otdmDataDao.queryReport1(parameterMap);

        List<String> columns = years.stream().map(e -> "Y" + e).collect(Collectors.toList());
        columns.addAll(months.stream().map(e -> "M" + e).toList());
        columns.addAll(weeks.stream().map(e -> "W" + e).toList());


        List<Map<String, Object>> resultList = new ArrayList<>();

        Map<String, Object> ontimeTotal = new HashMap<>();
        Map<String, Object> delayTotal = new HashMap<>();
        Map<String, Object> ratioTotal = new HashMap<>();

        ontimeTotal.put("TYPE", "ONTIME");
        delayTotal.put("TYPE", "DELAY");
        ratioTotal.put("TYPE", "RATIO");

        String tk = fields.get(fields.size() - 1);
        ontimeTotal.put(tk, "Total");
        delayTotal.put(tk, "Total");
        ratioTotal.put(tk, "Total");

        for (Map<String, Object> data : dataList) {
            Map<String, Object> ontime = new HashMap<>();
            Map<String, Object> delay = new HashMap<>();
            Map<String, Object> ratio = new HashMap<>();

            ontime.put("TYPE", "ONTIME");
            delay.put("TYPE", "DELAY");
            ratio.put("TYPE", "RATIO");

            for (String field : fields) {
                ontime.put(field, data.get(field));
                delay.put(field, data.get(field));
                ratio.put(field, data.get(field));
            }
            for (String column : columns) {
                ontime.put(column, data.get(column + "_ONTIME"));
                delay.put(column, data.get(column + "_DELAY"));
                ratio.put(column, data.get(column + "_RATIO"));
                delay.put(column + "_CONFIRMED", data.get(column + "_CONFIRMED"));

                BigDecimal t = Utils.parseBigDecimal(ontimeTotal.getOrDefault(column, BigDecimal.ZERO));
                ontimeTotal.put(column, t.add(Utils.parseBigDecimal(data.get(column + "_ONTIME"))));

                t = Utils.parseBigDecimal(delayTotal.getOrDefault(column, BigDecimal.ZERO));
                delayTotal.put(column, t.add(Utils.parseBigDecimal(data.get(column + "_DELAY"))));

                t = Utils.parseBigDecimal(delayTotal.getOrDefault(column + "_CONFIRMED", BigDecimal.ZERO));
                delayTotal.put(column + "_CONFIRMED", t.add(Utils.parseBigDecimal(data.get(column + "_CONFIRMED"))));
            }

            resultList.add(ratio);
            resultList.add(ontime);
            resultList.add(delay);
        }

        for (String column : columns) {
            BigDecimal ontime = Utils.parseBigDecimal(ontimeTotal.getOrDefault(column, BigDecimal.ZERO));
            BigDecimal delay = Utils.parseBigDecimal(delayTotal.getOrDefault(column, BigDecimal.ZERO));
            BigDecimal total = ontime.add(delay);
            if (total.compareTo(BigDecimal.ZERO) == 0) {
                ratioTotal.put(column, "");
            } else {
                ratioTotal.put(column, ontime.divide(total, 3, RoundingMode.HALF_UP));
            }
        }
        resultList.add(ratioTotal);
        resultList.add(ontimeTotal);
        resultList.add(delayTotal);

        page.setTotal(resultList.size());
        page.setData(resultList);
        return response.setBody(page);
    }

    @Override
    @SuppressWarnings("unchecked")
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1Details(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);

        this.generateReport1Columns(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(otdmDataDao.queryReport1DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(otdmDataDao.queryReport1Details(parameterMap));
        }
        return response.setBody(page);
    }

    private void generateReport1Columns(Map<String, Object> parameterMap) {
        List<String> report1SelectedValues = (List<String>) parameterMap.get("report1SelectedValues");
        if (report1SelectedValues.size() > 1) {
            String column = report1SelectedValues.get(1);
            if (column.startsWith("Y2")) {
                parameterMap.put("report1DetailsDateColumn", parameterMap.get("CALENDAR_YEAR"));
                parameterMap.put("report1DetailsDateValue", StringUtils.removeStart(column, "Y"));
            } else if (column.startsWith("M2")) {
                parameterMap.put("report1DetailsDateColumn", parameterMap.get("CALENDAR_MONTH"));
                parameterMap.put("report1DetailsDateValue", StringUtils.removeStart(column, "M"));
            } else if (column.startsWith("W2")) {
                parameterMap.put("report1DetailsDateColumn", parameterMap.get("CALENDAR_WEEK"));
                parameterMap.put("report1DetailsDateValue", StringUtils.removeStart(column, "W"));
            }
        }
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response saveOtdmReport1Details(Map<String, Object> parameterMap) {
        Object obj = parameterMap.get("report1DetailsUpdate");
        if (obj instanceof JSONObject) {
            JSONObject jsonObj = (JSONObject) obj;
            List<Map<String, Object>> dataList = new ArrayList<>();
            for (String key : jsonObj.keySet()) {
                JSONObject changeObj = jsonObj.getJSONObject(key);
                Map<String, Object> map = new HashMap<>();
                String[] keys = key.split("#");
                map.put("order", keys[0]);
                map.put("item", keys[1]);
                map.put("rca_result", changeObj.getString("RCA_RESULT"));
                map.put("rca_comments", changeObj.getString("RCA_COMMENTS"));
                map.put("type", 'W');
                dataList.add(map);
            }
            if (dataList.isEmpty() == false) {
                parameterMap.put("dataList", dataList);
                otdmDataDao.saveOtdmRCAResult(parameterMap);
            }
        }
        return response;
    }

    private LinkedHashMap<String, Object> processData(Map<String, Object> data, String prefix, Set<String> excludeKeys) {
        LinkedHashMap<String, Object> result = new LinkedHashMap<>();
        data.forEach((key, value) -> {
            if (!excludeKeys.contains(key)) {
                result.put(prefix + key, value);
            }
        });
        return result;
    }

    @Override
    @SuppressWarnings("unchecked")
    public void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        this.generateCascaderFilter(parameterMap);
        this.generateReport1Columns(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "otdm_data_" + Utils.randomStr(4) + ".xlsx";

        SheetInfoWithQueryKey sheet1 = new SheetInfoWithQueryKey();
        sheet1.setParameterMap(parameterMap);
        sheet1.setQueryKey("com.scp.customer.dao.IOTDMDao.queryReport1Details");
        sheet1.setSheetName("Daily Details");

        List<LinkedHashMap<String, Object>> otdmRcaTips = otdmDataDao.queryOtdmRcaTips();
        List<LinkedHashMap<String, Object>> codeDataList = new ArrayList<>();
        List<LinkedHashMap<String, Object>> tipsDataList = new ArrayList<>();

        for (LinkedHashMap<String, Object> row : otdmRcaTips) {
            String source = (String) row.get("SOURCE");
            if ("RCA_TIPS".equals(source)) {
                tipsDataList.add(processData(row, "RCA_TIPS_", Set.of("SOURCE")));
            } else if ("RCA_CODE".equals(source)) {
                codeDataList.add(processData(row, "RCA_", Set.of("COMPUTING_LOGIC", "SOURCE")));
            }
        }

        SheetInfoWithData sheet2 = new SheetInfoWithData();
        String sheet2Name = "RCA Code Description";
        sheet2.setDataList(codeDataList);
        sheet2.setSheetName(sheet2Name);

        SheetInfoWithData sheet3 = new SheetInfoWithData();
        String sheet3Name = "RCA Tips Description";
        sheet3.setDataList(tipsDataList);
        sheet3.setSheetName(sheet3Name);

        excelTemplate.create(response, fileName, sheet1, sheet2, sheet3);
    }

    @Override
    @SuppressWarnings("unchecked")
    public Response queryReport2(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);
        parameterMap.putAll(this.queryReport1ColumnsSource(parameterMap));

        List<String> xAxis;
        if ("By Month".equals(parameterMap.get("report2DateType"))) {
            xAxis = (List<String>) parameterMap.get("months");
            parameterMap.put("dateColumn", parameterMap.get("CALENDAR_MONTH"));
        } else {
            xAxis = (List<String>) parameterMap.get("weeks");
            parameterMap.put("dateColumn", parameterMap.get("CALENDAR_WEEK"));
        }

        OTDMReport2Result result = new OTDMReport2Result();
        List<Map<String, Object>> dataList = otdmDataDao.queryReport2(parameterMap);
        Map<String, Map<String, BigDecimal>> dataMap = new HashMap<>();

        for (Map<String, Object> map : dataList) {
            String key = (String) map.get("xAxis");
            String key2 = (String) map.get("RANGE");
            Map<String, BigDecimal> submap = dataMap.computeIfAbsent(key, k -> new HashMap<>());
            submap.put(key2, Utils.parseBigDecimal(map.get("CNT")));
        }

        for (String x : xAxis) {
            result.setxAxis(xAxis);
            result.setYAxis(dataMap.get(x));
        }

        return response.setBody(result);
    }

    @SuppressWarnings("unchecked")
    private void generateCascaderFilter(Map<String, Object> parameterMap) {
        parameterMap.put("CALENDAR_YEAR", "CALENDAR_YEAR");
        parameterMap.put("CALENDAR_MONTH", "CALENDAR_MONTH");
        parameterMap.put("CALENDAR_WEEK", "CALENDAR_WEEK");
        String otdmType = (String) parameterMap.get("otdmType");
        if (StringUtils.equalsIgnoreCase(otdmType, "OTDM Commit")) {
            parameterMap.put("otdmTypeColumn", "DELAY_RANGE_COMMIT");
        } else if (StringUtils.equalsIgnoreCase(otdmType, "OTDM")) {
            parameterMap.put("otdmTypeColumn", "OTDM");
        } else if (StringUtils.equalsIgnoreCase(otdmType, "OTDM 5WD")) {
            parameterMap.put("otdmTypeColumn", "OTDM_DELAY_WITHIN_5WD");
            parameterMap.put("CALENDAR_YEAR", "CALENDAR_NEXT_5WD_YEAR");
            parameterMap.put("CALENDAR_MONTH", "CALENDAR_NEXT_5WD_MONTH");
            parameterMap.put("CALENDAR_WEEK", "CALENDAR_NEXT_5WD_WEEK");
        }

        this.generateCascaderFilterSQL(parameterMap);

        List<String> fields = (List<String>) parameterMap.get("fields");
        for (String field : fields) {
            if (Utils.hasInjectionAttack(field)) {
                fields = new ArrayList<>();
                fields.add("ENTITY");
                break;
            }
        }
        if (fields.isEmpty()) {
            fields.add("ENTITY");
        }
    }

    /**
     * calc weeks and days between start month and end month
     *
     * @param parameterMap parameters
     * @return result map
     */
    private Map<String, Object> queryReport1ColumnsSource(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();

        resultMap.put("years", otdmDataDao.queryReport1YearList(parameterMap));
        resultMap.put("months", otdmDataDao.queryReport1MonthList(parameterMap));
        resultMap.put("weeks", otdmDataDao.queryReport1WeekList(parameterMap));

        return resultMap;
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryOtdmRcaTipsList(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(otdmDataDao.queryOtdmRcaTipsListCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(otdmDataDao.queryOtdmRcaTipsList(parameterMap));
        }
        return response.setBody(page);
    }
}
