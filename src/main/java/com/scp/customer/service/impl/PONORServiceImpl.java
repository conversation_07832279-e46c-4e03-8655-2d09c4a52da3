package com.scp.customer.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.scp.customer.bean.PONORReport1Treemap;
import com.scp.customer.bean.PONORReport1Bean;
import com.scp.customer.bean.PONORReport1Treemap;
import com.scp.customer.dao.IPONORDao;
import com.scp.customer.service.IPONORService;
import com.starter.context.StaticComponent;
import com.starter.context.bean.CacheRemove;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.awt.image.BufferedImage;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service("PONORService")
@Scope("prototype")
@Transactional
public class PONORServiceImpl extends ServiceHelper implements IPONORService {
    @Resource
    private IPONORDao poNorDao;

    @Resource
    private Response response;

    @Resource
    private ExcelTemplate excelTemplate;

    public final static String PARENT_CODE = "menu850";

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage(Map<String, Object> parameterMap, String user_id) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cascader", Utils.parseCascader(poNorDao.queryCascader(parameterMap)));

        List<LinkedHashMap<String, Object>> tipsList = poNorDao.queryPoNorRcaTips(parameterMap);

        String user_auth = poNorDao.queryAuthDetails(user_id, PARENT_CODE);
        resultMap.put("user_auth", user_auth);

        Map<String, String> tipsMap = new HashMap<>();
        for (Map<String, Object> map : tipsList) {
            String des = (String) map.get("DESCRIPTION");
            if (map.get("COMPUTING_LOGIC") != null) {
                des = ". " + map.get("COMPUTING_LOGIC");
            }
            tipsMap.put(String.valueOf(map.get("RCA_TIPS_CODE")), map.get("DESCRIPTION") + des);
        }
        resultMap.put("rcaTips", tipsMap);
        resultMap.put("rcaCode", poNorDao.queryRacCode(parameterMap));
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateReport1Tooltips(parameterMap);
        this.generateValueColumn(parameterMap);

        // 将前台传过来的label转换成列名, 同时也可以防止恶意代码注入
        parameterMap.put("level1", this.getColumnName(parameterMap.get("level1")));
        parameterMap.put("level2", this.getColumnName(parameterMap.get("level2")));
        parameterMap.put("level3", this.getColumnName(parameterMap.get("level3")));
        parameterMap.put("level4", this.getColumnName(parameterMap.get("level4")));
        parameterMap.put("level5", this.getColumnName(parameterMap.get("level5")));

        List<PONORReport1Treemap> resultList = new ArrayList<>();
        List<PONORReport1Bean> dataList = poNorDao.queryReport1(parameterMap);
        for (PONORReport1Bean data : dataList) {
            this.convertReport1Data(resultList, data);
        }

        // 遍历树, 变成一维表,方便计算
        List<PONORReport1Treemap> flatList = this.flatTree(resultList);

        // 遍历为每个节点赋颜色
        for (PONORReport1Treemap bean : flatList) {
            bean.setColor(this.getColor(bean.getK()));
        }

        // 第一层的趋势使用精确计算
        List<Map<String, Object>> level1Slope = poNorDao.queryReport1Level1Slope(parameterMap);
        Map<String, Object> level1SlopeMap = new HashMap<>();
        List<BigDecimal> positiveList = new ArrayList<>();
        List<BigDecimal> nagitiveList = new ArrayList<>();
        for (Map<String, Object> map : level1Slope) {
            BigDecimal k = Utils.parseBigDecimal(map.get("K"));
            level1SlopeMap.put((String) map.get("KEY"), k);
            if (k.compareTo(BigDecimal.ZERO) > 0) {
                positiveList.add(k);
            } else {
                nagitiveList.add(k);
            }
        }
        positiveList.sort(Comparator.naturalOrder());
        nagitiveList.sort(Comparator.reverseOrder());

        for (PONORReport1Treemap level1 : resultList) {
            BigDecimal k = Utils.parseBigDecimal(level1SlopeMap.get(level1.getName()));
            level1.setK(k.doubleValue());
            level1.setColor(this.getRangeColor(positiveList, nagitiveList, k));
        }

        // 第二层的趋势使用精确计算
        List<Map<String, Object>> level2Slope = poNorDao.queryReport1Level2Slope(parameterMap);
        Map<String, Object> level2SlopeMap = new HashMap<>();

        positiveList = new ArrayList<>();
        nagitiveList = new ArrayList<>();
        for (Map<String, Object> map : level2Slope) {
            BigDecimal k = Utils.parseBigDecimal(map.get("K"));
            level2SlopeMap.put((String) map.get("KEY"), k);
            if (k.compareTo(BigDecimal.ZERO) > 0) {
                positiveList.add(k);
            } else {
                nagitiveList.add(k);
            }
        }
        positiveList.sort(Comparator.naturalOrder());
        nagitiveList.sort(Comparator.reverseOrder());

        for (PONORReport1Treemap level1 : resultList) {
            for (PONORReport1Treemap level2 : level1.getChildren()) {
                BigDecimal k = Utils.parseBigDecimal(level2SlopeMap.get(level1.getName() + "[#]" + level2.getName()));
                level2.setK(k.doubleValue());
                level2.setColor(this.getRangeColor(positiveList, nagitiveList, k));
            }
        }

        return response.setBody(resultList);
    }


    private void convertReport1Data(List<PONORReport1Treemap> list, PONORReport1Bean data) {
        String[] categorysOrg = new String[]{data.getCategory1(), data.getCategory2(), data.getCategory3(), data.getCategory4(), data.getCategory5()};
        List<String> categories = new ArrayList<>();

        for (String category : categorysOrg) {
            if (StringUtils.isNotBlank(category)) {
                categories.add(category);
            } else {
                break;
            }
        }

        // 这边逻辑比较复杂, 所以用最笨的方法来描述了, 以免后期不好维护
        // 先把这一行数据转成treemap的数据
        // 第一个节点
        List<PONORReport1Treemap> child = new ArrayList<>();
        PONORReport1Treemap root = new PONORReport1Treemap();
        root.setName(categories.get(0));
        root.setTips(data.copyTooltips()); // 因为这个tooltips要放在树中全局使用, 所以必须要生成一个新节点
        root.setChildren(child);

        // 中间节点
        for (int i = 1; i < categories.size() - 1; i++) {
            PONORReport1Treemap treemap = new PONORReport1Treemap();
            treemap.setName(categories.get(i));
            treemap.setTips(data.copyTooltips());

            child.add(treemap);
            child = new ArrayList<>();
            treemap.setChildren(child);
        }

        // 最后一个节点
        PONORReport1Treemap lastNode = new PONORReport1Treemap();
        lastNode.setName(categories.get(categories.size() - 1));
        lastNode.setValue(data.getValue());
        lastNode.setTips(data.copyTooltips());
        child.add(lastNode);

        // 将这行treemap与原始数据相加
        // 先找到list中是否有这个数据节点
        Optional<PONORReport1Treemap> beanOpt = list.stream().filter(b -> b.getName().equals(categories.get(0))).findFirst();
        if (beanOpt.isPresent()) {
            PONORReport1Treemap bean = beanOpt.get();
            bean.add(root); // 两个节点合并
        } else { //找不到的时候最省事, 直接放入list就可以了
            list.add(root);
        }
    }

    /**
     * 广度优先遍历树
     *
     * @param tree 输入数
     * @return 遍历结果
     */
    private List<PONORReport1Treemap> flatTree(List<PONORReport1Treemap> tree) {
        List<PONORReport1Treemap> resultList = new ArrayList<>();

        // 遍历第一层
        List<PONORReport1Treemap> temp = new ArrayList<>();
        for (PONORReport1Treemap node : tree) {
            resultList.add(node);
            if (node.hasChildren()) {
                temp.addAll(node.getChildren());
            }
        }

        // 遍历剩余几层
        List<PONORReport1Treemap> temp2 = new ArrayList<>();
        while (temp.isEmpty() == false || temp2.isEmpty() == false) {
            temp2 = new ArrayList<>();
            for (PONORReport1Treemap node : temp) {
                resultList.add(node);
                if (node.hasChildren()) {
                    temp2.addAll(node.getChildren());
                }
            }

            temp = new ArrayList<>();
            for (PONORReport1Treemap node : temp2) {
                resultList.add(node);
                if (node.hasChildren()) {
                    temp.addAll(node.getChildren());
                }
            }
        }

        return resultList;
    }

    private String getRangeColor(List<BigDecimal> positiveList, List<BigDecimal> nagitiveList, BigDecimal k) {
        BufferedImage image = StaticComponent.getColorImageE();
        int width = image.getWidth();
        int middle = width / 2;
        if (nagitiveList.contains(k)) {
            int index = nagitiveList.lastIndexOf(k);
            int step = middle / nagitiveList.size();
            return "#" + Integer.toHexString(image.getRGB(Math.max(middle - step * (index + 1), 1), 1)).substring(2);
        } else if (positiveList.contains(k)) {
            int index = positiveList.lastIndexOf(k);
            int step = middle / positiveList.size();
            return "#" + Integer.toHexString(image.getRGB(Math.min(middle + step * (index + 1), width - 1), 1)).substring(2);
        }
        return "#000000";
    }

    // 根据色卡和最大最小值, 获取当前颜色
    int imageWidth = -1;
    int imageMiddle = -1;
    String middleColor = null;

    private String getColor(double k) {
        double minK = -15;
        double maxK = 15;
        BufferedImage image = StaticComponent.getColorImageE();
        if (imageWidth == -1) {
            imageWidth = image.getWidth();
            imageMiddle = imageWidth / 2;
            middleColor = "#" + Integer.toHexString(image.getRGB(imageMiddle, 1)).substring(2);
        }

        if (Math.abs(k) > 1) {
            int t = k < 0 ? -1 : 1;
            k = Math.log(Math.abs(k)) * t;
        }

        if (k == 0) {
            return middleColor;
        }

        k = Math.min(k, maxK);
        k = Math.max(k, minK);

        if (k > 0) {
            int index = (int) (imageMiddle - imageMiddle * k / maxK);
            return "#" + Integer.toHexString(image.getRGB(Math.max(index, 1), 1)).substring(2);
        } else {
            int index = (int) (imageMiddle + imageMiddle * k / minK);
            return "#" + Integer.toHexString(image.getRGB(Math.min(index, imageWidth - 1), 1)).substring(2);
        }
    }


    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        String report2DateType = (String) parameterMap.get("report2DateType");
        if (StringUtils.equals(report2DateType, "By Month")) {
            parameterMap.put("dateColumn", "CALENDAR_MONTH");
        } else if (StringUtils.equals(report2DateType, "By Quarter")) {
            parameterMap.put("dateColumn", "CALENDAR_QUARTER");
        } else if (StringUtils.equals(report2DateType, "By Year")) {
            parameterMap.put("dateColumn", "CALENDAR_YEAR");
        } else {
            parameterMap.put("dateColumn", "CALENDAR_WEEK");
        }

        String report2ViewType = (String) parameterMap.get("report2ViewType");

        Map<String, Object> resultMap = new HashMap<>();

        List<String> xAxis = poNorDao.queryReport2XAxis(parameterMap);

        parameterMap.put("xAxis", xAxis);

        Map<String, Object> lineData = poNorDao.queryReport2Line(parameterMap);
        Map<String, Object> replacedMap = new HashMap<>();
        if (lineData != null) {
            for (Map.Entry<String, Object> entry : lineData.entrySet()) {
                String newKey = entry.getKey().replace("'", "");
                replacedMap.put(newKey, entry.getValue());
            }
        }
        List<String> lineResult = new ArrayList<>();
        for (String x : xAxis) {
            lineResult.add(replacedMap.get(x).toString());
        }
        resultMap.put("lineData", lineResult);

        List<Map<String, Object>> yAxisData = poNorDao.queryReport2YAxis(parameterMap);
        resultMap.put("xAxis", xAxis);

        Map<String, Map<String, BigDecimal>> yAxisMap = new HashMap<>();
        for (Map<String, Object> data : yAxisData) {
            String range = (String) data.get(report2ViewType);
            Object yAxis = data.get("yAxis");
            String y = yAxis.toString();
            Map<String, BigDecimal> tempMap = yAxisMap.computeIfAbsent(range, key -> new HashMap<>());
            tempMap.put(y, Utils.parseBigDecimal(data.get("VAL")));
        }

        Map<String, List<BigDecimal>> yAxis = new HashMap<>();
        for (String key : yAxisMap.keySet()) {
            Map<String, BigDecimal> val = yAxisMap.get(key);
            List<BigDecimal> tempList = yAxis.computeIfAbsent(key, k -> new ArrayList<>());
            for (String x : xAxis) {
                tempList.add(val.get(x));
            }
        }
        resultMap.put("yAxis", yAxis);
        return response.setBody(resultMap);
    }

    @Override
    public Response queryReport2Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        String report2SelectedType = (String) parameterMap.get("report2SelectedType");
        String reportCalType = (String) parameterMap.get("reportCalType");
        String report2SelectedTypeResult = "";
        if ("Fail(%)".equalsIgnoreCase(report2SelectedType)) {
            if ("NOR".equalsIgnoreCase(reportCalType)) {
                report2SelectedTypeResult = "('RC=4~7', 'RC=8~14', 'RC>14')";
            } else {
                report2SelectedTypeResult = "('EROC=2~3', 'EROC=4~7', 'EROC=8~14', 'EROC>14')";
            }
        } else {
            report2SelectedTypeResult = "('" + report2SelectedType + "')";
        }

        parameterMap.put("report2SelectedType", report2SelectedTypeResult);

        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);

        page.setTotal(poNorDao.queryReport2DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(poNorDao.queryReport2Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport2Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "po_nor_details_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.customer.dao.IPONORDao.queryReport2Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Columns(Map<String, Object> parameterMap) {
        return response.setBody(this.queryReport3ColumnsSource(parameterMap));
    }

    @Override
    @SuppressWarnings("unchecked")
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        parameterMap.putAll(this.queryReport3ColumnsSource(parameterMap));
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);

        List<String> fields = (List<String>) parameterMap.get("fields");
        List<String> years = (List<String>) parameterMap.get("years");
        List<String> months = (List<String>) parameterMap.get("months");
        List<String> weeks = (List<String>) parameterMap.get("weeks");

        String sort = page.getSort();
        if (!fields.contains(page.getSortColumn())) {
            sort = sort.replace("\" ", "_RATIO\" ");
            page.setSort(sort);
        }

        List<String> columns = years.stream().map(e -> "Y" + e).collect(Collectors.toList());
        columns.addAll(months.stream().map(e -> "M" + e).toList());
        columns.addAll(weeks.stream().map(e -> "W" + e).toList());

        List<Map<String, Object>> resultList = new ArrayList<>();

        String reportCalType = (String) parameterMap.get("reportCalType");

        if (reportCalType.equalsIgnoreCase("NOR")) {
            List<Map<String, Object>> dataList = poNorDao.queryReport3NOR(parameterMap);
            Map<String, Object> norZeroTotal = new HashMap<>();
            Map<String, Object> norThreeTotal = new HashMap<>();
            Map<String, Object> norSevenTotal = new HashMap<>();
            Map<String, Object> norFourteenTotal = new HashMap<>();
            Map<String, Object> norInfiniteTotal = new HashMap<>();
            Map<String, Object> ratioTotal = new HashMap<>();

            norZeroTotal.put("TYPE", "RC=0");
            norThreeTotal.put("TYPE", "RC=1~3");
            norSevenTotal.put("TYPE", "RC=4~7");
            norFourteenTotal.put("TYPE", "RC=8~14");
            norInfiniteTotal.put("TYPE", "RC>14");
            ratioTotal.put("TYPE", "RATIO");

            String tk = fields.get(fields.size() - 1);
            norZeroTotal.put(tk, "Total");
            norThreeTotal.put(tk, "Total");
            norSevenTotal.put(tk, "Total");
            norFourteenTotal.put(tk, "Total");
            norInfiniteTotal.put(tk, "Total");
            ratioTotal.put(tk, "Total");

            for (Map<String, Object> data : dataList) {
                Map<String, Object> norZero = new HashMap<>();
                Map<String, Object> norThree = new HashMap<>();
                Map<String, Object> norSeven = new HashMap<>();
                Map<String, Object> norFourteen = new HashMap<>();
                Map<String, Object> norInfinite = new HashMap<>();
                Map<String, Object> ratio = new HashMap<>();

                norZero.put("TYPE", "RC=0");
                norThree.put("TYPE", "RC=1~3");
                norSeven.put("TYPE", "RC=4~7");
                norFourteen.put("TYPE", "RC=8~14");
                norInfinite.put("TYPE", "RC>14");
                ratio.put("TYPE", "RATIO");

                for (String field : fields) {
                    norZero.put(field, data.get(field));
                    norThree.put(field, data.get(field));
                    norSeven.put(field, data.get(field));
                    norFourteen.put(field, data.get(field));
                    norInfinite.put(field, data.get(field));
                    ratio.put(field, data.get(field));
                }
                for (String column : columns) {
                    norZero.put(column, data.get(column + "_RC=0"));
                    norThree.put(column, data.get(column + "_RC=1~3"));
                    norSeven.put(column, data.get(column + "_RC=4~7"));
                    norFourteen.put(column, data.get(column + "_RC=8~14"));
                    norInfinite.put(column, data.get(column + "_RC>14"));
                    norSeven.put(column + "_CONFIRMED", data.get(column + "_RC=4~7_CONFIRMED"));
                    norFourteen.put(column + "_CONFIRMED", data.get(column + "_RC=8~14_CONFIRMED"));
                    norInfinite.put(column + "_CONFIRMED", data.get(column + "_RC>14_CONFIRMED"));
                    ratio.put(column, data.get(column + "_RATIO"));

                    BigDecimal t = Utils.parseBigDecimal(norZeroTotal.getOrDefault(column, BigDecimal.ZERO));
                    norZeroTotal.put(column, t.add(Utils.parseBigDecimal(data.get(column + "_RC=0"))));
                    t = Utils.parseBigDecimal(norThreeTotal.getOrDefault(column, BigDecimal.ZERO));
                    norThreeTotal.put(column, t.add(Utils.parseBigDecimal(data.get(column + "_RC=1~3"))));
                    t = Utils.parseBigDecimal(norSevenTotal.getOrDefault(column, BigDecimal.ZERO));
                    norSevenTotal.put(column, t.add(Utils.parseBigDecimal(data.get(column + "_RC=4~7"))));
                    t = Utils.parseBigDecimal(norFourteenTotal.getOrDefault(column, BigDecimal.ZERO));
                    norFourteenTotal.put(column, t.add(Utils.parseBigDecimal(data.get(column + "_RC=8~14"))));
                    t = Utils.parseBigDecimal(norInfiniteTotal.getOrDefault(column, BigDecimal.ZERO));
                    norInfiniteTotal.put(column, t.add(Utils.parseBigDecimal(data.get(column + "_RC>14"))));

                    t = Utils.parseBigDecimal(norSevenTotal.getOrDefault(column + "_CONFIRMED", BigDecimal.ZERO));
                    norSevenTotal.put(column + "_CONFIRMED", t.add(Utils.parseBigDecimal(data.get(column + "_RC=4~7_CONFIRMED"))));
                    t = Utils.parseBigDecimal(norFourteenTotal.getOrDefault(column + "_CONFIRMED", BigDecimal.ZERO));
                    norFourteenTotal.put(column + "_CONFIRMED", t.add(Utils.parseBigDecimal(data.get(column + "_RC=8~14_CONFIRMED"))));
                    t = Utils.parseBigDecimal(norInfiniteTotal.getOrDefault(column + "_CONFIRMED", BigDecimal.ZERO));
                    norInfiniteTotal.put(column + "_CONFIRMED", t.add(Utils.parseBigDecimal(data.get(column + "_RC>14_CONFIRMED"))));
                }
                resultList.add(ratio);
                resultList.add(norZero);
                resultList.add(norThree);
                resultList.add(norSeven);
                resultList.add(norFourteen);
                resultList.add(norInfinite);
            }

            for (String column : columns) {
                BigDecimal norZero = Utils.parseBigDecimal(norZeroTotal.getOrDefault(column, BigDecimal.ZERO));
                BigDecimal norThree = Utils.parseBigDecimal(norThreeTotal.getOrDefault(column, BigDecimal.ZERO));
                BigDecimal norSeven = Utils.parseBigDecimal(norSevenTotal.getOrDefault(column, BigDecimal.ZERO));
                BigDecimal norFourteen = Utils.parseBigDecimal(norFourteenTotal.getOrDefault(column, BigDecimal.ZERO));
                BigDecimal norInfinite = Utils.parseBigDecimal(norInfiniteTotal.getOrDefault(column, BigDecimal.ZERO));
                BigDecimal total = norZero.add(norThree).add(norSeven).add(norFourteen).add(norInfinite);
                if (total.compareTo(BigDecimal.ZERO) == 0) {
                    ratioTotal.put(column, "");
                } else {
                    ratioTotal.put(column, (norSeven.add(norInfinite).add(norFourteen)).divide(total, 3, RoundingMode.HALF_UP));
                }
            }

            resultList.add(ratioTotal);
            resultList.add(norZeroTotal);
            resultList.add(norThreeTotal);
            resultList.add(norSevenTotal);
            resultList.add(norFourteenTotal);
            resultList.add(norInfiniteTotal);
        } else {
            List<Map<String, Object>> dataList = poNorDao.queryReport3NORO(parameterMap);
            Map<String, Object> norZeroTotal = new HashMap<>();
            Map<String, Object> norOneTotal = new HashMap<>();
            Map<String, Object> norThreeTotal = new HashMap<>();
            Map<String, Object> norSevenTotal = new HashMap<>();
            Map<String, Object> norFourteenTotal = new HashMap<>();
            Map<String, Object> norOthersTotal = new HashMap<>();
            Map<String, Object> ratioTotal = new HashMap<>();

            norZeroTotal.put("TYPE", "ON TARGET");
            norOneTotal.put("TYPE", "EROC=2~3");
            norThreeTotal.put("TYPE", "EROC=4~7");
            norSevenTotal.put("TYPE", "EROC=8~14");
            norFourteenTotal.put("TYPE", "EROC>14");
            norOthersTotal.put("TYPE", "Others");
            ratioTotal.put("TYPE", "RATIO");

            String tk = fields.get(fields.size() - 1);
            norZeroTotal.put(tk, "Total");
            norOneTotal.put(tk, "Total");
            norThreeTotal.put(tk, "Total");
            norSevenTotal.put(tk, "Total");
            norFourteenTotal.put(tk, "Total");
            norOthersTotal.put(tk, "Total");
            ratioTotal.put(tk, "Total");

            for (Map<String, Object> data : dataList) {
                Map<String, Object> norZero = new HashMap<>();
                Map<String, Object> norOne = new HashMap<>();
                Map<String, Object> norThree = new HashMap<>();
                Map<String, Object> norSeven = new HashMap<>();
                Map<String, Object> norFourteen = new HashMap<>();
                Map<String, Object> norOthers = new HashMap<>();
                Map<String, Object> ratio = new HashMap<>();

                norZero.put("TYPE", "ON TARGET");
                norOne.put("TYPE", "EROC=2~3");
                norThree.put("TYPE", "EROC=4~7");
                norSeven.put("TYPE", "EROC=8~14");
                norFourteen.put("TYPE", "EROC>14");
                norOthers.put("TYPE", "Others");
                ratio.put("TYPE", "RATIO");

                for (String field : fields) {
                    norZero.put(field, data.get(field));
                    norOne.put(field, data.get(field));
                    norThree.put(field, data.get(field));
                    norSeven.put(field, data.get(field));
                    norFourteen.put(field, data.get(field));
                    norOthers.put(field, data.get(field));
                    ratio.put(field, data.get(field));
                }
                for (String column : columns) {
                    norZero.put(column, data.get(column + "_ON TARGET"));
                    norOne.put(column, data.get(column + "_EROC=2~3"));
                    norThree.put(column, data.get(column + "_EROC=4~7"));
                    norSeven.put(column, data.get(column + "_EROC=8~14"));
                    norFourteen.put(column, data.get(column + "_EROC>14"));
                    norOthers.put(column, data.get(column + "_Others"));
                    norOne.put(column + "_CONFIRMED", data.get(column + "_EROC=2~3_CONFIRMED"));
                    norThree.put(column + "_CONFIRMED", data.get(column + "_EROC=4~7_CONFIRMED"));
                    norSeven.put(column + "_CONFIRMED", data.get(column + "_EROC=8~14_CONFIRMED"));
                    norFourteen.put(column + "_CONFIRMED", data.get(column + "_EROC>14_CONFIRMED"));
                    ratio.put(column, data.get(column + "_RATIO"));

                    BigDecimal t = Utils.parseBigDecimal(norZeroTotal.getOrDefault(column, BigDecimal.ZERO));
                    norZeroTotal.put(column, t.add(Utils.parseBigDecimal(data.get(column + "_ON TARGET"))));
                    t = Utils.parseBigDecimal(norOneTotal.getOrDefault(column, BigDecimal.ZERO));
                    norOneTotal.put(column, t.add(Utils.parseBigDecimal(data.get(column + "_EROC=2~3"))));
                    t = Utils.parseBigDecimal(norThreeTotal.getOrDefault(column, BigDecimal.ZERO));
                    norThreeTotal.put(column, t.add(Utils.parseBigDecimal(data.get(column + "_EROC=4~7"))));
                    t = Utils.parseBigDecimal(norSevenTotal.getOrDefault(column, BigDecimal.ZERO));
                    norSevenTotal.put(column, t.add(Utils.parseBigDecimal(data.get(column + "_EROC=8~14"))));
                    t = Utils.parseBigDecimal(norFourteenTotal.getOrDefault(column, BigDecimal.ZERO));
                    norFourteenTotal.put(column, t.add(Utils.parseBigDecimal(data.get(column + "_EROC>14"))));
                    t = Utils.parseBigDecimal(norOthersTotal.getOrDefault(column, BigDecimal.ZERO));
                    norOthersTotal.put(column, t.add(Utils.parseBigDecimal(data.get(column + "_Others"))));

                    t = Utils.parseBigDecimal(norOneTotal.getOrDefault(column + "_CONFIRMED", BigDecimal.ZERO));
                    norOneTotal.put(column + "_CONFIRMED", t.add(Utils.parseBigDecimal(data.get(column + "_EROC=2~3_CONFIRMED"))));
                    t = Utils.parseBigDecimal(norThreeTotal.getOrDefault(column + "_CONFIRMED", BigDecimal.ZERO));
                    norThreeTotal.put(column + "_CONFIRMED", t.add(Utils.parseBigDecimal(data.get(column + "_EROC=4~7_CONFIRMED"))));
                    t = Utils.parseBigDecimal(norSevenTotal.getOrDefault(column + "_CONFIRMED", BigDecimal.ZERO));
                    norSevenTotal.put(column + "_CONFIRMED", t.add(Utils.parseBigDecimal(data.get(column + "_EROC=8~14_CONFIRMED"))));
                    t = Utils.parseBigDecimal(norFourteenTotal.getOrDefault(column + "_CONFIRMED", BigDecimal.ZERO));
                    norFourteenTotal.put(column + "_CONFIRMED", t.add(Utils.parseBigDecimal(data.get(column + "_EROC>14_CONFIRMED"))));
                }
                resultList.add(ratio);
                resultList.add(norZero);
                resultList.add(norOne);
                resultList.add(norThree);
                resultList.add(norSeven);
                resultList.add(norFourteen);
                resultList.add(norOthers);
            }

            for (String column : columns) {
                BigDecimal norZero = Utils.parseBigDecimal(norZeroTotal.getOrDefault(column, BigDecimal.ZERO));
                BigDecimal norOne = Utils.parseBigDecimal(norOneTotal.getOrDefault(column, BigDecimal.ZERO));
                BigDecimal norThree = Utils.parseBigDecimal(norThreeTotal.getOrDefault(column, BigDecimal.ZERO));
                BigDecimal norSeven = Utils.parseBigDecimal(norSevenTotal.getOrDefault(column, BigDecimal.ZERO));
                BigDecimal norFourteen = Utils.parseBigDecimal(norFourteenTotal.getOrDefault(column, BigDecimal.ZERO));
                BigDecimal norOthers = Utils.parseBigDecimal(norOthersTotal.getOrDefault(column, BigDecimal.ZERO));
                BigDecimal total = norZero.add(norOne).add(norThree).add(norSeven).add(norFourteen).add(norOthers);
                if (total.compareTo(BigDecimal.ZERO) == 0) {
                    ratioTotal.put(column, "");
                } else {
                    ratioTotal.put(column, (norOne.add(norThree).add(norSeven).add(norFourteen)).divide(total, 3, RoundingMode.HALF_UP));
                }
            }

            resultList.add(ratioTotal);
            resultList.add(norZeroTotal);
            resultList.add(norOneTotal);
            resultList.add(norThreeTotal);
            resultList.add(norSevenTotal);
            resultList.add(norFourteenTotal);
            resultList.add(norOthersTotal);
        }
        page.setTotal(resultList.size());
        page.setData(resultList);
        return response.setBody(page);
    }

    @Override
    @SuppressWarnings("unchecked")
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        List<String> report3SelectedValues = (List<String>) parameterMap.get("report3SelectedValues");
        if (report3SelectedValues.size() > 1) {
            String column = report3SelectedValues.get(1);
            if (column.startsWith("Y2")) {
                parameterMap.put("report3DetailsDateColumn", "CALENDAR_YEAR");
                parameterMap.put("report3DetailsDateValue", StringUtils.removeStart(column, "Y"));
            } else if (column.startsWith("M2")) {
                parameterMap.put("report3DetailsDateColumn", "CALENDAR_MONTH");
                parameterMap.put("report3DetailsDateValue", StringUtils.removeStart(column, "M"));
            } else if (column.startsWith("W2")) {
                parameterMap.put("report3DetailsDateColumn", "CALENDAR_WEEK");
                parameterMap.put("report3DetailsDateValue", StringUtils.removeStart(column, "W"));
            }
        }

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(poNorDao.queryReport3DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(poNorDao.queryReport3Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    @SuppressWarnings("unchecked")
    public void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);

        List<String> report3SelectedValues = (List<String>) parameterMap.get("report3SelectedValues");
        if (report3SelectedValues.size() > 1) {
            String column = report3SelectedValues.get(1);
            if (column.startsWith("Y2")) {
                parameterMap.put("report3DetailsDateColumn", "CALENDAR_YEAR");
                parameterMap.put("report3DetailsDateValue", StringUtils.removeStart(column, "Y"));
            } else if (column.startsWith("M2")) {
                parameterMap.put("report3DetailsDateColumn", "CALENDAR_MONTH");
                parameterMap.put("report3DetailsDateValue", StringUtils.removeStart(column, "M"));
            } else if (column.startsWith("W2")) {
                parameterMap.put("report3DetailsDateColumn", "CALENDAR_WEEK");
                parameterMap.put("report3DetailsDateValue", StringUtils.removeStart(column, "W"));
            }
        }

        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "po_nor_data_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.customer.dao.IPONORDao.queryReport3Details", parameterMap);
    }

    @Override
    @SuppressWarnings("unchecked")
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3MoDetails(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        List<String> report3SelectedValues = (List<String>) parameterMap.get("report3SelectedValues");
        if (report3SelectedValues.size() > 1) {
            String column = report3SelectedValues.get(1);
            if (column.startsWith("Y2")) {
                parameterMap.put("report3DetailsDateColumn", "CALENDAR_YEAR");
                parameterMap.put("report3DetailsDateValue", StringUtils.removeStart(column, "Y"));
            } else if (column.startsWith("M2")) {
                parameterMap.put("report3DetailsDateColumn", "CALENDAR_MONTH");
                parameterMap.put("report3DetailsDateValue", StringUtils.removeStart(column, "M"));
            } else if (column.startsWith("W2")) {
                parameterMap.put("report3DetailsDateColumn", "CALENDAR_WEEK");
                parameterMap.put("report3DetailsDateValue", StringUtils.removeStart(column, "W"));
            }
        }

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(poNorDao.queryReport3MoDetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(poNorDao.queryReport3MoDetails(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    @SuppressWarnings("unchecked")
    public void downloadReport3MoDetails(Map<String, Object> parameterMap, HttpServletResponse response) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);

        List<String> report3SelectedValues = (List<String>) parameterMap.get("report3SelectedValues");
        if (report3SelectedValues.size() > 1) {
            String column = report3SelectedValues.get(1);
            if (column.startsWith("Y2")) {
                parameterMap.put("report3DetailsDateColumn", "CALENDAR_YEAR");
                parameterMap.put("report3DetailsDateValue", StringUtils.removeStart(column, "Y"));
            } else if (column.startsWith("M2")) {
                parameterMap.put("report3DetailsDateColumn", "CALENDAR_MONTH");
                parameterMap.put("report3DetailsDateValue", StringUtils.removeStart(column, "M"));
            } else if (column.startsWith("W2")) {
                parameterMap.put("report3DetailsDateColumn", "CALENDAR_WEEK");
                parameterMap.put("report3DetailsDateValue", StringUtils.removeStart(column, "W"));
            }
        }

        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "po_nor_data_mo_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.customer.dao.IPONORDao.queryReport3MoDetails", parameterMap);
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response saveReport3Details(Map<String, Object> parameterMap) {
        Object obj = parameterMap.get("report3DetailsUpdate");
        if (obj instanceof JSONObject jsonObj) {
            List<Map<String, Object>> dataList = new ArrayList<>();
            for (String key : jsonObj.keySet()) {
                JSONObject changeObj = jsonObj.getJSONObject(key);
                Map<String, Object> map = new HashMap<>();
                String[] keys = key.split("#");
                map.put("order", keys[0]);
                map.put("item", keys[1]);
                map.put("rca_type", changeObj.getString("RCA_TYPE"));
                map.put("rca_result", changeObj.getString("RCA_RESULT"));
                map.put("rca_comments", changeObj.getString("RCA_COMMENTS"));
                dataList.add(map);
            }
            if (dataList.isEmpty() == false) {
                parameterMap.put("dataList", dataList);
                poNorDao.saveReport3Details(parameterMap);
            }
        }
        return response;
    }


    private Map<String, List<String>> queryReport3ColumnsSource(Map<String, Object> parameterMap) {
        Map<String, List<String>> resultMap = new HashMap<>();

        resultMap.put("years", poNorDao.queryReport3YearList(parameterMap));
        resultMap.put("months", poNorDao.queryReport3MonthList(parameterMap));
        resultMap.put("weeks", poNorDao.queryReport3WeekList(parameterMap));

        return resultMap;
    }

    private String getColumnName(Object labelObj) {
        String label = (String) labelObj;
        if (label == null) {
            return null;
        }
        if (Utils.hasInjectionAttack(label)) {
            return "";
        }
        return label;
    }

    //region private functions
    @SuppressWarnings("unchecked")
    private void generateFilter(Map<String, Object> parameterMap) {
        this.generateCascaderFilterSQL(parameterMap);
    }

    private void generateReport1Tooltips(Map<String, Object> parameterMap) {
        String resultType = (String) parameterMap.get("resultType");
        List<String> tooltips = ((JSONArray) parameterMap.get("report1Tooltips")).toJavaList(String.class);
        if (!tooltips.isEmpty()) {
            List<String> tooltipsColumns = tooltips.stream().map(this::getColumnName).collect(Collectors.toList());
            List<String> tooltipsColumnsName = new ArrayList<>();
            List<String> polymorphicColumns = new ArrayList<>(Arrays.asList("COUNT"));

            for (String c : tooltipsColumns) {
                String tooltip = this.getColumnName(c);
                if (polymorphicColumns.contains(c)) {
                    if ("Line".equalsIgnoreCase(resultType)) {
                        tooltipsColumnsName.add("COUNT(1) AS COUNT");
                    }
                } else {
                    tooltipsColumnsName.add("NVL(AVG(" + c + "),0) AS " + tooltip);
                }
            }
            parameterMap.put("tooltipsColumns", StringUtils.join(tooltipsColumnsName, ", "));
        }
    }

    private void generateTreePathFilter(Map<String, Object> parameterMap) {
        String selectedTreePath = (String) parameterMap.get("selectedTreePath");
        if (StringUtils.isNotBlank(selectedTreePath)) {
            List<String> conditions = new ArrayList<>();
            String[] treePaths = selectedTreePath.split(" > ");
            for (int i = 1; i <= Math.min(treePaths.length, 5); i++) {
                String key = Utils.randomStr(8);
                if ("Others".equals(StringUtils.trim(treePaths[i - 1]))) {
                    String name = this.getColumnName(parameterMap.get("level" + i));
                    conditions.add("(" + name + " = #{" + key + ",jdbcType=VARCHAR} or " + name + " is null )");
                } else {
                    conditions.add(this.getColumnName(parameterMap.get("level" + i)) + " = #{" + key + ",jdbcType=VARCHAR}");
                }
                parameterMap.put(key, StringUtils.trim(treePaths[i - 1]));
            }
            parameterMap.put("treePathFilter", "(" + StringUtils.join(conditions, " and ") + ")");
        }
    }

    private void generateValueColumn(Map<String, Object> parameterMap) {
        String resultType = (String) parameterMap.get("resultType");
        String valueColumn = "COUNT(1)";
        if ("Line".equalsIgnoreCase(resultType)) {
            valueColumn = "COUNT(1)";
        }
        parameterMap.put("valueColumn", valueColumn);
    }


}
