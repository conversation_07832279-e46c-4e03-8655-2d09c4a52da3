package com.scp.customer.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.scp.customer.bean.E2ELTAnalysisReport1Bean;
import com.scp.customer.bean.E2ELTAnalysisReport1Treemap;
import com.scp.customer.bean.E2ELTAnalysisReport2Bean;
import com.scp.customer.bean.E2ELTOverviewReport2Bean;
import com.scp.customer.dao.IE2ELTAnalysisDao;
import com.scp.customer.service.IE2ELTAnalysisService;
import com.starter.context.bean.*;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Scope("prototype")
@Transactional
public class E2ELTAnalysisServiceImpl extends ServiceHelper implements IE2ELTAnalysisService {

    @Resource
    private Response response;

    @Resource
    private ExcelTemplate excelTemplate;

    @Resource
    private IE2ELTAnalysisDao e2ELTAnalysisDao;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryFilters(Map<String, Object> parameterMap) {
        return response.setBody(Utils.parseCascader(e2ELTAnalysisDao.queryCascader()));
    }


    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateReport1Tooltips(parameterMap);
        this.generateValueColumn(parameterMap);

        // 将前台传过来的label转换成列名, 同时也可以防止恶意代码注入
        parameterMap.put("level1", this.getColumnName(parameterMap.get("level1")));
        parameterMap.put("level2", this.getColumnName(parameterMap.get("level2")));
        parameterMap.put("level3", this.getColumnName(parameterMap.get("level3")));
        parameterMap.put("level4", this.getColumnName(parameterMap.get("level4")));
        parameterMap.put("level5", this.getColumnName(parameterMap.get("level5")));

        List<E2ELTAnalysisReport1Treemap> resultList = new ArrayList<>();
        List<E2ELTAnalysisReport1Bean> dataList = e2ELTAnalysisDao.queryReport1(parameterMap);
        for (E2ELTAnalysisReport1Bean data : dataList) {
            this.convertReport1Data(resultList, data);
        }
        return response.setBody(resultList);
    }

    private String getColumnName(Object labelObj) {
        String label = (String) labelObj;
        if (label == null) {
            return null;
        }
        if (Utils.hasInjectionAttack(label)) {
            return "";
        }
        return label;
    }

    private void convertReport1Data(List<E2ELTAnalysisReport1Treemap> list, E2ELTAnalysisReport1Bean data) {
        String[] categorysOrg = new String[]{data.getCategory1(), data.getCategory2(), data.getCategory3(), data.getCategory4(), data.getCategory5()};
        List<String> categories = new ArrayList<>();

        for (String category : categorysOrg) {
            if (StringUtils.isNotBlank(category)) {
                categories.add(category);
            } else {
                break;
            }
        }

        // 这边逻辑比较复杂, 所以用最笨的方法来描述了, 以免后期不好维护
        // 先把这一行数据转成treemap的数据
        // 第一个节点
        List<E2ELTAnalysisReport1Treemap> child = new ArrayList<>();
        E2ELTAnalysisReport1Treemap root = new E2ELTAnalysisReport1Treemap();
        root.setName(categories.get(0));
        root.setTips(data.copyTooltips()); // 因为这个tooltips要放在树中全局使用, 所以必须要生成一个新节点
        root.setChildren(child);

        // 中间节点
        for (int i = 1; i < categories.size() - 1; i++) {
            E2ELTAnalysisReport1Treemap treemap = new E2ELTAnalysisReport1Treemap();
            treemap.setName(categories.get(i));
            treemap.setTips(data.copyTooltips());

            child.add(treemap);
            child = new ArrayList<>();
            treemap.setChildren(child);
        }

        // 最后一个节点
        E2ELTAnalysisReport1Treemap lastNode = new E2ELTAnalysisReport1Treemap();
        lastNode.setName(categories.get(categories.size() - 1));
        lastNode.setValue(data.getValue());
        lastNode.setTips(data.copyTooltips());
        child.add(lastNode);

        // 将这行treemap与原始数据相加
        // 先找到list中是否有这个数据节点
        Optional<E2ELTAnalysisReport1Treemap> beanOpt = list.stream().filter(b -> b.getName().equals(categories.get(0))).findFirst();
        if (beanOpt.isPresent()) {
            E2ELTAnalysisReport1Treemap bean = beanOpt.get();
            bean.add(root); // 两个节点合并
        } else { //找不到的时候最省事, 直接放入list就可以了
            list.add(root);
        }
    }

    //region private functions
    @SuppressWarnings("unchecked")
    private void generateFilter(Map<String, Object> parameterMap) {
        // 生成筛选条件
        this.generateCascaderFilterSQL(parameterMap);
    }

    private void generateReport1Tooltips(Map<String, Object> parameterMap) {
        List<String> tooltips = ((JSONArray) parameterMap.get("report1Tooltips")).toJavaList(String.class);
        String calendarType = parameterMap.get("calendarType").toString();
        String calendarCalcType = parameterMap.get("calendarCalcType").toString();
        if (!tooltips.isEmpty()) {
            List<String> tooltipsColumns = tooltips.stream().map(this::getColumnName).toList();
            List<String> tooltipsColumnsName = new ArrayList<>();

            for (String c : tooltipsColumns) {
                String tooltip = this.getColumnName(c);
                tooltipsColumnsName.add(convertValueColumn(tooltip, calendarType, calendarCalcType) + " AS " + tooltip);
            }
            parameterMap.put("tooltipsColumns", StringUtils.join(tooltipsColumnsName, ", "));
        }
    }

    private String convertValueColumn(String resultType, String calendarType, String calendarCalcType) {
        String calendarCalcString = "";
        BigDecimal calendarCalc = BigDecimal.valueOf(1);
        if ("Working Days".equals(calendarCalcType)) {
            calendarCalc = BigDecimal.valueOf(5.0 / 7.0);
        }
        calendarCalcString = " * " + calendarCalc.toString();
        String valueColumn = "COUNT(1)";
        if ("Lines".equalsIgnoreCase(resultType)) {
            valueColumn = "COUNT(1)";
        } else if ("Number of Records".equalsIgnoreCase(resultType)) {
            valueColumn = "SUM(NUM_OF_RECORDS)";
        } else if ("Quantity".equalsIgnoreCase(resultType)) {
            valueColumn = "SUM(QTY)";
        } else if ("Net Value".equalsIgnoreCase(resultType)) {
            valueColumn = "SUM(SUM_NET_VALUE)";
        } else if ("LINES".equalsIgnoreCase(resultType)) {
            valueColumn = "COUNT(1)";
        } else if ("NUM_OF_RECORDS".equalsIgnoreCase(resultType)) {
            valueColumn = "NVL(SUM(NUM_OF_RECORDS),0)";
        } else if ("QUANTITY".equalsIgnoreCase(resultType)) {
            valueColumn = "NVL(SUM(QTY),0)";
        } else if ("NET_VALUE".equalsIgnoreCase(resultType)) {
            valueColumn = "NVL(SUM(SUM_NET_VALUE),0)";
        } else if ("Order Creation Date".equalsIgnoreCase(calendarType)) {
            if ("CUSTOMER_REQUESTED_LT".equalsIgnoreCase(resultType)) {
                valueColumn = "DECODE(SUM(CUSTOMER_REQUESTED_LT_CNT), 0, 0, SUM(SUM_CUSTOMER_REQUESTED_LT) / SUM(CUSTOMER_REQUESTED_LT_CNT))" + calendarCalcString;
            } else if ("LOGISTICS_OFFER_LT".equalsIgnoreCase(resultType)) {
                valueColumn = "DECODE(SUM(LOGISTICS_OFFER_LT_CNT), 0, 0, SUM(SUM_LOGISTICS_OFFER_LT) / SUM(LOGISTICS_OFFER_LT_CNT))" + calendarCalcString;
            } else if ("FIRST_CONFIRMED_LT".equalsIgnoreCase(resultType)) {
                valueColumn = "DECODE(SUM(IST_CONFIRMED_LT_CNT), 0, 0, SUM(SUM_1ST_CONFIRMED_LT) / SUM(IST_CONFIRMED_LT_CNT))" + calendarCalcString;
            } else if ("ACTUAL_LT".equalsIgnoreCase(resultType)) {
                valueColumn = "DECODE(SUM(ACTUAL_LT_CNT), 0, 0, SUM(SUM_ACTUAL_LT) / SUM(ACTUAL_LT_CNT))" + calendarCalcString;
            } else if ("CONTRACTUAL_LT".equalsIgnoreCase(resultType)) {
                valueColumn = "DECODE(SUM(CONTRACTUAL_LT_CNT), 0, 0, SUM(SUM_CONTRACTUAL_LT) / SUM(CONTRACTUAL_LT_CNT))" + calendarCalcString;
            }
        } else if ("Order Clean Date".equalsIgnoreCase(calendarType)) {
            if ("CUSTOMER_REQUESTED_LT".equalsIgnoreCase(resultType)) {
                valueColumn = "DECODE(SUM(CUSTOMER_REQUESTED_LT_CNT), 0, 0, SUM(SUM_CUSTOMER_REQUESTED_LT_1) / SUM(CUSTOMER_REQUESTED_LT_CNT))" + calendarCalcString;
            } else if ("LOGISTICS_OFFER_LT".equalsIgnoreCase(resultType)) {
                valueColumn = "DECODE(SUM(LOGISTICS_OFFER_LT_CNT), 0, 0, SUM(SUM_LOGISTICS_OFFER_LT_1) / SUM(LOGISTICS_OFFER_LT_CNT))" + calendarCalcString;
            } else if ("FIRST_CONFIRMED_LT".equalsIgnoreCase(resultType)) {
                valueColumn = "DECODE(SUM(IST_CONFIRMED_LT_CNT), 0, 0, SUM(SUM_1ST_CONFIRMED_LT_1) / SUM(IST_CONFIRMED_LT_CNT))" + calendarCalcString;
            } else if ("ACTUAL_LT".equalsIgnoreCase(resultType)) {
                valueColumn = "DECODE(SUM(ACTUAL_LT_CNT), 0, 0, SUM(SUM_ACTUAL_LT_1) / SUM(ACTUAL_LT_CNT))" + calendarCalcString;
            } else if ("CONTRACTUAL_LT".equalsIgnoreCase(resultType)) {
                valueColumn = "DECODE(SUM(CONTRACTUAL_LT_CNT), 0, 0, SUM(SUM_CONTRACTUAL_LT_1) / SUM(CONTRACTUAL_LT_CNT))" + calendarCalcString;
            }
        }
        return valueColumn;
    }

    private void generateValueColumn(Map<String, Object> parameterMap) {
        String resultType = (String) parameterMap.get("resultType");
        String calendarType = parameterMap.get("calendarType").toString();
        String calendarCalcType = parameterMap.get("calendarCalcType").toString();
        parameterMap.put("valueColumn", convertValueColumn(resultType, calendarType, calendarCalcType));
    }

    private void generateTreePathFilter(Map<String, Object> parameterMap) {
        String selectedTreePath = (String) parameterMap.get("selectedTreePath");
        if (StringUtils.isNotBlank(selectedTreePath)) {
            List<String> conditions = new ArrayList<>();
            String[] treePaths = selectedTreePath.split(" > ");
            for (int i = 1; i <= Math.min(treePaths.length, 5); i++) {
                String key = Utils.randomStr(8);
                if ("Others".equals(StringUtils.trim(treePaths[i - 1]))) {
                    String name = this.getColumnName(parameterMap.get("level" + i));
                    conditions.add("(" + name + " = #{" + key + ",jdbcType=VARCHAR} or " + name + " is null )");
                } else {
                    conditions.add(this.getColumnName(parameterMap.get("level" + i)) + " = #{" + key + ",jdbcType=VARCHAR}");
                }
                parameterMap.put(key, StringUtils.trim(treePaths[i - 1]));
            }
            parameterMap.put("treePathFilter", "(" + StringUtils.join(conditions, " and ") + ")");
        }
    }


    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        String resultType = (String) parameterMap.get("report2ResultType");
        String calendarType = parameterMap.get("calendarType").toString();
        String calendarCalcType = parameterMap.get("calendarCalcType").toString();
        parameterMap.put("report2ResultType", convertValueColumn(resultType, calendarType, calendarCalcType));

        List<E2ELTOverviewReport2Bean> resultList = e2ELTAnalysisDao.queryReport2(parameterMap);
        return response.setBody(resultList);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);

        page.setTotal(e2ELTAnalysisDao.queryReport2DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(e2ELTAnalysisDao.queryReport2Details(parameterMap));
        }

        return response.setBody(page);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public void downloadReport2Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        String fileName = "end_to_end_lt_map_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.customer.dao.IE2ELTAnalysisDao.queryReport2Details", parameterMap);
    }


    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);

        page.setTotal(e2ELTAnalysisDao.queryReport3Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(e2ELTAnalysisDao.queryReport3(parameterMap));
        }

        return response.setBody(page);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public void downloadReport3(Map<String, Object> parameterMap, HttpServletResponse response) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        String fileName = "end_to_end_lt_table_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.customer.dao.IE2ELTAnalysisDao.queryReport3", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);

        page.setTotal(e2ELTAnalysisDao.queryReport3DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(e2ELTAnalysisDao.queryReport3Details(parameterMap));
        }

        return response.setBody(page);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        String fileName = "end_to_end_lt_table_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.customer.dao.IE2ELTAnalysisDao.queryReport3Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initAnalysisPage(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cascader", Utils.parseCascader(e2ELTAnalysisDao.queryAnalysisCascader(parameterMap)));
        resultMap.put("pivotOpts", e2ELTAnalysisDao.queryAnalysisPivotOpts(parameterMap));
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryAnalysisReport1(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);

        String report1Parameter1 = (String) parameterMap.get("report1Parameter1");
        String report1Parameter2 = (String) parameterMap.get("report1Parameter2");
        String calendarType = parameterMap.get("calendarType").toString();
        String calendarCalcType = parameterMap.get("calendarCalcType").toString();
        report1Parameter1 = convertValueColumn(report1Parameter1, calendarType, calendarCalcType);
        report1Parameter2 = convertValueColumn(report1Parameter2, calendarType, calendarCalcType);
        parameterMap.put("report1Parameter1", report1Parameter1);
        parameterMap.put("report1Parameter2", report1Parameter2);

        JSONArray report1Categories = (JSONArray) parameterMap.get("report1Categories");
        if (report1Categories.isEmpty()) {
            report1Categories.add("GSC_SUB_REGION");
            report1Categories.add("GSC_REGION");
            report1Categories.add("COUNTRY_NAME");
            report1Categories.add("PLANT_CODE");
            report1Categories.add("PLANT_TYPE");
        }
        parameterMap.put("report1Category", report1Categories.get(0));
        List<Map<String, Object>> resultList = e2ELTAnalysisDao.queryAnalysisReport1(parameterMap);
        double max = this.renderAnalysisReport1Width(resultList, 0);
        for (Map<String, Object> map : resultList) {
            map.put("parentMaxWidth", max);
            map.put("parent", new ArrayList<>());
            map.put("id", Utils.randomStr(12));
            map.put("hasChildren", report1Categories.size() > 1 && !"Total".equals(map.get("category")));
        }
        return response.setBody(resultList);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryAnalysisReport1Sub(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);

        String report1Parameter1 = (String) parameterMap.get("report1Parameter1");
        String report1Parameter2 = (String) parameterMap.get("report1Parameter2");
        String calendarType = parameterMap.get("calendarType").toString();
        String calendarCalcType = parameterMap.get("calendarCalcType").toString();
        report1Parameter1 = convertValueColumn(report1Parameter1, calendarType, calendarCalcType);
        report1Parameter2 = convertValueColumn(report1Parameter2, calendarType, calendarCalcType);
        parameterMap.put("report1Parameter1", report1Parameter1);
        parameterMap.put("report1Parameter2", report1Parameter2);

        JSONArray report1Categories = (JSONArray) parameterMap.get("report1Categories");
        JSONArray parent = (JSONArray) parameterMap.get("parent");
        double parentMaxWidth = Utils.parseDouble(parameterMap.get("parentMaxWidth"));
        String expandValue = (String) parameterMap.get("expandValue");
        String expandColumn = report1Categories.getString(parent.size());
        parameterMap.put("expandValue", expandValue);
        parameterMap.put("expandColumn", expandColumn);
        parameterMap.put("category", report1Categories.getString(parent.size() + 1));
        List<Map<String, Object>> resultList = e2ELTAnalysisDao.queryAnalysisReport1Sub(parameterMap);

        this.renderAnalysisReport1Width(resultList, parentMaxWidth);
        for (Map<String, Object> map : resultList) {
            map.put("id", Utils.randomStr(12));
            map.put("hasChildren", report1Categories.size() > parent.size() + 2);
            JSONArray temp = new JSONArray();
            temp.addAll(parent);
            temp.add(expandValue);
            map.put("parent", temp);
            map.put("parentMaxWidth", parentMaxWidth);
        }
        return response.setBody(resultList);
    }

    private double queryAnalysisReport1Details(List<Map<String, Object>> data, double parentMax) {
        double max = Math.max(0, parentMax);
        for (Map<String, Object> map : data) {
            if (!"Total".equals(map.get("category"))) {
                max = Math.max(max, Utils.parseDouble(map.get("differenceInDaysMinus")));
                max = Math.max(max, Utils.parseDouble(map.get("differenceInDaysPlus")));
            }
        }

        for (Map<String, Object> map : data) {
            if (!"Total".equals(map.get("category"))) {
                if (max != 0) {
                    map.put("differenceInDaysMinusWidth", Utils.parseDouble(map.get("differenceInDaysMinus")) / max * 100);
                    map.put("differenceInDaysPlusWidth", Utils.parseDouble(map.get("differenceInDaysPlus")) / max * 100);
                } else {
                    map.put("differenceInDaysMinusWidth", 0);
                    map.put("differenceInDaysPlusWidth", 0);
                }
            }
        }
        return max;
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryAnalysisReport1Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTrendFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(e2ELTAnalysisDao.queryAnalysisReport1DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(e2ELTAnalysisDao.queryAnalysisReport1Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadAnalysisReport1Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateTrendFilter(parameterMap);

        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "e2e_lt_analysis_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.customer.dao.IE2ELTAnalysisDao.queryAnalysisReport1Details", parameterMap);
    }

    private void generateTrendFilter(Map<String, Object> parameterMap) {
        JSONObject selectObj = JSON.parseObject((String) parameterMap.get("report1SelectedValues"));
        if (selectObj != null) {
            if ("Total".equalsIgnoreCase(selectObj.getString("category"))) {
                parameterMap.put("report1Values", new ArrayList<>());
            } else {
                List<String> values = new ArrayList<>(selectObj.getJSONArray("parent").toJavaList(String.class));
                values.add(selectObj.getString("category"));
                parameterMap.put("report1Values", values);
            }
        }
    }

    private double renderAnalysisReport1Width(List<Map<String, Object>> data, double parentMax) {
        double max = Math.max(0, parentMax);
        for (Map<String, Object> map : data) {
            if (!"Total".equals(map.get("category"))) {
                max = Math.max(max, Math.abs(Utils.parseDouble(map.get("differenceInDaysMinus"))));
                max = Math.max(max, Math.abs(Utils.parseDouble(map.get("differenceInDaysPlus"))));
            }
        }

        for (Map<String, Object> map : data) {
            if (!"Total".equals(map.get("category"))) {
                if (max != 0) {
                    map.put("differenceInDaysMinusWidth", Math.abs(Utils.parseDouble(map.get("differenceInDaysMinus"))) / max * 100);
                    map.put("differenceInDaysPlusWidth", Math.abs(Utils.parseDouble(map.get("differenceInDaysPlus"))) / max * 100);
                } else {
                    map.put("differenceInDaysMinusWidth", 0);
                    map.put("differenceInDaysPlusWidth", 0);
                }
            }
        }
        return max;
    }


    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryAnalysisReport2(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        List<E2ELTAnalysisReport2Bean> dataList;

        dataList = e2ELTAnalysisDao.queryAnalysisReport2(parameterMap);

        Map<String, BigDecimal> dataMap = new HashMap<>();
        LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();
        Map<String, String> xAxisMap = new HashMap<>();

        List<String> legend = new ArrayList<>();

        // 先将数据转存到dataMap、xAxisMap
        for (E2ELTAnalysisReport2Bean data : dataList) {
            dataMap.put(data.getKey(), data.getVALUE());
            xAxisMap.put(data.getCALENDAR_DATE(), "");
            if (!legend.contains(data.getNAME())) {
                legend.add(data.getNAME());
            }
        }
        // 获取x轴
        List<String> xAxisList = xAxisMap.keySet().stream().sorted(String::compareTo).collect(Collectors.toList());
        //对于每一个图例做处理，生成{图例:[VALUE]}的map
        for (String l : legend) {
            List<BigDecimal> temp = new ArrayList<>();
            for (String x : xAxisList) {
                temp.add(dataMap.getOrDefault(l + "#" + x, BigDecimal.ZERO));
            }
            resultMap.put(l, temp);
        }

        // 放置x轴
        resultMap.put("xAxis", xAxisList);

        parameterMap.put("xAxisList", xAxisList);

        List<Map<String, Object>> rawData = e2ELTAnalysisDao.queryAnalysisReport2Line(parameterMap);
        Map<String, List<Object>> lineResult = new LinkedHashMap<>();

        for (Map<String, Object> row : rawData) {
            for (Map.Entry<String, Object> entry : row.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();
                if (parameterMap.get("reportViewType").equals(key)) {
                    continue;
                }
                lineResult.computeIfAbsent(key, k -> new ArrayList<>());

                if (value == null) {
                    lineResult.get(key).add(null);
                } else if (value instanceof BigDecimal) {
                    lineResult.get(key).add(((BigDecimal) value).doubleValue());
                } else {
                    lineResult.get(key).add(value);
                }
            }
        }
        resultMap.put("lineData", lineResult);
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryAnalysisReport2Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(e2ELTAnalysisDao.queryAnalysisReport2DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(e2ELTAnalysisDao.queryAnalysisReport2Details(parameterMap));
        }
        return response.setBody(page);
    }

    public void downloadAnalysisReport2Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "e2e_lt_analysis_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.customer.dao.IE2ELTAnalysisDao.queryAnalysisReport2Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryAnalysisReport3(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        List<LinkedHashMap<String, Object>> dataList = e2ELTAnalysisDao.queryAnalysisReport3(parameterMap);

        page.setData(dataList);
        page.setTotal(dataList.size());
        return response.setBody(page);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryAnalysisReport3Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(e2ELTAnalysisDao.queryAnalysisReport3DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(e2ELTAnalysisDao.queryAnalysisReport3Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryAnalysisReport3Columns(Map<String, Object> parameterMap) {
        return response.setBody(e2ELTAnalysisDao.queryAnalysisReport3Columns(parameterMap));
    }

    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public void downloadAnalysisReport3(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        String column1 = this.getColumnName(parameterMap.get("column1"));
        String column2 = this.getColumnName(parameterMap.get("column2"));
        parameterMap.put("column1", column1);
        parameterMap.put("column2", column2);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "e2e_lt_analysis_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.customer.IE2ELTAnalysisDao.queryAnalysisReport3", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public void downloadAnalysisReport3Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        parameterMap.put("column1", this.getColumnName(parameterMap.get("column1")));
        parameterMap.put("column2", this.getColumnName(parameterMap.get("column2")));
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "e2e_lt_analysis_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.customer.dao.IE2ELTAnalysisDao.queryAnalysisReport3Details", parameterMap);
    }
}
