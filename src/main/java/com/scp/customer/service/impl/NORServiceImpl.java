package com.scp.customer.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.scp.customer.dao.INORDao;
import com.scp.customer.service.INORService;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
@Scope("prototype")
@Transactional
public class NORServiceImpl extends ServiceHelper implements INORService {

    @Resource
    private Response response;

    @Resource
    private INORDao norDao;

    @Resource
    private ExcelTemplate excelTemplate;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryNorCascader(Map<String, Object> parameterMap) {
        return response.setBody(Utils.parseCascader(norDao.queryNorCascader()));
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        List<LinkedHashMap<String, Object>> dataList = norDao.queryReport1(parameterMap);

        page.setTotal(norDao.queryReport1Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(dataList);
            dataList.addAll(norDao.queryReport1Total(parameterMap));
        }

        return response.setBody(page);
    }

    @Override
    public void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateCascaderFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "nor_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.customer.dao.INORDao.queryReport1", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1Details(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateCascaderFilter(parameterMap);
        String report1SelectedColum = (String) parameterMap.get("report1SelectedColumn");

        switch (report1SelectedColum) {
            case "RC_EQ_1" -> parameterMap.put("rcFilter", "T.RESCH_COUNTER  = 1");
            case "RC_EQ_2" -> parameterMap.put("rcFilter", "T.RESCH_COUNTER  = 2");
            case "RC_EQ_3" -> parameterMap.put("rcFilter", "T.RESCH_COUNTER  = 3");
            case "RC_GE_4_LE_5" -> parameterMap.put("rcFilter", "T.RESCH_COUNTER IN (4, 5)");
            case "RC_GE_6_LE_10" -> parameterMap.put("rcFilter", "T.RESCH_COUNTER IN (6, 7, 8, 9, 10)");
            case "RC_GE_11" -> parameterMap.put("rcFilter", "T.RESCH_COUNTER >= 11");
        }

        page.setTotal(norDao.queryReport1DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(norDao.queryReport1Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateCascaderFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        String report1SelectedColum = (String) parameterMap.get("report1SelectedColumn");

        switch (report1SelectedColum) {
            case "RC_EQ_1" -> parameterMap.put("rcFilter", "T.RESCH_COUNTER  = 1");
            case "RC_EQ_2" -> parameterMap.put("rcFilter", "T.RESCH_COUNTER  = 2");
            case "RC_EQ_3" -> parameterMap.put("rcFilter", "T.RESCH_COUNTER  = 3");
            case "RC_GE_4_LE_5" -> parameterMap.put("rcFilter", "T.RESCH_COUNTER IN (4, 5)");
            case "RC_GE_6_LE_10" -> parameterMap.put("rcFilter", "T.RESCH_COUNTER IN (6, 7, 8, 9, 10)");
            case "RC_GE_11" -> parameterMap.put("rcFilter", "T.RESCH_COUNTER >= 11");
        }

        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "nor_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.customer.dao.INORDao.queryReport1Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);
        LinkedHashMap<String, Object> dataList = norDao.queryReport2(parameterMap);
        return response.setBody(dataList);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);
        String report3DateType = (String) parameterMap.get("report3DateType");
        if ("BY_WEEK".equals(report3DateType)) {
            parameterMap.put("dateColumn", "T.GI_WEEK");
        } else if ("BY_MONTH".equals(report3DateType)) {
            parameterMap.put("dateColumn", "T.GI_MONTH");
        } else {
            parameterMap.put("dateColumn", "TO_CHAR(T.GI_DATE, 'YYYY/MM/DD')");
        }


        List<LinkedHashMap<String, Object>> dataList = norDao.queryReport3(parameterMap);

        Map<String, List<Object>> result = new HashMap<>();
        String[] legends = new String[]{"RC_EQ_1", "RC_EQ_2", "RC_EQ_3", "RC_GE_4_LE_5", "RC_GE_6_LE_10", "RC_GE_11"};
        for (String legend : legends) {
            result.put(legend, new ArrayList<>());
        }
        result.put("xAxis", new ArrayList<>());

        for (Map<String, Object> data : dataList) {
            for (Map.Entry<String, Object> entry : data.entrySet()) {
                String key = entry.getKey();
                Object value = entry.getValue();

                switch (key) {
                    case "CALENDAR_DATE" -> result.get("xAxis").add(value);
                    case "RC_EQ_1", "RC_EQ_2", "RC_EQ_3", "RC_GE_4_LE_5", "RC_GE_6_LE_10", "RC_GE_11" -> result.get(key).add(value);
                }
            }
        }

        return response.setBody(result);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3RatiosColumns(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);
        List<String> dateRangeList = norDao.queryReport3RatiosColumns(parameterMap);
        return response.setBody(dateRangeList);
    }

    private LinkedHashMap<String, Object> createResultMap(String statistics) {
        LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();
        resultMap.put("STATISTICS", statistics);
        return resultMap;
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Ratios(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);
        List<String> dateRangeList = norDao.queryReport3RatiosColumns(parameterMap);
        parameterMap.put("dateRangeList", dateRangeList);

        List<LinkedHashMap<String, Object>> dataList = norDao.queryReport3Ratios(parameterMap);
        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        resultList.add(createResultMap("RC=1"));
        resultList.add(createResultMap("RC=2"));
        resultList.add(createResultMap("RC=3"));
        resultList.add(createResultMap("RC=4~5"));
        resultList.add(createResultMap("RC=6~10"));
        resultList.add(createResultMap("RC≥11"));
        List<String> rcKeys = Arrays.asList("RC_EQ_1", "RC_EQ_2", "RC_EQ_3", "RC_GE_4_LE_5", "RC_GE_6_LE_10", "RC_GE_11");

        for (int i = 0; i < resultList.size(); i++) {
            HashMap<String, Object> rcMap = resultList.get(i);

            for (LinkedHashMap<String, Object> data : dataList) {
                String key = (String) data.get("CALENDAR_DATE");
                rcMap.put(key, data.get(rcKeys.get(i)));
            }
        }

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(2);
        page.setData(resultList);

        return response.setBody(page);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport4(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(norDao.queryReport4Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(norDao.queryReport4(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport4Details(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(norDao.queryReport4DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(norDao.queryReport4Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport4(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateCascaderFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "nor_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.customer.dao.INORDao.queryReport4", parameterMap);
    }

    @Override
    public void downloadReport4Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateCascaderFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "nor_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.customer.dao.INORDao.queryReport4Details", parameterMap);
    }

    private void generateCascaderFilter(Map<String, Object> parameterMap, String... blackList) {
        this.generateCascaderFilterSQL(parameterMap, null, null, null, null, blackList);

        // load datarange
        String dateRangeType = (String) parameterMap.get("dateRangeType");
        if (Utils.hasInjectionAttack(dateRangeType) == false) {
            parameterMap.put("dateColumn", dateRangeType);
        }

        // categroy
        JSONArray categroyArray = (JSONArray) parameterMap.get("category");
        if (categroyArray == null || categroyArray.isEmpty()) {
            List<String> categroyDefault = new ArrayList<>();
            categroyDefault.add("ENTITY");
            parameterMap.put("category", categroyDefault);
        }
    }
}
