package com.scp.customer.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.scp.customer.dao.IOTCDao;
import com.scp.customer.service.IOTCService;
import com.starter.context.bean.CacheRemove;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import com.starter.utils.excel.SheetInfoWithQueryKey;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Scope("prototype")
@Transactional
public class OTCServiceImpl extends ServiceHelper implements IOTCService {

    @Resource
    private Response response;

    @Resource
    private IOTCDao otcDataDao;

    @Resource
    private ExcelTemplate excelTemplate;


    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryColumnsByDaterange(Map<String, Object> parameterMap) {
        return response.setBody(this.queryColumnsByDaterangeSource(parameterMap));
    }

    /**
     * calc weeks and days between start month and end month
     *
     * @param parameterMap parameters
     * @return result map
     */
    private Map<String, List<String>> queryColumnsByDaterangeSource(Map<String, Object> parameterMap) {
        Map<String, List<String>> resultMap = new HashMap<>();

        resultMap.put("weeks", otcDataDao.queryOtcWeeklyWeekColumns(parameterMap));
        resultMap.put("months", otcDataDao.queryOtcWeeklyMonthColumns(parameterMap));
        resultMap.put("years", otcDataDao.queryOtcWeeklyYearColumns(parameterMap));

        Calendar today = Calendar.getInstance();
        List<String> days = new ArrayList<>();
        SimpleDateFormat format = new SimpleDateFormat("MM/dd");
        resultMap.put("days", days);
        today.set(Calendar.DAY_OF_WEEK, 1);
        today.add(Calendar.WEEK_OF_YEAR, -1);
        for (int i = 0; i < 21; i++) {
            days.add(format.format(today.getTime()));
            today.add(Calendar.DAY_OF_YEAR, 1);
        }

        return resultMap;
    }

    @Override
    @SuppressWarnings("unchecked")
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryOtcWeekly(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap, "DIVISION");
        String reportOrderBy = (String) parameterMap.get("reportOrderBy");
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        List<String> fields = (List<String>) parameterMap.get("field");
        String sort = page.getSort();
        if (!fields.contains(page.getSortColumn())) {
            sort = sort.replace("\"", "\"'");
            if (Objects.equals(reportOrderBy, "ONTIME") || Objects.equals(reportOrderBy, "FAIL")) {
                sort = sort.replace("\"' ", "'_" + reportOrderBy + "\" ");
            } else {
                sort = sort.replace("\"' ", "'_RATIO\" ");
            }

            page.setSort(sort);
        }

        Map<String, List<String>> columnMap = this.queryColumnsByDaterangeSource(parameterMap);
        parameterMap.putAll(columnMap);

        parameterMap.put("defaultOrderColumn", columnMap.get("years").get(0));
        List<Map<String, Object>> dataList = otcDataDao.queryOtcWeekly(parameterMap);
        List<Map<String, Object>> resultList = new ArrayList<>();

        List<String> columns = columnMap.get("years");
        columns.addAll(columnMap.get("months"));
        columns.addAll(columnMap.get("weeks"));

        Map<String, Object> tempMap;
        for (Map<String, Object> map : dataList) {
            // RATIO
            tempMap = new HashMap<>();
            for (String field : fields) {
                tempMap.put(field, map.get(field));
            }
            tempMap.put("TYPE", "RATIO");
            for (String c : columns) {
                tempMap.put(c, map.get("'" + c + "'_RATIO"));
            }
            resultList.add(tempMap);
            // On Time
            tempMap = new HashMap<>();
            for (String field : fields) {
                tempMap.put(field, map.get(field));
            }
            tempMap.put("TYPE", "On Time");
            for (String c : columns) {
                tempMap.put(c, map.get("'" + c + "'_ONTIME"));
            }
            resultList.add(tempMap);

            // Delay
            tempMap = new HashMap<>();
            for (String field : fields) {
                tempMap.put(field, map.get(field));
            }
            tempMap.put("TYPE", "Delay");
            for (String c : columns) {
                tempMap.put(c, map.get("'" + c + "'_FAIL"));
                tempMap.put(c + "_CONFIRMED", map.get("'" + c + "'_CONFIRMED"));
            }
            resultList.add(tempMap);
        }
        this.generateOtcSummary(fields, resultList);
        page.setData(resultList);
        page.setTotal(resultList.size());

        return response.setBody(page);
    }

    /*
    @Override
    @SuppressWarnings("unchecked")
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryOtdsWeeklySummary(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap, "DIVISION");
        List<String> fields = (List<String>) parameterMap.get("field");

        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        String sort = page.getSort();
        if (StringUtils.contains(sort, "_RATIO")) {
            String[] as = sort.split(" ");
            sort = "nvl(" + as[0] + ", -1) " + as[1];
        }
        page.setSort(sort);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        Map<String, List<String>> columnMap = this.queryColumnsByDaterangeSource(parameterMap);
        parameterMap.putAll(columnMap);


        List<Map<String, Object>> dataList = otcDataDao.queryOtdsWeeklySummary(parameterMap);
        List<String> weeks = columnMap.get("weeks");

        Map<String, Object> totalMap = new HashMap<>();
        for (int i = 0; i < fields.size(); i++) {
            if (i == fields.size() - 1) {
                totalMap.put(fields.get(i), "Total");
            }
        }

        for (String week : weeks) {
            BigDecimal confirmed = BigDecimal.ZERO;
            BigDecimal total = BigDecimal.ZERO;
            for (Map<String, Object> map : dataList) {
                confirmed = confirmed.add(Utils.parseBigDecimal(map.get("'" + week + "'_CONFIRMED"), BigDecimal.ZERO));
                total = total.add(Utils.parseBigDecimal(map.get("'" + week + "'_FAIL"), BigDecimal.ZERO));
            }
            totalMap.put("'" + week + "'_FAIL", total.compareTo(BigDecimal.ZERO) == 0 ? null : total);
            totalMap.put("'" + week + "'_CONFIRMED", confirmed.compareTo(BigDecimal.ZERO) == 0 ? null : confirmed);
            totalMap.put("'" + week + "'_RATIO", total.compareTo(BigDecimal.ZERO) == 0 ? null : confirmed.multiply(BigDecimal.valueOf(100)).divide(total, 1, RoundingMode.HALF_UP));
        }

        dataList.add(totalMap);
        page.setData(dataList);
        page.setTotal(dataList.size());
        return response.setBody(page);
    }

     */

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryOtcWeeklyDetails(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateCascaderFilter(parameterMap, "DIVISION");
        String selectedDate = (String) parameterMap.get("selectedDate");
        if (StringUtils.endsWith(selectedDate, "YTD")) {
            parameterMap.put("selectedDate", StringUtils.removeEnd(selectedDate, "YTD"));
            parameterMap.put("selectedDateType", "year");
        } else if (StringUtils.contains(selectedDate, "W")) {
            parameterMap.put("selectedDateType", "week");
        } else if (StringUtils.equals(selectedDate, "-1")) {
            parameterMap.put("selectedDateType", "all");
        } else if (StringUtils.equals(selectedDate, "-2")) {
            parameterMap.put("selectedDateType", "weekly");
        } else {
            parameterMap.put("selectedDateType", "month");
        }
        page.setTotal(otcDataDao.queryOtcWeeklyDetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(otcDataDao.queryOtcWeeklyDetails(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryOtcWeeklyChart(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap, "DIVISION");
        String selectedDate = (String) parameterMap.get("selectedDate");
        if (StringUtils.endsWith(selectedDate, "YTD")) {
            parameterMap.put("selectedDate", StringUtils.removeEnd(selectedDate, "YTD"));
            parameterMap.put("selectedDateType", "year");
        } else if (StringUtils.contains(selectedDate, "W")) {
            parameterMap.put("selectedDateType", "week");
        } else if (StringUtils.equals(selectedDate, "-1")) {
            parameterMap.put("selectedDateType", "all");
        } else if (StringUtils.equals(selectedDate, "-2")) {
            parameterMap.put("selectedDateType", "weekly");
        } else {
            parameterMap.put("selectedDateType", "month");
        }

        Map<String, Object> resultMap = new HashMap<>();
        return response.setBody(resultMap);
    }

    @Override
    public void downloadOtcWeeklyDetails(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateCascaderFilter(parameterMap, "DIVISION");
        String selectedDate = (String) parameterMap.get("selectedDate");
        if (StringUtils.endsWith(selectedDate, "YTD")) {
            parameterMap.put("selectedDate", StringUtils.removeEnd(selectedDate, "YTD"));
            parameterMap.put("selectedDateType", "year");
        } else if (StringUtils.contains(selectedDate, "W")) {
            parameterMap.put("selectedDateType", "week");
        } else if (StringUtils.equals(selectedDate, "-1")) {
            parameterMap.put("selectedDateType", "all");
        } else if (StringUtils.equals(selectedDate, "-2")) {
            parameterMap.put("selectedDateType", "weekly");
        } else {
            parameterMap.put("selectedDateType", "month");
        }
        String fileName = "otc_official_" + Utils.randomStr(4) + ".xlsx";

        SheetInfoWithQueryKey sheet1 = new SheetInfoWithQueryKey();
        sheet1.setParameterMap(parameterMap);
        sheet1.setQueryKey("com.scp.customer.dao.IOTCDao.downloadOtcWeeklyDetails");
        sheet1.setSheetName("Weekly Details");

        excelTemplate.create(res, fileName, sheet1);
    }


    private void generateOtcSummary(List<String> fields, List<Map<String, Object>> resultList) {
        Map<String, Map<String, Object>> resultMap = new HashMap<>();
        for (Map<String, Object> map : resultList) {
            for (String key : map.keySet()) {
                if (fields.contains(key) == false && "TYPE".equals(key) == false && "RATIO".equals(map.get("TYPE")) == false && key.endsWith("_ORDER_BY") == false) {
                    String type = (String) map.get("TYPE");
                    Map<String, Object> subMap = resultMap.computeIfAbsent(type, k -> new HashMap<>());

                    if (subMap.isEmpty()) {
                        for (int i = 0; i < fields.size(); i++) {
                            if (i == fields.size() - 1) {
                                subMap.put(fields.get(i), "Total");
                            }
                        }
                        subMap.put("TYPE", type);
                    }

                    Object totalObject = subMap.get(key); // total cell value
                    Object currentCellObject = map.get(key); // current cell value
                    if (totalObject == null) {
                        subMap.put(key, currentCellObject);
                    } else {
                        if (currentCellObject != null) {
                            subMap.put(key, Utils.parseBigDecimal(totalObject).add(Utils.parseBigDecimal(currentCellObject)));
                        }
                    }
                }
            }
        }

        if (resultMap.isEmpty() == false) {
            Map<String, Object> otMap = resultMap.get("On Time");
            Map<String, Object> nllMap = resultMap.get("Delay");
            Map<String, Object> ratioMap = new HashMap<>();

            for (int i = 0; i < fields.size(); i++) {
                if (i == fields.size() - 1) {
                    ratioMap.put(fields.get(i), "Total");
                }
            }
            ratioMap.put("TYPE", "RATIO");

            for (String key : nllMap.keySet()) {
                BigDecimal otValue = Utils.parseBigDecimal(otMap.get(key), BigDecimal.ZERO);
                BigDecimal nllValue = Utils.parseBigDecimal(nllMap.get(key), BigDecimal.ZERO);
                BigDecimal total = otValue.add(nllValue);
                if (total.compareTo(BigDecimal.ZERO) > 0) {
                    ratioMap.put(key, otValue.divide(total, 6, RoundingMode.HALF_UP));
                }
            }

            resultList.add(ratioMap);
            resultList.add(otMap);
            resultList.add(nllMap);
        }
    }



    /**
     * 生成cascader filter
     *
     * @param parameterMap 参数map
     */
    @SuppressWarnings("unchecked")
    private void generateCascaderFilter(Map<String, Object> parameterMap, String... blackList) {
        // 生成筛选条件
        this.generateCascaderFilterSQL(parameterMap, null, null, "T", "_filters", blackList);

        // 处理field,防止传入空值
        List<String> defaultField = new ArrayList<>();
        defaultField.add("ENTITY");
        List<String> fields = (List<String>) parameterMap.get("field");
        if (fields.isEmpty()) {
            parameterMap.put("field", defaultField);
        } else {
            for (String field : fields) {
                if (Utils.hasInjectionAttack(field)) {
                    parameterMap.put("field", defaultField);
                    break;
                }
            }
        }
    }

}
