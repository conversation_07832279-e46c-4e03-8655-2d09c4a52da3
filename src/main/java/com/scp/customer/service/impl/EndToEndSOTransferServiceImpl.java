package com.scp.customer.service.impl;

import org.apache.commons.lang3.StringEscapeUtils;
import com.scp.customer.EndToEndSOTransferController;
import com.scp.customer.dao.IEndToEndSOTransferDao;
import com.scp.customer.service.IEndToEndSOTransferService;

import com.alibaba.fastjson.JSONObject;
import com.starter.context.bean.*;
import com.starter.context.bean.scptable.ScpTableHelper;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import com.starter.utils.excel.SimpleSheetContentsHandler;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.model.StylesTable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Scope("prototype")
@Transactional
public class EndToEndSOTransferServiceImpl extends ServiceHelper implements IEndToEndSOTransferService {

    @Resource
    private Response response;

    @Resource
    private IEndToEndSOTransferDao endToEndSOTransferDao;

    @Resource
    private ExcelTemplate excelTemplate;


    @Resource
    private ScpTableHelper scpTableHelper;

    @Override
    public Response allowlistInitPage() {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cascader", Utils.parseCascader(endToEndSOTransferDao.allowlistInitPage()));
        List<Map<String, String>> allowlistRuleTypeOpts = endToEndSOTransferDao.allowlistRuleTypeOpts();
        List<String> ruleTypeOpts = allowlistRuleTypeOpts.stream().map(map -> map.get("RULE_NAME")).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<String, List<String>> ruleTypeColumnMap = allowlistRuleTypeOpts.stream().filter(map -> map.get("RULE_NAME") != null && map.get("RULE_COLUMN") != null).collect(Collectors.groupingBy(map -> map.get("RULE_NAME"), Collectors.mapping(map -> map.get("RULE_COLUMN"), Collectors.toList())));
        resultMap.put("ruleTypeOpts", ruleTypeOpts);
        resultMap.put("ruleTypeColumnMap", ruleTypeColumnMap);
        return response.setBody(resultMap);
    }

    @Override
    public Response allowlistQueryReport1(String userid, Map<String, Object> parameterMap) {
        this.generateCascaderFilterSQL(parameterMap);
        String role = endToEndSOTransferDao.queryPageAdmin(userid, EndToEndSOTransferController.PARENT_CODE);
        boolean isAdmin = "ADMIN".equalsIgnoreCase(role) || "SOA_ADMIN".equalsIgnoreCase(role);
        parameterMap.put("isAdmin", isAdmin);
        parameterMap.put("userid", userid);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(endToEndSOTransferDao.allowlistQueryReport1Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(endToEndSOTransferDao.allowlistQueryReport1(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void allowlistDownloadReport1(String userid, Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        String role = endToEndSOTransferDao.queryPageAdmin(userid, EndToEndSOTransferController.PARENT_CODE);
        boolean isAdmin = "ADMIN".equalsIgnoreCase(role) || "SOA_ADMIN".equalsIgnoreCase(role);
        parameterMap.put("isAdmin", isAdmin);
        parameterMap.put("userid", userid);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "e2e_so_transfer_allowlist" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.customer.dao.IEndToEndSOTransferDao.allowlistQueryReport1", parameterMap);
    }

    @Override
    public Response allowlistSaveReport1(String userid, Map<String, Object> parameterMap) {
        scpTableHelper.setExcludeColumn(new ArrayList<>() {{
        }});

        String role = endToEndSOTransferDao.queryPageAdmin(userid, EndToEndSOTransferController.PARENT_CODE);
        boolean isAdmin = "ADMIN".equalsIgnoreCase(role) || "SOA_ADMIN".equalsIgnoreCase(role);
        String ruleType = parameterMap.get("ruleType").toString();
        scpTableHelper.setScpTableInsertHandler((headers, creates) -> endToEndSOTransferDao.allowlistCreateReport1(headers, creates, userid, ruleType));
        scpTableHelper.setScpTableDeleteHandler(deletes -> endToEndSOTransferDao.allowlistDeleteReport1(deletes, userid, isAdmin));
        scpTableHelper.setScpTableUpdateHandler((pk, updates) -> endToEndSOTransferDao.allowlistUpdateReport1(pk, updates, userid, isAdmin));
        scpTableHelper.setWarningMessage("Only ADMIN can modify the data");
        Message message = scpTableHelper.execCRUD(parameterMap);
        return response.setBody(message);
    }

    @Override
    public void downloadReport1Template(Map<String, Object> parameterMap, HttpServletResponse response) {
        String fileName = "allowlist.xlsx";
        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        List<String> report1ColumnsList = (List<String>) parameterMap.get("report1ColumnsList");
        resultList.add(map);
        for (String report1Column : report1ColumnsList) {
            map.put(report1Column, null);
        }
        excelTemplate.create(response, fileName, resultList);
    }


    @Override
    public Response uploadReport1(MultipartFile file, Map<String, Object> parameterMap) {
        String conditions = parameterMap.get("conditions").toString();
        String decodedConditions = StringEscapeUtils.unescapeHtml4(conditions);
        Map<String, Object> conditionsObj = JSONObject.parseObject(decodedConditions);

        StringBuilder sql = new StringBuilder();
        List<String> report1ColumnsList = (List<String>) conditionsObj.get("report1ColumnsList");
        try {
            // 将文件保存在本地
            File tempFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), ".tmp");
            file.transferTo(tempFile);
            List<Map<String, Object>> dataList = new ArrayList<>();

            excelTemplate.read(tempFile, 1, new SimpleSheetContentsHandler() {
                @Override
                public void handleRow(int rowNum) {
                    if (rowNum == 0) {
                        return;
                    }
                    // 从excel中取数据的时候要使用原始的表头
                    Map<String, Object> map = new HashMap<>();
                    boolean isBlackLine = true;
                    for (String v : row) {
                        if (StringUtils.isNotBlank(v)) {
                            isBlackLine = false;
                            break;
                        }
                    }
                    if (!isBlackLine) {
                        int i = 0;

                        for (String column : report1ColumnsList) {
                            map.put(column, row.get(i++));
                        }
                        dataList.add(map);
                    }

                    // 插入数据的时候要使用去空格之后的表头,否则会出现错行
                    if (dataList.size() >= 200) {
                        conditionsObj.put("dataList", dataList);

                        for (int i = 0; i < dataList.size(); i++) {
                            Map<String, Object> item = dataList.get(i);
                            if (i > 0) sql.append(" UNION ALL ");
                            sql.append("SELECT ");
                            for (int j = 0; j < report1ColumnsList.size(); j++) {
                                String col = report1ColumnsList.get(j);
                                if (j > 0) sql.append(", ");
                                String value = String.valueOf(item.get(col));
                                sql.append("'").append(value).append("'").append(" AS ").append(col);
                            }
                            sql.append(" FROM DUAL");
                        }
                        conditionsObj.put("sql", sql.toString());
                        endToEndSOTransferDao.mergeReport1Data(conditionsObj);
                        dataList.clear();
                    }
                }
            }, new StylesTable());

            if (!dataList.isEmpty()) {
                conditionsObj.put("dataList", dataList);
                for (int i = 0; i < dataList.size(); i++) {
                    Map<String, Object> item = dataList.get(i);
                    if (i > 0) sql.append(" UNION ALL ");
                    sql.append("SELECT ");
                    for (int j = 0; j < report1ColumnsList.size(); j++) {
                        String col = report1ColumnsList.get(j);
                        if (j > 0) sql.append(", ");
                        String value = String.valueOf(item.get(col));
                        sql.append("'").append(value).append("'").append(" AS ").append(col);
                    }
                    sql.append(" FROM DUAL");
                }
                conditionsObj.put("sql", sql.toString());
                endToEndSOTransferDao.mergeReport1Data(conditionsObj);
            }

            if (tempFile.delete() == false) {
                System.err.println(tempFile.getAbsolutePath() + " delete failed");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return response;
    }


    @Override
    public Response allowlistQueryReport2(String userid, Map<String, Object> parameterMap) {
        this.generateCascaderFilterSQL(parameterMap);
        String role = endToEndSOTransferDao.queryPageAdmin(userid, EndToEndSOTransferController.PARENT_CODE);
        boolean isAdmin = "ADMIN".equalsIgnoreCase(role) || "SOA_ADMIN".equalsIgnoreCase(role);
        parameterMap.put("isAdmin", isAdmin);
        parameterMap.put("userid", userid);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(endToEndSOTransferDao.allowlistQueryReport2Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(endToEndSOTransferDao.allowlistQueryReport2(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void allowlistDownloadReport2(String userid, Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        String role = endToEndSOTransferDao.queryPageAdmin(userid, EndToEndSOTransferController.PARENT_CODE);
        boolean isAdmin = "ADMIN".equalsIgnoreCase(role) || "SOA_ADMIN".equalsIgnoreCase(role);
        parameterMap.put("isAdmin", isAdmin);
        parameterMap.put("userid", userid);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "e2e_so_transfer_allowlist" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.customer.dao.IEndToEndSOTransferDao.allowlistQueryReport2", parameterMap);
    }
}
