package com.scp.customer.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.scp.customer.dao.ICloStructureDao;
import com.scp.customer.service.ICloStructureService;
import com.starter.context.bean.*;
import com.starter.context.configuration.SCPATableConfiguration;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import com.starter.utils.excel.ISheetInfo;
import com.starter.utils.excel.SheetInfoWithData;
import com.starter.utils.excel.SheetInfoWithQueryKey;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

@Service
@Scope("prototype")
@Transactional
public class CloStructureServiceImpl extends ServiceHelper implements ICloStructureService {

    public final static String PARENT_CODE = "menu895";
    @Resource
    private ExcelTemplate excelTemplate;

    @Resource
    private ICloStructureDao cloStructureDao;

    @Resource
    private Response response;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();

        resultMap.put("cascader", Utils.parseCascader(cloStructureDao.queryFilters(parameterMap)));
        resultMap.put("pivotOpts", cloStructureDao.queryPivotOpts(parameterMap));
        resultMap.put("cloVersionOpts", cloStructureDao.queryCloVersionOpts(parameterMap));
        resultMap.put("availableColumns", cloStructureDao.queryAvailableColumns());

        List<LinkedHashMap<String, Object>> improvTipsList = cloStructureDao.queryImprovTips();
        Map<String, Object> improvTipsMap = new HashMap<>();
        for (Map<String, Object> map : improvTipsList) {
            improvTipsMap.put(String.valueOf(map.get("CODE")), map.get("DESCRIPTION"));
        }
        resultMap.put("improvTips", improvTipsMap);
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        JSONObject selectObj = JSON.parseObject((String) parameterMap.get("report1SelectedValues"));
        if (selectObj != null) {
            if ("Total".equalsIgnoreCase(selectObj.getString("category"))) {
                parameterMap.put("report1Values", new ArrayList<>());
            } else {
                List<String> values = new ArrayList<>(selectObj.getJSONArray("parent").toJavaList(String.class));
                values.add(selectObj.getString("category"));
                parameterMap.put("report1Values", values);
            }
        }
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(cloStructureDao.queryReport1Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(cloStructureDao.queryReport1(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse response) {
        this.generateFilter(parameterMap);
        JSONObject selectObj = JSON.parseObject((String) parameterMap.get("report1SelectedValues"));
        if (selectObj != null) {
            if ("Total".equalsIgnoreCase(selectObj.getString("category"))) {
                parameterMap.put("report1Values", new ArrayList<>());
            } else {
                List<String> values = new ArrayList<>(selectObj.getJSONArray("parent").toJavaList(String.class));
                values.add(selectObj.getString("category"));
                parameterMap.put("report1Values", values);
            }
        }
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        SheetInfoWithQueryKey sheet1 = new SheetInfoWithQueryKey();
        sheet1.setQueryKey("com.scp.customer.dao.ICloStructureDao.downloadReport1");
        sheet1.setParameterMap(parameterMap);
        sheet1.setSheetName("RAW DATA");

        SheetInfoWithQueryKey sheet2 = new SheetInfoWithQueryKey();
        sheet2.setQueryKey("com.scp.customer.dao.ICloStructureDao.downloadReport1Tips");
        sheet2.setSheetName("IMPROVEMENT TIPS");

        List<String> availableTips = cloStructureDao.queryReport1AvailableTips();

        List<ISheetInfo> sheetList = new ArrayList<>();
        sheetList.add(sheet1);
        sheetList.add(sheet2);

        for (String tipsName : availableTips) {
            String tableName = "CLO_STRUCTURE_TIPS_V_$" + tipsName;
            parameterMap.put("tableName", tableName);
            List<TblColumn> columns = SCPATableConfiguration.getTblColumnsByTableName(tableName);
            List<String> joinColumns = columns.stream().filter(e -> e.getName().startsWith("T0_")
                            && e.getType().equals("VARCHAR2")
                            && SCPATableConfiguration.isColumnInTable("CLO_STRUCTURE_V", e.getName()))
                    .map(TblColumn::getName).toList();
            if (joinColumns.isEmpty()) {
                continue;
            }
            parameterMap.put("joinColumns", joinColumns);
            List<LinkedHashMap<String, Object>> evidenceData = cloStructureDao.downloadReport1Evidence(parameterMap);
            if (evidenceData.isEmpty() == false) {
                SheetInfoWithData evidence = new SheetInfoWithData();
                evidence.setDataList(evidenceData);
                evidence.setSheetName("Evidence of " + tipsName);
                sheetList.add(evidence);
            }
        }

        String fileName = "clo_structure_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, sheetList.toArray(new ISheetInfo[]{}));
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this.queryReport1")
    public Response saveReport1Comments(Map<String, Object> parameterMap) {
        cloStructureDao.saveReport1Comments(parameterMap);
        return response;
    }

    private void generateFilter(Map<String, Object> parameterMap) {
        List<String> tipsList = new ArrayList<>();
        this.generateCascaderFilterSQL(parameterMap, value -> {
            String key = value.getKey();
            if (key.equals("T0_IMPROVEMENT_TIPS")) {
                tipsList.add(value.getValue());
                return false;
            }
            return true;
        });
        parameterMap.put("tipsList", tipsList);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);

        JSONArray report2Categories = (JSONArray) parameterMap.get("report2Categories");
        if (report2Categories.isEmpty()) {
            report2Categories.add("T0_BU");
            report2Categories.add("T0_PRODUCT_LINE");
            report2Categories.add("T0_ENTITY");
            report2Categories.add("T0_VENDOR_NAME");
            report2Categories.add("T0_STOCKING_POLICY");
        }
        parameterMap.put("category", report2Categories.get(0));
        List<Map<String, Object>> resultList = cloStructureDao.queryReport2(parameterMap);
        double max = this.renderReport2Width(resultList, 0);
        for (Map<String, Object> map : resultList) {
            map.put("parentMaxWidth", max);
            map.put("parent", new ArrayList<>());
            map.put("id", Utils.randomStr(12));
            map.put("hasChildren", report2Categories.size() > 1 && "Total".equals(map.get("category")) == false);
        }
        return response.setBody(resultList);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2Sub(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);

        JSONArray report2Categories = (JSONArray) parameterMap.get("report2Categories");
        JSONArray parent = (JSONArray) parameterMap.get("parent");
        double parentMaxWidth = Utils.parseDouble(parameterMap.get("parentMaxWidth"));
        String expandValue = (String) parameterMap.get("expandValue");
        String expandColumn = report2Categories.getString(parent.size());
        parameterMap.put("expandValue", expandValue);
        parameterMap.put("expandColumn", expandColumn);
        parameterMap.put("category", report2Categories.getString(parent.size() + 1));
        List<Map<String, Object>> resultList = cloStructureDao.queryReport2Sub(parameterMap);

        this.renderReport2Width(resultList, parentMaxWidth);
        for (Map<String, Object> map : resultList) {
            map.put("id", Utils.randomStr(12));
            map.put("hasChildren", report2Categories.size() > parent.size() + 2);
            JSONArray temp = new JSONArray();
            temp.addAll(parent);
            temp.add(expandValue);
            map.put("parent", temp);
            map.put("parentMaxWidth", parentMaxWidth);
        }
        return response.setBody(resultList);
    }

    private double renderReport2Width(List<Map<String, Object>> data, double parentMax) {
        double max = Math.max(0, parentMax);
        for (Map<String, Object> map : data) {
            if ("Total".equals(map.get("category")) == false) {
                max = Math.max(max, Utils.parseDouble(map.get("fail")));
                max = Math.max(max, Utils.parseDouble(map.get("meet")));
            }
        }

        for (Map<String, Object> map : data) {
            if ("Total".equals(map.get("category")) == false) {
                if (max != 0) {
                    map.put("failWidth", Utils.parseDouble(map.get("fail")) / max * 100);
                    map.put("meetWidth", Utils.parseDouble(map.get("meet")) / max * 100);
                } else {
                    map.put("failWidth", 0);
                    map.put("meetWidth", 0);
                }
            }
        }
        return max;
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        JSONObject selectObj = JSON.parseObject((String) parameterMap.get("report2SelectedValues"));
        if ("Total".equalsIgnoreCase(selectObj.getString("category"))) {
            parameterMap.put("report2Values", new ArrayList<>());
        } else {
            List<String> values = new ArrayList<>(selectObj.getJSONArray("parent").toJavaList(String.class));
            values.add(selectObj.getString("category"));
            parameterMap.put("report2Values", values);
        }

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(cloStructureDao.queryReport2DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(cloStructureDao.queryReport2Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport2Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        JSONObject selectObj = JSON.parseObject((String) parameterMap.get("report2SelectedValues"));
        if ("Total".equalsIgnoreCase(selectObj.getString("category"))) {
            parameterMap.put("report2Values", new ArrayList<>());
        } else {
            List<String> values = new ArrayList<>(selectObj.getJSONArray("parent").toJavaList(String.class));
            values.add(selectObj.getString("category"));
            parameterMap.put("report2Values", values);
        }

        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "clo_structure_details_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.customer.dao.ICloStructureDao.queryReport2Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        JSONObject selectObj = JSON.parseObject((String) parameterMap.get("report3SelectedValues"));
        if (selectObj != null) {
            if ("Total".equalsIgnoreCase(selectObj.getString("category"))) {
                parameterMap.put("report3Values", new ArrayList<>());
            } else {
                List<String> values = new ArrayList<>(selectObj.getJSONArray("parent").toJavaList(String.class));
                values.add(selectObj.getString("category"));
                parameterMap.put("report3Values", values);
            }
        }

        List<Map<String, Object>> data = cloStructureDao.queryReport3(parameterMap);

        // 做数据转置
        List<String> legend = data.stream().map(e -> (String) e.get("T0_CLO_LT_GROUP")).distinct().sorted().toList();
        List<String> xAxis = data.stream().map(e -> (String) e.get("XAIXS")).distinct().sorted().toList();

        Map<String, Map<String, Map<String, Object>>> tempSeries = new HashMap<>();
        List<Map<String, Object>> series = new ArrayList<>();
        Map<String, Object> resultMap = new HashMap<>();

        for (Map<String, Object> map : data) {
            String lt = (String) map.get("T0_CLO_LT_GROUP");
            String x = (String) map.get("XAIXS");
            Map<String, Map<String, Object>> temp1 = tempSeries.computeIfAbsent(lt, k -> new HashMap<>());
            Map<String, Object> temp2 = temp1.computeIfAbsent(x, k -> new HashMap<>());
            temp2.put("MEET_VAL", map.get("MEET_VAL"));
            temp2.put("FAIL_VAL", map.get("FAIL_VAL"));
        }

        Map<String, BigDecimal> meetTotalMap = new HashMap<>();
        Map<String, BigDecimal> failTotalMap = new HashMap<>();
        for (String l : legend) {
            Map<String, Object> s = new LinkedHashMap<>();
            Map<String, Map<String, Object>> d = tempSeries.get(l);

            List<BigDecimal> meetVal = new ArrayList<>();
            List<BigDecimal> failVal = new ArrayList<>();

            for (String x : xAxis) {
                Map<String, Object> v = d.get(x);
                if (v != null) {
                    BigDecimal meet = Utils.parseBigDecimal(v.get("MEET_VAL"));
                    BigDecimal fail = Utils.parseBigDecimal(v.get("FAIL_VAL"));
                    meetVal.add(meet);
                    failVal.add(fail);
                    BigDecimal tv = meetTotalMap.computeIfAbsent(x, k -> BigDecimal.ZERO);
                    meetTotalMap.put(x, tv.add(meet));

                    tv = failTotalMap.computeIfAbsent(x, k -> BigDecimal.ZERO);
                    failTotalMap.put(x, tv.add(fail));
                } else {
                    meetVal.add(null);
                    failVal.add(null);
                }
            }
            s.put("name", l);
            s.put("meetVal", meetVal);
            s.put("failVal", failVal);
            series.add(s);
        }

        List<BigDecimal> percent = new ArrayList<>();
        BigDecimal minPercent = BigDecimal.valueOf(200);
        for (String x : xAxis) {
            BigDecimal meet = meetTotalMap.getOrDefault(x, BigDecimal.ZERO);
            BigDecimal fail = failTotalMap.getOrDefault(x, BigDecimal.ZERO);
            BigDecimal total = meet.add(fail.abs());
            BigDecimal per = BigDecimal.ZERO;
            if (total.compareTo(BigDecimal.ZERO) != 0) {
                per = meet.divide(total, 3, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(1, RoundingMode.HALF_UP);
            }
            minPercent = per.min(minPercent);
            percent.add(per);
        }

        resultMap.put("minPercent", minPercent.subtract(BigDecimal.TEN).min(BigDecimal.valueOf(100)));
        resultMap.put("percent", percent);
        resultMap.put("series", series);
        resultMap.put("xAxis", xAxis);
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        JSONObject selectObj = JSON.parseObject((String) parameterMap.get("report3SelectedValues"));
        if (selectObj != null) {
            if ("Total".equalsIgnoreCase(selectObj.getString("category"))) {
                parameterMap.put("report3Values", new ArrayList<>());
            } else {
                List<String> values = new ArrayList<>(selectObj.getJSONArray("parent").toJavaList(String.class));
                values.add(selectObj.getString("category"));
                parameterMap.put("report3Values", values);
            }
        }

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(cloStructureDao.queryReport3DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(cloStructureDao.queryReport3Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        JSONObject selectObj = JSON.parseObject((String) parameterMap.get("report3SelectedValues"));
        if (selectObj != null) {
            if ("Total".equalsIgnoreCase(selectObj.getString("category"))) {
                parameterMap.put("report3Values", new ArrayList<>());
            } else {
                List<String> values = new ArrayList<>(selectObj.getJSONArray("parent").toJavaList(String.class));
                values.add(selectObj.getString("category"));
                parameterMap.put("report3Values", values);
            }
        }

        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "clo_structure_details_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.customer.dao.ICloStructureDao.queryReport3Details", parameterMap);
    }
}
