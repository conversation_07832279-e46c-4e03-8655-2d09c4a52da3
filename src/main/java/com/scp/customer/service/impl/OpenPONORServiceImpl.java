package com.scp.customer.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.scp.customer.bean.OpenPONORReport1Bean;
import com.scp.customer.bean.OpenPONORReport1Treemap;
import com.scp.customer.bean.OpenPONORReport2Bean;
import com.scp.customer.bean.OpenPONORReport3Bean;
import com.scp.customer.dao.IOpenPONORDao;
import com.scp.customer.service.IOpenPONORService;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.servlet.ServiceHelper;
import com.starter.login.bean.Session;
import com.starter.utils.AuthUtils;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service("openPONORServiceImpl")
@Scope("prototype")
@Transactional
public class OpenPONORServiceImpl extends ServiceHelper implements IOpenPONORService {

    @Resource
    private Response response;
    @Resource
    private IOpenPONORDao openPONORDao;

    public static final String PARENT_CODE = "menu855";

    @Resource
    private ExcelTemplate excelTemplate;

    private final HashMap<String, String> tooltip1ColumnMap = new HashMap<>();
    private final HashMap<String, String> column1TooltipMap = new HashMap<>();

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage(Map<String, Object> parameterMap) {
        Map<String, Object> result = new HashMap<>();
        result.put("CASCADER", Utils.parseCascader(openPONORDao.queryCascader(parameterMap)));

        return response.setBody(result);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) throws Exception {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateReport1Tooltips(parameterMap);

        // 将前台传过来的label转换成列名, 同时也可以防止恶意代码注入
        parameterMap.put("level1", this.getColumnNameByLabel(parameterMap.get("level1")));
        parameterMap.put("level2", this.getColumnNameByLabel(parameterMap.get("level2")));
        parameterMap.put("level3", this.getColumnNameByLabel(parameterMap.get("level3")));
        parameterMap.put("level4", this.getColumnNameByLabel(parameterMap.get("level4")));
        parameterMap.put("level5", this.getColumnNameByLabel(parameterMap.get("level5")));

        List<OpenPONORReport1Treemap> resultList = new ArrayList<>();
        List<OpenPONORReport1Bean> dataList = openPONORDao.queryReport1(parameterMap);
        for (OpenPONORReport1Bean data : dataList) {
            this.convertReport1Data(resultList, data);
        }
        return response.setBody(resultList);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);

        List<OpenPONORReport2Bean> dataList;

        dataList = openPONORDao.queryReport2(parameterMap);

        Map<String, BigDecimal> dataMap = new HashMap<>();
        LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();
        Map<String, String> xAxisMap = new HashMap<>();

        List<String> legend = new ArrayList<>();

        for (OpenPONORReport2Bean data : dataList) {
            dataMap.put(data.getKey(), data.getVALUE());
            xAxisMap.put(data.getCALENDAR_DATE(), "");
            if (!legend.contains(data.getNAME())) {
                legend.add(data.getNAME());
            }
        }
        List<String> xAxisList = xAxisMap.keySet().stream().sorted(String::compareTo).collect(Collectors.toList());
        for (String l : legend) {
            List<BigDecimal> temp = new ArrayList<>();

            for (String x : xAxisList) {
                temp.add(dataMap.getOrDefault(l + "#" + x, BigDecimal.ZERO));
            }
            resultMap.put(l, temp);
        }

        resultMap.put("xAxis", xAxisList);

        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(openPONORDao.queryReport2DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(openPONORDao.queryReport2Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport2Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName;
        fileName = "open_po_nor_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.customer.dao.IOpenPONORDao.queryReport2Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);

        List<OpenPONORReport3Bean> dataList;

        dataList = openPONORDao.queryReport3(parameterMap);

        Map<String, BigDecimal> dataMap = new HashMap<>();
        LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();
        Map<String, String> xAxisMap = new HashMap<>();

        List<String> legend = openPONORDao.queryReport3Legend(parameterMap);

        for (OpenPONORReport3Bean data : dataList) {
            dataMap.put(data.getKey(), data.getVALUE());
            xAxisMap.put(data.getCALENDAR_DATE(), "");
        }
        List<String> xAxisList = xAxisMap.keySet().stream().sorted(String::compareTo).collect(Collectors.toList());
        for (String l : legend) {
            List<BigDecimal> temp = new ArrayList<>();

            for (String x : xAxisList) {
                temp.add(dataMap.getOrDefault(l + "#" + x, BigDecimal.ZERO));
            }
            resultMap.put(l, temp);
        }

        resultMap.put("xAxis", xAxisList);
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(openPONORDao.queryReport3DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(openPONORDao.queryReport3Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        String fileName = "open_po_nor_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.customer.dao.IOpenPONORDao.queryReport3Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport4(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        List<LinkedHashMap<String, Object>> dataList = openPONORDao.queryReport4(parameterMap);

        page.setData(dataList);

        return response.setBody(page);
    }

    @Override
    public void downloadReport4(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "open_po_nor" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.customer.dao.IOpenPONORDao.queryReport4", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport4Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        this.generateReport4Filter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);

        page.setTotal(openPONORDao.queryReport4DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(openPONORDao.queryReport4Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport4MODetails(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        this.generateReport4Filter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);

        page.setTotal(openPONORDao.queryReport4MODetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(openPONORDao.queryReport4MODetails(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport4Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        this.generateReport4Filter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);

        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "open_po_nor" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.customer.dao.IOpenPONORDao.queryReport4Details", parameterMap);
    }

    @Override
    public void downloadReport4MODetails(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        this.generateReport4Filter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);

        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "open_po_nor_mo" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.customer.dao.IOpenPONORDao.queryReport4MODetails", parameterMap);
    }

    private void generateReport4Filter(Map<String, Object> parameterMap) {
        String report4SelectedColum = (String) parameterMap.get("report4SelectedColumn");

        if ("NORO_FLAG".equalsIgnoreCase((String) parameterMap.get("reportCalType"))) {
            switch (report4SelectedColum) {
                case "RC_EQ_0" -> parameterMap.put("report4Filter", "T.NORO_COUNT  = 0");
                case "RC_EQ_1" -> parameterMap.put("report4Filter", "T.NORO_COUNT  = 1");
                case "RC_EQ_2" -> parameterMap.put("report4Filter", "T.NORO_COUNT  = 2");
                case "RC_EQ_3" -> parameterMap.put("report4Filter", "T.NORO_COUNT  = 3");
                case "RC_GE_4_LE_5" -> parameterMap.put("report4Filter", "T.NORO_COUNT IN (4, 5)");
                case "RC_GE_6_LE_10" -> parameterMap.put("report4Filter", "T.NORO_COUNT IN (6, 7, 8, 9, 10)");
                case "RC_GE_11" -> parameterMap.put("report4Filter", "T.NORO_COUNT >= 11");
            }
        } else {
            switch (report4SelectedColum) {
                case "RC_EQ_0" -> parameterMap.put("report4Filter", "T.NOR_COUNT  = 0");
                case "RC_EQ_1" -> parameterMap.put("report4Filter", "T.NOR_COUNT  = 1");
                case "RC_EQ_2" -> parameterMap.put("report4Filter", "T.NOR_COUNT  = 2");
                case "RC_EQ_3" -> parameterMap.put("report4Filter", "T.NOR_COUNT  = 3");
                case "RC_GE_4_LE_5" -> parameterMap.put("report4Filter", "T.NOR_COUNT IN (4, 5)");
                case "RC_GE_6_LE_10" -> parameterMap.put("report4Filter", "T.NOR_COUNT IN (6, 7, 8, 9, 10)");
                case "RC_GE_11" -> parameterMap.put("report4Filter", "T.NOR_COUNT >= 11");
            }
        }
    }

    /**
     * 将列表转化为Tree数据
     *
     * @param list 输出树
     * @param data 输入值
     * @throws Exception 异常
     */
    private void convertReport1Data(List<OpenPONORReport1Treemap> list, OpenPONORReport1Bean data) throws Exception {
        String[] categorysOrg = new String[]{data.getCategory1(), data.getCategory2(), data.getCategory3(), data.getCategory4(), data.getCategory5()};
        List<String> categories = new ArrayList<>();

        for (String category : categorysOrg) {
            if (StringUtils.isNotBlank(category)) {
                categories.add(category);
            } else {
                break;
            }
        }
        // 这边逻辑比较复杂, 所以用最笨的方法来描述了, 以免后期不好维护
        // 先把这一行数据转成treemap的数据
        // 第一个节点
        List<OpenPONORReport1Treemap> child = new ArrayList<>();
        OpenPONORReport1Treemap root = new OpenPONORReport1Treemap();
        root.setName(categories.get(0));
        root.setTips(data.copyTooltips()); // 因为这个tooltips要放在树中全局使用, 所以必须要生成一个新节点
        root.setChildren(child);

        // 中间节点
        for (int i = 1; i < categories.size() - 1; i++) {
            OpenPONORReport1Treemap treemap = new OpenPONORReport1Treemap();
            treemap.setName(categories.get(i));
            treemap.setTips(data.copyTooltips());
            child.add(treemap);
            child = new ArrayList<>();
            treemap.setChildren(child);
        }

        // 最后一个节点
        OpenPONORReport1Treemap lastNode = new OpenPONORReport1Treemap();
        lastNode.setName(categories.get(categories.size() - 1));
        lastNode.setValue(data.getValue());
        lastNode.setTips(data.copyTooltips());
        child.add(lastNode);

        // 将这行treemap与原始数据相加
        // 先找到list中是否有这个数据节点
        Optional<OpenPONORReport1Treemap> beanOpt = list.stream().filter(b -> b.getName().equals(categories.get(0))).findFirst();
        if (beanOpt.isPresent()) {
            OpenPONORReport1Treemap bean = beanOpt.get();
            bean.add(root); // 两个节点合并
        } else { //找不到的时候最省事, 直接放入list就可以了
            list.add(root);
        }
    }

    private void generateValueColumn(Map<String, Object> parameterMap) {
        String resultType = (String) parameterMap.get("resultType");

        String qtyColumn = "ORDER_QUANTITY";

        String valueColumn = "AVG_SELLING_PRICE_RMB";
        if ("Quantity".equalsIgnoreCase(resultType)) {
            valueColumn = "SUM(" + qtyColumn + ")";
        } else if ("Line".equalsIgnoreCase(resultType)) {
            valueColumn = "COUNT(1)";
        } else if ("Value".equalsIgnoreCase(resultType)) {
            valueColumn = "SUM(AVG_SELLING_PRICE_RMB * " + qtyColumn + ")";
        }
        parameterMap.put("valueColumn", valueColumn);
    }

    @SuppressWarnings("unchecked")
    private void generateFilter(Map<String, Object> parameterMap) {
        this.generateCascaderFilterSQL(parameterMap);

        Session session = (Session) parameterMap.get("session");
        parameterMap.putAll(AuthUtils.generateConditions(session, PARENT_CODE, "personalFilters"));
    }

    private void generateTreePathFilter(Map<String, Object> parameterMap) {
        String selectedTreePath = (String) parameterMap.get("selectedTreePath");
        if (StringUtils.isNotBlank(selectedTreePath)) {
            List<String> conditions = new ArrayList<>();
            String[] treePaths = selectedTreePath.split(" > ");
            for (int i = 1; i <= Math.min(treePaths.length, 5); i++) {
                String key = Utils.randomStr(8);
                if ("Others".equals(StringUtils.trim(treePaths[i - 1]))) {
                    String name = this.getColumnNameByLabel(parameterMap.get("level" + i));
                    conditions.add("(" + name + " = #{" + key + ",jdbcType=VARCHAR} or " + name + " is null )");
                } else {
                    conditions.add(this.getColumnNameByLabel(parameterMap.get("level" + i)) + " = #{" + key + ",jdbcType=VARCHAR}");
                }
                parameterMap.put(key, StringUtils.trim(treePaths[i - 1]));
            }

            parameterMap.put("treePathFilter", "(" + StringUtils.join(conditions, " and ") + ")");
        }
    }

    private void generateReport1Tooltips(Map<String, Object> parameterMap) {
        List<String> tooltips = ((JSONArray) parameterMap.get("report1Tooltips")).toJavaList(String.class);
        if (!tooltips.isEmpty()) {
            List<String> tooltipsColumns = tooltips.stream().map(
                    this::getColumnNameByLabel).collect(Collectors.toList());
            List<String> tooltipsColumnsName = new ArrayList<>();

            for (String c : tooltipsColumns) {
                String tooltip = column1TooltipMap.getOrDefault(c, c);
                tooltipsColumnsName.add("NVL(SUM(" + c + "),0) AS " + tooltip);
            }
            parameterMap.put("tooltipsColumns", StringUtils.join(tooltipsColumnsName, ", "));
        }
    }

    private String getColumnNameByLabel(Object labelObj) {
        String label = (String) labelObj;
        if (label == null) {
            return null;
        }
        if (Utils.hasInjectionAttack(label)) {
            return "";
        }
        return tooltip1ColumnMap.getOrDefault(label, label);
    }
}
