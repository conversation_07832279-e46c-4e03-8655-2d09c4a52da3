package com.scp.customer.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import com.starter.utils.excel.SheetInfoWithQueryKey;
import com.starter.context.bean.CacheRemove;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.scp.customer.dao.IOTDSDao;
import com.scp.customer.service.IOTDSService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Scope("prototype")
@Transactional
public class OTDSServiceImpl extends ServiceHelper implements IOTDSService {

    @Resource
    private Response response;

    @Resource
    private IOTDSDao otdsDataDao;

    @Resource
    private ExcelTemplate excelTemplate;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage() {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("rcaCode", otdsDataDao.queryRacCode());

        List<LinkedHashMap<String, Object>> tipsList = otdsDataDao.queryOtdsRcaTips();
        Map<String, String> tipsMap = new HashMap<>();
        for (Map<String, Object> map : tipsList) {
            String des = (String) map.get("DESCRIPTION");
            String logic = (String) map.get("COMPUTING_LOGIC");
            if (logic != null) {
                des += ". " + logic;
            }
            tipsMap.put(String.valueOf(map.get("RCA_TIPS_CODE")), des);
        }
        resultMap.put("rcaTips", tipsMap);
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryColumnsByDaterange(Map<String, Object> parameterMap) {
        return response.setBody(this.queryColumnsByDaterangeSource(parameterMap));
    }

    /**
     * calc weeks and days between start month and end month
     *
     * @param parameterMap parameters
     * @return result map
     */
    private Map<String, List<String>> queryColumnsByDaterangeSource(Map<String, Object> parameterMap) {
        Map<String, List<String>> resultMap = new HashMap<>();

        resultMap.put("weeks", otdsDataDao.queryOtdsWeeklyWeekColumns(parameterMap));
        resultMap.put("months", otdsDataDao.queryOtdsWeeklyMonthColumns(parameterMap));
        resultMap.put("years", otdsDataDao.queryOtdsWeeklyYearColumns(parameterMap));

        Calendar today = Calendar.getInstance();
        List<String> days = new ArrayList<>();
        SimpleDateFormat format = new SimpleDateFormat("MM/dd");
        resultMap.put("days", days);
        today.set(Calendar.DAY_OF_WEEK, 1);
        today.add(Calendar.WEEK_OF_YEAR, -1);
        for (int i = 0; i < 21; i++) {
            days.add(format.format(today.getTime()));
            today.add(Calendar.DAY_OF_YEAR, 1);
        }

        return resultMap;
    }

    @Override
    @SuppressWarnings("unchecked")
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryOtdsWeekly(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap, "DIVISION");
        String reportOrderBy = (String) parameterMap.get("reportOrderBy");
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        List<String> fields = (List<String>) parameterMap.get("field");
        String sort = page.getSort();
        if (!fields.contains(page.getSortColumn())) {
            sort = sort.replace("\"", "\"'");
            if (Objects.equals(reportOrderBy, "ONTIME") || Objects.equals(reportOrderBy, "FAIL")) {
                sort = sort.replace("\"' ", "'_" + reportOrderBy + "\" ");
            } else {
                sort = sort.replace("\"' ", "'_RATIO\" ");
            }

            page.setSort(sort);
        }

        Map<String, List<String>> columnMap = this.queryColumnsByDaterangeSource(parameterMap);
        parameterMap.putAll(columnMap);

        parameterMap.put("defaultOrderColumn", columnMap.get("years").get(0));
        List<Map<String, Object>> dataList = otdsDataDao.queryOtdsWeekly(parameterMap);
        List<Map<String, Object>> resultList = new ArrayList<>();

        List<String> columns = columnMap.get("years");
        columns.addAll(columnMap.get("months"));
        columns.addAll(columnMap.get("weeks"));

        Map<String, Object> tempMap;
        for (Map<String, Object> map : dataList) {
            // RATIO
            tempMap = new HashMap<>();
            for (String field : fields) {
                tempMap.put(field, map.get(field));
            }
            tempMap.put("TYPE", "RATIO");
            for (String c : columns) {
                tempMap.put(c, map.get("'" + c + "'_RATIO"));
            }
            resultList.add(tempMap);
            // On Time
            tempMap = new HashMap<>();
            for (String field : fields) {
                tempMap.put(field, map.get(field));
            }
            tempMap.put("TYPE", "On Time");
            for (String c : columns) {
                tempMap.put(c, map.get("'" + c + "'_ONTIME"));
            }
            resultList.add(tempMap);

            // Delay
            tempMap = new HashMap<>();
            for (String field : fields) {
                tempMap.put(field, map.get(field));
            }
            tempMap.put("TYPE", "Delay");
            for (String c : columns) {
                tempMap.put(c, map.get("'" + c + "'_FAIL"));
                tempMap.put(c + "_CONFIRMED", map.get("'" + c + "'_CONFIRMED"));
            }
            resultList.add(tempMap);
        }

        this.generateOtdsSummary(fields, resultList);
        page.setData(resultList);
        page.setTotal(resultList.size());

        return response.setBody(page);
    }

    @Override
    @SuppressWarnings("unchecked")
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryOtdsDaily(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);
        List<String> fields = (List<String>) parameterMap.get("field");

        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        String sort = page.getSort();
        String orderByTotal = null;
        String orders = null;
        String reportOrderBy = (String) parameterMap.get("reportOrderBy");
        if (StringUtils.contains(sort, "\"TOTAL_") == true) {
            orderByTotal = sort.split("\"")[1];
            orders = sort.split("\"")[2].trim();
            page.setSort(null);
        } else if (StringUtils.contains(sort, "\"WTD") == true) {
            orderByTotal = "WTD";
            orders = sort.split("\"")[2].trim();
            page.setSort(null);
        } else if (fields.contains(page.getSortColumn()) == false) {
            sort = sort.replace("\"", "\"'");
            if (Objects.equals(reportOrderBy, "ONTIME") || Objects.equals(reportOrderBy, "FAIL")) {
                sort = sort.replace("\"' ", "'_" + reportOrderBy + "\" ");
            } else {
                sort = sort.replace("\"' ", "'_RATIO\" ");
            }
            page.setSort(sort);
        }

        Map<String, List<String>> columnMap = this.queryColumnsByDaterangeSource(parameterMap);
        parameterMap.putAll(columnMap);

        List<Map<String, Object>> dataList = otdsDataDao.queryOtdsDaily(parameterMap);

        List<String> columns = columnMap.get("days");
        List<List<Map<String, Object>>> groupList = new ArrayList<>();
        for (Map<String, Object> map : dataList) {
            List<Map<String, Object>> subGroup = new ArrayList<>();
            // RATIO
            Map<String, Object> tempMap = new HashMap<>();
            for (String field : fields) {
                tempMap.put(field, map.get(field));
            }
            tempMap.put("TYPE", "RATIO");
            for (String c : columns) {
                tempMap.put(c, map.get("'" + c + "'_RATIO"));
            }
            subGroup.add(tempMap);

            // On Time
            tempMap = new HashMap<>();
            for (String field : fields) {
                tempMap.put(field, map.get(field));
            }
            tempMap.put("TYPE", "On Time");
            for (String c : columns) {
                tempMap.put(c, map.get("'" + c + "'_ONTIME"));
            }
            subGroup.add(tempMap);

            // Delay
            tempMap = new HashMap<>();
            for (String field : fields) {
                tempMap.put(field, map.get(field));
            }
            tempMap.put("TYPE", "Delay");
            for (String c : columns) {
                tempMap.put(c, map.get("'" + c + "'_FAIL"));
            }
            subGroup.add(tempMap);

            groupList.add(subGroup);
        }

        // calc weekly summary
        @SuppressWarnings("unchecked")
        List<String> days = (List<String>) parameterMap.get("days");
        List<String> wtds = this.getOtdsWTDDays();

        for (List<Map<String, Object>> list : groupList) {
            for (Map<String, Object> map : list) {
                int w = 0;
                BigDecimal total = BigDecimal.ZERO;
                BigDecimal total2 = BigDecimal.ZERO;
                for (int i = 1; i <= days.size(); i++) {
                    String key = days.get(i - 1);
                    BigDecimal value = Utils.parseBigDecimal(map.get(key), null);
                    if (value != null) {
                        total = total.add(value);

                        // WTD
                        if (wtds.contains(key)) {
                            total2 = total2.add(value);
                            w++;
                        }
                    }
                    if (i % 7 == 0) {
                        boolean isRatio = "RATIO".equals(map.get("TYPE"));
                        switch (i) {
                            case 7 -> map.put("TOTAL_LAST", isRatio ? BigDecimal.ZERO : total);
                            case 14 -> map.put("TOTAL_THIS", isRatio ? BigDecimal.ZERO : total);
                            case 21 -> map.put("TOTAL_NEXT", isRatio ? BigDecimal.ZERO : total);
                        }

                        total = BigDecimal.ZERO;
                    }
                }
                // WTD
                BigDecimal avg = (w == 0 ? BigDecimal.ZERO : total2.divide(BigDecimal.valueOf(w), 4, RoundingMode.HALF_UP));
                boolean isRatio = "RATIO".equals(map.get("TYPE"));
                map.put("WTD", isRatio ? avg : total2);
            }

            // total percent
            String[] array = new String[]{"TOTAL_LAST", "TOTAL_THIS", "TOTAL_NEXT"};
            for (String key : array) {
                BigDecimal ontime = Utils.parseBigDecimal(list.get(1).get(key), BigDecimal.ZERO);
                BigDecimal delay = Utils.parseBigDecimal(list.get(2).get(key), BigDecimal.ZERO);
                BigDecimal total = ontime.add(delay);
                list.get(0).put(key, total.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : ontime.divide(total, 4, RoundingMode.HALF_UP));
            }
        }


        // total sorting
        if (orderByTotal != null) {
            String finalOrderByTotal = orderByTotal;
            String finalOrders = orders;
            groupList = groupList.stream().sorted((e1, e2) -> {
                if (StringUtils.equalsIgnoreCase(finalOrders, "asc")) {
                    return ((BigDecimal) e1.get(0).get(finalOrderByTotal)).compareTo((BigDecimal) e2.get(0).get(finalOrderByTotal));
                } else {
                    return ((BigDecimal) e2.get(0).get(finalOrderByTotal)).compareTo((BigDecimal) e1.get(0).get(finalOrderByTotal));
                }
            }).collect(Collectors.toList());
        }

        List<Map<String, Object>> resultList = new ArrayList<>();
        for (List<Map<String, Object>> list : groupList) {
            resultList.addAll(list);
        }

        this.generateOtdsSummary(fields, resultList);
        page.setData(resultList);
        page.setTotal(resultList.size());

        return response.setBody(page);
    }

    @Override
    @SuppressWarnings("unchecked")
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryOtdsWeeklySummary(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap, "DIVISION");
        List<String> fields = (List<String>) parameterMap.get("field");

        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        String sort = page.getSort();
        if (StringUtils.contains(sort, "_RATIO")) {
            String[] as = sort.split(" ");
            sort = "nvl(" + as[0] + ", -1) " + as[1];
        }
        page.setSort(sort);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        Map<String, List<String>> columnMap = this.queryColumnsByDaterangeSource(parameterMap);
        parameterMap.putAll(columnMap);


        List<Map<String, Object>> dataList = otdsDataDao.queryOtdsWeeklySummary(parameterMap);
        List<String> weeks = columnMap.get("weeks");

        Map<String, Object> totalMap = new HashMap<>();
        for (int i = 0; i < fields.size(); i++) {
            if (i == fields.size() - 1) {
                totalMap.put(fields.get(i), "Total");
            }
        }

        for (String week : weeks) {
            BigDecimal confirmed = BigDecimal.ZERO;
            BigDecimal total = BigDecimal.ZERO;
            for (Map<String, Object> map : dataList) {
                confirmed = confirmed.add(Utils.parseBigDecimal(map.get("'" + week + "'_CONFIRMED"), BigDecimal.ZERO));
                total = total.add(Utils.parseBigDecimal(map.get("'" + week + "'_FAIL"), BigDecimal.ZERO));
            }
            totalMap.put("'" + week + "'_FAIL", total.compareTo(BigDecimal.ZERO) == 0 ? null : total);
            totalMap.put("'" + week + "'_CONFIRMED", confirmed.compareTo(BigDecimal.ZERO) == 0 ? null : confirmed);
            totalMap.put("'" + week + "'_RATIO", total.compareTo(BigDecimal.ZERO) == 0 ? null : confirmed.multiply(BigDecimal.valueOf(100)).divide(total, 1, RoundingMode.HALF_UP));
        }

        dataList.add(totalMap);
        page.setData(dataList);
        page.setTotal(dataList.size());
        return response.setBody(page);
    }

    @Override
    public Response queryOtdsTipsDescription(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(otdsDataDao.queryOtdsTipsDescriptionCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(otdsDataDao.queryOtdsTipsDescription(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryOtdsDailyDetails(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        Object selectedDateObj = parameterMap.get("selectedDate");
        if (selectedDateObj instanceof JSONArray) {
            JSONArray selectedDate = (JSONArray) selectedDateObj;
            if (selectedDate.size() == 1 && selectedDate.contains("WTD")) {
                parameterMap.put("selectedDate", this.getOtdsWTDDays());
            }
        }
        this.generateCascaderFilter(parameterMap);
        page.setTotal(otdsDataDao.queryOtdsDailyDetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(otdsDataDao.queryOtdsDailyDetails(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryOtdsDailyChart(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap);
        return response.setBody(otdsDataDao.queryOtdsDailyChart(parameterMap));
    }

    @Override
    public void downloadOtdsDailyDetails(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        Object selectedDateObj = parameterMap.get("selectedDate");
        if (selectedDateObj instanceof JSONArray) {
            JSONArray selectedDate = (JSONArray) selectedDateObj;
            if (selectedDate.size() == 1 && selectedDate.contains("WTD")) {
                parameterMap.put("selectedDate", this.getOtdsWTDDays());
            }
        }
        this.generateCascaderFilter(parameterMap);
        String fileName = "otds_projection_" + Utils.randomStr(4) + ".xlsx";

        SheetInfoWithQueryKey sheet1 = new SheetInfoWithQueryKey();
        sheet1.setParameterMap(parameterMap);
        sheet1.setQueryKey("com.scp.customer.dao.IOTDSDao.downloadOtdsDailyDetails");
        sheet1.setSheetName("Daily Details");

        SheetInfoWithQueryKey sheet2 = new SheetInfoWithQueryKey();
        sheet2.setQueryKey("com.scp.customer.dao.IOTDSDao.queryOtdsRcaTips");
        sheet2.setSheetName("RCA Tips Description");
        excelTemplate.create(res, fileName, sheet1, sheet2);
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response saveOtdsDailyDetails(Map<String, Object> parameterMap) {
        Object obj = parameterMap.get("dailyDetailsUpdate");
        if (obj instanceof JSONObject) {
            JSONObject jsonObj = (JSONObject) obj;
            List<Map<String, Object>> dataList = new ArrayList<>();
            for (String key : jsonObj.keySet()) {
                JSONObject changeObj = jsonObj.getJSONObject(key);
                Map<String, Object> map = new HashMap<>();
                String[] keys = key.split("#");
                map.put("order", keys[0]);
                map.put("item", keys[1]);
                map.put("rca_result", changeObj.getString("RCA_RESULT"));
                map.put("rca_comments", changeObj.getString("RCA_COMMENTS"));
                map.put("type", 'D');
                dataList.add(map);
            }
            if (dataList.isEmpty() == false) {
                parameterMap.put("dataList", dataList);
                otdsDataDao.saveOtdsRCAResult(parameterMap);
            }
        }
        return response;
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryOtdsWeeklyDetails(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateCascaderFilter(parameterMap, "DIVISION");
        String selectedDate = (String) parameterMap.get("selectedDate");
        if (StringUtils.endsWith(selectedDate, "YTD")) {
            parameterMap.put("selectedDate", StringUtils.removeEnd(selectedDate, "YTD"));
            parameterMap.put("selectedDateType", "year");
        } else if (StringUtils.contains(selectedDate, "W")) {
            parameterMap.put("selectedDateType", "week");
        } else if (StringUtils.equals(selectedDate, "-1")) {
            parameterMap.put("selectedDateType", "all");
        } else if (StringUtils.equals(selectedDate, "-2")) {
            parameterMap.put("selectedDateType", "weekly");
        } else {
            parameterMap.put("selectedDateType", "month");
        }
        page.setTotal(otdsDataDao.queryOtdsWeeklyDetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(otdsDataDao.queryOtdsWeeklyDetails(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryOtdsWeeklyChart(Map<String, Object> parameterMap) {
        this.generateCascaderFilter(parameterMap, "DIVISION");
        String selectedDate = (String) parameterMap.get("selectedDate");
        if (StringUtils.endsWith(selectedDate, "YTD")) {
            parameterMap.put("selectedDate", StringUtils.removeEnd(selectedDate, "YTD"));
            parameterMap.put("selectedDateType", "year");
        } else if (StringUtils.contains(selectedDate, "W")) {
            parameterMap.put("selectedDateType", "week");
        } else if (StringUtils.equals(selectedDate, "-1")) {
            parameterMap.put("selectedDateType", "all");
        } else if (StringUtils.equals(selectedDate, "-2")) {
            parameterMap.put("selectedDateType", "weekly");
        } else {
            parameterMap.put("selectedDateType", "month");
        }

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("chart1", otdsDataDao.queryOtdsWeeklyChart(parameterMap));
        resultMap.put("chart2", otdsDataDao.queryOtdsWeeklyChart2(parameterMap));
        return response.setBody(resultMap);
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response saveOtdsWeeklyDetails(Map<String, Object> parameterMap) {
        Object obj = parameterMap.get("weeklyDetailsUpdate");
        if (obj instanceof JSONObject) {
            JSONObject jsonObj = (JSONObject) obj;
            List<Map<String, Object>> dataList = new ArrayList<>();
            for (String key : jsonObj.keySet()) {
                JSONObject changeObj = jsonObj.getJSONObject(key);
                Map<String, Object> map = new HashMap<>();
                String[] keys = key.split("#");
                map.put("order", keys[0]);
                map.put("item", keys[1]);
                map.put("rca_result", changeObj.getString("RCA_RESULT"));
                map.put("rca_comments", changeObj.getString("RCA_COMMENTS"));
                map.put("type", 'W');
                dataList.add(map);
            }
            if (dataList.isEmpty() == false) {
                parameterMap.put("dataList", dataList);
                otdsDataDao.saveOtdsRCAResult(parameterMap);
            }
        }
        return response;
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryOtdsRcaTipsList(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(otdsDataDao.queryOtdsRcaTipsListCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(otdsDataDao.queryOtdsRcaTipsList(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadOtdsWeeklyDetails(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateCascaderFilter(parameterMap, "DIVISION");
        String selectedDate = (String) parameterMap.get("selectedDate");
        if (StringUtils.endsWith(selectedDate, "YTD")) {
            parameterMap.put("selectedDate", StringUtils.removeEnd(selectedDate, "YTD"));
            parameterMap.put("selectedDateType", "year");
        } else if (StringUtils.contains(selectedDate, "W")) {
            parameterMap.put("selectedDateType", "week");
        } else if (StringUtils.equals(selectedDate, "-1")) {
            parameterMap.put("selectedDateType", "all");
        } else if (StringUtils.equals(selectedDate, "-2")) {
            parameterMap.put("selectedDateType", "weekly");
        } else {
            parameterMap.put("selectedDateType", "month");
        }
        String fileName = "otds_official_" + Utils.randomStr(4) + ".xlsx";

        SheetInfoWithQueryKey sheet1 = new SheetInfoWithQueryKey();
        sheet1.setParameterMap(parameterMap);
        sheet1.setQueryKey("com.scp.customer.dao.IOTDSDao.downloadOtdsWeeklyDetails");
        sheet1.setSheetName("Weekly Details");

        SheetInfoWithQueryKey sheet2 = new SheetInfoWithQueryKey();
        sheet2.setQueryKey("com.scp.customer.dao.IOTDSDao.queryOtdsRcaTips");
        sheet2.setSheetName("RCA Tips Description");

        excelTemplate.create(res, fileName, sheet1, sheet2);
    }

    private void generateOtdsSummary(List<String> fields, List<Map<String, Object>> resultList) {
        Map<String, Map<String, Object>> resultMap = new HashMap<>();
        for (Map<String, Object> map : resultList) {
            for (String key : map.keySet()) {
                if (fields.contains(key) == false && "TYPE".equals(key) == false && "RATIO".equals(map.get("TYPE")) == false && key.endsWith("_ORDER_BY") == false) {
                    String type = (String) map.get("TYPE");
                    Map<String, Object> subMap = resultMap.computeIfAbsent(type, k -> new HashMap<>());

                    if (subMap.isEmpty()) {
                        for (int i = 0; i < fields.size(); i++) {
                            if (i == fields.size() - 1) {
                                subMap.put(fields.get(i), "Total");
                            }
                        }
                        subMap.put("TYPE", type);
                    }

                    Object totalObject = subMap.get(key); // total cell value
                    Object currentCellObject = map.get(key); // current cell value
                    if (totalObject == null) {
                        subMap.put(key, currentCellObject);
                    } else {
                        if (currentCellObject != null) {
                            subMap.put(key, Utils.parseBigDecimal(totalObject).add(Utils.parseBigDecimal(currentCellObject)));
                        }
                    }
                }
            }
        }

        if (resultMap.isEmpty() == false) {
            Map<String, Object> otMap = resultMap.get("On Time");
            Map<String, Object> nllMap = resultMap.get("Delay");
            Map<String, Object> ratioMap = new HashMap<>();

            for (int i = 0; i < fields.size(); i++) {
                if (i == fields.size() - 1) {
                    ratioMap.put(fields.get(i), "Total");
                }
            }
            ratioMap.put("TYPE", "RATIO");

            for (String key : nllMap.keySet()) {
                BigDecimal otValue = Utils.parseBigDecimal(otMap.get(key), BigDecimal.ZERO);
                BigDecimal nllValue = Utils.parseBigDecimal(nllMap.get(key), BigDecimal.ZERO);
                BigDecimal total = otValue.add(nllValue);
                if (total.compareTo(BigDecimal.ZERO) > 0) {
                    ratioMap.put(key, otValue.divide(total, 6, RoundingMode.HALF_UP));
                }
            }

            resultList.add(ratioMap);
            resultList.add(otMap);
            resultList.add(nllMap);
        }
    }

    /**
     * 生成cascader filter
     *
     * @param parameterMap 参数map
     */
    @SuppressWarnings("unchecked")
    private void generateCascaderFilter(Map<String, Object> parameterMap, String... blackList) {
        // 生成筛选条件
        this.generateCascaderFilterSQL(parameterMap, null, null, "T", "_filters", blackList);

        // 处理field,防止传入空值
        List<String> defaultField = new ArrayList<>();
        defaultField.add("ENTITY");
        List<String> fields = (List<String>) parameterMap.get("field");
        if (fields.isEmpty()) {
            parameterMap.put("field", defaultField);
        } else {
            for (String field : fields) {
                if (Utils.hasInjectionAttack(field)) {
                    parameterMap.put("field", defaultField);
                    break;
                }
            }
        }
    }

    private List<String> getOtdsWTDDays() {
        List<String> resultList = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        int day = calendar.get(Calendar.DAY_OF_WEEK);
        SimpleDateFormat format = new SimpleDateFormat("MM/dd");
        if (day > 2) {
            for (int i = 2; i < day; i++) {
                calendar.add(Calendar.DAY_OF_YEAR, -1);
                resultList.add(format.format(calendar.getTime()));
            }
        }
        return resultList;
    }
}
