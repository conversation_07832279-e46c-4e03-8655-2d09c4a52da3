package com.scp.customer.service;

import com.starter.context.bean.Response;
import jakarta.servlet.http.HttpServletResponse;

import java.util.Map;

public interface IE2ELTAnalysisService {

    Response queryFilters(Map<String, Object> parameterMap);

    Response queryReport1(Map<String, Object> parameterMap);

    Response queryReport2(Map<String, Object> parameterMap);

    Response queryReport2Details(Map<String, Object> parameterMap);

    void downloadReport2Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport3(Map<String, Object> parameterMap);

    void downloadReport3(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport3Details(Map<String, Object> parameterMap);

    void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response initAnalysisPage(Map<String, Object> parameterMap);

    Response queryAnalysisReport1(Map<String, Object> parameterMap);

    Response queryAnalysisReport1Sub(Map<String, Object> parameterMap);

    Response queryAnalysisReport1Details(Map<String, Object> parameterMap);

    void downloadAnalysisReport1Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryAnalysisReport2(Map<String, Object> parameterMap);

    Response queryAnalysisReport2Details(Map<String, Object> parameterMap);

    void downloadAnalysisReport2Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryAnalysisReport3(Map<String, Object> parameterMap);

    Response queryAnalysisReport3Columns(Map<String, Object> parameterMap);

    void downloadAnalysisReport3(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryAnalysisReport3Details(Map<String, Object> parameterMap);

    void downloadAnalysisReport3Details(Map<String, Object> parameterMap, HttpServletResponse response);


}
