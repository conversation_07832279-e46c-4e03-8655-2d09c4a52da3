package com.scp.customer.service;

import com.starter.context.bean.Response;
import jakarta.servlet.http.HttpServletResponse;

import java.util.Map;

public interface ITEXService {

    Response queryCascader();

    Response queryReport1(Map<String, Object> parameterMap);

    Response queryReport2(Map<String, Object> parameterMao);

    Response queryReport2Details(Map<String, Object> parameterMap);

    void downloadReport2Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport3(Map<String, Object> parameterMap);

    Response queryReport3Details(Map<String, Object> parameterMap);

    void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport4(Map<String, Object> parameterMap);

    Response queryReport4Columns(Map<String, Object> parameterMap);

    void downloadReport4(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport4Details(Map<String, Object> parameterMap);

    void downloadReport4Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport5(Map<String, Object> parameterMap);

    Response queryReport5Details(Map<String, Object> parameterMap);

    void downloadReport5Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport6(Map<String, Object> parameterMap);
}
