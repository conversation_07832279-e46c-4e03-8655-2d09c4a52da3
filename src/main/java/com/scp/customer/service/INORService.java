package com.scp.customer.service;

import com.starter.context.bean.Response;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;

public interface INORService {

    Response queryNorCascader(Map<String, Object> parameterMap);

    Response queryReport1(Map<String, Object> parameterMap);

    void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse res);

    Response queryReport1Details(Map<String, Object> parameterMap);

    void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse res);

    Response queryReport2(Map<String, Object> parameterMap);

    Response queryReport3(Map<String, Object> parameterMap);

    Response queryReport3RatiosColumns(Map<String, Object> parameterMap);

    Response queryReport3Ratios(Map<String, Object> parameterMap);

    Response queryReport4(Map<String, Object> parameterMap);

    Response queryReport4Details(Map<String, Object> parameterMap);

    void downloadReport4(Map<String, Object> parameterMap, HttpServletResponse res);

    void downloadReport4Details(Map<String, Object> parameterMap, HttpServletResponse res);

}
