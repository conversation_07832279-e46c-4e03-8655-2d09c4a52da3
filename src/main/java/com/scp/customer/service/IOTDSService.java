package com.scp.customer.service;

import com.starter.context.bean.Response;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;

public interface IOTDSService {

    Response initPage();

    Response queryColumnsByDaterange(Map<String, Object> parameterMap);

    Response queryOtdsWeekly(Map<String, Object> parameterMap);

    Response queryOtdsDaily(Map<String, Object> parameterMap);

    Response queryOtdsDailyDetails(Map<String, Object> parameterMap);

    Response queryOtdsDailyChart(Map<String, Object> parameterMap);

    void downloadOtdsDailyDetails(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadOtdsWeeklyDetails(Map<String, Object> parameterMap, HttpServletResponse response);

    Response saveOtdsDailyDetails(Map<String, Object> parameterMap);

    Response queryOtdsWeeklyDetails(Map<String, Object> parameterMap);

    Response saveOtdsWeeklyDetails(Map<String, Object> parameterMap);

    Response queryOtdsWeeklyChart(Map<String, Object> parameterMap);

    Response queryOtdsRcaTipsList(Map<String, Object> parameterMap);

    Response queryOtdsWeeklySummary(Map<String, Object> parameterMap);

    Response queryOtdsTipsDescription(Map<String, Object> parameterMap);
}
