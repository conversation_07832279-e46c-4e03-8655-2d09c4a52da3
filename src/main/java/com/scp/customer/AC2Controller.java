package com.scp.customer;

import com.scp.customer.service.IAC2Service;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/customer/ac2", parent = "menu880")
public class AC2Controller extends ControllerHelper {

    @Resource
    private IAC2Service ac2Service;

    @Resource
    private Response res;

    @SchneiderRequestMapping("/init_page")
    public Response initPage(HttpServletRequest request) {
        super.setGlobalCache(true);
        super.pageLoad(request);
        return ac2Service.initPage(parameterMap);
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.setGlobalCache(true);
        super.pageLoad(request);
        return ac2Service.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping("/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        super.setGlobalCache(true);
        super.pageLoad(request);
        return ac2Service.queryReport2(parameterMap);
    }

    @SchneiderRequestMapping("/query_report2_details")
    public Response queryReport2Details(HttpServletRequest request) {
        super.setGlobalCache(true);
        super.pageLoad(request);
        return ac2Service.queryReport2Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report2_details")
    public void downloadReport2Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        ac2Service.downloadReport2Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report3_columns")
    public Response queryReport3Columns(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return ac2Service.queryReport3Columns(parameterMap);
    }

    @SchneiderRequestMapping("/query_report3")
    public Response queryReport3(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return ac2Service.queryReport3(parameterMap);
    }

    @SchneiderRequestMapping("/query_report3_details")
    public Response queryReport3Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return ac2Service.queryReport3Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report3_details")
    public void downloadReport3Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        ac2Service.downloadReport3Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/save_report3_details")
    public Response saveReport3Details(HttpServletRequest request) {
        super.pageLoad(request);
        return ac2Service.saveReport3Details(parameterMap);
    }
}
