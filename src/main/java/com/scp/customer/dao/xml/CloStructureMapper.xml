<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.customer.dao.ICloStructureDao">
    <sql id="filter">
        AND T.CLO_VERSION = #{cloVersion, jdbcType=VARCHAR}
        <if test="_filters != null and _filters != ''.toString()">
            and ${_filters}
        </if>
        <if test="tipsList.size() > 0 ">
            <foreach collection="tipsList" item="item" open="and (" close=")" separator=" OR ">
                T.T0_IMPROVEMENT_TIPS LIKE '%' || #{item, jdbcType=VARCHAR} || '%'
            </foreach>
        </if>
    </sql>

    <select id="queryPivotOpts" resultType="java.lang.String">
        SELECT T.COLUMN_NAME
        FROM USER_TAB_COLS T
        WHERE T.TABLE_NAME = 'CLO_STRUCTURE_V'
          AND T.DATA_TYPE IN ('VARCHAR2', 'CHAR')
          AND T.COLUMN_NAME NOT LIKE '%$%'
          AND T.COLUMN_NAME != 'CLO_VERSION'
        ORDER BY T.COLUMN_NAME
    </select>

    <select id="queryCloVersionOpts" resultType="java.lang.String">
        SELECT DISTINCT T.CLO_VERSION FROM CLO_STRUCTURE_V T ORDER BY T.CLO_VERSION DESC
    </select>

    <select id="queryFilters" resultType="java.util.Map">
        SELECT NAME, CATEGORY FROM SCPA.CLO_STRUCTURE_FILTER_V T ORDER BY CATEGORY, NAME
    </select>

    <select id="queryImprovTips" resultType="java.util.Map">
        SELECT T.CODE, T.DESCRIPTION FROM MR3_CLO_IMPROVEMENT_TIPS T
    </select>

    <select id="queryAvailableColumns" resultType="java.lang.String">
        SELECT COLUMN_NAME
        FROM USER_TAB_COLS T
        WHERE T.TABLE_NAME = 'CLO_STRUCTURE_V'
          AND ((T.DATA_TYPE IN ('VARCHAR2', 'CHAR') AND T.COLUMN_NAME NOT IN ('CLO_VERSION', 'T0_MATERIAL', 'T0_PLANT_CODE', 'T0_MEET_OR_FAIL')) OR T.COLUMN_NAME IN ('T0_PDT_CD', 'T1_PDT_CD'))
        UNION
        SELECT 'T0_LAST_CLO_LT_WD' FROM DUAL
        UNION
        SELECT 'T0_CLO_LT_CHANGE_WD' FROM DUAL
        UNION
        SELECT 'T0_LAST_CLO_LT_CHANGE_DATE' FROM DUAL
    </select>

    <sql id="report1SQL">
        WITH ORDER_BASE AS (
                SELECT /*+ parallel(6) */
                       T.MATERIAL, T.PLANT_CODE,
                       <choose>
                          <when test="valueType =='Net Net Price'.toString()">
                              SUM(T.NET_NET_VALUE_RMB)
                          </when>
                          <when test="valueType =='Quantity'.toString()">
                              SUM(T.ORDER_QUANTITY)
                          </when>
                          <when test="valueType =='SO Line'.toString()">
                              COUNT(T.SALES_ORDER_ITEM)
                          </when>
                          <otherwise>
                              COUNT(DISTINCT T.MATERIAL)
                          </otherwise>
                       </choose> AS MATERIAL_VALUE
                FROM SCPA.DEMAND_ORDER_INTAKE_V T INNER JOIN ${SCPA.CLO_STRUCTURE_V} T2 ON T.MATERIAL = T2.T0_MATERIAL AND T.PLANT_CODE = T2.T0_PLANT_CODE
                WHERE T.CALENDAR_DATE BETWEEN TO_DATE(#{report2SOTimeRange[0], jdbcType=VARCHAR}, 'YYYYMM') AND LAST_DAY(TO_DATE(#{report2SOTimeRange[1], jdbcType=VARCHAR}, 'YYYYMM'))
                  AND T2.CLO_VERSION = #{cloVersion, jdbcType=VARCHAR}
                GROUP BY T.MATERIAL, T.PLANT_CODE
             ),
             CHANGED_LIST AS (SELECT /*+ parallel */
                                 DISTINCT T.T0_MATERIAL,
                                          T.T0_PLANT_CODE,
                                          T2.T0_CLO_LT_WD                                                                                      AS T0_TODAY_CLO_LT_WD,
                                          FIRST_VALUE(T.T0_CLO_LT_WD) over (PARTITION BY T.T0_MATERIAL, T.T0_PLANT_CODE ORDER BY T.DATE$ DESC) AS T0_LAST_CLO_LT_WD,
                                          FIRST_VALUE(T.DATE$) over (PARTITION BY T.T0_MATERIAL, T.T0_PLANT_CODE ORDER BY T.DATE$ DESC)        AS T0_LAST_CLO_LT_CHANGE_DATE
                              FROM CLO_STRUCTURE_HIST T
                                       INNER JOIN CLO_STRUCTURE_V T2
                                                  ON T.T0_MATERIAL = T2.T0_MATERIAL AND T.T0_PLANT_CODE = T2.T0_PLANT_CODE AND T.DATE$ &lt; T2.DATE$ AND T.T0_CLO_LT_WD != T2.T0_CLO_LT_WD
                                                  AND T.CLO_VERSION = T2.CLO_VERSION
                              WHERE T.CLO_VERSION = #{cloVersion, jdbcType=VARCHAR}
             )
            SELECT T.T0_MATERIAL,
                   T.T0_PLANT_CODE,
                   T4.COMMENTS,
                   <foreach collection="report1SelectedColumns" item="item">
                    <if test="item != 'T0_LAST_CLO_LT_WD'.toString() and item != 'T0_CLO_LT_CHANGE_WD'.toString() and item != 'T0_LAST_CLO_LT_CHANGE_DATE'.toString()">
                        ${item},
                    </if>
                   </foreach>
                   T2.MATERIAL_VALUE,
                   T.T0_MEET_OR_FAIL,
                   T.T0_CLO_LT_TARGET_WD,
                   T.T0_CLO_LT_WD,
                   T.T0_PO_PRS_LT_WD,
                   T.T0_TRLT_WD,
                   T.T0_ZMGC_CD,
                   T.T0_GR_LT_WD,
                   T.T0_PICK_PACK_LT_WD,
                   T.T1_EXW_LT_WD,
                   T.T1_IH_LT_WD,
                   T.T1_GR_LT_WD,
                   T.T1_PICK_PACK_WD,
                   T3.T0_LAST_CLO_LT_WD,
                   T3.T0_LAST_CLO_LT_WD - T3.T0_TODAY_CLO_LT_WD  AS T0_CLO_LT_CHANGE_WD,
                   T3.T0_LAST_CLO_LT_CHANGE_DATE
            FROM ${SCPA.CLO_STRUCTURE_V} T LEFT JOIN ORDER_BASE T2 ON T.T0_MATERIAL = T2.MATERIAL AND T.T0_PLANT_CODE = T2.PLANT_CODE
                                           LEFT JOIN CHANGED_LIST T3 ON T.T0_MATERIAL = T3.T0_MATERIAL AND T.T0_PLANT_CODE = T3.T0_PLANT_CODE
                                           LEFT JOIN CLO_STRUCTURE_COMMENTS T4 ON T.T0_MATERIAL = T4.T0_MATERIAL AND T.T0_PLANT_CODE = T4.T0_PLANT_CODE
            <where>
                <include refid="filter"/>
                <if test="report1Values != null">
                  <foreach collection="report1Values" item="item" index="index" separator=" AND " open=" AND ">
                    T."${report2Categories[index]}" = #{item, jdbcType=VARCHAR}
                  </foreach>
                </if>
            </where>
    </sql>

    <select id="queryReport1Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="report1SQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report1SQL"/>
        ORDER BY T.T0_MEET_OR_FAIL,
                 CASE
                     WHEN T.T0_CLO_LT_WD IS NOT NULL AND T.T0_CLO_LT_TARGET_WD IS NOT NULL
                          THEN ABS(T.T0_CLO_LT_TARGET_WD - T.T0_CLO_LT_WD)
                     ELSE -1 END DESC
        <include refid="global.select_footer"/>
    </select>

    <select id="downloadReport1" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        WITH ORDER_BASE AS (
                SELECT /*+ parallel(6) */
                       T.MATERIAL, T.PLANT_CODE,
                       <choose>
                          <when test="valueType =='Net Net Price'.toString()">
                              SUM(T.NET_NET_VALUE_RMB)
                          </when>
                          <when test="valueType =='Quantity'.toString()">
                              SUM(T.ORDER_QUANTITY)
                          </when>
                          <when test="valueType =='SO Line'.toString()">
                              COUNT(T.SALES_ORDER_ITEM)
                          </when>
                          <otherwise>
                              COUNT(DISTINCT T.MATERIAL)
                          </otherwise>
                       </choose> AS MATERIAL_VALUE
                FROM SCPA.DEMAND_ORDER_INTAKE_V T INNER JOIN ${SCPA.CLO_STRUCTURE_V} T2 ON T.MATERIAL = T2.T0_MATERIAL AND T.PLANT_CODE = T2.T0_PLANT_CODE
                WHERE T.CALENDAR_DATE BETWEEN TO_DATE(#{report2SOTimeRange[0], jdbcType=VARCHAR}, 'YYYYMM') AND LAST_DAY(TO_DATE(#{report2SOTimeRange[1], jdbcType=VARCHAR}, 'YYYYMM'))
                  AND T2.CLO_VERSION = #{cloVersion, jdbcType=VARCHAR}
                GROUP BY T.MATERIAL, T.PLANT_CODE
        ),
        CHANGED_LIST AS (SELECT /*+ parallel */
                             DISTINCT T.T0_MATERIAL,
                                      T.T0_PLANT_CODE,
                                      T2.T0_CLO_LT_WD                                                                                      AS T0_TODAY_CLO_LT_WD,
                                      FIRST_VALUE(T.T0_CLO_LT_WD) over (PARTITION BY T.T0_MATERIAL, T.T0_PLANT_CODE ORDER BY T.DATE$ DESC) AS T0_LAST_CLO_LT_WD,
                                      FIRST_VALUE(T.DATE$) over (PARTITION BY T.T0_MATERIAL, T.T0_PLANT_CODE ORDER BY T.DATE$ DESC)        AS T0_LAST_CLO_LT_CHANGE_DATE
                          FROM CLO_STRUCTURE_HIST T
                                   INNER JOIN CLO_STRUCTURE_V T2
                                              ON T.T0_MATERIAL = T2.T0_MATERIAL AND T.T0_PLANT_CODE = T2.T0_PLANT_CODE AND T.DATE$ &lt; T2.DATE$ AND T.T0_CLO_LT_WD != T2.T0_CLO_LT_WD
                                              AND T.CLO_VERSION = T2.CLO_VERSION
                                   WHERE T.CLO_VERSION = #{cloVersion, jdbcType=VARCHAR}
        )
        SELECT T.*, T2.MATERIAL_VALUE,
                    T3.T0_LAST_CLO_LT_WD,
                    T3.T0_LAST_CLO_LT_WD - T3.T0_TODAY_CLO_LT_WD  AS T0_CLO_LT_CHANGE_WD,
                    T3.T0_LAST_CLO_LT_CHANGE_DATE
        FROM ${SCPA.CLO_STRUCTURE_V} T LEFT JOIN ORDER_BASE T2 ON T.T0_MATERIAL = T2.MATERIAL AND T.T0_PLANT_CODE = T2.PLANT_CODE
                                       LEFT JOIN CHANGED_LIST T3 ON T.T0_MATERIAL = T3.T0_MATERIAL AND T.T0_PLANT_CODE = T3.T0_PLANT_CODE
        <where>
            <include refid="filter"/>
            <if test="report1Values != null">
              <foreach collection="report1Values" item="item" index="index" separator=" AND " open=" AND ">
                T."${report2Categories[index]}" = #{item, jdbcType=VARCHAR}
              </foreach>
            </if>
        </where>
        <include refid="global.select_footer"/>
    </select>

    <select id="downloadReport1Tips" resultType="java.util.LinkedHashMap">
        SELECT T.CODE AS TIPS, T.DESCRIPTION FROM MR3_CLO_IMPROVEMENT_TIPS T
    </select>

    <select id="queryReport1AvailableTips" resultType="java.lang.String">
        SELECT REPLACE(T.MVIEW_NAME, 'CLO_STRUCTURE_TIPS_V_$', '') FROM USER_MVIEWS T WHERE T.MVIEW_NAME LIKE 'CLO_STRUCTURE_TIPS_V_$%'
    </select>

    <select id="downloadReport1Evidence" resultType="java.util.LinkedHashMap" flushCache="true" useCache="false">
        WITH ORDER_BASE AS (
                <choose>
                    <when test='_page.conditions.sql.contains("MATERIAL_VALUE")'>
                        SELECT /*+ parallel(6) */
                               T.MATERIAL, T.PLANT_CODE,
                               <choose>
                                  <when test="valueType =='Net Net Price'.toString()">
                                      SUM(T.NET_NET_VALUE_RMB)
                                  </when>
                                  <when test="valueType =='Quantity'.toString()">
                                      SUM(T.ORDER_QUANTITY)
                                  </when>
                                  <when test="valueType =='SO Line'.toString()">
                                      COUNT(T.SALES_ORDER_ITEM)
                                  </when>
                                  <otherwise>
                                      COUNT(DISTINCT T.MATERIAL)
                                  </otherwise>
                               </choose> AS MATERIAL_VALUE
                        FROM SCPA.DEMAND_ORDER_INTAKE_V T INNER JOIN ${SCPA.CLO_STRUCTURE_V} T2 ON T.MATERIAL = T2.T0_MATERIAL AND T.PLANT_CODE = T2.T0_PLANT_CODE
                        WHERE T.CALENDAR_DATE BETWEEN TO_DATE(#{report2SOTimeRange[0], jdbcType=VARCHAR}, 'YYYYMM') AND LAST_DAY(TO_DATE(#{report2SOTimeRange[1], jdbcType=VARCHAR}, 'YYYYMM'))
                          AND T2.CLO_VERSION = #{cloVersion, jdbcType=VARCHAR}
                        GROUP BY T.MATERIAL, T.PLANT_CODE
                    </when>
                    <otherwise>
                        SELECT NULL AS MATERIAL, NULL AS PLANT_CODE, NULL AS MATERIAL_VALUE FROM DUAL
                    </otherwise>
                </choose>
             ), QUERY_BASE AS (
                SELECT T2.MATERIAL_VALUE, T.*
                FROM ${SCPA.CLO_STRUCTURE_V} T LEFT JOIN ORDER_BASE T2 ON T.T0_MATERIAL = T2.MATERIAL AND T.T0_PLANT_CODE = T2.PLANT_CODE
                <where>
                    <include refid="filter"/>
                    <if test="report1Values != null">
                      <foreach collection="report1Values" item="item" index="index" separator=" AND " open=" AND ">
                        T."${report2Categories[index]}" = #{item, jdbcType=VARCHAR}
                      </foreach>
                    </if>
                </where>
             ), QUERY_BASE2 AS (
                SELECT * FROM QUERY_BASE
                <if test="_page.conditions != null and _page.conditions.sql != ''.toString()">
                        WHERE ${_page.conditions.sql}
                </if>
             )

        SELECT T2.*
          FROM ${tableName} T2 INNER JOIN QUERY_BASE2 T ON
          <foreach collection="joinColumns" item="item" separator=" AND ">
              NVL(T.${item}, 'NULL') = NVL(T2.${item}, 'NULL')
          </foreach>
    </select>

    <update id="saveReport1Comments">
        MERGE INTO SCPA.CLO_STRUCTURE_COMMENTS T
        USING
        (
        <foreach collection="comments" item="item" separator="union all">
            SELECT #{item.material, jdbcType=VARCHAR} MATERIAL,
                   #{item.plantCode, jdbcType=VARCHAR} PLANT_CODE,
                   #{item.comments, jdbcType=VARCHAR} COMMENTS
            from dual
        </foreach>
        ) S ON (T.T0_MATERIAL = S.MATERIAL AND T.T0_PLANT_CODE = S.PLANT_CODE)
        WHEN MATCHED THEN
        UPDATE SET
            T.COMMENTS = S.COMMENTS,
            T.UPDATE_DATE$ = SYSDATE,
            T.UPDATE_BY$ = #{session.userid, jdbcType=VARCHAR}
        WHEN NOT MATCHED THEN
        INSERT (T0_MATERIAL, T0_PLANT_CODE, COMMENTS, CREATE_BY$, CREATE_DATE$)
        VALUES (S.MATERIAL, S.PLANT_CODE, S.COMMENTS, #{session.userid, jdbcType=VARCHAR}, SYSDATE)
    </update>

    <select id="queryReport2" resultType="java.util.Map">
        WITH CLO_BASE AS (
            SELECT /*+ materialize */
                   T.T0_MATERIAL, T.T0_PLANT_CODE,
                   T.T0_CLO_LT_WD, T.T0_MEET_OR_FAIL,
                   NVL(T.${category}, 'Others') "category"
            FROM ${SCPA.CLO_STRUCTURE_V} T
            WHERE T.T0_MEET_OR_FAIL IN ('Meet', 'Fail')
              <include refid="filter"/>),
         HIST_CLO_DAYS AS (
             SELECT /*+ parallel(6) */
                    T."category",
                    T.T0_MATERIAL,
                    T.T0_PLANT_CODE,
                    T2.DATE$ - 1 DATE$,
                    T.T0_MEET_OR_FAIL
             FROM CLO_BASE T
                      INNER JOIN ${SCPA.CLO_STRUCTURE_HIST} T2 ON T.T0_MATERIAL = T2.T0_MATERIAL AND T.T0_PLANT_CODE = T2.T0_PLANT_CODE
             WHERE T.T0_CLO_LT_WD >= T2.T0_CLO_LT_WD
             AND T2.DATE$ BETWEEN TO_DATE(#{report2SOTimeRange[0], jdbcType=VARCHAR}, 'YYYYMM') + 1 AND LAST_DAY(TO_DATE(#{report2SOTimeRange[1], jdbcType=VARCHAR}, 'YYYYMM')) + 1
             AND T2.CLO_VERSION = #{cloVersion, jdbcType=VARCHAR}
         ),
         OTDS_BASE AS (
            SELECT /*+ parallel(6) */
                   T.MATERIAL,
                   T.PLANT_CODE,
                   T.CREATED_DATE,
                   SUM(DECODE(ONTIME, '+', 1, 0)) AS ONTIME,
                   SUM(DECODE(ONTIME, '#', 1, 0)) AS DELAY
              FROM SD4_SO_OTDS_V T
             WHERE T.CREATED_DATE BETWEEN TO_DATE(#{report2SOTimeRange[0], jdbcType=VARCHAR}, 'YYYYMM') AND LAST_DAY(TO_DATE(#{report2SOTimeRange[1], jdbcType=VARCHAR}, 'YYYYMM'))
             GROUP BY T.MATERIAL, T.PLANT_CODE, T.CREATED_DATE
         ),
         OTDS_BASE2 AS (
            SELECT /*+ parallel(6) */
                   T."category",
                   SUM(DECODE(T.T0_MEET_OR_FAIL, 'Meet', T2.ONTIME, 0))  AS MEET_ONTIME_LINES,
                   SUM(DECODE(T.T0_MEET_OR_FAIL, 'Fail', T2.ONTIME, 0))  AS FAIL_ONTIME_LINES,
                   SUM(DECODE(T.T0_MEET_OR_FAIL, 'Meet', T2.DELAY, 0))  AS MEET_DELAY_LINES,
                   SUM(DECODE(T.T0_MEET_OR_FAIL, 'Fail', T2.DELAY, 0))  AS FAIL_DELAY_LINES
            FROM HIST_CLO_DAYS T
                    LEFT JOIN OTDS_BASE T2 ON T.T0_MATERIAL = T2.MATERIAL AND T.T0_PLANT_CODE = T2.PLANT_CODE AND T.DATE$ = T2.CREATED_DATE
            GROUP BY T."category"
         ),
         ORDER_BASE AS (
            SELECT T0_MATERIAL,
                   T0_PLANT_CODE,
                   MAX(T0_MEET_OR_FAIL) T0_MEET_OR_FAIL,
                   MAX("category") "category"
              FROM CLO_BASE T
             GROUP BY T.T0_MATERIAL, T0_PLANT_CODE
         ),
         START_POINT_LT AS (SELECT T.T0_MATERIAL, T.T0_PLANT_CODE, T.T0_CLO_LT_WD FROM CLO_STRUCTURE_HIST T WHERE T.DATE$ = TO_DATE(#{report2ReferenceDate,jdbcType=VARCHAR}, 'YYYY/MM/DD') AND T.CLO_VERSION = #{cloVersion, jdbcType=VARCHAR}),
         COMPARE_POINT_LT AS (SELECT T.T0_MATERIAL, T.T0_PLANT_CODE, T.T0_CLO_LT_WD FROM CLO_STRUCTURE_HIST T WHERE T.DATE$ = TO_DATE(#{report2TargetDate,jdbcType=VARCHAR}, 'YYYY/MM/DD') AND T.CLO_VERSION = #{cloVersion, jdbcType=VARCHAR}),
         COMPARE_RESULT AS (SELECT T.T0_MATERIAL,
                                   T.T0_PLANT_CODE,
                                   CASE WHEN T.T0_CLO_LT_WD > T2.T0_CLO_LT_WD THEN 'WORSE' ELSE 'BETTER' END AS CHANGE_TYPE
                            FROM START_POINT_LT T
                                     INNER JOIN COMPARE_POINT_LT T2 ON T.T0_MATERIAL = T2.T0_MATERIAL AND T.T0_PLANT_CODE = T2.T0_PLANT_CODE AND T.T0_CLO_LT_WD != T2.T0_CLO_LT_WD),
         ORDER_BASE2 AS (
            SELECT /*+ parallel(6) */
                   T2."category",
                   <choose>
                      <when test="valueType =='Net Net Price'.toString()">
                          SUM(CASE WHEN T2.T0_MEET_OR_FAIL = 'Meet' THEN T.NET_NET_VALUE_RMB END)   AS MEET_VAL,
                          SUM(CASE WHEN T2.T0_MEET_OR_FAIL = 'Fail' THEN T.NET_NET_VALUE_RMB END)   AS FAIL_VAL,
                          SUM(CASE WHEN T3.CHANGE_TYPE = 'BETTER' THEN T.NET_NET_VALUE_RMB END)     AS BETTER_VAL,
                          SUM(CASE WHEN T3.CHANGE_TYPE = 'WORSE' THEN T.NET_NET_VALUE_RMB END)      AS WORSE_VAL
                      </when>
                      <when test="valueType =='Quantity'.toString()">
                          SUM(CASE WHEN T2.T0_MEET_OR_FAIL = 'Meet' THEN T.ORDER_QUANTITY END)    AS MEET_VAL,
                          SUM(CASE WHEN T2.T0_MEET_OR_FAIL = 'Fail' THEN T.ORDER_QUANTITY END)    AS FAIL_VAL,
                          SUM(CASE WHEN T3.CHANGE_TYPE = 'BETTER' THEN T.ORDER_QUANTITY END)      AS BETTER_VAL,
                          SUM(CASE WHEN T3.CHANGE_TYPE = 'WORSE' THEN T.ORDER_QUANTITY END)       AS WORSE_VAL
                      </when>
                      <when test="valueType =='SO Line'.toString()">
                          COUNT(CASE WHEN T2.T0_MEET_OR_FAIL = 'Meet' THEN T.SALES_ORDER_ITEM END)    AS MEET_VAL,
                          COUNT(CASE WHEN T2.T0_MEET_OR_FAIL = 'Fail' THEN T.SALES_ORDER_ITEM END)    AS FAIL_VAL,
                          COUNT(CASE WHEN T3.CHANGE_TYPE = 'BETTER' THEN T.SALES_ORDER_ITEM END)      AS BETTER_VAL,
                          COUNT(CASE WHEN T3.CHANGE_TYPE = 'WORSE' THEN T.SALES_ORDER_ITEM END)       AS WORSE_VAL
                      </when>
                      <otherwise>
                          COUNT(DISTINCT CASE WHEN T2.T0_MEET_OR_FAIL = 'Meet' THEN T.MATERIAL END)    AS MEET_VAL,
                          COUNT(DISTINCT CASE WHEN T2.T0_MEET_OR_FAIL = 'Fail' THEN T.MATERIAL END)    AS FAIL_VAL,
                          COUNT(DISTINCT CASE WHEN T3.CHANGE_TYPE = 'BETTER' THEN T.MATERIAL END)      AS BETTER_VAL,
                          COUNT(DISTINCT CASE WHEN T3.CHANGE_TYPE = 'WORSE' THEN T.MATERIAL END)       AS WORSE_VAL
                      </otherwise>
                   </choose>
            FROM SCPA.DEMAND_ORDER_INTAKE_V T INNER JOIN ORDER_BASE T2 ON T.MATERIAL = T2.T0_MATERIAL AND T.PLANT_CODE = T2.T0_PLANT_CODE
                                              LEFT JOIN COMPARE_RESULT T3 ON T.MATERIAL = T3.T0_MATERIAL AND T.PLANT_CODE = T3.T0_PLANT_CODE
            WHERE T.CALENDAR_DATE BETWEEN TO_DATE(#{report2SOTimeRange[0], jdbcType=VARCHAR}, 'YYYYMM') AND LAST_DAY(TO_DATE(#{report2SOTimeRange[1], jdbcType=VARCHAR}, 'YYYYMM'))
            GROUP BY T2."category"
         ),
         BASE AS (
            SELECT NVL(T."category", T2."category") "category",
                   NVL(T.MEET_ONTIME_LINES, 0) MEET_ONTIME_LINES,
                   NVL(T.MEET_DELAY_LINES, 0) MEET_DELAY_LINES,
                   NVL(T.FAIL_ONTIME_LINES, 0) FAIL_ONTIME_LINES,
                   NVL(T.FAIL_DELAY_LINES, 0) FAIL_DELAY_LINES,
                   NVL(T2.BETTER_VAL, 0) BETTER_VAL,
                   NVL(T2.WORSE_VAL, 0) WORSE_VAL,
                   NVL(T2.MEET_VAL, 0) MEET_VAL,
                   NVL(T2.FAIL_VAL, 0) FAIL_VAL
              FROM OTDS_BASE2 T FULL JOIN ORDER_BASE2 T2 ON T."category" = T2."category"
         )
         SELECT * FROM (
            SELECT T."category",
                   T.MEET_VAL AS "meet",
                   T.FAIL_VAL AS "fail",
                   CASE WHEN (T.MEET_ONTIME_LINES + T.MEET_DELAY_LINES) != 0 THEN ROUND(T.MEET_ONTIME_LINES * 100 / (T.MEET_ONTIME_LINES + T.MEET_DELAY_LINES), 1) END AS "meetOTDS",
                   CASE WHEN (T.FAIL_ONTIME_LINES + T.FAIL_DELAY_LINES) != 0 THEN ROUND(T.FAIL_ONTIME_LINES * 100 / (T.FAIL_ONTIME_LINES + T.FAIL_DELAY_LINES), 1) END AS "failOTDS",
                   T.BETTER_VAL AS "better",
                   T.WORSE_VAL AS "worse"
            FROM BASE T
            UNION ALL
            SELECT 'Total',
                    SUM(T.MEET_VAL),
                    SUM(T.FAIL_VAL),
                    CASE WHEN SUM(T.MEET_ONTIME_LINES + T.MEET_DELAY_LINES) != 0 THEN ROUND(SUM(T.MEET_ONTIME_LINES) * 100 / SUM(T.MEET_ONTIME_LINES + T.MEET_DELAY_LINES), 1) END,
                    CASE WHEN SUM(T.FAIL_ONTIME_LINES + T.FAIL_DELAY_LINES) != 0 THEN ROUND(SUM(T.FAIL_ONTIME_LINES) * 100 / SUM(T.FAIL_ONTIME_LINES + T.FAIL_DELAY_LINES), 1) END,
                    SUM(T.BETTER_VAL) AS "better",
                    SUM(T.WORSE_VAL)  AS "worse"
            FROM BASE T
        ) T
        ORDER BY DECODE(T."category", 'Others', -1, 'Total', -2, nvl("fail",0)) DESC
        FETCH NEXT 200 ROWS ONLY
    </select>

    <select id="queryReport2Sub" resultType="java.util.Map" useCache="false" flushCache="true">
        WITH CLO_BASE AS (
            SELECT /*+ materialize */
                   T.T0_MATERIAL, T.T0_PLANT_CODE,
                   T.T0_CLO_LT_WD, T.T0_MEET_OR_FAIL,
                   NVL(T.${category}, 'Others') "category"
            FROM ${SCPA.CLO_STRUCTURE_V} T
            WHERE T.T0_MEET_OR_FAIL IN ('Meet', 'Fail')
              <include refid="filter"/>
              AND T."${expandColumn}" = #{expandValue, jdbcType=VARCHAR}
              <foreach collection="parent" item="item" index="index">
                AND T."${report2Categories[index]}" = #{item, jdbcType=VARCHAR}
              </foreach>),
         HIST_CLO_DAYS AS (
             SELECT /*+ parallel(6) */
                    T."category",
                    T.T0_MATERIAL,
                    T.T0_PLANT_CODE,
                    T2.DATE$ - 1 DATE$,
                    T.T0_MEET_OR_FAIL
             FROM CLO_BASE T
                      INNER JOIN ${SCPA.CLO_STRUCTURE_HIST} T2 ON T.T0_MATERIAL = T2.T0_MATERIAL AND T.T0_PLANT_CODE = T2.T0_PLANT_CODE
             WHERE T.T0_CLO_LT_WD >= T2.T0_CLO_LT_WD
             AND T2.CLO_VERSION = #{cloVersion, jdbcType=VARCHAR}
             AND T2.DATE$ BETWEEN TO_DATE(#{report2SOTimeRange[0], jdbcType=VARCHAR}, 'YYYYMM') + 1 AND LAST_DAY(TO_DATE(#{report2SOTimeRange[1], jdbcType=VARCHAR}, 'YYYYMM')) + 1
         ),
         OTDS_BASE AS (
            SELECT /*+ parallel(6) */
                   T.MATERIAL,
                   T.PLANT_CODE,
                   T.CREATED_DATE,
                   SUM(DECODE(ONTIME, '+', 1, 0)) AS ONTIME,
                   SUM(DECODE(ONTIME, '#', 1, 0)) AS DELAY
              FROM SD4_SO_OTDS_V T
             WHERE T.CREATED_DATE BETWEEN TO_DATE(#{report2SOTimeRange[0], jdbcType=VARCHAR}, 'YYYYMM') AND LAST_DAY(TO_DATE(#{report2SOTimeRange[1], jdbcType=VARCHAR}, 'YYYYMM'))
             GROUP BY T.MATERIAL, T.PLANT_CODE, T.CREATED_DATE
         ),
         OTDS_BASE2 AS (
            SELECT /*+ parallel(6) */
                   T."category",
                   SUM(DECODE(T.T0_MEET_OR_FAIL, 'Meet', T2.ONTIME, 0))  AS MEET_ONTIME_LINES,
                   SUM(DECODE(T.T0_MEET_OR_FAIL, 'Fail', T2.ONTIME, 0))  AS FAIL_ONTIME_LINES,
                   SUM(DECODE(T.T0_MEET_OR_FAIL, 'Meet', T2.DELAY, 0))  AS MEET_DELAY_LINES,
                   SUM(DECODE(T.T0_MEET_OR_FAIL, 'Fail', T2.DELAY, 0))  AS FAIL_DELAY_LINES
            FROM HIST_CLO_DAYS T
                    LEFT JOIN OTDS_BASE T2 ON T.T0_MATERIAL = T2.MATERIAL AND T.T0_PLANT_CODE = T2.PLANT_CODE AND T.DATE$ = T2.CREATED_DATE
            GROUP BY T."category"
         ),
         ORDER_BASE AS (
            SELECT T0_MATERIAL,
                   T0_PLANT_CODE,
                   MAX(T0_MEET_OR_FAIL) T0_MEET_OR_FAIL,
                   MAX("category") "category"
              FROM CLO_BASE T
             GROUP BY T.T0_MATERIAL, T0_PLANT_CODE
         ),
         START_POINT_LT AS (SELECT T.T0_MATERIAL, T.T0_PLANT_CODE, T.T0_CLO_LT_WD FROM CLO_STRUCTURE_HIST T WHERE T.DATE$ = TO_DATE(#{report2ReferenceDate,jdbcType=VARCHAR}, 'YYYY/MM/DD') AND T.CLO_VERSION = #{cloVersion, jdbcType=VARCHAR}),
         COMPARE_POINT_LT AS (SELECT T.T0_MATERIAL, T.T0_PLANT_CODE, T.T0_CLO_LT_WD FROM CLO_STRUCTURE_HIST T WHERE T.DATE$ = TO_DATE(#{report2TargetDate,jdbcType=VARCHAR}, 'YYYY/MM/DD') AND T.CLO_VERSION = #{cloVersion, jdbcType=VARCHAR}),
         COMPARE_RESULT AS (SELECT T.T0_MATERIAL,
                                   T.T0_PLANT_CODE,
                                   CASE WHEN T.T0_CLO_LT_WD > T2.T0_CLO_LT_WD THEN 'WORSE' ELSE 'BETTER' END AS CHANGE_TYPE
                            FROM START_POINT_LT T
                                     INNER JOIN COMPARE_POINT_LT T2 ON T.T0_MATERIAL = T2.T0_MATERIAL AND T.T0_PLANT_CODE = T2.T0_PLANT_CODE AND T.T0_CLO_LT_WD != T2.T0_CLO_LT_WD),
         ORDER_BASE2 AS (
            SELECT /*+ parallel(6) */
                   T2."category",
                   <choose>
                      <when test="valueType =='Net Net Price'.toString()">
                          SUM(CASE WHEN T2.T0_MEET_OR_FAIL = 'Meet' THEN T.NET_NET_VALUE_RMB END)   AS MEET_VAL,
                          SUM(CASE WHEN T2.T0_MEET_OR_FAIL = 'Fail' THEN T.NET_NET_VALUE_RMB END)   AS FAIL_VAL,
                          SUM(CASE WHEN T3.CHANGE_TYPE = 'BETTER' THEN T.NET_NET_VALUE_RMB END)     AS BETTER_VAL,
                          SUM(CASE WHEN T3.CHANGE_TYPE = 'WORSE' THEN T.NET_NET_VALUE_RMB END)      AS WORSE_VAL
                      </when>
                      <when test="valueType =='Quantity'.toString()">
                          SUM(CASE WHEN T2.T0_MEET_OR_FAIL = 'Meet' THEN T.ORDER_QUANTITY END)    AS MEET_VAL,
                          SUM(CASE WHEN T2.T0_MEET_OR_FAIL = 'Fail' THEN T.ORDER_QUANTITY END)    AS FAIL_VAL,
                          SUM(CASE WHEN T3.CHANGE_TYPE = 'BETTER' THEN T.ORDER_QUANTITY END)      AS BETTER_VAL,
                          SUM(CASE WHEN T3.CHANGE_TYPE = 'WORSE' THEN T.ORDER_QUANTITY END)       AS WORSE_VAL
                      </when>
                      <when test="valueType =='SO Line'.toString()">
                          COUNT(CASE WHEN T2.T0_MEET_OR_FAIL = 'Meet' THEN T.SALES_ORDER_ITEM END)    AS MEET_VAL,
                          COUNT(CASE WHEN T2.T0_MEET_OR_FAIL = 'Fail' THEN T.SALES_ORDER_ITEM END)    AS FAIL_VAL,
                          COUNT(CASE WHEN T3.CHANGE_TYPE = 'BETTER' THEN T.SALES_ORDER_ITEM END)      AS BETTER_VAL,
                          COUNT(CASE WHEN T3.CHANGE_TYPE = 'WORSE' THEN T.SALES_ORDER_ITEM END)       AS WORSE_VAL
                      </when>
                      <otherwise>
                          COUNT(DISTINCT CASE WHEN T2.T0_MEET_OR_FAIL = 'Meet' THEN T.MATERIAL END)    AS MEET_VAL,
                          COUNT(DISTINCT CASE WHEN T2.T0_MEET_OR_FAIL = 'Fail' THEN T.MATERIAL END)    AS FAIL_VAL,
                          COUNT(DISTINCT CASE WHEN T3.CHANGE_TYPE = 'BETTER' THEN T.MATERIAL END)      AS BETTER_VAL,
                          COUNT(DISTINCT CASE WHEN T3.CHANGE_TYPE = 'WORSE' THEN T.MATERIAL END)       AS WORSE_VAL
                      </otherwise>
                   </choose>
            FROM SCPA.DEMAND_ORDER_INTAKE_V T INNER JOIN ORDER_BASE T2 ON T.MATERIAL = T2.T0_MATERIAL AND T.PLANT_CODE = T2.T0_PLANT_CODE
                                              LEFT JOIN COMPARE_RESULT T3 ON T.MATERIAL = T3.T0_MATERIAL AND T.PLANT_CODE = T3.T0_PLANT_CODE
            WHERE T.CALENDAR_DATE BETWEEN TO_DATE(#{report2SOTimeRange[0], jdbcType=VARCHAR}, 'YYYYMM') AND LAST_DAY(TO_DATE(#{report2SOTimeRange[1], jdbcType=VARCHAR}, 'YYYYMM'))
            GROUP BY T2."category"
         ),
         BASE AS (
            SELECT NVL(T."category", T2."category") "category",
                   NVL(T.MEET_ONTIME_LINES, 0) MEET_ONTIME_LINES,
                   NVL(T.MEET_DELAY_LINES, 0) MEET_DELAY_LINES,
                   NVL(T.FAIL_ONTIME_LINES, 0) FAIL_ONTIME_LINES,
                   NVL(T.FAIL_DELAY_LINES, 0) FAIL_DELAY_LINES,
                   NVL(T2.BETTER_VAL, 0) BETTER_VAL,
                   NVL(T2.WORSE_VAL, 0) WORSE_VAL,
                   NVL(T2.MEET_VAL, 0) MEET_VAL,
                   NVL(T2.FAIL_VAL, 0) FAIL_VAL
              FROM OTDS_BASE2 T FULL JOIN ORDER_BASE2 T2 ON T."category" = T2."category"
         )
         SELECT T."category",
                T.MEET_VAL AS "meet",
                T.FAIL_VAL AS "fail",
                CASE WHEN (T.MEET_ONTIME_LINES + T.MEET_DELAY_LINES) != 0 THEN ROUND(T.MEET_ONTIME_LINES * 100 / (T.MEET_ONTIME_LINES + T.MEET_DELAY_LINES), 1) END AS "meetOTDS",
                CASE WHEN (T.FAIL_ONTIME_LINES + T.FAIL_DELAY_LINES) != 0 THEN ROUND(T.FAIL_ONTIME_LINES * 100 / (T.FAIL_ONTIME_LINES + T.FAIL_DELAY_LINES), 1) END AS "failOTDS",
                T.BETTER_VAL AS "better",
                T.WORSE_VAL AS "worse"
         FROM BASE T
        ORDER BY DECODE(T."category", 'Others', -1, 'Total', -2, nvl("fail",0)) DESC
        FETCH NEXT 200 ROWS ONLY
    </select>

    <sql id="queryReport2DetailsSQL">
        WITH CLO_BASE AS (
            SELECT /*+ materialize */ *
            FROM ${SCPA.CLO_STRUCTURE_V} T
            WHERE T.T0_MEET_OR_FAIL IN ('Meet', 'Fail')
              <include refid="filter"/>
              <foreach collection="report2Values" item="item" index="index" separator=" AND " open=" AND ">
                "${report2Categories[index]}" = #{item, jdbcType=VARCHAR}
              </foreach>),
         HIST_CLO_DAYS AS (
             SELECT /*+ parallel(6) */
                    T.T0_MATERIAL,
                    T.T0_PLANT_CODE,
                    T2.DATE$ - 1 DATE$,
                    T.T0_MEET_OR_FAIL
             FROM CLO_BASE T
                      INNER JOIN ${SCPA.CLO_STRUCTURE_HIST} T2 ON T.T0_MATERIAL = T2.T0_MATERIAL AND T.T0_PLANT_CODE = T2.T0_PLANT_CODE
             WHERE T.T0_CLO_LT_WD >= T2.T0_CLO_LT_WD
             AND T2.CLO_VERSION = #{cloVersion, jdbcType=VARCHAR}
             AND T2.DATE$ BETWEEN TO_DATE(#{report2SOTimeRange[0], jdbcType=VARCHAR}, 'YYYYMM') + 1 AND LAST_DAY(TO_DATE(#{report2SOTimeRange[1], jdbcType=VARCHAR}, 'YYYYMM')) + 1
         ),
         OTDS_BASE AS (
            SELECT /*+ parallel(6) */
                   T.MATERIAL,
                   T.PLANT_CODE,
                   T.CREATED_DATE,
                   SUM(DECODE(ONTIME, '+', 1, 0)) AS ONTIME,
                   SUM(DECODE(ONTIME, '#', 1, 0)) AS DELAY
              FROM SD4_SO_OTDS_V T
             WHERE T.CREATED_DATE BETWEEN TO_DATE(#{report2SOTimeRange[0], jdbcType=VARCHAR}, 'YYYYMM') AND LAST_DAY(TO_DATE(#{report2SOTimeRange[1], jdbcType=VARCHAR}, 'YYYYMM'))
             GROUP BY T.MATERIAL, T.PLANT_CODE, T.CREATED_DATE
         ),
         START_POINT_LT AS (
            SELECT T.T0_MATERIAL, T.T0_PLANT_CODE, T.T0_CLO_LT_WD
              FROM CLO_STRUCTURE_HIST T
             WHERE T.DATE$ = TO_DATE(#{report2ReferenceDate,jdbcType=VARCHAR}, 'YYYY/MM/DD')
               AND T.CLO_VERSION = #{cloVersion, jdbcType=VARCHAR}
         ),
         COMPARE_POINT_LT AS (
            SELECT T.T0_MATERIAL, T.T0_PLANT_CODE, T.T0_CLO_LT_WD
              FROM CLO_STRUCTURE_HIST T
             WHERE T.DATE$ = TO_DATE(#{report2TargetDate,jdbcType=VARCHAR}, 'YYYY/MM/DD')
               AND T.CLO_VERSION = #{cloVersion, jdbcType=VARCHAR}
         ),
         COMPARE_RESULT AS (SELECT T.T0_MATERIAL,
                                   T.T0_PLANT_CODE,
                                   CASE WHEN T.T0_CLO_LT_WD > T2.T0_CLO_LT_WD THEN 'WORSE' ELSE 'BETTER' END AS CHANGE_TYPE,
                                   T2.T0_CLO_LT_WD - T.T0_CLO_LT_WD AS SHORTEN_DAYS
                            FROM START_POINT_LT T
                                     INNER JOIN COMPARE_POINT_LT T2 ON T.T0_MATERIAL = T2.T0_MATERIAL AND T.T0_PLANT_CODE = T2.T0_PLANT_CODE AND T.T0_CLO_LT_WD != T2.T0_CLO_LT_WD),
         OTDS_BASE2 AS (
            SELECT /*+ parallel(6) */
                   T.T0_MATERIAL, T.T0_PLANT_CODE,
                   SUM(DECODE(T.T0_MEET_OR_FAIL, 'Meet', T2.ONTIME, 0))  AS MEET_OTDS_ONTIME_LINES,
                   SUM(DECODE(T.T0_MEET_OR_FAIL, 'Fail', T2.ONTIME, 0))  AS FAIL_OTDS_ONTIME_LINES,
                   SUM(DECODE(T.T0_MEET_OR_FAIL, 'Meet', T2.DELAY, 0))  AS MEET_OTDS_DELAY_LINES,
                   SUM(DECODE(T.T0_MEET_OR_FAIL, 'Fail', T2.DELAY, 0))  AS FAIL_OTDS_DELAY_LINES
            FROM HIST_CLO_DAYS T
                    INNER JOIN OTDS_BASE T2 ON T.T0_MATERIAL = T2.MATERIAL AND T.T0_PLANT_CODE = T2.PLANT_CODE AND T.DATE$ = T2.CREATED_DATE
            GROUP BY T.T0_MATERIAL, T.T0_PLANT_CODE
         ),
         ORDER_BASE AS (
            SELECT T0_MATERIAL,
                   T0_PLANT_CODE,
                   MAX(T0_MEET_OR_FAIL) T0_MEET_OR_FAIL
              FROM CLO_BASE T
             GROUP BY T.T0_MATERIAL, T0_PLANT_CODE
         ),
         ORDER_BASE2 AS (
            SELECT /*+ parallel(6) */
                   T.MATERIAL, T.PLANT_CODE,
                   SUM(CASE WHEN T2.T0_MEET_OR_FAIL = 'Meet' THEN T.NET_NET_VALUE_RMB END)   AS MEET_NET_NET_VALUE,
                   SUM(CASE WHEN T2.T0_MEET_OR_FAIL = 'Fail' THEN T.NET_NET_VALUE_RMB END)   AS FAIL_NET_NET_VALUE,
                   SUM(CASE WHEN T2.T0_MEET_OR_FAIL = 'Meet' THEN T.ORDER_QUANTITY END)    AS MEET_ORDER_QUANTITY,
                   SUM(CASE WHEN T2.T0_MEET_OR_FAIL = 'Fail' THEN T.ORDER_QUANTITY END)    AS FAIL_ORDER_QUANTITY,
                   COUNT(CASE WHEN T2.T0_MEET_OR_FAIL = 'Meet' THEN T.SALES_ORDER_ITEM END)    AS MEET_ORDER_LINE,
                   COUNT(CASE WHEN T2.T0_MEET_OR_FAIL = 'Fail' THEN T.SALES_ORDER_ITEM END)    AS FAIL_ORDER_LINE,
                   COUNT(DISTINCT CASE WHEN T2.T0_MEET_OR_FAIL = 'Meet' THEN T.MATERIAL END)    AS MEET_MATERIAL_CNT,
                   COUNT(DISTINCT CASE WHEN T2.T0_MEET_OR_FAIL = 'Fail' THEN T.MATERIAL END)    AS FAIL_MATERIAL_CNT
            FROM SCPA.DEMAND_ORDER_INTAKE_V T INNER JOIN ORDER_BASE T2 ON T.MATERIAL = T2.T0_MATERIAL AND T.PLANT_CODE = T2.T0_PLANT_CODE
            WHERE T.CALENDAR_DATE BETWEEN TO_DATE(#{report2SOTimeRange[0], jdbcType=VARCHAR}, 'YYYYMM') AND LAST_DAY(TO_DATE(#{report2SOTimeRange[1], jdbcType=VARCHAR}, 'YYYYMM'))
            GROUP BY T.MATERIAL, T.PLANT_CODE
         )
        SELECT T.*,
                <if test="report2SelectedType == 'better'.toString() or report2SelectedType == 'worse'.toString()">
                    T4.SHORTEN_DAYS,
                </if>
                T2.MEET_NET_NET_VALUE,
                T2.FAIL_NET_NET_VALUE,
                T2.MEET_ORDER_QUANTITY,
                T2.FAIL_ORDER_QUANTITY,
                T2.MEET_ORDER_LINE,
                T2.FAIL_ORDER_LINE,
                T2.MEET_MATERIAL_CNT,
                T2.FAIL_MATERIAL_CNT,
                T3.MEET_OTDS_ONTIME_LINES,
                T3.FAIL_OTDS_ONTIME_LINES,
                T3.MEET_OTDS_DELAY_LINES,
                T3.FAIL_OTDS_DELAY_LINES
          FROM ORDER_BASE2 T2
               LEFT JOIN CLO_BASE T ON T.T0_MATERIAL = T2.MATERIAL AND T.T0_PLANT_CODE = T2.PLANT_CODE
               LEFT JOIN OTDS_BASE2 T3 ON T.T0_MATERIAL = T3.T0_MATERIAL AND T.T0_PLANT_CODE = T3.T0_PLANT_CODE
        <if test="report2SelectedType != null and report2SelectedType != ''.toString()">
            <choose>
                <when test="report2SelectedType == 'meet'.toString()">
                    WHERE T2.MEET_MATERIAL_CNT > 0
                </when>
                <when test="report2SelectedType == 'fail'.toString()">
                    WHERE T2.FAIL_MATERIAL_CNT > 0
                </when>
                <when test="report2SelectedType == 'better'.toString() or report2SelectedType == 'worse'.toString()">
                    LEFT JOIN COMPARE_RESULT T4 ON T.T0_MATERIAL = T4.T0_MATERIAL AND T.T0_PLANT_CODE = T4.T0_PLANT_CODE
                    <choose>
                        <when test="report2SelectedType == 'better'.toString()">
                            WHERE T4.CHANGE_TYPE = 'BETTER'
                        </when>
                        <when test="report2SelectedType == 'worse'.toString()">
                            WHERE T4.CHANGE_TYPE = 'WORSE'
                        </when>
                    </choose>
                </when>
            </choose>
        </if>
    </sql>

    <select id="queryReport2DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport2DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport2Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport2DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport3" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        WITH CLO_BASE AS ( /* 找到符合条件的数据 */
           SELECT  /*+ parallel */ T.T0_MATERIAL,
                   T.T0_PLANT_CODE,
                   T.T0_MEET_OR_FAIL,
                   T.T0_CLO_LT_GROUP,
                   T2.TEXT AS XAIXS
            FROM ${SCPA.CLO_STRUCTURE_HIST} T
            <choose>
                <when test="report3DateType == 'Day'.toString()">
                    INNER JOIN (SELECT TT.TEXT, TT.DATE$ FROM SY_CALENDAR TT WHERE TT.NAME = 'National Holidays') T2 ON T.DATE$ = T2.DATE$
                </when>
                <when test="report3DateType == 'Week'.toString()">
                    INNER JOIN (SELECT TT.YEAR || TT.WEEK_NO AS TEXT, MIN(TT.DATE$) DATE$
                                  FROM SY_CALENDAR TT
                                 WHERE TT.NAME = 'National Holidays'
                                 GROUP BY TT.YEAR, TT.WEEK_NO) T2 ON T.DATE$ = T2.DATE$
                </when>
                <when test="report3DateType == 'Month'.toString()">
                    INNER JOIN (SELECT TT.YEAR || TT.MONTH AS TEXT, MIN(TT.DATE$) DATE$
                                  FROM SY_CALENDAR TT
                                 WHERE TT.NAME = 'National Holidays'
                                 GROUP BY TT.YEAR, TT.MONTH) T2 ON T.DATE$ = T2.DATE$
                </when>
            </choose>
            WHERE T.T0_MEET_OR_FAIL IN ('Meet', 'Fail')
              AND T.CLO_VERSION = #{cloVersion, jdbcType=VARCHAR}
              AND T.DATE$ BETWEEN TO_DATE(#{report3TimeRange[0], jdbcType=VARCHAR}, 'YYYY/MM/DD') AND TO_DATE(#{report3TimeRange[1], jdbcType=VARCHAR}, 'YYYY/MM/DD')
              <include refid="filter"/>
              <if test="report3Values != null">
                  <foreach collection="report3Values" item="item" index="index" separator=" AND " open=" AND ">
                    "${report2Categories[index]}" = #{item, jdbcType=VARCHAR}
                  </foreach>
              </if>)
             /* 找到这些天中创建的order, 并按天求和 */
            SELECT /*+ parallel */
                T.T0_CLO_LT_GROUP,
                T.XAIXS,
                <choose>
                  <when test="valueType =='Net Net Price'.toString()">
                      SUM(CASE WHEN T.T0_MEET_OR_FAIL = 'Meet' THEN T2.NET_NET_VALUE_RMB END)        AS MEET_VAL,
                      SUM(CASE WHEN T.T0_MEET_OR_FAIL = 'Fail' THEN T2.NET_NET_VALUE_RMB END) * -1   AS FAIL_VAL
                  </when>
                  <when test="valueType =='Quantity'.toString()">
                      SUM(CASE WHEN T.T0_MEET_OR_FAIL = 'Meet' THEN T2.ORDER_QUANTITY END)        AS MEET_VAL,
                      SUM(CASE WHEN T.T0_MEET_OR_FAIL = 'Fail' THEN T2.ORDER_QUANTITY END) * -1   AS FAIL_VAL
                  </when>
                  <when test="valueType =='SO Line'.toString()">
                      COUNT(CASE WHEN T.T0_MEET_OR_FAIL = 'Meet' THEN T2.SALES_ORDER_ITEM END)        AS MEET_VAL,
                      COUNT(CASE WHEN T.T0_MEET_OR_FAIL = 'Fail' THEN T2.SALES_ORDER_ITEM END) * -1   AS FAIL_VAL
                  </when>
                  <otherwise>
                      COUNT(DISTINCT CASE WHEN T.T0_MEET_OR_FAIL = 'Meet' THEN T.T0_MATERIAL END)        AS MEET_VAL,
                      COUNT(DISTINCT CASE WHEN T.T0_MEET_OR_FAIL = 'Fail' THEN T.T0_MATERIAL END) * -1   AS FAIL_VAL
                  </otherwise>
                </choose>
            FROM CLO_BASE T
                 LEFT JOIN SCPA.DEMAND_ORDER_INTAKE_V T2 ON T.T0_MATERIAL = T2.MATERIAL AND T.T0_PLANT_CODE = T2.PLANT_CODE
            WHERE T2.CALENDAR_DATE BETWEEN TO_DATE(#{report2SOTimeRange[0], jdbcType=VARCHAR}, 'YYYYMM') AND LAST_DAY(TO_DATE(#{report2SOTimeRange[1], jdbcType=VARCHAR}, 'YYYYMM'))
            GROUP BY T.T0_CLO_LT_GROUP, T.XAIXS
    </select>

    <sql id="queryReport3DetailsSQL">
        WITH CLO_BASE AS (
            SELECT /*+ parallel */ T.*
            FROM ${SCPA.CLO_STRUCTURE_HIST} T
            <choose>
                <when test="report3DateType == 'Day'.toString()">
                    INNER JOIN (SELECT TT.TEXT, TT.DATE$ FROM SY_CALENDAR TT WHERE TT.NAME = 'National Holidays' AND TT.WORKING_DAY = 1) T2 ON T.DATE$ = T2.DATE$
                </when>
                <when test="report3DateType == 'Week'.toString()">
                    INNER JOIN (SELECT TT.YEAR || TT.WEEK_NO AS TEXT, MIN(TT.DATE$) DATE$
                                  FROM SY_CALENDAR TT
                                 WHERE TT.NAME = 'National Holidays'
                                   AND TT.WORKING_DAY = 1
                                 GROUP BY TT.YEAR, TT.WEEK_NO) T2 ON T.DATE$ = T2.DATE$
                </when>
                <when test="report3DateType == 'Month'.toString()">
                    INNER JOIN (SELECT TT.YEAR || TT.MONTH AS TEXT, MIN(TT.DATE$) DATE$
                                  FROM SY_CALENDAR TT
                                 WHERE TT.NAME = 'National Holidays'
                                   AND TT.WORKING_DAY = 1
                                 GROUP BY TT.YEAR, TT.MONTH) T2 ON T.DATE$ = T2.DATE$
                </when>
            </choose>
            WHERE T.T0_MEET_OR_FAIL IN ('Meet', 'Fail')
              AND T.CLO_VERSION = #{cloVersion, jdbcType=VARCHAR}
              AND T2.TEXT = #{report3SelectedDate, jdbcType=VARCHAR}
              <include refid="filter"/>
              <if test="report3Values != null">
                  <foreach collection="report3Values" item="item" index="index" separator=" AND " open=" AND ">
                    "${report2Categories[index]}" = #{item, jdbcType=VARCHAR}
                  </foreach>
              </if>)
         SELECT /*+ parallel */
               T.T0_MATERIAL,
               T.T0_PLANT_CODE,
               T.T0_CLO_LT_WD,
               T.T0_DELIVERING_PLANT,
               T.T0_SOURCE_CATEGORY,
               T.T0_PURCHASING_GROUP,
               T.T0_FLOW_STRATEGRY,
               T.T0_ITEM_CATEGORY_GRP,
               T.T0_STOCKING_POLICY,
               T.T0_VENDOR_CODE,
               T.T0_VENDOR_NAME,
               T.T1_ENTITY,
               T.T1_STOCKING_POLICY,
               T.T0_TRIGGER_OR_NOT,
               T.T0_ENTITY,
               T.T0_PRODUCT_LINE,
               T.T0_BU,
               T.T0_CLUSTER_NAME,
               T.T0_LOCAL_BU,
               T.T0_LOCAL_PRODUCT_FAMILY,
               T.T0_LOCAL_PRODUCT_LINE,
               T.T0_LOCAL_PRODUCT_SUBFAMILY,
               T.T0_MATERIAL_CATEGORY,
               T.T0_LT_RANGE,
               T.T0_MRP_CONTROLLER,
               T.T0_MATERIAL_OWNER_NAME,
               T.T0_MATERIAL_OWNER_SESA,
               T.T0_PLANT_TYPE,
               T.T0_CLO_LT_GROUP,
               T.T0_PRICE_CONTROL,
               T.T0_ABC,
               T.T0_ACTIVENESS,
               T.T0_CALCULATED_ABC,
               T.T0_CALCULATED_FMR,
               T.T0_DES_EXCEPTION,
               T.T0_HYPER_CARE_LEVEL,
               T.T0_PLANNING_ALIVE_STATUS,
               T.T0_GRA,
               T.T0_GRA_EVENT,
               T.T0_GRA_METHOD,
               T.T0_GRA_TYPE,
               T.T0_NEW_PRODUCTS,
               T.T0_DEL_FLAG_CLIENT,
               T.T0_DEL_FLAG_PLANT,
               T.T0_DELETION,
               T.T0_DIST_CHANNEL_SP_ST,
               T.T0_EXISTING_IN_BOM,
               T.T0_FIRST_CONSUMPTION_RANGE,
               T.T0_FIRST_PO_CREATE_RANGE,
               T.T0_FIRST_SO_CREATE_RANGE,
               T.T0_LAST_SO_SALES_RANGE,
               T.T0_LOT_SIZE_CODE,
               T.T0_MAT_PRICING_GROUP,
               T.T0_MATERIAL_DESCRIPTION,
               T.T0_MATERIAL_TYPE,
               T.T0_MRP_CONTROLLER_DESCRIPTION,
               T.T0_MRP_GROUP,
               T.T0_MRP_TYPE,
               T.T0_NONE_SALES_PERIOD,
               T.T0_PRODUCTION_LINE,
               T.T0_SEIO_SUGGESTION_STOCKING_POLICY,
               T.T0_SPECIAL_PROC_CODE,
               T.T1_MATERIAL,
               T.T1_PLANT_CODE,
               T.T1_PLANT_TYPE,
               T.T1_VENDOR_CODE,
               T.T1_VENDOR_NAME,
               T.T1_VENDOR_NAME2,
               T.T1_VENDOR_MATERIAL,
               T.T1_BOM_EXSITS,
               T.T0_MEET_OR_FAIL,
               T.T0_IMPROVEMENT_TIPS,
               T.T0_STOCKING_POLICY_LT_GROUP,

               SUM(CASE WHEN T.T0_MEET_OR_FAIL = 'Meet' THEN T2.NET_NET_VALUE_RMB END)         AS MEET_NET_NET_VALUE,
               SUM(CASE WHEN T.T0_MEET_OR_FAIL = 'Fail' THEN T2.NET_NET_VALUE_RMB END)         AS FAIL_NET_NET_VALUE,
               SUM(CASE WHEN T.T0_MEET_OR_FAIL = 'Meet' THEN T2.ORDER_QUANTITY END)            AS MEET_ORDER_QUANTITY,
               SUM(CASE WHEN T.T0_MEET_OR_FAIL = 'Fail' THEN T2.ORDER_QUANTITY END)            AS FAIL_ORDER_QUANTITY,
               COUNT(CASE WHEN T.T0_MEET_OR_FAIL = 'Meet' THEN T2.SALES_ORDER_ITEM END)        AS MEET_ORDER_LINE,
               COUNT(CASE WHEN T.T0_MEET_OR_FAIL = 'Fail' THEN T2.SALES_ORDER_ITEM END)        AS FAIL_ORDER_LINE,
               COUNT(DISTINCT CASE WHEN T.T0_MEET_OR_FAIL = 'Meet' THEN T.T0_MATERIAL END)     AS MEET_MATERIAL_CNT,
               COUNT(DISTINCT CASE WHEN T.T0_MEET_OR_FAIL = 'Fail' THEN T.T0_MATERIAL END)     AS FAIL_MATERIAL_CNT
            FROM CLO_BASE T
                    LEFT JOIN SCPA.DEMAND_ORDER_INTAKE_V T2 ON T.T0_MATERIAL = T2.MATERIAL AND T.T0_PLANT_CODE = T2.PLANT_CODE
            WHERE T2.CALENDAR_DATE BETWEEN TO_DATE(#{report2SOTimeRange[0], jdbcType=VARCHAR}, 'YYYYMM') AND LAST_DAY(TO_DATE(#{report2SOTimeRange[1], jdbcType=VARCHAR}, 'YYYYMM'))
            GROUP BY
               T.T0_MATERIAL,
               T.T0_PLANT_CODE,
               T.T0_CLO_LT_WD,
               T.T0_DELIVERING_PLANT,
               T.T0_SOURCE_CATEGORY,
               T.T0_PURCHASING_GROUP,
               T.T0_FLOW_STRATEGRY,
               T.T0_ITEM_CATEGORY_GRP,
               T.T0_STOCKING_POLICY,
               T.T0_VENDOR_CODE,
               T.T0_VENDOR_NAME,
               T.T1_ENTITY,
               T.T1_STOCKING_POLICY,
               T.T0_TRIGGER_OR_NOT,
               T.T0_ENTITY,
               T.T0_PRODUCT_LINE,
               T.T0_BU,
               T.T0_CLUSTER_NAME,
               T.T0_LOCAL_BU,
               T.T0_LOCAL_PRODUCT_FAMILY,
               T.T0_LOCAL_PRODUCT_LINE,
               T.T0_LOCAL_PRODUCT_SUBFAMILY,
               T.T0_MATERIAL_CATEGORY,
               T.T0_LT_RANGE,
               T.T0_MRP_CONTROLLER,
               T.T0_MATERIAL_OWNER_NAME,
               T.T0_MATERIAL_OWNER_SESA,
               T.T0_PLANT_TYPE,
               T.T0_CLO_LT_GROUP,
               T.T0_PRICE_CONTROL,
               T.T0_ABC,
               T.T0_ACTIVENESS,
               T.T0_CALCULATED_ABC,
               T.T0_CALCULATED_FMR,
               T.T0_DES_EXCEPTION,
               T.T0_HYPER_CARE_LEVEL,
               T.T0_PLANNING_ALIVE_STATUS,
               T.T0_GRA,
               T.T0_GRA_EVENT,
               T.T0_GRA_METHOD,
               T.T0_GRA_TYPE,
               T.T0_NEW_PRODUCTS,
               T.T0_DEL_FLAG_CLIENT,
               T.T0_DEL_FLAG_PLANT,
               T.T0_DELETION,
               T.T0_DIST_CHANNEL_SP_ST,
               T.T0_EXISTING_IN_BOM,
               T.T0_FIRST_CONSUMPTION_RANGE,
               T.T0_FIRST_PO_CREATE_RANGE,
               T.T0_FIRST_SO_CREATE_RANGE,
               T.T0_LAST_SO_SALES_RANGE,
               T.T0_LOT_SIZE_CODE,
               T.T0_MAT_PRICING_GROUP,
               T.T0_MATERIAL_DESCRIPTION,
               T.T0_MATERIAL_TYPE,
               T.T0_MRP_CONTROLLER_DESCRIPTION,
               T.T0_MRP_GROUP,
               T.T0_MRP_TYPE,
               T.T0_NONE_SALES_PERIOD,
               T.T0_PRODUCTION_LINE,
               T.T0_SEIO_SUGGESTION_STOCKING_POLICY,
               T.T0_SPECIAL_PROC_CODE,
               T.T1_MATERIAL,
               T.T1_PLANT_CODE,
               T.T1_PLANT_TYPE,
               T.T1_VENDOR_CODE,
               T.T1_VENDOR_NAME,
               T.T1_VENDOR_NAME2,
               T.T1_VENDOR_MATERIAL,
               T.T1_BOM_EXSITS,
               T.T0_MEET_OR_FAIL,
               T.T0_IMPROVEMENT_TIPS,
               T.T0_STOCKING_POLICY_LT_GROUP
    </sql>

    <select id="queryReport3DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport3DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport3Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport3DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>
</mapper>
