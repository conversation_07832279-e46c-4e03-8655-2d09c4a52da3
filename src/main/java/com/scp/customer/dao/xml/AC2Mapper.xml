<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.customer.dao.IAC2Dao">

    <sql id="AC2Filter">
        <if test="_filters != null and _filters != ''.toString()">
            AND ${_filters}
        </if>
        <if test="treePathFilter != null and treePathFilter != ''.toString()">
            and ${treePathFilter}
        </if>
        <include refid="dateRangeFilter"/>
    </sql>

    <sql id="dateRangeFilter">
        <choose>
            <when test="searchDateType == 'Filter by Month'.toString()">
                AND t.CALENDAR_MONTH between #{dateRange[0], jdbcType=VARCHAR} and #{dateRange[1], jdbcType=VARCHAR}
            </when>
            <when test="searchDateType == 'Filter by Week'.toString()">
                AND t.CALENDAR_WEEK IN
                <choose>
                    <when test="weekRange != null and !weekRange.isEmpty()">
                        <foreach collection="weekRange" separator="," item="item" open="(" close=")">
                            #{item, jdbcType=VARCHAR}
                        </foreach>
                    </when>
                    <otherwise>
                        (#{currentWeekRecord, jdbcType=VARCHAR})
                    </otherwise>
                </choose>
            </when>
        </choose>
    </sql>

    <resultMap id="report1ResultMap" type="com.scp.customer.bean.AC2Report1Bean">
        <result property="category1" column="CATEGORY1"/>
        <result property="category2" column="CATEGORY2"/>
        <result property="category3" column="CATEGORY3"/>
        <result property="category4" column="CATEGORY4"/>
        <result property="category5" column="CATEGORY5"/>
        <result property="value" column="value"/>
        <association property="tooltips" javaType="com.scp.customer.bean.AC2Report1Tooltips">
            <result property="COUNT" column="COUNT"/>
            <result property="K" column="K"/>
        </association>
    </resultMap>

    <select id="queryCascader" resultType="java.util.Map">
        SELECT NAME,
               CATEGORY
        FROM ${SCPA.AC2_FILTER_V} T
        ORDER BY CATEGORY,NAME
    </select>

    <select id="queryCurrentWeek" resultType="java.lang.String">
        SELECT YEAR || WEEK_NO AS CURRENTWEEK
        FROM ${SCPA.SY_CALENDAR}
        WHERE DATE$ = TRUNC(SYSDATE, 'DD')
          AND NAME = 'National Holidays'
    </select>

    <resultMap id="weekMap" type="java.lang.String">
        <result column="CALENDAR_WEEK"/>
    </resultMap>

    <select id="queryWeekList" resultMap="weekMap" resultType="java.util.List">
        SELECT DISTINCT CALENDAR_WEEK
        FROM ${SCPA.AC2_V}
        ORDER BY CALENDAR_WEEK DESC
    </select>

    <select id="queryAc2RcaTips" resultType="java.util.Map">
        SELECT RCA_TIPS_CODE, DESCRIPTION, COMPUTING_LOGIC from AC2_RCA_TIPS
        UNION ALL
        select RCA_CODE, DESCRIPTION, null from AC2_RCA_CODE
    </select>

    <select id="queryRacCode" resultType="java.lang.String">
        select RCA_CODE from ${SCPA.AC2_RCA_CODE}
    </select>

    <select id="queryReport1" resultMap="report1ResultMap">
        SELECT NVL(${level1}, 'Others') AS CATEGORY1,
               NVL(${level2}, 'Others') AS CATEGORY2,
               NVL(${level3}, 'Others') AS CATEGORY3,
               <if test="level4 != null and level4 != ''.toString()">
                   NVL(${level4}, 'Others') AS CATEGORY4,
               </if>
               <if test="level5 != null and level5 != ''.toString()">
                   NVL(${level5}, 'Others') AS CATEGORY5,
               </if>
               ${valueColumn} AS VALUE,
               (COUNT(DECODE(T.AC2_TYPE, 'AC2_ON_TIME', 1, NULL)) / COUNT(1)) as K
               <if test="tooltipsColumns != null and tooltipsColumns != ''.toString()">
                   ,${tooltipsColumns}
               </if>
        FROM ${SCPA.AC2_V} T
        <where>
            <include refid="AC2Filter"/>
        </where>
        GROUP BY ${level1},
                 ${level2},
                 ${level3}
                 <if test="level4 != null and level4 != ''.toString()">,${level4}</if>
                 <if test="level5 != null and level5 != ''.toString()">,${level5}</if>
    </select>

    <select id="queryReport1Level1Slope" resultType="java.util.Map">
        with ac2_temp as (
            select * from ${SCPA.AC2_V} T where 1=1
            <include refid="AC2Filter"/>
        )
        SELECT /*+ parallel(8)  */
            TT.KEY,
            REGR_SLOPE(DAY_GAP, TT.LINE) K
        FROM (SELECT T.${level1} AS KEY,
                TO_NUMBER((T.CALENDAR_WEEK) - (SELECT max(CALENDAR_WEEK) FROM ac2_temp)) DAY_GAP,
                COUNT(DECODE(T.AC2_TYPE, 'AC2_ON_TIME', 1, NULL)) / COUNT(1) AS LINE
              FROM ac2_temp T
              WHERE T.${level1} IS NOT NULL
              GROUP BY T.${level1}, T.CALENDAR_WEEK) TT
        GROUP BY TT.KEY
    </select>

    <select id="queryReport1Level2Slope" resultType="java.util.Map">
        with AC2_TEMP as (
            select * from ${SCPA.AC2_V} t where 1=1
            <include refid="AC2Filter"/>
        )
        SELECT /*+ parallel(8) */
            TT.KEY,
            REGR_SLOPE(DAY_GAP, TT.LINE) K
        FROM (SELECT T.${level1} || '[#]' || T.${level2} AS KEY,
                TO_NUMBER((T.CALENDAR_WEEK) - (SELECT max(CALENDAR_WEEK) FROM ac2_temp)) DAY_GAP,
                COUNT(DECODE(T.AC2_TYPE, 'AC2_ON_TIME', 1, NULL)) / COUNT(1) AS LINE
              FROM AC2_TEMP T
              WHERE T.${level1} IS NOT NULL
                AND T.${level2} IS NOT NULL
              GROUP BY T.${level1}, T.${level2}, T.CALENDAR_WEEK) TT
        GROUP BY TT.KEY
    </select>

    <select id="queryReport2XAxis" resultType="java.lang.String">
        SELECT DISTINCT T.${dateColumn}
        FROM ${SCPA.AC2_V} T
        <where>
            <if test="report2AC2Type != null and report2AC2Type != ''.toString()">
                AC2_TYPE = #{report2AC2Type}
            </if>
            <include refid="AC2Filter"/>
        </where>
        ORDER BY T.${dateColumn}
    </select>

    <select id="queryReport2Line" resultType="java.util.HashMap">
        SELECT *
        FROM (SELECT
                    <choose>
                        <when test="reportCalType == 'AC2'.toString()">
                            COUNT(DECODE(T.AC2_TYPE, 'AC2_ON_TIME', 1, NULL)) / (COUNT(DECODE(T.AC2_TYPE, 'AC2_ON_TIME', 1, NULL)) +
                            COUNT(DECODE(T.AC2_TYPE, 'AC2_DELAY', 1, NULL)) +
                            COUNT(DECODE(T.AC2_TYPE, 'AC2_EARLY', 1, NULL)) +
                            COUNT(DECODE(T.AC2_TYPE, 'AC2_OTHERS', 1, NULL))) AS RATIO,
                        </when>
                        <when test="reportCalType == 'AC2_EARLY'.toString()">
                            (COUNT(DECODE(T.AC2_TYPE, 'AC2_ON_TIME', 1, NULL)) + COUNT(DECODE(T.AC2_TYPE, 'AC2_EARLY', 1, NULL))) / (COUNT(DECODE(T.AC2_TYPE, 'AC2_ON_TIME', 1, NULL)) +
                            COUNT(DECODE(T.AC2_TYPE, 'AC2_DELAY', 1, NULL)) +
                            COUNT(DECODE(T.AC2_TYPE, 'AC2_EARLY', 1, NULL)) +
                            COUNT(DECODE(T.AC2_TYPE, 'AC2_OTHERS', 1, NULL))) AS RATIO,
                        </when>
                        <when test="reportCalType == 'LATE C2'.toString()">
                            COUNT(DECODE(T.AC2_TYPE, 'AC2_DELAY', 1, NULL)) / (COUNT(DECODE(T.AC2_TYPE, 'AC2_ON_TIME', 1, NULL)) +
                            COUNT(DECODE(T.AC2_TYPE, 'AC2_DELAY', 1, NULL)) +
                            COUNT(DECODE(T.AC2_TYPE, 'AC2_EARLY', 1, NULL)) +
                            COUNT(DECODE(T.AC2_TYPE, 'AC2_OTHERS', 1, NULL))) AS RATIO,
                        </when>
                        <when test="reportCalType == 'LATE C2_OTHERS'.toString()">
                            (COUNT(DECODE(T.AC2_TYPE, 'AC2_DELAY', 1, NULL)) + COUNT(DECODE(T.AC2_TYPE, 'AC2_OTHERS', 1, NULL))) / (COUNT(DECODE(T.AC2_TYPE, 'AC2_ON_TIME', 1, NULL)) +
                            COUNT(DECODE(T.AC2_TYPE, 'AC2_DELAY', 1, NULL)) +
                            COUNT(DECODE(T.AC2_TYPE, 'AC2_EARLY', 1, NULL)) +
                            COUNT(DECODE(T.AC2_TYPE, 'AC2_OTHERS', 1, NULL))) AS RATIO,
                        </when>
                        <when test="reportCalType == 'FAILED_NONE'.toString()">
                            COUNT(DECODE(T.AC2_TYPE, 'AC2_OTHERS', 1, NULL)) / (COUNT(DECODE(T.AC2_TYPE, 'AC2_ON_TIME', 1, NULL)) +
                            COUNT(DECODE(T.AC2_TYPE, 'AC2_DELAY', 1, NULL)) +
                            COUNT(DECODE(T.AC2_TYPE, 'AC2_EARLY', 1, NULL)) +
                            COUNT(DECODE(T.AC2_TYPE, 'AC2_OTHERS', 1, NULL))) AS RATIO,
                        </when>
                        <otherwise>
                            COUNT(DECODE(T.AC2_TYPE, 'AC2_ON_TIME', 1, NULL)) / (COUNT(DECODE(T.AC2_TYPE, 'AC2_ON_TIME', 1, NULL)) +
                            COUNT(DECODE(T.AC2_TYPE, 'AC2_DELAY', 1, NULL)) +
                            COUNT(DECODE(T.AC2_TYPE, 'AC2_EARLY', 1, NULL)) +
                            COUNT(DECODE(T.AC2_TYPE, 'AC2_OTHERS', 1, NULL))) AS RATIO,
                        </otherwise>
                    </choose>
                    T.${dateColumn}    AS CALENDAR
              FROM ${SCPA.AC2_V} T
              <where>
                  <include refid="AC2Filter"/>
              </where>
              GROUP BY T.${dateColumn}) PIVOT (SUM(ROUND(RATIO * 100, 1)) FOR CALENDAR IN
                    <choose>
                        <when test="xAxis == {}">
                            ('')
                        </when>
                        <otherwise>
                            <foreach collection="xAxis" item="item" separator="," open="(" close=")">
                                '${item}'
                            </foreach>
                        </otherwise>
                    </choose>
                  )
    </select>

    <select id="queryReport2YAxis" resultType="java.util.HashMap">
        SELECT NVL(T.${report2ViewType},'Others') AS ${report2ViewType}, T.${dateColumn} AS "yAxis", ${valueColumn} VAL
        FROM ${SCPA.AC2_V} T
        <where>
            <if test="report2AC2Type != null and report2AC2Type != ''.toString()">
                AC2_TYPE = #{report2AC2Type}
            </if>
            <include refid="AC2Filter"/>
        </where>
        GROUP BY T.${dateColumn}, NVL(T.${report2ViewType},'Others')
        <choose>
            <when test="report2ViewType == 'AC2_TYPE'.toString()">
                ORDER BY decode(AC2_TYPE, 'AC2_DELAY', 'a', 'AC2_OTHERS', 'b', 'AC2_EARLY', 'c', 'AC2_ON_TIME', 'd'), T.${dateColumn}
            </when>
            <otherwise>
                ORDER BY NVL(T.${report2ViewType},'Others'), T.${dateColumn}
            </otherwise>
        </choose>
    </select>

    <sql id="queryReport2DetailsSQL">
        SELECT * FROM ${SCPA.AC2_V} T
        <where>
            <if test="report2AC2Type != null and report2AC2Type != ''.toString()">
                AC2_TYPE = #{report2AC2Type}
            </if>
            <include refid="AC2Filter"/>
            <choose>
                <when test="report2DateType == 'By Month'">
                    AND T.CALENDAR_MONTH = #{report2SelectedDate, jdbcType=VARCHAR}
                </when>
                <when test="report2DateType == 'By Week'">
                    AND T.CALENDAR_WEEK = #{report2SelectedDate, jdbcType=VARCHAR}
                </when>
                <when test="report2DateType == 'By Quarter'">
                    AND T.CALENDAR_QUARTER = #{report2SelectedDate, jdbcType=VARCHAR}
                </when>
                <when test="report2DateType == 'By Year'">
                    AND T.CALENDAR_YEAR = #{report2SelectedDate, jdbcType=VARCHAR}
                </when>
            </choose>
            <choose>
                <when test="report2SelectedType == 'AC2(%)'.toString()">
                    AND T.AC2_TYPE in ('AC2_ON_TIME')
                </when>
                <when test="report2SelectedType == 'AC2_EARLY(%)'.toString()">
                    AND T.AC2_TYPE in ('AC2_ON_TIME', 'AC2_EARLY')
                </when>
                <when test="report2SelectedType == 'LATE C2(%)'.toString()">
                    AND T.AC2_TYPE IN ('AC2_DELAY')
                </when>
                <when test="report2SelectedType == 'LATE C2_OTHERS(%)'.toString()">
                    AND T.AC2_TYPE IN ('AC2_DELAY', 'AC2_OTHERS')
                </when>
                <when test="report2SelectedType == 'FAILED_NONE(%)'.toString()">
                    AND T.AC2_TYPE IN ('AC2_OTHERS')
                </when>
                <otherwise>
                    AND T.AC2_TYPE in ('AC2_ON_TIME')
                </otherwise>
            </choose>
        </where>
    </sql>

    <select id="queryReport2DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport2DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport2Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport2DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport3YearList" resultType="java.lang.String">
        SELECT DISTINCT YEAR
        FROM SY_CALENDAR T
        WHERE T.DATE$ BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'YYYY/MM') AND LAST_DAY(TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'YYYY/MM'))
          AND T.NAME = 'National Holidays'
        ORDER BY YEAR
    </select>

    <select id="queryReport3MonthList" resultType="java.lang.String">
        SELECT DISTINCT T.YEAR || T.MONTH
        FROM SY_CALENDAR T
        WHERE T.DATE$ BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'YYYY/MM') AND LAST_DAY(TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'YYYY/MM'))
          AND T.NAME = 'National Holidays'
        ORDER BY YEAR || MONTH
    </select>

    <select id="queryReport3WeekList" resultType="java.lang.String">
        SELECT DISTINCT T.YEAR || T.WEEK_NO
        FROM SY_CALENDAR T
        WHERE T.DATE$ BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'YYYY/MM') AND LAST_DAY(TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'YYYY/MM'))
          AND T.NAME = 'National Holidays'
        ORDER BY T.YEAR || T.WEEK_NO
    </select>

    <select id="queryReport3" resultType="java.util.Map">
        with BASE AS (
                SELECT * FROM ${SCPA.AC2_V} T
                <where>
                    <include refid="AC2Filter"/>
                </where>
        ), AC2_WEEKLY as (
                SELECT t.*, T2.RCA_RESULT, T2.RCA_TYPE
                FROM BASE t LEFT JOIN AC2_RCA_FEEDBACK T2 ON T.SALES_ORDER_NUMBER = T2.SALES_ORDER_NUMBER AND T.SALES_ORDER_ITEM = T2.SALES_ORDER_ITEM
                WHERE t.CALENDAR_WEEK in
                <foreach collection="weeks" separator="," item="item" open="(" close=")">
                    #{item, jdbcType=VARCHAR}
                </foreach>
        ), AC2_MONTHLY as (
                SELECT t.*
                FROM BASE t
                WHERE t.CALENDAR_MONTH in
                <foreach collection="months" separator="," item="item" open="(" close=")">
                    #{item, jdbcType=VARCHAR}
                </foreach>
        ), AC2_YEARLY as (
                SELECT *
                FROM BASE t
                WHERE t.CALENDAR_YEAR in
                <foreach collection="years" separator="," item="item" open="(" close=")">
                    #{item, jdbcType=VARCHAR}
                </foreach>
        )
        SELECT <foreach collection="fields" separator="," item="item">
                    TT."${item}"
               </foreach>,
               <foreach collection="years" separator="," item="item">
                   TT."'${item}'_ONTIME" as "Y${item}_ONTIME",
                   TT."'${item}'_DELAY" as "Y${item}_DELAY",
                   TT."'${item}'_EARLY" as "Y${item}_EARLY",
                   TT."'${item}'_OTHERS" as "Y${item}_OTHERS",
                   TT."'${item}'_RATIO" as "Y${item}_RATIO"
               </foreach>,
               <foreach collection="months" separator="," item="item">
                   MM."'${item}'_ONTIME" as "M${item}_ONTIME",
                   MM."'${item}'_DELAY" as "M${item}_DELAY",
                   MM."'${item}'_EARLY" as "M${item}_EARLY",
                   MM."'${item}'_OTHERS" as "M${item}_OTHERS",
                   MM."'${item}'_RATIO" as "M${item}_RATIO"
               </foreach>,
               <foreach collection="weeks" separator="," item="item">
                   WW."'${item}'_ONTIME" as "W${item}_ONTIME",
                   WW."'${item}'_DELAY" as "W${item}_DELAY",
                   WW."'${item}'_EARLY" as "W${item}_EARLY",
                   WW."'${item}'_OTHERS" as "W${item}_OTHERS",
                   WW."'${item}'_RATIO" as "W${item}_RATIO",
                   WW."'${item}'_DELAY_CONFIRMED" as "W${item}_DELAY_CONFIRMED"
               </foreach>
        FROM (
            SELECT *
            FROM (select <foreach collection="fields" separator="," item="item">
                             nvl(t."${item}", 'Others')                  ${item}
                         </foreach>,
                         count(decode(t.${ac2TypeColumn}, 'AC2_ON_TIME', 1, null)) ontime,
                         count(decode(t.${ac2TypeColumn}, 'AC2_DELAY', 1, null)) delay,
                         count(decode(t.${ac2TypeColumn}, 'AC2_EARLY', 1, null)) early,
                         count(decode(t.${ac2TypeColumn}, 'AC2_OTHERS', 1, null)) others,
                         t.CALENDAR_YEAR                                  years
                    FROM AC2_YEARLY t
                    group by <foreach collection="fields" separator="," item="item">
                            nvl(t."${item}", 'Others')
                         </foreach>, t.CALENDAR_YEAR
                ) mm PIVOT (
                            sum(ontime) ontime, sum(delay) delay, sum(early) early, sum(others) others,
                            <choose>
                                <when test="reportCalType == 'AC2'.toString()">
                                    sum(decode((ontime + delay + early + others), 0, 0, ontime / (ontime + delay + early + others))) ratio
                                </when>
                                <when test="reportCalType == 'AC2_EARLY'.toString()">
                                    sum(decode((ontime + delay + early + others), 0, 0, (ontime + early) / (ontime + delay + early + others))) ratio
                                </when>
                                <when test="reportCalType == 'LATE C2'.toString()">
                                    sum(decode((ontime + delay + early + others), 0, 0, delay / (ontime + delay + early + others))) ratio
                                </when>
                                <when test="reportCalType == 'LATE C2_OTHERS'.toString()">
                                    sum(decode((ontime + delay + early + others), 0, 0, (delay + others) / (ontime + delay + early + others))) ratio
                                </when>
                                <when test="reportCalType == 'FAILED_NONE'.toString()">
                                    sum(decode((ontime + delay + early + others), 0, 0, others / (ontime + delay + early + others))) ratio
                                </when>
                                <otherwise>
                                    sum(decode((ontime + delay + early + others), 0, 0, ontime / (ontime + delay + early + others))) ratio
                                </otherwise>
                            </choose>
                            FOR years IN
                            <foreach collection="years" separator="," item="item" open="(" close=")">
                                '${item}'
                            </foreach>
                            )) tt
            LEFT JOIN (
                    SELECT *
                    FROM (SELECT <foreach collection="fields" separator="," item="item">
                                    nvl(t."${item}", 'Others')                  ${item}
                                 </foreach>,
                                 count(decode(t.${ac2TypeColumn}, 'AC2_ON_TIME', 1, null)) ontime,
                                 count(decode(t.${ac2TypeColumn}, 'AC2_DELAY', 1, null)) delay,
                                 count(decode(t.${ac2TypeColumn}, 'AC2_EARLY', 1, null)) early,
                                 count(decode(t.${ac2TypeColumn}, 'AC2_OTHERS', 1, null)) others,
                                 t.CALENDAR_MONTH                                 months
                           FROM AC2_MONTHLY t
                           group by <foreach collection="fields" separator="," item="item">
                                       nvl(t."${item}", 'Others')
                                    </foreach>, t.CALENDAR_MONTH
                        ) mm PIVOT (
                                    sum(ontime) ontime, sum(delay) delay, sum(early) early, sum(others) others,
                                    <choose>
                                        <when test="reportCalType == 'AC2'.toString()">
                                            sum(decode((ontime + delay + early + others), 0, 0, ontime / (ontime + delay + early + others))) ratio
                                        </when>
                                        <when test="reportCalType == 'AC2_EARLY'.toString()">
                                            sum(decode((ontime + delay + early + others), 0, 0, (ontime + early) / (ontime + delay + early + others))) ratio
                                        </when>
                                        <when test="reportCalType == 'LATE C2'.toString()">
                                            sum(decode((ontime + delay + early + others), 0, 0, delay / (ontime + delay + early + others))) ratio
                                        </when>
                                        <when test="reportCalType == 'LATE C2_OTHERS'.toString()">
                                            sum(decode((ontime + delay + early + others), 0, 0, (delay + others) / (ontime + delay + early + others))) ratio
                                        </when>
                                        <when test="reportCalType == 'FAILED_NONE'.toString()">
                                            sum(decode((ontime + delay + early + others), 0, 0, others / (ontime + delay + early + others))) ratio
                                        </when>
                                        <otherwise>
                                            sum(decode((ontime + delay + early + others), 0, 0, ontime / (ontime + delay + early + others))) ratio
                                        </otherwise>
                                    </choose>
                                    FOR months IN
                                    <foreach collection="months" separator="," item="item" open="(" close=")">
                                        '${item}'
                                    </foreach>
                                    )
                    ) mm ON <foreach collection="fields" separator=" and " item="item">
                                mm."${item}" = tt."${item}"
                            </foreach>
            LEFT JOIN (
                    SELECT *
                    FROM (SELECT <foreach collection="fields" separator="," item="item">
                            nvl(t."${item}", 'Others')                  ${item}
                            </foreach>,
                            count(decode(t.${ac2TypeColumn}, 'AC2_ON_TIME', 1, null)) ontime,
                            count(decode(t.${ac2TypeColumn}, 'AC2_DELAY', 1, null)) delay,
                            count(decode(t.${ac2TypeColumn}, 'AC2_EARLY', 1, null)) early,
                            count(decode(t.${ac2TypeColumn}, 'AC2_OTHERS', 1, null)) others,
                            count(decode(t.RCA_TYPE, 'DELAY', T.RCA_RESULT, NULL)) delay_confirmed,
                            t.CALENDAR_WEEK                                  weeks
                            FROM AC2_WEEKLY t
                            group by <foreach collection="fields" separator="," item="item">
                                        nvl(t."${item}", 'Others')
                                     </foreach>, CALENDAR_WEEK
                            ) mm PIVOT (
                                        sum(ontime) ontime, sum(delay) delay, sum(early) early, sum(others) others, sum(delay_confirmed) delay_confirmed,
                                        <choose>
                                            <when test="reportCalType == 'AC2'.toString()">
                                                sum(decode((ontime + delay + early + others), 0, 0, ontime / (ontime + delay + early + others))) ratio
                                            </when>
                                            <when test="reportCalType == 'AC2_EARLY'.toString()">
                                                sum(decode((ontime + delay + early + others), 0, 0, (ontime + early) / (ontime + delay + early + others))) ratio
                                            </when>
                                            <when test="reportCalType == 'LATE C2'.toString()">
                                                sum(decode((ontime + delay + early + others), 0, 0, delay / (ontime + delay + early + others))) ratio
                                            </when>
                                            <when test="reportCalType == 'LATE C2_OTHERS'.toString()">
                                                sum(decode((ontime + delay + early + others), 0, 0, (delay + others) / (ontime + delay + early + others))) ratio
                                            </when>
                                            <when test="reportCalType == 'FAILED_NONE'.toString()">
                                                sum(decode((ontime + delay + early + others), 0, 0, others / (ontime + delay + early + others))) ratio
                                            </when>
                                            <otherwise>
                                                sum(decode((ontime + delay + early + others), 0, 0, ontime / (ontime + delay + early + others))) ratio
                                            </otherwise>
                                        </choose>
                                    FOR weeks IN
                                    <foreach collection="weeks" separator="," item="item" open="(" close=")">
                                        '${item}'
                                    </foreach>
                                    )
                    ) ww ON <foreach collection="fields" separator=" and " item="item">
                                ww."${item}" = tt."${item}"
                            </foreach>
        order by
        <if test="_page.sort != null and _page.sort != ''.toString()">
            ${_page.sort},
        </if>
        <choose>
            <when test="fields.contains('ENTITY') or fields.contains('MRP_CONTROLLER')">
                <foreach collection="fields" item="item" separator=",">
                    decode(tt.${item}, 'Others', 'zzz', tt.${item})
                </foreach>
            </when>
            <otherwise>
                <foreach collection="fields" item="item" separator=",">
                    decode(tt.${item}, 'Others', 'zzz', tt.${item})
                </foreach>
            </otherwise>
        </choose>
    </select>

    <sql id="queryReport3DetailsSQL">
        SELECT T4.RCA_RESULT,
               T4.RCA_COMMENTS,
               T2.RCA_CODE AS RCA_TIPS,
               T3.RECOM_RCA_CODE,
               T3.DESCRIPTION AS RCA_TIPS_DESCRIPTION,
               T.*
        FROM ${SCPA.AC2_V} T LEFT JOIN ${SCPA.AC2_RCA_V} T2 ON T.SALES_ORDER_NUMBER = T2.SALES_ORDER_NUMBER AND T.SALES_ORDER_ITEM = T2.SALES_ORDER_ITEM
                             LEFT JOIN ${SCPA.AC2_RCA_TIPS} T3 ON T2.RCA_CODE = T3.RCA_TIPS_CODE
                             LEFT JOIN ${SCPA.AC2_RCA_FEEDBACK} T4 ON T.SALES_ORDER_NUMBER = T4.SALES_ORDER_NUMBER AND T.SALES_ORDER_ITEM = T4.SALES_ORDER_ITEM
        <where>
            <if test="report3SelectedValues.size() > 1">
                <if test="report3SelectedValues[0] != 'RATIO'.toString()">
                    <choose>
                        <when test="report3SelectedValues[0] == 'ONTIME'.toString() or report3SelectedValues[0] == 'On Time'.toString()">
                            AND T.${ac2TypeColumn} = 'AC2_ON_TIME'
                        </when>
                        <when test="report3SelectedValues[0] == 'DELAY'.toString()">
                            AND T.${ac2TypeColumn} = 'AC2_DELAY'
                        </when>
                        <when test="report3SelectedValues[0] == 'EARLY'.toString()">
                            AND T.${ac2TypeColumn} = 'AC2_EARLY'
                        </when>
                        <when test="report3SelectedValues[0] == 'OTHERS'.toString()">
                            AND T.${ac2TypeColumn} = 'AC2_OTHERS'
                        </when>
                        <when test="report3SelectedValues[0] == 'RATIO'.toString()"/>
                        <otherwise>
                            AND T.${ac2TypeColumn} = #{report3SelectedValues[0], jdbcType=VARCHAR}
                        </otherwise>
                    </choose>
                </if>
                <if test="report3DetailsDateColumn != null">
                    AND T.${report3DetailsDateColumn} = #{report3DetailsDateValue, jdbcType=VARCHAR}
                </if>
                <if test="report3SelectedValues.size() > 2">
                    <foreach collection="fields" item="item" index="index">
                        <if test="report3SelectedValues[index + 2] != null and report3SelectedValues[index + 2] != ''.toString() and report3SelectedValues[index + 2] != 'Total'.toString()">
                            <choose>
                                <when test="report3SelectedValues[index + 2] == 'Others'.toString()">
                                    AND (T.${item} = 'Others' or T.${item} is null)
                                </when>
                                <otherwise>
                                    AND T.${item} = #{report3SelectedValues[${index + 2}], jdbcType=VARCHAR}
                                </otherwise>
                            </choose>
                        </if>
                    </foreach>
                </if>
            </if>
            <include refid="AC2Filter"/>
        </where>
    </sql>

    <select id="queryReport3DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport3DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport3Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport3DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <insert id="saveReport3Details" parameterType="java.util.Map">
        merge into ${SCPA.AC2_RCA_FEEDBACK} t
        using
        (
        <foreach collection="dataList" item="item" separator="union all">
            SELECT #{item.order, jdbcType=VARCHAR} SALES_ORDER_NUMBER,
                   #{item.item, jdbcType=VARCHAR} SALES_ORDER_ITEM,
                   #{item.rca_result, jdbcType=VARCHAR} RCA_RESULT,
                   #{item.rca_type, jdbcType=VARCHAR} RCA_TYPE,
                   #{item.rca_comments, jdbcType=VARCHAR} RCA_COMMENTS
            from dual
        </foreach>
        ) s on (t.SALES_ORDER_NUMBER = s.SALES_ORDER_NUMBER and t.SALES_ORDER_ITEM = s.SALES_ORDER_ITEM)
        when matched then
        update set
            t.rca_result = s.rca_result,
            t.rca_type = s.rca_type,
            t.rca_comments = s.rca_comments,
            t.update_date$ = sysdate,
            t.update_by$ = #{session.userid, jdbcType=VARCHAR}
        when not matched then
        insert (SALES_ORDER_NUMBER, SALES_ORDER_ITEM, RCA_RESULT, RCA_TYPE, RCA_COMMENTS, CREATE_BY$, CREATE_DATE$)
        values (s.sales_order_number, s.sales_order_item, s.rca_result, s.rca_type, s.rca_comments, #{session.userid, jdbcType=VARCHAR}, sysdate)
    </insert>
</mapper>
