<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.customer.dao.IOTDCDao">
    <sql id="otdcFilter">
        <if test="_filters != null and _filters != ''.toString()">
            AND ${_filters}
        </if>
    </sql>

    <select id="queryOtdcCascader" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT CATEGORY, NAME
        FROM OTDC_DC_SOURCE_FILTER_V
        ORDER BY CATEGORY, DECODE(NAME, 'Others', CAST(UNISTR('\ffff\ffff') AS VARCHAR2(8)), NAME)
    </select>

    <select id="queryOtdcYears" parameterType="java.util.Map" resultType="java.lang.String">
        SELECT SOURCE_DATE
          FROM OTDC_DC_SOURCE_DATE_V
         WHERE SOURCE_DATE BETWEEN SUBSTR(#{dateRange[0], jdbcType=VARCHAR}, 0, 4) AND SUBSTR(#{dateRange[1], jdbcType=VARCHAR}, 0, 4)
           AND TYPE = 'YEAR'
         ORDER BY SOURCE_DATE
    </select>

    <select id="queryOtdcMonths" parameterType="java.util.Map" resultType="java.lang.String">
        SELECT SOURCE_DATE
          FROM OTDC_DC_SOURCE_DATE_V
         WHERE SOURCE_DATE BETWEEN #{dateRange[0], jdbcType=VARCHAR} AND #{dateRange[1], jdbcType=VARCHAR}
           AND TYPE = 'MONTH'
         ORDER BY SOURCE_DATE
    </select>

    <select id="queryOtdcWeeks" parameterType="java.util.Map" resultType="java.lang.String">
        SELECT SOURCE_DATE
          FROM OTDC_DC_SOURCE_DATE_V
         WHERE TYPE = 'WEEK'
         ORDER BY SOURCE_DATE
    </select>

    <select id="queryReport1" parameterType="java.util.Map" resultType="java.util.Map">
        WITH BASE AS (
            SELECT <foreach collection="selectedField" item="item">
                        ${item},
                   </foreach>
                   OTDC_MONTH            AS MONTH,
                   OTDC_ONTIME           AS ONTIME
            FROM ${SCPA.OTDC_DC_SOURCE_V} T
            WHERE OTDC_ONTIME IN ('O', 'D')
              AND OTDC_MONTH IN
            <foreach collection="selectedMonths" separator="," item="month" open="(" close=")">
                '${month}'
            </foreach>
            <include refid="otdcFilter"/>
        ),
             FIELDS AS (
                 SELECT DISTINCT
                   <foreach collection="selectedField" item="item" separator=",">
                        ${item}
                   </foreach>
                 FROM BASE
             ),
             ONTIME_BASE AS (
                 SELECT <foreach collection="selectedField" item="item">
                            F.${item},
                        </foreach>
                        <foreach collection="selectedMonths" separator="," item="month">
                            NVL("'${month}'_ONTIME", 0) AS "'${month}'_ONTIME"
                        </foreach>
                 FROM FIELDS F
                          LEFT JOIN
                      (
                          SELECT <foreach collection="selectedField" item="item">
                                    ${item},
                                 </foreach>
                                 <foreach collection="selectedMonths" separator="," item="month">
                                     "'${month}'_ONTIME"
                                 </foreach>
                          FROM (
                              SELECT <foreach collection="selectedField" item="item">
                                        ${item},
                                     </foreach>
                                     MONTH,
                                     COUNT(1) AS ONTIME_LINES
                              FROM BASE
                              WHERE ONTIME = 'O'
                              GROUP BY
                              <foreach collection="selectedField" item="item">
                                ${item},
                              </foreach> MONTH
                          )
                              PIVOT (
                              MAX(ONTIME_LINES) AS ONTIME
                              FOR MONTH IN
                                <foreach collection="selectedMonths" separator="," item="month" open="(" close=")">
                                    '${month}'
                                </foreach>
                              )
                      ) T ON <foreach collection="selectedField" item="item" separator=" and ">
                                    F.${item} = T.${item}
                             </foreach>
             ),
             DELAYED_BASE AS (
                 SELECT <foreach collection="selectedField" item="item">
                            F.${item},
                        </foreach>
                        <foreach collection="selectedMonths" separator="," item="month">
                            NVL("'${month}'_DELAYED", 0) AS "'${month}'_DELAYED"
                        </foreach>
                 FROM FIELDS F
                          LEFT JOIN
                      (
                          SELECT <foreach collection="selectedField" item="item">
                                    ${item},
                                 </foreach>
                                 <foreach collection="selectedMonths" separator="," item="month">
                                     "'${month}'_DELAYED"
                                 </foreach>
                          FROM (
                              SELECT <foreach collection="selectedField" item="item">
                                        ${item},
                                     </foreach>
                                     MONTH,
                                     COUNT(1) AS DELAY_LINES
                              FROM BASE
                              WHERE ONTIME = 'D'
                              GROUP BY
                              <foreach collection="selectedField" item="item">
                                  ${item},
                              </foreach> MONTH
                          )
                              PIVOT (
                              MAX(DELAY_LINES) AS DELAYED
                              FOR MONTH IN
                                <foreach collection="selectedMonths" separator="," item="month" open="(" close=")">
                                    '${month}'
                                </foreach>
                              )
                      ) T ON <foreach collection="selectedField" item="item" separator=" and ">
                                    F.${item} = T.${item}
                             </foreach>
             ),
             RATIO_BASE AS (
                 SELECT <foreach collection="selectedField" item="item">
                            O.${item},
                        </foreach>
                        <foreach collection="selectedMonths" separator="," item="month">
                            DECODE(O."'${month}'_ONTIME" + D."'${month}'_DELAYED", 0, 0,
                                   O."'${month}'_ONTIME" / (O."'${month}'_ONTIME" + D."'${month}'_DELAYED")) AS "'${month}'_RATIO"
                        </foreach>
                 FROM ONTIME_BASE O
                          INNER JOIN DELAYED_BASE D ON
                             <foreach collection="selectedField" item="item" separator=" and ">
                                    O.${item} = D.${item}
                             </foreach>
             ),
             TOTAL_ONTIME_BASE AS (
                 SELECT <foreach collection="selectedField" item="item">
                            'Total' AS "${item}",
                        </foreach>
                        <foreach collection="selectedMonths" separator="," item="month">
                            "'${month}'_ONTIME"
                        </foreach>
                 FROM (
                     SELECT MONTH,
                            COUNT(1) AS ONTIME_LINES
                     FROM BASE
                     WHERE ONTIME = 'O'
                     GROUP BY MONTH
                 )
                     PIVOT (
                     MAX(ONTIME_LINES) AS ONTIME
                     FOR MONTH IN
                        <foreach collection="selectedMonths" separator="," item="month" open="(" close=")">
                            '${month}'
                        </foreach>
                     )
             ),
             TOTAL_DELAYED_BASE AS (
                 SELECT <foreach collection="selectedField" item="item">
                            'Total' AS "${item}",
                        </foreach>
                        <foreach collection="selectedMonths" separator="," item="month">
                            NVL("'${month}'_DELAYED", 0) AS "'${month}'_DELAYED"
                        </foreach>
                 FROM (
                     SELECT MONTH,
                            COUNT(1) AS DELAY_LINES
                     FROM BASE
                     WHERE ONTIME = 'D'
                     GROUP BY MONTH
                 )
                     PIVOT (
                     MAX(DELAY_LINES) AS DELAYED
                     FOR MONTH IN
                        <foreach collection="selectedMonths" separator="," item="month" open="(" close=")">
                            '${month}'
                        </foreach>
                     )
             ),
             TOTAL_RATIO_BASE AS (
                 SELECT <foreach collection="selectedField" item="item">
                            O.${item},
                        </foreach>
                        <foreach collection="selectedMonths" separator="," item="month">
                            DECODE(O."'${month}'_ONTIME" + D."'${month}'_DELAYED", 0, 0,
                            O."'${month}'_ONTIME" / (O."'${month}'_ONTIME" + D."'${month}'_DELAYED")) AS "'${month}'_RATIO"
                        </foreach>
                 FROM TOTAL_ONTIME_BASE O
                          INNER JOIN TOTAL_DELAYED_BASE D ON
                          <foreach collection="selectedField" item="item" separator=" and ">
                            O.${item} = D.${item}
                          </foreach>
             ),
             YTD_BASE AS (
                 SELECT <foreach collection="selectedField" item="item">
                            ${item},
                        </foreach>
                        OTDC_YEAR             AS YEAR,
                        OTDC_ONTIME           AS ONTIME
                 FROM ${SCPA.OTDC_DC_SOURCE_V} T
                 WHERE OTDC_ONTIME IN ('O', 'D')
                   AND OTDC_YEAR IN
                 <foreach collection="selectedYears" separator="," item="year" open="(" close=")">
                     '${year}'
                 </foreach>
                 <include refid="otdcFilter"/>
             ),
             YTD_ONTIME_BASE AS (
                 SELECT <foreach collection="selectedField" item="item">
                            F.${item},
                        </foreach>
                        <foreach collection="selectedYears" separator="," item="year">
                            NVL("'${year}'_ONTIME", 0) AS "'${year}'_ONTIME"
                        </foreach>
                 FROM FIELDS F
                          LEFT JOIN
                      (
                          SELECT <foreach collection="selectedField" item="item">
                                    ${item},
                                 </foreach>
                                 <foreach collection="selectedYears" separator="," item="year">
                                     "'${year}'_ONTIME"
                                 </foreach>
                          FROM (
                              SELECT <foreach collection="selectedField" item="item">
                                        ${item},
                                     </foreach>
                                     YEAR,
                                     COUNT(1) AS ONTIME_LINES
                              FROM YTD_BASE
                              WHERE ONTIME = 'O'
                              GROUP BY
                              <foreach collection="selectedField" item="item">
                                ${item},
                              </foreach> YEAR
                          )
                              PIVOT (
                              MAX(ONTIME_LINES) AS ONTIME
                              FOR YEAR IN
                                <foreach collection="selectedYears" separator="," item="year" open="(" close=")">
                                    '${year}'
                                </foreach>
                              )
                      ) T ON
                      <foreach collection="selectedField" item="item" separator=" and ">
                            F.${item} = T.${item}
                      </foreach>
             ),
             YTD_DELAYED_BASE AS (
                 SELECT <foreach collection="selectedField" item="item">
                            F.${item},
                        </foreach>
                        <foreach collection="selectedYears" separator="," item="year">
                            NVL("'${year}'_DELAYED", 0) AS "'${year}'_DELAYED"
                        </foreach>
                 FROM FIELDS F
                          LEFT JOIN
                      (
                          SELECT <foreach collection="selectedField" item="item">
                                    ${item},
                                 </foreach>
                                 <foreach collection="selectedYears" separator="," item="year">
                                     "'${year}'_DELAYED"
                                 </foreach>
                          FROM (
                              SELECT <foreach collection="selectedField" item="item">
                                        ${item},
                                     </foreach>
                                     YEAR,
                                     COUNT(1) AS DELAY_LINES
                              FROM YTD_BASE
                              WHERE ONTIME = 'D'
                              GROUP BY
                              <foreach collection="selectedField" item="item">
                                ${item},
                              </foreach> YEAR
                          )
                              PIVOT (
                              MAX(DELAY_LINES) AS DELAYED
                              FOR YEAR IN
                                <foreach collection="selectedYears" separator="," item="year" open="(" close=")">
                                    '${year}'
                                </foreach>
                              )
                      ) T ON
                      <foreach collection="selectedField" item="item" separator=" and ">
                            F.${item} = T.${item}
                      </foreach>
             ),
             YTD_RATIO_BASE AS (
                 SELECT <foreach collection="selectedField" item="item">
                            O.${item},
                        </foreach>
                        <foreach collection="selectedYears" separator="," item="year">
                            DECODE(O."'${year}'_ONTIME" + D."'${year}'_DELAYED", 0, 0,
                            O."'${year}'_ONTIME" / (O."'${year}'_ONTIME" + D."'${year}'_DELAYED")) AS "'${year}'_RATIO"
                        </foreach>
                 FROM YTD_ONTIME_BASE O
                          INNER JOIN YTD_DELAYED_BASE D ON
                          <foreach collection="selectedField" item="item" separator=" and ">
                            O.${item} = D.${item}
                          </foreach>
             ),
             YTD_TOTAL_ONTIME_BASE AS (
                 SELECT <foreach collection="selectedField" item="item">
                            'Total' AS "${item}",
                        </foreach>
                        <foreach collection="selectedYears" separator="," item="year">
                            NVL("'${year}'_ONTIME", 0) AS "'${year}'_ONTIME"
                        </foreach>
                 FROM (
                     SELECT YEAR,
                            COUNT(1) AS ONTIME_LINES
                     FROM YTD_BASE
                     WHERE ONTIME = 'O'
                     GROUP BY YEAR
                 )
                     PIVOT (
                     MAX(ONTIME_LINES) AS ONTIME
                     FOR YEAR IN
                        <foreach collection="selectedYears" separator="," item="year" open="(" close=")">
                            '${year}'
                        </foreach>
                     )
             ),
             YTD_TOTAL_DELAYED_BASE AS (
                 SELECT <foreach collection="selectedField" item="item">
                            'Total' AS "${item}",
                        </foreach>
                        <foreach collection="selectedYears" separator="," item="year">
                            NVL("'${year}'_DELAYED", 0) AS "'${year}'_DELAYED"
                        </foreach>
                 FROM (
                     SELECT YEAR,
                            COUNT(1) AS DELAY_LINES
                     FROM YTD_BASE
                     WHERE ONTIME = 'D'
                     GROUP BY YEAR
                 )
                     PIVOT (
                     MAX(DELAY_LINES) AS DELAYED
                     FOR YEAR IN
                        <foreach collection="selectedYears" separator="," item="year" open="(" close=")">
                            '${year}'
                        </foreach>
                     )
             ),
             YTD_TOTAL_RATIO_BASE AS (
                 SELECT <foreach collection="selectedField" item="item">
                            O.${item},
                        </foreach>
                        <foreach collection="selectedYears" separator="," item="year">
                            DECODE(O."'${year}'_ONTIME" + D."'${year}'_DELAYED", 0, 0,
                            O."'${year}'_ONTIME" / (O."'${year}'_ONTIME" + D."'${year}'_DELAYED")) AS "'${year}'_RATIO"
                        </foreach>
                 FROM YTD_TOTAL_ONTIME_BASE O
                          INNER JOIN YTD_TOTAL_DELAYED_BASE D ON
                          <foreach collection="selectedField" item="item" separator=" and ">
                            O.${item} = D.${item}
                          </foreach>
             ),
             UNITED AS (
                 SELECT <foreach collection="selectedField" item="item">
                            F.${item},
                        </foreach>
                        <foreach collection="selectedYears" separator="," item="year">
                            NVL(YOB."'${year}'_ONTIME", 0)   AS "'${year}'_ONTIME",
                            NVL(YDB."'${year}'_DELAYED", 0)  AS "'${year}'_DELAYED",
                            NVL(YRB."'${year}'_RATIO", 0)    AS "'${year}'_RATIO"
                        </foreach>,
                        <foreach collection="selectedMonths" separator="," item="month">
                            NVL(OB."'${month}'_ONTIME", 0)  AS "'${month}'_ONTIME",
                            NVL(DB."'${month}'_DELAYED", 0) AS "'${month}'_DELAYED",
                            NVL(RB."'${month}'_RATIO", 0)   AS "'${month}'_RATIO"
                        </foreach>
                 FROM FIELDS F
                          LEFT JOIN ONTIME_BASE OB ON <foreach collection="selectedField" item="item" separator=" and ">F.${item} = OB.${item}</foreach>
                          LEFT JOIN DELAYED_BASE DB ON <foreach collection="selectedField" item="item" separator=" and ">F.${item} = DB.${item}</foreach>
                          LEFT JOIN RATIO_BASE RB ON <foreach collection="selectedField" item="item" separator=" and ">F.${item} = RB.${item}</foreach>
                          LEFT JOIN YTD_ONTIME_BASE YOB ON <foreach collection="selectedField" item="item" separator=" and ">F.${item} = YOB.${item}</foreach>
                          LEFT JOIN YTD_DELAYED_BASE YDB ON <foreach collection="selectedField" item="item" separator=" and ">F.${item} = YDB.${item}</foreach>
                          LEFT JOIN YTD_RATIO_BASE YRB ON <foreach collection="selectedField" item="item" separator=" and ">F.${item} = YRB.${item}</foreach>
                 UNION ALL
                 SELECT <foreach collection="selectedField" item="item">
                            TOB.${item},
                        </foreach>
                        <foreach collection="selectedYears" separator="," item="year">
                            NVL(YTOB."'${year}'_ONTIME", 0)   AS "'${year}'_ONTIME",
                            NVL(YTDB."'${year}'_DELAYED", 0)  AS "'${year}'_DELAYED",
                            NVL(YTRB."'${year}'_RATIO", 0)    AS "'${year}'_RATIO"
                        </foreach>,
                        <foreach collection="selectedMonths" separator="," item="month">
                            NVL(TOB."'${month}'_ONTIME", 0)  AS "'${month}'_ONTIME",
                            NVL(TDB."'${month}'_DELAYED", 0) AS "'${month}'_DELAYED",
                            NVL(TRB."'${month}'_RATIO", 0)   AS "'${month}'_RATIO"
                        </foreach>
                 FROM TOTAL_ONTIME_BASE TOB
                          LEFT JOIN TOTAL_DELAYED_BASE TDB ON <foreach collection="selectedField" item="item" separator=" and ">TOB.${item} = TDB.${item}</foreach>
                          LEFT JOIN TOTAL_RATIO_BASE TRB ON <foreach collection="selectedField" item="item" separator=" and ">TOB.${item} = TRB.${item}</foreach>
                          LEFT JOIN YTD_TOTAL_ONTIME_BASE YTOB ON <foreach collection="selectedField" item="item" separator=" and ">TOB.${item} = YTOB.${item}</foreach>
                          LEFT JOIN YTD_TOTAL_DELAYED_BASE YTDB ON <foreach collection="selectedField" item="item" separator=" and ">TOB.${item} = YTDB.${item}</foreach>
                          LEFT JOIN YTD_TOTAL_RATIO_BASE YTRB ON <foreach collection="selectedField" item="item" separator=" and ">TOB.${item} = YTRB.${item}</foreach>
             )
        SELECT <foreach collection="selectedField" item="item">
                    ${item},
               </foreach>
               <foreach collection="selectedYears" separator="," item="year">
                   "'${year}'_ONTIME",
                   "'${year}'_DELAYED",
                   "'${year}'_RATIO"
               </foreach>,
               <foreach collection="selectedMonths" separator="," item="month">
                   "'${month}'_ONTIME",
                   "'${month}'_DELAYED",
                   "'${month}'_RATIO"
               </foreach>
        FROM UNITED
        ORDER BY
        <choose>
            <when test="_page.sort != null and _page.sort != ''.toString()">
                ${_page.sort}
            </when>
            <otherwise>
                <foreach collection="selectedField" item="item" separator=",">
                    DECODE(${item}, 'Others', 'zzz', ${item})
                </foreach>
            </otherwise>
        </choose>
    </select>

    <select id="queryReport2" resultType="java.util.Map">
        SELECT T.OTDC_MONTH,
               T.ONTIME_CATEGORY,
               COUNT(1) AS LINES
        FROM ${SCPA.OTDC_DC_SOURCE_V} T
       WHERE OTDC_MONTH BETWEEN #{dateRange[0], jdbcType=VARCHAR} AND #{dateRange[1], jdbcType=VARCHAR}
        <if test="report1SelectedFieldValue.size() > 0">
            <foreach collection="selectedField" item="item" index="index">
                <if test="report1SelectedFieldValue[index] != null and report1SelectedFieldValue[index] != ''.toString() and report1SelectedFieldValue[index] != 'Total'.toString()">
                    and ${item} = #{report1SelectedFieldValue[${index}], jdbcType=VARCHAR}
                </if>
            </foreach>
        </if>
        <if test="ontimeCategories != null and ontimeCategories.isEmpty() == false">
            AND OTDC_ONTIME IN
            <foreach collection="ontimeCategories" item="cat" separator="," open="(" close=")">
                #{cat,jdbcType=VARCHAR}
            </foreach>
        </if>
        <include refid="otdcFilter"/>
        GROUP BY T.OTDC_MONTH, T.ONTIME_CATEGORY
        ORDER BY T.OTDC_MONTH, T.ONTIME_CATEGORY
    </select>

    <sql  id="otdcColumn">
        TO_CHAR(OTDC_DATE, 'yyyy/mm/dd') OTDC_DATE,
        OTDC_MONTH,
        OTDC_QUARTER,
        OTDC_YEAR,
        MATERIAL,
        MATERIAL_OWNER_NAME,
        MATERIAL_OWNER_SESA,
        PLANT_CODE,
        SALES_ORGANIZATION,
        SALES_DISTRICT,
        SALES_ORDER_NUMBER,
        SALES_ORDER_ITEM,
        ORDER_QUANTITY,
        SHIPPING_POINT,
        FORWARDER_CODE,
        FORWARDER_NAME,
        SHIP_TO,
        SOLD_TO,
        CUSTOMER_NAME,
        OTDC_ONTIME,
        OTDS_ONTIME,
        OTDT_ONTIME,
        SO04_OTDS_ONTIME,
        STOCK_INDENT,
        OTDS_RCA,
        RCA_RESULT,
        ONTIME_CATEGORY,
        TO_CHAR(CUSTOMER_ETA_DATE, 'yyyy/mm/dd') CUSTOMER_ETA_DATE,
        TO_CHAR(CUSTOMER_GR_DATE, 'yyyy/mm/dd') CUSTOMER_GR_DATE,
        TO_CHAR(SHIPMENT_DATE, 'yyyy/mm/dd') SHIPMENT_DATE,
        UNIT_COST,
        COUNTRY,
        MRP_CONTROLLER,
        PRODUCT_LINE,
        ENTITY,
        CLUSTER_NAME,
        BU,
        LOCAL_BU,
        LOCAL_PRODUCT_FAMILY,
        LOCAL_PRODUCT_LINE,
        LOCAL_PRODUCT_SUBFAMILY
    </sql>

    <sql id="queryReport1DetailsSql">
        SELECT
            <include refid="otdcColumn"/>
        FROM ${SCPA.OTDC_DC_SOURCE_V} T
        <where>
            <if test="report1SelectedFieldValue.size() > 0">
                <foreach collection="selectedField" item="item" index="index">
                    <if test="report1SelectedFieldValue[index] != null and report1SelectedFieldValue[index] != ''.toString() and report1SelectedFieldValue[index] != 'Total'.toString()">
                        and ${item} = #{report1SelectedFieldValue[${index}], jdbcType=VARCHAR}
                    </if>
                </foreach>
            </if>
            <include refid="otdcFilter"/>
            <if test="ontimeCategories != null and ontimeCategories.isEmpty() == false">
                AND OTDC_ONTIME IN
                <foreach collection="ontimeCategories" item="cat" separator="," open="(" close=")">
                    #{cat,jdbcType=VARCHAR}
                </foreach>
            </if>
            <choose>
                <when test="dateField == 'OTDC_MONTH'.toString()">
                    AND OTDC_MONTH IN
                    <foreach collection="months" item="month" separator="," open="(" close=")">
                        #{month, jdbcType=VARCHAR}
                    </foreach>
                </when>
                <otherwise>
                    AND OTDC_YEAR IN
                    <foreach collection="years" item="year" separator="," open="(" close=")">
                        #{year,jdbcType=VARCHAR}
                    </foreach>
                </otherwise>
            </choose>
            <if test='report2SelectedType != null and report2SelectedType.isEmpty() == false'>
                AND ONTIME_CATEGORY = #{report2SelectedType, jdbcType=VARCHAR}
            </if>
        </where>
    </sql>

    <select id="queryReport1DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1DetailsSql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1DetailsSql"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport3" parameterType="java.util.Map" resultType="java.util.Map">
        <include refid="global.select_header"/>
               SELECT
               <foreach collection="selectedField" item="item">
                    ${item},
               </foreach>
               <foreach collection="weeks" separator="," item="week">
                   NVL("'${week}'_OTDC_ONTIME", 0) AS "'${week}'_ONTIME",
                   NVL("'${week}'_OTDC_DELAYED", 0) AS "'${week}'_DELAYED",
                   DECODE(
                       NVL("'${week}'_OTDC_ONTIME", 0) + NVL("'${week}'_OTDC_DELAYED", 0), 0, 0,
                       NVL("'${week}'_OTDC_ONTIME", 0) /
                       (NVL("'${week}'_OTDC_ONTIME", 0) + NVL("'${week}'_OTDC_DELAYED", 0))
                   ) AS "'${week}'_RATIO"
               </foreach>
        FROM (
            SELECT <foreach collection="selectedField" item="item">
                        ${item},
                   </foreach>
                   YEAR || WEEK AS OTDC_WEEK,
                   NVL(((OTDS_ONTIME_LINES / OTDS_TOTAL_LINES) - OTDS_OTDC_RATIO_GAP) *
                       OTDS_TOTAL_LINES,
                       0)       AS OTDC_ONTIME,
                   NVL(OTDS_TOTAL_LINES -
                       ((OTDS_ONTIME_LINES / OTDS_TOTAL_LINES) - OTDS_OTDC_RATIO_GAP) *
                       OTDS_TOTAL_LINES,
                       0)       AS OTDC_DELAYED
            FROM ${SCPA.OTDC_DC_COMPARE_V} T
            <where>
                <include refid="otdcFilter"/>
            </where>
        )
            PIVOT (
            SUM(ROUND(OTDC_ONTIME, 0)) AS OTDC_ONTIME,
                SUM(ROUND(OTDC_DELAYED, 0)) AS OTDC_DELAYED
            FOR OTDC_WEEK IN
            <foreach collection="weeks" separator="," item="week" open="(" close=")">
                '${week}'
            </foreach>
            )
        UNION
        SELECT <foreach collection="selectedField" item="item">
                    ${item},
               </foreach>
               <foreach collection="weeks" separator="," item="week">
                   NVL("'${week}'_OTDC_ONTIME", 0) AS "'${week}'_ONTIME",
                   NVL("'${week}'_OTDC_DELAYED", 0) AS "'${week}'_DELAYED",
                   DECODE(
                       NVL("'${week}'_OTDC_ONTIME", 0) + NVL("'${week}'_OTDC_DELAYED", 0), 0, 0,
                       NVL("'${week}'_OTDC_ONTIME", 0) /
                       (NVL("'${week}'_OTDC_ONTIME", 0) + NVL("'${week}'_OTDC_DELAYED", 0))
                   ) AS "'${week}'_RATIO"
               </foreach>
        FROM (
            SELECT <foreach collection="selectedField" item="item">
                       'Total'  AS  "${item}",
                   </foreach>
                   YEAR || WEEK AS OTDC_WEEK,
                   NVL(((OTDS_ONTIME_LINES / OTDS_TOTAL_LINES) - OTDS_OTDC_RATIO_GAP) *
                       OTDS_TOTAL_LINES,
                       0)       AS OTDC_ONTIME,
                   NVL(OTDS_TOTAL_LINES -
                       ((OTDS_ONTIME_LINES / OTDS_TOTAL_LINES) - OTDS_OTDC_RATIO_GAP) *
                       OTDS_TOTAL_LINES,
                       0)       AS OTDC_DELAYED
            FROM ${SCPA.OTDC_DC_COMPARE_V} T
            <where>
                <include refid="otdcFilter"/>
            </where>
        )
            PIVOT (
            SUM(ROUND(OTDC_ONTIME, 0)) AS OTDC_ONTIME,
                SUM(ROUND(OTDC_DELAYED, 0)) AS OTDC_DELAYED
            FOR OTDC_WEEK IN
            <foreach collection="weeks" separator="," item="week" open="(" close=")">
                '${week}'
            </foreach>
            )
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport3DetailsSql">
        WITH BASE AS (
            SELECT MATERIAL,
                   PLANT_CODE,
                   YEAR || WEEK AS OTDC_WEEK,
                   OTDS_ONTIME_LINES,
                   OTDS_TOTAL_LINES,
                   AVG_OTDS_RATIO,
                   AVG_OTDC_RATIO,
                   OTDS_OTDC_RATIO_GAP,
                   SOLD_TO,
                   STOCK_INDENT,
                   FORWARDER_NAME,
                   FORWARDER_CODE,
                   SHIPPING_POINT,
                   COUNTRY,
                   MATERIAL_OWNER_NAME,
                   MATERIAL_OWNER_SESA,
                   MRP_CONTROLLER,
                   PRODUCT_LINE,
                   ENTITY,
                   CLUSTER_NAME,
                   BU,
                   LOCAL_BU,
                   LOCAL_PRODUCT_FAMILY,
                   LOCAL_PRODUCT_LINE,
                   LOCAL_PRODUCT_SUBFAMILY,
                   PLANT_NAME
            FROM ${SCPA.OTDC_DC_COMPARE_V}
        )
        SELECT * FROM BASE T
        <where>
            <if test="report1SelectedFieldValue.size() > 0">
                <foreach collection="selectedField" item="item" index="index">
                    <if test="report1SelectedFieldValue[index] != null and report1SelectedFieldValue[index] != ''.toString() and report1SelectedFieldValue[index] != 'Total'.toString()">
                        and ${item} = #{report1SelectedFieldValue[${index}], jdbcType=VARCHAR}
                    </if>
                </foreach>
            </if>
            <include refid="otdcFilter"/>
            AND OTDC_WEEK IN
            <foreach collection="weeks" item="week" separator="," open="(" close=")">
                #{week,jdbcType=VARCHAR}
            </foreach>
        </where>
    </sql>

    <select id="queryReport3DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport3DetailsSql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport3Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport3DetailsSql"/>
        <include refid="global.select_footer"/>
    </select>
</mapper>
