<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.customer.dao.IEndToEndSOTransferDao">

    <select id="queryPageAdmin" resultType="java.lang.String">
        select AUTH_DETAILS
        from SY_MENU_AUTH t
        where t.USER_ID = #{userid, jdbcType=VARCHAR} and t.MENU_CODE = #{parentCode, jdbcType=VARCHAR}
    </select>

    <select id="allowlistInitPage" resultType="java.util.Map">
        SELECT * FROM SCPA.E2E_SO_TRANSFER_ALLOWLIST_FILTER_V ORDER BY CATEGORY, DECODE(NAME,'Others','zzz',NAME)
    </select>

    <select id="allowlistRuleTypeOpts" resultType="java.util.Map">
        SELECT RULE_NAME, RULE_COLUMN FROM SCPA.MR3_E2E_SO_TRANSFER_ALLOWLIST_RULE
    </select>

	<select id="allowlistQueryReport1Count" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="allowlistQueryReport1SQL"/>
		<include refid="global.count_footer"/>
	</select>

	<select id="allowlistQueryReport1" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		<include refid="allowlistQueryReport1SQL"/>
		<include refid="global.select_footer"/>
	</select>

	<sql id="allowlistQueryReport1SQL">
		SELECT
        row_id,
		<foreach collection="report1ColumnsList" item="item" separator=",">
            ${item}
        </foreach>
		FROM ${SCPA.MR3_E2E_SO_TRANSFER_ALLOWLIST} T
        <where>
            T.RULE_NAME = #{ruleType, jdbcType=VARCHAR}
            <if test="_filters != null and _filters != ''.toString()">
                AND ${_filters}
            </if>
            <if test="isAdmin == false">
                AND created_by$ = #{userid, jdbcType=VARCHAR}
            </if>
        </where>
		ORDER BY CREATE_DATE$
	</sql>

	<insert id="allowlistCreateReport1">
		insert into MR3_E2E_SO_TRANSFER_ALLOWLIST
		(
        RULE_NAME,
		 <foreach collection="headers" item="header" separator=",">
            ${header}
		 </foreach>, create_by$, create_date$
		)
		<foreach collection="creates" item="list" separator=" union all ">
			select
			    #{ruleType, jdbcType=VARCHAR} AS RULE_NAME,
			    <foreach collection="headers" item="header" separator=",">
                    #{list.${header}, jdbcType=VARCHAR}
				   </foreach>
			, #{userid,jdbcType=VARCHAR}, sysdate
			FROM DUAL T
		</foreach>
	</insert>

    <update id="allowlistUpdateReport1">
        update MR3_E2E_SO_TRANSFER_ALLOWLIST
        SET
        <foreach collection="updates" item="col" separator=",">
            ${col.key} = #{col.value,jdbcType=VARCHAR}
        </foreach>,
        update_by$ = #{userid,jdbcType=VARCHAR},
        update_date$ = sysdate
        where row_id = #{rowid,jdbcType=VARCHAR}
        <if test="isAdmin == false">
            AND created_by$ = #{userid, jdbcType=VARCHAR}
        </if>
    </update>

	<delete id="allowlistDeleteReport1">
		delete from MR3_E2E_SO_TRANSFER_ALLOWLIST where row_id in
		<foreach collection="deletes" open="(" close=")" separator="," item="item">#{item, jdbcType=VARCHAR}</foreach>
		<if test="isAdmin == false">
			AND 0 = 1
		</if>
	</delete>

    <update id="mergeReport1Data">
        BEGIN
        MERGE INTO SCPA.MR3_E2E_SO_TRANSFER_ALLOWLIST T
        USING ( ${sql} ) T1
        ON (
            <foreach collection="report1ColumnsList" item="column" separator=" AND ">
                NVL(t.${column}, 'Others') = NVL(t1.${column}, 'Others')
            </foreach>
            AND #{session.userid, jdbcType=VARCHAR} = CREATE_BY$
        )
        WHEN NOT MATCHED THEN
        INSERT (
            <foreach collection="report1ColumnsList" item="column" separator=","> ${column} </foreach>,
            RULE_NAME,
            CREATE_BY$,
            CREATE_DATE$
        )
        VALUES
        (
            <foreach collection="report1ColumnsList" item="column" separator=","> T1.${column} </foreach>,
            #{ruleType, jdbcType=VARCHAR},
            #{session.userid, jdbcType=VARCHAR}, SYSDATE
        );
        COMMIT;
        END;
    </update>

    <select id="allowlistQueryReport2Count" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="allowlistQueryReport2SQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="allowlistQueryReport2" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="allowlistQueryReport2SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="allowlistQueryReport2SQL">
        SELECT T.*
        FROM ${SCPA.MR3_E2E_SO_TRANSFER_ALLOWLIST} T
        <where>
            <if test="_filters != null and _filters != ''.toString()">
                AND ${_filters}
            </if>
            <if test="isAdmin == false">
                AND created_by$ = #{userid, jdbcType=VARCHAR}
            </if>
        </where>
        ORDER BY CREATE_DATE$
    </sql>

</mapper>
