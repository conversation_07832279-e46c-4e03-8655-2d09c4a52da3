<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.customer.dao.INORDao">
    <sql id="filter">
        <if test="_filters != null and _filters != ''.toString()">
            AND ${_filters}
        </if>
    </sql>

    <sql id="giDateRange">
        <if test="dateRange != null and dateRange.size() == 2">
            AND T.GI_DATE BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
            AND TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
        </if>
    </sql>

    <sql id="fields">
        <choose>
            <when test='"report1".equals(${reportName})'>
                CASE WHEN TO_CHAR(ROUND(DECODE(NVL(COUNT(T.RESCH_COUNTER), 0), 0, 0,
                NVL(COUNT(CASE WHEN NVL(T.RESCH_COUNTER, -999) >= TO_NUMBER(${report1SelectedRatio}) THEN T.RESCH_COUNTER END), 0)
                /    NVL(COUNT(T.RESCH_COUNTER), 0)) * 100, 2), 'FM9999999990.00') = '0.00' THEN '0'
                ELSE TO_CHAR(ROUND(DECODE(NVL(COUNT(T.RESCH_COUNTER), 0), 0, 0,
                NVL(COUNT(CASE WHEN NVL(T.RESCH_COUNTER, -999) >= TO_NUMBER(${report1SelectedRatio}) THEN T.RESCH_COUNTER END), 0)
                   /    NVL(COUNT(T.RESCH_COUNTER), 0)) * 100, 2), 'FM9999999990.00') END || '%'           AS RC_LINES_RATIO,
            </when>
        </choose>
        NVL(SUM(DECODE(T.RESCH_COUNTER, 1, 1, 0)), 0)                                AS RC_EQ_1,
        NVL(SUM(DECODE(T.RESCH_COUNTER, 2, 1, 0)), 0)                                AS RC_EQ_2,
        NVL(SUM(DECODE(T.RESCH_COUNTER, 3, 1, 0)), 0)                                AS RC_EQ_3,
        NVL(SUM(CASE WHEN T.RESCH_COUNTER IN (4, 5) THEN 1 ELSE 0 END), 0)           AS RC_GE_4_LE_5,
        NVL(SUM(CASE WHEN T.RESCH_COUNTER IN (6, 7, 8, 9, 10) THEN 1 ELSE 0 END), 0) AS RC_GE_6_LE_10,
        NVL(SUM(CASE WHEN T.RESCH_COUNTER >= 11 THEN 1 ELSE 0 END), 0)               AS RC_GE_11,
        NVL(SUM(T.RESCH_COUNTER), 0)                                                 AS RC_TOTAL,
        NVL(COUNT(T.RESCH_COUNTER), 0)                                               AS RC_LINES,
        NVL(AVG(CASE WHEN T.RESCH_COUNTER > 0 THEN T.RESCH_COUNTER END), 0)          AS RC_AVERAGE
    </sql>

    <select id="queryNorCascader" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT CATEGORY, NAME
        FROM NOR_FILTER_V
        ORDER BY CATEGORY, DECODE(NAME, 'Others', CAST(UNISTR('\ffff\ffff') AS VARCHAR2(8)), NAME)
    </select>

    <sql id="queryReport1Sql">
        SELECT
        <foreach collection="category" separator="," item="item">
            NVL(T.${item},'Others') "${item}"
        </foreach>,
        <include refid="fields">
            <property name="reportName" value="'report1'"/>
        </include>
        FROM ${SCPA.NOR_V} T
        <where>
            <include refid="filter"/>
            <include refid="giDateRange"/>
        </where>
        GROUP BY
        <foreach collection="category" separator="," item="item">
            NVL(${item},'Others')
        </foreach>
        ORDER BY
        <foreach collection="category" separator="," item="item">
            DECODE(${item}, 'Others', CAST(UNISTR('\ffff\ffff') AS VARCHAR2(8)), ${item})
        </foreach>
    </sql>

    <select id="queryReport1Total" resultType="java.util.LinkedHashMap">
        SELECT
        <include refid="fields">
            <property name="reportName" value="'report1'"/>
        </include>
        FROM ${SCPA.NOR_V} T
        <where>
            <include refid="filter"/>
            <include refid="giDateRange"/>
        </where>
    </select>

    <select id="queryReport1Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1Sql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1Sql"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="norDetailsSQL">
        SELECT T.SALES_ORDER_NUMBER,
               T.SALES_ORDER_ITEM,
               T.GI_DATE,
               T.GI_WEEK,
               T.GI_MONTH,
               T.RESCH_COUNTER,
               T.MATERIAL,
               T.VENDOR_CODE,
               T.CREATED_DATE,
               T.CRD_DATE,
               T.PURCH_ORDER_NUMBER,
               T.PURCH_ORDER_ITEM,
               T.AC2_RANGE,
               T.ACTIVENESS,
               T.AVAILABILITY_CHECK,
               T.BLOCK_STATUS,
               T.BU,
               T.CLUSTER_NAME,
               T.CUSTOMER_CODE,
               T.DELIVERY_PRIORITY,
               T.ENTITY,
               T.FULFILL_OR_NOT_NONBLOCK,
               T.FULFILL_OR_NOT_UNRESTRICT,
               T.GRA_STATUS,
               T.GRA_TYPE,
               T.IMPORT_VENDOR,
               T.LT_RANGE,
               T.MATERIAL_OWNER_NAME,
               T.MATERIAL_OWNER_SESA,
               T.MRP_CONTROLLER,
               T.ORDER_TYPE,
               T.PLANT_CODE,
               T.PLANT_NAME,
               T.PLANT_TYPE,
               T.PRODUCTION_LINE,
               T.PRODUCT_LINE,
               T.RESCH_GROUP,
               T.SHIP_TO,
               T.SHIP_TO_CITY,
               T.SHIP_TO_COUNTRY,
               T.SHIP_TO_REGION,
               T.SHORTAGE_STATUS,
               T.SOLD_TO_REGION,
               T.SOURCE_CATEGORY,
               T.STOCKING_POLICY,
               T.UD_STATUS,
               T.VENDOR_NAME,
               T.VENDOR_PARENT_CODE,
               T.VENDOR_PARENT_NAME,
               T.VENDOR_SHORT_NAME,
               T.VENDOR_FULL_NAME
        FROM ${SCPA.NOR_V} T
        <where>
            <include refid="filter"/>
            <include refid="giDateRange"/>
            <if test="rcFilter != null and rcFilter != ''.toString()">
                and ${rcFilter}
            </if>
            <if test="report1SelectedValue != null and report1SelectedValue.isEmpty() == false">
                <foreach collection="category" separator=" and " item="item" index="index" open=" and ">
                    <choose>
                        <when test="item == 'Others'.toString()">
                            (${item} = 'Others' or ${item} is null)
                        </when>
                        <when test="item != null and item != ''.toString()">
                            ${item} = #{report1SelectedValue[${index}], jdbcType=VARCHAR}
                        </when>
                    </choose>
                </foreach>
            </if>
        </where>
    </sql>

    <select id="queryReport1DetailsCount" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="norDetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1Details" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="norDetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport2" resultType="java.util.LinkedHashMap">
        SELECT
        <include refid="fields">
            <property name="reportName" value="''"/>
        </include>
        FROM ${SCPA.NOR_V} T
        <where>
            <include refid="filter"/>
            <choose>
                <when test='report3SelectedValue != null and report3SelectedValue != ""'>
                    <choose>
                        <when test="report3DateType == 'BY_WEEK'.toString()">
                            AND T.GI_WEEK = #{report3SelectedValue, jdbcType=VARCHAR}
                        </when>
                        <when test="report3DateType == 'BY_MONTH'.toString()">
                            AND T.GI_MONTH = #{report3SelectedValue, jdbcType=VARCHAR}
                        </when>
                        <otherwise>
                            AND T.GI_DATE = TO_DATE(#{report3SelectedValue, jdbcType=VARCHAR}, 'yyyy/mm/dd')
                        </otherwise>
                    </choose>
                </when>
                <otherwise>
                    <include refid="giDateRange"/>
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="queryReport3" resultType="java.util.LinkedHashMap">
        SELECT ${dateColumn} AS CALENDAR_DATE,
        <include refid="fields">
            <property name="reportName" value="''"/>
        </include>
        FROM ${SCPA.NOR_V} T
        <where>
            <include refid="filter"/>
            <include refid="giDateRange"/>
        </where>
        GROUP BY ${dateColumn}
        ORDER BY ${dateColumn}
    </select>

    <select id="queryReport3RatiosColumns" resultType="java.lang.String">
        SELECT DISTINCT TO_CHAR(T.GI_DATE, 'yyyy/mm/dd') AS CALENDAR_DATE
        FROM ${SCPA.NOR_V} T
        <where>
            <include refid="filter"/>
            <include refid="giDateRange"/>
        </where>
        ORDER BY CALENDAR_DATE
    </select>

    <select id="queryReport3Ratios" resultType="java.util.LinkedHashMap">
        SELECT TO_CHAR(T.GI_DATE, 'yyyy/mm/dd') AS CALENDAR_DATE,
        <include refid="fields">
            <property name="reportName" value="''"/>
        </include>
        FROM ${SCPA.NOR_V} T
        <where>
            <include refid="filter"/>
            <include refid="giDateRange"/>
        </where>
        GROUP BY T.GI_DATE
        ORDER BY T.GI_DATE
    </select>

    <select id="queryReport4Count" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <choose>
            <when test='report4Type == "so"'>
                SELECT SALES_ORDER_NUMBER,
                       SALES_ORDER_ITEM,
                       TO_CHAR(GI_DATE, 'YYYY/MM/DD') GI_DATE,
                       GI_WEEK,
                       GI_MONTH,
                       NVL(RESCH_COUNTER, 0) AS RESCH_COUNTER,
                       MATERIAL,
                       VENDOR_CODE,
                       CREATED_DATE,
                       CRD_DATE,
                       PURCH_ORDER_NUMBER,
                       PURCH_ORDER_ITEM,
                       AC2_RANGE,
                       ACTIVENESS,
                       AVAILABILITY_CHECK,
                       BLOCK_STATUS,
                       BU,
                       CLUSTER_NAME,
                       CUSTOMER_CODE,
                       DELIVERY_PRIORITY,
                       ENTITY,
                       FULFILL_OR_NOT_NONBLOCK,
                       FULFILL_OR_NOT_UNRESTRICT,
                       GRA_STATUS,
                       GRA_TYPE,
                       IMPORT_VENDOR,
                       LOCAL_BU,
                       LOCAL_PRODUCT_FAMILY,
                       LOCAL_PRODUCT_LINE,
                       LOCAL_PRODUCT_SUBFAMILY,
                       LT_RANGE,
                       MATERIAL_OWNER_NAME,
                       MATERIAL_OWNER_SESA,
                       MRP_CONTROLLER,
                       ORDER_TYPE,
                       PLANT_CODE,
                       PLANT_NAME,
                       PLANT_TYPE,
                       PRODUCTION_LINE,
                       PRODUCT_LINE,
                       RESCH_GROUP,
                       SHIP_TO,
                       SHIP_TO_CITY,
                       SHIP_TO_COUNTRY,
                       SHIP_TO_REGION,
                       SHORTAGE_STATUS,
                       SOLD_TO_REGION,
                       SOURCE_CATEGORY,
                       STOCKING_POLICY,
                       UD_STATUS,
                       VENDOR_NAME,
                       VENDOR_PARENT_CODE,
                       VENDOR_PARENT_NAME,
                       VENDOR_SHORT_NAME,
                       VENDOR_FULL_NAME
                FROM ${SCPA.NOR_V} T
                    <where>
                        <include refid="filter"/>
                        <include refid="giDateRange"/>
                    </where>
            </when>
            <otherwise>
                SELECT MATERIAL,
                       PLANT_CODE,
                       NVL(SUM(T.RESCH_COUNTER), 0) AS RESCH_COUNTER,
                       MAX(T.VENDOR_CODE) VENDOR_CODE,
                       MAX(T.AC2_RANGE) AC2_RANGE,
                       MAX(T.ACTIVENESS) ACTIVENESS,
                       MAX(T.AVAILABILITY_CHECK) AVAILABILITY_CHECK,
                       MAX(T.BLOCK_STATUS) BLOCK_STATUS,
                       MAX(T.BU) BU,
                       MAX(T.CLUSTER_NAME) CLUSTER_NAME,
                       MAX(T.CUSTOMER_CODE) CUSTOMER_CODE,
                       MAX(T.DELIVERY_PRIORITY) DELIVERY_PRIORITY,
                       MAX(T.ENTITY) ENTITY,
                       MAX(T.FULEFILL_OR_NOT_NONBLOCK)  AS FULFILL_OR_NOT_NONBLOCK,
                       MAX(T.FULEFILL_OR_NOT_UNRESTRICT)    AS FULFILL_OR_NOT_UNRESTRICT,
                       MAX(T.GRA_STATUS) GRA_STATUS,
                       MAX(T.GRA_TYPE) GRA_TYPE,
                       MAX(T.IMPORT_VENDOR) IMPORT_VENDOR,
                       MAX(T.LOCAL_BU) LOCAL_BU,
                       MAX(T.LOCAL_PRODUCT_FAMILY) LOCAL_PRODUCT_FAMILY,
                       MAX(T.LOCAL_PRODUCT_LINE) LOCAL_PRODUCT_LINE,
                       MAX(T.LOCAL_PRODUCT_SUBFAMILY) LOCAL_PRODUCT_SUBFAMILY,
                       MAX(T.LT_RANGE) LT_RANGE,
                       MAX(T.MATERIAL_OWNER_NAME) MATERIAL_OWNER_NAME,
                       MAX(T.MATERIAL_OWNER_SESA) MATERIAL_OWNER_SESA,
                       MAX(T.MRP_CONTROLLER) MRP_CONTROLLER,
                       MAX(T.ORDER_TYPE) ORDER_TYPE,
                       MAX(T.PLANT_NAME) PLANT_NAME,
                       MAX(T.PLANT_TYPE) PLANT_TYPE,
                       MAX(T.PRODUCTION_LINE) PRODUCTION_LINE,
                       MAX(T.PRODUCT_LINE) PRODUCT_LINE,
                       MAX(T.RESCH_GROUP) RESCH_GROUP,
                       MAX(T.SHIP_TO) SHIP_TO,
                       MAX(T.SHIP_TO_CITY) SHIP_TO_CITY,
                       MAX(T.SHIP_TO_COUNTRY) SHIP_TO_COUNTRY,
                       MAX(T.SHIP_TO_REGION) SHIP_TO_REGION,
                       MAX(T.SHORTAGE_STATUS) SHORTAGE_STATUS,
                       MAX(T.SOLD_TO_REGION) SOLD_TO_REGION,
                       MAX(T.SOURCE_CATEGORY) SOURCE_CATEGORY,
                       MAX(T.STOCKING_POLICY) STOCKING_POLICY,
                       MAX(T.UD_STATUS) UD_STATUS,
                       MAX(T.VENDOR_NAME) VENDOR_NAME,
                       MAX(T.VENDOR_PARENT_CODE) VENDOR_PARENT_CODE,
                       MAX(T.VENDOR_PARENT_NAME) VENDOR_PARENT_NAME,
                       MAX(T.VENDOR_SHORT_NAME) VENDOR_SHORT_NAME,
                       MAX(T.VENDOR_FULL_NAME) VENDOR_FULL_NAME
                FROM ${SCPA.NOR_V} T
                <where>
                    <include refid="filter"/>
                    <include refid="giDateRange"/>
                </where>
                GROUP BY T.MATERIAL, T.PLANT_CODE
            </otherwise>
        </choose>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport4" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <choose>
            <when test='report4Type == "so"'>
                SELECT SALES_ORDER_NUMBER,
                       SALES_ORDER_ITEM,
                       TO_CHAR(GI_DATE, 'YYYY/MM/DD') GI_DATE,
                       GI_WEEK,
                       GI_MONTH,
                       NVL(RESCH_COUNTER, 0) AS RESCH_COUNTER,
                       MATERIAL,
                       VENDOR_CODE,
                       CREATED_DATE,
                       CRD_DATE,
                       PURCH_ORDER_NUMBER,
                       PURCH_ORDER_ITEM,
                       AC2_RANGE,
                       ACTIVENESS,
                       AVAILABILITY_CHECK,
                       BLOCK_STATUS,
                       BU,
                       CLUSTER_NAME,
                       CUSTOMER_CODE,
                       DELIVERY_PRIORITY,
                       ENTITY,
                       FULFILL_OR_NOT_NONBLOCK,
                       FULFILL_OR_NOT_UNRESTRICT,
                       GRA_STATUS,
                       GRA_TYPE,
                       IMPORT_VENDOR,
                       LOCAL_BU,
                       LOCAL_PRODUCT_FAMILY,
                       LOCAL_PRODUCT_LINE,
                       LOCAL_PRODUCT_SUBFAMILY,
                       LT_RANGE,
                       MATERIAL_OWNER_NAME,
                       MATERIAL_OWNER_SESA,
                       MRP_CONTROLLER,
                       ORDER_TYPE,
                       PLANT_CODE,
                       PLANT_NAME,
                       PLANT_TYPE,
                       PRODUCTION_LINE,
                       PRODUCT_LINE,
                       RESCH_GROUP,
                       SHIP_TO,
                       SHIP_TO_CITY,
                       SHIP_TO_COUNTRY,
                       SHIP_TO_REGION,
                       SHORTAGE_STATUS,
                       SOLD_TO_REGION,
                       SOURCE_CATEGORY,
                       STOCKING_POLICY,
                       UD_STATUS,
                       VENDOR_NAME,
                       VENDOR_PARENT_CODE,
                       VENDOR_PARENT_NAME,
                       VENDOR_SHORT_NAME,
                       VENDOR_FULL_NAME
                FROM ${SCPA.NOR_V} T
                    <where>
                        <include refid="filter"/>
                        <include refid="giDateRange"/>
                    </where>
                ORDER BY RESCH_COUNTER DESC
            </when>
            <otherwise>
                SELECT MATERIAL,
                       PLANT_CODE,
                       NVL(SUM(T.RESCH_COUNTER), 0) AS RESCH_COUNTER,
                       MAX(VENDOR_CODE) VENDOR_CODE,
                       MAX(T.AC2_RANGE) AC2_RANGE,
                       MAX(T.ACTIVENESS) ACTIVENESS,
                       MAX(T.AVAILABILITY_CHECK) AVAILABILITY_CHECK,
                       MAX(T.BLOCK_STATUS) BLOCK_STATUS,
                       MAX(T.BU) BU,
                       MAX(T.CLUSTER_NAME) CLUSTER_NAME,
                       MAX(T.CUSTOMER_CODE) CUSTOMER_CODE,
                       MAX(T.DELIVERY_PRIORITY) DELIVERY_PRIORITY,
                       MAX(T.ENTITY) ENTITY,
                       MAX(T.FULFILL_OR_NOT_NONBLOCK) FULFILL_OR_NOT_NONBLOCK,
                       MAX(T.FULFILL_OR_NOT_UNRESTRICT) FULFILL_OR_NOT_UNRESTRICT,
                       MAX(T.GRA_STATUS) GRA_STATUS,
                       MAX(T.GRA_TYPE) GRA_TYPE,
                       MAX(T.IMPORT_VENDOR) IMPORT_VENDOR,
                       MAX(T.LOCAL_BU) LOCAL_BU,
                       MAX(T.LOCAL_PRODUCT_FAMILY) LOCAL_PRODUCT_FAMILY,
                       MAX(T.LOCAL_PRODUCT_LINE) LOCAL_PRODUCT_LINE,
                       MAX(T.LOCAL_PRODUCT_SUBFAMILY) LOCAL_PRODUCT_SUBFAMILY,
                       MAX(T.LT_RANGE) LT_RANGE,
                       MAX(T.MATERIAL_OWNER_NAME) MATERIAL_OWNER_NAME,
                       MAX(T.MATERIAL_OWNER_SESA) MATERIAL_OWNER_SESA,
                       MAX(T.MRP_CONTROLLER) MRP_CONTROLLER,
                       MAX(T.ORDER_TYPE) ORDER_TYPE,
                       MAX(T.PLANT_NAME) PLANT_NAME,
                       MAX(T.PLANT_TYPE) PLANT_TYPE,
                       MAX(T.PRODUCTION_LINE) PRODUCTION_LINE,
                       MAX(T.PRODUCT_LINE) PRODUCT_LINE,
                       MAX(T.RESCH_GROUP) RESCH_GROUP,
                       MAX(T.SHIP_TO) SHIP_TO,
                       MAX(T.SHIP_TO_CITY) SHIP_TO_CITY,
                       MAX(T.SHIP_TO_COUNTRY) SHIP_TO_COUNTRY,
                       MAX(T.SHIP_TO_REGION) SHIP_TO_REGION,
                       MAX(T.SHORTAGE_STATUS) SHORTAGE_STATUS,
                       MAX(T.SOLD_TO_REGION) SOLD_TO_REGION,
                       MAX(T.SOURCE_CATEGORY) SOURCE_CATEGORY,
                       MAX(T.STOCKING_POLICY) STOCKING_POLICY,
                       MAX(T.UD_STATUS) UD_STATUS,
                       MAX(T.VENDOR_NAME) VENDOR_NAME,
                       MAX(T.VENDOR_PARENT_CODE) VENDOR_PARENT_CODE,
                       MAX(T.VENDOR_PARENT_NAME) VENDOR_PARENT_NAME,
                       MAX(T.VENDOR_SHORT_NAME) VENDOR_SHORT_NAME,
                       MAX(T.VENDOR_FULL_NAME) VENDOR_FULL_NAME
                FROM ${SCPA.NOR_V} T
                <where>
                    <include refid="filter"/>
                    <include refid="giDateRange"/>
                </where>
                GROUP BY T.MATERIAL, T.PLANT_CODE
                ORDER BY RESCH_COUNTER DESC
            </otherwise>
        </choose>
        <include refid="global.select_footer"/>
    </select>

    <sql id="norReport4DetailsSQL">
        SELECT T.SALES_ORDER_NUMBER,
            T.SALES_ORDER_ITEM,
            T.GI_DATE,
            T.GI_WEEK,
            T.GI_MONTH,
            T.RESCH_COUNTER,
            T.VENDOR_CODE,
            T.CREATED_DATE,
            T.CRD_DATE,
            T.PURCH_ORDER_NUMBER,
            T.PURCH_ORDER_ITEM,
            T.AC2_RANGE,
            T.ACTIVENESS,
            T.AVAILABILITY_CHECK,
            T.BLOCK_STATUS,
            T.BU,
            T.CLUSTER_NAME,
            T.CUSTOMER_CODE,
            T.DELIVERY_PRIORITY,
            T.ENTITY,
            T.FULFILL_OR_NOT_NONBLOCK,
            T.FULFILL_OR_NOT_UNRESTRICT,
            T.GRA_STATUS,
            T.GRA_TYPE,
            T.IMPORT_VENDOR,
            T.LOCAL_BU,
            T.LOCAL_PRODUCT_FAMILY,
            T.LOCAL_PRODUCT_LINE,
            T.LOCAL_PRODUCT_SUBFAMILY,
            T.LT_RANGE,
            T.MATERIAL_OWNER_NAME,
            T.MATERIAL_OWNER_SESA,
            T.MRP_CONTROLLER,
            T.ORDER_TYPE,
            T.PLANT_CODE,
            T.PLANT_NAME,
            T.PLANT_TYPE,
            T.PRODUCTION_LINE,
            T.PRODUCT_LINE,
            T.RESCH_GROUP,
            T.SHIP_TO,
            T.SHIP_TO_CITY,
            T.SHIP_TO_COUNTRY,
            T.SHIP_TO_REGION,
            T.SHORTAGE_STATUS,
            T.SOLD_TO_REGION,
            T.SOURCE_CATEGORY,
            T.STOCKING_POLICY,
            T.UD_STATUS,
            T.VENDOR_NAME,
            T.VENDOR_PARENT_CODE,
            T.VENDOR_PARENT_NAME,
            T.VENDOR_SHORT_NAME,
            T.VENDOR_FULL_NAME
        FROM ${SCPA.NOR_V} T
        <where>
            <include refid="filter"/>
            <include refid="giDateRange"/>
            <if test='report4Type != null and report4Type != ""'>
                <foreach collection="category" separator=" and " item="item" open=" and ">
                    <choose>
                        <when test='item == "so"'>
                            T.SALES_ORDER_NUMBER = #{report4SelectedValue[0],jdbcType=VARCHAR}
                            T.SALES_ORDER_ITEM = #{report4SelectedValue[1],jdbcType=VARCHAR}
                        </when>
                        <otherwise>
                            T.MATERIAL = #{report4SelectedValue[0],jdbcType=VARCHAR}
                        </otherwise>
                    </choose>
                </foreach>
            </if>
        </where>
    </sql>

    <select id="queryReport4DetailsCount" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="norReport4DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport4Details" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="norReport4DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

</mapper>
