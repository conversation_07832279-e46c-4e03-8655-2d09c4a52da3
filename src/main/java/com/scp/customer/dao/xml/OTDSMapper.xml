<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.customer.dao.IOTDSDao">
    <sql id="otds_filter">
        <if test="_filters != null and _filters != ''.toString()">
            and ${_filters}
        </if>
    </sql>

    <sql id="queryOtdsWeeklySQL">
        with OTDS_WEEKLY as (
                      select t.*, nvl(t2.rca_result,t3.rca_result) rca_result
                        from ${SCPA.OTDS_SOURCE_WEEKLY_V} t
                        left join OTDS_RCA_FEEDBACK_V t2 on t.SALES_ORDER_NUMBER = t2.SALES_ORDER_NUMBER and t.SALES_ORDER_ITEM = t2.SALES_ORDER_ITEM and t2.RCA_TYPE = 'W' and t.ontime = '#'
                        left join OTDS_RCA_FEEDBACK_V t3 on t.SALES_ORDER_NUMBER = t3.SALES_ORDER_NUMBER and t.SALES_ORDER_ITEM = t3.SALES_ORDER_ITEM and t3.RCA_TYPE = 'D' and t.ontime = '#'
                       where t.SO04_CRD between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm') - 7 and last_day(to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
                             and t.ontime is not null
                             <include refid="otds_filter"/>
                  ),
             OTDS_YEARLY as (
                      select t.*, nvl(t2.rca_result,t3.rca_result) rca_result
                        from ${SCPA.OTDS_SOURCE_WEEKLY_V} t
                        left join OTDS_RCA_FEEDBACK_V t2 on t.SALES_ORDER_NUMBER = t2.SALES_ORDER_NUMBER and t.SALES_ORDER_ITEM = t2.SALES_ORDER_ITEM and t2.RCA_TYPE = 'W' and t.ontime = '#'
                        left join OTDS_RCA_FEEDBACK_V t3 on t.SALES_ORDER_NUMBER = t3.SALES_ORDER_NUMBER and t.SALES_ORDER_ITEM = t3.SALES_ORDER_ITEM and t3.RCA_TYPE = 'D' and t.ontime = '#'
                       where t.SO04_CRD between TRUNC(to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm'),'YYYY') and last_day(to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
                             and t.ontime is not null
                             <include refid="otds_filter"/>
                  )

        select <foreach collection="field" item="item">
                    tt.${item},
               </foreach>
               <if test="field.contains('ENTITY') or field.contains('MRP_CONTROLLER')">
                    tt.order_field,
               </if>
               <foreach collection="weeks" separator="," item="item">
                    mm."'${item}'_FAIL",
                    mm."'${item}'_ONTIME",
                    mm."'${item}'_RATIO",
                    mm."'${item}'_CONFIRMED"
               </foreach>
               <foreach collection="months" separator="," item="item" open=",">
                    nn."'${item}'_FAIL",
                    nn."'${item}'_ONTIME",
                    nn."'${item}'_RATIO"
               </foreach>
               <foreach collection="years" separator="," item="item" open=",">
                    tt."'${item}'_FAIL",
                    tt."'${item}'_ONTIME",
                    tt."'${item}'_RATIO"
               </foreach>
        from (
                 select *
                 from (select
                        <foreach collection="field" item="item">
                              nvl(t.${item}, 'Others')             ${item},
                        </foreach>
                        <if test="field.contains('ENTITY') or field.contains('MRP_CONTROLLER')">
                              nvl(max(t.cluster_name), 'Others')    order_field,
                        </if>
                              count(decode(t.ontime, '#', 1, null)) fail,
                              count(decode(t.ontime, '+', 1, null)) ontime,
                              to_char(t.SO04_CRD, 'yyyy"YTD"')      years
                       from OTDS_YEARLY t
                       group by
                       <foreach collection="field" item="item">
                             t.${item},
                       </foreach>
                       to_char(t.SO04_CRD, 'yyyy"YTD"')) mm
                     PIVOT (
                         sum(ontime) ontime,
                         sum(fail) fail,
                         sum(decode((ontime + fail), 0, 0, ontime / (ontime + fail))) ratio FOR years
                     IN (
                        <foreach collection="years" separator="," item="item">
                            '${item}'
                        </foreach>
                        )
                     )
             ) tt
                left join
             (
                 select *
                 from (select
                        <foreach collection="field" item="item">
                              nvl(t.${item}, 'Others')             ${item},
                        </foreach>
                        <if test="field.contains('ENTITY') or field.contains('MRP_CONTROLLER')">
                              nvl(max(t.cluster_name), 'Others')    order_field,
                        </if>
                              count(decode(t.ontime, '#', 1, null)) fail,
                              count(decode(t.ontime, '+', 1, null)) ontime,
                              count(t.rca_result) confirmed,
                              to_char(t.SO04_CRD, 'YY') || SO04_CRD_WEEK WEEKS
                       from OTDS_WEEKLY t
                       group by
                       <foreach collection="field" item="item">
                             t.${item},
                       </foreach>
                       to_char(t.SO04_CRD, 'YY') || SO04_CRD_WEEK) mm
                     PIVOT (
                         sum(ontime) ontime,
                         sum(fail) fail,
                         sum(confirmed) confirmed,
                         sum(decode((ontime + fail), 0, 0, ontime / (ontime + fail))) ratio FOR weeks
                     IN (
                        <foreach collection="weeks" separator="," item="item">
                            '${item}'
                        </foreach>
                        )
                     )
             ) mm on
                <foreach collection="field" item="item" separator="and">
                    mm.${item} = tt.${item}
                </foreach>

                 left join
             (
                 select *
                 from (select
                        <foreach collection="field" item="item">
                              nvl(t.${item}, 'Others')             ${item},
                        </foreach>
                        <if test="field.contains('ENTITY') or field.contains('MRP_CONTROLLER')">
                              nvl(max(t.cluster_name), 'Others')    order_field,
                        </if>
                              count(decode(t.ontime, '#', 1, null))                    fail,
                              count(decode(t.ontime, '+', 1, null))                    ontime,
                              to_char(t.SO04_CRD, 'MON-YY', 'NLS_DATE_LANGUAGE=AMERICAN') months
                       from OTDS_WEEKLY t
                       group by
                       <foreach collection="field" item="item">
                             t.${item},
                       </foreach> to_char(t.SO04_CRD, 'MON-YY', 'NLS_DATE_LANGUAGE=AMERICAN')) mm
                     PIVOT (
                         sum(ontime) ontime,
                         sum(fail) fail,
                         sum(decode((ontime + fail), 0, 0, ontime / (ontime + fail))) ratio FOR months
                     IN (
                        <foreach collection="months" separator="," item="item">
                            '${item}'
                        </foreach>
                        )
                     )
             ) nn on
                <foreach collection="field" item="item" separator="and">
                    nn.${item} = tt.${item}
                </foreach>
    </sql>

    <sql id="queryOtdsDailySQL">
          with OTDS_DAILY as (
                   select t.*,t3.rca_result
                   from ${SCPA.OTDS_SOURCE_DAILY_V} t
                   left join OTDS_RCA_FEEDBACK_V t3
                            on t.SALES_ORDER_NUMBER = t3.SALES_ORDER_NUMBER and t.SALES_ORDER_ITEM = t3.SALES_ORDER_ITEM and t3.RCA_TYPE = 'D'
                   where t.OTDS_DAILY_CRD between sysdate - 13 and sysdate + 13
                   <include refid="otds_filter"/>
               )
          select * from (
           select <foreach collection="field" item="item">
                    nn.${item},
                  </foreach>
                  <if test="field.contains('ENTITY') or field.contains('MRP_CONTROLLER')">
                        nn.order_field,
                  </if>
                  nn.type                                           type,
                  <foreach collection="days" separator="," item="item">
                      nn."'${item}'_FAIL",
                      nn."'${item}'_ONTIME",
                      nn."'${item}'_RATIO"
                  </foreach>,
                  <foreach collection="days" separator=" + " item="item" close=" AS TOTAL_LINE">
                      NVL(nn."'${item}'_FAIL",0) +  NVL(nn."'${item}'_ONTIME",0)
                  </foreach>
             from (
                 select *
                   from (
                       select <foreach collection="field" item="item">
                                      nvl(t.${item}, 'Others')             ${item},
                              </foreach>
                              <if test="field.contains('ENTITY') or field.contains('MRP_CONTROLLER')">
                                      nvl(max(t.cluster_name), 'Others')    order_field,
                              </if>
                              'On Time'                                  type,
                              count(decode(nvl(t.ontime,'#'), '#', 1, null)) fail,
                              count(decode(t.ontime, '+', 1, null)) ontime,
                              to_char(t.OTDS_DAILY_CRD, 'mm/dd')    days
                         from OTDS_DAILY t
                        group by
                        <foreach collection="field" item="item">
                             t.${item},
                        </foreach> to_char(t.OTDS_DAILY_CRD, 'mm/dd')) mm
                       PIVOT (
                           sum(ontime) ontime,
                           sum(fail) fail,
                           sum(decode((ontime + fail), 0, 0 , ontime / (ontime + fail))) ratio
                       FOR days IN
                            (
                            <foreach collection="days" separator="," item="item">
                                '${item}'
                            </foreach>
                            )
                       )
             ) nn ) kk
    </sql>

    <select id="queryOtdsWeekly" parameterType="java.util.Map" resultType="java.util.Map">
        <include refid="queryOtdsWeeklySQL"/>
        order by
        <if test="_page.sort != null and _page.sort != ''.toString()">
            ${_page.sort},
        </if>
        "'${defaultOrderColumn}'_FAIL" + "'${defaultOrderColumn}'_ONTIME" DESC
        OFFSET 0 ROWS FETCH NEXT 65535 ROWS ONLY
    </select>

    <select id="queryOtdsDaily" parameterType="java.util.Map" resultType="java.util.Map">
        <include refid="queryOtdsDailySQL"/>
        order by
        <if test="_page.sort != null and _page.sort != ''.toString()">
            ${_page.sort},
        </if>
        TOTAL_LINE DESC
<!--        <choose>-->
<!--            <when test="field.contains('ENTITY') or field.contains('MRP_CONTROLLER')">-->
<!--                decode(kk.order_field, 'LV', '1', 'MV', '2', 'EE', '3', 'H&amp;D', '4', 'Trading', '5', 'Others', 'zzz', kk.order_field)-->
<!--                <foreach collection="field" item="item" separator="," open=",">-->
<!--                    decode(kk.${item}, 'Others', 'zzz', kk.${item})-->
<!--                </foreach>-->
<!--            </when>-->
<!--            <otherwise>-->
<!--                <foreach collection="field" item="item" separator=",">-->
<!--                    decode(kk.${item}, 'Others', 'zzz', kk.${item})-->
<!--                </foreach>-->
<!--            </otherwise>-->
<!--        </choose>-->
        OFFSET 0 ROWS FETCH NEXT 65535 ROWS ONLY
    </select>

    <sql id="queryOtdsDailyDetailsSql">
        SELECT * FROM (
        select  t3.rca_result,
                t3.rca_comments,
                T.*
          from ${SCPA.OTDS_SOURCE_DAILY_V} t
                   left join OTDS_RCA_FEEDBACK_V t3
                            on t.SALES_ORDER_NUMBER = t3.SALES_ORDER_NUMBER and t.SALES_ORDER_ITEM = t3.SALES_ORDER_ITEM and t3.RCA_TYPE = 'D'
         where  to_char(t.otds_daily_crd, 'mm/dd') in
                <foreach collection="selectedDate" open="(" close=")" item="item" separator=",">
                    #{item, jdbcType=VARCHAR}
                </foreach>
        <if test="selectedType == 'On Time'.toString()">
            and t.ontime = '+'
        </if>
        <if test="selectedType == 'Delay'.toString()">
            and t.ontime is null
        </if>
        <include refid="otds_filter"/>) MM
        WHERE 1 = 1
        <foreach collection="field" item="item" index="index">
            <choose>
                <when test="selectedField[index] == 'Others'.toString()">
                    and (${item} is null or ${item} = 'Others')
                </when>
                <otherwise>
                     <if test="selectedField[index] != 'Total'.toString() and selectedField[index] != null">
                        and ${item} = #{selectedField[${index}], jdbcType=VARCHAR}
                     </if>
                </otherwise>
            </choose>
        </foreach>
    </sql>

    <select id="queryOtdsDailyDetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryOtdsDailyDetailsSql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryOtdsDailyDetails" parameterType="java.util.Map" resultType="java.util.Map">
        <include refid="global.select_header"/>
        <include refid="queryOtdsDailyDetailsSql"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryOtdsDailyChart" resultType="java.util.Map">
        select mm."name", sum(mm."value") "value"
        from (
            select  nvl(t3.rca_result,'[Blank]') "name",
                    count(1) "value"
              from ${SCPA.OTDS_SOURCE_DAILY_V} t
                       left join OTDS_RCA_FEEDBACK_V t3
                                on t.SALES_ORDER_NUMBER = t3.SALES_ORDER_NUMBER and t.SALES_ORDER_ITEM = t3.SALES_ORDER_ITEM and t3.RCA_TYPE = 'D'
             where  to_char(t.otds_daily_crd, 'mm/dd') in
                    <foreach collection="selectedDate" open="(" close=")" item="item" separator=",">
                        #{item, jdbcType=VARCHAR}
                    </foreach>
            <foreach collection="field" item="item" index="index">
                <choose>
                    <when test="item == 'RCA_RESULT'.toString()">
                        and nvl(t3.rca_result,'[Blank]') = #{selectedField[${index}], jdbcType=VARCHAR}
                    </when>
                    <when test="selectedField[index] != 'Total'.toString() and selectedField[index] != null">
                        and t.${item} = #{selectedField[${index}], jdbcType=VARCHAR}
                    </when>
                </choose>
            </foreach>
            <include refid="otds_filter"/>
            and t.ontime is null
            group by t3.rca_result) mm
        group by mm."name"
        order by decode(mm."name", '[Blank]', 'A', mm."name")
    </select>

    <select id="queryRacCode" resultType="java.lang.String">
        select rca_code from scpa.OTDS_RCA_CODE
    </select>

    <select id="downloadOtdsDailyDetails" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryOtdsDailyDetailsSql"/>
        <include refid="global.select_footer"/>
    </select>

    <insert id="saveOtdsRCAResult" parameterType="java.util.Map">
        merge into scpa.OTDS_RCA_FEEDBACK t
        using
        (
        <foreach collection="dataList" item="item" separator="union all">
            SELECT #{item.order, jdbcType=VARCHAR} SALES_ORDER_NUMBER,
                   #{item.item, jdbcType=VARCHAR} SALES_ORDER_ITEM,
                   #{item.rca_result, jdbcType=VARCHAR} RCA_RESULT,
                   #{item.rca_comments, jdbcType=VARCHAR} RCA_COMMENTS,
                   #{item.type, jdbcType=VARCHAR} RCA_TYPE
            from dual
        </foreach>
        ) s on (t.SALES_ORDER_NUMBER = s.SALES_ORDER_NUMBER and t.SALES_ORDER_ITEM = s.SALES_ORDER_ITEM)
        when matched then
        update set
            t.rca_result = s.rca_result,
            t.rca_comments = s.rca_comments,
            t.rca_type = s.rca_type,
            t.update_date$ = sysdate,
            t.update_by$ = #{session.userid, jdbcType=VARCHAR}
        when not matched then
        insert (SALES_ORDER_NUMBER, SALES_ORDER_ITEM, RCA_RESULT, rca_comments, CREATE_BY$, CREATE_DATE$, RCA_TYPE)
        values (s.sales_order_number, s.sales_order_item, s.rca_result, s.rca_comments, #{session.userid, jdbcType=VARCHAR}, sysdate, s.rca_type)
    </insert>

    <sql id="queryOtdsWeeklyDetailsSql">
      select nvl(t4.RCA_RESULT, t3.RCA_RESULT)                  "RCA_RESULT",
             T2.RCA_CODE                                        "RCA_TIPS",
             T2.RECOM_RCA_CODE                                  "RECOM_RCA_CODE",
             nvl(t4.rca_comments, t3.rca_comments)              "RCA_COMMENTS",
             T2.DESCRIPTION                                     "RCA_REMARK",
             t.*
        from ${SCPA.OTDS_SOURCE_WEEKLY_V} T
               LEFT JOIN OTDS_RCA_V T2 ON T.SALES_ORDER_NUMBER = T2.SALES_ORDER_NUMBER AND
                                          T.SALES_ORDER_ITEM = T2.SALES_ORDER_ITEM AND
                                          T.WEEK = T2.WEEK
               left join OTDS_RCA_FEEDBACK_V t3
                         on t.SALES_ORDER_NUMBER = t3.SALES_ORDER_NUMBER and t.SALES_ORDER_ITEM = t3.SALES_ORDER_ITEM  and t3.RCA_TYPE = 'D'
               left join OTDS_RCA_FEEDBACK_V t4
                         on t.SALES_ORDER_NUMBER = t4.SALES_ORDER_NUMBER and t.SALES_ORDER_ITEM = t4.SALES_ORDER_ITEM  and t4.RCA_TYPE = 'W'
        where
        <choose>
            <when test="selectedDateType == 'month'.toString()">
                to_char(t.so04_crd, 'MON-YY', 'NLS_DATE_LANGUAGE=AMERICAN') = #{selectedDate, jdbcType=VARCHAR}
                and t.SO04_CRD between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm') and last_day(to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
            </when>
            <when test="selectedDateType == 'week'.toString()">
                to_char(t.SO04_CRD, 'YY') || t.SO04_CRD_WEEK = #{selectedDate, jdbcType=VARCHAR}
                and t.SO04_CRD between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm') - 7 and last_day(to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
            </when>
            <when test="selectedDateType == 'year'.toString()">
                to_char(t.so04_crd, 'yyyy') = #{selectedDate, jdbcType=VARCHAR}
                and t.SO04_CRD between TRUNC(to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm'),'YYYY') and last_day(to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
            </when>
            <when test="selectedDateType == 'all'.toString()">
                t.SO04_CRD between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm') and last_day(to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
            </when>
            <when test="selectedDateType == 'weekly'.toString()">
                t.SO04_CRD between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') and to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
            </when>
            <otherwise> 1 = 1 </otherwise>
        </choose>
        <if test="selectedType == 'On Time'.toString()">
            and t.ontime = '+'
        </if>
        <if test="selectedType == 'Delay'.toString()">
            and t.ontime = '#'
        </if>
        <include refid="otds_filter"/>
        <foreach collection="field" item="item" index="index">
            <choose>
                <when test="selectedField[index] == 'Others'.toString()">
                    and (${item} is null or ${item} = 'Others')
                </when>
                <otherwise>
                    <if test="selectedField[index] != 'Total'.toString() and selectedField[index] != null">
                        and ${item} = #{selectedField[${index}], jdbcType=VARCHAR}
                    </if>
                </otherwise>
            </choose>
        </foreach>
    </sql>

    <select id="queryOtdsWeeklyDetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryOtdsWeeklyDetailsSql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryOtdsWeeklyDetails" parameterType="java.util.Map" resultType="java.util.Map">
        <include refid="global.select_header"/>
        <include refid="queryOtdsWeeklyDetailsSql"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryOtdsWeeklyChart" resultType="java.util.Map">
        select mm."name", sum(mm."value") "value"
        from (
            select nvl(nvl(t4.rca_result, t3.rca_result),'[Blank]') "name",
                   count(1) "value"
            from ${SCPA.OTDS_SOURCE_WEEKLY_V} T
            left join OTDS_RCA_FEEDBACK_V t3 on t.SALES_ORDER_NUMBER = t3.SALES_ORDER_NUMBER and t.SALES_ORDER_ITEM = t3.SALES_ORDER_ITEM  and t3.RCA_TYPE = 'D'
            left join OTDS_RCA_FEEDBACK_V t4 on t.SALES_ORDER_NUMBER = t4.SALES_ORDER_NUMBER and t.SALES_ORDER_ITEM = t4.SALES_ORDER_ITEM  and t4.RCA_TYPE = 'W'
            where
            <choose>
                <when test="selectedDateType == 'month'.toString()">to_char(t.so04_crd, 'MON-YY', 'NLS_DATE_LANGUAGE=AMERICAN') = #{selectedDate, jdbcType=VARCHAR}
                    and t.SO04_CRD between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm') and last_day(to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
                </when>
                    <when test="selectedDateType == 'week'.toString()">
                        to_char(t.SO04_CRD, 'YY') || t.SO04_CRD_WEEK = #{selectedDate, jdbcType=VARCHAR}
                        and t.SO04_CRD between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm') - 7 and last_day(to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
                    </when>
                    <when test="selectedDateType == 'year'.toString()">
                        to_char(t.so04_crd, 'yyyy') = #{selectedDate, jdbcType=VARCHAR}
                        and t.SO04_CRD between TRUNC(to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm'),'YYYY') and last_day(to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
                    </when>
                    <when test="selectedDateType == 'all'.toString()">
                        t.SO04_CRD between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm') and last_day(to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
                    </when>
                    <when test="selectedDateType == 'weekly'.toString()">
                        t.SO04_CRD between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') and to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                    </when>
                    <otherwise> 1 = 1 </otherwise>
                </choose>
                <foreach collection="field" item="item" index="index">
                    <choose>
                        <when test="item == 'RCA_RESULT'.toString()">
                            and nvl(nvl(t4.rca_result, t3.rca_result),'[Blank]') = #{selectedField[${index}], jdbcType=VARCHAR}
                        </when>
                        <when test="selectedField[index] != 'Total'.toString() and selectedField[index] != null">
                            and t.${item} = #{selectedField[${index}], jdbcType=VARCHAR}
                        </when>
                    </choose>
                </foreach>
                <include refid="otds_filter"/>
                and t.ontime = '#'
                group by t3.rca_result,t4.rca_result
        ) mm
        group by mm."name"
        order by decode(mm."name", '[Blank]', 'A', mm."name")
    </select>

    <select id="queryOtdsWeeklyChart2" resultType="java.util.Map">
        select mm."name", sum(mm."value") "value"
        from (
            select nvl(t2.RCA_CODE,'[Blank]') "name",
                   count(1) "value"
            from ${SCPA.OTDS_SOURCE_WEEKLY_V} T
            LEFT JOIN OTDS_RCA_V T2 ON T.SALES_ORDER_NUMBER = T2.SALES_ORDER_NUMBER AND
                                          T.SALES_ORDER_ITEM = T2.SALES_ORDER_ITEM AND
                                          T.WEEK = T2.WEEK
            left join OTDS_RCA_FEEDBACK_V t3 on t.SALES_ORDER_NUMBER = t3.SALES_ORDER_NUMBER and t.SALES_ORDER_ITEM = t3.SALES_ORDER_ITEM  and t3.RCA_TYPE = 'D'
            left join OTDS_RCA_FEEDBACK_V t4 on t.SALES_ORDER_NUMBER = t4.SALES_ORDER_NUMBER and t.SALES_ORDER_ITEM = t4.SALES_ORDER_ITEM  and t4.RCA_TYPE = 'W'
            where
            <choose>
                <when test="selectedDateType == 'month'.toString()">to_char(t.so04_crd, 'MON-YY', 'NLS_DATE_LANGUAGE=AMERICAN') = #{selectedDate, jdbcType=VARCHAR}
                    and t.SO04_CRD between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm') and last_day(to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
                </when>
                    <when test="selectedDateType == 'week'.toString()">
                        to_char(t.SO04_CRD, 'YY') || t.SO04_CRD_WEEK = #{selectedDate, jdbcType=VARCHAR}
                        and t.SO04_CRD between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm') - 7 and last_day(to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
                    </when>
                    <when test="selectedDateType == 'year'.toString()">
                        to_char(t.so04_crd, 'yyyy') = #{selectedDate, jdbcType=VARCHAR}
                        and t.SO04_CRD between TRUNC(to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm'),'YYYY') and last_day(to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
                    </when>
                    <when test="selectedDateType == 'all'.toString()">
                        t.SO04_CRD between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm') and last_day(to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
                    </when>
                    <when test="selectedDateType == 'weekly'.toString()">
                        t.SO04_CRD between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') and to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                    </when>
                    <otherwise> 1 = 1 </otherwise>
                </choose>
                <foreach collection="field" item="item" index="index">
                    <choose>
                        <when test="item == 'RCA_RESULT'.toString()">
                            and nvl(nvl(t4.rca_result, t3.rca_result),'[Blank]') = #{selectedField[${index}], jdbcType=VARCHAR}
                        </when>
                        <when test="selectedField[index] != 'Total'.toString() and selectedField[index] != null">
                            and t.${item} = #{selectedField[${index}], jdbcType=VARCHAR}
                        </when>
                    </choose>
                </foreach>
                <include refid="otds_filter"/>
                and t.ontime = '#'
                group by t2.RCA_CODE
        ) mm
        group by mm."name"
        order by decode(mm."name", '[Blank]', 'A', mm."name")
    </select>

    <select id="downloadOtdsWeeklyDetails" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryOtdsWeeklyDetailsSql"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryOtdsRcaTipsSql">
        select RCA_TIPS_CODE, DESCRIPTION, COMPUTING_LOGIC from OTDS_RCA_TIPS
        union all
        select RCA_CODE, DESCRIPTION,null from OTDS_RCA_CODE
    </sql>

    <select id="queryOtdsRcaTips" resultType="java.util.LinkedHashMap">
        <include refid="queryOtdsRcaTipsSql"/>
    </select>

    <select id="queryOtdsRcaTipsListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryOtdsRcaTipsSql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryOtdsRcaTipsList" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryOtdsRcaTipsSql"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryOtdsWeeklySummary" resultType="java.util.Map">
        with OTDS_WEEKLY as (
                  select t.*,nvl(t2.rca_result,t3.rca_result) rca_result
                    from ${SCPA.OTDS_SOURCE_WEEKLY_V} t
                    left join OTDS_RCA_FEEDBACK_V t2 on t.SALES_ORDER_NUMBER = t2.SALES_ORDER_NUMBER and t.SALES_ORDER_ITEM = t2.SALES_ORDER_ITEM and t2.RCA_TYPE = 'W' and t.ontime = '#'
                    left join OTDS_RCA_FEEDBACK_V t3 on t.SALES_ORDER_NUMBER = t3.SALES_ORDER_NUMBER and t.SALES_ORDER_ITEM = t3.SALES_ORDER_ITEM and t3.RCA_TYPE = 'D' and t.ontime = '#'
                   where t.SO04_CRD between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm') and last_day(to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
                         and t.ontime is not null
                         <include refid="otds_filter"/>
              )

        <include refid="global.select_header"/>
            select <foreach collection="field" item="item">
                        mm.${item},
                   </foreach>
                   <foreach collection="weeks" separator="," item="item">
                        mm."'${item}'_FAIL",
                        mm."'${item}'_CONFIRMED",
                        decode(mm."'${item}'_FAIL", 0 ,0 ,round(mm."'${item}'_CONFIRMED" / mm."'${item}'_FAIL" * 100 ,1)) "'${item}'_RATIO"
                   </foreach>
            from (
                     select *
                     from (select <foreach collection="field" item="item">
                                      nvl(t.${item}, 'Others')             ${item},
                                  </foreach>
                                  count(decode(t.ontime, '#', 1, null)) fail,
                                  count(t.rca_result) confirmed,
                                  to_char(t.SO04_CRD, 'YY') || SO04_CRD_WEEK     weeks
                           from OTDS_WEEKLY t
                           group by
                           <foreach collection="field" item="item">
                                t.${item},
                           </foreach>
                           to_char(t.SO04_CRD, 'YY') || SO04_CRD_WEEK) mm
                         PIVOT (
                             sum(fail) fail,
                             sum(confirmed) confirmed FOR weeks
                         IN (
                            <foreach collection="weeks" separator="," item="item">
                                '${item}'
                            </foreach>
                            )
                         )
                 ) mm
        <include refid="global.select_footer"/>
    </select>

    <select id="queryOtdsWeeklyWeekColumns" resultType="java.lang.String">
        SELECT WEEK_NO
          FROM (
                 SELECT DISTINCT SUBSTR(T.YEAR, 3 ,2) || 'W' || T.WEEK_NO WEEK_NO
                 FROM SY_CALENDAR T
                 WHERE T.NAME = 'National Holidays'
                   AND T.DATE$ BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm') AND LAST_DAY(TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
         ) T
         ORDER BY WEEK_NO DESC
    </select>

    <select id="queryOtdsWeeklyMonthColumns" resultType="java.lang.String">
        SELECT MONTH
          FROM (
                 SELECT DISTINCT TO_CHAR(t.date$, 'MON-YY', 'NLS_DATE_LANGUAGE=AMERICAN') MONTH, T.YEAR || t.MONTH AS MONTH_ORDER
                 FROM SY_CALENDAR T
                 WHERE T.NAME = 'National Holidays'
                   AND T.DATE$ BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm') AND LAST_DAY(TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
             ) T
          ORDER BY MONTH_ORDER DESC
    </select>

    <select id="queryOtdsWeeklyYearColumns" resultType="java.lang.String">
        SELECT T.YEAR
          FROM (
                 SELECT DISTINCT T.YEAR || 'YTD' YEAR
                 FROM SY_CALENDAR T
                 WHERE T.NAME = 'National Holidays'
                   AND T.DATE$ BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm') AND LAST_DAY(TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
         ) T
         ORDER BY T.YEAR DESC
    </select>
</mapper>
