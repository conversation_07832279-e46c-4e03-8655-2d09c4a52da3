<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.customer.dao.IPONORDao">

	<sql id="PONORFilter">
		<if test="_filters != null and _filters != ''.toString()">
			AND ${_filters}
		</if>
		<if test="treePathFilter != null and treePathFilter != ''.toString()">
			and ${treePathFilter}
		</if>
		<include refid="dateRangeFilter"/>
	</sql>

	<sql id="dateRangeFilter">
		AND t.CALENDAR_MONTH between #{dateRange[0], jdbcType=VARCHAR} and #{dateRange[1], jdbcType=VARCHAR}
	</sql>

	<resultMap id="report1ResultMap" type="com.scp.customer.bean.PONORReport1Bean">
		<result property="category1" column="CATEGORY1"/>
		<result property="category2" column="CATEGORY2"/>
		<result property="category3" column="CATEGORY3"/>
		<result property="category4" column="CATEGORY4"/>
		<result property="category5" column="CATEGORY5"/>
		<result property="value" column="value"/>
		<association property="tooltips" javaType="com.scp.customer.bean.PONORReport1Tooltips">
			<result property="COUNT" column="COUNT"/>
			<result property="K" column="K"/>
		</association>
	</resultMap>

	<select id="queryAuthDetails" resultType="java.lang.String">
		select auth_details from SY_MENU_AUTH where lower(user_id) = lower(#{user_id, jdbcType=VARCHAR})
		                                        and menu_code = #{menuCode, jdbcType=VARCHAR}
	</select>

	<sql id="queryBase">
		<choose>
			<when test="testView == true">
				${SCPA.COMPLETED_PO_NOR_TEST_V}
			</when>
			<otherwise>
				${SCPA.COMPLETED_PO_NOR_V}
			</otherwise>
		</choose>
	</sql>


	<select id="queryCascader" resultType="java.util.Map">
		SELECT NAME,
		       CATEGORY
		FROM ${SCPA.PO_NOR_FILTER_V} T
		ORDER BY CATEGORY,NAME
	</select>

	<select id="queryPoNorRcaTips" resultType="java.util.Map">
		SELECT RCA_TIPS_CODE, DESCRIPTION, COMPUTING_LOGIC from ${SCPA.PO_NOR_RCA_TIPS}
		UNION ALL
		select RCA_CODE, DESCRIPTION, null from ${SCPA.PO_NOR_RCA_CODE}
	</select>

	<select id="queryRacCode" resultType="java.lang.String">
		select RCA_CODE from ${SCPA.PO_NOR_RCA_CODE}
	</select>

	<select id="queryReport1" resultMap="report1ResultMap">
		SELECT NVL(${level1}, 'Others') AS CATEGORY1,
		       NVL(${level2}, 'Others') AS CATEGORY2,
		       NVL(${level3}, 'Others') AS CATEGORY3,
			   <if test="level4 != null and level4 != ''.toString()">
			   		NVL(${level4}, 'Others') AS CATEGORY4,
			   </if>
			   <if test="level5 != null and level5 != ''.toString()">
			   		NVL(${level5}, 'Others') AS CATEGORY5,
			   </if>
			   		${valueColumn} AS VALUE,
			   		<choose>
					    <when test="reportCalType == 'NOR'.toString()">
						    COUNT(DECODE(T.NOR_FLAG, 'RC=1~3', 1, 'RC=0', 1, NULL)) / COUNT(1) as K
					    </when>
						<otherwise>
							COUNT(DECODE(T.NORO_FLAG, 'ON TARGET', NULL, 1)) / COUNT(1) as K
						</otherwise>
				    </choose>

			   <if test="tooltipsColumns != null and tooltipsColumns != ''.toString()">
			   		,${tooltipsColumns}
			   </if>
		FROM <include refid="queryBase"/> T
		<where>
			<include refid="PONORFilter"/>
		</where>
		GROUP BY ${level1},
		${level2},
		${level3}
		<if test="level4 != null and level4 != ''.toString()">,${level4}</if>
		<if test="level5 != null and level5 != ''.toString()">,${level5}</if>
	</select>

	<select id="queryReport1Level1Slope" resultType="java.util.Map">
		with PO_NOR_TEMP as (
		select * from <include refid="queryBase"/> T where 1=1
		<include refid="PONORFilter"/>
		)
		SELECT /*+ parallel(8)  */
			TT.KEY,
			REGR_SLOPE(DAY_GAP, TT.LINE) K
		FROM
		    (SELECT T.${level1} AS KEY, TO_NUMBER((T.CALENDAR_WEEK) - (SELECT max(CALENDAR_WEEK) FROM PO_NOR_TEMP)) DAY_GAP,
		<choose>
			<when test="reportCalType == 'NOR'.toString()">
				COUNT(DECODE(T.NOR_FLAG, 'RC=1~3', 1, 'RC=0', 1, NULL)) / COUNT(1) AS LINE
			</when>
			<otherwise>
				COUNT(DECODE(T.NORO_FLAG, 'ON TARGET', NULL, 1)) / COUNT(1) AS LINE
			</otherwise>
		</choose>
		FROM PO_NOR_TEMP T
		WHERE T.${level1} IS NOT NULL
		GROUP BY T.${level1}, T.CALENDAR_WEEK) TT
		GROUP BY TT.KEY
	</select>

	<select id="queryReport1Level2Slope" resultType="java.util.Map">
		with PO_NOR_TEMP as (
		select * from <include refid="queryBase"/> t where 1=1
		<include refid="PONORFilter"/>
		)
		SELECT /*+ parallel(8) */
		TT.KEY,
		REGR_SLOPE(DAY_GAP, TT.LINE) K
		FROM (SELECT T.${level1} || '[#]' || T.${level2} AS KEY,
		TO_NUMBER((T.CALENDAR_WEEK) - (SELECT max(CALENDAR_WEEK) FROM PO_NOR_TEMP)) DAY_GAP,
		<choose>
			<when test="reportCalType == 'NOR'.toString()">
				COUNT(DECODE(T.NOR_FLAG, 'RC=1~3', 1, 'RC=0', 1, NULL)) / COUNT(1) AS LINE
			</when>
			<otherwise>
				(COUNT(DECODE(T.NORO_FLAG, 'ON TARGET', NULL, 1)) / COUNT(1)) AS LINE
			</otherwise>
		</choose>
		FROM PO_NOR_TEMP T
		WHERE T.${level1} IS NOT NULL
		AND T.${level2} IS NOT NULL
		GROUP BY T.${level1}, T.${level2}, T.CALENDAR_WEEK) TT
		GROUP BY TT.KEY
	</select>

	<select id="queryReport2XAxis" resultType="java.lang.String">
		SELECT DISTINCT T.${dateColumn}
		FROM <include refid="queryBase"/> T
		<where>
			<if test="report2NORType != null and report2NORType != ''.toString()">
				<choose>
					<when test="reportCalType == 'NOR'.toString()">
						NOR_FLAG = #{report2NORType}
					</when>
					<otherwise>
						NORO_FLAG = #{report2NORType}
					</otherwise>
				</choose>
			</if>
			<include refid="PONORFilter"/>
		</where>
		ORDER BY T.${dateColumn}
	</select>

	<select id="queryReport2Line" resultType="java.util.HashMap">
		SELECT *
		FROM (SELECT
		          <choose>
			          <when test="reportCalType == 'NOR'.toString()">
				          CASE WHEN COUNT(1) = 0 THEN 0 ELSE
						       (COUNT(DECODE(T.NOR_FLAG, 'RC=4~7', 1, NULL)) +
						       COUNT(DECODE(T.NOR_FLAG, 'RC=8~14', 1, NULL)) +
						       COUNT(DECODE(T.NOR_FLAG, 'RC>14', 1, NULL)))
				          		/ COUNT(1) END AS RATIO,
			          </when>
					  <otherwise>
				        	CASE WHEN
						  		COUNT(DECODE(T.NORO_FLAG, 'Others', NULL, 1)) = 0 THEN 0
				        	ELSE
							  (COUNT(DECODE(T.NORO_FLAG, 'EROC=2~3', 1, NULL)) +
							  COUNT(DECODE(T.NORO_FLAG, 'EROC=4~7', 1, NULL)) +
							  COUNT(DECODE(T.NORO_FLAG, 'EROC=8~14', 1, NULL)) +
							  COUNT(DECODE(T.NORO_FLAG, 'EROC>14', 1, NULL))) / COUNT(DECODE(T.NORO_FLAG, 'Others', NULL, 1)) END AS RATIO,
					  </otherwise>
		          </choose>
				  T.${dateColumn}    AS CALENDAR
		FROM <include refid="queryBase"/> T
		<where>
			<include refid="PONORFilter"/>
		</where>
		GROUP BY T.${dateColumn}) PIVOT (SUM(ROUND(RATIO * 100, 1)) FOR CALENDAR IN
		<if test="xAxis == {}">
			('')
		</if>
		<foreach collection="xAxis" item="item" separator="," open="(" close=")">
			'${item}'
		</foreach>
		)
	</select>

	<select id="queryReport2YAxis" resultType="java.util.HashMap">
		SELECT NVL(T.${report2ViewType},'Others') AS ${report2ViewType}, T.${dateColumn} AS "yAxis", ${valueColumn} VAL
		FROM <include refid="queryBase"/> T
		<where>
			<if test="report2NORType != null and report2NORType != ''.toString()">
				<choose>
					<when test="reportCalType == 'NOR'.toString()">
						NOR_FLAG = #{report2NORType}
					</when>
					<otherwise>
						NORO_FLAG = #{report2NORType}
					</otherwise>
				</choose>
			</if>
			<include refid="PONORFilter"/>
		</where>
		GROUP BY T.${dateColumn}, NVL(T.${report2ViewType},'Others')
		ORDER BY NVL(T.${report2ViewType},'Others'), T.${dateColumn}
	</select>

	<sql id="queryReport2DetailsSQL">
		SELECT * FROM <include refid="queryBase"/> T
		<where>
			<choose>
				<when test="reportCalType == 'NOR'.toString()">
					<if test="report2NORType != null and report2NORType != ''.toString()">
						NOR_FLAG = #{report2NORType}
					</if>
					AND T.NOR_FLAG IN ${report2SelectedType}
				</when>
				<otherwise>
					<if test="report2NORType != null and report2NORType != ''.toString()">
						NORO_FLAG = #{report2NORType}
					</if>
					AND T.NORO_FLAG IN ${report2SelectedType}
				</otherwise>
			</choose>
			<include refid="PONORFilter"/>
			<choose>
				<when test="report2DateType == 'By Month'">
					AND T.CALENDAR_MONTH = #{report2SelectedDate, jdbcType=VARCHAR}
				</when>
				<when test="report2DateType == 'By Week'">
					AND T.CALENDAR_WEEK = #{report2SelectedDate, jdbcType=VARCHAR}
				</when>
				<when test="report2DateType == 'By Quarter'">
					AND T.CALENDAR_QUARTER = #{report2SelectedDate, jdbcType=VARCHAR}
				</when>
				<when test="report2DateType == 'By Year'">
					AND T.CALENDAR_YEAR = #{report2SelectedDate, jdbcType=VARCHAR}
				</when>
			</choose>
		</where>
	</sql>

	<select id="queryReport2DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="queryReport2DetailsSQL"/>
		<include refid="global.count_footer"/>
	</select>

	<select id="queryReport2Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		<include refid="queryReport2DetailsSQL"/>
		<include refid="global.select_footer"/>
	</select>

	<select id="queryReport3YearList" resultType="java.lang.String">
		SELECT DISTINCT YEAR
		FROM SCPA.SY_CALENDAR T
		WHERE T.DATE$ BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'YYYY/MM') AND LAST_DAY(TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'YYYY/MM'))
		  AND T.NAME = 'National Holidays'
		ORDER BY YEAR
	</select>

	<select id="queryReport3MonthList" resultType="java.lang.String">
		SELECT DISTINCT T.YEAR || T.MONTH
		FROM SCPA.SY_CALENDAR T
		WHERE T.DATE$ BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'YYYY/MM') AND LAST_DAY(TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'YYYY/MM'))
		  AND T.NAME = 'National Holidays'
		ORDER BY YEAR || MONTH
	</select>

	<select id="queryReport3WeekList" resultType="java.lang.String">
		SELECT DISTINCT T.YEAR || T.WEEK_NO
		FROM SCPA.SY_CALENDAR T
		WHERE T.DATE$ BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'YYYY/MM') AND LAST_DAY(TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'YYYY/MM'))
		  AND T.NAME = 'National Holidays'
		ORDER BY T.YEAR || T.WEEK_NO
	</select>

	<select id="queryReport3NOR" resultType="java.util.Map">
		with BASE AS (
						SELECT * FROM <include refid="queryBase"/> T
						<where>
							<include refid="PONORFilter"/>
						</where>
		),
		NOR_WEEKLY AS (
						SELECT t.*, T2.RCA_RESULT, T2.RCA_TYPE
						FROM BASE t LEFT JOIN ${SCPA.PO_NOR_RCA_FEEDBACK} T2 ON T.PURCH_ORDER_NUMBER = T2.PURCH_ORDER_NUMBER AND T.PURCH_ORDER_ITEM = T2.PURCH_ORDER_ITEM
						WHERE t.CALENDAR_WEEK in
						<foreach collection="weeks" separator="," item="item" open="(" close=")">
							#{item, jdbcType=VARCHAR}
						</foreach>),
		NOR_MONTHLY AS (
						SELECT t.*
						FROM BASE t
						WHERE t.CALENDAR_MONTH in
						<foreach collection="months" separator="," item="item" open="(" close=")">
							#{item, jdbcType=VARCHAR}
						</foreach>
		),
		NOR_YEARLY AS (
						SELECT *
						FROM BASE T
						WHERE T.CALENDAR_YEAR IN
						<foreach collection="years" separator="," item="item" open="(" close=")">
							#{item, jdbcType=VARCHAR}
						</foreach>
						)
		SELECT <foreach collection="fields" separator="," item="item">
		    		TT."${item}"
	           </foreach>,
		       <foreach collection="years" separator="," item="item">
			       	TT."'${item}'_RC=0"    as "Y${item}_RC=0",
			       	TT."'${item}'_RC=1~3"  as "Y${item}_RC=1~3",
			       	TT."'${item}'_RC=4~7"  as "Y${item}_RC=4~7",
			       	TT."'${item}'_RC=8~14" as "Y${item}_RC=8~14",
			       	TT."'${item}'_RC>14"   as "Y${item}_RC>14",
			       	TT."'${item}'_RATIO"   as "Y${item}_RATIO"
		       </foreach>,
		       <foreach collection="months" separator="," item="item">
			       	MM."'${item}'_RC=0"    as "M${item}_RC=0",
			       	MM."'${item}'_RC=1~3"  as "M${item}_RC=1~3",
			       	MM."'${item}'_RC=4~7"  as "M${item}_RC=4~7",
			       	MM."'${item}'_RC=8~14" as "M${item}_RC=8~14",
			       	MM."'${item}'_RC>14"   as "M${item}_RC>14",
			       	MM."'${item}'_RATIO"   as "M${item}_RATIO"
		       </foreach>,
		       <foreach collection="weeks" separator="," item="item">
			       	WW."'${item}'_RC=0"     as "W${item}_RC=0",
			       	WW."'${item}'_RC=1~3"   as "W${item}_RC=1~3",
			       	WW."'${item}'_RC=4~7"   as "W${item}_RC=4~7",
			       	WW."'${item}'_RC=8~14"  as "W${item}_RC=8~14",
			       	WW."'${item}'_RC>14"    as "W${item}_RC>14",
			       	WW."'${item}'_RATIO"    as "W${item}_RATIO",
			       	WW."'${item}'_RC=4~7_CONFIRMED" as "W${item}_RC=4~7_CONFIRMED",
			       	WW."'${item}'_RC=8~14_CONFIRMED" as "W${item}_RC=8~14_CONFIRMED",
			       	WW."'${item}'_RC>14_CONFIRMED" as "W${item}_RC>14_CONFIRMED"
		       </foreach>
		FROM (
			SELECT *
			FROM (select <foreach collection="fields" separator="," item="item">
							nvl(t."${item}", 'Others')                  ${item}
						 </foreach>,
						 count(decode(t.NOR_FLAG, 'RC=0', 1, null))   "RC=0",
						 count(decode(t.NOR_FLAG, 'RC=1~3', 1, null)) "RC=1~3",
						 count(decode(t.NOR_FLAG, 'RC=4~7', 1, null)) "RC=4~7",
						 count(decode(t.NOR_FLAG, 'RC=8~14', 1, null))"RC=8~14",
						 count(decode(t.NOR_FLAG, 'RC>14', 1, null))  "RC>14",
						 t.CALENDAR_YEAR                                  years
			FROM NOR_YEARLY t
			GROUP BY <foreach collection="fields" separator="," item="item">
					 	 nvl(t."${item}", 'Others')
					 </foreach>,
			    	 t.CALENDAR_YEAR
			) mm PIVOT (
				sum("RC=0") "RC=0", sum("RC=1~3") "RC=1~3", sum("RC=4~7") "RC=4~7", sum("RC=8~14") "RC=8~14",sum("RC>14") "RC>14",
				SUM(DECODE(("RC=0" + "RC=1~3" + "RC=4~7" + "RC=8~14" + "RC>14"), 0, 0, "RC=1~3" / ("RC=0" + "RC=1~3" + "RC=4~7" + "RC=8~14" + "RC>14"))) RATIO
				FOR YEARS IN
					<foreach collection="years" separator="," item="item" open="(" close=")">
						'${item}'
					</foreach>
			)) tt
		LEFT JOIN (
			SELECT *
			FROM (SELECT <foreach collection="fields" separator="," item="item">
							nvl(t."${item}", 'Others')                  ${item}
						 </foreach>,
						 count(decode(t.NOR_FLAG, 'RC=0', 1, null))   "RC=0",
						 count(decode(t.NOR_FLAG, 'RC=1~3', 1, null)) "RC=1~3",
						 count(decode(t.NOR_FLAG, 'RC=4~7', 1, null)) "RC=4~7",
						 count(decode(t.NOR_FLAG, 'RC=8~14', 1, null))"RC=8~14",
						 count(decode(t.NOR_FLAG, 'RC>14', 1, null))  "RC>14",
						 t.CALENDAR_MONTH                                 months
			FROM NOR_MONTHLY t
			group by <foreach collection="fields" separator="," item="item">
							nvl(t."${item}", 'Others')
					 </foreach>,
					 t.CALENDAR_MONTH
		) mm PIVOT (
			sum("RC=0") "RC=0", sum("RC=1~3") "RC=1~3", sum("RC=4~7") "RC=4~7", sum("RC=8~14") "RC=8~14",sum("RC>14") "RC>14",
			SUM(DECODE(("RC=0" + "RC=1~3" + "RC=4~7" + "RC=8~14" + "RC>14"), 0, 0, "RC=1~3" / ("RC=0" + "RC=1~3" + "RC=4~7" + "RC=8~14" + "RC>14"))) RATIO
			FOR months IN
			<foreach collection="months" separator="," item="item" open="(" close=")">
				'${item}'
			</foreach>
		)
		) mm ON <foreach collection="fields" separator=" and " item="item">
					mm."${item}" = tt."${item}"
				</foreach>
		LEFT JOIN (
			SELECT *
			FROM (SELECT <foreach collection="fields" separator="," item="item">
							nvl(t."${item}", 'Others')                  ${item}
						 </foreach>,
						 count(decode(t.NOR_FLAG, 'RC=0', 1, null))   "RC=0",
						 count(decode(t.NOR_FLAG, 'RC=1~3', 1, null)) "RC=1~3",
						 count(decode(t.NOR_FLAG, 'RC=4~7', 1, null)) "RC=4~7",
						 count(decode(t.NOR_FLAG, 'RC=8~14', 1, null))"RC=8~14",
						 count(decode(t.NOR_FLAG, 'RC>14', 1, null))  "RC>14",
						 count(DECODE(T.NOR_FLAG, 'RC=4~7', t.RCA_RESULT, NULL)) "RC=4~7_CONFIRMED",
						 count(DECODE(T.NOR_FLAG, 'RC=8~14', t.RCA_RESULT, NULL)) "RC=8~14_CONFIRMED",
						 count(DECODE(T.NOR_FLAG, 'RC>14', t.RCA_RESULT, NULL)) "RC>14_CONFIRMED",
						 t.CALENDAR_WEEK                                  weeks
				FROM NOR_WEEKLY t
				group by <foreach collection="fields" separator="," item="item">
							nvl(t."${item}", 'Others')
						 </foreach>, CALENDAR_WEEK
			) mm PIVOT (
				sum("RC=0") "RC=0", sum("RC=1~3") "RC=1~3", sum("RC=4~7") "RC=4~7", sum("RC=8~14") "RC=8~14", sum("RC>14") "RC>14",
				sum("RC=4~7_CONFIRMED") "RC=4~7_CONFIRMED",
				sum("RC=8~14_CONFIRMED") "RC=8~14_CONFIRMED",
				sum("RC>14_CONFIRMED") "RC>14_CONFIRMED",
				sum(decode(("RC=0" + "RC=1~3" + "RC=4~7" + "RC=8~14" + "RC>14"), 0, 0, "RC=1~3" / ("RC=0" + "RC=1~3" + "RC=4~7" + "RC=8~14" + "RC>14"))) ratio
				FOR weeks IN
				<foreach collection="weeks" separator="," item="item" open="(" close=")">
					'${item}'
				</foreach>
				)
		) WW ON <foreach collection="fields" separator=" and " item="item">
					WW."${item}" = tt."${item}"
				</foreach>

		<if test="_page.sort != null and _page.sort != ''.toString()">
			order by
			${_page.sort}
		</if>
	</select>

	<select id="queryReport3NORO" resultType="java.util.Map">
		with BASE AS (
			SELECT * FROM <include refid="queryBase"/> T
			<where>
				<include refid="PONORFilter"/>
			</where>
			),
		NOR_WEEKLY AS (
			SELECT t.*, T2.RCA_RESULT, T2.RCA_TYPE
			FROM BASE t
			LEFT JOIN ${SCPA.PO_NORO_RCA_FEEDBACK} T2 ON
				T.PURCH_ORDER_NUMBER = T2.PURCH_ORDER_NUMBER AND T.PURCH_ORDER_ITEM = T2.PURCH_ORDER_ITEM
			WHERE t.CALENDAR_WEEK in
			<foreach collection="weeks" separator="," item="item" open="(" close=")">
				#{item, jdbcType=VARCHAR}
			</foreach>),
		NOR_MONTHLY AS (
			SELECT t.*
			FROM BASE t
			WHERE t.CALENDAR_MONTH in
			<foreach collection="months" separator="," item="item" open="(" close=")">
				#{item, jdbcType=VARCHAR}
			</foreach>
		),
		NOR_YEARLY AS (
			SELECT *
			FROM BASE T
			WHERE T.CALENDAR_YEAR IN
			<foreach collection="years" separator="," item="item" open="(" close=")">
				#{item, jdbcType=VARCHAR}
			</foreach>
		)
		SELECT <foreach collection="fields" separator="," item="item">
			   	TT."${item}"
			   </foreach>,
				<foreach collection="years" separator="," item="item">
					TT."'${item}'_Others"  as "Y${item}_Others",
					TT."'${item}'_ON TARGET"  as "Y${item}_ON TARGET",
					TT."'${item}'_EROC=2~3"   as "Y${item}_EROC=2~3",
					TT."'${item}'_EROC=4~7"   as "Y${item}_EROC=4~7",
					TT."'${item}'_EROC=8~14"  as "Y${item}_EROC=8~14",
					TT."'${item}'_EROC>14"    as "Y${item}_EROC>14",
					TT."'${item}'_RATIO"      as "Y${item}_RATIO"
				</foreach>,
				<foreach collection="months" separator="," item="item">
					MM."'${item}'_Others"    as "M${item}_Others",
					MM."'${item}'_ON TARGET"    as "M${item}_ON TARGET",
					MM."'${item}'_EROC=2~3"     as "M${item}_EROC=2~3",
					MM."'${item}'_EROC=4~7"     as "M${item}_EROC=4~7",
					MM."'${item}'_EROC=8~14"    as "M${item}_EROC=8~14",
					MM."'${item}'_EROC>14"      as "M${item}_EROC>14",
					MM."'${item}'_RATIO"        as "M${item}_RATIO"
				</foreach>,
				<foreach collection="weeks" separator="," item="item">
					WW."'${item}'_Others"     as "W${item}_Others",
					WW."'${item}'_ON TARGET"     as "W${item}_ON TARGET",
					WW."'${item}'_EROC=2~3"      as "W${item}_EROC=2~3",
					WW."'${item}'_EROC=4~7"      as "W${item}_EROC=4~7",
					WW."'${item}'_EROC=8~14"     as "W${item}_EROC=8~14",
					WW."'${item}'_EROC>14"       as "W${item}_EROC>14",
					WW."'${item}'_RATIO"         as "W${item}_RATIO",
					WW."'${item}'_EROC=2~3_CONFIRMED"  as "W${item}_EROC=2~3_CONFIRMED",
					WW."'${item}'_EROC=4~7_CONFIRMED"  as "W${item}_EROC=4~7_CONFIRMED",
					WW."'${item}'_EROC=8~14_CONFIRMED" as "W${item}_EROC=8~14_CONFIRMED",
					WW."'${item}'_EROC>14_CONFIRMED"   as "W${item}_EROC>14_CONFIRMED"
				</foreach>
		FROM (
		SELECT *
		FROM (select <foreach collection="fields" separator="," item="item">
						nvl(t."${item}", 'Others')                  ${item}
					 </foreach>,
					 count(decode(t.NORO_FLAG, 'Others', 1, null))      "Others",
					 count(decode(t.NORO_FLAG, 'ON TARGET', 1, null))   "ON TARGET",
					 count(decode(t.NORO_FLAG, 'EROC=2~3', 1, null))    "EROC=2~3",
					 count(decode(t.NORO_FLAG, 'EROC=4~7', 1, null))    "EROC=4~7",
					 count(decode(t.NORO_FLAG, 'EROC=8~14', 1, null))   "EROC=8~14",
					 count(decode(t.NORO_FLAG, 'EROC>14', 1, null))     "EROC>14",
					 t.CALENDAR_YEAR                                    years
		FROM NOR_YEARLY t
		GROUP BY <foreach collection="fields" separator="," item="item">
			    		nvl(t."${item}", 'Others')
			  	 </foreach>,
		t.CALENDAR_YEAR
		) mm PIVOT (
			sum("Others") "Others",
			sum("ON TARGET") "ON TARGET",
			sum("EROC=2~3") "EROC=2~3",
			sum("EROC=4~7") "EROC=4~7",
			sum("EROC=8~14") "EROC=8~14",
			sum("EROC>14") "EROC>14",
			SUM(DECODE( ("ON TARGET" + "EROC=2~3" + "EROC=4~7" + "EROC=8~14" + "EROC>14"), 0, 0, ("EROC=2~3" + "EROC=4~7" + "EROC=8~14" + "EROC>14")  / ("ON TARGET" + "EROC=2~3" + "EROC=4~7" + "EROC=8~14" + "EROC>14")))  RATIO
		FOR YEARS IN
		<foreach collection="years" separator="," item="item" open="(" close=")">
			'${item}'
		</foreach>
		)) tt
		LEFT JOIN (
		SELECT *
		FROM (SELECT <foreach collection="fields" separator="," item="item">
				     	nvl(t."${item}", 'Others')                  ${item}
				     </foreach>,
				     count(decode(t.NORO_FLAG, 'Others', 1, null))   "Others",
				     count(decode(t.NORO_FLAG, 'ON TARGET', 1, null))   "ON TARGET",
				     count(decode(t.NORO_FLAG, 'EROC=2~3', 1, null))   "EROC=2~3",
				     count(decode(t.NORO_FLAG, 'EROC=4~7', 1, null))   "EROC=4~7",
				     count(decode(t.NORO_FLAG, 'EROC=8~14', 1, null))   "EROC=8~14",
				     count(decode(t.NORO_FLAG, 'EROC>14', 1, null))   "EROC>14",
				     t.CALENDAR_MONTH                                 months
		FROM NOR_MONTHLY t
		group by <foreach collection="fields" separator="," item="item">
					nvl(t."${item}", 'Others')
				</foreach>,
		t.CALENDAR_MONTH
		) mm PIVOT (
				sum("Others") "Others",
				sum("ON TARGET") "ON TARGET",
				sum("EROC=2~3") "EROC=2~3",
				sum("EROC=4~7") "EROC=4~7",
				sum("EROC=8~14") "EROC=8~14",
				sum("EROC>14") "EROC>14",
				SUM(DECODE(("ON TARGET" + "EROC=2~3" + "EROC=4~7" + "EROC=8~14" + "EROC>14"), 0, 0, ("EROC=2~3" + "EROC=4~7" + "EROC=8~14" + "EROC>14")  / ("ON TARGET" + "EROC=2~3" + "EROC=4~7" + "EROC=8~14" + "EROC>14"))) RATIO
				FOR months IN
					<foreach collection="months" separator="," item="item" open="(" close=")">
						'${item}'
					</foreach>
		)
		) mm ON <foreach collection="fields" separator=" and " item="item">
					mm."${item}" = tt."${item}"
				</foreach>
		LEFT JOIN (
		SELECT *
		FROM (SELECT <foreach collection="fields" separator="," item="item">
					 	nvl(t."${item}", 'Others')                  ${item}
					 </foreach>,
					 count(decode(t.NORO_FLAG, 'Others', 1, null))   "Others",
					 count(decode(t.NORO_FLAG, 'ON TARGET', 1, null))   "ON TARGET",
					 count(decode(t.NORO_FLAG, 'EROC=2~3', 1, null))   "EROC=2~3",
					 count(decode(t.NORO_FLAG, 'EROC=4~7', 1, null))   "EROC=4~7",
					 count(decode(t.NORO_FLAG, 'EROC=8~14', 1, null))   "EROC=8~14",
					 count(decode(t.NORO_FLAG, 'EROC>14', 1, null))   "EROC>14",
					 count(DECODE(T.NORO_FLAG, 'EROC=2~3' ,t.RCA_RESULT, NULL)) "EROC=2~3_CONFIRMED",
					 count(DECODE(T.NORO_FLAG, 'EROC=4~7' ,t.RCA_RESULT, NULL)) "EROC=4~7_CONFIRMED",
					 count(DECODE(T.NORO_FLAG, 'EROC=8~14' ,t.RCA_RESULT, NULL)) "EROC=8~14_CONFIRMED",
					 count(DECODE(T.NORO_FLAG, 'EROC>14' ,t.RCA_RESULT, NULL)) "EROC>14_CONFIRMED",
					 t.CALENDAR_WEEK                                  weeks
			  FROM NOR_WEEKLY t
		group by <foreach collection="fields" separator="," item="item">
				 	nvl(t."${item}", 'Others')
				 </foreach>, CALENDAR_WEEK
		) mm PIVOT (
			sum("Others") "Others",
			sum("ON TARGET") "ON TARGET",
			sum("EROC=2~3") "EROC=2~3",
			sum("EROC=4~7") "EROC=4~7",
			sum("EROC=8~14") "EROC=8~14",
			sum("EROC>14") "EROC>14",
			sum("EROC=2~3_CONFIRMED") "EROC=2~3_CONFIRMED",
			sum("EROC=4~7_CONFIRMED") "EROC=4~7_CONFIRMED",
			sum("EROC=8~14_CONFIRMED") "EROC=8~14_CONFIRMED",
			sum("EROC>14_CONFIRMED") "EROC>14_CONFIRMED",
			SUM(DECODE(("ON TARGET" + "EROC=2~3" + "EROC=4~7" + "EROC=8~14" + "EROC>14"), 0 , 0 ,("EROC=2~3" + "EROC=4~7" + "EROC=8~14" + "EROC>14")  / ("ON TARGET" + "EROC=2~3" + "EROC=4~7" + "EROC=8~14" + "EROC>14"))) ratio
			FOR weeks IN
				<foreach collection="weeks" separator="," item="item" open="(" close=")">
					'${item}'
				</foreach>
		)
		) WW ON <foreach collection="fields" separator=" and " item="item">
					WW."${item}" = tt."${item}"
				</foreach>
		<if test="_page.sort != null and _page.sort != ''.toString()">
			order by
			${_page.sort}
		</if>
	</select>

	<sql id="queryReport3DetailsSQL">
		SELECT T4.RCA_RESULT,
			   T4.RCA_COMMENTS,
-- 			   T2.RCA_CODE AS RCA_TIPS,
-- 			   T3.RECOM_RCA_CODE,
-- 			   T3.DESCRIPTION AS RCA_TIPS_DESCRIPTION,
			   T.*
		FROM <include refid="queryBase"/> T
-- 		    LEFT JOIN ${SCPA.PO_NOR_RCA_V} T2 ON T.PURCH_ORDER_NUMBER = T2.PURCH_ORDER_NUMBER AND T.PURCH_ORDER_ITEM = T2.PURCH_ORDER_ITEM
-- 		    LEFT JOIN ${SCPA.PO_NOR_RCA_TIPS} T3 ON T2.RCA_CODE = T3.RCA_TIPS_CODE
		    LEFT JOIN
				<choose>
					<when test="reportCalType == 'NOR'.toString()">
						${SCPA.PO_NOR_RCA_FEEDBACK}
					</when>
					<otherwise>
						${SCPA.PO_NORO_RCA_FEEDBACK}
					</otherwise>
				</choose>
		 		T4 ON T.PURCH_ORDER_NUMBER = T4.PURCH_ORDER_NUMBER AND T.PURCH_ORDER_ITEM = T4.PURCH_ORDER_ITEM
		<where>
			<if test="report3SelectedValues.size() > 1">
				<if test="report3SelectedValues[0] != 'RATIO'.toString()">
					<choose>
						<when test="reportCalType == 'NOR'.toString()">
							AND T.NOR_FLAG = #{report3SelectedValues[0], jdbcType=VARCHAR}
						</when>
						<otherwise>
							AND T.NORO_FLAG = #{report3SelectedValues[0], jdbcType=VARCHAR}
						</otherwise>
					</choose>
				</if>
				<if test="report3DetailsDateColumn != null">
					AND T.${report3DetailsDateColumn} = #{report3DetailsDateValue, jdbcType=VARCHAR}
				</if>
				<if test="report3SelectedValues.size() > 2">
					<foreach collection="fields" item="item" index="index">
						<if test="report3SelectedValues[index + 2] != null and report3SelectedValues[index + 2] != ''.toString() and report3SelectedValues[index + 2] != 'Total'.toString()">
							<choose>
								<when test="report3SelectedValues[index + 2] == 'Others'.toString()">
									AND (T.${item} = 'Others' or T.${item} is null)
								</when>
								<otherwise>
									AND T.${item} = #{report3SelectedValues[${index + 2}], jdbcType=VARCHAR}
								</otherwise>
							</choose>
						</if>
					</foreach>
				</if>
			</if>
			<include refid="PONORFilter"/>
		</where>
	</sql>

	<select id="queryReport3DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="queryReport3DetailsSQL"/>
		<include refid="global.count_footer"/>
	</select>

	<select id="queryReport3Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		<include refid="queryReport3DetailsSQL"/>
		<include refid="global.select_footer"/>
	</select>

	<sql id="queryReport3MoDetailsSQL">
		WITH BASE AS (SELECT T4.RCA_RESULT,
		T4.RCA_COMMENTS,
		-- 			   T2.RCA_CODE AS RCA_TIPS,
		-- 			   T3.RECOM_RCA_CODE,
		-- 			   T3.DESCRIPTION AS RCA_TIPS_DESCRIPTION,
		T.*
		FROM <include refid="queryBase"/> T
		-- 		    LEFT JOIN ${SCPA.PO_NOR_RCA_V} T2 ON T.PURCH_ORDER_NUMBER = T2.PURCH_ORDER_NUMBER AND T.PURCH_ORDER_ITEM = T2.PURCH_ORDER_ITEM
		-- 		    LEFT JOIN ${SCPA.PO_NOR_RCA_TIPS} T3 ON T2.RCA_CODE = T3.RCA_TIPS_CODE
		LEFT JOIN
		<choose>
			<when test="reportCalType == 'NOR'.toString()">
				${SCPA.PO_NOR_RCA_FEEDBACK}
			</when>
			<otherwise>
				${SCPA.PO_NORO_RCA_FEEDBACK}
			</otherwise>
		</choose>
		T4 ON T.PURCH_ORDER_NUMBER = T4.PURCH_ORDER_NUMBER AND T.PURCH_ORDER_ITEM = T4.PURCH_ORDER_ITEM
		<where>
			<if test="report3SelectedValues.size() > 1">
				<if test="report3SelectedValues[0] != 'RATIO'.toString()">
					<choose>
						<when test="reportCalType == 'NOR'.toString()">
							AND T.NOR_FLAG = #{report3SelectedValues[0], jdbcType=VARCHAR}
						</when>
						<otherwise>
							AND T.NORO_FLAG = #{report3SelectedValues[0], jdbcType=VARCHAR}
						</otherwise>
					</choose>
				</if>
				<if test="report3DetailsDateColumn != null">
					AND T.${report3DetailsDateColumn} = #{report3DetailsDateValue, jdbcType=VARCHAR}
				</if>
				<if test="report3SelectedValues.size() > 2">
					<foreach collection="fields" item="item" index="index">
						<if test="report3SelectedValues[index + 2] != null and report3SelectedValues[index + 2] != ''.toString() and report3SelectedValues[index + 2] != 'Total'.toString()">
							<choose>
								<when test="report3SelectedValues[index + 2] == 'Others'.toString()">
									AND (T.${item} = 'Others' or T.${item} is null)
								</when>
								<otherwise>
									AND T.${item} = #{report3SelectedValues[${index + 2}], jdbcType=VARCHAR}
								</otherwise>
							</choose>
						</if>
					</foreach>
				</if>
			</if>
			<include refid="PONORFilter"/>
		</where>
		    )
		SELECT MO.* FROM BASE B
		LEFT JOIN SCPA.PO_NOR_MO_DETAIL_V MO ON B.PURCH_ORDER_NUMBER = MO.PURCH_ORDER_NUMBER AND B.PURCH_ORDER_ITEM
		= MO.PURCH_ORDER_ITEM
	</sql>

	<select id="queryReport3MoDetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="queryReport3MoDetailsSQL"/>
		<include refid="global.count_footer"/>
	</select>

	<select id="queryReport3MoDetails" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		<include refid="queryReport3MoDetailsSQL"/>
		<include refid="global.select_footer"/>
	</select>

	<insert id="saveReport3Details" parameterType="java.util.Map">
		<choose>
			<when test="reportCalType == 'NOR'.toString()">
				merge into ${SCPA.PO_NOR_RCA_FEEDBACK} t
			</when>
			<otherwise>
				merge into ${SCPA.PO_NORO_RCA_FEEDBACK} t
			</otherwise>
		</choose>
		using
		(
		<foreach collection="dataList" item="item" separator="union all">
			SELECT #{item.order, jdbcType=VARCHAR} PURCH_ORDER_NUMBER,
				   #{item.item, jdbcType=VARCHAR} PURCH_ORDER_ITEM,
				   #{item.rca_result, jdbcType=VARCHAR} RCA_RESULT,
				   #{item.rca_type, jdbcType=VARCHAR} RCA_TYPE,
				   #{item.rca_comments, jdbcType=VARCHAR} RCA_COMMENTS
			FROM DUAL
		</foreach>
		) S ON (T.PURCH_ORDER_NUMBER = S.PURCH_ORDER_NUMBER AND T.PURCH_ORDER_ITEM = S.PURCH_ORDER_ITEM)
		when matched then
		update set
			t.rca_result = s.rca_result,
			t.rca_type = s.rca_type,
			t.rca_comments = s.rca_comments,
			t.update_date$ = sysdate,
			t.update_by$ = #{session.userid, jdbcType=VARCHAR}
		WHEN NOT MATCHED THEN
		INSERT (PURCH_ORDER_NUMBER, PURCH_ORDER_ITEM, RCA_RESULT, RCA_TYPE, RCA_COMMENTS, CREATE_BY$, CREATE_DATE$)
		VALUES (S.PURCH_ORDER_NUMBER, S.PURCH_ORDER_ITEM, S.RCA_RESULT, S.RCA_TYPE, S.RCA_COMMENTS, #{session.userid, jdbcType=VARCHAR}, sysdate)
	</insert>
</mapper>
