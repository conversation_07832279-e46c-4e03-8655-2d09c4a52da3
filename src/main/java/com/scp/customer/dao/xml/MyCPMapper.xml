<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.customer.dao.IMyCPDao">

    <sql id="MyCPFilter">
        AND T.${dateColumn} IS NOT NULL
        AND T.${filterDateColumn} between TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'YYYY/MM/DD') AND TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'YYYY/MM/DD')
        <if test="_filters != null and _filters != ''.toString()">
            AND ${_filters}
        </if>
        <if test="treePathFilter != null and treePathFilter != ''.toString()">
            and ${treePathFilter}
        </if>
        <if test="report1Values != null">
            <foreach collection="report1Values" item="item" index="index" separator=" AND " open=" AND ">
                ${report1Categories[index]} = #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
    </sql>

    <resultMap id="columnMap" type="java.lang.String">
        <result column="COLUMN_NAME"/>
    </resultMap>

    <select id="queryFilterDateColumns" resultMap="columnMap" resultType="java.util.List">
        SELECT COLUMN_NAME
        FROM USER_TAB_COLS
        WHERE TABLE_NAME = #{tableName}
          AND DATA_TYPE = 'DATE'
          AND COLUMN_NAME NOT IN ('CREATE_DATE$', 'UPDATE_DATE$')
        ORDER BY COLUMN_NAME
    </select>

    <select id="queryPivotOpts" resultType="java.lang.String">
        SELECT T.COLUMN_NAME
        FROM USER_TAB_COLS T
        WHERE T.TABLE_NAME = #{tableName}
          AND T.DATA_TYPE = 'VARCHAR2'
          AND T.COLUMN_NAME NOT LIKE '%$%'
          AND T.COLUMN_NAME NOT LIKE '%BUSINESS_UNIT%'
        ORDER BY T.COLUMN_NAME
    </select>

    <select id="queryCascader" resultType="java.util.Map">
        SELECT NAME,
               CATEGORY
        FROM ${filterTableName} T
        ORDER BY CATEGORY,NAME
    </select>

    <sql id="convertTableName">
        <choose>
            <when test="pageType == 'MyCP by Response'.toString()">
                ${SCPA.MYCP_RESPONSE_V}
            </when>
            <otherwise>
                ${SCPA.MYCP_COMMITMENT_V}
            </otherwise>
        </choose>
    </sql>

    <select id="queryReport1" resultType="java.util.Map">
        WITH BASE AS (
            SELECT * FROM <include refid="convertTableName"/> T
            <where>
                <include refid="MyCPFilter"/>
            </where>
        )
        SELECT * FROM (SELECT NVL(${report1Category}, 'Others')                            AS "category",
               SUM(DECODE(MYCP_ONTIME_STATUS, 'Fail', 1, 'Ontime', 1, 0))      as "totalLine",
               SUM(DECODE(MYCP_ONTIME_STATUS, 'Fail', 1, 0))                   AS                 "fail",
               SUM(DECODE(MYCP_ONTIME_STATUS, 'Ontime', 1, 0))                 AS                 "onTime",
               ROUND(DECODE((SUM(DECODE(MYCP_ONTIME_STATUS, 'Ontime', 1, 0)) + SUM(DECODE(MYCP_ONTIME_STATUS, 'Fail', 1, 0))),0,0,
                            SUM(DECODE(MYCP_ONTIME_STATUS, 'Ontime', 1, 0)) /
                     (SUM(DECODE(MYCP_ONTIME_STATUS, 'Ontime', 1, 0)) + SUM(DECODE(MYCP_ONTIME_STATUS, 'Fail', 1, 0)))),
                     4) * 100                                               AS "onTimeRatio",
               ROUND(DECODE((SUM(DECODE(MYCP_ONTIME_STATUS, 'Ontime', 1, 0)) +
                             SUM(DECODE(MYCP_ONTIME_STATUS, 'Fail', 1, 0))),0,0,SUM(DECODE(MYCP_ONTIME_STATUS, 'Fail', 1, 0)) /
                     (SUM(DECODE(MYCP_ONTIME_STATUS, 'Ontime', 1, 0)) +
                      SUM(DECODE(MYCP_ONTIME_STATUS, 'Fail', 1, 0)))), 4) * 100 AS "failRatio"
        FROM BASE T
        GROUP BY NVL(${report1Category}, 'Others')
        order by "totalLine" DESC)
        UNION ALL
        SELECT 'Total'                                                      AS "category",
               SUM(DECODE(MYCP_ONTIME_STATUS, 'Fail', 1, 'Ontime', 1, 0))      as "totalLine",
               SUM(DECODE(MYCP_ONTIME_STATUS, 'Fail', 1, 0))                   AS "fail",
               SUM(DECODE(MYCP_ONTIME_STATUS, 'Ontime', 1, 0))                 AS "onTime",
               ROUND(DECODE((SUM(DECODE(MYCP_ONTIME_STATUS, 'Ontime', 1, 0)) + SUM(DECODE(MYCP_ONTIME_STATUS, 'Fail', 1, 0))),0,0,SUM(DECODE(MYCP_ONTIME_STATUS, 'Ontime', 1, 0)) /
                     (SUM(DECODE(MYCP_ONTIME_STATUS, 'Ontime', 1, 0)) + SUM(DECODE(MYCP_ONTIME_STATUS, 'Fail', 1, 0)))),
                     4) * 100                                               AS "onTimeRatio",
               ROUND(DECODE((SUM(DECODE(MYCP_ONTIME_STATUS, 'Ontime', 1, 0)) +
                             SUM(DECODE(MYCP_ONTIME_STATUS, 'Fail', 1, 0))),0,0,SUM(DECODE(MYCP_ONTIME_STATUS, 'Fail', 1, 0)) /
                     (SUM(DECODE(MYCP_ONTIME_STATUS, 'Ontime', 1, 0)) +
                      SUM(DECODE(MYCP_ONTIME_STATUS, 'Fail', 1, 0)))), 4) * 100 AS "failRatio"
        FROM BASE T
    </select>

    <select id="queryReport1Sub" resultType="java.util.Map" useCache="false" flushCache="true">
        SELECT NVL(${category}, 'Others') AS "category",
               SUM(DECODE(MYCP_ONTIME_STATUS, 'Fail', 1, 0))                   AS "fail",
               SUM(DECODE(MYCP_ONTIME_STATUS, 'Ontime', 1, 0))                 AS "onTime",
               ROUND(DECODE((SUM(DECODE(MYCP_ONTIME_STATUS, 'Ontime', 1, 0)) + SUM(DECODE(MYCP_ONTIME_STATUS, 'Fail', 1, 0))),0,0,
        SUM(DECODE(MYCP_ONTIME_STATUS, 'Ontime', 1, 0)) /
                     (SUM(DECODE(MYCP_ONTIME_STATUS, 'Ontime', 1, 0)) + SUM(DECODE(MYCP_ONTIME_STATUS, 'Fail', 1, 0)))),
                     4) * 100                                               AS "onTimeRatio",
               ROUND(DECODE((SUM(DECODE(MYCP_ONTIME_STATUS, 'Ontime', 1, 0)) +
        SUM(DECODE(MYCP_ONTIME_STATUS, 'Fail', 1, 0))),0,0,SUM(DECODE(MYCP_ONTIME_STATUS, 'Fail', 1, 0)) /
                     (SUM(DECODE(MYCP_ONTIME_STATUS, 'Ontime', 1, 0)) +
                      SUM(DECODE(MYCP_ONTIME_STATUS, 'Fail', 1, 0)))), 4) * 100 AS "failRatio"
        FROM <include refid="convertTableName"/> T
        <where>
            "${expandColumn}" = #{expandValue, jdbcType=VARCHAR}
            <foreach collection="parent" item="item" index="index">
                AND "${report1Categories[index]}" = #{item, jdbcType=VARCHAR}
            </foreach>
            <include refid="MyCPFilter"/>
        </where>
        GROUP BY NVL(${category}, 'Others')
    </select>

    <sql id="queryReport1DetailsSQL">
        SELECT *
        FROM <include refid="convertTableName"/> T
        <where>
            <include refid="MyCPFilter"/>
        </where>
    </sql>

    <select id="queryReport1DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport2XAxis" resultType="java.lang.String">
        SELECT DISTINCT T.${dateColumn}
        FROM <include refid="convertTableName"/> T
        <where>
            <include refid="MyCPFilter"/>
        </where>
        ORDER BY T.${dateColumn}
    </select>

    <select id="queryReport2Line" resultType="java.util.HashMap">
        SELECT *
        FROM (SELECT
                ROUND(DECODE((SUM(DECODE(MYCP_ONTIME_STATUS, 'Ontime', 1, 0)) + SUM(DECODE(MYCP_ONTIME_STATUS, 'Fail', 1, 0))),0,0,
        SUM(DECODE(MYCP_ONTIME_STATUS, 'Ontime', 1, 0)) /
                    (SUM(DECODE(MYCP_ONTIME_STATUS, 'Ontime', 1, 0)) + SUM(DECODE(MYCP_ONTIME_STATUS, 'Fail', 1, 0)))),2) AS RATIO,
                T.${dateColumn}
              FROM <include refid="convertTableName"/> T
              <where>
                  <include refid="MyCPFilter"/>
              </where>
              GROUP BY T.${dateColumn}) PIVOT (SUM(ROUND(RATIO * 100, 1)) FOR ${dateColumn} IN
                    <choose>
                        <when test="xAxis == {}">
                            ('')
                        </when>
                        <otherwise>
                            <foreach collection="xAxis" item="item" separator="," open="(" close=")">
                                '${item}'
                            </foreach>
                        </otherwise>
                    </choose>
                  )
    </select>

    <select id="queryReport2YAxis" resultType="java.util.HashMap">
        SELECT NVL(T.${report2ViewType},'Others') AS ${report2ViewType},
               T.${dateColumn} AS "yAxis",
               ${valueColumn} AS VAL
        FROM <include refid="convertTableName"/> T
        <where>
            <include refid="MyCPFilter"/>
        </where>
        GROUP BY T.${dateColumn}, NVL(T.${report2ViewType},'Others')
        ORDER BY T.${dateColumn},NVL(T.${report2ViewType},'Others')
    </select>

    <sql id="queryReport2DetailsSQL">
        SELECT * FROM <include refid="convertTableName"/> T
        <where>
            T.${dateColumn} = #{report2SelectedDate, jdbcType=VARCHAR}
            <include refid="MyCPFilter"/>
        </where>
    </sql>

    <select id="queryReport2DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport2DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport2Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport2DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport3DateColumns" resultType="java.lang.String">
        SELECT DISTINCT ${dateColumn}
        FROM <include refid="convertTableName"/> T
        <where>
            <include refid="MyCPFilter"/>
        </where>
        UNION
        SELECT 'Total' from dual
        ORDER BY ${dateColumn} DESC
    </select>

    <select id="queryReport3" resultType="java.util.Map">
        WITH BASE AS (
            SELECT * FROM <include refid="convertTableName"/> T
            <where>
                T.${dateColumn} in
                <foreach collection="report3DateColumns" separator="," item="item" open="(" close=")">
                    #{item, jdbcType=VARCHAR}
                </foreach>
                <include refid="MyCPFilter"/>
            </where>
        )
        SELECT <foreach collection="fields" separator="," item="item">
                T."${item}"
                </foreach>,
                <foreach collection="report3DateColumns" separator="," item="item">
                    T."'${item}'_ONTIME" as "${item}_ONTIME",
                    T."'${item}'_FAIL" as "${item}_FAIL",
                    T."'${item}'_OTHERS" as "${item}_OTHERS",
                    T."'${item}'_RATIO" as "${item}_RATIO"
                </foreach>
        FROM (
        SELECT *
        FROM (select <foreach collection="fields" separator="," item="item">
                        nvl(t."${item}", 'Others')                  ${item}
                     </foreach>,
                     count(decode(t.MYCP_ONTIME_STATUS, 'Ontime', 1, null)) ontime,
                     count(decode(t.MYCP_ONTIME_STATUS, 'Fail', 1, null)) fail,
                     count(CASE WHEN t.MYCP_ONTIME_STATUS NOT IN ('Ontime', 'Fail') THEN 1 ELSE NULL END) others,
                     ${dateColumn}
                FROM BASE t
                group by <foreach collection="fields" separator="," item="item">
                            nvl(t."${item}", 'Others')
                         </foreach>, T.${dateColumn}

        UNION ALL
                SELECT <foreach collection="fields" separator="," item="item">
                            nvl(t."${item}", 'Others')                  ${item}
                        </foreach>,
                        count(decode(t.MYCP_ONTIME_STATUS, 'Ontime', 1, null)) ontime,
                        count(decode(t.MYCP_ONTIME_STATUS, 'Fail', 1, null)) fail,
                        count(CASE WHEN t.MYCP_ONTIME_STATUS NOT IN ('Ontime', 'Fail') THEN 1 ELSE NULL END) others,
                        'Total'
                FROM BASE T
                GROUP BY <foreach collection="fields" separator="," item="item">
                            nvl(t."${item}", 'Others')
                        </foreach>
        ) mm PIVOT (
            sum(ontime) ontime, sum(fail) fail, sum(others) others,
            sum(decode((ontime + fail), 0, 0, ontime / (ontime + fail))) ratio
        FOR ${dateColumn} IN
        <foreach collection="report3DateColumns" separator="," item="item" open="(" close=")">
            '${item}'
        </foreach>
        )) T
        order by
        <if test="_page.sort != null and _page.sort != ''.toString()">
            ${_page.sort},
        </if>
        <foreach collection="fields" item="item" separator=",">
            <choose>
                <when test="item == 'COMMITMENT_DELAY_DAYS'">
                    DECODE(T.COMMITMENT_DELAY_DAYS,
                    'Others',
                    9999,
                    TO_NUMBER(T.COMMITMENT_DELAY_DAYS))
                </when>
                <otherwise>
                    DECODE(T.${item}, 'Others', 'zzz', T.${item})
                </otherwise>
            </choose>
        </foreach>
    </select>

    <sql id="queryReport3DetailsSQL">
        SELECT T.*, T1.DESCRIPTION
        FROM <include refid="convertTableName"/> T
        LEFT JOIN SCPA.MR3_MYCP_RCA_CODE T1 ON T.RCA_CODE= T1.RCA_CODE
        <where>
            <if test="report3SelectedValues.size() > 1">
                <if test="report3SelectedValues[0] != 'RATIO'.toString()">
                    <choose>
                        <when test="report3SelectedValues[0] == 'OTHERS'.toString()">
                            AND UPPER(T.MYCP_ONTIME_STATUS) NOT IN ('ONTIME', 'FAIL')
                        </when>
                        <otherwise>
                            AND UPPER(T.MYCP_ONTIME_STATUS) = UPPER(#{report3SelectedValues[0], jdbcType=VARCHAR})
                        </otherwise>
                    </choose>
                </if>
                <choose>
                    <when test="report3SelectedValues[1] == ''.toString()">
                    </when>
                    <when test="reportViewType == 'VIEW_BY_DAY'.toString()">
                        AND CALENDAR_DATE = #{report3SelectedValues[1], jdbcType=VARCHAR}
                    </when>
                    <when test="reportViewType == 'VIEW_BY_WEEK'.toString()">
                        AND CALENDAR_WEEK = #{report3SelectedValues[1], jdbcType=VARCHAR}
                    </when>
                    <when test="reportViewType == 'VIEW_BY_MONTH'.toString()">
                        AND CALENDAR_MONTH = #{report3SelectedValues[1], jdbcType=VARCHAR}
                    </when>
                    <when test="reportViewType == 'VIEW_BY_QUARTER'.toString()">
                        AND CALENDAR_QUARTER = #{report3SelectedValues[1], jdbcType=VARCHAR}
                    </when>
                    <when test="reportViewType == 'VIEW_BY_YEAR'.toString()">
                        AND CALENDAR_YEAR = #{report3SelectedValues[1], jdbcType=VARCHAR}
                    </when>
                </choose>
                <if test="report3SelectedValues.size() > 2">
                    <foreach collection="fields" item="item" index="index">
                        <if test="report3SelectedValues[index + 2] != null and report3SelectedValues[index + 2] != ''.toString() and report3SelectedValues[index + 2] != 'Total'.toString()">
                            AND T.${item} = #{report3SelectedValues[${index + 2}], jdbcType=VARCHAR}
                        </if>
                    </foreach>
                </if>
            </if>
            <include refid="MyCPFilter"/>
        </where>
    </sql>

    <select id="queryReport3DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport3DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport3Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport3DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport3RCADesc" resultType="java.util.LinkedHashMap">
        SELECT RCA_CODE, DESCRIPTION from ${SCPA.MR3_MYCP_RCA_CODE}
    </select>
</mapper>
