<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.customer.dao.IE2ELTAnalysisDao">

	<sql id="e2eLTFilter">
		T.CALENDAR_MONTH between #{dateRange[0], jdbcType=VARCHAR} AND #{dateRange[1], jdbcType=VARCHAR}
		<if test="_filters != null and _filters != ''.toString()">
			AND ${_filters}
		</if>
		<if test="treePathFilter != null and treePathFilter != ''.toString()">
			and ${treePathFilter}
		</if>
	</sql>

	<select id="queryCascader" resultType="java.util.Map">
		SELECT * FROM E2E_LT_ANALYSIS_FILTER_V ORDER BY CATEGORY, DECODE(NAME,'Others','zzz',NAME)
	</select>


	<resultMap id="report1ResultMap" type="com.scp.customer.bean.E2ELTAnalysisReport1Bean">
		<result property="category1" column="CATEGORY1"/>
		<result property="category2" column="CATEGORY2"/>
		<result property="category3" column="CATEGORY3"/>
		<result property="category4" column="CATEGORY4"/>
		<result property="category5" column="CATEGORY5"/>
		<result property="value" column="value"/>
		<association property="tooltips" javaType="com.scp.customer.bean.E2ELTAnalysisReport1Tooltips">
			<result property="LINES" column="LINES"/>
			<result property="NUM_OF_RECORDS" column="NUM_OF_RECORDS"/>
			<result property="QUANTITY" column="QUANTITY"/>
			<result property="NET_VALUE" column="NET_VALUE"/>
			<result property="CUSTOMER_REQUESTED_LT" column="CUSTOMER_REQUESTED_LT"/>
			<result property="LOGISTICS_OFFER_LT" column="LOGISTICS_OFFER_LT"/>
			<result property="FIRST_CONFIRMED_LT" column="FIRST_CONFIRMED_LT"/>
			<result property="ACTUAL_LT" column="ACTUAL_LT"/>
			<result property="CONTRACTUAL_LT" column="CONTRACTUAL_LT"/>
		</association>
	</resultMap>

	<select id="queryReport1" resultMap="report1ResultMap">
		WITH BASE AS (SELECT * FROM ${SCPA.MD3_IDS_CLO_SO_DATA_AGG_V})
		SELECT NVL(${level1}, 'Others') AS CATEGORY1,
			   NVL(${level2}, 'Others') AS CATEGORY2,
			   NVL(${level3}, 'Others') AS CATEGORY3,
		       <if test="level4 != null and level4 != ''.toString()">
		       	NVL(${level4}, 'Others') AS CATEGORY4,
		       </if>
		       <if test="level5 != null and level5 != ''.toString()">
			       NVL(${level5}, 'Others') AS CATEGORY5,
		       </if>
		       ${valueColumn} AS VALUE
		       <if test="tooltipsColumns != null and tooltipsColumns != ''.toString()">
		       		,${tooltipsColumns}
		       </if>
		FROM BASE T
		<where>
			<include refid="e2eLTFilter"/>
		</where>
		GROUP BY ${level1},
				 ${level2},
				 ${level3}
		<if test="level4 != null and level4 != ''.toString()">,${level4}</if>
		<if test="level5 != null and level5 != ''.toString()">,${level5}</if>
	</select>

	<select id="queryReport2" resultType="com.scp.customer.bean.E2ELTOverviewReport2Bean">
		SELECT DECODE(${report2MapType}, 'Hong Kong', 'China', 'Taiwan', 'China', ${report2MapType}) AS NAME, GSC_REGION AS REGION, ${report2ResultType} AS VALUE
		FROM ${SCPA.MD3_IDS_CLO_SO_DATA_AGG_V} T
		<where>
			<include refid="e2eLTFilter"/>
		</where>
		GROUP BY DECODE(${report2MapType}, 'Hong Kong', 'China', 'Taiwan', 'China', ${report2MapType}), GSC_REGION
	</select>

	<sql id="queryReport2DetailsSql">
		SELECT *
		FROM ${SCPA.MD3_IDS_CLO_SO_DATA_AGG_V} T
		<where>
			<include refid="e2eLTFilter"/>
			<if test="report2DetailsType != null and report2DetailsType.isEmpty() == false">
				AND DECODE(${report2MapType}, 'Hong Kong', 'China', 'Taiwan', 'China', ${report2MapType}) = #{report2DetailsType, jdbcType=VARCHAR}
			</if>
		</where>
	</sql>


	<select id="queryReport2DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="queryReport2DetailsSql"/>
		<include refid="global.count_footer"/>
	</select>

	<select id="queryReport2Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		<include refid="queryReport2DetailsSql"/>
		<include refid="global.select_footer"/>
	</select>


	<sql id="queryReport3Sql">
        SELECT /*+ parallel */ * FROM (
            SELECT
                <foreach collection="report3Category" separator="," item="item">
                    nvl(${item},'Others') "${item}"
                </foreach>,
                SUM(NUM_OF_RECORDS) 																		AS NUM_OF_RECORDS,
                SUM(SUM_NET_VALUE) 																			AS VALUE,
                ROUND(SUM(CASE WHEN STOCKING_POLICY = 'MTS' THEN 1 ELSE 0 END) / COUNT(1), 4) * 100 || '%' 	AS MTS_RATIO,
                <choose>
                    <when test="calendarType == 'Order Creation Date'.toString()">
                        DECODE(SUM(CUSTOMER_REQUESTED_LT_CNT), 0, 0, SUM(SUM_CUSTOMER_REQUESTED_LT) / SUM(CUSTOMER_REQUESTED_LT_CNT) * DECODE(#{calendarCalcType}, 'Working Days', 5 / 7, 1))       AS "Customer Requested LT",
                        DECODE(SUM(LOGISTICS_OFFER_LT_CNT), 0, 0, SUM(SUM_LOGISTICS_OFFER_LT) / SUM(LOGISTICS_OFFER_LT_CNT) * DECODE(#{calendarCalcType}, 'Working Days', 5 / 7, 1))                AS "Logistics Offer LT",
                        DECODE(SUM(IST_CONFIRMED_LT_CNT), 0, 0, SUM(SUM_1ST_CONFIRMED_LT) / SUM(IST_CONFIRMED_LT_CNT) * DECODE(#{calendarCalcType}, 'Working Days', 5 / 7, 1))                      AS "First Confirmed LT",
                        DECODE(SUM(ACTUAL_LT_CNT), 0, 0, SUM(SUM_ACTUAL_LT) / SUM(ACTUAL_LT_CNT) * DECODE(#{calendarCalcType}, 'Working Days', 5 / 7, 1))                                           AS "Actual LT",
                        DECODE(SUM(CONTRACTUAL_LT_CNT), 0, 0, SUM(SUM_CONTRACTUAL_LT) / SUM(CONTRACTUAL_LT_CNT) * DECODE(#{calendarCalcType}, 'Working Days', 5 / 7, 1))                            AS "Contractual LT"
                    </when>
                    <otherwise>
                        DECODE(SUM(CUSTOMER_REQUESTED_LT_CNT), 0, 0, SUM(SUM_CUSTOMER_REQUESTED_LT_1) / (SUM(CUSTOMER_REQUESTED_LT_CNT)) * DECODE(#{calendarCalcType}, 'Working Days', 5 / 7, 1))      AS "Customer Requested LT",
                        DECODE(SUM(LOGISTICS_OFFER_LT_CNT), 0, 0, SUM(SUM_LOGISTICS_OFFER_LT_1) / (SUM(LOGISTICS_OFFER_LT_CNT)) * DECODE(#{calendarCalcType}, 'Working Days', 5 / 7, 1))               AS "Logistics Offer LT",
                        DECODE(SUM(IST_CONFIRMED_LT_CNT), 0, 0, SUM(SUM_1ST_CONFIRMED_LT_1) / (SUM(IST_CONFIRMED_LT_CNT)) * DECODE(#{calendarCalcType}, 'Working Days', 5 / 7, 1))                     AS "First Confirmed LT",
                        DECODE(SUM(ACTUAL_LT_CNT), 0, 0, SUM(SUM_ACTUAL_LT_1) / (SUM(ACTUAL_LT_CNT)) * DECODE(#{calendarCalcType}, 'Working Days', 5 / 7, 1))                                          AS "Actual LT",
                        DECODE(SUM(CONTRACTUAL_LT_CNT), 0, 0, SUM(SUM_CONTRACTUAL_LT_1) / (SUM(CONTRACTUAL_LT_CNT)) * DECODE(#{calendarCalcType}, 'Working Days', 5 / 7, 1))                           AS "Contractual LT"
                    </otherwise>
                </choose>
            FROM ${SCPA.MD3_IDS_CLO_SO_DATA_AGG_V} T
            <where>
                <include refid="e2eLTFilter"/>
            </where>
            GROUP BY
                <foreach collection="report3Category"  item="item" separator=", ">
                    nvl(${item},'Others')
                </foreach>
            UNION
            SELECT
                'Total',
                <foreach collection="report3Category" item="item" index="index">
                    <if test="index &lt; report3Category.size - 1">
                        NULL,
                    </if>
                </foreach>
                SUM(NUM_OF_RECORDS) 																		AS NUM_OF_RECORDS,
                SUM(SUM_NET_VALUE) 																			AS VALUE,
                ROUND(SUM(CASE WHEN STOCKING_POLICY = 'MTS' THEN 1 ELSE 0 END) / COUNT(1), 4) * 100 || '%' 	AS MTS_RATIO,
                <choose>
                    <when test="calendarType == 'Order Creation Date'.toString()">
                        DECODE(SUM(CUSTOMER_REQUESTED_LT_CNT), 0, 0, SUM(SUM_CUSTOMER_REQUESTED_LT) / SUM(CUSTOMER_REQUESTED_LT_CNT) * DECODE(#{calendarCalcType}, 'Working Days', 5 / 7, 1))       AS "Customer Requested LT",
                        DECODE(SUM(LOGISTICS_OFFER_LT_CNT), 0, 0, SUM(SUM_LOGISTICS_OFFER_LT) / SUM(LOGISTICS_OFFER_LT_CNT) * DECODE(#{calendarCalcType}, 'Working Days', 5 / 7, 1))                AS "Logistics Offer LT",
                        DECODE(SUM(IST_CONFIRMED_LT_CNT), 0, 0, SUM(SUM_1ST_CONFIRMED_LT) / SUM(IST_CONFIRMED_LT_CNT) * DECODE(#{calendarCalcType}, 'Working Days', 5 / 7, 1))                      AS "First Confirmed LT",
                        DECODE(SUM(ACTUAL_LT_CNT), 0, 0, SUM(SUM_ACTUAL_LT) / SUM(ACTUAL_LT_CNT) * DECODE(#{calendarCalcType}, 'Working Days', 5 / 7, 1))                                           AS "Actual LT",
                        DECODE(SUM(CONTRACTUAL_LT_CNT), 0, 0, SUM(SUM_CONTRACTUAL_LT) / SUM(CONTRACTUAL_LT_CNT) * DECODE(#{calendarCalcType}, 'Working Days', 5 / 7, 1))                            AS "Contractual LT"
                    </when>
                    <otherwise>
                        DECODE(SUM(CUSTOMER_REQUESTED_LT_CNT), 0, 0, SUM(SUM_CUSTOMER_REQUESTED_LT_1) / (SUM(CUSTOMER_REQUESTED_LT_CNT)) * DECODE(#{calendarCalcType}, 'Working Days', 5 / 7, 1))      AS "Customer Requested LT",
                        DECODE(SUM(LOGISTICS_OFFER_LT_CNT), 0, 0, SUM(SUM_LOGISTICS_OFFER_LT_1) / (SUM(LOGISTICS_OFFER_LT_CNT)) * DECODE(#{calendarCalcType}, 'Working Days', 5 / 7, 1))               AS "Logistics Offer LT",
                        DECODE(SUM(IST_CONFIRMED_LT_CNT), 0, 0, SUM(SUM_1ST_CONFIRMED_LT_1) / (SUM(IST_CONFIRMED_LT_CNT)) * DECODE(#{calendarCalcType}, 'Working Days', 5 / 7, 1))                     AS "First Confirmed LT",
                        DECODE(SUM(ACTUAL_LT_CNT), 0, 0, SUM(SUM_ACTUAL_LT_1) / (SUM(ACTUAL_LT_CNT)) * DECODE(#{calendarCalcType}, 'Working Days', 5 / 7, 1))                                          AS "Actual LT",
                        DECODE(SUM(CONTRACTUAL_LT_CNT), 0, 0, SUM(SUM_CONTRACTUAL_LT_1) / (SUM(CONTRACTUAL_LT_CNT)) * DECODE(#{calendarCalcType}, 'Working Days', 5 / 7, 1))                           AS "Contractual LT"
                    </otherwise>
                </choose>
            FROM ${SCPA.MD3_IDS_CLO_SO_DATA_AGG_V} T
        )
		ORDER BY
			<foreach collection="report3Category" item="item" separator=", ">
				DECODE(nvl(${item},'Others'), 'Total', 'ZZZZZZZ', nvl(${item},'Others'))
			</foreach>
	</sql>

	<select id="queryReport3Count" parameterType="java.util.Map" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="queryReport3Sql"/>
		<include refid="global.count_footer"/>
	</select>

	<select id="queryReport3" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		<include refid="queryReport3Sql"/>
		<include refid="global.select_footer"/>
	</select>

	<sql id="queryReport3DetailsSql">
		SELECT *
		FROM ${SCPA.MD3_IDS_CLO_SO_DATA_AGG_V} T
		<where>
			<include refid="e2eLTFilter"/>
			<if test="report3SelectedValue != null and report3SelectedValue.isEmpty() == false">
				<foreach collection="report3TotalColumns" separator=" and " item="item" index="index" open=" and ">
					<choose>
						<when test="report3SelectedValue[index] == 'Others'.toString()">
							(${item} = 'Others' OR ${item} IS NULL)
						</when>
						<when test="report3SelectedValue[index] != null and report3SelectedValue[index] != ''.toString()">
							${item} = #{report3SelectedValue[${index}], jdbcType=VARCHAR}
						</when>
					</choose>
				</foreach>
			</if>
		</where>
	</sql>


	<select id="queryReport3DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="queryReport3DetailsSql"/>
		<include refid="global.count_footer"/>
	</select>

	<select id="queryReport3Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		<include refid="queryReport3DetailsSql"/>
		<include refid="global.select_footer"/>
	</select>

	<sql id="e2eLTAnalysisFilter">
		T.CALENDAR_MONTH between #{dateRange[0], jdbcType=VARCHAR} AND #{dateRange[1], jdbcType=VARCHAR}
		<if test="_filters != null and _filters != ''.toString()">
			AND ${_filters}
		</if>
		<if test="treePathFilter != null and treePathFilter != ''.toString()">
			and ${treePathFilter}
		</if>
	</sql>

	<select id="queryAnalysisPivotOpts" resultType="java.lang.String">
		SELECT
		    T.COLUMN_NAME
		FROM USER_TAB_COLS T
		WHERE T.TABLE_NAME = 'MD3_IDS_CLO_SO_DATA_AGG_V'
		  AND T.DATA_TYPE = 'VARCHAR2'
		  AND T.COLUMN_NAME NOT LIKE '%$%'
		ORDER BY T.COLUMN_NAME
	</select>

	<select id="queryAnalysisCascader" resultType="java.util.Map">
		SELECT NAME,
		       CATEGORY
		FROM ${SCPA.E2E_LT_ANALYSIS_FILTER_V} T
		ORDER BY CATEGORY,NAME
	</select>

	<select id="queryAnalysisReport1" resultType="java.util.Map">
		WITH BASE AS (
			SELECT T.*,
					NVL(${report1Category}, 'Others') AS category
			       FROM ${SCPA.MD3_IDS_CLO_SO_DATA_AGG_V} T
			<where>
				<include refid="e2eLTAnalysisFilter"/>
			</where>
		)
		SELECT
		    NVL(category, 'Total')                         				   		AS "category",
			COUNT(1)      														AS "totalLine",
			CASE WHEN NVL(${report1Parameter1}, 0) - NVL(${report1Parameter2}, 0) &lt; 0 THEN NVL(${report1Parameter1}, 0) - NVL(${report1Parameter2}, 0) ELSE 0 END      AS "differenceInDaysMinus",
			CASE WHEN NVL(${report1Parameter1}, 0) - NVL(${report1Parameter2}, 0) > 0 THEN NVL(${report1Parameter1}, 0) - NVL(${report1Parameter2}, 0) ELSE 0 END      AS "differenceInDaysPlus",
			SUM(SUM_NET_VALUE) 				 AS "netValue",
			SUM(NUM_OF_RECORDS)  			 AS "numberOfRecords"
		FROM BASE T
		GROUP BY ROLLUP(category)
		ORDER by DECODE("category", 'Total', 0, 1) DESC, "totalLine" DESC
	</select>

	<select id="queryAnalysisReport1Sub" resultType="java.util.Map" useCache="false" flushCache="true">
		SELECT NVL(${category}, 'Others') AS "category",
			CASE WHEN NVL(${report1Parameter1}, 0) - NVL(${report1Parameter2}, 0) &lt; 0 THEN NVL(${report1Parameter1}, 0) - NVL(${report1Parameter2}, 0) ELSE 0 END	AS "differenceInDaysMinus",
			CASE WHEN NVL(${report1Parameter1}, 0) - NVL(${report1Parameter2}, 0) > 0 THEN NVL(${report1Parameter1}, 0) - NVL(${report1Parameter2}, 0) ELSE 0 END		AS "differenceInDaysPlus",
			SUM(SUM_NET_VALUE)  											AS "netValue",
			SUM(NUM_OF_RECORDS)  											AS "numberOfRecords"
		FROM ${SCPA.MD3_IDS_CLO_SO_DATA_AGG_V} T
		<where>
			<include refid="e2eLTAnalysisFilter"/>
			<foreach collection="parent" item="item" index="index">
				AND "${report1Categories[index]}" = #{item, jdbcType=VARCHAR}
			</foreach>
			AND "${expandColumn}" = #{expandValue, jdbcType=VARCHAR}
		</where>
		GROUP BY NVL(${category}, 'Others')
	</select>

	<sql id="queryAnalysisReport1DetailsSQL">
		SELECT *
		FROM ${SCPA.MD3_IDS_CLO_SO_DATA_AGG_V} T
		<where>
			<include refid="e2eLTAnalysisFilter"/>
		</where>
	</sql>

	<select id="queryAnalysisReport1DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="queryAnalysisReport1DetailsSQL"/>
		<include refid="global.count_footer"/>
	</select>

	<select id="queryAnalysisReport1Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		<include refid="queryAnalysisReport1DetailsSQL"/>
		<include refid="global.select_footer"/>
	</select>


	<resultMap id="analysisReport2ResultMap" type="com.scp.customer.bean.E2ELTAnalysisReport2Bean">
		<result property="CALENDAR_DATE" column="CALENDAR_DATE"/>
		<result property="NAME" column="NAME"/>
		<result property="VALUE" column="VALUE"/>
	</resultMap>

	<select id="queryAnalysisReport2" parameterType="java.util.Map" resultMap="analysisReport2ResultMap">
		SELECT
			${reportViewType} 					AS CALENDAR_DATE,
			NVL(${report2ViewType}, 'Others') 	AS NAME,
			ROUND(${valueColumn}, 2) 			AS VALUE
		FROM ${SCPA.MD3_IDS_CLO_SO_DATA_AGG_V} T
		<where>
			<include refid="e2eLTAnalysisFilter"/>
		</where>
		GROUP BY
			${reportViewType},
			NVL(${report2ViewType}, 'Others')
		ORDER BY NVL(${report2ViewType}, 'Others')
	</select>

	<select id="queryAnalysisReport2Line" resultType="java.util.HashMap">
		WITH FULL_MONTHS AS (
			<foreach collection="xAxisList" item="xAxisItem" separator=" UNION ALL ">
				SELECT #{xAxisItem} AS xAxis FROM dual
			</foreach>
		),
		BASE AS (
			SELECT
				T.${reportViewType},
				<choose>
					<when test="calendarType == 'Order Creation Date'.toString()">
						DECODE(SUM(CUSTOMER_REQUESTED_LT_CNT), 0, 0, SUM(SUM_CUSTOMER_REQUESTED_LT) / SUM(CUSTOMER_REQUESTED_LT_CNT) * DECODE(#{calendarCalcType}, 'Working Days', 5 / 7, 1))        AS CUSTOMER_REQUESTED_LT,
						DECODE(SUM(LOGISTICS_OFFER_LT_CNT), 0, 0, SUM(SUM_LOGISTICS_OFFER_LT) / SUM(LOGISTICS_OFFER_LT_CNT) * DECODE(#{calendarCalcType}, 'Working Days', 5 / 7, 1))                 AS LOGISTICS_OFFER_LT,
						DECODE(SUM(IST_CONFIRMED_LT_CNT), 0, 0, SUM(SUM_1ST_CONFIRMED_LT) / SUM(IST_CONFIRMED_LT_CNT) * DECODE(#{calendarCalcType}, 'Working Days', 5 / 7, 1))                       AS FIRST_CONFIRMED_LT,
						DECODE(SUM(ACTUAL_LT_CNT), 0, 0, SUM(SUM_ACTUAL_LT) / SUM(ACTUAL_LT_CNT) * DECODE(#{calendarCalcType}, 'Working Days', 5 / 7, 1))                                            AS ACTUAL_LT,
						DECODE(SUM(CONTRACTUAL_LT_CNT), 0, 0, SUM(SUM_CONTRACTUAL_LT) / SUM(CONTRACTUAL_LT_CNT) * DECODE(#{calendarCalcType}, 'Working Days', 5 / 7, 1))                             AS CONTRACTUAL_LT
					</when>
					<otherwise>
						DECODE(SUM(CUSTOMER_REQUESTED_LT_CNT), 0, 0, SUM(SUM_CUSTOMER_REQUESTED_LT_1) / SUM(CUSTOMER_REQUESTED_LT_CNT) * DECODE(#{calendarCalcType}, 'Working Days', 5 / 7, 1))        AS CUSTOMER_REQUESTED_LT,
						DECODE(SUM(LOGISTICS_OFFER_LT_CNT), 0, 0, SUM(SUM_LOGISTICS_OFFER_LT_1) / SUM(LOGISTICS_OFFER_LT_CNT) * DECODE(#{calendarCalcType}, 'Working Days', 5 / 7, 1))                 AS LOGISTICS_OFFER_LT,
						DECODE(SUM(IST_CONFIRMED_LT_CNT), 0, 0, SUM(SUM_1ST_CONFIRMED_LT_1) / SUM(IST_CONFIRMED_LT_CNT) * DECODE(#{calendarCalcType}, 'Working Days', 5 / 7, 1))                       AS FIRST_CONFIRMED_LT,
						DECODE(SUM(ACTUAL_LT_CNT), 0, 0, SUM(SUM_ACTUAL_LT_1) / SUM(ACTUAL_LT_CNT) * DECODE(#{calendarCalcType}, 'Working Days', 5 / 7, 1))                                            AS ACTUAL_LT,
						DECODE(SUM(CONTRACTUAL_LT_CNT), 0, 0, SUM(SUM_CONTRACTUAL_LT_1) / SUM(CONTRACTUAL_LT_CNT) * DECODE(#{calendarCalcType}, 'Working Days', 5 / 7, 1))                             AS CONTRACTUAL_LT
					</otherwise>
				</choose>
			FROM ${SCPA.MD3_IDS_CLO_SO_DATA_AGG_V} T
			<where>
				<include refid="e2eLTAnalysisFilter"/>
			</where>
			GROUP BY T.${reportViewType}
		)
		SELECT T1.* FROM FULL_MONTHS T LEFT JOIN BASE T1 ON T.xAxis = T1.${reportViewType}
		ORDER BY T1.${reportViewType}
	</select>

	<sql id="analysisReport2DetailsSQL">
		SELECT * FROM ${SCPA.MD3_IDS_CLO_SO_DATA_AGG_V} T
		<where>
			<include refid="e2eLTAnalysisFilter"/>
			AND ${reportViewType} = #{report2SelectedValue, jdbcType=VARCHAR}
		</where>
	</sql>

	<select id="queryAnalysisReport2DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="analysisReport2DetailsSQL"/>
		<include refid="global.count_footer"/>
	</select>

	<select id="queryAnalysisReport2Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		<include refid="analysisReport2DetailsSQL"/>
		<include refid="global.select_footer"/>
	</select>

	<sql id="analysisReport3SQL">
		WITH E2E_LT_BASE AS (
			SELECT * FROM ${SCPA.MD3_IDS_CLO_SO_DATA_AGG_V} T
			<where>
				<include refid="e2eLTAnalysisFilter"/>
			</where>),
		BASE AS (
			SELECT
				<foreach collection="report3SelectedColumns" item="item">
					NVL(${item}, 'Others')               AS ${item},
				</foreach>
				${reportViewType}      					 AS CALENDAR_DATE,
				ROUND(${valueColumn}, 3)                 AS VALUE
			FROM E2E_LT_BASE t
			GROUP BY
			<foreach collection="report3SelectedColumns" item="item">
				${item},
			</foreach>
			${reportViewType})
		SELECT * FROM
			( SELECT <foreach collection="report3SelectedColumns" item="item">
						NVL(${item}, 'Others')         AS ${item},
					 </foreach>
						NVL(T.CALENDAR_DATE, 'Others') AS CALENDAR_DATE,
						NVL(T.VALUE, 0) AS TOTAL
			FROM BASE T
			) MM
		PIVOT (
					SUM(TOTAL) AS TOTAL
					FOR CALENDAR_DATE
						IN (
						<foreach collection="report3ColumnNames" separator="," item="item">
							'${item}'
						</foreach>)
				)
			ORDER BY
			<foreach collection="report3SelectedColumns" item="item" separator=",">
				DECODE(${item}, 'Others', 'zzz', ${item})
			</foreach>
	</sql>

	<select id="queryAnalysisReport3Columns" resultType="java.lang.String">
		SELECT DISTINCT ${reportViewType} AS RESULT
		FROM ${SCPA.MD3_IDS_CLO_SO_DATA_AGG_V} t
		WHERE CALENDAR_MONTH BETWEEN #{dateRange[0], jdbcType=VARCHAR}
		AND #{dateRange[1], jdbcType=VARCHAR}
		ORDER BY ${reportViewType} DESC
		OFFSET 0 ROWS FETCH NEXT 30 ROWS ONLY
	</select>

	<select id="queryAnalysisReport3" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		<include refid="analysisReport3SQL"/>
		<include refid="global.select_footer"/>
	</select>

	<sql id="analysisReport3DetailsSQL">
		SELECT * FROM
		(SELECT * FROM ${SCPA.MD3_IDS_CLO_SO_DATA_AGG_V} T
		<where>
			<include refid="e2eLTAnalysisFilter"/>
			<foreach collection="report3SelectedColumns" item="item" index="index">
				<if test="report3SelectedValues[index] != null and report3SelectedValues[index] != ''.toString()">
					<if test="report3SelectedValues[index] == 'Others'">
						AND t.${item} IS NULL
					</if>
					<if test="report3SelectedValues[index] != 'Others'">
						AND t.${item} = #{report3SelectedValues[${index}], jdbcType=VARCHAR}
					</if>
				</if>
			</foreach>
			<choose>
				<when test="report3SelectedDate != null and report3SelectedDate != ''.toString()">
					AND ${reportViewType} = #{report3SelectedDate, jdbcType=VARCHAR}
				</when>
				<otherwise>
					AND ${reportViewType} BETWEEN #{dateRange[0], jdbcType=VARCHAR}
					AND #{dateRange[1], jdbcType=VARCHAR}
				</otherwise>
			</choose>
		</where>
		)
	</sql>

	<select id="queryAnalysisReport3DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="analysisReport3DetailsSQL"/>
		<include refid="global.count_footer"/>
	</select>

	<select id="queryAnalysisReport3Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		<include refid="analysisReport3DetailsSQL"/>
		<include refid="global.select_footer"/>
	</select>

</mapper>
