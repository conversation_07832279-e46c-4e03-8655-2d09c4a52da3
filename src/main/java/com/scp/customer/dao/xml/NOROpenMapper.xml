<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.customer.dao.INOROpenDao">
    <sql id="filter">
        <if test="_filters != null and _filters != ''.toString()">
            AND ${_filters}
        </if>
    </sql>

    <sql id="calendarDateRange">
        <if test="dateRange != null and dateRange.size() == 2">
            AND T.CALENDAR_DATE BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
            AND TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
        </if>
    </sql>

    <sql id="fields">
        NVL(SUM(DECODE(T.RESCH_COUNTER, 1, 1, 0)), 0)                                AS RC_EQ_1,
        NVL(SUM(DECODE(T.RESCH_COUNTER, 2, 1, 0)), 0)                                AS RC_EQ_2,
        NVL(SUM(DECODE(T.RESCH_COUNTER, 3, 1, 0)), 0)                                AS RC_EQ_3,
        NVL(SUM(CASE WHEN T.RESCH_COUNTER IN (4, 5) THEN 1 ELSE 0 END), 0)           AS RC_GE_4_LE_5,
        NVL(SUM(CASE WHEN T.RESCH_COUNTER IN (6, 7, 8, 9, 10) THEN 1 ELSE 0 END), 0) AS RC_GE_6_LE_10,
        NVL(SUM(CASE WHEN T.RESCH_COUNTER >= 11 THEN 1 ELSE 0 END), 0)               AS RC_GE_11,
        NVL(SUM(T.RESCH_COUNTER), 0)                                                 AS RC_TOTAL,
        NVL(COUNT(T.RESCH_COUNTER), 0)                                               AS RC_LINES,
        NVL(AVG(CASE WHEN T.RESCH_COUNTER > 0 THEN T.RESCH_COUNTER END), 0)          AS RC_AVERAGE
    </sql>

    <select id="queryNorCascader" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT CATEGORY, NAME
        FROM NOR_FILTER_V
        ORDER BY CATEGORY, DECODE(NAME, 'Others', CAST(UNISTR('\ffff\ffff') AS VARCHAR2(8)), NAME)
    </select>

    <sql id="queryReport1Sql">
        SELECT
        <foreach collection="category" separator="," item="item">
            NVL(T.${item},'Others') "${item}"
        </foreach>,
        <include refid="fields"/>
        FROM ${SCPA.NOR_OPEN_V} T
        <where>
            <include refid="filter"/>
            <include refid="calendarDateRange"/>
        </where>
        GROUP BY
        <foreach collection="category" separator="," item="item">
            NVL(${item},'Others')
        </foreach>
        ORDER BY
        <foreach collection="category" separator="," item="item">
            DECODE(${item}, 'Others', CAST(UNISTR('\ffff\ffff') AS VARCHAR2(8)), ${item})
        </foreach>
    </sql>

    <select id="queryReport1Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1Sql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1Sql"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport1Total" resultType="java.util.LinkedHashMap">
        SELECT
        <include refid="fields"/>
        FROM ${SCPA.NOR_OPEN_V} T
        <where>
            <include refid="filter"/>
            <include refid="calendarDateRange"/>
        </where>
    </select>

    <sql id="norOpenDetailsSQL">
        SELECT *
        FROM ${SCPA.NOR_OPEN_V} T
        <where>
            <include refid="filter"/>
            <include refid="calendarDateRange"/>
            <if test="rcFilter != null and rcFilter != ''.toString()">
                and ${rcFilter}
            </if>
            <if test="report1SelectedValue != null and report1SelectedValue.isEmpty() == false">
                <foreach collection="category" separator=" and " item="item" index="index" open=" and ">
                    <choose>
                        <when test="item == 'Others'.toString()">
                            (${item} = 'Others' or ${item} is null)
                        </when>
                        <when test="item != null and item != ''.toString()">
                            ${item} = #{report1SelectedValue[${index}], jdbcType=VARCHAR}
                        </when>
                    </choose>
                </foreach>
            </if>
        </where>
    </sql>

    <select id="queryReport1DetailsCount" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="norOpenDetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1Details" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="norOpenDetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport2" resultType="java.util.LinkedHashMap">
        SELECT <include refid="fields"/>
        FROM NOR_OPEN_HIST_V T
        <where>
            <include refid="filter"/>
            <choose>
                <when test='report3SelectedValue != null and report3SelectedValue != ""'>
                    <choose>
                        <when test="report3DateType == 'BY_WEEK'.toString()">
                            AND T.CALENDAR_WEEK = #{report3SelectedValue, jdbcType=VARCHAR}
                        </when>
                        <when test="report3DateType == 'BY_MONTH'.toString()">
                            AND T.CALENDAR_MONTH = #{report3SelectedValue, jdbcType=VARCHAR}
                        </when>
                    </choose>
                </when>
                <otherwise>
                    AND T.CALENDAR_WEEK = TO_CHAR(SYSDATE, 'yyyyiw')
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="queryReport3" resultType="java.util.LinkedHashMap">
        SELECT ${dateColumn} CALENDAR_DATE,
               <include refid="fields"/>
        FROM NOR_OPEN_HIST_V T
        <where>
            <include refid="filter"/>
            <include refid="calendarDateRange"/>
        </where>
        GROUP BY ${dateColumn}
        ORDER BY ${dateColumn}
    </select>

    <select id="queryReport3RatiosColumns" resultType="java.lang.String">
        SELECT DISTINCT TO_CHAR(T.CALENDAR_DATE, 'yyyy/mm/dd') AS CALENDAR_DATE
        FROM NOR_OPEN_HIST_V T
        <where>
            <include refid="filter"/>
            <include refid="calendarDateRange"/>
        </where>
        ORDER BY CALENDAR_DATE
    </select>

    <select id="queryReport3Ratios" resultType="java.util.LinkedHashMap">
        SELECT TO_CHAR(T.CALENDAR_DATE, 'yyyy/mm/dd') AS CALENDAR_DATE,
               DECODE(NVL(COUNT(T.RESCH_COUNTER), 0), 0, 0,
               NVL(SUM(CASE WHEN T.RESCH_COUNTER > 0 THEN 1 ELSE 0 END), 0))  AS RC_GT_0,
               DECODE(NVL(COUNT(T.RESCH_COUNTER), 0), 0, 0,
               NVL(SUM(CASE WHEN T.RESCH_COUNTER >= 2 THEN 1 ELSE 0 END), 0)) AS RC_GE_2
        FROM NOR_OPEN_HIST_V T
        <where>
            <include refid="filter"/>
            <include refid="calendarDateRange"/>
        </where>
        GROUP BY T.CALENDAR_DATE
        ORDER BY T.CALENDAR_DATE
    </select>

    <select id="queryReport4Count" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <choose>
            <when test='report4Type == "so"'>
                SELECT SALES_ORDER_NUMBER,
                        SALES_ORDER_ITEM,
                        TO_CHAR(CALENDAR_DATE, 'YYYY/MM/DD') GI_DATE,
                        CALENDAR_WEEK GI_WEEK,
                        CALENDAR_MONTH GI_MONTH,
                        NVL(RESCH_COUNTER, 0) AS RESCH_COUNTER,
                        MATERIAL,
                        VENDOR_CODE,
                        AC2_RANGE,
                        ACTIVENESS,
                        AVAILABILITY_CHECK,
                        BLOCK_STATUS,
                        BU,
                        CLUSTER_NAME,
                        CUSTOMER_CODE,
                        DELIVERY_PRIORITY,
                        ENTITY,
                        FULFILL_OR_NOT_NONBLOCK,
                        FULFILL_OR_NOT_UNRESTRICT,
                        GRA_STATUS,
                        GRA_TYPE,
                        IMPORT_VENDOR,
                        LOCAL_BU,
                        LOCAL_PRODUCT_FAMILY,
                        LOCAL_PRODUCT_LINE,
                        LOCAL_PRODUCT_SUBFAMILY,
                        LT_RANGE,
                        MATERIAL_OWNER_NAME,
                        MATERIAL_OWNER_SESA,
                        MRP_CONTROLLER,
                        ORDER_TYPE,
                        PLANT_CODE,
                        PLANT_NAME,
                        PLANT_TYPE,
                        PRODUCTION_LINE,
                        PRODUCT_LINE,
                        RESCH_GROUP,
                        SHIP_TO,
                        SHIP_TO_CITY,
                        SHIP_TO_COUNTRY,
                        SHIP_TO_REGION,
                        SHORTAGE_STATUS,
                        SOLD_TO_REGION,
                        SOURCE_CATEGORY,
                        STOCKING_POLICY,
                        UD_STATUS,
                        VENDOR_NAME
                FROM ${SCPA.NOR_OPEN_V} T
                    <where>
                        <include refid="filter"/>
                        <include refid="calendarDateRange"/>
                    </where>
            </when>
            <otherwise>
                SELECT MATERIAL,
                       PLANT_CODE,
                       NVL(SUM(T.RESCH_COUNTER), 0) AS RESCH_COUNTER,
                       MAX(VENDOR_CODE) VENDOR_CODE,
                       MAX(AC2_RANGE) AC2_RANGE,
                       MAX(ACTIVENESS) ACTIVENESS,
                       MAX(AVAILABILITY_CHECK) AVAILABILITY_CHECK,
                       MAX(BLOCK_STATUS) BLOCK_STATUS,
                       MAX(BU) BU,
                       MAX(CLUSTER_NAME) CLUSTER_NAME,
                       MAX(CUSTOMER_CODE) CUSTOMER_CODE,
                       MAX(DELIVERY_PRIORITY) DELIVERY_PRIORITY,
                       MAX(ENTITY) ENTITY,
                       MAX(FULFILL_OR_NOT_NONBLOCK) FULFILL_OR_NOT_NONBLOCK,
                       MAX(FULFILL_OR_NOT_UNRESTRICT) FULFILL_OR_NOT_UNRESTRICT,
                       MAX(GRA_STATUS) GRA_STATUS,
                       MAX(GRA_TYPE) GRA_TYPE,
                       MAX(IMPORT_VENDOR) IMPORT_VENDOR,
                       MAX(LOCAL_BU) LOCAL_BU,
                       MAX(LOCAL_PRODUCT_FAMILY) LOCAL_PRODUCT_FAMILY,
                       MAX(LOCAL_PRODUCT_LINE) LOCAL_PRODUCT_LINE,
                       MAX(LOCAL_PRODUCT_SUBFAMILY) LOCAL_PRODUCT_SUBFAMILY,
                       MAX(LT_RANGE) LT_RANGE,
                       MAX(MATERIAL_OWNER_NAME) MATERIAL_OWNER_NAME,
                       MAX(MATERIAL_OWNER_SESA) MATERIAL_OWNER_SESA,
                       MAX(MRP_CONTROLLER) MRP_CONTROLLER,
                       MAX(ORDER_TYPE) ORDER_TYPE,
                       MAX(PLANT_NAME) PLANT_NAME,
                       MAX(PLANT_TYPE) PLANT_TYPE,
                       MAX(PRODUCTION_LINE) PRODUCTION_LINE,
                       MAX(PRODUCT_LINE) PRODUCT_LINE,
                       MAX(RESCH_GROUP) RESCH_GROUP,
                       MAX(SHIP_TO) SHIP_TO,
                       MAX(SHIP_TO_CITY) SHIP_TO_CITY,
                       MAX(SHIP_TO_COUNTRY) SHIP_TO_COUNTRY,
                       MAX(SHIP_TO_REGION) SHIP_TO_REGION,
                       MAX(SHORTAGE_STATUS) SHORTAGE_STATUS,
                       MAX(SOLD_TO_REGION) SOLD_TO_REGION,
                       MAX(SOURCE_CATEGORY) SOURCE_CATEGORY,
                       MAX(STOCKING_POLICY) STOCKING_POLICY,
                       MAX(UD_STATUS) UD_STATUS,
                       MAX(VENDOR_NAME) VENDOR_NAME
                FROM ${SCPA.NOR_OPEN_V} T
                <where>
                    <include refid="filter"/>
                    <include refid="calendarDateRange"/>
                </where>
                GROUP BY T.MATERIAL, T.PLANT_CODE
            </otherwise>
        </choose>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport4" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <choose>
            <when test='report4Type == "so"'>
                SELECT SALES_ORDER_NUMBER,
                       SALES_ORDER_ITEM,
                       TO_CHAR(CALENDAR_DATE, 'YYYY/MM/DD') GI_DATE,
                       CALENDAR_WEEK GI_WEEK,
                       CALENDAR_MONTH GI_MONTH,
                       NVL(RESCH_COUNTER, 0) AS RESCH_COUNTER,
                       MATERIAL,
                       VENDOR_CODE,
                       CREATED_DATE,
                       CRD_DATE,
                       PURCH_ORDER_NUMBER,
                       PURCH_ORDER_ITEM,
                       AC2_RANGE,
                       ACTIVENESS,
                       AVAILABILITY_CHECK,
                       BLOCK_STATUS,
                       BU,
                       CLUSTER_NAME,
                       CUSTOMER_CODE,
                       DELIVERY_PRIORITY,
                       ENTITY,
                       FULFILL_OR_NOT_NONBLOCK,
                       FULFILL_OR_NOT_UNRESTRICT,
                       GRA_STATUS,
                       GRA_TYPE,
                       IMPORT_VENDOR,
                       LOCAL_BU,
                       LOCAL_PRODUCT_FAMILY,
                       LOCAL_PRODUCT_LINE,
                       LOCAL_PRODUCT_SUBFAMILY,
                       LT_RANGE,
                       MATERIAL_OWNER_NAME,
                       MATERIAL_OWNER_SESA,
                       MRP_CONTROLLER,
                       ORDER_TYPE,
                       PLANT_CODE,
                       PLANT_NAME,
                       PLANT_TYPE,
                       PRODUCTION_LINE,
                       PRODUCT_LINE,
                       RESCH_GROUP,
                       SHIP_TO,
                       SHIP_TO_CITY,
                       SHIP_TO_COUNTRY,
                       SHIP_TO_REGION,
                       SHORTAGE_STATUS,
                       SOLD_TO_REGION,
                       SOURCE_CATEGORY,
                       STOCKING_POLICY,
                       UD_STATUS,
                       VENDOR_NAME
                FROM ${SCPA.NOR_OPEN_V} T
                    <where>
                        <include refid="filter"/>
                        <include refid="calendarDateRange"/>
                    </where>
                ORDER BY RESCH_COUNTER DESC
            </when>
            <otherwise>
                SELECT MATERIAL,
                       PLANT_CODE,
                       NVL(SUM(T.RESCH_COUNTER), 0) AS RESCH_COUNTER,
                       MAX(VENDOR_CODE) VENDOR_CODE,
                       MAX(AC2_RANGE) AC2_RANGE,
                       MAX(ACTIVENESS) ACTIVENESS,
                       MAX(AVAILABILITY_CHECK) AVAILABILITY_CHECK,
                       MAX(BLOCK_STATUS) BLOCK_STATUS,
                       MAX(BU) BU,
                       MAX(CLUSTER_NAME) CLUSTER_NAME,
                       MAX(CUSTOMER_CODE) CUSTOMER_CODE,
                       MAX(DELIVERY_PRIORITY) DELIVERY_PRIORITY,
                       MAX(ENTITY) ENTITY,
                       MAX(FULFILL_OR_NOT_NONBLOCK) FULFILL_OR_NOT_NONBLOCK,
                       MAX(FULFILL_OR_NOT_UNRESTRICT) FULFILL_OR_NOT_UNRESTRICT,
                       MAX(GRA_STATUS) GRA_STATUS,
                       MAX(GRA_TYPE) GRA_TYPE,
                       MAX(IMPORT_VENDOR) IMPORT_VENDOR,
                       MAX(LOCAL_BU) LOCAL_BU,
                       MAX(LOCAL_PRODUCT_FAMILY) LOCAL_PRODUCT_FAMILY,
                       MAX(LOCAL_PRODUCT_LINE) LOCAL_PRODUCT_LINE,
                       MAX(LOCAL_PRODUCT_SUBFAMILY) LOCAL_PRODUCT_SUBFAMILY,
                       MAX(LT_RANGE) LT_RANGE,
                       MAX(MATERIAL_OWNER_NAME) MATERIAL_OWNER_NAME,
                       MAX(MATERIAL_OWNER_SESA) MATERIAL_OWNER_SESA,
                       MAX(MRP_CONTROLLER) MRP_CONTROLLER,
                       MAX(ORDER_TYPE) ORDER_TYPE,
                       MAX(PLANT_NAME) PLANT_NAME,
                       MAX(PLANT_TYPE) PLANT_TYPE,
                       MAX(PRODUCTION_LINE) PRODUCTION_LINE,
                       MAX(PRODUCT_LINE) PRODUCT_LINE,
                       MAX(RESCH_GROUP) RESCH_GROUP,
                       MAX(SHIP_TO) SHIP_TO,
                       MAX(SHIP_TO_CITY) SHIP_TO_CITY,
                       MAX(SHIP_TO_COUNTRY) SHIP_TO_COUNTRY,
                       MAX(SHIP_TO_REGION) SHIP_TO_REGION,
                       MAX(SHORTAGE_STATUS) SHORTAGE_STATUS,
                       MAX(SOLD_TO_REGION) SOLD_TO_REGION,
                       MAX(SOURCE_CATEGORY) SOURCE_CATEGORY,
                       MAX(STOCKING_POLICY) STOCKING_POLICY,
                       MAX(UD_STATUS) UD_STATUS,
                       MAX(VENDOR_NAME) VENDOR_NAME
                FROM ${SCPA.NOR_OPEN_V} T
                <where>
                    <include refid="filter"/>
                    <include refid="calendarDateRange"/>
                </where>
                GROUP BY T.MATERIAL, T.PLANT_CODE
                ORDER BY RESCH_COUNTER DESC
            </otherwise>
        </choose>
        <include refid="global.select_footer"/>
    </select>

    <sql id="norOpenReport4DetailsSQL">
        SELECT *
        FROM ${SCPA.NOR_OPEN_V} T
        <where>
            <include refid="filter"/>
            <include refid="calendarDateRange"/>
            <if test='report4Type != null and report4Type != ""'>
                <foreach collection="category" separator=" and " item="item" open=" and ">
                    <choose>
                        <when test='item == "so"'>
                            T.SALES_ORDER_NUMBER = #{report4SelectedValue[0],jdbcType=VARCHAR}
                            T.SALES_ORDER_ITEM = #{report4SelectedValue[1],jdbcType=VARCHAR}
                        </when>
                        <otherwise>
                            T.MATERIAL = #{report4SelectedValue[0],jdbcType=VARCHAR}
                        </otherwise>
                    </choose>
                </foreach>
            </if>
        </where>
    </sql>

    <select id="queryReport4DetailsCount" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="norOpenReport4DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport4Details" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="norOpenReport4DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

</mapper>
