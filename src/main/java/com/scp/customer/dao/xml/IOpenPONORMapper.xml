<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.customer.dao.IOpenPONORDao">
	<sql id="openPONORFilter">
		<if test="_filters != null and _filters != ''.toString()">
			and ${_filters}
		</if>
		<if test="treePathFilter != null and treePathFilter != ''.toString()">
			and ${treePathFilter}
		</if>
		<if test="personalFilters != null and personalFilters != ''.toString()">
			and ${personalFilters}
		</if>
	</sql>

	<select id="queryCascader" resultType="java.util.Map">
		SELECT NAME, CATEGORY FROM ${SCPA.OPEN_PO_NOR_FILTER_V} T ORDER BY CATEGORY, NLSSORT(decode(t.NAME, 'Others', 'zzz', t.NAME), 'NLS_SORT = SCHINESE_PINYIN_M')
	</select>

	<resultMap id="report1ResultMap" type="com.scp.customer.bean.OpenPONORReport1Bean">
		<result property="category1" column="CATEGORY1"/>
		<result property="category2" column="CATEGORY2"/>
		<result property="category3" column="CATEGORY3"/>
		<result property="category4" column="CATEGORY4"/>
		<result property="category5" column="CATEGORY5"/>
		<result property="value" column="value"/>
		<association property="tooltips" javaType="com.scp.customer.bean.OpenPONORReport1Tooltips">
			<result property="AVG_SELLING_PRICE_RMB" column="AVG_SELLING_PRICE_RMB"/>
		</association>
	</resultMap>

	<select id="queryReport1" resultMap="report1ResultMap">
		WITH BASE AS (
		SELECT /*+ parallel(t 6) */ * FROM ${SCPA.OPEN_PO_NOR_V} T
		<where>
			<include refid="openPONORFilter"/>
		</where>
		)
		SELECT
		NVL(${level1}, 'Others') AS CATEGORY1,
		NVL(${level2}, 'Others') AS CATEGORY2,
		NVL(${level3}, 'Others') AS CATEGORY3,
		<if test="level4 != null and level4 != ''.toString()">
			NVL(${level4}, 'Others') AS CATEGORY4,
		</if>
		<if test="level5 != null and level5 != ''.toString()">
			NVL(${level5}, 'Others') AS CATEGORY5,
		</if>
		${valueColumn} AS VALUE
		<if test="tooltipsColumns != null and tooltipsColumns != ''.toString()">
			,${tooltipsColumns}
		</if>
		FROM BASE t
		GROUP BY
		${level1}, ${level2}, ${level3}
		<if test="level4 != null and level4 != ''.toString()">,${level4}</if>
		<if test="level5 != null and level5 != ''.toString()">,${level5}</if>
	</select>

	<resultMap id="report2ResultMap" type="com.scp.customer.bean.OpenPONORReport2Bean">
		<result property="CALENDAR_DATE" column="CALENDAR_DATE"/>
		<result property="NAME" column="NAME"/>
		<result property="VALUE" column="VALUE"/>
	</resultMap>

	<select id="queryReport2" parameterType="java.util.Map" resultMap="report2ResultMap">
		WITH BASE AS (
		SELECT /*+ parallel(t 6)*/ T.*
		                FROM ${SCPA.OPEN_PO_NOR_V} T
						<where>
							T.DELIVERY_DATE BETWEEN TO_DATE(#{report2DeliveryDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') AND TO_DATE(#{report2DeliveryDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
							<include refid="openPONORFilter"/>
						</where>
		)
		<choose>
			<when test='report2SelectedType == "VIEW_BY_DAY".toString()'>
				SELECT TO_CHAR(DELIVERY_DATE, 'yyyy/mm/dd') AS CALENDAR_DATE,
				NVL(t.${report2ViewType}, 'Others') AS NAME,
				ROUND(${valueColumn}, 2) AS VALUE
				FROM BASE T
				GROUP BY TO_CHAR(DELIVERY_DATE, 'yyyy/mm/dd'),
						 NVL(t.${report2ViewType}, 'Others')
				ORDER BY TO_CHAR(DELIVERY_DATE, 'yyyy/mm/dd') DESC
			</when>
			<when test='report2SelectedType == "VIEW_BY_WEEK".toString()'>
				SELECT DELIVERY_DATE_WEEK AS CALENDAR_DATE,
				NVL(t.${report2ViewType}, 'Others') AS NAME,
				ROUND(${valueColumn}, 2) AS VALUE
				FROM BASE T
				GROUP BY DELIVERY_DATE_WEEK,
						 NVL(t.${report2ViewType}, 'Others')
				ORDER BY DELIVERY_DATE_WEEK DESC
			</when>
			<when test='report2SelectedType == "VIEW_BY_MONTH".toString()'>
				SELECT DELIVERY_DATE_MONTH AS CALENDAR_DATE,
					NVL(t.${report2ViewType}, 'Others') AS NAME,
					ROUND(${valueColumn}, 2) AS VALUE
				FROM BASE T
				GROUP BY DELIVERY_DATE_MONTH,
						NVL(t.${report2ViewType}, 'Others')
				ORDER BY DELIVERY_DATE_MONTH DESC
			</when>
			<when test='report2SelectedType == "VIEW_BY_QUARTER".toString()'>
				SELECT DELIVERY_DATE_QUARTER AS CALENDAR_DATE,
						NVL(t.${report2ViewType}, 'Others') AS NAME,
						ROUND(${valueColumn}, 2) AS VALUE
				FROM BASE T
				GROUP BY DELIVERY_DATE_QUARTER,
					NVL(t.${report2ViewType}, 'Others')
				ORDER BY DELIVERY_DATE_QUARTER DESC
			</when>
			<when test='report2SelectedType == "VIEW_BY_YEAR".toString()'>
				SELECT DELIVERY_DATE_YEAR AS CALENDAR_DATE,
					NVL(t.${report2ViewType}, 'Others') AS NAME,
					ROUND(${valueColumn}, 2) AS VALUE
				FROM BASE T
				GROUP BY DELIVERY_DATE_YEAR,
						NVL(t.${report2ViewType}, 'Others')
				ORDER BY DELIVERY_DATE_YEAR DESC
			</when>
		</choose>
	</select>

	<sql id="reportDateFilter">
		<choose>
			<when test="${viewType} == 'VIEW_BY_DAY'.toString()">
				T.${viewByDay} = TO_DATE(#{${selectedDate}, jdbcType=VARCHAR}, 'yyyy/mm/dd')
			</when>
			<when test="${viewType} == 'VIEW_BY_WEEK'.toString()">
				T.${viewByWeek} = #{${selectedDate}, jdbcType=VARCHAR}
			</when>
			<when test="${viewType} == 'VIEW_BY_MONTH'.toString()">
				T.${viewByMonth} = #{${selectedDate}, jdbcType=VARCHAR}
			</when>
			<when test="${viewType} == 'VIEW_BY_QUARTER'.toString()">
				T.${viewByQuarter} = #{${selectedDate}, jdbcType=VARCHAR}
			</when>
			<when test="${viewType} == 'VIEW_BY_YEAR'.toString()">
				T.${viewByYear} = #{${selectedDate}, jdbcType=VARCHAR}
			</when>
		</choose>
	</sql>

	<sql id="report2DetailsSQL">
		SELECT *
		FROM ${SCPA.OPEN_PO_NOR_V} T
		<where>
			<include refid="reportDateFilter">
				<property name="selectedDate" value="report2DetailsDate"/>
				<property name="viewByDay" value="DELIVERY_DATE"/>
				<property name="viewByWeek" value="DELIVERY_DATE_WEEK"/>
				<property name="viewByMonth" value="DELIVERY_DATE_MONTH"/>
				<property name="viewByQuarter" value="DELIVERY_DATE_QUARTER"/>
				<property name="viewByYear" value="DELIVERY_DATE_YEAR"/>
				<property name="viewType" value="report2SelectedType"/>
			</include>
			<if test="report2DetailsValue != ''.toString() and report2DetailsValue != null">
				AND T.${report2ViewType} = #{report2DetailsValue, jdbcType=VARCHAR}
			</if>
			<include refid="openPONORFilter"/>
		</where>
	</sql>

	<select id="queryReport2DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="report2DetailsSQL"/>
		<include refid="global.count_footer"/>
	</select>

	<select id="queryReport2Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		<include refid="report2DetailsSQL"/>
		<include refid="global.select_footer"/>
	</select>


	<resultMap id="report3ResultMap" type="com.scp.customer.bean.OpenPONORReport3Bean">
		<result property="CALENDAR_DATE" column="CALENDAR_DATE"/>
		<result property="NAME" column="NAME"/>
		<result property="VALUE" column="VALUE"/>
	</resultMap>

	<select id="queryReport3Legend" resultType="java.lang.String">
		WITH  BASE AS (
			SELECT DISTINCT NVL(T.${report3ViewType}, 'Others') AS NAME
			FROM ${SCPA.OPEN_PO_NOR_HIST} T
			<where>
				<include refid="openPONORFilter"/>
				<choose>
					<when test='report3SelectedType == "VIEW_BY_WEEK".toString()'>
						AND T.DATE$ = TRUNC(T.DATE$, 'WW')
					</when>
					<when test='report3SelectedType == "VIEW_BY_MONTH".toString()'>
						AND T.DATE$ = TRUNC(T.DATE$, 'MM')
					</when>
					<when test='report3SelectedType == "VIEW_BY_QUARTER".toString()'>
						AND T.DATE$ = TRUNC(T.DATE$, 'Q')
					</when>
					<when test='report3SelectedType == "VIEW_BY_YEAR".toString()'>
						AND T.DATE$ = TRUNC(T.DATE$, 'YYYY')
					</when>
				</choose>
				AND T.DATE$ BETWEEN TO_DATE(#{report3DateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') AND TO_DATE(#{report3DateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
			</where>
		)
		SELECT * FROM BASE ORDER BY NAME
	</select>

	<select id="queryReport3" resultMap="report3ResultMap">
		WITH BASE AS (
		SELECT /*+ parallel(t 6) */ * FROM ${SCPA.OPEN_PO_NOR_HIST} T
		<where>
			<include refid="openPONORFilter"/>
			<choose>
				<when test='report3SelectedType == "VIEW_BY_WEEK".toString()'>
					AND T.DATE$ = TRUNC(T.DATE$, 'WW')
				</when>
				<when test='report3SelectedType == "VIEW_BY_MONTH".toString()'>
					AND T.DATE$ = TRUNC(T.DATE$, 'MM')
				</when>
				<when test='report3SelectedType == "VIEW_BY_QUARTER".toString()'>
					AND T.DATE$ = TRUNC(T.DATE$, 'Q')
				</when>
				<when test='report3SelectedType == "VIEW_BY_YEAR".toString()'>
					AND T.DATE$ = TRUNC(T.DATE$, 'YYYY')
				</when>
			</choose>
			AND T.DATE$ BETWEEN TO_DATE(#{report3DateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') AND TO_DATE(#{report3DateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
		</where>
		)
		SELECT TO_CHAR(T.DATE$, 'yyyy/mm/dd') AS "CALENDAR_DATE",
				   NVL(T.${report3ViewType}, 'Others') AS "NAME",
				   ${valueColumn} AS "VALUE"
		FROM BASE T
		GROUP BY T.DATE$, NVL(T.${report3ViewType}, 'Others')
	</select>
	<sql id="queryReport3DetailsSQL">
		SELECT *
		FROM  ${SCPA.OPEN_PO_NOR_HIST}  T LEFT JOIN SY_CALENDAR CD ON T.DELIVERY_DATE = CD.DATE$ AND CD.NAME = 'National Holidays'
		<where>
			<include refid="openPONORFilter"/>
			AND T.DATE$ = TO_DATE(#{report3DetailsDate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
			<if test="report3DetailsValue != ''.toString() and report3DetailsValue != null">
				AND T.${report3ViewType} = #{report3DetailsValue, jdbcType=VARCHAR}
			</if>
		</where>
	</sql>

	<select id="queryReport3DetailsCount" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="queryReport3DetailsSQL"/>
		<include refid="global.count_footer"/>
	</select>

	<select id="queryReport3Details" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		<include refid="queryReport3DetailsSQL"/>
		<include refid="global.select_footer"/>
	</select>

	<sql id="report4NORFields">
		NVL(SUM(DECODE(T.NOR_COUNT, 0, 1, 0)), 0)                                AS RC_EQ_0,
		NVL(SUM(DECODE(T.NOR_COUNT, 1, 1, 0)), 0)                                AS RC_EQ_1,
        NVL(SUM(DECODE(T.NOR_COUNT, 2, 1, 0)), 0)                                AS RC_EQ_2,
        NVL(SUM(DECODE(T.NOR_COUNT, 3, 1, 0)), 0)                                AS RC_EQ_3,
        NVL(SUM(CASE WHEN T.NOR_COUNT IN (4, 5) THEN 1 ELSE 0 END), 0)           AS RC_GE_4_LE_5,
        NVL(SUM(CASE WHEN T.NOR_COUNT IN (6, 7, 8, 9, 10) THEN 1 ELSE 0 END), 0) AS RC_GE_6_LE_10,
        NVL(SUM(CASE WHEN T.NOR_COUNT >= 11 THEN 1 ELSE 0 END), 0)               AS RC_GE_11,
        NVL(SUM(T.NOR_COUNT), 0)                                                 AS RC_TOTAL,
        NVL(COUNT(T.NOR_COUNT), 0)                                               AS RC_LINES,
        NVL(AVG(CASE WHEN T.NOR_COUNT > 0 THEN T.NOR_COUNT END), 0)              AS RC_AVERAGE
	</sql>

	<sql id="report4NOROFields">
		NVL(SUM(DECODE(T.NORO_COUNT, 0, 1, 0)), 0)                                AS RC_EQ_0,
		NVL(SUM(DECODE(T.NORO_COUNT, 1, 1, 0)), 0)                                AS RC_EQ_1,
        NVL(SUM(DECODE(T.NORO_COUNT, 2, 1, 0)), 0)                                AS RC_EQ_2,
        NVL(SUM(DECODE(T.NORO_COUNT, 3, 1, 0)), 0)                                AS RC_EQ_3,
        NVL(SUM(CASE WHEN T.NORO_COUNT IN (4, 5) THEN 1 ELSE 0 END), 0)           AS RC_GE_4_LE_5,
        NVL(SUM(CASE WHEN T.NORO_COUNT IN (6, 7, 8, 9, 10) THEN 1 ELSE 0 END), 0) AS RC_GE_6_LE_10,
        NVL(SUM(CASE WHEN T.NORO_COUNT >= 11 THEN 1 ELSE 0 END), 0)               AS RC_GE_11,
        NVL(SUM(T.NORO_COUNT), 0)                                                 AS RC_TOTAL,
        NVL(COUNT(T.NORO_COUNT), 0)                                               AS RC_LINES,
        NVL(AVG(CASE WHEN T.NORO_COUNT > 0 THEN T.NORO_COUNT END), 0)              AS RC_AVERAGE
	</sql>

	<sql id="queryReport4Sql">
		SELECT
		<foreach collection="report4ViewColumns" separator="," item="item">
			NVL(T.${item},'Others') "${item}"
		</foreach>,
		<choose>
			<when test="reportCalType == 'NORO_FLAG'.toString()">
				<include refid="report4NOROFields"/>
			</when>
			<otherwise>
				<include refid="report4NORFields"/>
			</otherwise>
		</choose>
		FROM ${SCPA.OPEN_PO_NOR_V} T
		<where>
			<include refid="openPONORFilter"/>
			<if test="report4DateRange != null and report4DateRange.size() == 2">
				AND T.DELIVERY_DATE BETWEEN TO_DATE(#{report4DateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
				AND TO_DATE(#{report4DateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
			</if>
		</where>
		GROUP BY
		<foreach collection="report4ViewColumns" separator="," item="item">
			NVL(${item},'Others')
		</foreach>
		ORDER BY
		<foreach collection="report4ViewColumns" separator="," item="item">
			DECODE(${item}, 'Others', CAST(UNISTR('\ffff\ffff') AS VARCHAR2(8)), ${item})
		</foreach>
	</sql>

	<select id="queryReport4" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		<include refid="queryReport4Sql"/>
		<include refid="global.select_footer"/>
	</select>

	<sql id="report4DetailsSQL">
		SELECT *
		FROM ${SCPA.OPEN_PO_NOR_V} T
		<where>
			<include refid="openPONORFilter"/>
			<if test="report4DateRange != null and report4DateRange.size() == 2">
				AND T.DELIVERY_DATE BETWEEN TO_DATE(#{report4DateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
				AND TO_DATE(#{report4DateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
			</if>
			<if test="report4Filter != null and report4Filter != ''.toString()">
				and ${report4Filter}
			</if>
			<if test="report4SelectedValue != null and report4SelectedValue.isEmpty() == false">
				<foreach collection="report4ViewColumns" separator=" and " item="item" index="index" open=" and ">
					<choose>
						<when test="item == 'Others'.toString()">
							(${item} = 'Others' or ${item} is null)
						</when>
						<when test="item != null and item != ''.toString()">
							${item} = #{report4SelectedValue[${index}], jdbcType=VARCHAR}
						</when>
					</choose>
				</foreach>
			</if>
		</where>
	</sql>

	<select id="queryReport4DetailsCount" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="report4DetailsSQL"/>
		<include refid="global.count_footer"/>
	</select>

	<select id="queryReport4Details" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		<include refid="report4DetailsSQL"/>
		<include refid="global.select_footer"/>
	</select>

	<sql id="report4DetailsMOSQL">
        WITH BASE AS ( SELECT *
		FROM ${SCPA.OPEN_PO_NOR_V} T
		<where>
			<include refid="openPONORFilter"/>
			<if test="report4DateRange != null and report4DateRange.size() == 2">
				AND T.DELIVERY_DATE BETWEEN TO_DATE(#{report4DateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
				AND TO_DATE(#{report4DateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
			</if>
			<if test="report4Filter != null and report4Filter != ''.toString()">
				and ${report4Filter}
			</if>
			<if test="report4SelectedValue != null and report4SelectedValue.isEmpty() == false">
				<foreach collection="report4ViewColumns" separator=" and " item="item" index="index" open=" and ">
					<choose>
						<when test="item == 'Others'.toString()">
							(${item} = 'Others' or ${item} is null)
						</when>
						<when test="item != null and item != ''.toString()">
							${item} = #{report4SelectedValue[${index}], jdbcType=VARCHAR}
						</when>
					</choose>
				</foreach>
			</if>
		</where>
        )
        SELECT MO.* FROM BASE B
        LEFT JOIN ${SCPA.PO_NOR_MO_DETAIL_V} MO ON B.PURCH_ORDER_NUMBER = MO.PURCH_ORDER_NUMBER AND B.PURCH_ORDER_ITEM
        = MO.PURCH_ORDER_ITEM
    </sql>

	<select id="queryReport4MODetailsCount" resultType="java.lang.Integer">
		<include refid="global.count_header"/>
		<include refid="report4DetailsMOSQL"/>
		<include refid="global.count_footer"/>
	</select>

	<select id="queryReport4MODetails" resultType="java.util.LinkedHashMap">
		<include refid="global.select_header"/>
		<include refid="report4DetailsMOSQL"/>
		<include refid="global.select_footer"/>
	</select>
</mapper>
