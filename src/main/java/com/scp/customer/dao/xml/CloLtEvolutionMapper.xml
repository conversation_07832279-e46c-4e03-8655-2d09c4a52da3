<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.customer.dao.ICloLtEvolutionDao">

    <sql id="CloLtEvolutionFilter">
        <if test="filters != null and filters != ''.toString()">
            and ${filters}
        </if>
        <if test="specialList != null and specialList.size() > 0">
            <foreach collection="specialList" item="list" separator=" or " open=" and (" close=")">
                t.${specialColumn} in
                <foreach collection="list" item="item" separator="," open="(" close=")">
                    #{item, jdbcType=VARCHAR}
                </foreach>
            </foreach>
        </if>
        <if test="treePathFilter != null and treePathFilter != ''.toString()">
            and ${treePathFilter}
        </if>
    </sql>

    <select id="queryCascader" resultType="java.util.Map">
        SELECT NAME,
               CATEGORY
        FROM CLO_LT_EVOLUTION_FILTER_V T
        ORDER BY CATEGORY
    </select>

    <resultMap id="report1ResultMap" type="com.scp.customer.bean.CloLtEvolutionReport1Bean">
        <result property="category1" column="CATEGORY1"/>
        <result property="category2" column="CATEGORY2"/>
        <result property="category3" column="CATEGORY3"/>
        <result property="category4" column="CATEGORY4"/>
        <result property="category5" column="CATEGORY5"/>
        <result property="value" column="value"/>
        <association property="tooltips" javaType="com.scp.customer.bean.CloLtEvolutionReport1Tooltips">
            <result property="AVG_VALUE" column="AVG_VALUE"/>
            <result property="AVG_LINES" column="AVG_LINES"/>
        </association>
    </resultMap>

    <select id="queryReport1" resultMap="report1ResultMap">
        SELECT NVL(${level1}, 'Others') AS CATEGORY1,
               NVL(${level2}, 'Others') AS CATEGORY2,
               NVL(${level3}, 'Others') AS CATEGORY3,
               <if test="level4 != null and level4 != ''.toString()">
                   NVL(${level4}, 'Others') AS CATEGORY4,
               </if>
               <if test="level5 != null and level5 != ''.toString()">
                   NVL(${level5}, 'Others') AS CATEGORY5,
               </if>
               ${valueColumn} AS VALUE
               <if test="tooltipsColumns != null and tooltipsColumns != ''.toString()">
                   ,${tooltipsColumns}
               </if>
        FROM CLO_LT_EVOLUTION_V T
        <where>
            <include refid="CloLtEvolutionFilter"/>
            <if test="report1PickerDate != null and report1PickerDate != ''.toString()">
                AND T.DATE$ = TRUNC(TO_DATE(#{report1PickerDate, jdbcType=VARCHAR}, 'YYYY/MM/DD'), 'MM')
            </if>
        </where>
        GROUP BY ${level1},
                 ${level2},
                 ${level3}
        <if test="level4 != null and level4 != ''.toString()">,${level4}</if>
        <if test="level5 != null and level5 != ''.toString()">,${level5}</if>
    </select>

    <resultMap id="report2ResultMap" type="com.scp.customer.bean.CloLtEvolutionReport2Bean">
        <result property="CALENDAR_DATE" column="CALENDAR_DATE"/>
        <result property="NAME" column="NAME"/>
        <result property="VALUE" column="VALUE"/>
    </resultMap>

    <select id="queryReport2" parameterType="java.util.Map" resultMap="report2ResultMap">
        SELECT
            TO_CHAR(T.DATE$, 'YYYY/MM/DD') AS CALENDAR_DATE,
            NVL(${report2ViewType}, 'Others') AS NAME,
            ROUND(${valueColumn}, 2) AS VALUE
        FROM CLO_LT_EVOLUTION_V T
        WHERE T.DATE$ BETWEEN TO_DATE(#{report2DateRange[0], jdbcType=VARCHAR}, 'YYYY/MM/DD')
                      AND TO_DATE(#{report2DateRange[1], jdbcType=VARCHAR}, 'YYYY/MM/DD')
              <include refid="CloLtEvolutionFilter"/>
              <choose>
                  <when test='report2DateType == "VIEW_BY_DAY".toString()'>
                      AND 1 = 1
                  </when>
                  <when test='report2DateType == "VIEW_BY_WEEK".toString()'>
                      AND t.date$ in (
                      SELECT min(t.DATE$) as DATE$
                      FROM CLO_LT_EVOLUTION_V T
                      LEFT JOIN SY_CALENDAR CALENDAR ON T.DATE$ = CALENDAR.DATE$ AND CALENDAR.NAME = 'National Holidays'
                      group by CALENDAR.YEAR || CALENDAR.WEEK_NO
                      )
                  </when>
                  <when test='report2DateType == "VIEW_BY_MONTH".toString()'>
                      AND TRUNC(T.DATE$,'MM') = T.DATE$
                  </when>
                  <when test='report2DateType == "VIEW_BY_QUARTER".toString()'>
                      AND TRUNC(T.DATE$, 'Q') = T.DATE$
                  </when>
                  <when test='report2DateType == "VIEW_BY_YEAR".toString()'>
                      AND TRUNC(T.DATE$, 'YYYY') = T.DATE$
                  </when>
              </choose>
        GROUP BY
            T.DATE$,
            NVL(${report2ViewType}, 'Others')
        ORDER BY DECODE(NVL(T.${report2ViewType}, 'Others'),
        '2.8_MTO_>1Y',
        'a',
        '2.7_MTO_6-12M',
        'b',
        '2.6_MTO_3-6M',
        'c',
        '2.5_MTO_2-3M',
        'd',
        '2.4_MTO_1-2M',
        'e',
        '2.3_MTO_2-4W',
        'f',
        '2.2_MTO_1-2W',
        'g',
        '2.1_MTO_&lt;1W',
        'h',
        '1.8_MTS_>6M',
        'i',
        '1.7_MTS_3-6M',
        'j',
        '1.6_MTS_2-3M',
        'k',
        '1.5_MTS_1-2M',
        'l',
        '1.4_MTS_2-4W',
        'm',
        '1.3_MTS_1-2W',
        'n',
        '1.2_MTS_&lt;1W',
        'o',
        '1.1_MTS_D+1',
        'p','Others',
        'q',
        NVL(T.${report2ViewType}, 'Others'))
    </select>

    <sql id="report2DetailsSQL">
        SELECT * FROM CLO_LT_EVOLUTION_V T
        WHERE T.DATE$ = TO_DATE(#{report2SelectedXAxis, jdbcType=VARCHAR}, 'yyyy/mm/dd')
        <if test="report2SelectedValue != null and report2SelectedValue != ''.toString()">
            AND T.${report2ViewType} = #{report2SelectedValue, jdbcType=VARCHAR}
        </if>
        <include refid="CloLtEvolutionFilter"/>
    </sql>

    <select id="queryReport2DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="report2DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport2Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report2DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="report3SQL">
        WITH BASE AS (
                    SELECT
                        <foreach collection="report3SelectedColumns" item="item">
                                NVL(${item}, 'Others')               AS ${item},
                        </foreach>
                        TO_CHAR(T.DATE$, 'yyyy/mm/dd')               AS CALENDAR_DATE,
                        ROUND(nvl(${report3ValueColumn},0), 3)                     AS VALUE
                    FROM CLO_LT_EVOLUTION_V t
                    <where>
                        T.DATE$ BETWEEN TO_DATE(#{report3DateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                        AND TO_DATE(#{report3DateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                        <include refid="CloLtEvolutionFilter"/>
                        <choose>
                            <when test='report3DateType == "VIEW_BY_DAY".toString()'>
                                AND 1 = 1
                            </when>
                            <when test='report3DateType == "VIEW_BY_WEEK".toString()'>
                                AND t.date$ in (
                                SELECT min(t.DATE$) as DATE$
                                FROM CLO_LT_EVOLUTION_V T
                                LEFT JOIN SY_CALENDAR CALENDAR ON T.DATE$ = CALENDAR.DATE$ AND CALENDAR.NAME = 'National Holidays'
                                group by CALENDAR.YEAR || CALENDAR.WEEK_NO
                                )
                            </when>
                            <when test='report3DateType == "VIEW_BY_MONTH".toString()'>
                                AND TRUNC(T.DATE$,'MM') = T.DATE$
                            </when>
                            <when test='report3DateType == "VIEW_BY_QUARTER".toString()'>
                                AND TRUNC(T.DATE$, 'Q') = T.DATE$
                            </when>
                            <when test='report3DateType == "VIEW_BY_YEAR".toString()'>
                                AND TRUNC(T.DATE$, 'YYYY') = T.DATE$
                            </when>
                        </choose>
                    </where>
                    GROUP BY
                        <foreach collection="report3SelectedColumns" item="item">
                            ${item},
                        </foreach>
                        T.DATE$)
        SELECT * FROM
            ( SELECT <foreach collection="report3SelectedColumns" item="item">
                          NVL(${item}, 'Others')         AS ${item},
                     </foreach>
                     CALENDAR_DATE,
                     NVL(T.VALUE, 0) AS TOTAL
                     FROM BASE T
                <if test="report3SelectedColumns.contains('MATERIAL')">
                    WHERE
                    <foreach collection="report3SelectedColumns" item="item"  separator=" || ">
                        ${item}
                    </foreach>  IN (
                    select  <foreach collection="report3SelectedColumns" item="item"  separator=" || ">
                    ${item}
                </foreach>  from BASE
                        where CALENDAR_DATE = (
                            SELECT DISTINCT t.CALENDAR_DATE
                            FROM BASE t
                            WHERE TRUNC(to_date(t.CALENDAR_DATE, 'YYYY-MM-DD'), 'dd') BETWEEN TO_DATE(#{report3DateRange[0], jdbcType=VARCHAR}, 'yyyy-mm-dd')
                            AND TO_DATE(#{report3DateRange[1], jdbcType=VARCHAR}, 'yyyy-mm-dd')
                            ORDER BY to_date(t.CALENDAR_DATE, 'YYYY-MM-DD') DESC
                            OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY
                        )
                        group by  <foreach collection="report3SelectedColumns" item="item"  separator=" || ">
                    ${item}
                </foreach>  order by SUM(VALUE) desc fetch next ${report3TopQuantities} rows only
                    )
                </if>
            ) MM
            PIVOT (
                SUM(TOTAL) AS TOTAL
                FOR CALENDAR_DATE
                    IN (
                    <foreach collection="report3ColumnNames" separator="," item="item">
                        '${item}'
                    </foreach>)
            )
        ORDER BY
        <foreach collection="report3SelectedColumns" item="item" separator=",">
            DECODE(${item}, 'Others', 'zzz', ${item})
        </foreach>
    </sql>

    <select id="queryReport3Columns" resultType="java.lang.String">
        SELECT DISTINCT TO_CHAR(t.DATE$, 'YYYY/MM/DD') AS RESULT
        FROM CLO_LT_EVOLUTION_V t
        WHERE TRUNC(t.DATE$, 'dd') BETWEEN TO_DATE(#{report3DateRange[0], jdbcType=VARCHAR}, 'yyyy-mm-dd')
        AND TO_DATE(#{report3DateRange[1], jdbcType=VARCHAR}, 'yyyy-mm-dd')
        <choose>
            <when test='report3DateType == "VIEW_BY_DAY".toString()'>
                AND 1 = 1
            </when>
            <when test='report3DateType == "VIEW_BY_WEEK".toString()'>
                AND t.date$ in (
                SELECT min(t.DATE$) as DATE$
                FROM CLO_LT_EVOLUTION_V T
                LEFT JOIN SY_CALENDAR CALENDAR ON T.DATE$ = CALENDAR.DATE$ AND CALENDAR.NAME = 'National Holidays'
                group by CALENDAR.YEAR || CALENDAR.WEEK_NO
                )
            </when>
            <when test='report3DateType == "VIEW_BY_MONTH".toString()'>
                AND TRUNC(T.DATE$,'MM') = T.DATE$
            </when>
            <when test='report3DateType == "VIEW_BY_QUARTER".toString()'>
                AND TRUNC(T.DATE$, 'Q') = T.DATE$
            </when>
            <when test='report3DateType == "VIEW_BY_YEAR".toString()'>
                AND TRUNC(T.DATE$, 'YYYY') = T.DATE$
            </when>
        </choose>
        ORDER BY t.DATE$ DESC
        OFFSET 0 ROWS FETCH NEXT 30 ROWS ONLY
    </select>

    <select id="queryReport3" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report3SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="report3DetailsSQL">
        SELECT * FROM
        (SELECT * FROM CLO_LT_EVOLUTION_V T
        WHERE t.DATE$ BETWEEN TO_DATE(#{report3DateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
        AND TO_DATE(#{report3DateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
        <if test="report3SelectedDate != null and report3SelectedDate != ''.toString()">
            and t.DATE$ = TO_DATE(#{report3SelectedDate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
        </if>
        <include refid="CloLtEvolutionFilter"/>
        <foreach collection="report3SelectedColumns" item="item" index="index">
            <if test="report3SelectedValues[index] != null and report3SelectedValues[index] != ''.toString()">
                <if test="report3SelectedValues[index] == 'Others'">
                    AND t.${item} IS NULL
                </if>
                <if test="report3SelectedValues[index] != 'Others'">
                    AND t.${item} = #{report3SelectedValues[${index}], jdbcType=VARCHAR}
                </if>
            </if>
        </foreach>)
    </sql>

    <select id="queryReport3DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="report3DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport3Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report3DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport4" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        SELECT MATERIAL || '\t' || PLANT_CODE || '\t' || ENTITY || '\t' || CALANDER_TYPE AS xAxis,
               PLANNED_DELIV_TIME,
               PICK_PACK_TIME,
               GR_PROCESSING_TIME,
               T_REPLENISHMENT_LT,
               AVG_VALUE,
               AVG_LINES,
               IN_HOUSE_PRODN_LT,
               TRANSPORTATION_ZMGC,
               PLANT_PLANNED_DELIV_TIME,
               PLANT_GR_PROCESSING_TIME,
               PLANT_T_REPLENISHMENT_LT,
               PLANT_IN_HOUSE_PRODN_LT,
               ERROR_LABEL
        FROM SCPA.CLO_LT_EVOLUTION_V T
        <where>
            DATE$ = TO_DATE(#{report4PickerDate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
            AND FAC_VENDOR_CODE IS NOT NULL
            <include refid="CloLtEvolutionFilter"/>
        </where>
        ORDER BY nvl(${report4SortColumn},0) DESC FETCH NEXT ${report4TopQuantities} ROWS ONLY
    </select>

    <sql id="report4DetailsSQL">
        SELECT * FROM CLO_LT_EVOLUTION_V T
        <where>
            DATE$ = TO_DATE(#{report4PickerDate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
            AND FAC_VENDOR_CODE IS NOT NULL
            <if test="report4SelectedValue != null and report4SelectedValue != ''.toString()">
               AND T.MATERIAL || '\t' || T.PLANT_CODE || '\t' || T.ENTITY || '\t' || CALANDER_TYPE = #{report4SelectedValue, jdbcType=VARCHAR}
            </if>
            <include refid="CloLtEvolutionFilter"/>
        </where>
    </sql>

    <select id="queryReport4DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="report4DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport4Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report4DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="downloadReport4" parameterType="java.util.Map"  resultType="java.util.LinkedHashMap">
        SELECT * FROM (SELECT *
        FROM SCPA.CLO_LT_EVOLUTION_V T
        <where>
            DATE$ = TO_DATE(#{report4PickerDate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
            AND FAC_VENDOR_CODE IS NOT NULL
            <include refid="CloLtEvolutionFilter"/>
        </where>
        <if test="report4DownloadType == 'CURRENT_VIEW_RESULT'.toString()">
            ORDER BY nvl(${report4SortColumn},0) DESC FETCH NEXT ${report4TopQuantities} ROWS ONLY
        </if>)
        <where>
            <choose>
                <when test="report4Exclude == 1 and report4DownloadType == 'CURRENT_VIEW_RESULT'.toString()">
                    ERROR_LABEL IN (1, 3)
                </when>
                <when test="report4Exclude == 2 and report4DownloadType == 'CURRENT_VIEW_RESULT'.toString()">
                    ERROR_LABEL IN (2, 3)
                </when>
                <when test="report4Exclude == 3 and report4DownloadType == 'CURRENT_VIEW_RESULT'.toString()">
                    ERROR_LABEL IN (3)
                </when>
            </choose>
        </where>
    </select>
</mapper>
