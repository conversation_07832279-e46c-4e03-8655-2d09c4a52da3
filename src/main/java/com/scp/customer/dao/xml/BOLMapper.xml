<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.customer.dao.IBOLDao">
    <sql id="filter">
        and t.BOM_CATEGORY != 'HEADER'
        <if test="_filters != null and _filters != ''.toString()">
            and ${_filters}
        </if>
        <include refid="scope_filter"/>
    </sql>

    <sql id="scope_filter">
        <if test="scope != null and scope != ''.toString()">
            <choose>
                <when test="subScope == null or subScope.size() == 0">
                    <choose>
                        <when test="scope == 'SECI'.toString()">
                            AND T.SE_SCOPE IS NOT NULL
                        </when>
                        <when test="scope == 'PLANT'.toString()">
                            AND t.PLANT_SCOPE IS NOT NULL
                        </when>
                    </choose>
                </when>
                <otherwise>
                    <choose>
                        <when test="scope == 'SECI'.toString()">
                            AND T.SE_SCOPE IN
                            <foreach collection="subScope" separator="," open="(" close=")" item="item">
                                #{item, jdbcType=VARCHAR}
                            </foreach>
                        </when>
                        <when test="scope == 'PLANT'.toString()">
                            AND t.PLANT_SCOPE IN
                            <foreach collection="subScope" separator="," open="(" close=")" item="item">
                                #{item, jdbcType=VARCHAR}
                            </foreach>
                        </when>
                    </choose>
                </otherwise>
            </choose>
        </if>
    </sql>

    <select id="queryCascader" resultType="java.util.Map">
        SELECT * FROM BOL_FILTER_V T ORDER BY T.CATEGORY, DECODE(T.NAME,'Others','zzz',T.NAME)
    </select>

    <select id="queryReport1Columns" resultType="java.lang.String">
        SELECT DISTINCT to_char(date$, 'yyyy/mm/dd')
        FROM DEMAND_BACK_ORDER_HIST T
        WHERE DATE$ BETWEEN least(TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd'),TRUNC(SYSDATE, 'dd'))
            AND least(TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'),TRUNC(SYSDATE, 'dd'))
        ORDER BY DATE$ DESC FETCH NEXT 7 ROWS ONLY
    </select>

    <select id="queryReport1" resultType="java.util.Map">
        select * from (
        select *
        from (
        select to_char(date$, 'yyyy/mm/dd') DAY,
            ${category1Value}            CATEGORY1,
            SUM(${valueColumn})          VAL
        from ${SCPA.DEMAND_BACK_ORDER_HIST} T
        where date$ in (
        <foreach collection="days" item="item" separator=",">
            to_date(#{item, jdbcType=VARCHAR}, 'yyyy/mm/dd')
        </foreach>
        )
        <include refid="filter"/>
        group by date$, ${category1Value}

        union all

        select 'Target',
            ${category1Value},
            ${targeColumn}
        FROM ${SCPA.DEMAND_BACK_ORDER_V} t
        where 1 = 1
        <include refid="filter"/>
        group by ${category1Value} ) mm
            PIVOT (
                sum(VAL) VAL
                FOR day IN
                    ( 'Target',
                    <foreach collection="days" item="item" separator=",">
                        '${item}'
                    </foreach>
                    )
            )
        ) nn
        WHERE CATEGORY1 IS NOT NULL
        order by decode(nn.category1, 'Others', 'zzz', nn.category1)
    </select>

    <select id="queryReport1Sub" resultType="java.util.Map">
        select * from (
        select *
        from (
        select to_char(date$, 'yyyy/mm/dd') DAY,
            ${category2Value}            CATEGORY2,
            SUM(${valueColumn})          VAL
        from ${SCPA.DEMAND_BACK_ORDER_HIST} T
        where date$ in (
        <foreach collection="days" item="item" separator=",">
            to_date(#{item, jdbcType=VARCHAR}, 'yyyy/mm/dd')
        </foreach>
        )
        <include refid="filter"/>
        and t.${category1Value} = #{expandCategory1, jdbcType=VARCHAR}
        group by date$, ${category2Value}

        union all

        select 'Target',
            ${category2Value},
            sum(${valueColumn})
        FROM ${SCPA.DEMAND_BACK_ORDER_V} t
        where t.${category1Value} = #{expandCategory1, jdbcType=VARCHAR}
        <include refid="filter"/>
        group by ${category2Value}
        ) mm
        PIVOT (
        sum(VAL) VAL
        FOR day IN
        ('Target',
        <foreach collection="days" item="item" separator=",">
            '${item}'
        </foreach>
        )
        )
        ) nn
        order by decode(nn.category2, 'Others', 'zzz', nn.category2)
    </select>

    <sql id="queryReport1SQL">
        select to_char(DATE$, 'yyyy/mm/dd') "DATE",
            to_char(CALENDAR_DATE, 'yyyy/mm/dd') CRD_DATE,
            SALES_ORDER_NUMBER,
            SALES_ORDER_ITEM,
            MATERIAL,
            PLANT_CODE,
            HIGHER_LEVEL_ITEM,
            ORDER_CATEGORY,
            BOM_CATEGORY,
            SE_SCOPE,
            DATA_SOURCE,
            UNIT_COST,
            AVG_SELLING_PRICE_RMB,
            NET_NET_VALUE_RMB,
            ORDER_QUANTITY,
            ORDER_TYPE,
            SALES_ORGANIZATION,
            SOLD_TO,
            SOLD_TO_COUNTRY,
            SHIP_TO,
            SHIP_TO_REGION,
            SHIP_TO_SUB_REGION,
            MATERIAL_OWNER_NAME,
            MATERIAL_OWNER_SESA,
            MRP_CONTROLLER,
            PRODUCT_LINE,
            ENTITY,
            CLUSTER_NAME,
            BU,
            LOCAL_BU,
            LOCAL_PRODUCT_FAMILY,
            LOCAL_PRODUCT_LINE,
            LOCAL_PRODUCT_SUBFAMILY,
            STOCKING_POLICY,
            VENDOR_CODE,
            VENDOR_NAME,
            VENDOR_PARENT_CODE,
            VENDOR_PARENT_NAME,
            VENDOR_SHORT_NAME,
            VENDOR_FULL_NAME,
            DELAY_DAYS,
            DELAY_DEPTH,
            OPEN_SO_W_O_DEL,
            t.order_quantity * t.unit_cost as mvp,
            t.ac2_range,
            t.ac2_type,
            t.ACTIVENESS,
            T.AVAILABILITY_CHECK,
            T.BLOCK_STATUS,
            T.CUSTOMER_CODE,
            T.DELIVERY_PRIORITY,
            T.FULFILL_OR_NOT_NONBLOCK,
            T.FULFILL_OR_NOT_UNRESTRICT,
            T.GRA,
            T.GRA_TYPE,
            T.ITEM_CATEGORY,
            T.LT_RANGE,
            T.PAST_DUE_INDICATOR,
            T.PLANT_NAME,
            T.PLANT_TYPE,
            T.SALES_GROUP,
            T.SHIPPING_POINT,
            T.SHIP_TO_CITY,
            T.SHIP_TO_COUNTRY,
            T.SOLD_TO_REGION,
            T.SOURCE_CATEGORY,
            T.SO_BLOCK_STATUS,
            T.SO_PRODUCT_LINE,
            T.UD_AGING_RANGE,
            T.UD_STATUS,
            t.VIP_SO_INDICATOR,
            T.SO_CONTRACTUAL_GI_DATE,
            T.SO_BLOCK_REASON,
            T.SO_LN_DELV_ST_VBEP_LIFSP,
            T.SO_HD_DELV_ST_VBAK_LIFSK,
            T.SO_HD_BILL_ST_VBAK_FAKSK,
            T.SO_IT_BILL_ST_VBAP_FAKSP,
            T.SO_HD_CRED_ST_VBUK_CMGST,
            T.DN_HD_DELV_ST_LIKP_LIFSK,
            T.DN_HD_CRED_ST_VBUK_CMGST,
            T.DN_HD_RISK_ST_LIKP_CTLPC
        from ${SCPA.DEMAND_BACK_ORDER_HIST} t
        where t.date$ = to_date(#{viewDate, jdbcType=VARCHAR},'yyyy/mm/dd')
        <include refid="filter"/>
        <if test="viewCategory1 != null and viewCategory1 != ''.toString()">
            and t.${category1Value} = #{viewCategory1,jdbcType=VARCHAR}
        </if>
        <if test="viewCategory2 != null and viewCategory2 != ''.toString()">
            and t.${category2Value} = #{viewCategory2,jdbcType=VARCHAR}
        </if>
    </sql>

    <sql id="queryReport1TargetSQL">
        select to_char(CALENDAR_DATE, 'yyyy/mm/dd') CRD_DATE,
            t.SALES_ORDER_NUMBER,
            t.SALES_ORDER_ITEM,
            MATERIAL,
            PLANT_CODE,
            HIGHER_LEVEL_ITEM,
            ORDER_CATEGORY,
            BOM_CATEGORY,
            DATA_SOURCE,
            UNIT_COST,
            AVG_SELLING_PRICE_RMB,
            NET_NET_VALUE_RMB,
            ORDER_QUANTITY,
            ORDER_TYPE,
            SALES_ORGANIZATION,
            SOLD_TO,
            SOLD_TO_COUNTRY,
            SHIP_TO,
            SHIP_TO_REGION,
            SHIP_TO_SUB_REGION,
            t.MATERIAL_OWNER_NAME,
            t.MATERIAL_OWNER_SESA,
            MRP_CONTROLLER,
            PRODUCT_LINE,
            ENTITY,
            CLUSTER_NAME,
            BU,
            LOCAL_BU,
            LOCAL_PRODUCT_FAMILY,
            LOCAL_PRODUCT_LINE,
            LOCAL_PRODUCT_SUBFAMILY,
            STOCKING_POLICY,
            t.VENDOR_CODE,
            t.VENDOR_NAME,
            t.VENDOR_PARENT_CODE,
            t.VENDOR_PARENT_NAME,
            t.VENDOR_SHORT_NAME,
            t.VENDOR_FULL_NAME,
            ACTIVENESS,
            DELAY_DAYS,
            DELAY_DEPTH,
            OPEN_SO_W_O_DEL,
            t.order_quantity * t.unit_cost as mvp,
            t.BACK_ORDER_LINE_TARGET,
            t.BACK_ORDER_QUANTITY_TARGET,
            t.BACK_ORDER_VALUE_TARGET,
            t2.RCA_RESULT AS OTDS_RCA,
            t2.RCA_COMMENTS AS OTDS_RCA_COMMENTS,
            t3.BOL_RCA,
            t3.COMMENTS AS BOL_RCA_COMMENTS,
            t2.FULFILL_STRATEGY_UNBLOCK_LAST_WEEK,
            t2.FULFILL_STRATEGY_UNBLOCK,
            t2.FULFILL_OR_NOT_NONBLOCK_LAST_WEEK,
            t.FULFILL_OR_NOT_NONBLOCK,
            t2.GRA_TYPE,
            t2.SO_STOCKING_POLICY,
            t2.MOQ_TO_CUSTOMER,
            t2.MDQ_TO_CUSTOMER,
            t2.RV_TO_CUSTOMER,
            t.ITEM_CATEGORY,
            t.ac2_range,
            t.ac2_type,
            T.AVAILABILITY_CHECK,
            T.BLOCK_STATUS,
            T.CUSTOMER_CODE,
            T.DELIVERY_PRIORITY,
            T.FULFILL_OR_NOT_UNRESTRICT,
            T.GRA,
            T.LT_RANGE,
            T.PAST_DUE_INDICATOR,
            T.PLANT_NAME,
            T.PLANT_TYPE,
            T.SALES_GROUP,
            T.SHIPPING_POINT,
            T.SHIP_TO_CITY,
            T.SHIP_TO_COUNTRY,
            T.SOLD_TO_REGION,
            T.SOURCE_CATEGORY,
            T.SO_BLOCK_STATUS,
            T.SO_PRODUCT_LINE,
            T.UD_AGING_RANGE,
            T.UD_STATUS,
            t.VIP_SO_INDICATOR,
            T.SO_CONTRACTUAL_GI_DATE,
            T.SO_BLOCK_REASON,
            T.SO_LN_DELV_ST_VBEP_LIFSP,
            T.SO_HD_DELV_ST_VBAK_LIFSK,
            T.SO_HD_BILL_ST_VBAK_FAKSK,
            T.SO_IT_BILL_ST_VBAP_FAKSP,
            T.SO_HD_CRED_ST_VBUK_CMGST,
            T.DN_HD_DELV_ST_LIKP_LIFSK,
            T.DN_HD_CRED_ST_VBUK_CMGST,
            T.DN_HD_RISK_ST_LIKP_CTLPC
        from ${SCPA.DEMAND_BACK_ORDER_V} t
        left join BOL_SOURCE_DAILY_V t2
        on t.SALES_ORDER_NUMBER = t2.SALES_ORDER_NUMBER and t.SALES_ORDER_ITEM = t2.SALES_ORDER_ITEM
        LEFT JOIN MR3_BOL_RCA t3
        on t.SALES_ORDER_NUMBER = t3.SALES_ORDER_NUMBER and t.SALES_ORDER_ITEM = t3.SALES_ORDER_ITEM
        where 1 = 1
        <include refid="filter"/>
        <if test="viewCategory1 != null and viewCategory1 != ''.toString()">
            and t.${category1Value} = #{viewCategory1,jdbcType=VARCHAR}
        </if>
        <if test="viewCategory2 != null and viewCategory2 != ''.toString()">
            and t.${category2Value} = #{viewCategory2,jdbcType=VARCHAR}
        </if>
    </sql>

    <select id="queryReport1DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <choose>
            <when test="viewDate == 'Target'.toString()">
                <include refid="queryReport1TargetSQL"/>
            </when>
            <otherwise>
                <include refid="queryReport1SQL"/>
            </otherwise>
        </choose>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <choose>
            <when test="viewDate == 'Target'.toString()">
                <include refid="queryReport1TargetSQL"/>
            </when>
            <otherwise>
                <include refid="queryReport1SQL"/>
            </otherwise>
        </choose>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport2" resultType="java.util.Map">
        SELECT NN."xAxis",
            NN."yAxis",
            NN."yAxis1",
            NN."yAxis2",
            NN."yAxis3",
            NN."yAxis4",
            NN."yAxis5",
            NN."yAxis6",
            NN."yAxis7",
            NN."yAxis8"
        FROM (
        SELECT TO_CHAR(T.DATE$, 'YYYY/MM/DD') "xAxis",
            SUM(${valueColumn}) "yAxis",
            SUM(DECODE(T.DELAY_DEPTH, '0-3D', ${valueColumn})) "yAxis1",
            SUM(DECODE(T.DELAY_DEPTH, '3-7D', ${valueColumn})) "yAxis2",
            SUM(DECODE(T.DELAY_DEPTH, '7-14D', ${valueColumn})) "yAxis3",
            SUM(DECODE(T.DELAY_DEPTH, '14-30D', ${valueColumn})) "yAxis4",
            SUM(DECODE(T.DELAY_DEPTH, '1-2M', ${valueColumn})) "yAxis5",
            SUM(DECODE(T.DELAY_DEPTH, '2-3M', ${valueColumn})) "yAxis6",
            SUM(DECODE(T.DELAY_DEPTH, '3-6M', ${valueColumn})) "yAxis7",
            SUM(DECODE(T.DELAY_DEPTH, '>6M', ${valueColumn})) "yAxis8"
        FROM ${SCPA.DEMAND_BACK_ORDER_HIST} T
        WHERE 1 = 1
        <include refid="filter"/>
        GROUP BY TO_CHAR(T.DATE$, 'YYYY/MM/DD')
        ) NN
        ORDER BY NN."xAxis"
    </select>

    <select id="queryReport6" resultType="java.util.Map">
        WITH RCA_TEMP AS (SELECT SALES_ORDER_NUMBER, SALES_ORDER_ITEM, nvl(BOL_RCA, 'Others') AS BOL_RCA, COMMENTS
        FROM MR3_BOL_RCA),
        TEMP AS (select *
        from ${SCPA.DEMAND_BACK_ORDER_HIST} t
        left join RCA_TEMP t2
        on t.SALES_ORDER_NUMBER = t2.SALES_ORDER_NUMBER and t.SALES_ORDER_ITEM = t2.SALES_ORDER_ITEM)
        SELECT NN."xAxis"
        <foreach collection="legends" separator="," open="," item="item" close="">
            NN."${item}"
        </foreach>
        FROM (
        SELECT TO_CHAR(T.DATE$, 'YYYY/MM/DD') "xAxis"
        <foreach collection="legends" separator="," open="," item="item" close="">
            SUM(DECODE(T.BOL_RCA, '${item}', ${valueColumn}))  "${item}"
        </foreach>
        FROM TEMP T
        WHERE 1 = 1
        <include refid="filter"/>
        GROUP BY TO_CHAR(T.DATE$, 'YYYY/MM/DD')
        ) NN
        ORDER BY NN."xAxis"
    </select>

    <select id="queryReport6Legend" resultType="java.util.Map">
        SELECT DISTINCT NVL(BOL_RCA, 'Others') BOL_RCA
        FROM MR3_BOL_RCA
    </select>

    <select id="queryReport3" resultType="java.util.Map">
        SELECT DELAY_DEPTH KEY,
        SUM(${valueColumn}) VAL
        FROM ${SCPA.DEMAND_BACK_ORDER_HIST} T
        WHERE T.DATE$ = TO_DATE(#{report3Date, jdbcType=VARCHAR}, 'YYYY/MM/DD')
        <include refid="filter"/>
        GROUP BY DELAY_DEPTH
        ORDER BY DECODE(DELAY_DEPTH, '0-3D', 'A', '3-7D', 'B', '7-14D', 'C', '14-30D', 'D', '1-2M', 'E', '2-3M', 'F', '3-6M', 'G', '>6M', 'H')
    </select>

    <sql id="queryReport3SQL">
        select to_char(DATE$, 'yyyy/mm/dd') "DATE",
            to_char(CALENDAR_DATE, 'yyyy/mm/dd') CRD_DATE,
            SALES_ORDER_NUMBER,
            SALES_ORDER_ITEM,
            MATERIAL,
            PLANT_CODE,
            HIGHER_LEVEL_ITEM,
            ORDER_CATEGORY,
            BOM_CATEGORY,
            DATA_SOURCE,
            UNIT_COST,
            AVG_SELLING_PRICE_RMB,
            NET_NET_VALUE_RMB,
            ORDER_QUANTITY,
            ORDER_TYPE,
            SALES_ORGANIZATION,
            SOLD_TO,
            SOLD_TO_COUNTRY,
            SHIP_TO,
            SHIP_TO_REGION,
            SHIP_TO_SUB_REGION,
            MATERIAL_OWNER_NAME,
            MATERIAL_OWNER_SESA,
            MRP_CONTROLLER,
            PRODUCT_LINE,
            ENTITY,
            CLUSTER_NAME,
            BU,
            LOCAL_BU,
            LOCAL_PRODUCT_FAMILY,
            LOCAL_PRODUCT_LINE,
            LOCAL_PRODUCT_SUBFAMILY,
            STOCKING_POLICY,
            VENDOR_CODE,
            VENDOR_NAME,
            VENDOR_PARENT_CODE,
            VENDOR_PARENT_NAME,
            VENDOR_SHORT_NAME,
            VENDOR_FULL_NAME,
            ACTIVENESS,
            DELAY_DAYS,
            DELAY_DEPTH,
            OPEN_SO_W_O_DEL,
            T.AC2_RANGE,
            T.AC2_TYPE,
            T.AVAILABILITY_CHECK,
            T.BLOCK_STATUS,
            T.CUSTOMER_CODE,
            T.DELIVERY_PRIORITY,
            T.FULFILL_OR_NOT_NONBLOCK,
            T.FULFILL_OR_NOT_UNRESTRICT,
            T.GRA,
            T.GRA_TYPE,
            T.ITEM_CATEGORY,
            T.LT_RANGE,
            T.PAST_DUE_INDICATOR,
            T.PLANT_NAME,
            T.PLANT_TYPE,
            T.SALES_GROUP,
            T.SHIPPING_POINT,
            T.SHIP_TO_CITY,
            T.SHIP_TO_COUNTRY,
            T.SOLD_TO_REGION,
            T.SOURCE_CATEGORY,
            T.SO_BLOCK_STATUS,
            T.SO_PRODUCT_LINE,
            T.UD_AGING_RANGE,
            T.UD_STATUS,
            t.order_quantity * t.unit_cost as mvp,
            t.VIP_SO_INDICATOR,
            T.SO_CONTRACTUAL_GI_DATE,
            T.SO_BLOCK_REASON,
            T.SO_LN_DELV_ST_VBEP_LIFSP,
            T.SO_HD_DELV_ST_VBAK_LIFSK,
            T.SO_HD_BILL_ST_VBAK_FAKSK,
            T.SO_IT_BILL_ST_VBAP_FAKSP,
            T.SO_HD_CRED_ST_VBUK_CMGST,
            T.DN_HD_DELV_ST_LIKP_LIFSK,
            T.DN_HD_CRED_ST_VBUK_CMGST,
            T.DN_HD_RISK_ST_LIKP_CTLPC
        from ${SCPA.DEMAND_BACK_ORDER_HIST} t
        where t.date$ = to_date(#{report3Date, jdbcType=VARCHAR},'yyyy/mm/dd')
        and t.DELAY_DEPTH = #{report3Type, jdbcType=VARCHAR}
        <include refid="filter"/>
    </sql>

    <select id="queryReport3DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport3SQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport3Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport3SQL"/>
        <include refid="global.select_footer"/>
    </select>

</mapper>
