<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.customer.dao.IOTDMDao">
    <sql id="filter">
        <if test="_filters != null and _filters != ''.toString()">
            and ${_filters}
        </if>
    </sql>

    <select id="queryRacCode" resultType="java.lang.String">
        -- 保持与otds统一，故调用OTDS底表
        select distinct rca_code from scpa.OTDS_RCA_CODE order by rca_code
    </select>

    <select id="queryReport1YearList" resultType="java.lang.String">
        SELECT DISTINCT YEAR
          FROM SY_CALENDAR T
         WHERE T.DATE$ BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'YYYY/MM') AND LAST_DAY(TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'YYYY/MM'))
           AND T.NAME = 'National Holidays'
         ORDER BY YEAR
    </select>

    <select id="queryReport1MonthList" resultType="java.lang.String">
        SELECT DISTINCT T.YEAR || T.MONTH
         FROM SY_CALENDAR T
        WHERE T.DATE$ BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'YYYY/MM') AND LAST_DAY(TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'YYYY/MM'))
          AND T.NAME = 'National Holidays'
        ORDER BY YEAR || MONTH
    </select>

    <select id="queryReport1WeekList" resultType="java.lang.String">
        SELECT DISTINCT T.YEAR || T.WEEK_NO
          FROM SY_CALENDAR T
         WHERE T.DATE$ BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'YYYY/MM') AND LAST_DAY(TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'YYYY/MM'))
           AND T.NAME = 'National Holidays'
         ORDER BY T.YEAR || T.WEEK_NO
    </select>

    <select id="queryReport1" resultType="java.util.Map">
        with BASE AS (
                SELECT * FROM ${SCPA.OTDM_V} T
                <where>
                    <include refid="filter"/>
                </where>
            ), OTDM_WEEKLY AS (
                SELECT T.*, NVL(T2.RCA_RESULT,T3.RCA_RESULT) RCA_RESULT
                FROM BASE T
                LEFT JOIN OTDM_RCA_FEEDBACK_V T2 ON T.SALES_ORDER_NUMBER = T2.SALES_ORDER_NUMBER AND T.SALES_ORDER_ITEM = T2.SALES_ORDER_ITEM AND T2.RCA_TYPE = 'W' and t.OTDM = 'DELAY'
                LEFT JOIN OTDM_RCA_FEEDBACK_V T3 ON T.SALES_ORDER_NUMBER = T3.SALES_ORDER_NUMBER AND T.SALES_ORDER_ITEM = T3.SALES_ORDER_ITEM AND T3.RCA_TYPE = 'D' and t.OTDM = 'DELAY'
                WHERE t.${CALENDAR_WEEK} in
                        <foreach collection="weeks" separator="," item="item" open="(" close=")">
                             #{item, jdbcType=VARCHAR}
                        </foreach>
            ), OTDM_MONTHLY as (
                SELECT t.*
                FROM BASE t
                WHERE t.${CALENDAR_MONTH} in
                <foreach collection="months" separator="," item="item" open="(" close=")">
                     #{item, jdbcType=VARCHAR}
                </foreach>
            ), OTDM_YEARLY as (
                 SELECT *
                 FROM BASE t
                 WHERE t.${CALENDAR_YEAR} in
                 <foreach collection="years" separator="," item="item" open="(" close=")">
                     #{item, jdbcType=VARCHAR}
                 </foreach>
                 AND t.${CALENDAR_MONTH} in
                <foreach collection="months" separator="," item="item" open="(" close=")">
                     #{item, jdbcType=VARCHAR}
                </foreach>
             )
        SELECT <foreach collection="fields" separator="," item="item">
                   TT."${item}"
               </foreach>,
               <foreach collection="years" separator="," item="item">
                   TT."'${item}'_ONTIME" as "Y${item}_ONTIME",
                   TT."'${item}'_DELAY" as "Y${item}",
                   TT."'${item}'_DELAY" as "Y${item}_DELAY",
                   TT."'${item}'_RATIO" as "Y${item}_RATIO"
               </foreach>,
               <foreach collection="months" separator="," item="item">
                   MM."'${item}'_ONTIME" as "M${item}_ONTIME",
                   MM."'${item}'_DELAY" as "M${item}",
                   MM."'${item}'_DELAY" as "M${item}_DELAY",
                   MM."'${item}'_RATIO" as "M${item}_RATIO"
               </foreach>,
               <foreach collection="weeks" separator="," item="item">
                   WW."'${item}'_ONTIME" as "W${item}_ONTIME",
                   WW."'${item}'_DELAY" as "W${item}",
                   WW."'${item}'_DELAY" as "W${item}_DELAY",
                   WW."'${item}'_RATIO" as "W${item}_RATIO",
                   WW."'${item}'_CONFIRMED" AS "W${item}_CONFIRMED"
               </foreach>
        FROM (
                 SELECT *
                 FROM (select <foreach collection="fields" separator="," item="item">
                                   nvl(t."${item}", 'Others')                  ${item}
                              </foreach>,
                              count(decode(t.${otdmTypeColumn}, 'ON_TIME', 1, null)) ontime,
                              count(decode(t.${otdmTypeColumn}, 'DELAY', 1, null)) delay,
                              t.${CALENDAR_YEAR}                                  years
                       FROM OTDM_YEARLY t
                       group by <foreach collection="fields" separator="," item="item">
                                   nvl(t."${item}", 'Others')
                                </foreach>, t.${CALENDAR_YEAR}
                 ) mm PIVOT ( sum(ontime) ontime, sum(delay) delay, sum(decode((ontime + delay), 0, 0, ontime / (ontime + delay))) ratio
                 FOR years IN
                   <foreach collection="years" separator="," item="item" open="(" close=")">
                       '${item}'
                   </foreach>
                 )) tt
                 LEFT JOIN (
                        SELECT *
                        FROM (SELECT <foreach collection="fields" separator="," item="item">
                                          nvl(t."${item}", 'Others')                  ${item}
                                     </foreach>,
                                     count(decode(t.${otdmTypeColumn}, 'ON_TIME', 1, null)) ontime,
                                     count(decode(t.${otdmTypeColumn}, 'DELAY', 1, null)) delay,
                                     t.${CALENDAR_MONTH}                                 months
                              FROM OTDM_MONTHLY t
                              group by <foreach collection="fields" separator="," item="item">
                                          nvl(t."${item}", 'Others')
                                       </foreach>, t.${CALENDAR_MONTH}
                        ) mm PIVOT ( sum(ontime) ontime, sum(delay) delay, sum(decode((ontime + delay), 0, 0, ontime / (ontime + delay))) ratio
                        FOR months IN
                         <foreach collection="months" separator="," item="item" open="(" close=")">
                                '${item}'
                         </foreach>
                 )) mm ON <foreach collection="fields" separator=" and " item="item">
                              mm."${item}" = tt."${item}"
                          </foreach>
                 LEFT JOIN (
                    SELECT *
                    FROM (SELECT <foreach collection="fields" separator="," item="item">
                                      nvl(t."${item}", 'Others')                  ${item}
                                 </foreach>,
                                 count(decode(t.${otdmTypeColumn}, 'ON_TIME', 1, null)) ontime,
                                 count(decode(t.${otdmTypeColumn}, 'DELAY', 1, null)) delay,
                                 count(t.rca_result)                                    confirmed,
                                 t.${CALENDAR_WEEK}                                  weeks
                          FROM OTDM_WEEKLY t
                          group by <foreach collection="fields" separator="," item="item">
                                      nvl(t."${item}", 'Others')
                                   </foreach>, ${CALENDAR_WEEK}
                    ) mm PIVOT ( sum(ontime) ontime, sum(delay) delay, sum(confirmed) confirmed, sum(decode((ontime + delay), 0, 0, ontime / (ontime + delay))) ratio
                    FOR weeks IN
                           <foreach collection="weeks" separator="," item="item" open="(" close=")">
                               '${item}'
                           </foreach>
                 )) ww ON <foreach collection="fields" separator=" and " item="item">
                              ww."${item}" = tt."${item}"
                          </foreach>
        order by
        <if test="_page.sort != null and _page.sort != ''.toString()">
            ${_page.sort},
        </if>
        "'${defaultOrderColumn}'_DELAY" DESC
        OFFSET 0 ROWS FETCH NEXT 512 ROWS ONLY
    </select>

    <insert id="saveOtdmRCAResult" parameterType="java.util.Map">
        merge into scpa.OTDM_RCA_FEEDBACK t
        using
        (
        <foreach collection="dataList" item="item" separator="union all">
            SELECT #{item.order, jdbcType=VARCHAR} SALES_ORDER_NUMBER,
            #{item.item, jdbcType=VARCHAR} SALES_ORDER_ITEM,
            #{item.rca_result, jdbcType=VARCHAR} RCA_RESULT,
            #{item.rca_comments, jdbcType=VARCHAR} RCA_COMMENTS,
            #{item.type, jdbcType=VARCHAR} RCA_TYPE
            from dual
        </foreach>
        ) s on (t.SALES_ORDER_NUMBER = s.SALES_ORDER_NUMBER and t.SALES_ORDER_ITEM = s.SALES_ORDER_ITEM)
        when matched then
        update set
            t.rca_result = s.rca_result,
            t.rca_comments = s.rca_comments,
            t.rca_type = s.rca_type,
            t.update_date$ = sysdate,
            t.update_by$ = #{session.userid, jdbcType=VARCHAR}
        when not matched then
        insert (SALES_ORDER_NUMBER, SALES_ORDER_ITEM, RCA_RESULT, rca_comments, CREATE_BY$, CREATE_DATE$, RCA_TYPE)
        values (s.sales_order_number, s.sales_order_item, s.rca_result, s.rca_comments, #{session.userid, jdbcType=VARCHAR}, sysdate, s.rca_type)
    </insert>

    <sql id="queryReport1DetailsSQL">
        SELECT NVL(T4.RCA_RESULT, T3.RCA_RESULT)                  "RCA_RESULT",
               T2.RCA_CODE                                        "RCA_TIPS",
               T2.RECOM_RCA_CODE                                  "RECOM_RCA_CODE",
               NVL(T4.RCA_COMMENTS, T3.RCA_COMMENTS)              "RCA_COMMENTS",
               T2.DESCRIPTION                                     "RCA_REMARK",
               T.*
          FROM ${SCPA.OTDM_V} T
          LEFT JOIN OTDM_RCA_V T2 ON T.SALES_ORDER_NUMBER = T2.SALES_ORDER_NUMBER AND
                                      T.SALES_ORDER_ITEM = T2.SALES_ORDER_ITEM AND
                                      T.CALENDAR_WEEK = T2.CALENDAR_WEEK
          LEFT JOIN OTDM_RCA_FEEDBACK_V T3
                  ON T.SALES_ORDER_NUMBER = T3.SALES_ORDER_NUMBER AND T.SALES_ORDER_ITEM = T3.SALES_ORDER_ITEM  AND T3.RCA_TYPE = 'D'
          LEFT JOIN OTDM_RCA_FEEDBACK_V T4
                    ON T.SALES_ORDER_NUMBER = t4.SALES_ORDER_NUMBER and t.SALES_ORDER_ITEM = t4.SALES_ORDER_ITEM  and t4.RCA_TYPE = 'W'
        <where>
            <if test="report1SelectedValues.size() > 1">
                <if test="report1SelectedValues[0] != 'RATIO'.toString()">
                    <choose>
                        <when test="report1SelectedValues[0] == 'ONTIME'.toString() or report1SelectedValues[0] == 'On Time'.toString()">
                            AND T.${otdmTypeColumn} = 'ON_TIME'
                        </when>
                        <when test="report1SelectedValues[0] == 'DELAY'.toString()">
                            AND T.${otdmTypeColumn} = 'DELAY'
                        </when>
                        <when test="report1SelectedValues[0] == 'RATIO'.toString() or report1SelectedValues[0] == 'On Time(%)'.toString()"/>
                        <otherwise>
                            AND T.${otdmTypeColumn} = #{report1SelectedValues[0], jdbcType=VARCHAR}
                        </otherwise>
                    </choose>
                </if>
                <if test="report1DetailsDateColumn != null">
                    AND T.${report1DetailsDateColumn} = #{report1DetailsDateValue, jdbcType=VARCHAR}
                </if>
                <if test="report1SelectedValues.size() > 2">
                    <foreach collection="fields" item="item" index="index">
                        <if test="report1SelectedValues[index + 2] != null and report1SelectedValues[index + 2] != ''.toString() and report1SelectedValues[index + 2] != 'Total'.toString()">
                            <choose>
                                <when test="report1SelectedValues[index + 2] == 'Others'.toString()">
                                    AND (T.${item} = 'Others' or T.${item} is null)
                                </when>
                                <otherwise>
                                    AND T.${item} = #{report1SelectedValues[${index + 2}], jdbcType=VARCHAR}
                                </otherwise>
                            </choose>
                        </if>
                    </foreach>
                </if>
            </if>
            <include refid="filter"/>
        </where>
    </sql>

    <select id="queryReport1DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport2" resultType="java.util.Map">
        with BASE AS (
                SELECT * FROM ${SCPA.OTDM_V} T
                <choose>
                    <when test="report2DateType == 'By Month'.toString()">
                        WHERE t.${CALENDAR_MONTH} in
                        <foreach collection="months" separator="," item="item" open="(" close=")">
                             #{item, jdbcType=VARCHAR}
                        </foreach>
                    </when>
                    <otherwise>
                        WHERE t.${CALENDAR_WEEK} in
                        <foreach collection="weeks" separator="," item="item" open="(" close=")">
                             #{item, jdbcType=VARCHAR}
                        </foreach>
                    </otherwise>
                </choose>
                <if test="report1SelectedValues.size() > 1">
                    <foreach collection="fields" item="item" index="index">
                        <if test="report1SelectedValues[index + 2] != null and report1SelectedValues[index + 2] != ''.toString() and report1SelectedValues[index + 2] != 'Total'.toString()">
                            <choose>
                                <when test="report1SelectedValues[index + 2] == 'Others'.toString()">
                                    AND (T.${item} = 'Others' or T.${item} is null)
                                </when>
                                <otherwise>
                                    AND T.${item} = #{report1SelectedValues[${index + 2}], jdbcType=VARCHAR}
                                </otherwise>
                            </choose>
                        </if>
                    </foreach>
                </if>
                <include refid="filter"/>
        )
        SELECT * FROM (
            SELECT T.${dateColumn} "xAxis", T.${otdmTypeColumn} AS "RANGE", COUNT(1) CNT
            FROM BASE T
            GROUP BY T.${dateColumn}, T.${otdmTypeColumn}

            UNION ALL

            SELECT ONTIME."xAxis", 'ON_TIME_RATIO', ROUND(DECODE(TOTAL.TOTAL, 0, 0, ONTIME.CNT / TOTAL.TOTAL * 100), 1) CNT
            FROM (
                     SELECT T.${dateColumn} "xAxis", COUNT(1) CNT
                       FROM BASE T
                      WHERE T.${otdmTypeColumn} = 'ON_TIME'
                      GROUP BY T.${dateColumn}, T.${otdmTypeColumn}
            ) ONTIME INNER JOIN (
                SELECT T.${dateColumn} "xAxis", COUNT(1) TOTAL
                  FROM BASE T
                    WHERE T.${otdmTypeColumn} IN ('ON_TIME', 'DELAY')
                 GROUP BY T.${dateColumn}
            ) TOTAL ON ONTIME."xAxis" = TOTAL."xAxis"
        ) MM
        ORDER BY MM."xAxis"
    </select>

    <sql id="queryOtdmRcaTipsSql">
	    select 'RCA_TIPS' AS SOURCE, RCA_TIPS_CODE AS CODE, DESCRIPTION, COMPUTING_LOGIC
	    from OTDM_RCA_TIPS
	    union all
	    select 'RCA_CODE' AS SOURCE, RCA_CODE, DESCRIPTION, null
	    from OTDS_RCA_CODE
    </sql>

    <select id="queryOtdmRcaTips" resultType="java.util.LinkedHashMap">
        <include refid="queryOtdmRcaTipsSql"/>
    </select>

    <select id="queryOtdmRcaTipsListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryOtdmRcaTipsSql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryOtdmRcaTipsList" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryOtdmRcaTipsSql"/>
        <include refid="global.select_footer"/>
    </select>
</mapper>
