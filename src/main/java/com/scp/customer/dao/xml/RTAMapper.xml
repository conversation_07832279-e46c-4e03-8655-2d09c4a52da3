<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.customer.dao.IRTADao">

    <sql id="RTAFilter">
        <if test="_filters != null and _filters != ''.toString()">
            AND ${_filters}
        </if>
        <if test="treePathFilter != null and treePathFilter != ''.toString()">
            AND ${treePathFilter}
        </if>
        AND ${dateColumn} BETWEEN TO_DATE(#{reportDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
        AND TO_DATE(#{reportDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
    </sql>

    <resultMap id="report1ResultMap" type="com.scp.customer.bean.RTAReport1Bean">
        <result property="category1" column="CATEGORY1"/>
        <result property="category2" column="CATEGORY2"/>
        <result property="category3" column="CATEGORY3"/>
        <result property="category4" column="CATEGORY4"/>
        <result property="category5" column="CATEGORY5"/>
        <result property="value" column="value"/>
        <association property="tooltips" javaType="com.scp.customer.bean.RTAReport1Tooltips">
            <result property="QUANTITY" column="QUANTITY"/>
            <result property="COST" column="COST"/>
        </association>
    </resultMap>

    <resultMap id="columnMap" type="java.lang.String">
        <result column="COLUMN_NAME"/>
    </resultMap>

    <select id="queryDateColumns" resultMap="columnMap" resultType="java.util.List">
        SELECT COLUMN_NAME
        FROM USER_TAB_COLS
        WHERE TABLE_NAME IN ('MYCP_RTA_V')
          AND DATA_TYPE = 'DATE'
          AND COLUMN_NAME NOT IN ('CREATE_DATE$', 'UPDATE_DATE$')
        ORDER BY COLUMN_NAME
    </select>

    <select id="queryCascader" resultType="java.util.Map">
        SELECT NAME,
               CATEGORY
        FROM SCPA.MYCP_RTA_FILTER_V T
        ORDER BY CATEGORY
    </select>

    <select id="queryReport1" resultMap="report1ResultMap">
        SELECT NVL(${level1}, 'Others') AS CATEGORY1,
               NVL(${level2}, 'Others') AS CATEGORY2,
               NVL(${level3}, 'Others') AS CATEGORY3,
               <if test="level4 != null and level4 != ''.toString()">
                   NVL(${level4}, 'Others') AS CATEGORY4,
               </if>
               <if test="level5 != null and level5 != ''.toString()">
                   NVL(${level5}, 'Others') AS CATEGORY5,
               </if>
               ${valueColumn} AS VALUE
               <if test="tooltipsColumns != null and tooltipsColumns != ''.toString()">
                   ,${tooltipsColumns}
               </if>
        FROM ${SCPA.MYCP_RTA_V} T
        <where>
            <include refid="RTAFilter"/>
        </where>
        GROUP BY ${level1},
                 ${level2},
                 ${level3}
                 <if test="level4 != null and level4 != ''.toString()">,${level4}</if>
                 <if test="level5 != null and level5 != ''.toString()">,${level5}</if>
    </select>


    <select id="queryReport2" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        SELECT * FROM (SELECT ${report2yAxis}                      AS "xAxis"
                              <if test="report2Dimensions != null and  report2Dimensions != ''.toString()">
                                  ,${report2Dimensions}
                              </if>
                        FROM ${SCPA.MYCP_RTA_V} T
                        <where>
                            <include refid="RTAFilter"/>
                        </where>
                        GROUP BY ${report2yAxis}
                        ORDER BY ${report2OrderBy} DESC)
        FETCH NEXT ${report2TopQuantities} ROWS ONLY
    </select>

    <sql id="report2DetailsSQL">
        SELECT * FROM ${SCPA.MYCP_RTA_V} T
        <where>
            <if test="report2SelectedValue != null and report2SelectedValue != ''.toString()">
                AND ${report2yAxis} = #{report2SelectedValue, jdbcType=VARCHAR}
            </if>
            <include refid="RTAFilter"/>
        </where>
    </sql>

    <select id="queryReport2DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="report2DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport2Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report2DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <resultMap id="report3ResultMap" type="com.scp.customer.bean.RTAReport3Bean">
        <result property="CALENDAR_DATE" column="CALENDAR_DATE"/>
        <result property="NAME" column="NAME"/>
        <result property="VALUE" column="VALUE"/>
    </resultMap>

    <sql id="calendarDateCol">
        <choose>
            <when test='reportViewType == "VIEW_BY_DAY".toString()'>
                CALENDAR_DATE
            </when>
            <when test='reportViewType == "VIEW_BY_WEEK".toString()'>
                CALENDAR_WEEK
            </when>
            <when test='reportViewType == "VIEW_BY_MONTH".toString()'>
                CALENDAR_MONTH
            </when>
            <when test='reportViewType == "VIEW_BY_QUARTER".toString()'>
                CALENDAR_QUARTER
            </when>
            <when test='reportViewType == "VIEW_BY_YEAR".toString()'>
                CALENDAR_YEAR
            </when>
            <otherwise>
                CALENDAR_DATE
            </otherwise>
        </choose>
    </sql>

    <sql id="rtaBase">
        WITH RTA_BASE AS (
            SELECT T.*,
                   TO_CHAR(SY.DATE$, 'yyyy-mm-dd')          AS CALENDAR_DATE,
                   SY.YEAR || SY.WEEK_NO                    AS CALENDAR_WEEK,
                   SY.YEAR || SY.MONTH                      AS CALENDAR_MONTH,
                   TO_CHAR(T.${dateColumn}, 'YYYY"Q"Q')     AS CALENDAR_QUARTER,
                   TO_CHAR(SY.YEAR)                         AS CALENDAR_YEAR
            FROM ${SCPA.MYCP_RTA_V}  T
                     LEFT JOIN SY_CALENDAR SY on sy.DATE$ = T.${dateColumn} and
                                                 sy.NAME = 'National Holidays'
        )
    </sql>

    <select id="queryReport3" parameterType="java.util.Map" resultMap="report3ResultMap">
        <include refid="rtaBase"/>
        SELECT
            <include refid="calendarDateCol"/>  AS CALENDAR_DATE,
            NVL(${report3ViewType}, 'Others')   AS NAME,
            NVL(ROUND(${valueColumn}, 2), 0)    AS VALUE
        FROM RTA_BASE T
        <where>
            <include refid="RTAFilter"/>
        </where>
        GROUP BY
            <include refid="calendarDateCol"/>,
            NVL(${report3ViewType}, 'Others')
    </select>

    <sql id="report3DetailsSQL">
        <include refid="rtaBase"/>
        SELECT * FROM RTA_BASE T
        <where>
            <include refid="calendarDateCol"/> = #{report3SelectedDate, jdbcType=VARCHAR}
            <if test="report3SelectedSeriesName != ''.toString() and report3SelectedSeriesName != null">
                AND ${report3ViewType} = #{report3SelectedSeriesName, jdbcType=VARCHAR}
            </if>
            <include refid="RTAFilter"/>
        </where>

    </sql>

    <select id="queryReport3DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="report3DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>


    <select id="queryReport3TotalLine" parameterType="java.util.Map" resultType="BigDecimal">
        <include refid="rtaBase"/>
        SELECT NVL(${valueColumn}, 0)
        FROM RTA_BASE T
        <where>
            CALENDAR_MONTH != TO_CHAR(SYSDATE, 'YYYYMM')
            <include refid="RTAFilter"/>
        </where>
    </select>

    <select id="queryReport3Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report3DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="report4SQL">
        <include refid="rtaBase"/>,
        TEMP AS (
                SELECT * FROM RTA_BASE T
                <where>
                    <include refid="RTAFilter"/>
                </where>
            ),
        BASE AS (
                    SELECT
                        <foreach collection="report4SelectedColumns" item="item">
                            NVL(${item}, 'Others')         AS ${item},
                        </foreach>
                        NVL(<include refid="calendarDateCol"/>, 'Others')     AS CALENDAR_DATE,
                        ROUND(${valueColumn}, 3)                              AS TOTAL
                    FROM TEMP t
                    GROUP BY
                        <foreach collection="report4SelectedColumns" item="item" separator="," close=",">
                            NVL(${item}, 'Others')
                        </foreach>
                        NVL(<include refid="calendarDateCol"/>, 'Others')
                    UNION ALL
                    SELECT <foreach collection="report4SelectedColumns" item="item">
                                NVL(${item}, 'Others')         AS ${item},
                            </foreach>
                            'Total',
                            ROUND(${valueColumn}, 3)                              AS TOTAL
                    FROM TEMP T
                    GROUP BY <foreach collection="report4SelectedColumns" item="item" separator=",">
                                NVL(${item}, 'Others')
                             </foreach>
                 )
        SELECT * FROM BASE MM
            PIVOT (
                SUM(TOTAL) AS TOTAL
                FOR CALENDAR_DATE
                    IN (
                    <foreach collection="report4ColumnNames" separator="," item="item">
                        '${item}'
                    </foreach>)
                  )
        ORDER BY
        <foreach collection="report4SelectedColumns" item="item" separator=",">
            DECODE(${item}, 'Others', 'zzz', ${item})
        </foreach>
    </sql>

    <select id="queryReport4Columns" resultType="java.lang.String">
        <include refid="rtaBase"/>
        SELECT * FROM (SELECT DISTINCT <include refid="calendarDateCol"/> AS CALENDAR
                        FROM RTA_BASE t
                        <where>
                            <include refid="RTAFilter"/>
                        </where>
                        UNION SELECT 'Total' from dual
                        )
        ORDER BY CALENDAR DESC
        OFFSET 0 ROWS FETCH NEXT 30 ROWS ONLY
    </select>

    <select id="queryReport4" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report4SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="report4DetailsSQL">
        <include refid="rtaBase"/>
        SELECT * FROM RTA_BASE T
        <where>
            <if test="report4SelectedDate != null and report4SelectedDate != ''.toString() and report4SelectedDate != 'Total'.toString()">
                <include refid="calendarDateCol"/> = #{report4SelectedDate, jdbcType=VARCHAR}
            </if>
            <include refid="RTAFilter"/>
            <foreach collection="report4SelectedColumns" item="item" index="index">
                <if test="report4SelectedValues[index] != null and report4SelectedValues[index] != ''.toString()">
                    <if test="report4SelectedValues[index] == 'Others'">
                        AND t.${item} IS NULL
                    </if>
                    <if test="report4SelectedValues[index] != 'Others'">
                        AND t.${item} = #{report4SelectedValues[${index}], jdbcType=VARCHAR}
                    </if>
                </if>
            </foreach>
        </where>
    </sql>

    <select id="queryReport4DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="report4DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport4Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="report4DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>


    <select id="queryReport5" resultType="java.util.Map">
        <include refid="rtaBase"/>
        SELECT SUM(NVL(QUANTITY,0))                           AS TOTAL_QTY,
               SUM(DECODE(JVFINDT, null, 0, NVL(QUANTITY,0))) AS FINISHED_QTY,
               AVG(RTA_OCT)                                   AS AVG_FINISH_DAYS,
               SUM(DECODE(JVFINDT, null, NVL(QUANTITY,0), 0)) AS PENDING_QUANTITY,
               AVG(TOTAL_TAT)                                 AS AVG_TAT,
               COUNT(1)                                       AS TOTAL_LINES
        FROM RTA_BASE T
        <where>
            <include refid="RTAFilter"/>
        </where>
    </select>

    <sql id="queryReport5DetailsSQL">
        <include refid="rtaBase"/>
        SELECT *
        FROM RTA_BASE T
        <where>
            <include refid="RTAFilter"/>
        </where>
    </sql>

    <select id="queryReport5DetailsCount" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport5DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport5Details" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport5DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>
</mapper>
