<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.customer.dao.IOTCDao">
    <sql id="otc_filter">
        <if test="_filters != null and _filters != ''.toString()">
            and ${_filters}
        </if>
    </sql>

    <sql id="queryOtcWeeklySQL">
        with OTC_WEEKLY as (
                      select t.*
                        from ${SCPA.OPM_OTC_DATA_V} t
                       where TO_DATE(t.FIRST_CONFIRMED_GI,'YYYY/MM/DD') between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm') - 7 and last_day(to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
                             <include refid="otc_filter"/>
                  ),
             OTC_YEARLY as (
                      select t.*
                        from ${SCPA.OPM_OTC_DATA_V} t
                        where TO_DATE(t.FIRST_CONFIRMED_GI,'YYYY/MM/DD') between TRUNC(to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm'),'YYYY') and last_day(to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
                             <include refid="otc_filter"/>
                  )

        select <foreach collection="field" item="item">
                    tt.${item},
               </foreach>
               <if test="field.contains('ENTITY') or field.contains('MRP_CONTROLLER')">
                    tt.order_field,
               </if>
               <foreach collection="weeks" separator="," item="item">
                    mm."'${item}'_FAIL",
                    mm."'${item}'_ONTIME",
                    mm."'${item}'_RATIO"
               </foreach>
               <foreach collection="months" separator="," item="item" open=",">
                    nn."'${item}'_FAIL",
                    nn."'${item}'_ONTIME",
                    nn."'${item}'_RATIO"
               </foreach>
               <foreach collection="years" separator="," item="item" open=",">
                    tt."'${item}'_FAIL",
                    tt."'${item}'_ONTIME",
                    tt."'${item}'_RATIO"
               </foreach>
        from (
                 select *
                 from (select
                        <foreach collection="field" item="item">
                              nvl(t.${item}, 'Others')             ${item},
                        </foreach>
                        <if test="field.contains('ENTITY') or field.contains('MRP_CONTROLLER')">
                              nvl(max(t.cluster_name), 'Others')    order_field,
                        </if>
                              count(decode(t.OTC, '0', 1, null)) fail,
                              count(decode(t.OTC, '100', 1, null)) ontime,
                              to_char(TO_DATE(t.FIRST_CONFIRMED_GI,'YYYY/MM/DD'), 'yyyy"YTD"')      years
                       from OTC_YEARLY t
                       group by
                       <foreach collection="field" item="item">
                             t.${item},
                       </foreach>
                       to_char(to_date(t.FIRST_CONFIRMED_GI,'YYYY/MM/DD'), 'yyyy"YTD"')) mm
                     PIVOT (
                         sum(ontime) ontime,
                         sum(fail) fail,
                         sum(decode((ontime + fail), 0, 0, ontime / (ontime + fail))) ratio FOR years
                     IN (
                        <foreach collection="years" separator="," item="item">
                            '${item}'
                        </foreach>
                        )
                     )
             ) tt
                left join
             (
                 select *
                 from (select
                        <foreach collection="field" item="item">
                              nvl(t.${item}, 'Others')             ${item},
                        </foreach>
                        <if test="field.contains('ENTITY') or field.contains('MRP_CONTROLLER')">
                              nvl(max(t.cluster_name), 'Others')    order_field,
                        </if>
                              count(decode(t.OTC, '0', 1, null)) fail,
                              count(decode(t.OTC, '100', 1, null)) ontime,
                              to_char(TO_DATE(t.FIRST_CONFIRMED_GI,'YYYY/MM/DD'), 'YY"W"')|| WEEK WEEKS
                       from OTC_WEEKLY t
                       group by
                       <foreach collection="field" item="item">
                             t.${item},
                       </foreach>
                       to_char(TO_DATE(t.FIRST_CONFIRMED_GI,'YYYY/MM/DD'), 'YY"W"') || WEEK) mm
                     PIVOT (
                         sum(ontime) ontime,
                         sum(fail) fail,
                         sum(decode((ontime + fail), 0, 0, ontime / (ontime + fail))) ratio FOR weeks
                     IN (
                        <foreach collection="weeks" separator="," item="item">
                            '${item}'
                        </foreach>
                        )
                     )
             ) mm on
                <foreach collection="field" item="item" separator="and">
                    mm.${item} = tt.${item}
                </foreach>

                 left join
             (
                 select *
                 from (select
                        <foreach collection="field" item="item">
                              nvl(t.${item}, 'Others')             ${item},
                        </foreach>
                        <if test="field.contains('ENTITY') or field.contains('MRP_CONTROLLER')">
                              nvl(max(t.cluster_name), 'Others')    order_field,
                        </if>
                              count(decode(t.OTC, '0', 1, null))                    fail,
                              count(decode(t.OTC, '100', 1, null))                    ontime,
                        to_char(TO_DATE(t.FIRST_CONFIRMED_GI,'YYYY/MM/DD'), 'MON-YY', 'NLS_DATE_LANGUAGE=AMERICAN') months
                       from OTC_WEEKLY t
                       group by
                       <foreach collection="field" item="item">
                             t.${item},
                       </foreach> to_char(TO_DATE(t.FIRST_CONFIRMED_GI,'YYYY/MM/DD'), 'MON-YY', 'NLS_DATE_LANGUAGE=AMERICAN')) mm
                     PIVOT (
                         sum(ontime) ontime,
                         sum(fail) fail,
                         sum(decode((ontime + fail), 0, 0, ontime / (ontime + fail))) ratio FOR months
                     IN (
                        <foreach collection="months" separator="," item="item">
                            '${item}'
                        </foreach>
                        )
                     )
             ) nn on
                <foreach collection="field" item="item" separator="and">
                    nn.${item} = tt.${item}
                </foreach>
    </sql>

    <select id="queryOtcWeekly" parameterType="java.util.Map" resultType="java.util.Map">
        <include refid="queryOtcWeeklySQL"/>
        order by
        <if test="_page.sort != null and _page.sort != ''.toString()">
            ${_page.sort},
        </if>
        "'${defaultOrderColumn}'_FAIL" + "'${defaultOrderColumn}'_ONTIME" DESC
        OFFSET 0 ROWS FETCH NEXT 65535 ROWS ONLY
    </select>


    <sql id="queryOtcWeeklyDetailsSql">
      select
                CLIENT_ID,
                SALES_ORG,
                DISTR_CH,
                PLANT_CODE,
                PLANT_ENTITY_GROUP,
                SALES_DOC,
                ITEM,
                COMPLETED,
                SOLD_TO,
                SOLD_TO_NAME,
                SHIP_TO,
                FIRST_GI,
                LAST_GI,
                FIRST_AVAILABILITY,
                MATERIAL,
                OPM_PRODUCT_LINE,
                FLOW_TYPE,
                MRP_CONTROLLER,
                VENDOR,
                VENDOR_NAME,
                VENDOR_ENTITY_GROUP,
                VENDOR_LOGISTIC_TERRITORY,
                VENDOR_CLUSTER,
                VENDOR_COUNTRY,
                SHIP_TO_COUNTRY,
                VENDOR_REGION,
                VENDOR_TYPE,
                ORDER_TYPE,
                STATUS,
                OLD_BLK,
                SALES_GROUP,
                SALES_OFFICE,
                PURCHASING_GROUP,
                PURCH_GROUP_DESC,
                MATERIAL_NOTICE,
                SO_ITEM_COMMENTS,
                LOQ_RULE,
                MANUFACTURER,
                SHORTAGE_TYPE,
                SHIPPING_POINT,
                ROUTE,
                CUSTOMER_PO_NUMBER,
                CUSTOMER_PO_ITEM,
                PO_PTO,
                PO_ITEM_PTO,
                DELIVERY_NUMBER,
                DELIVERY_ITEM,
                DELIVERY_CREATION_DATE,
                GDP,
                SHORTAGE_EVENT_ID,
                CURRENT_BLOCK,
                CURRENT_BLK_DESCRIPTION,
                MANUFACTURER_MATERIAL,
                CLUSTER_MATERIAL,
                CENTRAL_STOCK,
                SPECIFIC_SCOPE,
                OTD_EXCLUSION,
                ACTUAL_GI_DATE,
                VIP_CODE,
                VIP_DESCRIPTION,
                PERSONA_CODE,
                PERSONA_DESCRIPTION,
                PERSONA_NAME,
                MRP_CONTROLLER_DESCRIPTION,
                FORWARD_ORDER,
                DTO_RELEVANT,
                STOCK_RESERVATION,
                ATP_RULE,
                CAPACITY_TYPE,
                SHIPMENT,
                OPM_PLANT_TYPE,
                SCU,
                PRODUCT_FAMILY,
                LINE_OF_BUSINESS,
                SHIP_TO_COUNTRY_NAME,
                SHIP_TO_COUNTRY_ZONE,
                OPM_DELIVERY_PRIORITY,
                CURRENCY,
                SALES_UNIT,
                MATERIAL_BASE_UOM,
                GSC_REGION,
                SHORTAGE_EVENT_NAME,
                SHORTAGE_EVENT_METHOD,
                SHORTAGE_EVENT_TYPE,
                SO_CREATED_DATE,
                CUSTOMER_REQUESTED_DATE,
                FINAL_CUSTOMER_NAME,
                SHIPPING_CONDITIONS,
                SHIPPING_CONDITIONS_2,
                C2_COMMUNICATION_DATE,
                SD_DOCUMENT_CURRENCY,
                ULTIMATE_PARENT_NAME,
                ULTIMATE_PARENT_GOLD,
                CUSTOMER_GOLDEN_ID,
                CUSTOMER_NAME_IN_ERP,
                MARKET_SEGMENT_L1,
                MKT_SUB_SEGMENT_L2,
                CUSTOMER_CLASS_L1,
                CUSTOMER_CLASS_L2,
                BFO_KEY_ACCOUNT_TYPE,
                BFO_VIP_STATUS,
                PLANT_CLUSTER,
                SUB_REGION,
                FIRST_CONF_DELIVERY_DATE,
                FIRST_CONFIRMED_GI,
                WEEK,
                DELAYED_OR_NOT,
                DELAYED_OR_NOT_ORIGINAL,
                OTD_FORCED,
                OTD_FORCED_CREATED_ON,
                PENDING_QTY,
                DELAY_NB_DAYS,
                DELAY_NB_IT,
                DELAY_NB_IT_ORIGINAL,
                NET_VALUE,
                OTC,
                TOTAL_QTY_IN_SALES_UNIT,
                TOTAL_QTY_IN_BASE_UOM,
                TOTAL_VALUE_IN_STANDARD_PRICE,
                DELIVERY_QUANTITY,
                ACTUAL_GI_QUANTITY,
                NET_VALUE_IN_LOCAL_CURRENCY,
                ENTITY,
                BU,
                CLUSTER_NAME,
                GRA_TYPE,
                GRA_EVENT_NAME,
                GRA_BTN,
                DSS_PRODUCT_LINE,
                AVAILABILITY_CHECK,
                OTDS_DELIVERY_PRIORITY,
                FULFILL_OR_NOT_NONBLOCK,
                FULFILL_OR_NOT_UNRESTRICT,
                GRA_STATUS,
                OTDS_PLANT_TYPE,
                SHIP_TO_SHORT_NAME,
                SHIP_TO_FULL_NAME,
                SHIP_TO_PARENT_NAME,
                SHIP_TO_PARENT_CODE,
                SHIP_TO_CITY,
                STOCKING_POLICY,
                PLANT_NAME,
                BUSINESS_UNIT,
                LOCAL_BUSINESS_UNIT,
                OTDS_BU,
                LOCAL_BU,
                LOCAL_PRODUCT_FAMILY,
                LOCAL_PRODUCT_LINE,
                LOCAL_PRODUCT_SUBFAMILY,
                OTDS_CLUSTER_NAME,
                MATERIAL_OWNER_NAME,
                MATERIAL_OWNER_SESA,
                SOURCE_CATEGORY,
                SOLD_TO_REGION,
                SOLD_TO_SHORT_NAME,
                SOLD_TO_FULL_NAME,
                SOLD_TO_PARENT_NAME,
                SOLD_TO_PARENT_CODE,
                SO_BLOCK_STATUS,
                SO_BLOCK_REASON,
                VIP_BU,
                VIP_VIP_NAME,
                VIP_REGION,
                VIP_SEGMENT_L2,
                VIP_CUSTOMER_TYPE,
                VIP_VIP_TYPE,
                VIP_DIRECT_INDIRECT,
                VIP_EU,
                VIP_VCP_OEM,
                VIP_PROJECT_NAME,
                VIP_SO_INDICATOR,
                PRODUCT_GROUP_A,
                PRODUCT_GROUP_B,
                PRODUCT_GROUP_C,
                PRODUCT_GROUP_D,
                PRODUCT_GROUP_E
        from ${SCPA.OPM_OTC_DATA_V} T
        where
        <choose>
            <when test="selectedDateType == 'month'.toString()">
                to_char(TO_DATE(t.FIRST_CONFIRMED_GI, 'YYYY/MM/DD'), 'MON-YY', 'NLS_DATE_LANGUAGE=AMERICAN') = #{selectedDate, jdbcType=VARCHAR}
                and TO_DATE(t.FIRST_CONFIRMED_GI, 'YYYY/MM/DD') between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm') and last_day(to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
            </when>
            <when test="selectedDateType == 'week'.toString()">
                to_char(TO_DATE(t.FIRST_CONFIRMED_GI, 'YYYY/MM/DD'), 'YY') ||'W' ||t.WEEK = #{selectedDate, jdbcType=VARCHAR}
                and TO_DATE(t.FIRST_CONFIRMED_GI, 'YYYY/MM/DD') between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm') - 7 and last_day(to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
            </when>
            <when test="selectedDateType == 'year'.toString()">
                to_char(TO_DATE(t.FIRST_CONFIRMED_GI, 'YYYY/MM/DD'), 'yyyy') = #{selectedDate, jdbcType=VARCHAR}
                and TO_DATE(t.FIRST_CONFIRMED_GI, 'YYYY/MM/DD') between TRUNC(to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm'),'YYYY') and last_day(to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
            </when>
            <when test="selectedDateType == 'all'.toString()">
                TO_DATE(t.FIRST_CONFIRMED_GI, 'YYYY/MM/DD') between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm') and last_day(to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
            </when>
            <when test="selectedDateType == 'weekly'.toString()">
                TO_DATE(t.FIRST_CONFIRMED_GI, 'YYYY/MM/DD') between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') and to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
            </when>
            <otherwise> 1 = 1 </otherwise>
        </choose>
        <if test="selectedType == 'On Time'.toString()">
            and t.OTC = '100'
        </if>
        <if test="selectedType == 'Delay'.toString()">
            and t.OTC = '0'
        </if>
        <include refid="otc_filter"/>
        <foreach collection="field" item="item" index="index">
            <choose>
                <when test="selectedField[index] == 'Others'.toString()">
                    and (${item} is null or ${item} = 'Others')
                </when>
                <otherwise>
                    <if test="selectedField[index] != 'Total'.toString() and selectedField[index] != null">
                        and ${item} = #{selectedField[${index}], jdbcType=VARCHAR}
                    </if>
                </otherwise>
            </choose>
        </foreach>
    </sql>

    <select id="queryOtcWeeklyDetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryOtcWeeklyDetailsSql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryOtcWeeklyDetails" parameterType="java.util.Map" resultType="java.util.Map">
        <include refid="global.select_header"/>
        <include refid="queryOtcWeeklyDetailsSql"/>
        <include refid="global.select_footer"/>
    </select>


    <select id="downloadOtcWeeklyDetails" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryOtcWeeklyDetailsSql"/>
        <include refid="global.select_footer"/>
    </select>


    <select id="queryOtcWeeklySummary" resultType="java.util.Map">
        with OTC_WEEKLY as (
                  select t.*,nvl(t2.rca_result,t3.rca_result) rca_result
                    from ${SCPA.OPM_OTC_DATA_V} t
                   where TO_DATE(t.FIRST_CONFIRMED_GI, 'YYYY/MM/DD') between to_date(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm') and last_day(to_date(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
                         <include refid="otc_filter"/>
              )

        <include refid="global.select_header"/>
            select <foreach collection="field" item="item">
                        mm.${item},
                   </foreach>
                   <foreach collection="weeks" separator="," item="item">
                        mm."'${item}'_FAIL"
                   </foreach>
            from (
                     select *
                     from (select <foreach collection="field" item="item">
                                      nvl(t.${item}, 'Others')             ${item},
                                  </foreach>
                                  count(decode(t.ontime, '#', 1, null)) fail,
                                  count(t.rca_result) confirmed,
                                  to_char(TO_DATE(t.FIRST_CONFIRMED_GI, 'YYYY/MM/DD'), 'YY"W"') || WEEK     weeks
                           from OTC_WEEKLY t
                           group by
                           <foreach collection="field" item="item">
                                t.${item},
                           </foreach>
                           to_char(TO_DATE(t.FIRST_CONFIRMED_GI, 'YYYY/MM/DD'), 'YY"W"') || WEEK) mm
                         PIVOT (
                             sum(fail) fail,
                             sum(confirmed) confirmed FOR weeks
                         IN (
                            <foreach collection="weeks" separator="," item="item">
                                '${item}'
                            </foreach>
                            )
                         )
                 ) mm
        <include refid="global.select_footer"/>
    </select>

    <select id="queryOtcWeeklyWeekColumns" resultType="java.lang.String">
        SELECT WEEK_NO
          FROM (
                 SELECT DISTINCT SUBSTR(T.YEAR, 3 ,2) || 'W' || T.WEEK_NO WEEK_NO
                 FROM SY_CALENDAR T
                 WHERE T.NAME = 'National Holidays'
                   AND T.DATE$ BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm') AND LAST_DAY(TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
         ) T
         ORDER BY WEEK_NO DESC
    </select>

    <select id="queryOtcWeeklyMonthColumns" resultType="java.lang.String">
        SELECT MONTH
          FROM (
                 SELECT DISTINCT TO_CHAR(t.date$, 'MON-YY', 'NLS_DATE_LANGUAGE=AMERICAN') MONTH, T.YEAR || t.MONTH AS MONTH_ORDER
                 FROM SY_CALENDAR T
                 WHERE T.NAME = 'National Holidays'
                   AND T.DATE$ BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm') AND LAST_DAY(TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
             ) T
          ORDER BY MONTH_ORDER DESC
    </select>

    <select id="queryOtcWeeklyYearColumns" resultType="java.lang.String">
        SELECT T.YEAR
          FROM (
                 SELECT DISTINCT T.YEAR || 'YTD' YEAR
                 FROM SY_CALENDAR T
                 WHERE T.NAME = 'National Holidays'
                   AND T.DATE$ BETWEEN TO_DATE(#{dateRange[0], jdbcType=VARCHAR}, 'yyyy/mm') AND LAST_DAY(TO_DATE(#{dateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
         ) T
         ORDER BY T.YEAR DESC
    </select>
</mapper>
