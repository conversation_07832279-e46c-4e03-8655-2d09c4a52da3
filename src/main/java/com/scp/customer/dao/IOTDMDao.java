package com.scp.customer.dao;

import org.apache.ibatis.annotations.Mapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IOTDMDao {

    List<Map<String, String>> queryCascader();

    List<String> queryRacCode();

    List<LinkedHashMap<String, Object>> queryOtdmRcaTips();

    List<String> queryReport1YearList(Map<String, Object> parameterMap);

    List<String> queryReport1MonthList(Map<String, Object> parameterMap);

    List<String> queryReport1WeekList(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport1(Map<String, Object> parameterMap);

    int queryReport1DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1Details(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport2(Map<String, Object> parameterMap);

    int queryOtdmRcaTipsListCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryOtdmRcaTipsList(Map<String, Object> parameterMap);

    void saveOtdmRCAResult(Map<String, Object> parameterMap);
}
