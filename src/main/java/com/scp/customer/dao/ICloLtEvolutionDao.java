package com.scp.customer.dao;

import com.scp.customer.bean.CloLtEvolutionReport1Bean;
import com.scp.customer.bean.CloLtEvolutionReport2Bean;
import org.apache.ibatis.annotations.Mapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface ICloLtEvolutionDao {

    List<Map<String, String>> queryCascader();

    List<CloLtEvolutionReport1Bean> queryReport1(Map<String, Object> parameterMap);

    List<CloLtEvolutionReport2Bean> queryReport2(Map<String, Object> parameterMap);

    int queryReport2DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport2Details(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3(Map<String, Object> parameterMap);

    List<String> queryReport3Columns(Map<String, Object> parameterMap);

    int queryReport3DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3Details(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport4(Map<String, Object> parameterMap);

    int queryReport4DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport4Details(Map<String, Object> parameterMap);
}
