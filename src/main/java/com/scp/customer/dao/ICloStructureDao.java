package com.scp.customer.dao;

import org.apache.ibatis.annotations.Mapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface ICloStructureDao {

    List<Map<String, String>> queryFilters(Map<String, Object> parameterMap);

    int queryReport1Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> downloadReport1Details(Map<String, Object> parameterMap);

    List<String> queryPivotOpts(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport2(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport2Sub(Map<String, Object> parameterMap);

    int queryReport2DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport2Details(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport3(Map<String, Object> parameterMap);

    int queryReport3DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3Details(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryImprovTips();

    List<String> queryAvailableColumns();

    List<LinkedHashMap<String, Object>> downloadReport1Evidence(Map<String, Object> param);

    List<String> queryReport1AvailableTips();

    void saveReport1Comments(Map<String, Object> parameterMap);

    List<String> queryCloVersionOpts(Map<String, Object> parameterMap);
}
