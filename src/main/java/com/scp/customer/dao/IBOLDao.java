package com.scp.customer.dao;

import com.scp.customer.bean.BOLUpload;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IBOLDao {

    List<Map<String, String>> queryCascader();

    List<String> queryReport1Columns(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport1(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport1Sub(Map<String, Object> parameterMap);

    int queryReport1DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1Details(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport3(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport2(Map<String, Object> parameterMap);

    int queryReport3DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3Details(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport4(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport4Sub(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport5(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport6(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport6Legend(Map<String, Object> parameterMap);

    void insertBolReportDataTemp(@Param("list") List<BOLUpload> data);

    void mergeBolData(String userid);
}
