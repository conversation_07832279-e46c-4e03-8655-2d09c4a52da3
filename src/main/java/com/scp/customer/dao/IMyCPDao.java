package com.scp.customer.dao;

import org.apache.ibatis.annotations.Mapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IMyCPDao {

    List<String> queryFilterDateColumns(Map<String, Object> parameterMap);

    List<Map<String, String>> queryCascader(Map<String, Object> parameterMap);

    List<String> queryPivotOpts(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport1(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport1Sub(Map<String, Object> parameterMap);

    int queryReport1DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1Details(Map<String, Object> parameterMap);

    List<String> queryReport2XAxis(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport2YAxis(Map<String, Object> parameterMap);

    Map<String, Object> queryReport2Line(Map<String, Object> parameterMap);

    int queryReport2DetailsCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport2Details(Map<String, Object> parameterMap);

    List<String> queryReport3DateColumns(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport3(Map<String, Object> parameterMap);

    int queryReport3DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3Details(Map<String, Object> parameterMap);

    void saveReport3Details(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3RCADesc(Map<String, Object> parameterMap);
}
