package com.scp.customer.dao;

import org.apache.ibatis.annotations.Mapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IOTDSDao {

    List<Map<String, Object>> queryOtdsWeekly(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryOtdsDaily(Map<String, Object> parameterMap);

    int queryOtdsDailyDetailsCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryOtdsDailyDetails(Map<String, Object> parameterMap);

    List<String> queryRacCode();

    void saveOtdsRCAResult(Map<String, Object> parameterMap);

    int queryOtdsWeeklyDetailsCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryOtdsWeeklyDetails(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryOtdsRcaTips();

    List<Map<String, Object>> queryOtdsDailyChart(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryOtdsWeeklyChart(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryOtdsWeeklyChart2(Map<String, Object> parameterMap);

    int queryOtdsRcaTipsListCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryOtdsRcaTipsList(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryOtdsWeeklySummary(Map<String, Object> parameterMap);

    List<String> queryOtdsWeeklyWeekColumns(Map<String, Object> parameterMap);

    List<String> queryOtdsWeeklyMonthColumns(Map<String, Object> parameterMap);

    List<String> queryOtdsWeeklyYearColumns(Map<String, Object> parameterMap);

    int queryOtdsTipsDescriptionCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryOtdsTipsDescription(Map<String, Object> parameterMap);
}
