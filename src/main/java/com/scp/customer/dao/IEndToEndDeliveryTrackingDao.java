package com.scp.customer.dao;

import com.scp.customer.bean.EndToEndDeliveryTrackingReport1Bean;
import com.scp.customer.bean.EndToEndDeliveryTrackingReport3Bean;
import com.starter.context.bean.scptable.ScpTableCell;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IEndToEndDeliveryTrackingDao {

    List<Map<String, String>> queryCascader();

    List<EndToEndDeliveryTrackingReport1Bean> queryReport1(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport2(Map<String, Object> parameterMap);

    List<EndToEndDeliveryTrackingReport3Bean> queryReport3(Map<String, Object> parameterMap);

    Map<String, Object> queryReport3Line(Map<String, Object> parameterMap);

    int queryReport3DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3Details(Map<String, Object> parameterMap);

    int queryReport4Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport4(Map<String, Object> parameterMap);

    int queryReport4DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport4Details(Map<String, Object> parameterMap);

    Map<String, BigDecimal> queryReport5(Map<String, Object> parameterMap);

    int queryReport5DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport5Details(Map<String, Object> parameterMap);

    String queryPageAdmin(@Param("userid") String userid, @Param("parentCode") String parentCode);

    int queryConfigurationCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryConfiguration(Map<String, Object> parameterMap);

    int createConfigurationByTable(@Param("headers") List<String> headers, @Param("creates") List<Map<String, Object>> creates, @Param("userid") String userid);

    int deleteConfigurationByTable(@Param("deletes") List<String> deletes, @Param("userid") String userid, @Param("isAdmin") boolean isAdmin);

    int updateConfigurationByTable(@Param("plantCode") String plantCode, @Param("updates") List<ScpTableCell> updates, @Param("userid") String userid, @Param("isAdmin") boolean isAdmin);

    int queryCotConfigurationCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryCotConfiguration(Map<String, Object> parameterMap);

    int createCotConfigurationByTable(@Param("headers") List<String> headers, @Param("creates") List<Map<String, Object>> creates, @Param("userid") String userid);

    int deleteCotConfigurationByTable(@Param("deletes") List<String> deletes, @Param("userid") String userid, @Param("isAdmin") boolean isAdmin);

    int updateCotConfigurationByTable(@Param("rowId") String rowId, @Param("updates") List<ScpTableCell> updates, @Param("userid") String userid, @Param("isAdmin") boolean isAdmin);


    List<Map<String, Object>> queryRCAWeekly(Map<String, Object> parameterMap);

    List<String> queryRCACode();

    List<String> queryWeekOpts();

    void saveRCAResult(Map<String, Object> parameterMap);

    int queryRCAWeeklyDetailsCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryRCAWeeklyDetails(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryRCATips();

    int queryRCATipsListCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryRCATipsList(Map<String, Object> parameterMap);

    List<String> queryRCAWeeklyWeekColumns(Map<String, Object> parameterMap);

    List<String> queryRCAWeeklyMonthColumns(Map<String, Object> parameterMap);

    List<String> queryRCAWeeklyYearColumns(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryManualD0(Map<String, Object> parameterMap);

    List<Map<String, String>> queryManualD0Cascader();

    List<String> queryReport2AvailableColumns();

    int queryManualD0Count(Map<String, Object> parameterMap);

    int createManualD0(@Param("headers") List<String> headers, @Param("creates") List<Map<String, Object>> creates, @Param("userid") String userid);

    int deleteManualD0(@Param("deletes") List<String> deletes, @Param("userid") String userid, @Param("isAdmin") boolean isAdmin);

    int updateManualD0(@Param("primaryKey") String primaryKey, @Param("updates") List<ScpTableCell> updates, @Param("userid") String userid, @Param("isAdmin") boolean isAdmin);

    List<LinkedHashMap<String, Object>> queryManualD0Details(Map<String, Object> parameterMap);

    int queryManualD0DetailsCount(Map<String, Object> parameterMap);

}
