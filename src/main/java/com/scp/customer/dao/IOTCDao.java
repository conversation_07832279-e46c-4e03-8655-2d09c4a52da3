package com.scp.customer.dao;

import org.apache.ibatis.annotations.Mapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IOTCDao {

    List<Map<String, Object>> queryOtcWeekly(Map<String, Object> parameterMap);


    int queryOtcWeeklyDetailsCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryOtcWeeklyDetails(Map<String, Object> parameterMap);

/*
    List<Map<String, Object>> queryOtdsWeeklySummary(Map<String, Object> parameterMap);

 */

    List<String> queryOtcWeeklyWeekColumns(Map<String, Object> parameterMap);

    List<String> queryOtcWeeklyMonthColumns(Map<String, Object> parameterMap);

    List<String> queryOtcWeeklyYearColumns(Map<String, Object> parameterMap);

}
