package com.scp.customer.dao;

import com.scp.customer.bean.AC2Report1Bean;
import org.apache.ibatis.annotations.Mapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IAC2Dao {

    List<Map<String, String>> queryCascader(Map<String, Object> parameterMap);

    String queryCurrentWeek(Map<String, Object> parameterMap);

    List<String> queryWeekList(Map<String, Object> parameterMap);

    List<AC2Report1Bean> queryReport1(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport1Level1Slope(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport1Level2Slope(Map<String, Object> parameterMap);

    List<String> queryReport2XAxis(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport2YAxis(Map<String, Object> parameterMap);

    Map<String, Object> queryReport2Line(Map<String, Object> parameterMap);

    int queryReport2DetailsCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport2Details(Map<String, Object> parameterMap);

    List<String> queryReport3YearList(Map<String, Object> parameterMap);

    List<String> queryReport3MonthList(Map<String, Object> parameterMap);

    List<String> queryReport3WeekList(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport3(Map<String, Object> parameterMap);

    int queryReport3DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3Details(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryAc2RcaTips(Map<String, Object> parameterMap);

    List<String> queryRacCode(Map<String, Object> parameterMap);

    void saveReport3Details(Map<String, Object> parameterMap);
}
