package com.scp.customer.dao;

import com.scp.customer.bean.E2ELTAnalysisReport1Bean;
import com.scp.customer.bean.E2ELTAnalysisReport2Bean;
import com.scp.customer.bean.E2ELTOverviewReport2Bean;
import com.scp.inventory.bean.ExcessSOStockReport2Bean;
import org.apache.ibatis.annotations.Mapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IE2ELTAnalysisDao {

    List<Map<String, String>> queryCascader();

    List<E2ELTAnalysisReport1Bean> queryReport1(Map<String, Object> parameterMap);

    List<E2ELTOverviewReport2Bean> queryReport2(Map<String, Object> parameterMap);

    int queryReport2DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport2Details(Map<String, Object> parameterMap);

    int queryReport3Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3(Map<String, Object> parameterMap);

    int queryReport3DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3Details(Map<String, Object> parameterMap);

    List<Map<String, String>> queryAnalysisCascader(Map<String, Object> parameterMap);

    List<String> queryAnalysisPivotOpts(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryAnalysisReport1(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryAnalysisReport1Sub(Map<String, Object> parameterMap);

    int queryAnalysisReport1DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryAnalysisReport1Details(Map<String, Object> parameterMap);

    List<E2ELTAnalysisReport2Bean> queryAnalysisReport2(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryAnalysisReport2Line(Map<String, Object> parameterMap);

    int queryAnalysisReport2DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryAnalysisReport2Details(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryAnalysisReport3(Map<String, Object> parameterMap);

    List<String> queryAnalysisReport3Columns(Map<String, Object> parameterMap);

    int queryAnalysisReport3DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryAnalysisReport3Details(Map<String, Object> parameterMap);

}
