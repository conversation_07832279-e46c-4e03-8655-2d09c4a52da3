package com.scp.customer.dao;

import org.apache.ibatis.annotations.Mapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface INORDao {

    List<Map<String, String>> queryNorCascader();

    List<LinkedHashMap<String, Object>> queryReport1Total(Map<String, Object> parameterMap);

    int queryReport1Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1(Map<String, Object> parameterMap);

    int queryReport1DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1Details(Map<String, Object> parameterMap);

    LinkedHashMap<String, Object> queryReport2(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3(Map<String, Object> parameterMap);

    List<String> queryReport3RatiosColumns(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3Ratios(Map<String, Object> parameterMap);

    int queryReport4Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport4(Map<String, Object> parameterMap);

    int queryReport4DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport4Details(Map<String, Object> parameterMap);

}
