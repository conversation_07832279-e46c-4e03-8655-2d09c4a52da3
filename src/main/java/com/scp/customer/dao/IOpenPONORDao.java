package com.scp.customer.dao;

import com.scp.customer.bean.OpenPONORReport1Bean;
import com.scp.customer.bean.OpenPONORReport2Bean;
import com.scp.customer.bean.OpenPONORReport3Bean;
import org.apache.ibatis.annotations.Mapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IOpenPONORDao {
    List<Map<String, String>> queryCascader(Map<String, Object> parameterMap);

    List<OpenPONORReport1Bean> queryReport1(Map<String, Object> parameterMap);


    List<OpenPONORReport2Bean> queryReport2(Map<String, Object> parameterMap);

    int queryReport2DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport2Details(Map<String, Object> parameterMap);

    List<OpenPONORReport3Bean> queryReport3(Map<String, Object> parameterMap);

    int queryReport3DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3Details(Map<String, Object> parameterMap);

    List<String> queryReport3Legend(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport4(Map<String, Object> parameterMap);

    int queryReport4DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport4Details(Map<String, Object> parameterMap);

    int queryReport4MODetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport4MODetails(Map<String, Object> parameterMap);
}
