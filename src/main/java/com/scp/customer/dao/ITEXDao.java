package com.scp.customer.dao;

import com.scp.customer.bean.TEXReport1Bean;
import com.scp.customer.bean.TEXReport3Bean;
import com.scp.customer.bean.TEXReport6Bean;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface ITEXDao {

    List<String> queryDateColumns();

    List<Map<String, String>> queryCascader();

    List<TEXReport1Bean> queryReport1(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport2(Map<String, Object> parameterMap);

    int queryReport2DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport2Details(Map<String, Object> parameterMap);

    List<TEXReport3Bean> queryReport3(Map<String, Object> parameterMap);

    BigDecimal queryReport3TotalLine(Map<String, Object> parameterMap);

    int queryReport3DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3Details(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport4(Map<String, Object> parameterMap);

    List<String> queryReport4Columns(Map<String, Object> parameterMap);

    int queryReport4DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport4Details(Map<String, Object> parameterMap);

    Map<String, BigDecimal> queryReport5(Map<String, Object> parameterMap);

    int queryReport5DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport5Details(Map<String, Object> parameterMap);

    List<TEXReport6Bean> queryReport6(Map<String, Object> parameterMap);
}
