package com.scp.customer.dao;

import com.starter.context.bean.scptable.ScpTableCell;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IEndToEndSOTransferDao {

    String queryPageAdmin(@Param("userid") String userid, @Param("parentCode") String parentCode);

    List<Map<String, String>> allowlistInitPage();

    List<Map<String, String>> allowlistRuleTypeOpts();

    List<LinkedHashMap<String, Object>> allowlistQueryReport1(Map<String, Object> parameterMap);

    int allowlistQueryReport1Count(Map<String, Object> parameterMap);

    int allowlistCreateReport1(@Param("headers") List<String> headers, @Param("creates") List<Map<String, Object>> creates, @Param("userid") String userid, @Param("ruleType") String ruleType);

    int allowlistDeleteReport1(@Param("deletes") List<String> deletes, @Param("userid") String userid, @Param("isAdmin") boolean isAdmin);

    int allowlistUpdateReport1(@Param("rowid") String rowid, @Param("updates") List<ScpTableCell> updates, @Param("userid") String userid, @Param("isAdmin") boolean isAdmin);

    List<LinkedHashMap<String, Object>> allowlistQueryReport2(Map<String, Object> parameterMap);

    int allowlistQueryReport2Count(Map<String, Object> parameterMap);

    void mergeReport1Data(Map<String, Object> parameterMap);
}
