package com.scp.demand;

import com.scp.demand.service.IAbnormalDemandService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/demand/abnormal_demand", parent = "menu160")
public class AbnormalDemandController extends ControllerHelper {

    @Resource
    private IAbnormalDemandService abnormalDemandService;

    @SchneiderRequestMapping("/init_page")
    public Response initPage() {
        super.setGlobalCache(true);
        return abnormalDemandService.initPage();
    }

    @SchneiderRequestMapping("/query_report_columns")
    public Response queryReportColumns(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return abnormalDemandService.queryReportColumns(parameterMap);
    }

    @SchneiderRequestMapping("/query_report")
    public Response queryReport(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return abnormalDemandService.queryReport(parameterMap);
    }

    @SchneiderRequestMapping("/download_report_data")
    public void downloadReport(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        abnormalDemandService.downloadReport(parameterMap, response);
    }
}
