package com.scp.demand.dao;

import org.apache.ibatis.annotations.Mapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IEvolutionDao {

    List<Map<String, String>> queryCascader();

    List<String> queryFcstVersion();

    int queryReport1Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1(Map<String, Object> parameterMap);

    int queryReport1DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1Details(Map<String, Object> parameterMap);

    int queryReport1PipelineCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1Pipeline(Map<String, Object> parameterMap);
}
