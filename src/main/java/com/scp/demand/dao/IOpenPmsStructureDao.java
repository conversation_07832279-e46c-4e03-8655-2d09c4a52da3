package com.scp.demand.dao;

import com.scp.demand.bean.OpenPmsReport2Bean;
import com.scp.demand.bean.OpenPmsReport4Bean;
import org.apache.ibatis.annotations.Mapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IOpenPmsStructureDao {
    List<Map<String, String>> queryCascader();

    List<String> queryDateColumns();

    List<LinkedHashMap<String, Object>> queryReport1Details(Map<String, Object> parameterMap);

    List<OpenPmsReport2Bean> queryReport2(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport3(Map<String, Object> parameterMap);

    int queryReport3DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3Details(Map<String, Object> parameterMap);

    List<String> queryReport4Legend(Map<String, Object> parameterMap);

    List<OpenPmsReport4Bean> queryReport4(Map<String, Object> parameterMap);

    int queryReport4DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport4Details(Map<String, Object> parameterMap);

    List<String> queryReport5Columns(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport5(Map<String, Object> parameterMap);

    int queryReport5DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport5Details(Map<String, Object> parameterMap);

}
