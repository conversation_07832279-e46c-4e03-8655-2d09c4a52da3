package com.scp.demand.dao;

import com.scp.demand.bean.PmsReport1Bean;
import com.scp.demand.bean.PmsReport2Bean;
import org.apache.ibatis.annotations.Mapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IPmsStructureDao {

    List<String> queryDateColumns(Map<String, Object> parameterMap);

    List<Map<String, String>> queryCascader(Map<String, Object> parameterMap);

    List<PmsReport1Bean> queryReport1(Map<String, Object> parameterMap);

    List<PmsReport2Bean> queryReport2(Map<String, Object> parameterMap);

    int queryReport2DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport2Details(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3(Map<String, Object> parameterMap);

    List<String> queryReport3Columns(Map<String, Object> parameterMap);

    int queryReport3DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3Details(Map<String, Object> parameterMap);
}
