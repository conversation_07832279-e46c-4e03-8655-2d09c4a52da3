package com.scp.demand.dao;

import com.starter.context.bean.SimpleSelect;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface IPipelineDao {

    List<SimpleSelect> querySubmissionDate();

    List<SimpleSelect> queryUpdateBy(Map<String, Object> parameterMap);

    List<SimpleSelect> queryProductLine(Map<String, Object> parameterMap);

    List<SimpleSelect> queryProjectStatus(Map<String, Object> parameterMap);

    List<SimpleSelect> querySalesTeam(Map<String, Object> parameterMap);

    List<SimpleSelect> querySalesPerson(Map<String, Object> parameterMap);

    List<String> queryReport1Columns(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport1(Map<String, Object> parameterMap);

    void saveReport1Comments(Map<String, Object> parameterMap);

    Object queryReport1Comments(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport1Details(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport1Source(Map<String, Object> parameterMap);

    void saveReport1Source(Map<String, Object> param);

    List<String> queryLast2SubmissionDate(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport2(Map<String, Object> parameterMap);

    Map<String, Object> queryReport2Summary(Map<String, Object> parameterMap);

    List<String> queryFullMonthRange(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport2DetailsCloseOrLost(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport2DetailsNewAdded(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport2DetailsInherited(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport2DetailsInheritedDetails(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport2DetailsMissing(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport2Source(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport2SourceInherited(Map<String, Object> parameterMap);

    String querySubmissionDateByMonth(String submissionMonth);

    List<Map<String, Object>> queryReport2DetailsSummary(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport2SourceSummary(Map<String, Object> parameterMap);

    List<String> queryMonthByCalender(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport3(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport3Details(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport4(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport5Details(Map<String, Object> parameterMap);

    int queryInvalidPipelineDataCount();

    List<Map<String, Object>> queryInvalidPipelineData(Map<String, Object> parameterMap);

    Map<String, Object> queryReport6(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport6Details(Map<String, Object> parameterMap);
}
