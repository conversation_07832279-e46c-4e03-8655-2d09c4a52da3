package com.scp.demand.dao;

import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IWaterfallDao {

    List<Map<String, String>> queryCascader();

    List<String> queryFcstScopeOptions();

    List<Map<String, Object>> queryReport(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReportFCSTNPIEOL(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReportCrd(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReportOrderIntake(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReportOrderIntakeNPIEOL(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReportSales(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReportSalesNPIEOL(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport2(Map<String, Object> parameterMap);

    int queryReport2DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport2Details(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3(Map<String, Object> parameterMap);

    Map<String, Object> queryReport3Total(Map<String, Object> parameterMap);
}
