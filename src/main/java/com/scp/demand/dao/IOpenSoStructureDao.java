package com.scp.demand.dao;

import com.scp.demand.bean.OpenSOReport2Bean;
import com.scp.demand.bean.OpenSOReport4Bean;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IOpenSoStructureDao {
    List<Map<String, String>> queryCascader();

    List<String> queryDateColumns();

    Map<String, BigDecimal> queryReport1(Map<String, Object> parameterMap);

    int queryReport1DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1Details(Map<String, Object> parameterMap);

    List<OpenSOReport2Bean> queryReport2(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryReport3(Map<String, Object> parameterMap);

    int queryReport3DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3Details(Map<String, Object> parameterMap);

    List<OpenSOReport4Bean> queryReport4(Map<String, Object> parameterMap);

    List<OpenSOReport4Bean> queryReport4DueDate(Map<String, Object> parameterMap);

    List<OpenSOReport4Bean> queryReport4ConfirmDate(Map<String, Object> parameterMap);

    List<OpenSOReport4Bean> queryReport4PlannedFirstGIDate(Map<String, Object> parameterMap);

    int queryReport4DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport4Details(Map<String, Object> parameterMap);

    List<String> queryReport5Columns(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport5(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport5DueDate(Map<String, Object> parameterMap);

    int queryReport5DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport5Details(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport5ConfirmDate(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport5PlannedFirstGIDate(Map<String, Object> parameterMap);

    List<String> queryReport4Legend(Map<String, Object> parameterMap);
}
