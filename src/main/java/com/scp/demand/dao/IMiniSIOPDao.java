package com.scp.demand.dao;

import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Mapper
public interface IMiniSIOPDao {

    List<Map<String, String>> queryCascader();

    List<String> queryFcstVersion();

    List<Map<String, Object>> querySummary_Demand(Map<String, Object> parameterMap);

    BigDecimal querySummary_Demand_ActualSalesBeforeSF(Map<String, Object> parameterMap);

    BigDecimal querySummary_STDSales_IGSales(Map<String, Object> parameterMap);

    List<Map<String, Object>> querySummary_STDOI(Map<String, Object> parameterMap);

    BigDecimal querySummary_STDOI_IGOI(Map<String, Object> parameterMap);

    BigDecimal querySummary_STDOI_ActualOIBeforeSF(Map<String, Object> parameterMap);

    List<Map<String, Object>> querySummary_STDCRD(Map<String, Object> parameterMap);

    BigDecimal querySummary_STDCRD_IGCRD(Map<String, Object> parameterMap);

    BigDecimal querySummary_STDCRD_ActualCRDBeforeSF(Map<String, Object> parameterMap);

    BigDecimal querySummary_OrderedDemand_BackOrder(Map<String, Object> parameterMap);

    BigDecimal querySummary_OrderedDemand_CRDInSP(Map<String, Object> parameterMap);

    BigDecimal querySummary_OrderedDemand_IGCRD(Map<String, Object> parameterMap);

    BigDecimal querySummary_OrderedDemand_CRDOutSP(Map<String, Object> parameterMap);

    List<Map<String, Object>> querySummary_SOHDemand(Map<String, Object> parameterMap);

    List<Map<String, Object>> querySummary_GITDemand(Map<String, Object> parameterMap);

    List<Map<String, Object>> querySummary_ConfResDemand(Map<String, Object> parameterMap);

    List<Map<String, Object>> querySummary_SOHOrder(Map<String, Object> parameterMap);

    List<Map<String, Object>> querySummary_GITOrder(Map<String, Object> parameterMap);

    List<Map<String, Object>> querySummary_ConfResOrder(Map<String, Object> parameterMap);

    int queryDetailsCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryDetails(Map<String, Object> parameterMap);

    List<String> queryProductLineForMiniSIOPMeeting();

    List<Map<String, String>> queryLocalProductLineForMiniSIOPMeeting();

    List<Map<String, Object>> querySummary_Demand_ForMiniSIOPMeeting(Map<String, Object> parameterMap);

    List<Map<String, Object>> querySummary_Demand_ActualSalesBeforeSF_ForMiniSIOPMeeting(Map<String, Object> parameterMap);

    List<Map<String, Object>> querySummary_STDOI_IGOI_ForMiniSIOPMeeting(Map<String, Object> parameterMap);

    List<Map<String, Object>> querySummary_SOHDemand_ForMiniSIOPMeeting(Map<String, Object> parameterMap);

    List<Map<String, Object>> querySummary_GITDemand_ForMiniSIOPMeeting(Map<String, Object> parameterMap);

    List<Map<String, Object>> querySummary_OrderedDemand_ForMiniSIOPMeeting(Map<String, Object> parameterMap);

    List<Map<String, Object>> querySummary_ConfResDemand_ForMiniSIOPMeeting(Map<String, Object> parameterMap);
}
