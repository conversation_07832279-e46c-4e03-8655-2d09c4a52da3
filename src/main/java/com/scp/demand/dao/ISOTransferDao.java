package com.scp.demand.dao;

import org.apache.ibatis.annotations.Mapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface ISOTransferDao {
    List<Map<String, String>> queryCascader();

    List<Map<String, Object>> queryTransferSO(Map<String, Object> parameterMap);

    int queryTransferSOCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryAvailableSOH(Map<String, Object> parameterMap);

    void deleteCalcHistory(Map<String, Object> parameterMap);

    int queryReport1Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1(Map<String, Object> parameterMap);

    String queryReport1Logs(Map<String, Object> parameterMap);

    int queryReport2Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport2(Map<String, Object> parameterMap);

    int queryReport2DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport2Details(Map<String, Object> parameterMap);

    int queryReport3Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport3(Map<String, Object> parameterMap);
}
