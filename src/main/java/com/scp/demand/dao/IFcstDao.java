package com.scp.demand.dao;

import com.scp.demand.bean.FcstBean;
import com.starter.context.bean.SimpleSelect;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Mapper
public interface IFcstDao {

    String queryAuthSalesOrgsByUserid(String userid);

    void mergeFcstData(@Param("upload_by") String uploadBy, @Param("upload_mode") String uploadMode);

    void insertFsctDataTemp(@Param("upload_by") String uploadBy, @Param("upload_version") String uploadVersion, @Param("list") List<FcstBean> data);

    void deleteFcstData(@Param("upload_by") String uploadBy, @Param("upload_version") String uploadVersion);

    List<SimpleSelect> queryFcstVersion();

    int queryFcstDataCount(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryFcstData(Map<String, Object> parameterMap);

    void saveForcastSource(Map<String, Object> parameterMap);

    List<String> queryAvaliableCustomerCode();

    Map<String, BigDecimal> queryExchangeRate();
}
