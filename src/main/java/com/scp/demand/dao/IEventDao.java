package com.scp.demand.dao;

import com.starter.context.bean.scptable.ScpTableCell;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IEventDao {

    List<Map<String, String>> queryCascader();

    List<String> queryEventClass();

    List<String> queryEventStatus();

    List<String> queryCycleMonth();

    int queryReport1Count(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1(Map<String, Object> parameterMap);

    int updateReport1(@Param("rowid") String rowid, @Param("updates") List<ScpTableCell> updates, @Param("userid") String userid);

    int deleteReport1(@Param("userid") String userid, @Param("list") List<String> removeList);

    int createReport1(@Param("userid") String userid, @Param("headers") List<String> headers, @Param("createList") List<Map<String, Object>> createList);

    List<String> queryNotesFilter(@Param("category") String category);

    void generateNewNote(@Param("lastCycleMonth") String lastCycleMonth, @Param("cycleMonth") String cycleMonth);

    int queryAdminCnt(@Param("parentCode") String parentCode, @Param("userid") String userid);
}
