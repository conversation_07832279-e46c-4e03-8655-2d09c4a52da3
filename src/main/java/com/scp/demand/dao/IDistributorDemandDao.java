package com.scp.demand.dao;

import com.scp.demand.bean.OpenSOReport2Bean;
import com.scp.demand.bean.OpenSOReport4Bean;
import org.apache.ibatis.annotations.Mapper;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IDistributorDemandDao {

    List<Map<String, String>> initPage(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1(Map<String, Object> parameterMap);

    int queryReport1DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1Details(Map<String, Object> parameterMap);

    List<String> queryVersionList(Map<String, Object> parameterMap);

}
