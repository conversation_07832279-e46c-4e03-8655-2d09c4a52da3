<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.demand.dao.ISOTransferDao">
    <sql id="calcFilter">
        <if test="filters != null and filters != ''.toString()">
            and ${filters}
        </if>
        <if test="specialList != null and specialList.size() > 0">
            <foreach collection="specialList" item="list" separator=" or " open=" and (" close=")">
                t.${specialColumn} in
                <foreach collection="list" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </foreach>
        </if>
    </sql>

    <sql id="filter">
        <if test="filters != null and filters != ''.toString()">
            and ${filters}
        </if>
        <if test="specialList != null and specialList.size() > 0">
            <foreach collection="specialList" item="list" separator=" or " open=" and (" close=")">
                t.${specialColumn} in
                <foreach collection="list" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </foreach>
        </if>
    </sql>

    <select id="queryCascader" resultType="java.util.Map">
        SELECT NAME, CATEGORY FROM (
            SELECT NAME, CATEGORY FROM OPEN_SO_STRUCTURE_FILTER_V T0 WHERE T0.CATEGORY NOT IN ('PLANT_CODE')
            UNION ALL
            SELECT T1.PLANT_CODE, 'PLANT_CODE' FROM MR3_PLANT_MASTER_DATA T1 WHERE T1.PLANT_TYPE = 'DC'
        ) T ORDER BY CATEGORY, decode(t.NAME,'Others','zzz',t.NAME)
    </select>

    <sql id="calcCommonSQL">
        /* 取库存的逻辑, 参考SO_FULFILL_SIMULATION_V */

        /* SO_FULFILL_SIMULATION_V库存逻辑开始 ↓  */
        WITH SOH_V AS (SELECT T.MATERIAL,
                      T.PLANT_CODE,
                      SUM(T.UU_STOCK)      AS UU_STOCK,
                      SUM(T.STOCK_IN_QI)   AS STOCK_IN_QI,
                      SUM(T.BLOCKED_STOCK) AS BLOCKED_STOCK,
                      SUM(T.RETURNS_STOCK) AS RETURN_STOCK
               FROM IM3_SOH_V T
               WHERE (SPECIAL_ST IS NULL OR SPECIAL_ST = 'K')
                 and PLANT_CODE in (
                                    'O001', 'N001', 'M001', 'I001', 'I003'
                   )
                 AND STORAGE_LOCATION IN ('M101', 'N101', 'O101', 'I101', 'I301', '4000', '4100', '4600')
               GROUP BY T.MATERIAL, T.PLANT_CODE),
            UD_GROUP AS (SELECT MATERIAL,
                         PLANT_CODE,
                         SUM(ORDER_QUANTITY) AS UD_PCS
                  FROM DEMAND_UD_V DL
                  WHERE PLANT_CODE IN (
                                       'O001', 'N001', 'M001', 'I001', 'I003'
                      )
                  GROUP BY MATERIAL, PLANT_CODE),
            SOH_UD AS (SELECT SOH.MATERIAL,
                       SOH.PLANT_CODE,
                       NVL(SOH.UU_STOCK, 0)                     AS UU_STOCK,
                       UD.UD_PCS,
                       NVL(SOH.UU_STOCK, 0) - NVL(UD.UD_PCS, 0) AS AVAILABLE_STOCK
                FROM SOH_V SOH
                         LEFT JOIN UD_GROUP UD ON SOH.MATERIAL = UD.MATERIAL AND SOH.PLANT_CODE = UD.PLANT_CODE),
            SOH AS (SELECT SOH_UD.MATERIAL,
                           SOH_UD.PLANT_CODE,
                           MAX(SOH_UD.AVAILABLE_STOCK) AS UU_STOCK
                     FROM SOH_UD
                     WHERE EXISTS (
                           SELECT 1 FROM OPEN_SO_STRUCTURE_V T WHERE SOH_UD.MATERIAL = T.MATERIAL
                           <include refid="calcFilter"/>
                       )
                     GROUP BY SOH_UD.MATERIAL, SOH_UD.PLANT_CODE),
            /* SO_FULFILL_SIMULATION_V库存逻辑结束 ↑  */

             FULFILL_FIXED_UNION AS (
                <choose>
                    <when test="dcInfulfillType == 'FULFILL_OR_NOT_UNRESTRICT'.toString()">
                        /* UNRESTRICT FULFILL*/
                        SELECT SFSV.MATERIAL,
                               SFSV.PLANT_CODE,
                               SUM(SFSV.OPEN_QTY) FIXED_QTY,
                               replace(JSON_ARRAYAGG(SFSV.SALES_ORDER_NUMBER || '/' || SFSV.SALES_ORDER_ITEM || '@' || SFSV.PLANT_CODE || ': ' || SFSV.OPEN_QTY ORDER BY MATERIAL
                               RETURNING CLOB), '"', '')
                               FIXED_ORDER
                        FROM SD4_FULFILL_SIMULATION_V SFSV
                        WHERE CRD_DATE &lt;= TO_DATE(#{fulfillFixedDate, jdbcType=VARCHAR}, 'YYYY/MM/DD')
                          AND SFSV.AVAILABLE_STOCK >= SFSV.OPEN_QTY_SUM_OVER_A
                        GROUP BY SFSV.MATERIAL, SFSV.PLANT_CODE

                        UNION ALL

                        /* UNRESTRICT P-FULFILL*/
                        SELECT SFSV.MATERIAL,
                               SFSV.PLANT_CODE,
                               SUM(SFSV.AVAILABLE_STOCK) FIXED_QTY,
                               replace(JSON_ARRAYAGG(SALES_ORDER_NUMBER || '/' || SFSV.SALES_ORDER_ITEM || '@' || SFSV.PLANT_CODE || ': (P)' || SFSV.AVAILABLE_STOCK ORDER BY MATERIAL
                               RETURNING CLOB), '"', '')
                               FIXED_ORDER
                        FROM SD4_FULFILL_SIMULATION_V SFSV
                        WHERE CRD_DATE &lt;= TO_DATE(#{fulfillFixedDate, jdbcType=VARCHAR}, 'YYYY/MM/DD')
                          AND SFSV.AVAILABLE_STOCK &lt; SFSV.OPEN_QTY_SUM_OVER_A
                          AND ABS(SFSV.AVAILABLE_STOCK - SFSV.OPEN_QTY_SUM_OVER_A) &lt; SFSV.OPEN_QTY
                        GROUP BY SFSV.MATERIAL, SFSV.PLANT_CODE
                    </when>
                    <when test="dcInfulfillType == 'FULFILL_OR_NOT_NONBLOCK'.toString()">
                        /* NONE-BLOCK FULFILL*/
                        SELECT SFSV.MATERIAL,
                               SFSV.PLANT_CODE,
                               SUM(SFSV.OPEN_QTY) FIXED_QTY,
                               replace(JSON_ARRAYAGG(SFSV.SALES_ORDER_NUMBER || '/' || SFSV.SALES_ORDER_ITEM || '@' || SFSV.PLANT_CODE || ': ' || SFSV.OPEN_QTY ORDER BY MATERIAL
                               RETURNING CLOB), '"', '')
                               FIXED_ORDER
                        FROM SD4_FULFILL_SIMULATION_V SFSV
                        WHERE CRD_DATE &lt;= TO_DATE(#{fulfillFixedDate, jdbcType=VARCHAR}, 'YYYY/MM/DD')
                          AND SFSV.AVAILABLE_STOCK >= SFSV.OPEN_QTY_SUM_OVER_B
                        GROUP BY SFSV.MATERIAL, SFSV.PLANT_CODE

                        UNION ALL

                        /* NONE-BLOCK P-FULFILL*/
                        SELECT SFSV.MATERIAL,
                               SFSV.PLANT_CODE,
                               SUM(SFSV.OPEN_QTY) FIXED_QTY,
                               replace(JSON_ARRAYAGG(SFSV.SALES_ORDER_NUMBER || '/' || SFSV.SALES_ORDER_ITEM || '@' || SFSV.PLANT_CODE || ': (P)' || SFSV.OPEN_QTY ORDER BY MATERIAL
                               RETURNING CLOB), '"', '')
                               FIXED_ORDER
                        FROM SD4_FULFILL_SIMULATION_V SFSV
                        WHERE CRD_DATE &lt;= TO_DATE(#{fulfillFixedDate, jdbcType=VARCHAR}, 'YYYY/MM/DD')
                          AND SFSV.AVAILABLE_STOCK &lt; SFSV.OPEN_QTY_SUM_OVER_B
                          AND ABS(SFSV.AVAILABLE_STOCK - SFSV.OPEN_QTY_SUM_OVER_B) &lt; SFSV.OPEN_QTY
                        GROUP BY SFSV.MATERIAL, SFSV.PLANT_CODE
                    </when>
                </choose>
             ),
             FULFILL_FIXED AS (SELECT MATERIAL, PLANT_CODE, SUM(FIXED_QTY) AS FIXED_QTY,
                                      REPLACE(TRIM(TRAILING ']' FROM
                                      TRIM(LEADING '[' FROM REPLACE(JSON_ARRAYAGG(FIXED_ORDER
                                      RETURNING CLOB), '"',
                                      ''))), ',', ';') AS FIXED_ORDER
                                FROM FULFILL_FIXED_UNION GROUP BY MATERIAL, PLANT_CODE),
             AVAILABLE_SOH AS (SELECT S.MATERIAL,
                                      S.PLANT_CODE,
                                      NVL(S.UU_STOCK, 0) - NVL(F.FIXED_QTY, 0) AS AVAILABLE_QTY,
                                      NVL(S.UU_STOCK, 0) AS UU_STOCK,
                                      NVL(F.FIXED_QTY, 0) AS FIXED_QTY,
                                      F.FIXED_ORDER,
                                      NVL(MMV.SAFETY_STOCK, 0) AS SAFETY_STOCK
                               FROM SOH S
                                    LEFT JOIN FULFILL_FIXED F ON S.MATERIAL = F.MATERIAL AND S.PLANT_CODE = F.PLANT_CODE
                                    INNER JOIN MATERIAL_MASTER_V MMV ON S.MATERIAL = MMV.MATERIAL AND S.PLANT_CODE = MMV.PLANT_CODE)
    </sql>

    <sql id="queryTransferSOCondition">
        WHERE (SFSV.CRD_DATE &lt;= TO_DATE(#{transferEndDate, jdbcType=VARCHAR}, 'YYYY/MM/DD') OR SFSV.DELIVERY_PRIORITY = '1')
          AND SFSV.OPEN_QTY > 0
          <choose>
            <when test="dcOutSoPolicy == 'MTO'.toString()">
                AND SFSV.SO_STOCKING_POLICY = 'MTO'
            </when>
            <when test="dcOutSoPolicy == 'MTS'.toString()">
                AND SFSV.SO_STOCKING_POLICY = 'MTS'
            </when>
          </choose>
          <choose>
            <when test="dcOutfulfillType == 'FULFILL_OR_NOT_UNRESTRICT'.toString()">
                /* Cannot Fulfill的另一种写法 */
                AND SFSV.OPEN_QTY_SUM_OVER_A > (SFSV.AVAILABLE_STOCK - SFSV.SAFETY_STOCK * ${doOutSsPercent})
            </when>
            <when test="dcOutfulfillType == 'FULFILL_OR_NOT_NONBLOCK'.toString()">
                /* Cannot Fulfill的另一种写法 */
                AND SFSV.OPEN_QTY_SUM_OVER_B > (SFSV.AVAILABLE_STOCK - SFSV.SAFETY_STOCK * ${doOutSsPercent})
            </when>
          </choose>
          <if test="partiallDeliveried == 'N'.toString()">
              AND SFSV.PARTIAL_DELIVERED_OR_NOT IS NULL
          </if>
          AND SFSV.SOLD_TO NOT IN (SELECT T0.EXCLUDE_CODE FROM MR3_SO_TRANSFER_EXCLUDE T0 WHERE T0.TYPE = 'SOLD_TO')
          AND SFSV.ITEM_CATEGORY NOT IN (SELECT T0.EXCLUDE_CODE FROM MR3_SO_TRANSFER_EXCLUDE T0 WHERE T0.TYPE = 'ITEM_CATEGORY')
          AND SFSV.PLANT_CODE IN ('O001', 'N001', 'M001', 'I001', 'I003')
          AND EXISTS (
               SELECT 1 FROM OPEN_SO_STRUCTURE_V T WHERE SFSV.MATERIAL = T.MATERIAL
               <include refid="calcFilter"/>
          )
          AND EXISTS(
               SELECT 1 FROM MR3_PLANT_MASTER_DATA TT0 WHERE TT0.PLANT_TYPE = 'DC' AND SFSV.PLANT_CODE = TT0.PLANT_CODE
          )
    </sql>

    <select id="queryTransferSOCount" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM SD4_FULFILL_SIMULATION_V SFSV
        <include refid="queryTransferSOCondition"/>
    </select>

    <select id="queryTransferSO" resultType="java.util.Map">
        SELECT SFSV.SALES_ORDER_NUMBER,
               SFSV.SALES_ORDER_ITEM,
               SFSV.MATERIAL,
               SFSV.PLANT_CODE,
               SFSV.CRD_DATE,
               SFSV.OPEN_QTY,
               SFSV.DELIVERY_PRIORITY,
               SFSV.SO_TOTAL_QTY
        FROM SD4_FULFILL_SIMULATION_V SFSV
        <include refid="queryTransferSOCondition"/>
        ORDER BY DELIVERY_PRIORITY, CRD_DATE, OPEN_QTY, ${dcOrderBy}
    </select>

    <select id="queryAvailableSOH" resultType="java.util.Map">
        <include refid="calcCommonSQL"/>
        SELECT * FROM AVAILABLE_SOH
    </select>

    <delete id="deleteCalcHistory">
        DELETE FROM SO_TRANSFER_RESULT WHERE OPR_USERID = #{session.userid,jdbcType=VARCHAR}
    </delete>

    <sql id="queryReport1Sql">
        SELECT TRANSFER_SEQ,
               SALES_ORDER_NUMBER,
               SALES_ORDER_ITEM,
               MATERIAL,
               PLANT_CODE,
               CRD_DATE,
               DELIVERY_PRIORITY,
               TRANSFER_OR_NOT,
               TRANSFER_PLANT,
               TRANSFER_QTY,
               SO_TOTAL_QTY,
               TO_CHAR(OPR_DATE, 'YYYY/MM/DD HH24:MI:SS') OPR_TIME
        FROM SO_TRANSFER_RESULT T
       WHERE T.OPR_USERID = #{session.userid,jdbcType=VARCHAR}
       ORDER BY TRANSFER_SEQ
    </sql>

    <select id="queryReport1Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1Sql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1Sql"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport1Logs" resultType="java.lang.String">
        SELECT TRANSFER_LOG FROM SO_TRANSFER_RESULT T WHERE T.OPR_USERID = #{session.userid,jdbcType=VARCHAR} AND T.TRANSFER_SEQ = #{report1SelectedValues, jdbcType=NUMERIC}
    </select>

    <sql id="queryReport2Sql">
        SELECT <foreach collection="report2Type" item="item">
                  T.${item},
               </foreach>
               STD.PLANT_CODE           AS TRANSFER_FROM_DC,
               STD.TRANSFER_PLANT       AS TRANSFER_TO_DC,
               SUM(STD.TRANSFER_QTY)    AS TRANSFER_QTY,
               SUM(T.NET_NET_VALUE_RMB) AS TRANSFER_VALUE,
               COUNT(1)                 AS TRANSFER_ORDER_LINE
        FROM SO_TRANSFER_RESULT STD
                 INNER JOIN OPEN_SO_STRUCTURE_V T ON STD.SALES_ORDER_NUMBER = T.SALES_ORDER_NUMBER AND STD.SALES_ORDER_ITEM = T.SALES_ORDER_ITEM
        WHERE STD.OPR_USERID = #{session.userid,jdbcType=VARCHAR}
          AND STD.TRANSFER_OR_NOT = 'Y'
          <include refid="filter"/>
        GROUP BY <foreach collection="report2Type" item="item">
                    T.${item},
                 </foreach>STD.PLANT_CODE, STD.TRANSFER_PLANT
    </sql>

    <select id="queryReport2Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport2Sql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport2" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport2Sql"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport2DetailsSql">
        SELECT  STD.PLANT_CODE     AS TRANSFER_FROM_DC,
                STD.TRANSFER_PLANT AS TRANSFER_TO_DC,
                STD.TRANSFER_QTY   AS TRANSFER_QTY,
                STD.SO_TOTAL_QTY   AS SO_TOTAL_QTY,
                T.SALES_ORDER_NUMBER,
                T.SALES_ORDER_ITEM,
                T.CRD_DATE,
                T.CALENDAR_DATE,
                T.CALENDAR_YEAR,
                T.CALENDAR_WEEK,
                T.CALENDAR_MONTH,
                T.SALES_ORGANIZATION,
                T.SHIPPING_POINT,
                T.MATERIAL,
                T.PAST_DUE_INDICATOR,
                T.DELIVERY_RANGE,
                T.SO_BLOCK_STATUS,
                T.ORDER_QUANTITY,
                T.NET_VALUE,
                T.NET_NET_VALUE_RMB,
                T.BACK_ORDER_QTY,
                T.OPEN_SO_QTY,
                T.OPEN_SO_W_O_DEL,
                T.OPEN_UD_QTY,
                T.VALUE_W_O_DEL,
                T.VALUE_W_O_GI,
                T.UD_CB,
                T.UD_NORMAL,
                T.UD_MID_AGING,
                T.UD_LONG_AGING,
                T.AVG_SELLING_PRICE_RMB,
                T.CREATED_DATE,
                T.SOLD_TO,
                T.SOLD_TO_SHORT_NAME,
                T.SOLD_TO_FULL_NAME,
                T.SOLD_TO_PARENT_NAME,
                T.SOLD_TO_PARENT_CODE,
                T.SOLD_TO_REGION,
                T.SOLD_TO_COUNTRY,
                T.SHIP_TO,
                T.SHIP_TO_SHORT_NAME,
                T.SHIP_TO_FULL_NAME,
                T.SHIP_TO_PARENT_CODE,
                T.SHIP_TO_PARENT_NAME,
                T.SHIP_TO_REGION,
                T.SHIP_TO_CITY,
                T.SHIP_TO_COUNTRY,
                T.VENDOR_CODE,
                T.VENDOR_NAME,
                T.ITEM_CATEGORY,
                T.ORDER_TYPE,
                T.STOCKING_POLICY,
                T.PO_ITEM_CREATED_DATE,
                T.PURCH_ORDER_NUMBER,
                T.PURCH_ORDER_ITEM,
                T.PO_QTY,
                T.OPEN_UD_VALUE,
                T.PR_NUMBER,
                T.PR_ITEM,
                T.RESCH_COUNTER,
                T.SE_SCOPE,
                T.PLANT_SCOPE,
                T.DELIVERY_PRIORITY,
                T.MRP_CONTROLLER,
                T.PRODUCT_LINE,
                T.ENTITY,
                T.CLUSTER_NAME,
                T.BU,
                T.LOCAL_BU,
                T.LOCAL_PRODUCT_FAMILY,
                T.LOCAL_PRODUCT_LINE,
                T.LOCAL_PRODUCT_SUBFAMILY,
                T.ACTIVENESS,
                T.SOURCE_CATEGORY,
                T.AC2_DATE,
                T.CONFIRM_DATE,
                T.ONTIME,
                T.ERROR_CODE,
                T.GRA_STATUS,
                T.SHORTAGE_STATUS,
                T.COMPL_DELIV_FLAG,
                T.COMPL_DELIV_IND,
                T.QMAX_LEAD_TIME,
                T.QMAX_LEAD_TIME_GROUP,
                T.VALIDATE_FROM,
                T.VALIDATE_TO,
                T.BTN_DATE,
                T.MATERIAL_GROUP_1,
                T.MATERIAL_GROUP_2,
                T.MATERIAL_GROUP_3,
                T.MATERIAL_GROUP_4,
                T.MATERIAL_GROUP_5,
                T.FULFILL_OR_NOT_UNRESTRICT,
                T.FULFILL_OR_NOT_NONBLOCK,
                T.CRDD_FIRST_DATE,
                T.PLANT_TYPE,
                T.PRODUCTION_LINE,
                T.CALCULATED_ABC,
                T.CALCULATED_FMR,
                T.NEW_PRODUCTS,
                T.MRP_TYPE,
                T.AVAILABILITY_CHECK,
                T.MAT_PRICING_GROUP,
                T.MATERIAL_ST_PLANT,
                T.DIST_CHANNEL_SP_ST,
                T.MATERIAL_OWNER_SESA,
                T.MATERIAL_OWNER_NAME,
                T.DIS_CHANNEL,
                T.SALES_GROUP,
                T.SALES_OFFICE,
                T.UD_STATUS,
                T.CUST_PO_NUMBER,
                T.CUST_PO_DATE,
                T.CUST_PO_ITEM,
                T.MAT_AVAILABLE_DATE,
                T.NAME_OF_ORDER
        FROM SO_TRANSFER_RESULT STD
                 INNER JOIN OPEN_SO_STRUCTURE_V T ON STD.SALES_ORDER_NUMBER = T.SALES_ORDER_NUMBER AND STD.SALES_ORDER_ITEM = T.SALES_ORDER_ITEM
        WHERE STD.OPR_USERID = #{session.userid,jdbcType=VARCHAR}
          AND STD.TRANSFER_OR_NOT = 'Y'
          <include refid="filter"/>
          <if test="report2SelectedFrom != ''.toString()">
            AND STD.PLANT_CODE = #{report2SelectedFrom, jdbcType=VARCHAR}
          </if>
          <if test="report2SelectedTo != ''.toString()">
            AND STD.TRANSFER_PLANT = #{report2SelectedTo, jdbcType=VARCHAR}
          </if>
          <foreach collection="report2Type" item="item" index="index">
             AND T.${item} = #{report2SelectedType[${index}], jdbcType=VARCHAR}
          </foreach>
    </sql>

    <select id="queryReport2DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport2DetailsSql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport2Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport2DetailsSql"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport3Sql">
        WITH TEMP AS (
            SELECT 'Change TO Another DC'                       AS BASIC_CHANGE_INFORMATION,
                   TRUNC(SYSDATE, 'DD')                         AS CHANGE_DATE,
                   T.ENTITY                                     AS PLANT,
                   T.MATERIAL                                   AS MATERIAL,
                   STD.TRANSFER_QTY                             AS REQUEST_QTY,
                   MMV.GROSS_WEIGHT_IN_KG * STD.TRANSFER_QTY    AS GROSS_WEIGHT_KG,
                   T.SALES_ORDER_NUMBER                         AS SO_NUMBER,
                   T.SALES_ORDER_ITEM                           AS SO_ITEM,
                   T.ITEM_CATEGORY,
                   T.SHIP_TO_CITY,
                   T.SOLD_TO                                    AS CUSTOMER_CODE,
                   T.SOLD_TO_SHORT_NAME,
                   T.SOLD_TO_FULL_NAME,
                   T.SOLD_TO_PARENT_NAME,
                   T.SOLD_TO_PARENT_CODE,
                   PMD.ENTITY_NAME                              AS ORIGINAL_SHIPPING_DC,
                   PMD2.ENTITY_NAME                             AS CHANGED_SHIPPING_DC,
                   T.CRD_DATE                                   AS CUSTOMER_REQUEST_DATE,
                   RATE.LT                                      AS ORIGINAL_DC_LT,
                   RATE_CHANGED.LT                              AS CHANGE_DC_LT,
                   CASE WHEN MMV.GROSS_WEIGHT_IN_KG * STD.TRANSFER_QTY &lt;= 10 THEN RATE.RATE_0_10
                        WHEN MMV.GROSS_WEIGHT_IN_KG * STD.TRANSFER_QTY &lt;= 30 THEN RATE.RATE_10_30
                        WHEN MMV.GROSS_WEIGHT_IN_KG * STD.TRANSFER_QTY &lt;= 100 THEN RATE.RATE_30_100
                        WHEN MMV.GROSS_WEIGHT_IN_KG * STD.TRANSFER_QTY &lt;= 300 THEN RATE.RATE_100_300
                        WHEN MMV.GROSS_WEIGHT_IN_KG * STD.TRANSFER_QTY &lt;= 1000 THEN RATE.RATE_300_1000
                        WHEN MMV.GROSS_WEIGHT_IN_KG * STD.TRANSFER_QTY > 1000 THEN RATE.RATE_ABOVE_1000
                   ELSE 0 END * MMV.GROSS_WEIGHT_IN_KG * STD.TRANSFER_QTY AS ORIGINAL_DC_COST,

                   CASE WHEN MMV.GROSS_WEIGHT_IN_KG * STD.TRANSFER_QTY &lt;= 10 THEN RATE_CHANGED.RATE_0_10
                        WHEN MMV.GROSS_WEIGHT_IN_KG * STD.TRANSFER_QTY &lt;= 30 THEN RATE_CHANGED.RATE_10_30
                        WHEN MMV.GROSS_WEIGHT_IN_KG * STD.TRANSFER_QTY &lt;= 100 THEN RATE_CHANGED.RATE_30_100
                        WHEN MMV.GROSS_WEIGHT_IN_KG * STD.TRANSFER_QTY &lt;= 300 THEN RATE_CHANGED.RATE_100_300
                        WHEN MMV.GROSS_WEIGHT_IN_KG * STD.TRANSFER_QTY &lt;= 1000 THEN RATE_CHANGED.RATE_300_1000
                        WHEN MMV.GROSS_WEIGHT_IN_KG * STD.TRANSFER_QTY > 1000 THEN RATE_CHANGED.RATE_ABOVE_1000
                   ELSE 0 END * MMV.GROSS_WEIGHT_IN_KG * STD.TRANSFER_QTY AS CHANGE_DC_COST
            FROM SO_TRANSFER_RESULT STD
                     INNER JOIN OPEN_SO_STRUCTURE_V T ON STD.SALES_ORDER_NUMBER = T.SALES_ORDER_NUMBER AND STD.SALES_ORDER_ITEM = T.SALES_ORDER_ITEM
                     LEFT JOIN MR3_PLANT_MASTER_DATA PMD ON STD.PLANT_CODE = PMD.PLANT_CODE
                     LEFT JOIN MR3_PLANT_MASTER_DATA PMD2 ON STD.TRANSFER_PLANT = PMD2.PLANT_CODE
                     LEFT JOIN MATERIAL_MASTER_V MMV ON T.MATERIAL = MMV.MATERIAL AND T.PLANT_CODE = MMV.PLANT_CODE
                     LEFT JOIN (SELECT LANE,
                                MAX(LT) AS LT,
                                MAX(RATE_0_10)       AS RATE_0_10,
                                MAX(RATE_10_30)      AS RATE_10_30,
                                MAX(RATE_30_100)     AS RATE_30_100,
                                MAX(RATE_100_300)    AS RATE_100_300,
                                MAX(RATE_300_1000)   AS RATE_300_1000,
                                MAX(RATE_ABOVE_1000) AS RATE_ABOVE_1000
                                FROM MR3_SO_TRANSFER_RATE
                                GROUP BY LANE) RATE ON PMD.ENTITY_NAME || '-' || T.SHIP_TO_CITY = RATE.LANE
                     LEFT JOIN (SELECT LANE,
                                MAX(LT) AS LT,
                                MAX(RATE_0_10)       AS RATE_0_10,
                                MAX(RATE_10_30)      AS RATE_10_30,
                                MAX(RATE_30_100)     AS RATE_30_100,
                                MAX(RATE_100_300)    AS RATE_100_300,
                                MAX(RATE_300_1000)   AS RATE_300_1000,
                                MAX(RATE_ABOVE_1000) AS RATE_ABOVE_1000
                                FROM MR3_SO_TRANSFER_RATE
                                GROUP BY LANE) RATE_CHANGED ON PMD2.ENTITY_NAME || '-' || T.SHIP_TO_CITY = RATE_CHANGED.LANE
            WHERE STD.OPR_USERID = #{session.userid,jdbcType=VARCHAR}
              AND STD.TRANSFER_OR_NOT = 'Y'
              <include refid="filter"/>
        )

        SELECT T.*,
               TRUNC(SYSDATE, 'DD') + CHANGE_DC_LT         AS ARRIVE_CUSTOMER_BY_CHANGE_DC,
               NVL(T.CHANGE_DC_COST - ORIGINAL_DC_COST,0)  AS COST_IMPACT
          FROM TEMP T
    </sql>

    <select id="queryReport3Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport3Sql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport3" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport3Sql"/>
        <include refid="global.select_footer"/>
    </select>
</mapper>
