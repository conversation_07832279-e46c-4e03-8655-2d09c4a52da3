<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.demand.dao.IEvolutionDao">
    <sql id="scope_filter">
        <if test="scope != null and scope != ''.toString()">
            <choose>
                <when test="subScope == null or subScope.size() == 0">
                    <choose>
                        <when test="scope == 'SECI'.toString()">
                            AND T.SE_SCOPE IS NOT NULL
                        </when>
                        <when test="scope == 'PLANT'.toString()">
                            AND t.PLANT_SCOPE IS NOT NULL
                        </when>
                    </choose>
                </when>
                <otherwise>
                    <choose>
                        <when test="scope == 'SECI'.toString()">
                            AND T.SE_SCOPE IN
                            <foreach collection="subScope" separator="," open="(" close=")" item="item">
                                #{item, jdbcType=VARCHAR}
                            </foreach>
                        </when>
                        <when test="scope == 'PLANT'.toString()">
                            AND t.PLANT_SCOPE IN
                            <foreach collection="subScope" separator="," open="(" close=")" item="item">
                                #{item, jdbcType=VARCHAR}
                            </foreach>
                        </when>
                    </choose>
                </otherwise>
            </choose>
        </if>
    </sql>

    <sql id="evolutionFilters">
        <include refid="scope_filter"/>
        <if test="filters != null and filters != ''.toString()">
            AND ${filters}
        </if>
        <if test="specialList != null and specialList.size() > 0">
            <foreach collection="specialList" item="list" separator=" or " open=" and (" close=")">
                t.${specialColumn} in
                <foreach collection="list" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </foreach>
        </if>
    </sql>

    <select id="queryCascader" resultType="java.util.Map">
        SELECT *
        FROM DEMAND_TRACKING_FILTER_V T
        order by CATEGORY, DECODE(NAME, 'Others', 'zzz', NAME)
    </select>

    <select id="queryFcstVersion" resultType="java.lang.String">
        SELECT DISTINCT FCST_VERSION
          FROM DEMAND_FCST_V
         ORDER BY FCST_VERSION DESC
        OFFSET 0 ROWS FETCH NEXT 12 ROWS ONLY
    </select>

    <sql id="queryReport1SQL">
        WITH PIPELINE_TEMP AS (
            SELECT
            <foreach collection="report1GroupColumns" item="item" separator=",">
              T.${item}
            </foreach>,
            CREATE_MONTH,
            SUM(MONTH01) MONTH01,
            SUM(MONTH02) MONTH02,
            SUM(MONTH03) MONTH03,
            SUM(MONTH04) MONTH04,
            SUM(MONTH05) MONTH05,
            SUM(MONTH06) MONTH06,
            SUM(MONTH07) MONTH07,
            SUM(MONTH08) MONTH08,
            SUM(MONTH09) MONTH09,
            SUM(MONTH10) MONTH10,
            SUM(MONTH11) MONTH11,
            SUM(MONTH12) MONTH12,
            SUM(MONTH13) MONTH13,
            SUM(MONTH14) MONTH14,
            SUM(MONTH15) MONTH15,
            SUM(MONTH16) MONTH16,
            SUM(MONTH17) MONTH17,
            SUM(MONTH18) MONTH18,
            SUM(MONTH19) MONTH19,
            SUM(MONTH20) MONTH20,
            SUM(MONTH21) MONTH21,
            SUM(MONTH22) MONTH22,
            SUM(MONTH23) MONTH23,
            SUM(MONTH24) MONTH24,
            SUM(MONTH25) MONTH25
            FROM DEMAND_EVOLUTION_PIPELINE_V T
            WHERE CREATE_MONTH = #{fcstVersion, jdbcType=VARCHAR}
            GROUP BY CREATE_MONTH,
            <foreach collection="report1GroupColumns" item="item" separator=",">
                 T.${item}
            </foreach>
        ), FCST_TEMP AS (
            SELECT <foreach collection="report1GroupColumns" item="item" separator=",">
                      T.${item}
                   </foreach>,
                   SUM(T.MONTH01 * ${fcstValueColumn}) MONTH01,
                   SUM(T.MONTH02 * ${fcstValueColumn}) MONTH02,
                   SUM(T.MONTH03 * ${fcstValueColumn}) MONTH03,
                   SUM(T.MONTH04 * ${fcstValueColumn}) MONTH04,
                   SUM(T.MONTH05 * ${fcstValueColumn}) MONTH05,
                   SUM(T.MONTH06 * ${fcstValueColumn}) MONTH06,
                   SUM(T.MONTH07 * ${fcstValueColumn}) MONTH07,
                   SUM(T.MONTH08 * ${fcstValueColumn}) MONTH08,
                   SUM(T.MONTH09 * ${fcstValueColumn}) MONTH09,
                   SUM(T.MONTH10 * ${fcstValueColumn}) MONTH10,
                   SUM(T.MONTH11 * ${fcstValueColumn}) MONTH11,
                   SUM(T.MONTH12 * ${fcstValueColumn}) MONTH12,
                   SUM(T.MONTH13 * ${fcstValueColumn}) MONTH13,
                   SUM(T.MONTH14 * ${fcstValueColumn}) MONTH14,
                   SUM(T.MONTH15 * ${fcstValueColumn}) MONTH15,
                   SUM(T.MONTH16 * ${fcstValueColumn}) MONTH16,
                   SUM(T.MONTH17 * ${fcstValueColumn}) MONTH17,
                   SUM(T.MONTH18 * ${fcstValueColumn}) MONTH18,
                   SUM(T.MONTH19 * ${fcstValueColumn}) MONTH19,
                   SUM(T.MONTH20 * ${fcstValueColumn}) MONTH20,
                   SUM(T.MONTH21 * ${fcstValueColumn}) MONTH21,
                   SUM(T.MONTH22 * ${fcstValueColumn}) MONTH22,
                   SUM(T.MONTH23 * ${fcstValueColumn}) MONTH23,
                   SUM(T.MONTH24 * ${fcstValueColumn}) MONTH24,
                   SUM(T.MONTH25 * ${fcstValueColumn}) MONTH25,
                   SUM(T2.MONTH01) PIPE_MONTH01,
                   SUM(T2.MONTH02) PIPE_MONTH02,
                   SUM(T2.MONTH03) PIPE_MONTH03,
                   SUM(T2.MONTH04) PIPE_MONTH04,
                   SUM(T2.MONTH05) PIPE_MONTH05,
                   SUM(T2.MONTH06) PIPE_MONTH06,
                   SUM(T2.MONTH07) PIPE_MONTH07,
                   SUM(T2.MONTH08) PIPE_MONTH08,
                   SUM(T2.MONTH09) PIPE_MONTH09,
                   SUM(T2.MONTH10) PIPE_MONTH10,
                   SUM(T2.MONTH11) PIPE_MONTH11,
                   SUM(T2.MONTH12) PIPE_MONTH12,
                   SUM(T2.MONTH13) PIPE_MONTH13,
                   SUM(T2.MONTH14) PIPE_MONTH14,
                   SUM(T2.MONTH15) PIPE_MONTH15,
                   SUM(T2.MONTH16) PIPE_MONTH16,
                   SUM(T2.MONTH17) PIPE_MONTH17,
                   SUM(T2.MONTH18) PIPE_MONTH18,
                   SUM(T2.MONTH19) PIPE_MONTH19,
                   SUM(T2.MONTH20) PIPE_MONTH20,
                   SUM(T2.MONTH21) PIPE_MONTH21,
                   SUM(T2.MONTH22) PIPE_MONTH22,
                   SUM(T2.MONTH23) PIPE_MONTH23,
                   SUM(T2.MONTH24) PIPE_MONTH24,
                   SUM(T2.MONTH25) PIPE_MONTH25
              FROM DEMAND_FCST_V T LEFT JOIN PIPELINE_TEMP T2 ON
                   <foreach collection="report1GroupColumns" item="item" separator=" AND ">
                      T.${item} = T2.${item}
                   </foreach>
                   AND T.FCST_VERSION = T2.CREATE_MONTH
             WHERE FCST_VERSION = #{fcstVersion, jdbcType=VARCHAR}
                   <include refid="evolutionFilters"/>
              GROUP BY
              <foreach collection="report1GroupColumns" item="item" separator=",">
                 T.${item}
              </foreach>
        ), HIST AS (
            SELECT /*+ parallel(t 6) */
                   <foreach collection="report1GroupColumns" item="item" separator=",">
                       T.${item} ${item}
                   </foreach>,
                   SUM(T.${currentMonth}) CURRENT_MONTH,
                   <foreach collection="historyColumns" item="item" separator="," index="index">
                       SUM(${prefix}${item}) ${prefix}${item}
                   </foreach>
              FROM ${tableName} T
              <where>
                  <include refid="evolutionFilters"/>
              </where>
              GROUP BY
              <foreach collection="report1GroupColumns" item="item" separator=",">
                 T.${item}
              </foreach>
        ), AMUF AS (
            SELECT
            <foreach collection="report1GroupColumns" item="item" separator=",">
               T0.${item} ${item}
            </foreach>,
            SUM(T0.WEEKLY_ORDER_LINE) WEEKLY_ORDER_LINE,
            SUM(${amuColumn}) AMU,
            SUM(${amfColumn}) AMF
            FROM MATERIAL_MASTER_V T0
            WHERE EXISTS(
                SELECT 1 FROM ${tableName} T WHERE T0.MATERIAL = T.MATERIAL AND T0.PLANT_CODE = T.PLANT_CODE
                <include refid="evolutionFilters"/>
            )
            GROUP BY
            <foreach collection="report1GroupColumns" item="item" separator=",">
             T0.${item}
            </foreach>
        ),
        temp as (
        SELECT
            <foreach collection="report1GroupColumns" item="item" separator=",">
                NVL(T.${item},T2.${item}) AS ${item}
            </foreach>,
                T.CURRENT_MONTH CURRENT_MONTH,
                ${currentYgr},
            <foreach collection="tableColumns" item="item" separator="," index="index">
                   ${item} AS "${displayColumns[index]}"
            </foreach>,
            <foreach collection="pipelineColumns" item="item" separator="," index="index">
                   ${item} AS "${pipelineNames[index]}"
            </foreach>
        FROM HIST T
            FULL JOIN FCST_TEMP T2 ON
            <foreach collection="report1GroupColumns" item="item" separator=" AND ">
                T.${item} = T2.${item}
            </foreach>
        ORDER BY
            <foreach collection="report1GroupColumns" item="item" separator=",">
                DECODE(T.${item}, 'Others', 'zzz', T.${item})
            </foreach>)
        SELECT
            T.*,T3.AMU,
            T3.AMF,
            T3.WEEKLY_ORDER_LINE
        FROM TEMP  T
            LEFT JOIN AMUF T3 ON
            <foreach collection="report1GroupColumns" item="item" separator=" AND ">
                T.${item} = T3.${item}
            </foreach>
            ORDER BY
            <foreach collection="report1GroupColumns" item="item" separator=",">
                T.${item}
            </foreach>
    </sql>

    <select id="queryReport1Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1SQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1SQL"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport1DetailsSQL">
        SELECT * FROM ${tableName} T
        <where>
            <if test="detailsMonth != null and detailsMonth != ''.toString()">
                <choose>
                    <when test="tableName == 'DEMAND_FCST_V'.toString()">
                        AND T.FCST_VERSION = #{fcstVersion, jdbcType=VARCHAR}
                        AND T.${monthColumn} > 0
                    </when>
                    <otherwise>
                        AND T.CALENDAR_MONTH = #{detailsMonth, jdbcType=VARCHAR}
                    </otherwise>
                </choose>
            </if>
            <foreach collection="groupColumns" item="item" index="index">
                <if test="groupValue[index] != ''.toString() and groupValue[index] != 'Total'.toString()">
                    and ${item} = #{groupValue[${index}], jdbcType=VARCHAR}
                </if>
            </foreach>
            <include refid="evolutionFilters"/>
        </where>
    </sql>

    <select id="queryReport1DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport1PipelineSQL">
        SELECT CREATE_MONTH,
               MATERIAL,
               PLANT_CODE,
               CUSTOMER_CODE,
               SALES_ORGANIZATION,
               SALES_NAME,
               PROJECT_NAME,
               MATERIAL_CLASS,
               MRP_CONTROLLER,
               PRODUCT_LINE,
               ENTITY,
               CLUSTER_NAME,
               BU,
               LOCAL_BU,
               LOCAL_PRODUCT_FAMILY,
               LOCAL_PRODUCT_LINE,
               LOCAL_PRODUCT_SUBFAMILY,
               STOCKING_POLICY,
               VENDOR_CODE,
               VENDOR_NAME,
               ACTIVENESS,
               SOURCE_CATEGORY,
               ${pipeMonth} QTY,
               AMU,
               AMF,
               WEEKLY_ORDER_LINE
          FROM DEMAND_EVOLUTION_PIPELINE_V T
         WHERE T.CREATE_MONTH = #{fcstVersion, jdbcType=VARCHAR}
           AND T.${pipeMonth} > 0
           <foreach collection="groupColumns" item="item" separator="and" open="and" index="index">
                ${item} = #{groupValue[${index}], jdbcType=VARCHAR}
           </foreach>
           <if test="pipeLineFilterList != null and pipeLineFilterList != ''.toString()">
                AND ${pipeLineFilterList}
           </if>
           <if test="specialList != null and specialList.size() > 0">
                <foreach collection="specialList" item="list" separator=" or " open=" and (" close=")">
                    t.${specialColumn} in
                    <foreach collection="list" item="item" separator="," open="(" close=")">
                        #{item,jdbcType=VARCHAR}
                    </foreach>
                </foreach>
           </if>
    </sql>

    <select id="queryReport1PipelineCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1PipelineSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1Pipeline" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1PipelineSQL"/>
        <include refid="global.select_footer"/>
    </select>

</mapper>
