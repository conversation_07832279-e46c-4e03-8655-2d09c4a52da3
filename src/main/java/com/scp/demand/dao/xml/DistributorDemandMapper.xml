<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.demand.dao.IDistributorDemandDao">

    <sql id="filter">
        <if test="_filters != null and _filters != ''.toString()">
            and ${_filters}
        </if>
    </sql>

    <select id="initPage" resultType="java.util.Map">
        SELECT NAME,
               CATEGORY
        FROM SCPA.DISTRIBUTOR_DEMAND_FILTER_V T
        ORDER BY CATEGORY,NAME
    </select>

    <select id="queryReport1" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        SELECT
        <foreach collection="report1ViewColumns" separator="," item="item">
            NVL(T.${item},'Others')     AS   "${item}"
        </foreach>,
        SUM(NVL(T.DEMAND_QUANTITY,0))                                          AS POTENTIAL_DEMAND_QTY,
        SUM(NVL(T.DEMAND_VALUE,0))                                             AS POTENTIAL_DEMAND_VALUE,
        SUM(NVL(T.SO_QUANTITY,0))                                              AS ORDER_INTAKE_SO_FAR_QTY,
        SUM(NVL(T.POTENTIAL_DEMAND_QUANTITY,0)) - SUM(NVL(T.SO_QUANTITY,0))    AS POTENTIAL_DEMAND_LEFT_QTY,
        SUM(NVL(T.AVAILABLE_STOCK,0))                                          AS AVAILABLE_STOCK_QTY,
        SUM(NVL(T.CURRENT_MONTH_FCST,0))                                       AS CURRENT_MONTH_FCST
        <choose>
            <when test="report1Version == 'Current'.toString()">
                FROM ${SCPA.DISTRIBUTOR_DEMAND_V} T
            </when>
            <otherwise>
                FROM (SELECT * FROM ${SCPA.DISTRIBUTOR_DEMAND_HIST} WHERE DATE$ = TO_DATE(#{report1Version}, 'YYYY-MM-DD')) T
            </otherwise>
        </choose>
        <where>
            <include refid="filter"/>
        </where>
        GROUP BY
        <foreach collection="report1ViewColumns" separator="," item="item">
            NVL(${item},'Others')
        </foreach>
        ORDER BY
        <foreach collection="report1ViewColumns" separator="," item="item">
            DECODE(${item}, 'Others', CAST(UNISTR('\ffff\ffff') AS VARCHAR2(8)), ${item})
        </foreach>
        , DECODE(POTENTIAL_DEMAND_QTY, 0, 0, ORDER_INTAKE_SO_FAR_QTY / POTENTIAL_DEMAND_QTY)
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport1DetailsCount" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1Details" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport1DetailsSQL">
        SELECT *
        <choose>
            <when test="report1Version == 'Current'.toString()">
                FROM ${SCPA.DISTRIBUTOR_DEMAND_V} T
            </when>
            <otherwise>
                FROM (SELECT * FROM SCPA.DISTRIBUTOR_DEMAND_HIST WHERE DATE$ = TO_DATE(#{report1Version}, 'YYYY-MM-DD')) T
            </otherwise>
        </choose>
        <where>
            <if test="report1SelectedValue != null and report1SelectedValue.isEmpty() == false">
                <foreach collection="report1ViewColumns" separator=" and " item="item" index="index" open=" and ">
                    <choose>
                        <when test="item == 'Others'.toString()">
                            (${item} = 'Others' or ${item} is null)
                        </when>
                        <when test="item != null and item != ''.toString()">
                            ${item} = #{report1SelectedValue[${index}], jdbcType=VARCHAR}
                        </when>
                    </choose>
                </foreach>
            </if>
            <include refid="filter"/>
        </where>
    </sql>

    <select id="queryVersionList" resultType="java.lang.String">
        SELECT 'Current' AS VERSION
        FROM DUAL
        UNION
        SELECT DISTINCT TO_CHAR(DATE$, 'YYYY-MM-DD')
        FROM SCPA.DISTRIBUTOR_DEMAND_HIST
        WHERE DATE$ = TRUNC(ADD_MONTHS(COLLECTION_DATE, +1), 'MM')
    </select>
</mapper>
