<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.demand.dao.IWaterfallDao">
    <sql id="waterfall_filter">
        <if test="_filters != null and _filters != ''.toString()">
            and ${_filters}
        </if>
        <if test="scope != null and scope.size() > 0">
            and (t.SE_SCOPE in
            <foreach collection="scope" separator="," open="(" close=")" item="item">
                #{item, jdbcType=VARCHAR}
            </foreach>
            OR t.plant_scope in
            <foreach collection="scope" separator="," open="(" close=")" item="item">
                #{item, jdbcType=VARCHAR}
            </foreach>
            )
        </if>
        <if test="materialList != null and materialList.size() > 0">
            <foreach collection="materialList" item="list" separator=" or " open=" and (" close=")">
                t.<PERSON><PERSON>RI<PERSON> in
                <foreach collection="list" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </foreach>
        </if>
    </sql>

    <select id="queryCascader" resultType="java.util.Map">
        SELECT *
        FROM (
                 SELECT T.NAME, T.CATEGORY
                 FROM DEMAND_TRACKING_FILTER_V T
                 WHERE T.CATEGORY NOT IN ('DELETION', 'VENDOR_CODE', 'VENDOR_NAME', 'ACTIVE', 'SOH', 'ORDER_TYPE')
             ) MM
        ORDER BY MM.CATEGORY, DECODE(MM.NAME, 'Others', 'zzz', MM.NAME)
    </select>

    <select id="queryFcstScopeOptions" resultType="java.lang.String">
        SELECT DISTINCT FCST_SCOPE
        FROM SCPA.DEMAND_FCST_V
        ORDER BY FCST_SCOPE
    </select>

    <select id="queryReport" parameterType="java.util.Map" resultType="java.util.Map">
        select /*+ parallel(t 6) */ t.fcst_version,
               <foreach collection="column" separator="," item="item">${item}</foreach>
        from ${SCPA.DEMAND_FCST_V} t
        where t.fcst_version between #{startMonth, jdbcType=VARCHAR} and #{endMonth, jdbcType=VARCHAR}
        <include refid="waterfall_filter"/>
        <if test="fcstMppSource != ''.toString() and fcstMppSource != null">
            AND FCST_SCOPE = #{fcstMppSource}
        </if>
        group by fcst_version order by fcst_version
    </select>

    <select id="queryReportFCSTNPIEOL" parameterType="java.util.Map" resultType="java.util.Map">
        select /*+ parallel(t 6) */ t.fcst_version,t.subt_flag,
               <foreach collection="column" separator="," item="item">${item}</foreach>
        from ${SCPA.DEMAND_FCST_V} t
        where t.fcst_version between #{startMonth, jdbcType=VARCHAR} and #{endMonth, jdbcType=VARCHAR}
        and t.subt_flag in ('NPI', 'EOL')
        <include refid="waterfall_filter"/>
        <if test="fcstMppSource != ''.toString() and fcstMppSource != null">
            AND FCST_SCOPE = #{fcstMppSource}
        </if>
        group by fcst_version, subt_flag order by fcst_version
    </select>

    <select id="queryReportCrd" parameterType="java.util.Map" resultType="java.util.Map">
        select /*+ parallel(t 6) */ t.CALENDAR_MONTH,  ${valueColumn} as "VALUE"
               from ${SCPA.DEMAND_CRD_V} t
         where t.CALENDAR_MONTH between #{startMonth, jdbcType=VARCHAR} and #{endMonth, jdbcType=VARCHAR}
           and t.bom_category != 'HEADER'
         <include refid="waterfall_filter"/>
         group by t.CALENDAR_MONTH
    </select>

    <select id="queryReportOrderIntake" parameterType="java.util.Map" resultType="java.util.Map">
        select /*+ parallel(t 6) */ t.CALENDAR_MONTH,  ${valueColumn} as "VALUE"
          from ${SCPA.DEMAND_ORDER_INTAKE_V} t
         where t.CALENDAR_MONTH between #{startMonth, jdbcType=VARCHAR} and #{endMonth, jdbcType=VARCHAR}
           and t.bom_category != 'HEADER'
         <include refid="waterfall_filter"/>
         group by t.CALENDAR_MONTH
    </select>

    <select id="queryReportOrderIntakeNPIEOL" parameterType="java.util.Map" resultType="java.util.Map">
        select /*+ parallel(t 6) */ t.CALENDAR_MONTH, t.SUBT_FLAG, ${valueColumn} as "VALUE"
          from ${SCPA.DEMAND_ORDER_INTAKE_V} t
         where t.CALENDAR_MONTH between #{startMonth, jdbcType=VARCHAR} and #{endMonth, jdbcType=VARCHAR}
           and t.bom_category != 'HEADER'
           and t.subt_flag in ('NPI', 'EOL')
         <include refid="waterfall_filter"/>
         group by t.CALENDAR_MONTH, t.SUBT_FLAG
    </select>

    <select id="queryReportSales" parameterType="java.util.Map" resultType="java.util.Map">
        select /*+ parallel(t 6) */ CALENDAR_MONTH,  ${valueColumn} as "VALUE"
          from ${SCPA.DEMAND_SALES_V} t
         where CALENDAR_MONTH between #{startMonth, jdbcType=VARCHAR} and #{endMonth, jdbcType=VARCHAR}
           and t.bom_category != 'HEADER'
         <include refid="waterfall_filter"/>
         group by CALENDAR_MONTH
    </select>

    <select id="queryReportSalesNPIEOL" parameterType="java.util.Map" resultType="java.util.Map">
        select /*+ parallel(t 6) */ CALENDAR_MONTH, SUBT_FLAG,  ${valueColumn} as "VALUE"
          from ${SCPA.DEMAND_SALES_V} t
         where CALENDAR_MONTH between #{startMonth, jdbcType=VARCHAR} and #{endMonth, jdbcType=VARCHAR}
           and t.bom_category != 'HEADER'
           and t.subt_flag in ('NPI', 'EOL')
         <include refid="waterfall_filter"/>
         group by CALENDAR_MONTH, SUBT_FLAG
    </select>

    <select id="queryReport2" parameterType="java.util.Map" resultType="java.util.Map">
        SELECT /*+ parallel(t 6) */ t.CALENDAR_MONTH,
               SUM((NVL(t.ORDER_QUANTITY, 0) - NVL(OPSO.OPEN_SO_W_O_GI, 0)) ${valueColumn})   GI,
               SUM(NVL(OPSO.OPEN_UD_QTY, 0) ${valueColumn})                                UD,
               SUM((NVL(OPSO.OPEN_SO_W_O_GI, 0) - NVL(OPSO.OPEN_UD_QTY, 0)) ${valueColumn})   BACKLOG
        FROM ${SCPA.DEMAND_CRD_V} t
                 LEFT JOIN ${SCPA.DEMAND_BACKLOG_V} OPSO
                           ON t.SALES_ORDER_NUMBER = OPSO.SALES_ORDER_NUMBER
                               AND t.SALES_ORDER_ITEM = OPSO.SALES_ORDER_ITEM
        WHERE t.CALENDAR_MONTH BETWEEN #{startMonth, jdbcType=VARCHAR} and #{endMonth, jdbcType=VARCHAR}
          and t.bom_category != 'HEADER'
        <include refid="waterfall_filter"/>
        GROUP BY t.CALENDAR_MONTH
    </select>

    <sql id="queryReport2DetailSQL">
        SELECT * FROM (
            SELECT to_char(t.CALENDAR_DATE, 'yyyy/mm/dd') CRD_DATE,
                   t.MATERIAL,
                   t.PLANT_CODE,
                   t.MATERIAL_OWNER_NAME,
                   T.MATERIAL_OWNER_SESA,
                   t.SALES_ORDER_NUMBER,
                   t.SALES_ORDER_ITEM,
                   t.HIGHER_LEVEL_ITEM,
                   t.ORDER_CATEGORY,
                   t.BOM_CATEGORY,
                   t.UNIT_COST,
                   t.AVG_SELLING_PRICE_RMB,
                   t.ORDER_QUANTITY,
                   t.ORDER_TYPE,
                   t.SALES_ORGANIZATION,
                   t.SOLD_TO,
                   t.SOLD_TO_COUNTRY,
                   t.SHIP_TO,
                   t.SHIP_TO_REGION,
                   t.SHIP_TO_SUB_REGION,
                   t.MRP_CONTROLLER,
                   t.PRODUCT_LINE,
                   t.ENTITY,
                   t.CLUSTER_NAME,
                   t.BU,
                   t.LOCAL_BU,
                   t.LOCAL_PRODUCT_FAMILY,
                   t.LOCAL_PRODUCT_LINE,
                   t.LOCAL_PRODUCT_SUBFAMILY,
                   t.STOCKING_POLICY,
                   t.VENDOR_CODE,
                   t.VENDOR_NAME,
                   t.ACTIVENESS,
                   t.SOURCE_CATEGORY,
                   t.ORDER_QUANTITY * t.UNIT_COST AS MVP,
                   (NVL(t.ORDER_QUANTITY, 0) - NVL(OPSO.OPEN_SO_W_O_GI, 0)) ${valueColumn}   GI,
                   NVL(OPSO.OPEN_UD_QTY, 0) ${valueColumn}                                UD,
                   (NVL(OPSO.OPEN_SO_W_O_GI, 0) - NVL(OPSO.OPEN_UD_QTY, 0)) ${valueColumn}   BACKLOG
            FROM ${SCPA.DEMAND_CRD_V} t
                     LEFT JOIN ${SCPA.DEMAND_BACKLOG_V} OPSO
                               ON t.SALES_ORDER_NUMBER = OPSO.SALES_ORDER_NUMBER
                                   AND t.SALES_ORDER_ITEM = OPSO.SALES_ORDER_ITEM
            WHERE t.CALENDAR_MONTH = #{report2SelectedDate, jdbcType=VARCHAR}
              and t.bom_category != 'HEADER'
            <include refid="waterfall_filter"/>
        ) mm
        where
        <choose>
            <when test="report2SelectedType == 'CRD-GI'.toString()">
                mm.GI > 0
            </when>
            <when test="report2SelectedType == 'CRD-UD'.toString()">
                mm.UD > 0
            </when>
            <when test="report2SelectedType == 'CRD-Backlog'.toString()">
                mm.BACKLOG > 0
            </when>
            <otherwise> 1 = 0 </otherwise>
        </choose>
        order by CRD_DATE
    </sql>

    <select id="queryReport2DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport2DetailSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport2Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport2DetailSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport3" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        SELECT <foreach collection="report3Column" item="item">
                    MM.${item},
               </foreach>
               SUM(MM.SALES_VALUE) SALES_VALUE,
               SUM(MM.CRD_VALUE) CRD_VALUE,
               SUM(MM.UD_VALUE) UD_VALUE,
               SUM(MM.BACKLOG_VALUE) BACKLOG_VALUE,
               SUM(MM.ORDERINTAKE_VALUE) ORDERINTAKE_VALUE,
               <choose>
                    <when test="sortColumn == 'CRD_VALUE'.toString()">
                        SUM(MM.CRD_VALUE)
                    </when>
                    <when test="sortColumn == 'BACKLOG_VALUE'.toString()">
                        SUM(MM.BACKLOG_VALUE)
                    </when>
                    <when test="sortColumn == 'ORDERINTAKE_VALUE'.toString()">
                        SUM(MM.ORDERINTAKE_VALUE)
                    </when>
                    <when test="sortColumn == 'UD_VALUE'.toString()">
                        SUM(MM.UD_VALUE)
                    </when>
                    <otherwise>
                        SUM(MM.SALES_VALUE)
                    </otherwise>
                </choose> AS RATIO_VALUE
        FROM (
            SELECT COALESCE(SALES.PROJECT_NAME, CRD.PROJECT_NAME, BACKLOG.PROJECT_NAME)                      AS PROJECT_NAME,
                   COALESCE(SALES.SOLD_TO_SHORT_NAME, CRD.SOLD_TO_SHORT_NAME, BACKLOG.SOLD_TO_SHORT_NAME)    AS SOLD_TO_SHORT_NAME,
                   COALESCE(SALES.SOLD_TO_FULL_NAME, CRD.SOLD_TO_FULL_NAME, BACKLOG.SOLD_TO_FULL_NAME)       AS SOLD_TO_FULL_NAME,
                   COALESCE(SALES.SHIP_TO_COUNTRY, CRD.SHIP_TO_COUNTRY, BACKLOG.SHIP_TO_COUNTRY)             AS SHIP_TO_COUNTRY,
                   COALESCE(SALES.SOLD_TO_PARENT_NAME, CRD.SOLD_TO_PARENT_NAME, BACKLOG.SOLD_TO_PARENT_NAME) AS SOLD_TO_PARENT_NAME,
                   COALESCE(SALES.SOLD_TO_PARENT_CODE, CRD.SOLD_TO_PARENT_CODE, BACKLOG.SOLD_TO_PARENT_CODE) AS SOLD_TO_PARENT_CODE,
                   COALESCE(SALES.SHIP_TO_SHORT_NAME, CRD.SHIP_TO_SHORT_NAME, BACKLOG.SHIP_TO_SHORT_NAME)    AS SHIP_TO_SHORT_NAME,
                   COALESCE(SALES.SHIP_TO_FULL_NAME, CRD.SHIP_TO_FULL_NAME, BACKLOG.SHIP_TO_FULL_NAME)       AS SHIP_TO_FULL_NAME,
                   COALESCE(SALES.SHIP_TO_PARENT_NAME, CRD.SHIP_TO_PARENT_NAME, BACKLOG.SHIP_TO_PARENT_NAME) AS SHIP_TO_PARENT_NAME,
                   COALESCE(SALES.SHIP_TO_PARENT_CODE, CRD.SHIP_TO_PARENT_CODE, BACKLOG.SHIP_TO_PARENT_CODE) AS SHIP_TO_PARENT_CODE,
                   SALES.SALES_VALUE,
                   CRD.CRD_VALUE,
                   BACKLOG.BACKLOG_VALUE,
                   BACKLOG.UD_VALUE,
                   ORDERINTAKE.ORDERINTAKE_VALUE
            FROM (SELECT /*+ parallel(t 6) */
                         MAX(T.PROJECT_NAME)        AS PROJECT_NAME,
                         MAX(T.SOLD_TO_SHORT_NAME)  AS SOLD_TO_SHORT_NAME,
                         MAX(T.SOLD_TO_FULL_NAME)   AS SOLD_TO_FULL_NAME,
                         MAX(T.SHIP_TO_COUNTRY)     AS SHIP_TO_COUNTRY,
                         MAX(T.SOLD_TO_PARENT_NAME) AS SOLD_TO_PARENT_NAME,
                         MAX(T.SOLD_TO_PARENT_CODE) AS SOLD_TO_PARENT_CODE,
                         MAX(T.SHIP_TO_SHORT_NAME)  AS SHIP_TO_SHORT_NAME,
                         MAX(T.SHIP_TO_FULL_NAME)   AS SHIP_TO_FULL_NAME,
                         MAX(T.SHIP_TO_PARENT_NAME) AS SHIP_TO_PARENT_NAME,
                         MAX(T.SHIP_TO_PARENT_CODE) AS SHIP_TO_PARENT_CODE,
                         T.SALES_ORDER_NUMBER,
                         T.SALES_ORDER_ITEM,
                         SUM(${valueColumn}) SALES_VALUE
                  FROM ${SCPA.DEMAND_SALES_V} T
                  WHERE T.CALENDAR_MONTH BETWEEN #{startMonth, jdbcType=VARCHAR} and #{endMonth, jdbcType=VARCHAR}
                  <include refid="waterfall_filter"/>
                  GROUP BY T.SALES_ORDER_NUMBER, T.SALES_ORDER_ITEM
                 ) SALES

                     FULL JOIN (SELECT /*+ parallel(t 6) */
                                       T.PROJECT_NAME,
                                       T.SOLD_TO_SHORT_NAME,
                                       T.SOLD_TO_FULL_NAME,
                                       T.SHIP_TO_COUNTRY,
                                       T.SOLD_TO_PARENT_NAME,
                                       T.SOLD_TO_PARENT_CODE,
                                       T.SHIP_TO_SHORT_NAME,
                                       T.SHIP_TO_FULL_NAME,
                                       T.SHIP_TO_PARENT_NAME,
                                       T.SHIP_TO_PARENT_CODE,
                                       T.SALES_ORDER_NUMBER,
                                       T.SALES_ORDER_ITEM,
                                       ${valueColumn} CRD_VALUE
                                FROM ${SCPA.DEMAND_CRD_V} T
                                WHERE T.CALENDAR_MONTH BETWEEN #{startMonth, jdbcType=VARCHAR} and #{endMonth, jdbcType=VARCHAR}
                                <include refid="waterfall_filter"/>
                     ) CRD ON SALES.SALES_ORDER_NUMBER = CRD.SALES_ORDER_NUMBER AND SALES.SALES_ORDER_ITEM = CRD.SALES_ORDER_ITEM

                     FULL JOIN (SELECT /*+ parallel(t 6) */
                                       T.PROJECT_NAME,
                                       T.SOLD_TO_SHORT_NAME,
                                       T.SOLD_TO_FULL_NAME,
                                       T.SHIP_TO_COUNTRY,
                                       T.SOLD_TO_PARENT_NAME,
                                       T.SOLD_TO_PARENT_CODE,
                                       T.SHIP_TO_SHORT_NAME,
                                       T.SHIP_TO_FULL_NAME,
                                       T.SHIP_TO_PARENT_NAME,
                                       T.SHIP_TO_PARENT_CODE,
                                       T.SALES_ORDER_NUMBER,
                                       T.SALES_ORDER_ITEM,
                                       ${valueColumn} BACKLOG_VALUE,
                                       ${udColumn} UD_VALUE
                                FROM ${SCPA.DEMAND_BACKLOG_V} T
                                WHERE T.CALENDAR_MONTH BETWEEN #{startMonth, jdbcType=VARCHAR} and #{endMonth, jdbcType=VARCHAR}
                                <include refid="waterfall_filter"/>
                     ) BACKLOG ON SALES.SALES_ORDER_NUMBER = BACKLOG.SALES_ORDER_NUMBER AND SALES.SALES_ORDER_ITEM = BACKLOG.SALES_ORDER_ITEM

                     FULL JOIN (  SELECT /*+ parallel(t 6) */
                                         MAX(T.PROJECT_NAME)        PROJECT_NAME,
                                         MAX(T.SOLD_TO_SHORT_NAME)  SOLD_TO_SHORT_NAME,
                                         MAX(T.SOLD_TO_FULL_NAME)   SOLD_TO_FULL_NAME,
                                         MAX(T.SOLD_TO_PARENT_NAME) SOLD_TO_PARENT_NAME,
                                         MAX(T.SOLD_TO_PARENT_CODE) SOLD_TO_PARENT_CODE,
                                         MAX(T.SHIP_TO_COUNTRY)     SHIP_TO_COUNTRY,
                                         MAX(T.SHIP_TO_SHORT_NAME)  SHIP_TO_SHORT_NAME,
                                         MAX(T.SHIP_TO_FULL_NAME)   SHIP_TO_FULL_NAME,
                                         MAX(T.SHIP_TO_PARENT_NAME) SHIP_TO_PARENT_NAME,
                                         MAX(T.SHIP_TO_PARENT_CODE) SHIP_TO_PARENT_CODE,
                                         T.SALES_ORDER_NUMBER,
                                         T.SALES_ORDER_ITEM,
                                         SUM(${valueColumn}) ORDERINTAKE_VALUE
                                  FROM ${SCPA.DEMAND_ORDER_INTAKE_V} T
                                  WHERE T.CALENDAR_MONTH BETWEEN #{startMonth, jdbcType=VARCHAR} and #{endMonth, jdbcType=VARCHAR}
                                  <include refid="waterfall_filter"/>
                                  GROUP BY T.SALES_ORDER_NUMBER, T.SALES_ORDER_ITEM
                     ) ORDERINTAKE ON SALES.SALES_ORDER_NUMBER = ORDERINTAKE.SALES_ORDER_NUMBER AND SALES.SALES_ORDER_ITEM = ORDERINTAKE.SALES_ORDER_ITEM
        ) MM GROUP BY
        <foreach collection="report3Column" item="item" separator=",">
            MM.${item}
        </foreach>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport3Total" resultType="java.util.Map">
        SELECT CRD.TOTAL_VALUE AS CRD_TOTAL_VALUE,
               BACKLOG.TOTAL_VALUE AS BACKLOG_TOTAL_VALUE,
               BACKLOG.UD_TOTAL_VALUE,
               SALES.TOTAL_VALUE AS SALES_TOTAL_VALUE,
               ORDERINTAKE.ORDERINTAKE_VALUE
          FROM
          (
            SELECT SUM(${valueColumn}) TOTAL_VALUE
              FROM ${SCPA.DEMAND_CRD_V} T
             WHERE T.CALENDAR_MONTH BETWEEN #{startMonth, jdbcType=VARCHAR} and #{endMonth, jdbcType=VARCHAR}
             <include refid="waterfall_filter"/>
          ) CRD,
          (
            SELECT SUM(${valueColumn}) ORDERINTAKE_VALUE
              FROM ${SCPA.DEMAND_ORDER_INTAKE_V} T
             WHERE T.CALENDAR_MONTH BETWEEN #{startMonth, jdbcType=VARCHAR} and #{endMonth, jdbcType=VARCHAR}
             <include refid="waterfall_filter"/>
          ) ORDERINTAKE,
          (
            SELECT SUM(${valueColumn}) TOTAL_VALUE,
                   SUM(${udColumn}) UD_TOTAL_VALUE
              FROM ${SCPA.DEMAND_BACKLOG_V} T
             WHERE T.CALENDAR_MONTH BETWEEN #{startMonth, jdbcType=VARCHAR} and #{endMonth, jdbcType=VARCHAR}
             <include refid="waterfall_filter"/>
          ) BACKLOG,
          (
            SELECT SUM(${valueColumn}) TOTAL_VALUE
              FROM ${SCPA.DEMAND_SALES_V} T
             WHERE T.CALENDAR_MONTH BETWEEN #{startMonth, jdbcType=VARCHAR} and #{endMonth, jdbcType=VARCHAR}
             <include refid="waterfall_filter"/>
          ) SALES
    </select>
</mapper>
