<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.demand.dao.IAbnormalDemandDao">
    <select id="queryFcstVersion" resultType="java.lang.String">
        select distinct fcst_version
          from MR3_COLLABORATIVE_SALES_FORECAST
         order by fcst_version desc
        offset 0 rows fetch next 12 rows only
    </select>

    <select id="queryCascader" resultType="java.util.Map">
        select * from DEMAND_TRACKING_FILTER_V t where t.CATEGORY in
        ('PLANT_CODE','MRP_CONTROLLER','PRODUCT_LINE','CLUSTER_NAME','ENTITY','BU','LOCAL_BU','LOCAL_PRODUCT_FAMILY','LOCAL_PRODUCT_LINE','LOCAL_PRODUCT_SUBFAMILY','STOCKING_POLICY','ACTIVENESS','DELETION')
         order by t.category,decode(t.name,'Others','zzz',t.name)
    </select>

    <select id="queryReportColumns" parameterType="java.util.Map" resultType="java.lang.String">
        select distinct mm.COLUMN_NAME
          from (
                   select substr(t.COLUMN_NAME, 0, instr(t.COLUMN_NAME, '_', 1, 1) - 1) COLUMN_NAME
                     from USER_TAB_COLUMNS t
                    where t.TABLE_NAME = 'SALES_PIVOT_V'
                      and t.COLUMN_NAME like '2%') mm
         order by mm.COLUMN_NAME
    </select>

    <sql id="queryReportSQL">
        select * from (
            select
                material, plant_code, max(cluster_name) cluster_name, max(entity) entity, max(bu) bu,
                max(product_line) product_line, max(local_bu) local_bu,
                max(local_product_family) local_product_family, max(local_product_line) local_product_line,
                max(local_product_subfamily) local_product_subfamily, max(mrp_controller) mrp_controller,
                max(material_owner_name) material_owner_name,
                max(material_owner_sesa) material_owner_sesa,
                max(stocking_policy) stocking_policy,
                max(vendor_code) vendor_code,max(vendor_name) vendor_name,sum(cov) cov, max(wd_ratio) wd_ratio,
                max(top1_customer_code) top1_customer_code, max(top1_customer_name) top1_customer_name,
                max(top2_customer_code) top2_customer_code,max(top2_customer_name) top2_customer_name,
                max(top3_customer_code) top3_customer_code,max(top3_customer_name) top3_customer_name,
                <foreach collection="sumColumns" item="item" separator=",">
                    ${item}
                </foreach>
            from (
                select  t.material, t.plant_code, t.cluster_name, t.entity, t.bu, t.product_line,
                        t.local_bu,t.local_product_family,t.local_product_line,
                        t.local_product_subfamily,t.mrp_controller, t.material_owner_name, t.material_owner_sesa,
                        t.stocking_policy, t.vendor_code,
                        t.vendor_name, t.cov, t.wd_ratio, t.top1_customer_code, t.top1_customer_name, t.top2_customer_code,
                        t.top2_customer_name, t.top3_customer_code, t.top3_customer_name,
                       <foreach collection="columns" item="item" separator=",">
                            ${item}
                       </foreach>
                <choose>
                    <when test="scope != null and scope.isEmpty() == false">
                        from SALES_PIVOT_V t left join DEMAND_FCST_V t2 on t.material = t2.material and t.plant_code = t2.plant_code and t2.fcst_version = #{fcstVersion, jdbcType=VARCHAR}
                        where t.category = #{salesType, jdbcType=VARCHAR}
                        <if test="scope != null and scope != ''.toString()">
                            <choose>
                                <when test="subScope == null or subScope.size() == 0">
                                    <choose>
                                        <when test="scope == 'SECI'.toString()">
                                            AND T.SE_SCOPE IS NOT NULL
                                        </when>
                                        <when test="scope == 'PLANT'.toString()">
                                            AND t.PLANT_SCOPE IS NOT NULL
                                        </when>
                                    </choose>
                                </when>
                                <otherwise>
                                    <choose>
                                        <when test="scope == 'SECI'.toString()">
                                            AND T.SE_SCOPE IN
                                            <foreach collection="subScope" separator="," open="(" close=")" item="item">
                                                #{item, jdbcType=VARCHAR}
                                            </foreach>
                                        </when>
                                        <when test="scope == 'PLANT'.toString()">
                                            AND t.PLANT_SCOPE IN
                                            <foreach collection="subScope" separator="," open="(" close=")" item="item">
                                                #{item, jdbcType=VARCHAR}
                                            </foreach>
                                        </when>
                                    </choose>
                                </otherwise>
                            </choose>
                        </if>
                    </when>
                    <otherwise>
                        from SALES_PIVOT_ALL_V t left join DEMAND_FCST_V t2 on t.material = t2.material and t.plant_code = t2.plant_code and t2.fcst_version = #{fcstVersion, jdbcType=VARCHAR}
                        where t.category = #{salesType, jdbcType=VARCHAR}
                    </otherwise>
                </choose>
                <if test="filters != null and filters != ''.toString()">and ${filters}</if>
                <if test="materialList != null and materialList.isEmpty() == false">
                    <foreach collection="materialList" item="list" separator=" or " open=" and (" close=")">
                        t.material in
                        <foreach collection="list" item="item" separator="," open="(" close=")">
                            #{item,jdbcType=VARCHAR}
                        </foreach>
                    </foreach>
                </if>
        ) mm group by material, plant_code ) nn
        where nn.FULFILL_RATIO > #{warningValue, jdbcType=INTEGER}
    </sql>

    <select id="queryReportCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReportSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReportSQL"/>
        <include refid="global.select_footer"/>
    </select>
</mapper>
