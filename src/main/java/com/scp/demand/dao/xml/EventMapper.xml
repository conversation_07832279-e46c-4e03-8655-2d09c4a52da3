<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.demand.dao.IEventDao">
    <select id="queryCascader" resultType="java.util.Map">
        select * from dp_event_notes_filter_v order by category,decode (name,'Others','zzz',name)
    </select>

    <select id="queryEventClass" resultType="java.lang.String">
        select event_class_name from dp_event_class
    </select>

    <select id="queryEventStatus" resultType="java.lang.String">
        select event_status_name from dp_event_status
    </select>

    <select id="queryCycleMonth" resultType="java.lang.String">
        select distinct cycle_month from dp_event_notes order by cycle_month desc
    </select>

    <select id="queryNotesFilter" resultType="java.lang.String">
        select name from dp_event_notes_filter_v t where t.category = #{category, jdbcType=VARCHAR} order by name
    </select>

    <select id="queryAdminCnt" resultType="java.lang.Integer">
        select count(1) from SY_MENU_AUTH t
         where t.user_id = upper(#{userid,jdbcType=VARCHAR})
           and t.menu_code = #{parentCode,jdbcType=VARCHAR}
           and t.accessible = 'true'
           and lower(t.auth_details) = 'admin'
    </select>

    <sql id="queryReport1Sql">
        select  ROWIDTOCHAR(rowid) row_id,
                cycle,
                business_unit,
                sales_organization,
                local_product_line,
                local_product_family,
                local_product_subfamily,
                event_start_date,
                event_end_date,
                event_month,
                <choose>
                    <when test="isAdmin == true">
                        event_class,
                        event_status,
                        notice_anounce_date,
                        price_increase_ratio,
                        incentive_ratio,
                        order_sales_driven,
                        channel,
                        enrichment_value,
                        consider_or_not,
                        remark,
                        show_in_data_pack
                    </when>
                    <otherwise>
                        event_class
                    </otherwise>
                </choose>
        from dp_event_notes t
        where 1 = 1
        <if test="isAdmin == false">
            and t.show_in_data_pack = 'Y'
        </if>
        <if test="eventClass != null and eventClass.isEmpty() == false">
            and t.event_class in
            <foreach collection="eventClass" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="eventStatus != null and eventStatus.isEmpty() == false">
            and t.event_status in
            <foreach collection="eventStatus" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="cycleMonth != null and cycleMonth.isEmpty() == false">
            and t.cycle_month in
            <foreach collection="cycleMonth" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="filters != null and filters != ''.toString()">
            and ${filters}
        </if>
    </sql>

    <select id="queryReport1Count" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1Sql"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1Sql"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="downloadReport1" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        select  to_char(cycle,'yyyy/mm/dd') cycle,
                sales_organization,
                business_unit,
                local_product_line,
                local_product_family,
                local_product_subfamily,
                to_char(event_start_date,'yyyy/mm/dd') event_start_date,
                to_char(event_end_date,'yyyy/mm/dd') event_end_date,
                event_month,
                <choose>
                    <when test="isAdmin == true">
                        event_class,
                        event_status,
                        to_char(notice_anounce_date,'yyyy/mm/dd') notice_anounce_date,
                        price_increase_ratio,
                        incentive_ratio,
                        order_sales_driven,
                        channel,
                        enrichment_value,
                        consider_or_not,
                        remark,
                        show_in_data_pack
                    </when>
                    <otherwise>
                        event_class
                    </otherwise>
                </choose>
        from (
        <include refid="queryReport1Sql"/>
        ) mm
        <include refid="global.select_footer"/>
    </select>

    <update id="updateReport1">
        update dp_event_notes
        SET
        <foreach collection="updates" item="col" separator=",">
            <choose>
                <when test="col.key == 'CYCLE'.toString()
                            or col.key == 'EVENT_START_DATE'.toString()
                            or col.key == 'EVENT_END_DATE'.toString()
                            or col.key == 'NOTICE_ANOUNCE_DATE'.toString()">
                    ${col.key} = to_date(#{col.value,jdbcType=VARCHAR}, 'yyyy/mm/dd')
                </when>
                <otherwise>${col.key} = #{col.value,jdbcType=VARCHAR}</otherwise>
            </choose>
        </foreach>,
        update_by$ = #{userid,jdbcType=VARCHAR},
        update_date$ = sysdate
        where rowid = #{rowid,jdbcType=VARCHAR} and create_by$ = #{userid,jdbcType=VARCHAR}
    </update>

    <insert id="createReport1">
        insert into dp_event_notes
        (
            <foreach collection="headers" item="header" separator=",">
                ${header}
            </foreach>, create_by$, create_date$
        )
        <foreach collection="createList" item="list" separator=" union all ">
            select
            <foreach collection="headers" item="header" separator=",">
                <choose>
                    <when test="header == 'EVENT_START_DATE'.toString()">to_date(#{list.${header}, jdbcType=VARCHAR},'yyyy/mm/dd')</when>
                    <when test="header == 'EVENT_END_DATE'.toString()">to_date(#{list.${header}, jdbcType=VARCHAR},'yyyy/mm/dd')</when>
                    <when test="header == 'NOTICE_ANOUNCE_DATE'.toString()">to_date(#{list.${header}, jdbcType=VARCHAR},'yyyy/mm/dd')</when>
                    <when test="header == 'CYCLE'.toString()">to_date(#{list.${header}, jdbcType=VARCHAR},'yyyy/mm/dd')</when>
                    <otherwise>#{list.${header}, jdbcType=VARCHAR}</otherwise>
                </choose>
            </foreach>, #{userid,jdbcType=VARCHAR}, sysdate
            from dual
        </foreach>
    </insert>

    <delete id="deleteReport1">
        delete from dp_event_notes where rowid in
        <foreach collection="list" open="(" close=")" separator="," item="item">#{item, jdbcType=VARCHAR}</foreach>
        and create_by$ = #{userid,jdbcType=VARCHAR}
    </delete>

    <update id="generateNewNote">
        insert
        /*+  IGNORE_ROW_ON_DUPKEY_INDEX (DP_EVENT_NOTES,DP_EVENT_NOTES_PK) */
        into dp_event_notes
        (CREATE_BY$, CREATE_DATE$, CYCLE, SALES_ORGANIZATION, CYCLE_MONTH, BUSINESS_UNIT,
         LOCAL_PRODUCT_LINE, LOCAL_PRODUCT_FAMILY, LOCAL_PRODUCT_SUBFAMILY, EVENT_START_DATE, EVENT_END_DATE, EVENT_MONTH,
         EVENT_CLASS, EVENT_STATUS, NOTICE_ANOUNCE_DATE, PRICE_INCREASE_RATIO, INCENTIVE_RATIO, ORDER_SALES_DRIVEN, CHANNEL,
         ENRICHMENT_VALUE, CONSIDER_OR_NOT, REMARK, SHOW_IN_DATA_PACK
         )
        select CREATE_BY$,
               sysdate,
               to_date(#{cycleMonth, jdbcType=VARCHAR},'yyyy/mm'),
               SALES_ORGANIZATION,
               #{cycleMonth, jdbcType=VARCHAR},
               BUSINESS_UNIT,
               LOCAL_PRODUCT_LINE,
               LOCAL_PRODUCT_FAMILY,
               LOCAL_PRODUCT_SUBFAMILY,
               EVENT_START_DATE,
               EVENT_END_DATE,
               EVENT_MONTH,
               EVENT_CLASS,
               EVENT_STATUS,
               NOTICE_ANOUNCE_DATE,
               PRICE_INCREASE_RATIO,
               INCENTIVE_RATIO,
               ORDER_SALES_DRIVEN,
               CHANNEL,
               ENRICHMENT_VALUE,
               CONSIDER_OR_NOT,
               REMARK,
               SHOW_IN_DATA_PACK
          from DP_EVENT_NOTES
         where CYCLE_MONTH = #{lastCycleMonth, jdbcType=VARCHAR}
    </update>
</mapper>
