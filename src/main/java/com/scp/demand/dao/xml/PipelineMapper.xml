<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.demand.dao.IPipelineDao">
    <sql id="reportCondition">
        <if test="updateBy != null and updateBy != ''.toString()">
            and (t.update_by$ = #{updateBy, jdbcType=VARCHAR} or t.create_by$ = #{updateBy, jdbcType=VARCHAR})
        </if>
        <if test="productLine != null and productLine.isEmpty() == false">
            and t.product_family in
            <foreach collection="productLine" separator="," open="(" close=")" item="item">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="projectStatus != null and projectStatus.isEmpty() == false">
            and t.project_status in
            <foreach collection="projectStatus" separator="," open="(" close=")" item="item">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="salesTeam != null and salesTeam.isEmpty() == false">
            and t.sales_team in
            <foreach collection="salesTeam" separator="," open="(" close=")" item="item">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="salesPerson != null and salesPerson.isEmpty() == false">
            and t.sales_person in
            <foreach collection="salesPerson" separator="," open="(" close=")" item="item">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
    </sql>

    <select id="querySubmissionDate" resultType="com.starter.context.bean.SimpleSelect">
        select distinct to_char(submission_date, 'yyyy/mm/dd') as "value"
          from mr3_dp_project_pipeline_management
         where SUBMISSION_DATE is not null
         order by "value" desc
    </select>

    <select id="queryUpdateBy" parameterType="java.util.Map" resultType="com.starter.context.bean.SimpleSelect">
        select mm.create_by$ as "value", t.user_name as "label"
          from (
                   select distinct create_by$
                     from mr3_dp_project_pipeline_management
                    where submission_date = to_date(#{submissionDate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
                    union
                   select distinct update_by$
                     from mr3_dp_project_pipeline_management
                    where submission_date = to_date(#{submissionDate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
               ) mm
                   inner join SY_USER_MASTER_DATA t on mm.create_by$ = t.sesa_code
         where mm.create_by$ is not null
    </select>

    <select id="queryProductLine" parameterType="java.util.Map" resultType="com.starter.context.bean.SimpleSelect">
        select distinct product_family as "value"
        from mr3_dp_project_pipeline_management
        where product_family is not null
        <if test="updateBy != null and updateBy != ''.toString()">
            and (update_by$ = #{updateBy, jdbcType=VARCHAR} or create_by$ = #{updateBy, jdbcType=VARCHAR})
        </if>
        and submission_date = to_date(#{submissionDate, jdbcType=VARCHAR},'yyyy/mm/dd')
    </select>

    <select id="queryProjectStatus" parameterType="java.util.Map" resultType="com.starter.context.bean.SimpleSelect">
        select distinct project_status as "value"
        from mr3_dp_project_pipeline_management
        where project_status is not null
        <if test="updateBy != null and updateBy != ''.toString()">
            and (update_by$ = #{updateBy, jdbcType=VARCHAR} or create_by$ = #{updateBy, jdbcType=VARCHAR})
        </if>
        <if test="productLine != null and productLine.isEmpty() == false">
            and product_family in
            <foreach collection="productLine" separator="," open="(" close=")" item="item">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        and submission_date = to_date(#{submissionDate, jdbcType=VARCHAR},'yyyy/mm/dd')
    </select>

    <select id="querySalesTeam" parameterType="java.util.Map" resultType="com.starter.context.bean.SimpleSelect">
        select distinct sales_team as "value"
        from mr3_dp_project_pipeline_management
        where sales_team is not null
        <if test="productLine != null and productLine.isEmpty() == false">
            and product_family in
            <foreach collection="productLine" separator="," open="(" close=")" item="item">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        and submission_date = to_date(#{submissionDate, jdbcType=VARCHAR},'yyyy/mm/dd')
        order by sales_team
    </select>

    <select id="querySalesPerson" parameterType="java.util.Map" resultType="com.starter.context.bean.SimpleSelect">
        select distinct sales_person as "value"
        from mr3_dp_project_pipeline_management
        where sales_person is not null
        <if test="salesTeam != null and salesTeam.isEmpty() == false">
            and sales_team in
            <foreach collection="salesTeam" separator="," open="(" close=")" item="item">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="productLine != null and productLine.isEmpty() == false">
            and product_family in
            <foreach collection="productLine" separator="," open="(" close=")" item="item">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        and submission_date = to_date(#{submissionDate, jdbcType=VARCHAR},'yyyy/mm/dd')
        order by sales_person
    </select>

    <select id="queryReport1Columns" parameterType="java.util.Map" resultType="java.lang.String">
        select mm.column1
          from (
                   select distinct to_char(est_delivery_date, 'MON-yy', 'NLS_DATE_LANGUAGE=AMERICAN') column1, to_char(est_delivery_date, 'yyyymm') column2
                     from mr3_dp_project_pipeline_management
                    where est_delivery_date between to_date(#{deliveryDate[0], jdbcType=VARCHAR}, 'yyyy/mm') and last_day(to_date(#{deliveryDate[1], jdbcType=VARCHAR}, 'yyyy/mm'))
               ) mm
         order by mm.column2
    </select>

    <select id="queryReport1" parameterType="java.util.Map" resultType="java.util.HashMap">
        select material,
        to_char(est_delivery_date,'MON-yy','NLS_DATE_LANGUAGE=AMERICAN') delivery_date,
        sum(${reportType}) qty
        from mr3_dp_project_pipeline_management t
        where submission_date = to_date(#{submissionDate, jdbcType=VARCHAR},'yyyy/mm/dd')
        and est_delivery_date between to_date(#{deliveryDate[0], jdbcType=VARCHAR},'yyyy/mm') and last_day(to_date(#{deliveryDate[1], jdbcType=VARCHAR},'yyyy/mm'))
        <include refid="reportCondition"></include>
        group by to_char(est_delivery_date,'MON-yy','NLS_DATE_LANGUAGE=AMERICAN'), material
    </select>

    <select id="saveReport1Comments" parameterType="java.util.Map">
        merge
        into dp_project_pipeline_comments t
        using (select to_date(#{submissionDate, jdbcType=VARCHAR}, 'yyyy/mm/dd') submission_date, #{report1Comments, jdbcType=VARCHAR} pipeline_comments from dual) s
        on (t.submission_date = s.submission_date)
        when matched then
            update set t.pipeline_comments = s.pipeline_comments
        when not matched then
            insert (submission_date, pipeline_comments)
            values (s.submission_date, s.pipeline_comments)
    </select>

    <select id="queryReport1Comments" parameterType="java.util.Map" resultType="java.lang.Object">
        select pipeline_comments from dp_project_pipeline_comments where submission_date = to_date(#{submissionDate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
    </select>

    <select id="queryReport1Details" parameterType="java.util.Map" resultType="java.util.HashMap">
        select project_name,sales_person,project_status,sum(quantity) qty,sum(forecast_quantity) forecast_quantity, sum(order_value) order_value
          from mr3_dp_project_pipeline_management t
        where submission_date = to_date(#{submissionDate, jdbcType=VARCHAR},'yyyy/mm/dd')
        and est_delivery_date between to_date(#{deliveryDate[0], jdbcType=VARCHAR},'yyyy/mm') and last_day(to_date(#{deliveryDate[1], jdbcType=VARCHAR},'yyyy/mm'))
        <if test="selectedDate != null and selectedDate != ''.toString()">
            and est_delivery_date between to_date(#{selectedDate, jdbcType=VARCHAR},'MON-yy','NLS_DATE_LANGUAGE=AMERICAN') and last_day(to_date(#{selectedDate, jdbcType=VARCHAR},'MON-yy','NLS_DATE_LANGUAGE=AMERICAN'))
        </if>
        <if test="material != null and material != ''.toString()">
            and material = #{material, jdbcType=VARCHAR}
        </if>
        <include refid="reportCondition"></include>
        group by project_name,sales_person,project_status
        order by sum(quantity) desc
    </select>

    <select id="queryReport1Source" parameterType="java.util.Map" resultType="java.util.HashMap">
        select ROWIDTOCHAR(rowid) row_id, project_name, sales_person, material, quantity, forecast_quantity, order_value, dp_comments
        from mr3_dp_project_pipeline_management t
        where submission_date = to_date(#{submissionDate, jdbcType=VARCHAR},'yyyy/mm/dd')
        and est_delivery_date between to_date(#{deliveryDate[0], jdbcType=VARCHAR},'yyyy/mm') and last_day(to_date(#{deliveryDate[1], jdbcType=VARCHAR},'yyyy/mm'))
        <if test="selectedDate != null and selectedDate != ''.toString()">
            and est_delivery_date between to_date(#{selectedDate, jdbcType=VARCHAR},'MON-yy','NLS_DATE_LANGUAGE=AMERICAN') and last_day(to_date(#{selectedDate, jdbcType=VARCHAR},'MON-yy','NLS_DATE_LANGUAGE=AMERICAN'))
        </if>
        <if test="material != null and material != ''.toString()">
            and material = #{material, jdbcType=VARCHAR}
        </if>
        <include refid="reportCondition"></include>
        order by quantity desc
    </select>

    <update id="saveReport1Source" parameterType="java.util.Map">
        update mr3_dp_project_pipeline_management
        SET
        <foreach collection="cols" item="col" separator=",">
            ${col.key} = #{col.value,jdbcType=VARCHAR}
        </foreach>,
        update_by$ = #{userid,jdbcType=VARCHAR},
        update_date$ = sysdate
        where rowid = #{rowid,jdbcType=VARCHAR}
    </update>

    <select id="queryLast2SubmissionDate" parameterType="java.util.Map" resultType="java.lang.String">
        select distinct to_char(submission_date,'yyyy/mm/dd')
          from mr3_dp_project_pipeline_management
         where submission_date <![CDATA[<=]]> to_date(#{submissionDate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
         order by submission_date desc
         fetch next 2 rows only
    </select>

    <select id="queryReport2" parameterType="java.util.Map" resultType="java.util.HashMap">
        select decode(project_status,'丢标','Lost','已下单','Closed',project_status) as project_status,
        ${reportType} as cnt
        from mr3_dp_project_pipeline_management t
        where project_status in ('丢标', '已下单')
        and t.submission_date = to_date(#{currentSubmissionDate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
        <include refid="reportCondition"></include>
        group by project_status

        <choose>
         <when test="lastSubmissionDate != null and lastSubmissionDate != ''.toString()">
             union all

             select 'New Added', ${reportType} cnt
             from mr3_dp_project_pipeline_management t
             where project_status not in ('丢标', '已下单')
             and t.submission_date = to_date(#{currentSubmissionDate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
             and not exists(select 1 from mr3_dp_project_pipeline_management t2 where t.project_name = t2.project_name and t.material = t2.material and t2.submission_date = to_date(#{lastSubmissionDate, jdbcType=VARCHAR}, 'yyyy/mm/dd'))
             <include refid="reportCondition"></include>

             union all

             select 'Inherited', ${reportType} cnt
             from mr3_dp_project_pipeline_management t
             where project_status not in ('丢标', '已下单')
             and t.submission_date = to_date(#{currentSubmissionDate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
             and exists(select 1 from mr3_dp_project_pipeline_management t2 where t.project_name = t2.project_name and t.material = t2.material and t2.submission_date = to_date(#{lastSubmissionDate, jdbcType=VARCHAR}, 'yyyy/mm/dd'))
             <include refid="reportCondition"></include>

             union all

             select 'Missing', ${reportType} cnt
             from mr3_dp_project_pipeline_management t
             where project_status not in ('丢标', '已下单')
             and t.submission_date = to_date(#{lastSubmissionDate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
             and not exists(select 1 from mr3_dp_project_pipeline_management t2 where t.project_name = t2.project_name and t.material = t2.material and t2.submission_date = to_date(#{currentSubmissionDate, jdbcType=VARCHAR}, 'yyyy/mm/dd'))
             <include refid="reportCondition"></include>
         </when>
         <otherwise>
             union all

             select 'New Added' as project_status,
             ${reportType} as cnt
             from mr3_dp_project_pipeline_management t
             where project_status not in ('丢标', '已下单')
             and t.submission_date = to_date(#{currentSubmissionDate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
             <include refid="reportCondition"></include>
         </otherwise>
        </choose>
    </select>

    <select id="queryReport2Summary" parameterType="java.util.Map" resultType="java.util.HashMap">
        select count(1) row_cnt,
               sum(order_value) value_sum,
               to_char(submission_date, 'MON-yy', 'NLS_DATE_LANGUAGE=AMERICAN') submission_month
          from mr3_dp_project_pipeline_management
         where submission_date = to_date(#{submissionDate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
         group by to_char(submission_date, 'MON-yy', 'NLS_DATE_LANGUAGE=AMERICAN')
    </select>

    <select id="queryFullMonthRange" parameterType="java.util.Map" resultType="java.lang.String">
        select m1
          from (select distinct to_char(to_date(text, 'yyyy/mm/dd'), 'MON-yy', 'NLS_DATE_LANGUAGE=AMERICAN') m1,
                                substr(text, 1, 7)                                                           m2
                  from sy_calendar
                 where text between #{submissionDateRange[0], jdbcType=VARCHAR} || '/01' and #{submissionDateRange[1], jdbcType=VARCHAR} || '/31') mm
         order by mm.m2
    </select>

    <select id="queryReport2DetailsCloseOrLost" parameterType="java.util.Map" resultType="java.util.HashMap">
        select to_char(submission_date, 'MON-yy', 'NLS_DATE_LANGUAGE=AMERICAN') submission_month,
               ${reportType}                                                    cnt
          from mr3_dp_project_pipeline_management t
         where submission_date between to_date(#{submissionDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm') and last_day(to_date(#{submissionDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
               and project_status = #{project_status,jdbcType=VARCHAR}
               <include refid="reportCondition"></include>
         group by to_char(submission_date, 'MON-yy', 'NLS_DATE_LANGUAGE=AMERICAN')
    </select>

    <select id="queryReport2DetailsNewAdded" parameterType="java.util.Map" resultType="java.util.HashMap">
        select to_char(t.submission_date, 'MON-yy', 'NLS_DATE_LANGUAGE=AMERICAN') submission_month,
               ${reportType} cnt
          from dp_project_pipeline_management_v t left join dp_project_pipeline_management_v t2
               on t.submission_seq = t2.submission_seq + 1 and t.project_name = t2.project_name and t.material = t2.material
         where t2.project_name is null
           and t.project_status not in ('丢标', '已下单')
           <include refid="reportCondition"></include>
           and t.submission_date between to_date(#{submissionDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm') and last_day(to_date(#{submissionDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
         group by to_char(t.submission_date, 'MON-yy', 'NLS_DATE_LANGUAGE=AMERICAN')
    </select>

    <select id="queryReport2DetailsInherited" parameterType="java.util.Map" resultType="java.util.HashMap">
        select to_char(t.submission_date, 'MON-yy', 'NLS_DATE_LANGUAGE=AMERICAN') submission_month,
               ${reportType} cnt
          from dp_project_pipeline_management_v t inner join dp_project_pipeline_management_v t2
               on t.submission_seq = t2.submission_seq + 1 and t.project_name = t2.project_name and t.material = t2.material
         where t.project_status not in ('丢标', '已下单')
               <include refid="reportCondition"></include>
               and t.submission_date between to_date(#{submissionDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm') and last_day(to_date(#{submissionDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
         group by to_char(t.submission_date, 'MON-yy', 'NLS_DATE_LANGUAGE=AMERICAN')
    </select>

    <select id="queryReport2DetailsInheritedDetails" parameterType="java.util.Map" resultType="java.util.HashMap">
        select to_char(tt.submission_date, 'MON-yy', 'NLS_DATE_LANGUAGE=AMERICAN') submission_month,
            sum(tt.rsd_change) rsd_change,
            sum(tt.qty_change) qty_change,
            sum(tt.keep_flat) keep_flat,
            sum(tt.rsd_change_value) rsd_change_value,
            sum(tt.qty_change_value) qty_change_value,
            sum(tt.keep_flat_value) keep_flat_value
            from (
            select
            mm.submission_date,
            case when mm.est_delivery_date1 != mm.est_delivery_date2 then 1 end rsd_change,
            case when mm.est_delivery_date1 = mm.est_delivery_date2 and mm.quantity1 != mm.quantity2 then 1 end qty_change,
            case when mm.est_delivery_date1 = mm.est_delivery_date2 and mm.quantity1 = mm.quantity2 then 1 end keep_flat,

            case when mm.est_delivery_date1 != mm.est_delivery_date2 then mm.order_value end rsd_change_value,
            case when mm.est_delivery_date1 = mm.est_delivery_date2 and mm.quantity1 != mm.quantity2 then mm.order_value end qty_change_value,
            case when mm.est_delivery_date1 = mm.est_delivery_date2 and mm.quantity1 = mm.quantity2 then mm.order_value end keep_flat_value
            from (
                select
                    t.submission_date,
                    t.quantity quantity1,
                    t2.quantity quantity2,
                    t.order_value,
                    to_char(t.est_delivery_date, 'yyyy/mm') est_delivery_date1,
                    to_char(t2.est_delivery_date, 'yyyy/mm') est_delivery_date2
                from dp_project_pipeline_management_v t inner join dp_project_pipeline_management_v t2
                on t.submission_seq = t2.submission_seq + 1 and t.project_name = t2.project_name and t.material = t2.material
                where t.project_status not in ('丢标', '已下单')
                <if test="submissionDateRange != null and submissionDateRange.isEmpty() == false">
                                                 and t.submission_date between to_date(#{submissionDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm') and last_day(to_date(#{submissionDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
                                             </if>
                                             <if test="selectedSubmissionDate != null and selectedSubmissionDate != ''.toString">
                                                and t.submission_date = to_date(#{selectedSubmissionDate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
                                             </if>
                                             <include refid="reportCondition"></include>
                                 ) mm ) tt
                 group by to_char(tt.submission_date, 'MON-yy', 'NLS_DATE_LANGUAGE=AMERICAN')
    </select>

    <select id="queryReport2DetailsMissing" parameterType="java.util.Map" resultType="java.util.HashMap">
        select mm.submission_month, nn.cnt
          from (select distinct submission_seq, to_char(submission_date, 'MON-yy', 'NLS_DATE_LANGUAGE=AMERICAN') submission_month from DP_PROJECT_PIPELINE_MANAGEMENT_V) mm
         inner join
               (select t.submission_seq + 1 submission_seq, ${reportType} cnt
                  from dp_project_pipeline_management_v t left join dp_project_pipeline_management_v t2
                    on t.submission_seq + 1 = t2.submission_seq and t.project_name = t2.project_name and t.material = t2.material
                 where t.project_status not in ('丢标', '已下单')
                   and t.submission_seq != (select max(submission_seq) from DP_PROJECT_PIPELINE_MANAGEMENT_V)
                   and t2.submission_date is null
                   and t.submission_date between to_date(#{submissionDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm') and last_day(to_date(#{submissionDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
                   <include refid="reportCondition"></include>
                 group by t.submission_seq) nn on mm.submission_seq = nn.submission_seq
    </select>

    <select id="queryReport2DetailsSummary" parameterType="java.util.Map" resultType="java.util.HashMap">
        select to_char(submission_date, 'MON-yy', 'NLS_DATE_LANGUAGE=AMERICAN') submission_month,
               ${reportType} cnt
         from mr3_dp_project_pipeline_management t
        where t.submission_date between to_date(#{submissionDateRange[0], jdbcType=VARCHAR}, 'yyyy/mm') and last_day(to_date(#{submissionDateRange[1], jdbcType=VARCHAR}, 'yyyy/mm'))
              and t.project_status not in ('丢标', '已下单')
              <include refid="reportCondition"></include>
        group by to_char(submission_date, 'MON-yy', 'NLS_DATE_LANGUAGE=AMERICAN')
    </select>

    <select id="querySubmissionDateByMonth" parameterType="java.lang.String" resultType="java.lang.String">
        select to_char(submission_date, 'yyyy/mm/dd')
          from mr3_dp_project_pipeline_management
         where to_char(submission_date, 'MON-yy', 'NLS_DATE_LANGUAGE=AMERICAN') = #{_parameter, jdbcType=VARCHAR}
         fetch next 1 rows only
    </select>

    <select id="queryReport2Source" parameterType="java.util.Map" resultType="java.util.HashMap">
        <choose>
            <when test="reportName eq 'Missing'">
                select to_char(submission_date,'yyyy/mm/dd') submission_date,
                to_char(t.est_delivery_date,'yyyy/mm/dd') est_delivery_date,
                t.forecast_quantity,
                project_name, sales_person, material, quantity, order_value, dp_comments
                from mr3_dp_project_pipeline_management t
                where project_status not in ('丢标', '已下单')
                    and not exists(select 1 from mr3_dp_project_pipeline_management t2 where t.project_name = t2.project_name and t.material = t2.material and t2.submission_date = to_date(#{submissionDate, jdbcType=VARCHAR}, 'yyyy/mm/dd'))
                    and submission_date = to_date(#{lastSubmissionDate, jdbcType=VARCHAR},'yyyy/mm/dd')
                <include refid="reportCondition"></include>
                order by t.quantity desc
            </when>
            <otherwise>
                select to_char(submission_date,'yyyy/mm/dd') submission_date,
                to_char(t.est_delivery_date,'yyyy/mm/dd') est_delivery_date,
                t.forecast_quantity,
                project_name, sales_person, material, quantity, order_value, dp_comments
                from mr3_dp_project_pipeline_management t
                where submission_date = to_date(#{submissionDate, jdbcType=VARCHAR},'yyyy/mm/dd')
                <choose>
                    <when test="reportName eq 'Closed'">
                        and project_status = '已下单'
                    </when>
                    <when test="reportName eq 'Lost'">
                        and project_status = '丢标'
                    </when>
                    <when test="reportName eq 'Inherited'">
                        and project_status not in ('丢标', '已下单')
                        and exists(select 1 from mr3_dp_project_pipeline_management t2 where t.project_name = t2.project_name and t.material = t2.material and t2.submission_date = to_date(#{lastSubmissionDate, jdbcType=VARCHAR}, 'yyyy/mm/dd'))
                    </when>
                    <when test="reportName eq 'New Added'">
                        and project_status not in ('丢标', '已下单')
                        and not exists(select 1 from mr3_dp_project_pipeline_management t2 where t.project_name = t2.project_name and t.material = t2.material and t2.submission_date = to_date(#{lastSubmissionDate, jdbcType=VARCHAR}, 'yyyy/mm/dd'))
                    </when>
                </choose>
                <include refid="reportCondition"></include>
                order by t.quantity desc
            </otherwise>
        </choose>
    </select>

    <select id="queryReport2SourceInherited" parameterType="java.util.Map" resultType="java.util.HashMap">
        select  to_char(t.submission_date, 'yyyy/mm/dd') submission_date,
                t.project_name,
                t.material,
                t.sales_team,
                t.sales_person,
                t.forecast_quantity,
                to_char(t.est_delivery_date,'yyyy/mm/dd') est_delivery_date,
                to_char(t.est_delivery_date, 'MON-yy', 'NLS_DATE_LANGUAGE=AMERICAN') est_delivery_month,
                t.quantity quantity,
                t2.quantity quantity2,
                t.order_value,
                to_char(t.est_delivery_date, 'yyyy/mm') est_delivery_date1,
                to_char(t2.est_delivery_date, 'yyyy/mm') est_delivery_date2
           from dp_project_pipeline_management_v t inner join dp_project_pipeline_management_v t2
                on t.project_name = t2.project_name and t.material = t2.material and t.submission_seq = t2.submission_seq + 1
          where t.project_status not in ('丢标', '已下单')
                and t.submission_date = to_date(#{submissionDate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
        <include refid="reportCondition"></include>
    </select>

    <select id="queryReport2SourceSummary" parameterType="java.util.Map" resultType="java.util.HashMap">
        select to_char(submission_date,'yyyy/mm/dd') submission_date,
        to_char(t.est_delivery_date,'yyyy/mm/dd') est_delivery_date,
        t.forecast_quantity,
        project_name, sales_person, material, quantity, order_value, dp_comments
        from mr3_dp_project_pipeline_management t
        where project_status not in ('丢标', '已下单')
        and submission_date = to_date(#{submissionDate, jdbcType=VARCHAR},'yyyy/mm/dd')
        <include refid="reportCondition"></include>
        order by t.quantity desc
    </select>

    <select id="queryMonthByCalender" parameterType="java.util.Map" resultType="java.lang.String">
        select d1
          from (
                   select distinct to_char(to_date(t.text, 'yyyy/mm/dd'), 'MON-yy', 'NLS_DATE_LANGUAGE=AMERICAN') as d1,
                                   to_char(to_date(t.text, 'yyyy/mm/dd'), 'yyyymm')                               as d2
                     from sy_calendar t
                    where t.name = 'National Holidays'
                      and to_date(t.text, 'yyyy/mm/dd') between to_date(#{deliveryDate[0], jdbcType=VARCHAR}, 'yyyy/mm') and last_day(to_date(#{deliveryDate[1], jdbcType=VARCHAR}, 'yyyy/mm'))
               ) mm
         order by d2
    </select>

    <select id="queryReport3" parameterType="java.util.Map" resultType="java.util.HashMap">
        select 'New Added' project_status,
               to_char(est_delivery_date, 'MON-yy', 'NLS_DATE_LANGUAGE=AMERICAN') month,
               ${reportType} cnt
        from mr3_dp_project_pipeline_management t
        where project_status not in ('丢标', '已下单')
            and t.submission_date = to_date(#{currentSubmissionDate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
            and not exists(select 1 from mr3_dp_project_pipeline_management t2 where t.project_name = t2.project_name and t.material = t2.material and t2.submission_date = to_date(#{lastSubmissionDate, jdbcType=VARCHAR}, 'yyyy/mm/dd'))
        <include refid="reportCondition"></include>
        group by to_char(est_delivery_date, 'MON-yy', 'NLS_DATE_LANGUAGE=AMERICAN')

        union all

        select 'Inherited', to_char(est_delivery_date, 'MON-yy', 'NLS_DATE_LANGUAGE=AMERICAN'), ${reportType} cnt
        from mr3_dp_project_pipeline_management t
        where project_status not in ('丢标', '已下单')
            and t.submission_date = to_date(#{currentSubmissionDate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
            and exists(select 1 from mr3_dp_project_pipeline_management t2 where t.project_name = t2.project_name and t.material = t2.material and t2.submission_date = to_date(#{lastSubmissionDate, jdbcType=VARCHAR}, 'yyyy/mm/dd'))
        <include refid="reportCondition"></include>
        group by to_char(est_delivery_date, 'MON-yy', 'NLS_DATE_LANGUAGE=AMERICAN')
    </select>

    <select id="queryReport3Details" parameterType="java.util.Map" resultType="java.util.HashMap">
        select ROWIDTOCHAR(rowid) row_id,
        to_char(submission_date,'yyyy/mm/dd') submission_date,
        to_char(est_delivery_date,'yyyy/mm/dd') est_delivery_date,
        t.forecast_quantity,
        project_name, sales_person, material, quantity, order_value, dp_comments
        from mr3_dp_project_pipeline_management t
        where submission_date = to_date(#{submissionDate, jdbcType=VARCHAR},'yyyy/mm/dd')
        and est_delivery_date between to_date(#{deliveryDate, jdbcType=VARCHAR},'MON-yy', 'NLS_DATE_LANGUAGE=AMERICAN') and last_day(to_date(#{deliveryDate, jdbcType=VARCHAR},'MON-yy', 'NLS_DATE_LANGUAGE=AMERICAN'))
        <choose>
            <when test="type eq 'Inherited'">
                and project_status not in ('丢标', '已下单')
                and exists(select 1 from mr3_dp_project_pipeline_management t2 where t.project_name = t2.project_name and t.material = t2.material and t2.submission_date = to_date(#{lastSubmissionDate, jdbcType=VARCHAR}, 'yyyy/mm/dd'))
            </when>
            <when test="type eq 'New Added'">
                and project_status not in ('丢标', '已下单')
                and not exists(select 1 from mr3_dp_project_pipeline_management t2 where t.project_name = t2.project_name and t.material = t2.material and t2.submission_date = to_date(#{lastSubmissionDate, jdbcType=VARCHAR}, 'yyyy/mm/dd'))
            </when>
        </choose>
        <include refid="reportCondition"></include>
        order by t.quantity desc
    </select>

    <select id="queryReport4" parameterType="java.util.Map" resultType="java.util.HashMap">
        select decode(t.project_status, '丢标', 'Lost', '已下单', 'Close', 'Open') as project_status,
               t.submission_seq,
               t.material || '@' || t.project_name                               as project_name,
               to_number(to_char(t.est_delivery_date, 'yyyymm'))                 as est_delivery_date
          from dp_project_pipeline_management_v t
         where exists
                   (
                       select 1
                         from dp_project_pipeline_management_v this_month
                                  inner join dp_project_pipeline_management_v last_month
                                             on this_month.submission_seq = last_month.submission_seq + 1
                                                 and this_month.project_name = last_month.project_name and this_month.material = last_month.material
                                                 and last_month.project_status not in ('丢标', '已下单')
                                                 and to_char(this_month.submission_date,'yyyy/mm') = #{submissionMonth, jdbcType=VARCHAR}
                                                 and t.project_name = this_month.project_name and t.material = this_month.material
                   )
         and t.submission_date &lt;= last_day(to_date(#{submissionMonth, jdbcType=VARCHAR},'yyyy/mm'))
         <include refid="reportCondition"></include>
         order by t.project_name, t.material, t.submission_seq
    </select>

    <select id="queryReport5Details" parameterType="java.util.Map" resultType="java.util.HashMap">
        select
            to_char(submission_date,'yyyy/mm/dd') submission_date,
            to_char(est_delivery_date,'yyyy/mm/dd') est_delivery_date,
            t.forecast_quantity,
            project_name, sales_person, material, quantity, order_value, dp_comments
        from mr3_dp_project_pipeline_management t
        where to_char(submission_date,'yyyy/mm') = #{submissionMonth, jdbcType=VARCHAR}
        and (t.project_name,t.material) in
        <foreach collection="projectNameList" separator="," open="(" close=")" item="item">
            (#{item.project_name, jdbcType=VARCHAR}, #{item.material, jdbcType=VARCHAR})
        </foreach>
        order by t.quantity desc
    </select>

    <select id="queryInvalidPipelineDataCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        select count(1)
          from mr3_dp_project_pipeline_management t
                   inner join(
              select submission_date, project_name, material, count(1) cnt
                from mr3_dp_project_pipeline_management
               group by submission_date, project_name, material
              having count(1) > 1) mm on t.submission_date = mm.submission_date and t.project_name = mm.project_name
              and t.material = mm.material
    </select>

    <select id="queryInvalidPipelineData" parameterType="java.util.Map" resultType="java.util.HashMap">
        <include refid="global.select_header"/>
        select to_char(t.submission_date,'yyyy/mm/dd') submission_date,
               to_char(t.est_delivery_date,'yyyy/mm/dd') est_delivery_date,
               t.forecast_quantity,
               t.project_name,
               t.sales_person,
               t.material,
               t.quantity,
               t.order_value,
               t.dp_comments,
               mm.cnt
          from mr3_dp_project_pipeline_management t
                   inner join(
              select submission_date, project_name, material, count(1) cnt
                from mr3_dp_project_pipeline_management
               group by submission_date, project_name, material
              having count(1) > 1) mm on t.submission_date = mm.submission_date and t.project_name = mm.project_name
              and t.material = mm.material
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport6" parameterType="java.util.Map" resultType="java.util.HashMap">
        select count(1) total, count(case when t.est_delivery_date - t.submission_date &lt; nvl(t3.std_delivery_days, 0) then 1 end) warning_cnt
          from dp_project_pipeline_management_v t
                   left join mr3_dp_project_pipeline_std t3 on t.product_family = t3.product_family
         where project_status not in ('丢标', '已下单')
           and to_char(t.submission_date,'yyyy/mm') = #{submissionMonth, jdbcType=VARCHAR}
           and not exists(select 1 from dp_project_pipeline_management_v t2 where t.project_name = t2.project_name and t.material = t2.material and t.submission_seq = t2.submission_seq + 1)
           <include refid="reportCondition"></include>
        group by to_char(t.submission_date, 'yyyy/mm')
    </select>

    <select id="queryReport6Details" parameterType="java.util.Map" resultType="java.util.HashMap">
        select  to_char(t.submission_date,'yyyy/mm/dd') submission_date,
                to_char(t.est_delivery_date,'yyyy/mm/dd') est_delivery_date,
                t.forecast_quantity,
                t.project_name,
                t.sales_person,
                t.material,
                t.quantity,
                t.order_value,
                t.dp_comments,
                t.est_delivery_date - t.submission_date as actual_delivery_days,
                t3.std_delivery_days
        from dp_project_pipeline_management_v t
        left join mr3_dp_project_pipeline_std t3 on t.product_family = t3.product_family
        where project_status not in ('丢标', '已下单')
            and to_char(t.submission_date,'yyyy/mm') = #{submissionMonth, jdbcType=VARCHAR}
            and not exists(select 1 from dp_project_pipeline_management_v t2 where t.project_name = t2.project_name and t.material = t2.material and t.submission_seq = t2.submission_seq + 1)
            and t.est_delivery_date - t.submission_date &lt; nvl(t3.std_delivery_days, 0)
        <include refid="reportCondition"></include>
    </select>
</mapper>
