<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.demand.dao.ITrackingTestDao">
    <sql id="base_filter">
        <if test="salesOrg != null and salesOrg.isEmpty() == false">
            and t.sales_organization in
            <foreach collection="salesOrg" separator="," open="(" close=")" item="item">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="specialList != null and specialList.size() > 0 and specialColumn != 'PROJECT_NAME'">
            <foreach collection="specialList" item="list" separator=" or " open=" and (" close=")">
                t.${specialColumn} in
                <foreach collection="list" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </foreach>
        </if>
        <if test="shortageLevel != null and shortageLevel.size() > 0">
            and exists(
            select 1 from GRA t0 where t.material = t0.material and nvl(t0.type, 'Others') in
            <foreach collection="shortageLevel" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
            )
        </if>
    </sql>

    <sql id="scope_filter">
        <if test="scope != null and scope != ''.toString()">
            <choose>
                <when test="subScope == null or subScope.size() == 0">
                    <choose>
                        <when test="scope == 'SECI'.toString()">
                            AND T.SE_SCOPE IS NOT NULL
                        </when>
                        <when test="scope == 'PLANT'.toString()">
                            AND t.PLANT_SCOPE IS NOT NULL
                        </when>
                    </choose>
                </when>
                <otherwise>
                    <choose>
                        <when test="scope == 'SECI'.toString()">
                            AND T.SE_SCOPE IN
                            <foreach collection="subScope" separator="," open="(" close=")" item="item">
                                #{item, jdbcType=VARCHAR}
                            </foreach>
                        </when>
                        <when test="scope == 'PLANT'.toString()">
                            AND t.PLANT_SCOPE IN
                            <foreach collection="subScope" separator="," open="(" close=")" item="item">
                                #{item, jdbcType=VARCHAR}
                            </foreach>
                        </when>
                    </choose>
                </otherwise>
            </choose>
        </if>
    </sql>

    <sql id="tracking_filter">
        <include refid="base_filter"/>
        <include refid="scope_filter"/>
        <if test="filters != null and filters != ''.toString()">
            and ${filters}
        </if>
        <if test="personalFilters != null and personalFilters != ''.toString()">
            and ${personalFilters}
        </if>
    </sql>

    <sql id="ud_tracking_filter">
        <include refid="tracking_filter"/>
        <if test="category7Value != null and category7Value.size() > 0">
            and T.UD_TYPE IN
            <foreach collection="category7Value" item="item" open="(" close=")" separator=",">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
    </sql>

    <sql id="soh_tracking_filter">
        <include refid="base_filter"/>
        <if test="filterListWithoutOrderType != null and filterListWithoutOrderType != ''.toString()">
            and ${filterListWithoutOrderType}
        </if>
        <if test="personalFilterListWithoutOrderType != null and personalFilterListWithoutOrderType != ''.toString()">
            and ${personalFilterListWithoutOrderType}
        </if>
        <if test="SOHCRD == 'Y'.toString()">
            and t.CRD_RECORD = 'Y'
        </if>
        <if test="SOHCRD == 'N'.toString()">
            and t.CRD_RECORD is null
        </if>
        <if test="sohScopeFilter != null and sohScopeFilter != ''.toString()">
            AND ${sohScopeFilter}
        </if>
    </sql>

    <sql id="output_tracking_filter">
        <include refid="base_filter"/>
        <if test="sohScopeFilter != null and sohScopeFilter != ''.toString()">
            AND ${sohScopeFilter}
        </if>
        <if test="filters != null and filters != ''.toString()">
            and ${filters}
        </if>
        <if test="personalFilters != null and personalFilters != ''.toString()">
            and ${personalFilters}
        </if>
    </sql>

    <sql id="fcst_tracking_filter">
        <include refid="base_filter"/>
        <include refid="scope_filter"/>
        <if test="category8Value != null and category8Value != ''.toString()">
            AND T.FCST_SCOPE = #{category8Value, jdbcType=VARCHAR}
        </if>
        <if test="filterListWithoutOrderType != null and filterListWithoutOrderType != ''.toString()">
            and ${filterListWithoutOrderType}
        </if>
        <if test="personalFilterListWithoutOrderType != null and personalFilterListWithoutOrderType != ''.toString()">
            and ${personalFilterListWithoutOrderType}
        </if>
    </sql>

    <sql id="project_name_tracking_filter">
        <if test="specialList != null and specialList.size() > 0 and specialColumn == 'PROJECT_NAME'">
            and (  t.${specialColumn} in
            <foreach collection="specialList[0]" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
            )
        </if>
    </sql>

    <select id="queryFcstVersion" resultType="java.lang.String">
        SELECT FCST_VERSION
        from (SELECT FCST_VERSION, count(1) as lines, AVG(COUNT(1)) over () AS AVGLINE
              FROM DEMAND_FCST_V
              WHERE FCST_VERSION >= TO_CHAR(ADD_MONTHS(SYSDATE, -12), 'YYYYMM')
              group by FCST_VERSION)
        where lines >= AVGLINE * 0.8
        order by fcst_version desc
    </select>

    <select id="queryLastWdMonth" resultType="java.lang.String">
        SELECT T.YEAR || T.MONTH
        FROM SY_CALENDAR T
        WHERE T.NAME = 'National Holidays'
          AND T.WORKING_DAY = 1
          AND T.DATE$ &lt; SYSDATE - 1
        ORDER BY DATE$ DESC
            FETCH NEXT 1 ROWS ONLY
    </select>

    <select id="queryTrackingCascader" resultType="java.util.Map">
        select * from DEMAND_TRACKING_FILTER_V t
        order by t.category,decode(t.name,'Others','zzz',t.name)
    </select>

    <select id="queryReport1" parameterType="java.util.Map" resultType="com.scp.demand.bean.TrackingTestReport1Bean">
        select mm.category1,
        sum(mm.sales_qty)                   sales_qty,
        sum(mm.sales_net_net_value)         sales_net_net_value,
        sum(mm.sales_cost_value)            sales_cost_value,
        sum(mm.sales_line)                  sales_line,
        sum(mm.order_intake_qty)            order_intake_qty,
        sum(mm.order_intake_net_net_value)  order_intake_net_net_value,
        sum(mm.order_intake_cost_value)     order_intake_cost_value,
        sum(mm.order_intake_line)           order_intake_line,
        sum(mm.crd_qty)                     crd_qty,
        sum(mm.crd_net_net_value)           crd_net_net_value,
        sum(mm.crd_cost_value)              crd_cost_value,
        sum(mm.crd_line)                    crd_line,
        sum(mm.backorder_qty)               backorder_qty,
        sum(mm.backorder_net_net_value)     backorder_net_net_value,
        sum(mm.backorder_cost_value)        backorder_cost_value,
        sum(mm.backorder_line)              backorder_line,
        sum(mm.backlog_qty)                 backlog_qty,
        sum(mm.backlog_net_net_value)       backlog_net_net_value,
        sum(mm.backlog_cost_value)          backlog_cost_value,
        sum(mm.backlog_line)                backlog_line,
        sum(mm.soh_qty)                     soh_qty,
        sum(mm.soh_net_net_value)           soh_net_net_value,
        sum(mm.soh_cost_value)              soh_cost_value,
        sum(mm.soh_line)                    soh_line,
        sum(mm.ud_qty)                      ud_qty,
        sum(mm.ud_net_net_value)            ud_net_net_value,
        sum(mm.ud_cost_value)               ud_cost_value,
        sum(mm.ud_line)                     ud_line,
        sum(mm.output_qty)                  output_qty,
        sum(mm.output_net_net_value)        output_net_net_value,
        sum(mm.output_cost_value)           output_cost_value,
        sum(mm.output_line)                 output_line
        from (
        select coalesce(sales.category1, order_intake.category1, crd.category1, backorder.category1, backlog.category1, soh.category1,  ud.category1, output.category1, 'Others') category1,
        sales.sales_qty,
        sales.sales_net_net_value,
        sales.sales_cost_value,
        sales.sales_line,
        order_intake.order_intake_qty,
        order_intake.order_intake_net_net_value,
        order_intake.order_intake_cost_value,
        order_intake.order_intake_line,
        crd.crd_qty,
        crd.crd_net_net_value,
        crd.crd_cost_value,
        crd.crd_line,
        backorder.backorder_qty,
        backorder.backorder_net_net_value,
        backorder.backorder_cost_value,
        backorder.backorder_line,
        backlog.backlog_qty,
        backlog.backlog_net_net_value,
        backlog.backlog_cost_value,
        backlog.backlog_line,
        soh.soh_qty,
        soh.soh_net_net_value,
        soh.soh_cost_value,
        soh.soh_line,
        ud.ud_qty,
        ud.ud_net_net_value,
        ud.ud_cost_value,
        ud.ud_line,
        output.output_qty,
        output.output_net_net_value,
        output.output_cost_value,
        output.output_line
        from (select t.${category1Column} as                       category1,
        sum(t.order_quantity)                         sales_qty,
        sum(t.order_quantity * t.${netPriceColumn})   sales_net_net_value,
        sum(t.order_quantity * t.${mvpPriceColumn})   sales_cost_value,
        count(1)                                      sales_line
        from demand_sales_v t
        where t.${category1Column} is not null
        and t.${dateColumn} = #{saleMonth,jdbcType=VARCHAR}
        and t.bom_category != 'HEADER'
        <include refid="tracking_filter"/>
        <include refid="project_name_tracking_filter"/>
        group by t.${category1Column}) sales

        full join

        (select t.${category1Column} as                       category1,
        sum(t.order_quantity)                         order_intake_qty,
        sum(t.order_quantity * t.${netPriceColumn})   order_intake_net_net_value,
        sum(t.order_quantity * t.${mvpPriceColumn})   order_intake_cost_value,
        count(1)                                      order_intake_line
        from demand_order_intake_v t
        where t.${category1Column} is not null
        and t.${dateColumn} = #{saleMonth,jdbcType=VARCHAR}
        and t.bom_category != 'HEADER'
        <include refid="tracking_filter"/>
        <include refid="project_name_tracking_filter"/>
        group by t.${category1Column}) order_intake
        on sales.category1 = order_intake.category1

        full join

        (select t.${category1Column} as                       category1,
        sum(t.order_quantity)                         crd_qty,
        sum(t.order_quantity * t.${netPriceColumn})   crd_net_net_value,
        sum(t.order_quantity * t.${mvpPriceColumn})   crd_cost_value,
        count(1)                                      crd_line
        from demand_crd_v t
        where t.${category1Column} is not null
        and t.BOM_CATEGORY != 'HEADER'
        and t.${dateColumn} = #{saleMonth,jdbcType=VARCHAR}
        <include refid="tracking_filter"/>
        <include refid="project_name_tracking_filter"/>
        group by t.${category1Column}) crd
        on sales.category1 = crd.category1

        full join

        (select  t.${category1Column} as                          category1,
        sum(${backorderQtyColumn})                        backorder_qty,
        sum(${backorderQtyColumn} * t.${netPriceColumn})  backorder_net_net_value,
        sum(${backorderQtyColumn} * t.${mvpPriceColumn})  backorder_cost_value,
        count(1)                                          backorder_line
        FROM DEMAND_BACK_ORDER_V t
        WHERE t.${category1Column} is not null
        and t.BOM_CATEGORY != 'HEADER'
        <include refid="tracking_filter"/>
        <include refid="project_name_tracking_filter"/>
        <choose>
            <when test="category5Value == 'Back Order Block'.toString()">
                and t.BLOCK_STATUS = 'BLOCK'
            </when>
            <when test="category5Value == 'Back Order Non-Block'.toString()">
                and t.BLOCK_STATUS = 'NON_BLOCK'
            </when>
        </choose>
        group by t.${category1Column}) backorder
        on sales.category1 = backorder.category1

        full join

        (select  t.${category1Column} as                          category1,
        sum(${backlogQtyColumn})                          backlog_qty,
        sum(${backlogQtyColumn} * t.${netPriceColumn})    backlog_net_net_value,
        sum(${backlogQtyColumn} * t.${mvpPriceColumn})    backlog_cost_value,
        count(1)                                          backlog_line
        FROM DEMAND_BACKLOG_V t
        WHERE t.${category1Column} is not null
        and t.BOM_CATEGORY != 'HEADER'
        <include refid="tracking_filter"/>
        <include refid="project_name_tracking_filter"/>
        <choose>
            <when test="category6Value == 'Backlog Block'.toString()">
                and t.BLOCK_STATUS = 'BLOCK'
            </when>
            <when test="category6Value == 'Backlog Non-Block'.toString()">
                and t.BLOCK_STATUS = 'NON_BLOCK'
            </when>
        </choose>
        group by t.${category1Column}) backlog
        on sales.category1 = backlog.category1

        full join

        (select  t.${category1Column} as                     category1,
        sum(${sohQtyColumn})                        soh_qty,
        sum(${sohQtyColumn} * t.${netPriceColumn})  soh_net_net_value,
        sum(${sohQtyColumn} * t.${mvpPriceColumn})  soh_cost_value,
        count(1)                                    soh_line
        FROM demand_soh_v t
        WHERE t.${category1Column} is not null
        <include refid="soh_tracking_filter"/>
        group by t.${category1Column}) soh
        on sales.category1 = soh.category1

        full join

        (select t.${category1Column} as                      category1,
        sum(t.order_quantity)                        ud_qty,
        sum(t.order_quantity * t.${netPriceColumn})  ud_net_net_value,
        sum(t.order_quantity * t.${mvpPriceColumn})  ud_cost_value,
        count(1)                                     ud_line
        FROM demand_ud_v t
        WHERE t.${category1Column} is not null
        and t.bom_category != 'HEADER'
        <include refid="ud_tracking_filter"/>
        <include refid="project_name_tracking_filter"/>
        group by t.${category1Column}) ud
        on sales.category1 = ud.category1

        full join
        (select t.${category1Column} as                       category1,
        sum(t.order_quantity)                         output_qty,
        sum(t.order_quantity * t.${netPriceColumn})   output_net_net_value,
        sum(t.order_quantity * t.${mvpPriceColumn})   output_cost_value,
        count(1)                                      output_line
        from demand_output_v t
        where t.${category1Column} is not null
        and t.${dateColumn} = #{saleMonth,jdbcType=VARCHAR}
        and t.bom_category != 'HEADER'
        <include refid="output_tracking_filter"/>
        <include refid="project_name_tracking_filter"/>
        group by t.${category1Column}) output
        on sales.category1 = output.category1
        ) mm
        group by mm.category1
        order by
        <if test="report1OrderBy != null and report1OrderBy != ''.toString()">
            nvl(${report1OrderBy},0) desc,
        </if>
        decode(mm.category1, 'Others', 'zzz', mm.category1)
    </select>

    <select id="queryReport1Sub" parameterType="java.util.Map" resultType="com.scp.demand.bean.TrackingTestReport1Bean">
        select mm.category2,
        sum(mm.sales_qty)                   sales_qty,
        sum(mm.sales_net_net_value)         sales_net_net_value,
        sum(mm.sales_cost_value)            sales_cost_value,
        sum(mm.sales_line)                  sales_line,
        sum(mm.order_intake_qty)            order_intake_qty,
        sum(mm.order_intake_net_net_value)  order_intake_net_net_value,
        sum(mm.order_intake_cost_value)     order_intake_cost_value,
        sum(mm.order_intake_line)           order_intake_line,
        sum(mm.crd_qty)                     crd_qty,
        sum(mm.crd_net_net_value)           crd_net_net_value,
        sum(mm.crd_cost_value)              crd_cost_value,
        sum(mm.crd_line)                    crd_line,
        sum(mm.backorder_qty)               backorder_qty,
        sum(mm.backorder_net_net_value)     backorder_net_net_value,
        sum(mm.backorder_cost_value)        backorder_cost_value,
        sum(mm.backorder_line)              backorder_line,
        sum(mm.backlog_qty)                 backlog_qty,
        sum(mm.backlog_net_net_value)       backlog_net_net_value,
        sum(mm.backlog_cost_value)          backlog_cost_value,
        sum(mm.backlog_line)                backlog_line,
        sum(mm.soh_qty)                     soh_qty,
        sum(mm.soh_net_net_value)           soh_net_net_value,
        sum(mm.soh_cost_value)              soh_cost_value,
        sum(mm.soh_line)                    soh_line,
        sum(mm.ud_qty)                      ud_qty,
        sum(mm.ud_net_net_value)            ud_net_net_value,
        sum(mm.ud_cost_value)               ud_cost_value,
        sum(mm.ud_line)                     ud_line,
        sum(mm.output_qty)                  output_qty,
        sum(mm.output_net_net_value)        output_net_net_value,
        sum(mm.output_cost_value)           output_cost_value,
        sum(mm.output_line)                 output_line
        from (
        select   coalesce(sales.category2, order_intake.category2, crd.category2, backorder.category2, backlog.category2, soh.category2, ud.category2, output.category2, 'Others') category2,
        sales.sales_qty,
        sales.sales_net_net_value,
        sales.sales_cost_value,
        sales.sales_line,
        order_intake.order_intake_qty,
        order_intake.order_intake_net_net_value,
        order_intake.order_intake_cost_value,
        order_intake.order_intake_line,
        crd.crd_qty,
        crd.crd_net_net_value,
        crd.crd_cost_value,
        crd.crd_line crd_line,
        backorder.backorder_qty,
        backorder.backorder_net_net_value,
        backorder.backorder_cost_value,
        backorder.backorder_line,
        backlog.backlog_qty,
        backlog.backlog_net_net_value,
        backlog.backlog_cost_value,
        backlog.backlog_line,
        soh.soh_qty,
        soh.soh_net_net_value,
        soh.soh_cost_value,
        soh.soh_line,
        ud.ud_qty,
        ud.ud_net_net_value,
        ud.ud_cost_value,
        ud.ud_line,
        output.output_qty,
        output.output_net_net_value,
        output.output_cost_value,
        output.output_line
        from (select nvl(t.${category2Column}, 'Others')           category2,
        sum(t.order_quantity)                         sales_qty,
        sum(t.order_quantity * t.${netPriceColumn})   sales_net_net_value,
        sum(t.order_quantity * t.${mvpPriceColumn})   sales_cost_value,
        count(1)                                      sales_line
        from demand_sales_v t
        where t.${dateColumn} = #{saleMonth,jdbcType=VARCHAR}
        and t.${category1Column} = #{expandCategory1,jdbcType=VARCHAR}
        and t.bom_category != 'HEADER'
        <include refid="tracking_filter"/>
        <include refid="project_name_tracking_filter"/>
        group by t.${category2Column}) sales

        full join

        (select nvl(t.${category2Column}, 'Others')           category2,
        sum(t.order_quantity)                         order_intake_qty,
        sum(t.order_quantity * t.${netPriceColumn})   order_intake_net_net_value,
        sum(t.order_quantity * t.${mvpPriceColumn})   order_intake_cost_value,
        count(1)                                      order_intake_line
        from demand_order_intake_v t
        where t.bom_category != 'HEADER'
        and t.${dateColumn} = #{saleMonth,jdbcType=VARCHAR}
        and t.${category1Column} = #{expandCategory1,jdbcType=VARCHAR}
        <include refid="tracking_filter"/>
        <include refid="project_name_tracking_filter"/>
        group by t.${category2Column}) order_intake on sales.category2 = order_intake.category2

        full join

        (select nvl(t.${category2Column}, 'Others')           category2,
        sum(t.order_quantity)                         crd_qty,
        sum(t.order_quantity * t.${netPriceColumn})   crd_net_net_value,
        sum(t.order_quantity * t.${mvpPriceColumn})   crd_cost_value,
        count(1)                                      crd_line
        from demand_crd_v t
        where t.BOM_CATEGORY != 'HEADER'
        and t.${dateColumn} = #{saleMonth,jdbcType=VARCHAR}
        and t.${category1Column} = #{expandCategory1,jdbcType=VARCHAR}
        <include refid="tracking_filter"/>
        <include refid="project_name_tracking_filter"/>
        group by t.${category2Column}) crd on sales.category2 = crd.category2

        full join

        (select nvl(t.${category2Column}, 'Others')                category2,
        sum(${backorderQtyColumn})                         backorder_qty,
        sum(${backorderQtyColumn} * t.${netPriceColumn})   backorder_net_net_value,
        sum(${backorderQtyColumn} * t.${mvpPriceColumn})   backorder_cost_value,
        count(1)                                           backorder_line
        from DEMAND_BACK_ORDER_V t
        where t.BOM_CATEGORY != 'HEADER'
        and t.${category1Column} = #{expandCategory1,jdbcType=VARCHAR}
        <include refid="tracking_filter"/>
        <include refid="project_name_tracking_filter"/>
        <choose>
            <when test="category5Value == 'Back Order Block'.toString()">
                and t.BLOCK_STATUS = 'BLOCK'
            </when>
            <when test="category5Value == 'Back Order Non-Block'.toString()">
                and t.BLOCK_STATUS = 'NON_BLOCK'
            </when>
        </choose>
        group by t.${category2Column}) backorder on sales.category2 = backorder.category2

        full join

        (select nvl(t.${category2Column}, 'Others')                category2,
        sum(${backlogQtyColumn})                           backlog_qty,
        sum(${backlogQtyColumn} * t.${netPriceColumn})     backlog_net_net_value,
        sum(${backlogQtyColumn} * t.${mvpPriceColumn})     backlog_cost_value,
        count(1)                                           backlog_line
        from DEMAND_BACKLOG_V t
        where t.BOM_CATEGORY != 'HEADER'
        and t.${category1Column} = #{expandCategory1,jdbcType=VARCHAR}
        <include refid="tracking_filter"/>
        <include refid="project_name_tracking_filter"/>
        <choose>
            <when test="category6Value == 'Backlog Block'.toString()">
                and t.BLOCK_STATUS = 'BLOCK'
            </when>
            <when test="category6Value == 'Backlog Non-Block'.toString()">
                and t.BLOCK_STATUS = 'NON_BLOCK'
            </when>
        </choose>
        group by t.${category2Column}) backlog on sales.category2 = backlog.category2

        full join

        (select nvl(t.${category2Column}, 'Others')           category2,
        sum(${sohQtyColumn})                          soh_qty,
        sum(${sohQtyColumn} * t.${netPriceColumn})    soh_net_net_value,
        sum(${sohQtyColumn} * t.${mvpPriceColumn})    soh_cost_value,
        count(1)                                      soh_line
        from demand_soh_v t
        where t.${category1Column} = #{expandCategory1,jdbcType=VARCHAR}
        <include refid="soh_tracking_filter"/>
        group by t.${category2Column}) soh on sales.category2 = soh.category2

        full join

        (select nvl(t.${category2Column}, 'Others')            category2,
        sum(t.order_quantity)                          ud_qty,
        sum(t.order_quantity * t.${netPriceColumn})    ud_net_net_value,
        sum(t.order_quantity * t.${mvpPriceColumn})    ud_cost_value,
        count(1)                                       ud_line
        from demand_ud_v t
        where t.bom_category != 'HEADER'
        and t.${category1Column} = #{expandCategory1,jdbcType=VARCHAR}
        <include refid="ud_tracking_filter"/>
        <include refid="project_name_tracking_filter"/>
        group by t.${category2Column}) ud on sales.category2 = ud.category2

        full join

        (select nvl(t.${category2Column}, 'Others')   category2,
        sum(t.order_quantity)                         output_qty,
        sum(t.order_quantity * t.${netPriceColumn})   output_net_net_value,
        sum(t.order_quantity * t.${mvpPriceColumn})   output_cost_value,
        count(1)                                      output_line
        from demand_output_v t
        where t.bom_category != 'HEADER'
        and t.${dateColumn} = #{saleMonth,jdbcType=VARCHAR}
        and t.${category1Column} = #{expandCategory1,jdbcType=VARCHAR}
        <include refid="output_tracking_filter"/>
        <include refid="project_name_tracking_filter"/>
        group by t.${category2Column}) output on sales.category2 = output.category2
        ) mm
        group by mm.category2
        order by
        <if test="report1OrderBy != null and report1OrderBy != ''.toString()">
            nvl(${report1OrderBy},0) desc,
        </if>
        decode(mm.category2, 'Others', 'zzz', mm.category2)
    </select>

    <select id="downloadReport1" parameterType="java.util.Map" resultType="com.scp.demand.bean.TrackingTestReport1Bean">
        select mm.category1,
        mm.category2,
        sum(mm.sales_qty)                   sales_qty,
        sum(mm.sales_net_net_value)         sales_net_net_value,
        sum(mm.sales_cost_value)            sales_cost_value,
        sum(mm.sales_line)                  sales_line,
        sum(mm.order_intake_qty)            order_intake_qty,
        sum(mm.order_intake_net_net_value)  order_intake_net_net_value,
        sum(mm.order_intake_cost_value)     order_intake_cost_value,
        sum(mm.order_intake_line)           order_intake_line,
        sum(mm.crd_qty)                     crd_qty,
        sum(mm.crd_net_net_value)           crd_net_net_value,
        sum(mm.crd_cost_value)              crd_cost_value,
        sum(mm.crd_line)                    crd_line,
        sum(mm.backorder_qty)               backorder_qty,
        sum(mm.backorder_net_net_value)     backorder_net_net_value,
        sum(mm.backorder_cost_value)        backorder_cost_value,
        sum(mm.backorder_line)              backorder_line,
        sum(mm.backlog_qty)                 backlog_qty,
        sum(mm.backlog_net_net_value)       backlog_net_net_value,
        sum(mm.backlog_cost_value)          backlog_cost_value,
        sum(mm.backlog_line)                backlog_line,
        sum(mm.ud_qty)                      ud_qty,
        sum(mm.ud_net_net_value)            ud_net_net_value,
        sum(mm.ud_cost_value)               ud_cost_value,
        sum(mm.ud_line)                     ud_line,
        sum(mm.soh_qty)                     soh_qty,
        sum(mm.soh_net_net_value)           soh_net_net_value,
        sum(mm.soh_cost_value)              soh_cost_value,
        sum(mm.soh_line)                    soh_line,
        sum(mm.fcst_qty)                    fcst_qty,
        sum(mm.fcst_net_net_value)          fcst_net_net_value,
        sum(mm.fcst_cost_value)             fcst_cost_value,
        sum(mm.fcst_line)                   fcst_line,
        sum(mm.output_qty)            output_qty,
        sum(mm.output_net_net_value)  output_net_net_value,
        sum(mm.output_cost_value)     output_cost_value,
        sum(mm.output_line)           output_line
        from (
        select coalesce(sales.category1, order_intake.category1, crd.category1, backorder.category1, backlog.category1, soh.category1, fcst.category1, ud.category1, output.category1, 'Others') category1,
        coalesce(sales.category2, order_intake.category2, crd.category2, backorder.category2, backlog.category2, soh.category2, fcst.category2, ud.category2, output.category2, 'Others') category2,
        sales.sales_qty,
        sales.sales_net_net_value,
        sales.sales_cost_value,
        sales.sales_line,
        order_intake.order_intake_qty,
        order_intake.order_intake_net_net_value,
        order_intake.order_intake_cost_value,
        order_intake.order_intake_line,
        crd.crd_qty,
        crd.crd_net_net_value,
        crd.crd_cost_value,
        crd.crd_line,
        backorder.backorder_qty,
        backorder.backorder_net_net_value,
        backorder.backorder_cost_value,
        backorder.backorder_line,
        backlog.backlog_qty,
        backlog.backlog_net_net_value,
        backlog.backlog_cost_value,
        backlog.backlog_line,
        soh.soh_qty,
        soh.soh_net_net_value,
        soh.soh_cost_value,
        soh.soh_line,
        fcst.fcst_qty,
        fcst.fcst_net_net_value,
        fcst.fcst_cost_value,
        fcst.fcst_line,
        ud.ud_qty,
        ud.ud_net_net_value,
        ud.ud_cost_value,
        ud.ud_line,
        output.output_qty,
        output.output_net_net_value,
        output.output_cost_value,
        output.output_line
        from (select t.${category1Column} as                       category1,
        t.${category2Column} as                       category2,
        sum(t.order_quantity)                         sales_qty,
        sum(t.order_quantity * t.${netPriceColumn})   sales_net_net_value,
        sum(t.order_quantity * t.${mvpPriceColumn})   sales_cost_value,
        count(1)                                      sales_line
        from demand_sales_v t
        where t.${category1Column} is not null
        and t.${category2Column} is not null
        and t.${dateColumn} = #{saleMonth,jdbcType=VARCHAR}
        and t.bom_category != 'HEADER'
        <include refid="tracking_filter"/>
        <include refid="project_name_tracking_filter"/>
        group by t.${category1Column}, t.${category2Column}) sales

        full join

        (select t.${category1Column} as                       category1,
        t.${category2Column} as                       category2,
        sum(t.order_quantity)                         order_intake_qty,
        sum(t.order_quantity * t.${netPriceColumn})   order_intake_net_net_value,
        sum(t.order_quantity * t.${mvpPriceColumn})   order_intake_cost_value,
        count(1)                                      order_intake_line
        from demand_order_intake_v t
        where t.${category1Column} is not null
        and t.${category2Column} is not null
        and t.${dateColumn} = #{saleMonth,jdbcType=VARCHAR}
        and t.bom_category != 'HEADER'
        <include refid="tracking_filter"/>
        <include refid="project_name_tracking_filter"/>
        group by t.${category1Column}, t.${category2Column}) order_intake
        on sales.category1 = order_intake.category1 and sales.category2 = order_intake.category2

        full join

        (select t.${category1Column} as                       category1,
        t.${category2Column} as                       category2,
        sum(t.order_quantity)                         crd_qty,
        sum(t.order_quantity * t.${netPriceColumn})   crd_net_net_value,
        sum(t.order_quantity * t.${mvpPriceColumn})   crd_cost_value,
        count(1)                                      crd_line
        from demand_crd_v t
        where t.${category1Column} is not null
        and t.${category2Column} is not null
        and t.BOM_CATEGORY != 'HEADER'
        and t.${dateColumn} = #{saleMonth,jdbcType=VARCHAR}
        <include refid="tracking_filter"/>
        <include refid="project_name_tracking_filter"/>
        group by t.${category1Column}, t.${category2Column}) crd on sales.category1 = crd.category1 and sales.category2 = crd.category2

        full join

        (select  t.${category1Column} as                           category1,
        t.${category2Column} as                           category2,
        sum(${backorderQtyColumn})                        backorder_qty,
        sum(${backorderQtyColumn} * t.${netPriceColumn})  backorder_net_net_value,
        sum(${backorderQtyColumn} * t.${mvpPriceColumn})  backorder_cost_value,
        count(1)                                          backorder_line
        FROM DEMAND_BACK_ORDER_V t
        WHERE t.${category1Column} is not null
        and t.${category2Column} is not null
        and t.BOM_CATEGORY != 'HEADER'
        <include refid="tracking_filter"/>
        <include refid="project_name_tracking_filter"/>
        <choose>
            <when test="category5Value == 'Back Order Block'.toString()">
                and t.BLOCK_STATUS = 'BLOCK'
            </when>
            <when test="category5Value == 'Back Order Non-Block'.toString()">
                and t.BLOCK_STATUS = 'NON_BLOCK'
            </when>
        </choose>
        group by t.${category1Column}, t.${category2Column}
        ) backorder ON sales.category1 = backorder.category1 and sales.category2 = backorder.category2

        full join

        (select  t.${category1Column} as                          category1,
        t.${category2Column} as                          category2,
        sum(${backlogQtyColumn})                         backlog_qty,
        sum(${backlogQtyColumn} * t.${netPriceColumn})   backlog_net_net_value,
        sum(${backlogQtyColumn} * t.${mvpPriceColumn})   backlog_cost_value,
        count(1)                                         backlog_line
        FROM DEMAND_BACKLOG_V t
        WHERE t.${category1Column} is not null
        and t.${category2Column} is not null
        and t.BOM_CATEGORY != 'HEADER'
        <include refid="tracking_filter"/>
        <include refid="project_name_tracking_filter"/>
        <choose>
            <when test="category6Value == 'Backlog Block'.toString()">
                and t.BLOCK_STATUS = 'BLOCK'
            </when>
            <when test="category6Value == 'Backlog Non-Block'.toString()">
                and t.BLOCK_STATUS = 'NON_BLOCK'
            </when>
        </choose>
        group by t.${category1Column}, t.${category2Column}
        ) backlog
        ON sales.category1 = backlog.category1 and sales.category2= backlog.category2

        full join

        (select  t.${category1Column} as                     category1,
        t.${category2Column} as                     category2,
        sum(${sohQtyColumn})                        soh_qty,
        sum(${sohQtyColumn} * t.${netPriceColumn})  soh_net_net_value,
        sum(${sohQtyColumn} * t.${mvpPriceColumn})  soh_cost_value,
        count(1)                                    soh_line
        FROM demand_soh_v t
        WHERE t.${category1Column} is not null
        and t.${category2Column} is not null
        <include refid="soh_tracking_filter"/>
        group by t.${category1Column}, t.${category2Column}
        ) soh
        ON sales.category1 = soh.category1 and sales.category2 = soh.category2

        full join

        (select t.${category1Column} as                    category1,
        t.${category2Column} as                    category2,
        sum(${fcstMonths})                         fcst_qty,
        sum((${fcstMonths}) * t.${netPriceColumn}) fcst_net_net_value,
        sum((${fcstMonths}) * t.${mvpPriceColumn}) fcst_cost_value,
        count(1)                                   fcst_line
        FROM demand_fcst_v t
        WHERE t.${category1Column} is not null
        and t.${category2Column} is not null
        and t.FCST_VERSION = #{fcstVersion,jdbcType=VARCHAR}
        <include refid="fcst_tracking_filter"/>
        group by t.${category1Column}, t.${category2Column}
        ) fcst
        ON sales.category1 = fcst.category1 and sales.category2= fcst.category2

        full join

        (select  t.${category1Column} as                        category1,
        t.${category2Column} as                        category2,
        sum(t.order_quantity)                          ud_qty,
        sum(t.order_quantity * t.${netPriceColumn})    ud_net_net_value,
        sum(t.order_quantity * t.${mvpPriceColumn})    ud_cost_value,
        count(1)                                       ud_line
        from demand_ud_v t
        where t.${category1Column} is not null
        and t.${category2Column} is not null
        and t.bom_category != 'HEADER'
        <include refid="ud_tracking_filter"/>
        <include refid="project_name_tracking_filter"/>
        group by t.${category1Column}, t.${category2Column}
        ) ud
        ON sales.category1 = ud.category1 and sales.category2= ud.category2

        full join

        (select t.${category1Column} as                       category1,
        t.${category2Column} as                       category2,
        sum(t.order_quantity)                         output_qty,
        sum(t.order_quantity * t.${netPriceColumn})   output_net_net_value,
        sum(t.order_quantity * t.${mvpPriceColumn})   output_cost_value,
        count(1)                                      output_line
        from demand_output_v t
        where t.${category1Column} is not null
        and t.${category2Column} is not null
        and t.${dateColumn} = #{saleMonth,jdbcType=VARCHAR}
        and t.bom_category != 'HEADER'
        <include refid="output_tracking_filter"/>
        <include refid="project_name_tracking_filter"/>
        group by t.${category1Column}, t.${category2Column}) output
        on sales.category1 = output.category1 and sales.category2 = output.category2
        ) mm
        group by mm.category1, mm.category2
        order by
        <if test="report1OrderBy != null and report1OrderBy != ''.toString()">
            nvl(${report1OrderBy},0) desc,
        </if>
        decode(mm.category1, 'Others', 'zzz', mm.category1), decode(mm.category2, 'Others', 'zzz', mm.category2)
    </select>

    <sql id="queryReport1DetailsSQL">
        select t.*,
        t.order_quantity * t.${mvpPriceColumn} as mvp
        from ${tableName} t
        where 1 = 1
        <if test="tableName != 'demand_back_order_v'.toString() and tableName != 'demand_backlog_v'.toString() and tableName != 'demand_soh_v'.toString() and tableName != 'demand_ud_v'.toString()">
            and ${dateColumn} = #{saleMonth,jdbcType=VARCHAR}
        </if>
        <if test="viewCategory1 != null and viewCategory1 != ''.toString()">
            and t.${category1Column} = #{viewCategory1,jdbcType=VARCHAR}
        </if>
        <if test="viewCategory2 != null and viewCategory2 != ''.toString()">
            and t.${category2Column} = #{viewCategory2,jdbcType=VARCHAR}
        </if>
        <if test="tableName != 'demand_soh_v'.toString()">
            and t.BOM_CATEGORY != 'HEADER'
        </if>
        <choose>
            <when test="tableName == 'demand_back_order_v'.toString()">
                <choose>
                    <when test="category5Value == 'Back Order Block'.toString()">
                        and t.BLOCK_STATUS = 'BLOCK'
                    </when>
                    <when test="category5Value == 'Back Order Non-Block'.toString()">
                        and t.BLOCK_STATUS = 'NON_BLOCK'
                    </when>
                </choose>
            </when>
            <when test="tableName == 'demand_backlog_v'.toString()">
                <choose>
                    <when test="category6Value == 'Backlog Block'.toString()">
                        and t.BLOCK_STATUS = 'BLOCK'
                    </when>
                    <when test="category6Value == 'Backlog Non-Block'.toString()">
                        and t.BLOCK_STATUS = 'NON_BLOCK'
                    </when>
                </choose>
            </when>
        </choose>
        <choose>
            <when test="tableName == 'demand_soh_v'.toString()">
                <include refid="soh_tracking_filter"/>
            </when>
            <when test="tableName == 'demand_output_v'.toString()">
                <include refid="output_tracking_filter"/>
            </when>
            <when test="tableName == 'demand_ud_v'.toString()">
                <include refid="ud_tracking_filter"/>
            </when>
            <otherwise>
                <include refid="tracking_filter"/>
            </otherwise>
        </choose>
        <if test="tableName != 'demand_fcst_v'.toString() and tableName != 'demand_soh_v'.toString()">
            <include refid="project_name_tracking_filter"/>
        </if>
    </sql>

    <select id="queryReport1DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport1FCSTDetailsSQL">
        <if test="saleMonthList != null and saleMonthList.isEmpty() == false">
            select  t.BU,
            t.MATERIAL,
            t.SALES_ORGANIZATION,
            t.BOM_CATEGORY,
            '' as HEADER_MATERIAL,
            t.RESCH_GROUP,
            t.MAT_PRICING_GROUP,
            t.MATERIAL_OWNER_NAME,
            t.MATERIAL_OWNER_SESA,
            t.MRP_CONTROLLER,
            t.PRODUCT_LINE,
            t.LOCAL_PRODUCT_FAMILY,
            t.LOCAL_PRODUCT_LINE,
            t.LOCAL_PRODUCT_SUBFAMILY,
            round(t.order_quantity)                         qty,
            round(t.order_quantity * t.${netPriceColumn})   net_net_value,
            round(t.order_quantity * t.${mvpPriceColumn})   mvp
            from demand_sales_v t
            where t.bom_category != 'HEADER'
            and t.calendar_month in
            <foreach collection="saleMonthList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
            <if test="viewCategory1 != null and viewCategory1 != ''.toString()">
                and t.${category1Column} = #{viewCategory1,jdbcType=VARCHAR}
            </if>
            <if test="viewCategory2 != null and viewCategory2 != ''.toString()">
                and t.${category2Column} = #{viewCategory2,jdbcType=VARCHAR}
            </if>
            <include refid="tracking_filter"/>
            <include refid="project_name_tracking_filter"/>
        </if>
        <if test="saleMonthList != null and saleMonthList.isEmpty() == false and fcstMonths != null and fcstMonths != ''.toString()">
            union all
        </if>
        <if test="fcstMonths != null and fcstMonths != ''.toString()">
            select  t.BU,
            t.MATERIAL,
            t.SALES_ORGANIZATION,
            t.BOM_CATEGORY,
            t.HEADER_MATERIAL,
            t.RESCH_GROUP,
            t.MAT_PRICING_GROUP,
            t.MATERIAL_OWNER_NAME,
            t.MATERIAL_OWNER_SESA,
            t.MRP_CONTROLLER,
            t.PRODUCT_LINE,
            t.LOCAL_PRODUCT_FAMILY,
            t.LOCAL_PRODUCT_LINE,
            t.LOCAL_PRODUCT_SUBFAMILY,
            round((${fcstMonths}),0) as qty,
            round((${fcstMonths}) * t.${netPriceColumn},0) as net_net_value,
            round((${fcstMonths}) * t.${mvpPriceColumn},0) as mvp
            from demand_fcst_v t
            where t.fcst_version = #{fcstVersion,jdbcType=VARCHAR}
            <if test="viewCategory1 != null and viewCategory1 != ''.toString()">
                and ${category1Column} = #{viewCategory1,jdbcType=VARCHAR}
            </if>
            <if test="viewCategory2 != null and viewCategory2 != ''.toString()">
                and ${category2Column} = #{viewCategory2,jdbcType=VARCHAR}
            </if>
            <include refid="fcst_tracking_filter"/>
        </if>
    </sql>

    <select id="queryReport1FCSTDetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1FCSTDetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1FCSTDetails" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1FCSTDetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport2" parameterType="java.util.Map" resultType="com.scp.demand.bean.TrackingReport2Bean">
        select coalesce(sales.xAxis, order_intake.xAxis, output.xAxis) xAxis,
        sales.sales_qty,
        sales.sales_net_net_value,
        sales.sales_cost_value,
        sales.sales_line,
        order_intake.order_intake_qty,
        order_intake.order_intake_net_net_value,
        order_intake.order_intake_cost_value,
        order_intake.order_intake_line,
        output.output_qty,
        output.output_net_net_value,
        output.output_cost_value,
        output.output_line
        from (
        select /*+ parallel(t 6) */
        ${valueColumn} as                            xAxis,
        sum(t.order_quantity)                        sales_qty,
        sum(t.order_quantity * t.${netPriceColumn})  sales_net_net_value,
        sum(t.order_quantity * t.${mvpPriceColumn})  sales_cost_value,
        count(1)                                     sales_line
        from demand_sales_v t
        where t.${dateColumn} = #{saleMonth,jdbcType=VARCHAR}
        and t.bom_category != 'HEADER'
        <if test="selectedCategory1 != null and selectedCategory1 != ''.toString()">
            and t.${category1Column} = #{selectedCategory1,jdbcType=VARCHAR}
        </if>
        <if test="selectedCategory2 != null and selectedCategory2 != ''.toString()">
            and t.${category2Column} = #{selectedCategory2,jdbcType=VARCHAR}
        </if>
        <include refid="tracking_filter"/>
        <include refid="project_name_tracking_filter"/>
        group by ${valueColumn}) sales
        full join (
        select /*+ parallel(t 6) */
        ${valueColumn} as                            xAxis,
        sum(t.order_quantity)                        order_intake_qty,
        sum(t.order_quantity * t.${netPriceColumn})  order_intake_net_net_value,
        sum(t.order_quantity * t.${mvpPriceColumn})  order_intake_cost_value,
        count(1)                                     order_intake_line
        from demand_order_intake_v t
        where ${dateColumn} = #{saleMonth,jdbcType=VARCHAR}
        and t.bom_category != 'HEADER'
        <if test="selectedCategory1 != null and selectedCategory1 != ''.toString()">
            and ${category1Column} = #{selectedCategory1,jdbcType=VARCHAR}
        </if>
        <if test="selectedCategory2 != null and selectedCategory2 != ''.toString()">
            and ${category2Column} = #{selectedCategory2,jdbcType=VARCHAR}
        </if>
        <include refid="tracking_filter"/>
        <include refid="project_name_tracking_filter"/>
        group by ${valueColumn}
        ) order_intake on sales.xaxis = order_intake.xaxis
        full join (
        select /*+ parallel(t 6) */
        ${valueColumn} as                            xAxis,
        sum(t.order_quantity)                        output_qty,
        sum(t.order_quantity * t.${netPriceColumn})  output_net_net_value,
        sum(t.order_quantity * t.${mvpPriceColumn})  output_cost_value,
        count(1)                                     output_line
        from demand_output_v t
        where ${dateColumn} = #{saleMonth,jdbcType=VARCHAR}
        and t.bom_category != 'HEADER'
        <if test="selectedCategory1 != null and selectedCategory1 != ''.toString()">
            and ${category1Column} = #{selectedCategory1,jdbcType=VARCHAR}
        </if>
        <if test="selectedCategory2 != null and selectedCategory2 != ''.toString()">
            and ${category2Column} = #{selectedCategory2,jdbcType=VARCHAR}
        </if>
        <include refid="output_tracking_filter"/>
        <include refid="project_name_tracking_filter"/>
        group by ${valueColumn}
        ) output on sales.xaxis = output.xaxis
        order by coalesce(sales.xAxis, order_intake.xAxis, output.xAxis)
    </select>

    <select id="queryReport2AMU" parameterType="java.util.Map" resultType="java.util.Map">
        select sum(t.order_quantity) amu_qty,
        sum(t.order_quantity * t.${netPriceColumn}) amu_net_net_value,
        sum(t.order_quantity * t.${mvpPriceColumn}) amu_cost_value
        from demand_sales_amu_v t
        where 1 = 1
        <if test="selectedCategory1 != null and selectedCategory1 != ''.toString()">
            and ${category1Column} = #{selectedCategory1,jdbcType=VARCHAR}
        </if>
        <if test="selectedCategory2 != null and selectedCategory2 != ''.toString()">
            and ${category2Column} = #{selectedCategory2,jdbcType=VARCHAR}
        </if>
        <include refid="tracking_filter"/>
        <include refid="project_name_tracking_filter"/>
    </select>

    <sql id="queryReport2DetailsSQL">
        select t.*,
        t.order_quantity * t.${mvpPriceColumn} as mvp
        from ${tableName} t
        where ${column} in (${saleMonth})
        and t.bom_category != 'HEADER'
        <if test="selectedCategory1 != null and selectedCategory1 != ''.toString()">
            and t.${category1Column} = #{selectedCategory1,jdbcType=VARCHAR}
        </if>
        <if test="selectedCategory2 != null and selectedCategory2 != ''.toString()">
            and t.${category2Column} = #{selectedCategory2,jdbcType=VARCHAR}
        </if>
        <include refid="tracking_filter"/>
        <if test="tableName != 'demand_order_fcst_v'.toString() and tableName != 'demand_order_soh_v'.toString()">
            <include refid="project_name_tracking_filter"/>
        </if>
    </sql>

    <select id="queryReport2DetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport2DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport2Details" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport2DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport3" parameterType="java.util.Map" resultType="com.scp.demand.bean.TrackingReport3Bean">
        select  mm.xAxis,
        sum(mm.sales_qty)                   sales_qty,
        sum(mm.sales_net_net_value)         sales_net_net_value,
        sum(mm.sales_cost_value)            sales_cost_value,
        sum(mm.sales_line)                  sales_line,
        sum(mm.order_intake_qty)            order_intake_qty,
        sum(mm.order_intake_net_net_value)  order_intake_net_net_value,
        sum(mm.order_intake_cost_value)     order_intake_cost_value,
        sum(mm.order_intake_line)           order_intake_line,
        sum(mm.crd_qty)                     crd_qty,
        sum(mm.crd_net_net_value)           crd_net_net_value,
        sum(mm.crd_cost_value)              crd_cost_value,
        sum(mm.crd_line)                    crd_line,
        sum(mm.output_qty)                  output_qty,
        sum(mm.output_net_net_value)        output_net_net_value,
        sum(mm.output_cost_value)           output_cost_value,
        sum(mm.output_line)                 output_line
        from (
        select  nvl(sales.xAxis, coalesce(order_intake.xAxis, crd.xAxis, output.xAxis)) xAxis,
        sales.sales_qty,
        sales.sales_net_net_value,
        sales.sales_cost_value,
        sales.sales_line,
        order_intake.order_intake_qty,
        order_intake.order_intake_net_net_value,
        order_intake.order_intake_cost_value,
        order_intake.order_intake_line,
        crd.crd_qty,
        crd.crd_net_net_value,
        crd.crd_cost_value,
        crd.crd_line,
        output.output_qty,
        output.output_net_net_value,
        output.output_cost_value,
        output.output_line
        from (
        select /*+ parallel */
        t.calendar_month                             xAxis,
        sum(t.order_quantity)                        sales_qty,
        sum(t.order_quantity * t.${netPriceColumn})  sales_net_net_value,
        sum(t.order_quantity * t.${mvpPriceColumn})  sales_cost_value,
        count(1)                                     sales_line
        from demand_sales_v t
        where t.calendar_month in
        <choose>
            <when test="report3SaleMonths != null and report3SaleMonths.isEmpty() == false ">
                <foreach collection="report3SaleMonths" item="item" open="(" close=")" separator=",">
                    #{item, jdbcType=VARCHAR}
                </foreach>
            </when>
            <otherwise>('0')</otherwise>
        </choose>
        and t.bom_category != 'HEADER'
        <if test="selectedCategory1 != null and selectedCategory1 != ''.toString()">
            and ${category1Column} = #{selectedCategory1,jdbcType=VARCHAR}
        </if>
        <if test="selectedCategory2 != null and selectedCategory2 != ''.toString()">
            and ${category2Column} = #{selectedCategory2,jdbcType=VARCHAR}
        </if>
        <include refid="project_name_tracking_filter"/>
        <include refid="tracking_filter"/>
        group by t.calendar_month) sales

        full join

        (
        select /*+ parallel */
        t.calendar_month                             xAxis,
        sum(t.order_quantity)                        order_intake_qty,
        sum(t.order_quantity * t.${netPriceColumn})  order_intake_net_net_value,
        sum(t.order_quantity * t.${mvpPriceColumn})  order_intake_cost_value,
        count(1)                                     order_intake_line
        from demand_order_intake_v t
        where t.calendar_month in
        <choose>
            <when test="report3SaleMonths != null and report3SaleMonths.isEmpty() == false ">
                <foreach collection="report3SaleMonths" item="item" open="(" close=")" separator=",">
                    #{item, jdbcType=VARCHAR}
                </foreach>
            </when>
            <otherwise>('0')</otherwise>
        </choose>
        and t.bom_category != 'HEADER'
        <if test="selectedCategory1 != null and selectedCategory1 != ''.toString()">
            and ${category1Column} = #{selectedCategory1,jdbcType=VARCHAR}
        </if>
        <if test="selectedCategory2 != null and selectedCategory2 != ''.toString()">
            and ${category2Column} = #{selectedCategory2,jdbcType=VARCHAR}
        </if>
        <include refid="project_name_tracking_filter"/>
        <include refid="tracking_filter"/>
        group by t.calendar_month) order_intake on sales.xAxis = order_intake.xAxis

        full join

        (
        select /*+ parallel */
        t.calendar_month                             xAxis,
        sum(t.order_quantity)                        crd_qty,
        sum(t.order_quantity * t.${netPriceColumn})  crd_net_net_value,
        sum(t.order_quantity * t.${mvpPriceColumn})  crd_cost_value,
        count(1)                                     crd_line
        from demand_crd_v t
        where t.BOM_CATEGORY != 'HEADER'
        and t.calendar_month in
        <choose>
            <when test="report3SaleMonths != null and report3SaleMonths.isEmpty() == false ">
                <foreach collection="report3SaleMonths" item="item" open="(" close=")" separator=",">
                    #{item, jdbcType=VARCHAR}
                </foreach>
            </when>
            <otherwise>('0')</otherwise>
        </choose>
        <if test="selectedCategory1 != null and selectedCategory1 != ''.toString()">
            and ${category1Column} = #{selectedCategory1,jdbcType=VARCHAR}
        </if>
        <if test="selectedCategory2 != null and selectedCategory2 != ''.toString()">
            and ${category2Column} = #{selectedCategory2,jdbcType=VARCHAR}
        </if>
        <include refid="project_name_tracking_filter"/>
        <include refid="tracking_filter"/>
        group by t.calendar_month) crd on sales.xAxis = crd.xAxis

        full join

        (
        select /*+ parallel */
        t.calendar_month                             xAxis,
        sum(t.order_quantity)                        output_qty,
        sum(t.order_quantity * t.${netPriceColumn})  output_net_net_value,
        sum(t.order_quantity * t.${mvpPriceColumn})  output_cost_value,
        count(1)                                     output_line
        from demand_output_v t
        where t.BOM_CATEGORY != 'HEADER'
        and t.calendar_month in
        <choose>
            <when test="report3SaleMonths != null and report3SaleMonths.isEmpty() == false ">
                <foreach collection="report3SaleMonths" item="item" open="(" close=")" separator=",">
                    #{item, jdbcType=VARCHAR}
                </foreach>
            </when>
            <otherwise>('0')</otherwise>
        </choose>
        <if test="selectedCategory1 != null and selectedCategory1 != ''.toString()">
            and ${category1Column} = #{selectedCategory1,jdbcType=VARCHAR}
        </if>
        <if test="selectedCategory2 != null and selectedCategory2 != ''.toString()">
            and ${category2Column} = #{selectedCategory2,jdbcType=VARCHAR}
        </if>
        <include refid="project_name_tracking_filter"/>
        <include refid="output_tracking_filter"/>
        group by t.calendar_month) output on sales.xAxis = output.xAxis
        ) mm
        group by mm.xAxis
        order by mm.xAxis
    </select>

    <select id="queryReport3FCST" parameterType="java.util.Map" resultType="java.util.Map">
        select /*+ parallel(t 6) */
        <foreach collection="fcstMonthList" separator="," item="item">
            <choose>
                <when test="item == '0'.toString()">
                    0 as qty,
                    0 as net_net_value,
                    0 as cost_value
                </when>
                <otherwise>
                    sum(${item}) as ${item}_qty,
                    sum(${item} * t.${netPriceColumn}) as ${item}_net_net_value,
                    sum(${item} * t.${mvpPriceColumn}) as ${item}_cost_value
                </otherwise>
            </choose>
        </foreach>
        from demand_fcst_v t
        where t.fcst_version = #{fcstVersion,jdbcType=VARCHAR}
        <if test="selectedCategory1 != null and selectedCategory1 != ''.toString()">
            and ${category1Column} = #{selectedCategory1,jdbcType=VARCHAR}
        </if>
        <if test="selectedCategory2 != null and selectedCategory2 != ''.toString()">
            and ${category2Column} = #{selectedCategory2,jdbcType=VARCHAR}
        </if>
        <include refid="fcst_tracking_filter"/>
    </select>

    <select id="queryReport4" parameterType="java.util.Map" resultType="com.scp.demand.bean.TrackingReport4Bean">
        select /*+ parallel(t 6) */
        ${crdValueColumn}                            xAxis,
        sum(t.ORDER_QUANTITY)                        crd_qty,
        sum(t.ORDER_QUANTITY * t.${netPriceColumn})  crd_net_net_value,
        sum(t.ORDER_QUANTITY * t.${mvpPriceColumn})  crd_cost_value
        from DEMAND_CRD_HIST t
        where ${crdDateColumn} = #{saleMonth,jdbcType=VARCHAR}
        <if test="selectedCategory1 != null and selectedCategory1 != ''.toString()">
            and ${category1Column} = #{selectedCategory1,jdbcType=VARCHAR}
        </if>
        <if test="selectedCategory2 != null and selectedCategory2 != ''.toString()">
            and ${category2Column} = #{selectedCategory2,jdbcType=VARCHAR}
        </if>
        <include refid="project_name_tracking_filter"/>
        <include refid="tracking_filter"/>
        group by ${crdValueColumn}
    </select>

    <select id="queryFCSTData" parameterType="java.util.Map" resultType="java.util.Map">
        select <if test="groupby != null and groupby != ''.toString()">
        mm.category,
    </if>
        SUM(CASE WHEN TYPE IN ('SALES', 'FCST') THEN QTY ELSE 0 END) QTY,
        SUM(CASE WHEN TYPE IN ('SALES', 'FCST') THEN NET_NET_VALUE ELSE 0 END) NET_NET_VALUE,
        SUM(CASE WHEN TYPE IN ('SALES', 'FCST') THEN COST_VALUE ELSE 0 END) COST_VALUE,
        SUM(CASE WHEN TYPE IN ('SALES', 'FULFILL_FCST') THEN QTY ELSE 0 END) FULFILL_QTY,
        SUM(CASE WHEN TYPE IN ('SALES', 'FULFILL_FCST') THEN NET_NET_VALUE ELSE 0 END) FULFILL_NET_NET_VALUE,
        SUM(CASE WHEN TYPE IN ('SALES', 'FULFILL_FCST') THEN COST_VALUE ELSE 0 END) FULFILL_COST_VALUE
        from (
        <if test="saleMonthList != null and saleMonthList.isEmpty() == false">
            select /*+ parallel(t 6) */
            'SALES' AS TYPE,
            <if test="groupby != null and groupby != ''.toString()">
                t.${groupby} category,
            </if>
            sum(t.order_quantity)                        qty,
            sum(t.order_quantity * t.${netPriceColumn})  net_net_value,
            sum(t.order_quantity * t.${mvpPriceColumn})  cost_value
            from demand_sales_v t
            where t.bom_category != 'HEADER'
            and t.calendar_month in
            <foreach collection="saleMonthList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
            <if test="selectedCategory1 != null and selectedCategory1 != ''.toString()">
                and t.${category1Column} = #{selectedCategory1,jdbcType=VARCHAR}
            </if>
            <if test="selectedCategory2 != null and selectedCategory2 != ''.toString()">
                and t.${category2Column} = #{selectedCategory2,jdbcType=VARCHAR}
            </if>
            <include refid="tracking_filter"/>
            <include refid="project_name_tracking_filter"/>
            <if test="groupby != null and groupby != ''.toString()">
                group by t.${groupby}
            </if>
        </if>
        <if test="saleMonthList != null and saleMonthList.isEmpty() == false and fcstMonths != null and fcstMonths != ''.toString()">
            union all
        </if>
        <if test="fcstMonths != null and fcstMonths != ''.toString()">
            select  'FCST' as type,
            <if test="groupby != null and groupby != ''.toString()">
                t.${groupby} category,
            </if>
            sum(${fcstMonths})                   qty,
            sum(t.${netPriceColumn} * (${fcstMonths})) net_net_value,
            sum(t.${mvpPriceColumn} * (${fcstMonths})) cost_value
            from demand_fcst_v t
            where t.fcst_version = #{fcstVersion,jdbcType=VARCHAR}
            <if test="selectedCategory1 != null and selectedCategory1 != ''.toString()">
                and ${category1Column} = #{selectedCategory1,jdbcType=VARCHAR}
            </if>
            <if test="selectedCategory2 != null and selectedCategory2 != ''.toString()">
                and ${category2Column} = #{selectedCategory2,jdbcType=VARCHAR}
            </if>
            <include refid="fcst_tracking_filter"/>
            <if test="groupby != null and groupby != ''.toString()">
                group by t.${groupby}
            </if>
        </if>
        <if test="fcstFulfillMonths != null and fcstFulfillMonths != ''.toString()">
            union all
            select  'FULFILL_FCST' as type,
            <if test="groupby != null and groupby != ''.toString()">
                t.${groupby} category,
            </if>
            sum(${fcstFulfillMonths})                   qty,
            sum(t.${netPriceColumn} * (${fcstFulfillMonths})) net_net_value,
            sum(t.${mvpPriceColumn} * (${fcstFulfillMonths})) cost_value
            from demand_fcst_v t
            where t.fcst_version = #{fcstVersion,jdbcType=VARCHAR}
            <if test="selectedCategory1 != null and selectedCategory1 != ''.toString()">
                and ${category1Column} = #{selectedCategory1,jdbcType=VARCHAR}
            </if>
            <if test="selectedCategory2 != null and selectedCategory2 != ''.toString()">
                and ${category2Column} = #{selectedCategory2,jdbcType=VARCHAR}
            </if>
            <include refid="fcst_tracking_filter"/>
            <if test="groupby != null and groupby != ''.toString()">
                group by t.${groupby}
            </if>
        </if>
        ) mm
        <if test="groupby != null and groupby != ''.toString()">
            group by mm.category
        </if>
    </select>

    <select id="getLastWDMonthRatio" resultType="java.lang.Double">
        WITH LAST_WD AS (SELECT T.DATE$
                         FROM SY_CALENDAR T
                         WHERE T.NAME = 'National Holidays'
                           AND T.WORKING_DAY = 1
                           AND T.DATE$ &lt; SYSDATE - 1
                         ORDER BY DATE$ DESC
                             FETCH NEXT 1 ROWS ONLY),
             TEMP AS (SELECT T.DATE$, T.WORKING_DAY
                      FROM SY_CALENDAR T
                      WHERE T.YEAR = TO_CHAR((SELECT T.DATE$ FROM LAST_WD T), 'YYYY')
                        AND T.MONTH = TO_CHAR((SELECT T.DATE$ FROM LAST_WD T), 'MM')
                        AND T.NAME = 'National Holidays'
                        AND T.WORKING_DAY = '1'),
             TOTAL_WD AS (SELECT DECODE(COUNT(1), 0, 9999, COUNT(1)) TOTAL
                          FROM TEMP),
             CURRENT_WD AS (SELECT COUNT(1) CURT
                            FROM TEMP T
                            WHERE T.DATE$ &lt;= (SELECT T.DATE$ FROM LAST_WD T))
        SELECT CURRENT_WD.CURT / TOTAL_WD.TOTAL FROM TOTAL_WD CROSS JOIN CURRENT_WD
    </select>

    <select id="queryWorkingDayList" parameterType="java.lang.String" resultType="java.util.Map">
        select text,working_day from sy_calendar where name = 'National Holidays' and text like #{_parameter,jdbcType=VARCHAR} || '%' order by text
    </select>

    <select id="getTotalWorkingDay" resultType="java.lang.Integer">
        select NVL(sum(working_day), 1) as total from sy_calendar where name = 'National Holidays' and ${column} = #{month,jdbcType=VARCHAR}
    </select>

    <select id="getTotalFulfillWorkingDay" resultType="java.lang.Integer">
        select NVL(sum(working_day), 1) as total from sy_calendar
        where name = 'National Holidays' and ${column} = #{month,jdbcType=VARCHAR} and DATE$ &lt; TRUNC(ADD_MONTHS(SYSDATE - 1, 1), 'MM')
    </select>

    <select id="queryAuthDetails" resultType="java.lang.String">
        select auth_details from sy_menu_auth where lower(user_id) = lower(#{userid, jdbcType=VARCHAR}) and menu_code = #{menuCode, jdbcType=VARCHAR}
    </select>

    <select id="queryQuarterWeekList" resultType="java.lang.String">
        SELECT DISTINCT T.YEAR || 'W' || T.WEEK_NO FROM SY_CALENDAR T
        WHERE T.NAME = 'National Holidays' AND T.YEAR = #{year, jdbcType=VARCHAR}
        <choose>
            <when test="quarter == '1'.toString()">
                AND T.MONTH IN ('01', '02', '03')
            </when>
            <when test="quarter == '2'.toString()">
                AND T.MONTH IN ('04', '05', '06')
            </when>
            <when test="quarter == '3'.toString()">
                AND T.MONTH IN ('07', '08', '09')
            </when>
            <when test="quarter == '4'.toString()">
                AND T.MONTH IN ('10', '11', '12')
            </when>
            <otherwise>
                AND 0 = 1
            </otherwise>
        </choose>
        ORDER BY T.YEAR || 'W' || T.WEEK_NO
    </select>

    <select id="queryThisWeek" resultType="java.lang.String">
        SELECT T.YEAR || 'W' || T.WEEK_NO FROM SY_CALENDAR T
        WHERE T.NAME = 'National Holidays' AND T.DATE$ = TRUNC(SYSDATE, 'DD')
    </select>

    <select id="getFulfillWorkingDay" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM SY_CALENDAR T
        WHERE T.NAME = 'National Holidays'
          AND T.YEAR || T.MONTH = #{current, jdbcType=VARCHAR}
          AND T.DATE$ &lt; TRUNC(SYSDATE, 'DD')
          AND T.WORKING_DAY = '1'
    </select>

    <select id="queryWorkingDayRatioQuarterList" resultType="java.util.Map">
        WITH LAST_WD AS (SELECT T.DATE$
                         FROM SY_CALENDAR T
                         WHERE T.NAME = 'National Holidays'
                           AND T.WORKING_DAY = 1
                           AND T.DATE$ &lt; SYSDATE - 1
                         ORDER BY DATE$ DESC
                             FETCH NEXT 1 ROWS ONLY)
        SELECT YEAR || 'W' || WEEK_NO AS KEY, SUM(WORKING_DAY) AS VAL
        FROM SY_CALENDAR
        WHERE NAME = 'National Holidays'
          AND TO_CHAR(TO_DATE(TEXT, 'YYYY/MM/DD'), 'YYYY"Q"Q') = #{quarter, jdbcType=VARCHAR}
          AND DATE$ &lt;= (SELECT T.DATE$ FROM LAST_WD T)
        GROUP BY YEAR || 'W' || WEEK_NO
    </select>

    <select id="queryWorkingFulfillMonth" resultType="java.lang.Double">
        WITH LAST_WD AS (SELECT T.DATE$
                         FROM SY_CALENDAR T
                         WHERE T.NAME = 'National Holidays'
                           AND T.WORKING_DAY = 1
                           AND T.DATE$ &lt; SYSDATE - 1
                         ORDER BY DATE$ DESC
                             FETCH NEXT 1 ROWS ONLY),
             TEMP AS (SELECT T.DATE$, T.WORKING_DAY
                      FROM SY_CALENDAR T
                      WHERE T.YEAR = TO_CHAR((SELECT T.DATE$ FROM LAST_WD T), 'YYYY')
                        AND T.MONTH = TO_CHAR((SELECT T.DATE$ FROM LAST_WD T), 'MM')
                        AND T.NAME = 'National Holidays'
                        AND T.WORKING_DAY = '1'),
             TOTAL_WD AS (SELECT DECODE(COUNT(1), 0, 9999, COUNT(1)) TOTAL
                          FROM TEMP),
             CURRENT_WD AS (SELECT COUNT(1) CURT
                            FROM TEMP T
                            WHERE T.DATE$ &lt;= (SELECT T.DATE$ FROM LAST_WD T))
        SELECT CURRENT_WD.CURT / TOTAL_WD.TOTAL + (SELECT TO_NUMBER(TO_CHAR(T.DATE$, 'MM') - 1) FROM LAST_WD T)
        FROM TOTAL_WD CROSS JOIN CURRENT_WD
    </select>

    <select id="queryQuarterFcstByWeek" parameterType="java.util.Map" resultType="java.util.Map">
        WITH DATA AS (<if test="saleMonthList != null and saleMonthList.isEmpty() == false">
        SELECT /*+ PARALLEL(T 6) */
        SUBSTR(T.CALENDAR_WEEK,0,4) || 'W' || SUBSTR(T.CALENDAR_WEEK,5,2)    MONTH,
        SUM(T.ORDER_QUANTITY * ${columnType})                                VAL
        FROM DEMAND_SALES_V t
        WHERE T.BOM_CATEGORY != 'HEADER'
        AND T.CALENDAR_MONTH IN
        <foreach collection="saleMonthList" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        <if test="selectedCategory1 != null and selectedCategory1 != ''.toString()">
            and t.${category1Column} = #{selectedCategory1,jdbcType=VARCHAR}
        </if>
        <if test="selectedCategory2 != null and selectedCategory2 != ''.toString()">
            and t.${category2Column} = #{selectedCategory2,jdbcType=VARCHAR}
        </if>
        <include refid="tracking_filter"/>
        <include refid="project_name_tracking_filter"/>
        GROUP BY T.CALENDAR_WEEK
    </if>
        <if test="saleMonthList != null and saleMonthList.isEmpty() == false and fcstFulfillMonthList != null and fcstFulfillMonthList.size() > 0">
            union all
        </if>
        <if test="fcstFulfillMonthList != null and fcstFulfillMonthList.size() > 0">
            <foreach collection="fcstFulfillMonthList" item="item" separator=" union all">
                SELECT ${item}
                FROM DEMAND_FCST_V t
                WHERE T.FCST_VERSION = #{fcstVersion,jdbcType=VARCHAR}
                <if test="selectedCategory1 != null and selectedCategory1 != ''.toString()">
                    and ${category1Column} = #{selectedCategory1,jdbcType=VARCHAR}
                </if>
                <if test="selectedCategory2 != null and selectedCategory2 != ''.toString()">
                    and ${category2Column} = #{selectedCategory2,jdbcType=VARCHAR}
                </if>
                <include refid="fcst_tracking_filter"/>
            </foreach>
        </if>),
        LAST_WD AS (SELECT T.DATE$
        FROM SY_CALENDAR T
        WHERE T.NAME = 'National Holidays'
        AND T.WORKING_DAY = 1
        AND T.DATE$ &lt; SYSDATE - 1
        ORDER BY DATE$ DESC
        FETCH NEXT 1 ROWS ONLY),
        TEMP AS (SELECT T.YEAR, T.WORKING_DAY, T.MONTH, T.WEEK_NO
        FROM SY_CALENDAR T
        WHERE T.NAME = 'National Holidays'
        AND T.DATE$ BETWEEN (SELECT DATE$ - 365 FROM LAST_WD)
        AND (SELECT DATE$ FROM LAST_WD)
        ),
        TEMP2 AS (SELECT T.YEAR || T.MONTH AS MONTH, SUM(T.WORKING_DAY) TOTAL_WD
        FROM TEMP T
        GROUP BY T.YEAR || T.MONTH),
        MONTH_RATIO AS (SELECT T.YEAR || 'W' || T.WEEK_NO AS WEEK, T.YEAR || T.MONTH AS MONTH, SUM(WORKING_DAY) / T2.TOTAL_WD AS RATIO
        FROM TEMP T
        LEFT JOIN TEMP2 T2 ON T.YEAR || T.MONTH = T2.MONTH
        GROUP BY T.WEEK_NO, T.YEAR, T.MONTH, T2.TOTAL_WD),
        MONTH_VAL AS (
        SELECT NVL(T2.WEEK, T.MONTH) WEEK, NVL(T.VAL * T2.RATIO, T.VAL) VAL
        FROM DATA T LEFT JOIN MONTH_RATIO T2 ON T.MONTH = T2.MONTH
        )
        SELECT WEEK, CASE WHEN ${total} = 0  THEN 0 ELSE SUM(VAL) / ${total} END RATIO
        FROM MONTH_VAL
        GROUP BY WEEK
    </select>

    <select id="queryYearFcstByMonth" parameterType="java.util.Map" resultType="java.util.Map">
        WITH DATA AS (<if test="saleMonthList != null and saleMonthList.isEmpty() == false">
        SELECT /*+ PARALLEL(T 6) */
        CALENDAR_MONTH                              MONTH,
        SUM(T.ORDER_QUANTITY * ${columnType})       VAL
        FROM DEMAND_SALES_V t
        WHERE T.BOM_CATEGORY != 'HEADER'
        AND T.CALENDAR_MONTH IN
        <foreach collection="saleMonthList" item="item" separator="," open="(" close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        <if test="selectedCategory1 != null and selectedCategory1 != ''.toString()">
            and t.${category1Column} = #{selectedCategory1,jdbcType=VARCHAR}
        </if>
        <if test="selectedCategory2 != null and selectedCategory2 != ''.toString()">
            and t.${category2Column} = #{selectedCategory2,jdbcType=VARCHAR}
        </if>
        <include refid="tracking_filter"/>
        <include refid="project_name_tracking_filter"/>
        GROUP BY T.CALENDAR_MONTH
    </if>
        <if test="saleMonthList != null and saleMonthList.isEmpty() == false and fcstFulfillMonthList != null and fcstFulfillMonthList.size() > 0">
            union all
        </if>
        <if test="fcstFulfillMonthList != null and fcstFulfillMonthList.size() > 0">
            <foreach collection="fcstFulfillMonthList" item="item" separator=" union all">
                SELECT ${item}
                FROM DEMAND_FCST_V t
                WHERE T.FCST_VERSION = #{fcstVersion,jdbcType=VARCHAR}
                <if test="selectedCategory1 != null and selectedCategory1 != ''.toString()">
                    and ${category1Column} = #{selectedCategory1,jdbcType=VARCHAR}
                </if>
                <if test="selectedCategory2 != null and selectedCategory2 != ''.toString()">
                    and ${category2Column} = #{selectedCategory2,jdbcType=VARCHAR}
                </if>
                <include refid="fcst_tracking_filter"/>
            </foreach>
        </if>)
        SELECT T.MONTH, CASE WHEN ${total} = 0 THEN 0 ELSE SUM(T.VAL) / ${total} END RATIO
        FROM DATA T
        GROUP BY T.MONTH
    </select>
</mapper>
