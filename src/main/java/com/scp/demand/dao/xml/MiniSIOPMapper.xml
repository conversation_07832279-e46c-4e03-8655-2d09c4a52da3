<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.demand.dao.IMiniSIOPDao">

    <sql id="minisiop_filter">
        <if test="filters != null and filters != ''.toString()">
            AND ${filters}
        </if>
        <if test="specialList != null and specialList.size() > 0">
            <foreach collection="specialList" item="list" separator=" or " open=" and (" close=")">
                t.${specialColumn} in
                <foreach collection="list" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </foreach>
        </if>
        <if test="scopeList != null and scopeList.size() > 0">
            AND T.SE_SCOPE in
            <foreach collection="scopeList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </sql>

    <sql id="minisiop_filter2">
        <if test="filters != null and filters != ''.toString()">
            AND ${filters}
        </if>
        <if test="specialList != null and specialList.size() > 0">
            <foreach collection="specialList" item="list" separator=" or " open=" and (" close=")">
                t.${specialColumn} in
                <foreach collection="list" item="item" separator="," open="(" close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </foreach>
        </if>
        <if test="scope != null and scope.size() > 0">
            AND T.SE_SCOPE IN
            <foreach collection="scope" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </sql>

    <select id="queryCascader" resultType="java.util.Map">
        SELECT * FROM SIOP_FILTER_V T ORDER BY T.CATEGORY,DECODE(t.NAME,'Others','zzz',t.NAME)
    </select>

    <select id="queryFcstVersion" resultType="java.lang.String">
        SELECT DISTINCT T.FCST_VERSION FROM SIOP_FCST_MONTH_V T ORDER BY FCST_VERSION DESC
    </select>

    <!-- Common Configration -->

    <sql id="columnNames">
           T.MATERIAL,
           T.PLANT_CODE,
           T.SALES_ORGANIZATION,
           T.SE_SCOPE,
           T.SOLD_TO,
           T.SOLD_TO_COUNTRY,
           T.SHIP_TO,
           T.SHIP_TO_REGION,
           T.SHIP_TO_SUB_REGION,
           T.MATERIAL_OWNER_NAME,
           T.MATERIAL_OWNER_SESA,
           T.MRP_CONTROLLER,
           T.PRODUCT_LINE,
           T.ENTITY,
           T.CLUSTER_NAME,
           T.BU,
           T.LOCAL_BU,
           T.LOCAL_PRODUCT_FAMILY,
           T.LOCAL_PRODUCT_LINE,
           T.LOCAL_PRODUCT_SUBFAMILY,
           T.STOCKING_POLICY,
           T.VENDOR_CODE,
           T.VENDOR_NAME,
           T.ACTIVENESS
    </sql>

    <!-- Demand -->

    <select id="querySummary_Demand" resultType="java.util.Map">
        SELECT RATIO_LEVEL,
               SUM(CASE WHEN RATIO_LEVEL IN ('GT070', 'GT030', 'LT030') THEN -1 * "VALUE"
                        ELSE "VALUE" END)        "VALUE",
               SUM(PREDICTED_VALUE)              PREDICTED_VALUE
        from (
                 SELECT CASE
                            WHEN RATIO > 2 THEN 'GT200'
                            WHEN RATIO >= 1.7 THEN 'GT170'
                            WHEN RATIO >= 1.3 THEN 'GT130'
                            WHEN RATIO > 1 THEN 'GT100'
                            WHEN RATIO >= 0.7 THEN 'GT070'
                            WHEN RATIO >= 0.3 THEN 'GT030'
                            ELSE 'LT030' END RATIO_LEVEL,
                        "VALUE",
                        PREDICTED_VALUE
                 from (
                          SELECT DECODE(NVL(fcst.FCST_QTY, 0), 0, 9.9, NVL(sales.SALES_QTY, 0) / fcst.FCST_QTY) RATIO,
                                 NVL(sales.SALES_VALUE, 0) - NVL(fcst.FCST_VALUE, 0) "VALUE",
                                 CASE
                                     WHEN NVL(fcst.FCST_QTY, 0) > NVL(sales.SALES_QTY, 0) THEN NVL(sales.SALES_VALUE, 0)
                                     ELSE NVL(fcst.FCST_VALUE, 0) END               PREDICTED_VALUE

                          from (SELECT t.MATERIAL,
                                       SUM((${fcst_month_columns}))                 FCST_QTY,
                                       SUM((${fcst_month_columns}) ${fcstColumn})   FCST_VALUE
                                  FROM SIOP_FCST_MONTH_V t
                                 WHERE t.FCST_VERSION = #{fcstVersion, jdbcType=VARCHAR}
                                   AND t.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                                <include refid="minisiop_filter"/>
                                GROUP BY t.MATERIAL) fcst
                                   FULL JOIN
                               (
                                   SELECT t.MATERIAL,
                                          SUM(ORDER_QUANTITY) SALES_QTY,
                                          SUM(${salesColumn}) SALES_VALUE
                                     FROM SIOP_SALES_MONTH_V t
                                    WHERE t.CALENDAR_MONTH BETWEEN GREATEST(#{fcstVersion, jdbcType=VARCHAR}, #{dateRange[0], jdbcType=VARCHAR}) AND #{dateRange[1], jdbcType=VARCHAR}
                                      AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                                   <include refid="minisiop_filter"/>
                                   GROUP BY t.MATERIAL
                               ) sales
                               ON sales.MATERIAL = fcst.MATERIAL) mm) nn
        GROUP BY RATIO_LEVEL
    </select>

    <select id="querySummary_Demand_ActualSalesBeforeSF" resultType="java.math.BigDecimal">
        SELECT SUM(${salesColumn}) SALES_VALUE
        FROM SIOP_SALES_MONTH_V t
        WHERE t.CALENDAR_MONTH >= #{dateRange[0], jdbcType=VARCHAR}
        AND t.CALENDAR_MONTH &lt; #{fcstVersion, jdbcType=VARCHAR}
        AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
        <include refid="minisiop_filter"/>
    </select>

    <sql id="queryDetails_Demand_ActualSalesBeforeSF">
       SELECT <include refid="columnNames"/>,
              T.ORDER_QUANTITY,
              T.NET_NET_VALUE,
              T.MOVING_AVERAGE_P,
              T.UNIT_COST
         FROM SIOP_SALES_MONTH_V t
        WHERE t.CALENDAR_MONTH >= #{dateRange[0], jdbcType=VARCHAR}
        AND t.CALENDAR_MONTH &lt; #{fcstVersion, jdbcType=VARCHAR}
        AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
        <include refid="minisiop_filter"/>
    </sql>

    <sql id="queryDetails_Demand_Base">
        WITH DETAILS_TEMP AS (
         SELECT MATERIAL,
                CASE
                WHEN RATIO > 2 THEN 'GT200'
                WHEN RATIO >= 1.7 THEN 'GT170'
                WHEN RATIO >= 1.3 THEN 'GT130'
                WHEN RATIO > 1 THEN 'GT100'
                WHEN RATIO >= 0.7 THEN 'GT070'
                WHEN RATIO >= 0.3 THEN 'GT030'
                ELSE 'LT030' END RATIO_LEVEL,
                "VALUE" * -1     UNREALIZED_VALUE,
                "VALUE"          EXCEED_VALUE,
                PREDICTED_VALUE
         from (
                  SELECT NVL(fcst.MATERIAL, sales.MATERIAL) MATERIAL,
                         DECODE(NVL(fcst.FCST_QTY, 0), 0, 9.9, NVL(sales.SALES_QTY, 0) / fcst.FCST_QTY) RATIO,
                         NVL(sales.SALES_VALUE, 0) - NVL(fcst.FCST_VALUE, 0) "VALUE",
                         CASE
                             WHEN NVL(fcst.FCST_QTY, 0) > NVL(sales.SALES_QTY, 0) THEN NVL(sales.SALES_VALUE, 0)
                             ELSE NVL(fcst.FCST_VALUE, 0) END               PREDICTED_VALUE

                  from (SELECT t.MATERIAL,
                               SUM((${fcst_month_columns}))                 FCST_QTY,
                               SUM((${fcst_month_columns}) ${fcstColumn})   FCST_VALUE
                          FROM SIOP_FCST_MONTH_V t
                         WHERE t.FCST_VERSION = #{fcstVersion, jdbcType=VARCHAR}
                           AND t.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                        <include refid="minisiop_filter"/>
                        GROUP BY t.MATERIAL) fcst
                           FULL JOIN
                       (
                           SELECT t.MATERIAL,
                                  SUM(ORDER_QUANTITY) SALES_QTY,
                                  SUM(${salesColumn}) SALES_VALUE
                             FROM SIOP_SALES_MONTH_V t
                            WHERE t.CALENDAR_MONTH BETWEEN GREATEST(#{fcstVersion, jdbcType=VARCHAR}, #{dateRange[0], jdbcType=VARCHAR}) AND #{dateRange[1], jdbcType=VARCHAR}
                              AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                           <include refid="minisiop_filter"/>
                           GROUP BY t.MATERIAL
                       ) sales
                       ON sales.MATERIAL = fcst.MATERIAL) mm
        )
    </sql>

    <sql id="queryDetails_Demand_Unrealized_Base">
       <include refid="queryDetails_Demand_Base"/>
       SELECT <include refid="columnNames"/>,
              (${fcst_month_columns}) FCST_QTY,
              T.UNIT_COST,
              T.AVG_SELLING_PRICE_RMB,
              T2.UNREALIZED_VALUE
         FROM SIOP_FCST_MONTH_V T INNER JOIN DETAILS_TEMP T2 ON T.MATERIAL = T2.MATERIAL
        WHERE T.FCST_VERSION = #{fcstVersion, jdbcType=VARCHAR}
              AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
              <include refid="minisiop_filter"/>
    </sql>

    <sql id="queryDetails_Demand_UnrealizedLT30Fcst">
       <include refid="queryDetails_Demand_Unrealized_Base"/>
       AND RATIO_LEVEL = 'LT030'
    </sql>

    <sql id="queryDetails_Demand_UnrealizedGT30Fcst">
        <include refid="queryDetails_Demand_Unrealized_Base"/>
        AND RATIO_LEVEL = 'GT030'
    </sql>

    <sql id="queryDetails_Demand_UnrealizedGT70Fcst">
        <include refid="queryDetails_Demand_Unrealized_Base"/>
        AND RATIO_LEVEL = 'GT070'
    </sql>

    <sql id="queryDetails_Demand_PredictedSales">
        <include refid="queryDetails_Demand_Base"/>
       SELECT <include refid="columnNames"/>,
              T.ORDER_QUANTITY,
              T.NET_NET_VALUE,
              T.MOVING_AVERAGE_P,
              T.UNIT_COST,
              T2.PREDICTED_VALUE
         FROM SIOP_SALES_MONTH_V T INNER JOIN DETAILS_TEMP T2 ON T.MATERIAL = T2.MATERIAL
        WHERE t.CALENDAR_MONTH BETWEEN GREATEST(#{fcstVersion, jdbcType=VARCHAR}, #{dateRange[0], jdbcType=VARCHAR}) AND #{dateRange[1], jdbcType=VARCHAR}
              AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
              <include refid="minisiop_filter"/>
    </sql>

    <sql id="queryDetails_Demand_SalesExFcst_Base">
       <include refid="queryDetails_Demand_Base"/>
       SELECT <include refid="columnNames"/>,
              T.ORDER_QUANTITY,
              T.NET_NET_VALUE,
              T.MOVING_AVERAGE_P,
              T.UNIT_COST,
              T2.EXCEED_VALUE
         FROM SIOP_SALES_MONTH_V T INNER JOIN DETAILS_TEMP T2 ON T.MATERIAL = T2.MATERIAL
        WHERE t.CALENDAR_MONTH BETWEEN GREATEST(#{fcstVersion, jdbcType=VARCHAR}, #{dateRange[0], jdbcType=VARCHAR}) AND #{dateRange[1], jdbcType=VARCHAR}
              AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
              <include refid="minisiop_filter"/>
    </sql>

    <sql id="queryDetails_Demand_SalesExFcstLT30">
        <include refid="queryDetails_Demand_SalesExFcst_Base"/>
        AND RATIO_LEVEL = 'GT100'
    </sql>

    <sql id="queryDetails_Demand_SalesExFcstGT30">
        <include refid="queryDetails_Demand_SalesExFcst_Base"/>
        AND RATIO_LEVEL = 'GT130'
    </sql>

    <sql id="queryDetails_Demand_SalesExFcstGT70">
        <include refid="queryDetails_Demand_SalesExFcst_Base"/>
        AND RATIO_LEVEL = 'GT170'
    </sql>

    <sql id="queryDetails_Demand_SalesExFcstGT100">
        <include refid="queryDetails_Demand_SalesExFcst_Base"/>
        AND RATIO_LEVEL = 'GT200'
    </sql>

    <!-- STD Sales -->

    <select id="querySummary_STDSales_IGSales" resultType="java.math.BigDecimal">
        SELECT SUM(${salesColumn})
          FROM SIOP_SALES_MONTH_V t
         WHERE t.SE_SCOPE IN ('SECI_IG', 'SEHK_IG')
           AND t.CALENDAR_MONTH BETWEEN #{dateRange[0], jdbcType=VARCHAR} AND #{dateRange[1], jdbcType=VARCHAR}
        <include refid="minisiop_filter"/>
    </select>

    <sql id="queryDetails_STDSales_IGSales">
        SELECT <include refid="columnNames"/>,
               T.ORDER_QUANTITY,
               T.NET_NET_VALUE,
               T.MOVING_AVERAGE_P,
               T.UNIT_COST
          FROM SIOP_SALES_MONTH_V t
         WHERE t.SE_SCOPE IN ('SECI_IG', 'SEHK_IG')
           AND t.CALENDAR_MONTH BETWEEN #{dateRange[0], jdbcType=VARCHAR} AND #{dateRange[1], jdbcType=VARCHAR}
        <include refid="minisiop_filter"/>
    </sql>

    <!-- STD Order Intake -->

    <select id="querySummary_STDOI" resultType="java.util.Map">
        SELECT RATIO_LEVEL,
               SUM(VALUE)             "VALUE",
               SUM(PREDICTED_VALUE)   PREDICTED_VALUE
        from (
                 SELECT CASE
                            WHEN RATIO > 2 THEN 'GT200'
                            WHEN RATIO >= 1.7 THEN 'GT170'
                            WHEN RATIO >= 1.3 THEN 'GT130'
                            WHEN RATIO > 1 THEN 'GT100'
                            ELSE 'LT100' END RATIO_LEVEL,
                        "VALUE",
                        PREDICTED_VALUE
                 from (
                          SELECT DECODE(NVL(fcst.FCST_QTY, 0), 0, 9.9, NVL(sales.SALES_QTY, 0) / fcst.FCST_QTY) RATIO,
                                 NVL(sales.SALES_VALUE, 0) - NVL(fcst.FCST_VALUE, 0) "VALUE",
                                 CASE
                                     WHEN NVL(fcst.FCST_QTY, 0) > NVL(sales.SALES_QTY, 0) THEN NVL(sales.SALES_VALUE, 0)
                                     ELSE NVL(fcst.FCST_VALUE, 0) END               PREDICTED_VALUE

                          from (SELECT t.MATERIAL,
                                       SUM((${fcst_month_columns}))                 FCST_QTY,
                                       SUM((${fcst_month_columns}) ${fcstColumn})   FCST_VALUE
                                  FROM SIOP_FCST_MONTH_V t
                                 WHERE t.FCST_VERSION = #{fcstVersion, jdbcType=VARCHAR}
                                   AND t.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                                <include refid="minisiop_filter"/>
                                GROUP BY t.MATERIAL) fcst
                                   FULL JOIN
                               (
                                   SELECT t.MATERIAL,
                                          SUM(ORDER_QUANTITY) SALES_QTY,
                                          SUM(${salesColumn}) SALES_VALUE
                                     FROM SIOP_ORDER_INTAKE_MONTH_V t
                                    WHERE t.CALENDAR_MONTH BETWEEN GREATEST(#{fcstVersion, jdbcType=VARCHAR}, #{dateRange[0], jdbcType=VARCHAR}) AND #{dateRange[1], jdbcType=VARCHAR}
                                      AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                                   <include refid="minisiop_filter"/>
                                   GROUP BY t.MATERIAL
                               ) sales
                               ON sales.MATERIAL = fcst.MATERIAL) mm) nn
        GROUP BY RATIO_LEVEL
    </select>

    <select id="querySummary_STDOI_IGOI" resultType="java.math.BigDecimal">
        SELECT SUM(${salesColumn})
          FROM SIOP_ORDER_INTAKE_MONTH_V t
         WHERE t.SE_SCOPE IN ('SECI_IG', 'SEHK_IG')
           AND t.CALENDAR_MONTH BETWEEN #{dateRange[0], jdbcType=VARCHAR} AND #{dateRange[1], jdbcType=VARCHAR}
        <include refid="minisiop_filter"/>
    </select>

    <select id="querySummary_STDOI_ActualOIBeforeSF" resultType="java.math.BigDecimal">
        SELECT SUM(${salesColumn}) OI_VALUE
          FROM SIOP_ORDER_INTAKE_MONTH_V t
         WHERE t.CALENDAR_MONTH >= #{dateRange[0], jdbcType=VARCHAR}
           AND t.CALENDAR_MONTH &lt; #{fcstVersion, jdbcType=VARCHAR}
           AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
        <include refid="minisiop_filter"/>
    </select>

    <sql id="queryDetails_STDOI_Base">
        WITH DETAILS_TEMP AS (
             SELECT MATERIAL,
                    CASE
                        WHEN RATIO > 2 THEN 'GT200'
                        WHEN RATIO >= 1.7 THEN 'GT170'
                        WHEN RATIO >= 1.3 THEN 'GT130'
                        WHEN RATIO > 1 THEN 'GT100'
                        ELSE 'LT100' END RATIO_LEVEL,
                    "VALUE" EXCEED_VALUE,
                    PREDICTED_VALUE
             from (
                      SELECT NVL(fcst.MATERIAL, sales.MATERIAL) MATERIAL,
                             DECODE(NVL(fcst.FCST_QTY, 0), 0, 9.9, NVL(sales.SALES_QTY, 0) / fcst.FCST_QTY) RATIO,
                             NVL(sales.SALES_VALUE, 0) - NVL(fcst.FCST_VALUE, 0) "VALUE",
                             CASE
                                 WHEN NVL(fcst.FCST_QTY, 0) > NVL(sales.SALES_QTY, 0) THEN NVL(sales.SALES_VALUE, 0)
                                 ELSE NVL(fcst.FCST_VALUE, 0) END               PREDICTED_VALUE

                      from (SELECT t.MATERIAL,
                                   SUM((${fcst_month_columns}))                 FCST_QTY,
                                   SUM((${fcst_month_columns}) ${fcstColumn})   FCST_VALUE
                              FROM SIOP_FCST_MONTH_V t
                             WHERE t.FCST_VERSION = #{fcstVersion, jdbcType=VARCHAR}
                               AND t.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                            <include refid="minisiop_filter"/>
                            GROUP BY t.MATERIAL) fcst
                               FULL JOIN
                           (
                               SELECT t.MATERIAL,
                                      SUM(ORDER_QUANTITY) SALES_QTY,
                                      SUM(${salesColumn}) SALES_VALUE
                                 FROM SIOP_ORDER_INTAKE_MONTH_V t
                                WHERE t.CALENDAR_MONTH BETWEEN GREATEST(#{fcstVersion, jdbcType=VARCHAR}, #{dateRange[0], jdbcType=VARCHAR}) AND #{dateRange[1], jdbcType=VARCHAR}
                                  AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                               <include refid="minisiop_filter"/>
                               GROUP BY t.MATERIAL
                           ) sales
                           ON sales.MATERIAL = fcst.MATERIAL) mm
        )
    </sql>

    <sql id="queryDetails_STDOI_ExFcst_Base">
        <include refid="queryDetails_STDOI_Base"/>
        SELECT <include refid="columnNames"/>,
               T.ORDER_QUANTITY,
               T.NET_NET_VALUE,
               T.MOVING_AVERAGE_P,
               T.UNIT_COST,
               T2.EXCEED_VALUE
          FROM SIOP_ORDER_INTAKE_MONTH_V T INNER JOIN DETAILS_TEMP T2 ON T.MATERIAL = T2.MATERIAL
         WHERE t.CALENDAR_MONTH BETWEEN GREATEST(#{fcstVersion, jdbcType=VARCHAR}, #{dateRange[0], jdbcType=VARCHAR}) AND #{dateRange[1], jdbcType=VARCHAR}
          AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
        <include refid="minisiop_filter"/>
    </sql>

    <sql id="queryDetails_STDOI_ExFcstGT100">
        <include refid="queryDetails_STDOI_ExFcst_Base"/>
        AND RATIO_LEVEL = 'GT200'
    </sql>

    <sql id="queryDetails_STDOI_ExFcstGT70">
        <include refid="queryDetails_STDOI_ExFcst_Base"/>
        AND RATIO_LEVEL = 'GT170'
    </sql>

    <sql id="queryDetails_STDOI_ExFcstGT30">
        <include refid="queryDetails_STDOI_ExFcst_Base"/>
        AND RATIO_LEVEL = 'GT130'
    </sql>

    <sql id="queryDetails_STDOI_ExFcstLT30">
        <include refid="queryDetails_STDOI_ExFcst_Base"/>
        AND RATIO_LEVEL = 'GT100'
    </sql>

    <sql id="queryDetails_STDOI_IGOI">
       SELECT <include refid="columnNames"/>,
              T.ORDER_QUANTITY,
              T.NET_NET_VALUE,
              T.MOVING_AVERAGE_P,
              T.UNIT_COST
         FROM SIOP_ORDER_INTAKE_MONTH_V t
        WHERE t.SE_SCOPE IN ('SECI_IG', 'SEHK_IG')
        AND t.CALENDAR_MONTH BETWEEN #{dateRange[0], jdbcType=VARCHAR} AND #{dateRange[1], jdbcType=VARCHAR}
        <include refid="minisiop_filter"/>
    </sql>

    <sql id="queryDetails_STDOI_PredictedOI">
       <include refid="queryDetails_STDOI_Base"/>
       SELECT <include refid="columnNames"/>,
              T.ORDER_QUANTITY,
              T.NET_NET_VALUE,
              T.MOVING_AVERAGE_P,
              T.UNIT_COST,
              T2.PREDICTED_VALUE
         FROM SIOP_ORDER_INTAKE_MONTH_V T INNER JOIN DETAILS_TEMP T2 ON T.MATERIAL = T2.MATERIAL
        WHERE t.CALENDAR_MONTH BETWEEN GREATEST(#{fcstVersion, jdbcType=VARCHAR}, #{dateRange[0], jdbcType=VARCHAR}) AND #{dateRange[1], jdbcType=VARCHAR}
              AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
              <include refid="minisiop_filter"/>
    </sql>

    <sql id="queryDetails_STDOI_ActualOIBeforeSP">
        SELECT <include refid="columnNames"/>,
               T.ORDER_QUANTITY,
               T.NET_NET_VALUE,
               T.MOVING_AVERAGE_P,
               T.UNIT_COST
          FROM SIOP_ORDER_INTAKE_MONTH_V t
         WHERE t.CALENDAR_MONTH >= #{dateRange[0], jdbcType=VARCHAR}
           AND t.CALENDAR_MONTH &lt; #{fcstVersion, jdbcType=VARCHAR}
           AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
        <include refid="minisiop_filter"/>
    </sql>

    <!-- STD CRD -->

    <select id="querySummary_STDCRD" resultType="java.util.Map">
        SELECT RATIO_LEVEL,
               SUM(VALUE)               "VALUE",
               SUM(PREDICTED_VALUE)     PREDICTED_VALUE
        FROM (
                 SELECT CASE
                            WHEN RATIO > 2 THEN 'GT200'
                            WHEN RATIO >= 1.7 THEN 'GT170'
                            WHEN RATIO >= 1.3 THEN 'GT130'
                            WHEN RATIO > 1 THEN 'GT100'
                            ELSE 'LT100' END RATIO_LEVEL,
                        "VALUE",
                        PREDICTED_VALUE
                 FROM (
                          SELECT DECODE(NVL(fcst.FCST_QTY, 0), 0, 9.9, NVL(sales.SALES_QTY, 0) / fcst.FCST_QTY) RATIO,
                                 NVL(sales.SALES_VALUE, 0) - NVL(fcst.FCST_VALUE, 0) "VALUE",
                                 CASE
                                     WHEN NVL(fcst.FCST_QTY, 0) > NVL(sales.SALES_QTY, 0) THEN NVL(sales.SALES_VALUE, 0)
                                     ELSE NVL(fcst.FCST_VALUE, 0) END               PREDICTED_VALUE

                          FROM (SELECT t.MATERIAL,
                                       SUM((${fcst_month_columns}))                 FCST_QTY,
                                       SUM((${fcst_month_columns}) ${fcstColumn})   FCST_VALUE
                                  FROM SIOP_FCST_MONTH_V t
                                 WHERE t.FCST_VERSION = #{fcstVersion, jdbcType=VARCHAR}
                                   AND t.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                                <include refid="minisiop_filter"/>
                                GROUP BY t.MATERIAL) fcst
                                   FULL JOIN
                               (
                                   SELECT t.MATERIAL,
                                          SUM(ORDER_QUANTITY) SALES_QTY,
                                          SUM(${salesColumn}) SALES_VALUE
                                     FROM SIOP_CRD_MONTH_V t
                                    WHERE t.CALENDAR_MONTH BETWEEN GREATEST(#{fcstVersion, jdbcType=VARCHAR}, #{dateRange[0], jdbcType=VARCHAR}) AND #{dateRange[1], jdbcType=VARCHAR}
                                      AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                                   <include refid="minisiop_filter"/>
                                   GROUP BY t.MATERIAL
                               ) sales
                               ON sales.MATERIAL = fcst.MATERIAL) mm) nn
        GROUP BY RATIO_LEVEL
    </select>

    <select id="querySummary_STDCRD_IGCRD" resultType="java.math.BigDecimal">
        SELECT SUM(${salesColumn})
          FROM SIOP_CRD_MONTH_V t
         WHERE t.SE_SCOPE IN ('SECI_IG', 'SEHK_IG')
           and t.CALENDAR_MONTH BETWEEN #{dateRange[0], jdbcType=VARCHAR} AND #{dateRange[1], jdbcType=VARCHAR}
        <include refid="minisiop_filter"/>
    </select>

    <select id="querySummary_STDCRD_ActualCRDBeforeSF" resultType="java.math.BigDecimal">
        SELECT SUM(${salesColumn}) OI_VALUE
          FROM SIOP_CRD_MONTH_V t
         WHERE t.CALENDAR_MONTH >= #{dateRange[0], jdbcType=VARCHAR}
           AND t.CALENDAR_MONTH &lt; #{fcstVersion, jdbcType=VARCHAR}
           AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
        <include refid="minisiop_filter"/>
    </select>

    <sql id="queryDetails_STDCRD_Base">
        WITH DETAILS_TEMP AS (
             SELECT MATERIAL,
                    CASE
                    WHEN RATIO > 2 THEN 'GT200'
                    WHEN RATIO >= 1.7 THEN 'GT170'
                    WHEN RATIO >= 1.3 THEN 'GT130'
                    WHEN RATIO > 1 THEN 'GT100'
                    ELSE 'LT100' END RATIO_LEVEL,
                "VALUE" EXCEED_VALUE,
                PREDICTED_VALUE
         FROM (
                  SELECT NVL(fcst.MATERIAL, sales.MATERIAL) MATERIAL,
                         DECODE(NVL(fcst.FCST_QTY, 0), 0, 9.9, NVL(sales.SALES_QTY, 0) / fcst.FCST_QTY) RATIO,
                         NVL(sales.SALES_VALUE, 0) - NVL(fcst.FCST_VALUE, 0) "VALUE",
                         CASE
                             WHEN NVL(fcst.FCST_QTY, 0) > NVL(sales.SALES_QTY, 0) THEN NVL(sales.SALES_VALUE, 0)
                             ELSE NVL(fcst.FCST_VALUE, 0) END               PREDICTED_VALUE

                  FROM (SELECT t.MATERIAL,
                               SUM((${fcst_month_columns}))                 FCST_QTY,
                               SUM((${fcst_month_columns}) ${fcstColumn})   FCST_VALUE
                          FROM SIOP_FCST_MONTH_V t
                         WHERE t.FCST_VERSION = #{fcstVersion, jdbcType=VARCHAR}
                           AND t.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                        <include refid="minisiop_filter"/>
                        GROUP BY t.MATERIAL) fcst
                           FULL JOIN
                       (
                           SELECT t.MATERIAL,
                                  SUM(ORDER_QUANTITY) SALES_QTY,
                                  SUM(${salesColumn}) SALES_VALUE
                             FROM SIOP_CRD_MONTH_V t
                            WHERE t.CALENDAR_MONTH BETWEEN GREATEST(#{fcstVersion, jdbcType=VARCHAR}, #{dateRange[0], jdbcType=VARCHAR}) AND #{dateRange[1], jdbcType=VARCHAR}
                              AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                           <include refid="minisiop_filter"/>
                           GROUP BY t.MATERIAL
                       ) sales
                       ON sales.MATERIAL = fcst.MATERIAL) mm
        )
    </sql>

    <sql id="queryDetails_STDCRD_ExFcst_Base">
        <include refid="queryDetails_STDCRD_Base"/>
        SELECT <include refid="columnNames"/>,
               T.ORDER_QUANTITY,
               T.NET_NET_VALUE,
               T.MOVING_AVERAGE_P,
               T.UNIT_COST,
               T2.EXCEED_VALUE
         FROM SIOP_CRD_MONTH_V T INNER JOIN DETAILS_TEMP T2 ON T.MATERIAL = T2.MATERIAL
        WHERE t.CALENDAR_MONTH BETWEEN GREATEST(#{fcstVersion, jdbcType=VARCHAR}, #{dateRange[0], jdbcType=VARCHAR}) AND #{dateRange[1], jdbcType=VARCHAR}
          AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
        <include refid="minisiop_filter"/>
    </sql>

    <sql id="queryDetails_STDCRD_ExFcstGT100">
        <include refid="queryDetails_STDCRD_ExFcst_Base"/>
        AND RATIO_LEVEL = 'GT200'
    </sql>

    <sql id="queryDetails_STDCRD_ExFcstGT70">
        <include refid="queryDetails_STDCRD_ExFcst_Base"/>
        AND RATIO_LEVEL = 'GT170'
    </sql>

    <sql id="queryDetails_STDCRD_ExFcstGT30">
        <include refid="queryDetails_STDCRD_ExFcst_Base"/>
        AND RATIO_LEVEL = 'GT130'
    </sql>

    <sql id="queryDetails_STDCRD_ExFcstLT30">
        <include refid="queryDetails_STDCRD_ExFcst_Base"/>
        AND RATIO_LEVEL = 'GT100'
    </sql>

    <sql id="queryDetails_STDCRD_IGCRD">
        SELECT <include refid="columnNames"/>,
               T.ORDER_QUANTITY,
               T.NET_NET_VALUE,
               T.MOVING_AVERAGE_P,
               T.UNIT_COST
          FROM SIOP_CRD_MONTH_V t
         WHERE t.SE_SCOPE IN ('SECI_IG', 'SEHK_IG')
           and t.CALENDAR_MONTH BETWEEN #{dateRange[0], jdbcType=VARCHAR} AND #{dateRange[1], jdbcType=VARCHAR}
        <include refid="minisiop_filter"/>
    </sql>

    <sql id="queryDetails_STDCRD_PredictedOI">
       <include refid="queryDetails_STDCRD_Base"/>
       SELECT <include refid="columnNames"/>,
              T.ORDER_QUANTITY,
              T.NET_NET_VALUE,
              T.MOVING_AVERAGE_P,
              T.UNIT_COST,
              T2.PREDICTED_VALUE
         FROM SIOP_CRD_MONTH_V T INNER JOIN DETAILS_TEMP T2 ON T.MATERIAL = T2.MATERIAL
        WHERE t.CALENDAR_MONTH BETWEEN GREATEST(#{fcstVersion, jdbcType=VARCHAR}, #{dateRange[0], jdbcType=VARCHAR}) AND #{dateRange[1], jdbcType=VARCHAR}
              AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
              <include refid="minisiop_filter"/>
    </sql>

    <sql id="queryDetails_STDCRD_ActualCRDBeforeSP">
        SELECT <include refid="columnNames"/>,
               T.ORDER_QUANTITY,
               T.NET_NET_VALUE,
               T.MOVING_AVERAGE_P,
               T.UNIT_COST
          FROM SIOP_CRD_MONTH_V t
         WHERE t.CALENDAR_MONTH >= #{dateRange[0], jdbcType=VARCHAR}
           AND t.CALENDAR_MONTH &lt; #{fcstVersion, jdbcType=VARCHAR}
           AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
        <include refid="minisiop_filter"/>
    </sql>

    <!-- SOH Demand -->

    <sql id="querySummary_SOHDemand_Base">
        WITH UNREALIZED_FCST AS (
            SELECT
                NVL(fcst.MATERIAL, sales.MATERIAL)                  "MATERIAL",
                NVL(fcst.FCST_QTY, 0) - NVL(sales.SALES_QTY, 0)     "QTY",
                NVL(fcst.FCST_VALUE, 0) - NVL(sales.SALES_VALUE, 0) "VALUE"
            FROM (
                SELECT t.MATERIAL,
                       SUM(${fcst_month_columns})                  FCST_QTY,
                       SUM((${fcst_month_columns}) ${fcstColumn})  FCST_VALUE
                  FROM SIOP_FCST_MONTH_V t
                 WHERE t.FCST_VERSION = #{fcstVersion, jdbcType=VARCHAR}
                   AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                   <include refid="minisiop_filter"/>
                GROUP BY t.MATERIAL
            ) fcst

            FULL JOIN

            (
                SELECT t.MATERIAL,
                       SUM(t.ORDER_QUANTITY) SALES_QTY,
                       SUM(${salesColumn})   SALES_VALUE
                       FROM SIOP_SALES_MONTH_V t
                       WHERE t.CALENDAR_MONTH BETWEEN GREATEST(#{fcstVersion, jdbcType=VARCHAR}, #{dateRange[0], jdbcType=VARCHAR}) AND #{dateRange[1], jdbcType=VARCHAR}
                         AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                         <include refid="minisiop_filter"/>
                       GROUP BY t.MATERIAL
            ) sales ON sales.MATERIAL = fcst.MATERIAL
            WHERE NVL(sales.SALES_QTY, 0) &lt; NVL(fcst.FCST_QTY, 0)
        ),
        OPEN_CRD_IN_SP AS (
            SELECT t.MATERIAL,
                   SUM(t.OPEN_SO_QTY) OPEN_SO_QTY,
                   SUM(${crdColumn}) OPEN_SO_VALUE
              FROM SIOP_OPEN_SO_MONTH_V t
             WHERE t.CALENDAR_MONTH &lt;= #{dateRange[1], jdbcType=VARCHAR}
               AND t.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
               <include refid="minisiop_filter"/>
             GROUP BY t.MATERIAL
        ),
        DEMAND_OG AS (
            SELECT  MATERIAL,
                    MAX(QTY) "QTY",
                    MAX(VALUE) "VALUE"
              FROM (
                    SELECT NVL(T.MATERIAL, T2.MATERIAL)                    MATERIAL,
                           GREATEST(NVL(T.QTY, 0), NVL(T2.OPEN_SO_QTY, 0)) "QTY",
                           CASE
                           WHEN NVL(T.QTY, 0) > NVL(T2.OPEN_SO_QTY, 0) THEN NVL(T.VALUE, 0)
                           ELSE NVL(T2.OPEN_SO_VALUE, 0) END           "VALUE"
                      FROM UNREALIZED_FCST T FULL JOIN OPEN_CRD_IN_SP T2 ON T.MATERIAL = T2.MATERIAL
                    ) MM
             GROUP BY MATERIAL
        ),
        DEMAND_UD AS (
            SELECT  /*+ materialize */
                    t.MATERIAL,
                    NVL(AVG(${priceColumn}), 0) UD_PRICE,
                    NVL(SUM(CASE WHEN t.SE_SCOPE IN ('SECI_IG', 'SEHK_IG') THEN UD_LT_7_CD  END), 0)            IG_LT_7_QTY,
                    NVL(SUM(CASE WHEN t.SE_SCOPE IN ('SECI_OG', 'SEHK_OG') THEN UD_LT_7_CD  END), 0)            OG_LT_7_QTY,
                    NVL(SUM(CASE WHEN t.SE_SCOPE IN ('SECI_IG', 'SEHK_IG') THEN UD_LT_30_CD  END), 0)           IG_LT_30_QTY,
                    NVL(SUM(CASE WHEN t.SE_SCOPE IN ('SECI_OG', 'SEHK_OG') THEN UD_LT_30_CD  END), 0)           OG_LT_30_QTY,
                    NVL(SUM(CASE WHEN t.SE_SCOPE IN ('SECI_IG', 'SEHK_IG') THEN UD_GT_30_CD  END), 0)           IG_GT_30_QTY,
                    NVL(SUM(CASE WHEN t.SE_SCOPE IN ('SECI_OG', 'SEHK_OG') THEN UD_GT_30_CD  END), 0)           OG_GT_30_QTY
              FROM  SIOP_OPEN_SO_MONTH_V t
             WHERE  t.SE_SCOPE IN ('SECI_OG', 'SECI_IG')
             <include refid="minisiop_filter"/>
             GROUP BY t.MATERIAL
        ),
        SOH_SUPPLY AS (
            SELECT t.MATERIAL,
                   SUM(t.ORDER_QUANTITY) SOH_QTY,
                   AVG(${priceColumn})   PRICE
              FROM DEMAND_SOH_V t
             WHERE 1 = 1
             <include refid="minisiop_filter2"/>
             GROUP BY t.MATERIAL
        ),
        SOH_FOR_UD_LT_7CD AS (
            SELECT MM.*,
                   NVL(SOH_QTY,0) - NVL(SOH_COVER_OG_QTY,0) - NVL(SOH_COVER_IG_QTY,0)  SOH_REMAIN_QTY
              FROM (
                SELECT NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)   MATERIAL,
                       NVL(SUPPLY.SOH_QTY, 0)                   SOH_QTY,
                       NVL(SUPPLY.PRICE, 0)                     PRICE,
                       NVL(DEMANDS.UD_PRICE, SUPPLY.PRICE)      UD_PRICE,

                       CASE WHEN NVL(SUPPLY.SOH_QTY, 0) >= NVL(DEMANDS.OG_LT_7_QTY, 0) THEN NVL(DEMANDS.OG_LT_7_QTY, 0)
                            ELSE NVL(SUPPLY.SOH_QTY, 0)
                       END AS SOH_COVER_OG_QTY,

                       CASE WHEN NVL(SUPPLY.SOH_QTY, 0) &lt; NVL(DEMANDS.OG_LT_7_QTY, 0) THEN 0
                            WHEN (NVL(SUPPLY.SOH_QTY, 0) - NVL(DEMANDS.OG_LT_7_QTY, 0)) >= NVL(DEMANDS.IG_LT_7_QTY, 0) THEN NVL(DEMANDS.IG_LT_7_QTY, 0)
                            ELSE (NVL(SUPPLY.SOH_QTY, 0) - NVL(DEMANDS.OG_LT_7_QTY, 0))
                       END AS SOH_COVER_IG_QTY

                 FROM SOH_SUPPLY SUPPLY FULL JOIN DEMAND_UD DEMANDS ON SUPPLY.MATERIAL = DEMANDS.MATERIAL
            ) MM
        ),
        SOH_FOR_UD_LT_30CD AS (
             SELECT MM.*,
                    NVL(SOH_QTY,0) - NVL(SOH_COVER_OG_QTY, 0) - NVL(SOH_COVER_IG_QTY, 0)  SOH_REMAIN_QTY,
                    IG_UD_QTY0 + SOH_COVER_IG_QTY                                         IG_UD_QTY
               FROM (
                        SELECT NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)   MATERIAL,
                               NVL(SUPPLY.SOH_REMAIN_QTY, 0)            SOH_QTY,
                               NVL(SUPPLY.PRICE, 0)                     PRICE,
                               NVL(DEMANDS.UD_PRICE, SUPPLY.PRICE)      UD_PRICE,
                               NVL(SUPPLY.SOH_COVER_IG_QTY, 0)          IG_UD_QTY0,

                               CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.OG_LT_30_QTY, 0) THEN NVL(DEMANDS.OG_LT_30_QTY, 0)
                                    ELSE NVL(SUPPLY.SOH_REMAIN_QTY, 0)
                                END AS SOH_COVER_OG_QTY,

                               CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) &lt; NVL(DEMANDS.OG_LT_30_QTY, 0) THEN 0
                                    WHEN (NVL(SUPPLY.SOH_REMAIN_QTY, 0) - NVL(DEMANDS.OG_LT_30_QTY, 0)) >= NVL(DEMANDS.IG_LT_30_QTY, 0) THEN NVL(DEMANDS.IG_LT_30_QTY, 0)
                                    ELSE (NVL(SUPPLY.SOH_REMAIN_QTY, 0) - NVL(DEMANDS.OG_LT_30_QTY, 0))
                                END AS SOH_COVER_IG_QTY

                        FROM SOH_FOR_UD_LT_7CD SUPPLY FULL JOIN DEMAND_UD DEMANDS ON SUPPLY.MATERIAL = DEMANDS.MATERIAL
            ) MM
        ),
        SOH_FOR_UD_GT_30CD AS (
            SELECT  MM.*,
                    NVL(SOH_QTY,0) - NVL(SOH_COVER_OG_QTY,0) - NVL(SOH_COVER_IG_QTY,0)  SOH_REMAIN_QTY,
                    IG_UD_QTY0 + SOH_COVER_IG_QTY                                       IG_UD_QTY
              FROM (
                SELECT  NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)   MATERIAL,
                        NVL(SUPPLY.SOH_REMAIN_QTY, 0)            SOH_QTY,
                        NVL(SUPPLY.PRICE, 0)                     PRICE,
                        NVL(DEMANDS.UD_PRICE, SUPPLY.PRICE)      UD_PRICE,
                        NVL(SUPPLY.IG_UD_QTY, 0)                 IG_UD_QTY0,

                        CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.OG_GT_30_QTY, 0) THEN NVL(DEMANDS.OG_GT_30_QTY, 0)
                             ELSE NVL(SUPPLY.SOH_REMAIN_QTY, 0)
                        END AS SOH_COVER_OG_QTY,

                        CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) &lt; NVL(DEMANDS.OG_GT_30_QTY, 0) THEN 0
                             WHEN (NVL(SUPPLY.SOH_REMAIN_QTY, 0) - NVL(DEMANDS.OG_GT_30_QTY, 0)) >= NVL(DEMANDS.IG_GT_30_QTY, 0) THEN NVL(DEMANDS.IG_GT_30_QTY, 0)
                             ELSE (NVL(SUPPLY.SOH_REMAIN_QTY, 0) - NVL(DEMANDS.OG_GT_30_QTY, 0))
                        END AS SOH_COVER_IG_QTY

                 FROM SOH_FOR_UD_LT_30CD SUPPLY FULL JOIN DEMAND_UD DEMANDS ON SUPPLY.MATERIAL = DEMANDS.MATERIAL
            ) MM
        ),
        SOH_FOR_DEMAND_SP AS (
            SELECT  MM.*,
                    NVL(SOH_QTY,0) - NVL(SOH_COVER_QTY,0)   SOH_REMAIN_QTY
              FROM (
                SELECT  NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)                   MATERIAL,
                        NVL(SUPPLY.SOH_REMAIN_QTY, 0)                            SOH_QTY,
                        DECODE(DEMANDS.QTY, 0, 0, DEMANDS.VALUE / DEMANDS.QTY)   DEMAND_PRICE,

                        CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN NVL(DEMANDS.QTY, 0)
                        ELSE NVL(SUPPLY.SOH_REMAIN_QTY, 0) END AS SOH_COVER_QTY

                  FROM SOH_FOR_UD_GT_30CD SUPPLY FULL JOIN DEMAND_OG DEMANDS ON SUPPLY.MATERIAL = DEMANDS.MATERIAL
            ) MM
        ),
        IG_ORDER_DEMAND AS (
            SELECT  MATERIAL,
                    SUM(t.OPEN_SO_QTY)   QTY,
                    AVG(${priceColumn})  ORDER_PRICE
              FROM SIOP_OPEN_SO_MONTH_V t
             WHERE t.SE_SCOPE IN ('SECI_IG', 'SEHK_IG')
             <include refid="minisiop_filter"/>
             GROUP BY t.MATERIAL
        ),
        SOH_FOR_IG_ORDER AS (
            SELECT  MM.*,
                    NVL(SOH_QTY,0) - NVL(SOH_COVER_QTY,0)   SOH_REMAIN_QTY
            FROM (
                SELECT  NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)   MATERIAL,
                        NVL(SUPPLY.SOH_REMAIN_QTY, 0)            SOH_QTY,
                        NVL(DEMANDS.ORDER_PRICE, 0)              ORDER_PRICE,

                        CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN NVL(DEMANDS.QTY, 0)
                             ELSE NVL(SUPPLY.SOH_REMAIN_QTY, 0) END AS SOH_COVER_QTY

                  FROM SOH_FOR_DEMAND_SP SUPPLY FULL JOIN IG_ORDER_DEMAND DEMANDS ON SUPPLY.MATERIAL = DEMANDS.MATERIAL
            ) MM
        ),
        SS_DEMAND AS (
            SELECT  t.MATERIAL,
                    SUM(t.SAFETY_STOCK)   QTY,
                    AVG(${priceColumn}) PRICE
              FROM  DEMAND_SOH_V t
             WHERE 1 = 1
             <include refid="minisiop_filter2"/>
             GROUP BY t.MATERIAL
        ),
        SOH_FOR_SS AS (
            SELECT  MM.*,
                    NVL(SOH_QTY,0) - NVL(SOH_COVER_QTY,0)   SOH_REMAIN_QTY
              FROM (
                    SELECT  NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)   MATERIAL,
                            NVL(SUPPLY.SOH_REMAIN_QTY, 0)            SOH_QTY,
                            NVL(DEMANDS.PRICE, 0)                    DEMAND_PRICE,

                            CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN NVL(DEMANDS.QTY, 0)
                                 ELSE NVL(SUPPLY.SOH_REMAIN_QTY, 0) END AS SOH_COVER_QTY

                      FROM SOH_FOR_IG_ORDER SUPPLY FULL JOIN SS_DEMAND DEMANDS ON SUPPLY.MATERIAL = DEMANDS.MATERIAL
            ) MM
        ),
        AGING_DEMAND AS (
            SELECT  t.MATERIAL,
                    SUM(t.STOCK_1Y_2Y)  AGING_1Y_2Y_QTY,
                    SUM(t.STOCK_GT_2Y)  AGING_GT_2Y_QTY,
                    AVG(${priceColumn}) PRICE
              FROM DEMAND_SOH_V t
             WHERE 1 = 1
             <include refid="minisiop_filter2"/>
             GROUP BY t.MATERIAL
        ),
        SOH_FOR_AGING AS (
            SELECT  MM.*,
                    NVL(SOH_QTY,0) - NVL(SOH_COVER_AGING1_QTY,0) - NVL(SOH_COVER_AGING2_QTY,0)  SOH_REMAIN_QTY
            FROM (
                SELECT  NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)   MATERIAL,
                        NVL(SUPPLY.SOH_REMAIN_QTY, 0)            SOH_QTY,
                        NVL(DEMANDS.PRICE, 0)                    DEMAND_PRICE,

                        CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.AGING_1Y_2Y_QTY, 0) THEN NVL(AGING_1Y_2Y_QTY, 0)
                             ELSE NVL(SUPPLY.SOH_REMAIN_QTY, 0)
                        END AS SOH_COVER_AGING1_QTY,

                        CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) &lt; NVL(DEMANDS.AGING_1Y_2Y_QTY, 0) THEN 0
                             WHEN (NVL(SUPPLY.SOH_REMAIN_QTY, 0) - NVL(DEMANDS.AGING_1Y_2Y_QTY, 0)) >= NVL(DEMANDS.AGING_GT_2Y_QTY, 0) THEN NVL(DEMANDS.AGING_GT_2Y_QTY, 0)
                             ELSE (NVL(SUPPLY.SOH_REMAIN_QTY, 0) - NVL(DEMANDS.AGING_1Y_2Y_QTY, 0))
                        END AS SOH_COVER_AGING2_QTY

                FROM SOH_FOR_SS SUPPLY FULL JOIN AGING_DEMAND DEMANDS ON SUPPLY.MATERIAL = DEMANDS.MATERIAL
            ) MM
        )
    </sql>

    <select id="querySummary_SOHDemand" resultType="java.util.Map">
        <include refid="querySummary_SOHDemand_Base"/>

        SELECT 'OG_UD_LT_7_CD' AS "TYPE", SUM(SOH_COVER_OG_QTY * UD_PRICE) AS "VALUE" FROM SOH_FOR_UD_LT_7CD

        UNION ALL

        SELECT 'OG_UD_LT_30_CD', SUM(SOH_COVER_OG_QTY * UD_PRICE) FROM SOH_FOR_UD_LT_30CD

        UNION ALL

        SELECT 'OG_UD_GT_30_CD', SUM(SOH_COVER_OG_QTY * UD_PRICE) FROM SOH_FOR_UD_GT_30CD

        UNION ALL

        SELECT 'IG_UD', SUM(IG_UD_QTY * UD_PRICE) FROM SOH_FOR_UD_GT_30CD

        UNION ALL

        SELECT 'FOR_DEMAND_IN_SELECTED_PERIOD', SUM(SOH_COVER_QTY * DEMAND_PRICE) FROM SOH_FOR_DEMAND_SP

        UNION ALL

        SELECT 'FOR_IG_ORDER_IN_SELECTED_PERIOD', SUM(SOH_COVER_QTY * ORDER_PRICE) FROM SOH_FOR_IG_ORDER

        UNION ALL

        SELECT 'FOR_SAFETY_STOCK', SUM(SOH_COVER_QTY * DEMAND_PRICE) FROM SOH_FOR_SS

        UNION ALL

        SELECT 'STOCK_AGING_GT_1Y', SUM(SOH_COVER_AGING1_QTY * DEMAND_PRICE) FROM SOH_FOR_AGING

        UNION ALL

        SELECT 'STOCK_AGING_GT_2Y', SUM(SOH_COVER_AGING2_QTY * DEMAND_PRICE) FROM SOH_FOR_AGING

        UNION ALL

        SELECT 'FOR_FUTURE_DEMAND', SUM(SOH_REMAIN_QTY * DEMAND_PRICE) FROM SOH_FOR_AGING
    </select>

    <sql id="queryDetails_SOHDemand_Base">
        <include refid="querySummary_SOHDemand_Base"/>
        SELECT <include refid="columnNames"/>,
               T.AVG_SELLING_PRICE_RMB,
               T.UNIT_COST,
               T.SAFETY_STOCK,
               T.ORDER_QUANTITY,
               T.NET_NET_VALUE,
               T.MOVING_AVERAGE_P,
    </sql>

    <sql id="queryDetails_SOHDemand_UDLT7">
         <include refid="queryDetails_SOHDemand_Base"/>
               T2.UD_PRICE,
               T2.SOH_COVER_OG_QTY SOH_COVER_OG_UD_QTY
          FROM DEMAND_SOH_V T
         INNER JOIN SOH_FOR_UD_LT_7CD T2 ON T.MATERIAL = T2.MATERIAL
         WHERE T2.SOH_COVER_OG_QTY > 0
    </sql>

    <sql id="queryDetails_SOHDemand_UDGT7">
         <include refid="queryDetails_SOHDemand_Base"/>
               T2.UD_PRICE,
               T2.SOH_COVER_OG_QTY SOH_COVER_OG_UD_QTY
          FROM DEMAND_SOH_V T
         INNER JOIN SOH_FOR_UD_LT_30CD T2 ON T.MATERIAL = T2.MATERIAL
         WHERE T2.SOH_COVER_OG_QTY > 0
    </sql>

    <sql id="queryDetails_SOHDemand_UDGT30">
         <include refid="queryDetails_SOHDemand_Base"/>
               T2.UD_PRICE,
               T2.SOH_COVER_OG_QTY SOH_COVER_OG_UD_QTY
          FROM DEMAND_SOH_V T
         INNER JOIN SOH_FOR_UD_GT_30CD T2 ON T.MATERIAL = T2.MATERIAL
         WHERE T2.SOH_COVER_OG_QTY > 0
    </sql>

    <sql id="queryDetails_SOHDemand_IGUD">
         <include refid="queryDetails_SOHDemand_Base"/>
               T2.UD_PRICE,
               T2.IG_UD_QTY SOH_COVER_IG_UD_QTY
          FROM DEMAND_SOH_V T
         INNER JOIN SOH_FOR_UD_GT_30CD T2 ON T.MATERIAL = T2.MATERIAL
         WHERE T2.IG_UD_QTY > 0
    </sql>

    <sql id="queryDetails_SOHDemand_DemandInSP">
         <include refid="queryDetails_SOHDemand_Base"/>
               T2.DEMAND_PRICE,
               T2.SOH_COVER_QTY
          FROM DEMAND_SOH_V T
          INNER JOIN SOH_FOR_DEMAND_SP T2 ON T.MATERIAL = T2.MATERIAL
          WHERE T2.SOH_COVER_QTY > 0
    </sql>

    <sql id="queryDetails_SOHDemand_IGOrderInSP">
         <include refid="queryDetails_SOHDemand_Base"/>
               T2.ORDER_PRICE,
               T2.SOH_COVER_QTY
          FROM DEMAND_SOH_V T
          INNER JOIN SOH_FOR_IG_ORDER T2 ON T.MATERIAL = T2.MATERIAL
          WHERE T2.SOH_COVER_QTY > 0
    </sql>

    <sql id="queryDetails_SOHDemand_SafetyStock">
         <include refid="queryDetails_SOHDemand_Base"/>
               T2.DEMAND_PRICE PRICE,
               T2.SOH_COVER_QTY
          FROM DEMAND_SOH_V T
          INNER JOIN SOH_FOR_SS T2 ON T.MATERIAL = T2.MATERIAL
          WHERE T2.SOH_COVER_QTY > 0
    </sql>

    <sql id="queryDetails_SOHDemand_FutureDemand">
         <include refid="queryDetails_SOHDemand_Base"/>
               T2.DEMAND_PRICE PRICE,
               T2.SOH_REMAIN_QTY
          FROM DEMAND_SOH_V T
          INNER JOIN SOH_FOR_AGING T2 ON T.MATERIAL = T2.MATERIAL
          WHERE T2.SOH_REMAIN_QTY > 0
    </sql>

    <sql id="queryDetails_SOHDemand_AgingGT1Y">
         <include refid="queryDetails_SOHDemand_Base"/>
               T2.DEMAND_PRICE PRICE,
               T2.SOH_COVER_AGING1_QTY SOH_COVER_QTY
          FROM DEMAND_SOH_V T
          INNER JOIN SOH_FOR_AGING T2 ON T.MATERIAL = T2.MATERIAL
          WHERE T2.SOH_COVER_AGING1_QTY > 0
    </sql>

    <sql id="queryDetails_SOHDemand_AgingGT2Y">
         <include refid="queryDetails_SOHDemand_Base"/>
               T2.DEMAND_PRICE PRICE,
               T2.SOH_COVER_AGING2_QTY SOH_COVER_QTY
          FROM DEMAND_SOH_V T
          INNER JOIN SOH_FOR_AGING T2 ON T.MATERIAL = T2.MATERIAL
          WHERE T2.SOH_COVER_AGING2_QTY > 0
    </sql>

    <!-- GIT Demand -->

    <sql id="querySummary_GITDemand_Base">
        WITH UNREALIZED_FCST AS (
             SELECT
                NVL(fcst.MATERIAL, sales.MATERIAL)                  "MATERIAL",
                NVL(fcst.FCST_QTY, 0) - NVL(sales.SALES_QTY, 0)     "QTY",
                NVL(fcst.FCST_VALUE, 0) - NVL(sales.SALES_VALUE, 0) "VALUE"
             FROM (
                     SELECT t.MATERIAL,
                             SUM(${fcst_month_columns})                  FCST_QTY,
                             SUM((${fcst_month_columns}) ${fcstColumn})  FCST_VALUE
                       FROM SIOP_FCST_MONTH_V t
                      WHERE t.FCST_VERSION = #{fcstVersion, jdbcType=VARCHAR}
                        AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                        <include refid="minisiop_filter"/>
                      GROUP BY t.MATERIAL
                ) fcst

                FULL JOIN

                (
                     SELECT t.MATERIAL,
                            SUM(t.ORDER_QUANTITY) SALES_QTY,
                            SUM(${salesColumn})  SALES_VALUE
                      FROM SIOP_SALES_MONTH_V t
                     WHERE t.CALENDAR_MONTH BETWEEN GREATEST(#{fcstVersion, jdbcType=VARCHAR}, #{dateRange[0], jdbcType=VARCHAR}) AND #{dateRange[1], jdbcType=VARCHAR}
                       AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                       <include refid="minisiop_filter"/>
                     GROUP BY t.MATERIAL
                ) sales ON sales.MATERIAL = fcst.MATERIAL
             WHERE NVL(sales.SALES_QTY, 0) &lt; NVL(fcst.FCST_QTY, 0)
             ),
             TOTAL_UD AS (
                SELECT  t.MATERIAL,
                        SUM(NVL(UD_GT_30_CD, 0) + NVL(UD_LT_30_CD, 0) + NVL(UD_LT_7_CD, 0))                                  "QTY",
                        SUM(NVL(UD_GT_30_CD${UDSubfix}, 0) + NVL(UD_LT_30_CD${UDSubfix}, 0) + NVL(UD_LT_7_CD${UDSubfix}, 0)) "VALUE"
                  FROM  SIOP_OPEN_SO_MONTH_V t
                 WHERE  1 = 1
                 <include refid="minisiop_filter"/>
                 GROUP BY t.MATERIAL
             ),
             OPEN_CRD_IN_SP AS (
                SELECT MATERIAL,
                       SUM(OPEN_SO_QTY)  "QTY",
                       SUM(${crdColumn}) "VALUE"
                  FROM SIOP_OPEN_SO_MONTH_V t
                 WHERE t.CALENDAR_MONTH &lt;= #{dateRange[1], jdbcType=VARCHAR}
                   AND t.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                <include refid="minisiop_filter"/>
                 GROUP BY MATERIAL
             ),
             DEMAND_TEMP AS (
                SELECT MATERIAL,
                       MAX(QTY) "QTY",
                       MAX(VALUE) "VALUE"
                FROM (
                    SELECT COALESCE(T.MATERIAL, T2.MATERIAL, T3.MATERIAL) MATERIAL,
                           GREATEST(NVL(T.QTY, 0),NVL(T2.QTY, 0),NVL(T3.QTY, 0)) AS QTY,
                           CASE WHEN NVL(T.QTY, 0)  >= NVL(T2.QTY, 0) AND NVL(T.QTY, 0) >= NVL(T3.QTY, 0) THEN T.VALUE
                                WHEN NVL(T2.QTY, 0) >= NVL(T.QTY, 0) AND NVL(T2.QTY, 0) >= NVL(T3.QTY, 0) THEN T2.VALUE
                                WHEN NVL(T3.QTY, 0) >= NVL(T.QTY, 0) AND NVL(T3.QTY, 0) >= NVL(T2.QTY, 0) THEN T3.VALUE
                           END AS "VALUE"
                      FROM UNREALIZED_FCST T FULL JOIN TOTAL_UD T2 ON T.MATERIAL = T2.MATERIAL
                                             FULL JOIN OPEN_CRD_IN_SP T3 ON T.MATERIAL = T3.MATERIAL
                ) MM GROUP BY MATERIAL
             ),
             SUPPLY_TEMP AS (
                SELECT NVL(T.MATERIAL, T2.MATERIAL)                    MATERIAL,
                       NVL(T.ORDER_QUANTITY, 0)                        SOH_QTY,
                       NVL(T2.QUANTITY, 0)                             LA_QTY,
                       NVL(T2.AVG_SELLING_PRICE_RMB, T.AVG_SELLING_PRICE_RMB)  AVG_SELLING_PRICE_RMB,
                       NVL(T2.UNIT_COST, T.UNIT_COST)                  UNIT_COST
                  FROM (
                          SELECT T.MATERIAL,
                                 SUM(T.ORDER_QUANTITY)    ORDER_QUANTITY,
                                 AVG(T.AVG_SELLING_PRICE_RMB) AVG_SELLING_PRICE_RMB,
                                 AVG(T.UNIT_COST)         UNIT_COST
                           FROM DEMAND_SOH_V T
                          WHERE 1 = 1
                          <include refid="minisiop_filter2"/>
                          GROUP BY T.MATERIAL
                        ) T FULL JOIN
                       (SELECT MATERIAL,
                               SUM(QUANTITY)          QUANTITY,
                               AVG(AVG_SELLING_PRICE_RMB) AVG_SELLING_PRICE_RMB,
                               AVG(UNIT_COST)         UNIT_COST
                          FROM SIOP_LA_AB_MONTH_V T
                         WHERE CATEGORY IN ('LA','ZA')
                           AND CALENDAR_MONTH &lt;= #{dateRange[1], jdbcType=VARCHAR}
                           <include refid="minisiop_filter2"/>
                         GROUP BY MATERIAL) T2
                       ON T.MATERIAL = T2.MATERIAL
             ),
             LA_FOR_DEMAND_IN_SP AS (
                SELECT MM.*,
                       NVL(SOH_QTY,0) - NVL(SOH_COVER_QTY,0)   SOH_REMAIN_QTY,
                       NVL(LA_QTY,0) - NVL(LA_COVER_QTY,0)     LA_REMAIN_QTY
                FROM (
                    SELECT NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)                   MATERIAL,
                           NVL(SUPPLY.SOH_QTY, 0)                                   SOH_QTY,
                           NVL(SUPPLY.LA_QTY, 0)                                    LA_QTY,
                           NVL(${priceColumn}, 0)                                   PRICE,
                           DECODE(DEMANDS.QTY, 0, 0, DEMANDS.VALUE / DEMANDS.QTY)   DEMAND_PRICE,

                           CASE WHEN NVL(SUPPLY.SOH_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN 0
                                WHEN (NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_QTY, 0)) >= NVL(SUPPLY.LA_QTY, 0) THEN NVL(SUPPLY.LA_QTY, 0)
                                WHEN (NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_QTY, 0)) &lt; NVL(SUPPLY.LA_QTY, 0) THEN NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_QTY, 0)
                            END AS LA_COVER_QTY,

                           CASE WHEN NVL(SUPPLY.SOH_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN NVL(DEMANDS.QTY, 0)
                                ELSE NVL(SUPPLY.SOH_QTY, 0) END AS SOH_COVER_QTY

                      FROM SUPPLY_TEMP SUPPLY FULL JOIN DEMAND_TEMP DEMANDS ON SUPPLY.MATERIAL = DEMANDS.MATERIAL
                ) MM
             ),
             OPENSO_IG_DEMAND_TEMP AS (
                 SELECT MATERIAL,
                        SUM(t.OPEN_SO_QTY - UD_GT_30_CD - UD_LT_30_CD - UD_LT_7_CD)                                 "QTY",
                        SUM(${crdColumn} - UD_GT_30_CD${UDSubfix} - UD_LT_30_CD${UDSubfix} - UD_LT_7_CD${UDSubfix}) "VALUE",
                        AVG(${priceColumn}) PRICE
                   FROM SIOP_OPEN_SO_MONTH_V t
                  WHERE t.SE_SCOPE IN ('SECI_IG', 'SEHK_IG')
                  <include refid="minisiop_filter"/>
                  GROUP BY t.MATERIAL
             ),
             LA_FOR_IG_ORDER AS (
                SELECT MM.*,
                       NVL(SOH_QTY,0) - NVL(SOH_COVER_QTY,0)   SOH_REMAIN_QTY,
                       NVL(LA_QTY,0) - NVL(LA_COVER_QTY,0)     LA_REMAIN_QTY
                  FROM (
                    SELECT NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)   MATERIAL,
                           NVL(SUPPLY.SOH_REMAIN_QTY, 0)            SOH_QTY,
                           NVL(SUPPLY.LA_REMAIN_QTY, 0)             LA_QTY,
                           NVL(SUPPLY.PRICE, DEMANDS.PRICE)         PRICE,

                           CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN 0
                                WHEN (NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_REMAIN_QTY, 0)) >= NVL(SUPPLY.LA_REMAIN_QTY, 0) THEN NVL(SUPPLY.LA_REMAIN_QTY, 0)
                                ELSE NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_REMAIN_QTY, 0)
                            END AS LA_COVER_QTY,

                           CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN NVL(DEMANDS.QTY, 0)
                                ELSE NVL(SUPPLY.SOH_REMAIN_QTY, 0) END SOH_COVER_QTY

                      FROM LA_FOR_DEMAND_IN_SP SUPPLY FULL JOIN OPENSO_IG_DEMAND_TEMP DEMANDS ON SUPPLY.MATERIAL = DEMANDS.MATERIAL
                ) MM
             ),
             SS_DEMAND AS (
                SELECT T.MATERIAL,
                       SUM(T.SAFETY_STOCK) QTY,
                       AVG(${priceColumn}) PRICE
                FROM DEMAND_SOH_V T
                where 1 = 1
                <include refid="minisiop_filter2"/>
                GROUP BY T.MATERIAL
             ),
             LA_FOR_SS AS (
                SELECT MM.*,
                       NVL(LA_QTY,0) - NVL(LA_COVER_QTY,0)     LA_REMAIN_QTY
                  FROM (
                    SELECT NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)   MATERIAL,
                           NVL(SUPPLY.LA_REMAIN_QTY, 0)             LA_QTY,
                           NVL(SUPPLY.PRICE, DEMANDS.PRICE)         PRICE,

                           CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN 0
                                WHEN (NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_REMAIN_QTY, 0)) >= NVL(SUPPLY.LA_REMAIN_QTY, 0) THEN NVL(SUPPLY.LA_REMAIN_QTY, 0)
                                ELSE NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_REMAIN_QTY, 0)
                            END AS LA_COVER_QTY

                      FROM LA_FOR_IG_ORDER SUPPLY FULL JOIN SS_DEMAND DEMANDS ON SUPPLY.MATERIAL = DEMANDS.MATERIAL
                ) MM
            )
    </sql>

    <select id="querySummary_GITDemand" resultType="java.util.Map">
        <include refid="querySummary_GITDemand_Base"/>

         SELECT 'LA_FOR_DEMAND_IN_SP' AS TYPE, SUM(LA_COVER_QTY * DEMAND_PRICE) AS "VALUE" FROM LA_FOR_DEMAND_IN_SP

         UNION ALL

         SELECT 'LA_FOR_IG_ORDER', SUM(LA_COVER_QTY * PRICE) FROM LA_FOR_IG_ORDER

         UNION ALL

         SELECT 'LA_FOR_SS', SUM(LA_COVER_QTY * PRICE) FROM LA_FOR_SS

         UNION ALL

         SELECT 'LA_FOR_FURTHER_DEMAND', SUM(LA_REMAIN_QTY * PRICE) FROM LA_FOR_SS

    </select>

    <sql id="queryDetails_GITDemand_Base">
        <include refid="querySummary_GITDemand_Base"/>
        SELECT <include refid="columnNames"/>,
               T.CATEGORY,
               T.QUANTITY,
               T.NET_NET_VALUE,
               T.MOVING_AVERAGE_P,
               T.AVG_SELLING_PRICE_RMB,
               T.UNIT_COST,
    </sql>

    <sql id="queryDetails_GITDemand_DemandInSP">
        <include refid="queryDetails_GITDemand_Base"/>
            T2.DEMAND_PRICE,
            T2.LA_COVER_QTY
        FROM SIOP_LA_AB_MONTH_V T INNER JOIN LA_FOR_DEMAND_IN_SP T2 ON T.MATERIAL = T2.MATERIAL
       WHERE T2.LA_COVER_QTY > 0
    </sql>

    <sql id="queryDetails_GITDemand_IGOrderInSP">
        <include refid="queryDetails_GITDemand_Base"/>
            T2.PRICE ORDER_PRICE,
            T2.LA_COVER_QTY
        FROM SIOP_LA_AB_MONTH_V T INNER JOIN LA_FOR_IG_ORDER T2 ON T.MATERIAL = T2.MATERIAL
        WHERE T2.LA_COVER_QTY > 0
    </sql>

    <sql id="queryDetails_GITDemand_SafetyStock">
        <include refid="queryDetails_GITDemand_Base"/>
            T2.PRICE,
            T2.LA_COVER_QTY
        FROM SIOP_LA_AB_MONTH_V T INNER JOIN LA_FOR_SS T2 ON T.MATERIAL = T2.MATERIAL
        WHERE T2.LA_COVER_QTY > 0
    </sql>

    <sql id="queryDetails_GITDemand_FutureDemand">
        <include refid="queryDetails_GITDemand_Base"/>
            T2.PRICE,
            T2.LA_REMAIN_QTY
        FROM SIOP_LA_AB_MONTH_V T INNER JOIN LA_FOR_SS T2 ON T.MATERIAL = T2.MATERIAL
        WHERE T2.LA_REMAIN_QTY > 0
    </sql>

    <!-- Conf.Reource Demand  -->

    <sql id="querySummary_ConfResDemand_Base">
        WITH UNREALIZED_FCST AS (
             SELECT
                NVL(fcst.MATERIAL, sales.MATERIAL)                  "MATERIAL",
                NVL(fcst.FCST_QTY, 0) - NVL(sales.SALES_QTY, 0)     "QTY",
                NVL(fcst.FCST_VALUE, 0) - NVL(sales.SALES_VALUE, 0) "VALUE"
             FROM (
                     SELECT t.MATERIAL,
                             SUM(${fcst_month_columns})                  FCST_QTY,
                             SUM((${fcst_month_columns}) ${fcstColumn})  FCST_VALUE
                       FROM SIOP_FCST_MONTH_V t
                      WHERE t.FCST_VERSION = #{fcstVersion, jdbcType=VARCHAR}
                        AND t.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                        <include refid="minisiop_filter"/>
                      GROUP BY t.MATERIAL
                ) fcst

                FULL JOIN

                (
                     SELECT t.MATERIAL,
                            SUM(t.ORDER_QUANTITY) SALES_QTY,
                            SUM(${salesColumn})  SALES_VALUE
                      FROM SIOP_SALES_MONTH_V t
                     WHERE t.CALENDAR_MONTH BETWEEN GREATEST(#{fcstVersion, jdbcType=VARCHAR}, #{dateRange[0], jdbcType=VARCHAR}) AND #{dateRange[1], jdbcType=VARCHAR}
                       AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                       <include refid="minisiop_filter"/>
                     GROUP BY t.MATERIAL
                ) sales ON sales.MATERIAL = fcst.MATERIAL
             WHERE NVL(sales.SALES_QTY, 0) &lt; NVL(fcst.FCST_QTY, 0)
             ),
             TOTAL_UD AS (
                SELECT  t.MATERIAL,
                        SUM(NVL(UD_GT_30_CD, 0) + NVL(UD_LT_30_CD, 0) + NVL(UD_LT_7_CD, 0))                                  "QTY",
                        SUM(NVL(UD_GT_30_CD${UDSubfix}, 0) + NVL(UD_LT_30_CD${UDSubfix}, 0) + NVL(UD_LT_7_CD${UDSubfix}, 0)) "VALUE"
                  FROM  SIOP_OPEN_SO_MONTH_V t
                 WHERE  1 = 1
                 <include refid="minisiop_filter"/>
                 GROUP BY t.MATERIAL
             ),
             OPEN_CRD_IN_SP AS (
                SELECT MATERIAL,
                       SUM(OPEN_SO_QTY)  "QTY",
                       SUM(${crdColumn}) "VALUE"
                  FROM SIOP_OPEN_SO_MONTH_V t
                 WHERE t.CALENDAR_MONTH &lt;= #{dateRange[1], jdbcType=VARCHAR}
                   AND t.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                <include refid="minisiop_filter"/>
                 GROUP BY MATERIAL
             ),
             DEMAND_TEMP AS (
                SELECT MATERIAL,
                       MAX(QTY) "QTY",
                       MAX(VALUE) "VALUE"
                FROM (
                    SELECT COALESCE(T.MATERIAL, T2.MATERIAL, T3.MATERIAL) MATERIAL,
                           GREATEST(NVL(T.QTY, 0),NVL(T2.QTY, 0),NVL(T3.QTY, 0)) AS QTY,
                           CASE WHEN NVL(T.QTY, 0)  >= NVL(T2.QTY, 0) AND NVL(T.QTY, 0) >= NVL(T3.QTY, 0) THEN T.VALUE
                                WHEN NVL(T2.QTY, 0) >= NVL(T.QTY, 0) AND NVL(T2.QTY, 0) >= NVL(T3.QTY, 0) THEN T2.VALUE
                                WHEN NVL(T3.QTY, 0) >= NVL(T.QTY, 0) AND NVL(T3.QTY, 0) >= NVL(T2.QTY, 0) THEN T3.VALUE
                           END AS "VALUE"
                      FROM UNREALIZED_FCST T FULL JOIN TOTAL_UD T2 ON T.MATERIAL = T2.MATERIAL
                                             FULL JOIN OPEN_CRD_IN_SP T3 ON T.MATERIAL = T3.MATERIAL
                ) MM GROUP BY MATERIAL
             ),
             SUPPLY_TEMP AS (
                SELECT NVL(T.MATERIAL, T2.MATERIAL)                    MATERIAL,
                       NVL(T.ORDER_QUANTITY, 0)                        SOH_QTY,
                       NVL(T2.QUANTITY, 0)                             LA_QTY,
                       NVL(T2.AVG_SELLING_PRICE_RMB, T.AVG_SELLING_PRICE_RMB)  AVG_SELLING_PRICE_RMB,
                       NVL(T2.UNIT_COST, T.UNIT_COST)                  UNIT_COST
                  FROM (
                          SELECT T.MATERIAL,
                                 SUM(T.ORDER_QUANTITY)    ORDER_QUANTITY,
                                 AVG(T.AVG_SELLING_PRICE_RMB) AVG_SELLING_PRICE_RMB,
                                 AVG(T.UNIT_COST)         UNIT_COST
                           FROM DEMAND_SOH_V T
                          WHERE 1 = 1
                          <include refid="minisiop_filter2"/>
                          GROUP BY T.MATERIAL
                        ) T FULL JOIN
                       (SELECT MATERIAL,
                               SUM(QUANTITY)          QUANTITY,
                               AVG(AVG_SELLING_PRICE_RMB) AVG_SELLING_PRICE_RMB,
                               AVG(UNIT_COST)         UNIT_COST
                          FROM SIOP_LA_AB_MONTH_V T
                         WHERE CALENDAR_MONTH &lt;= #{dateRange[1], jdbcType=VARCHAR}
                           <include refid="minisiop_filter2"/>
                         GROUP BY MATERIAL) T2
                       ON T.MATERIAL = T2.MATERIAL
             ),
             LA_FOR_DEMAND_IN_SP AS (
                SELECT MM.*,
                       NVL(SOH_QTY,0) - NVL(SOH_COVER_QTY,0)   SOH_REMAIN_QTY,
                       NVL(LA_QTY,0) - NVL(LA_COVER_QTY,0)     LA_REMAIN_QTY
                FROM (
                    SELECT NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)                   MATERIAL,
                           NVL(SUPPLY.SOH_QTY, 0)                                   SOH_QTY,
                           NVL(SUPPLY.LA_QTY, 0)                                    LA_QTY,
                           NVL(${priceColumn}, 0)                                   PRICE,
                           DECODE(DEMANDS.QTY, 0, 0, DEMANDS.VALUE / DEMANDS.QTY)   DEMAND_PRICE,

                           CASE WHEN NVL(SUPPLY.SOH_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN 0
                                WHEN (NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_QTY, 0)) >= NVL(SUPPLY.LA_QTY, 0) THEN NVL(SUPPLY.LA_QTY, 0)
                                WHEN (NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_QTY, 0)) &lt; NVL(SUPPLY.LA_QTY, 0) THEN NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_QTY, 0)
                            END AS LA_COVER_QTY,

                           CASE WHEN NVL(SUPPLY.SOH_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN NVL(DEMANDS.QTY, 0)
                                ELSE NVL(SUPPLY.SOH_QTY, 0) END AS SOH_COVER_QTY

                      FROM SUPPLY_TEMP SUPPLY FULL JOIN DEMAND_TEMP DEMANDS ON SUPPLY.MATERIAL = DEMANDS.MATERIAL
                ) MM
             ),
             OPENSO_IG_DEMAND_TEMP AS (
                 SELECT MATERIAL,
                        SUM(t.OPEN_SO_QTY - UD_GT_30_CD - UD_LT_30_CD - UD_LT_7_CD)                                 "QTY",
                        SUM(${crdColumn} - UD_GT_30_CD${UDSubfix} - UD_LT_30_CD${UDSubfix} - UD_LT_7_CD${UDSubfix}) "VALUE",
                        AVG(${priceColumn}) OPENSO_PRICE
                   FROM SIOP_OPEN_SO_MONTH_V t
                  WHERE t.SE_SCOPE IN ('SECI_IG', 'SEHK_IG')
                  <include refid="minisiop_filter"/>
                  GROUP BY t.MATERIAL
             ),
             LA_FOR_IG_ORDER AS (
                SELECT MM.*,
                       NVL(SOH_QTY,0) - NVL(SOH_COVER_QTY,0)   SOH_REMAIN_QTY,
                       NVL(LA_QTY,0) - NVL(LA_COVER_QTY,0)     LA_REMAIN_QTY
                  FROM (
                    SELECT NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)   MATERIAL,
                           NVL(SUPPLY.SOH_REMAIN_QTY, 0)            SOH_QTY,
                           NVL(SUPPLY.LA_REMAIN_QTY, 0)             LA_QTY,
                           NVL(SUPPLY.PRICE, DEMANDS.OPENSO_PRICE)  PRICE,
                           NVL(DEMANDS.OPENSO_PRICE, SUPPLY.PRICE)  OPENSO_PRICE,

                           CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN 0
                                WHEN (NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_REMAIN_QTY, 0)) >= NVL(SUPPLY.LA_REMAIN_QTY, 0) THEN NVL(SUPPLY.LA_REMAIN_QTY, 0)
                                ELSE NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_REMAIN_QTY, 0)
                            END AS LA_COVER_QTY,

                           CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN NVL(DEMANDS.QTY, 0)
                                ELSE NVL(SUPPLY.SOH_REMAIN_QTY, 0) END SOH_COVER_QTY

                      FROM LA_FOR_DEMAND_IN_SP SUPPLY FULL JOIN OPENSO_IG_DEMAND_TEMP DEMANDS ON SUPPLY.MATERIAL = DEMANDS.MATERIAL
                ) MM
             ),
             SS_DEMAND AS (
                SELECT T.MATERIAL,
                       SUM(T.SAFETY_STOCK) QTY,
                       AVG(${priceColumn}) PRICE
                FROM DEMAND_SOH_V T
                where 1 = 1
                <include refid="minisiop_filter2"/>
                GROUP BY T.MATERIAL
             ),
             LA_FOR_SS AS (
                SELECT MM.*,
                       NVL(LA_QTY,0) - NVL(LA_COVER_QTY,0)     LA_REMAIN_QTY
                  FROM (
                    SELECT NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)   MATERIAL,
                           NVL(SUPPLY.LA_REMAIN_QTY, 0)             LA_QTY,
                           NVL(SUPPLY.PRICE, DEMANDS.PRICE)         PRICE,

                           CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN 0
                                WHEN (NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_REMAIN_QTY, 0)) >= NVL(SUPPLY.LA_REMAIN_QTY, 0) THEN NVL(SUPPLY.LA_REMAIN_QTY, 0)
                                ELSE NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_REMAIN_QTY, 0)
                            END AS LA_COVER_QTY

                      FROM LA_FOR_IG_ORDER SUPPLY FULL JOIN SS_DEMAND DEMANDS ON SUPPLY.MATERIAL = DEMANDS.MATERIAL
                ) MM
            )
    </sql>

    <select id="querySummary_ConfResDemand" resultType="java.util.Map">
        <include refid="querySummary_ConfResDemand_Base"/>

         SELECT 'LA_FOR_DEMAND_IN_SP' AS TYPE, SUM(LA_COVER_QTY * DEMAND_PRICE) AS "VALUE" FROM LA_FOR_DEMAND_IN_SP

         UNION ALL

         SELECT 'LA_FOR_IG_ORDER', SUM(LA_COVER_QTY * OPENSO_PRICE) FROM LA_FOR_IG_ORDER

         UNION ALL

         SELECT 'LA_FOR_SS', SUM(LA_COVER_QTY * PRICE) FROM LA_FOR_SS

         UNION ALL

         SELECT 'LA_FOR_FURTHER_DEMAND', SUM(LA_REMAIN_QTY * PRICE) FROM LA_FOR_SS
    </select>

    <sql id="queryDetails_ConfRes_Base">
        <include refid="querySummary_ConfResDemand_Base"/>
        SELECT <include refid="columnNames"/>,
               T.CATEGORY,
               T.QUANTITY,
               T.NET_NET_VALUE,
               T.MOVING_AVERAGE_P,
               T.AVG_SELLING_PRICE_RMB,
               T.UNIT_COST,
    </sql>

    <sql id="queryDetails_ConfRes_DemandInSP">
        <include refid="queryDetails_ConfRes_Base"/>
            T2.DEMAND_PRICE,
            T2.LA_COVER_QTY
        FROM SIOP_LA_AB_MONTH_V T INNER JOIN LA_FOR_DEMAND_IN_SP T2 ON T.MATERIAL = T2.MATERIAL
        WHERE T2.LA_COVER_QTY > 0
    </sql>

    <sql id="queryDetails_ConfRes_IGOrderInSP">
        <include refid="queryDetails_ConfRes_Base"/>
            T2.ORDER_PRICE,
            T2.LA_COVER_QTY
        FROM SIOP_LA_AB_MONTH_V T INNER JOIN LA_FOR_IG_ORDER T2 ON T.MATERIAL = T2.MATERIAL
        WHERE T2.LA_COVER_QTY > 0
    </sql>

    <sql id="queryDetails_ConfRes_SafetyStock">
        <include refid="queryDetails_ConfRes_Base"/>
            T2.PRICE,
            T2.LA_COVER_QTY
        FROM SIOP_LA_AB_MONTH_V T INNER JOIN LA_FOR_SS T2 ON T.MATERIAL = T2.MATERIAL
        WHERE T2.LA_COVER_QTY > 0
    </sql>

    <sql id="queryDetails_ConfRes_FutureDemand">
        <include refid="queryDetails_ConfRes_Base"/>
            T2.PRICE,
            T2.LA_REMAIN_QTY
        FROM SIOP_LA_AB_MONTH_V T INNER JOIN LA_FOR_SS T2 ON T.MATERIAL = T2.MATERIAL
        WHERE T2.LA_REMAIN_QTY > 0
    </sql>

    <!-- Ordered Demand -->

    <select id="querySummary_OrderedDemand_BackOrder" resultType="java.math.BigDecimal">
        SELECT SUM(BACK_ORDER${backOrderSubfix})
        FROM SIOP_OPEN_SO_MONTH_V t
        WHERE T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
        <include refid="minisiop_filter"/>
    </select>

    <select id="querySummary_OrderedDemand_CRDInSP" resultType="java.math.BigDecimal">
        SELECT SUM(${crdColumn})
        FROM SIOP_OPEN_SO_MONTH_V t
        WHERE t.CALENDAR_MONTH &lt;= #{dateRange[1], jdbcType=VARCHAR}
        AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
        <include refid="minisiop_filter"/>
    </select>

    <select id="querySummary_OrderedDemand_IGCRD" resultType="java.math.BigDecimal">
        SELECT SUM(${crdColumn})
        FROM SIOP_OPEN_SO_MONTH_V t
        WHERE T.SE_SCOPE IN ('SECI_IG', 'SEHK_IG')
        <include refid="minisiop_filter"/>
    </select>

    <select id="querySummary_OrderedDemand_CRDOutSP" resultType="java.math.BigDecimal">
        SELECT SUM(${crdColumn})
        FROM SIOP_OPEN_SO_MONTH_V t
        WHERE t.CALENDAR_MONTH > #{dateRange[1], jdbcType=VARCHAR}
        AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
        <include refid="minisiop_filter"/>
    </select>

    <sql id="queryDetails_OrderedDemand_ColumnNames">
        <include refid="columnNames"/>,
        T.OPEN_SO_QTY,
        T.OPEN_SO_VALUE,
        T.OPEN_SO_MVP,
        T.BACK_ORDER_QTY,
        T.BACK_ORDER_VALUE,
        T.BACK_ORDER_MVP,
        T.OPEN_UD_QTY,
        T.OPEN_UD_VALUE,
        T.OPEN_UD_MVP,
        T.UNIT_COST,
        T.AVG_SELLING_PRICE_RMB
    </sql>

    <sql id="queryDetails_OrderedDemand_BackOrder">
        SELECT <include refid="queryDetails_OrderedDemand_ColumnNames"/>
         FROM SIOP_OPEN_SO_MONTH_V t
        WHERE T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
        <include refid="minisiop_filter"/>
    </sql>

    <sql id="queryDetails_OrderedDemand_CRDInSP">
        SELECT <include refid="queryDetails_OrderedDemand_ColumnNames"/>
          FROM SIOP_OPEN_SO_MONTH_V t
         WHERE t.CALENDAR_MONTH &lt;= #{dateRange[1], jdbcType=VARCHAR}
           AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
        <include refid="minisiop_filter"/>
    </sql>

    <sql id="queryDetails_OrderedDemand_IGCRD">
        SELECT <include refid="queryDetails_OrderedDemand_ColumnNames"/>
          FROM SIOP_OPEN_SO_MONTH_V t
         WHERE T.SE_SCOPE IN ('SECI_IG', 'SEHK_IG')
         <include refid="minisiop_filter"/>
    </sql>

    <sql id="queryDetails_OrderedDemand_CRDOutSP">
        SELECT <include refid="queryDetails_OrderedDemand_ColumnNames"/>
          FROM SIOP_OPEN_SO_MONTH_V t
         WHERE t.CALENDAR_MONTH > #{dateRange[1], jdbcType=VARCHAR}
           AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
        <include refid="minisiop_filter"/>
    </sql>

    <!-- SOH Order  -->

    <sql id="querySummary_SOHOrder_Base">
        WITH DEMAND_OG AS (
                SELECT t.MATERIAL,
                       SUM(t.OPEN_SO_QTY) QTY,
                       SUM(${crdColumn}) "VALUE"
                  FROM SIOP_OPEN_SO_MONTH_V t
                 WHERE t.CALENDAR_MONTH &lt;= #{dateRange[1], jdbcType=VARCHAR}
                       AND t.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                       <include refid="minisiop_filter"/>
                 GROUP BY t.MATERIAL
            ),
            DEMAND_UD AS (
                SELECT /*+ materialize */
                       t.MATERIAL,
                       NVL(AVG(${priceColumn}), 0) UD_PRICE,
                       NVL(SUM(CASE WHEN t.SE_SCOPE IN ('SECI_IG', 'SEHK_IG') THEN UD_LT_7_CD  END), 0)            IG_LT_7_QTY,
                       NVL(SUM(CASE WHEN t.SE_SCOPE IN ('SECI_OG', 'SEHK_OG') THEN UD_LT_7_CD  END), 0)            OG_LT_7_QTY,
                       NVL(SUM(CASE WHEN t.SE_SCOPE IN ('SECI_IG', 'SEHK_IG') THEN UD_LT_30_CD  END), 0)           IG_LT_30_QTY,
                       NVL(SUM(CASE WHEN t.SE_SCOPE IN ('SECI_OG', 'SEHK_OG') THEN UD_LT_30_CD  END), 0)           OG_LT_30_QTY,
                       NVL(SUM(CASE WHEN t.SE_SCOPE IN ('SECI_IG', 'SEHK_IG') THEN UD_GT_30_CD  END), 0)           IG_GT_30_QTY,
                       NVL(SUM(CASE WHEN t.SE_SCOPE IN ('SECI_OG', 'SEHK_OG') THEN UD_GT_30_CD  END), 0)           OG_GT_30_QTY
                 FROM  SIOP_OPEN_SO_MONTH_V t
                WHERE  t.SE_SCOPE IN ('SECI_OG', 'SECI_IG')
                <include refid="minisiop_filter"/>
                GROUP BY t.MATERIAL
            ),
            SOH_SUPPLY AS (
                 SELECT t.MATERIAL,
                        SUM(t.ORDER_QUANTITY) SOH_QTY,
                        AVG(${priceColumn})   PRICE
                   FROM DEMAND_SOH_V t
                 WHERE 1 = 1
                 <include refid="minisiop_filter2"/>
                 GROUP BY t.MATERIAL
            ),
            SOH_FOR_UD_LT_7CD AS (
                SELECT MM.*,
                       NVL(SOH_QTY,0) - NVL(SOH_COVER_OG_QTY,0) - NVL(SOH_COVER_IG_QTY,0)  SOH_REMAIN_QTY
                FROM (
                    SELECT NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)   MATERIAL,
                           NVL(SUPPLY.SOH_QTY, 0)                   SOH_QTY,
                           NVL(SUPPLY.PRICE, 0)                     PRICE,
                           NVL(DEMANDS.UD_PRICE, SUPPLY.PRICE)      UD_PRICE,

                           CASE WHEN NVL(SUPPLY.SOH_QTY, 0) >= NVL(DEMANDS.OG_LT_7_QTY, 0) THEN NVL(DEMANDS.OG_LT_7_QTY, 0)
                                ELSE NVL(SUPPLY.SOH_QTY, 0)
                            END AS SOH_COVER_OG_QTY,

                           CASE WHEN NVL(SUPPLY.SOH_QTY, 0) &lt; NVL(DEMANDS.OG_LT_7_QTY, 0) THEN 0
                                WHEN (NVL(SUPPLY.SOH_QTY, 0) - NVL(DEMANDS.OG_LT_7_QTY, 0)) >= NVL(DEMANDS.IG_LT_7_QTY, 0) THEN NVL(DEMANDS.IG_LT_7_QTY, 0)
                                ELSE (NVL(SUPPLY.SOH_QTY, 0) - NVL(DEMANDS.OG_LT_7_QTY, 0))
                            END AS SOH_COVER_IG_QTY

                      FROM SOH_SUPPLY SUPPLY FULL JOIN DEMAND_UD DEMANDS ON SUPPLY.MATERIAL = DEMANDS.MATERIAL
                ) MM
            ),
            SOH_FOR_UD_LT_30CD AS (
                SELECT MM.*,
                       NVL(SOH_QTY,0) - NVL(SOH_COVER_OG_QTY, 0) - NVL(SOH_COVER_IG_QTY, 0)  SOH_REMAIN_QTY,
                       IG_UD_QTY0 + SOH_COVER_IG_QTY                                         IG_UD_QTY
                FROM (
                    SELECT NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)   MATERIAL,
                           NVL(SUPPLY.SOH_REMAIN_QTY, 0)            SOH_QTY,
                           NVL(SUPPLY.PRICE, 0)                     PRICE,
                           NVL(DEMANDS.UD_PRICE, SUPPLY.PRICE)      UD_PRICE,
                           NVL(SUPPLY.SOH_COVER_IG_QTY, 0)          IG_UD_QTY0,

                           CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.OG_LT_30_QTY, 0) THEN NVL(DEMANDS.OG_LT_30_QTY, 0)
                                ELSE NVL(SUPPLY.SOH_REMAIN_QTY, 0)
                           END AS SOH_COVER_OG_QTY,

                           CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) &lt; NVL(DEMANDS.OG_LT_30_QTY, 0) THEN 0
                                WHEN (NVL(SUPPLY.SOH_REMAIN_QTY, 0) - NVL(DEMANDS.OG_LT_30_QTY, 0)) >= NVL(DEMANDS.IG_LT_30_QTY, 0) THEN NVL(DEMANDS.IG_LT_30_QTY, 0)
                                ELSE (NVL(SUPPLY.SOH_REMAIN_QTY, 0) - NVL(DEMANDS.OG_LT_30_QTY, 0))
                           END AS SOH_COVER_IG_QTY

                      FROM SOH_FOR_UD_LT_7CD SUPPLY FULL JOIN DEMAND_UD DEMANDS ON SUPPLY.MATERIAL = DEMANDS.MATERIAL
                ) MM
            ),
            SOH_FOR_UD_GT_30CD AS (
                SELECT MM.*,
                       NVL(SOH_QTY,0) - NVL(SOH_COVER_OG_QTY,0) - NVL(SOH_COVER_IG_QTY,0)  SOH_REMAIN_QTY,
                       IG_UD_QTY0 + SOH_COVER_IG_QTY                                       IG_UD_QTY
                FROM (
                    SELECT NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)   MATERIAL,
                           NVL(SUPPLY.SOH_REMAIN_QTY, 0)            SOH_QTY,
                           NVL(SUPPLY.PRICE, 0)                     PRICE,
                           NVL(DEMANDS.UD_PRICE, SUPPLY.PRICE)      UD_PRICE,
                           NVL(SUPPLY.IG_UD_QTY, 0)                 IG_UD_QTY0,

                           CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.OG_GT_30_QTY, 0) THEN NVL(DEMANDS.OG_GT_30_QTY, 0)
                                ELSE NVL(SUPPLY.SOH_REMAIN_QTY, 0)
                           END AS SOH_COVER_OG_QTY,

                           CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) &lt; NVL(DEMANDS.OG_GT_30_QTY, 0) THEN 0
                                WHEN (NVL(SUPPLY.SOH_REMAIN_QTY, 0) - NVL(DEMANDS.OG_GT_30_QTY, 0)) >= NVL(DEMANDS.IG_GT_30_QTY, 0) THEN NVL(DEMANDS.IG_GT_30_QTY, 0)
                                ELSE (NVL(SUPPLY.SOH_REMAIN_QTY, 0) - NVL(DEMANDS.OG_GT_30_QTY, 0))
                           END AS SOH_COVER_IG_QTY

                      FROM SOH_FOR_UD_LT_30CD SUPPLY FULL JOIN DEMAND_UD DEMANDS ON SUPPLY.MATERIAL = DEMANDS.MATERIAL
                ) MM
            ),
            SOH_FOR_DEMAND_SP AS (
                SELECT MM.*,
                       NVL(SOH_QTY,0) - NVL(SOH_COVER_QTY,0)   SOH_REMAIN_QTY
                FROM (
                    SELECT NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)                   MATERIAL,
                           NVL(SUPPLY.SOH_REMAIN_QTY, 0)                            SOH_QTY,
                           DECODE(DEMANDS.QTY, 0, 0, DEMANDS.VALUE / DEMANDS.QTY)   DEMAND_PRICE,

                           CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN NVL(DEMANDS.QTY, 0)
                                ELSE NVL(SUPPLY.SOH_REMAIN_QTY, 0) END AS SOH_COVER_QTY

                      FROM SOH_FOR_UD_GT_30CD SUPPLY FULL JOIN DEMAND_OG DEMANDS ON SUPPLY.MATERIAL = DEMANDS.MATERIAL
                ) MM
            ),
            IG_ORDER_DEMAND AS (
                 SELECT MATERIAL,
                        SUM(t.OPEN_SO_QTY)   QTY,
                        AVG(${priceColumn})  ORDER_PRICE
                   FROM SIOP_OPEN_SO_MONTH_V t
                  WHERE t.SE_SCOPE IN ('SECI_IG', 'SEHK_IG')
                  <include refid="minisiop_filter"/>
                  GROUP BY t.MATERIAL
            ),
            SOH_FOR_IG_ORDER AS (
                SELECT MM.*,
                       NVL(SOH_QTY,0) - NVL(SOH_COVER_QTY,0)   SOH_REMAIN_QTY
                FROM (
                    SELECT NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)   MATERIAL,
                           NVL(SUPPLY.SOH_REMAIN_QTY, 0)            SOH_QTY,
                           NVL(DEMANDS.ORDER_PRICE, 0)              ORDER_PRICE,

                           CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN NVL(DEMANDS.QTY, 0)
                                ELSE NVL(SUPPLY.SOH_REMAIN_QTY, 0) END AS SOH_COVER_QTY

                      FROM SOH_FOR_DEMAND_SP SUPPLY FULL JOIN IG_ORDER_DEMAND DEMANDS ON SUPPLY.MATERIAL = DEMANDS.MATERIAL
                ) MM
            ),
            SS_DEMAND AS (
                SELECT t.MATERIAL,
                       SUM(t.SAFETY_STOCK)   QTY,
                       AVG(${priceColumn}) PRICE
                from DEMAND_SOH_V t
                where 1 = 1
                <include refid="minisiop_filter2"/>
                GROUP BY t.MATERIAL
            ),
            SOH_FOR_SS AS (
                SELECT MM.*,
                       NVL(SOH_QTY,0) - NVL(SOH_COVER_QTY,0)   SOH_REMAIN_QTY
                FROM (
                    SELECT NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)   MATERIAL,
                           NVL(SUPPLY.SOH_REMAIN_QTY, 0)            SOH_QTY,
                           NVL(DEMANDS.PRICE, 0)                    DEMAND_PRICE,

                           CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN NVL(DEMANDS.QTY, 0)
                                ELSE NVL(SUPPLY.SOH_REMAIN_QTY, 0) END AS SOH_COVER_QTY

                      FROM SOH_FOR_IG_ORDER SUPPLY FULL JOIN SS_DEMAND DEMANDS ON SUPPLY.MATERIAL = DEMANDS.MATERIAL
                ) MM
            ),
            FUTURE_ORDER_DEMAND AS (
                 SELECT MATERIAL,
                        SUM(t.OPEN_SO_QTY)  QTY,
                        SUM(${crdColumn})   "VALUE",
                        AVG(${priceColumn}) "OPEN_ORDER_PRICE"
                   FROM SIOP_OPEN_SO_MONTH_V t
                  WHERE t.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                  <include refid="minisiop_filter"/>
                  GROUP BY t.MATERIAL
             ),
            SOH_FOR_FUTURE_ORDER AS (
                SELECT MM.*,
                       NVL(SOH_QTY,0) - NVL(SOH_COVER_QTY,0)   SOH_REMAIN_QTY
                  FROM (
                    SELECT NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)               MATERIAL,
                           NVL(SUPPLY.SOH_REMAIN_QTY, 0)                        SOH_QTY,
                           NVL(SUPPLY.DEMAND_PRICE, DEMANDS.OPEN_ORDER_PRICE)   PRICE,
                           NVL(DEMANDS.OPEN_ORDER_PRICE, SUPPLY.DEMAND_PRICE)   ORDER_PRICE,

                           CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN NVL(DEMANDS.QTY, 0)
                                ELSE NVL(SUPPLY.SOH_REMAIN_QTY, 0) END SOH_COVER_QTY

                      FROM SOH_FOR_SS SUPPLY FULL JOIN FUTURE_ORDER_DEMAND DEMANDS ON SUPPLY.MATERIAL = DEMANDS.MATERIAL
                ) MM
             ),
            AGING_DEMAND AS (
                 SELECT t.MATERIAL,
                        SUM(t.STOCK_1Y_2Y)  AGING_1Y_2Y_QTY,
                        SUM(t.STOCK_GT_2Y)  AGING_GT_2Y_QTY,
                        AVG(${priceColumn}) PRICE
                   FROM DEMAND_SOH_V t
                  WHERE 1 = 1
                  <include refid="minisiop_filter2"/>
                  GROUP BY t.MATERIAL
            ),
            SOH_FOR_AGING AS (
                SELECT MM.*,
                       NVL(SOH_QTY,0) - NVL(SOH_COVER_AGING1_QTY,0) - NVL(SOH_COVER_AGING2_QTY,0)  SOH_REMAIN_QTY
                FROM (
                    SELECT NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)   MATERIAL,
                           NVL(SUPPLY.SOH_REMAIN_QTY, 0)            SOH_QTY,
                           NVL(DEMANDS.PRICE, 0)                    DEMAND_PRICE,

                           CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.AGING_1Y_2Y_QTY, 0) THEN NVL(AGING_1Y_2Y_QTY, 0)
                                ELSE NVL(SUPPLY.SOH_REMAIN_QTY, 0)
                           END AS SOH_COVER_AGING1_QTY,

                           CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) &lt; NVL(DEMANDS.AGING_1Y_2Y_QTY, 0) THEN 0
                                WHEN (NVL(SUPPLY.SOH_REMAIN_QTY, 0) - NVL(DEMANDS.AGING_1Y_2Y_QTY, 0)) >= NVL(DEMANDS.AGING_GT_2Y_QTY, 0) THEN NVL(DEMANDS.AGING_GT_2Y_QTY, 0)
                                ELSE (NVL(SUPPLY.SOH_REMAIN_QTY, 0) - NVL(DEMANDS.AGING_1Y_2Y_QTY, 0))
                           END AS SOH_COVER_AGING2_QTY

                      FROM SOH_FOR_FUTURE_ORDER SUPPLY FULL JOIN AGING_DEMAND DEMANDS ON SUPPLY.MATERIAL = DEMANDS.MATERIAL
                ) MM
            )
    </sql>

    <select id="querySummary_SOHOrder" resultType="java.util.Map">
        <include refid="querySummary_SOHOrder_Base"></include>

            SELECT 'OG_UD_LT_7_CD' AS "TYPE", SUM(SOH_COVER_OG_QTY * UD_PRICE) AS "VALUE" FROM SOH_FOR_UD_LT_7CD

            UNION ALL

            SELECT 'OG_UD_LT_30_CD', SUM(SOH_COVER_OG_QTY * UD_PRICE) FROM SOH_FOR_UD_LT_30CD

            UNION ALL

            SELECT 'OG_UD_GT_30_CD', SUM(SOH_COVER_OG_QTY * UD_PRICE) FROM SOH_FOR_UD_GT_30CD

            UNION ALL

            SELECT 'IG_UD', SUM(IG_UD_QTY * UD_PRICE) FROM SOH_FOR_UD_GT_30CD

            UNION ALL

            SELECT 'FOR_ORDER_IN_SELECTED_PERIOD', SUM(SOH_COVER_QTY * DEMAND_PRICE) FROM SOH_FOR_DEMAND_SP

            UNION ALL

            SELECT 'FOR_IG_ORDER_IN_SELECTED_PERIOD', SUM(SOH_COVER_QTY * ORDER_PRICE) FROM SOH_FOR_IG_ORDER

            UNION ALL

            SELECT 'FOR_SAFETY_STOCK', SUM(SOH_COVER_QTY * DEMAND_PRICE) FROM SOH_FOR_SS

            UNION ALL

            SELECT 'FOR_FUTURE_ORDER', SUM(SOH_COVER_QTY * ORDER_PRICE) FROM SOH_FOR_FUTURE_ORDER

            UNION ALL

            SELECT 'STOCK_AGING_GT_1Y', SUM(SOH_COVER_AGING1_QTY * DEMAND_PRICE) FROM SOH_FOR_AGING

            UNION ALL

            SELECT 'STOCK_AGING_GT_2Y', SUM(SOH_COVER_AGING2_QTY * DEMAND_PRICE) FROM SOH_FOR_AGING

            UNION ALL

            SELECT 'FOR_FUTURE_DEMAND', SUM(SOH_REMAIN_QTY * DEMAND_PRICE) FROM SOH_FOR_AGING
    </select>

    <sql id="queryDetails_SOHOrder_Base">
        <include refid="querySummary_SOHOrder_Base"/>
        SELECT <include refid="columnNames"/>,
               T.AVG_SELLING_PRICE_RMB,
               T.UNIT_COST,
               T.SAFETY_STOCK,
               T.ORDER_QUANTITY,
               T.NET_NET_VALUE,
               T.MOVING_AVERAGE_P,
    </sql>

    <sql id="queryDetails_SOHOrder_UDLT7">
         <include refid="queryDetails_SOHOrder_Base"/>
               T2.UD_PRICE,
               T2.SOH_COVER_OG_QTY SOH_COVER_OG_UD_QTY
          FROM DEMAND_SOH_V T
         INNER JOIN SOH_FOR_UD_LT_7CD T2 ON T.MATERIAL = T2.MATERIAL
         WHERE T2.SOH_COVER_OG_QTY > 0
    </sql>

    <sql id="queryDetails_SOHOrder_UDGT7">
         <include refid="queryDetails_SOHOrder_Base"/>
               T2.UD_PRICE,
               T2.SOH_COVER_OG_QTY SOH_COVER_OG_UD_QTY
          FROM DEMAND_SOH_V T
         INNER JOIN SOH_FOR_UD_LT_30CD T2 ON T.MATERIAL = T2.MATERIAL
         WHERE T2.SOH_COVER_OG_QTY > 0
    </sql>

    <sql id="queryDetails_SOHOrder_UDGT30">
         <include refid="queryDetails_SOHOrder_Base"/>
               T2.UD_PRICE,
               T2.SOH_COVER_OG_QTY SOH_COVER_OG_UD_QTY
          FROM DEMAND_SOH_V T
         INNER JOIN SOH_FOR_UD_GT_30CD T2 ON T.MATERIAL = T2.MATERIAL
         WHERE T2.SOH_COVER_OG_QTY > 0
    </sql>

    <sql id="queryDetails_SOHOrder_IGUD">
         <include refid="queryDetails_SOHOrder_Base"/>
               T2.UD_PRICE,
               T2.IG_UD_QTY SOH_COVER_IG_UD_QTY
          FROM DEMAND_SOH_V T
         INNER JOIN SOH_FOR_UD_GT_30CD T2 ON T.MATERIAL = T2.MATERIAL
         WHERE T2.IG_UD_QTY > 0
    </sql>

    <sql id="queryDetails_SOHOrder_OrderInSP">
         <include refid="queryDetails_SOHOrder_Base"/>
               T2.DEMAND_PRICE ORDER_PRICE,
               T2.SOH_COVER_QTY
          FROM DEMAND_SOH_V T
          INNER JOIN SOH_FOR_DEMAND_SP T2 ON T.MATERIAL = T2.MATERIAL
          WHERE T2.SOH_COVER_QTY > 0
    </sql>

    <sql id="queryDetails_SOHOrder_IGOrderInSP">
         <include refid="queryDetails_SOHOrder_Base"/>
               T2.ORDER_PRICE,
               T2.SOH_COVER_QTY
          FROM DEMAND_SOH_V T
          INNER JOIN SOH_FOR_IG_ORDER T2 ON T.MATERIAL = T2.MATERIAL
          WHERE T2.SOH_COVER_QTY > 0
    </sql>

    <sql id="queryDetails_SOHOrder_SafetyStock">
         <include refid="queryDetails_SOHOrder_Base"/>
               T2.DEMAND_PRICE PRICE,
               T2.SOH_COVER_QTY
          FROM DEMAND_SOH_V T
          INNER JOIN SOH_FOR_SS T2 ON T.MATERIAL = T2.MATERIAL
          WHERE T2.SOH_COVER_QTY > 0
    </sql>

    <sql id="queryDetails_SOHOrder_FutureOrder">
         <include refid="queryDetails_SOHOrder_Base"/>
               T2.ORDER_PRICE,
               T2.SOH_COVER_QTY
          FROM DEMAND_SOH_V T
          INNER JOIN SOH_FOR_FUTURE_ORDER T2 ON T.MATERIAL = T2.MATERIAL
          WHERE T2.SOH_COVER_QTY > 0
    </sql>

    <sql id="queryDetails_SOHOrder_FutureDemand">
         <include refid="queryDetails_SOHOrder_Base"/>
               T2.DEMAND_PRICE PRICE,
               T2.SOH_REMAIN_QTY
          FROM DEMAND_SOH_V T
          INNER JOIN SOH_FOR_AGING T2 ON T.MATERIAL = T2.MATERIAL
          WHERE T2.SOH_REMAIN_QTY > 0
    </sql>

    <sql id="queryDetails_SOHOrder_AgingGT1Y">
         <include refid="queryDetails_SOHOrder_Base"/>
               T2.DEMAND_PRICE PRICE,
               T2.SOH_COVER_AGING1_QTY SOH_COVER_QTY
          FROM DEMAND_SOH_V T
          INNER JOIN SOH_FOR_AGING T2 ON T.MATERIAL = T2.MATERIAL
          WHERE T2.SOH_COVER_AGING1_QTY > 0
    </sql>

    <sql id="queryDetails_SOHOrder_AgingGT2Y">
         <include refid="queryDetails_SOHOrder_Base"/>
               T2.DEMAND_PRICE PRICE,
               T2.SOH_COVER_AGING2_QTY SOH_COVER_QTY
          FROM DEMAND_SOH_V T
          INNER JOIN SOH_FOR_AGING T2 ON T.MATERIAL = T2.MATERIAL
          WHERE T2.SOH_COVER_AGING2_QTY > 0
    </sql>

    <!-- GIT Order  -->

    <sql id="querySummary_GITOrder_Base">
        WITH TOTAL_UD AS (
                SELECT  t.MATERIAL,
                        SUM(NVL(UD_GT_30_CD, 0) + NVL(UD_LT_30_CD, 0) + NVL(UD_LT_7_CD, 0))                                  "QTY",
                        SUM(NVL(UD_GT_30_CD${UDSubfix}, 0) + NVL(UD_LT_30_CD${UDSubfix}, 0) + NVL(UD_LT_7_CD${UDSubfix}, 0)) "VALUE"
                  FROM  SIOP_OPEN_SO_MONTH_V t
                 WHERE  1 = 1
                 <include refid="minisiop_filter"/>
                 GROUP BY t.MATERIAL
             ),
             OPEN_CRD_IN_SP AS (
                SELECT MATERIAL,
                       SUM(OPEN_SO_QTY)  "QTY",
                       SUM(${crdColumn}) "VALUE"
                  FROM SIOP_OPEN_SO_MONTH_V t
                 WHERE t.CALENDAR_MONTH &lt;= #{dateRange[1], jdbcType=VARCHAR}
                   AND t.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                <include refid="minisiop_filter"/>
                 GROUP BY MATERIAL
             ),
             DEMAND_TEMP AS (
                SELECT MATERIAL,
                       MAX(QTY) "QTY",
                       MAX(VALUE) "VALUE"
                FROM (
                    SELECT NVL(T.MATERIAL, T2.MATERIAL) MATERIAL,
                           GREATEST(NVL(T.QTY, 0),NVL(T2.QTY, 0)) AS QTY,
                           CASE WHEN NVL(T.QTY, 0)  >= NVL(T2.QTY, 0) THEN T.VALUE
                                WHEN NVL(T2.QTY, 0) >= NVL(T.QTY, 0) THEN T2.VALUE
                           END AS "VALUE"
                      FROM TOTAL_UD T FULL JOIN OPEN_CRD_IN_SP T2 ON T.MATERIAL = T2.MATERIAL
                ) MM GROUP BY MATERIAL
             ),
             SUPPLY_TEMP AS (
                SELECT NVL(T.MATERIAL, T2.MATERIAL)                    MATERIAL,
                       NVL(T.ORDER_QUANTITY, 0)                        SOH_QTY,
                       NVL(T2.QUANTITY, 0)                             LA_QTY,
                       NVL(T2.AVG_SELLING_PRICE_RMB, T.AVG_SELLING_PRICE_RMB)  AVG_SELLING_PRICE_RMB,
                       NVL(T2.UNIT_COST, T.UNIT_COST)                  UNIT_COST
                  FROM (
                          SELECT T.MATERIAL,
                                 SUM(T.ORDER_QUANTITY)    ORDER_QUANTITY,
                                 AVG(T.AVG_SELLING_PRICE_RMB) AVG_SELLING_PRICE_RMB,
                                 AVG(T.UNIT_COST)         UNIT_COST
                           FROM DEMAND_SOH_V T
                          WHERE 1 = 1
                          <include refid="minisiop_filter2"/>
                          GROUP BY T.MATERIAL
                        ) T FULL JOIN
                       (SELECT MATERIAL,
                               SUM(QUANTITY)          QUANTITY,
                               AVG(AVG_SELLING_PRICE_RMB) AVG_SELLING_PRICE_RMB,
                               AVG(UNIT_COST)         UNIT_COST
                          FROM SIOP_LA_AB_MONTH_V T
                         WHERE CATEGORY IN ('LA','ZA')
                           AND CALENDAR_MONTH &lt;= #{dateRange[1], jdbcType=VARCHAR}
                           <include refid="minisiop_filter2"/>
                         GROUP BY MATERIAL) T2
                       ON T.MATERIAL = T2.MATERIAL
             ),
             LA_FOR_DEMAND_IN_SP AS (
                SELECT MM.*,
                       NVL(SOH_QTY,0) - NVL(SOH_COVER_QTY,0)   SOH_REMAIN_QTY,
                       NVL(LA_QTY,0) - NVL(LA_COVER_QTY,0)     LA_REMAIN_QTY
                FROM (
                    SELECT NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)                   MATERIAL,
                           NVL(SUPPLY.SOH_QTY, 0)                                   SOH_QTY,
                           NVL(SUPPLY.LA_QTY, 0)                                    LA_QTY,
                           NVL(${priceColumn}, 0)                                   PRICE,
                           DECODE(DEMANDS.QTY, 0, 0, DEMANDS.VALUE / DEMANDS.QTY)   DEMAND_PRICE,

                           CASE WHEN NVL(SUPPLY.SOH_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN 0
                                WHEN (NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_QTY, 0)) >= NVL(SUPPLY.LA_QTY, 0) THEN NVL(SUPPLY.LA_QTY, 0)
                                WHEN (NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_QTY, 0)) &lt; NVL(SUPPLY.LA_QTY, 0) THEN NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_QTY, 0)
                            END AS LA_COVER_QTY,

                           CASE WHEN NVL(SUPPLY.SOH_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN NVL(DEMANDS.QTY, 0)
                                ELSE NVL(SUPPLY.SOH_QTY, 0) END AS SOH_COVER_QTY

                      FROM SUPPLY_TEMP SUPPLY FULL JOIN DEMAND_TEMP DEMANDS ON SUPPLY.MATERIAL = DEMANDS.MATERIAL
                ) MM
             ),
             OPENSO_IG_DEMAND_TEMP AS (
                 SELECT MATERIAL,
                        SUM(t.OPEN_SO_QTY - UD_GT_30_CD - UD_LT_30_CD - UD_LT_7_CD)                                 "QTY",
                        SUM(${crdColumn} - UD_GT_30_CD${UDSubfix} - UD_LT_30_CD${UDSubfix} - UD_LT_7_CD${UDSubfix}) "VALUE",
                        AVG(${priceColumn}) OPENSO_PRICE
                   FROM SIOP_OPEN_SO_MONTH_V t
                  WHERE t.SE_SCOPE IN ('SECI_IG', 'SEHK_IG')
                  <include refid="minisiop_filter"/>
                  GROUP BY t.MATERIAL
             ),
             LA_FOR_IG_ORDER AS (
                SELECT MM.*,
                       NVL(SOH_QTY,0) - NVL(SOH_COVER_QTY,0)   SOH_REMAIN_QTY,
                       NVL(LA_QTY,0) - NVL(LA_COVER_QTY,0)     LA_REMAIN_QTY
                  FROM (
                    SELECT NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)   MATERIAL,
                           NVL(SUPPLY.SOH_REMAIN_QTY, 0)            SOH_QTY,
                           NVL(SUPPLY.LA_REMAIN_QTY, 0)             LA_QTY,
                           NVL(SUPPLY.PRICE, DEMANDS.OPENSO_PRICE)  PRICE,
                           NVL(DEMANDS.OPENSO_PRICE, SUPPLY.PRICE)  OPENSO_PRICE,

                           CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN 0
                                WHEN (NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_REMAIN_QTY, 0)) >= NVL(SUPPLY.LA_REMAIN_QTY, 0) THEN NVL(SUPPLY.LA_REMAIN_QTY, 0)
                                ELSE NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_REMAIN_QTY, 0)
                            END AS LA_COVER_QTY,

                           CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN NVL(DEMANDS.QTY, 0)
                                ELSE NVL(SUPPLY.SOH_REMAIN_QTY, 0) END SOH_COVER_QTY

                      FROM LA_FOR_DEMAND_IN_SP SUPPLY FULL JOIN OPENSO_IG_DEMAND_TEMP DEMANDS ON SUPPLY.MATERIAL = DEMANDS.MATERIAL
                ) MM
             ),
             SS_DEMAND AS (
                SELECT T.MATERIAL,
                       SUM(T.SAFETY_STOCK) QTY,
                       AVG(${priceColumn}) PRICE
                FROM DEMAND_SOH_V T
                where 1 = 1
                <include refid="minisiop_filter2"/>
                GROUP BY T.MATERIAL
             ),
             LA_FOR_SS AS (
                SELECT MM.*,
                       NVL(SOH_QTY,0) - NVL(SOH_COVER_QTY,0)   SOH_REMAIN_QTY,
                       NVL(LA_QTY,0) - NVL(LA_COVER_QTY,0)     LA_REMAIN_QTY
                  FROM (
                    SELECT NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)   MATERIAL,
                           NVL(SUPPLY.SOH_REMAIN_QTY, 0)            SOH_QTY,
                           NVL(SUPPLY.LA_REMAIN_QTY, 0)             LA_QTY,
                           NVL(DEMANDS.PRICE, SUPPLY.PRICE)         PRICE,

                           CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN 0
                                WHEN (NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_REMAIN_QTY, 0)) >= NVL(SUPPLY.LA_REMAIN_QTY, 0) THEN NVL(SUPPLY.LA_REMAIN_QTY, 0)
                                ELSE NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_REMAIN_QTY, 0)
                            END AS LA_COVER_QTY,

                           CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN NVL(DEMANDS.QTY, 0)
                                ELSE NVL(SUPPLY.SOH_REMAIN_QTY, 0) END SOH_COVER_QTY

                      FROM LA_FOR_IG_ORDER SUPPLY FULL JOIN SS_DEMAND DEMANDS ON SUPPLY.MATERIAL = DEMANDS.MATERIAL
                ) MM
            ),
             OPEN_ORDER_DEMAND AS (
                 SELECT MATERIAL,
                        SUM(t.OPEN_SO_QTY)  QTY,
                        SUM(${crdColumn})   "VALUE",
                        AVG(${priceColumn}) "OPEN_ORDER_PRICE"
                   FROM SIOP_OPEN_SO_MONTH_V t
                  WHERE t.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                  <include refid="minisiop_filter"/>
                  GROUP BY t.MATERIAL
             ),
             LA_FOR_OPEN_ORDER AS (
                SELECT MM.*,
                       NVL(SOH_QTY,0) - NVL(SOH_COVER_QTY,0)   SOH_REMAIN_QTY,
                       NVL(LA_QTY,0) - NVL(LA_COVER_QTY,0)     LA_REMAIN_QTY
                  FROM (
                    SELECT NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)       MATERIAL,
                           NVL(SUPPLY.SOH_REMAIN_QTY, 0)                SOH_QTY,
                           NVL(SUPPLY.LA_REMAIN_QTY, 0)                 LA_QTY,
                           NVL(SUPPLY.PRICE, DEMANDS.OPEN_ORDER_PRICE)  PRICE,
                           NVL(DEMANDS.OPEN_ORDER_PRICE, SUPPLY.PRICE)  OPEN_ORDER_PRICE,

                           CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN 0
                                WHEN (NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_REMAIN_QTY, 0)) >= NVL(SUPPLY.LA_REMAIN_QTY, 0) THEN NVL(SUPPLY.LA_REMAIN_QTY, 0)
                                ELSE NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_REMAIN_QTY, 0)
                            END AS LA_COVER_QTY,

                           CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN NVL(DEMANDS.QTY, 0)
                                ELSE NVL(SUPPLY.SOH_REMAIN_QTY, 0) END SOH_COVER_QTY

                      FROM LA_FOR_SS SUPPLY FULL JOIN OPEN_ORDER_DEMAND DEMANDS ON SUPPLY.MATERIAL = DEMANDS.MATERIAL
                ) MM
             )
    </sql>

    <select id="querySummary_GITOrder" resultType="java.util.Map">
            <include refid="querySummary_GITOrder_Base"/>

             SELECT 'LA_FOR_ORDER_IN_SELECTED_PERIOD' AS TYPE, SUM(LA_COVER_QTY * DEMAND_PRICE) AS "VALUE" FROM LA_FOR_DEMAND_IN_SP

             UNION ALL

             SELECT 'LA_FOR_IG_ORDER', SUM(LA_COVER_QTY * OPENSO_PRICE) FROM LA_FOR_IG_ORDER

             UNION ALL

             SELECT 'LA_FOR_SS', SUM(LA_COVER_QTY * PRICE) FROM LA_FOR_SS

             UNION ALL

             SELECT 'LA_FOR_FURTHER_ORDER', SUM(LA_COVER_QTY * OPEN_ORDER_PRICE) FROM LA_FOR_OPEN_ORDER

             UNION ALL

             SELECT 'LA_FOR_FURTHER_DEMAND', SUM(LA_REMAIN_QTY * PRICE) FROM LA_FOR_OPEN_ORDER
    </select>

    <sql id="queryDetails_GITOrder_Base">
        <include refid="querySummary_GITOrder_Base"/>
        SELECT <include refid="columnNames"/>,
               T.CATEGORY,
               T.QUANTITY,
               T.NET_NET_VALUE,
               T.MOVING_AVERAGE_P,
               T.AVG_SELLING_PRICE_RMB,
               T.UNIT_COST,
    </sql>

    <sql id="queryDetails_GITOrder_OrderInSP">
        <include refid="queryDetails_GITOrder_Base"/>
            T2.DEMAND_PRICE ORDER_PRICE,
            T2.LA_COVER_QTY
        FROM SIOP_LA_AB_MONTH_V T INNER JOIN LA_FOR_DEMAND_IN_SP T2 ON T.MATERIAL = T2.MATERIAL
       WHERE T2.LA_COVER_QTY > 0
    </sql>

    <sql id="queryDetails_GITOrder_IGOrderInSP">
        <include refid="queryDetails_GITOrder_Base"/>
            T2.OPENSO_PRICE ORDER_PRICE,
            T2.LA_COVER_QTY
        FROM SIOP_LA_AB_MONTH_V T INNER JOIN LA_FOR_IG_ORDER T2 ON T.MATERIAL = T2.MATERIAL
        WHERE T2.LA_COVER_QTY > 0
    </sql>

    <sql id="queryDetails_GITOrder_SafetyStock">
        <include refid="queryDetails_GITOrder_Base"/>
            T2.PRICE,
            T2.LA_COVER_QTY
        FROM SIOP_LA_AB_MONTH_V T INNER JOIN LA_FOR_SS T2 ON T.MATERIAL = T2.MATERIAL
        WHERE T2.LA_COVER_QTY > 0
    </sql>

    <sql id="queryDetails_GITOrder_FutureOrder">
        <include refid="queryDetails_GITOrder_Base"/>
            T2.OPEN_ORDER_PRICE ORDER_PRICE,
            T2.LA_COVER_QTY
        FROM SIOP_LA_AB_MONTH_V T INNER JOIN LA_FOR_OPEN_ORDER T2 ON T.MATERIAL = T2.MATERIAL
        WHERE T2.LA_COVER_QTY > 0
    </sql>

    <sql id="queryDetails_GITOrder_FutureDemand">
        <include refid="queryDetails_GITOrder_Base"/>
            T2.PRICE,
            T2.LA_REMAIN_QTY
        FROM SIOP_LA_AB_MONTH_V T INNER JOIN LA_FOR_OPEN_ORDER T2 ON T.MATERIAL = T2.MATERIAL
        WHERE T2.LA_REMAIN_QTY > 0
    </sql>

    <!-- Conf.Reource Order  -->

    <sql id="querySummary_ConfResOrder_Base">
        WITH TOTAL_UD AS (
                SELECT  t.MATERIAL,
                        SUM(NVL(UD_GT_30_CD, 0) + NVL(UD_LT_30_CD, 0) + NVL(UD_LT_7_CD, 0))                                  "QTY",
                        SUM(NVL(UD_GT_30_CD${UDSubfix}, 0) + NVL(UD_LT_30_CD${UDSubfix}, 0) + NVL(UD_LT_7_CD${UDSubfix}, 0)) "VALUE"
                  FROM  SIOP_OPEN_SO_MONTH_V t
                 WHERE  1 = 1
                 <include refid="minisiop_filter"/>
                 GROUP BY t.MATERIAL
             ),
             OPEN_CRD_IN_SP AS (
                SELECT MATERIAL,
                       SUM(OPEN_SO_QTY)  "QTY",
                       SUM(${crdColumn}) "VALUE"
                  FROM SIOP_OPEN_SO_MONTH_V t
                 WHERE t.CALENDAR_MONTH &lt;= #{dateRange[1], jdbcType=VARCHAR}
                   AND t.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                <include refid="minisiop_filter"/>
                 GROUP BY MATERIAL
             ),
             DEMAND_TEMP AS (
                SELECT MATERIAL,
                       MAX(QTY) "QTY",
                       MAX(VALUE) "VALUE"
                FROM (
                    SELECT NVL(T.MATERIAL, T2.MATERIAL) MATERIAL,
                           GREATEST(NVL(T.QTY, 0),NVL(T2.QTY, 0)) AS QTY,
                           CASE WHEN NVL(T.QTY, 0)  >= NVL(T2.QTY, 0) THEN T.VALUE
                                WHEN NVL(T2.QTY, 0) >= NVL(T.QTY, 0) THEN T2.VALUE
                           END AS "VALUE"
                      FROM TOTAL_UD T FULL JOIN OPEN_CRD_IN_SP T2 ON T.MATERIAL = T2.MATERIAL
                ) MM GROUP BY MATERIAL
             ),
             SUPPLY_TEMP AS (
                SELECT NVL(T.MATERIAL, T2.MATERIAL)                    MATERIAL,
                       NVL(T.ORDER_QUANTITY, 0)                        SOH_QTY,
                       NVL(T2.QUANTITY, 0)                             LA_QTY,
                       NVL(T2.AVG_SELLING_PRICE_RMB, T.AVG_SELLING_PRICE_RMB)  AVG_SELLING_PRICE_RMB,
                       NVL(T2.UNIT_COST, T.UNIT_COST)                  UNIT_COST
                  FROM (
                          SELECT T.MATERIAL,
                                 SUM(T.ORDER_QUANTITY)    ORDER_QUANTITY,
                                 AVG(T.AVG_SELLING_PRICE_RMB) AVG_SELLING_PRICE_RMB,
                                 AVG(T.UNIT_COST)         UNIT_COST
                           FROM DEMAND_SOH_V T
                          WHERE 1 = 1
                          <include refid="minisiop_filter2"/>
                          GROUP BY T.MATERIAL
                        ) T FULL JOIN
                       (SELECT MATERIAL,
                               SUM(QUANTITY)          QUANTITY,
                               AVG(AVG_SELLING_PRICE_RMB) AVG_SELLING_PRICE_RMB,
                               AVG(UNIT_COST)         UNIT_COST
                          FROM SIOP_LA_AB_MONTH_V T
                         WHERE CALENDAR_MONTH &lt;= #{dateRange[1], jdbcType=VARCHAR}
                           <include refid="minisiop_filter2"/>
                         GROUP BY MATERIAL) T2
                       ON T.MATERIAL = T2.MATERIAL
             ),
             LA_FOR_DEMAND_IN_SP AS (
                SELECT MM.*,
                       NVL(SOH_QTY,0) - NVL(SOH_COVER_QTY,0)   SOH_REMAIN_QTY,
                       NVL(LA_QTY,0) - NVL(LA_COVER_QTY,0)     LA_REMAIN_QTY
                FROM (
                    SELECT NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)                   MATERIAL,
                           NVL(SUPPLY.SOH_QTY, 0)                                   SOH_QTY,
                           NVL(SUPPLY.LA_QTY, 0)                                    LA_QTY,
                           NVL(${priceColumn}, 0)                                   PRICE,
                           DECODE(DEMANDS.QTY, 0, 0, DEMANDS.VALUE / DEMANDS.QTY)   DEMAND_PRICE,

                           CASE WHEN NVL(SUPPLY.SOH_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN 0
                                WHEN (NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_QTY, 0)) >= NVL(SUPPLY.LA_QTY, 0) THEN NVL(SUPPLY.LA_QTY, 0)
                                WHEN (NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_QTY, 0)) &lt; NVL(SUPPLY.LA_QTY, 0) THEN NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_QTY, 0)
                            END AS LA_COVER_QTY,

                           CASE WHEN NVL(SUPPLY.SOH_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN NVL(DEMANDS.QTY, 0)
                                ELSE NVL(SUPPLY.SOH_QTY, 0) END AS SOH_COVER_QTY

                      FROM SUPPLY_TEMP SUPPLY FULL JOIN DEMAND_TEMP DEMANDS ON SUPPLY.MATERIAL = DEMANDS.MATERIAL
                ) MM
             ),
             OPENSO_IG_DEMAND_TEMP AS (
                 SELECT MATERIAL,
                        SUM(t.OPEN_SO_QTY - UD_GT_30_CD - UD_LT_30_CD - UD_LT_7_CD)                                 "QTY",
                        SUM(${crdColumn} - UD_GT_30_CD${UDSubfix} - UD_LT_30_CD${UDSubfix} - UD_LT_7_CD${UDSubfix}) "VALUE",
                        AVG(${priceColumn}) OPENSO_PRICE
                   FROM SIOP_OPEN_SO_MONTH_V t
                  WHERE t.SE_SCOPE IN ('SECI_IG', 'SEHK_IG')
                  <include refid="minisiop_filter"/>
                  GROUP BY t.MATERIAL
             ),
             LA_FOR_IG_ORDER AS (
                SELECT MM.*,
                       NVL(SOH_QTY,0) - NVL(SOH_COVER_QTY,0)   SOH_REMAIN_QTY,
                       NVL(LA_QTY,0) - NVL(LA_COVER_QTY,0)     LA_REMAIN_QTY
                  FROM (
                    SELECT NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)   MATERIAL,
                           NVL(SUPPLY.SOH_REMAIN_QTY, 0)            SOH_QTY,
                           NVL(SUPPLY.LA_REMAIN_QTY, 0)             LA_QTY,
                           NVL(SUPPLY.PRICE, DEMANDS.OPENSO_PRICE)  PRICE,
                           NVL(DEMANDS.OPENSO_PRICE, SUPPLY.PRICE)  OPENSO_PRICE,

                           CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN 0
                                WHEN (NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_REMAIN_QTY, 0)) >= NVL(SUPPLY.LA_REMAIN_QTY, 0) THEN NVL(SUPPLY.LA_REMAIN_QTY, 0)
                                ELSE NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_REMAIN_QTY, 0)
                            END AS LA_COVER_QTY,

                           CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN NVL(DEMANDS.QTY, 0)
                                ELSE NVL(SUPPLY.SOH_REMAIN_QTY, 0) END SOH_COVER_QTY

                      FROM LA_FOR_DEMAND_IN_SP SUPPLY FULL JOIN OPENSO_IG_DEMAND_TEMP DEMANDS ON SUPPLY.MATERIAL = DEMANDS.MATERIAL
                ) MM
             ),
             SS_DEMAND AS (
                SELECT T.MATERIAL,
                       SUM(T.SAFETY_STOCK) QTY,
                       AVG(${priceColumn}) PRICE
                FROM DEMAND_SOH_V T
                where 1 = 1
                <include refid="minisiop_filter2"/>
                GROUP BY T.MATERIAL
             ),
             LA_FOR_SS AS (
                SELECT MM.*,
                       NVL(SOH_QTY,0) - NVL(SOH_COVER_QTY,0)   SOH_REMAIN_QTY,
                       NVL(LA_QTY,0) - NVL(LA_COVER_QTY,0)     LA_REMAIN_QTY
                  FROM (
                    SELECT NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)   MATERIAL,
                           NVL(SUPPLY.SOH_REMAIN_QTY, 0)            SOH_QTY,
                           NVL(SUPPLY.LA_REMAIN_QTY, 0)             LA_QTY,
                           NVL(DEMANDS.PRICE, SUPPLY.PRICE)         PRICE,

                           CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN 0
                                WHEN (NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_REMAIN_QTY, 0)) >= NVL(SUPPLY.LA_REMAIN_QTY, 0) THEN NVL(SUPPLY.LA_REMAIN_QTY, 0)
                                ELSE NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_REMAIN_QTY, 0)
                            END AS LA_COVER_QTY,

                           CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN NVL(DEMANDS.QTY, 0)
                                ELSE NVL(SUPPLY.SOH_REMAIN_QTY, 0) END SOH_COVER_QTY

                      FROM LA_FOR_IG_ORDER SUPPLY FULL JOIN SS_DEMAND DEMANDS ON SUPPLY.MATERIAL = DEMANDS.MATERIAL
                ) MM
            ),
             OPEN_ORDER_DEMAND AS (
                 SELECT MATERIAL,
                        SUM(t.OPEN_SO_QTY)  QTY,
                        SUM(${crdColumn})   "VALUE",
                        AVG(${priceColumn}) "OPEN_ORDER_PRICE"
                   FROM SIOP_OPEN_SO_MONTH_V t
                  WHERE t.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                  <include refid="minisiop_filter"/>
                  GROUP BY t.MATERIAL
             ),
             LA_FOR_OPEN_ORDER AS (
                SELECT MM.*,
                       NVL(SOH_QTY,0) - NVL(SOH_COVER_QTY,0)   SOH_REMAIN_QTY,
                       NVL(LA_QTY,0) - NVL(LA_COVER_QTY,0)     LA_REMAIN_QTY
                  FROM (
                    SELECT NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)       MATERIAL,
                           NVL(SUPPLY.SOH_REMAIN_QTY, 0)                SOH_QTY,
                           NVL(SUPPLY.LA_REMAIN_QTY, 0)                 LA_QTY,
                           NVL(SUPPLY.PRICE, DEMANDS.OPEN_ORDER_PRICE)  PRICE,
                           NVL(DEMANDS.OPEN_ORDER_PRICE, SUPPLY.PRICE)  OPEN_ORDER_PRICE,

                           CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN 0
                                WHEN (NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_REMAIN_QTY, 0)) >= NVL(SUPPLY.LA_REMAIN_QTY, 0) THEN NVL(SUPPLY.LA_REMAIN_QTY, 0)
                                ELSE NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_REMAIN_QTY, 0)
                            END AS LA_COVER_QTY,

                           CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN NVL(DEMANDS.QTY, 0)
                                ELSE NVL(SUPPLY.SOH_REMAIN_QTY, 0) END SOH_COVER_QTY

                      FROM LA_FOR_SS SUPPLY FULL JOIN OPEN_ORDER_DEMAND DEMANDS ON SUPPLY.MATERIAL = DEMANDS.MATERIAL
                ) MM
             )
    </sql>

    <select id="querySummary_ConfResOrder" resultType="java.util.Map">
        <include refid="querySummary_ConfResOrder_Base"/>

         SELECT 'LA_FOR_ORDER_IN_SELECTED_PERIOD' AS TYPE, SUM(LA_COVER_QTY * DEMAND_PRICE) AS "VALUE" FROM LA_FOR_DEMAND_IN_SP

         UNION ALL

         SELECT 'LA_FOR_IG_ORDER', SUM(LA_COVER_QTY * OPENSO_PRICE) FROM LA_FOR_IG_ORDER

         UNION ALL

         SELECT 'LA_FOR_SS', SUM(LA_COVER_QTY * PRICE) FROM LA_FOR_SS

         UNION ALL

         SELECT 'LA_FOR_FURTHER_ORDER', SUM(LA_COVER_QTY * OPEN_ORDER_PRICE) FROM LA_FOR_OPEN_ORDER

         UNION ALL

         SELECT 'LA_FOR_FURTHER_DEMAND', SUM(LA_REMAIN_QTY * PRICE) FROM LA_FOR_OPEN_ORDER
    </select>

    <sql id="queryDetails_ConfResOrder_Base">
        <include refid="querySummary_ConfResOrder_Base"/>
        SELECT <include refid="columnNames"/>,
               T.CATEGORY,
               T.QUANTITY,
               T.NET_NET_VALUE,
               T.MOVING_AVERAGE_P,
               T.AVG_SELLING_PRICE_RMB,
               T.UNIT_COST,
    </sql>

    <sql id="queryDetails_ConfResOrder_DemandInSP">
        <include refid="queryDetails_ConfResOrder_Base"/>
            T2.DEMAND_PRICE ORDER_PRICE,
            T2.LA_COVER_QTY
        FROM SIOP_LA_AB_MONTH_V T INNER JOIN LA_FOR_DEMAND_IN_SP T2 ON T.MATERIAL = T2.MATERIAL
        WHERE T2.LA_COVER_QTY > 0
    </sql>

    <sql id="queryDetails_ConfResOrder_IGOrderInSP">
        <include refid="queryDetails_ConfResOrder_Base"/>
            T2.OPENSO_PRICE ORDER_PRICE,
            T2.LA_COVER_QTY
        FROM SIOP_LA_AB_MONTH_V T INNER JOIN LA_FOR_IG_ORDER T2 ON T.MATERIAL = T2.MATERIAL
        WHERE T2.LA_COVER_QTY > 0
    </sql>

    <sql id="queryDetails_ConfResOrder_SafetyStock">
        <include refid="queryDetails_ConfResOrder_Base"/>
            T2.PRICE,
            T2.LA_COVER_QTY
        FROM SIOP_LA_AB_MONTH_V T INNER JOIN LA_FOR_SS T2 ON T.MATERIAL = T2.MATERIAL
        WHERE T2.LA_COVER_QTY > 0
    </sql>

    <sql id="queryDetails_ConfResOrder_FutureOrder">
        <include refid="queryDetails_ConfResOrder_Base"/>
            T2.OPEN_ORDER_PRICE ORDER_PRICE,
            T2.LA_COVER_QTY
        FROM SIOP_LA_AB_MONTH_V T INNER JOIN LA_FOR_OPEN_ORDER T2 ON T.MATERIAL = T2.MATERIAL
        WHERE T2.LA_COVER_QTY > 0
    </sql>

    <sql id="queryDetails_ConfResOrder_FutureDemand">
        <include refid="queryDetails_ConfResOrder_Base"/>
            T2.PRICE,
            T2.LA_REMAIN_QTY
        FROM SIOP_LA_AB_MONTH_V T INNER JOIN LA_FOR_OPEN_ORDER T2 ON T.MATERIAL = T2.MATERIAL
        WHERE T2.LA_REMAIN_QTY > 0
    </sql>

    <!-- View Details Dispatcher  -->

    <sql id="queryDetailsDispatcher">
        <choose>
            <when test="queryKey == 'queryDetails_Demand_ActualSalesBeforeSF'.toString()"><include refid="queryDetails_Demand_ActualSalesBeforeSF"/></when>
            <when test="queryKey == 'queryDetails_Demand_UnrealizedLT30Fcst'.toString()"><include refid="queryDetails_Demand_UnrealizedLT30Fcst"/></when>
            <when test="queryKey == 'queryDetails_Demand_UnrealizedGT30Fcst'.toString()"><include refid="queryDetails_Demand_UnrealizedGT30Fcst"/></when>
            <when test="queryKey == 'queryDetails_Demand_UnrealizedGT70Fcst'.toString()"><include refid="queryDetails_Demand_UnrealizedGT70Fcst"/></when>
            <when test="queryKey == 'queryDetails_Demand_PredictedSales'.toString()"><include refid="queryDetails_Demand_PredictedSales"/></when>
            <when test="queryKey == 'queryDetails_Demand_SalesExFcstLT30'.toString()"><include refid="queryDetails_Demand_SalesExFcstLT30"/></when>
            <when test="queryKey == 'queryDetails_Demand_SalesExFcstGT30'.toString()"><include refid="queryDetails_Demand_SalesExFcstGT30"/></when>
            <when test="queryKey == 'queryDetails_Demand_SalesExFcstGT70'.toString()"><include refid="queryDetails_Demand_SalesExFcstGT70"/></when>
            <when test="queryKey == 'queryDetails_Demand_SalesExFcstGT100'.toString()"><include refid="queryDetails_Demand_SalesExFcstGT100"/></when>

            <when test="queryKey == 'queryDetails_STDSales_IGSales'.toString()"><include refid="queryDetails_STDSales_IGSales"/></when>

            <when test="queryKey == 'queryDetails_STDOI_IGOI'.toString()"><include refid="queryDetails_STDOI_IGOI"/></when>
            <when test="queryKey == 'queryDetails_STDOI_ExFcstGT100'.toString()"><include refid="queryDetails_STDOI_ExFcstGT100"/></when>
            <when test="queryKey == 'queryDetails_STDOI_ExFcstGT70'.toString()"><include refid="queryDetails_STDOI_ExFcstGT70"/></when>
            <when test="queryKey == 'queryDetails_STDOI_ExFcstGT30'.toString()"><include refid="queryDetails_STDOI_ExFcstGT30"/></when>
            <when test="queryKey == 'queryDetails_STDOI_ExFcstLT30'.toString()"><include refid="queryDetails_STDOI_ExFcstLT30"/></when>
            <when test="queryKey == 'queryDetails_STDOI_PredictedOI'.toString()"><include refid="queryDetails_STDOI_PredictedOI"/></when>
            <when test="queryKey == 'queryDetails_STDOI_ActualOIBeforeSP'.toString()"><include refid="queryDetails_STDOI_ActualOIBeforeSP"/></when>

            <when test="queryKey == 'queryDetails_STDCRD_IGCRD'.toString()"><include refid="queryDetails_STDCRD_IGCRD"/></when>
            <when test="queryKey == 'queryDetails_STDCRD_ExFcstGT100'.toString()"><include refid="queryDetails_STDCRD_ExFcstGT100"/></when>
            <when test="queryKey == 'queryDetails_STDCRD_ExFcstGT70'.toString()"><include refid="queryDetails_STDCRD_ExFcstGT70"/></when>
            <when test="queryKey == 'queryDetails_STDCRD_ExFcstGT30'.toString()"><include refid="queryDetails_STDCRD_ExFcstGT30"/></when>
            <when test="queryKey == 'queryDetails_STDCRD_ExFcstLT30'.toString()"><include refid="queryDetails_STDCRD_ExFcstLT30"/></when>
            <when test="queryKey == 'queryDetails_STDCRD_PredictedOI'.toString()"><include refid="queryDetails_STDCRD_PredictedOI"/></when>
            <when test="queryKey == 'queryDetails_STDCRD_ActualCRDBeforeSP'.toString()"><include refid="queryDetails_STDCRD_ActualCRDBeforeSP"/></when>

            <when test="queryKey == 'queryDetails_SOHDemand_UDLT7'.toString()"><include refid="queryDetails_SOHDemand_UDLT7"/></when>
            <when test="queryKey == 'queryDetails_SOHDemand_UDGT7'.toString()"><include refid="queryDetails_SOHDemand_UDGT7"/></when>
            <when test="queryKey == 'queryDetails_SOHDemand_UDGT30'.toString()"><include refid="queryDetails_SOHDemand_UDGT30"/></when>
            <when test="queryKey == 'queryDetails_SOHDemand_DemandInSP'.toString()"><include refid="queryDetails_SOHDemand_DemandInSP"/></when>
            <when test="queryKey == 'queryDetails_SOHDemand_IGUD'.toString()"><include refid="queryDetails_SOHDemand_IGUD"/></when>
            <when test="queryKey == 'queryDetails_SOHDemand_IGOrderInSP'.toString()"><include refid="queryDetails_SOHDemand_IGOrderInSP"/></when>
            <when test="queryKey == 'queryDetails_SOHDemand_SafetyStock'.toString()"><include refid="queryDetails_SOHDemand_SafetyStock"/></when>
            <when test="queryKey == 'queryDetails_SOHDemand_FutureDemand'.toString()"><include refid="queryDetails_SOHDemand_FutureDemand"/></when>
            <when test="queryKey == 'queryDetails_SOHDemand_AgingGT1Y'.toString()"><include refid="queryDetails_SOHDemand_AgingGT1Y"/></when>
            <when test="queryKey == 'queryDetails_SOHDemand_AgingGT2Y'.toString()"><include refid="queryDetails_SOHDemand_AgingGT2Y"/></when>

            <when test="queryKey == 'queryDetails_GITDemand_DemandInSP'.toString()"><include refid="queryDetails_GITDemand_DemandInSP"/></when>
            <when test="queryKey == 'queryDetails_GITDemand_IGOrderInSP'.toString()"><include refid="queryDetails_GITDemand_IGOrderInSP"/></when>
            <when test="queryKey == 'queryDetails_GITDemand_SafetyStock'.toString()"><include refid="queryDetails_GITDemand_SafetyStock"/></when>
            <when test="queryKey == 'queryDetails_GITDemand_FutureDemand'.toString()"><include refid="queryDetails_GITDemand_FutureDemand"/></when>

            <when test="queryKey == 'queryDetails_ConfRes_DemandInSP'.toString()"><include refid="queryDetails_ConfRes_DemandInSP"/></when>
            <when test="queryKey == 'queryDetails_ConfRes_IGOrderInSP'.toString()"><include refid="queryDetails_ConfRes_IGOrderInSP"/></when>
            <when test="queryKey == 'queryDetails_ConfRes_SafetyStock'.toString()"><include refid="queryDetails_ConfRes_SafetyStock"/></when>
            <when test="queryKey == 'queryDetails_ConfRes_FutureDemand'.toString()"><include refid="queryDetails_ConfRes_FutureDemand"/></when>

            <when test="queryKey == 'queryDetails_OrderedDemand_BackOrder'.toString()"><include refid="queryDetails_OrderedDemand_BackOrder"/></when>
            <when test="queryKey == 'queryDetails_OrderedDemand_CRDInSP'.toString()"><include refid="queryDetails_OrderedDemand_CRDInSP"/></when>
            <when test="queryKey == 'queryDetails_OrderedDemand_CRDOutSP'.toString()"><include refid="queryDetails_OrderedDemand_CRDOutSP"/></when>
            <when test="queryKey == 'queryDetails_OrderedDemand_IGCRD'.toString()"><include refid="queryDetails_OrderedDemand_IGCRD"/></when>

            <when test="queryKey == 'queryDetails_SOHOrder_UDLT7'.toString()"><include refid="queryDetails_SOHOrder_UDLT7"/></when>
            <when test="queryKey == 'queryDetails_SOHOrder_UDGT7'.toString()"><include refid="queryDetails_SOHOrder_UDGT7"/></when>
            <when test="queryKey == 'queryDetails_SOHOrder_UDGT30'.toString()"><include refid="queryDetails_SOHOrder_UDGT30"/></when>
            <when test="queryKey == 'queryDetails_SOHOrder_OrderInSP'.toString()"><include refid="queryDetails_SOHOrder_OrderInSP"/></when>
            <when test="queryKey == 'queryDetails_SOHOrder_IGUD'.toString()"><include refid="queryDetails_SOHOrder_IGUD"/></when>
            <when test="queryKey == 'queryDetails_SOHOrder_IGOrderInSP'.toString()"><include refid="queryDetails_SOHOrder_IGOrderInSP"/></when>
            <when test="queryKey == 'queryDetails_SOHOrder_SafetyStock'.toString()"><include refid="queryDetails_SOHOrder_SafetyStock"/></when>
            <when test="queryKey == 'queryDetails_SOHOrder_FutureOrder'.toString()"><include refid="queryDetails_SOHOrder_FutureOrder"/></when>
            <when test="queryKey == 'queryDetails_SOHOrder_FutureDemand'.toString()"><include refid="queryDetails_SOHOrder_FutureDemand"/></when>
            <when test="queryKey == 'queryDetails_SOHOrder_AgingGT1Y'.toString()"><include refid="queryDetails_SOHOrder_AgingGT1Y"/></when>
            <when test="queryKey == 'queryDetails_SOHOrder_AgingGT2Y'.toString()"><include refid="queryDetails_SOHOrder_AgingGT2Y"/></when>

            <when test="queryKey == 'queryDetails_GITOrder_OrderInSP'.toString()"><include refid="queryDetails_GITOrder_OrderInSP"/></when>
            <when test="queryKey == 'queryDetails_GITOrder_IGOrderInSP'.toString()"><include refid="queryDetails_GITOrder_IGOrderInSP"/></when>
            <when test="queryKey == 'queryDetails_GITOrder_SafetyStock'.toString()"><include refid="queryDetails_GITOrder_SafetyStock"/></when>
            <when test="queryKey == 'queryDetails_GITOrder_FutureOrder'.toString()"><include refid="queryDetails_GITOrder_FutureOrder"/></when>
            <when test="queryKey == 'queryDetails_GITOrder_FutureDemand'.toString()"><include refid="queryDetails_GITOrder_FutureDemand"/></when>

            <when test="queryKey == 'queryDetails_ConfResOrder_DemandInSP'.toString()"><include refid="queryDetails_ConfResOrder_DemandInSP"/></when>
            <when test="queryKey == 'queryDetails_ConfResOrder_IGOrderInSP'.toString()"><include refid="queryDetails_ConfResOrder_IGOrderInSP"/></when>
            <when test="queryKey == 'queryDetails_ConfResOrder_SafetyStock'.toString()"><include refid="queryDetails_ConfResOrder_SafetyStock"/></when>
            <when test="queryKey == 'queryDetails_ConfResOrder_FutureOrder'.toString()"><include refid="queryDetails_ConfResOrder_FutureOrder"/></when>
            <when test="queryKey == 'queryDetails_ConfResOrder_FutureDemand'.toString()"><include refid="queryDetails_ConfResOrder_FutureDemand"/></when>
            <otherwise>
                SELECT 1 FROM DUAL
            </otherwise>
        </choose>
    </sql>

    <select id="queryDetailsCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryDetailsDispatcher"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryDetails" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryDetailsDispatcher"/>
        <include refid="global.select_footer"/>
    </select>

    <!-- View Details Dispatcher  -->

    <!-- For MiniSIOP Meeting  -->
    <select id="queryProductLineForMiniSIOPMeeting" resultType="java.lang.String">
        SELECT DISTINCT T.PRODUCT_LINE
          FROM SIOP_ORDER_INTAKE_MONTH_V T
         ORDER BY DECODE(T.PRODUCT_LINE, 'Others', 'ZZZZZ', t.PRODUCT_LINE)
    </select>

    <select id="queryLocalProductLineForMiniSIOPMeeting" resultType="java.util.Map">
        SELECT T.LOCAL_PRODUCT_LINE, T.LOCAL_PRODUCT_FAMILY
          FROM SIOP_ORDER_INTAKE_MONTH_V T
         GROUP BY T.LOCAL_PRODUCT_LINE, T.LOCAL_PRODUCT_FAMILY
         ORDER BY DECODE(T.LOCAL_PRODUCT_LINE, 'Others', 'ZZZZZ', t.LOCAL_PRODUCT_LINE),
                  DECODE(T.LOCAL_PRODUCT_FAMILY, 'Others', 'ZZZZZ', t.LOCAL_PRODUCT_FAMILY)
    </select>

    <select id="querySummary_Demand_ForMiniSIOPMeeting" resultType="java.util.Map">
        SELECT RATIO_LEVEL,
               SUM(CASE WHEN RATIO_LEVEL IN ('GT070', 'GT030', 'LT030') THEN -1 * "VALUE"
                        ELSE "VALUE" END)        "VALUE",
               SUM(PREDICTED_VALUE)              PREDICTED_VALUE,
               ${_type}
        from (
                 SELECT CASE
                            WHEN RATIO > 2 THEN 'GT200'
                            WHEN RATIO >= 1.7 THEN 'GT170'
                            WHEN RATIO >= 1.3 THEN 'GT130'
                            WHEN RATIO > 1 THEN 'GT100'
                            WHEN RATIO >= 0.7 THEN 'GT070'
                            WHEN RATIO >= 0.3 THEN 'GT030'
                            ELSE 'LT030' END RATIO_LEVEL,
                        "VALUE",
                        PREDICTED_VALUE,
                        ${_type}
                 from (
                          SELECT DECODE(NVL(fcst.FCST_QTY, 0), 0, 9.9, NVL(sales.SALES_QTY, 0) / fcst.FCST_QTY) RATIO,
                                 NVL(sales.SALES_VALUE, 0) - NVL(fcst.FCST_VALUE, 0) "VALUE",
                                 CASE
                                     WHEN NVL(fcst.FCST_QTY, 0) > NVL(sales.SALES_QTY, 0) THEN NVL(sales.SALES_VALUE, 0)
                                     ELSE NVL(fcst.FCST_VALUE, 0) END               PREDICTED_VALUE,
                                 sales.${_type},
                                 sales.MATERIAL
                          from (SELECT t.${_type},
                                       t.MATERIAL,
                                       SUM((${fcst_month_columns}))                 FCST_QTY,
                                       SUM((${fcst_month_columns}) ${fcstColumn})   FCST_VALUE
                                  FROM SIOP_FCST_MONTH_V t
                                 WHERE t.FCST_VERSION = #{fcstVersion, jdbcType=VARCHAR}
                                   AND t.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                                <include refid="minisiop_filter"/>
                                GROUP BY t.${_type}, t.MATERIAL) fcst
                                   FULL JOIN
                               (
                                   SELECT t.${_type},
                                          t.MATERIAL,
                                          SUM(ORDER_QUANTITY) SALES_QTY,
                                          SUM(${salesColumn}) SALES_VALUE
                                     FROM SIOP_SALES_MONTH_V t
                                    WHERE t.CALENDAR_MONTH BETWEEN GREATEST(#{fcstVersion, jdbcType=VARCHAR}, #{dateRange[0], jdbcType=VARCHAR}) AND #{dateRange[1], jdbcType=VARCHAR}
                                      AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                                   <include refid="minisiop_filter"/>
                                   GROUP BY t.${_type}, t.MATERIAL
                               ) sales
                               ON sales.${_type} = fcst.${_type} and sales.MATERIAL = fcst.MATERIAL
                    ) mm
                ) nn
        GROUP BY RATIO_LEVEL, ${_type}
    </select>

    <select id="querySummary_Demand_ActualSalesBeforeSF_ForMiniSIOPMeeting" resultType="java.util.Map">
        SELECT T.${_type}, SUM(${salesColumn}) "VALUE"
        FROM SIOP_SALES_MONTH_V t
        WHERE t.CALENDAR_MONTH >= #{dateRange[0], jdbcType=VARCHAR}
        AND t.CALENDAR_MONTH &lt; #{fcstVersion, jdbcType=VARCHAR}
        AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
        <include refid="minisiop_filter"/>
        GROUP BY T.${_type}
    </select>

    <select id="querySummary_STDOI_IGOI_ForMiniSIOPMeeting" resultType="java.util.Map">
        SELECT T.${_type}, SUM(${salesColumn}) "VALUE"
          FROM SIOP_ORDER_INTAKE_MONTH_V t
         WHERE t.SE_SCOPE IN ('SECI_IG', 'SEHK_IG')
           AND t.CALENDAR_MONTH BETWEEN #{dateRange[0], jdbcType=VARCHAR} AND #{dateRange[1], jdbcType=VARCHAR}
        <include refid="minisiop_filter"/>
         GROUP BY T.${_type}
    </select>

    <select id="querySummary_SOHDemand_ForMiniSIOPMeeting" resultType="java.util.Map">
        WITH UNREALIZED_FCST AS (
            SELECT
                NVL(fcst.${_type}, sales.${_type})                  ${_type},
                NVL(fcst.MATERIAL, sales.MATERIAL)                  MATERIAL,
                NVL(fcst.FCST_QTY, 0) - NVL(sales.SALES_QTY, 0)     "QTY",
                NVL(fcst.FCST_VALUE, 0) - NVL(sales.SALES_VALUE, 0) "VALUE"
            FROM (
                SELECT t.${_type},
                       t.MATERIAL,
                       SUM(${fcst_month_columns})                  FCST_QTY,
                       SUM((${fcst_month_columns}) ${fcstColumn})  FCST_VALUE
                  FROM SIOP_FCST_MONTH_V t
                 WHERE t.FCST_VERSION = #{fcstVersion, jdbcType=VARCHAR}
                   AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                   <include refid="minisiop_filter"/>
                GROUP BY t.${_type}, MATERIAL
            ) fcst

            FULL JOIN

            (
                SELECT t.${_type},
                       t.MATERIAL,
                       SUM(t.ORDER_QUANTITY) SALES_QTY,
                       SUM(${salesColumn})   SALES_VALUE
                       FROM SIOP_SALES_MONTH_V t
                       WHERE t.CALENDAR_MONTH BETWEEN GREATEST(#{fcstVersion, jdbcType=VARCHAR}, #{dateRange[0], jdbcType=VARCHAR}) AND #{dateRange[1], jdbcType=VARCHAR}
                         AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                         <include refid="minisiop_filter"/>
                       GROUP BY t.${_type}, t.MATERIAL
            ) sales ON sales.${_type} = fcst.${_type} and sales.MATERIAL = fcst.MATERIAL
            WHERE NVL(sales.SALES_QTY, 0) &lt; NVL(fcst.FCST_QTY, 0)
        ),
        OPEN_CRD_IN_SP AS (
            SELECT t.${_type},
                   t.MATERIAL,
                   SUM(t.OPEN_SO_QTY) OPEN_SO_QTY,
                   SUM(${crdColumn}) OPEN_SO_VALUE
              FROM SIOP_OPEN_SO_MONTH_V t
             WHERE t.CALENDAR_MONTH &lt;= #{dateRange[1], jdbcType=VARCHAR}
               AND t.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
               <include refid="minisiop_filter"/>
             GROUP BY t.${_type}, t.MATERIAL
        ),
        DEMAND_OG AS (
            SELECT  ${_type},
                    MATERIAL,
                    MAX(QTY) "QTY",
                    MAX(VALUE) "VALUE"
              FROM (
                    SELECT NVL(T.MATERIAL, T2.MATERIAL)                    MATERIAL,
                           GREATEST(NVL(T.QTY, 0), NVL(T2.OPEN_SO_QTY, 0)) "QTY",
                           CASE
                           WHEN NVL(T.QTY, 0) > NVL(T2.OPEN_SO_QTY, 0) THEN NVL(T.VALUE, 0)
                           ELSE NVL(T2.OPEN_SO_VALUE, 0) END           "VALUE",
                           NVL(T.${_type}, T2.${_type})                    ${_type}
                      FROM UNREALIZED_FCST T FULL JOIN OPEN_CRD_IN_SP T2 ON T.${_type} = T2.${_type} AND T.MATERIAL = T2.MATERIAL
                    ) MM
             GROUP BY ${_type}, MATERIAL
        ),
        DEMAND_UD AS (
            SELECT  /*+ materialize */
                    t.${_type},
                    T.MATERIAL,
                    NVL(AVG(${priceColumn}), 0) UD_PRICE,
                    NVL(SUM(CASE WHEN t.SE_SCOPE IN ('SECI_IG', 'SEHK_IG') THEN UD_LT_7_CD  END), 0)            IG_LT_7_QTY,
                    NVL(SUM(CASE WHEN t.SE_SCOPE IN ('SECI_OG', 'SEHK_OG') THEN UD_LT_7_CD  END), 0)            OG_LT_7_QTY,
                    NVL(SUM(CASE WHEN t.SE_SCOPE IN ('SECI_IG', 'SEHK_IG') THEN UD_LT_30_CD  END), 0)           IG_LT_30_QTY,
                    NVL(SUM(CASE WHEN t.SE_SCOPE IN ('SECI_OG', 'SEHK_OG') THEN UD_LT_30_CD  END), 0)           OG_LT_30_QTY,
                    NVL(SUM(CASE WHEN t.SE_SCOPE IN ('SECI_IG', 'SEHK_IG') THEN UD_GT_30_CD  END), 0)           IG_GT_30_QTY,
                    NVL(SUM(CASE WHEN t.SE_SCOPE IN ('SECI_OG', 'SEHK_OG') THEN UD_GT_30_CD  END), 0)           OG_GT_30_QTY
              FROM  SIOP_OPEN_SO_MONTH_V t
             WHERE  t.SE_SCOPE IN ('SECI_OG', 'SECI_IG')
             <include refid="minisiop_filter"/>
             GROUP BY t.${_type}, T.MATERIAL
        ),
        SOH_SUPPLY AS (
            SELECT t.${_type},
                   T.MATERIAL,
                   SUM(t.ORDER_QUANTITY) SOH_QTY,
                   AVG(${priceColumn})   PRICE
              FROM DEMAND_SOH_V t
             WHERE 1 = 1
             <include refid="minisiop_filter2"/>
             GROUP BY t.${_type}, T.MATERIAL
        ),
        SOH_FOR_UD_LT_7CD AS (
            SELECT MM.*,
                   NVL(SOH_QTY,0) - NVL(SOH_COVER_OG_QTY,0) - NVL(SOH_COVER_IG_QTY,0)  SOH_REMAIN_QTY
              FROM (
                SELECT NVL(SUPPLY.${_type}, DEMANDS.${_type})   ${_type},
                       NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)   MATERIAL,
                       NVL(SUPPLY.SOH_QTY, 0)                   SOH_QTY,
                       NVL(SUPPLY.PRICE, 0)                     PRICE,
                       NVL(DEMANDS.UD_PRICE, SUPPLY.PRICE)      UD_PRICE,

                       CASE WHEN NVL(SUPPLY.SOH_QTY, 0) >= NVL(DEMANDS.OG_LT_7_QTY, 0) THEN NVL(DEMANDS.OG_LT_7_QTY, 0)
                            ELSE NVL(SUPPLY.SOH_QTY, 0)
                       END AS SOH_COVER_OG_QTY,

                       CASE WHEN NVL(SUPPLY.SOH_QTY, 0) &lt; NVL(DEMANDS.OG_LT_7_QTY, 0) THEN 0
                            WHEN (NVL(SUPPLY.SOH_QTY, 0) - NVL(DEMANDS.OG_LT_7_QTY, 0)) >= NVL(DEMANDS.IG_LT_7_QTY, 0) THEN NVL(DEMANDS.IG_LT_7_QTY, 0)
                            ELSE (NVL(SUPPLY.SOH_QTY, 0) - NVL(DEMANDS.OG_LT_7_QTY, 0))
                       END AS SOH_COVER_IG_QTY

                 FROM SOH_SUPPLY SUPPLY FULL JOIN DEMAND_UD DEMANDS ON SUPPLY.${_type} = DEMANDS.${_type} and SUPPLY.MATERIAL = DEMANDS.MATERIAL
            ) MM
        ),
        SOH_FOR_UD_LT_30CD AS (
             SELECT MM.*,
                    NVL(SOH_QTY,0) - NVL(SOH_COVER_OG_QTY, 0) - NVL(SOH_COVER_IG_QTY, 0)  SOH_REMAIN_QTY,
                    IG_UD_QTY0 + SOH_COVER_IG_QTY                                         IG_UD_QTY
               FROM (
                        SELECT NVL(SUPPLY.${_type}, DEMANDS.${_type})   ${_type},
                               NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)   MATERIAL,
                               NVL(SUPPLY.SOH_REMAIN_QTY, 0)            SOH_QTY,
                               NVL(SUPPLY.PRICE, 0)                     PRICE,
                               NVL(DEMANDS.UD_PRICE, SUPPLY.PRICE)      UD_PRICE,
                               NVL(SUPPLY.SOH_COVER_IG_QTY, 0)          IG_UD_QTY0,

                               CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.OG_LT_30_QTY, 0) THEN NVL(DEMANDS.OG_LT_30_QTY, 0)
                                    ELSE NVL(SUPPLY.SOH_REMAIN_QTY, 0)
                                END AS SOH_COVER_OG_QTY,

                               CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) &lt; NVL(DEMANDS.OG_LT_30_QTY, 0) THEN 0
                                    WHEN (NVL(SUPPLY.SOH_REMAIN_QTY, 0) - NVL(DEMANDS.OG_LT_30_QTY, 0)) >= NVL(DEMANDS.IG_LT_30_QTY, 0) THEN NVL(DEMANDS.IG_LT_30_QTY, 0)
                                    ELSE (NVL(SUPPLY.SOH_REMAIN_QTY, 0) - NVL(DEMANDS.OG_LT_30_QTY, 0))
                                END AS SOH_COVER_IG_QTY

                        FROM SOH_FOR_UD_LT_7CD SUPPLY FULL JOIN DEMAND_UD DEMANDS ON SUPPLY.${_type} = DEMANDS.${_type} AND SUPPLY.MATERIAL = DEMANDS.MATERIAL
            ) MM
        ),
        SOH_FOR_UD_GT_30CD AS (
            SELECT  MM.*,
                    NVL(SOH_QTY,0) - NVL(SOH_COVER_OG_QTY,0) - NVL(SOH_COVER_IG_QTY,0)  SOH_REMAIN_QTY,
                    IG_UD_QTY0 + SOH_COVER_IG_QTY                                       IG_UD_QTY
              FROM (
                SELECT  NVL(SUPPLY.${_type}, DEMANDS.${_type})   ${_type},
                        NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)   MATERIAL,
                        NVL(SUPPLY.SOH_REMAIN_QTY, 0)            SOH_QTY,
                        NVL(SUPPLY.PRICE, 0)                     PRICE,
                        NVL(DEMANDS.UD_PRICE, SUPPLY.PRICE)      UD_PRICE,
                        NVL(SUPPLY.IG_UD_QTY, 0)                 IG_UD_QTY0,

                        CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.OG_GT_30_QTY, 0) THEN NVL(DEMANDS.OG_GT_30_QTY, 0)
                             ELSE NVL(SUPPLY.SOH_REMAIN_QTY, 0)
                        END AS SOH_COVER_OG_QTY,

                        CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) &lt; NVL(DEMANDS.OG_GT_30_QTY, 0) THEN 0
                             WHEN (NVL(SUPPLY.SOH_REMAIN_QTY, 0) - NVL(DEMANDS.OG_GT_30_QTY, 0)) >= NVL(DEMANDS.IG_GT_30_QTY, 0) THEN NVL(DEMANDS.IG_GT_30_QTY, 0)
                             ELSE (NVL(SUPPLY.SOH_REMAIN_QTY, 0) - NVL(DEMANDS.OG_GT_30_QTY, 0))
                        END AS SOH_COVER_IG_QTY

                 FROM SOH_FOR_UD_LT_30CD SUPPLY FULL JOIN DEMAND_UD DEMANDS ON SUPPLY.${_type} = DEMANDS.${_type} AND SUPPLY.MATERIAL = DEMANDS.MATERIAL
            ) MM
        ),
        SOH_FOR_DEMAND_SP AS (
            SELECT  MM.*,
                    NVL(SOH_QTY,0) - NVL(SOH_COVER_QTY,0)   SOH_REMAIN_QTY
              FROM (
                SELECT  NVL(SUPPLY.${_type}, DEMANDS.${_type})                   ${_type},
                        NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)                   MATERIAL,
                        NVL(SUPPLY.SOH_REMAIN_QTY, 0)                            SOH_QTY,
                        DECODE(DEMANDS.QTY, 0, 0, DEMANDS.VALUE / DEMANDS.QTY)   DEMAND_PRICE,

                        CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN NVL(DEMANDS.QTY, 0)
                        ELSE NVL(SUPPLY.SOH_REMAIN_QTY, 0) END AS SOH_COVER_QTY

                  FROM SOH_FOR_UD_GT_30CD SUPPLY FULL JOIN DEMAND_OG DEMANDS ON SUPPLY.${_type} = DEMANDS.${_type} AND SUPPLY.MATERIAL = DEMANDS.MATERIAL
            ) MM
        ),
        IG_ORDER_DEMAND AS (
            SELECT  ${_type},
                    MATERIAL,
                    SUM(t.OPEN_SO_QTY)   QTY,
                    AVG(${priceColumn})  ORDER_PRICE
              FROM SIOP_OPEN_SO_MONTH_V t
             WHERE t.SE_SCOPE IN ('SECI_IG', 'SEHK_IG')
             <include refid="minisiop_filter"/>
             GROUP BY t.${_type}, MATERIAL
        ),
        SOH_FOR_IG_ORDER AS (
            SELECT  MM.*,
                    NVL(SOH_QTY,0) - NVL(SOH_COVER_QTY,0)   SOH_REMAIN_QTY
            FROM (
                SELECT  NVL(SUPPLY.${_type}, DEMANDS.${_type})   ${_type},
                        NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)   MATERIAL,
                        NVL(SUPPLY.SOH_REMAIN_QTY, 0)            SOH_QTY,
                        NVL(DEMANDS.ORDER_PRICE, 0)              ORDER_PRICE,

                        CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN NVL(DEMANDS.QTY, 0)
                             ELSE NVL(SUPPLY.SOH_REMAIN_QTY, 0) END AS SOH_COVER_QTY

                  FROM SOH_FOR_DEMAND_SP SUPPLY FULL JOIN IG_ORDER_DEMAND DEMANDS ON SUPPLY.${_type} = DEMANDS.${_type} AND SUPPLY.MATERIAL = DEMANDS.MATERIAL
            ) MM
        ),
        SS_DEMAND AS (
            SELECT  t.${_type},
                    MATERIAL,
                    SUM(t.SAFETY_STOCK)   QTY,
                    AVG(${priceColumn}) PRICE
              FROM  DEMAND_SOH_V t
             WHERE 1 = 1
             <include refid="minisiop_filter2"/>
             GROUP BY t.${_type}, MATERIAL
        ),
        SOH_FOR_SS AS (
            SELECT  MM.*,
                    NVL(SOH_QTY,0) - NVL(SOH_COVER_QTY,0)   SOH_REMAIN_QTY
              FROM (
                    SELECT  NVL(SUPPLY.${_type}, DEMANDS.${_type})   ${_type},
                            NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)   MATERIAL,
                            NVL(SUPPLY.SOH_REMAIN_QTY, 0)            SOH_QTY,
                            NVL(DEMANDS.PRICE, 0)                    DEMAND_PRICE,

                            CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN NVL(DEMANDS.QTY, 0)
                                 ELSE NVL(SUPPLY.SOH_REMAIN_QTY, 0) END AS SOH_COVER_QTY

                      FROM SOH_FOR_IG_ORDER SUPPLY FULL JOIN SS_DEMAND DEMANDS ON SUPPLY.${_type} = DEMANDS.${_type} AND SUPPLY.MATERIAL = DEMANDS.MATERIAL
            ) MM
        ),
        AGING_DEMAND AS (
            SELECT  t.${_type},
                    T.MATERIAL,
                    SUM(t.STOCK_1Y_2Y)  AGING_1Y_2Y_QTY,
                    SUM(t.STOCK_GT_2Y)  AGING_GT_2Y_QTY,
                    AVG(${priceColumn}) PRICE
              FROM DEMAND_SOH_V t
             WHERE 1 = 1
             <include refid="minisiop_filter2"/>
             GROUP BY t.${_type}, MATERIAL
        ),
        SOH_FOR_AGING AS (
            SELECT  MM.*,
                    NVL(SOH_QTY,0) - NVL(SOH_COVER_AGING1_QTY,0) - NVL(SOH_COVER_AGING2_QTY,0)  SOH_REMAIN_QTY
            FROM (
                SELECT  NVL(SUPPLY.${_type}, DEMANDS.${_type})   ${_type},
                        NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)   MATERIAL,
                        NVL(SUPPLY.SOH_REMAIN_QTY, 0)            SOH_QTY,
                        NVL(DEMANDS.PRICE, 0)                    DEMAND_PRICE,

                        CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.AGING_1Y_2Y_QTY, 0) THEN NVL(AGING_1Y_2Y_QTY, 0)
                             ELSE NVL(SUPPLY.SOH_REMAIN_QTY, 0)
                        END AS SOH_COVER_AGING1_QTY,

                        CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) &lt; NVL(DEMANDS.AGING_1Y_2Y_QTY, 0) THEN 0
                             WHEN (NVL(SUPPLY.SOH_REMAIN_QTY, 0) - NVL(DEMANDS.AGING_1Y_2Y_QTY, 0)) >= NVL(DEMANDS.AGING_GT_2Y_QTY, 0) THEN NVL(DEMANDS.AGING_GT_2Y_QTY, 0)
                             ELSE (NVL(SUPPLY.SOH_REMAIN_QTY, 0) - NVL(DEMANDS.AGING_1Y_2Y_QTY, 0))
                        END AS SOH_COVER_AGING2_QTY

                FROM SOH_FOR_SS SUPPLY FULL JOIN AGING_DEMAND DEMANDS ON SUPPLY.${_type} = DEMANDS.${_type} AND SUPPLY.MATERIAL = DEMANDS.MATERIAL
            ) MM
        )

        SELECT 'OG_UD_LT_7_CD' AS "TYPE", ${_type}, SUM(SOH_COVER_OG_QTY * UD_PRICE) AS "VALUE" FROM SOH_FOR_UD_LT_7CD GROUP BY ${_type}

        UNION ALL

        SELECT 'OG_UD_LT_30_CD', ${_type}, SUM(SOH_COVER_OG_QTY * UD_PRICE) FROM SOH_FOR_UD_LT_30CD GROUP BY ${_type}

        UNION ALL

        SELECT 'OG_UD_GT_30_CD', ${_type}, SUM(SOH_COVER_OG_QTY * UD_PRICE) FROM SOH_FOR_UD_GT_30CD GROUP BY ${_type}

        UNION ALL

        SELECT 'IG_UD', ${_type}, SUM(IG_UD_QTY * UD_PRICE) FROM SOH_FOR_UD_GT_30CD GROUP BY ${_type}

        UNION ALL

        SELECT 'FOR_DEMAND_IN_SELECTED_PERIOD', ${_type}, SUM(SOH_COVER_QTY * DEMAND_PRICE) FROM SOH_FOR_DEMAND_SP GROUP BY ${_type}

        UNION ALL

        SELECT 'FOR_IG_ORDER_IN_SELECTED_PERIOD', ${_type}, SUM(SOH_COVER_QTY * ORDER_PRICE) FROM SOH_FOR_IG_ORDER GROUP BY ${_type}

        UNION ALL

        SELECT 'FOR_SAFETY_STOCK', ${_type}, SUM(SOH_COVER_QTY * DEMAND_PRICE) FROM SOH_FOR_SS GROUP BY ${_type}

        UNION ALL

        SELECT 'STOCK_AGING_GT_1Y', ${_type}, SUM(SOH_COVER_AGING1_QTY * DEMAND_PRICE) FROM SOH_FOR_AGING GROUP BY ${_type}

        UNION ALL

        SELECT 'STOCK_AGING_GT_2Y', ${_type}, SUM(SOH_COVER_AGING2_QTY * DEMAND_PRICE) FROM SOH_FOR_AGING GROUP BY ${_type}

        UNION ALL

        SELECT 'FOR_FUTURE_DEMAND', ${_type}, SUM(SOH_REMAIN_QTY * DEMAND_PRICE) FROM SOH_FOR_AGING GROUP BY ${_type}
    </select>

    <select id="querySummary_GITDemand_ForMiniSIOPMeeting" resultType="java.util.Map">
        WITH UNREALIZED_FCST AS (
             SELECT
                NVL(fcst.${_type}, sales.${_type})                  ${_type},
                NVL(fcst.MATERIAL, sales.MATERIAL)                  MATERIAL,
                NVL(fcst.FCST_QTY, 0) - NVL(sales.SALES_QTY, 0)     "QTY",
                NVL(fcst.FCST_VALUE, 0) - NVL(sales.SALES_VALUE, 0) "VALUE"
             FROM (
                     SELECT t.${_type},
                            t.MATERIAL,
                            SUM(${fcst_month_columns})                  FCST_QTY,
                            SUM((${fcst_month_columns}) ${fcstColumn})  FCST_VALUE
                       FROM SIOP_FCST_MONTH_V t
                      WHERE t.FCST_VERSION = #{fcstVersion, jdbcType=VARCHAR}
                        AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                        <include refid="minisiop_filter"/>
                      GROUP BY t.${_type}, t.MATERIAL
                ) fcst

                FULL JOIN

                (
                     SELECT t.${_type},
                            t.MATERIAL,
                            SUM(t.ORDER_QUANTITY) SALES_QTY,
                            SUM(${salesColumn})  SALES_VALUE
                      FROM SIOP_SALES_MONTH_V t
                     WHERE t.CALENDAR_MONTH BETWEEN GREATEST(#{fcstVersion, jdbcType=VARCHAR}, #{dateRange[0], jdbcType=VARCHAR}) AND #{dateRange[1], jdbcType=VARCHAR}
                       AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                       <include refid="minisiop_filter"/>
                     GROUP BY t.${_type}, t.MATERIAL
                ) sales ON sales.${_type} = fcst.${_type} and sales.MATERIAL = fcst.MATERIAL
             WHERE NVL(sales.SALES_QTY, 0) &lt; NVL(fcst.FCST_QTY, 0)
             ),
             TOTAL_UD AS (
                SELECT  t.${_type},
                        t.MATERIAL,
                        SUM(NVL(UD_GT_30_CD, 0) + NVL(UD_LT_30_CD, 0) + NVL(UD_LT_7_CD, 0))                                  "QTY",
                        SUM(NVL(UD_GT_30_CD${UDSubfix}, 0) + NVL(UD_LT_30_CD${UDSubfix}, 0) + NVL(UD_LT_7_CD${UDSubfix}, 0)) "VALUE"
                  FROM  SIOP_OPEN_SO_MONTH_V t
                 WHERE  1 = 1
                 <include refid="minisiop_filter"/>
                 GROUP BY t.${_type}, t.MATERIAL
             ),
             OPEN_CRD_IN_SP AS (
                SELECT ${_type},
                       MATERIAL,
                       SUM(OPEN_SO_QTY)  "QTY",
                       SUM(${crdColumn}) "VALUE"
                  FROM SIOP_OPEN_SO_MONTH_V t
                 WHERE t.CALENDAR_MONTH &lt;= #{dateRange[1], jdbcType=VARCHAR}
                   AND t.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                <include refid="minisiop_filter"/>
                 GROUP BY ${_type}, MATERIAL
             ),
             DEMAND_TEMP AS (
                SELECT ${_type},
                       MATERIAL,
                       MAX(QTY) "QTY",
                       MAX(VALUE) "VALUE"
                FROM (
                    SELECT COALESCE(T.${_type}, T2.${_type}, T3.${_type}) ${_type},
                           COALESCE(T.MATERIAL, T2.MATERIAL, T3.MATERIAL) MATERIAL,
                           GREATEST(NVL(T.QTY, 0),NVL(T2.QTY, 0),NVL(T3.QTY, 0)) AS QTY,
                           CASE WHEN NVL(T.QTY, 0)  >= NVL(T2.QTY, 0) AND NVL(T.QTY, 0) >= NVL(T3.QTY, 0) THEN T.VALUE
                                WHEN NVL(T2.QTY, 0) >= NVL(T.QTY, 0) AND NVL(T2.QTY, 0) >= NVL(T3.QTY, 0) THEN T2.VALUE
                                WHEN NVL(T3.QTY, 0) >= NVL(T.QTY, 0) AND NVL(T3.QTY, 0) >= NVL(T2.QTY, 0) THEN T3.VALUE
                           END AS "VALUE"
                      FROM UNREALIZED_FCST T FULL JOIN TOTAL_UD T2 ON T.${_type} = T2.${_type} AND T.MATERIAL = T2.MATERIAL
                                             FULL JOIN OPEN_CRD_IN_SP T3 ON T.${_type} = T3.${_type} AND T.MATERIAL = T3.MATERIAL
                ) MM GROUP BY ${_type}, MATERIAL
             ),
             SUPPLY_TEMP AS (
                SELECT NVL(T.${_type}, T2.${_type})                    ${_type},
                       NVL(T.MATERIAL, T2.MATERIAL)                    MATERIAL,
                       NVL(T.ORDER_QUANTITY, 0)                        SOH_QTY,
                       NVL(T2.QUANTITY, 0)                             LA_QTY,
                       NVL(T2.AVG_SELLING_PRICE_RMB, T.AVG_SELLING_PRICE_RMB)  AVG_SELLING_PRICE_RMB,
                       NVL(T2.UNIT_COST, T.UNIT_COST)                  UNIT_COST
                  FROM (
                          SELECT T.${_type},
                                 t.MATERIAL,
                                 SUM(T.ORDER_QUANTITY)    ORDER_QUANTITY,
                                 AVG(T.AVG_SELLING_PRICE_RMB) AVG_SELLING_PRICE_RMB,
                                 AVG(T.UNIT_COST)         UNIT_COST
                           FROM DEMAND_SOH_V T
                          WHERE 1 = 1
                          <include refid="minisiop_filter2"/>
                          GROUP BY T.${_type}, t.MATERIAL
                        ) T FULL JOIN
                       (SELECT ${_type},
                               MATERIAL,
                               SUM(QUANTITY)          QUANTITY,
                               AVG(AVG_SELLING_PRICE_RMB) AVG_SELLING_PRICE_RMB,
                               AVG(UNIT_COST)         UNIT_COST
                          FROM SIOP_LA_AB_MONTH_V T
                         WHERE CATEGORY IN ('LA','ZA')
                           AND CALENDAR_MONTH &lt;= #{dateRange[1], jdbcType=VARCHAR}
                           <include refid="minisiop_filter2"/>
                         GROUP BY ${_type}, MATERIAL
                       ) T2
                       ON T.${_type} = T2.${_type} and t.MATERIAL = t2.MATERIAL
             ),
             LA_FOR_DEMAND_IN_SP AS (
                SELECT MM.*,
                       NVL(SOH_QTY,0) - NVL(SOH_COVER_QTY,0)   SOH_REMAIN_QTY,
                       NVL(LA_QTY,0) - NVL(LA_COVER_QTY,0)     LA_REMAIN_QTY
                FROM (
                    SELECT NVL(SUPPLY.${_type}, DEMANDS.${_type})                   ${_type},
                           NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)                   MATERIAL,
                           NVL(SUPPLY.SOH_QTY, 0)                                   SOH_QTY,
                           NVL(SUPPLY.LA_QTY, 0)                                    LA_QTY,
                           NVL(${priceColumn}, 0)                                   PRICE,
                           DECODE(DEMANDS.QTY, 0, 0, DEMANDS.VALUE / DEMANDS.QTY)   DEMAND_PRICE,

                           CASE WHEN NVL(SUPPLY.SOH_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN 0
                                WHEN (NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_QTY, 0)) >= NVL(SUPPLY.LA_QTY, 0) THEN NVL(SUPPLY.LA_QTY, 0)
                                WHEN (NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_QTY, 0)) &lt; NVL(SUPPLY.LA_QTY, 0) THEN NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_QTY, 0)
                            END AS LA_COVER_QTY,

                           CASE WHEN NVL(SUPPLY.SOH_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN NVL(DEMANDS.QTY, 0)
                                ELSE NVL(SUPPLY.SOH_QTY, 0) END AS SOH_COVER_QTY

                      FROM SUPPLY_TEMP SUPPLY FULL JOIN DEMAND_TEMP DEMANDS ON SUPPLY.${_type} = DEMANDS.${_type} AND SUPPLY.MATERIAL = DEMANDS.MATERIAL
                ) MM
             )

         SELECT  ${_type}, SUM(LA_COVER_QTY * DEMAND_PRICE) AS "VALUE" FROM LA_FOR_DEMAND_IN_SP GROUP BY ${_type}

    </select>

    <select id="querySummary_OrderedDemand_ForMiniSIOPMeeting" resultType="java.util.Map">
        SELECT T.${_type},
               SUM(BACK_ORDER${backOrderSubfix}) BACK_ORDER_VALUE,
               SUM(CASE WHEN t.CALENDAR_MONTH &lt;= #{dateRange[1], jdbcType=VARCHAR} THEN ${crdColumn} ELSE 0 END) CRDINSP_VALUE,
               SUM(CASE WHEN t.CALENDAR_MONTH > #{dateRange[1], jdbcType=VARCHAR} THEN ${crdColumn} ELSE 0 END) CRDOUTSP_VALUE
        FROM SIOP_OPEN_SO_MONTH_V t
        WHERE T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
        <include refid="minisiop_filter"/>
        GROUP BY T.${_type}
    </select>

    <select id="querySummary_ConfResDemand_ForMiniSIOPMeeting" resultType="java.util.Map">
         WITH UNREALIZED_FCST AS (
             SELECT
                NVL(fcst.${_type}, sales.${_type})                  ${_type},
                NVL(fcst.MATERIAL, sales.MATERIAL)                  MATERIAL,
                NVL(fcst.FCST_QTY, 0) - NVL(sales.SALES_QTY, 0)     "QTY",
                NVL(fcst.FCST_VALUE, 0) - NVL(sales.SALES_VALUE, 0) "VALUE"
             FROM (
                     SELECT t.${_type},
                            t.MATERIAL,
                            SUM(${fcst_month_columns})                  FCST_QTY,
                            SUM((${fcst_month_columns}) ${fcstColumn})  FCST_VALUE
                       FROM SIOP_FCST_MONTH_V t
                      WHERE t.FCST_VERSION = #{fcstVersion, jdbcType=VARCHAR}
                        AND t.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                        <include refid="minisiop_filter"/>
                      GROUP BY t.${_type}, t.MATERIAL
                ) fcst

                FULL JOIN

                (
                     SELECT t.${_type},
                            t.MATERIAL,
                            SUM(t.ORDER_QUANTITY) SALES_QTY,
                            SUM(${salesColumn})  SALES_VALUE
                      FROM SIOP_SALES_MONTH_V t
                     WHERE t.CALENDAR_MONTH BETWEEN GREATEST(#{fcstVersion, jdbcType=VARCHAR}, #{dateRange[0], jdbcType=VARCHAR}) AND #{dateRange[1], jdbcType=VARCHAR}
                       AND T.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                       <include refid="minisiop_filter"/>
                     GROUP BY t.${_type}, t.MATERIAL
                ) sales ON sales.${_type} = fcst.${_type} and sales.MATERIAL = fcst.MATERIAL
             WHERE NVL(sales.SALES_QTY, 0) &lt; NVL(fcst.FCST_QTY, 0)
             ),
             TOTAL_UD AS (
                SELECT  t.${_type},
                        t.MATERIAL,
                        SUM(NVL(UD_GT_30_CD, 0) + NVL(UD_LT_30_CD, 0) + NVL(UD_LT_7_CD, 0))                                  "QTY",
                        SUM(NVL(UD_GT_30_CD${UDSubfix}, 0) + NVL(UD_LT_30_CD${UDSubfix}, 0) + NVL(UD_LT_7_CD${UDSubfix}, 0)) "VALUE"
                  FROM  SIOP_OPEN_SO_MONTH_V t
                 WHERE  1 = 1
                 <include refid="minisiop_filter"/>
                 GROUP BY t.${_type}, t.MATERIAL
             ),
             OPEN_CRD_IN_SP AS (
                SELECT ${_type},
                       MATERIAL,
                       SUM(OPEN_SO_QTY)  "QTY",
                       SUM(${crdColumn}) "VALUE"
                  FROM SIOP_OPEN_SO_MONTH_V t
                 WHERE t.CALENDAR_MONTH &lt;= #{dateRange[1], jdbcType=VARCHAR}
                   AND t.SE_SCOPE IN ('SECI_OG', 'SEHK_OG')
                <include refid="minisiop_filter"/>
                 GROUP BY ${_type}, MATERIAL
             ),
             DEMAND_TEMP AS (
                SELECT ${_type},
                       MATERIAL,
                       MAX(QTY) "QTY",
                       MAX(VALUE) "VALUE"
                FROM (
                    SELECT COALESCE(T.${_type}, T2.${_type}, T3.${_type}) ${_type},
                           COALESCE(T.MATERIAL, T2.MATERIAL, T3.MATERIAL) MATERIAL,
                           GREATEST(NVL(T.QTY, 0),NVL(T2.QTY, 0),NVL(T3.QTY, 0)) AS QTY,
                           CASE WHEN NVL(T.QTY, 0)  >= NVL(T2.QTY, 0) AND NVL(T.QTY, 0) >= NVL(T3.QTY, 0) THEN T.VALUE
                                WHEN NVL(T2.QTY, 0) >= NVL(T.QTY, 0) AND NVL(T2.QTY, 0) >= NVL(T3.QTY, 0) THEN T2.VALUE
                                WHEN NVL(T3.QTY, 0) >= NVL(T.QTY, 0) AND NVL(T3.QTY, 0) >= NVL(T2.QTY, 0) THEN T3.VALUE
                           END AS "VALUE"
                      FROM UNREALIZED_FCST T FULL JOIN TOTAL_UD T2 ON T.${_type} = T2.${_type} and t.MATERIAL = t2.MATERIAL
                                             FULL JOIN OPEN_CRD_IN_SP T3 ON T.${_type} = T3.${_type} and t.MATERIAL = t3.MATERIAL
                ) MM GROUP BY ${_type}, MATERIAL
             ),
             SUPPLY_TEMP AS (
                SELECT NVL(T.${_type}, T2.${_type})                    ${_type},
                       NVL(T.MATERIAL, T2.MATERIAL)                    MATERIAL,
                       NVL(T.ORDER_QUANTITY, 0)                        SOH_QTY,
                       NVL(T2.QUANTITY, 0)                             LA_QTY,
                       NVL(T2.AVG_SELLING_PRICE_RMB, T.AVG_SELLING_PRICE_RMB)  AVG_SELLING_PRICE_RMB,
                       NVL(T2.UNIT_COST, T.UNIT_COST)                  UNIT_COST
                  FROM (
                          SELECT T.${_type},
                                 t.MATERIAL,
                                 SUM(T.ORDER_QUANTITY)    ORDER_QUANTITY,
                                 AVG(T.AVG_SELLING_PRICE_RMB) AVG_SELLING_PRICE_RMB,
                                 AVG(T.UNIT_COST)         UNIT_COST
                           FROM DEMAND_SOH_V T
                          WHERE 1 = 1
                          <include refid="minisiop_filter2"/>
                          GROUP BY T.${_type}, t.MATERIAL
                        ) T FULL JOIN
                       (SELECT ${_type},
                               MATERIAL,
                               SUM(QUANTITY)          QUANTITY,
                               AVG(AVG_SELLING_PRICE_RMB) AVG_SELLING_PRICE_RMB,
                               AVG(UNIT_COST)         UNIT_COST
                          FROM SIOP_LA_AB_MONTH_V T
                         WHERE CALENDAR_MONTH &lt;= #{dateRange[1], jdbcType=VARCHAR}
                           <include refid="minisiop_filter2"/>
                         GROUP BY ${_type}, MATERIAL
                       ) T2
                       ON T.${_type} = T2.${_type} and t.MATERIAL = t2.MATERIAL
             ),
             LA_FOR_DEMAND_IN_SP AS (
                SELECT MM.*,
                       NVL(SOH_QTY,0) - NVL(SOH_COVER_QTY,0)   SOH_REMAIN_QTY,
                       NVL(LA_QTY,0) - NVL(LA_COVER_QTY,0)     LA_REMAIN_QTY
                FROM (
                    SELECT NVL(SUPPLY.${_type}, DEMANDS.${_type})                   ${_type},
                           NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)                   MATERIAL,
                           NVL(SUPPLY.SOH_QTY, 0)                                   SOH_QTY,
                           NVL(SUPPLY.LA_QTY, 0)                                    LA_QTY,
                           NVL(${priceColumn}, 0)                                   PRICE,
                           DECODE(DEMANDS.QTY, 0, 0, DEMANDS.VALUE / DEMANDS.QTY)   DEMAND_PRICE,

                           CASE WHEN NVL(SUPPLY.SOH_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN 0
                                WHEN (NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_QTY, 0)) >= NVL(SUPPLY.LA_QTY, 0) THEN NVL(SUPPLY.LA_QTY, 0)
                                WHEN (NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_QTY, 0)) &lt; NVL(SUPPLY.LA_QTY, 0) THEN NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_QTY, 0)
                            END AS LA_COVER_QTY,

                           CASE WHEN NVL(SUPPLY.SOH_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN NVL(DEMANDS.QTY, 0)
                                ELSE NVL(SUPPLY.SOH_QTY, 0) END AS SOH_COVER_QTY

                      FROM SUPPLY_TEMP SUPPLY FULL JOIN DEMAND_TEMP DEMANDS ON SUPPLY.${_type} = DEMANDS.${_type} and SUPPLY.MATERIAL = DEMANDS.MATERIAL
                ) MM
             ),
             OPENSO_IG_DEMAND_TEMP AS (
                 SELECT ${_type},
                        MATERIAL,
                        SUM(t.OPEN_SO_QTY - UD_GT_30_CD - UD_LT_30_CD - UD_LT_7_CD)                                 "QTY",
                        SUM(${crdColumn} - UD_GT_30_CD${UDSubfix} - UD_LT_30_CD${UDSubfix} - UD_LT_7_CD${UDSubfix}) "VALUE",
                        AVG(${priceColumn}) OPENSO_PRICE
                   FROM SIOP_OPEN_SO_MONTH_V t
                  WHERE t.SE_SCOPE IN ('SECI_IG', 'SEHK_IG')
                  <include refid="minisiop_filter"/>
                  GROUP BY t.${_type}, t.MATERIAL
             ),
             LA_FOR_IG_ORDER AS (
                SELECT MM.*,
                       NVL(SOH_QTY,0) - NVL(SOH_COVER_QTY,0)   SOH_REMAIN_QTY,
                       NVL(LA_QTY,0) - NVL(LA_COVER_QTY,0)     LA_REMAIN_QTY
                  FROM (
                    SELECT NVL(SUPPLY.${_type}, DEMANDS.${_type})   ${_type},
                           NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)   MATERIAL,
                           NVL(SUPPLY.SOH_REMAIN_QTY, 0)            SOH_QTY,
                           NVL(SUPPLY.LA_REMAIN_QTY, 0)             LA_QTY,
                           NVL(SUPPLY.PRICE, DEMANDS.OPENSO_PRICE)  PRICE,
                           NVL(DEMANDS.OPENSO_PRICE, SUPPLY.PRICE)  OPENSO_PRICE,

                           CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN 0
                                WHEN (NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_REMAIN_QTY, 0)) >= NVL(SUPPLY.LA_REMAIN_QTY, 0) THEN NVL(SUPPLY.LA_REMAIN_QTY, 0)
                                ELSE NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_REMAIN_QTY, 0)
                            END AS LA_COVER_QTY,

                           CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN NVL(DEMANDS.QTY, 0)
                                ELSE NVL(SUPPLY.SOH_REMAIN_QTY, 0) END SOH_COVER_QTY

                      FROM LA_FOR_DEMAND_IN_SP SUPPLY
                      FULL JOIN OPENSO_IG_DEMAND_TEMP DEMANDS ON SUPPLY.${_type} = DEMANDS.${_type} AND SUPPLY.MATERIAL = DEMANDS.MATERIAL
                ) MM
             ),
             SS_DEMAND AS (
                SELECT T.${_type},
                       T.MATERIAL,
                       SUM(T.SAFETY_STOCK) QTY,
                       AVG(${priceColumn}) PRICE
                FROM DEMAND_SOH_V T
                where 1 = 1
                <include refid="minisiop_filter2"/>
                GROUP BY T.${_type}, T.MATERIAL
             ),
             LA_FOR_SS AS (
                SELECT MM.*,
                       NVL(LA_QTY,0) - NVL(LA_COVER_QTY,0)     LA_REMAIN_QTY
                  FROM (
                    SELECT NVL(SUPPLY.${_type}, DEMANDS.${_type})   ${_type},
                           NVL(SUPPLY.MATERIAL, DEMANDS.MATERIAL)   MATERIAL,
                           NVL(SUPPLY.LA_REMAIN_QTY, 0)             LA_QTY,
                           NVL(SUPPLY.PRICE, DEMANDS.PRICE)         PRICE,

                           CASE WHEN NVL(SUPPLY.SOH_REMAIN_QTY, 0) >= NVL(DEMANDS.QTY, 0) THEN 0
                                WHEN (NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_REMAIN_QTY, 0)) >= NVL(SUPPLY.LA_REMAIN_QTY, 0) THEN NVL(SUPPLY.LA_REMAIN_QTY, 0)
                                ELSE NVL(DEMANDS.QTY, 0) - NVL(SUPPLY.SOH_REMAIN_QTY, 0)
                            END AS LA_COVER_QTY

                      FROM LA_FOR_IG_ORDER SUPPLY FULL JOIN SS_DEMAND DEMANDS ON SUPPLY.${_type} = DEMANDS.${_type} AND SUPPLY.MATERIAL = DEMANDS.MATERIAL
                ) MM
            )

         SELECT 'LA_FOR_DEMAND_IN_SP' AS TYPE, ${_type}, SUM(LA_COVER_QTY * DEMAND_PRICE) AS "VALUE" FROM LA_FOR_DEMAND_IN_SP GROUP BY ${_type}

         UNION ALL

         SELECT 'LA_FOR_IG_ORDER', ${_type}, SUM(LA_COVER_QTY * OPENSO_PRICE) FROM LA_FOR_IG_ORDER GROUP BY ${_type}

         UNION ALL

         SELECT 'LA_FOR_SS', ${_type}, SUM(LA_COVER_QTY * PRICE) FROM LA_FOR_SS GROUP BY ${_type}

         UNION ALL

         SELECT 'LA_FOR_FURTHER_DEMAND', ${_type}, SUM(LA_REMAIN_QTY * PRICE) FROM LA_FOR_SS GROUP BY ${_type}
    </select>
</mapper>
