<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.demand.dao.IOpenSoStructureDao">
    <sql id="openSOFilter">
        <if test="_filters != null and _filters != ''.toString()">
            and ${_filters}
        </if>
        <if test="scopeFilter != null and scopeFilter != ''.toString()">
            and ${scopeFilter}
        </if>
        <if test="treePathFilter != null and treePathFilter != ''.toString()">
            and ${treePathFilter}
        </if>
        <if test="personalFilters != null and personalFilters != ''.toString()">
            and ${personalFilters}
        </if>
        <if test="dateColumnRange != null and dateColumnRange.size() > 0">
            AND T.${dateColumn} BETWEEN TO_DATE(#{dateColumnRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
            AND TRUNC(TO_DATE(#{dateColumnRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
        </if>
    </sql>

    <sql id="openSOHistFilter">
        <if test="_filters != null and _filters != ''.toString()">
            and ${_filters}
        </if>
        <if test="scopeFilter != null and scopeFilter != ''.toString()">
            and ${scopeFilter}
        </if>
        <if test="treePathFilter != null and treePathFilter != ''.toString()">
            and ${treePathFilter}
        </if>
        <if test="personalFilters != null and personalFilters != ''.toString()">
            and ${personalFilters}
        </if>
        <if test="dateColumnRange != null and dateColumnRange.size() > 0">
            AND T.${dateColumn} BETWEEN TO_DATE(#{dateColumnRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
            AND TRUNC(TO_DATE(#{dateColumnRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'), 'dd')
        </if>
    </sql>

    <sql id="decodeName">
        DECODE(
        T.DELIVERY_RANGE,
        'P >6M', 'a',
        'P 3-6M', 'b',
        'P 2-3M', 'c',
        'P 1-2M', 'd',
        'P 14-30D', 'e',
        'P 7-14D', 'f',
        'P 3-7D', 'g',
        'P 0-3D', 'h',
        'F 0-3D', 'i',
        'F 3-7D', 'j',
        'F 7-14D', 'k',
        'F 14-30D', 'l',
        'F 1-2M', 'm',
        'F 2-3M', 'n',
        'F 3-6M', 'o',
        'F >6M', 'p',
        'Others', 'z',
        T.DELIVERY_RANGE)
    </sql>

    <select id="queryCascader" resultType="java.util.Map">
        SELECT NAME, CATEGORY FROM OPEN_SO_STRUCTURE_FILTER_V T ORDER BY CATEGORY, NLSSORT(decode(t.NAME, 'Others', 'zzz', t.NAME), 'NLS_SORT = SCHINESE_PINYIN_M')
    </select>

    <resultMap id="columnMap" type="java.lang.String">
        <result column="COLUMN_NAME"/>
    </resultMap>

    <select id="queryDateColumns" resultMap="columnMap" resultType="java.util.List">
        SELECT COLUMN_NAME
        FROM USER_TAB_COLS
        WHERE TABLE_NAME IN ('OPEN_SO_STRUCTURE_V', 'OPEN_SO_STRUCTURE_HIST')
          AND DATA_TYPE = 'DATE'
        GROUP BY COLUMN_NAME
        HAVING COUNT(1) = 2
        ORDER BY COLUMN_NAME
    </select>

    <select id="queryReport1" resultType="java.util.Map">
        SELECT SUM(DECODE(T.ONTIME, '#', ${valueColumn})) OTDS_FAILED,
               SUM(DECODE(T.PAST_DUE_INDICATOR, 'Y', ${valueColumn})) BACKORDER,
--                SUM(DECODE(T.AC2_TYPE, 'EARLY', ${valueColumn}, 'DELAY', ${valueColumn})) AC2_DELAY,
--                ROUND(AVG(RESCH_COUNTER), 2) NOR_AVG_COUNTER,
               SUM(DECODE(T.VIP_SO_INDICATOR, 'Y', ${valueColumn})) VIP_SO_INDICATOR,
               SUM(DECODE(T.DELIVERY_PRIORITY, '1', ${valueColumn}, '4', ${valueColumn})) PRIORITY_ORDER,
               SUM(DECODE(T.GRA_STATUS, 'GRA', ${valueColumn})) GRA_ORDER
        FROM ${SCPA.OPEN_SO_STRUCTURE_V} T
        <where>
            <include refid="openSOFilter"/>
        </where>
        GROUP BY 1
    </select>

    <sql id="queryReport1DetailsSQL">
        SELECT *
          FROM ${SCPA.OPEN_SO_STRUCTURE_V} T
        <where>
            <include refid="openSOFilter"/>
            <choose>
                <when test="report1DetailsType == 'OTDS_FAILED'.toString()">
                    AND T.ONTIME = '#'
                </when>
                <when test="report1DetailsType == 'BACKORDER'.toString()">
                    AND T.PAST_DUE_INDICATOR = 'Y'
                </when>
<!--                <when test="report1DetailsType == 'AC2_DELAY'.toString()">-->
<!--                    AND T.AC2_TYPE IN ('EARLY','DELAY')-->
<!--                </when>-->
                <when test="report1DetailsType == 'NOR_AVG_COUNTER'.toString()">
                    AND T.RESCH_COUNTER IS NOT NULL
                </when>
                <when test="report1DetailsType == 'PRIORITY_ORDER'.toString()">
                    AND T.DELIVERY_PRIORITY IN ('2','99')
                </when>
                <when test="report1DetailsType == 'GRA_ORDER'.toString()">
                    AND T.GRA_STATUS = 'GRA'
                </when>
            </choose>
        </where>
    </sql>

    <select id="queryReport1DetailsCount" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport1DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport1Details" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport1DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <resultMap id="report2ResultMap" type="com.scp.demand.bean.OpenSOReport2Bean">
        <result property="category1" column="CATEGORY1"/>
        <result property="category2" column="CATEGORY2"/>
        <result property="category3" column="CATEGORY3"/>
        <result property="category4" column="CATEGORY4"/>
        <result property="category5" column="CATEGORY5"/>
        <result property="value" column="value"/>
        <association property="tooltips" javaType="com.scp.demand.bean.OpenSOReport2Tooltips">
            <result property="OPEN_UD_QTY" column="OPEN_UD_QTY"/>
            <result property="UD_CB" column="UD_CB"/>
            <result property="UD_NORMAL" column="UD_NORMAL"/>
            <result property="UD_MID_AGING" column="UD_MID_AGING"/>
            <result property="UD_LONG_AGING" column="UD_LONG_AGING"/>
        </association>
    </resultMap>

    <select id="queryReport2" resultMap="report2ResultMap">
        WITH BASE AS (
            SELECT /*+ parallel(t 6) */ * FROM ${SCPA.OPEN_SO_STRUCTURE_V} T
            <where>
                <include refid="openSOFilter"/>
            </where>
        )
        SELECT
        NVL(${level1}, 'Others') AS CATEGORY1,
        NVL(${level2}, 'Others') AS CATEGORY2,
        NVL(${level3}, 'Others') AS CATEGORY3,
        <if test="level4 != null and level4 != ''.toString()">
            NVL(${level4}, 'Others') AS CATEGORY4,
        </if>
        <if test="level5 != null and level5 != ''.toString()">
            NVL(${level5}, 'Others') AS CATEGORY5,
        </if>
        ${valueColumn} AS VALUE
        <if test="tooltipsColumns != null and tooltipsColumns != ''.toString()">
            ,${tooltipsColumns}
        </if>
        FROM BASE t
        <where>
            <include refid="openSOFilter"/>
        </where>
        GROUP BY
        ${level1}, ${level2}, ${level3}
        <if test="level4 != null and level4 != ''.toString()">,${level4}</if>
        <if test="level5 != null and level5 != ''.toString()">,${level5}</if>
    </select>

    <select id="queryReport3" resultType="java.util.Map">
        WITH BASE AS (
            SELECT /*+ parallel(t 6) */ * FROM ${SCPA.OPEN_SO_STRUCTURE_V} T
            <where>
                <include refid="openSOFilter"/>
            </where>
        )
        SELECT T.DELIVERY_RANGE "name",
               ${valueColumn} "value"
        FROM BASE T
       GROUP BY DELIVERY_RANGE
       ORDER BY <include refid="decodeName"/>
    </select>

    <sql id="queryReport3DetailsSQL">
        SELECT *
          FROM ${SCPA.OPEN_SO_STRUCTURE_V} T
        <where>
            <include refid="openSOFilter"/>
            <if test="report3DetailsType != null and report3DetailsType != ''">
                AND T.DELIVERY_RANGE IN (
                <foreach collection="report3DetailsType" separator="," item="item">
                    '${item}'
                </foreach>
                )
            </if>
        </where>
    </sql>

    <select id="queryReport3DetailsCount" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport3DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport3Details" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport3DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <resultMap id="report4ResultMap" type="com.scp.demand.bean.OpenSOReport4Bean">
        <result property="CALENDAR_DATE" column="CALENDAR_DATE"/>
        <result property="NAME" column="NAME"/>
        <result property="VALUE" column="VALUE"/>
    </resultMap>

    <select id="queryReport4Legend" resultType="java.lang.String">
        <choose>
            <when test="report4ViewType =='DELIVERY_RANGE'.toString()">
                SELECT NAME FROM OPEN_SO_STRUCTURE_FILTER_V T
                WHERE T.CATEGORY = #{report4ViewType, jdbcType=VARCHAR}
                ORDER BY
                DECODE(
                T.NAME,
                'P >6M', 'a',
                'P 3-6M', 'b',
                'P 2-3M', 'c',
                'P 1-2M', 'd',
                'P 14-30D', 'e',
                'P 7-14D', 'f',
                'P 3-7D', 'g',
                'P 0-3D', 'h',
                'F 0-3D', 'i',
                'F 3-7D', 'j',
                'F 7-14D', 'k',
                'F 14-30D', 'l',
                'F 1-2M', 'm',
                'F 2-3M', 'n',
                'F 3-6M', 'o',
                'F >6M', 'p',
                'Others', 'z',
                T.NAME)
            </when>
            <when test="report4ViewType =='AC2_RANGE'.toString()">
                SELECT NAME FROM OPEN_SO_STRUCTURE_FILTER_V T
                    WHERE T.CATEGORY = #{report4ViewType, jdbcType=VARCHAR}
                    ORDER BY
                    DECODE(
                    T.NAME,
                    '&lt;-30', 'a',
                    '-15~-30', 'b',
                    '-8~-14', 'c',
                    '-3~-7', 'd',
                    '-2~0', 'e',
                    '1~7', 'f',
                    '8~14', 'g',
                    '15~30', 'h',
                    '>30', 'i',
                    'NO_AC2', 'j',
                    'Others', 'z',
                    T.NAME)
            </when>
            <otherwise>
                WITH <include refid="mv.so_backlog_hist_v"/>,
                BASE AS (
                    SELECT DISTINCT NVL(T.${report4ViewType}, 'Others') AS NAME FROM SO_BACKLOG_HIST_V T
                    <where>
                        <include refid="openSOHistFilter"/>
                        <choose>
                            <when test='report4SelectedType == "VIEW_BY_DAY".toString()'/>
                            <when test='report4SelectedType == "VIEW_BY_WEEK".toString()'>
                                AND TO_CHAR(T.DATE$, 'D') = 7
                            </when>
                            <when test='report4SelectedType == "VIEW_BY_MONTH".toString()'>
                                AND TO_CHAR(T.DATE$, 'DD') = 1
                            </when>
                            <when test='report4SelectedType == "VIEW_BY_QUARTER".toString()'>
                                AND T.DATE$ = TRUNC(T.DATE$, 'Q')
                            </when>
                            <when test='report4SelectedType == "VIEW_BY_YEAR".toString()'>
                                AND T.DATE$ = TRUNC(T.DATE$, 'YYYY')
                            </when>
                            <otherwise>
                                AND TO_CHAR(T.DATE$, 'DD') = 1
                            </otherwise>
                        </choose>
                        AND T.DATE$ BETWEEN TO_DATE(#{report4DateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') AND TO_DATE(#{report4DateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                    </where>
                )
                SELECT * FROM BASE ORDER BY
                    <![CDATA[ DECODE(NVL(NAME, 'Others'), '0-3D', 'a', '3-7D', 'b', '7-14D', 'c', '14-30D', 'd', '1-2M', 'e', '2-3M', 'f',
                '3-6M', 'g', '>6M', 'h', 'Others', 'zzz', NVL(NAME, 'Others'))]]>
            </otherwise>
        </choose>
    </select>

    <select id="queryReport4" resultMap="report4ResultMap">
        WITH <include refid="mv.so_backlog_hist_v"/>,
        BASE AS (
            SELECT /*+ parallel(t 6) */ * FROM SO_BACKLOG_HIST_V T
            <where>
                <include refid="openSOHistFilter"/>
                <choose>
                    <when test='report4SelectedType == "VIEW_BY_DAY".toString()'/>
                    <when test='report4SelectedType == "VIEW_BY_WEEK".toString()'>
                        AND TO_CHAR(T.DATE$, 'D') = 7
                    </when>
                    <when test='report4SelectedType == "VIEW_BY_MONTH".toString()'>
                        AND TO_CHAR(T.DATE$, 'DD') = 1
                    </when>
                    <when test='report4SelectedType == "VIEW_BY_QUARTER".toString()'>
                        AND T.DATE$ = TRUNC(T.DATE$, 'Q')
                    </when>
                    <when test='report4SelectedType == "VIEW_BY_YEAR".toString()'>
                        AND T.DATE$ = TRUNC(T.DATE$, 'YYYY')
                    </when>
                    <otherwise>
                        AND TO_CHAR(T.DATE$, 'DD') = 1
                    </otherwise>
                </choose>
                AND T.DATE$ IS NOT NULL
                AND T.DATE$ BETWEEN TO_DATE(#{report4DateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') AND TO_DATE(#{report4DateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
            </where>
        )
        SELECT TO_CHAR(T.DATE$, 'yyyy/mm/dd') AS "CALENDAR_DATE",
               NVL(T.${report4ViewType}, 'Others') AS "NAME",
               ${valueColumn} AS "VALUE"
          FROM BASE T
         GROUP BY T.DATE$, NVL(T.${report4ViewType}, 'Others')
    </select>

    <select id="queryReport4DueDate" resultMap="report4ResultMap">
        WITH <include refid="mv.so_backlog_hist_v"/>,
        BASE AS (
            SELECT /*+ parallel(t 6) */
            T.*,
            <choose>
                <when test='report4SelectedTypeDuedate == "VIEW_BY_DAY".toString()'>
                    TO_CHAR(T.CALENDAR_DATE, 'yyyy/mm/dd') AS DUE_DATE
                </when>
                <when test='report4SelectedTypeDuedate == "VIEW_BY_WEEK".toString()'>
                    CALENDAR_WEEK AS DUE_DATE
                </when>
                <when test='report4SelectedTypeDuedate == "VIEW_BY_MONTH".toString()'>
                    CALENDAR_MONTH AS DUE_DATE
                </when>
                <when test='report4SelectedTypeDuedate == "VIEW_BY_QUARTER".toString()'>
                    CALENDAR_QUARTER AS DUE_DATE
                </when>
                <when test='report4SelectedTypeDuedate == "VIEW_BY_YEAR".toString()'>
                    CALENDAR_YEAR AS DUE_DATE
                </when>
                <otherwise>
                    CALENDAR_MONTH AS DUE_DATE
                </otherwise>
            </choose>
            FROM SO_BACKLOG_HIST_V T
            <where>
                <include refid="openSOHistFilter"/>
                AND T.CALENDAR_DATE IS NOT NULL
                AND T.CALENDAR_DATE BETWEEN TO_DATE(#{report4DateRangeDuedate[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') AND TO_DATE(#{report4DateRangeDuedate[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                AND T.DATE$ = TO_DATE(#{report4Duedate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
            </where>
        )
        SELECT DUE_DATE AS "CALENDAR_DATE",
               NVL(T.${report4ViewType}, 'Others') AS "NAME",
               ${valueColumn} AS "VALUE"
          FROM BASE T
         GROUP BY T.DUE_DATE, NVL(T.${report4ViewType}, 'Others')
    </select>

    <select id="queryReport4ConfirmDate" resultMap="report4ResultMap">
        WITH <include refid="mv.so_backlog_hist_v"/>,
        BASE AS (
            SELECT /*+ parallel(t 6) */
            T.*,
            <choose>
                <when test='report4SelectedTypeDuedate == "VIEW_BY_DAY".toString()'>
                    TO_CHAR(T.CONFIRM_DATE, 'yyyy/mm/dd') AS CONFIRM_DATE_STR
                </when>
                <when test='report4SelectedTypeDuedate == "VIEW_BY_WEEK".toString()'>
                    TO_CHAR(T.CONFIRM_DATE, 'yyyy') || CD.WEEK_NO AS CONFIRM_DATE_STR
                </when>
                <when test='report4SelectedTypeDuedate == "VIEW_BY_MONTH".toString()'>
                    TO_CHAR(T.CONFIRM_DATE, 'yyyymm') AS CONFIRM_DATE_STR
                </when>
                <when test='report4SelectedTypeDuedate == "VIEW_BY_QUARTER".toString()'>
                    TO_CHAR(T.CONFIRM_DATE, 'yyyy"Q"q') AS CONFIRM_DATE_STR
                </when>
                <when test='report4SelectedTypeDuedate == "VIEW_BY_YEAR".toString()'>
                    TO_CHAR(T.CONFIRM_DATE, 'yyyy"/01/01"') AS CONFIRM_DATE_STR
                </when>
                <otherwise>
                    TO_CHAR(T.CONFIRM_DATE, 'yyyymm') AS CONFIRM_DATE_STR
                </otherwise>
            </choose>
            FROM SO_BACKLOG_HIST_V T LEFT JOIN SY_CALENDAR CD ON T.CONFIRM_DATE = CD.DATE$ AND CD.NAME = 'National Holidays'
            <where>
                <include refid="openSOHistFilter"/>
                AND T.CONFIRM_DATE IS NOT NULL
                AND T.CONFIRM_DATE BETWEEN TO_DATE(#{report4DateRangeDuedate[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') AND TO_DATE(#{report4DateRangeDuedate[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                AND T.DATE$ = TO_DATE(#{report4Duedate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
            </where>
        )
        SELECT T.CONFIRM_DATE_STR AS "CALENDAR_DATE",
               NVL(T.${report4ViewType}, 'Others') AS "NAME",
               ${valueColumn} AS "VALUE"
          FROM BASE T
         GROUP BY T.CONFIRM_DATE_STR, NVL(T.${report4ViewType}, 'Others')
    </select>

    <select id="queryReport4PlannedFirstGIDate" resultMap="report4ResultMap">
        WITH <include refid="mv.so_backlog_hist_v"/>,
        BASE AS (
            SELECT /*+ parallel(t 6) */
            T.*,
            <choose>
                <when test='report4SelectedTypeDuedate == "VIEW_BY_DAY".toString()'>
                    TO_CHAR(T.PLANNED_FIRST_GI_DATE, 'yyyy/mm/dd') AS PLANNED_FIRST_GI_DATE_STR
                </when>
                <when test='report4SelectedTypeDuedate == "VIEW_BY_WEEK".toString()'>
                    TO_CHAR(T.PLANNED_FIRST_GI_DATE, 'yyyy') || CD.WEEK_NO AS PLANNED_FIRST_GI_DATE_STR
                </when>
                <when test='report4SelectedTypeDuedate == "VIEW_BY_MONTH".toString()'>
                    TO_CHAR(T.PLANNED_FIRST_GI_DATE, 'yyyymm') AS PLANNED_FIRST_GI_DATE_STR
                </when>
                <when test='report4SelectedTypeDuedate == "VIEW_BY_QUARTER".toString()'>
                    TO_CHAR(T.PLANNED_FIRST_GI_DATE, 'yyyy"Q"q') AS PLANNED_FIRST_GI_DATE_STR
                </when>
                <when test='report4SelectedTypeDuedate == "VIEW_BY_YEAR".toString()'>
                    TO_CHAR(T.PLANNED_FIRST_GI_DATE, 'yyyy"/01/01"') AS PLANNED_FIRST_GI_DATE_STR
                </when>
                <otherwise>
                    TO_CHAR(T.PLANNED_FIRST_GI_DATE, 'yyyymm') AS PLANNED_FIRST_GI_DATE_STR
                </otherwise>
            </choose>
            FROM SO_BACKLOG_HIST_V T LEFT JOIN SY_CALENDAR CD ON T.CONFIRM_DATE = CD.DATE$ AND CD.NAME = 'National Holidays'
            <where>
                <include refid="openSOHistFilter"/>
                AND T.PLANNED_FIRST_GI_DATE IS NOT NULL
                AND T.CONFIRM_DATE BETWEEN TO_DATE(#{report4DateRangeDuedate[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') AND TO_DATE(#{report4DateRangeDuedate[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                AND T.DATE$ = TO_DATE(#{report4Duedate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
            </where>
        )
        SELECT T.PLANNED_FIRST_GI_DATE_STR AS "CALENDAR_DATE",
               NVL(T.${report4ViewType}, 'Others') AS "NAME",
               ${valueColumn} AS "VALUE"
          FROM BASE T
         GROUP BY T.PLANNED_FIRST_GI_DATE_STR, NVL(T.${report4ViewType}, 'Others')
    </select>

    <sql id="queryReport4DetailsSQL">
        WITH <include refid="mv.so_backlog_hist_v"/>
        SELECT *
          FROM SO_BACKLOG_HIST_V T LEFT JOIN SY_CALENDAR CD ON T.CONFIRM_DATE = CD.DATE$ AND CD.NAME = 'National Holidays'
        <where>
            <include refid="openSOHistFilter"/>
            <choose>
                <when test="report4DateType == 'BY_DATE'.toString()">
                    AND T.DATE$ = TO_DATE(#{report4DetailsType, jdbcType=VARCHAR}, 'yyyy/mm/dd')
                </when>
                <when test="report4DateType == 'BY_DUEDATE'.toString()">
                    <choose>
                        <when test="report4SelectedTypeDuedate == 'VIEW_BY_DAY'.toString()">
                            AND T.CALENDAR_DATE = TO_DATE(#{report4DetailsType, jdbcType=VARCHAR}, 'yyyy/mm/dd')
                            AND T.DATE$ = TO_DATE(#{report4Duedate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
                        </when>
                        <when test="report4SelectedTypeDuedate == 'VIEW_BY_WEEK'.toString()">
                            AND T.CALENDAR_WEEK = #{report4DetailsType, jdbcType=VARCHAR}
                            AND T.DATE$ = TO_DATE(#{report4Duedate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
                        </when>
                        <when test="report4SelectedTypeDuedate == 'VIEW_BY_MONTH'.toString()">
                            AND T.CALENDAR_MONTH = #{report4DetailsType, jdbcType=VARCHAR}
                            AND T.DATE$ = TO_DATE(#{report4Duedate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
                        </when>
                        <when test="report4SelectedTypeDuedate == 'VIEW_BY_QUARTER'.toString()">
                            AND T.CALENDAR_QUARTER = #{report4DetailsType, jdbcType=VARCHAR}
                            AND T.DATE$ = TO_DATE(#{report4Duedate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
                        </when>
                        <when test="report4SelectedTypeDuedate == 'VIEW_BY_YEAR'.toString()">
                            AND T.CALENDAR_YEAR = #{report4DetailsType, jdbcType=VARCHAR}
                            AND T.DATE$ = TO_DATE(#{report4Duedate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
                        </when>
                    </choose>
                </when>
                <when test="report4DateType == 'BY_CONFIRMDATE'.toString()">
                    <choose>
                        <when test="report4SelectedTypeDuedate == 'VIEW_BY_DAY'.toString()">
                            AND T.CONFIRM_DATE = TO_DATE(#{report4DetailsType, jdbcType=VARCHAR}, 'yyyy/mm/dd')
                            AND T.DATE$ = TO_DATE(#{report4Duedate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
                        </when>
                        <when test="report4SelectedTypeDuedate == 'VIEW_BY_WEEK'.toString()">
                            AND TO_CHAR(T.CONFIRM_DATE, 'YYYY') || CD.WEEK_NO = #{report4DetailsType, jdbcType=VARCHAR}
                            AND T.DATE$ = TO_DATE(#{report4Duedate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
                        </when>
                        <when test="report4SelectedTypeDuedate == 'VIEW_BY_MONTH'.toString()">
                            AND TO_CHAR(T.CONFIRM_DATE, 'YYYYMM') = #{report4DetailsType, jdbcType=VARCHAR}
                            AND T.DATE$ = TO_DATE(#{report4Duedate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
                        </when>
                        <when test="report4SelectedTypeDuedate == 'VIEW_BY_QUARTER'.toString()">
                            AND TO_CHAR(T.CONFIRM_DATE, 'yyyy"Q"q') = #{report4DetailsType, jdbcType=VARCHAR}
                            AND T.DATE$ = TO_DATE(#{report4Duedate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
                        </when>
                        <when test="report4SelectedTypeDuedate == 'VIEW_BY_YEAR'.toString()">
                            AND TO_CHAR(T.CONFIRM_DATE, 'yyyy"/01/01"') = #{report4DetailsType, jdbcType=VARCHAR}
                            AND T.DATE$ = TO_DATE(#{report4Duedate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
                        </when>
                    </choose>
                </when>
                <when test="report4DateType == 'BY_ORI_PLANNED_FIRST_GI_DATE'.toString()">
                    <choose>
                        <when test="report4SelectedTypeDuedate == 'VIEW_BY_DAY'.toString()">
                            AND T.PLANNED_FIRST_GI_DATE = TO_DATE(#{report4DetailsType, jdbcType=VARCHAR}, 'yyyy/mm/dd')
                            AND T.DATE$ = TO_DATE(#{report4Duedate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
                        </when>
                        <when test="report4SelectedTypeDuedate == 'VIEW_BY_WEEK'.toString()">
                            AND TO_CHAR(T.PLANNED_FIRST_GI_DATE, 'YYYY') || CD.WEEK_NO = #{report4DetailsType, jdbcType=VARCHAR}
                            AND T.DATE$ = TO_DATE(#{report4Duedate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
                        </when>
                        <when test="report4SelectedTypeDuedate == 'VIEW_BY_MONTH'.toString()">
                            AND TO_CHAR(T.PLANNED_FIRST_GI_DATE, 'YYYYMM') = #{report4DetailsType, jdbcType=VARCHAR}
                            AND T.DATE$ = TO_DATE(#{report4Duedate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
                        </when>
                        <when test="report4SelectedTypeDuedate == 'VIEW_BY_QUARTER'.toString()">
                            AND TO_CHAR(T.PLANNED_FIRST_GI_DATE, 'yyyy"Q"q') = #{report4DetailsType, jdbcType=VARCHAR}
                            AND T.DATE$ = TO_DATE(#{report4Duedate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
                        </when>
                        <when test="report4SelectedTypeDuedate == 'VIEW_BY_YEAR'.toString()">
                            AND TO_CHAR(T.PLANNED_FIRST_GI_DATE, 'yyyy"/01/01"') = #{report4DetailsType, jdbcType=VARCHAR}
                            AND T.DATE$ = TO_DATE(#{report4Duedate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
                        </when>
                    </choose>
                </when>
            </choose>
        </where>
    </sql>

    <select id="queryReport4DetailsCount" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport4DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport4Details" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport4DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport5Columns" resultType="java.lang.String">
        <choose>
            <when test='report4DateType == "BY_DATE".toString()'>
               SELECT distinct TO_CHAR(DATE$, 'YYYY/MM/DD') AS label
                 FROM ${SCPA.OPEN_SO_STRUCTURE_HIST} t
                WHERE T.DATE$ BETWEEN least(TO_DATE(#{report4DateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd'),TRUNC(SYSDATE, 'dd'))
                AND least(TO_DATE(#{report4DateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd'),TRUNC(SYSDATE, 'dd'))
                <choose>
                    <when test='report4SelectedType == "VIEW_BY_DAY".toString()'/>
                    <when test='report4SelectedType == "VIEW_BY_WEEK".toString()'>
                        AND TO_CHAR(T.DATE$, 'D') = 7
                    </when>
                    <when test='report4SelectedType == "VIEW_BY_MONTH".toString()'>
                        AND TO_CHAR(T.DATE$, 'DD') = 1
                    </when>
                    <when test='report4SelectedType == "VIEW_BY_QUARTER".toString()'>
                        AND T.DATE$ = TRUNC(T.DATE$, 'Q')
                    </when>
                    <when test='report4SelectedType == "VIEW_BY_YEAR".toString()'>
                        AND T.DATE$ = TRUNC(T.DATE$, 'yyyy"/01/01"')
                    </when>
                    <otherwise>
                        AND TO_CHAR(T.DATE$, 'DD') = 1
                    </otherwise>
                </choose>
                ORDER BY label DESC
                FETCH NEXT 63 ROWS ONLY
            </when>
            <when test="report4DateType == 'BY_DUEDATE'.toString() or report4DateType == 'BY_CONFIRMDATE'.toString() or report4DateType == 'BY_ORI_PLANNED_FIRST_GI_DATE'.toString()">
                <choose>
                    <when test='report4SelectedTypeDuedate == "VIEW_BY_DAY".toString()'>
                        SELECT DISTINCT TEXT AS label
                          FROM SY_CALENDAR t
                         WHERE T.DATE$ BETWEEN TO_DATE(#{report4DateRangeDuedate[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') AND TO_DATE(#{report4DateRangeDuedate[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                               AND T.NAME = 'National Holidays'
                         ORDER BY label DESC
                         FETCH NEXT 63 ROWS ONLY
                    </when>
                    <when test='report4SelectedTypeDuedate == "VIEW_BY_WEEK".toString()'>
                        SELECT DISTINCT T.YEAR || T.WEEK_NO AS label
                          FROM SY_CALENDAR t
                         WHERE T.DATE$ BETWEEN TO_DATE(#{report4DateRangeDuedate[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') AND TO_DATE(#{report4DateRangeDuedate[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                           AND T.NAME = 'National Holidays'
                         ORDER BY label DESC
                        FETCH NEXT 63 ROWS ONLY
                    </when>
                    <when test='report4SelectedTypeDuedate == "VIEW_BY_MONTH".toString()'>
                        SELECT DISTINCT T.YEAR || T.MONTH AS label
                          FROM SY_CALENDAR t
                         WHERE T.DATE$ BETWEEN TO_DATE(#{report4DateRangeDuedate[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') AND TO_DATE(#{report4DateRangeDuedate[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                           AND T.NAME = 'National Holidays'
                         ORDER BY label DESC
                        FETCH NEXT 63 ROWS ONLY
                    </when>
                    <when test='report4SelectedTypeDuedate == "VIEW_BY_QUARTER".toString()'>
                        SELECT DISTINCT TO_CHAR(T.DATE$, 'yyyy"Q"q') AS label
                          FROM SY_CALENDAR t
                          WHERE T.DATE$ BETWEEN TO_DATE(#{report4DateRangeDuedate[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') AND TO_DATE(#{report4DateRangeDuedate[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                           AND T.NAME = 'National Holidays'
                         ORDER BY label DESC
                        FETCH NEXT 63 ROWS ONLY
                    </when>
                    <when test='report4SelectedTypeDuedate == "VIEW_BY_YEAR".toString()'>
                        SELECT DISTINCT T.YEAR || '/01/01' AS label
                          FROM SY_CALENDAR t
                         WHERE T.DATE$ BETWEEN TO_DATE(#{report4DateRangeDuedate[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') AND TO_DATE(#{report4DateRangeDuedate[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                           AND T.NAME = 'National Holidays'
                         ORDER BY label DESC
                        FETCH NEXT 63 ROWS ONLY
                    </when>
                </choose>
            </when>
        </choose>
    </select>

    <select id="queryReport5" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        WITH <include refid="mv.so_backlog_hist_v"/>,
        BASE AS (
            SELECT /*+ parallel(t 6) */
            TO_CHAR(DATE$, 'YYYY/MM/DD') PDATE,
            <foreach collection="report5Columns" separator="," item="item">
                NVL(${item}, 'Others') AS "${item}"
            </foreach>,
            OPEN_SO_QTY,
            OPEN_UD_QTY,
            NET_NET_VALUE_RMB,
            NET_PRICE,
            AVG_SELLING_PRICE_RMB,
            AVG_SELLING_PRICE_HKD,
            GROSS_WEIGHT_IN_KG
            FROM SO_BACKLOG_HIST_V T
            <where>
                <include refid="openSOHistFilter"/>
                <choose>
                    <when test='report4SelectedType == "VIEW_BY_DAY".toString()'/>
                    <when test='report4SelectedType == "VIEW_BY_WEEK".toString()'>
                        AND TO_CHAR(T.DATE$, 'D') = 7
                    </when>
                    <when test='report4SelectedType == "VIEW_BY_MONTH".toString()'>
                        AND TO_CHAR(T.DATE$, 'DD') = 1
                    </when>
                    <when test='report4SelectedType == "VIEW_BY_QUARTER".toString()'>
                        AND T.DATE$ = TRUNC(T.DATE$, 'Q')
                    </when>
                    <when test='report4SelectedType == "VIEW_BY_YEAR".toString()'>
                        AND T.DATE$ = TRUNC(T.DATE$, 'YYYY')
                    </when>
                    <otherwise>
                        AND TO_CHAR(T.DATE$, 'DD') = 1
                    </otherwise>
                </choose>
                AND T.DATE$ BETWEEN TO_DATE(#{report4DateRange[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') AND TO_DATE(#{report4DateRange[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
            </where>
        )
        SELECT *
          FROM (
              SELECT PDATE,
                     <foreach collection="report5Columns" separator="," item="item">
                        ${item}
                     </foreach>,
                     ${valueColumn} AS TOTAL
              FROM BASE T0
              GROUP BY PDATE,
              <foreach collection="report5Columns" separator="," item="item">
                 ${item}
              </foreach>
        ) T PIVOT (
            SUM(TOTAL) AS TOTAL
            FOR PDATE IN (
            <foreach collection="report5DateColumns" separator="," item="item">
                '${item}'
            </foreach>)
        )
        ORDER BY
        <foreach collection="report5Columns" separator="," item="item">
            DECODE(${item}, 'Others', 'zzz', ${item})
        </foreach>
        FETCH FIRST 5000 ROWS ONLY
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport5DueDate" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        WITH <include refid="mv.so_backlog_hist_v"/>,
        BASE AS (
            SELECT /*+ parallel(t 6) */
            <choose>
                <when test='report4SelectedTypeDuedate == "VIEW_BY_DAY".toString()'>
                    TO_CHAR(CALENDAR_DATE, 'YYYY/MM/DD') PDATE,
                </when>
                <when test='report4SelectedTypeDuedate == "VIEW_BY_WEEK".toString()'>
                    CALENDAR_WEEK PDATE,
                </when>
                <when test='report4SelectedTypeDuedate == "VIEW_BY_MONTH".toString()'>
                    CALENDAR_MONTH PDATE,
                </when>
                <when test='report4SelectedTypeDuedate == "VIEW_BY_QUARTER".toString()'>
                    CALENDAR_QUARTER PDATE,
                </when>
                <when test='report4SelectedTypeDuedate == "VIEW_BY_YEAR".toString()'>
                    CALENDAR_YEAR PDATE,
                </when>
            </choose>
            <foreach collection="report5Columns" separator="," item="item">
                NVL(${item}, 'Others') AS "${item}"
            </foreach>,
            OPEN_SO_QTY,
            OPEN_UD_QTY,
            NET_NET_VALUE_RMB,
            NET_PRICE,
            AVG_SELLING_PRICE_RMB,
            AVG_SELLING_PRICE_HKD,
            GROSS_WEIGHT_IN_KG
            FROM SO_BACKLOG_HIST_V T
            <where>
                <include refid="openSOHistFilter"/>
                AND T.CALENDAR_DATE BETWEEN TO_DATE(#{report4DateRangeDuedate[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') AND TO_DATE(#{report4DateRangeDuedate[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                AND T.DATE$ = TO_DATE(#{report4Duedate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
            </where>
        )
        SELECT *
          FROM (
              SELECT PDATE,
                     <foreach collection="report5Columns" separator="," item="item">
                        ${item}
                     </foreach>,
                     ${valueColumn} AS TOTAL
              FROM BASE T0
              GROUP BY PDATE,
              <foreach collection="report5Columns" separator="," item="item">
                 ${item}
              </foreach>
        ) T PIVOT (
            SUM(TOTAL) AS TOTAL
            FOR PDATE IN (
            <foreach collection="report5DateColumns" separator="," item="item">
                '${item}'
            </foreach>)
        )
        ORDER BY
        <foreach collection="report5Columns" separator="," item="item">
            DECODE(${item}, 'Others', 'zzz', ${item})
        </foreach>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport5ConfirmDate" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        WITH <include refid="mv.so_backlog_hist_v"/>,
        BASE AS (
            SELECT /*+ parallel(t 6) */
            <choose>
                <when test='report4SelectedTypeDuedate == "VIEW_BY_DAY".toString()'>
                    TO_CHAR(T.CONFIRM_DATE, 'yyyy/mm/dd') AS CONFIRM_DATE,
                </when>
                <when test='report4SelectedTypeDuedate == "VIEW_BY_WEEK".toString()'>
                    TO_CHAR(T.CONFIRM_DATE, 'yyyy') || CD.WEEK_NO AS CONFIRM_DATE,
                </when>
                <when test='report4SelectedTypeDuedate == "VIEW_BY_MONTH".toString()'>
                    TO_CHAR(T.CONFIRM_DATE, 'yyyymm') AS CONFIRM_DATE,
                </when>
                <when test='report4SelectedTypeDuedate == "VIEW_BY_QUARTER".toString()'>
                    TO_CHAR(T.CONFIRM_DATE, 'yyyy"Q"q') AS CONFIRM_DATE,
                </when>
                <when test='report4SelectedTypeDuedate == "VIEW_BY_YEAR".toString()'>
                    TO_CHAR(T.CONFIRM_DATE, 'yyyy"/01/01"') AS CONFIRM_DATE,
                </when>
                <otherwise>
                    TO_CHAR(T.CONFIRM_DATE, 'yyyymm') AS CONFIRM_DATE,
                </otherwise>
            </choose>
            <foreach collection="report5Columns" separator="," item="item">
                NVL(${item}, 'Others') AS "${item}"
            </foreach>,
            OPEN_SO_QTY,
            OPEN_UD_QTY,
            NET_NET_VALUE_RMB,
            NET_PRICE,
            AVG_SELLING_PRICE_RMB,
            AVG_SELLING_PRICE_HKD,
            GROSS_WEIGHT_IN_KG
            FROM SO_BACKLOG_HIST_V T LEFT JOIN SY_CALENDAR CD ON T.CONFIRM_DATE = CD.DATE$ AND CD.NAME = 'National Holidays'
            <where>
                <include refid="openSOHistFilter"/>
                AND T.CONFIRM_DATE BETWEEN TO_DATE(#{report4DateRangeDuedate[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') AND TO_DATE(#{report4DateRangeDuedate[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                AND T.DATE$ = TO_DATE(#{report4Duedate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
            </where>
        )
        SELECT *
          FROM (
              SELECT CONFIRM_DATE,
                     <foreach collection="report5Columns" separator="," item="item">
                        ${item}
                     </foreach>,
                     ${valueColumn} AS TOTAL
              FROM BASE T0
              GROUP BY CONFIRM_DATE,
              <foreach collection="report5Columns" separator="," item="item">
                 ${item}
              </foreach>
        ) T PIVOT (
            SUM(TOTAL) AS TOTAL
            FOR CONFIRM_DATE IN (
            <foreach collection="report5DateColumns" separator="," item="item">
                '${item}'
            </foreach>)
        )
        ORDER BY
        <foreach collection="report5Columns" separator="," item="item">
            DECODE(${item}, 'Others', 'zzz', ${item})
        </foreach>
        <include refid="global.select_footer"/>
    </select>

    <select id="queryReport5PlannedFirstGIDate" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        WITH <include refid="mv.so_backlog_hist_v"/>,
        BASE AS (
        SELECT /*+ parallel(t 6) */
        <choose>
            <when test='report4SelectedTypeDuedate == "VIEW_BY_DAY".toString()'>
                TO_CHAR(T.PLANNED_FIRST_GI_DATE, 'yyyy/mm/dd') AS PLANNED_FIRST_GI_DATE,
            </when>
            <when test='report4SelectedTypeDuedate == "VIEW_BY_WEEK".toString()'>
                TO_CHAR(T.PLANNED_FIRST_GI_DATE, 'yyyy') || CD.WEEK_NO AS PLANNED_FIRST_GI_DATE,
            </when>
            <when test='report4SelectedTypeDuedate == "VIEW_BY_MONTH".toString()'>
                TO_CHAR(T.PLANNED_FIRST_GI_DATE, 'yyyymm') AS PLANNED_FIRST_GI_DATE,
            </when>
            <when test='report4SelectedTypeDuedate == "VIEW_BY_QUARTER".toString()'>
                TO_CHAR(T.PLANNED_FIRST_GI_DATE, 'yyyy"Q"q') AS PLANNED_FIRST_GI_DATE,
            </when>
            <when test='report4SelectedTypeDuedate == "VIEW_BY_YEAR".toString()'>
                TO_CHAR(T.PLANNED_FIRST_GI_DATE, 'yyyy"/01/01"') AS PLANNED_FIRST_GI_DATE,
            </when>
            <otherwise>
                TO_CHAR(T.PLANNED_FIRST_GI_DATE, 'yyyymm') AS PLANNED_FIRST_GI_DATE,
            </otherwise>
        </choose>
        <foreach collection="report5Columns" separator="," item="item">
            NVL(${item}, 'Others') AS "${item}"
        </foreach>,
        OPEN_SO_QTY,
        OPEN_UD_QTY,
        NET_NET_VALUE_RMB,
        NET_PRICE,
        AVG_SELLING_PRICE_RMB,
        AVG_SELLING_PRICE_HKD,
        GROSS_WEIGHT_IN_KG
        FROM SO_BACKLOG_HIST_V T LEFT JOIN SY_CALENDAR CD ON T.PLANNED_FIRST_GI_DATE = CD.DATE$ AND CD.NAME = 'National Holidays'
        <where>
            <include refid="openSOHistFilter"/>
            AND T.PLANNED_FIRST_GI_DATE BETWEEN TO_DATE(#{report4DateRangeDuedate[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') AND TO_DATE(#{report4DateRangeDuedate[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
            AND T.DATE$ = TO_DATE(#{report4Duedate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
        </where>
        )
        SELECT *
        FROM (
        SELECT PLANNED_FIRST_GI_DATE,
        <foreach collection="report5Columns" separator="," item="item">
            ${item}
        </foreach>,
        ${valueColumn} AS TOTAL
        FROM BASE T0
        GROUP BY PLANNED_FIRST_GI_DATE,
        <foreach collection="report5Columns" separator="," item="item">
            ${item}
        </foreach>
        ) T PIVOT (
        SUM(TOTAL) AS TOTAL
        FOR PLANNED_FIRST_GI_DATE IN (
        <foreach collection="report5DateColumns" separator="," item="item">
            '${item}'
        </foreach>)
        )
        ORDER BY
        <foreach collection="report5Columns" separator="," item="item">
            DECODE(${item}, 'Others', 'zzz', ${item})
        </foreach>
        <include refid="global.select_footer"/>
    </select>

    <sql id="queryReport5DetailsSQL">
        WITH <include refid="mv.so_backlog_hist_v"/>
        SELECT *
          FROM SO_BACKLOG_HIST_V T
            <choose>
                <when test="report4DateType == 'BY_DATE'.toString()">
                    <where>
                        <include refid="openSOHistFilter"/>
                        <if test="report5DetailsType[0] != ''.toString()">
                            AND T.DATE$ = TO_DATE(#{report5DetailsType[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                        </if>
                    </where>
                </when>
                <when test="report4DateType == 'BY_DUEDATE'.toString()">
                    <where>
                        <include refid="openSOHistFilter"/>
                        <if test="report5DetailsType[0] != ''.toString()">
                            <choose>
                                <when test='report4SelectedTypeDuedate == "VIEW_BY_DAY".toString()'>
                                    AND T.CALENDAR_DATE = TO_DATE(#{report5DetailsType[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                                </when>
                                <when test='report4SelectedTypeDuedate == "VIEW_BY_WEEK".toString()'>
                                    AND T.CALENDAR_WEEK = #{report5DetailsType[0], jdbcType=VARCHAR}
                                </when>
                                <when test='report4SelectedTypeDuedate == "VIEW_BY_MONTH".toString()'>
                                    AND T.CALENDAR_MONTH = #{report5DetailsType[0], jdbcType=VARCHAR}
                                </when>
                                <when test='report4SelectedTypeDuedate == "VIEW_BY_QUARTER".toString()'>
                                    AND T.CALENDAR_QUARTER = #{report5DetailsType[0], jdbcType=VARCHAR}
                                </when>
                                <when test='report4SelectedTypeDuedate == "VIEW_BY_YEAR".toString()'>
                                    AND T.CALENDAR_YEAR = #{report5DetailsType[0], jdbcType=VARCHAR}
                                </when>
                            </choose>
                        </if>
                        AND T.DATE$ = TO_DATE(#{report4Duedate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
                        AND T.CALENDAR_DATE BETWEEN TO_DATE(#{report4DateRangeDuedate[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') AND TO_DATE(#{report4DateRangeDuedate[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                    </where>
                </when>
                <when test="report4DateType == 'BY_CONFIRMDATE'.toString()">
                    LEFT JOIN SY_CALENDAR CD ON T.CONFIRM_DATE = CD.DATE$ AND CD.NAME = 'National Holidays'
                    <where>
                        <include refid="openSOHistFilter"/>
                        <if test="report5DetailsType[0] != ''.toString()">
                            <choose>
                                <when test='report4SelectedTypeDuedate == "VIEW_BY_DAY".toString()'>
                                    AND T.CONFIRM_DATE = TO_DATE(#{report5DetailsType[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                                </when>
                                <when test='report4SelectedTypeDuedate == "VIEW_BY_WEEK".toString()'>
                                    AND TO_CHAR(T.CONFIRM_DATE, 'YYYY') || CD.WEEK_NO = #{report5DetailsType[0], jdbcType=VARCHAR}
                                </when>
                                <when test='report4SelectedTypeDuedate == "VIEW_BY_MONTH".toString()'>
                                    AND TO_CHAR(T.CONFIRM_DATE, 'YYYYMM') = #{report5DetailsType[0], jdbcType=VARCHAR}
                                </when>
                                <when test='report4SelectedTypeDuedate == "VIEW_BY_QUARTER".toString()'>
                                    AND TO_CHAR(T.CONFIRM_DATE, 'yyyy"Q"q') = #{report5DetailsType[0], jdbcType=VARCHAR}
                                </when>
                                <when test='report4SelectedTypeDuedate == "VIEW_BY_YEAR".toString()'>
                                    AND TO_CHAR(T.CONFIRM_DATE, 'yyyy"/01/01"') = #{report5DetailsType[0], jdbcType=VARCHAR}
                                </when>
                            </choose>
                        </if>
                        AND T.DATE$ = TO_DATE(#{report4Duedate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
                        AND T.CONFIRM_DATE BETWEEN TO_DATE(#{report4DateRangeDuedate[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') AND TO_DATE(#{report4DateRangeDuedate[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                    </where>
                </when>
                <when test="report4DateType == 'BY_ORI_PLANNED_FIRST_GI_DATE'.toString()">
                    LEFT JOIN SY_CALENDAR CD ON T.PLANNED_FIRST_GI_DATE = CD.DATE$ AND CD.NAME = 'National Holidays'
                    <where>
                        <include refid="openSOHistFilter"/>
                        <if test="report5DetailsType[0] != ''.toString()">
                            <choose>
                                <when test='report4SelectedTypeDuedate == "VIEW_BY_DAY".toString()'>
                                    AND T.PLANNED_FIRST_GI_DATE = TO_DATE(#{report5DetailsType[0], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                                </when>
                                <when test='report4SelectedTypeDuedate == "VIEW_BY_WEEK".toString()'>
                                    AND TO_CHAR(T.PLANNED_FIRST_GI_DATE, 'YYYY') || CD.WEEK_NO = #{report5DetailsType[0], jdbcType=VARCHAR}
                                </when>
                                <when test='report4SelectedTypeDuedate == "VIEW_BY_MONTH".toString()'>
                                    AND TO_CHAR(T.PLANNED_FIRST_GI_DATE, 'YYYYMM') = #{report5DetailsType[0], jdbcType=VARCHAR}
                                </when>
                                <when test='report4SelectedTypeDuedate == "VIEW_BY_QUARTER".toString()'>
                                    AND TO_CHAR(T.PLANNED_FIRST_GI_DATE, 'yyyy"Q"q') = #{report5DetailsType[0], jdbcType=VARCHAR}
                                </when>
                                <when test='report4SelectedTypeDuedate == "VIEW_BY_YEAR".toString()'>
                                    AND TO_CHAR(T.PLANNED_FIRST_GI_DATE, 'yyyy"/01/01"') = #{report5DetailsType[0], jdbcType=VARCHAR}
                                </when>
                            </choose>
                        </if>
                        AND T.DATE$ = TO_DATE(#{report4Duedate, jdbcType=VARCHAR}, 'yyyy/mm/dd')
                        AND T.PLANNED_FIRST_GI_DATE BETWEEN TO_DATE(#{report4DateRangeDuedate[0], jdbcType=VARCHAR}, 'yyyy/mm/dd') AND TO_DATE(#{report4DateRangeDuedate[1], jdbcType=VARCHAR}, 'yyyy/mm/dd')
                    </where>
                </when>
            </choose>

            <foreach collection="report5DetailsType" index="index" item="item">
                <if test="index > 0">
                    <if test="report5DetailsType[index] != 'Total'.toString()">
                        AND T.${report5Columns[index - 1]} = #{item, jdbcType=VARCHAR}
                    </if>
                </if>
            </foreach>

    </sql>

    <select id="queryReport5DetailsCount" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        <include refid="queryReport5DetailsSQL"/>
        <include refid="global.count_footer"/>
    </select>

    <select id="queryReport5Details" resultType="java.util.LinkedHashMap">
        <include refid="global.select_header"/>
        <include refid="queryReport5DetailsSQL"/>
        <include refid="global.select_footer"/>
    </select>
</mapper>
