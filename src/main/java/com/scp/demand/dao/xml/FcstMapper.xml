<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.scp.demand.dao.IFcstDao">
    <select id="queryAuthSalesOrgsByUserid" parameterType="java.lang.String" resultType="java.lang.String">
        select sales_organizations from MR3_COLLABORATIVE_SALES_FORCAST_PRIVILEGES where lower(sesa_code) = lower(#{_parameter, jdbcType=VARCHAR})
    </select>

    <delete id="deleteFcstData" parameterType="java.lang.String">
        delete from MR3_COLLABORATIVE_SALES_FORECAST where create_by$ = #{upload_by, jdbcType=VARCHAR} and fcst_version = #{upload_version, jdbcType=VARCHAR}
    </delete>

    <insert id="insertFsctDataTemp">
        insert into MR3_COLLABORATIVE_SALES_FORCAST_TEMPORARY
        (fcst_version,material, sales_organization, customer_code, sales_group, month01,month02,month03,month04,month05,month06,month07,month08,month09,month10,month11,month12,month13,
        month14,month15,month16,month17,month18,month19,month20,month21,month22,month23,month24,month25,is_valid$, net_net_price_rmb, net_net_price_hkd)
        <foreach collection="list" separator=" union all" item="item">
            select
            #{upload_version, jdbcType=VARCHAR},
            #{item.material, jdbcType=VARCHAR},
            #{item.sales_org, jdbcType=VARCHAR},
            #{item.customer_code, jdbcType=VARCHAR},
            #{item.sales_group, jdbcType=VARCHAR},
            #{item.month01, jdbcType=DOUBLE},
            #{item.month02, jdbcType=DOUBLE},
            #{item.month03, jdbcType=DOUBLE},
            #{item.month04, jdbcType=DOUBLE},
            #{item.month05, jdbcType=DOUBLE},
            #{item.month06, jdbcType=DOUBLE},
            #{item.month07, jdbcType=DOUBLE},
            #{item.month08, jdbcType=DOUBLE},
            #{item.month09, jdbcType=DOUBLE},
            #{item.month10, jdbcType=DOUBLE},
            #{item.month11, jdbcType=DOUBLE},
            #{item.month12, jdbcType=DOUBLE},
            #{item.month13, jdbcType=DOUBLE},
            #{item.month14, jdbcType=DOUBLE},
            #{item.month15, jdbcType=DOUBLE},
            #{item.month16, jdbcType=DOUBLE},
            #{item.month17, jdbcType=DOUBLE},
            #{item.month18, jdbcType=DOUBLE},
            #{item.month19, jdbcType=DOUBLE},
            #{item.month20, jdbcType=DOUBLE},
            #{item.month21, jdbcType=DOUBLE},
            #{item.month22, jdbcType=DOUBLE},
            #{item.month23, jdbcType=DOUBLE},
            #{item.month24, jdbcType=DOUBLE},
            #{item.month25, jdbcType=DOUBLE},
            #{item.isValid, jdbcType=INTEGER},
            #{item.net_net_price_rmb, jdbcType=DOUBLE},
            #{item.net_net_price_hkd, jdbcType=DOUBLE}
            from dual
        </foreach>
    </insert>

    <insert id="mergeFcstData">
        merge into MR3_COLLABORATIVE_SALES_FORECAST t
        using
        (
        select fcst_version,
               material,
               sales_organization,
               customer_code,
               sales_group,
               max(net_net_price_rmb) net_net_price_rmb,
               max(net_net_price_hkd) net_net_price_hkd,
               max(month01) month01,
               max(month02) month02,
               max(month03) month03,
               max(month04) month04,
               max(month05) month05,
               max(month06) month06,
               max(month07) month07,
               max(month08) month08,
               max(month09) month09,
               max(month10) month10,
               max(month11) month11,
               max(month12) month12,
               max(month13) month13,
               max(month14) month14,
               max(month15) month15,
               max(month16) month16,
               max(month17) month17,
               max(month18) month18,
               max(month19) month19,
               max(month20) month20,
               max(month21) month21,
               max(month22) month22,
               max(month23) month23,
               max(month24) month24,
               max(month25) month25,
               max(is_valid$) is_valid$
        from MR3_COLLABORATIVE_SALES_FORCAST_TEMPORARY
        group by fcst_version, material, sales_organization, customer_code,sales_group
        ) s on (t.fcst_version = s.fcst_version and t.material = s.material and t.sales_organization = s.sales_organization and t.customer_code = s.customer_code and t.sales_group = s.sales_group)
        <if test="upload_mode != 'INSERT-IGNORE'.toString()">
            when matched then
            update set
            t.net_net_price_rmb = s.net_net_price_rmb,
            t.net_net_price_hkd = s.net_net_price_hkd,
            t.month01 = s.month01,
            t.month02 = s.month02,
            t.month03 = s.month03,
            t.month04 = s.month04,
            t.month05 = s.month05,
            t.month06 = s.month06,
            t.month07 = s.month07,
            t.month08 = s.month08,
            t.month09 = s.month09,
            t.month10 = s.month10,
            t.month11 = s.month11,
            t.month12 = s.month12,
            t.month13 = s.month13,
            t.month14 = s.month14,
            t.month15 = s.month15,
            t.month16 = s.month16,
            t.month17 = s.month17,
            t.month18 = s.month18,
            t.month19 = s.month19,
            t.month20 = s.month20,
            t.month21 = s.month21,
            t.month22 = s.month22,
            t.month23 = s.month23,
            t.month24 = s.month24,
            t.month25 = s.month25,
            t.is_valid$ = s.is_valid$,
            t.update_by$ = #{upload_by, jdbcType=VARCHAR},
            t.update_date$ = sysdate
        </if>
        when not matched then
        insert
        (fcst_version, material, sales_organization, customer_code, sales_group, month01,month02,month03,month04,month05,month06,month07,month08,month09,month10,month11,month12,month13,
        month14,month15,month16,month17,month18,month19,month20,month21,month22,month23,month24,month25,is_valid$,create_by$,create_date$,net_net_price_rmb,net_net_price_hkd)
        values (s.fcst_version, s.material, s.sales_organization, s.customer_code,s.sales_group, s.month01,s.month02,s.month03,s.month04,s.month05,s.month06,s.month07,s.month08,s.month09,s.month10,
        s.month11,s.month12,s.month13,s.month14,s.month15,s.month16,s.month17,s.month18,s.month19,s.month20,s.month21,s.month22,s.month23,s.month24,s.month25,s.is_valid$,#{upload_by, jdbcType=VARCHAR},sysdate,
        s.net_net_price_rmb, s.net_net_price_hkd)
    </insert>

    <select id="queryFcstVersion" resultType="com.starter.context.bean.SimpleSelect">
        select distinct fcst_version as "value" from MR3_COLLABORATIVE_SALES_FORECAST order by fcst_version desc
    </select>

    <select id="queryFcstDataCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        <include refid="global.count_header"/>
        select * from MR3_COLLABORATIVE_SALES_FORECAST
         where
         <if test="is_admin != '1'.toString()">
            sales_organization in
            <foreach collection="sales_orgs" item="item" open="(" close=")" separator=",">#{item, jdbcType=VARCHAR}</foreach> and
         </if>
         fcst_version = #{fcst_version, jdbcType=VARCHAR}
        <include refid="global.count_footer"/>
    </select>

    <select id="queryFcstData" parameterType="java.util.Map" resultType="java.util.HashMap">
        <include refid="global.select_header"/>
        select ROWIDTOCHAR(rowid) row_id, t.* from MR3_COLLABORATIVE_SALES_FORECAST t
        where
        <if test="is_admin != '1'.toString()">
            sales_organization in
            <foreach collection="sales_orgs" item="item" open="(" close=")" separator=",">#{item, jdbcType=VARCHAR}</foreach> and
        </if>
        fcst_version = #{fcst_version, jdbcType=VARCHAR}
        <include refid="global.select_footer"/>
    </select>

    <select id="saveForcastSource" parameterType="java.util.Map">
        update MR3_COLLABORATIVE_SALES_FORECAST
        SET
        <foreach collection="cols" item="col" separator=",">
            ${col.key} = #{col.value,jdbcType=VARCHAR}
        </foreach>,
        update_by$ = #{userid,jdbcType=VARCHAR},
        update_date$ = sysdate
        where rowid = #{rowid,jdbcType=VARCHAR}
    </select>

    <select id="downloadFcstData" parameterType="java.util.Map" resultType="java.util.LinkedHashMap">
        select MATERIAL,SALES_ORGANIZATION,CUSTOMER_CODE,SALES_GROUP,NET_NET_PRICE_RMB, NET_NET_PRICE_HKD
         <foreach collection="columns" item="item" open="," separator=",">
             ${item.value} "${item.name}"
         </foreach>
         from MR3_COLLABORATIVE_SALES_FORECAST t
        where
        <if test="is_admin != '1'.toString()">
            sales_organization in
            <foreach collection="sales_orgs" item="item" open="(" close=")" separator=",">#{item, jdbcType=VARCHAR}</foreach> and
        </if>
        fcst_version = #{fcst_version, jdbcType=VARCHAR}
    </select>

    <select id="queryAvaliableCustomerCode" resultType="java.lang.String">
        select 'SECI-OG' as customer_code from dual
        union
        select 'PLANT-OG' from dual
        union
        select 'PCW-OG' from dual
        union
        select 'SEHK-OG' from dual
        union
        select 'SPC-OG' from dual
        union
        select 'SPHK-OG' from dual
        union
        select upper(t.customer_code) from CM3_CUSTOMER_MASTER_V t where t.account_group like 'IG%' and t.payment_block_st is null and t.order_block_status is null
    </select>

    <select id="queryExchangeRate" resultType="java.util.Map">
        with temp as (
            select t.FROM_CURRENCY,
                   TO_CURRENCY,
                   VALID_FROM,
                   EXCHANGE_RATE
            from MM3_EXCHANGE_RATE_V t
            where (t.FROM_CURRENCY, t.TO_CURRENCY)
                      in (('HKD', 'RMB'), ('RMB', 'HKD'))
        )

        select max(EXCHANGE_RATE) as RMB_TO_HKD, min(EXCHANGE_RATE) as HKD_TO_RMB
        from temp t
        where t.VALID_FROM = (select max(t0.VALID_FROM) from temp t0)
    </select>
</mapper>
