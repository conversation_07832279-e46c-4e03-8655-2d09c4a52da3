package com.scp.demand.dao;

import com.scp.demand.bean.TrackingReport1Bean;
import com.scp.demand.bean.TrackingReport2Bean;
import com.scp.demand.bean.TrackingReport3Bean;
import com.scp.demand.bean.TrackingReport4Bean;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface ITrackingDao {

    List<String> queryFcstVersion();

    List<TrackingReport1Bean> queryReport1(Map<String, Object> parameterMap);

    List<TrackingReport1Bean> queryReport1Sub(Map<String, Object> parameterMap);

    List<TrackingReport1Bean> downloadReport1(Map<String, Object> parameterMap);

    int queryReport1DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1Details(Map<String, Object> parameterMap);

    int queryReport1FCSTDetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport1FCSTDetails(Map<String, Object> parameterMap);

    List<TrackingReport2Bean> queryReport2(Map<String, Object> parameterMap);

    int queryReport2DetailsCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport2Details(Map<String, Object> parameterMap);

    Map<String, Object> queryReport2AMU(Map<String, Object> parameterMap);

    double getLastWDMonthRatio();

    List<Map<String, String>> queryTrackingCascader();

    List<Map<String, Object>> queryWorkingDayList(String saleMonth);

    List<Map<String, Object>> queryFCSTData(Map<String, Object> parameterMap);

    List<TrackingReport3Bean> queryReport3(Map<String, Object> parameterMap);

    Map<String, Object> queryReport3FCST(Map<String, Object> parameterMap);

    List<TrackingReport4Bean> queryReport4(Map<String, Object> parameterMap);

    int getTotalWorkingDay(Map<String, String> param);

    String queryAuthDetails(@Param("userid") String userid, @Param("menuCode") String menuCode);

    String queryLastWdMonth();

    List<String> queryQuarterWeekList(String year, String quarter);

    String queryThisWeek();

    List<Map<String, Object>> queryWorkingDayRatioQuarterList(String quarter);

    int getTotalFulfillWorkingDay(Map<String, String> param);

    double queryWorkingFulfillMonth(String year);

    List<Map<String, Object>> queryYearFcstByMonth(Map<String, Object> parameterMap);

    List<Map<String, Object>> queryQuarterFcstByWeek(Map<String, Object> params);

    int getFulfillWorkingDay(String current);

    List<String> queryPivotOpts();
}
