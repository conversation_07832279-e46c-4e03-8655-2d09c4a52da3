package com.scp.demand.dao;

import org.apache.ibatis.annotations.Mapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IAbnormalDemandDao {

    List<String> queryFcstVersion();

    List<Map<String, String>> queryCascader();

    List<String> queryReportColumns();

    int queryReportCount(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryReport(Map<String, Object> parameterMap);
}
