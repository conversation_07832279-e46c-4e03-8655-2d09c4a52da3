package com.scp.demand;

import com.scp.demand.service.IEventService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/demand/event", parent = EventController.PARENT_CODE)
public class EventController extends ControllerHelper {

    @Resource
    private IEventService eventService;

    public static final String PARENT_CODE = "menu180";

    @SchneiderRequestMapping("/init_page")
    public Response initPage(HttpServletRequest request) {
        super.setGlobalCache(true);
        super.pageLoad(request);
        return eventService.initPage(session.getUserid());
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1Data(HttpServletRequest request) {
        super.pageLoad(request);
        return eventService.queryReport1Data(parameterMap);
    }

    @SchneiderRequestMapping("/save_report1")
    public Response saveReport1(HttpServletRequest request) {
        super.pageLoad(request);
        return eventService.saveReport1(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping("/download_report1")
    public void downloadReport1(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        eventService.downloadReport1(parameterMap, response);
    }

    @SchneiderRequestMapping("/generate_new_note")
    public Response generateNewNote() {
        return eventService.generateNewNote();
    }

}
