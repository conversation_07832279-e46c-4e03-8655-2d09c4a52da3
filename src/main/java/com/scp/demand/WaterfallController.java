package com.scp.demand;

import com.scp.demand.service.IWaterfallService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/demand/waterfall", parent = "menu170")
public class WaterfallController extends ControllerHelper {

    @Resource
    private IWaterfallService waterfallService;

    @SchneiderRequestMapping("/query_cascader")
    public Response queryCascader() {
        super.setGlobalCache(true);
        return waterfallService.queryCascader();
    }

    @SchneiderRequestMapping("/query_report")
    public Response queryReport(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return waterfallService.queryReport(parameterMap);
    }

    @SchneiderRequestMapping("/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return waterfallService.queryReport2(parameterMap);
    }

    @SchneiderRequestMapping("/query_report2_details")
    public Response queryReport2Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return waterfallService.queryReport2Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report2_details")
    public void downloadReport2Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        waterfallService.downloadReport2Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report3")
    public Response queryReport3(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return waterfallService.queryReport3(parameterMap);
    }
}
