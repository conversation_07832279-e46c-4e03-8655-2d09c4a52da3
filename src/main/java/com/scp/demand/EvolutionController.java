package com.scp.demand;

import com.scp.demand.service.IEvolutionService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/demand/evolution", parent = "menu1D0")
public class EvolutionController extends ControllerHelper {

    @Resource
    private IEvolutionService evolutionService;

    @SchneiderRequestMapping("/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return evolutionService.initPage(parameterMap);
    }

    @SchneiderRequestMapping("/query_report1_columns")
    public Response queryReport1Columns(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return evolutionService.queryReport1Columns(parameterMap);
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return evolutionService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping("/query_report1_details")
    public Response queryReport1Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return evolutionService.queryReport1Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report1")
    public void downloadReport1(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        evolutionService.downloadReport1(parameterMap, response);
    }

    @SchneiderRequestMapping("/download_report1_details")
    public void downloadReport1Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        evolutionService.downloadReport1Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report1_pipeline")
    public Response queryReport1Pipeline(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return evolutionService.queryReport1Pipeline(parameterMap);
    }

    @SchneiderRequestMapping("/download_report1_pipeline")
    public void downloadReport1Pipeline(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        evolutionService.downloadReport1Pipeline(parameterMap, response);
    }
}
