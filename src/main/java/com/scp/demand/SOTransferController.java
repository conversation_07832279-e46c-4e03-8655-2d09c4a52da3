package com.scp.demand;

import com.scp.demand.service.ISOTransferService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/demand/so_transfer", parent = "menu1F0")
public class SOTransferController extends ControllerHelper {

    @Resource
    private ISOTransferService SOTransferService;

    @SchneiderRequestMapping("/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return SOTransferService.initPage(parameterMap);
    }

    @SchneiderRequestMapping("/calc")
    public Response calc(HttpServletRequest request) {
        super.pageLoad(request);
        return SOTransferService.calc(parameterMap, session.getUserid());
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        return SOTransferService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping("/download_report1")
    public void downloadReport1(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        SOTransferService.downloadReport1(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report1_logs")
    public Response queryReport1Logs(HttpServletRequest request) {
        super.pageLoad(request);
        return SOTransferService.queryReport1Logs(parameterMap);
    }

    @SchneiderRequestMapping("/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        super.pageLoad(request);
        return SOTransferService.queryReport2(parameterMap);
    }

    @SchneiderRequestMapping("/download_report2")
    public void downloadReport2(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        SOTransferService.downloadReport2(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report2_details")
    public Response queryReport2Details(HttpServletRequest request) {
        super.pageLoad(request);
        return SOTransferService.queryReport2Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report2_details")
    public void downloadReport2Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        SOTransferService.downloadReport2Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report3")
    public Response queryReport3(HttpServletRequest request) {
        super.pageLoad(request);
        return SOTransferService.queryReport3(parameterMap);
    }

    @SchneiderRequestMapping("/download_report3")
    public void downloadReport3(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        SOTransferService.downloadReport3(parameterMap, response);
    }
}
