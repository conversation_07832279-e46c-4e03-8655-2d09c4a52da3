package com.scp.demand;

import com.scp.demand.service.IMiniSIOPService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/demand/mini_siop", parent = "menu1B0")
public class MiniSIOPController extends ControllerHelper {

    @Resource
    private IMiniSIOPService miniSIOPService;

    @SchneiderRequestMapping("/init_page")
    public Response intiPage() {
        super.setGlobalCache(true);
        return miniSIOPService.intiPage();
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return miniSIOPService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping("/query_report1_details")
    public Response queryReport1Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return miniSIOPService.queryReport1Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report1_details")
    public void downloadReport1Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        miniSIOPService.downloadReport1Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/download_report1_for_minisiop_meeting")
    public void downloadReport1ForMiniSIOPMeeting(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        miniSIOPService.downloadReport1ForMiniSIOPMeeting(parameterMap, response);
    }
}
