package com.scp.demand.bean;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class OpenPmsReport2Tooltips {
    private BigDecimal OPEN_PMS_QTY;

    private static final Class<OpenPmsReport2Tooltips> clazz;
    private static final List<String> fields;

    static {
        clazz = OpenPmsReport2Tooltips.class;
        fields = Arrays.stream(clazz.getDeclaredFields()).filter(f -> f.getAnnotatedType().getType().getTypeName().equalsIgnoreCase("java.math.BigDecimal")).map(Field::getName).collect(Collectors.toList());
    }

    public OpenPmsReport2Tooltips copyOf(OpenPmsReport2Tooltips tooltips) throws Exception {
        for (String f : fields) {
            Method method = clazz.getMethod("get" + f);
            Object value = method.invoke(tooltips);
            if (value != null) {
                clazz.getMethod("set" + f, BigDecimal.class).invoke(this, (BigDecimal) value);
            }
        }
        return this;
    }

    public BigDecimal getOPEN_PMS_QTY() {
        return OPEN_PMS_QTY;
    }

    public void setOPEN_PMS_QTY(BigDecimal OPEN_PMS_QTY) {
        this.OPEN_PMS_QTY = OPEN_PMS_QTY;
    }

    public void add(OpenPmsReport2Tooltips tips) throws Exception {
        for (String f : fields) {
            Method method = clazz.getMethod("get" + f);
            Object value = method.invoke(tips);
            if (value != null) {
                BigDecimal val = BigDecimal.ZERO;
                Object valueOrg = method.invoke(this);
                if (valueOrg != null) {
                    val = (BigDecimal) valueOrg;
                }
                clazz.getMethod("set" + f, BigDecimal.class).invoke(this, val.add((BigDecimal) value));
            }
        }
    }
}
