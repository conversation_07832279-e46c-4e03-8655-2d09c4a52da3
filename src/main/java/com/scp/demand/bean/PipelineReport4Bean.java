package com.scp.demand.bean;

import java.util.ArrayList;
import java.util.List;

public class PipelineReport4Bean {
    private String projectName;
    private String projectStatus;
    private int delayFrequency;
    private int delayDepth;
    private List<String> estDateTracking;

    public final static String DF_LESS_THAN_3 = "DF < 3";
    public final static String DF_LESS_THAN_5 = "DF < 5";
    public final static String DF_EQ_OR_GREATER_5 = "DF >= 5";

    public final static String DD_LESS_THAN_3 = "DD < 3";
    public final static String DD_LESS_THAN_5 = "DD < 5";
    public final static String DD_EQ_OR_GREATER_5 = "DD >= 5";

    public List<String> getEstDateTracking() {
        return estDateTracking;
    }

    public void setEstDateTracking(List<String> estDateTracking) {
        this.estDateTracking = estDateTracking;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public String getProjectStatus() {
        return projectStatus;
    }

    public void setProjectStatus(String projectStatus) {
        this.projectStatus = projectStatus;
    }

    public int getDelayFrequency() {
        return delayFrequency;
    }

    public void setDelayFrequency(int delayFrequency) {
        this.delayFrequency = delayFrequency;
    }

    public int getDelayDepth() {
        return delayDepth;
    }

    public void setDelayDepth(int delayDepth) {
        this.delayDepth = delayDepth;
    }

    public String getDelayFrequencyName() {
        if (delayFrequency < 3) {
            return DF_LESS_THAN_3;
        } else if (delayFrequency < 5) {
            return DF_LESS_THAN_5;
        } else {
            return DF_EQ_OR_GREATER_5;
        }
    }

    public String getDelayDepthName() {
        if (delayDepth < 3) {
            return DD_LESS_THAN_3;
        } else if (delayDepth < 5) {
            return DD_LESS_THAN_5;
        } else {
            return DD_EQ_OR_GREATER_5;
        }
    }
}
