package com.scp.demand.bean;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class TrackingReport4Bean {
    private String xAxis = "";
    private BigDecimal crdQty;
    private BigDecimal crdNetNetValue;
    private BigDecimal crdCostValue;

    public String getxAxis() {
        return xAxis;
    }

    public void setxAxis(String xAxis) {
        this.xAxis = xAxis;
    }

    public BigDecimal getCrdQty() {
        return this.parseValue(crdQty);
    }

    public void setCrdQty(BigDecimal crdQty) {
        this.crdQty = crdQty;
    }

    public BigDecimal getCrdNetNetValue() {
        return this.parseValue(crdNetNetValue);
    }

    public void setCrdNetNetValue(BigDecimal crdNetNetValue) {
        this.crdNetNetValue = crdNetNetValue;
    }

    public BigDecimal getCrdCostValue() {
        return this.parseValue(crdCostValue);
    }

    public void setCrdCostValue(BigDecimal crdCostValue) {
        this.crdCostValue = crdCostValue;
    }

    private BigDecimal parseValue(BigDecimal value) {
        if (value != null) {
            return value.setScale(0, RoundingMode.HALF_UP);
        } else {
            return null;
        }
    }
}
