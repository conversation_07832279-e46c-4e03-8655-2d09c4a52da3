package com.scp.demand.bean;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class PipelineReport5Result {
    private List<String> xAxis = new ArrayList<>(); // x轴
    private List<Integer> yAxis1 = new ArrayList<>(); // DF < 3
    private List<Integer> yAxis2 = new ArrayList<>(); // DF < 5
    private List<Integer> yAxis3 = new ArrayList<>(); // DF >= 5
    private List<BigDecimal> yAxis4 = new ArrayList<>(); // DD < 3
    private List<BigDecimal> yAxis5 = new ArrayList<>(); // DD < 5
    private List<BigDecimal> yAxis6 = new ArrayList<>(); // DD >= 5


    public List<String> getxAxis() {
        return xAxis;
    }

    public void setxAxis(List<String> xAxis) {
        this.xAxis = xAxis;
    }

    public void addxAxis(String x) {
        this.xAxis.add(x);
    }

    public List<Integer> getyAxis1() {
        return yAxis1;
    }

    public void setyAxis1(List<Integer> yAxis1) {
        this.yAxis1 = yAxis1;
    }

    public void addyAxis1(Integer y) {
        this.yAxis1.add(y);
    }

    public List<Integer> getyAxis2() {
        return yAxis2;
    }

    public void setyAxis2(List<Integer> yAxis2) {
        this.yAxis2 = yAxis2;
    }

    public void addyAxis2(Integer y) {
        this.yAxis2.add(y);
    }

    public List<Integer> getyAxis3() {
        return yAxis3;
    }

    public void setyAxis3(List<Integer> yAxis3) {
        this.yAxis3 = yAxis3;
    }

    public void addyAxis3(Integer y) {
        this.yAxis3.add(y);
    }

    public List<BigDecimal> getyAxis4() {
        return yAxis4;
    }

    public void setyAxis4(List<BigDecimal> yAxis4) {
        this.yAxis4 = yAxis4;
    }

    public void addyAxis4(BigDecimal y) {
        this.yAxis4.add(y);
    }

    public List<BigDecimal> getyAxis5() {
        return yAxis5;
    }

    public void setyAxis5(List<BigDecimal> yAxis5) {
        this.yAxis5 = yAxis5;
    }

    public void addyAxis5(BigDecimal y) {
        this.yAxis5.add(y);
    }

    public List<BigDecimal> getyAxis6() {
        return yAxis6;
    }

    public void setyAxis6(List<BigDecimal> yAxis6) {
        this.yAxis6 = yAxis6;
    }

    public void addyAxis6(BigDecimal y) {
        this.yAxis6.add(y);
    }
}
