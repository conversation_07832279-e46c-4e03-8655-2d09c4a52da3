package com.scp.demand.bean;

public class MiniSIOP {
    public static final String X_DEMAND = "Demand";
    public static final String X_STD_SALES = "STD Sales";
    public static final String X_STD_OI = "STD Order Intake";
    public static final String X_STD_CRD = "STD CRD";
    public static final String X_SOH_DEMAND = "SOH[Demand]";
    public static final String X_GIT_DEMAND = "GIT[Demand]";
    public static final String X_CONF_RES_DEMAND = "Conf.Reource[Demand]";
    public static final String X_ORDERED_DEMAND = "Ordered Demand";
    public static final String X_SOH_ORDER = "SOH[Order]";
    public static final String X_GIT_ORDER = "GIT[Order]";
    public static final String X_CONF_RES_ORDER = "Conf.Reource[Order]";

    public static final String Y_SALES_EX_FCST_GT100 = "Sales Exceed FCST >100%";
    public static final String Y_SALES_EX_FCST_GT70 = "Sales Exceed FCST >70%";
    public static final String Y_SALES_EX_FCST_GT30 = "Sales Exceed FCST >30%";
    public static final String Y_SALES_EX_FCST_LT30 = "Sales Exceed FCST <=30%";
    public static final String Y_PREDICTED_SALES = "Predicted Sales";
    public static final String Y_PREDICTED_OI = "Predicted OI";
    public static final String Y_PREDICTED_CRD = "Predicted CRD";
    public static final String Y_OI_EX_FCST_GT100 = "OI Exceed FCST >100%";
    public static final String Y_OI_EX_FCST_GT70 = "OI Exceed FCST >70%";
    public static final String Y_OI_EX_FCST_GT30 = "OI Exceed FCST >30%";
    public static final String Y_OI_EX_FCST_LT30 = "OI Exceed FCST <=30%";
    public static final String Y_UNREALIZED_LT30_FCST = "Unrealized <=30% FCST";
    public static final String Y_UNREALIZED_GT30_FCST = "Unrealized >30% FCST";
    public static final String Y_UNREALIZED_GT70_FCST = "Unrealized >70% FCST";
    public static final String Y_IG_SALES = "IG Sales";
    public static final String Y_IG_OI = "IG OI";
    public static final String Y_IG_CRD = "IG CRD";
    public static final String Y_CRD_EX_FCST_GT100 = "CRD Exceed FCST >100%";
    public static final String Y_CRD_EX_FCST_GT70 = "CRD Exceed FCST >70%";
    public static final String Y_CRD_EX_FCST_GT30 = "CRD Exceed FCST >30%";
    public static final String Y_CRD_EX_FCST_LT30 = "CRD Exceed FCST <=30%";
    public static final String Y_STOCK_AGING_GT2Y = "Stock Aging >2Y";
    public static final String Y_STOCK_AGING_GT1Y = "Stock Aging >1Y";
    public static final String Y_FOR_FUTURE_DEMAND = "For Future Demand";
    public static final String Y_FOR_SAFETY_STOCK = "For Safety Stock";
    public static final String Y_FOR_IG_ORDER_IN_SP = "For IG Order in Selected Period";
    public static final String Y_IG_UD = "IG Un-Issue Delivery";
    public static final String Y_FOR_DEMAND_IN_SP = "For Demand in Selected Period";
    public static final String Y_UD_GT30CD = "Un-Issue Delivery >30CD";
    public static final String Y_UD_GT7CD = "Un-Issue Delivery >7CD";
    public static final String Y_UD_LT7CD = "Un-Issue Delivery <=7CD";
    public static final String Y_CRD_OUT_SP = "CRD Out of Selected Period";
    public static final String Y_CRD_IN_SP = "CRD in Selected Period";
    public static final String Y_BACK_ORDER = "Back Order";
    public static final String Y_FOR_FUTURE_ORDER = "For Future Order";
    public static final String Y_FOR_ORDER_IN_SP = "For Order in Selected Period";
    public static final String Y_ACTUAL_SALES_BEFORE_SF = "Actual Sales Before Selected FCST";
    public static final String Y_ACTUAL_OI_BEFORE_SF = "Actual OI Before Selected FCST";
    public static final String Y_ACTUAL_CRD_BEFORE_SF = "Actual CRD Before Selected FCST";
}
