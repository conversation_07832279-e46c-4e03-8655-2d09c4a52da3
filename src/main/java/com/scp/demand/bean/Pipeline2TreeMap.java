package com.scp.demand.bean;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Pipeline2TreeMap {
    private BigDecimal value;
    private String name;
    private String path;
    private List<Pipeline2TreeMap> children = new ArrayList<>();
    private Map<String, Object> itemStyle = new HashMap<>();

    public Pipeline2TreeMap() {

    }

    public Pipeline2TreeMap(String name, BigDecimal value, String color) {
        this.name = name;
        this.value = value;
        this.itemStyle.put("color", color);
    }

    public void setItemColor(String color) {
        itemStyle.put("color", color);
    }

    public Map<String, Object> getItemStyle() {
        return itemStyle;
    }

    public void setItemStyle(Map<String, Object> itemStyle) {
        this.itemStyle = itemStyle;
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPath() {
        return path == null ? name : path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public List<Pipeline2TreeMap> getChildren() {
        return children;
    }

    public void setChildren(List<Pipeline2TreeMap> children) {
        this.children = children;
    }

    public void addChild(Pipeline2TreeMap child) {
        this.children.add(child);
    }

}
