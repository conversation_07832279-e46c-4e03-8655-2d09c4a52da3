package com.scp.demand.bean;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class TrackingReport2Bean {
    private String xAxis = "";
    private BigDecimal salesQty = BigDecimal.ZERO;
    private BigDecimal salesNetNetValue = BigDecimal.ZERO;
    private BigDecimal salesCostValue = BigDecimal.ZERO;
    private BigDecimal salesLine = BigDecimal.ZERO;
    private BigDecimal orderIntakeQty = BigDecimal.ZERO;
    private BigDecimal orderIntakeNetNetValue = BigDecimal.ZERO;
    private BigDecimal orderIntakeCostValue = BigDecimal.ZERO;
    private BigDecimal orderIntakeLine = BigDecimal.ZERO;

    private BigDecimal outputQty = BigDecimal.ZERO;
    private BigDecimal outputNetNetValue = BigDecimal.ZERO;
    private BigDecimal outputCostValue = BigDecimal.ZERO;
    private BigDecimal outputLine = BigDecimal.ZERO;

    private BigDecimal salesQtyY_1 = BigDecimal.ZERO;
    private BigDecimal salesNetNetValueY_1 = BigDecimal.ZERO;
    private BigDecimal salesCostValueY_1 = BigDecimal.ZERO;
    private BigDecimal salesLineY_1 = BigDecimal.ZERO;
    private BigDecimal orderIntakeQtyY_1 = BigDecimal.ZERO;
    private BigDecimal orderIntakeNetNetValueY_1 = BigDecimal.ZERO;
    private BigDecimal orderIntakeCostValueY_1 = BigDecimal.ZERO;
    private BigDecimal orderIntakeLineY_1 = BigDecimal.ZERO;

    private BigDecimal outputQtyY_1 = BigDecimal.ZERO;
    private BigDecimal outputNetNetValueY_1 = BigDecimal.ZERO;
    private BigDecimal outputCostValueY_1 = BigDecimal.ZERO;
    private BigDecimal outputLineY_1 = BigDecimal.ZERO;

    private BigDecimal aduQty = BigDecimal.ZERO;
    private BigDecimal aduNetNetValue = BigDecimal.ZERO;
    private BigDecimal aduCostValue = BigDecimal.ZERO;
    private BigDecimal aduLine = BigDecimal.ZERO;

    private BigDecimal adfQty = BigDecimal.ZERO;
    private BigDecimal adfNetNetValue = BigDecimal.ZERO;
    private BigDecimal adfCostValue = BigDecimal.ZERO;
    private BigDecimal adfLine = BigDecimal.ZERO;

    private BigDecimal adfFulfillQty = BigDecimal.ZERO;
    private BigDecimal adfFulfillNetNetValue = BigDecimal.ZERO;
    private BigDecimal adfFulfillCostValue = BigDecimal.ZERO;
    private BigDecimal adfFulfillLine = BigDecimal.ZERO;

    private boolean isLastYearData = false;

    public TrackingReport2Bean() {

    }

    public TrackingReport2Bean(TrackingReport2Bean bean) {
        this.initial = bean.isInitial();
        this.isLastYearData = bean.isLastYearData();
        this.xAxis = bean.getxAxis();
        this.salesQty = bean.getSalesQty();
        this.salesNetNetValue = bean.getSalesNetNetValue();
        this.salesCostValue = bean.getSalesCostValue();
        this.salesLine = bean.getSalesLine();
        this.orderIntakeQty = bean.getOrderIntakeQty();
        this.orderIntakeNetNetValue = bean.getOrderIntakeNetNetValue();
        this.orderIntakeCostValue = bean.getOrderIntakeCostValue();
        this.orderIntakeLine = bean.getOrderIntakeLine();
        this.outputQty = bean.getOutputQty();
        this.outputNetNetValue = bean.getOutputNetNetValue();
        this.outputCostValue = bean.getOutputCostValue();
        this.outputLine = bean.getOutputLine();

        this.salesQtyY_1 = bean.getSalesQtyY_1();
        this.salesNetNetValueY_1 = bean.getSalesNetNetValueY_1();
        this.salesCostValueY_1 = bean.getSalesCostValueY_1();
        this.salesLineY_1 = bean.getSalesLineY_1();
        this.orderIntakeQtyY_1 = bean.getOrderIntakeQtyY_1();
        this.orderIntakeNetNetValueY_1 = bean.getOrderIntakeNetNetValueY_1();
        this.orderIntakeCostValueY_1 = bean.getOrderIntakeCostValueY_1();
        this.orderIntakeLineY_1 = bean.getOrderIntakeLineY_1();
        this.outputQtyY_1 = bean.getOutputQtyY_1();
        this.outputNetNetValueY_1 = bean.getOutputNetNetValueY_1();
        this.outputCostValueY_1 = bean.getOutputCostValueY_1();
        this.outputLineY_1 = bean.getOutputLineY_1();
        this.aduQty = bean.getAduQty();
        this.aduNetNetValue = bean.getAduNetNetValue();
        this.aduCostValue = bean.getAduCostValue();
        this.aduLine = bean.getAduLine();

        this.adfQty = bean.getAdfQty();
        this.adfNetNetValue = bean.getAdfNetNetValue();
        this.adfCostValue = bean.getAdfCostValue();
        this.adfLine = bean.adfLine;

        this.adfFulfillQty = bean.getAdfFulfillQty();
        this.adfFulfillNetNetValue = bean.getAdfFulfillNetNetValue();
        this.adfFulfillCostValue = bean.getAdfFulfillCostValue();
        this.adfFulfillLine = bean.adfFulfillLine;
    }

    private boolean initial = true;

    public void add(TrackingReport2Bean bean) {
        this.salesQty = this.getSalesQty().add(bean.getSalesQty());
        this.salesNetNetValue = this.getSalesNetNetValue().add(bean.getSalesNetNetValue());
        this.salesCostValue = this.getSalesCostValue().add(bean.getSalesCostValue());
        this.salesLine = this.getSalesLine().add(bean.getSalesLine());
        this.orderIntakeQty = this.getOrderIntakeQty().add(bean.getOrderIntakeQty());
        this.orderIntakeNetNetValue = this.getOrderIntakeNetNetValue().add(bean.getOrderIntakeNetNetValue());
        this.orderIntakeCostValue = this.getOrderIntakeCostValue().add(bean.getOrderIntakeCostValue());
        this.orderIntakeLine = this.getOrderIntakeLine().add(bean.getOrderIntakeLine());
        this.outputQty = this.getOutputQty().add(bean.getOutputQty());
        this.outputNetNetValue = this.getOutputNetNetValue().add(bean.getOutputNetNetValue());
        this.outputCostValue = this.getOutputCostValue().add(bean.getOutputCostValue());
        this.outputLine = this.getOutputLine().add(bean.getOutputLine());

        this.salesQtyY_1 = this.getSalesQtyY_1().add(bean.getSalesQtyY_1());
        this.salesNetNetValueY_1 = this.getSalesNetNetValueY_1().add(bean.getSalesNetNetValueY_1());
        this.salesCostValueY_1 = this.getSalesCostValueY_1().add(bean.getSalesCostValueY_1());
        this.salesLineY_1 = this.getSalesLineY_1().add(bean.getSalesLineY_1());
        this.orderIntakeQtyY_1 = this.getOrderIntakeQtyY_1().add(bean.getOrderIntakeQtyY_1());
        this.orderIntakeNetNetValueY_1 = this.getOrderIntakeNetNetValueY_1().add(bean.getOrderIntakeNetNetValueY_1());
        this.orderIntakeCostValueY_1 = this.getOrderIntakeCostValueY_1().add(bean.getOrderIntakeCostValueY_1());
        this.orderIntakeLineY_1 = this.getOrderIntakeLineY_1().add(bean.getOrderIntakeLineY_1());
        this.outputQtyY_1 = this.getOutputQtyY_1().add(bean.getOutputQtyY_1());
        this.outputNetNetValueY_1 = this.getOutputNetNetValueY_1().add(bean.getOutputNetNetValueY_1());
        this.outputCostValueY_1 = this.getOutputCostValueY_1().add(bean.getOutputCostValueY_1());
        this.outputLineY_1 = this.getOutputLineY_1().add(bean.getOutputLineY_1());
        this.initial = false;
    }

    public String getxAxis() {
        return xAxis;
    }

    public void setxAxis(String xAxis) {
        this.xAxis = xAxis;
    }

    public BigDecimal getSalesQty() {
        return this.parseValue(salesQty);
    }

    public void setSalesQty(BigDecimal salesQty) {
        this.salesQty = salesQty;
    }

    public BigDecimal getSalesNetNetValue() {
        return this.parseValue(salesNetNetValue);
    }

    public void setSalesNetNetValue(BigDecimal salesNetNetValue) {
        this.salesNetNetValue = salesNetNetValue;
    }

    public BigDecimal getSalesCostValue() {
        return this.parseValue(salesCostValue);
    }

    public void setSalesCostValue(BigDecimal salesCostValue) {
        this.salesCostValue = salesCostValue;
    }

    public BigDecimal getOrderIntakeQty() {
        return this.parseValue(orderIntakeQty);
    }

    public void setOrderIntakeQty(BigDecimal orderIntakeQty) {
        this.orderIntakeQty = orderIntakeQty;
    }

    public BigDecimal getOrderIntakeNetNetValue() {
        return this.parseValue(orderIntakeNetNetValue);
    }

    public void setOrderIntakeNetNetValue(BigDecimal orderIntakeNetNetValue) {
        this.orderIntakeNetNetValue = orderIntakeNetNetValue;
    }

    public BigDecimal getOrderIntakeCostValue() {
        return this.parseValue(orderIntakeCostValue);
    }

    public void setOrderIntakeCostValue(BigDecimal orderIntakeCostValue) {
        this.orderIntakeCostValue = orderIntakeCostValue;
    }

    public BigDecimal getOrderIntakeLine() {
        return this.parseValue(orderIntakeLine);
    }

    public void setOrderIntakeLine(BigDecimal orderIntakeLine) {
        this.orderIntakeLine = orderIntakeLine;
    }

    public BigDecimal getSalesQtyY_1() {
        return this.parseValue(salesQtyY_1);
    }

    public void setSalesQtyY_1(BigDecimal salesQtyY_1) {
        this.salesQtyY_1 = salesQtyY_1;
    }

    public BigDecimal getSalesNetNetValueY_1() {
        return this.parseValue(salesNetNetValueY_1);
    }

    public void setSalesNetNetValueY_1(BigDecimal salesNetNetValueY_1) {
        this.salesNetNetValueY_1 = salesNetNetValueY_1;
    }

    public BigDecimal getSalesCostValueY_1() {
        return this.parseValue(salesCostValueY_1);
    }

    public void setSalesCostValueY_1(BigDecimal salesCostValueY_1) {
        this.salesCostValueY_1 = salesCostValueY_1;
    }

    public BigDecimal getOrderIntakeQtyY_1() {
        return this.parseValue(orderIntakeQtyY_1);
    }

    public void setOrderIntakeQtyY_1(BigDecimal orderIntakeQtyY_1) {
        this.orderIntakeQtyY_1 = orderIntakeQtyY_1;
    }

    public BigDecimal getOrderIntakeNetNetValueY_1() {
        return this.parseValue(orderIntakeNetNetValueY_1);
    }

    public void setOrderIntakeNetNetValueY_1(BigDecimal orderIntakeNetNetValueY_1) {
        this.orderIntakeNetNetValueY_1 = orderIntakeNetNetValueY_1;
    }

    public BigDecimal getOrderIntakeCostValueY_1() {
        return this.parseValue(orderIntakeCostValueY_1);
    }

    public void setOrderIntakeCostValueY_1(BigDecimal orderIntakeCostValueY_1) {
        this.orderIntakeCostValueY_1 = orderIntakeCostValueY_1;
    }

    public BigDecimal getOrderIntakeLineY_1() {
        return this.parseValue(orderIntakeLineY_1);
    }

    public void setOrderIntakeLineY_1(BigDecimal orderIntakeLineY_1) {
        this.orderIntakeLineY_1 = orderIntakeLineY_1;
    }

    public BigDecimal getAduQty() {
        return this.parseValue(aduQty);
    }

    public void setAduQty(BigDecimal aduQty) {
        this.aduQty = aduQty;
    }

    public BigDecimal getAduNetNetValue() {
        return this.parseValue(aduNetNetValue);
    }

    public void setAduNetNetValue(BigDecimal aduNetNetValue) {
        this.aduNetNetValue = aduNetNetValue;
    }

    public BigDecimal getAduCostValue() {
        return this.parseValue(aduCostValue);
    }

    public void setAduCostValue(BigDecimal aduCostValue) {
        this.aduCostValue = aduCostValue;
    }

    public BigDecimal getAdfQty() {
        return this.parseValue(adfQty);
    }

    public void setAdfQty(BigDecimal adfQty) {
        this.adfQty = adfQty;
    }

    public BigDecimal getAdfNetNetValue() {
        return this.parseValue(adfNetNetValue);
    }

    public void setAdfNetNetValue(BigDecimal adfNetNetValue) {
        this.adfNetNetValue = adfNetNetValue;
    }

    public BigDecimal getAdfCostValue() {
        return this.parseValue(adfCostValue);
    }

    public void setAdfCostValue(BigDecimal adfCostValue) {
        this.adfCostValue = adfCostValue;
    }

    public boolean isInitial() {
        return initial;
    }

    public void setInitial(boolean initial) {
        this.initial = initial;
    }

    public BigDecimal getSalesLine() {
        return this.parseValue(salesLine);
    }

    public void setSalesLine(BigDecimal salesLine) {
        this.salesLine = salesLine;
    }

    public BigDecimal getSalesLineY_1() {
        return salesLineY_1;
    }

    public void setSalesLineY_1(BigDecimal salesLineY_1) {
        this.salesLineY_1 = salesLineY_1;
    }

    public boolean isLastYearData() {
        return isLastYearData;
    }

    public void setLastYearData(boolean lastYearData) {
        isLastYearData = lastYearData;
    }

    public BigDecimal getAduLine() {
        return aduLine;
    }

    public void setAduLine(BigDecimal aduLine) {
        this.aduLine = aduLine;
    }

    public BigDecimal getAdfLine() {
        return adfLine;
    }

    public void setAdfLine(BigDecimal adfLine) {
        this.adfLine = adfLine;
    }

    public BigDecimal getOutputQty() {
        return this.parseValue(outputQty);
    }

    public void setOutputQty(BigDecimal outputQty) {
        this.outputQty = outputQty;
    }

    public BigDecimal getOutputNetNetValue() {
        return this.parseValue(outputNetNetValue);
    }

    public void setOutputNetNetValue(BigDecimal outputNetNetValue) {
        this.outputNetNetValue = outputNetNetValue;
    }

    public BigDecimal getOutputCostValue() {
        return this.parseValue(outputCostValue);
    }

    public void setOutputCostValue(BigDecimal outputCostValue) {
        this.outputCostValue = outputCostValue;
    }

    public BigDecimal getOutputLine() {
        return this.parseValue(outputLine);
    }

    public void setOutputLine(BigDecimal outputLine) {
        this.outputLine = outputLine;
    }

    public BigDecimal getOutputQtyY_1() {
        return this.parseValue(outputQtyY_1);
    }

    public void setOutputQtyY_1(BigDecimal outputQtyY_1) {
        this.outputQtyY_1 = outputQtyY_1;
    }

    public BigDecimal getOutputNetNetValueY_1() {
        return this.parseValue(outputNetNetValueY_1);
    }

    public void setOutputNetNetValueY_1(BigDecimal outputNetNetValueY_1) {
        this.outputNetNetValueY_1 = outputNetNetValueY_1;
    }

    public BigDecimal getOutputCostValueY_1() {
        return this.parseValue(outputCostValueY_1);
    }

    public void setOutputCostValueY_1(BigDecimal outputCostValueY_1) {
        this.outputCostValueY_1 = outputCostValueY_1;
    }

    public BigDecimal getOutputLineY_1() {
        return this.parseValue(outputLineY_1);
    }

    public void setOutputLineY_1(BigDecimal outputLineY_1) {
        this.outputLineY_1 = outputLineY_1;
    }

    public BigDecimal getAdfFulfillQty() {
        return adfFulfillQty;
    }

    public void setAdfFulfillQty(BigDecimal adfFulfillQty) {
        this.adfFulfillQty = adfFulfillQty;
    }

    public BigDecimal getAdfFulfillNetNetValue() {
        return adfFulfillNetNetValue;
    }

    public void setAdfFulfillNetNetValue(BigDecimal adfFulfillNetNetValue) {
        this.adfFulfillNetNetValue = adfFulfillNetNetValue;
    }

    public BigDecimal getAdfFulfillCostValue() {
        return adfFulfillCostValue;
    }

    public void setAdfFulfillCostValue(BigDecimal adfFulfillCostValue) {
        this.adfFulfillCostValue = adfFulfillCostValue;
    }

    public BigDecimal getAdfFulfillLine() {
        return adfFulfillLine;
    }

    public void setAdfFulfillLine(BigDecimal adfFulfillLine) {
        this.adfFulfillLine = adfFulfillLine;
    }

    private BigDecimal parseValue(BigDecimal value) {
        if (value != null) {
            return value.setScale(0, RoundingMode.HALF_UP);
        } else {
            return BigDecimal.ZERO;
        }
    }
}
