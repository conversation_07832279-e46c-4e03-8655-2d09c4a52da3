package com.scp.demand.bean;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class OpenPmsReport2Treemap {
    private String name;
    private BigDecimal value;
    private OpenSOReport2Tooltips tips = new OpenSOReport2Tooltips();
    private List<OpenPmsReport2Treemap> children;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        if (value == null) value = BigDecimal.ZERO;
        else this.value = value;
    }

    public List<OpenPmsReport2Treemap> getChildren() {
        if (children == null) {
            children = new ArrayList<>();
        }
        return children;
    }

    public boolean hasChildren() {
        return this.getChildren() != null && !this.getChildren().isEmpty();
    }

    public void setChildren(List<OpenPmsReport2Treemap> children) {
        this.children = children;
    }

    public void setTips(OpenSOReport2Tooltips tips) {
        this.tips = tips;
    }

    public OpenSOReport2Tooltips getTips() {
        return tips;
    }

    // 合并两个节点
    public void add(OpenPmsReport2Treemap addElement) throws Exception {
        OpenPmsReport2Treemap mainElement = this;

        mainElement.getTips().add(addElement.getTips()); // 先相加根节点

        // 再相加子节点
        while (addElement.hasChildren()) {
            List<OpenPmsReport2Treemap> mainChildren = mainElement.getChildren();
            OpenPmsReport2Treemap child = addElement.getChildren().get(0); // 加数节点只有一个子节点

            Optional<OpenPmsReport2Treemap> beanOpt = mainChildren.stream().filter(b -> b.getName().equals(child.getName())).findFirst();
            if (beanOpt.isPresent()) {
                OpenPmsReport2Treemap bean = beanOpt.get();
                bean.getTips().add(child.getTips()); // 如果找到了, 那就合并两个子节点

                // 向下移动一层
                addElement = child;
                mainElement = bean;
            } else {
                mainChildren.add(child);// 如果找不到子节点, 那直接把需要相加的节点附在这个子节点下面
                break; // 然后直接跳出循环, 相加结束
            }
        }
    }
}
