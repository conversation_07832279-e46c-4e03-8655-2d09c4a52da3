package com.scp.demand.bean;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

public class PipelineReport6Result {
    private List<String> xAxis = new ArrayList<>(); // x轴
    private List<BigDecimal> yAxis1 = new ArrayList<>();
    private List<BigDecimal> yAxis2 = new ArrayList<>();


    public List<String> getxAxis() {
        return xAxis;
    }

    public void setxAxis(List<String> xAxis) {
        this.xAxis = xAxis;
    }

    public void addxAxis(String x) {
        this.xAxis.add(x);
    }

    public List<BigDecimal> getyAxis1() {
        return yAxis1;
    }

    public void setyAxis1(List<BigDecimal> yAxis1) {
        this.yAxis1 = yAxis1;
    }

    public void addyAxis1(BigDecimal y) {
        if (y == null) {
            this.yAxis1.add(null);
        } else {
            this.yAxis1.add(y.multiply(new BigDecimal("100")).setScale(1, RoundingMode.HALF_UP));
        }
    }

    public List<BigDecimal> getyAxis2() {
        return yAxis2;
    }

    public void setyAxis2(List<BigDecimal> yAxis2) {
        this.yAxis2 = yAxis2;
    }

    public void addyAxis2(BigDecimal y) {
        if (y == null) {
            this.yAxis2.add(null);
        } else {
            this.yAxis2.add(y.setScale(0, RoundingMode.HALF_UP));
        }
    }
}
