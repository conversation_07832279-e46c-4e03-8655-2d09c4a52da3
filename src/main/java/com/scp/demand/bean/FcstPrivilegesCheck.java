package com.scp.demand.bean;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class FcstPrivilegesCheck {

    private boolean passed = true;
    private List<String> salesOrg = new ArrayList<>();
    private List<String> customerCode = new ArrayList<>();

    public boolean isPassed() {
        return passed;
    }

    public void setPassed(boolean passed) {
        this.passed = passed;
    }

    public List<String> getSalesOrg() {
        return salesOrg;
    }

    public void setSalesOrg(List<String> salesOrg) {
        this.salesOrg = salesOrg;
    }

    public void addSalesOrg(String org) {
        if (this.salesOrg.contains(org) == false) {
            this.salesOrg.add(org);
        }
    }

    public void addCustomerCode(String code) {
        if (this.customerCode.contains(code) == false) {
            this.customerCode.add(code);
        }
    }

    public List<String> getCustomerCode() {
        return customerCode;
    }

    public void setCustomerCode(List<String> customerCode) {
        this.customerCode = customerCode;
    }

    public String getSalesOrgs() {
        return StringUtils.join(salesOrg, ", ");
    }

    public String getCustomerCodes() {
        return StringUtils.join(customerCode, ", ");
    }
}
