package com.scp.demand.bean;

import com.starter.utils.Utils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

public class TrackingTestReport1Bean {
    private String id;
    private String category1;
    private String category2;
    protected BigDecimal salesQty;
    protected BigDecimal salesNetNetValue;
    protected BigDecimal salesCostValue;
    protected BigDecimal salesLine;
    protected BigDecimal orderIntakeQty;
    protected BigDecimal orderIntakeNetNetValue;
    protected BigDecimal orderIntakeCostValue;
    protected BigDecimal orderIntakeLine;
    protected BigDecimal crdQty;
    protected BigDecimal crdNetNetValue;
    protected BigDecimal crdCostValue;
    protected BigDecimal crdLine;
    protected BigDecimal fcstQty;
    protected BigDecimal fcstNetNetValue;
    protected BigDecimal fcstCostValue;
    protected BigDecimal fcstLine;
    protected BigDecimal fcstFulfillQty;
    protected BigDecimal fcstFulfillNetNetValue;
    protected BigDecimal fcstFulfillCostValue;
    protected BigDecimal fcstFulfillLine;
    protected BigDecimal backorderQty;
    protected BigDecimal backorderNetNetValue;
    protected BigDecimal backorderCostValue;
    protected BigDecimal backorderLine;
    protected BigDecimal backlogQty;
    protected BigDecimal backlogNetNetValue;
    protected BigDecimal backlogCostValue;
    protected BigDecimal backlogLine;
    protected BigDecimal sohQty;
    protected BigDecimal sohNetNetValue;
    protected BigDecimal sohCostValue;
    protected BigDecimal sohLine;
    protected BigDecimal udQty;
    protected BigDecimal udNetNetValue;
    protected BigDecimal udCostValue;
    protected BigDecimal udLine;

    protected BigDecimal outputQty;
    protected BigDecimal outputNetNetValue;
    protected BigDecimal outputCostValue;
    protected BigDecimal outputLine;

    private boolean warning = false;
    private boolean hasChildren = true;

    private String parentName;

    public TrackingTestReport1Bean() {
        id = Utils.randomStr(16);
    }

    public void addToSummary(TrackingTestReport1Bean bean) {
        this.salesQty = this.add(this.salesQty, bean.salesQty);
        this.salesNetNetValue = this.add(this.salesNetNetValue, bean.salesNetNetValue);
        this.salesCostValue = this.add(this.salesCostValue, bean.salesCostValue);
        this.salesLine = this.add(this.salesLine, bean.salesLine);
        this.orderIntakeQty = this.add(this.orderIntakeQty, bean.orderIntakeQty);
        this.orderIntakeNetNetValue = this.add(this.orderIntakeNetNetValue, bean.orderIntakeNetNetValue);
        this.orderIntakeCostValue = this.add(this.orderIntakeCostValue, bean.orderIntakeCostValue);
        this.orderIntakeLine = this.add(this.orderIntakeLine, bean.orderIntakeLine);
        this.crdQty = this.add(this.crdQty, bean.crdQty);
        this.crdNetNetValue = this.add(this.crdNetNetValue, bean.crdNetNetValue);
        this.crdCostValue = this.add(this.crdCostValue, bean.crdCostValue);
        this.crdLine = this.add(this.crdLine, bean.crdLine);
        this.fcstQty = this.add(this.fcstQty, bean.fcstQty);
        this.fcstNetNetValue = this.add(this.fcstNetNetValue, bean.fcstNetNetValue);
        this.fcstCostValue = this.add(this.fcstCostValue, bean.fcstCostValue);
        this.fcstLine = this.add(this.fcstLine, bean.fcstLine);
        this.fcstFulfillQty = this.add(this.fcstFulfillQty, bean.fcstFulfillQty);
        this.fcstFulfillNetNetValue = this.add(this.fcstFulfillNetNetValue, bean.fcstFulfillNetNetValue);
        this.fcstFulfillCostValue = this.add(this.fcstFulfillCostValue, bean.fcstFulfillCostValue);
        this.fcstFulfillLine = this.add(this.fcstFulfillLine, bean.fcstFulfillLine);
        this.backorderQty = this.add(this.backorderQty, bean.backorderQty);
        this.backorderNetNetValue = this.add(this.backorderNetNetValue, bean.backorderNetNetValue);
        this.backorderCostValue = this.add(this.backorderCostValue, bean.backorderCostValue);
        this.backorderLine = this.add(this.backorderLine, bean.backorderLine);
        this.backlogQty = this.add(this.backlogQty, bean.backlogQty);
        this.backlogNetNetValue = this.add(this.backlogNetNetValue, bean.backlogNetNetValue);
        this.backlogCostValue = this.add(this.backlogCostValue, bean.backlogCostValue);
        this.backlogLine = this.add(this.backlogLine, bean.backlogLine);
        this.sohQty = this.add(this.sohQty, bean.sohQty);
        this.sohNetNetValue = this.add(this.sohNetNetValue, bean.sohNetNetValue);
        this.sohCostValue = this.add(this.sohCostValue, bean.sohCostValue);
        this.sohLine = this.add(this.sohLine, bean.sohLine);
        this.udQty = this.add(this.udQty, bean.udQty);
        this.udNetNetValue = this.add(this.udNetNetValue, bean.udNetNetValue);
        this.udCostValue = this.add(this.udCostValue, bean.udCostValue);
        this.udLine = this.add(this.udLine, bean.udLine);
        this.outputQty = this.add(this.outputQty, bean.outputQty);
        this.outputNetNetValue = this.add(this.outputNetNetValue, bean.outputNetNetValue);
        this.outputCostValue = this.add(this.outputCostValue, bean.outputCostValue);
        this.outputLine = this.add(this.outputLine, bean.outputLine);
    }

    public BigDecimal add(BigDecimal a, BigDecimal b) {
        if (a == null && b == null) {
            return null;
        } else if (a != null && b != null) {
            return a.add(b);
        } else return Objects.requireNonNullElse(a, b);
    }

    public void addFcstQty(BigDecimal fcstQty) {
        if (this.fcstQty == null) {
            this.fcstQty = BigDecimal.ZERO;
        }
        if (fcstQty == null) {
            return;
        }
        this.fcstQty = this.fcstQty.add(fcstQty);
    }

    public void addFcstCostValue(BigDecimal fcstCostValue) {
        if (this.fcstCostValue == null) {
            this.fcstCostValue = BigDecimal.ZERO;
        }
        if (fcstCostValue == null) {
            return;
        }
        this.fcstCostValue = this.fcstCostValue.add(fcstCostValue);
    }

    public void addFcstNetNetValue(BigDecimal NetNetValue) {
        if (this.fcstNetNetValue == null) {
            this.fcstNetNetValue = BigDecimal.ZERO;
        }
        if (NetNetValue == null) {
            return;
        }
        this.fcstNetNetValue = this.fcstNetNetValue.add(NetNetValue);
    }

    public void addFcstFulfillQty(BigDecimal fcstFulfillQty) {
        if (this.fcstFulfillQty == null) {
            this.fcstFulfillQty = BigDecimal.ZERO;
        }
        if (fcstFulfillQty == null) {
            return;
        }
        this.fcstFulfillQty = this.fcstFulfillQty.add(fcstFulfillQty);
    }

    public void addFcstFulfillCostValue(BigDecimal fcstFulfillCostValue) {
        if (this.fcstFulfillCostValue == null) {
            this.fcstFulfillCostValue = BigDecimal.ZERO;
        }
        if (fcstFulfillCostValue == null) {
            return;
        }
        this.fcstFulfillCostValue = this.fcstFulfillCostValue.add(fcstFulfillCostValue);
    }

    public void addFcstFulfillNetNetValue(BigDecimal fcstFulfillNetNetValue) {
        if (this.fcstFulfillNetNetValue == null) {
            this.fcstFulfillNetNetValue = BigDecimal.ZERO;
        }
        if (fcstFulfillNetNetValue == null) {
            return;
        }
        this.fcstFulfillNetNetValue = this.fcstFulfillNetNetValue.add(fcstFulfillNetNetValue);
    }

    public BigDecimal getSalesLine() {
        return salesLine;
    }

    public void setSalesLine(BigDecimal salesLine) {
        this.salesLine = salesLine;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCategory1() {
        return category1;
    }

    public void setCategory1(String category1) {
        this.category1 = category1;
    }

    public String getCategory2() {
        return category2;
    }

    public void setCategory2(String category2) {
        this.category2 = category2;
    }

    public BigDecimal getSalesQty() {
        return this.parseValue(salesQty);
    }

    public void setSalesQty(BigDecimal salesQty) {
        this.salesQty = salesQty;
    }

    public BigDecimal getSalesNetNetValue() {
        return this.parseValue(salesNetNetValue);
    }

    public void setSalesNetNetValue(BigDecimal salesNetNetValue) {
        this.salesNetNetValue = salesNetNetValue;
    }

    public BigDecimal getSalesCostValue() {
        return this.parseValue(salesCostValue);
    }

    public void setSalesCostValue(BigDecimal salesCostValue) {
        this.salesCostValue = salesCostValue;
    }

    public BigDecimal getOrderIntakeQty() {
        return this.parseValue(orderIntakeQty);
    }

    public void setOrderIntakeQty(BigDecimal orderIntakeQty) {
        this.orderIntakeQty = orderIntakeQty;
    }

    public BigDecimal getOrderIntakeNetNetValue() {
        return this.parseValue(orderIntakeNetNetValue);
    }

    public void setOrderIntakeNetNetValue(BigDecimal orderIntakeNetNetValue) {
        this.orderIntakeNetNetValue = orderIntakeNetNetValue;
    }

    public BigDecimal getOrderIntakeCostValue() {
        return this.parseValue(orderIntakeCostValue);
    }

    public void setOrderIntakeCostValue(BigDecimal orderIntakeCostValue) {
        this.orderIntakeCostValue = orderIntakeCostValue;
    }

    public BigDecimal getOrderIntakeLine() {
        return this.parseValue(orderIntakeLine);
    }

    public void setOrderIntakeLine(BigDecimal orderIntakeLine) {
        this.orderIntakeLine = orderIntakeLine;
    }

    public BigDecimal getCrdQty() {
        return this.parseValue(crdQty);
    }

    public void setCrdQty(BigDecimal crdQty) {
        this.crdQty = crdQty;
    }

    public BigDecimal getCrdNetNetValue() {
        return this.parseValue(crdNetNetValue);
    }

    public void setCrdNetNetValue(BigDecimal crdNetNetValue) {
        this.crdNetNetValue = crdNetNetValue;
    }

    public BigDecimal getCrdCostValue() {
        return this.parseValue(crdCostValue);
    }

    public void setCrdCostValue(BigDecimal crdCostValue) {
        this.crdCostValue = crdCostValue;
    }

    public BigDecimal getCrdLine() {
        return this.parseValue(crdLine);
    }

    public void setCrdLine(BigDecimal crdLine) {
        this.crdLine = crdLine;
    }

    public BigDecimal getFcstQty() {
        return this.parseValue(fcstQty);
    }

    public void setFcstQty(BigDecimal fcstQty) {
        this.fcstQty = fcstQty;
    }

    public BigDecimal getFcstNetNetValue() {
        return this.parseValue(fcstNetNetValue);
    }

    public void setFcstNetNetValue(BigDecimal fcstNetNetValue) {
        this.fcstNetNetValue = fcstNetNetValue;
    }

    public BigDecimal getFcstCostValue() {
        return this.parseValue(fcstCostValue);
    }

    public void setFcstCostValue(BigDecimal fcstCostValue) {
        this.fcstCostValue = fcstCostValue;
    }

    public BigDecimal getFcstLine() {
        return this.parseValue(fcstLine);
    }

    public void setFcstLine(BigDecimal fcstLine) {
        this.fcstLine = fcstLine;
    }

    public boolean isHasChildren() {
        return hasChildren;
    }

    public void setHasChildren(boolean hasChildren) {
        this.hasChildren = hasChildren;
    }

    public boolean isWarning() {
        return warning;
    }

    public void setWarning(boolean warning) {
        this.warning = warning;
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public BigDecimal getBackorderQty() {
        return this.parseValue(backorderQty);
    }

    public void setBackorderQty(BigDecimal backorderQty) {
        this.backorderQty = backorderQty;
    }

    public BigDecimal getBackorderNetNetValue() {
        return this.parseValue(backorderNetNetValue);
    }

    public void setBackorderNetNetValue(BigDecimal backorderNetNetValue) {
        this.backorderNetNetValue = backorderNetNetValue;
    }

    public BigDecimal getBackorderCostValue() {
        return this.parseValue(backorderCostValue);
    }

    public void setBackorderCostValue(BigDecimal backorderCostValue) {
        this.backorderCostValue = backorderCostValue;
    }

    public BigDecimal getBackorderLine() {
        return this.parseValue(backorderLine);
    }

    public void setBackorderLine(BigDecimal backorderLine) {
        this.backorderLine = backorderLine;
    }

    public BigDecimal getBacklogQty() {
        return this.parseValue(backlogQty);
    }

    public void setBacklogQty(BigDecimal backlogQty) {
        this.backlogQty = backlogQty;
    }

    public BigDecimal getBacklogNetNetValue() {
        return this.parseValue(backlogNetNetValue);
    }

    public void setBacklogNetNetValue(BigDecimal backlogNetNetValue) {
        this.backlogNetNetValue = backlogNetNetValue;
    }

    public BigDecimal getBacklogCostValue() {
        return this.parseValue(backlogCostValue);
    }

    public void setBacklogCostValue(BigDecimal backlogCostValue) {
        this.backlogCostValue = backlogCostValue;
    }

    public BigDecimal getBacklogLine() {
        return this.parseValue(backlogLine);
    }

    public void setBacklogLine(BigDecimal backlogLine) {
        this.backlogLine = backlogLine;
    }

    public BigDecimal getSohQty() {
        return this.parseValue(sohQty);
    }

    public void setSohQty(BigDecimal sohQty) {
        this.sohQty = sohQty;
    }

    public BigDecimal getSohNetNetValue() {
        return this.parseValue(sohNetNetValue);
    }

    public void setSohNetNetValue(BigDecimal sohNetNetValue) {
        this.sohNetNetValue = sohNetNetValue;
    }

    public BigDecimal getSohCostValue() {
        return this.parseValue(sohCostValue);
    }

    public void setSohCostValue(BigDecimal sohCostValue) {
        this.sohCostValue = sohCostValue;
    }

    public BigDecimal getSohLine() {
        return this.parseValue(sohLine);
    }

    public void setSohLine(BigDecimal sohLine) {
        this.sohLine = sohLine;
    }

    public BigDecimal getUdQty() {
        return this.parseValue(udQty);
    }

    public void setUdQty(BigDecimal udQty) {
        this.udQty = udQty;
    }

    public BigDecimal getUdNetNetValue() {
        return this.parseValue(udNetNetValue);
    }

    public void setUdNetNetValue(BigDecimal udNetNetValue) {
        this.udNetNetValue = udNetNetValue;
    }

    public BigDecimal getUdCostValue() {
        return this.parseValue(udCostValue);
    }

    public void setUdCostValue(BigDecimal udCostValue) {
        this.udCostValue = udCostValue;
    }

    public BigDecimal getUdLine() {
        return this.parseValue(udLine);
    }

    public void setUdLine(BigDecimal udLine) {
        this.udLine = udLine;
    }

    public BigDecimal getOutputQty() {
        return this.parseValue(outputQty);
    }

    public void setOutputQty(BigDecimal outputQty) {
        this.outputQty = outputQty;
    }

    public BigDecimal getOutputNetNetValue() {
        return this.parseValue(outputNetNetValue);
    }

    public void setOutputNetNetValue(BigDecimal outputNetNetValue) {
        this.outputNetNetValue = outputNetNetValue;
    }

    public BigDecimal getOutputCostValue() {
        return this.parseValue(outputCostValue);
    }

    public void setOutputCostValue(BigDecimal outputCostValue) {
        this.outputCostValue = outputCostValue;
    }

    public BigDecimal getOutputLine() {
        return this.parseValue(outputLine);
    }

    public void setOutputLine(BigDecimal outputLine) {
        this.outputLine = outputLine;
    }

    public BigDecimal getFcstFulfillQty() {
        return fcstFulfillQty;
    }

    public void setFcstFulfillQty(BigDecimal fcstFulfillQty) {
        this.fcstFulfillQty = fcstFulfillQty;
    }

    public BigDecimal getFcstFulfillNetNetValue() {
        return fcstFulfillNetNetValue;
    }

    public void setFcstFulfillNetNetValue(BigDecimal fcstFulfillNetNetValue) {
        this.fcstFulfillNetNetValue = fcstFulfillNetNetValue;
    }

    public BigDecimal getFcstFulfillCostValue() {
        return fcstFulfillCostValue;
    }

    public void setFcstFulfillCostValue(BigDecimal fcstFulfillCostValue) {
        this.fcstFulfillCostValue = fcstFulfillCostValue;
    }

    public BigDecimal getFcstFulfillLine() {
        return fcstFulfillLine;
    }

    public void setFcstFulfillLine(BigDecimal fcstFulfillLine) {
        this.fcstFulfillLine = fcstFulfillLine;
    }

    private BigDecimal parseValue(BigDecimal value) {
        if (value != null) {
            return value.setScale(0, RoundingMode.HALF_UP);
        } else {
            return null;
        }
    }
}
