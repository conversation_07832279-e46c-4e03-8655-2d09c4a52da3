package com.scp.demand.bean;

import java.math.BigDecimal;

public class NpiEolReport5 {
    private String cycle_month;
    private String sales_organization;
    private String project;
    private String material_old;
    private String material_new;
    private BigDecimal sub_qty_mix;
    private BigDecimal sub_value_mix;

    public String getCycle_month() {
        return cycle_month;
    }

    public void setCycle_month(String cycle_month) {
        this.cycle_month = cycle_month;
    }

    public String getSales_organization() {
        return sales_organization;
    }

    public void setSales_organization(String sales_organization) {
        this.sales_organization = sales_organization;
    }

    public String getProject() {
        return project;
    }

    public void setProject(String project) {
        this.project = project;
    }

    public String getMaterial_old() {
        return material_old;
    }

    public void setMaterial_old(String material_old) {
        this.material_old = material_old;
    }

    public String getMaterial_new() {
        return material_new;
    }

    public void setMaterial_new(String material_new) {
        this.material_new = material_new;
    }

    public BigDecimal getSub_qty_mix() {
        return sub_qty_mix;
    }

    public void setSub_qty_mix(BigDecimal sub_qty_mix) {
        this.sub_qty_mix = sub_qty_mix;
    }

    public BigDecimal getSub_value_mix() {
        return sub_value_mix;
    }

    public void setSub_value_mix(BigDecimal sub_value_mix) {
        this.sub_value_mix = sub_value_mix;
    }
}
