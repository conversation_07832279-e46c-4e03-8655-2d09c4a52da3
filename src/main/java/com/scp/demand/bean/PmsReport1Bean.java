package com.scp.demand.bean;

import java.math.BigDecimal;

public class PmsReport1Bean {
    private String category1;
    private String category2;
    private String category3;
    private String category4;
    private String category5;
    private BigDecimal value;

    private SOReport1Tooltips tooltips = new SOReport1Tooltips();

    public String getCategory1() {
        return category1;
    }

    public void setCategory1(String category1) {
        this.category1 = category1;
    }

    public String getCategory2() {
        return category2;
    }

    public void setCategory2(String category2) {
        this.category2 = category2;
    }

    public String getCategory3() {
        return category3;
    }

    public void setCategory3(String category3) {
        this.category3 = category3;
    }

    public String getCategory4() {
        return category4;
    }

    public void setCategory4(String category4) {
        this.category4 = category4;
    }

    public String getCategory5() {
        return category5;
    }

    public void setCategory5(String category5) {
        this.category5 = category5;
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public SOReport1Tooltips getTooltips() {
        return tooltips;
    }

    public void setTooltips(SOReport1Tooltips tooltips) {
        this.tooltips = tooltips;
    }

    public SOReport1Tooltips copyTooltips() throws Exception {
        return new SOReport1Tooltips().copyOf(tooltips);
    }
}
