package com.scp.demand.bean;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class PmsReport1Tooltips {

    private BigDecimal LINE;
    private BigDecimal NET_NET_VALUE_RMB;

    public BigDecimal getLINE() {
        return LINE;
    }

    public void setLINE(BigDecimal LINE) {
        this.LINE = LINE;
    }

    public BigDecimal getNET_NET_VALUE_RMB() {
        return NET_NET_VALUE_RMB;
    }

    public void setNET_NET_VALUE_RMB(BigDecimal NET_NET_VALUE_RMB) {
        this.NET_NET_VALUE_RMB = NET_NET_VALUE_RMB;
    }

    private static final Class<PmsReport1Tooltips> clazz;
    private static final List<String> fields;

    static {
        clazz = PmsReport1Tooltips.class;
        fields = Arrays.stream(clazz.getDeclaredFields()).filter(f -> f.getAnnotatedType().getType().getTypeName().equalsIgnoreCase("java.math.BigDecimal")).map(Field::getName).collect(Collectors.toList());
    }

    public PmsReport1Tooltips copyOf(PmsReport1Tooltips tooltips) throws Exception {
        for (String f : fields) {
            Method method = clazz.getMethod("get" + f);
            Object value = method.invoke(tooltips);
            if (value != null) {
                clazz.getMethod("set" + f, BigDecimal.class).invoke(this, (BigDecimal) value);
            }
        }
        return this;
    }

    public void add(PmsReport1Tooltips tips) throws Exception {
        for (String f : fields) {
            Method method = clazz.getMethod("get" + f);
            Object value = method.invoke(tips);
            if (value != null) {
                BigDecimal val = BigDecimal.ZERO;
                Object valueOrg = method.invoke(this);
                if (valueOrg != null) {
                    val = (BigDecimal) valueOrg;
                }
                clazz.getMethod("set" + f, BigDecimal.class).invoke(this, val.add((BigDecimal) value));
            }
        }
    }
}
