package com.scp.demand.bean;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class TrackingReport3Bean {
    private String xAxis = "";
    private BigDecimal salesQty = BigDecimal.ZERO;
    private BigDecimal salesNetNetValue = BigDecimal.ZERO;
    private BigDecimal salesCostValue = BigDecimal.ZERO;
    private BigDecimal salesLine = BigDecimal.ZERO;
    private BigDecimal orderIntakeQty = BigDecimal.ZERO;
    private BigDecimal orderIntakeNetNetValue = BigDecimal.ZERO;
    private BigDecimal orderIntakeCostValue = BigDecimal.ZERO;
    private BigDecimal orderIntakeLine = BigDecimal.ZERO;
    private BigDecimal outputQty = BigDecimal.ZERO;
    private BigDecimal outputNetNetValue = BigDecimal.ZERO;
    private BigDecimal outputCostValue = BigDecimal.ZERO;
    private BigDecimal outputLine = BigDecimal.ZERO;
    private BigDecimal fcstQty = BigDecimal.ZERO;
    private BigDecimal fcstNetNetValue = BigDecimal.ZERO;
    private BigDecimal fcstCostValue = BigDecimal.ZERO;
    private BigDecimal crdQty = BigDecimal.ZERO;
    private BigDecimal crdNetNetValue = BigDecimal.ZERO;
    private BigDecimal crdCostValue = BigDecimal.ZERO;
    private BigDecimal crdLine = BigDecimal.ZERO;

    public String getxAxis() {
        return xAxis;
    }

    public void setxAxis(String xAxis) {
        this.xAxis = xAxis;
    }

    public BigDecimal getSalesQty() {
        return this.parseValue(salesQty);
    }

    public void setSalesQty(BigDecimal salesQty) {
        this.salesQty = salesQty;
    }

    public BigDecimal getSalesNetNetValue() {
        return this.parseValue(salesNetNetValue);
    }

    public void setSalesNetNetValue(BigDecimal salesNetNetValue) {
        this.salesNetNetValue = salesNetNetValue;
    }

    public BigDecimal getSalesCostValue() {
        return this.parseValue(salesCostValue);
    }

    public void setSalesCostValue(BigDecimal salesCostValue) {
        this.salesCostValue = salesCostValue;
    }

    public BigDecimal getOrderIntakeQty() {
        return this.parseValue(orderIntakeQty);
    }

    public void setOrderIntakeQty(BigDecimal orderIntakeQty) {
        this.orderIntakeQty = orderIntakeQty;
    }

    public BigDecimal getOrderIntakeNetNetValue() {
        return this.parseValue(orderIntakeNetNetValue);
    }

    public void setOrderIntakeNetNetValue(BigDecimal orderIntakeNetNetValue) {
        this.orderIntakeNetNetValue = orderIntakeNetNetValue;
    }

    public BigDecimal getOrderIntakeCostValue() {
        return this.parseValue(orderIntakeCostValue);
    }

    public void setOrderIntakeCostValue(BigDecimal orderIntakeCostValue) {
        this.orderIntakeCostValue = orderIntakeCostValue;
    }

    public BigDecimal getOrderIntakeLine() {
        return this.parseValue(orderIntakeLine);
    }

    public void setOrderIntakeLine(BigDecimal orderIntakeLine) {
        this.orderIntakeLine = orderIntakeLine;
    }

    public BigDecimal getCrdQty() {
        return this.parseValue(crdQty);
    }

    public void setCrdQty(BigDecimal crdQty) {
        this.crdQty = crdQty;
    }

    public BigDecimal getCrdNetNetValue() {
        return this.parseValue(crdNetNetValue);
    }

    public void setCrdNetNetValue(BigDecimal crdNetNetValue) {
        this.crdNetNetValue = crdNetNetValue;
    }

    public BigDecimal getCrdCostValue() {
        return this.parseValue(crdCostValue);
    }

    public void setCrdCostValue(BigDecimal crdCostValue) {
        this.crdCostValue = crdCostValue;
    }

    public BigDecimal getFcstQty() {
        return this.parseValue(fcstQty);
    }

    public void setFcstQty(BigDecimal fcstQty) {
        this.fcstQty = fcstQty;
    }

    public BigDecimal getFcstNetNetValue() {
        return this.parseValue(fcstNetNetValue);
    }

    public void setFcstNetNetValue(BigDecimal fcstNetNetValue) {
        this.fcstNetNetValue = fcstNetNetValue;
    }

    public BigDecimal getFcstCostValue() {
        return this.parseValue(fcstCostValue);
    }

    public void setFcstCostValue(BigDecimal fcstCostValue) {
        this.fcstCostValue = fcstCostValue;
    }

    public BigDecimal getOutputQty() {
        return this.parseValue(outputQty);
    }

    public void setOutputQty(BigDecimal outputQty) {
        this.outputQty = outputQty;
    }

    public BigDecimal getOutputNetNetValue() {
        return this.parseValue(outputNetNetValue);
    }

    public void setOutputNetNetValue(BigDecimal outputNetNetValue) {
        this.outputNetNetValue = outputNetNetValue;
    }

    public BigDecimal getOutputCostValue() {
        return this.parseValue(outputCostValue);
    }

    public void setOutputCostValue(BigDecimal outputCostValue) {
        this.outputCostValue = outputCostValue;
    }

    public BigDecimal getSalesLine() {
        return this.parseValue(salesLine);
    }

    public void setSalesLine(BigDecimal salesLine) {
        this.salesLine = salesLine;
    }

    public BigDecimal getOutputLine() {
        return this.parseValue(outputLine);
    }

    public void setOutputLine(BigDecimal outputLine) {
        this.outputLine = outputLine;
    }

    public BigDecimal getCrdLine() {
        return this.parseValue(crdLine);
    }

    public void setCrdLine(BigDecimal crdLine) {
        this.crdLine = crdLine;
    }

    private BigDecimal parseValue(BigDecimal value) {
        if (value != null) {
            return value.setScale(0, RoundingMode.HALF_UP);
        } else {
            return BigDecimal.ZERO;
        }
    }
}
