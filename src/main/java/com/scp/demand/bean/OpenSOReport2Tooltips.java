package com.scp.demand.bean;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class OpenSOReport2Tooltips {
    private BigDecimal OPEN_UD_QTY;
    private BigDecimal UD_CB;
    private BigDecimal UD_NORMAL;
    private BigDecimal UD_MID_AGING;
    private BigDecimal UD_LONG_AGING;

    private static final Class<OpenSOReport2Tooltips> clazz;
    private static final List<String> fields;

    static {
        clazz = OpenSOReport2Tooltips.class;
        fields = Arrays.stream(clazz.getDeclaredFields()).filter(f -> f.getAnnotatedType().getType().getTypeName().equalsIgnoreCase("java.math.BigDecimal")).map(Field::getName).collect(Collectors.toList());
    }

    public OpenSOReport2Tooltips copyOf(OpenSOReport2Tooltips tooltips) throws Exception {
        for (String f : fields) {
            Method method = clazz.getMethod("get" + f);
            Object value = method.invoke(tooltips);
            if (value != null) {
                clazz.getMethod("set" + f, BigDecimal.class).invoke(this, (BigDecimal) value);
            }
        }
        return this;
    }

    public BigDecimal getOPEN_UD_QTY() {
        return OPEN_UD_QTY;
    }

    public void setOPEN_UD_QTY(BigDecimal OPEN_UD_QTY) {
        this.OPEN_UD_QTY = OPEN_UD_QTY;
    }

    public BigDecimal getUD_CB() {
        return UD_CB;
    }

    public void setUD_CB(BigDecimal UD_CB) {
        this.UD_CB = UD_CB;
    }

    public BigDecimal getUD_NORMAL() {
        return UD_NORMAL;
    }

    public void setUD_NORMAL(BigDecimal UD_NORMAL) {
        this.UD_NORMAL = UD_NORMAL;
    }

    public BigDecimal getUD_MID_AGING() {
        return UD_MID_AGING;
    }

    public void setUD_MID_AGING(BigDecimal UD_MID_AGING) {
        this.UD_MID_AGING = UD_MID_AGING;
    }

    public BigDecimal getUD_LONG_AGING() {
        return UD_LONG_AGING;
    }

    public void setUD_LONG_AGING(BigDecimal UD_LONG_AGING) {
        this.UD_LONG_AGING = UD_LONG_AGING;
    }

    public void add(OpenSOReport2Tooltips tips) throws Exception {
        for (String f : fields) {
            Method method = clazz.getMethod("get" + f);
            Object value = method.invoke(tips);
            if (value != null) {
                BigDecimal val = BigDecimal.ZERO;
                Object valueOrg = method.invoke(this);
                if (valueOrg != null) {
                    val = (BigDecimal) valueOrg;
                }
                clazz.getMethod("set" + f, BigDecimal.class).invoke(this, val.add((BigDecimal) value));
            }
        }
    }
}
