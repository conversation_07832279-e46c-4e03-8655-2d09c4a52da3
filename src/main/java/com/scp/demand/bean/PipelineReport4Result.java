package com.scp.demand.bean;

import java.util.ArrayList;
import java.util.List;

public class PipelineReport4Result {
    private String name;
    private int value = 0;
    private List<PipelineReport4Result> children = new ArrayList<>();

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public void addValue() {
        this.value++;
    }

    public List<PipelineReport4Result> getChildren() {
        return children;
    }

    public void setChildren(List<PipelineReport4Result> children) {
        this.children = children;
    }

    public void addChild(PipelineReport4Bean bean) {
        PipelineReport4Result summary = this.getReportSummaryByName(children, bean.getDelayFrequencyName());
        summary.setName(bean.getDelayFrequencyName());
        summary.addValue();

        // 为了保证图形美观, 隐藏最外层 Delay Depth < 3 的数据
        if (bean.getDelayDepthName().equals(PipelineReport4Bean.DD_LESS_THAN_3) == false) {
            PipelineReport4Result sub = this.getReportSummaryByName(summary.getChildren(), bean.getDelayDepthName());
            sub.setName(bean.getDelayDepthName());
            sub.addValue();
        }
    }

    private PipelineReport4Result getReportSummaryByName(List<PipelineReport4Result> list, String key) {
        for (PipelineReport4Result summay : list) {
            if (summay.getName().equalsIgnoreCase(key)) {
                return summay;
            }
        }
        PipelineReport4Result summary = new PipelineReport4Result();
        list.add(summary);
        return summary;
    }
}
