package com.scp.demand.bean;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PipelineTreeMap {
    private BigDecimal value;
    private String name;
    private String path;
    private List<PipelineTreeMap> children = new ArrayList<>();
    private Map<String, Object> itemStyle = new HashMap<>();

    public PipelineTreeMap() {

    }

    public PipelineTreeMap(String name, BigDecimal value, String color) {
        this.name = name;
        this.value = value;
        this.itemStyle.put("color", color);
    }

    public void setItemColor(String color) {
        itemStyle.put("color", color);
    }

    public Map<String, Object> getItemStyle() {
        return itemStyle;
    }

    public void setItemStyle(Map<String, Object> itemStyle) {
        this.itemStyle = itemStyle;
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPath() {
        return path == null ? name : path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public List<PipelineTreeMap> getChildren() {
        return children;
    }

    public void setChildren(List<PipelineTreeMap> children) {
        this.children = children;
    }

    public void addChild(PipelineTreeMap child) {
        this.children.add(child);
    }

}
