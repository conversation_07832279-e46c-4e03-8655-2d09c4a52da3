package com.scp.demand;

import com.scp.demand.service.IOpenSoStructureService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/demand/open_so_structure", parent = "menu1E0")
public class OpenSoStructureController extends ControllerHelper {

    @Resource
    private Response res;

    @Resource
    private IOpenSoStructureService openSoStructureService;

    @SchneiderRequestMapping("/init_page")
    public Response initPage() {
        return openSoStructureService.initPage();
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        return openSoStructureService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping("/query_report1_details")
    public Response queryReport1Details(HttpServletRequest request) {
        super.pageLoad(request);
        return openSoStructureService.queryReport1Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report1_details")
    public void downloadReport1Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        openSoStructureService.downloadReport1Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        super.pageLoad(request);
        try {
            return openSoStructureService.queryReport2(parameterMap);
        } catch (Exception e) {
            return res.setError(e);
        }
    }

    @SchneiderRequestMapping("/query_report3")
    public Response queryReport3(HttpServletRequest request) {
        super.pageLoad(request);
        return openSoStructureService.queryReport3(parameterMap);
    }

    @SchneiderRequestMapping("/query_report3_details")
    public Response queryReport3Details(HttpServletRequest request) {
        super.pageLoad(request);
        return openSoStructureService.queryReport3Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report3_details")
    public void downloadReport3Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        openSoStructureService.downloadReport3Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report4")
    public Response queryReport4(HttpServletRequest request) {
        super.pageLoad(request);
        return openSoStructureService.queryReport4(parameterMap);
    }

    @SchneiderRequestMapping("/query_report4_details")
    public Response queryReport4Details(HttpServletRequest request) {
        super.pageLoad(request);
        return openSoStructureService.queryReport4Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report4_details")
    public void downloadReport4Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        openSoStructureService.downloadReport4Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report5_columns")
    public Response queryReport5Columns(HttpServletRequest request) {
        super.pageLoad(request);
        return openSoStructureService.queryReport5Columns(parameterMap);
    }

    @SchneiderRequestMapping("/query_report5")
    public Response queryReport5(HttpServletRequest request) {
        super.pageLoad(request);
        return openSoStructureService.queryReport5(parameterMap);
    }

    @SchneiderRequestMapping("/query_report5_details")
    public Response queryReport5Details(HttpServletRequest request) {
        super.pageLoad(request);
        return openSoStructureService.queryReport5Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report5_details")
    public void downloadReport5Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        openSoStructureService.downloadReport5Details(parameterMap, response);
    }
}
