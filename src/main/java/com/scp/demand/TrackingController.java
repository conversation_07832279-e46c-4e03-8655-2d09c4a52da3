package com.scp.demand;

import com.scp.demand.service.ITrackingService;
import com.scp.demand.service.impl.TrackingServiceImpl;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/demand/tracking", parent = TrackingServiceImpl.TRACKING_PARENT_CODE)
public class TrackingController extends ControllerHelper {

    @Resource
    private ITrackingService trackingService;

    @SchneiderRequestMapping("/init_page")
    public Response initPage(HttpServletRequest request) {
        super.setGlobalCache(true);
        super.pageLoad(request);
        return trackingService.initPage();
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        return trackingService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping("/query_report1_sub")
    public Response queryReport1Sub(HttpServletRequest request) {
        super.pageLoad(request);
        return trackingService.queryReport1Sub(parameterMap);
    }

    @SchneiderRequestMapping("/download_report1")
    public void downloadReport1(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        trackingService.downloadReport1(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report1_details")
    public Response queryReport1Details(HttpServletRequest request) {
        super.pageLoad(request);
        String type = (String) parameterMap.get("viewType");
        if (type.equals("FCST")) {
            return trackingService.queryReport1FCSTDetails(parameterMap);
        } else {
            return trackingService.queryReport1Details(parameterMap);
        }
    }

    @SchneiderRequestMapping("/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        super.pageLoad(request);
        return trackingService.queryReport2(parameterMap);
    }

    @SchneiderRequestMapping("/query_report2_details")
    public Response queryReport2Details(HttpServletRequest request) {
        super.pageLoad(request);
        return trackingService.queryReport2Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_data")
    public void downloadData(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        String type = (String) parameterMap.get("viewType");
        if (type.equals("FCST")) {
            trackingService.downloadFCSTData(parameterMap, response);
        } else {
            trackingService.downloadData(parameterMap, response);
        }
    }

    @SchneiderRequestMapping("/download_data2")
    public void downloadData2(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        trackingService.downloadData2(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report3")
    public Response queryReport3(HttpServletRequest request) {
        super.pageLoad(request);
        return trackingService.queryReport3(parameterMap);
    }

    @SchneiderRequestMapping("/query_report4")
    public Response queryReport4(HttpServletRequest request) {
        super.pageLoad(request);
        return trackingService.queryReport4(parameterMap);
    }
}
