package com.scp.demand;

import com.scp.demand.service.ISOStructureService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/demand/so_structure", parent = "menu190")
public class SOStructureController extends ControllerHelper {

    @Resource
    private ISOStructureService soStructureService;

    @Resource
    private Response res;

    @SchneiderRequestMapping("/query_filters")
    public Response queryFilters(HttpServletRequest request) {
        super.pageLoad(request);
        return soStructureService.queryFilters(parameterMap);
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request){
        super.pageLoad(request);
        try {
            return soStructureService.queryReport1(parameterMap);
        } catch (Exception e) {
            return res.setError(e);
        }
    }

    @SchneiderRequestMapping("/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        super.pageLoad(request);
        return soStructureService.queryReport2(parameterMap);
    }

    @SchneiderRequestMapping("/query_report2_details")
    public Response queryReport2Details(HttpServletRequest request) {
        super.pageLoad(request);
        return soStructureService.queryReport2Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report2_details")
    public void downloadReport2Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        soStructureService.downloadReport2Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report3_columns")
    public Response queryReport3Columns(HttpServletRequest request) {
        super.pageLoad(request);
        return soStructureService.queryReport3Columns(parameterMap);
    }

    @SchneiderRequestMapping("/query_report3")
    public Response queryReport3(HttpServletRequest request) {
        super.pageLoad(request);
        return soStructureService.queryReport3(parameterMap);
    }

    @SchneiderRequestMapping("/query_report3_details")
    public Response queryReport3Details(HttpServletRequest request) {
        super.pageLoad(request);
        return soStructureService.queryReport3Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report3_details")
    public void downloadReport3Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        soStructureService.downloadReport3Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/download_report2_display_data")
    public void downloadReport2DisplayData(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        soStructureService.downloadReport2DisplayData(parameterMap, response);
    }
}
