package com.scp.demand;

import com.scp.demand.service.IFcstService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.util.WebUtils;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/demand/fcst", parent = "menu140")
public class FcstController extends ControllerHelper {

    @Resource
    private IFcstService fcstService;

    @Resource
    private Response response;

    @SchneiderRequestMapping("/upload_fcst")
    public Response uploadFcst(HttpServletRequest request) {
        super.pageLoad(request);
        MultipartHttpServletRequest multipartRequest = WebUtils.getNativeRequest(request, MultipartHttpServletRequest.class);
        if (multipartRequest != null) {
            MultipartFile file = multipartRequest.getFile("file");
            try {
                return fcstService.uploadFcst(session.getUserid(), (String) parameterMap.get("uploadVersion"), (String) parameterMap.get("uploadMode"), file);
            } catch (Exception e) {
                return response.setError(e);
            }
        } else {
            return response.setError(new Exception("upload failed, multipartRequest is null !"));
        }
    }

    @SchneiderRequestMapping("/init_page")
    public Response initPage(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return fcstService.initPage(session.getUserid());
    }

    @SchneiderRequestMapping("/download_template")
    public void downloadTemplate(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        fcstService.downloadTemplate(parameterMap, response);
    }

    @SchneiderRequestMapping("/download_fcst_data")
    public void downloadFcstData(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        fcstService.downloadFcstData(session.getUserid(), parameterMap, response);
    }

    @SchneiderRequestMapping("/query_fcst_columns")
    public Response queryFcstColumns(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return fcstService.queryFcstColumns((String) parameterMap.get("fcst_version"));
    }

    @SchneiderRequestMapping("/query_fcst_data")
    public Response queryFcstData(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return fcstService.queryFcstData(session.getUserid(), parameterMap);
    }

    @SchneiderRequestMapping("/save_forcast_source")
    public Response saveForcastSource(HttpServletRequest request) {
        super.pageLoad(request);
        return fcstService.saveForcastSource(session.getUserid(), parameterMap);
    }
}
