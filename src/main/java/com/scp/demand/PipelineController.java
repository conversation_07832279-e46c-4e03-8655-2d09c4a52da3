package com.scp.demand;

import com.scp.demand.service.IPipelineService;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/demand/pipeline", parent = "menu110")
public class PipelineController extends ControllerHelper {

    @Resource
    private IPipelineService pipelineService;

    @SchneiderRequestMapping("/query_submission_date")
    public Response querySubmissionDate(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return pipelineService.querySubmissionDate();
    }

    @SchneiderRequestMapping("/query_update_by")
    public Response queryUpdateBy(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return pipelineService.queryUpdateBy(parameterMap);
    }

    @SchneiderRequestMapping("/query_product_line")
    public Response queryProductLine(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return pipelineService.queryProductLine(parameterMap);
    }

    @SchneiderRequestMapping("/query_project_status")
    public Response queryProjectStatus(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return pipelineService.queryProjectStatus(parameterMap);
    }

    @SchneiderRequestMapping("/query_invalid_pipeline_data")
    public Response queryInvalidPipelineData(HttpServletRequest request) {
        super.pageLoad(request);
        return pipelineService.queryInvalidPipelineData(parameterMap);
    }

    @SchneiderRequestMapping("/query_sales_team")
    public Response querySalesTeam(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return pipelineService.querySalesTeam(parameterMap);
    }

    @SchneiderRequestMapping("/query_sales_person")
    public Response querySalesPerson(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return pipelineService.querySalesPerson(parameterMap);
    }

    @SchneiderRequestMapping("/query_report1_columns_comments")
    public Response queryReport1ColumnsComments(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return pipelineService.queryReport1ColumnsComments(parameterMap);
    }

    @SchneiderRequestMapping("/query_report1")
    public Response queryReport1(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return pipelineService.queryReport1(parameterMap);
    }

    @SchneiderRequestMapping("/save_report1_comments")
    public Response saveReport1Comments(HttpServletRequest request) {
        super.pageLoad(request);
        return pipelineService.saveReport1Comments(parameterMap);
    }

    @SchneiderRequestMapping("/query_report1_details")
    public Response queryReport1Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return pipelineService.queryReport1Details(parameterMap);
    }

    @SchneiderRequestMapping("/query_report1_source")
    public Response queryReport1Source(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return pipelineService.queryReport1Source(parameterMap);
    }

    @SchneiderRequestMapping("/save_report1_source")
    public Response saveReport1Source(HttpServletRequest request) {
        super.pageLoad(request);
        return pipelineService.saveReport1Source(parameterMap);
    }

    @SchneiderRequestMapping("/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return pipelineService.queryReport2(parameterMap);
    }

    @SchneiderRequestMapping("/query_report2_details")
    public Response queryReport2Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return pipelineService.queryReport2Details(parameterMap);
    }

    @SchneiderRequestMapping("/query_report2_source")
    public Response queryReport2Source(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return pipelineService.queryReport2Source(parameterMap);
    }

    @SchneiderRequestMapping("/query_report3")
    public Response queryReport3(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return pipelineService.queryReport3(parameterMap);
    }

    @SchneiderRequestMapping("/query_report3_details")
    public Response queryReport3Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return pipelineService.queryReport3Details(parameterMap);
    }

    @SchneiderRequestMapping("/query_report4")
    public Response queryReport4(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return pipelineService.queryReport4(parameterMap);
    }

    @SchneiderRequestMapping("/query_report5")
    public Response queryReport5(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return pipelineService.queryReport5(parameterMap);
    }

    @SchneiderRequestMapping("/query_report5_details")
    public Response queryReport5Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return pipelineService.queryReport5Details(parameterMap);
    }

    @SchneiderRequestMapping("/query_report6")
    public Response queryReport6(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return pipelineService.queryReport6(parameterMap);
    }

    @SchneiderRequestMapping("/query_report6_details")
    public Response queryReport6Details(HttpServletRequest request) {
        super.pageLoad(request);
        super.setGlobalCache(true);
        return pipelineService.queryReport6Details(parameterMap);
    }
}
