package com.scp.demand.service;

import com.starter.context.bean.Response;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;

public interface IEventService {

    Response initPage(String userid);

    Response queryReport1Data(Map<String, Object> parameterMap);

    Response saveReport1(String userid, Map<String, Object> parameterMap);

    Response generateNewNote();

    void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse response);
}
