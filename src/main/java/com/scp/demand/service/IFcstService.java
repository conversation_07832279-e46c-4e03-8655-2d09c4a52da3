package com.scp.demand.service;

import com.starter.context.bean.Response;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;

import java.util.Map;

public interface IFcstService {

    Response uploadFcst(String uploadBy, String uploadVersion, String uploadMode, MultipartFile file) throws Exception;

    Response queryFcstColumns(String fcstVersion);

    Response queryFcstData(String userid, Map<String, Object> parameterMap);

    Response initPage(String userid);

    void downloadTemplate(Map<String, Object> parameterMap, HttpServletResponse response);

    Response saveForcastSource(String userid, Map<String, Object> parameterMap);

    void downloadFcstData(String uploadBy, Map<String, Object> parameterMap, HttpServletResponse response);
}
