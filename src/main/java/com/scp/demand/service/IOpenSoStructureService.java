package com.scp.demand.service;

import com.starter.context.bean.Response;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;

public interface IOpenSoStructureService {

    Response initPage();

    Response queryReport1(Map<String, Object> parameterMap);

    Response queryReport1Details(Map<String, Object> parameterMap);

    void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport2(Map<String, Object> parameterMap) throws Exception;

    Response queryReport3(Map<String, Object> parameterMap);

    Response queryReport3Details(Map<String, Object> parameterMap);

    void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport4(Map<String, Object> parameterMap);

    Response queryReport4Details(Map<String, Object> parameterMap);

    void downloadReport4Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport5Columns(Map<String, Object> parameterMap);

    Response queryReport5(Map<String, Object> parameterMap);

    Response queryReport5Details(Map<String, Object> parameterMap);

    void downloadReport5Details(Map<String, Object> parameterMap, HttpServletResponse response);
}
