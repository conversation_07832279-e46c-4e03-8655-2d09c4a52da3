package com.scp.demand.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.bean.scptable.ScpTableHelper;
import com.scp.demand.EventController;
import com.scp.demand.dao.IEventDao;
import com.scp.demand.service.IEventService;
import com.starter.login.bean.Session;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;

@Service("eventService")
@Scope("prototype")
@Transactional
public class EventServiceImpl implements IEventService {

    @Resource
    private IEventDao eventDao;

    @Resource
    private Response response;

    @Resource
    private ExcelTemplate excelTemplate;

    @Resource
    private ScpTableHelper scpTableHelper;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage(String userid) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cascader", Utils.parseCascader(eventDao.queryCascader()));
        resultMap.put("eventClass", eventDao.queryEventClass());
        resultMap.put("eventStatus", eventDao.queryEventStatus());
        resultMap.put("cycleMonth", eventDao.queryCycleMonth());
        resultMap.put("bu", eventDao.queryNotesFilter("BUSINESS_UNIT"));
        resultMap.put("local_product_line", eventDao.queryNotesFilter("LOCAL_PRODUCT_LINE"));
        resultMap.put("local_product_family", eventDao.queryNotesFilter("LOCAL_PRODUCT_FAMILY"));
        resultMap.put("local_product_subfamily", eventDao.queryNotesFilter("LOCAL_PRODUCT_SUBFAMILY"));
        resultMap.put("isAdmin", eventDao.queryAdminCnt(EventController.PARENT_CODE, userid) > 0);
        return response.setBody(resultMap);
    }

    @Override
    public Response queryReport1Data(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        this.generateCascaderFilter(parameterMap);
        int total = eventDao.queryReport1Count(parameterMap);
        page.setTotal(total);
        if (total > 0) {
            page.setData(eventDao.queryReport1(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public Response saveReport1(String userid, Map<String, Object> parameterMap) {
        scpTableHelper.setWarningMessage("You have no privileges to modify data does not belong to you");
        scpTableHelper.setScpTableInsertHandler((headers, inserts) ->
                {
                    headers.add("CYCLE_MONTH");
                    for (Map<String, Object> map : inserts) {
                        if (map.containsKey("CYCLE")) {
                            String value = (String) map.get("CYCLE");
                            map.put("CYCLE_MONTH", StringUtils.substring(value, 0, 7));
                        }
                    }
                    return eventDao.createReport1(userid, headers, inserts);
                }
        );
        scpTableHelper.setScpTableDeleteHandler(deletes ->
                eventDao.deleteReport1(userid, deletes)
        );
        scpTableHelper.setScpTableUpdateHandler((pk, updates) ->
                eventDao.updateReport1(pk, updates, userid)
        );
        return response.setBody(scpTableHelper.execCRUD(parameterMap));
    }

    @Override
    public Response generateNewNote() {
        Calendar calendar = Calendar.getInstance();
        String cycleMonth = new SimpleDateFormat("yyyy/MM").format(calendar.getTime());
        calendar.add(Calendar.MONTH, -1);
        String lastCycleMonth = new SimpleDateFormat("yyyy/MM").format(calendar.getTime());
        eventDao.generateNewNote(lastCycleMonth, cycleMonth);

        return response.setBody(cycleMonth);
    }

    @Override
    public void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateCascaderFilter(parameterMap);

        String fileName = "demand_event_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.demand.dao.IEventDao.downloadReport1", parameterMap);
    }

    /**
     * 生成cascader filter
     *
     * @param parameterMap 参数map
     */
    private void generateCascaderFilter(Map<String, Object> parameterMap) {
        parameterMap.put("isAdmin", eventDao.queryAdminCnt(EventController.PARENT_CODE, ((Session) parameterMap.get("session")).getUserid()) > 0);
        // 生成筛选条件
        JSONArray categoryArray = (JSONArray) parameterMap.get("filterList");
        if (categoryArray != null) {
            Map<String, List<String>> filterMap = new HashMap<>();

            for (Object subObj : categoryArray) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = subArray.getString(0);

                String value = subArray.getString(1);
                String key = Utils.randomStr(8);

                if (Utils.hasInjectionAttack(columnName)) {
                    continue;
                }

                List<String> fl = filterMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                fl.add("#{" + key + ",jdbcType=VARCHAR}");
                parameterMap.put(key, value);
            }

            List<String> filterList = new ArrayList<>();

            for (String key : filterMap.keySet()) {
                List<String> fl = filterMap.get(key);
                filterList.add("t." + key + " in (" + StringUtils.join(fl, ",") + ")");
            }

            parameterMap.put("filters", StringUtils.join(filterList, " and "));
        }
    }
}
