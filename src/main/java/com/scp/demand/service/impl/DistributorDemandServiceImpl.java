package com.scp.demand.service.impl;
import com.scp.demand.dao.IDistributorDemandDao;
import com.scp.demand.service.IDistributorDemandService;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service("distributorDemandServiceImpl")
@Scope("prototype")
@Transactional
public class DistributorDemandServiceImpl extends ServiceHelper implements IDistributorDemandService {

    @Resource
    private Response response;

    @Resource
    private ExcelTemplate excelTemplate;

    @Resource
    private IDistributorDemandDao distributorDemandDao;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cascader", Utils.parseCascader(distributorDemandDao.initPage(parameterMap)));
        resultMap.put("versionList", distributorDemandDao.queryVersionList(parameterMap));
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        this.generateCascaderFilterSQL(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        List<LinkedHashMap<String, Object>> dataList = distributorDemandDao.queryReport1(parameterMap);
        page.setData(dataList);
        return response.setBody(page);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1Details(Map<String, Object> parameterMap) {
        this.generateCascaderFilterSQL(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(distributorDemandDao.queryReport1DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(distributorDemandDao.queryReport1Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateCascaderFilterSQL(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "distributor_demand" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.demand.dao.IDistributorDemandDao.queryReport1", parameterMap);
    }


    @Override
    public void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateCascaderFilterSQL(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "distributor_demand" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.demand.dao.IDistributorDemandDao.queryReport1Details", parameterMap);
    }
}
