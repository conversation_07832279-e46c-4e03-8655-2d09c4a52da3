package com.scp.demand.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.starter.utils.Utils;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.scp.demand.dao.IAbnormalDemandDao;
import com.scp.demand.service.IAbnormalDemandService;
import com.starter.utils.DateCalUtil;
import com.starter.utils.excel.ExcelTemplate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;

@Service("abnormalDemandServiceImpl")
@Scope("prototype")
@Transactional
public class AbnormalDemandServiceImpl implements IAbnormalDemandService {

    @Resource
    private Response response;

    @Resource
    private IAbnormalDemandDao abnormalDemandDao;

    @Resource
    private ExcelTemplate excelTemplate;

    @Override
    public Response initPage() {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("fcstVersion", abnormalDemandDao.queryFcstVersion());
        resultMap.put("cascader", Utils.parseCascader(abnormalDemandDao.queryCascader()));
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReportColumns(Map<String, Object> parameterMap) {
        List<String> dataList = abnormalDemandDao.queryReportColumns();

        Map<String, List<String>> resultMap = new HashMap<>();

        for (String column : dataList) {
            if (column.length() == 6) {
                List<String> temp = resultMap.computeIfAbsent("Actual", key -> new ArrayList<>());
                temp.add(column);
            } else if (column.length() == 8) {
                List<String> temp = resultMap.computeIfAbsent("MTD", key -> new ArrayList<>());
                temp.add(column);
            }
        }

        Calendar date = Calendar.getInstance();
        List<String> fcstList = new ArrayList<>();
        SimpleDateFormat format = new SimpleDateFormat("yyyyMM");
        for (int i = 0; i < 7; i++) {
            fcstList.add(format.format(date.getTime()) + "_FCST");
            date.add(Calendar.MONTH, 1);
        }
        resultMap.put("FCST", fcstList);

        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);

        this.prepareReportFilters(parameterMap);

        page.setTotal(abnormalDemandDao.queryReportCount(parameterMap));
        if (page.getTotal() > 0) {
            List<LinkedHashMap<String, Object>> resultList = abnormalDemandDao.queryReport(parameterMap);
            page.setData(resultList);
        }

        return response.setBody(page);
    }

    public void downloadReport(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        this.prepareReportFilters(parameterMap);

        String fileName = "Abnormal_Demand_Alert_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.demand.dao.IAbnormalDemandDao.queryReport", parameterMap);
    }

    /**
     * 生成计算report需要的所有信息
     *
     * @param parameterMap 参数列表
     */
    private void prepareReportFilters(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);

        List<String> dataList = abnormalDemandDao.queryReportColumns();
        List<String> columns = new ArrayList<>();
        List<String> sumColumns = new ArrayList<>();
        String type = (String) parameterMap.get("type");
        String currentMonth = new SimpleDateFormat("yyyyMM").format(new Date());
        String fcstVersion = (String) parameterMap.get("fcstVersion");
        String suffix = switch (type) {
            case "Net Net Price" -> "_NET_NET_VALUE";
            case "Moving Average Price" -> "_COST_VALUE";
            case "Quantity" -> "_ORDER_QUANTITY";
            case "Line" -> "_ORDER_LINE";
            default -> "";
        };

        for (String c : dataList) {
            sumColumns.add("sum(\"" + c + "\") \"" + c + "\"");
            columns.add("\"" + c + suffix + "\" \"" + c + "\"");
        }

        switch (type) {
            case "Net Net Price" -> {
                columns.add("t.AMU_ORDER_LINE ORDER_LINE"); // ORDER_LINE
                columns.add("t.AMU_NET_NET_VALUE AMU"); // AMU
                columns.add("t.OPEN_SO_QUANTITY * t.AMU_NET_NET_PRICE BACKLOG"); // BACKLOG
                columns.add("t.UNISSUE_DELIVERY_QUANTITY * t.AMU_NET_NET_PRICE DELIVERY"); // DELIVERY
                columns.add("t.STOCK_ON_HAND_QUANTITY * t.AMU_NET_NET_PRICE STOCK_ON_HAND"); // STOCK_ON_HAND
                columns.add("t.SO_STOCK_QUANTITY * t.AMU_NET_NET_PRICE SO_STOCK"); // SO_STOCK
                columns.add("t.TOP1_MTD_NET_NET_VALUE  TOP1_MTD"); // TOP1_MTD
                columns.add("t.TOP1_AMU_NET_NET_VALUE  TOP1_AMU"); // TOP1_AMU
                columns.add("t.TOP1_MTD_NET_NET_VALUE - t.TOP1_AMU_NET_NET_VALUE  TOP1_DIFF"); // TOP1_DIFF
                columns.add("t.TOP2_MTD_NET_NET_VALUE  TOP2_MTD"); // TOP2_MTD
                columns.add("t.TOP2_AMU_NET_NET_VALUE  TOP2_AMU"); // TOP2_AMU
                columns.add("t.TOP2_MTD_NET_NET_VALUE - t.TOP2_AMU_NET_NET_VALUE  TOP2_DIFF"); // TOP2_DIFF
                columns.add("t.TOP3_MTD_NET_NET_VALUE  TOP3_MTD"); // TOP3_MTD
                columns.add("t.TOP3_AMU_NET_NET_VALUE  TOP3_AMU"); // TOP3_AMU
                columns.add("t.TOP3_MTD_NET_NET_VALUE - t.TOP3_AMU_NET_NET_VALUE  TOP3_DIFF"); // TOP3_DIFF
            }
            case "Moving Average Price" -> {
                columns.add("t.AMU_ORDER_LINE ORDER_LINE"); // ORDER_LINE
                columns.add("t.AMU_COST_VALUE AMU"); // AMU
                columns.add("t.OPEN_SO_QUANTITY * t.AMU_COST_VALUE BACKLOG"); // BACKLOG
                columns.add("t.UNISSUE_DELIVERY_QUANTITY * t.AMU_COST_VALUE DELIVERY"); // DELIVERY
                columns.add("t.STOCK_ON_HAND_QUANTITY * t.AMU_COST_VALUE STOCK_ON_HAND"); // STOCK_ON_HAND
                columns.add("t.SO_STOCK_QUANTITY * t.AMU_COST_VALUE SO_STOCK"); // SO_STOCK
                columns.add("t.TOP1_MTD_COST_VALUE  TOP1_MTD"); // TOP1_MTD
                columns.add("t.TOP1_AMU_COST_VALUE  TOP1_AMU"); // TOP1_AMU
                columns.add("t.TOP1_MTD_COST_VALUE - t.TOP1_AMU_COST_VALUE  TOP1_DIFF"); // TOP1_DIFF
                columns.add("t.TOP2_MTD_COST_VALUE  TOP2_MTD"); // TOP2_MTD
                columns.add("t.TOP2_AMU_COST_VALUE  TOP2_AMU"); // TOP2_AMU
                columns.add("t.TOP2_MTD_COST_VALUE - t.TOP2_AMU_COST_VALUE  TOP2_DIFF"); // TOP2_DIFF
                columns.add("t.TOP3_MTD_COST_VALUE  TOP3_MTD"); // TOP3_MTD
                columns.add("t.TOP3_AMU_COST_VALUE  TOP3_AMU"); // TOP3_AMU
                columns.add("t.TOP3_MTD_COST_VALUE - t.TOP3_AMU_COST_VALUE  TOP3_DIFF"); // TOP3_DIFF
            }
            case "Quantity" -> {
                columns.add("t.AMU_ORDER_LINE ORDER_LINE"); // ORDER_LINE
                columns.add("t.AMU_ORDER_QUANTITY AMU"); // AMU
                columns.add("t.OPEN_SO_QUANTITY BACKLOG"); // BACKLOG
                columns.add("t.UNISSUE_DELIVERY_QUANTITY DELIVERY"); // DELIVERY
                columns.add("t.STOCK_ON_HAND_QUANTITY STOCK_ON_HAND"); // STOCK_ON_HAND
                columns.add("t.SO_STOCK_QUANTITY SO_STOCK"); // SO_STOCK
                columns.add("t.TOP1_MTD_ORDER_QUANTITY  TOP1_MTD"); // TOP1_MTD
                columns.add("t.TOP1_AMU_ORDER_QUANTITY  TOP1_AMU"); // TOP1_AMU
                columns.add("t.TOP1_MTD_ORDER_QUANTITY - t.TOP1_AMU_ORDER_QUANTITY  TOP1_DIFF"); // TOP1_DIFF
                columns.add("t.TOP2_MTD_ORDER_QUANTITY  TOP2_MTD"); // TOP2_MTD
                columns.add("t.TOP2_AMU_ORDER_QUANTITY  TOP2_AMU"); // TOP2_AMU
                columns.add("t.TOP2_MTD_ORDER_QUANTITY - t.TOP2_AMU_ORDER_QUANTITY  TOP2_DIFF"); // TOP2_DIFF
                columns.add("t.TOP3_MTD_ORDER_QUANTITY  TOP3_MTD"); // TOP3_MTD
                columns.add("t.TOP3_AMU_ORDER_QUANTITY  TOP3_AMU"); // TOP3_AMU
                columns.add("t.TOP3_MTD_ORDER_QUANTITY - t.TOP3_AMU_ORDER_QUANTITY  TOP3_DIFF"); // TOP3_DIFF
            }
            case "Line" -> {
                columns.add("t.AMU_ORDER_LINE ORDER_LINE"); // ORDER_LINE
                columns.add("t.AMU_ORDER_LINE AMU"); // AMU
                columns.add("t.OPEN_SO_LINE BACKLOG"); // BACKLOG
                columns.add("t.UNISSUE_DELIVERY_LINE DELIVERY"); // DELIVERY
                columns.add("t.STOCK_ON_HAND_LINE STOCK_ON_HAND"); // STOCK_ON_HAND
                columns.add("t.SO_STOCK_LINE SO_STOCK"); // SO_STOCK
                columns.add("t.TOP1_MTD_ORDER_LINE  TOP1_MTD"); // TOP1_MTD
                columns.add("t.TOP1_AMU_ORDER_LINE  TOP1_AMU"); // TOP1_AMU
                columns.add("t.TOP1_MTD_ORDER_LINE - t.TOP1_AMU_ORDER_LINE  TOP1_DIFF"); // TOP1_DIFF
                columns.add("t.TOP2_MTD_ORDER_LINE  TOP2_MTD"); // TOP2_MTD
                columns.add("t.TOP2_AMU_ORDER_LINE  TOP2_AMU"); // TOP2_AMU
                columns.add("t.TOP2_MTD_ORDER_LINE - t.TOP2_AMU_ORDER_LINE  TOP2_DIFF"); // TOP2_DIFF
                columns.add("t.TOP3_MTD_ORDER_LINE  TOP3_MTD"); // TOP3_MTD
                columns.add("t.TOP3_AMU_ORDER_LINE  TOP3_AMU"); // TOP3_AMU
                columns.add("t.TOP3_MTD_ORDER_LINE - t.TOP3_AMU_ORDER_LINE  TOP3_DIFF"); // TOP3_DIFF
            }
        }
        columns.add("t.\"" + currentMonth + "_ORDER_QUANTITY\" CURRENT_QTY"); // TOP3_DIFF
        sumColumns.add("round(case when sum(\"" + currentMonth + "_FCST\") is null or sum(\"" + currentMonth + "_FCST\") = 0 or avg(WD_RATIO) = 0 then 999.99 else sum(CURRENT_QTY) * 100 / sum(\"" + currentMonth + "_FCST\" * WD_RATIO) end, 2) FULFILL_RATIO");
        sumColumns.add("sum(ORDER_LINE) ORDER_LINE");
        sumColumns.add("sum(AMU) AMU");
        sumColumns.add("sum(BACKLOG) BACKLOG");
        sumColumns.add("sum(DELIVERY) DELIVERY");
        sumColumns.add("sum(STOCK_ON_HAND) STOCK_ON_HAND");
        sumColumns.add("sum(SO_STOCK) SO_STOCK");
        sumColumns.add("sum(TOP1_MTD) TOP1_MTD");
        sumColumns.add("sum(TOP1_AMU) TOP1_AMU");
        sumColumns.add("sum(TOP1_DIFF) TOP1_DIFF");
        sumColumns.add("sum(TOP2_MTD) TOP2_MTD");
        sumColumns.add("sum(TOP2_AMU) TOP2_AMU");
        sumColumns.add("sum(TOP2_DIFF) TOP2_DIFF");
        sumColumns.add("sum(TOP3_MTD) TOP3_MTD");
        sumColumns.add("sum(TOP3_AMU) TOP3_AMU");
        sumColumns.add("sum(TOP3_DIFF) TOP3_DIFF");
        parameterMap.put("columns", columns);
        parameterMap.put("sumColumns", sumColumns);

        // 计算当前月, 根据当前月计算fcst的起止版本
        Calendar calendar = Calendar.getInstance();
        for (int i = 0; i < 7; i++) {
            String month = new SimpleDateFormat("yyyyMM").format(calendar.getTime());
            int monthGap = DateCalUtil.calcMonthGap(Utils.parseInt(fcstVersion), Utils.parseInt(month)) + 1;
            String monthGapStr = monthGap < 10 ? "0" + monthGap : "" + monthGap;
            if (monthGap > 0 && monthGap < 25) {
                columns.add("t2.MONTH" + monthGapStr + " \"" + month + "_FCST\"");
            } else {
                columns.add("'' " + month);
            }
            sumColumns.add("sum(\"" + month + "_FCST\") \"" + month + "_FCST\"");
            calendar.add(Calendar.MONTH, 1);
        }
    }

    private void generateFilter(Map<String, Object> parameterMap) {
        // 生成筛选条件
        Object category = parameterMap.get("category");
        if (category instanceof JSONArray) {
            JSONArray categoryArray = (JSONArray) parameterMap.get("category");

            Map<String, List<String>> filterMap = new HashMap<>();

            for (Object subObj : categoryArray) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = subArray.getString(0);

                String value = subArray.getString(1);
                String key = Utils.randomStr(8);

                if (Utils.hasInjectionAttack(columnName)) {
                    continue;
                }

                List<String> fl = filterMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                fl.add("#{" + key + ",jdbcType=VARCHAR}");
                parameterMap.put(key, value);
            }

            List<String> filterList = new ArrayList<>();

            for (String key : filterMap.keySet()) {
                List<String> fl = filterMap.get(key);
                filterList.add("t." + key + " in (" + StringUtils.join(fl, ",") + ")");
            }

            parameterMap.put("filters", StringUtils.join(filterList, " and "));
        }

        String material = (String) parameterMap.get("material");
        if (StringUtils.isNotBlank(material)) {
            parameterMap.put("materialList", Utils.splitValue(material));
        }
    }
}
