package com.scp.demand.service.impl;

import com.starter.utils.Utils;
import com.starter.context.bean.CacheRemove;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.scp.demand.bean.FcstBean;
import com.scp.demand.bean.FcstPrivilegesCheck;
import com.scp.demand.dao.IFcstDao;
import com.scp.demand.service.IFcstService;
import com.starter.utils.excel.ExcelTemplate;
import com.starter.utils.excel.SimpleSheetContentsHandler;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.model.StylesTable;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;

@Service("fcstService")
@Scope("prototype")
@Transactional
public class FcstServiceImpl implements IFcstService {

    @Resource
    private Response response;

    @Resource
    private IFcstDao fcstDao;

    @Resource
    private ExcelTemplate excelTemplate;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage(String userid) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("fcstVersion", fcstDao.queryFcstVersion());
        resultMap.put("authedSalesOrgs", StringUtils.join(this.queryAuthSalesOrgsByUserid(userid), ","));
        return response.setBody(resultMap);
    }

    @Override
    public void downloadTemplate(Map<String, Object> parameterMap, HttpServletResponse response) {
        String uploadVersion = (String) parameterMap.get("uploadVersion");
        uploadVersion = StringUtils.isBlank(uploadVersion) ? new SimpleDateFormat("yyyyMM").format(new Date()) : uploadVersion;
        String fileName = "fcst_tempalte_" + uploadVersion + ".xlsx";
        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        LinkedHashMap<String, Object> map = new LinkedHashMap<>();
        resultList.add(map);
        map.put("Material", null);
        map.put("Sales Organization", null);
        map.put("Customer Code", null);
        map.put("Sales Group", null);
        map.put("Net Net Price RMB", null);
        map.put("Net Net Price HKD", null);
        for (int i = 0; i < 25; i++) {
            map.put(this.convertMonth(uploadVersion), null);
            uploadVersion = this.addMonth(uploadVersion);
        }
        excelTemplate.create(response, fileName, resultList);
    }

    @Override
    public void downloadFcstData(String uploadBy, Map<String, Object> parameterMap, HttpServletResponse response) {
        String fcstVersion = (String) parameterMap.get("fcst_version");
        fcstVersion = StringUtils.isBlank(fcstVersion) ? new SimpleDateFormat("yyyyMM").format(new Date()) : fcstVersion;
        String fileName = "fcst_data_" + fcstVersion + ".xlsx";
        parameterMap.put("fcst_version", fcstVersion);
        List<String> authedSalesOrgs = this.queryAuthSalesOrgsByUserid(uploadBy);
        if (authedSalesOrgs != null && authedSalesOrgs.isEmpty() == false) {
            if (authedSalesOrgs.size() == 1 && authedSalesOrgs.get(0).equals("*")) {
                parameterMap.put("is_admin", "1");
            }
            parameterMap.put("sales_orgs", authedSalesOrgs);

            List<Map<String, String>> columns = new ArrayList<>();
            for (int i = 1; i <= 25; i++) {
                Map<String, String> map = new HashMap<>();
                map.put("value", "MONTH" + (i < 10 ? "0" + i : i));
                map.put("name", this.convertMonth(fcstVersion));
                fcstVersion = this.addMonth(fcstVersion);
                columns.add(map);
            }
            parameterMap.put("columns", columns);

            excelTemplate.create(response, fileName, "com.scp.demand.dao.IFcstDao.downloadFcstData", parameterMap);
        } else {
            excelTemplate.create(response, fileName, new ArrayList<>());
        }
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response uploadFcst(String uploadBy, String uploadVersion, String uploadMode, MultipartFile file) throws Exception {
        File tempFile = File.createTempFile(String.valueOf(System.currentTimeMillis()), ".tmp");
        file.transferTo(tempFile);

        final String version = StringUtils.isBlank(uploadVersion) ? new SimpleDateFormat("yyyyMM").format(new Date()) : uploadVersion;

        List<String> authPlants = this.queryAuthSalesOrgsByUserid(uploadBy);
        if (authPlants == null) {
            return response.setError(new Exception("You have no privileges to upload forcast data"));
        }

        int salesOrgIndex = 1; // index start from 0
        int customerIndex = 2; // index start from 0

        boolean isAdmin = authPlants.size() == 1 && "*".equals(authPlants.get(0));

        // inner class 无法向外部传值,在这里只能定义一个对象, 通过对象进行传值
        FcstPrivilegesCheck fcstPrivilegesCheck = new FcstPrivilegesCheck();

        List<String> customerCodeList = fcstDao.queryAvaliableCustomerCode();
        Map<String, Object> customerCache = new HashMap<>();

        // 查询HKD和RMB的汇率
        Map<String, BigDecimal> exchangeRate = fcstDao.queryExchangeRate();
        BigDecimal rmb2hkd = exchangeRate.get("RMB_TO_HKD");
        BigDecimal hkd2rmb = exchangeRate.get("HKD_TO_RMB");

        // 检查是否有权限修改和添加SALES_ORGANIZATION
        excelTemplate.read(tempFile, 1, new SimpleSheetContentsHandler() {
            @Override
            public void handleRow(int rowNum) {

                if (rowNum == 0) {
                    return;
                }

                // 管理员不检查SALES_ORGANIZATION
                if (isAdmin == false) {
                    String so = row.get(salesOrgIndex);
                    if (authPlants.contains(so) == false) {
                        fcstPrivilegesCheck.setPassed(false);
                        fcstPrivilegesCheck.addSalesOrg(so);
                    }
                }

                String cc = StringUtils.upperCase(row.get(customerIndex));
                if (customerCache.containsKey(cc) == false) {
                    if (customerCodeList.contains(cc)) {
                        customerCache.put(cc, 0);
                    } else {
                        fcstPrivilegesCheck.setPassed(false);
                        fcstPrivilegesCheck.addCustomerCode(cc);
                    }
                }

            }
        }, new StylesTable());

        if (fcstPrivilegesCheck.isPassed() == false) {
            List<String> message = new ArrayList<>();
            message.add("You cannot access the data below:");
            if (fcstPrivilegesCheck.getSalesOrg().isEmpty() == false) {
                message.add("- Sales organization: " + fcstPrivilegesCheck.getSalesOrgs());
            }
            if (fcstPrivilegesCheck.getCustomerCode().isEmpty() == false) {
                message.add("- Customer Code: " + fcstPrivilegesCheck.getCustomerCodes());
            }
            return response.setError(new Exception(StringUtils.join(message, "<br/>")));
        }

        List<String> HKSalesOrg = new ArrayList<>();
        HKSalesOrg.add("K001");
        HKSalesOrg.add("K003");

        List<FcstBean> data = new ArrayList<>();
        excelTemplate.read(tempFile, 1, new SimpleSheetContentsHandler() {
            @Override
            public void handleRow(int rowNum) {

                if (rowNum == 0) {
                    return;
                }

                int i = 0;
                FcstBean bean = new FcstBean();
                bean.setMaterial(row.get(i++));
                bean.setSales_org(row.get(i++));
                bean.setCustomer_code(row.get(i++));
                bean.setSales_group(row.get(i++));

                if (HKSalesOrg.contains(bean.getSales_org()) == true) {
                    i++; // skip rmb
                    bean.setNet_net_price_hkd(Utils.parseBigDecimal(row.get(i++), 4));
                    bean.setNet_net_price_rmb(bean.getNet_net_price_hkd().multiply(hkd2rmb).setScale(4, RoundingMode.HALF_UP)); // convert hkd to rmb
                } else {
                    bean.setNet_net_price_rmb(Utils.parseBigDecimal(row.get(i++), 4));
                    i++; // skip hkd
                    bean.setNet_net_price_hkd(bean.getNet_net_price_rmb().multiply(rmb2hkd).setScale(4, RoundingMode.HALF_UP));
                }

                bean.setMonth01(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth02(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth03(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth04(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth05(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth06(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth07(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth08(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth09(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth10(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth11(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth12(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth13(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth14(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth15(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth16(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth17(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth18(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth19(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth20(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth21(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth22(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth23(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth24(Utils.parseBigDecimal(row.get(i++), 2));
                bean.setMonth25(Utils.parseBigDecimal(row.get(i), 2));

                data.add(bean);

                if (data.size() >= 1000) {
                    fcstDao.insertFsctDataTemp(uploadBy, version, data);
                    data.clear();
                }
            }
        }, new StylesTable());

        if (data.size() > 0) {
            fcstDao.insertFsctDataTemp(uploadBy, version, data);
            data.clear();
        }

        // 如果是REPLACE-ALL, 先删掉所有数据
        if ("REPLACE-ALL".equalsIgnoreCase(uploadMode)) {
            fcstDao.deleteFcstData(uploadBy, version);
        }

        // INSERT-IGNORE 不需要执行 "when matched then"
        // 其余选项都按照MERGE规则执行
        fcstDao.mergeFcstData(uploadBy, uploadMode);

        if (tempFile.delete() == false) {
            System.err.println(tempFile.getAbsolutePath() + " delete failed");
        }

        return response;
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryFcstColumns(String fcstVersion) {
        List<Map<String, String>> resultList = new ArrayList<>();
        for (int i = 1; i <= 25; i++) {
            Map<String, String> map = new HashMap<>();
            map.put("data", "MONTH" + (i < 10 ? "0" + i : i));
            map.put("title", this.convertMonth(fcstVersion));
            fcstVersion = this.addMonth(fcstVersion);
            resultList.add(map);
        }

        return response.setBody(resultList);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryFcstData(String uploadBy, Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);

        List<String> authedSalesOrgs = this.queryAuthSalesOrgsByUserid(uploadBy);
        int total = 0;
        if (authedSalesOrgs != null && authedSalesOrgs.isEmpty() == false) {
            if (authedSalesOrgs.size() == 1 && authedSalesOrgs.get(0).equals("*")) {
                parameterMap.put("is_admin", "1");
            }
            parameterMap.put("sales_orgs", authedSalesOrgs);
            total = fcstDao.queryFcstDataCount(parameterMap);
        }

        page.setTotal(total);
        if (total > 0) {
            page.setData(fcstDao.queryFcstData(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    @SuppressWarnings("unchecked")
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response saveForcastSource(String userid, Map<String, Object> parameterMap) {
        Map<String, Object> updateMap = (Map<String, Object>) parameterMap.get("forcastSourceUpdated");
        Map<String, Object> param = new HashMap<>();
        for (String key : updateMap.keySet()) {
            List<Map<String, Object>> cols = new ArrayList<>();
            param.put("rowid", key);
            param.put("cols", cols);

            Map<String, Object> colMap = (Map<String, Object>) updateMap.get(key);
            for (String k : colMap.keySet()) {
                if (Utils.hasInjectionAttack(k) == true) {
                    return response.setError(new Exception("Invalid update parameters!"));
                }

                Map<String, Object> col = new HashMap<>();
                col.put("key", k);
                col.put("value", colMap.get(k));
                cols.add(col);
            }
            param.put("userid", userid);
            fcstDao.saveForcastSource(param);
        }
        return response;
    }

    private String convertMonth(String fullMonth) {
        if (StringUtils.length(fullMonth) != 6) {
            return fullMonth;
        }
        String year = fullMonth.substring(2, 4);
        String month = fullMonth.substring(4, 6);
        return switch (month) {
            case "01" -> "JAN-" + year;
            case "02" -> "FEB-" + year;
            case "03" -> "MAR-" + year;
            case "04" -> "APR-" + year;
            case "05" -> "MAY-" + year;
            case "06" -> "JUN-" + year;
            case "07" -> "JUL-" + year;
            case "08" -> "AUG-" + year;
            case "09" -> "SEP-" + year;
            case "10" -> "OCT-" + year;
            case "11" -> "NOV-" + year;
            case "12" -> "DEC-" + year;
            default -> fullMonth;
        };
    }

    private String addMonth(String fullMonth) {
        if (StringUtils.length(fullMonth) != 6) {
            return fullMonth;
        }
        int year = Utils.parseInt(fullMonth.substring(0, 4));
        int month = Utils.parseInt(fullMonth.substring(4, 6));

        if (month == 12) {
            month = 1;
            year++;
        } else {
            month++;
        }
        return year + "" + (month > 9 ? month : "0" + month);
    }

    private List<String> queryAuthSalesOrgsByUserid(String userid) {
        String authedSalesOrgs = fcstDao.queryAuthSalesOrgsByUserid(userid);
        if (StringUtils.isBlank(authedSalesOrgs)) {
            return null;
        } else {
            List<String> resultList = new ArrayList<>();
            authedSalesOrgs = authedSalesOrgs.replace("，", ",");
            for (String s : authedSalesOrgs.split(",")) {
                resultList.add(s.trim().toUpperCase());
            }
            return resultList;
        }
    }
}
