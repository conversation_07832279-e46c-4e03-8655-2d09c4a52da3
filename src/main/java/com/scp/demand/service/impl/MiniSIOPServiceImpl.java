package com.scp.demand.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.scp.demand.bean.MiniSIOP;
import com.scp.demand.dao.IMiniSIOPDao;
import com.scp.demand.service.IMiniSIOPService;
import com.starter.utils.DateCalUtil;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import com.starter.utils.excel.SheetInfoWithData;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;

@Service("miniSIOPServiceImpl")
@Scope("prototype")
@Transactional
public class MiniSIOPServiceImpl implements IMiniSIOPService {

    @Resource
    private Response response;

    @Resource
    private IMiniSIOPDao miniSIOPDao;

    @Resource
    private ExcelTemplate excelTemplate;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response intiPage() {
        Map<String, Object> finalResult = new HashMap<>();
        finalResult.put("cascader", Utils.parseCascader(miniSIOPDao.queryCascader()));
        finalResult.put("fcstVersion", miniSIOPDao.queryFcstVersion());

        return response.setBody(finalResult);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);

        Map<String, Map<String, Object>> finalResult = new HashMap<>();

        this.calcDemand(parameterMap, finalResult);
        this.calcSTDSales(parameterMap, finalResult);
        this.calcSTDOrderIntake(parameterMap, finalResult);
        this.calcSTDCRD(parameterMap, finalResult);
        this.calcSOHDemand(parameterMap, finalResult);
        this.calcGITDemand(parameterMap, finalResult);
        this.calcConfirmResourceDemand(parameterMap, finalResult);
        this.calcOrderedDemand(parameterMap, finalResult);
        this.calcSOHOrder(parameterMap, finalResult);
        this.calcGITOrder(parameterMap, finalResult);
        this.calcConfirmResourceOrder(parameterMap, finalResult);

        return response.setBody(finalResult);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        parameterMap.put("queryKey", this.setQueryKeyBySelected(parameterMap));

        page.setTotal(miniSIOPDao.queryDetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(miniSIOPDao.queryDetails(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        this.generateFilter(parameterMap);
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        parameterMap.put("queryKey", this.setQueryKeyBySelected(parameterMap));

        String fileName = "MiniSIOP_[" + parameterMap.get("report1Category") + "]_[" + parameterMap.get("report1SubCategory") + "]_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.demand.dao.IMiniSIOPDao.queryDetails", parameterMap);
    }

    private String setQueryKeyBySelected(Map<String, Object> parameterMap) {
        String category = (String) parameterMap.get("report1Category");
        String subCategory = (String) parameterMap.get("report1SubCategory");

        String prefix = "queryDetails";

        switch (category) {
            case MiniSIOP.X_DEMAND:
                switch (subCategory) {
                    case MiniSIOP.Y_ACTUAL_SALES_BEFORE_SF -> {
                        return prefix + "_Demand_ActualSalesBeforeSF";
                    }
                    case MiniSIOP.Y_UNREALIZED_LT30_FCST -> {
                        return prefix + "_Demand_UnrealizedLT30Fcst";
                    }
                    case MiniSIOP.Y_UNREALIZED_GT30_FCST -> {
                        return prefix + "_Demand_UnrealizedGT30Fcst";
                    }
                    case MiniSIOP.Y_UNREALIZED_GT70_FCST -> {
                        return prefix + "_Demand_UnrealizedGT70Fcst";
                    }
                    case MiniSIOP.Y_PREDICTED_SALES -> {
                        return prefix + "_Demand_PredictedSales";
                    }
                    case MiniSIOP.Y_SALES_EX_FCST_LT30 -> {
                        return prefix + "_Demand_SalesExFcstLT30";
                    }
                    case MiniSIOP.Y_SALES_EX_FCST_GT30 -> {
                        return prefix + "_Demand_SalesExFcstGT30";
                    }
                    case MiniSIOP.Y_SALES_EX_FCST_GT70 -> {
                        return prefix + "_Demand_SalesExFcstGT70";
                    }
                    case MiniSIOP.Y_SALES_EX_FCST_GT100 -> {
                        return prefix + "_Demand_SalesExFcstGT100";
                    }
                }
            case MiniSIOP.X_STD_SALES:
                switch (subCategory) {
                    case MiniSIOP.Y_ACTUAL_SALES_BEFORE_SF -> {
                        return prefix + "_Demand_ActualSalesBeforeSF";
                    }
                    case MiniSIOP.Y_PREDICTED_SALES -> {
                        return prefix + "_Demand_PredictedSales";
                    }
                    case MiniSIOP.Y_SALES_EX_FCST_LT30 -> {
                        return prefix + "_Demand_SalesExFcstLT30";
                    }
                    case MiniSIOP.Y_SALES_EX_FCST_GT30 -> {
                        return prefix + "_Demand_SalesExFcstGT30";
                    }
                    case MiniSIOP.Y_SALES_EX_FCST_GT70 -> {
                        return prefix + "_Demand_SalesExFcstGT70";
                    }
                    case MiniSIOP.Y_SALES_EX_FCST_GT100 -> {
                        return prefix + "_Demand_SalesExFcstGT100";
                    }
                    case MiniSIOP.Y_IG_SALES -> {
                        return prefix + "_STDSales_IGSales";
                    }
                }
            case MiniSIOP.X_STD_OI:
                switch (subCategory) {
                    case MiniSIOP.Y_IG_OI -> {
                        return prefix + "_STDOI_IGOI";
                    }
                    case MiniSIOP.Y_OI_EX_FCST_GT100 -> {
                        return prefix + "_STDOI_ExFcstGT100";
                    }
                    case MiniSIOP.Y_OI_EX_FCST_GT70 -> {
                        return prefix + "_STDOI_ExFcstGT70";
                    }
                    case MiniSIOP.Y_OI_EX_FCST_GT30 -> {
                        return prefix + "_STDOI_ExFcstGT30";
                    }
                    case MiniSIOP.Y_OI_EX_FCST_LT30 -> {
                        return prefix + "_STDOI_ExFcstLT30";
                    }
                    case MiniSIOP.Y_PREDICTED_OI -> {
                        return prefix + "_STDOI_PredictedOI";
                    }
                    case MiniSIOP.Y_ACTUAL_OI_BEFORE_SF -> {
                        return prefix + "_STDOI_ActualOIBeforeSP";
                    }
                }
            case MiniSIOP.X_STD_CRD:
                switch (subCategory) {
                    case MiniSIOP.Y_IG_CRD -> {
                        return prefix + "_STDCRD_IGCRD";
                    }
                    case MiniSIOP.Y_CRD_EX_FCST_GT100 -> {
                        return prefix + "_STDCRD_ExFcstGT100";
                    }
                    case MiniSIOP.Y_CRD_EX_FCST_GT70 -> {
                        return prefix + "_STDCRD_ExFcstGT70";
                    }
                    case MiniSIOP.Y_CRD_EX_FCST_GT30 -> {
                        return prefix + "_STDCRD_ExFcstGT30";
                    }
                    case MiniSIOP.Y_CRD_EX_FCST_LT30 -> {
                        return prefix + "_STDCRD_ExFcstLT30";
                    }
                    case MiniSIOP.Y_PREDICTED_CRD -> {
                        return prefix + "_STDCRD_PredictedOI";
                    }
                    case MiniSIOP.Y_ACTUAL_CRD_BEFORE_SF -> {
                        return prefix + "_STDCRD_ActualCRDBeforeSP";
                    }
                }
            case MiniSIOP.X_SOH_DEMAND:
                switch (subCategory) {
                    case MiniSIOP.Y_UD_LT7CD -> {
                        return prefix + "_SOHDemand_UDLT7";
                    }
                    case MiniSIOP.Y_UD_GT7CD -> {
                        return prefix + "_SOHDemand_UDGT7";
                    }
                    case MiniSIOP.Y_UD_GT30CD -> {
                        return prefix + "_SOHDemand_UDGT30";
                    }
                    case MiniSIOP.Y_FOR_DEMAND_IN_SP -> {
                        return prefix + "_SOHDemand_DemandInSP";
                    }
                    case MiniSIOP.Y_IG_UD -> {
                        return prefix + "_SOHDemand_IGUD";
                    }
                    case MiniSIOP.Y_FOR_IG_ORDER_IN_SP -> {
                        return prefix + "_SOHDemand_IGOrderInSP";
                    }
                    case MiniSIOP.Y_FOR_SAFETY_STOCK -> {
                        return prefix + "_SOHDemand_SafetyStock";
                    }
                    case MiniSIOP.Y_FOR_FUTURE_DEMAND -> {
                        return prefix + "_SOHDemand_FutureDemand";
                    }
                    case MiniSIOP.Y_STOCK_AGING_GT1Y -> {
                        return prefix + "_SOHDemand_AgingGT1Y";
                    }
                    case MiniSIOP.Y_STOCK_AGING_GT2Y -> {
                        return prefix + "_SOHDemand_AgingGT2Y";
                    }
                }
            case MiniSIOP.X_GIT_DEMAND:
                switch (subCategory) {
                    case MiniSIOP.Y_FOR_DEMAND_IN_SP -> {
                        return prefix + "_GITDemand_DemandInSP";
                    }
                    case MiniSIOP.Y_FOR_IG_ORDER_IN_SP -> {
                        return prefix + "_GITDemand_IGOrderInSP";
                    }
                    case MiniSIOP.Y_FOR_SAFETY_STOCK -> {
                        return prefix + "_GITDemand_SafetyStock";
                    }
                    case MiniSIOP.Y_FOR_FUTURE_DEMAND -> {
                        return prefix + "_GITDemand_FutureDemand";
                    }
                }
            case MiniSIOP.X_CONF_RES_DEMAND:
                switch (subCategory) {
                    case MiniSIOP.Y_FOR_DEMAND_IN_SP -> {
                        return prefix + "_ConfRes_DemandInSP";
                    }
                    case MiniSIOP.Y_FOR_IG_ORDER_IN_SP -> {
                        return prefix + "_ConfRes_IGOrderInSP";
                    }
                    case MiniSIOP.Y_FOR_SAFETY_STOCK -> {
                        return prefix + "_ConfRes_SafetyStock";
                    }
                    case MiniSIOP.Y_FOR_FUTURE_DEMAND -> {
                        return prefix + "_ConfRes_FutureDemand";
                    }
                }
            case MiniSIOP.X_ORDERED_DEMAND:
                switch (subCategory) {
                    case MiniSIOP.Y_ACTUAL_SALES_BEFORE_SF -> {
                        return prefix + "_Demand_ActualSalesBeforeSF";
                    }
                    case MiniSIOP.Y_PREDICTED_SALES -> {
                        return prefix + "_Demand_PredictedSales";
                    }
                    case MiniSIOP.Y_SALES_EX_FCST_LT30 -> {
                        return prefix + "_Demand_SalesExFcstLT30";
                    }
                    case MiniSIOP.Y_SALES_EX_FCST_GT30 -> {
                        return prefix + "_Demand_SalesExFcstGT30";
                    }
                    case MiniSIOP.Y_SALES_EX_FCST_GT70 -> {
                        return prefix + "_Demand_SalesExFcstGT70";
                    }
                    case MiniSIOP.Y_SALES_EX_FCST_GT100 -> {
                        return prefix + "_Demand_SalesExFcstGT100";
                    }
                    case MiniSIOP.Y_BACK_ORDER -> {
                        return prefix + "_OrderedDemand_BackOrder";
                    }
                    case MiniSIOP.Y_CRD_IN_SP -> {
                        return prefix + "_OrderedDemand_CRDInSP";
                    }
                    case MiniSIOP.Y_CRD_OUT_SP -> {
                        return prefix + "_OrderedDemand_CRDOutSP";
                    }
                    case MiniSIOP.Y_IG_CRD -> {
                        return prefix + "_OrderedDemand_IGCRD";
                    }
                }
            case MiniSIOP.X_SOH_ORDER:
                switch (subCategory) {
                    case MiniSIOP.Y_UD_LT7CD -> {
                        return prefix + "_SOHOrder_UDLT7";
                    }
                    case MiniSIOP.Y_UD_GT7CD -> {
                        return prefix + "_SOHOrder_UDGT7";
                    }
                    case MiniSIOP.Y_UD_GT30CD -> {
                        return prefix + "_SOHOrder_UDGT30";
                    }
                    case MiniSIOP.Y_FOR_ORDER_IN_SP -> {
                        return prefix + "_SOHOrder_OrderInSP";
                    }
                    case MiniSIOP.Y_IG_UD -> {
                        return prefix + "_SOHOrder_IGUD";
                    }
                    case MiniSIOP.Y_FOR_IG_ORDER_IN_SP -> {
                        return prefix + "_SOHOrder_IGOrderInSP";
                    }
                    case MiniSIOP.Y_FOR_SAFETY_STOCK -> {
                        return prefix + "_SOHOrder_SafetyStock";
                    }
                    case MiniSIOP.Y_FOR_FUTURE_ORDER -> {
                        return prefix + "_SOHOrder_FutureOrder";
                    }
                    case MiniSIOP.Y_FOR_FUTURE_DEMAND -> {
                        return prefix + "_SOHOrder_FutureDemand";
                    }
                    case MiniSIOP.Y_STOCK_AGING_GT1Y -> {
                        return prefix + "_SOHOrder_AgingGT1Y";
                    }
                    case MiniSIOP.Y_STOCK_AGING_GT2Y -> {
                        return prefix + "_SOHOrder_AgingGT2Y";
                    }
                }
            case MiniSIOP.X_GIT_ORDER:
                switch (subCategory) {
                    case MiniSIOP.Y_FOR_ORDER_IN_SP -> {
                        return prefix + "_GITOrder_OrderInSP";
                    }
                    case MiniSIOP.Y_FOR_IG_ORDER_IN_SP -> {
                        return prefix + "_GITOrder_IGOrderInSP";
                    }
                    case MiniSIOP.Y_FOR_SAFETY_STOCK -> {
                        return prefix + "_GITOrder_SafetyStock";
                    }
                    case MiniSIOP.Y_FOR_FUTURE_ORDER -> {
                        return prefix + "_GITOrder_FutureOrder";
                    }
                    case MiniSIOP.Y_FOR_FUTURE_DEMAND -> {
                        return prefix + "_GITOrder_FutureDemand";
                    }
                }
            case MiniSIOP.X_CONF_RES_ORDER:
                switch (subCategory) {
                    case MiniSIOP.Y_FOR_ORDER_IN_SP -> {
                        return prefix + "_ConfResOrder_DemandInSP";
                    }
                    case MiniSIOP.Y_FOR_IG_ORDER_IN_SP -> {
                        return prefix + "_ConfResOrder_IGOrderInSP";
                    }
                    case MiniSIOP.Y_FOR_SAFETY_STOCK -> {
                        return prefix + "_ConfResOrder_SafetyStock";
                    }
                    case MiniSIOP.Y_FOR_FUTURE_ORDER -> {
                        return prefix + "_ConfResOrder_FutureOrder";
                    }
                    case MiniSIOP.Y_FOR_FUTURE_DEMAND -> {
                        return prefix + "_ConfResOrder_FutureDemand";
                    }
                }
        }
        return "EMPTY";
    }

    // 计算Demand
    private void calcDemand(Map<String, Object> parameterMap, Map<String, Map<String, Object>> finalResult) {
        Map<String, Object> result = new HashMap<>();

        // 计算数据
        List<Map<String, Object>> data = miniSIOPDao.querySummary_Demand(parameterMap);
        BigDecimal predictedSales = BigDecimal.ZERO;
        for (Map<String, Object> map : data) {
            predictedSales = predictedSales.add(Utils.parseBigDecimal(map.get("PREDICTED_VALUE"), BigDecimal.ZERO));
            String key = (String) map.get("RATIO_LEVEL");
            switch (key) {
                case "GT200" -> result.put(MiniSIOP.Y_SALES_EX_FCST_GT100, Utils.parseBigDecimal(map.get("VALUE"), null));
                case "GT170" -> result.put(MiniSIOP.Y_SALES_EX_FCST_GT70, Utils.parseBigDecimal(map.get("VALUE"), null));
                case "GT130" -> result.put(MiniSIOP.Y_SALES_EX_FCST_GT30, Utils.parseBigDecimal(map.get("VALUE"), null));
                case "GT100" -> result.put(MiniSIOP.Y_SALES_EX_FCST_LT30, Utils.parseBigDecimal(map.get("VALUE"), null));
                case "GT070" -> result.put(MiniSIOP.Y_UNREALIZED_GT70_FCST, Utils.parseBigDecimal(map.get("VALUE"), null));
                case "GT030" -> result.put(MiniSIOP.Y_UNREALIZED_GT30_FCST, Utils.parseBigDecimal(map.get("VALUE"), null));
                case "LT030" -> result.put(MiniSIOP.Y_UNREALIZED_LT30_FCST, Utils.parseBigDecimal(map.get("VALUE"), null));
            }
        }

        result.put(MiniSIOP.Y_PREDICTED_SALES, predictedSales);
        result.put(MiniSIOP.Y_ACTUAL_SALES_BEFORE_SF, miniSIOPDao.querySummary_Demand_ActualSalesBeforeSF(parameterMap));
        finalResult.put(MiniSIOP.X_DEMAND, result);
    }

    // 计算STD Sales
    private void calcSTDSales(Map<String, Object> parameterMap, Map<String, Map<String, Object>> finalResult) {
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> demand = finalResult.get(MiniSIOP.X_DEMAND);

        result.put(MiniSIOP.Y_IG_SALES, miniSIOPDao.querySummary_STDSales_IGSales(parameterMap));

        result.put(MiniSIOP.Y_SALES_EX_FCST_GT100, demand.get(MiniSIOP.Y_SALES_EX_FCST_GT100));
        result.put(MiniSIOP.Y_SALES_EX_FCST_GT70, demand.get(MiniSIOP.Y_SALES_EX_FCST_GT70));
        result.put(MiniSIOP.Y_SALES_EX_FCST_GT30, demand.get(MiniSIOP.Y_SALES_EX_FCST_GT30));
        result.put(MiniSIOP.Y_SALES_EX_FCST_LT30, demand.get(MiniSIOP.Y_SALES_EX_FCST_LT30));
        result.put(MiniSIOP.Y_PREDICTED_SALES, demand.get(MiniSIOP.Y_PREDICTED_SALES));
        result.put(MiniSIOP.Y_ACTUAL_SALES_BEFORE_SF, demand.get(MiniSIOP.Y_ACTUAL_SALES_BEFORE_SF));

        finalResult.put(MiniSIOP.X_STD_SALES, result);
    }

    // 计算STD Order Intake
    private void calcSTDOrderIntake(Map<String, Object> parameterMap, Map<String, Map<String, Object>> finalResult) {
        Map<String, Object> result = new HashMap<>();

        // 计算数据
        List<Map<String, Object>> data = miniSIOPDao.querySummary_STDOI(parameterMap);
        BigDecimal predictedOI = BigDecimal.ZERO;
        for (Map<String, Object> map : data) {
            predictedOI = predictedOI.add(Utils.parseBigDecimal(map.get("PREDICTED_VALUE"), BigDecimal.ZERO));
            String key = (String) map.get("RATIO_LEVEL");
            switch (key) {
                case "GT200" -> result.put(MiniSIOP.Y_OI_EX_FCST_GT100, Utils.parseBigDecimal(map.get("VALUE"), null));
                case "GT170" -> result.put(MiniSIOP.Y_OI_EX_FCST_GT70, Utils.parseBigDecimal(map.get("VALUE"), null));
                case "GT130" -> result.put(MiniSIOP.Y_OI_EX_FCST_GT30, Utils.parseBigDecimal(map.get("VALUE"), null));
                case "GT100" -> result.put(MiniSIOP.Y_OI_EX_FCST_LT30, Utils.parseBigDecimal(map.get("VALUE"), null));
            }
        }

        result.put(MiniSIOP.Y_PREDICTED_OI, predictedOI);
        result.put(MiniSIOP.Y_IG_OI, miniSIOPDao.querySummary_STDOI_IGOI(parameterMap));
        result.put(MiniSIOP.Y_ACTUAL_OI_BEFORE_SF, miniSIOPDao.querySummary_STDOI_ActualOIBeforeSF(parameterMap));
        finalResult.put(MiniSIOP.X_STD_OI, result);
    }

    // 计算STD CRD
    private void calcSTDCRD(Map<String, Object> parameterMap, Map<String, Map<String, Object>> finalResult) {
        Map<String, Object> result = new HashMap<>();

        // 计算数据
        List<Map<String, Object>> data = miniSIOPDao.querySummary_STDCRD(parameterMap);
        BigDecimal predictedCRD = BigDecimal.ZERO;
        for (Map<String, Object> map : data) {
            predictedCRD = predictedCRD.add(Utils.parseBigDecimal(map.get("PREDICTED_VALUE"), BigDecimal.ZERO));
            String key = (String) map.get("RATIO_LEVEL");
            switch (key) {
                case "GT200" -> result.put(MiniSIOP.Y_CRD_EX_FCST_GT100, Utils.parseBigDecimal(map.get("VALUE"), null));
                case "GT170" -> result.put(MiniSIOP.Y_CRD_EX_FCST_GT70, Utils.parseBigDecimal(map.get("VALUE"), null));
                case "GT130" -> result.put(MiniSIOP.Y_CRD_EX_FCST_GT30, Utils.parseBigDecimal(map.get("VALUE"), null));
                case "GT100" -> result.put(MiniSIOP.Y_CRD_EX_FCST_LT30, Utils.parseBigDecimal(map.get("VALUE"), null));
            }
        }

        result.put(MiniSIOP.Y_PREDICTED_CRD, predictedCRD);
        result.put(MiniSIOP.Y_IG_CRD, miniSIOPDao.querySummary_STDCRD_IGCRD(parameterMap));
        result.put(MiniSIOP.Y_ACTUAL_CRD_BEFORE_SF, miniSIOPDao.querySummary_STDCRD_ActualCRDBeforeSF(parameterMap));


        finalResult.put(MiniSIOP.X_STD_CRD, result);
    }

    // 计算SOH Demand
    private void calcSOHDemand(Map<String, Object> parameterMap, Map<String, Map<String, Object>> finalResult) {
        Map<String, Object> result = new HashMap<>();

        // 计算数据
        List<Map<String, Object>> data = miniSIOPDao.querySummary_SOHDemand(parameterMap);

        for (Map<String, Object> map : data) {
            String resultType = (String) map.get("TYPE");
            BigDecimal value = Utils.parseBigDecimal(map.get("VALUE"), BigDecimal.ZERO);
            switch (resultType) {
                case "OG_UD_LT_7_CD" -> result.put(MiniSIOP.Y_UD_LT7CD, value);
                case "OG_UD_LT_30_CD" -> result.put(MiniSIOP.Y_UD_GT7CD, value);
                case "OG_UD_GT_30_CD" -> result.put(MiniSIOP.Y_UD_GT30CD, value);
                case "IG_UD" -> result.put(MiniSIOP.Y_IG_UD, value);
                case "FOR_DEMAND_IN_SELECTED_PERIOD" -> result.put(MiniSIOP.Y_FOR_DEMAND_IN_SP, value);
                case "FOR_IG_ORDER_IN_SELECTED_PERIOD" -> result.put(MiniSIOP.Y_FOR_IG_ORDER_IN_SP, value);
                case "FOR_SAFETY_STOCK" -> result.put(MiniSIOP.Y_FOR_SAFETY_STOCK, value);
                case "FOR_FUTURE_DEMAND" -> result.put(MiniSIOP.Y_FOR_FUTURE_DEMAND, value);
                case "STOCK_AGING_GT_1Y" -> result.put(MiniSIOP.Y_STOCK_AGING_GT1Y, value);
                case "STOCK_AGING_GT_2Y" -> result.put(MiniSIOP.Y_STOCK_AGING_GT2Y, value);
            }
        }

        finalResult.put(MiniSIOP.X_SOH_DEMAND, result);
    }

    // 计算GIT Demand
    private void calcGITDemand(Map<String, Object> parameterMap, Map<String, Map<String, Object>> finalResult) {
        Map<String, Object> result = new HashMap<>();

        // 计算数据
        List<Map<String, Object>> data = miniSIOPDao.querySummary_GITDemand(parameterMap);

        for (Map<String, Object> map : data) {
            String type = (String) map.get("TYPE");
            BigDecimal value = Utils.parseBigDecimal(map.get("VALUE"), BigDecimal.ZERO);
            switch (type) {
                case "LA_FOR_DEMAND_IN_SP" -> result.put(MiniSIOP.Y_FOR_DEMAND_IN_SP, value);
                case "LA_FOR_IG_ORDER" -> result.put(MiniSIOP.Y_FOR_IG_ORDER_IN_SP, value);
                case "LA_FOR_SS" -> result.put(MiniSIOP.Y_FOR_SAFETY_STOCK, value);
                case "LA_FOR_FURTHER_DEMAND" -> result.put(MiniSIOP.Y_FOR_FUTURE_DEMAND, value);
            }
        }

        finalResult.put(MiniSIOP.X_GIT_DEMAND, result);
    }

    // 计算Confirm Resource Demand
    private void calcConfirmResourceDemand(Map<String, Object> parameterMap, Map<String, Map<String, Object>> finalResult) {
        Map<String, Object> result = new HashMap<>();

        // 计算数据
        List<Map<String, Object>> data = miniSIOPDao.querySummary_ConfResDemand(parameterMap);

        for (Map<String, Object> map : data) {
            String type = (String) map.get("TYPE");
            BigDecimal value = Utils.parseBigDecimal(map.get("VALUE"), BigDecimal.ZERO);
            switch (type) {
                case "LA_FOR_DEMAND_IN_SP" -> result.put(MiniSIOP.Y_FOR_DEMAND_IN_SP, value);
                case "LA_FOR_IG_ORDER" -> result.put(MiniSIOP.Y_FOR_IG_ORDER_IN_SP, value);
                case "LA_FOR_SS" -> result.put(MiniSIOP.Y_FOR_SAFETY_STOCK, value);
                case "LA_FOR_FURTHER_DEMAND" -> result.put(MiniSIOP.Y_FOR_FUTURE_DEMAND, value);
            }
        }

        finalResult.put(MiniSIOP.X_CONF_RES_DEMAND, result);
    }

    // 计算Ordered Demand
    private void calcOrderedDemand(Map<String, Object> parameterMap, Map<String, Map<String, Object>> finalResult) {
        Map<String, Object> result = new HashMap<>();
        Map<String, Object> demand = finalResult.get(MiniSIOP.X_DEMAND);

        BigDecimal backorder = Utils.parseBigDecimal(miniSIOPDao.querySummary_OrderedDemand_BackOrder(parameterMap), BigDecimal.ZERO);
        BigDecimal crdInSP = Utils.parseBigDecimal(miniSIOPDao.querySummary_OrderedDemand_CRDInSP(parameterMap), BigDecimal.ZERO);
        crdInSP = crdInSP.subtract(backorder); // 计算出来的CRD in Selected Period结果需要扣除掉backorder的数值

        result.put(MiniSIOP.Y_IG_CRD, miniSIOPDao.querySummary_OrderedDemand_IGCRD(parameterMap));
        result.put(MiniSIOP.Y_CRD_OUT_SP, miniSIOPDao.querySummary_OrderedDemand_CRDOutSP(parameterMap));
        result.put(MiniSIOP.Y_CRD_IN_SP, crdInSP);
        result.put(MiniSIOP.Y_BACK_ORDER, backorder);

        result.put(MiniSIOP.Y_SALES_EX_FCST_GT100, demand.get(MiniSIOP.Y_SALES_EX_FCST_GT100));
        result.put(MiniSIOP.Y_SALES_EX_FCST_GT70, demand.get(MiniSIOP.Y_SALES_EX_FCST_GT70));
        result.put(MiniSIOP.Y_SALES_EX_FCST_GT30, demand.get(MiniSIOP.Y_SALES_EX_FCST_GT30));
        result.put(MiniSIOP.Y_SALES_EX_FCST_LT30, demand.get(MiniSIOP.Y_SALES_EX_FCST_LT30));
        result.put(MiniSIOP.Y_PREDICTED_SALES, demand.get(MiniSIOP.Y_PREDICTED_SALES));
        result.put(MiniSIOP.Y_ACTUAL_SALES_BEFORE_SF, demand.get(MiniSIOP.Y_ACTUAL_SALES_BEFORE_SF));

        finalResult.put(MiniSIOP.X_ORDERED_DEMAND, result);
    }

    // 计算SOH Order
    private void calcSOHOrder(Map<String, Object> parameterMap, Map<String, Map<String, Object>> finalResult) {
        Map<String, Object> result = new HashMap<>();

        // 计算数据
        List<Map<String, Object>> data = miniSIOPDao.querySummary_SOHOrder(parameterMap);

        for (Map<String, Object> map : data) {
            String resultType = (String) map.get("TYPE");
            BigDecimal value = Utils.parseBigDecimal(map.get("VALUE"), BigDecimal.ZERO);
            switch (resultType) {
                case "OG_UD_LT_7_CD" -> result.put(MiniSIOP.Y_UD_LT7CD, value);
                case "OG_UD_LT_30_CD" -> result.put(MiniSIOP.Y_UD_GT7CD, value);
                case "OG_UD_GT_30_CD" -> result.put(MiniSIOP.Y_UD_GT30CD, value);
                case "IG_UD" -> result.put(MiniSIOP.Y_IG_UD, value);
                case "FOR_ORDER_IN_SELECTED_PERIOD" -> result.put(MiniSIOP.Y_FOR_ORDER_IN_SP, value);
                case "FOR_IG_ORDER_IN_SELECTED_PERIOD" -> result.put(MiniSIOP.Y_FOR_IG_ORDER_IN_SP, value);
                case "FOR_SAFETY_STOCK" -> result.put(MiniSIOP.Y_FOR_SAFETY_STOCK, value);
                case "FOR_FUTURE_ORDER" -> result.put(MiniSIOP.Y_FOR_FUTURE_ORDER, value);
                case "FOR_FUTURE_DEMAND" -> result.put(MiniSIOP.Y_FOR_FUTURE_DEMAND, value);
                case "STOCK_AGING_GT_1Y" -> result.put(MiniSIOP.Y_STOCK_AGING_GT1Y, value);
                case "STOCK_AGING_GT_2Y" -> result.put(MiniSIOP.Y_STOCK_AGING_GT2Y, value);
            }
        }

        finalResult.put(MiniSIOP.X_SOH_ORDER, result);
    }

    // 计算GIT Order
    private void calcGITOrder(Map<String, Object> parameterMap, Map<String, Map<String, Object>> finalResult) {
        Map<String, Object> result = new HashMap<>();

        // 计算数据
        List<Map<String, Object>> data = miniSIOPDao.querySummary_GITOrder(parameterMap);

        for (Map<String, Object> map : data) {
            String type = (String) map.get("TYPE");
            BigDecimal value = Utils.parseBigDecimal(map.get("VALUE"), BigDecimal.ZERO);
            switch (type) {
                case "LA_FOR_ORDER_IN_SELECTED_PERIOD":
                    result.put(MiniSIOP.Y_FOR_ORDER_IN_SP, value);
                    break;
                case "LA_FOR_IG_ORDER":
                    result.put(MiniSIOP.Y_FOR_IG_ORDER_IN_SP, value);
                    break;
                case "LA_FOR_SS":
                    result.put(MiniSIOP.Y_FOR_SAFETY_STOCK, value);
                    break;
                case "LA_FOR_FURTHER_ORDER":
                    result.put(MiniSIOP.Y_FOR_FUTURE_ORDER, value);
                    break;
                case "LA_FOR_FURTHER_DEMAND":
                    result.put(MiniSIOP.Y_FOR_FUTURE_DEMAND, value);
                    break;
            }
        }

        finalResult.put(MiniSIOP.X_GIT_ORDER, result);
    }

    // 计算Confirm Resource Order
    private void calcConfirmResourceOrder(Map<String, Object> parameterMap, Map<String, Map<String, Object>> finalResult) {
        Map<String, Object> result = new HashMap<>();

        // 计算数据
        List<Map<String, Object>> data = miniSIOPDao.querySummary_ConfResOrder(parameterMap);

        for (Map<String, Object> map : data) {
            String type = (String) map.get("TYPE");
            BigDecimal value = Utils.parseBigDecimal(map.get("VALUE"), BigDecimal.ZERO);
            switch (type) {
                case "LA_FOR_ORDER_IN_SELECTED_PERIOD":
                    result.put(MiniSIOP.Y_FOR_ORDER_IN_SP, value);
                    break;
                case "LA_FOR_IG_ORDER":
                    result.put(MiniSIOP.Y_FOR_IG_ORDER_IN_SP, value);
                    break;
                case "LA_FOR_SS":
                    result.put(MiniSIOP.Y_FOR_SAFETY_STOCK, value);
                    break;
                case "LA_FOR_FURTHER_ORDER":
                    result.put(MiniSIOP.Y_FOR_FUTURE_ORDER, value);
                    break;
                case "LA_FOR_FURTHER_DEMAND":
                    result.put(MiniSIOP.Y_FOR_FUTURE_DEMAND, value);
                    break;
            }
        }

        finalResult.put(MiniSIOP.X_CONF_RES_ORDER, result);
    }

    private void generateFilter(Map<String, Object> parameterMap) {
        // 根据type计算对应列
        String type = (String) parameterMap.get("type");
        String fcstColumn = "";
        String salesColumn = "";
        String UDSubfix = "";
        String backOrderSubfix = "";
        String crdColumn = "";
        String priceColumn = "";

        if ("Net Net Price".equals(type)) {
            fcstColumn = " * AVG_SELLING_PRICE_RMB";
            salesColumn = "NET_NET_VALUE";
            UDSubfix = "_VALUE";
            backOrderSubfix = "_VALUE";
            crdColumn = "OPEN_SO_VALUE";
            priceColumn = "AVG_SELLING_PRICE_RMB";
        } else if ("Moving Average Price".equals(type)) {
            fcstColumn = " * UNIT_COST";
            salesColumn = "MOVING_AVERAGE_P";
            UDSubfix = "_MVP";
            backOrderSubfix = "_MVP";
            crdColumn = "OPEN_SO_MVP";
            priceColumn = "UNIT_COST";
        } else if ("Quantity".equals(type)) {
            fcstColumn = "";
            salesColumn = "ORDER_QUANTITY";
            UDSubfix = "";
            backOrderSubfix = "_QTY";
            crdColumn = "OPEN_SO_QTY";
            priceColumn = "1";
        }

        parameterMap.put("crdColumn", crdColumn);
        parameterMap.put("UDSubfix", UDSubfix);
        parameterMap.put("backOrderSubfix", backOrderSubfix);
        parameterMap.put("fcstColumn", fcstColumn);
        parameterMap.put("salesColumn", salesColumn);
        parameterMap.put("priceColumn", priceColumn);

        // 根据FCST Version和选择的Period, 计算应该取FCST哪些列
        JSONArray dateRange = (JSONArray) parameterMap.get("dateRange");
        int fcstStart = Utils.parseInt(parameterMap.get("fcstVersion")); // fcst开始月
        int fcstEnd = Utils.parseInt(DateCalUtil.addMonth((String) parameterMap.get("fcstVersion"), 24)); // fcst结束月
        int startDate = Utils.parseInt(dateRange.get(0)); // 用户选择开始月
        int endDate = Utils.parseInt(dateRange.get(1)); // 用户选择结束月

        // 1. 当用户选择的开始或结束日期在FCST区间内时, 可以取到有效的月份范围
        // 2. 有效的时间区间为[Math.max(fcstStart, startDate), Math.min(fcstEnd, endDate)]
        // 3. 从fcstversion开始, 往后递推, 遇到start开始, 遇到end结束
        List<String> monthList = new ArrayList<>();
        if ((startDate >= fcstStart && startDate <= fcstEnd) || (endDate >= fcstStart && endDate <= fcstEnd)) {
            int start = Math.max(fcstStart, startDate);
            int end = Math.min(fcstEnd, endDate);

            int startTemp = fcstStart;

            // fcst最多也就有25个月
            for (int i = 1; i <= 25; i++) {
                if (startTemp >= start && startTemp <= end) {
                    monthList.add("MONTH" + (i < 10 ? "0" + i : String.valueOf(i)));
                }

                startTemp = Utils.parseInt(DateCalUtil.addMonth(String.valueOf(startTemp), 1));
                // 超过结束边界, 直接终止循环
                if (startTemp > end) {
                    break;
                }
            }
        }

        parameterMap.put("fcst_month_columns", monthList.isEmpty() ? "0" : StringUtils.join(monthList, " + "));

        // 生成筛选条件
        Object category = parameterMap.get("category");
        if (category instanceof JSONArray) {
            JSONArray categoryArray = (JSONArray) parameterMap.get("category");

            Map<String, List<String>> filterMap = new HashMap<>();

            for (Object subObj : categoryArray) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = subArray.getString(0);

                String value = subArray.getString(1);
                String key = Utils.randomStr(8);

                if (Utils.hasInjectionAttack(columnName)) {
                    continue;
                }

                List<String> fl = filterMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                fl.add("#{" + key + ",jdbcType=VARCHAR}");
                parameterMap.put(key, value);
            }

            List<String> filterList = new ArrayList<>();

            for (String key : filterMap.keySet()) {
                List<String> fl = filterMap.get(key);
                filterList.add("t." + key + " in (" + StringUtils.join(fl, ",") + ")");
            }

            parameterMap.put("filters", StringUtils.join(filterList, " and "));
        }

        // load special parameter
        String specialContent = (String) parameterMap.get("specialContent");
        String specialColumn = (String) parameterMap.get("specialType");
        if (Utils.hasInjectionAttack(specialColumn) == false) {
            if (StringUtils.isNotBlank(specialContent)) {
                parameterMap.put("specialList", Utils.splitValue(specialContent));
                parameterMap.put("specialColumn", specialColumn);
            }
        }

        // 转换scope
        JSONArray scope = (JSONArray) parameterMap.get("scope");
        if (scope != null) {
            List<String> scopeList = new ArrayList<>();
            if (scope.contains("SECI")) {
                scopeList.add("SECI_IG");
                scopeList.add("SECI_OG");
            }
            if (scope.contains("SEHK")) {
                scopeList.add("SEHK_IG");
                scopeList.add("SEHK_OG");
            }
            parameterMap.put("scopeList", scopeList);
        }
    }


    // 为了周日和周四的MINISIOP会议使用
    @Override
    public void downloadReport1ForMiniSIOPMeeting(Map<String, Object> parameterMap, HttpServletResponse response) {
        List<String> productLineList = miniSIOPDao.queryProductLineForMiniSIOPMeeting();
        List<Map<String, String>> localProductLineList = miniSIOPDao.queryLocalProductLineForMiniSIOPMeeting();

        this.generateFilter(parameterMap);

        // 先计算PRODUCT_LINE
        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        Map<String, Map<String, Object>> finalResult = new HashMap<>();

        parameterMap.put("_type", "PRODUCT_LINE");
        this.calcDemandForMiniSIOPMeeting(parameterMap, finalResult);
        this.calcSTDOrderIntakeForMiniSIOPMeeting(parameterMap, finalResult);
        this.calcSOHDemandForMiniSIOPMeeting(parameterMap, finalResult);
        this.calcGITDemandForMiniSIOPMeeting(parameterMap, finalResult);
        this.calcOrderedDemandForMiniSIOPMeeting(parameterMap, finalResult);
        this.calcConfirmResourceDemandForMiniSIOPMeeting(parameterMap, finalResult);
        for (String productLine : productLineList) {
            resultList.add(this.generatedeport1ForMiniSIOPMeeting("PRODUCT_LINE", finalResult, productLine));
        }
        SheetInfoWithData sheet1 = new SheetInfoWithData();
        sheet1.setSheetName("Sheet1");
        sheet1.setDataList(resultList);

        // 再计算LOCAL_PRODUCT_FAMILY
        resultList = new ArrayList<>();
        finalResult = new HashMap<>();

        parameterMap.put("_type", "LOCAL_PRODUCT_FAMILY");
        this.calcDemandForMiniSIOPMeeting(parameterMap, finalResult);
        this.calcSTDOrderIntakeForMiniSIOPMeeting(parameterMap, finalResult);
        this.calcSOHDemandForMiniSIOPMeeting(parameterMap, finalResult);
        this.calcGITDemandForMiniSIOPMeeting(parameterMap, finalResult);
        this.calcOrderedDemandForMiniSIOPMeeting(parameterMap, finalResult);
        this.calcConfirmResourceDemandForMiniSIOPMeeting(parameterMap, finalResult);

        for (Map<String, String> map : localProductLineList) {
            resultList.add(this.generatedeport1ForMiniSIOPMeeting("LOCAL_PRODUCT_FAMILY", finalResult, map.get("LOCAL_PRODUCT_LINE"), map.get("LOCAL_PRODUCT_FAMILY")));
        }

        SheetInfoWithData sheet2 = new SheetInfoWithData();
        sheet2.setSheetName("By Local Product Family");
        sheet2.setDataList(resultList);

        String fileName = "MiniSIOP_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, sheet1, sheet2);
    }

    private LinkedHashMap<String, Object> generatedeport1ForMiniSIOPMeeting(String type, Map<String, Map<String, Object>> finalResult, String... params) {
        LinkedHashMap<String, Object> data = new LinkedHashMap<>();
        Map<String, Object> demand = finalResult.get(MiniSIOP.X_DEMAND);
        Map<String, Object> orderIntake = finalResult.get(MiniSIOP.X_STD_OI);
        Map<String, Object> sohDemand = finalResult.get(MiniSIOP.X_SOH_DEMAND);
        Map<String, Object> gitDemand = finalResult.get(MiniSIOP.X_GIT_DEMAND);
        Map<String, Object> orderedDemand = finalResult.get(MiniSIOP.X_ORDERED_DEMAND);
        Map<String, Object> confirmDemand = finalResult.get(MiniSIOP.X_CONF_RES_DEMAND);


        String key = "";
        if ("PRODUCT_LINE".equals(type)) {
            key = params[0];
            data.put("Product Line", params[0]);
        } else if ("LOCAL_PRODUCT_FAMILY".equals(type)) {
            key = params[1];
            data.put("Local Product Line", params[0]);
            data.put("Local Product Family", params[1]);
        }

        data.put("Back Order", orderedDemand.get(MiniSIOP.Y_BACK_ORDER + "@" + key));
        data.put("Open Order", orderedDemand.get(MiniSIOP.Y_CRD_IN_SP + "@" + key));
        data.put("Open Order in Future", orderedDemand.get(MiniSIOP.Y_CRD_OUT_SP + "@" + key));
        data.put("Order Intake IG", orderIntake.get(MiniSIOP.Y_IG_OI + "@" + key));

        BigDecimal v1 = Utils.parseBigDecimal(demand.getOrDefault(MiniSIOP.Y_ACTUAL_SALES_BEFORE_SF + "@" + key, BigDecimal.ZERO));
        BigDecimal v2 = Utils.parseBigDecimal(demand.getOrDefault(MiniSIOP.Y_PREDICTED_SALES + "@" + key, BigDecimal.ZERO));
        BigDecimal v5 = Utils.parseBigDecimal(demand.getOrDefault(MiniSIOP.Y_SALES_EX_FCST_LT30 + "@" + key, BigDecimal.ZERO));
        BigDecimal v6 = Utils.parseBigDecimal(demand.getOrDefault(MiniSIOP.Y_SALES_EX_FCST_GT30 + "@" + key, BigDecimal.ZERO));
        BigDecimal v7 = Utils.parseBigDecimal(demand.getOrDefault(MiniSIOP.Y_SALES_EX_FCST_GT70 + "@" + key, BigDecimal.ZERO));
        BigDecimal v8 = Utils.parseBigDecimal(demand.getOrDefault(MiniSIOP.Y_SALES_EX_FCST_GT100 + "@" + key, BigDecimal.ZERO));

        data.put("Actual Sales", v1.add(v2).add(v5).add(v6).add(v7).add(v8));
        data.put("UD <=7 CD", sohDemand.get(MiniSIOP.Y_UD_LT7CD + "@" + key));
        data.put("UD >7 CD & <=30 CD", sohDemand.get(MiniSIOP.Y_UD_GT7CD + "@" + key));
        data.put("UD >30 CD", sohDemand.get(MiniSIOP.Y_UD_GT30CD + "@" + key));
        data.put("SOH For FCST", sohDemand.get(MiniSIOP.Y_FOR_DEMAND_IN_SP + "@" + key));
        data.put("GIT For FCST", gitDemand.get(MiniSIOP.Y_FOR_DEMAND_IN_SP + "@" + key));
        data.put("SOH For SS", sohDemand.get(MiniSIOP.Y_FOR_SAFETY_STOCK + "@" + key));
        data.put("SOH For Future Demand", sohDemand.get(MiniSIOP.Y_FOR_FUTURE_DEMAND + "@" + key));

        v1 = Utils.parseBigDecimal(sohDemand.getOrDefault(MiniSIOP.Y_STOCK_AGING_GT1Y + "@" + key, BigDecimal.ZERO));
        v2 = Utils.parseBigDecimal(sohDemand.getOrDefault(MiniSIOP.Y_STOCK_AGING_GT2Y + "@" + key, BigDecimal.ZERO));

        data.put("Long Aging SOH > 1Y", v1.add(v2));
        data.put("Unrealized >70% FCST", demand.get(MiniSIOP.Y_UNREALIZED_GT70_FCST + "@" + key));
        data.put("Unrealized >30% FCST", demand.get(MiniSIOP.Y_UNREALIZED_GT30_FCST + "@" + key));
        data.put("Unrealized <=30% FCST", demand.get(MiniSIOP.Y_UNREALIZED_LT30_FCST + "@" + key));

        data.put("Conf.Resource in Selected Period OG", confirmDemand.get(MiniSIOP.Y_FOR_DEMAND_IN_SP + "@" + key));
        data.put("Conf.Resource in Selected Period IG", confirmDemand.get(MiniSIOP.Y_FOR_IG_ORDER_IN_SP + "@" + key));
        data.put("Conf.Resource after Selected Period SS", confirmDemand.get(MiniSIOP.Y_FOR_SAFETY_STOCK + "@" + key));
        data.put("Conf.Resource after Selected Period Future Demand", confirmDemand.get(MiniSIOP.Y_FOR_FUTURE_DEMAND + "@" + key));

        return data;
    }

    private void calcDemandForMiniSIOPMeeting(Map<String, Object> parameterMap, Map<String, Map<String, Object>> finalResult) {
        Map<String, Object> result = new HashMap<>();

        // 计算数据
        String type = (String) parameterMap.get("_type");
        List<Map<String, Object>> data = miniSIOPDao.querySummary_Demand_ForMiniSIOPMeeting(parameterMap);
        Map<String, BigDecimal> predictedSalesMap = new HashMap<>();

        for (Map<String, Object> map : data) {
            String typeName = (String) map.get(type);
            BigDecimal predictedSales = predictedSalesMap.getOrDefault(MiniSIOP.Y_PREDICTED_SALES + "@" + typeName, BigDecimal.ZERO);
            predictedSales = predictedSales.add(Utils.parseBigDecimal(map.get("PREDICTED_VALUE"), BigDecimal.ZERO));
            predictedSalesMap.put(MiniSIOP.Y_PREDICTED_SALES + "@" + typeName, predictedSales);

            String key = (String) map.get("RATIO_LEVEL");
            switch (key) {
                case "GT200":
                    result.put(MiniSIOP.Y_SALES_EX_FCST_GT100 + "@" + typeName, Utils.parseBigDecimal(map.get("VALUE"), null));
                    break;
                case "GT170":
                    result.put(MiniSIOP.Y_SALES_EX_FCST_GT70 + "@" + typeName, Utils.parseBigDecimal(map.get("VALUE"), null));
                    break;
                case "GT130":
                    result.put(MiniSIOP.Y_SALES_EX_FCST_GT30 + "@" + typeName, Utils.parseBigDecimal(map.get("VALUE"), null));
                    break;
                case "GT100":
                    result.put(MiniSIOP.Y_SALES_EX_FCST_LT30 + "@" + typeName, Utils.parseBigDecimal(map.get("VALUE"), null));
                    break;
                case "GT070":
                    result.put(MiniSIOP.Y_UNREALIZED_GT70_FCST + "@" + typeName, Utils.parseBigDecimal(map.get("VALUE"), null));
                    break;
                case "GT030":
                    result.put(MiniSIOP.Y_UNREALIZED_GT30_FCST + "@" + typeName, Utils.parseBigDecimal(map.get("VALUE"), null));
                    break;
                case "LT030":
                    result.put(MiniSIOP.Y_UNREALIZED_LT30_FCST + "@" + typeName, Utils.parseBigDecimal(map.get("VALUE"), null));
                    break;
            }
        }
        result.putAll(predictedSalesMap);

        data = miniSIOPDao.querySummary_Demand_ActualSalesBeforeSF_ForMiniSIOPMeeting(parameterMap);
        for (Map<String, Object> map : data) {
            result.put(MiniSIOP.Y_ACTUAL_SALES_BEFORE_SF + "@" + map.get(type), map.get("VALUE"));
        }
        finalResult.put(MiniSIOP.X_DEMAND, result);
    }

    private void calcSTDOrderIntakeForMiniSIOPMeeting(Map<String, Object> parameterMap, Map<String, Map<String, Object>> finalResult) {
        Map<String, Object> result = new HashMap<>();

        String type = (String) parameterMap.get("_type");
        List<Map<String, Object>> data = miniSIOPDao.querySummary_STDOI_IGOI_ForMiniSIOPMeeting(parameterMap);
        for (Map<String, Object> map : data) {
            result.put(MiniSIOP.Y_IG_OI + "@" + map.get(type), map.get("VALUE"));
        }
        finalResult.put(MiniSIOP.X_STD_OI, result);
    }

    private void calcSOHDemandForMiniSIOPMeeting(Map<String, Object> parameterMap, Map<String, Map<String, Object>> finalResult) {
        Map<String, Object> result = new HashMap<>();

        // 计算数据
        List<Map<String, Object>> data = miniSIOPDao.querySummary_SOHDemand_ForMiniSIOPMeeting(parameterMap);

        for (Map<String, Object> map : data) {
            String resultType = (String) map.get("TYPE");
            String type = (String) parameterMap.get("_type");
            String typeName = (String) map.get(type);
            BigDecimal value = Utils.parseBigDecimal(map.get("VALUE"), BigDecimal.ZERO);
            switch (resultType) {
                case "OG_UD_LT_7_CD":
                    result.put(MiniSIOP.Y_UD_LT7CD + "@" + typeName, value);
                    break;
                case "OG_UD_LT_30_CD":
                    result.put(MiniSIOP.Y_UD_GT7CD + "@" + typeName, value);
                    break;
                case "OG_UD_GT_30_CD":
                    result.put(MiniSIOP.Y_UD_GT30CD + "@" + typeName, value);
                    break;
                case "IG_UD":
                    result.put(MiniSIOP.Y_IG_UD + "@" + typeName, value);
                    break;
                case "FOR_DEMAND_IN_SELECTED_PERIOD":
                    result.put(MiniSIOP.Y_FOR_DEMAND_IN_SP + "@" + typeName, value);
                    break;
                case "FOR_IG_ORDER_IN_SELECTED_PERIOD":
                    result.put(MiniSIOP.Y_FOR_IG_ORDER_IN_SP + "@" + typeName, value);
                    break;
                case "FOR_SAFETY_STOCK":
                    result.put(MiniSIOP.Y_FOR_SAFETY_STOCK + "@" + typeName, value);
                    break;
                case "FOR_FUTURE_DEMAND":
                    result.put(MiniSIOP.Y_FOR_FUTURE_DEMAND + "@" + typeName, value);
                    break;
                case "STOCK_AGING_GT_1Y":
                    result.put(MiniSIOP.Y_STOCK_AGING_GT1Y + "@" + typeName, value);
                    break;
                case "STOCK_AGING_GT_2Y":
                    result.put(MiniSIOP.Y_STOCK_AGING_GT2Y + "@" + typeName, value);
                    break;
            }
        }

        finalResult.put(MiniSIOP.X_SOH_DEMAND, result);
    }

    private void calcGITDemandForMiniSIOPMeeting(Map<String, Object> parameterMap, Map<String, Map<String, Object>> finalResult) {
        Map<String, Object> result = new HashMap<>();

        // 计算数据
        List<Map<String, Object>> data = miniSIOPDao.querySummary_GITDemand_ForMiniSIOPMeeting(parameterMap);

        for (Map<String, Object> map : data) {
            String type = (String) parameterMap.get("_type");
            String typeName = (String) map.get(type);
            result.put(MiniSIOP.Y_FOR_DEMAND_IN_SP + "@" + typeName, Utils.parseBigDecimal(map.get("VALUE"), BigDecimal.ZERO));
        }

        finalResult.put(MiniSIOP.X_GIT_DEMAND, result);
    }

    private void calcOrderedDemandForMiniSIOPMeeting(Map<String, Object> parameterMap, Map<String, Map<String, Object>> finalResult) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> data = miniSIOPDao.querySummary_OrderedDemand_ForMiniSIOPMeeting(parameterMap);
        for (Map<String, Object> map : data) {
            String type = (String) parameterMap.get("_type");
            String typeName = (String) map.get(type);

            BigDecimal backorder = Utils.parseBigDecimal(map.get("BACK_ORDER_VALUE"), BigDecimal.ZERO);
            BigDecimal crdInSP = Utils.parseBigDecimal(map.get("CRDINSP_VALUE"), BigDecimal.ZERO);
            crdInSP = crdInSP.subtract(backorder);

            result.put(MiniSIOP.Y_BACK_ORDER + "@" + typeName, backorder);
            result.put(MiniSIOP.Y_CRD_IN_SP + "@" + typeName, crdInSP);
            result.put(MiniSIOP.Y_CRD_OUT_SP + "@" + typeName, Utils.parseBigDecimal(map.get("CRDOUTSP_VALUE"), BigDecimal.ZERO));
        }

        finalResult.put(MiniSIOP.X_ORDERED_DEMAND, result);
    }

    private void calcConfirmResourceDemandForMiniSIOPMeeting(Map<String, Object> parameterMap, Map<String, Map<String, Object>> finalResult) {
        Map<String, Object> result = new HashMap<>();

        // 计算数据
        List<Map<String, Object>> data = miniSIOPDao.querySummary_ConfResDemand_ForMiniSIOPMeeting(parameterMap);

        for (Map<String, Object> map : data) {
            String resultType = (String) map.get("TYPE");
            String type = (String) parameterMap.get("_type");
            String typeName = (String) map.get(type);

            BigDecimal value = Utils.parseBigDecimal(map.get("VALUE"), BigDecimal.ZERO);
            switch (resultType) {
                case "LA_FOR_DEMAND_IN_SP":
                    result.put(MiniSIOP.Y_FOR_DEMAND_IN_SP + "@" + typeName, value);
                    break;
                case "LA_FOR_IG_ORDER":
                    result.put(MiniSIOP.Y_FOR_IG_ORDER_IN_SP + "@" + typeName, value);
                    break;
                case "LA_FOR_SS":
                    result.put(MiniSIOP.Y_FOR_SAFETY_STOCK + "@" + typeName, value);
                    break;
                case "LA_FOR_FURTHER_DEMAND":
                    result.put(MiniSIOP.Y_FOR_FUTURE_DEMAND + "@" + typeName, value);
                    break;
            }
        }

        finalResult.put(MiniSIOP.X_CONF_RES_DEMAND, result);
    }
}
