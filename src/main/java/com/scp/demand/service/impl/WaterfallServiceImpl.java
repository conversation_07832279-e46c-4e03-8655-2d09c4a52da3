package com.scp.demand.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.scp.demand.dao.IWaterfallDao;
import com.scp.demand.service.IWaterfallService;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.DateCalUtil;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

@Service("waterfallServiceImpl")
@Scope("prototype")
@Transactional
public class WaterfallServiceImpl extends ServiceHelper implements IWaterfallService {

    @Resource
    private Response response;

    @Resource
    private IWaterfallDao waterfallDao;

    @Resource
    private ExcelTemplate excelTemplate;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> resultList = new ArrayList<>();
        JSONArray array = (JSONArray) parameterMap.get("dateRange");
        String start = StringUtils.remove(array.getString(0), "/");
        String end = StringUtils.remove(array.getString(1), "/");
        parameterMap.put("startMonth", start);
        parameterMap.put("endMonth", end);

        String type = (String) parameterMap.get("type");

        List<String> column = new ArrayList<>();

        // 生成查询fcst的查询列
        for (int i = 1; i <= 25; i++) {
            String key = i < 10 ? "0" + i : "" + i;
            switch (type) {
                case "Net Net Price" -> column.add("sum(t.MONTH" + key + " * t.AVG_SELLING_PRICE_RMB) MONTH" + key);
                case "Net Net Price HKD" -> column.add("sum(t.MONTH" + key + " * t.AVG_SELLING_PRICE_HKD) MONTH" + key);
                case "Moving Average Price" -> column.add("sum(t.MONTH" + key + " * t.UNIT_COST) MONTH" + key);
                case "Quantity" -> column.add("sum(t.MONTH" + key + ") MONTH" + key);
                case "Line" -> column.add("count(1) MONTH" + key);
            }
        }

        parameterMap.put("column", column);

        this.generateFilter(parameterMap);

        // 查询fcst数据
        List<Map<String, Object>> fcstList = waterfallDao.queryReport(parameterMap);
        String actualMaxMonth = "0";
        Map<String, Map<String, Object>> fcstMap = new HashMap<>();
        for (Map<String, Object> map : fcstList) {
            String fcstVersion = (String) map.get("FCST_VERSION");
            if (fcstVersion.compareTo(actualMaxMonth) > 0) {
                actualMaxMonth = fcstVersion;
            }
            fcstMap.put(fcstVersion, map);
        }

        String startMonth = start;
        for (int l = 0; l < 48; l++) {
            Map<String, Object> map = fcstMap.get(startMonth);
            Map<String, Object> temp = new HashMap<>();
            if (map != null) {
                String fcstVersion = (String) map.get("FCST_VERSION");
                temp.put("FCST_VERSION", fcstVersion);

                int startGap = DateCalUtil.calcMonthGap(Utils.parseInt(fcstVersion), Utils.parseInt(start)) + 1;
                int endGap = DateCalUtil.calcMonthGap(Utils.parseInt(fcstVersion), Utils.parseInt(end)) + 1;

                int j = 0;
                for (int i = startGap; i <= Math.min(endGap, 25); i++) {
                    if (i < 0) {
                        j++;
                        continue;
                    }
                    String key = i < 10 ? "0" + i : "" + i;
                    temp.put("MONTH" + (j++), map.get("MONTH" + key));
                }
            } else {
                temp.put("FCST_VERSION", startMonth);

            }
            resultList.add(temp);
            startMonth = Utils.addMonth(startMonth, 1);
            if (startMonth.compareTo(actualMaxMonth) > 0) {
                break;
            }
        }

        String valueColumn = switch (type) {
            case "Net Net Price" -> "sum(t.order_quantity * t.avg_selling_price_rmb)";
            case "Net Net Price HKD" -> "sum(t.order_quantity * t.avg_selling_price_hkd)";
            case "Moving Average Price" -> "sum(t.order_quantity * t.unit_cost)";
            case "Quantity" -> "sum(t.order_quantity)";
            case "Line" -> "count(1)";
            default -> "";
        };

        // 查询Actual CRD
        parameterMap.put("valueColumn", valueColumn);
        Map<String, Object> actualCRD = new HashMap<>();
        List<Map<String, Object>> crdList = waterfallDao.queryReportCrd(parameterMap);
        actualCRD.put("FCST_VERSION", "Actual CRD");
        for (Map<String, Object> map : crdList) {
            int monthGap = DateCalUtil.calcMonthGap(Utils.parseInt(start), Utils.parseInt(map.get("CALENDAR_MONTH")));
            actualCRD.put("MONTH" + monthGap, Utils.parseDouble(map.get("VALUE")));
        }
        resultList.add(actualCRD);

        // Actual Order Intake
        Map<String, Object> actualOrderIntake = new HashMap<>();
        List<Map<String, Object>> orderIntakeList = waterfallDao.queryReportOrderIntake(parameterMap);
        actualOrderIntake.put("FCST_VERSION", "Actual Order Intake");
        for (Map<String, Object> map : orderIntakeList) {
            int monthGap = DateCalUtil.calcMonthGap(Utils.parseInt(start), Utils.parseInt(map.get("CALENDAR_MONTH")));
            actualOrderIntake.put("MONTH" + monthGap, Utils.parseDouble(map.get("VALUE")));
        }
        resultList.add(actualOrderIntake);

        // Actual Sales
        Map<String, Object> actualSales = new HashMap<>();
        List<Map<String, Object>> salesList = waterfallDao.queryReportSales(parameterMap);
        actualSales.put("FCST_VERSION", "Actual Sales");
        for (Map<String, Object> map : salesList) {
            int monthGap = DateCalUtil.calcMonthGap(Utils.parseInt(start), Utils.parseInt(map.get("CALENDAR_MONTH")));
            actualSales.put("MONTH" + monthGap, Utils.parseDouble(map.get("VALUE")));
        }
        resultList.add(actualSales);
        resultMap.put("waterfall", resultList);
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2(Map<String, Object> parameterMap) {
        JSONArray array = (JSONArray) parameterMap.get("dateRange");
        String start = StringUtils.remove(array.getString(0), "/");
        String end = StringUtils.remove(array.getString(1), "/");
        parameterMap.put("startMonth", start);
        parameterMap.put("endMonth", end);

        this.generateFilter(parameterMap);
        this.generateReport2ValueColumn(parameterMap);

        List<Map<String, Object>> list = waterfallDao.queryReport2(parameterMap);

        Map<String, Map<String, Object>> resultMap = new HashMap<>();

        for (Map<String, Object> map : list) {
            resultMap.put(Utils.convertMonth((String) map.get("CALENDAR_MONTH")), map);
        }

        List<String> monthList = new ArrayList<>();
        for (int i = 0; i < 48 && start.compareTo(end) <= 0; i++) {
            monthList.add(start);
            start = Utils.addMonth(start);
        }

        String type = (String) parameterMap.get("type");
        // 查询fcst数据
        List<String> column = new ArrayList<>();
        String valueColumn = "";

        // 生成查询fcst的查询列
        for (int i = 1; i <= 25; i++) {
            String key = i < 10 ? "0" + i : "" + i;
            switch (type) {
                case "Net Net Price" -> {
                    valueColumn = "sum(t.order_quantity * t.avg_selling_price_rmb)";
                    column.add("sum(t.MONTH" + key + " * t.AVG_SELLING_PRICE_RMB) MONTH" + key);
                }
                case "Net Net Price HKD" -> {
                    valueColumn = "sum(t.order_quantity * t.avg_selling_price_hkd)";
                    column.add("sum(t.MONTH" + key + " * t.AVG_SELLING_PRICE_HKD) MONTH" + key);
                }
                case "Moving Average Price" -> {
                    valueColumn = "sum(t.order_quantity * t.unit_cost)";
                    column.add("sum(t.MONTH" + key + " * t.UNIT_COST) MONTH" + key);
                }
                case "Quantity" -> {
                    valueColumn = "sum(t.order_quantity)";
                    column.add("sum(t.MONTH" + key + ") MONTH" + key);
                }
                case "Line" -> {
                    valueColumn = "count(1)";
                    column.add("count(1) MONTH" + key);
                }
            }
        }
        parameterMap.put("column", column);
        parameterMap.put("valueColumn", valueColumn);

        List<Map<String, Object>> fcstList = waterfallDao.queryReportFCSTNPIEOL(parameterMap);

        // convert fcst from list to map
        Map<String, Map<String, Object>> fcstNPIMap = new HashMap<>();
        Map<String, Map<String, Object>> fcstEOLMap = new HashMap<>();

        for (Map<String, Object> map : fcstList) {
            if ("NPI".equals(map.get("SUBT_FLAG"))) {
                fcstNPIMap.put((String) map.get("FCST_VERSION"), map);
            } else if ("EOL".equals(map.get("SUBT_FLAG"))) {
                fcstEOLMap.put((String) map.get("FCST_VERSION"), map);
            }
        }

        int index = Integer.parseInt(StringUtils.remove((String) parameterMap.get("report2FCSTType"), "M-"));

        // 将FCST的数据做成一个数据二维表
        Map<String, List<BigDecimal>> dataMatrixNPI = new LinkedHashMap<>();
        Map<String, List<BigDecimal>> dataMatrixEOL = new LinkedHashMap<>();
        String startMonth = StringUtils.remove(array.getString(0), "/");
        String endMonth = startMonth; // 最后一个月不能以页面输入为准, 而应该以数据源最后一条记录为准
        if (fcstList.isEmpty() == false) {
            endMonth = (String) fcstList.get(fcstList.size() - 1).get("FCST_VERSION");
        }

        // 最多只取48个月, 超过48个月暂不考虑
        for (int i = 0; i < 48; i++) {
            // 从查询条件的开始作为起点, 开始计算
            Map<String, Object> nmap = fcstNPIMap.get(startMonth);

            // 如果这个月没有NPI, 那么赋值25个null
            List<BigDecimal> temp = new ArrayList<>();
            if (nmap == null) {
                for (int j = 1; j <= 25; j++) {
                    temp.add(null);
                }
            } else {// 否则从map中获取1-25个月的数据
                for (int j = 1; j <= 25; j++) {
                    String key = j < 10 ? "0" + j : "" + j;
                    temp.add(Utils.parseBigDecimal(nmap.get("MONTH" + key)));
                }
            }
            dataMatrixNPI.put(startMonth, temp);

            // EOL
            Map<String, Object> emap = fcstEOLMap.get(startMonth);

            // 如果这个月没有EOL, 那么赋值25个null
            temp = new ArrayList<>();
            if (emap == null) {
                for (int j = 1; j <= 25; j++) {
                    temp.add(null);
                }
            } else {// 否则从map中获取1-25个月的数据
                for (int j = 1; j <= 25; j++) {
                    String key = j < 10 ? "0" + j : "" + j;
                    temp.add(Utils.parseBigDecimal(emap.get("MONTH" + key)));
                }
            }
            dataMatrixEOL.put(startMonth, temp);

            startMonth = Utils.addMonth(startMonth, 1);

            // 如果起始月大于结束月, 那么直接跳出循环
            if (startMonth.compareTo(endMonth) > 0) {
                break;
            }
        }

        Map<String, Object> fcstNPI = new HashMap<>();
        Map<String, Object> fcstEOL = new HashMap<>();

        // 取FCST前半段, 也就是可以正常获取M-n的FCST
        startMonth = StringUtils.remove(array.getString(0), "/");
        List<String> keys = new ArrayList<>(dataMatrixNPI.keySet());
        for (int i = 0; i < keys.size(); i++) {
            int j = Math.max(0, i - index);
            String key = keys.get(j);
            String monthName = Utils.convertMonth(Utils.addMonth(startMonth, i));
            if (dataMatrixNPI.get(key) != null && dataMatrixNPI.get(key).isEmpty() == false) {
                fcstNPI.put(monthName, dataMatrixNPI.get(key).get(i - j));
            }

            if (dataMatrixEOL.get(key) != null && dataMatrixEOL.get(key).isEmpty() == false) {
                fcstEOL.put(monthName, dataMatrixEOL.get(key).get(i - j));
            }
        }

        // 取末段
        if (keys.size() > 0) {
            String key = keys.get(Math.max(0, keys.size() - 1 - index)); // 取有效数据的最后一行, 如果是M-3, 有效数据的最后一行为倒数第三行
            List<BigDecimal> lastNPI = dataMatrixNPI.get(key);
            List<BigDecimal> lastEOL = dataMatrixEOL.get(key);

            startMonth = Utils.addMonth(startMonth, keys.size()); // 末段的开始时间

            for (int i = 1; i <= 48; i++) {
                String monthName = Utils.convertMonth(startMonth);
                fcstNPI.put(monthName, (lastNPI.size() > index + i) ? lastNPI.get(index + i) : null);
                fcstEOL.put(monthName, (lastEOL.size() > index + i) ? lastEOL.get(index + i) : null);

                startMonth = Utils.addMonth(startMonth, 1);
                if (startMonth.compareTo(end) > 0) { // 这个边界要以用户输入的为准, 如果显示边界超出用户输入的部分, 跳出循环
                    break;
                }
            }
        }

        // Order Intake NPI EOL
        Map<String, Object> orderNPI = new HashMap<>();
        Map<String, Object> orderEOL = new HashMap<>();
        List<Map<String, Object>> orderList = waterfallDao.queryReportOrderIntakeNPIEOL(parameterMap);
        for (Map<String, Object> map : orderList) {
            if ("NPI".equals(map.get("SUBT_FLAG"))) {
                orderNPI.put((String) map.get("CALENDAR_MONTH"), map.get("VALUE"));
            } else if ("EOL".equals(map.get("SUBT_FLAG"))) {
                orderEOL.put((String) map.get("CALENDAR_MONTH"), map.get("VALUE"));
            }
        }

        // Actual Sales NPI EOL
        Map<String, Object> salesNPI = new HashMap<>();
        Map<String, Object> salesEOL = new HashMap<>();
        List<Map<String, Object>> salesList = waterfallDao.queryReportSalesNPIEOL(parameterMap);
        for (Map<String, Object> map : salesList) {
            if ("NPI".equals(map.get("SUBT_FLAG"))) {
                salesNPI.put((String) map.get("CALENDAR_MONTH"), map.get("VALUE"));
            } else if ("EOL".equals(map.get("SUBT_FLAG"))) {
                salesEOL.put((String) map.get("CALENDAR_MONTH"), map.get("VALUE"));
            }
        }

        for (String month : monthList) {
            String monthName = Utils.convertMonth(month);
            Map<String, Object> map = resultMap.computeIfAbsent(monthName, k -> new HashMap<>());
            map.put("ORDER_NPI", orderNPI.get(month));
            map.put("ORDER_EOL", orderEOL.get(month));
            map.put("SALES_NPI", salesNPI.get(month));
            map.put("SALES_EOL", salesEOL.get(month));
            map.put("FCST_NPI", fcstNPI.get(monthName));
            map.put("FCST_EOL", fcstEOL.get(monthName));
        }
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateReport2ValueColumn(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(waterfallDao.queryReport2DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(waterfallDao.queryReport2Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport2Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        this.generateFilter(parameterMap);
        this.generateReport2ValueColumn(parameterMap);

        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "crd_details_data_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.demand.dao.IWaterfallDao.queryReport2Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateReport3ValueColumn(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        String sort = page.getSort();
        String sortColumn = "SALES_VALUE";
        if (sort.contains("\"") && sort.contains("RATIO") == false) {
            String[] sorts = sort.split(" ");
            if (sorts.length == 2) {
                page.setSort("NVL(" + sorts[0] + ", 0) desc");
            }

            sortColumn = StringUtils.trim(StringUtils.remove(sorts[0], "\""));
            parameterMap.put("sortColumn", sortColumn);
        } else {
            page.setSort("NVL(SALES_VALUE, 0) desc");
        }

        Map<String, Object> totalMap = waterfallDao.queryReport3Total(parameterMap);
        if (totalMap == null) {
            totalMap = new HashMap<>();
        }
        BigDecimal total;
        if ("CRD_VALUE".equals(sortColumn)) {
            total = Utils.parseBigDecimal(totalMap.get("CRD_TOTAL_VALUE"), BigDecimal.ZERO);
        } else if ("BACKLOG_VALUE".equals(sortColumn)) {
            total = Utils.parseBigDecimal(totalMap.get("BACKLOG_TOTAL_VALUE"), BigDecimal.ZERO);
        } else if ("UD_VALUE".equals(sortColumn)) {
            total = Utils.parseBigDecimal(totalMap.get("UD_TOTAL_VALUE"), BigDecimal.ZERO);
        } else if ("ORDERINTAKE_VALUE".equals(sortColumn)) {
            total = Utils.parseBigDecimal(totalMap.get("ORDERINTAKE_VALUE"), BigDecimal.ZERO);
        } else {
            total = Utils.parseBigDecimal(totalMap.get("SALES_TOTAL_VALUE"), BigDecimal.ZERO);
        }

        page.setStart(0);
        page.setMaxRows(50);
        List<LinkedHashMap<String, Object>> dataList = waterfallDao.queryReport3(parameterMap);

        for (LinkedHashMap<String, Object> map : dataList) {
            map.put("TOTAL", total);
            if (total.compareTo(BigDecimal.ZERO) == 0) {
                map.put("RATIO", 0);
            } else {
                BigDecimal ratioValue = Utils.parseBigDecimal(map.get("RATIO_VALUE"));
                map.put("RATIO", ratioValue.divide(total, 4, RoundingMode.HALF_UP));
            }
        }

        LinkedHashMap<String, Object> headerMap = new LinkedHashMap<>();
        headerMap.put("PROJECT_NAME", "Total");
        headerMap.put("SOLD_TO_SHORT_NAME", "Total");
        headerMap.put("SOLD_TO_FULL_NAME", "Total");
        headerMap.put("SOLD_TO_PARENT_NAME", "Total");
        headerMap.put("SOLD_TO_PARENT_CODE", "Total");
        headerMap.put("SHIP_TO_SHORT_NAME", "Total");
        headerMap.put("SHIP_TO_FULL_NAME", "Total");
        headerMap.put("SHIP_TO_PARENT_NAME", "Total");
        headerMap.put("SHIP_TO_PARENT_CODE", "Total");
        headerMap.put("ORDERINTAKE_VALUE", totalMap.get("ORDERINTAKE_VALUE"));
        headerMap.put("SALES_VALUE", totalMap.get("SALES_TOTAL_VALUE"));
        headerMap.put("CRD_VALUE", totalMap.get("CRD_TOTAL_VALUE"));
        headerMap.put("UD_VALUE", totalMap.get("UD_TOTAL_VALUE"));
        headerMap.put("BACKLOG_VALUE", totalMap.get("BACKLOG_TOTAL_VALUE"));
        headerMap.put("RATIO", 1);

        List<LinkedHashMap<String, Object>> resultList = new ArrayList<>();
        resultList.add(headerMap);
        resultList.addAll(dataList);

        page.setData(resultList);
        page.setTotal(resultList.size());
        return response.setBody(page);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryCascader() {
        Map<String, Object> result = new HashMap<>();
        result.put("CASCADER", Utils.parseCascader(waterfallDao.queryCascader()));
        result.put("fcstScopeOptions", waterfallDao.queryFcstScopeOptions());

        return response.setBody(result);
    }

    private void generateFilter(Map<String, Object> parameterMap) {
        this.generateCascaderFilterSQL(parameterMap);

        String materialList = (String) parameterMap.get("material");
        if (StringUtils.isNotBlank(materialList)) {
            parameterMap.put("materialList", Utils.splitValue(materialList));
        }
    }

    private void generateReport3ValueColumn(Map<String, Object> parameterMap) {
        JSONArray columns = (JSONArray) parameterMap.get("report3Column");

        if (columns == null || columns.isEmpty()) {
            columns = new JSONArray();
            columns.add("PROJECT_NAME");
        }

        parameterMap.put("report3Column", columns);

        JSONArray array = (JSONArray) parameterMap.get("dateRange2");
        String start = StringUtils.remove(array.getString(0), "/");
        String end = StringUtils.remove(array.getString(1), "/");
        parameterMap.put("startMonth", start);
        parameterMap.put("endMonth", end);

        String valueColumn = "";
        String udColumn = "";
        String type = (String) parameterMap.get("type");
        switch (type) {
            case "Net Net Price" -> {
                valueColumn = "T.ORDER_QUANTITY * T.avg_selling_price_rmb";
                udColumn = "T.OPEN_UD_QTY * T.AVG_SELLING_PRICE_RMB";
            }
            case "Net Net Price HKD" -> {
                valueColumn = "T.ORDER_QUANTITY * T.AVG_SELLING_PRICE_HKD";
                udColumn = "T.OPEN_UD_QTY * T.AVG_SELLING_PRICE_HKD";
            }
            case "Moving Average Price" -> {
                valueColumn = "T.ORDER_QUANTITY * T.UNIT_COST";
                udColumn = "T.OPEN_UD_QTY * T.AVG_SELLING_PRICE_RMB";
            }
            case "Quantity" -> {
                valueColumn = "T.ORDER_QUANTITY";
                udColumn = "T.OPEN_UD_QTY";
            }
            case "Line" -> {
                valueColumn = "DECODE(T.MATERIAL, NULL, 0, 1)";
                udColumn = "DECODE(T.MATERIAL, NULL, 0, 1)";
            }
        }
        parameterMap.put("valueColumn", valueColumn);
        parameterMap.put("udColumn", udColumn);
    }

    private void generateReport2ValueColumn(Map<String, Object> parameterMap) {
        String valueColumn = "";
        String type = (String) parameterMap.get("type");
        parameterMap.put("report2SelectedDate", Utils.convertMonthFromName2Code((String) parameterMap.get("report2SelectedDate")));
        switch (type) {
            case "Net Net Price" -> valueColumn = " * t.AVG_SELLING_PRICE_RMB";
            case "Net Net Price HKD" -> valueColumn = " * t.avg_selling_price_hkd";
            case "Moving Average Price" -> valueColumn = "* t.unit_cost";
            case "Quantity" -> valueColumn = "";
        }
        parameterMap.put("valueColumn", valueColumn);
    }
}
