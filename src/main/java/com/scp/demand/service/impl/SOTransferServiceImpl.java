package com.scp.demand.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.scp.demand.service.ISOTransferService;
import com.starter.utils.Utils;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.scp.demand.dao.ISOTransferDao;
import com.starter.utils.excel.ExcelTemplate;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;

import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service("SOTransferService")
@Scope("prototype")
@Transactional
public class SOTransferServiceImpl implements ISOTransferService {

    @Resource
    private Response response;

    @Resource
    private JdbcTemplate jdbcTemplate;

    @Resource
    private ExcelTemplate excelTemplate;

    @Resource
    private ISOTransferDao SOTransferDao;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage(Map<String, Object> parameterMap) {
        Map<String, Object> result = new HashMap<>();
        result.put("CASCADER", Utils.parseCascader(SOTransferDao.queryCascader()));
        return response.setBody(result);
    }

    @Override
    public Response queryReport1(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);

        page.setTotal(SOTransferDao.queryReport1Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(SOTransferDao.queryReport1(parameterMap));
        }

        return response.setBody(page);
    }

    @Override
    public Response queryReport1Logs(Map<String, Object> parameterMap) {
        return response.setBody(SOTransferDao.queryReport1Logs(parameterMap));
    }

    @Override
    public void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "so_transfer_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.demand.dao.ISOTransferDao.queryReport1", parameterMap);
    }

    @Override
    public Response queryReport2(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);

        page.setTotal(SOTransferDao.queryReport2Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(SOTransferDao.queryReport2(parameterMap));
        }

        return response.setBody(page);
    }

    @Override
    public Response queryReport3(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);

        page.setTotal(SOTransferDao.queryReport3Count(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(SOTransferDao.queryReport3(parameterMap));
        }

        return response.setBody(page);
    }

    @Override
    public void downloadReport3(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        List<LinkedHashMap<String, Object>> dataList = SOTransferDao.queryReport3(parameterMap);

        // 初始化响应头
        String filename = "Change Delivery Plant Request-" + new SimpleDateFormat("MMdd").format(new Date()) + "_" + Utils.randomStr(4) + ".xlsx";
        ExcelTemplate.initExcelResponseHeader(response, filename);

        SXSSFWorkbook book = null;
        try {
            InputStream is = ExcelTemplate.class.getClassLoader().getResourceAsStream("files/Change Delivery Plant Request Template.xlsx");
            assert is != null;
            book = new SXSSFWorkbook(new XSSFWorkbook(is), 128);
            SXSSFSheet sheet = book.getSheet("Change Record");

            String[] headers = new String[]{"BASIC_CHANGE_INFORMATION", "CHANGE_DATE", "PLANT", "MATERIAL", "REQUEST_QTY", "GROSS_WEIGHT_KG",
                    "SO_NUMBER", "SO_ITEM", "ITEM_CATEGORY", "SHIP_TO_CITY", "CUSTOMER_CODE", "CUSTOMER_NAME", "ORIGINAL_SHIPPING_DC",
                    "CHANGED_SHIPPING_DC", "VENDOR_NEXT_REPLENISH_QTY", "REPLENISH_ARRIVE_DC_DATE", "CUSTOMER_REQUEST_DATE", "ARRIVE_CUSTOMER_BY_ORGINAL_DC",
                    "ARRIVE_CUSTOMER_BY_CHANGE_DC", "ORIGINAL_DC_LT", "CHANGE_DC_LT", "ORIGINAL_DC_COST", "CHANGE_DC_COST", "COST_IMPACT"};

            CellStyle dateStyle = book.createCellStyle();
            DataFormat format = book.createDataFormat();
            dateStyle.setDataFormat(format.getFormat("yyyy/m/d"));
            dateStyle.setBorderBottom(BorderStyle.THIN);
            dateStyle.setBorderLeft(BorderStyle.THIN);
            dateStyle.setBorderTop(BorderStyle.THIN);
            dateStyle.setBorderRight(BorderStyle.THIN);

            CellStyle borderStyle = book.createCellStyle();
            borderStyle.setBorderBottom(BorderStyle.THIN);
            borderStyle.setBorderLeft(BorderStyle.THIN);
            borderStyle.setBorderTop(BorderStyle.THIN);
            borderStyle.setBorderRight(BorderStyle.THIN);

            for (int i = 0; i < dataList.size(); i++) {
                // 从第五行开始写, 前三行是固定表头
                int index = i + 4;
                Row rowBody = sheet.createRow(index);

                for (int j = 0; j < headers.length; j++) {
                    String header = headers[j];
                    Cell cellBody = rowBody.createCell(j);
                    Object v = dataList.get(i).get(header);
                    ExcelTemplate.setCellValue(cellBody, v);
                    if (v instanceof Date) {
                        cellBody.setCellStyle(dateStyle);
                    } else {
                        cellBody.setCellStyle(borderStyle);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        ExcelTemplate.writeBookToResponse(book, response);
    }

    @Override
    public void downloadReport2(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateFilter(parameterMap);

        String fileName = "so_transfer_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.demand.dao.ISOTransferDao.queryReport2", parameterMap);
    }

    @Override
    public Response queryReport2Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        List<String> report2SelectedType = (List<String>) parameterMap.get("report2SelectedType");
        if (ArrayUtils.isEquals(report2SelectedType, Arrays.asList("Total",""))) {
            parameterMap.put("report2Type", new ArrayList<>());
        }
        page.setTotal(SOTransferDao.queryReport2DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(SOTransferDao.queryReport2Details(parameterMap));
        }

        return response.setBody(page);
    }

    @Override
    public void downloadReport2Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateFilter(parameterMap);

        String fileName = "so_transfer_details_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(res, fileName, "com.scp.demand.dao.ISOTransferDao.queryReport2Details", parameterMap);
    }

    @Override
    @SuppressWarnings("unchecked")
    public Response calc(Map<String, Object> parameterMap, String userid) {
        this.generateCalcFilter(parameterMap);

        // 检查计算量, 判断是否超出最大要求
        int transferSOCnt = SOTransferDao.queryTransferSOCount(parameterMap);
        BigDecimal ssPercent = Utils.parseBigDecimal(parameterMap.get("ssPercent"));
        BigDecimal doOutSsPercent = Utils.parseBigDecimal(parameterMap.get("doOutSsPercent"));
        int maxLine = 65535;
        if (transferSOCnt > maxLine) {
            return response.setBody("计算超出允许最大值 " + maxLine + ", 实际参与运算SO行 " + transferSOCnt);
        }
        if (transferSOCnt == 0) {
            return response.setBody("未找到需要调拨的SO!");
        }

        SOTransferDao.deleteCalcHistory(parameterMap);

        List<String> soDCPriority = (List<String>) parameterMap.get("soDCPriority");
        List<String> sohDCPriority = (List<String>) parameterMap.get("sohDCPriority");
        StringBuilder dcOrderBy = new StringBuilder("DECODE(SFSV.PLANT_CODE, ");
        for (int i = 0; i < soDCPriority.size(); i++) {
            dcOrderBy.append("'").append(soDCPriority.get(i)).append("',").append(i).append(", ");
        }
        dcOrderBy.append("999), SFSV.PLANT_CODE");
        parameterMap.put("dcOrderBy", dcOrderBy);

        List<Map<String, Object>> transferSOList = SOTransferDao.queryTransferSO(parameterMap);
        List<Map<String, Object>> availableSOHList = SOTransferDao.queryAvailableSOH(parameterMap);

        // 将库存写入map, 方便检索
        Map<String, List<Map<String, Object>>> availableSOHMap = new HashMap<>();

        for (Map<String, Object> map : availableSOHList) {
            List<Map<String, Object>> list = availableSOHMap.computeIfAbsent((String) map.get("MATERIAL"), k -> new ArrayList<>());
            list.add(map);
        }

        // 遍历待transfer的SO
        List<Map<String, Object>> buffer = new ArrayList<>();
        Map<String, List<String>> transferedSO = new HashMap<>();
        int i = 0;
        for (Map<String, Object> so : transferSOList) {
            List<String> logs = new ArrayList<>();
            String material = (String) so.get("MATERIAL");
            String plantCode = (String) so.get("PLANT_CODE");
            String soNumber = (String) so.get("SALES_ORDER_NUMBER");
            String soItem = (String) so.get("SALES_ORDER_ITEM");
            String priority = (String) so.get("DELIVERY_PRIORITY");
            String crd = Utils.parseDate(so.get("CRD_DATE"));
            BigDecimal qty = Utils.parseBigDecimal(so.get("OPEN_QTY"));

            // 打印SO明细
            logs.add("**任务开始 " + (++i) + "/" + transferSOList.size() + "**");
            logs.add("");
            logs.add("SO DC计算优先级 " + StringUtils.join(soDCPriority, " $\\Rightarrow$ "));
            logs.add("");
            logs.add("计算了" + doOutSsPercent.toPlainString() + "%安全库存后, 该订单进入调拨池, 等待调拨");
            logs.add("");
            logs.add("开始处理SO " + soNumber + "/" + soItem);
            logs.add("|Material|Plant Code|Delivery Priority|CRD|Open Qty|");
            logs.add("|--|--|--|--|--|");
            logs.add("|" + material + "|" + plantCode + "|" + priority + "|" + crd + "|" + Utils.thousandBitSeparator(qty) + "|");
            logs.add("");

            List<Map<String, Object>> sohList = availableSOHMap.get(material);
            if (sohList == null || sohList.size() == 0) {
                logs.add("**未在任何DC找到可用库存, 停止调拨**");
                so.put("transferOrNot", "N"); // 未调拨
                so.put("transferLog", StringUtils.join(logs, "\r\n").replace(":", " $\\Rightarrow$"));
                so.put("transferSeq", i);
                buffer.add(so);
                continue;
            }

            // 处理库存
            logs.add("**找到" + sohList.size() + "个DC有可用库存**");
            logs.add("");
            logs.add("初始可用库存");
            logs.add("");
            List<String> fixedSOList = new ArrayList<>();
            int j = 1;
            for (Map<String, Object> soh : sohList) {
                logs.add((j++) + ". " + material + "@" + soh.get("PLANT_CODE") + ": " + Utils.thousandBitSeparator(soh.get("UU_STOCK")));
                String fixedOrder = (String) soh.get("FIXED_ORDER");
                if (StringUtils.isNotBlank(fixedOrder)) {
                    String[] orders = fixedOrder.split(";");
                    for (String o : orders) {
                        if (StringUtils.isNotBlank(o)) {
                            fixedSOList.add(o);
                        }
                    }
                }
            }

            // 处理已被fulfill扣除的库存, 仅做日志输出, 计算已经在数据库中完成
            logs.add("");
            if (fixedSOList.isEmpty() == false) {
                logs.add(material + "已被Fulfill SO锁定的库存");
                logs.add("");
                j = 1;
                for (String order : fixedSOList) {
                    logs.add((j++) + ". " + order);
                }
            } else {
                logs.add("未找到" + material + "对应的Fulfill SO, 不执行Fulfill扣除操作");
            }

            // 计算被前序SO扣除掉的库存, 也仅做日志输出, 因为扣除操作已经在前序做完了
            logs.add("");
            if (transferedSO.containsKey(material)) {
                logs.add(material + "部分库存已被前序SO锁定");
                logs.add("");
                j = 1;
                for (String ts : transferedSO.get(material)) {
                    logs.add((j++) + ". " + ts);
                }
            } else {
                logs.add("未找到" + material + "前序调拨记录, 不执行调拨扣除操作");
            }

            // TODO 已经调拨的订单, 重复调拨的时候提醒

            // 计算安全库存
            logs.add("");
            logs.add(material + "安全库存");
            logs.add("");
            j = 1;
            for (Map<String, Object> soh : sohList) {
                logs.add((j++) + ". " + material + "@" + soh.get("PLANT_CODE") + ": " + Utils.thousandBitSeparator(soh.get("SAFETY_STOCK")));
            }
            logs.add("");
            logs.add("扣除 **" + ssPercent.toPlainString() + "%** 安全库存和锁定库存后的可用库存");
            logs.add("");
            j = 1;
            for (Map<String, Object> soh : sohList) {
                // 可用库存
                BigDecimal aqty = Utils.parseBigDecimal(soh.get("AVAILABLE_QTY"));

                // 扣除SS之后的可用库存, 用于日志输出
                BigDecimal ss = Utils.parseBigDecimal(soh.get("SAFETY_STOCK"));
                BigDecimal aqty_ss = aqty.subtract(ss.multiply(ssPercent).divide(BigDecimal.valueOf(100), 0, RoundingMode.HALF_UP));
                logs.add((j++) + ". " + material + "@" + soh.get("PLANT_CODE") + ": " + Utils.thousandBitSeparator(aqty_ss));
            }

            // 检查本DC是否满足需求
            String transferOrNot = "N";
            String transferPlant = "";
            BigDecimal transferQty = null;

            boolean currentDC = false;
            for (Map<String, Object> soh : sohList) {
                if (StringUtils.equals(plantCode, (String) soh.get("PLANT_CODE"))) {
                    // 可用库存
                    BigDecimal availableQty = Utils.parseBigDecimal(soh.get("AVAILABLE_QTY"));

                    // 减去SS之后可用库存
                    BigDecimal ss = Utils.parseBigDecimal(soh.get("SAFETY_STOCK"));
                    BigDecimal availableQty_ss = availableQty.subtract(ss.multiply(ssPercent).divide(BigDecimal.valueOf(100), 0, RoundingMode.HALF_UP));

                    if (availableQty_ss.compareTo(qty) >= 0) {
                        // 计算库存是否充足的时候, 要减去SS, 可用库存进入下一个循环的时候, 只需要减去SO数量
                        BigDecimal leftSOH = availableQty.subtract(qty);
                        BigDecimal leftSOH_ss = availableQty_ss.subtract(qty);

                        logs.add("");
                        logs.add("**当前DC(" + plantCode + ")库存(" + Utils.thousandBitSeparator(availableQty_ss) + ")可以满足SO需求, 无需调拨, 发货数量" + Utils.thousandBitSeparator(qty) + ", 剩余库存" + Utils.thousandBitSeparator(leftSOH_ss) + "**");
                        soh.put("AVAILABLE_QTY", leftSOH);
                        transferOrNot = "C";
                        transferQty = qty;
                        transferPlant = (String) soh.get("PLANT_CODE");

                        logs.add("");
                        logs.add("**>> 可本DC发货!**");

                        List<String> transferedSOList = transferedSO.computeIfAbsent(material, k -> new ArrayList<>());
                        transferedSOList.add(soNumber + "/" + soItem + "@" + transferPlant + ": " + Utils.thousandBitSeparator(qty));

                        currentDC = true;
                        break;
                    }
                }
            }

            // 如果当前DC能满足需求, 直接保存数据库
            if (currentDC) {
                so.put("transferPlant", transferPlant);
                so.put("transferOrNot", transferOrNot);
                so.put("transferQty", transferQty);
                so.put("transferLog", StringUtils.join(logs, "\r\n").replace(":", " $\\Rightarrow$"));
                so.put("transferSeq", i);
                buffer.add(so);
                continue;
            }

            // 调整库存调拨优先级
            logs.add("");
            logs.add("DC库存消耗优先级 " + StringUtils.join(sohDCPriority, " $\\Rightarrow$ "));
            logs.add("");

            // 按配置DC顺序调拨
            sohList.sort((e0, e1) -> {
                int i0 = sohDCPriority.indexOf((String) e0.get("PLANT_CODE"));
                int i1 = sohDCPriority.indexOf((String) e1.get("PLANT_CODE"));
                if (i0 == -1) {
                    return -1;
                }
                if (i1 == -1) {
                    return 1;
                }
                return Integer.compare(i0, i1);
            });
            logs.add("**调拨顺序**");
            j = 1;
            for (Map<String, Object> soh : sohList) {
                logs.add((j++) + ". " + material + "@" + soh.get("PLANT_CODE"));
            }

            // 执行调拨
            logs.add("");
            logs.add("**开始调拨**");

            j = 1;
            for (Map<String, Object> soh : sohList) {
                // 总可用库存
                BigDecimal availableQty = Utils.parseBigDecimal(soh.get("AVAILABLE_QTY"));
                // 扣掉SS之后可用的安全库存
                BigDecimal ss = Utils.parseBigDecimal(soh.get("SAFETY_STOCK"));
                BigDecimal availableQty_ss = availableQty.subtract(ss.multiply(ssPercent).divide(BigDecimal.valueOf(100), 0, RoundingMode.HALF_UP));

                if (availableQty_ss.compareTo(qty) >= 0) {
                    BigDecimal leftSOH = availableQty.subtract(qty);
                    BigDecimal leftSOH_ss = availableQty_ss.subtract(qty);

                    logs.add(j + ". 尝试向" + soh.get("PLANT_CODE") + "调拨, 调拨数量" + Utils.thousandBitSeparator(qty) + " $\\Longrightarrow$ " + soh.get("PLANT_CODE") + "库存充足, 可以调拨, 调拨数量" + Utils.thousandBitSeparator(qty) + ", 剩余库存" + Utils.thousandBitSeparator(leftSOH_ss));
                    soh.put("AVAILABLE_QTY", leftSOH);
                    transferOrNot = "Y";
                    transferQty = qty;
                    transferPlant = (String) soh.get("PLANT_CODE");

                    List<String> transferedSOList = transferedSO.computeIfAbsent(material, k -> new ArrayList<>());
                    transferedSOList.add(soNumber + "/" + soItem + "@" + transferPlant + ": " + qty.toPlainString());
                    break;
                } else {
                    logs.add((j++) + ". 尝试向" + soh.get("PLANT_CODE") + "调拨, 调拨数量" + Utils.thousandBitSeparator(qty) + " $\\Longrightarrow$ " + soh.get("PLANT_CODE") + "库存不足(" + Utils.thousandBitSeparator(availableQty_ss) + "), 无法调拨, 尝试向其他DC调拨");
                }
            }
            logs.add("");
            if ("N".equals(transferOrNot)) {
                logs.add("**>> 尝试所有调拨方案, SO无法调拨**");
            } else {
                logs.add("**>> 调拨成功!**");
            }

            // 计算结果保存数据库
            so.put("transferPlant", transferPlant);
            so.put("transferOrNot", transferOrNot);
            so.put("transferQty", transferQty);
            so.put("transferLog", StringUtils.join(logs, "\r\n").replace(":", " $\\Rightarrow$"));
            so.put("transferSeq", i);
            buffer.add(so);
            if (buffer.size() > 100) {
                this.saveTransferLog(buffer, userid);
                buffer.clear();
            }
        }

        if (buffer.size() > 0) {
            this.saveTransferLog(buffer, userid);
            buffer.clear();
        }
        return response;
    }

    private void generateCalcFilter(Map<String, Object> parameterMap) {
        parameterMap.put("doOutSsPercent", Utils.parseBigDecimal(parameterMap.get("doOutSsPercent")));

        // 生成筛选条件
        JSONArray categoryArray = (JSONArray) parameterMap.get("filterList");
        if (categoryArray != null) {
            Map<String, List<String>> filterMap = new HashMap<>();

            for (Object subObj : categoryArray) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = subArray.getString(0);

                String value = subArray.getString(1);
                String key = Utils.randomStr(8);

                if (Utils.hasInjectionAttack(columnName)) {
                    continue;
                }

                List<String> fl = filterMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                fl.add("#{" + key + ",jdbcType=VARCHAR}");
                parameterMap.put(key, value);
            }

            List<String> filterList = new ArrayList<>();

            for (String key : filterMap.keySet()) {
                List<String> fl = filterMap.get(key);
                filterList.add("t." + key + " in (" + StringUtils.join(fl, ",") + ")");
            }

            parameterMap.put("filters", StringUtils.join(filterList, " and "));
        }

        // load special parameter
        String specialContent = (String) parameterMap.get("specialContent");
        if (StringUtils.isNotBlank(specialContent)) {
            parameterMap.put("specialList", Utils.splitValue(specialContent));
            parameterMap.put("specialColumn", this.getColumnNameByLabel(parameterMap.get("specialType")));
        }
    }

    @SuppressWarnings("unchecked")
    private void generateFilter(Map<String, Object> parameterMap) {
        List<String> report2Type = (List<String>) parameterMap.get("report2Type");

        if (report2Type == null || report2Type.isEmpty()) {
            report2Type = new ArrayList<>();
            report2Type.add("BU");
            report2Type.add("ENTITY");
        } else {
            for (String type : report2Type) {
                if (Utils.hasInjectionAttack(type)) {
                    report2Type = new ArrayList<>();
                    report2Type.add("BU");
                    report2Type.add("ENTITY");
                    break;
                }
            }
        }
        parameterMap.put("report2Type", report2Type);


        // 生成筛选条件
        JSONArray categoryArray = (JSONArray) parameterMap.get("filterList");
        if (categoryArray != null) {
            Map<String, List<String>> filterMap = new HashMap<>();

            for (Object subObj : categoryArray) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = subArray.getString(0);

                String value = subArray.getString(1);
                String key = Utils.randomStr(8);

                if (Utils.hasInjectionAttack(columnName)) {
                    continue;
                }

                List<String> fl = filterMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                fl.add("#{" + key + ",jdbcType=VARCHAR}");
                parameterMap.put(key, value);
            }

            List<String> filterList = new ArrayList<>();

            for (String key : filterMap.keySet()) {
                List<String> fl = filterMap.get(key);
                filterList.add("t." + key + " in (" + StringUtils.join(fl, ",") + ")");
            }

            parameterMap.put("filters", StringUtils.join(filterList, " and "));
        }

        // load special parameter
        String specialContent = (String) parameterMap.get("specialContent");
        if (StringUtils.isNotBlank(specialContent)) {
            parameterMap.put("specialList", Utils.splitValue(specialContent));
            parameterMap.put("specialColumn", this.getColumnNameByLabel(parameterMap.get("specialType")));
        }
    }

    private void saveTransferLog(List<Map<String, Object>> buffer, String userid) {
        try {
            jdbcTemplate.batchUpdate("INSERT INTO SCPA.SO_TRANSFER_RESULT(SALES_ORDER_NUMBER, SALES_ORDER_ITEM, MATERIAL, PLANT_CODE, CRD_DATE, " + "DELIVERY_PRIORITY, TRANSFER_OR_NOT, TRANSFER_PLANT, TRANSFER_LOG, OPR_USERID, OPR_DATE, TRANSFER_QTY, TRANSFER_SEQ, SO_TOTAL_QTY) " + "VALUES (?,?,?,?,?,?,?,?,?,?,SYSDATE,?,?,?)", new BatchPreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps, int i) throws SQLException {
                    Map<String, Object> p = buffer.get(i);
                    ps.setString(1, (String) p.get("SALES_ORDER_NUMBER"));
                    ps.setString(2, (String) p.get("SALES_ORDER_ITEM"));
                    ps.setString(3, (String) p.get("MATERIAL"));
                    ps.setString(4, (String) p.get("PLANT_CODE"));
                    ps.setTimestamp(5, (java.sql.Timestamp) p.get("CRD_DATE"));
                    ps.setString(6, (String) p.get("DELIVERY_PRIORITY"));
                    ps.setString(7, (String) p.get("transferOrNot"));
                    ps.setString(8, (String) p.get("transferPlant"));
                    ps.setString(9, (String) p.get("transferLog"));
                    ps.setString(10, userid);
                    ps.setBigDecimal(11, (BigDecimal) p.get("transferQty"));
                    ps.setInt(12, (Integer) p.get("transferSeq"));
                    ps.setBigDecimal(13, (BigDecimal) p.get("SO_TOTAL_QTY"));
                }

                @Override
                public int getBatchSize() {
                    return buffer.size();
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private String getColumnNameByLabel(Object labelObj) {
        String label = (String) labelObj;
        if (label == null) {
            return null;
        }
        if (Utils.hasInjectionAttack(label)) {
            return "";
        }
        return label;
    }
}
