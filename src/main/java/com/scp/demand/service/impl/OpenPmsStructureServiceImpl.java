package com.scp.demand.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.scp.demand.bean.OpenPmsReport2Bean;
import com.scp.demand.bean.OpenPmsReport2Treemap;
import com.scp.demand.bean.OpenPmsReport4Bean;
import com.scp.demand.dao.IOpenPmsStructureDao;
import com.scp.demand.service.IOpenPmsStructureService;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.servlet.ServiceHelper;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service("openPmsStructureServiceImpl")
@Scope("prototype")
@Transactional
public class OpenPmsStructureServiceImpl extends ServiceHelper implements IOpenPmsStructureService {

    @Resource
    private Response response;

    @Resource
    private IOpenPmsStructureDao openPmsStructureDao;

    public static final String PARENT_CODE = "menu1H0";

    @Resource
    private ExcelTemplate excelTemplate;

    private final HashMap<String, String> tooltip2ColumnMap = new HashMap<>();
    private final HashMap<String, String> column2TooltipMap = new HashMap<>();

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage() {
        Map<String, Object> result = new HashMap<>();
        result.put("CASCADER", Utils.parseCascader(openPmsStructureDao.queryCascader()));
        result.put("dateColumns", openPmsStructureDao.queryDateColumns());

        return response.setBody(result);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2(Map<String, Object> parameterMap) throws Exception {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateReport2Tooltips(parameterMap);

        // 将前台传过来的label转换成列名, 同时也可以防止恶意代码注入
        parameterMap.put("level1", this.getColumnNameByLabel(parameterMap.get("level1")));
        parameterMap.put("level2", this.getColumnNameByLabel(parameterMap.get("level2")));
        parameterMap.put("level3", this.getColumnNameByLabel(parameterMap.get("level3")));
        parameterMap.put("level4", this.getColumnNameByLabel(parameterMap.get("level4")));
        parameterMap.put("level5", this.getColumnNameByLabel(parameterMap.get("level5")));

        List<OpenPmsReport2Treemap> resultList = new ArrayList<>();
        List<OpenPmsReport2Bean> dataList = openPmsStructureDao.queryReport2(parameterMap);
        for (OpenPmsReport2Bean data : dataList) {
            this.convertReport1Data(resultList, data);
        }
        return response.setBody(resultList);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);
        return response.setBody(openPmsStructureDao.queryReport3(parameterMap));
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(openPmsStructureDao.queryReport3DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(openPmsStructureDao.queryReport3Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);

        String fileName = "open_pms_structure_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.demand.dao.IOpenPmsStructureDao.queryReport3Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport4(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);

        List<OpenPmsReport4Bean> dataList;
        Map<String, BigDecimal> dataMap = new HashMap<>();
        Map<String, String> xAxisMap = new HashMap<>();
        LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();

        dataList = openPmsStructureDao.queryReport4(parameterMap);
        List<String> legend = openPmsStructureDao.queryReport4Legend(parameterMap);

        for (OpenPmsReport4Bean data : dataList) {
            dataMap.put(data.getKey(), data.getVALUE());
            xAxisMap.put(data.getCALENDAR_DATE(), "");
        }
        List<String> xAxisList = xAxisMap.keySet().stream().sorted(String::compareTo).collect(Collectors.toList());
        for (String l : legend) {
            List<BigDecimal> temp = new ArrayList<>();

            for (String x : xAxisList) {
                temp.add(dataMap.getOrDefault(l + "#" + x, BigDecimal.ZERO));
            }
            resultMap.put(l, temp);
        }

        resultMap.put("xAxis", xAxisList);
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport4Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(openPmsStructureDao.queryReport4DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(openPmsStructureDao.queryReport4Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport4Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);

        String fileName = "open_pms_structure_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.demand.dao.IOpenPmsStructureDao.queryReport4Details", parameterMap);
    }

    @Override
    public Response queryReport5Columns(Map<String, Object> parameterMap) {
        return response.setBody(openPmsStructureDao.queryReport5Columns(parameterMap));
    }

    @Override
    @SuppressWarnings("unchecked")
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport5(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        List<String> columns = (List<String>) parameterMap.get("report5Columns");
        columns.replaceAll(this::getColumnNameByLabel);

        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        List<LinkedHashMap<String, Object>> dataList;
        dataList = openPmsStructureDao.queryReport5(parameterMap);

        page.setData(dataList);
        return response.setBody(page);
    }

    @Override
    public Response queryReport5Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(openPmsStructureDao.queryReport5DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(openPmsStructureDao.queryReport5Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport5Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);

        String fileName = "open_pms_structure_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.demand.dao.IOpenPmsStructureDao.queryReport5Details", parameterMap);
    }


    /**
     * 将列表转化为Tree数据
     *
     * @param list 输出树
     * @param data 输入值
     * @throws Exception 异常
     */
    private void convertReport1Data(List<OpenPmsReport2Treemap> list, OpenPmsReport2Bean data) throws Exception {
        String[] categorysOrg = new String[]{data.getCategory1(), data.getCategory2(), data.getCategory3(), data.getCategory4(), data.getCategory5()};
        List<String> categories = new ArrayList<>();

        for (String category : categorysOrg) {
            if (StringUtils.isNotBlank(category)) {
                categories.add(category);
            } else {
                break;
            }
        }

        // 这边逻辑比较复杂, 所以用最笨的方法来描述了, 以免后期不好维护
        // 先把这一行数据转成treemap的数据
        // 第一个节点
        List<OpenPmsReport2Treemap> child = new ArrayList<>();
        OpenPmsReport2Treemap root = new OpenPmsReport2Treemap();
        root.setName(categories.get(0));
        root.setTips(data.copyTooltips()); // 因为这个tooltips要放在树中全局使用, 所以必须要生成一个新节点
        root.setChildren(child);

        // 中间节点
        for (int i = 1; i < categories.size() - 1; i++) {
            OpenPmsReport2Treemap treemap = new OpenPmsReport2Treemap();
            treemap.setName(categories.get(i));
            treemap.setTips(data.copyTooltips());

            child.add(treemap);
            child = new ArrayList<>();
            treemap.setChildren(child);
        }

        // 最后一个节点
        OpenPmsReport2Treemap lastNode = new OpenPmsReport2Treemap();
        lastNode.setName(categories.get(categories.size() - 1));
        lastNode.setValue(data.getValue());
        lastNode.setTips(data.copyTooltips());
        child.add(lastNode);

        // 将这行treemap与原始数据相加
        // 先找到list中是否有这个数据节点
        Optional<OpenPmsReport2Treemap> beanOpt = list.stream().filter(b -> b.getName().equals(categories.get(0))).findFirst();
        if (beanOpt.isPresent()) {
            OpenPmsReport2Treemap bean = beanOpt.get();
            bean.add(root); // 两个节点合并
        } else { //找不到的时候最省事, 直接放入list就可以了
            list.add(root);
        }
    }

    private void generateValueColumn(Map<String, Object> parameterMap) {
        String valueType = (String) parameterMap.get("valueType");

        String qtyColumn = "OPEN_PMS_QTY";

        String valueColumn = "NET_NET_VALUE_RMB";
        if ("Net Net Price".equalsIgnoreCase(valueType)) {
            valueColumn = "SUM(AVG_SELLING_PRICE_RMB * " + qtyColumn + ")";
        } else if ("Quantity".equalsIgnoreCase(valueType)) {
            valueColumn = "SUM(" + qtyColumn + ")";
        } else if ("Line".equalsIgnoreCase(valueType)) {
            valueColumn = "COUNT(1)";
        } else if ("Weight".equalsIgnoreCase(valueType)) {
            valueColumn = "SUM(GROSS_WEIGHT_IN_KG)";
        }
        parameterMap.put("valueColumn", valueColumn);
    }

    @SuppressWarnings("unchecked")
    private void generateFilter(Map<String, Object> parameterMap) {
        this.generateCascaderFilterSQL(parameterMap);
    }

    private void generateTreePathFilter(Map<String, Object> parameterMap) {
        String selectedTreePath = (String) parameterMap.get("selectedTreePath");
        if (StringUtils.isNotBlank(selectedTreePath)) {
            List<String> conditions = new ArrayList<>();
            String[] treePaths = selectedTreePath.split(" > ");
            for (int i = 1; i <= Math.min(treePaths.length, 5); i++) {
                String key = Utils.randomStr(8);
                if ("Others".equals(StringUtils.trim(treePaths[i - 1]))) {
                    String name = this.getColumnNameByLabel(parameterMap.get("level" + i));
                    conditions.add("(" + name + " = #{" + key + ",jdbcType=VARCHAR} or " + name + " is null )");
                } else {
                    conditions.add(this.getColumnNameByLabel(parameterMap.get("level" + i)) + " = #{" + key + ",jdbcType=VARCHAR}");
                }
                parameterMap.put(key, StringUtils.trim(treePaths[i - 1]));
            }

            parameterMap.put("treePathFilter", "(" + StringUtils.join(conditions, " and ") + ")");
        }
    }

    private void generateReport2Tooltips(Map<String, Object> parameterMap) {
        List<String> tooltips = ((JSONArray) parameterMap.get("report2Tooltips")).toJavaList(String.class);
        if (!tooltips.isEmpty()) {
            List<String> tooltipsColumns = tooltips.stream().map(
                    this::getColumnNameByLabel).collect(Collectors.toList());
            List<String> tooltipsColumnsName = new ArrayList<>();

            for (String c : tooltipsColumns) {
                String tooltip = column2TooltipMap.getOrDefault(c, c);
                tooltipsColumnsName.add("NVL(SUM(" + c + "),0) AS " + tooltip);
            }
            parameterMap.put("tooltipsColumns", StringUtils.join(tooltipsColumnsName, ", "));
        }
    }

    private String getColumnNameByLabel(Object labelObj) {
        String label = (String) labelObj;
        if (label == null) {
            return null;
        }
        if (Utils.hasInjectionAttack(label)) {
            return "";
        }
        return tooltip2ColumnMap.getOrDefault(label, label);
    }
}
