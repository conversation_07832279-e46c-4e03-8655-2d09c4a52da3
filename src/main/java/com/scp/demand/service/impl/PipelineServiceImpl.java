package com.scp.demand.service.impl;

import com.starter.utils.Utils;
import com.starter.context.bean.CacheRemove;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.scp.demand.bean.*;
import com.scp.demand.dao.IPipelineDao;
import com.scp.demand.service.IPipelineService;
import com.starter.login.bean.Session;
import com.starter.utils.DateCalUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;

@Service("pipelineService")
@Scope("prototype")
@Transactional
public class PipelineServiceImpl implements IPipelineService {

    @Resource
    private IPipelineDao pipelineDao;

    @Resource
    private Response response;

    @Override
    public Response querySubmissionDate() {
        return response.setBody(pipelineDao.querySubmissionDate());
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryUpdateBy(Map<String, Object> parameterMap) {
        return response.setBody(pipelineDao.queryUpdateBy(parameterMap));
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryProductLine(Map<String, Object> parameterMap) {
        return response.setBody(pipelineDao.queryProductLine(parameterMap));
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryProjectStatus(Map<String, Object> parameterMap) {
        return response.setBody(pipelineDao.queryProjectStatus(parameterMap));
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response querySalesTeam(Map<String, Object> parameterMap) {
        return response.setBody(pipelineDao.querySalesTeam(parameterMap));
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response querySalesPerson(Map<String, Object> parameterMap) {
        return response.setBody(pipelineDao.querySalesPerson(parameterMap));
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1ColumnsComments(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("columns", pipelineDao.queryReport1Columns(parameterMap));
        resultMap.put("comments", Utils.clob2String(pipelineDao.queryReport1Comments(parameterMap)));
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);

        if ("order_value".equalsIgnoreCase((String) parameterMap.get("reportType"))) {
            parameterMap.put("reportType", "order_value");
        } else {
            parameterMap.put("reportType", "forecast_quantity");
        }

        List<Map<String, Object>> dataList = pipelineDao.queryReport1(parameterMap);
        Map<String, Map<String, Object>> dataMap = new HashMap<>();
        for (Map<String, Object> map : dataList) {
            String material = (String) map.get("MATERIAL");
            String delivery_date = (String) map.get("DELIVERY_DATE");
            Object qtyObj = map.get("QTY");

            dataMap.putIfAbsent(material, new HashMap<>());
            if (qtyObj != null) {
                int qty = Utils.parseInt(qtyObj);


                Object sumQty = dataMap.get(material).getOrDefault(delivery_date, 0);
                dataMap.get(material).put(delivery_date, Utils.parseInt(sumQty) + qty);

                Object grandTotal = dataMap.get(material).getOrDefault("grandTotal", 0);
                dataMap.get(material).put("grandTotal", Utils.parseInt(grandTotal) + qty);
            }
        }
        List<Map<String, Object>> resultList = new ArrayList<>();

        for (String key : dataMap.keySet()) {
            Map<String, Object> map = dataMap.get(key);
            map.put("MATERIAL", key);
            resultList.add(map);
        }

        int rowIndex = resultList.size();

        if (rowIndex > 0) {
            Map<String, Object> totalMap = new HashMap<>();
            for (Map<String, Object> map : resultList) {
                for (String key : map.keySet()) {
                    if (key.equalsIgnoreCase("MATERIAL")) {
                        continue;
                    }
                    Object total = totalMap.get(key);
                    Object monthValue = map.get(key);
                    if (total != null || monthValue != null) {
                        totalMap.put(key, Utils.parseInt(total) + Utils.parseInt(map.get(key)));
                    }
                }

            }
            totalMap.put("MATERIAL", "Grand Total");
            resultList.add(totalMap);
        }

        page.setTotal(resultList.size());
        page.setData(resultList);

        return response.setBody(page);
    }

    @Override
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response saveReport1Comments(Map<String, Object> parameterMap) {
        pipelineDao.saveReport1Comments(parameterMap);
        return response;
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1Details(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        List<Map<String, Object>> resultList = pipelineDao.queryReport1Details(parameterMap);
        page.setData(resultList);
        page.setTotal(resultList.size());
        return response.setBody(page);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1Source(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        List<Map<String, Object>> resultList = pipelineDao.queryReport1Source(parameterMap);
        page.setData(resultList);
        page.setTotal(resultList.size());
        return response.setBody(page);
    }

    @Override
    @SuppressWarnings("unchecked")
    @CacheRemove(value = Configuration.APPLICATION_NAME + ":1d", key = "this")
    public Response saveReport1Source(Map<String, Object> parameterMap) {
        Map<String, Object> updateMap = (Map<String, Object>) parameterMap.get("report1SourceUpdated");
        Map<String, Object> param = new HashMap<>();
        Session loginSession = (Session) parameterMap.get("session");
        for (String key : updateMap.keySet()) {
            List<Map<String, Object>> cols = new ArrayList<>();
            param.put("rowid", key);
            param.put("cols", cols);

            Map<String, Object> colMap = (Map<String, Object>) updateMap.get(key);
            for (String k : colMap.keySet()) {
                if (Utils.hasInjectionAttack(k) == true) {
                    return response.setError(new Exception("Invalid update parameters!"));
                }

                Map<String, Object> col = new HashMap<>();
                col.put("key", k);
                col.put("value", colMap.get(k));
                cols.add(col);
            }
            param.put("userid", loginSession.getUserid());
            pipelineDao.saveReport1Source(param);
        }
        return response;
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2(Map<String, Object> parameterMap) {
        List<String> submissionDateList = pipelineDao.queryLast2SubmissionDate(parameterMap);
        String reportType = (String) parameterMap.get("report2Type");
        if ("by_row".equals(reportType)) {
            parameterMap.put("reportType", "count(1)");
        } else {
            parameterMap.put("reportType", "sum(order_value)");
        }

        switch (submissionDateList.size()) {
            case 0:
                return response.setError(new Exception("Submission date invalid, " + parameterMap.get("submissionDate")));
            case 2:
                parameterMap.put("lastSubmissionDate", submissionDateList.get(1));
            case 1:
                parameterMap.put("currentSubmissionDate", submissionDateList.get(0));
        }

        List<Map<String, Object>> dataList = pipelineDao.queryReport2(parameterMap);

        Map<String, String> types = new LinkedHashMap<>();
        types.put("Lost", "#ff5c33");
        types.put("Closed", "#5c6573");
        types.put("New Added", "#5cd65c");
        types.put("Inherited", "#1aa3ff");
        types.put("Missing", "#ffad33");

        Map<String, Object> dataMap = new HashMap<>();
        for (Map<String, Object> map : dataList) {
            dataMap.put((String) map.get("PROJECT_STATUS"), map.get("CNT"));
        }

        PipelineTreeMap root = new PipelineTreeMap();
        PipelineTreeMap pipelineTreeMap;
        PipelineTreeMap inheritedTreeMap = null;
        for (String key : types.keySet()) {
            BigDecimal total = BigDecimal.ZERO;

            if (dataMap.containsKey(key)) {
                total = total.add(Utils.parseBigDecimal(dataMap.get(key)));
            }
            pipelineTreeMap = new PipelineTreeMap();
            pipelineTreeMap.setName(key);
            pipelineTreeMap.setValue(total);
            pipelineTreeMap.setItemColor(types.get(key));
            root.addChild(pipelineTreeMap);
            if (key.equals("Inherited") == true) {
                inheritedTreeMap = pipelineTreeMap;
            }
        }

        // 查找Inherited细分
        Map<String, String> subTypes = new LinkedHashMap<>();
        subTypes.put("RSD Change", "#4db8ff");
        subTypes.put("QTY Change", "#7fccff");
        subTypes.put("Keep Flat", "#1aa3ff");
        Map<String, Object> summaryMap = pipelineDao.queryReport2Summary(parameterMap);
        if (inheritedTreeMap != null) {
            parameterMap.put("selectedSubmissionDate", parameterMap.get("submissionDate"));
            List<Map<String, Object>> inheritedList = pipelineDao.queryReport2DetailsInheritedDetails(parameterMap);
            if (inheritedList.size() > 0) {
                Map<String, Object> inheritedDataMap = inheritedList.get(0);

                for (String t : subTypes.keySet()) {
                    String key = t.toUpperCase().replace(" ", "_");
                    if ("by_value".equals(reportType)) {
                        key += "_VALUE";
                    }

                    inheritedTreeMap.addChild(new PipelineTreeMap(t, Utils.parseBigDecimal(inheritedDataMap.get(key)), subTypes.get(t)));
                }
            }
        }

        Map<String, Object> resultMap = new HashMap<>();
        root.setName("Summary");
        if ("by_row".equals(reportType)) {
            root.setValue(Utils.parseBigDecimal(summaryMap.get("ROW_CNT")));
        } else {
            root.setValue(Utils.parseBigDecimal(summaryMap.get("VALUE_SUM")));
        }
        resultMap.put("data", new PipelineTreeMap[]{root});
        resultMap.putAll(summaryMap);

        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2Details(Map<String, Object> parameterMap) {
        String reportName = (String) parameterMap.get("reportName");
        List<String> monthList = pipelineDao.queryFullMonthRange(parameterMap);
        Map<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> dataList = new ArrayList<>();

        String reportType = (String) parameterMap.get("report2Type");
        if ("by_row".equals(reportType)) {
            parameterMap.put("reportType", "count(1)");
        } else if ("by_value".equals(reportType)) {
            parameterMap.put("reportType", "sum(order_value)");
        } else {
            return response.setError(new Exception("Invalid report type"));
        }

        switch (reportName) {
            case "Closed":
                parameterMap.put("project_status", "已下单");
                dataList = pipelineDao.queryReport2DetailsCloseOrLost(parameterMap);
                break;
            case "Lost":
                parameterMap.put("project_status", "丢标");
                dataList = pipelineDao.queryReport2DetailsCloseOrLost(parameterMap);
                break;
            case "New Added":
                dataList = pipelineDao.queryReport2DetailsNewAdded(parameterMap);
                break;
            case "Inherited":
                dataList = pipelineDao.queryReport2DetailsInherited(parameterMap);
                break;
            case "RSD Change":
            case "QTY Change":
            case "Keep Flat":
                List<Map<String, Object>> list = pipelineDao.queryReport2DetailsInheritedDetails(parameterMap);
                String key = reportName.toUpperCase().replace(" ", "_");
                if ("by_value".equals(reportType)) {
                    key += "_VALUE";
                }

                for (Map<String, Object> map : list) {
                    map.put("CNT", map.get(key));
                    dataList.add(map);
                }

                break;
            case "Missing":
                dataList = pipelineDao.queryReport2DetailsMissing(parameterMap);
                break;
            default:
                dataList = pipelineDao.queryReport2DetailsSummary(parameterMap);
                break;
        }

        Map<String, Object> dataMap = new HashMap<>();
        for (Map<String, Object> map : dataList) {
            dataMap.put((String) map.get("SUBMISSION_MONTH"), Utils.parseBigDecimal(map.get("CNT"), null));
        }

        List<BigDecimal> yAxis = new ArrayList<>();

        boolean patchZero = true;
        for (String m : monthList) {
            if (dataMap.get(m) == null) {
                if (patchZero) {
                    yAxis.add(BigDecimal.ZERO);
                } else {
                    yAxis.add(null);
                }
            } else {
                patchZero = false;
                yAxis.add((BigDecimal) dataMap.get(m));
            }

        }
        resultMap.put("yAxis", yAxis);
        resultMap.put("xAxis", monthList);
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2Source(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        List<Map<String, Object>> resultList = new ArrayList<>();
        String reportName = (String) parameterMap.get("reportName");

        parameterMap.put("submissionDate", pipelineDao.querySubmissionDateByMonth((String) parameterMap.get("submissionMonth")));

        switch (reportName) {
            case "Closed":
            case "Lost":
            case "New Added":
            case "Inherited":
            case "Missing":
                List<String> submissionDateList = pipelineDao.queryLast2SubmissionDate(parameterMap);
                switch (submissionDateList.size()) {
                    case 0:
                        return response.setError(new Exception("Submission date invalid, " + parameterMap.get("submissionDate")));
                    case 2:
                        parameterMap.put("lastSubmissionDate", submissionDateList.get(1));
                    case 1:
                        parameterMap.put("currentSubmissionDate", submissionDateList.get(0));
                }
                resultList = pipelineDao.queryReport2Source(parameterMap);
                break;
            case "RSD Change":
            case "QTY Change":
            case "Keep Flat":
                List<Map<String, Object>> dataList = pipelineDao.queryReport2SourceInherited(parameterMap);

                for (Map<String, Object> data : dataList) {
                    BigDecimal qtyCurrent = Utils.parseBigDecimal(data.get("QUANTITY"));
                    BigDecimal qtyLast = Utils.parseBigDecimal(data.get("QUANTITY2"));
                    String deliveryDateCurrent = String.valueOf(data.get("EST_DELIVERY_DATE1"));
                    String deliveryDateLast = String.valueOf(data.get("EST_DELIVERY_DATE2"));

                    switch (reportName) {
                        case "Keep Flat":
                            if (deliveryDateCurrent.equals(deliveryDateLast) && qtyCurrent.compareTo(qtyLast) == 0) {
                                resultList.add(data);
                            }
                            break;
                        case "RSD Change":
                            if (deliveryDateCurrent.equals(deliveryDateLast) == false) {
                                data.put("RSD_CHANGE", deliveryDateLast + " → " + deliveryDateCurrent);
                                if (qtyCurrent.compareTo(qtyLast) != 0) {
                                    data.put("QUANTITY_CHANGE", Utils.thousandBitSeparator(qtyLast.toPlainString()) + " → " + Utils.thousandBitSeparator(qtyCurrent.toPlainString()));
                                }
                                resultList.add(data);
                            }
                            break;
                        case "QTY Change":
                            if (deliveryDateCurrent.equals(deliveryDateLast) == true && qtyCurrent.compareTo(qtyLast) != 0) {
                                data.put("QUANTITY_CHANGE", Utils.thousandBitSeparator(qtyLast.toPlainString()) + " → " + Utils.thousandBitSeparator(qtyCurrent.toPlainString()));
                                resultList.add(data);
                            }
                            break;
                    }
                }

                break;
            default:
                resultList = pipelineDao.queryReport2SourceSummary(parameterMap);
        }

        page.setData(resultList);
        page.setTotal(resultList.size());
        return response.setBody(page);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        List<String> monthList = pipelineDao.queryMonthByCalender(parameterMap);
        List<String> submissionDateList = pipelineDao.queryLast2SubmissionDate(parameterMap);
        if ("by_qty".equalsIgnoreCase((String) parameterMap.get("report3Type"))) {
            parameterMap.put("reportType", "sum(forecast_quantity)");
        } else {
            parameterMap.put("reportType", "sum(order_value)");
        }

        resultMap.put("xAxis", monthList);
        if (submissionDateList.size() != 2) {
            return response.setBody(resultMap);
        }

        parameterMap.put("currentSubmissionDate", submissionDateList.get(0));
        parameterMap.put("lastSubmissionDate", submissionDateList.get(1));

        List<Map<String, Object>> dataList = pipelineDao.queryReport3(parameterMap);

        String[] types = new String[]{"New Added", "Inherited"};

        Map<String, Object> dataMap = new HashMap<>();
        for (Map<String, Object> map : dataList) {
            dataMap.put(map.get("PROJECT_STATUS") + "#" + map.get("MONTH"), map.get("CNT"));
        }


        for (String t : types) {
            List<BigDecimal> subResultList = new ArrayList<>();

            for (String m : monthList) {
                String key = t + "#" + m;
                subResultList.add(dataMap.containsKey(key) ? (BigDecimal) dataMap.get(key) : null);
            }

            resultMap.put(t, subResultList);
        }


        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Details(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);

        List<String> submissionDateList = pipelineDao.queryLast2SubmissionDate(parameterMap);

        if (submissionDateList.size() != 2) {
            return response.setBody(page);
        }

        parameterMap.put("currentSubmissionDate", submissionDateList.get(0));
        parameterMap.put("lastSubmissionDate", submissionDateList.get(1));

        List<Map<String, Object>> resultList = pipelineDao.queryReport3Details(parameterMap);

        page.setData(resultList);
        page.setTotal(resultList.size());
        return response.setBody(page);
    }

    /**
     * 第四第五张报表使用的都是同一个数据源, 所以单独做一个方法, 不同的是, 第四张报表只需要执行一次, 第五张报表需要执行六次
     *
     * @param parameterMap 前台查询参数
     * @return 返回项目明细
     */
    private List<PipelineReport4Bean> queryReport4Data(Map<String, Object> parameterMap) {
        // 查询出来所有项目数据
        List<Map<String, Object>> rawDataList = pipelineDao.queryReport4(parameterMap);

        // 根据项目内容统计项目信息  开始↓

        List<PipelineReport4Bean> resultList = new ArrayList<>();

        int firstEstDeliveryDate = -1;
        int delayFrequency = 0;

        Map<String, Object> current; // 计数器指向的当前行
        Map<String, Object> next; // 计数器指向的下一行
        List<String> estDateTracking = new ArrayList<>();
        for (int i = 0; i < rawDataList.size(); i++) {
            current = rawDataList.get(i);
            if (i + 1 < rawDataList.size()) {
                next = rawDataList.get(i + 1);
            } else {
                next = new HashMap<>(current);
                next.put("PROJECT_NAME", "LOOP_END");
            }

            if (i == 0) {
                firstEstDeliveryDate = Utils.parseInt(current.get("EST_DELIVERY_DATE"));
            }
            estDateTracking.add(String.valueOf(current.get("EST_DELIVERY_DATE")));

            // 如果当前行和下一行不同, 则记录当前行
            if (StringUtils.equalsIgnoreCase((String) current.get("PROJECT_NAME"), (String) next.get("PROJECT_NAME")) == false) {
                PipelineReport4Bean report4Bean = new PipelineReport4Bean();
                report4Bean.setProjectName((String) current.get("PROJECT_NAME"));
                report4Bean.setProjectStatus((String) current.get("PROJECT_STATUS"));
                report4Bean.setDelayFrequency(delayFrequency);
                report4Bean.setDelayDepth(DateCalUtil.calcMonthGap(firstEstDeliveryDate, Utils.parseInt(current.get("EST_DELIVERY_DATE"))));
                report4Bean.setEstDateTracking(estDateTracking);
                resultList.add(report4Bean);
                firstEstDeliveryDate = Utils.parseInt(next.get("EST_DELIVERY_DATE"));
                // 重置计数器
                delayFrequency = 0;
                estDateTracking = new ArrayList<>();
            } else {
                // 如果下月交货期大于本月交货期,那么frequency++
                if (Utils.parseInt(next.get("EST_DELIVERY_DATE")) > Utils.parseInt(current.get("EST_DELIVERY_DATE"))) {
                    delayFrequency++;
                }
            }
        }
        return resultList;
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport4(Map<String, Object> parameterMap) {
        String submissionDate = (String) parameterMap.get("submissionDate");
        parameterMap.put("submissionMonth", submissionDate.substring(0, 7));
        List<PipelineReport4Bean> tempList = this.queryReport4Data(parameterMap);

        // 根据查询出来的结果进行汇总
        Map<String, PipelineReport4Result> resultMap = new LinkedHashMap<>();
        resultMap.put("Open", new PipelineReport4Result());
        resultMap.put("Close", new PipelineReport4Result());
        resultMap.put("Lost", new PipelineReport4Result());
        for (PipelineReport4Bean bean : tempList) {
            PipelineReport4Result summary = resultMap.computeIfAbsent(bean.getProjectStatus(), key -> new PipelineReport4Result());
            summary.addValue();
            summary.addChild(bean);
            summary.setName(bean.getProjectStatus());
            resultMap.put(bean.getProjectStatus(), summary);
        }

        return response.setBody(new ArrayList<>(resultMap.values()));
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport5(Map<String, Object> parameterMap) {
        try {
            // 生成X轴
            PipelineReport5Result result = new PipelineReport5Result();
            Date submissionDate = new SimpleDateFormat("yyyy/MM/dd").parse((String) parameterMap.get("submissionDate"));

            SimpleDateFormat format = new SimpleDateFormat("MMM-yy", Locale.ENGLISH);
            SimpleDateFormat format2 = new SimpleDateFormat("yyyy/MM");

            List<String> xAxisList = new ArrayList<>();

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(submissionDate);

            xAxisList.add(format2.format(calendar.getTime()));
            result.addxAxis(format.format(calendar.getTime()).toUpperCase());
            for (int i = 0; i < 5; i++) {
                calendar.add(Calendar.MONTH, -1);
                xAxisList.add(format2.format(calendar.getTime()));
                result.addxAxis(format.format(calendar.getTime()).toUpperCase());
            }
            Collections.reverse(xAxisList);
            Collections.reverse(result.getxAxis());

            // 按月查询数据
            for (String month : xAxisList) {
                parameterMap.put("submissionMonth", month);
                List<PipelineReport4Bean> dataList = this.queryReport4Data(parameterMap);
                int df3 = 0; // delay frequency < 3
                int df5 = 0; // delay frequency < 5
                int df51 = 0; // delay frequency >= 5
                int dd3 = 0; // delay depth >= 5
                int dd5 = 0; // delay depth >= 5
                int dd51 = 0; // delay depth >= 5
                for (PipelineReport4Bean data : dataList) {
                    switch (data.getDelayFrequencyName()) {
                        case PipelineReport4Bean.DF_LESS_THAN_3:
                            df3++;
                            break;
                        case PipelineReport4Bean.DF_LESS_THAN_5:
                            df5++;
                            break;
                        case PipelineReport4Bean.DF_EQ_OR_GREATER_5:
                            df51++;
                            break;
                    }
                    switch (data.getDelayDepthName()) {
                        case PipelineReport4Bean.DD_LESS_THAN_3:
                            dd3++;
                            break;
                        case PipelineReport4Bean.DD_LESS_THAN_5:
                            dd5++;
                            break;
                        case PipelineReport4Bean.DD_EQ_OR_GREATER_5:
                            dd51++;
                            break;
                    }
                }
                result.addyAxis1(df3 == 0 ? null : df3);
                result.addyAxis2(df5 == 0 ? null : df5);
                result.addyAxis3(df51 == 0 ? null : df51);

                int total = dataList.size();
                if (total == 0) {
                    result.addyAxis4(null);
                    result.addyAxis5(null);
                    result.addyAxis6(null);
                } else {
                    result.addyAxis4(dd3 == 0 ? null : new BigDecimal(String.valueOf(dd3 * 100.0 / total)).setScale(2, RoundingMode.HALF_UP));
                    result.addyAxis5(dd5 == 0 ? null : new BigDecimal(String.valueOf(dd5 * 100.0 / total)).setScale(2, RoundingMode.HALF_UP));
                    result.addyAxis6(dd51 == 0 ? null : new BigDecimal(String.valueOf(dd51 * 100.0 / total)).setScale(2, RoundingMode.HALF_UP));
                }
            }

            response.setBody(result);
        } catch (Exception e) {
            response.setError(e);
        }

        return response;
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport5Details(Map<String, Object> parameterMap) {
        try {
            SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
            parameterMap.put("submissionMonth", new SimpleDateFormat("yyyy/MM").format(new SimpleDateFormat("MMM-yy", Locale.ENGLISH).parse((String) parameterMap.get("submissionDate"))));

            List<PipelineReport4Bean> projectList = this.queryReport4Data(parameterMap);

            List<Map<String, String>> projectNameList = new ArrayList<>();

            String reportName = (String) parameterMap.get("reportName");

            Map<String, PipelineReport4Bean> estDateTrackingMap = new HashMap<>();

            for (PipelineReport4Bean project : projectList) {
                if (project.getDelayDepthName().equals(reportName) || project.getDelayFrequencyName().equals(reportName)) {
                    String[] pn = project.getProjectName().split("@");
                    if (pn.length == 2) {
                        projectNameList.add(new HashMap<>() {{
                            put("project_name", pn[1]);
                            put("material", pn[0]);
                        }});
                    }

                    estDateTrackingMap.put(project.getProjectName(), project);
                }
            }
            parameterMap.put("projectNameList", projectNameList);

            List<Map<String, Object>> resultList = pipelineDao.queryReport5Details(parameterMap);

            for (Map<String, Object> map : resultList) {
                String projectName = map.get("MATERIAL") + "@" + map.get("PROJECT_NAME");
                PipelineReport4Bean project = estDateTrackingMap.get(projectName);
                if (project != null) {
                    map.put("EST_DATE_TRACKING", StringUtils.join(estDateTrackingMap.get(projectName).getEstDateTracking(), ","));
                    map.put("DF", project.getDelayFrequency());
                    map.put("DD", project.getDelayDepth());
                }
            }

            page.setData(resultList);
            page.setTotal(resultList.size());
            response.setBody(page);
        } catch (Exception e) {
            response.setError(e);
        }

        return response;
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport6(Map<String, Object> parameterMap) {
        try {
            // 生成X轴
            PipelineReport6Result result = new PipelineReport6Result();
            Date submissionDate = new SimpleDateFormat("yyyy/MM/dd").parse((String) parameterMap.get("submissionDate"));

            SimpleDateFormat format = new SimpleDateFormat("MMM-yy", Locale.ENGLISH);
            SimpleDateFormat format2 = new SimpleDateFormat("yyyy/MM");

            List<String> xAxisList = new ArrayList<>();

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(submissionDate);

            xAxisList.add(format2.format(calendar.getTime()));
            result.addxAxis(format.format(calendar.getTime()).toUpperCase());
            for (int i = 0; i < 5; i++) {
                calendar.add(Calendar.MONTH, -1);
                xAxisList.add(format2.format(calendar.getTime()));
                result.addxAxis(format.format(calendar.getTime()).toUpperCase());
            }
            Collections.reverse(xAxisList);
            Collections.reverse(result.getxAxis());

            // 按月查询数据
            for (String month : xAxisList) {
                parameterMap.put("submissionMonth", month);
                Map<String, Object> dataMap = pipelineDao.queryReport6(parameterMap);
                if (dataMap != null && dataMap.isEmpty() == false) {
                    BigDecimal total = Utils.parseBigDecimal(dataMap.get("TOTAL"));
                    BigDecimal warningCount = Utils.parseBigDecimal(dataMap.get("WARNING_CNT"));
                    result.addyAxis1(warningCount.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : warningCount.divide(total, 4, RoundingMode.HALF_UP));
                    result.addyAxis2(warningCount);
                } else {
                    result.addyAxis1(null);
                    result.addyAxis2(null);
                }
            }

            response.setBody(result);
        } catch (Exception e) {
            response.setError(e);
        }

        return response;
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport6Details(Map<String, Object> parameterMap) {
        try {
            SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
            parameterMap.put("submissionMonth", new SimpleDateFormat("yyyy/MM").format(new SimpleDateFormat("MMM-yy", Locale.ENGLISH).parse((String) parameterMap.get("submissionDate"))));

            List<Map<String, Object>> resultList = pipelineDao.queryReport6Details(parameterMap);

            page.setData(resultList);
            page.setTotal(resultList.size());
            response.setBody(page);
        } catch (Exception e) {
            response.setError(e);
        }

        return response;
    }

    @Override
    public Response queryInvalidPipelineData(Map<String, Object> parameterMap) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);

        page.setTotal(pipelineDao.queryInvalidPipelineDataCount());
        if (page.getTotal() != 0) {
            page.setData(pipelineDao.queryInvalidPipelineData(parameterMap));
        }
        return response.setBody(page);
    }
}
