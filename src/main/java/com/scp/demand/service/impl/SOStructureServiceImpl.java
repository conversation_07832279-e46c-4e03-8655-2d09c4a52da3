package com.scp.demand.service.impl;

import com.adm.system.bean.CascaderBean;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.scp.demand.bean.SOReport1Bean;
import com.scp.demand.bean.SOReport2Bean;
import com.scp.demand.bean.SOTreemap;
import com.scp.demand.dao.ISOStructureDao;
import com.scp.demand.service.ISOStructureService;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.servlet.ServiceHelper;
import com.starter.login.bean.Session;
import com.starter.utils.AuthUtils;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service("SOStructureService")
@Scope("prototype")
@Transactional
public class SOStructureServiceImpl extends ServiceHelper implements ISOStructureService {

    @Resource
    private ISOStructureDao SOStructureDao;

    @Resource
    private Response response;

    @Resource
    private ExcelTemplate excelTemplate;

    public static final String SO_PARENT_CODE = "menu190";

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryFilters(Map<String, Object> parameterMap) {
        List<CascaderBean> resultList = new ArrayList<>();
        List<Map<String, String>> dataList = SOStructureDao.queryCascader(parameterMap);
        Map<String, List<Map<String, String>>> tempMap = new LinkedHashMap<>();
        for (Map<String, String> map : dataList) {
            tempMap.computeIfAbsent(map.get("CATEGORY"), key -> new ArrayList<>()).add(map);
        }
        for (String key : tempMap.keySet()) {
            CascaderBean bean = new CascaderBean();
            resultList.add(bean);
            bean.setLabel(key);
            bean.setValue(key);
            for (Map<String, String> map : tempMap.get(key)) {
                CascaderBean subBean = new CascaderBean();
                subBean.setLabel(map.get("NAME"));
                subBean.setValue(map.get("NAME"));
                bean.addChild(subBean);
            }
        }
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("cascader", resultList);
        resultMap.put("dateColumns", SOStructureDao.queryDateColumns(parameterMap));
        return response.setBody(resultMap);
    }

    private String getColumnName(Object labelObj) {
        String label = (String) labelObj;
        if (label == null) {
            return null;
        }
        if (Utils.hasInjectionAttack(label)) {
            return "";
        }
        if ("SALES_ORDER_NUMBER_ITEM".equalsIgnoreCase(label)) {
            return "SALES_ORDER_NUMBER || ' ' || SALES_ORDER_ITEM";
        }
        return label;
    }

    @SuppressWarnings("unchecked")
    public void generateFilter(Map<String, Object> parameterMap) {
        List<String> report3SelectedColumns = (List<String>) parameterMap.get("report3SelectedColumns");
        List<String> listWithoutDuplicates = (List<String>) parameterMap.get("report3ColumnNames");
        if (report3SelectedColumns == null || report3SelectedColumns.isEmpty()) {
            report3SelectedColumns = new ArrayList<>();
            report3SelectedColumns.add("BU");
        }
        parameterMap.put("report3SelectedColumns", report3SelectedColumns);
        listWithoutDuplicates = listWithoutDuplicates.stream().distinct().collect(Collectors.toList());
        parameterMap.put("listWithoutDuplicates", listWithoutDuplicates);

        this.generateCascaderFilterSQL(parameterMap);

        // scope
        String scope = (String) parameterMap.get("scope");
        List<String> subScope = (List<String>) parameterMap.get("subScope");
        if (!(subScope == null || subScope.isEmpty())) {
            List<String> scList = new ArrayList<>();
            for (String s : subScope) {
                String key = Utils.randomStr(8);
                parameterMap.put(key, s);
                scList.add("#{" + key + ", jdbcType=VARCHAR}");
            }
            if ("SECI".equals(scope)) {
                parameterMap.put("scopeFilter", "T.SE_SCOPE IN (" + StringUtils.join(scList, ",") + ")");
            } else {
                parameterMap.put("scopeFilter", "T.PLANT_SCOPE IN (" + StringUtils.join(scList, ",") + ")");
            }
        }

        Session session = (Session) parameterMap.get("session");
        parameterMap.putAll(AuthUtils.generateConditions(session, SO_PARENT_CODE, "personalFilters"));
    }

    private void generateReport1Tooltips(Map<String, Object> parameterMap) {
        List<String> tooltipsColumns = ((JSONArray) parameterMap.get("report1Tooltips")).toJavaList(String.class);
        if (!tooltipsColumns.isEmpty()) {
            List<String> tooltipsColumnsName = new ArrayList<>();
            for (String c : tooltipsColumns) {
                if ("LINE".equalsIgnoreCase(c)) {
                    tooltipsColumnsName.add("COUNT(1) AS " + c);
                } else {
                    tooltipsColumnsName.add("NVL(AVG(" + c + "),0) AS " + c);
                }
            }
            parameterMap.put("tooltipsColumns", StringUtils.join(tooltipsColumnsName, ", "));
        }
    }

    private void generateValueColumn(Map<String, Object> parameterMap) {
        String resultType = (String) parameterMap.get("resultType");
        String valueColumn = "0";
        if ("Net Net Value".equalsIgnoreCase(resultType)) {
            valueColumn = "NVL(SUM(NET_NET_VALUE_RMB), 0)";
        } else if ("Line".equalsIgnoreCase(resultType)) {
            valueColumn = "COUNT(1)";
        } else if ("Quantity".equalsIgnoreCase(resultType)) {
            String pageType = (String) parameterMap.get("pageType");
            if ("SO_ORDER_INTAKE".equalsIgnoreCase(pageType)) {
                valueColumn = "NVL(SUM(ORDER_QUANTITY), 0)";
            } else {
                valueColumn = "NVL(SUM(BILLING_QUANTITY), 0)";
            }
        } else if ("Avg LT".equalsIgnoreCase(resultType)) {
            valueColumn = "NVL(AVG(ACTUAL_LT), 0)";
        } else if ("Med LT".equalsIgnoreCase(resultType)) {
            valueColumn = "NVL(PERCENTILE_DISC(" + (BigDecimal.valueOf((int) parameterMap.get("medianPosition"))).divide(new BigDecimal("100")) + ") WITHIN GROUP (ORDER BY ACTUAL_LT), 0)";
        } else if ("Avg LT Shorten".equalsIgnoreCase(resultType)) {
            valueColumn = "NVL(AVG(LT_SHORTEN), 0)";
        }
        parameterMap.put("valueColumn", valueColumn);
    }

    private void generateTreePathFilter(Map<String, Object> parameterMap) {
        String selectedTreePath = (String) parameterMap.get("selectedTreePath");
        if (StringUtils.isNotBlank(selectedTreePath)) {
            List<String> conditions = new ArrayList<>();
            String[] treePaths = selectedTreePath.split(" > ");
            for (int i = 1; i <= Math.min(treePaths.length, 5); i++) {
                String key = Utils.randomStr(8);
                if ("Others".equals(StringUtils.trim(treePaths[i - 1]))) {
                    String name = this.getColumnName(parameterMap.get("level" + i));
                    conditions.add("(" + name + " = #{" + key + ",jdbcType=VARCHAR} or " + name + " is null )");
                } else {
                    conditions.add(
                            this.getColumnName(parameterMap.get("level" + i))
                                    + " = #{" + key + ",jdbcType=VARCHAR}"
                    );
                }
                parameterMap.put(key, StringUtils.trim(treePaths[i - 1]));
            }
            parameterMap.put("treePathFilter", "(" + StringUtils.join(conditions, " and ") + ")");
        }
    }

    /**
     * 将列表转化为Tree数据
     *
     * @param list 输出树
     * @param data 输入值
     * @throws Exception 异常
     */
    private void convertReport1Data(List<SOTreemap> list, SOReport1Bean data) throws Exception {
        String[] categoryOrg = new String[]{data.getCategory1(), data.getCategory2(), data.getCategory3(), data.getCategory4(), data.getCategory5()};
        List<String> categories = new ArrayList<>();
        for (String category : categoryOrg) {
            if (StringUtils.isNotBlank(category)) {
                categories.add(category);
            } else {
                break;
            }
        }
        // 这边逻辑比较复杂, 所以用最笨的方法来描述了, 以免后期不好维护
        // 先把这一行数据转成treemap的数据
        // 第一个节点
        List<SOTreemap> child = new ArrayList<>();
        SOTreemap root = new SOTreemap();
        root.setName(categories.get(0));
        root.setTips(data.copyTooltips()); // 因为这个tooltips要放在树中全局使用, 所以必须要生成一个新节点
        root.setChildren(child);

        // 中间节点
        for (int i = 1; i < categories.size() - 1; i++) {
            SOTreemap treemap = new SOTreemap();
            treemap.setName(categories.get(i));
            treemap.setTips(data.copyTooltips());

            child.add(treemap);
            child = new ArrayList<>();
            treemap.setChildren(child);
        }

        // 最后一个节点
        SOTreemap lastNode = new SOTreemap();
        lastNode.setName(categories.get(categories.size() - 1));
        lastNode.setValue(data.getValue());
        lastNode.setTips(data.copyTooltips());
        child.add(lastNode);

        // 将这行treemap与原始数据相加
        // 先找到list中是否有这个数据节点
        Optional<SOTreemap> beanOpt = list.stream().filter(b -> b.getName().equals(categories.get(0))).findFirst();
        if (beanOpt.isPresent()) {
            SOTreemap bean = beanOpt.get();
            bean.add(root); // 两个节点合并
        } else { //找不到的时候最省事, 直接放入list就可以了
            list.add(root);
        }
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) throws Exception {
        this.generateFilter(parameterMap);
        this.generateReport1Tooltips(parameterMap);
        this.generateValueColumn(parameterMap);

        parameterMap.put("level1", this.getColumnName(parameterMap.get("level1")));
        parameterMap.put("level2", this.getColumnName(parameterMap.get("level2")));
        parameterMap.put("level3", this.getColumnName(parameterMap.get("level3")));
        parameterMap.put("level4", this.getColumnName(parameterMap.get("level4")));
        parameterMap.put("level5", this.getColumnName(parameterMap.get("level5")));

        List<SOReport1Bean> dataList = SOStructureDao.queryReport1(parameterMap);
        List<SOTreemap> resultList = new ArrayList<>();
        for (SOReport1Bean data : dataList) {
            this.convertReport1Data(resultList, data);
        }
        return response.setBody(resultList);
    }


    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);

        List<SOReport2Bean> dataList;

        dataList = SOStructureDao.queryReport2(parameterMap);

        Map<String, BigDecimal> dataMap = new HashMap<>();
        LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();
        Map<String, String> xAxisMap = new HashMap<>();

        List<String> legend = new ArrayList<>();

        for (SOReport2Bean data : dataList) {
            dataMap.put(data.getKey(), data.getVALUE());
            xAxisMap.put(data.getCALENDAR_DATE(), "");
            if (!legend.contains(data.getNAME())) {
                legend.add(data.getNAME());
            }
        }
        List<String> xAxisList = xAxisMap.keySet().stream().sorted(String::compareTo).collect(Collectors.toList());
        for (String l : legend) {
            List<BigDecimal> temp = new ArrayList<>();

            for (String x : xAxisList) {
                temp.add(dataMap.getOrDefault(l + "#" + x, BigDecimal.ZERO));
            }
            resultMap.put(l, temp);
        }

        resultMap.put("xAxis", xAxisList);

        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(SOStructureDao.queryReport2DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(SOStructureDao.queryReport2Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport2Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);

        String pageType = (String) parameterMap.get("pageType");
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName;
        if ("SO_ORDER_INTAKE".equalsIgnoreCase(pageType)) {
            fileName = "so_structure_" + Utils.randomStr(4) + ".xlsx";
        } else {
            fileName = "so_billing_structure_" + Utils.randomStr(4) + ".xlsx";
        }
        excelTemplate.create(res, fileName, "com.scp.demand.dao.ISOStructureDao.queryReport2Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        this.generateValueColumn(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        List<LinkedHashMap<String, Object>> dataList = SOStructureDao.queryReport3(parameterMap);

        page.setData(dataList);
        page.setTotal(dataList.size());
        return response.setBody(page);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(SOStructureDao.queryReport3DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(SOStructureDao.queryReport3Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse res) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);
        String pageType = (String) parameterMap.get("pageType");
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        parameterMap.put("column1", this.getColumnName(parameterMap.get("column1")));
        parameterMap.put("column2", this.getColumnName(parameterMap.get("column2")));
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName;
        if ("SO_ORDER_INTAKE".equalsIgnoreCase(pageType)) {
            fileName = "so_structure_" + Utils.randomStr(4) + ".xlsx";
        } else {
            fileName = "so_billing_structure_" + Utils.randomStr(4) + ".xlsx";
        }
        excelTemplate.create(res, fileName, "com.scp.demand.dao.ISOStructureDao.queryReport3Details", parameterMap);
    }

    @Override
    public void downloadReport2DisplayData(Map<String, Object> parameterMap, HttpServletResponse res) {
        JSONObject report2DisplayData = (JSONObject) parameterMap.get("report2DisplayData");
        String dateColumn = (String) parameterMap.get("dateColumn");
        String resultType = (String) parameterMap.get("resultType");
        @SuppressWarnings("unchecked")
        List<String> movAvgTempData = (List<String>) report2DisplayData.get("movAvgTempData");
        @SuppressWarnings("unchecked")
        List<String> avgTempData = (List<String>) report2DisplayData.get("avgTempData");
        JSONObject yAxisData = (JSONObject) report2DisplayData.get("yAxisData");
        @SuppressWarnings("unchecked")
        List<String> xAxis = (List<String>) yAxisData.get("xAxis");
        List<LinkedHashMap<String, Object>> dataList = new ArrayList<>();
        for (int i = 0; i < xAxis.size(); i++) {
            LinkedHashMap<String, Object> rowData = new LinkedHashMap<>();
            for (String key : yAxisData.keySet()) {
                List<?> valueList = (List<?>) yAxisData.get(key);
                if (valueList != null && i < valueList.size()) {
                    if ("xAxis".equals(key)) {
                        rowData.put(dateColumn, valueList.get(i));
                    } else {
                        rowData.put(resultType, valueList.get(i));
                    }
                }
            }
            rowData.put("Average Value of Points", avgTempData.get(i));
            rowData.put("Moving Average Value of Points", movAvgTempData.get(i));
            dataList.add(rowData);
        }
        excelTemplate.create(res, "SO Report2 Display Details.xlsx", dataList);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Columns(Map<String, Object> parameterMap) {
        return response.setBody(SOStructureDao.queryReport3Columns(parameterMap));
    }

}
