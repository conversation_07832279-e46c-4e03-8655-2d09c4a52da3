package com.scp.demand.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.starter.utils.Utils;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.scp.demand.dao.IEvolutionDao;
import com.scp.demand.service.IEvolutionService;
import com.starter.utils.DateCalUtil;
import com.starter.utils.excel.ExcelTemplate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service("evolutionServiceImpl")
@Scope("prototype")
@Transactional
public class EvolutionServiceImpl implements IEvolutionService {

    @Resource
    private Response response;

    @Resource
    private IEvolutionDao evolutionDao;

    @Resource
    private ExcelTemplate excelTemplate;

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage(Map<String, Object> parameterMap) {
        Map<String, Object> result = new HashMap<>();
        result.put("FCST", evolutionDao.queryFcstVersion());
        result.put("CASCADER", Utils.parseCascader(evolutionDao.queryCascader()));
        return response.setBody(result);
    }

    private Map<String, Object> generateReport1Columns(Map<String, Object> parameterMap) {
        Map<String, Object> resultMap = new HashMap<>();
        List<String> dateColumns = new ArrayList<>(); // 存放日期信息
        List<String> historyColumns = new ArrayList<>(); // dateColumns中历史的部分
        List<String> fcstColumns = new ArrayList<>(); // dateColumns中预测的部分
        List<String> pipelineColumns = new ArrayList<>(); // pipeline在数据库中的名字, 如MONTH01, MONTH02
        List<String> pipelineNames = new ArrayList<>(); // pipeline在数据库中的别名, 如202105, 202106
        List<String> tableColumns = new ArrayList<>(); // 数据表字段名

        // 看用户选择的valueType
        String valueType = (String) parameterMap.get("valueType");
        String prefix = "";
        String fcstValueColumn = "";
        String amuColumn = "";
        String amfColumn = "";
        switch (valueType) {
            case "Net Net Price" -> {
                prefix = "VAL_";
                fcstValueColumn = "AVG_SELLING_PRICE_RMB";
                amuColumn = "AMU * AVG_SELLING_PRICE_RMB";
                amfColumn = "AMF * AVG_SELLING_PRICE_RMB";
            }
            case "Moving Average Price" -> {
                prefix = "MVP_";
                fcstValueColumn = "UNIT_COST";
                amuColumn = "AMU * UNIT_COST";
                amfColumn = "AMF * UNIT_COST";
            }
            case "Net Net Price HKD" -> {
                prefix = "VAL_HKD_";
                fcstValueColumn = "AVG_SELLING_PRICE_HKD";
                amuColumn = "AMU * AVG_SELLING_PRICE_HKD";
                amfColumn = "AMF * AVG_SELLING_PRICE_HKD";
            }
            case "Quantity" -> {
                prefix = "QTY_";
                fcstValueColumn = "1";
                amuColumn = "AMU";
                amfColumn = "AMF";
            }
            case "Line" -> {
                prefix = "LINE_";
                fcstValueColumn = " null ";
                amuColumn = " null ";
                amfColumn = " null ";
            }
        }

        String dataScope = (String) parameterMap.get("dataScope");
        String fcstVersion = (String) parameterMap.get("fcstVersion");

        // 一共36个月, 去年第一个月到明年的最后一个月, 先生成36个月
        // dataScope 如果是full hist, 那么fcst补位
        // 如果是full fcst, 那么先算fcst, 再算hist
        SimpleDateFormat format = new SimpleDateFormat("yyyyMM");
        Calendar calendar = Calendar.getInstance();
        List<String> allMonth = new ArrayList<>();

        String currentMonth = format.format(calendar.getTime());
        parameterMap.put("currentMonth", prefix + currentMonth);
        parameterMap.put("tableColumns", tableColumns);
        parameterMap.put("pipelineNames", pipelineNames);
        parameterMap.put("pipelineColumns", pipelineColumns);
        parameterMap.put("displayColumns", dateColumns);
        parameterMap.put("prefix", prefix);
        parameterMap.put("historyColumns", historyColumns);
        parameterMap.put("fcstValueColumn", fcstValueColumn);
        parameterMap.put("amuColumn", amuColumn);
        parameterMap.put("amfColumn", amfColumn);

        calendar.add(Calendar.YEAR, -1);
        calendar.set(Calendar.MONTH, Calendar.JANUARY);
        for (int i = 0; i < 36; i++) {
            allMonth.add(format.format(calendar.getTime()));
            calendar.add(Calendar.MONTH, 1);
        }

        String lastMonth = allMonth.get(allMonth.size() - 1);

        if (StringUtils.equals(dataScope, "Full FCST")) {
            // Full FCST, 先放History的部分
            for (String month : allMonth) {
                if (fcstVersion.compareTo(month) > 0) {
                    tableColumns.add(prefix + month);
                    dateColumns.add(month);
                    historyColumns.add(month);
                } else {
                    break;
                }
            }
            // 再放FCST, 最多放到明年的最后一个月
            for (int gap = 1; gap <= 25; gap++) {
                if (lastMonth.compareTo(DateCalUtil.addMonth(fcstVersion, gap)) < 0) {
                    break;
                }
                String fcstColumn = gap < 10 ? "MONTH0" + gap : "MONTH" + gap;
                tableColumns.add(fcstColumn);

                String display = DateCalUtil.addMonth(fcstVersion, gap - 1);
                dateColumns.add(display);
                fcstColumns.add(display);

                pipelineColumns.add("PIPE_" + fcstColumn);
                pipelineNames.add("PIPE_" + display);
            }

        } else {
            // Full History, 先放History的部分
            for (String month : allMonth) {
                if (currentMonth.compareTo(month) > 0) {
                    tableColumns.add(prefix + month);
                    dateColumns.add(month);
                    historyColumns.add(month);
                } else {
                    break;
                }
            }
            // 再放FCST
            int gap = DateCalUtil.calcMonthGap(Utils.parseInt(fcstVersion), Utils.parseInt(currentMonth)) + 1;
            for (; gap <= 25; gap++) {
                String display = DateCalUtil.addMonth(fcstVersion, gap - 1);
                if (lastMonth.compareTo(display) < 0) {
                    break;
                }
                String fcstColumn = gap < 10 ? "MONTH0" + gap : "MONTH" + gap;
                tableColumns.add(fcstColumn);

                dateColumns.add(display);
                fcstColumns.add(display);

                pipelineColumns.add("PIPE_" + fcstColumn);
                pipelineNames.add("PIPE_" + display);
            }
        }

        resultMap.put("HIST_COLUMNS", historyColumns);
        resultMap.put("FCST_COLUMNS", fcstColumns);
        resultMap.put("DATE_COLUMNS", dateColumns);

        return resultMap;
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1Columns(Map<String, Object> parameterMap) {
        return response.setBody(this.generateReport1Columns(parameterMap));
    }

    @Override
    @SuppressWarnings("unchecked")
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);

        this.generateFilter(parameterMap);
        this.generateReport1Columns(parameterMap);
        List<String> groupColumns = (List<String>) parameterMap.get("report1GroupColumns");
        // 计算CURRENT_YGR = Year / (Year - 1) - 1
        List<String> tableColumns = (List<String>) parameterMap.get("tableColumns");
        String firstYear = StringUtils.join(tableColumns.stream()
                .skip(0).limit(12)
                .map(n -> "NVL(" + n + ",0)")
                .collect(Collectors.toList()), " + ");
        String lastYear = StringUtils.join(tableColumns.stream()
                .skip(12).limit(12)
                .map(n -> "NVL(" + n + ",0)")
                .collect(Collectors.toList()), " + ");
        String currentYgr = StringUtils.join("(", firstYear, ")", " / ", "DECODE ((", lastYear, "), 0, NULL, " + lastYear + ") - 1 AS CURRENT_YGR");
        parameterMap.put("currentYgr", currentYgr);
        if (groupColumns.isEmpty()) {
            groupColumns.add("CLUSTER_NAME");
            groupColumns.add("ENTITY");
            parameterMap.put("report1GroupColumns", groupColumns);
        }

        String dataType = (String) parameterMap.get("dataType");
        switch (dataType) {
            case "ORDER_INTAKE" -> parameterMap.put("tableName", "DEMAND_EVOLUTION_ORDER_INTAKE_V");
            case "SALES" -> parameterMap.put("tableName", "DEMAND_EVOLUTION_SALES_V");
            case "CRD" -> parameterMap.put("tableName", "DEMAND_EVOLUTION_CRD_V");
            default -> {
                return response.setBody(page);
            }
        }

        page.setTotal(evolutionDao.queryReport1Count(parameterMap));
        if (page.getTotal() > 0) {
            List<LinkedHashMap<String, Object>> resultList = evolutionDao.queryReport1(parameterMap);
            page.setData(resultList);
        }

        return response.setBody(page);
    }

    @Override
    @SuppressWarnings("unchecked")
    public void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse response) {
        this.generateFilter(parameterMap);
        this.generateReport1Columns(parameterMap);
        List<String> groupColumns = (List<String>) parameterMap.get("report1GroupColumns");
        // 计算CURRENT_YGR = Year / (Year - 1) - 1
        List<String> tableColumns = (List<String>) parameterMap.get("tableColumns");
        String firstYear = StringUtils.join(tableColumns.stream()
                .skip(0).limit(12)
                .map(n -> "NVL(" + n + ",0)")
                .collect(Collectors.toList()), " + ");
        String lastYear = StringUtils.join(tableColumns.stream()
                .skip(12).limit(12)
                .map(n -> "NVL(" + n + ",0)")
                .collect(Collectors.toList()), " + ");
        String currentYgr = StringUtils.join("(", firstYear, ")", " / ", "DECODE ((", lastYear, "), 0, NULL, " + lastYear + ") - 1 AS CURRENT_YGR");
        parameterMap.put("currentYgr", currentYgr);
        if (groupColumns.isEmpty()) {
            groupColumns.add("CLUSTER_NAME");
            groupColumns.add("ENTITY");
            parameterMap.put("report1GroupColumns", groupColumns);
        }

        String dataType = (String) parameterMap.get("dataType");
        switch (dataType) {
            case "ORDER_INTAKE" -> parameterMap.put("tableName", "DEMAND_EVOLUTION_ORDER_INTAKE_V");
            case "SALES" -> parameterMap.put("tableName", "DEMAND_EVOLUTION_SALES_V");
            case "CRD" -> parameterMap.put("tableName", "DEMAND_EVOLUTION_CRD_V");
            default -> {
                return;
            }
        }

        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "demand_evolution_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.demand.dao.IEvolutionDao.queryReport1", parameterMap);

    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1Details(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);

        this.generateFilter(parameterMap);
        this.generateReport1Columns(parameterMap);

        String tableName;
        String dataType = (String) parameterMap.get("dataType");
        String detailsType = (String) parameterMap.get("detailsType");

        String fcstVersion = (String) parameterMap.get("fcstVersion");
        String detailsMonth = (String) parameterMap.get("detailsMonth");
        int gap = DateCalUtil.calcMonthGap(Utils.parseInt(fcstVersion), Utils.parseInt(detailsMonth));
        parameterMap.put("monthColumn", "MONTH" + ((gap < 10) ? "0" + gap : gap));

        if ("FCST".equals(detailsType)) {
            tableName = "DEMAND_FCST_V";
        } else {
            switch (dataType) {
                case "ORDER_INTAKE" -> tableName = "DEMAND_ORDER_INTAKE_V";
                case "SALES" -> tableName = "DEMAND_SALES_V";
                case "CRD" -> tableName = "DEMAND_CRD_V";
                default -> {
                    return response.setBody(page);
                }
            }
        }

        parameterMap.put("tableName", tableName);
        if ("CURRENT_MONTH".equals(detailsType)) {
            parameterMap.put("detailsMonth", new SimpleDateFormat("yyyyMM").format(new Date()));
        } else if ("ALL".equals(detailsType)) {
            parameterMap.remove("detailsMonth");
        }

        page.setTotal(evolutionDao.queryReport1DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            List<LinkedHashMap<String, Object>> resultList = evolutionDao.queryReport1Details(parameterMap);
            page.setData(resultList);
        }

        return response.setBody(page);
    }

    @Override
    public void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);

        this.generateFilter(parameterMap);
        this.generateReport1Columns(parameterMap);

        String tableName;
        String dataType = (String) parameterMap.get("dataType");
        String detailsType = (String) parameterMap.get("detailsType");

        String fcstVersion = (String) parameterMap.get("fcstVersion");
        String detailsMonth = (String) parameterMap.get("detailsMonth");
        int gap = DateCalUtil.calcMonthGap(Utils.parseInt(fcstVersion), Utils.parseInt(detailsMonth));
        parameterMap.put("monthColumn", "MONTH" + ((gap < 10) ? "0" + gap : gap));

        if ("FCST".equals(detailsType)) {
            tableName = "DEMAND_FCST_V";
        } else {
            switch (dataType) {
                case "ORDER_INTAKE" -> tableName = "DEMAND_ORDER_INTAKE_V";
                case "SALES" -> tableName = "DEMAND_SALES_V";
                case "CRD" -> tableName = "DEMAND_CRD_V";
                default -> {
                    return;
                }
            }
        }

        parameterMap.put("tableName", tableName);
        if ("CURRENT_MONTH".equals(detailsType)) {
            parameterMap.put("detailsMonth", new SimpleDateFormat("yyyyMM").format(new Date()));
        } else if ("ALL".equals(detailsType)) {
            parameterMap.remove("detailsMonth");
        }

        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "demand_evolution_details_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.demand.dao.IEvolutionDao.queryReport1Details", parameterMap);
    }

    @Override
    @SuppressWarnings("unchecked")
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1Pipeline(Map<String, Object> parameterMap) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);

        this.generateFilter(parameterMap);

        List<String> groupColumns = (List<String>) parameterMap.get("report1GroupColumns");
        if (groupColumns.isEmpty()) {
            groupColumns.add("CLUSTER_NAME");
            groupColumns.add("ENTITY");
            parameterMap.put("report1GroupColumns", groupColumns);
        }

        String pipelineMonth = (String) parameterMap.get("pipelineMonth");
        String fcstVersion = (String) parameterMap.get("fcstVersion");
        int gap = DateCalUtil.calcMonthGap(Utils.parseInt(fcstVersion), Utils.parseInt(pipelineMonth)) + 1;
        parameterMap.put("pipeMonth", "MONTH" + (gap < 10 ? "0" + gap : "" + gap));
        page.setTotal(evolutionDao.queryReport1PipelineCount(parameterMap));
        if (page.getTotal() > 0) {
            List<LinkedHashMap<String, Object>> resultList = evolutionDao.queryReport1Pipeline(parameterMap);
            page.setData(resultList);
        }

        return response.setBody(page);
    }

    @Override
    @SuppressWarnings("unchecked")
    public void downloadReport1Pipeline(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);

        this.generateFilter(parameterMap);

        List<String> groupColumns = (List<String>) parameterMap.get("report1GroupColumns");
        if (groupColumns.isEmpty()) {
            groupColumns.add("CLUSTER_NAME");
            groupColumns.add("ENTITY");
            parameterMap.put("report1GroupColumns", groupColumns);
        }

        String pipelineMonth = (String) parameterMap.get("pipelineMonth");
        String fcstVersion = (String) parameterMap.get("fcstVersion");
        int gap = DateCalUtil.calcMonthGap(Utils.parseInt(fcstVersion), Utils.parseInt(pipelineMonth)) + 1;
        parameterMap.put("pipeMonth", "MONTH" + (gap < 10 ? "0" + gap : "" + gap));

        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        String fileName = "demand_evolution_pipeline_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.demand.dao.IEvolutionDao.queryReport1Pipeline", parameterMap);
    }

    private void generateFilter(Map<String, Object> parameterMap) {
        // 生成筛选条件
        JSONArray filterArray = (JSONArray) parameterMap.get("filterList");

        if (filterArray != null) {
            Map<String, List<String>> filterMap = new HashMap<>();
            Map<String, List<String>> valueMap = new HashMap<>();

            for (Object subObj : filterArray) {
                JSONArray subArray = (JSONArray) subObj;
                String columnName = subArray.getString(0);

                String value = subArray.getString(1);
                String key = Utils.randomStr(8);

                if (Utils.hasInjectionAttack(columnName)) {
                    continue;
                }

                List<String> fl = filterMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                fl.add("#{" + key + ",jdbcType=VARCHAR}");
                List<String> fv = valueMap.computeIfAbsent(columnName, k -> new ArrayList<>());
                fv.add(value);
                parameterMap.put(key, value);
            }

            List<String> filterList = new ArrayList<>();
            List<String> pipeLineFilterList = new ArrayList<>();

            List<String> pipeLineExcludeFilter = new ArrayList<>();
            pipeLineExcludeFilter.add("ITEM_CATEGORY");
            pipeLineExcludeFilter.add("SALES_GROUP");
            pipeLineExcludeFilter.add("SALES_ORGANIZATION");
            pipeLineExcludeFilter.add("SHIPPING_POINT");
            pipeLineExcludeFilter.add("SHIP_TO_REGION");

            for (String key : filterMap.keySet()) {
                List<String> fl = filterMap.get(key);
                List<String> fv = valueMap.get(key);
                if (fv.contains("Others")) {
                    filterList.add("(t." + key + " in (" + StringUtils.join(fl, ",") + ") or t." + key + " is null)");
                } else {
                    filterList.add("t." + key + " in (" + StringUtils.join(fl, ",") + ")");
                }

                if (pipeLineExcludeFilter.contains(key) == true) {
                    pipeLineFilterList.add("t." + key + " in (" + StringUtils.join(fl, ",") + ")");
                }
            }

            parameterMap.put("filters", StringUtils.join(filterList, " and "));
            parameterMap.put("pipeLineFilterList", StringUtils.join(pipeLineFilterList, " and "));
        }

        // load special parameter
        String specialContent = (String) parameterMap.get("specialContent");
        String specialColumn = (String) parameterMap.get("specialType");
        if (Utils.hasInjectionAttack(specialColumn) == false) {
            if (StringUtils.isNotBlank(specialContent)) {
                parameterMap.put("specialList", Utils.splitValue(specialContent));
                parameterMap.put("specialColumn", specialColumn);
            }
        }
    }
}
