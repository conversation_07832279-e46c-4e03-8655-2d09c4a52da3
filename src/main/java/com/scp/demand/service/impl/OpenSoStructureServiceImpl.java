package com.scp.demand.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.scp.demand.bean.OpenSOReport2Bean;
import com.scp.demand.bean.OpenSOReport2Treemap;
import com.scp.demand.dao.IOpenSoStructureDao;
import com.scp.demand.service.IOpenSoStructureService;
import com.starter.context.bean.Configuration;
import com.starter.context.bean.Response;
import com.starter.context.bean.SimplePage;
import com.starter.context.servlet.ServiceHelper;
import com.starter.login.bean.Session;
import com.starter.utils.AuthUtils;
import com.starter.utils.Utils;
import com.starter.utils.excel.ExcelTemplate;
import com.scp.demand.bean.OpenSOReport4Bean;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service("openSoStructureServiceImpl")
@Scope("prototype")
@Transactional
public class OpenSoStructureServiceImpl extends ServiceHelper implements IOpenSoStructureService {

    @Resource
    private Response response;

    @Resource
    private IOpenSoStructureDao openSoStructureDao;

    public static final String SO_PARENT_CODE = "menu1E0";

    @Resource
    private ExcelTemplate excelTemplate;

    private final HashMap<String, String> tooltip2ColumnMap = new HashMap<>();
    private final HashMap<String, String> column2TooltipMap = new HashMap<>();

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response initPage() {
        Map<String, Object> result = new HashMap<>();
        result.put("CASCADER", Utils.parseCascader(openSoStructureDao.queryCascader()));
        result.put("dateColumns", openSoStructureDao.queryDateColumns());

        return response.setBody(result);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport1(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);
        String valueType = (String) parameterMap.get("valueType");
        String orderType = (String) parameterMap.get("orderType");

        String qtyColumn = "OPEN_SO_QTY";
        if ("OPEN_SO_W_O_GI".equals(orderType)) {
            qtyColumn = "OPEN_SO_QTY";
        } else if ("OPEN_SO_W_O_DEL".equals(orderType)) {
            qtyColumn = "(OPEN_SO_QTY - OPEN_UD_QTY)";
        }

        String valueColumn = "NET_NET_VALUE_RMB";
        if ("Net Net Price".equalsIgnoreCase(valueType)) {
            valueColumn = "(AVG_SELLING_PRICE_RMB * " + qtyColumn + ")";
        } else if ("Quantity".equalsIgnoreCase(valueType)) {
            valueColumn = "(" + qtyColumn + ")";
        } else if ("Line".equalsIgnoreCase(valueType)) {
            valueColumn = "1";
        } else if ("Net Price".equalsIgnoreCase(valueType)) {
            valueColumn = "(NET_PRICE * " + qtyColumn + ")";
        } else if ("Net Net Price HKD".equalsIgnoreCase(valueType)) {
            valueColumn = "(AVG_SELLING_PRICE_HKD * " + qtyColumn + ")";
        } else if ("Weight".equalsIgnoreCase(valueType)) {
            valueColumn = "(" + "GROSS_WEIGHT_IN_KG" + ")";
        }
        parameterMap.put("valueColumn", valueColumn);

        return response.setBody(openSoStructureDao.queryReport1(parameterMap));
    }

    @Override
    public Response queryReport1Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(openSoStructureDao.queryReport1DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(openSoStructureDao.queryReport1Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);

        String fileName = "open_so_structure_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.demand.dao.IOpenSoStructureDao.queryReport1Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport2(Map<String, Object> parameterMap) throws Exception {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateReport2Tooltips(parameterMap);

        // 将前台传过来的label转换成列名, 同时也可以防止恶意代码注入
        parameterMap.put("level1", this.getColumnNameByLabel(parameterMap.get("level1")));
        parameterMap.put("level2", this.getColumnNameByLabel(parameterMap.get("level2")));
        parameterMap.put("level3", this.getColumnNameByLabel(parameterMap.get("level3")));
        parameterMap.put("level4", this.getColumnNameByLabel(parameterMap.get("level4")));
        parameterMap.put("level5", this.getColumnNameByLabel(parameterMap.get("level5")));

        List<OpenSOReport2Treemap> resultList = new ArrayList<>();
        List<OpenSOReport2Bean> dataList = openSoStructureDao.queryReport2(parameterMap);
        for (OpenSOReport2Bean data : dataList) {
            this.convertReport1Data(resultList, data);
        }
        return response.setBody(resultList);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);
        return response.setBody(openSoStructureDao.queryReport3(parameterMap));
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport3Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(openSoStructureDao.queryReport3DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(openSoStructureDao.queryReport3Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);

        String fileName = "open_so_structure_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.demand.dao.IOpenSoStructureDao.queryReport3Details", parameterMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport4(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);

        List<OpenSOReport4Bean> dataList;

        String report4DateType = (String) parameterMap.get("report4DateType");

        if ("BY_DATE".equals(report4DateType)) {
            dataList = openSoStructureDao.queryReport4(parameterMap);
        } else if ("BY_DUEDATE".equals(report4DateType)) {
            dataList = openSoStructureDao.queryReport4DueDate(parameterMap);
        } else if ("BY_CONFIRMDATE".equals(report4DateType)) {
            dataList = openSoStructureDao.queryReport4ConfirmDate(parameterMap);
        } else if ("BY_ORI_PLANNED_FIRST_GI_DATE".equals(report4DateType)) {
            dataList = openSoStructureDao.queryReport4PlannedFirstGIDate(parameterMap);
        } else {
            dataList = openSoStructureDao.queryReport4(parameterMap);
        }

        Map<String, BigDecimal> dataMap = new HashMap<>();
        LinkedHashMap<String, Object> resultMap = new LinkedHashMap<>();
        Map<String, String> xAxisMap = new HashMap<>();

        List<String> legend = openSoStructureDao.queryReport4Legend(parameterMap);

        for (OpenSOReport4Bean data : dataList) {
            dataMap.put(data.getKey(), data.getVALUE());
            xAxisMap.put(data.getCALENDAR_DATE(), "");
        }
        List<String> xAxisList = xAxisMap.keySet().stream().sorted(String::compareTo).collect(Collectors.toList());
        for (String l : legend) {
            List<BigDecimal> temp = new ArrayList<>();

            for (String x : xAxisList) {
                temp.add(dataMap.getOrDefault(l + "#" + x, BigDecimal.ZERO));
            }
            resultMap.put(l, temp);
        }

        resultMap.put("xAxis", xAxisList);
        return response.setBody(resultMap);
    }

    @Override
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport4Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(openSoStructureDao.queryReport4DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(openSoStructureDao.queryReport4Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport4Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);

        String fileName = "open_so_structure_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.demand.dao.IOpenSoStructureDao.queryReport4Details", parameterMap);
    }

    @Override
    public Response queryReport5Columns(Map<String, Object> parameterMap) {
        return response.setBody(openSoStructureDao.queryReport5Columns(parameterMap));
    }

    @Override
    @SuppressWarnings("unchecked")
    @Cacheable(Configuration.APPLICATION_NAME + ":1d")
    public Response queryReport5(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateValueColumn(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        List<String> columns = (List<String>) parameterMap.get("report5Columns");
        for (int i = 0; i < columns.size(); i++) {
            columns.set(i, this.getColumnNameByLabel(columns.get(i)));
        }

        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);

        String report4DateType = (String) parameterMap.get("report4DateType");
        List<LinkedHashMap<String, Object>> dataList;
        if ("BY_DATE".equals(report4DateType)) {
            dataList = openSoStructureDao.queryReport5(parameterMap);
        } else if ("BY_DUEDATE".equals(report4DateType)) {
            dataList = openSoStructureDao.queryReport5DueDate(parameterMap);
        } else if ("BY_CONFIRMDATE".equals(report4DateType)) {
            dataList = openSoStructureDao.queryReport5ConfirmDate(parameterMap);
        } else if ("BY_ORI_PLANNED_FIRST_GI_DATE".equals(report4DateType)) {
            dataList = openSoStructureDao.queryReport5PlannedFirstGIDate(parameterMap);
        } else {
            dataList = openSoStructureDao.queryReport5(parameterMap);
        }

        page.setData(dataList);
        return response.setBody(page);
    }

    @Override
    public Response queryReport5Details(Map<String, Object> parameterMap) {
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);

        SimplePage<LinkedHashMap<String, Object>> page = new SimplePage<>(parameterMap);
        page.setTotal(openSoStructureDao.queryReport5DetailsCount(parameterMap));
        if (page.getTotal() > 0) {
            page.setData(openSoStructureDao.queryReport5Details(parameterMap));
        }
        return response.setBody(page);
    }

    @Override
    public void downloadReport5Details(Map<String, Object> parameterMap, HttpServletResponse response) {
        SimplePage<Map<String, Object>> page = new SimplePage<>(parameterMap);
        page.setStart(0);
        page.setLength(SimplePage.PAGE_MAX);
        this.generateFilter(parameterMap);
        this.generateTreePathFilter(parameterMap);

        String fileName = "open_so_structure_" + Utils.randomStr(4) + ".xlsx";
        excelTemplate.create(response, fileName, "com.scp.demand.dao.IOpenSoStructureDao.queryReport5Details", parameterMap);
    }


    /**
     * 将列表转化为Tree数据
     *
     * @param list 输出树
     * @param data 输入值
     * @throws Exception 异常
     */
    private void convertReport1Data(List<OpenSOReport2Treemap> list, OpenSOReport2Bean data) throws Exception {
        String[] categorysOrg = new String[]{data.getCategory1(), data.getCategory2(), data.getCategory3(), data.getCategory4(), data.getCategory5()};
        List<String> categories = new ArrayList<>();

        for (String category : categorysOrg) {
            if (StringUtils.isNotBlank(category)) {
                categories.add(category);
            } else {
                break;
            }
        }

        // 这边逻辑比较复杂, 所以用最笨的方法来描述了, 以免后期不好维护
        // 先把这一行数据转成treemap的数据
        // 第一个节点
        List<OpenSOReport2Treemap> child = new ArrayList<>();
        OpenSOReport2Treemap root = new OpenSOReport2Treemap();
        root.setName(categories.get(0));
        root.setTips(data.copyTooltips()); // 因为这个tooltips要放在树中全局使用, 所以必须要生成一个新节点
        root.setChildren(child);

        // 中间节点
        for (int i = 1; i < categories.size() - 1; i++) {
            OpenSOReport2Treemap treemap = new OpenSOReport2Treemap();
            treemap.setName(categories.get(i));
            treemap.setTips(data.copyTooltips());

            child.add(treemap);
            child = new ArrayList<>();
            treemap.setChildren(child);
        }

        // 最后一个节点
        OpenSOReport2Treemap lastNode = new OpenSOReport2Treemap();
        lastNode.setName(categories.get(categories.size() - 1));
        lastNode.setValue(data.getValue());
        lastNode.setTips(data.copyTooltips());
        child.add(lastNode);

        // 将这行treemap与原始数据相加
        // 先找到list中是否有这个数据节点
        Optional<OpenSOReport2Treemap> beanOpt = list.stream().filter(b -> b.getName().equals(categories.get(0))).findFirst();
        if (beanOpt.isPresent()) {
            OpenSOReport2Treemap bean = beanOpt.get();
            bean.add(root); // 两个节点合并
        } else { //找不到的时候最省事, 直接放入list就可以了
            list.add(root);
        }
    }

    private void generateValueColumn(Map<String, Object> parameterMap) {
        String valueType = (String) parameterMap.get("valueType");
        String orderType = (String) parameterMap.get("orderType");

        String qtyColumn = "OPEN_SO_QTY";
        if ("OPEN_SO_W_O_GI".equals(orderType)) {
            qtyColumn = "OPEN_SO_QTY";
        } else if ("OPEN_SO_W_O_DEL".equals(orderType)) {
            qtyColumn = "(OPEN_SO_QTY - OPEN_UD_QTY)";
        }

        String valueColumn = "NET_NET_VALUE_RMB";
        if ("Net Net Price".equalsIgnoreCase(valueType)) {
            valueColumn = "SUM(AVG_SELLING_PRICE_RMB * " + qtyColumn + ")";
        } else if ("Quantity".equalsIgnoreCase(valueType)) {
            valueColumn = "SUM(" + qtyColumn + ")";
        } else if ("Line".equalsIgnoreCase(valueType)) {
            valueColumn = "COUNT(1)";
        } else if ("Net Price".equalsIgnoreCase(valueType)) {
            valueColumn = "SUM(NET_PRICE * " + qtyColumn + ")";
        } else if ("Net Net Price HKD".equalsIgnoreCase(valueType)) {
            valueColumn = "SUM(AVG_SELLING_PRICE_HKD * " + qtyColumn + ")";
        } else if ("Weight".equalsIgnoreCase(valueType)) {
            valueColumn = "SUM(GROSS_WEIGHT_IN_KG)";
        }
        parameterMap.put("valueColumn", valueColumn);
    }

    @SuppressWarnings("unchecked")
    private void generateFilter(Map<String, Object> parameterMap) {
        this.generateCascaderFilterSQL(parameterMap);

        String scope = (String) parameterMap.get("scope");
        List<String> subScope = (List<String>) parameterMap.get("subScope");
        if (StringUtils.isNotBlank(scope)) {
            String scopeColumnName = scope.equals("SECI") ? "SE_SCOPE" : "PLANT_SCOPE";
            if (subScope == null || subScope.isEmpty()) {
                parameterMap.put("scopeFilter", scopeColumnName + " IS NOT NULL");
            } else {
                List<String> scList = new ArrayList<>();
                for (String s : subScope) {
                    String key = Utils.randomStr(8);
                    parameterMap.put(key, s);
                    scList.add("#{" + key + ", jdbcType=VARCHAR}");
                }
                parameterMap.put("scopeFilter", scopeColumnName + " in (" + StringUtils.join(scList, ",") + ")");
            }
        }


        Session session = (Session) parameterMap.get("session");
        parameterMap.putAll(AuthUtils.generateConditions(session, SO_PARENT_CODE, "personalFilters"));
    }

    private void generateTreePathFilter(Map<String, Object> parameterMap) {
        String selectedTreePath = (String) parameterMap.get("selectedTreePath");
        if (StringUtils.isNotBlank(selectedTreePath)) {
            List<String> conditions = new ArrayList<>();
            String[] treePaths = selectedTreePath.split(" > ");
            for (int i = 1; i <= Math.min(treePaths.length, 5); i++) {
                String key = Utils.randomStr(8);
                if ("Others".equals(StringUtils.trim(treePaths[i - 1]))) {
                    String name = this.getColumnNameByLabel(parameterMap.get("level" + i));
                    conditions.add("(" + name + " = #{" + key + ",jdbcType=VARCHAR} or " + name + " is null )");
                } else {
                    conditions.add(this.getColumnNameByLabel(parameterMap.get("level" + i)) + " = #{" + key + ",jdbcType=VARCHAR}");
                }
                parameterMap.put(key, StringUtils.trim(treePaths[i - 1]));
            }

            parameterMap.put("treePathFilter", "(" + StringUtils.join(conditions, " and ") + ")");
        }
    }

    private void generateReport2Tooltips(Map<String, Object> parameterMap) {
        String resultType = (String) parameterMap.get("resultType");
        List<String> tooltips = ((JSONArray) parameterMap.get("report2Tooltips")).toJavaList(String.class);
        if (!tooltips.isEmpty()) {
            List<String> tooltipsColumns = tooltips.stream().map(
                    this::getColumnNameByLabel).collect(Collectors.toList());
            List<String> tooltipsColumnsName = new ArrayList<>();
            List<String> polymorphicColumns = new ArrayList<>(Arrays.asList("PO_AB", "PO_LA", "PO_NON_ABLA"));

            for (String c : tooltipsColumns) {
                String tooltip = column2TooltipMap.getOrDefault(c, c);
                if (polymorphicColumns.contains(c)) {
                    if ("Quantity".equalsIgnoreCase(resultType)) {
                        tooltipsColumnsName.add("NVL(SUM(" + c + "),0) AS " + tooltip);
                    } else if ("Value".equalsIgnoreCase(resultType)) {
                        tooltipsColumnsName.add("NVL(SUM(" + c + "_VALUE),0) AS " + tooltip);
                    } else if ("Line".equalsIgnoreCase(resultType)) {
                        tooltipsColumnsName.add("NVL(SUM(CASE WHEN " + c + " > 0 THEN 1 ELSE 0 END),0) AS " + tooltip);
                    }
                } else {
                    tooltipsColumnsName.add("NVL(AVG(" + c + "),0) AS " + tooltip);
                }
            }
            parameterMap.put("tooltipsColumns", StringUtils.join(tooltipsColumnsName, ", "));
        }
    }

    private String getColumnNameByLabel(Object labelObj) {
        String label = (String) labelObj;
        if (label == null) {
            return null;
        }
        if (Utils.hasInjectionAttack(label)) {
            return "";
        }
        if ("SALES_ORDER_NUMBER_ITEM".equalsIgnoreCase(label)) {
            return "SALES_ORDER_NUMBER || ' ' || SALES_ORDER_ITEM";
        }
        return tooltip2ColumnMap.getOrDefault(label, label);
    }
}
