package com.scp.demand.service;

import com.starter.context.bean.Response;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;

public interface IAbnormalDemandService {

    Response initPage();

    Response queryReportColumns(Map<String, Object> parameterMap);

    Response queryReport(Map<String, Object> parameterMap);

    void downloadReport(Map<String, Object> parameterMap, HttpServletResponse response);
}
