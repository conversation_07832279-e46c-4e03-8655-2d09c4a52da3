package com.scp.demand.service;

import com.starter.context.bean.Response;

import java.util.Map;

public interface IPipelineService {

    Response querySubmissionDate();

    Response queryUpdateBy(Map<String, Object> parameterMap);

    Response queryProductLine(Map<String, Object> parameterMap);

    Response queryProjectStatus(Map<String, Object> parameterMap);

    Response querySalesTeam(Map<String, Object> parameterMap);

    Response querySalesPerson(Map<String, Object> parameterMap);

    Response queryReport1(Map<String, Object> parameterMap);

    Response saveReport1Comments(Map<String, Object> parameterMap);

    Response queryReport1ColumnsComments(Map<String, Object> parameterMap);

    Response queryReport1Details(Map<String, Object> parameterMap);

    Response queryReport1Source(Map<String, Object> parameterMap);

    Response saveReport1Source(Map<String, Object> parameterMap);

    Response queryReport2(Map<String, Object> parameterMap);

    Response queryReport2Details(Map<String, Object> parameterMap);

    Response queryReport2Source(Map<String, Object> parameterMap);

    Response queryReport3(Map<String, Object> parameterMap);

    Response queryReport3Details(Map<String, Object> parameterMap);

    Response queryReport4(Map<String, Object> parameterMap);

    Response queryReport5(Map<String, Object> parameterMap);

    Response queryReport5Details(Map<String, Object> parameterMap);

    Response queryInvalidPipelineData(Map<String, Object> parameterMap);

    Response queryReport6(Map<String, Object> parameterMap);

    Response queryReport6Details(Map<String, Object> parameterMap);
}
