package com.scp.demand.service;

import com.starter.context.bean.Response;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;

public interface IWaterfallService {

    Response queryCascader();

    Response queryReport(Map<String, Object> parameterMap);

    Response queryReport2(Map<String, Object> parameterMap);

    Response queryReport2Details(Map<String, Object> parameterMap);

    void downloadReport2Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport3(Map<String, Object> parameterMap);
}
