package com.scp.demand.service;

import com.starter.context.bean.Response;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;

public interface ITrackingService {

    Response initPage();

    Response queryReport1(Map<String, Object> parameterMap);

    void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport2(Map<String, Object> parameterMap);

    Response queryReport1Sub(Map<String, Object> parameterMap);

    Response queryReport1Details(Map<String, Object> parameterMap);

    void downloadData(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport1FCSTDetails(Map<String, Object> parameterMap);

    void downloadFCSTData(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport3(Map<String, Object> parameterMap);

    Response queryReport4(Map<String, Object> parameterMap);

    Response queryReport2Details(Map<String, Object> parameterMap);

    void downloadData2(Map<String, Object> parameterMap, HttpServletResponse response);
}
