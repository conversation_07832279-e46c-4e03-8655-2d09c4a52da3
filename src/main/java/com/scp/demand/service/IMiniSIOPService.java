package com.scp.demand.service;

import com.starter.context.bean.Response;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;

public interface IMiniSIOPService {

    Response intiPage();

    Response queryReport1(Map<String, Object> parameterMap);

    Response queryReport1Details(Map<String, Object> parameterMap);

    void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse response);

    void downloadReport1ForMiniSIOPMeeting(Map<String, Object> parameterMap, HttpServletResponse response);
}
