package com.scp.demand.service;

import com.starter.context.bean.Response;
import jakarta.servlet.http.HttpServletResponse;

import java.util.Map;

public interface IPmsStructureService {

    Response queryFilters(Map<String, Object> parameterMap);

    Response queryReport1(Map<String, Object> parameterMap) throws Exception;

    Response queryReport2(Map<String, Object> parameterMap);

    Response queryReport2Details(Map<String, Object> parameterMap);

    void downloadReport2Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport3(Map<String, Object> parameterMap);

    Response queryReport3Columns(Map<String, Object> parameterMap);

    Response queryReport3Details(Map<String, Object> parameterMap);

    void downloadReport3Details(Map<String, Object> parameterMap, HttpServletResponse res);

    void downloadReport2DisplayData(Map<String, Object> parameterMap, HttpServletResponse res);

}
