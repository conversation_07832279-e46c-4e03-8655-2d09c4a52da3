package com.scp.demand.service;

import com.starter.context.bean.Response;

import jakarta.servlet.http.HttpServletResponse;
import java.util.Map;

public interface IEvolutionService {

    Response initPage(Map<String, Object> parameterMap);

    Response queryReport1Columns(Map<String, Object> parameterMap);

    Response queryReport1(Map<String, Object> parameterMap);

    void downloadReport1(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport1Details(Map<String, Object> parameterMap);

    void downloadReport1Details(Map<String, Object> parameterMap, HttpServletResponse response);

    Response queryReport1Pipeline(Map<String, Object> parameterMap);

    void downloadReport1Pipeline(Map<String, Object> parameterMap, HttpServletResponse response);
}
