package com.scp.demand;

import com.scp.demand.service.IOpenPmsStructureService;
import com.scp.demand.service.impl.OpenPmsStructureServiceImpl;
import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RestController;

@RestController
@CrossOrigin
@Scope("prototype")
@SchneiderRequestMapping(value = "/demand/open_pms_structure", parent = OpenPmsStructureServiceImpl.PARENT_CODE)
public class OpenPmsStructureController extends ControllerHelper {

    @Resource
    private Response res;

    @Resource
    private IOpenPmsStructureService openPmsStructureService;

    @SchneiderRequestMapping("/init_page")
    public Response initPage() {
        return openPmsStructureService.initPage();
    }

    @SchneiderRequestMapping("/query_report2")
    public Response queryReport2(HttpServletRequest request) {
        super.pageLoad(request);
        try {
            return openPmsStructureService.queryReport2(parameterMap);
        } catch (Exception e) {
            return res.setError(e);
        }
    }

    @SchneiderRequestMapping("/query_report3")
    public Response queryReport3(HttpServletRequest request) {
        super.pageLoad(request);
        return openPmsStructureService.queryReport3(parameterMap);
    }

    @SchneiderRequestMapping("/query_report3_details")
    public Response queryReport3Details(HttpServletRequest request) {
        super.pageLoad(request);
        return openPmsStructureService.queryReport3Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report3_details")
    public void downloadReport3Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        openPmsStructureService.downloadReport3Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report4")
    public Response queryReport4(HttpServletRequest request) {
        super.pageLoad(request);
        return openPmsStructureService.queryReport4(parameterMap);
    }

    @SchneiderRequestMapping("/query_report4_details")
    public Response queryReport4Details(HttpServletRequest request) {
        super.pageLoad(request);
        return openPmsStructureService.queryReport4Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report4_details")
    public void downloadReport4Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        openPmsStructureService.downloadReport4Details(parameterMap, response);
    }

    @SchneiderRequestMapping("/query_report5_columns")
    public Response queryReport5Columns(HttpServletRequest request) {
        super.pageLoad(request);
        return openPmsStructureService.queryReport5Columns(parameterMap);
    }

    @SchneiderRequestMapping("/query_report5")
    public Response queryReport5(HttpServletRequest request) {
        super.pageLoad(request);
        return openPmsStructureService.queryReport5(parameterMap);
    }

    @SchneiderRequestMapping("/query_report5_details")
    public Response queryReport5Details(HttpServletRequest request) {
        super.pageLoad(request);
        return openPmsStructureService.queryReport5Details(parameterMap);
    }

    @SchneiderRequestMapping("/download_report5_details")
    public void downloadReport5Details(HttpServletRequest request, HttpServletResponse response) {
        super.pageLoad(request);
        openPmsStructureService.downloadReport5Details(parameterMap, response);
    }
}
