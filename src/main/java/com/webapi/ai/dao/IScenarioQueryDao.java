package com.webapi.ai.dao;

import com.alibaba.fastjson.JSONArray;
import org.apache.ibatis.annotations.Mapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Mapper
public interface IScenarioQueryDao {
    List<LinkedHashMap<String, Object>> queryMaterialOwnerByMaterial(Map<String, Object> parameterMap);

    List<String> queryValidPlantCodes(List<String> plantCodes);

    List<String> queryValidProductLines(List<String> productLines);

    List<String> queryValidMaterials(List<String> materials);

    List<LinkedHashMap<String, Object>> queryOrderIntakeByMaterial(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryBackOrderByMaterial(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryBacklogByMaterial(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> querySohByMaterial(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryDeliveryByMaterial(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryPoGrByMaterial(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryPoLaByMaterial(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryMoOutputByMaterial(Map<String, Object> parameterMap);

    List<LinkedHashMap<String, Object>> queryOpenMoByMaterial(Map<String, Object> parameterMap);

    int queryValidSaleOrderCnt(String order, String item);
}
