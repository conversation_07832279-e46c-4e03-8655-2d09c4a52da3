<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.webapi.ai.dao.IScenarioQueryDao">
	<sql id="filters">
        <if test="materials != null and materials.size() > 0">
			AND UPPER(T.MATERIAL) IN
			<foreach collection="materials" item="material" open="(" close=")" separator=",">
				UPPER(#{material, jdbcType=VARCHAR})
			</foreach>
		</if>
		<if test="plantCodes != null and plantCodes.size() > 0">
			AND T.PLANT_CODE IN
			<foreach collection="plantCodes" item="plantCode" open="(" close=")" separator=",">
				#{plantCode, jdbcType=VARCHAR}
			</foreach>
		</if>
		<if test="productLines != null and productLines.size() > 0">
			AND T.PRODUCT_LINE IN
			<foreach collection="productLines" item="productLine" open="(" close=")" separator=",">
				#{productLine, jdbcType=VARCHAR}
			</foreach>
		</if>
    </sql>

	<sql id="limit">
		OFFSET 0 ROWS FETCH NEXT 128 ROWS ONLY
	</sql>

	<select id="queryValidPlantCodes" resultType="java.lang.String">
		SELECT DISTINCT PLANT_CODE FROM MR3_PLANT_MASTER_DATA T WHERE
		<choose>
			<when test="plantCodes != null and plantCodes.size() > 0">
				UPPER(T.PLANT_CODE) IN
				<foreach collection="plantCodes" item="plantCode" open="(" close=")" separator=",">
					UPPER(#{plantCode, jdbcType=VARCHAR})
				</foreach>
			</when>
			<otherwise> 1 = 0 </otherwise>
		</choose>
		<include refid="limit"/>
	</select>

	<select id="queryValidProductLines" resultType="java.lang.String">
		SELECT DISTINCT NAME FROM MATERIAL_MASTER_FILTER_V T WHERE T.CATEGORY = 'PRODUCT_LINE'
		<choose>
			<when test="productLines != null and productLines.size() > 0">
				AND UPPER(T.NAME) IN
				<foreach collection="productLines" item="productLine" open="(" close=")" separator=",">
					UPPER(#{productLine, jdbcType=VARCHAR})
				</foreach>
			</when>
			<otherwise> AND 1 = 0 </otherwise>
		</choose>
		<include refid="limit"/>
	</select>

	<select id="queryValidMaterials" resultType="java.lang.String">
		SELECT DISTINCT MATERIAL FROM MATERIAL_MASTER_V T
		<choose>
			<when test="materials != null and materials.size() > 0">
				WHERE UPPER(T.MATERIAL) IN
				<foreach collection="materials" item="material" open="(" close=")" separator=",">
					UPPER(#{material, jdbcType=VARCHAR})
				</foreach>
			</when>
			<otherwise> WHERE 1 = 0 </otherwise>
		</choose>
		<include refid="limit"/>
	</select>

	<select id="queryMaterialOwnerByMaterial" resultType="java.util.LinkedHashMap">
		SELECT T.MATERIAL, T.PLANT_CODE, T.ENTITY, T.MATERIAL_OWNER_SESA, T.MATERIAL_OWNER_NAME
		FROM SCPA.MATERIAL_MASTER_V T
		<where>
			<include refid="filters"/>
		</where>
		<include refid="limit"/>
	</select>

	<select id="queryOrderIntakeByMaterial" resultType="java.util.LinkedHashMap">
		SELECT TO_CHAR(CALENDAR_DATE, 'YYYY/MM/DD') AS "DATE",
			   <foreach collection="keys" item="item" separator=",">
				   T.${item}
			   </foreach>,
			   COUNT(1)              		   AS "Order Line",
			   ROUND(SUM(T.ORDER_QUANTITY), 0) AS "Order Qty",
			   ROUND(SUM(T.ORDER_QUANTITY * T.AVG_SELLING_PRICE_RMB), 0)  AS "Order Value"
		FROM SCPA.DEMAND_ORDER_INTAKE_V T
		<where>
			<include refid="filters"/>
			<if test="date != null">
				AND T.CALENDAR_DATE = TO_DATE(#{date,jdbcType=VARCHAR}, 'YYYY/MM/DD')
			</if>
		</where>
		GROUP BY <foreach collection="keys" item="item" separator=",">
				   T.${item}
			     </foreach>, TO_CHAR(CALENDAR_DATE, 'YYYY/MM/DD')
		<include refid="limit"/>
	</select>

	<select id="queryBackOrderByMaterial" resultType="java.util.LinkedHashMap">
		SELECT <foreach collection="keys" item="item" separator=",">
				   T.${item}
			   </foreach>,
			   COUNT(1)              AS "Order Line",
			   ROUND(SUM(T.ORDER_QUANTITY), 0) AS "Order Qty",
			   ROUND(SUM(T.ORDER_QUANTITY * T.AVG_SELLING_PRICE_RMB), 0)  AS "Order Value"
		FROM SCPA.DEMAND_BACK_ORDER_V T
		<where>
			<include refid="filters"/>
		</where>
		GROUP BY <foreach collection="keys" item="item" separator=",">
				   T.${item}
			     </foreach>
		<include refid="limit"/>
	</select>

	<select id="queryBacklogByMaterial" resultType="java.util.LinkedHashMap">
		SELECT <foreach collection="keys" item="item" separator=",">
				   T.${item}
			   </foreach>,
			   COUNT(1)              				AS "Order Line",
			   ROUND(SUM(T.OPEN_SO_W_O_GI), 0) 		AS "Order Qty",
			   ROUND(SUM(T.OPEN_SO_W_O_GI * t.AVG_SELLING_PRICE_RMB), 0)   AS "Order Value"
		FROM SCPA.DEMAND_BACKLOG_V T
		<where>
			<include refid="filters"/>
		</where>
		GROUP BY <foreach collection="keys" item="item" separator=",">
				   T.${item}
			     </foreach>
		<include refid="limit"/>
	</select>

	<select id="querySohByMaterial" resultType="java.util.LinkedHashMap">
		SELECT <foreach collection="keys" item="item" separator=",">
				   T.${item}
			   </foreach>,
			   SUM(T.UU_STOCK)            AS UU,
			   SUM(T.BLOCKED_STOCK)       AS BLOCKED,
			   SUM(T.GIT_QTY)             AS GIT,
			   SUM(T.WIP_QTY)             AS WIP,
			   SUM(T.RESTRICTED_STOCK)    AS RESTRICTED,
			   SUM(T.RETURNS_STOCK)       AS RETURNS,
			   SUM(T.STOCK_IN_QI)         AS STOCK_IN_QI,
			   SUM(NVL(T.UU_STOCK, 0) + NVL(T.BLOCKED_STOCK, 0) +
				   NVL(T.GIT_QTY, 0) + NVL(T.WIP_QTY, 0) +
				   NVL(T.RESTRICTED_STOCK, 0) + NVL(T.RETURNS_STOCK, 0) +
				   NVL(T.STOCK_IN_QI, 0)) AS TOTAL
		FROM SCPA.INVENTORY_STRUCTURE_V T
		<where>
			<include refid="filters"/>
		</where>
		GROUP BY <foreach collection="keys" item="item" separator=",">
				   T.${item}
			     </foreach>
		<include refid="limit"/>
	</select>

	<select id="queryDeliveryByMaterial" resultType="java.util.LinkedHashMap">
		SELECT <foreach collection="keys" item="item" separator=",">
				   T.${item}
			   </foreach>,
			   COUNT(1)     AS "Order Line",
			   SUM(CASE
					   WHEN T.FULFILL_OR_NOT_NONBLOCK = 'Fulfill' THEN T.OPEN_SO_W_O_GI
					   WHEN T.FULFILL_OR_NOT_NONBLOCK = 'Partially Fulfill' THEN LEAST(T.PARTIALL_FULFILL_WITH_QTY, T.OPEN_SO_W_O_GI)
				   END)     AS "Order Qty",
			   ROUND(SUM(CASE
							 WHEN T.FULFILL_OR_NOT_NONBLOCK = 'Fulfill' THEN T.VALUE_W_O_GI
							 WHEN T.FULFILL_OR_NOT_NONBLOCK = 'Partially Fulfill' THEN LEAST(T.PARTIALL_FULFILL_WITH_ASP, T.VALUE_W_O_GI)
				   END), 0) AS "Order Value"
		FROM SCPA.OPEN_SO_STRUCTURE_V T
		WHERE T.FULFILL_OR_NOT_NONBLOCK IN ('Partially Fulfill', 'Fulfill')
		<include refid="filters"/>
		<if test="date != null">
			AND T.CALENDAR_DATE = TO_DATE(#{date,jdbcType=VARCHAR}, 'YYYY/MM/DD')
		</if>
		GROUP BY <foreach collection="keys" item="item" separator=",">
				   T.${item}
			     </foreach>
		<include refid="limit"/>
	</select>

	<select id="queryPoGrByMaterial" resultType="java.util.LinkedHashMap">
		SELECT TO_CHAR(T.GR_DATE, 'YYYY/MM/DD') AS "DATE",
		       <foreach collection="keys" item="item" separator=",">
				   T.${item}
			   </foreach>,
			   COUNT(1)        AS "GR Line",
			   ROUND(SUM(T.GR_QTY), 0)   AS "GR Qty",
			   ROUND(SUM(T.GR_VALUE), 0) AS "GR Value"
		FROM SCPA.PO_STRUCTURE_GR_V T
		<where>
			<include refid="filters"/>
			<if test="date != null">
				AND T.GR_DATE = TO_DATE(#{date,jdbcType=VARCHAR}, 'YYYY/MM/DD')
			</if>
		</where>
		GROUP BY <foreach collection="keys" item="item" separator=",">
				   T.${item}
			     </foreach>, TO_CHAR(T.GR_DATE, 'YYYY/MM/DD')
		<include refid="limit"/>
	</select>

	<select id="queryPoLaByMaterial" resultType="java.util.LinkedHashMap">
		SELECT <foreach collection="keys" item="item" separator=",">
				   T.${item}
			   </foreach>,
			   COUNT(1)                     AS LINE,
			   ROUND(SUM(T.PO_AB), 0)       AS AB_QTY,
			   ROUND(SUM(T.PO_AB_VALUE), 0) AS AB_VALUE,
			   ROUND(SUM(T.PO_LA), 0)       AS LA_QTY,
			   ROUND(SUM(T.PO_LA_VALUE), 0) AS LA_VALUE
		FROM SCPA.OPEN_PO_STRUCTURE_V T
		<where>
			<include refid="filters"/>
		</where>
		GROUP BY <foreach collection="keys" item="item" separator=",">
				   T.${item}
			     </foreach>
		<include refid="limit"/>
	</select>

	<select id="queryMoOutputByMaterial" resultType="java.util.LinkedHashMap">
		SELECT TO_CHAR(T.CALENDAR_DATE, 'YYYY/MM/DD') AS "DATE",
		       <foreach collection="keys" item="item" separator=",">
				   T.${item}
			   </foreach>,
		       SUM(T.ORDER_QUANTITY) AS 	  "Output Qty",
		       ROUND(SUM(T.ORDER_QUANTITY * T.AVG_SELLING_PRICE_RMB), 0) "Output Value"
		FROM DEMAND_OUTPUT_V T
		<where>
			<include refid="filters"/>
			<if test="date != null">
				AND T.CALENDAR_DATE = TO_DATE(#{date,jdbcType=VARCHAR}, 'YYYY/MM/DD')
			</if>
		</where>
		GROUP BY <foreach collection="keys" item="item" separator=",">
				   T.${item}
			     </foreach>, TO_CHAR(T.CALENDAR_DATE, 'YYYY/MM/DD')
		<include refid="limit"/>
	</select>

	<select id="queryOpenMoByMaterial" resultType="java.util.LinkedHashMap">
		SELECT <foreach collection="keys" item="item" separator=",">
				   T.${item}
			   </foreach>,
		       SUM(T.MO_QTY) AS "MO Qty",
		       ROUND(SUM(T.MO_VALUE), 0) "MO Value"
		FROM OPEN_MO_STRUCTURE_V T
		<where>
			<include refid="filters"/>
		</where>
		GROUP BY <foreach collection="keys" item="item" separator=",">
				   T.${item}
			     </foreach>
		<include refid="limit"/>
	</select>

	<select id="queryValidSaleOrderCnt" resultType="java.lang.Integer">
		SELECT COUNT(1) FROM SCPA.OPEN_SO_STRUCTURE_V T WHERE T.SALES_ORDER_NUMBER = #{order, jdbcType=VARCHAR} AND T.SALES_ORDER_ITEM = #{item, jdbcType=VARCHAR}
	</select>
</mapper>
