package com.webapi.ai;

import com.starter.context.bean.Response;
import com.starter.context.bean.SchneiderRequestMapping;
import com.starter.context.servlet.ControllerHelper;
import com.webapi.ai.service.ScenarioQueryService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.RestController;

@RestController
@SchneiderRequestMapping("/webapi/ai/scenario_query")
public class ScenarioQueryController extends ControllerHelper {

    @Resource
    private ScenarioQueryService scenarioQueryService;

    @SchneiderRequestMapping(value = "/query_material_owner_by_material")
    public Response queryMaterialOwnerByMaterial(HttpServletRequest request) {
        super.pageLoad(request);
        return scenarioQueryService.queryDataBySimpleScenarioQueryKey(SimpleScenarioQuery.MATERIAL_OWNER, parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_order_intake_by_material")
    public Response queryOrderIntakeByMaterial(HttpServletRequest request) {
        super.pageLoad(request);
        return scenarioQueryService.queryDataBySimpleScenarioQueryKey(SimpleScenarioQuery.ORDER_INTAKE, parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_backorder_by_material")
    public Response queryBackOrderByMaterial(HttpServletRequest request) {
        super.pageLoad(request);
        return scenarioQueryService.queryDataBySimpleScenarioQueryKey(SimpleScenarioQuery.BACK_ORDER, parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_backlog_by_material")
    public Response queryBacklogByMaterial(HttpServletRequest request) {
        super.pageLoad(request);
        return scenarioQueryService.queryDataBySimpleScenarioQueryKey(SimpleScenarioQuery.BACKLOG, parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_soh_by_material")
    public Response querySohByMaterial(HttpServletRequest request) {
        super.pageLoad(request);
        return scenarioQueryService.queryDataBySimpleScenarioQueryKey(SimpleScenarioQuery.SOH_QTY, parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_delivery_by_material")
    public Response queryDeliveryByMaterial(HttpServletRequest request) {
        super.pageLoad(request);
        return scenarioQueryService.queryDataBySimpleScenarioQueryKey(SimpleScenarioQuery.SO_DELIVERY, parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_po_gr_by_material")
    public Response queryPoGrByMaterial(HttpServletRequest request) {
        super.pageLoad(request);
        return scenarioQueryService.queryDataBySimpleScenarioQueryKey(SimpleScenarioQuery.PO_GR, parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_po_la_by_material")
    public Response queryPoLaByMaterial(HttpServletRequest request) {
        super.pageLoad(request);
        return scenarioQueryService.queryDataBySimpleScenarioQueryKey(SimpleScenarioQuery.PO_ABLA, parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_mo_output_by_material")
    public Response queryMoOutputByMaterial(HttpServletRequest request) {
        super.pageLoad(request);
        return scenarioQueryService.queryDataBySimpleScenarioQueryKey(SimpleScenarioQuery.MO_OUTPUT, parameterMap);
    }

    @SchneiderRequestMapping(value = "/query_open_mo_by_material")
    public Response queryOpenMoByMaterial(HttpServletRequest request) {
        super.pageLoad(request);
        return scenarioQueryService.queryDataBySimpleScenarioQueryKey(SimpleScenarioQuery.OPEN_MO, parameterMap);
    }
}
