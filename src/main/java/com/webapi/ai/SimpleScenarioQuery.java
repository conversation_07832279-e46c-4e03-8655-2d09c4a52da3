package com.webapi.ai;

public enum SimpleScenarioQuery {
    MATERIAL_OWNER("Material Owner"),
    ORDER_INTAKE("Order Intake"),
    BACK_ORDER("Back Order"),
    BACKLOG("BackLog"),
    SOH_QTY("SOH Qty"),
    SO_DELIVERY("SO Delivery"),
    PO_GR("PO GR"),
    PO_ABLA("PO ABLA"),
    MO_OUTPUT("MO Output"),
    OPEN_MO("Open MO");

    private final String text;

    SimpleScenarioQuery(String text) {
        this.text = text;
    }

    public String toString() {
        return text;
    }
}
