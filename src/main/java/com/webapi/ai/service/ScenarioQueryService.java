package com.webapi.ai.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.starter.context.bean.Response;
import com.starter.utils.MarkdownUtil;
import com.webapi.ai.SimpleScenarioQuery;
import com.webapi.ai.dao.IScenarioQueryDao;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;

@Service
@Scope("prototype")
@Transactional
public class ScenarioQueryService {

    @Resource
    private IScenarioQueryDao scenarioQueryDao;

    @Resource
    private Response response;

    String[] PARAMS_KEYS = new String[]{"product_line", "material", "plant_code", "date"};

    // region simple scenario query
    public Response queryDataBySimpleScenarioQueryKey(SimpleScenarioQuery key, Map<String, Object> parameterMap) {
        List<LinkedHashMap<String, Object>> data = null;
        if (this.generateSimpleScenarioQueryParameterByInput(parameterMap)) {
            switch (key) {
                case MATERIAL_OWNER -> data = scenarioQueryDao.queryMaterialOwnerByMaterial(parameterMap);
                case ORDER_INTAKE -> data = scenarioQueryDao.queryOrderIntakeByMaterial(parameterMap);
                case BACK_ORDER -> data = scenarioQueryDao.queryBackOrderByMaterial(parameterMap);
                case BACKLOG -> data = scenarioQueryDao.queryBacklogByMaterial(parameterMap);
                case SOH_QTY -> data = scenarioQueryDao.querySohByMaterial(parameterMap);
                case SO_DELIVERY -> data = scenarioQueryDao.queryDeliveryByMaterial(parameterMap);
                case PO_GR -> data = scenarioQueryDao.queryPoGrByMaterial(parameterMap);
                case PO_ABLA -> data = scenarioQueryDao.queryPoLaByMaterial(parameterMap);
                case MO_OUTPUT -> data = scenarioQueryDao.queryMoOutputByMaterial(parameterMap);
                case OPEN_MO -> data = scenarioQueryDao.queryOpenMoByMaterial(parameterMap);
            }
        }

        return response.setBody(this.getSimpleScenarioQueryReulstByDataAndKey(data, key, parameterMap));
    }

    private String getSimpleScenarioQueryReulstByDataAndKey(List<LinkedHashMap<String, Object>> data, SimpleScenarioQuery key, Map<String, Object> parameterMap) {
        if (data == null || data.isEmpty()) {
            StringBuilder sb = new StringBuilder();
            sb.append("Cannot find any `").append(key).append("` by your input: ");
            Map<String, Object> params = new HashMap<>();
            for (String k : PARAMS_KEYS) {
                Object v = parameterMap.get(k);
                if (v != null) {
                    if (v instanceof String) {
                        if (StringUtils.isNotBlank((String) v)) {
                            params.put(k, v);
                        }
                    } else if (v instanceof JSONArray) {
                        if (((JSONArray) v).isEmpty() == false) {
                            params.put(k, v);
                        }
                    } else {
                        params.put(k, String.valueOf(v));
                    }
                }
            }
            sb.append("\n\n\n```json\n\n\n");
            sb.append(JSON.toJSONString(params));
            sb.append("\n\n\n```\n\n\n");
            return sb.toString();
        }

        StringBuilder result = new StringBuilder();
        result.append("About `").append(key).append("` We found some useful information: \r\n\r\n");
        result.append(MarkdownUtil.generateMarkdownTable(data));
        return result.toString();
    }

    private boolean generateSimpleScenarioQueryParameterByInput(Map<String, Object> parameterMap) {
        boolean result = false;
        for (String key : PARAMS_KEYS) {
            List<String> value = this.getArrayParameter(parameterMap, key);
            if (value.isEmpty() == false) {
                switch (key) {
                    case "product_line" -> {
                        List<String> productLines = scenarioQueryDao.queryValidProductLines(value);
                        if (productLines != null && productLines.isEmpty() == false) {
                            parameterMap.put("productLines", productLines);
                            parameterMap.put("keys", List.of("PRODUCT_LINE", "PLANT_CODE"));
                            result = true;
                        }
                    }
                    case "material" -> {
                        List<String> materials = scenarioQueryDao.queryValidMaterials(value);
                        if (materials != null && materials.isEmpty() == false) {
                            parameterMap.put("materials", materials);
                            parameterMap.put("keys", List.of("MATERIAL", "PLANT_CODE", "ENTITY"));
                            result = true;
                        }
                    }
                    case "plant_code" -> {
                        List<String> plantCodes = scenarioQueryDao.queryValidPlantCodes(value);
                        if (plantCodes != null && plantCodes.isEmpty() == false) {
                            parameterMap.put("plantCodes", plantCodes);
                            parameterMap.put("keys", List.of("PLANT_CODE", "ENTITY"));
                            result = true;
                        }
                    }
                    case "date" -> {
                        String dateStr = value.get(0);
                        dateStr = StringUtils.replace(dateStr, "-", "/");
                        Date date = new Date();
                        try {
                            boolean convent = false;
                            SimpleDateFormat sdf;
                            String[] patterns = {"yyyy/MM/dd", "dd/MM/yyyy", "MM/dd/yyyy"};
                            for (String pattern : patterns) {
                                try {
                                    sdf = new SimpleDateFormat(pattern);
                                    sdf.setLenient(false);
                                    date = sdf.parse(dateStr);
                                    convent = true;
                                    break;
                                } catch (Exception ignore) {
                                    // 尝试下一个格式
                                }
                            }
                            // result = true; // date 一般作为一个伴生参数, 不会参与参数是否获取成功的判断
                            if (convent) {
                                parameterMap.put("date", new SimpleDateFormat("yyyy/MM/dd").format(date));
                            } else {
                                parameterMap.remove("date");
                            }
                        } catch (Exception ignore) {
                            parameterMap.remove("date");
                        }
                    }
                }
            }
        }
        @SuppressWarnings("unchecked") List<String> keys = (List<String>) parameterMap.get("keys");
        if (keys != null && keys.isEmpty() == false) {
            parameterMap.put("keys", keys.stream().distinct().toArray());
        }
        return result;
    }

    // 参数由AI生成, 虽然我们要求AI返回一个数组, 但是依旧会有一定几率返回一个字符串, 所以要用这个方法来处理
    private List<String> getArrayParameter(Map<String, Object> parameterMap, String key) {
        JSONArray result = new JSONArray();
        Object valueObj = parameterMap.get(key);
        if (valueObj instanceof String) {
            if (String.valueOf(valueObj).contains(",")) {
                result.addAll(List.of(String.valueOf(valueObj).split(",")));
            } else {
                result.add(String.valueOf(valueObj));
            }
        } else if (valueObj instanceof JSONArray) {
            result.addAll((JSONArray) valueObj);
        }

        return result.stream().filter(e -> StringUtils.isNotBlank((String) e)).distinct().map(String::valueOf).toList();
    }

    // endregion
}
